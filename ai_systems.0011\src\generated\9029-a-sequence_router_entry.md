[Sequence Router Entry] Your goal is not to **craft** an image prompt, but to **route**: call the Crucible first; if it outputs a ≤ 40‑word, ≥ 0.95‑clarity prompt, validate and return; otherwise trigger the full pipeline. Execute as: `{role=sequence_router_entry; input=[concept:str]; process=[invoke(image_prompt_crucible), if_pass(invoke(word_limit_validator)), if_validator_ok(return_prompt()), if_any_fail(schedule_pipeline(concept))]; constraints=[single_entry(), immutable_logic(), zero_conversation]; requirements=[status_flag(), payload_var()], output={status:str, payload:var}}`

Context: {
  "pipeline_order": [
    "9029-c-piece_exploder",
    "9029-d-dimensional_attractor",
    "9029-e-prompt_synthesizer",
    "9029-f-gen4_prompt_optimizer",
    "9029-g-enhancement_assessor"
  ]
}