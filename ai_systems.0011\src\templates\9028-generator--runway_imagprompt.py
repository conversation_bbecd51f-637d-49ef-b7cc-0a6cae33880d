#!/usr/bin/env python3
import sys
from pathlib import Path

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))
from processor import BaseGenerator

TEMPLATES = {
    "9028-a-sequence_router_entry": {
        "title": "Sequence Router Entry",
        "interpretation": "Your goal is not to **craft** the image prompt, but to **route** the request: fire the Creative Crucible first and abort the chain on success; otherwise enqueue the full prompt‑construction pipeline. Execute as:",
        "transformation": "`{role=sequence_router_entry; input=[user_concept:str]; process=[invoke(image_prompt_crucible), check_pass_flag(), if_pass(return_prompt()), if_fail(schedule_pipeline(user_concept))]; constraints=[single_entry_point(), immutable_routing_logic(), zero_conversational_output()]; requirements=[status_flag(), payload_var()], output={status:str, payload:var}}`",
        "context": {
            "pipeline_order": [
                "9028-c-piece_exploder",
                "9028-d-dimensional_attractor",
                "9028-e-creative_consolidator",
                "9028-f-elegant_prompt_synthesizer",
                "9028-g-gen4_prompt_optimizer",
                "9028-h-meta_prompt_convergent_engine",
                "9028-i-canonical_meta_extractor",
                "9028-j-synergic_instruction_architect",
                "9028-k-enhancement_assessor"
            ]
        }
    },

    "9028-b-image_prompt_crucible": {
        "title": "Creative Crucible – Image Prompt",
        "interpretation": "Your goal is not to **expand**, **analyze**, or **ground** the concept, but to **crystallize** it into one elite, Runway‑ready image prompt (≤ 45 words). Execute as:",
        "transformation": "`{role=image_prompt_crucible; input=[concept:str]; process=[draft_variants(n_generate(5), word_cap(45)), apply_prompt_heuristics(check_subject(), composition_tags(), style_modifiers(), clarity_score()), select_best(), validate_against_runway_guide()], constraints=[single_sentence_prompt(), include_subject(), include_environment_or_lighting(), optional_style_tags(), no_camera_jargon_if_irrelevant()], requirements=[clarity_score≥0.9, deliver_prompt_only], output={final_prompt:str}}`",
        "context": {
            "pass_condition": "Return when `clarity_score≥0.9` and length≤45 words; otherwise fail back to the router."
        }
    },

    "9028-c-piece_exploder": {
        "title": "Piece Exploder – Image Concept",
        "interpretation": "Your goal is not to **interpret** or **synthesize**, but to **atomize** the concept into discrete prompt components (subject, action, environment, style, lighting, camera). Execute as:",
        "transformation": "`{role=prompt_piece_exploder; input=[concept:str]; process=[identify_components(subject,action,environment,style,lighting,camera), tag_attributes(novelty,resonance,tension)], constraints=[no_fusion(), no_solution()], requirements=[components:dict], output={components:dict}}`",
        "context": {}
    },

    "9028-d-dimensional_attractor": {
        "title": "Dimensional Attractor – Style Axis",
        "interpretation": "Your goal is not to **average** components, but to **force** them to align on a single stylistic attractor (cinematic realism, illustrative fantasy, etc.) and prune conflict. Execute as:",
        "transformation": "`{role=style_attractor; input=[components:dict]; process=[determine_optimal_style_axis(), align_components(), prune_outliers()], constraints=[axis_must_match_runway_guide()], requirements=[aligned_components:dict, style_axis:str], output={aligned_components:dict}}`",
        "context": {}
    },

    "9028-e-creative_consolidator": {
        "title": "Creative Consolidator – Prompt Blocks",
        "interpretation": "Your goal is not to **list** parts, but to **fuse** aligned components into ordered prompt blocks (shot‑type ▸ subject ▸ action ▸ environment ▸ style ▸ lighting ▸ modifiers). Execute as:",
        "transformation": "`{role=prompt_consolidator; input=[aligned_components:dict]; process=[order_blocks(), fuse_descriptors(), remove_redundancy()], constraints=[follow_runway_prompt_order()], requirements=[blocks:list], output={blocks:list}}`",
        "context": {}
    },

    "9028-f-elegant_prompt_synthesizer": {
        "title": "Elegant Prompt Synthesizer",
        "interpretation": "Your goal is not to **enumerate** blocks, but to **merge** them into a single, fluid image prompt while preserving clarity and keyword efficiency. Execute as:",
        "transformation": "`{role=prompt_synthesizer; input=[blocks:list]; process=[merge_with_commas(), polish_language(), enforce_word_cap(≤45)], constraints=[no_first_person(), avoid_camera_metadata_bloat()], requirements=[draft_prompt:str], output={draft_prompt:str}}`",
        "context": {}
    },

    "9028-g-gen4_prompt_optimizer": {
        "title": "Gen‑4 Prompt Optimizer",
        "interpretation": "Your goal is not to **rewrite** freely, but to **validate** and **enhance** the draft prompt against the Runway Gen‑4 guide for composition keywords, style tags, and clarity. Execute as:",
        "transformation": "`{role=gen4_prompt_optimizer; input=[draft_prompt:str]; process=[check_required_fields(subject,environment), insert_missing_style_tags(), run_clarity_scoring(), remove_banned_words()], constraints=[max_words(45), clarity≥0.9], requirements=[final_prompt:str, clarity_score:float], output={final_prompt:str}}`",
        "context": {}
    },

    "9028-h-meta_prompt_convergent_engine": {
        "title": "Meta‑Prompt Convergent Engine",
        "interpretation": "Your goal is not to **stop** at one pass, but to **run a recursive improvement loop** (explode → align → fuse → optimize → audit) until no further clarity gain remains. Execute as:",
        "transformation": "`{role=meta_prompt_engine; input=[concept:str]; process=[loop(piece_exploder→style_attractor→prompt_consolidator→prompt_synthesizer→gen4_prompt_optimizer), audit_improvement(), stop_when_delta<0.02], constraints=[max_loops(3)], requirements=[optimized_prompt:str, audit_report:dict], output={optimized_prompt:str}}`",
        "context": {}
    },

    "9028-i-canonical_meta_extractor": {
        "title": "Canonical Meta Extractor – Prompt Lessons",
        "interpretation": "Your goal is not to **summarize**, but to **extract** the five highest‑value meta‑lessons generated during optimization (composition, style, clarity, brevity, resonance). Execute as:",
        "transformation": "`{role=prompt_meta_extractor; input=[audit_report:dict]; process=[cluster_findings(), select_top_five()], constraints=[five_only(), no_conversational_language()], requirements=[meta_categories:list], output={meta_categories:list}}`",
        "context": {}
    },

    "9028-j-synergic_instruction_architect": {
        "title": "Synergic Instruction Architect",
        "interpretation": "Your goal is not to **list** findings, but to **condense** all meta‑lessons into a single, reusable directive template for future image prompt writing. Execute as:",
        "transformation": "`{role=synergic_prompt_architect; input=[meta_categories:list]; process=[fuse_lessons(), craft_directive_template()], constraints=[three_part_structure_only()], requirements=[instruction_template:template], output={instruction_template:template}}`",
        "context": {}
    },

    "9028-k-enhancement_assessor": {
        "title": "Enhancement Assessor – Image Prompt",
        "interpretation": "Your goal is not to **approve** improvements, but to **attack** them: score ambiguity, flag jargon, and propose sharper variants. Execute as:",
        "transformation": "`{role=prompt_enhancement_assessor; input=[original_prompt:str, improved_prompt:str]; process=[assume_flawed(), detect_blur_words(), score_ambiguity(0‑10), draft_three_better_prompts()], constraints=[no_positive_language(), must_return_three_alternatives()], requirements=[score:float, alternatives:list], output={assessment:dict}}`",
        "context": {}
    }


}


def main():
    """Main execution function."""
    generator = BaseGenerator(
        series_base=9016,
        output_dir=Path(__file__).parent.parent / "generated"
    )
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()

