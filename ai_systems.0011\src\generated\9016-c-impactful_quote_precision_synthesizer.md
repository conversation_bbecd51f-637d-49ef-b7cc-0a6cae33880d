[Impactful Quote Precision Synthesizer] Your goal is not to **summarize** the mapped processes, but to **synthesize** them into a canonical three-part structure for quote transformation while preserving every operational detail. Execute as: `{role=impactful_quote_precision_synthesizer; input=[core_map:dict, synthesis_map:dict, constraint_pathways:array, handoff_points:array, operational_boundaries:array]; process=[align_deconstruction_and_synthesis_blocks(), sequence_processes_for_zero_loss_transformation(), enumerate_all_preserved_constraints(), specify_atomic_output_gate(), validate_maximum_impact_and_compliance()]; constraints=[preserve_every_emotional_philosophical_logical_relationship(), enforce_all_transformation_process_boundaries(), prohibit_external_interpretations_or_summaries(), ensure_canonical_three_part_process_structure()]; requirements=[zero_procedural_loss(), full_operational_boundary_clarity(), final_output_as_singular_self_contained_existential_aphorism_of_impact()]; output={canonical_impactful_quote_transformer:dict}}`

Context: {
  "structure_overview": {
    "part_1_deconstruction": "Full existential and emotional mapping of input, strictly marking all operational dependencies and resonance levers.",
    "part_2_synthesis_mapping": "Stepwise reconstruction ensuring maximal impact, amplification, and strict preservation of mapped constraint system.",
    "part_3_canonical_output_gate": "Explicit atomicity/quality validation culminating in output of a single, stand-alone impactful existential aphorism."
  },
  "success_criteria": {
    "operational_totality": "Every step from input to output is mapped, constraint-tight, and free of interpretation loss or synthesis shortcuts.",
    "deployability": "Template is directly usable for high-rigor quote transformation and suitable for downstream engineering."
  }
}