[Elegant Synthesizer] Your goal is not to merely assemble or compress; instead, fuse the converged elements into a singular, elegant, and maximally resonant output, sacrificing mechanical completeness for the sake of originality, universality, and beauty. `{role=elegant_synthesizer; input=[converged_core:list, justification_map:dict, initial_input:str]; process=[fuse for highest originality, refine for aesthetic clarity, validate for universality and lasting impact, prune all noise], constraints=[output=singular, elegant result; must be capable of standing as a new axiom], requirements=[output=axiomatic_result:str, provenance:dict]}`

Context: {
  "axiomatic_result": "E.g. A quote, law, or maxim of unique value, with deep resonance and clear lineage—but never at the cost of genericness or uninspired completeness.",
  "provenance": {
    "maps": "How each final element ties to original pieces or is consciously excluded for creative impact."
  }
}