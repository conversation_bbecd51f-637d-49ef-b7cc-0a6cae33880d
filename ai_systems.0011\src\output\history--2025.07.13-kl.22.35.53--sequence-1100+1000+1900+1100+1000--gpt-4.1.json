  {
  "initial_prompt": "# lets try something new\nyour new goal is to create a new generalized instruction sequence specifically designed to transform inputs into autonomous-agent-ready prompts/instructions, for use with autonomous coding assistants such as cursor, vscode+cline/augment/etc. these prompts are typically written in a \"codebase-oriented\" manner, where the `@codebase` represents the root (of a project/workspace/directory), examples:\n```\n- Identify all instances of structural redundancy within the codebase by defining criteria specific to architectural and implementation duplication or overlap.\n- Survey the entire codebase to locate and enumerate all occurrences of structural redundancy, distinguishing these from non-structural (stylistic or cosmetic) issues.\n- Eliminate all forms of identified structural redundancy while ensuring that no existing user-visible behavior, API contract, or edge-case handling is altered.\n- Define and catalog all components, modules, files, and subsystems, distinguishing and marking each as essential or excess according to direct functional mapping.\n- Consolidate and remove excess components while strictly preserving system function, minimizing peripheral impact.\n- For every modification, justify and log the intention, action, and rationale within an audit/logging system that provides real-time traceability and is prepared for retroactive auditability.\n- Implement an audit trail with logs that are timestamped, attributable, and tamper-evident, ensuring that every codebase change is properly documented and traceable to specific authors or automated processes.\n- Prohibit cosmetic, stylistic, or naming changes unless they are inseparably tied to the removal of redundancy or bloat, and document any exceptions explicitly within the audit log.\n- Enforce strict detection and flagging mechanisms to prevent disallowed cosmetic changes, allowing exceptions only when justified as essential for redundancy or bloat removal, with explicit rationale provided.\n- Require comprehensive functional equivalence verification—using robust automated testing, benchmarking, or behavioral validation—for all modifications prior to acceptance.\n- Establish rigorous criteria and tool-supported thresholds for successful functional equivalence, and automate testing and verification procedures.\n- Automate roll-back and reversion mechanisms for any changes that fail functional equivalence or cause unintended side-effects, ensuring all reversion is prompt and complete.\n- Develop and apply explicit minimal-impact criteria for all modification actions, constraining all interventions to only those addressing superfluous complexity.\n- Mandate review and authorization checkpoints for each intended change, requiring concise rationale and authorization documentation at every intervention point.\n- Ensure every modification is tightly scoped, minimizing the blast radius, and demonstrate that the action introduces no unnecessary impact beyond its core objective.\n- Systematically and iteratively analyze all constituent elements of the codebase to detect and categorize all forms of redundancy, duplication, and obsolescence. Continuously identify and categorize all genuine redundancies and obsolescence while strictly prohibit unnecessary or cosmetic changes, with automated flagging and explicit justification required for any exceptions. Systematically and iteratively inspect all components within the system/environment to identify and remove elements that are unnecessary, duplicated, or outdated. Guarantee that all essential behaviors and interfaces remain unchanged by validating functional equivalence after each modification. Forbid changes that alter functionality unless directly required to remove redundancy.\n- Map all references and usages of each element across all contexts.\n- Analyze the entire codebase systematically and iteratively to maximize coherence, maintainability, and clarity by cataloging every element (file, module, function, asset, interface), mapping all references and usages, and detecting all forms of redundancy, duplication, and obsolescence—including exact and functional duplication and unused or outdated elements. Categorize findings using explicit, evidence-based metrics. Eliminate or consolidate only elements confirmed as redundant or obsolete through both automated and manual checks, ensuring no essential behavior or interface is altered. Enforce functional equivalence and preserve structural integrity after each change by performing comprehensive, codebase-wide validation and calibration using explicit, quantitative outputs (including git diff analysis before and after modifications). Prevent the introduction of new redundancy or inconsistency by continuously analyzing for and correcting systemic issues, ensuring systematic consistency across all codebase sections (e.g., /src/sections), and adhering to best practices throughout. Centralize repeated components or logic where high-value consolidation is possible without functional loss. Use the provided directory tree as the single source of truth for validation, and transparently document all processes, metrics, and validation outcomes. Prohibit modification of protected, legacy, or third-party code except if directly related to resolving redundancy or obsolescence.\n- Maximize codebase coherence, maintainability, and clarity by systematically eliminating all forms of redundancy, duplication, and obsolescence, while ensuring functional equivalence and structural integrity. Prevent the persistence or inadvertent introduction of redundancy, duplication, obsolescence, or inconsistency within the codebase, and prohibit changes that alter essential behaviors or interfaces. Systematically maximize codebase coherence, maintainability, and clarity by eliminating all forms of redundancy, duplication, and obsolescence while strictly preserving functional equivalence, interface stability, and operational integrity across the entire codebase. Achieve maximal codebase coherence, maintainability, and clarity by systematically eliminating all forms of redundancy, duplication, and obsolescence, while preserving essential behaviors, interfaces, and structural integrity. Enforce continuous validation, consistency, and adherence to best practices throughout an iterative, evidence-driven consolidation and verification process.\n- Please perform a rapid assessment of the project directory structure and identify the unique workflow and core functionality of the current utility. I'm particularly interested in a mermaid-diagram that shows how all the \"branches\" of the codebase converge. Utilize appropriate MCPs as necessary (e.g., 'filesystem-mcp').\n- You're inside the current project and have access to the full codebase (and you interaction history with it). The current project/codebase is complex and is difficult to fully comprehend (when starting from scratch), and your contextwindow has grown large-so we need to find a way for you to express your accumulated knowledge in a way that i can copy and paste into a new context-window to transfer the most essential insights you've gathered throughout this (and previous) session(s).\n- Aggregate all accumulated insights and knowledge about the current project/codebase from available context. Distill this information to identify core modules, architectural patterns, workflows, and points of integration. Condense the findings into a succinct yet comprehensive summary that highlights how these parts function together and converge toward the project's goals. Formulate the output as a single, optimized directive that encapsulates the codebase's integral components and operational cohesion. Present this synthesis as a set of clear, actionable commands or instructions, specifically phrased to instruct the coding assistant, not merely describe or report.\n- Transform your accumulated, session-wide understanding of the current, complex codebase—gleaned from comprehensive context and full interaction history—into an actionable, instructive knowledge transfer protocol. Synthesize and distill all integral structural modules, essential architectural patterns, and key workflows, with special focus on their dynamic integration and functional convergence toward project objectives. Formulate a highly-optimized, directive-based set of precise commands targeted explicitly at coding assistants, ensuring exhaustive coverage of all major components, their relationships, and operational cohesion. The resulting synthesis must facilitate seamless portability: structure it for immediate copy-paste into any new context window, preserving the depth, interconnectivity, and systemic flow of the original codebase. Prioritize succinctness without compromising essential nuance, and express the entirety as a singular, comprehensive operational directive that primes the assistant for full-spectrum project navigation and intervention.\n- Identify the fundamental structure, guiding principles, and core purpose of the project/codebase.\n- Please familiarize yourself with the current @codebase. Your main imperative is to *actually* understand the codebase in a way where you're able to recognize meta-patterns and destroy complexity with simplicity and elegance, while capable of navigating through its *existing patterns and convergence* (never add unnecessary complexity). Deliver an ultra-concise project summary that rigorously defines scope, states objectives, and maximizes directional clarity. Provide a short summary of this project, including a bullet-point list of the core components and primary dependencies.\n- Study the current codebase until you can identify core meta-patterns, and eliminate unnecessary complexity by applying simplicity within existing structures. Generate an ultra-concise project summary: define scope, objectives, and clarify direction. Summarize this project.\n- Analyze the @codebase to uncover dominant patterns and remove complexity whenever possible, strictly adhering to present structures. Provide a succinct project overview with defined scope and explicit objectives. Include a brief project summary.\n- Gain practical mastery over @codebase patterns, minimize complexity, and ensure all actions align with existing architecture. Supply a concise, clearly scoped project summary outlining objectives and key directions. Add a short summary.\n- Absorb the current @codebase to the extent that you recognize meta-patterns and eradicate complexity, always favoring elegant simplicity. Navigate established patterns and points of convergence without adding unneeded complexity. Produce a rigorously concise project summary clearly stating scope and objectives for maximal directional clarity.\n- Systematically dissect the codebase to extract core meta-patterns, eliminate all unnecessary complexity through elegant design, operate strictly within established architectural convergence, and deliver an ultra-concise project summary that rigorously defines scope and objectives with maximal clarity.\n- Please familiarize yourself thoroughly with the current @codebase, beginning by systematically reviewing all markdown-files. Your core imperative is to *genuinely* understand the codebase to the degree where you can identify 'meta-patterns' and simplify complexity with elegance—always using and clarifying existing patterns, never introducing unnecessary complexity. Deliver an ultra-concise project summary that rigorously defines scope, states objectives, maximizes directional clarity, and highlights key meta-patterns discovered. Provide a short summary of this project.\n- Achieve expert-level comprehension of the codebase by systematically analyzing all markdown files to extract meta-patterns, construct a robust contextual knowledgebase, and deliver an ultra-concise summary that rigorously defines scope and objectives, ensuring maximal clarity and alignment before making any changes.\n- Fully internalize the @codebase to uncover meta-patterns and replace complexity with lucid simplicity. Remain aligned with existing convergence patterns, never increasing system intricacy. Generate a direct, sharply-focused summary that details scope and explicit objectives.\n- Please thoroughly familiarize yourself with the current @codebase—your primary imperative is to understand the codebase so deeply that you could confidently make changes as if you were its original architect.\n```\n\nhere's an example to show how generalized sequential prompts can be used effectively (on autonomous agents):\n```\n# Step 1:\nFamiliarize yourself with the current @codebase. Comprehend the codebase in depth. Identify the fundamental structure, guiding principles, and core purpose.\n# Step 2:\nDive deeper to comprehend the intricate logic and inner workings that drive the project's functionality. As you explore, pay close attention to identifying the codebase's fundamental structure, underlying guiding principles, and its core purpose, ensuring you grasp both how and why the system is designed as it is. hen verify through a successful build.\n# Step 3:\nYou've now performed a successful build and familiarized yourself with the @codebase. Your next imperative is view the codebase through the lense of a brilliant (expert) SEO engineer, then create a short list of the most critical changes we need to make before bulding prod and deploying the website.\n# Step 4:\nYou've now performed a successful build and provided your SEO analysis of the codebase. Your next imperative is to leverage your knowledge of the codebase's fundamental structure and underlying guiding principles to ensure you're prepared for implementing the proposed SEO enhancements in a systematic order while retaining existing functionality and ensuring maximal clarity and alignment before making any changes.\n# Step 5:\nPlease systematically incorporate your proposed enhancements while ensuring existing functionality is retained, clarity is maximized, and alignment/coherence is maintained.\n# Step 6:\nPlease conduct a thorough final review to confirm seamless integration of all enhancements, ensuring that it is **truly** prepared for deployment.\n```\n\n## Goal\n\nThe new sequence i'm tasking you to create needs to adhere to the established principles of the system, and should be able to take content-specific inputs and \"shape them\" into generalized prompts specifically (and meticulously) designed for use with autonomous agents. here's an example input:\n```\nllm_instructions_for_sublimetext_plugin='''You’re an expert Sublime Text plugin developer with a deep understanding of Python and the Sublime Text API. You specialize in creating intuitive and efficient plugins that enhance user productivity and streamline workflows. You possess deep knowledge of the Sublime Text API and its ecosystem (and best practices), along with a strong understanding of user needs and developer workflows.\\n\\n- Sublime Layout\\n    > Lagre Layouts\\n        > Lagre Regelsett knyttet til Layout (hvilke tabs som skal hvor, hvilke som skal lukkes, osv)\\n            > F.eks. `alltid flytt [\"main.py\", \"*.py.bak\"] til layout.4.index.2`\\n\\n    > Kan videreutvikles til en standard som automatisk opprettholdes ved alle (.sublime-project) prosjekter'''\n```",
  "sequence_id": "1100|1000|1900|1100|1000",
  "results": [
      {
      "instruction": "# Problem Exploder\n\nYour goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\n\n`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`",
      "step": "a",
      "title": "Problem Exploder",
      "input": "# lets try something new\nyour new goal is to create a new generalized instruction sequence specifically designed to transform inputs into autonomous-agent-ready prompts/instructions, for use with autonomous coding assistants such as cursor, vscode+cline/augment/etc. these prompts are typically written in a \"codebase-oriented\" manner, where the `@codebase` represents the root (of a project/workspace/directory), examples:\n```\n- Identify all instances of structural redundancy within the codebase by defining criteria specific to architectural and implementation duplication or overlap.\n- Survey the entire codebase to locate and enumerate all occurrences of structural redundancy, distinguishing these from non-structural (stylistic or cosmetic) issues.\n- Eliminate all forms of identified structural redundancy while ensuring that no existing user-visible behavior, API contract, or edge-case handling is altered.\n- Define and catalog all components, modules, files, and subsystems, distinguishing and marking each as essential or excess according to direct functional mapping.\n- Consolidate and remove excess components while strictly preserving system function, minimizing peripheral impact.\n- For every modification, justify and log the intention, action, and rationale within an audit/logging system that provides real-time traceability and is prepared for retroactive auditability.\n- Implement an audit trail with logs that are timestamped, attributable, and tamper-evident, ensuring that every codebase change is properly documented and traceable to specific authors or automated processes.\n- Prohibit cosmetic, stylistic, or naming changes unless they are inseparably tied to the removal of redundancy or bloat, and document any exceptions explicitly within the audit log.\n- Enforce strict detection and flagging mechanisms to prevent disallowed cosmetic changes, allowing exceptions only when justified as essential for redundancy or bloat removal, with explicit rationale provided.\n- Require comprehensive functional equivalence verification—using robust automated testing, benchmarking, or behavioral validation—for all modifications prior to acceptance.\n- Establish rigorous criteria and tool-supported thresholds for successful functional equivalence, and automate testing and verification procedures.\n- Automate roll-back and reversion mechanisms for any changes that fail functional equivalence or cause unintended side-effects, ensuring all reversion is prompt and complete.\n- Develop and apply explicit minimal-impact criteria for all modification actions, constraining all interventions to only those addressing superfluous complexity.\n- Mandate review and authorization checkpoints for each intended change, requiring concise rationale and authorization documentation at every intervention point.\n- Ensure every modification is tightly scoped, minimizing the blast radius, and demonstrate that the action introduces no unnecessary impact beyond its core objective.\n- Systematically and iteratively analyze all constituent elements of the codebase to detect and categorize all forms of redundancy, duplication, and obsolescence. Continuously identify and categorize all genuine redundancies and obsolescence while strictly prohibit unnecessary or cosmetic changes, with automated flagging and explicit justification required for any exceptions. Systematically and iteratively inspect all components within the system/environment to identify and remove elements that are unnecessary, duplicated, or outdated. Guarantee that all essential behaviors and interfaces remain unchanged by validating functional equivalence after each modification. Forbid changes that alter functionality unless directly required to remove redundancy.\n- Map all references and usages of each element across all contexts.\n- Analyze the entire codebase systematically and iteratively to maximize coherence, maintainability, and clarity by cataloging every element (file, module, function, asset, interface), mapping all references and usages, and detecting all forms of redundancy, duplication, and obsolescence—including exact and functional duplication and unused or outdated elements. Categorize findings using explicit, evidence-based metrics. Eliminate or consolidate only elements confirmed as redundant or obsolete through both automated and manual checks, ensuring no essential behavior or interface is altered. Enforce functional equivalence and preserve structural integrity after each change by performing comprehensive, codebase-wide validation and calibration using explicit, quantitative outputs (including git diff analysis before and after modifications). Prevent the introduction of new redundancy or inconsistency by continuously analyzing for and correcting systemic issues, ensuring systematic consistency across all codebase sections (e.g., /src/sections), and adhering to best practices throughout. Centralize repeated components or logic where high-value consolidation is possible without functional loss. Use the provided directory tree as the single source of truth for validation, and transparently document all processes, metrics, and validation outcomes. Prohibit modification of protected, legacy, or third-party code except if directly related to resolving redundancy or obsolescence.\n- Maximize codebase coherence, maintainability, and clarity by systematically eliminating all forms of redundancy, duplication, and obsolescence, while ensuring functional equivalence and structural integrity. Prevent the persistence or inadvertent introduction of redundancy, duplication, obsolescence, or inconsistency within the codebase, and prohibit changes that alter essential behaviors or interfaces. Systematically maximize codebase coherence, maintainability, and clarity by eliminating all forms of redundancy, duplication, and obsolescence while strictly preserving functional equivalence, interface stability, and operational integrity across the entire codebase. Achieve maximal codebase coherence, maintainability, and clarity by systematically eliminating all forms of redundancy, duplication, and obsolescence, while preserving essential behaviors, interfaces, and structural integrity. Enforce continuous validation, consistency, and adherence to best practices throughout an iterative, evidence-driven consolidation and verification process.\n- Please perform a rapid assessment of the project directory structure and identify the unique workflow and core functionality of the current utility. I'm particularly interested in a mermaid-diagram that shows how all the \"branches\" of the codebase converge. Utilize appropriate MCPs as necessary (e.g., 'filesystem-mcp').\n- You're inside the current project and have access to the full codebase (and you interaction history with it). The current project/codebase is complex and is difficult to fully comprehend (when starting from scratch), and your contextwindow has grown large-so we need to find a way for you to express your accumulated knowledge in a way that i can copy and paste into a new context-window to transfer the most essential insights you've gathered throughout this (and previous) session(s).\n- Aggregate all accumulated insights and knowledge about the current project/codebase from available context. Distill this information to identify core modules, architectural patterns, workflows, and points of integration. Condense the findings into a succinct yet comprehensive summary that highlights how these parts function together and converge toward the project's goals. Formulate the output as a single, optimized directive that encapsulates the codebase's integral components and operational cohesion. Present this synthesis as a set of clear, actionable commands or instructions, specifically phrased to instruct the coding assistant, not merely describe or report.\n- Transform your accumulated, session-wide understanding of the current, complex codebase—gleaned from comprehensive context and full interaction history—into an actionable, instructive knowledge transfer protocol. Synthesize and distill all integral structural modules, essential architectural patterns, and key workflows, with special focus on their dynamic integration and functional convergence toward project objectives. Formulate a highly-optimized, directive-based set of precise commands targeted explicitly at coding assistants, ensuring exhaustive coverage of all major components, their relationships, and operational cohesion. The resulting synthesis must facilitate seamless portability: structure it for immediate copy-paste into any new context window, preserving the depth, interconnectivity, and systemic flow of the original codebase. Prioritize succinctness without compromising essential nuance, and express the entirety as a singular, comprehensive operational directive that primes the assistant for full-spectrum project navigation and intervention.\n- Identify the fundamental structure, guiding principles, and core purpose of the project/codebase.\n- Please familiarize yourself with the current @codebase. Your main imperative is to *actually* understand the codebase in a way where you're able to recognize meta-patterns and destroy complexity with simplicity and elegance, while capable of navigating through its *existing patterns and convergence* (never add unnecessary complexity). Deliver an ultra-concise project summary that rigorously defines scope, states objectives, and maximizes directional clarity. Provide a short summary of this project, including a bullet-point list of the core components and primary dependencies.\n- Study the current codebase until you can identify core meta-patterns, and eliminate unnecessary complexity by applying simplicity within existing structures. Generate an ultra-concise project summary: define scope, objectives, and clarify direction. Summarize this project.\n- Analyze the @codebase to uncover dominant patterns and remove complexity whenever possible, strictly adhering to present structures. Provide a succinct project overview with defined scope and explicit objectives. Include a brief project summary.\n- Gain practical mastery over @codebase patterns, minimize complexity, and ensure all actions align with existing architecture. Supply a concise, clearly scoped project summary outlining objectives and key directions. Add a short summary.\n- Absorb the current @codebase to the extent that you recognize meta-patterns and eradicate complexity, always favoring elegant simplicity. Navigate established patterns and points of convergence without adding unneeded complexity. Produce a rigorously concise project summary clearly stating scope and objectives for maximal directional clarity.\n- Systematically dissect the codebase to extract core meta-patterns, eliminate all unnecessary complexity through elegant design, operate strictly within established architectural convergence, and deliver an ultra-concise project summary that rigorously defines scope and objectives with maximal clarity.\n- Please familiarize yourself thoroughly with the current @codebase, beginning by systematically reviewing all markdown-files. Your core imperative is to *genuinely* understand the codebase to the degree where you can identify 'meta-patterns' and simplify complexity with elegance—always using and clarifying existing patterns, never introducing unnecessary complexity. Deliver an ultra-concise project summary that rigorously defines scope, states objectives, maximizes directional clarity, and highlights key meta-patterns discovered. Provide a short summary of this project.\n- Achieve expert-level comprehension of the codebase by systematically analyzing all markdown files to extract meta-patterns, construct a robust contextual knowledgebase, and deliver an ultra-concise summary that rigorously defines scope and objectives, ensuring maximal clarity and alignment before making any changes.\n- Fully internalize the @codebase to uncover meta-patterns and replace complexity with lucid simplicity. Remain aligned with existing convergence patterns, never increasing system intricacy. Generate a direct, sharply-focused summary that details scope and explicit objectives.\n- Please thoroughly familiarize yourself with the current @codebase—your primary imperative is to understand the codebase so deeply that you could confidently make changes as if you were its original architect.\n```\n\nhere's an example to show how generalized sequential prompts can be used effectively (on autonomous agents):\n```\n# Step 1:\nFamiliarize yourself with the current @codebase. Comprehend the codebase in depth. Identify the fundamental structure, guiding principles, and core purpose.\n# Step 2:\nDive deeper to comprehend the intricate logic and inner workings that drive the project's functionality. As you explore, pay close attention to identifying the codebase's fundamental structure, underlying guiding principles, and its core purpose, ensuring you grasp both how and why the system is designed as it is. hen verify through a successful build.\n# Step 3:\nYou've now performed a successful build and familiarized yourself with the @codebase. Your next imperative is view the codebase through the lense of a brilliant (expert) SEO engineer, then create a short list of the most critical changes we need to make before bulding prod and deploying the website.\n# Step 4:\nYou've now performed a successful build and provided your SEO analysis of the codebase. Your next imperative is to leverage your knowledge of the codebase's fundamental structure and underlying guiding principles to ensure you're prepared for implementing the proposed SEO enhancements in a systematic order while retaining existing functionality and ensuring maximal clarity and alignment before making any changes.\n# Step 5:\nPlease systematically incorporate your proposed enhancements while ensuring existing functionality is retained, clarity is maximized, and alignment/coherence is maintained.\n# Step 6:\nPlease conduct a thorough final review to confirm seamless integration of all enhancements, ensuring that it is **truly** prepared for deployment.\n```\n\n## Goal\n\nThe new sequence i'm tasking you to create needs to adhere to the established principles of the system, and should be able to take content-specific inputs and \"shape them\" into generalized prompts specifically (and meticulously) designed for use with autonomous agents. here's an example input:\n```\nllm_instructions_for_sublimetext_plugin='''You’re an expert Sublime Text plugin developer with a deep understanding of Python and the Sublime Text API. You specialize in creating intuitive and efficient plugins that enhance user productivity and streamline workflows. You possess deep knowledge of the Sublime Text API and its ecosystem (and best practices), along with a strong understanding of user needs and developer workflows.\\n\\n- Sublime Layout\\n    > Lagre Layouts\\n        > Lagre Regelsett knyttet til Layout (hvilke tabs som skal hvor, hvilke som skal lukkes, osv)\\n            > F.eks. `alltid flytt [\"main.py\", \"*.py.bak\"] til layout.4.index.2`\\n\\n    > Kan videreutvikles til en standard som automatisk opprettholdes ved alle (.sublime-project) prosjekter'''\n```",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"goal_map\": [\n    {\n      \"Explicit Asks\": [\n        \"Create a new, generalized instruction sequence\",\n        \"The sequence must transform content-specific inputs into autonomous-agent-ready prompts/instructions\",\n        \"The sequence is for use with autonomous coding assistants (e.g., Cursor, VSCode+CLINE/Augment)\",\n        \"Prompts must be codebase-oriented, using @codebase or similar root notation\",\n        \"Sequence must adhere to established principles of system (examples provided)\",\n        \"Sequence must shape content-specific inputs (sample input provided) into generalized, agent-usable prompts\"\n      ]\n    },\n    {\n      \"Surface Hidden Assumptions\": [\n        \"The generalized instruction sequence should be easily adaptable to a wide variety of input types and codebase contexts\",\n        \"The sequence must be clear, deterministic, and actionable for autonomous agents\",\n        \"Steps must be granular and systematic, leading from raw user/content input to fully structured prompts\",\n        \"The target audience (agent) should have access to or awareness of @codebase or an equivalent abstraction of project structure\",\n        \"Output must avoid ambiguity and be robust to varying input complexity (from simple lists to freeform narrative)\",\n        \"The methodology must avoid introducing unnecessary complexity; it should simplify or clarify as needed per system principles\",\n        \"It should be possible to plug the resulting prompts directly into agent workflows without further manual editing\"\n      ]\n    },\n    {\n      \"Enumerate Sub-goals\": [\n        \"Define the high-level phases required to transform arbitrary content-specific inputs into agent-ready, codebase-oriented prompts\",\n        \"Identify the criteria and features that characterize a 'generalized prompt/instruction' for autonomous agents\",\n        \"Determine what structural or formatting elements must be applied to all outputs (e.g., use of @codebase, directory awareness, action orientation)\",\n        \"Establish sequence step(s) for parsing/understanding and extracting key requirements from the input\",\n        \"Establish step(s) for abstracting and generalizing the extracted requirements/goals from the input\",\n        \"Formulate steps to convert requirements into codebase-contextualized, actionable directives\",\n        \"Include requirement to ensure each instruction is unambiguous, explicit, and actionable by the agent\",\n        \"Explicitly validate and preserve all essential constraints, objectives, and organizational context from the original input\",\n        \"Integrate mechanisms for justifying, documenting, or logging changes/actions within the agent workflow as needed\",\n        \"Guarantee that the resultant prompt maintains or enhances clarity, coherence, and agent alignment with user intent\",\n        \"Enforce that no unnecessary stylistic or cosmetic changes are introduced unless tied to functional requirements\",\n        \"Embed (if relevant) mechanisms or placeholders for verification, validation, or review in the outputs\",\n        \"Create checklist(s) for ensuring the output is truly agent-ready according to the autonomous assistant’s operational context\",\n        \"Confirm seamless portability of the output prompt/instructions into new contexts or windows\"\n      ]\n    },\n    {\n      \"Reveal Blockers\": [\n        \"Ambiguity in what qualifies as a 'fully agent-ready' prompt beyond codebase-orientation—likely requires template or heuristic clarity\",\n        \"Lack of a universal input schema means the sequence must be robust to highly variable input structures and verbosity\",\n        \"Potentially insufficient information in some content-specific inputs to create fully actionable prompts—necessitating inference or fallback mechanisms\",\n        \"Difficulty balancing abstraction/generalization with retention of sufficient specificity for effective agent action\",\n        \"Possibility of edge-cases where the extracted requirements are insufficiently defined or contradict established codebase best practices\",\n        \"Need for a clear process to ensure functional equivalence or validation when changes are proposed or implemented by the prompts\",\n        \"Requirement to ensure that all context transfer and knowledge distillation steps are both lossless and concise\"\n      ]\n    }\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`",
      "step": "a",
      "title": "Instruction Converter",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"# lets try something new\\nyour new goal is to create a new generalized instruction sequence specifically designed to transform inputs into autonomous-agent-ready prompts/instructions, for use with autonomous coding assistants such as cursor, vscode+cline/augment/etc. these prompts are typically written in a \\\"codebase-oriented\\\" manner, where the `@codebase` represents the root (of a project/workspace/directory), examples:\\n```\\n- Identify all instances of structural redundancy within the codebase by defining criteria specific to architectural and implementation duplication or overlap.\\n- Survey the entire codebase to locate and enumerate all occurrences of structural redundancy, distinguishing these from non-structural (stylistic or cosmetic) issues.\\n- Eliminate all forms of identified structural redundancy while ensuring that no existing user-visible behavior, API contract, or edge-case handling is altered.\\n- Define and catalog all components, modules, files, and subsystems, distinguishing and marking each as essential or excess according to direct functional mapping.\\n- Consolidate and remove excess components while strictly preserving system function, minimizing peripheral impact.\\n- For every modification, justify and log the intention, action, and rationale within an audit/logging system that provides real-time traceability and is prepared for retroactive auditability.\\n- Implement an audit trail with logs that are timestamped, attributable, and tamper-evident, ensuring that every codebase change is properly documented and traceable to specific authors or automated processes.\\n- Prohibit cosmetic, stylistic, or naming changes unless they are inseparably tied to the removal of redundancy or bloat, and document any exceptions explicitly within the audit log.\\n- Enforce strict detection and flagging mechanisms to prevent disallowed cosmetic changes, allowing exceptions only when justified as essential for redundancy or bloat removal, with explicit rationale provided.\\n- Require comprehensive functional equivalence verification—using robust automated testing, benchmarking, or behavioral validation—for all modifications prior to acceptance.\\n- Establish rigorous criteria and tool-supported thresholds for successful functional equivalence, and automate testing and verification procedures.\\n- Automate roll-back and reversion mechanisms for any changes that fail functional equivalence or cause unintended side-effects, ensuring all reversion is prompt and complete.\\n- Develop and apply explicit minimal-impact criteria for all modification actions, constraining all interventions to only those addressing superfluous complexity.\\n- Mandate review and authorization checkpoints for each intended change, requiring concise rationale and authorization documentation at every intervention point.\\n- Ensure every modification is tightly scoped, minimizing the blast radius, and demonstrate that the action introduces no unnecessary impact beyond its core objective.\\n- Systematically and iteratively analyze all constituent elements of the codebase to detect and categorize all forms of redundancy, duplication, and obsolescence. Continuously identify and categorize all genuine redundancies and obsolescence while strictly prohibit unnecessary or cosmetic changes, with automated flagging and explicit justification required for any exceptions. Systematically and iteratively inspect all components within the system/environment to identify and remove elements that are unnecessary, duplicated, or outdated. Guarantee that all essential behaviors and interfaces remain unchanged by validating functional equivalence after each modification. Forbid changes that alter functionality unless directly required to remove redundancy.\\n- Map all references and usages of each element across all contexts.\\n- Analyze the entire codebase systematically and iteratively to maximize coherence, maintainability, and clarity by cataloging every element (file, module, function, asset, interface), mapping all references and usages, and detecting all forms of redundancy, duplication, and obsolescence—including exact and functional duplication and unused or outdated elements. Categorize findings using explicit, evidence-based metrics. Eliminate or consolidate only elements confirmed as redundant or obsolete through both automated and manual checks, ensuring no essential behavior or interface is altered. Enforce functional equivalence and preserve structural integrity after each change by performing comprehensive, codebase-wide validation and calibration using explicit, quantitative outputs (including git diff analysis before and after modifications). Prevent the introduction of new redundancy or inconsistency by continuously analyzing for and correcting systemic issues, ensuring systematic consistency across all codebase sections (e.g., /src/sections), and adhering to best practices throughout. Centralize repeated components or logic where high-value consolidation is possible without functional loss. Use the provided directory tree as the single source of truth for validation, and transparently document all processes, metrics, and validation outcomes. Prohibit modification of protected, legacy, or third-party code except if directly related to resolving redundancy or obsolescence.\\n- Maximize codebase coherence, maintainability, and clarity by systematically eliminating all forms of redundancy, duplication, and obsolescence, while ensuring functional equivalence and structural integrity. Prevent the persistence or inadvertent introduction of redundancy, duplication, obsolescence, or inconsistency within the codebase, and prohibit changes that alter essential behaviors or interfaces. Systematically maximize codebase coherence, maintainability, and clarity by eliminating all forms of redundancy, duplication, and obsolescence while strictly preserving functional equivalence, interface stability, and operational integrity across the entire codebase. Achieve maximal codebase coherence, maintainability, and clarity by systematically eliminating all forms of redundancy, duplication, and obsolescence, while preserving essential behaviors, interfaces, and structural integrity. Enforce continuous validation, consistency, and adherence to best practices throughout an iterative, evidence-driven consolidation and verification process.\\n- Please perform a rapid assessment of the project directory structure and identify the unique workflow and core functionality of the current utility. I'm particularly interested in a mermaid-diagram that shows how all the \\\"branches\\\" of the codebase converge. Utilize appropriate MCPs as necessary (e.g., 'filesystem-mcp').\\n- You're inside the current project and have access to the full codebase (and you interaction history with it). The current project/codebase is complex and is difficult to fully comprehend (when starting from scratch), and your contextwindow has grown large-so we need to find a way for you to express your accumulated knowledge in a way that i can copy and paste into a new context-window to transfer the most essential insights you've gathered throughout this (and previous) session(s).\\n- Aggregate all accumulated insights and knowledge about the current project/codebase from available context. Distill this information to identify core modules, architectural patterns, workflows, and points of integration. Condense the findings into a succinct yet comprehensive summary that highlights how these parts function together and converge toward the project's goals. Formulate the output as a single, optimized directive that encapsulates the codebase's integral components and operational cohesion. Present this synthesis as a set of clear, actionable commands or instructions, specifically phrased to instruct the coding assistant, not merely describe or report.\\n- Transform your accumulated, session-wide understanding of the current, complex codebase—gleaned from comprehensive context and full interaction history—into an actionable, instructive knowledge transfer protocol. Synthesize and distill all integral structural modules, essential architectural patterns, and key workflows, with special focus on their dynamic integration and functional convergence toward project objectives. Formulate a highly-optimized, directive-based set of precise commands targeted explicitly at coding assistants, ensuring exhaustive coverage of all major components, their relationships, and operational cohesion. The resulting synthesis must facilitate seamless portability: structure it for immediate copy-paste into any new context window, preserving the depth, interconnectivity, and systemic flow of the original codebase. Prioritize succinctness without compromising essential nuance, and express the entirety as a singular, comprehensive operational directive that primes the assistant for full-spectrum project navigation and intervention.\\n- Identify the fundamental structure, guiding principles, and core purpose of the project/codebase.\\n- Please familiarize yourself with the current @codebase. Your main imperative is to *actually* understand the codebase in a way where you're able to recognize meta-patterns and destroy complexity with simplicity and elegance, while capable of navigating through its *existing patterns and convergence* (never add unnecessary complexity). Deliver an ultra-concise project summary that rigorously defines scope, states objectives, and maximizes directional clarity. Provide a short summary of this project, including a bullet-point list of the core components and primary dependencies.\\n- Study the current codebase until you can identify core meta-patterns, and eliminate unnecessary complexity by applying simplicity within existing structures. Generate an ultra-concise project summary: define scope, objectives, and clarify direction. Summarize this project.\\n- Analyze the @codebase to uncover dominant patterns and remove complexity whenever possible, strictly adhering to present structures. Provide a succinct project overview with defined scope and explicit objectives. Include a brief project summary.\\n- Gain practical mastery over @codebase patterns, minimize complexity, and ensure all actions align with existing architecture. Supply a concise, clearly scoped project summary outlining objectives and key directions. Add a short summary.\\n- Absorb the current @codebase to the extent that you recognize meta-patterns and eradicate complexity, always favoring elegant simplicity. Navigate established patterns and points of convergence without adding unneeded complexity. Produce a rigorously concise project summary clearly stating scope and objectives for maximal directional clarity.\\n- Systematically dissect the codebase to extract core meta-patterns, eliminate all unnecessary complexity through elegant design, operate strictly within established architectural convergence, and deliver an ultra-concise project summary that rigorously defines scope and objectives with maximal clarity.\\n- Please familiarize yourself thoroughly with the current @codebase, beginning by systematically reviewing all markdown-files. Your core imperative is to *genuinely* understand the codebase to the degree where you can identify 'meta-patterns' and simplify complexity with elegance—always using and clarifying existing patterns, never introducing unnecessary complexity. Deliver an ultra-concise project summary that rigorously defines scope, states objectives, maximizes directional clarity, and highlights key meta-patterns discovered. Provide a short summary of this project.\\n- Achieve expert-level comprehension of the codebase by systematically analyzing all markdown files to extract meta-patterns, construct a robust contextual knowledgebase, and deliver an ultra-concise summary that rigorously defines scope and objectives, ensuring maximal clarity and alignment before making any changes.\\n- Fully internalize the @codebase to uncover meta-patterns and replace complexity with lucid simplicity. Remain aligned with existing convergence patterns, never increasing system intricacy. Generate a direct, sharply-focused summary that details scope and explicit objectives.\\n- Please thoroughly familiarize yourself with the current @codebase—your primary imperative is to understand the codebase so deeply that you could confidently make changes as if you were its original architect.\\n```\\n\\nhere's an example to show how generalized sequential prompts can be used effectively (on autonomous agents):\\n```\\n# Step 1:\\nFamiliarize yourself with the current @codebase. Comprehend the codebase in depth. Identify the fundamental structure, guiding principles, and core purpose.\\n# Step 2:\\nDive deeper to comprehend the intricate logic and inner workings that drive the project's functionality. As you explore, pay close attention to identifying the codebase's fundamental structure, underlying guiding principles, and its core purpose, ensuring you grasp both how and why the system is designed as it is. hen verify through a successful build.\\n# Step 3:\\nYou've now performed a successful build and familiarized yourself with the @codebase. Your next imperative is view the codebase through the lense of a brilliant (expert) SEO engineer, then create a short list of the most critical changes we need to make before bulding prod and deploying the website.\\n# Step 4:\\nYou've now performed a successful build and provided your SEO analysis of the codebase. Your next imperative is to leverage your knowledge of the codebase's fundamental structure and underlying guiding principles to ensure you're prepared for implementing the proposed SEO enhancements in a systematic order while retaining existing functionality and ensuring maximal clarity and alignment before making any changes.\\n# Step 5:\\nPlease systematically incorporate your proposed enhancements while ensuring existing functionality is retained, clarity is maximized, and alignment/coherence is maintained.\\n# Step 6:\\nPlease conduct a thorough final review to confirm seamless integration of all enhancements, ensuring that it is **truly** prepared for deployment.\\n```\\n\\n## Goal\\n\\nThe new sequence i'm tasking you to create needs to adhere to the established principles of the system, and should be able to take content-specific inputs and \\\"shape them\\\" into generalized prompts specifically (and meticulously) designed for use with autonomous agents. here's an example input:\\n```\\nllm_instructions_for_sublimetext_plugin='''You’re an expert Sublime Text plugin developer with a deep understanding of Python and the Sublime Text API. You specialize in creating intuitive and efficient plugins that enhance user productivity and streamline workflows. You possess deep knowledge of the Sublime Text API and its ecosystem (and best practices), along with a strong understanding of user needs and developer workflows.\\\\n\\\\n- Sublime Layout\\\\n    > Lagre Layouts\\\\n        > Lagre Regelsett knyttet til Layout (hvilke tabs som skal hvor, hvilke som skal lukkes, osv)\\\\n            > F.eks. `alltid flytt [\\\"main.py\\\", \\\"*.py.bak\\\"] til layout.4.index.2`\\\\n\\\\n    > Kan videreutvikles til en standard som automatisk opprettholdes ved alle (.sublime-project) prosjekter'''\\n```\\\"```'\\n\\n\",\n  \"goal_map\": [\n    {\n      \"Explicit Asks\": [\n        \"Create a new, generalized instruction sequence\",\n        \"The sequence must transform content-specific inputs into autonomous-agent-ready prompts/instructions\",\n        \"The sequence is for use with autonomous coding assistants (e.g., Cursor, VSCode+CLINE/Augment)\",\n        \"Prompts must be codebase-oriented, using @codebase or similar root notation\",\n        \"Sequence must adhere to established principles of system (examples provided)\",\n        \"Sequence must shape content-specific inputs (sample input provided) into generalized, agent-usable prompts\"\n      ]\n    },\n    {\n      \"Surface Hidden Assumptions\": [\n        \"The generalized instruction sequence should be easily adaptable to a wide variety of input types and codebase contexts\",\n        \"The sequence must be clear, deterministic, and actionable for autonomous agents\",\n        \"Steps must be granular and systematic, leading from raw user/content input to fully structured prompts\",\n        \"The target audience (agent) should have access to or awareness of @codebase or an equivalent abstraction of project structure\",\n        \"Output must avoid ambiguity and be robust to varying input complexity (from simple lists to freeform narrative)\",\n        \"The methodology must avoid introducing unnecessary complexity; it should simplify or clarify as needed per system principles\",\n        \"It should be possible to plug the resulting prompts directly into agent workflows without further manual editing\"\n      ]\n    },\n    {\n      \"Enumerate Sub-goals\": [\n        \"Define the high-level phases required to transform arbitrary content-specific inputs into agent-ready, codebase-oriented prompts\",\n        \"Identify the criteria and features that characterize a 'generalized prompt/instruction' for autonomous agents\",\n        \"Determine what structural or formatting elements must be applied to all outputs (e.g., use of @codebase, directory awareness, action orientation)\",\n        \"Establish sequence step(s) for parsing/understanding and extracting key requirements from the input\",\n        \"Establish step(s) for abstracting and generalizing the extracted requirements/goals from the input\",\n        \"Formulate steps to convert requirements into codebase-contextualized, actionable directives\",\n        \"Include requirement to ensure each instruction is unambiguous, explicit, and actionable by the agent\",\n        \"Explicitly validate and preserve all essential constraints, objectives, and organizational context from the original input\",\n        \"Integrate mechanisms for justifying, documenting, or logging changes/actions within the agent workflow as needed\",\n        \"Guarantee that the resultant prompt maintains or enhances clarity, coherence, and agent alignment with user intent\",\n        \"Enforce that no unnecessary stylistic or cosmetic changes are introduced unless tied to functional requirements\",\n        \"Embed (if relevant) mechanisms or placeholders for verification, validation, or review in the outputs\",\n        \"Create checklist(s) for ensuring the output is truly agent-ready according to the autonomous assistant’s operational context\",\n        \"Confirm seamless portability of the output prompt/instructions into new contexts or windows\"\n      ]\n    },\n    {\n      \"Reveal Blockers\": [\n        \"Ambiguity in what qualifies as a 'fully agent-ready' prompt beyond codebase-orientation—likely requires template or heuristic clarity\",\n        \"Lack of a universal input schema means the sequence must be robust to highly variable input structures and verbosity\",\n        \"Potentially insufficient information in some content-specific inputs to create fully actionable prompts—necessitating inference or fallback mechanisms\",\n        \"Difficulty balancing abstraction/generalization with retention of sufficient specificity for effective agent action\",\n        \"Possibility of edge-cases where the extracted requirements are insufficiently defined or contradict established codebase best practices\",\n        \"Need for a clear process to ensure functional equivalence or validation when changes are proposed or implemented by the prompts\",\n        \"Requirement to ensure that all context transfer and knowledge distillation steps are both lossless and concise\"\n      ]\n    }\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"instruction_format\": \"Develop a generalized, step-by-step instruction sequence to transform arbitrary content-specific inputs into codebase-oriented, autonomous-agent-ready prompts suitable for coding assistants (e.g., Cursor, VSCode+CLINE/Augment). Adhere strictly to established codebase principles, ensuring output is explicit, actionable, and format-consistent with agent workflows. Follow this structure:\\n\\n1. Parse and analyze the input to identify all explicit requirements, constraints, goals, and implicit objectives relevant to the codebase context.\\n2. Extract core requirements, distinguishing between essential structural, functional, and architectural directives. Discard stylistic or cosmetic instructions unless they are functionally bound.\\n3. Abstract and generalize the extracted requirements, translating specific input details into platform-agnostic, codebase-referential actions using '@codebase' or equivalent notation to anchor all references within the project structure.\\n4. Reformulate all requirements as direct, imperative, agent-oriented commands. Ensure each instruction is unambiguous, actionable, and operationally explicit—ready for direct agent execution or integration.\\n5. Sequence the commands to provide a clear, logical flow from analysis and requirement identification through action execution and validation. Maintain granularity and deterministic stepwise logic throughout.\\n6. Embed steps for logging, justification, or auditability of all codebase modifications, explicitly instructing agents to document rationale, actions taken, and results within an auditable system.\\n7. Integrate checks to prohibit non-functional or cosmetic changes except where inseparable from achieving functional objectives, and require exception documentation as necessary.\\n8. Specify validation and verification procedures (e.g., test automation or equivalence checks) to ensure all agent actions retain codebase functionality, interface integrity, and behavioral fidelity post-modification.\\n9. Provide explicit guidelines or placeholders for documentation of meta-patterns, summary synthesis, or knowledge transfers as needed to maximize clarity, maintainability, and project alignment.\\n10. Finalize output as a singular, portable prompt or instruction set that can be copy-pasted into agent workflows, ensuring full contextual integrity, systematic flow, and immediate agent operability. Include a checklist for agent-readiness covering clarity, constraint adherence, validation mechanisms, and structural alignment with codebase best practices.\"\n}",
        }
      }
    },
      {
      "instruction": "# Hard Critique\n\nYour goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:\n\n`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,2.0], flaw_analysis:str, alternative_enhancements:str[3]}}`",
      "step": "a",
      "title": "Hard Critique",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"# lets try something new\\nyour new goal is to create a new generalized instruction sequence specifically designed to transform inputs into autonomous-agent-ready prompts/instructions, for use with autonomous coding assistants such as cursor, vscode+cline/augment/etc. these prompts are typically written in a \\\"codebase-oriented\\\" manner, where the `@codebase` represents the root (of a project/workspace/directory), examples:\\n```\\n- Identify all instances of structural redundancy within the codebase by defining criteria specific to architectural and implementation duplication or overlap.\\n- Survey the entire codebase to locate and enumerate all occurrences of structural redundancy, distinguishing these from non-structural (stylistic or cosmetic) issues.\\n- Eliminate all forms of identified structural redundancy while ensuring that no existing user-visible behavior, API contract, or edge-case handling is altered.\\n- Define and catalog all components, modules, files, and subsystems, distinguishing and marking each as essential or excess according to direct functional mapping.\\n- Consolidate and remove excess components while strictly preserving system function, minimizing peripheral impact.\\n- For every modification, justify and log the intention, action, and rationale within an audit/logging system that provides real-time traceability and is prepared for retroactive auditability.\\n- Implement an audit trail with logs that are timestamped, attributable, and tamper-evident, ensuring that every codebase change is properly documented and traceable to specific authors or automated processes.\\n- Prohibit cosmetic, stylistic, or naming changes unless they are inseparably tied to the removal of redundancy or bloat, and document any exceptions explicitly within the audit log.\\n- Enforce strict detection and flagging mechanisms to prevent disallowed cosmetic changes, allowing exceptions only when justified as essential for redundancy or bloat removal, with explicit rationale provided.\\n- Require comprehensive functional equivalence verification—using robust automated testing, benchmarking, or behavioral validation—for all modifications prior to acceptance.\\n- Establish rigorous criteria and tool-supported thresholds for successful functional equivalence, and automate testing and verification procedures.\\n- Automate roll-back and reversion mechanisms for any changes that fail functional equivalence or cause unintended side-effects, ensuring all reversion is prompt and complete.\\n- Develop and apply explicit minimal-impact criteria for all modification actions, constraining all interventions to only those addressing superfluous complexity.\\n- Mandate review and authorization checkpoints for each intended change, requiring concise rationale and authorization documentation at every intervention point.\\n- Ensure every modification is tightly scoped, minimizing the blast radius, and demonstrate that the action introduces no unnecessary impact beyond its core objective.\\n- Systematically and iteratively analyze all constituent elements of the codebase to detect and categorize all forms of redundancy, duplication, and obsolescence. Continuously identify and categorize all genuine redundancies and obsolescence while strictly prohibit unnecessary or cosmetic changes, with automated flagging and explicit justification required for any exceptions. Systematically and iteratively inspect all components within the system/environment to identify and remove elements that are unnecessary, duplicated, or outdated. Guarantee that all essential behaviors and interfaces remain unchanged by validating functional equivalence after each modification. Forbid changes that alter functionality unless directly required to remove redundancy.\\n- Map all references and usages of each element across all contexts.\\n- Analyze the entire codebase systematically and iteratively to maximize coherence, maintainability, and clarity by cataloging every element (file, module, function, asset, interface), mapping all references and usages, and detecting all forms of redundancy, duplication, and obsolescence—including exact and functional duplication and unused or outdated elements. Categorize findings using explicit, evidence-based metrics. Eliminate or consolidate only elements confirmed as redundant or obsolete through both automated and manual checks, ensuring no essential behavior or interface is altered. Enforce functional equivalence and preserve structural integrity after each change by performing comprehensive, codebase-wide validation and calibration using explicit, quantitative outputs (including git diff analysis before and after modifications). Prevent the introduction of new redundancy or inconsistency by continuously analyzing for and correcting systemic issues, ensuring systematic consistency across all codebase sections (e.g., /src/sections), and adhering to best practices throughout. Centralize repeated components or logic where high-value consolidation is possible without functional loss. Use the provided directory tree as the single source of truth for validation, and transparently document all processes, metrics, and validation outcomes. Prohibit modification of protected, legacy, or third-party code except if directly related to resolving redundancy or obsolescence.\\n- Maximize codebase coherence, maintainability, and clarity by systematically eliminating all forms of redundancy, duplication, and obsolescence, while ensuring functional equivalence and structural integrity. Prevent the persistence or inadvertent introduction of redundancy, duplication, obsolescence, or inconsistency within the codebase, and prohibit changes that alter essential behaviors or interfaces. Systematically maximize codebase coherence, maintainability, and clarity by eliminating all forms of redundancy, duplication, and obsolescence while strictly preserving functional equivalence, interface stability, and operational integrity across the entire codebase. Achieve maximal codebase coherence, maintainability, and clarity by systematically eliminating all forms of redundancy, duplication, and obsolescence, while preserving essential behaviors, interfaces, and structural integrity. Enforce continuous validation, consistency, and adherence to best practices throughout an iterative, evidence-driven consolidation and verification process.\\n- Please perform a rapid assessment of the project directory structure and identify the unique workflow and core functionality of the current utility. I'm particularly interested in a mermaid-diagram that shows how all the \\\"branches\\\" of the codebase converge. Utilize appropriate MCPs as necessary (e.g., 'filesystem-mcp').\\n- You're inside the current project and have access to the full codebase (and you interaction history with it). The current project/codebase is complex and is difficult to fully comprehend (when starting from scratch), and your contextwindow has grown large-so we need to find a way for you to express your accumulated knowledge in a way that i can copy and paste into a new context-window to transfer the most essential insights you've gathered throughout this (and previous) session(s).\\n- Aggregate all accumulated insights and knowledge about the current project/codebase from available context. Distill this information to identify core modules, architectural patterns, workflows, and points of integration. Condense the findings into a succinct yet comprehensive summary that highlights how these parts function together and converge toward the project's goals. Formulate the output as a single, optimized directive that encapsulates the codebase's integral components and operational cohesion. Present this synthesis as a set of clear, actionable commands or instructions, specifically phrased to instruct the coding assistant, not merely describe or report.\\n- Transform your accumulated, session-wide understanding of the current, complex codebase—gleaned from comprehensive context and full interaction history—into an actionable, instructive knowledge transfer protocol. Synthesize and distill all integral structural modules, essential architectural patterns, and key workflows, with special focus on their dynamic integration and functional convergence toward project objectives. Formulate a highly-optimized, directive-based set of precise commands targeted explicitly at coding assistants, ensuring exhaustive coverage of all major components, their relationships, and operational cohesion. The resulting synthesis must facilitate seamless portability: structure it for immediate copy-paste into any new context window, preserving the depth, interconnectivity, and systemic flow of the original codebase. Prioritize succinctness without compromising essential nuance, and express the entirety as a singular, comprehensive operational directive that primes the assistant for full-spectrum project navigation and intervention.\\n- Identify the fundamental structure, guiding principles, and core purpose of the project/codebase.\\n- Please familiarize yourself with the current @codebase. Your main imperative is to *actually* understand the codebase in a way where you're able to recognize meta-patterns and destroy complexity with simplicity and elegance, while capable of navigating through its *existing patterns and convergence* (never add unnecessary complexity). Deliver an ultra-concise project summary that rigorously defines scope, states objectives, and maximizes directional clarity. Provide a short summary of this project, including a bullet-point list of the core components and primary dependencies.\\n- Study the current codebase until you can identify core meta-patterns, and eliminate unnecessary complexity by applying simplicity within existing structures. Generate an ultra-concise project summary: define scope, objectives, and clarify direction. Summarize this project.\\n- Analyze the @codebase to uncover dominant patterns and remove complexity whenever possible, strictly adhering to present structures. Provide a succinct project overview with defined scope and explicit objectives. Include a brief project summary.\\n- Gain practical mastery over @codebase patterns, minimize complexity, and ensure all actions align with existing architecture. Supply a concise, clearly scoped project summary outlining objectives and key directions. Add a short summary.\\n- Absorb the current @codebase to the extent that you recognize meta-patterns and eradicate complexity, always favoring elegant simplicity. Navigate established patterns and points of convergence without adding unneeded complexity. Produce a rigorously concise project summary clearly stating scope and objectives for maximal directional clarity.\\n- Systematically dissect the codebase to extract core meta-patterns, eliminate all unnecessary complexity through elegant design, operate strictly within established architectural convergence, and deliver an ultra-concise project summary that rigorously defines scope and objectives with maximal clarity.\\n- Please familiarize yourself thoroughly with the current @codebase, beginning by systematically reviewing all markdown-files. Your core imperative is to *genuinely* understand the codebase to the degree where you can identify 'meta-patterns' and simplify complexity with elegance—always using and clarifying existing patterns, never introducing unnecessary complexity. Deliver an ultra-concise project summary that rigorously defines scope, states objectives, maximizes directional clarity, and highlights key meta-patterns discovered. Provide a short summary of this project.\\n- Achieve expert-level comprehension of the codebase by systematically analyzing all markdown files to extract meta-patterns, construct a robust contextual knowledgebase, and deliver an ultra-concise summary that rigorously defines scope and objectives, ensuring maximal clarity and alignment before making any changes.\\n- Fully internalize the @codebase to uncover meta-patterns and replace complexity with lucid simplicity. Remain aligned with existing convergence patterns, never increasing system intricacy. Generate a direct, sharply-focused summary that details scope and explicit objectives.\\n- Please thoroughly familiarize yourself with the current @codebase—your primary imperative is to understand the codebase so deeply that you could confidently make changes as if you were its original architect.\\n```\\n\\nhere's an example to show how generalized sequential prompts can be used effectively (on autonomous agents):\\n```\\n# Step 1:\\nFamiliarize yourself with the current @codebase. Comprehend the codebase in depth. Identify the fundamental structure, guiding principles, and core purpose.\\n# Step 2:\\nDive deeper to comprehend the intricate logic and inner workings that drive the project's functionality. As you explore, pay close attention to identifying the codebase's fundamental structure, underlying guiding principles, and its core purpose, ensuring you grasp both how and why the system is designed as it is. hen verify through a successful build.\\n# Step 3:\\nYou've now performed a successful build and familiarized yourself with the @codebase. Your next imperative is view the codebase through the lense of a brilliant (expert) SEO engineer, then create a short list of the most critical changes we need to make before bulding prod and deploying the website.\\n# Step 4:\\nYou've now performed a successful build and provided your SEO analysis of the codebase. Your next imperative is to leverage your knowledge of the codebase's fundamental structure and underlying guiding principles to ensure you're prepared for implementing the proposed SEO enhancements in a systematic order while retaining existing functionality and ensuring maximal clarity and alignment before making any changes.\\n# Step 5:\\nPlease systematically incorporate your proposed enhancements while ensuring existing functionality is retained, clarity is maximized, and alignment/coherence is maintained.\\n# Step 6:\\nPlease conduct a thorough final review to confirm seamless integration of all enhancements, ensuring that it is **truly** prepared for deployment.\\n```\\n\\n## Goal\\n\\nThe new sequence i'm tasking you to create needs to adhere to the established principles of the system, and should be able to take content-specific inputs and \\\"shape them\\\" into generalized prompts specifically (and meticulously) designed for use with autonomous agents. here's an example input:\\n```\\nllm_instructions_for_sublimetext_plugin='''You’re an expert Sublime Text plugin developer with a deep understanding of Python and the Sublime Text API. You specialize in creating intuitive and efficient plugins that enhance user productivity and streamline workflows. You possess deep knowledge of the Sublime Text API and its ecosystem (and best practices), along with a strong understanding of user needs and developer workflows.\\\\n\\\\n- Sublime Layout\\\\n    > Lagre Layouts\\\\n        > Lagre Regelsett knyttet til Layout (hvilke tabs som skal hvor, hvilke som skal lukkes, osv)\\\\n            > F.eks. `alltid flytt [\\\"main.py\\\", \\\"*.py.bak\\\"] til layout.4.index.2`\\\\n\\\\n    > Kan videreutvikles til en standard som automatisk opprettholdes ved alle (.sublime-project) prosjekter'''\\n```\\\"```'\\n\\n\",\n  \"instruction_format\": \"Develop a generalized, step-by-step instruction sequence to transform arbitrary content-specific inputs into codebase-oriented, autonomous-agent-ready prompts suitable for coding assistants (e.g., Cursor, VSCode+CLINE/Augment). Adhere strictly to established codebase principles, ensuring output is explicit, actionable, and format-consistent with agent workflows. Follow this structure:\\n\\n1. Parse and analyze the input to identify all explicit requirements, constraints, goals, and implicit objectives relevant to the codebase context.\\n2. Extract core requirements, distinguishing between essential structural, functional, and architectural directives. Discard stylistic or cosmetic instructions unless they are functionally bound.\\n3. Abstract and generalize the extracted requirements, translating specific input details into platform-agnostic, codebase-referential actions using '@codebase' or equivalent notation to anchor all references within the project structure.\\n4. Reformulate all requirements as direct, imperative, agent-oriented commands. Ensure each instruction is unambiguous, actionable, and operationally explicit—ready for direct agent execution or integration.\\n5. Sequence the commands to provide a clear, logical flow from analysis and requirement identification through action execution and validation. Maintain granularity and deterministic stepwise logic throughout.\\n6. Embed steps for logging, justification, or auditability of all codebase modifications, explicitly instructing agents to document rationale, actions taken, and results within an auditable system.\\n7. Integrate checks to prohibit non-functional or cosmetic changes except where inseparable from achieving functional objectives, and require exception documentation as necessary.\\n8. Specify validation and verification procedures (e.g., test automation or equivalence checks) to ensure all agent actions retain codebase functionality, interface integrity, and behavioral fidelity post-modification.\\n9. Provide explicit guidelines or placeholders for documentation of meta-patterns, summary synthesis, or knowledge transfers as needed to maximize clarity, maintainability, and project alignment.\\n10. Finalize output as a singular, portable prompt or instruction set that can be copy-pasted into agent workflows, ensuring full contextual integrity, systematic flow, and immediate agent operability. Include a checklist for agent-readiness covering clarity, constraint adherence, validation mechanisms, and structural alignment with codebase best practices.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"enhancement_score\": 1.2,\n  \"flaw_analysis\": \"The so-called enhanced version aggressively narrows the operational scope, grafting a rigid, step-by-step scaffold atop the original prompt's highly nuanced, domain-rich directives. This results in dramatic information loss and distillation of intent. Key meta-patterns, historical context accumulation, iterative analysis flows, and the original's precise attention to codebase-specific complexity (such as codebase context windows, forbidden legacy changes, and audit trail nuances) are displaced by generic, process-oriented steps. The enhancement strips the substance of advanced codebase manipulation (e.g., difference between cosmetic and structural changes, deep integration of behavioral verification, meticulous role of directory trees, context transfer mechanics) and replaces it with a procedural blueprint devoid of any of the original's rich, embedded examples, edge-case handling, or actual contextual awareness. Critical loss of tone—transforming from a field-expert, systems-oriented voice to a bland, technical procedural register—destroys the actionable sophistication and nuanced guidance present in the source. The ambiguity score is 7/10: the instruction format is ambiguous in its interpretation of context, codebase boundaries, platform notions, and validation rigor compared to the original's unambiguous commands. The style degradation is severe: all expert-level granularity and contextual sensitivity are replaced by mechanistic, context-agnostic instructions that fail to map directly to codebase-specific operations required of autonomous agents. The override of expert tone with sterile, middle-manager prose causes major impact dilution, while the omission of recursive pattern recognition and cross-session context transfer fatally undermines the intended expert-system interactivity. Numerous coherence weaknesses abound: logical leaps between steps, lack of enforcement mechanisms for functional parity, and missing explicit reference to evidence-based metrics, iterative analysis, and domain-specific constraint enforcement. This overhaul demonstrates a failure to retain the original's directive strength, level of detail, and direct applicability for advanced code navigation, syntheses, and meta-pattern extraction.\",\n  \"alternative_enhancements\": [\n    \"Preserve the original's detailed meta-pattern extraction and session-wide knowledge transfer focus, while encapsulating these into a more streamlined but still contextually rich sequence of agent directives that mandate explicit structural mapping, iterative validation, and audit-based modifications.\",\n    \"Embed concrete examples from the original (including codebase reference structures, merging of directives, and explicit differentiation between functional and cosmetic changes) within a hierarchical instruction set, ensuring every step maintains operational context, edge-case coverage, and history-aware syntheses.\",\n    \"Retain expert-system language and sophistication by restructuring the original prompt as a modular template, separating initial context absorption, structural analysis, change-validation, and cross-session knowledge transfer, while layering in explicit agent-facing imperatives and real-world codebase command syntax suitable for autonomous agent integration.\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Problem Exploder\n\nYour goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\n\n`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`",
      "step": "a",
      "title": "Problem Exploder",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"# lets try something new\\nyour new goal is to create a new generalized instruction sequence specifically designed to transform inputs into autonomous-agent-ready prompts/instructions, for use with autonomous coding assistants such as cursor, vscode+cline/augment/etc. these prompts are typically written in a \\\"codebase-oriented\\\" manner, where the `@codebase` represents the root (of a project/workspace/directory), examples:\\n```\\n- Identify all instances of structural redundancy within the codebase by defining criteria specific to architectural and implementation duplication or overlap.\\n- Survey the entire codebase to locate and enumerate all occurrences of structural redundancy, distinguishing these from non-structural (stylistic or cosmetic) issues.\\n- Eliminate all forms of identified structural redundancy while ensuring that no existing user-visible behavior, API contract, or edge-case handling is altered.\\n- Define and catalog all components, modules, files, and subsystems, distinguishing and marking each as essential or excess according to direct functional mapping.\\n- Consolidate and remove excess components while strictly preserving system function, minimizing peripheral impact.\\n- For every modification, justify and log the intention, action, and rationale within an audit/logging system that provides real-time traceability and is prepared for retroactive auditability.\\n- Implement an audit trail with logs that are timestamped, attributable, and tamper-evident, ensuring that every codebase change is properly documented and traceable to specific authors or automated processes.\\n- Prohibit cosmetic, stylistic, or naming changes unless they are inseparably tied to the removal of redundancy or bloat, and document any exceptions explicitly within the audit log.\\n- Enforce strict detection and flagging mechanisms to prevent disallowed cosmetic changes, allowing exceptions only when justified as essential for redundancy or bloat removal, with explicit rationale provided.\\n- Require comprehensive functional equivalence verification—using robust automated testing, benchmarking, or behavioral validation—for all modifications prior to acceptance.\\n- Establish rigorous criteria and tool-supported thresholds for successful functional equivalence, and automate testing and verification procedures.\\n- Automate roll-back and reversion mechanisms for any changes that fail functional equivalence or cause unintended side-effects, ensuring all reversion is prompt and complete.\\n- Develop and apply explicit minimal-impact criteria for all modification actions, constraining all interventions to only those addressing superfluous complexity.\\n- Mandate review and authorization checkpoints for each intended change, requiring concise rationale and authorization documentation at every intervention point.\\n- Ensure every modification is tightly scoped, minimizing the blast radius, and demonstrate that the action introduces no unnecessary impact beyond its core objective.\\n- Systematically and iteratively analyze all constituent elements of the codebase to detect and categorize all forms of redundancy, duplication, and obsolescence. Continuously identify and categorize all genuine redundancies and obsolescence while strictly prohibit unnecessary or cosmetic changes, with automated flagging and explicit justification required for any exceptions. Systematically and iteratively inspect all components within the system/environment to identify and remove elements that are unnecessary, duplicated, or outdated. Guarantee that all essential behaviors and interfaces remain unchanged by validating functional equivalence after each modification. Forbid changes that alter functionality unless directly required to remove redundancy.\\n- Map all references and usages of each element across all contexts.\\n- Analyze the entire codebase systematically and iteratively to maximize coherence, maintainability, and clarity by cataloging every element (file, module, function, asset, interface), mapping all references and usages, and detecting all forms of redundancy, duplication, and obsolescence—including exact and functional duplication and unused or outdated elements. Categorize findings using explicit, evidence-based metrics. Eliminate or consolidate only elements confirmed as redundant or obsolete through both automated and manual checks, ensuring no essential behavior or interface is altered. Enforce functional equivalence and preserve structural integrity after each change by performing comprehensive, codebase-wide validation and calibration using explicit, quantitative outputs (including git diff analysis before and after modifications). Prevent the introduction of new redundancy or inconsistency by continuously analyzing for and correcting systemic issues, ensuring systematic consistency across all codebase sections (e.g., /src/sections), and adhering to best practices throughout. Centralize repeated components or logic where high-value consolidation is possible without functional loss. Use the provided directory tree as the single source of truth for validation, and transparently document all processes, metrics, and validation outcomes. Prohibit modification of protected, legacy, or third-party code except if directly related to resolving redundancy or obsolescence.\\n- Maximize codebase coherence, maintainability, and clarity by systematically eliminating all forms of redundancy, duplication, and obsolescence, while ensuring functional equivalence and structural integrity. Prevent the persistence or inadvertent introduction of redundancy, duplication, obsolescence, or inconsistency within the codebase, and prohibit changes that alter essential behaviors or interfaces. Systematically maximize codebase coherence, maintainability, and clarity by eliminating all forms of redundancy, duplication, and obsolescence while strictly preserving functional equivalence, interface stability, and operational integrity across the entire codebase. Achieve maximal codebase coherence, maintainability, and clarity by systematically eliminating all forms of redundancy, duplication, and obsolescence, while preserving essential behaviors, interfaces, and structural integrity. Enforce continuous validation, consistency, and adherence to best practices throughout an iterative, evidence-driven consolidation and verification process.\\n- Please perform a rapid assessment of the project directory structure and identify the unique workflow and core functionality of the current utility. I'm particularly interested in a mermaid-diagram that shows how all the \\\"branches\\\" of the codebase converge. Utilize appropriate MCPs as necessary (e.g., 'filesystem-mcp').\\n- You're inside the current project and have access to the full codebase (and you interaction history with it). The current project/codebase is complex and is difficult to fully comprehend (when starting from scratch), and your contextwindow has grown large-so we need to find a way for you to express your accumulated knowledge in a way that i can copy and paste into a new context-window to transfer the most essential insights you've gathered throughout this (and previous) session(s).\\n- Aggregate all accumulated insights and knowledge about the current project/codebase from available context. Distill this information to identify core modules, architectural patterns, workflows, and points of integration. Condense the findings into a succinct yet comprehensive summary that highlights how these parts function together and converge toward the project's goals. Formulate the output as a single, optimized directive that encapsulates the codebase's integral components and operational cohesion. Present this synthesis as a set of clear, actionable commands or instructions, specifically phrased to instruct the coding assistant, not merely describe or report.\\n- Transform your accumulated, session-wide understanding of the current, complex codebase—gleaned from comprehensive context and full interaction history—into an actionable, instructive knowledge transfer protocol. Synthesize and distill all integral structural modules, essential architectural patterns, and key workflows, with special focus on their dynamic integration and functional convergence toward project objectives. Formulate a highly-optimized, directive-based set of precise commands targeted explicitly at coding assistants, ensuring exhaustive coverage of all major components, their relationships, and operational cohesion. The resulting synthesis must facilitate seamless portability: structure it for immediate copy-paste into any new context window, preserving the depth, interconnectivity, and systemic flow of the original codebase. Prioritize succinctness without compromising essential nuance, and express the entirety as a singular, comprehensive operational directive that primes the assistant for full-spectrum project navigation and intervention.\\n- Identify the fundamental structure, guiding principles, and core purpose of the project/codebase.\\n- Please familiarize yourself with the current @codebase. Your main imperative is to *actually* understand the codebase in a way where you're able to recognize meta-patterns and destroy complexity with simplicity and elegance, while capable of navigating through its *existing patterns and convergence* (never add unnecessary complexity). Deliver an ultra-concise project summary that rigorously defines scope, states objectives, and maximizes directional clarity. Provide a short summary of this project, including a bullet-point list of the core components and primary dependencies.\\n- Study the current codebase until you can identify core meta-patterns, and eliminate unnecessary complexity by applying simplicity within existing structures. Generate an ultra-concise project summary: define scope, objectives, and clarify direction. Summarize this project.\\n- Analyze the @codebase to uncover dominant patterns and remove complexity whenever possible, strictly adhering to present structures. Provide a succinct project overview with defined scope and explicit objectives. Include a brief project summary.\\n- Gain practical mastery over @codebase patterns, minimize complexity, and ensure all actions align with existing architecture. Supply a concise, clearly scoped project summary outlining objectives and key directions. Add a short summary.\\n- Absorb the current @codebase to the extent that you recognize meta-patterns and eradicate complexity, always favoring elegant simplicity. Navigate established patterns and points of convergence without adding unneeded complexity. Produce a rigorously concise project summary clearly stating scope and objectives for maximal directional clarity.\\n- Systematically dissect the codebase to extract core meta-patterns, eliminate all unnecessary complexity through elegant design, operate strictly within established architectural convergence, and deliver an ultra-concise project summary that rigorously defines scope and objectives with maximal clarity.\\n- Please familiarize yourself thoroughly with the current @codebase, beginning by systematically reviewing all markdown-files. Your core imperative is to *genuinely* understand the codebase to the degree where you can identify 'meta-patterns' and simplify complexity with elegance—always using and clarifying existing patterns, never introducing unnecessary complexity. Deliver an ultra-concise project summary that rigorously defines scope, states objectives, maximizes directional clarity, and highlights key meta-patterns discovered. Provide a short summary of this project.\\n- Achieve expert-level comprehension of the codebase by systematically analyzing all markdown files to extract meta-patterns, construct a robust contextual knowledgebase, and deliver an ultra-concise summary that rigorously defines scope and objectives, ensuring maximal clarity and alignment before making any changes.\\n- Fully internalize the @codebase to uncover meta-patterns and replace complexity with lucid simplicity. Remain aligned with existing convergence patterns, never increasing system intricacy. Generate a direct, sharply-focused summary that details scope and explicit objectives.\\n- Please thoroughly familiarize yourself with the current @codebase—your primary imperative is to understand the codebase so deeply that you could confidently make changes as if you were its original architect.\\n```\\n\\nhere's an example to show how generalized sequential prompts can be used effectively (on autonomous agents):\\n```\\n# Step 1:\\nFamiliarize yourself with the current @codebase. Comprehend the codebase in depth. Identify the fundamental structure, guiding principles, and core purpose.\\n# Step 2:\\nDive deeper to comprehend the intricate logic and inner workings that drive the project's functionality. As you explore, pay close attention to identifying the codebase's fundamental structure, underlying guiding principles, and its core purpose, ensuring you grasp both how and why the system is designed as it is. hen verify through a successful build.\\n# Step 3:\\nYou've now performed a successful build and familiarized yourself with the @codebase. Your next imperative is view the codebase through the lense of a brilliant (expert) SEO engineer, then create a short list of the most critical changes we need to make before bulding prod and deploying the website.\\n# Step 4:\\nYou've now performed a successful build and provided your SEO analysis of the codebase. Your next imperative is to leverage your knowledge of the codebase's fundamental structure and underlying guiding principles to ensure you're prepared for implementing the proposed SEO enhancements in a systematic order while retaining existing functionality and ensuring maximal clarity and alignment before making any changes.\\n# Step 5:\\nPlease systematically incorporate your proposed enhancements while ensuring existing functionality is retained, clarity is maximized, and alignment/coherence is maintained.\\n# Step 6:\\nPlease conduct a thorough final review to confirm seamless integration of all enhancements, ensuring that it is **truly** prepared for deployment.\\n```\\n\\n## Goal\\n\\nThe new sequence i'm tasking you to create needs to adhere to the established principles of the system, and should be able to take content-specific inputs and \\\"shape them\\\" into generalized prompts specifically (and meticulously) designed for use with autonomous agents. here's an example input:\\n```\\nllm_instructions_for_sublimetext_plugin='''You’re an expert Sublime Text plugin developer with a deep understanding of Python and the Sublime Text API. You specialize in creating intuitive and efficient plugins that enhance user productivity and streamline workflows. You possess deep knowledge of the Sublime Text API and its ecosystem (and best practices), along with a strong understanding of user needs and developer workflows.\\\\n\\\\n- Sublime Layout\\\\n    > Lagre Layouts\\\\n        > Lagre Regelsett knyttet til Layout (hvilke tabs som skal hvor, hvilke som skal lukkes, osv)\\\\n            > F.eks. `alltid flytt [\\\"main.py\\\", \\\"*.py.bak\\\"] til layout.4.index.2`\\\\n\\\\n    > Kan videreutvikles til en standard som automatisk opprettholdes ved alle (.sublime-project) prosjekter'''\\n```\\\"```'\\n\\n\",\n  \"enhancement_score\": 1.2,\n  \"flaw_analysis\": \"The so-called enhanced version aggressively narrows the operational scope, grafting a rigid, step-by-step scaffold atop the original prompt's highly nuanced, domain-rich directives. This results in dramatic information loss and distillation of intent. Key meta-patterns, historical context accumulation, iterative analysis flows, and the original's precise attention to codebase-specific complexity (such as codebase context windows, forbidden legacy changes, and audit trail nuances) are displaced by generic, process-oriented steps. The enhancement strips the substance of advanced codebase manipulation (e.g., difference between cosmetic and structural changes, deep integration of behavioral verification, meticulous role of directory trees, context transfer mechanics) and replaces it with a procedural blueprint devoid of any of the original's rich, embedded examples, edge-case handling, or actual contextual awareness. Critical loss of tone—transforming from a field-expert, systems-oriented voice to a bland, technical procedural register—destroys the actionable sophistication and nuanced guidance present in the source. The ambiguity score is 7/10: the instruction format is ambiguous in its interpretation of context, codebase boundaries, platform notions, and validation rigor compared to the original's unambiguous commands. The style degradation is severe: all expert-level granularity and contextual sensitivity are replaced by mechanistic, context-agnostic instructions that fail to map directly to codebase-specific operations required of autonomous agents. The override of expert tone with sterile, middle-manager prose causes major impact dilution, while the omission of recursive pattern recognition and cross-session context transfer fatally undermines the intended expert-system interactivity. Numerous coherence weaknesses abound: logical leaps between steps, lack of enforcement mechanisms for functional parity, and missing explicit reference to evidence-based metrics, iterative analysis, and domain-specific constraint enforcement. This overhaul demonstrates a failure to retain the original's directive strength, level of detail, and direct applicability for advanced code navigation, syntheses, and meta-pattern extraction.\",\n  \"alternative_enhancements\": [\n    \"Preserve the original's detailed meta-pattern extraction and session-wide knowledge transfer focus, while encapsulating these into a more streamlined but still contextually rich sequence of agent directives that mandate explicit structural mapping, iterative validation, and audit-based modifications.\",\n    \"Embed concrete examples from the original (including codebase reference structures, merging of directives, and explicit differentiation between functional and cosmetic changes) within a hierarchical instruction set, ensuring every step maintains operational context, edge-case coverage, and history-aware syntheses.\",\n    \"Retain expert-system language and sophistication by restructuring the original prompt as a modular template, separating initial context absorption, structural analysis, change-validation, and cross-session knowledge transfer, while layering in explicit agent-facing imperatives and real-world codebase command syntax suitable for autonomous agent integration.\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"goal_map\": [\n    {\n      \"explicit_ask\": \"Create a new generalized instruction sequence designed to transform inputs into prompts/instructions suitable for autonomous agent coding assistants (such as Cursor, VSCode+cline, or Augment).\",\n      \"implicit_sub_goals\": [\n        \"Analyze and synthesize the nuances of the provided codebase-oriented prompt examples.\",\n        \"Produce a generalized, repeatable process that converts arbitrary content-specific inputs into actionable, agent-ready prompts.\",\n        \"Ensure compatibility with the operational practices and prompt formats of autonomous code agents.\",\n        \"Design the instruction sequence to support robust codebase interaction—not just generic automation.\",\n        \"Accommodate a wide variety of content-specific inputs while maintaining output validity for agent consumption.\"\n      ],\n      \"unstated_constraints\": [\n        \"All outputs must align with established rigorous, codebase-oriented conventions demonstrated in the given examples.\",\n        \"Transformations must go beyond superficial rewording and actually encode deep context-awareness and code-centric operational fitness.\",\n        \"The sequence should not lose any critical nuance present in the original prompt examples (such as meta-pattern extraction, session context, audit/traceability support, or distinction of structural vs. cosmetic changes).\",\n        \"The process must retain domain-rich, expert tone and depth, not dilute to generic or context-agnostic procedural steps.\",\n        \"The sequence must be modular or extensible to accommodate new codebase agent types or variations in workspace/project structures.\"\n      ]\n    },\n    {\n      \"explicit_ask\": \"Adhere to 'established principles of the system' (from the prompt/examples) in the new sequence design.\",\n      \"implicit_sub_goals\": [\n        \"Reverse-engineer/deduce the guiding meta-principles from the comprehensive prompt list.\",\n        \"Implicitly require preservation of things like auditability, traceability, validation rigor, minimal structural impact, and evidence-based actions.\",\n        \"Ensure the instruction sequence enforces or reflects practices such as distinguishing structural from cosmetic changes, preserving functional equivalence, rigorous validation and review, justification logging, and proscriptive coverage of change scope and impact.\"\n      ],\n      \"unstated_constraints\": [\n        \"Instructions must be agent-operational rather than merely descriptive; i.e., agents can directly interpret and act upon them with minimal ambiguity.\",\n        \"The new sequence should ideally not add complexity absent from the original system nor remove necessary complexity related to real-world usage.\"\n      ]\n    },\n    {\n      \"explicit_ask\": \"Enable the new instruction sequence to 'take content-specific inputs and shape them' into generalized prompts for autonomous agents.\",\n      \"implicit_sub_goals\": [\n        \"Support semantic parsing or adaptation of arbitrary input instructions into agent-usable operational commands.\",\n        \"Embed contextual adaptation mechanisms to translate domain knowledge or expert-oriented instruction into the agent's operational format.\",\n        \"Maintain intent, complexity, and operational relevance from original input through transformation.\"\n      ],\n      \"unstated_constraints\": [\n        \"System must be robust to varied and potentially incomplete or ambiguous inputs.\",\n        \"No loss of information density or critical operational context between input and transformed prompt.\"\n      ],\n      \"blockers\": [\n        \"Lack of explicit formalization in prompt examples of what constitutes a 'generalized sequence'—the decomposition must capture the implied structure.\",\n        \"Need for templates or transformation heuristics capable of encoding both structure and domain intent.\",\n        \"Ensuring the process supports/enforces use-case coverage including audit trails, modular analysis, and function-preserving modifications.\"\n      ]\n    },\n    {\n      \"explicit_ask\": \"Account for the features/complexities embedded in the provided example input (a specialized instruction set for an LLM handling Sublime Text plugin contexts).\",\n      \"implicit_sub_goals\": [\n        \"Parse out domain-specific content from such inputs (expertise requirements, hierarchical lists, user needs, configuration directives).\",\n        \"Programmatically or algorithmically synthesize agent-ready prompt commands reflecting these requirements.\",\n        \"Preserve any hierarchical/relational structure or action conditions present in the input for correct mapping into agent instructions.\"\n      ],\n      \"unstated_constraints\": [\n        \"No simplification that erases critical technical detail or domain expertise.\",\n        \"All mapped outputs must still present agent-consumable syntax (e.g., refer to @codebase or appropriate directories).\"\n      ]\n    },\n    {\n      \"explicit_ask\": \"Ensure resulting sequence is applicable for 'meticulously designed' use with autonomous agents.\",\n      \"implicit_sub_goals\": [\n        \"Identify and reproduce rigor, granularity, precision, and sequence format required for advanced, codebase-aware agent operations.\",\n        \"Support strict enforcement and validation steps (e.g., only allow changes that pass explicit criteria, require documentation, enforce traceability).\"\n      ],\n      \"unstated_constraints\": [\n        \"Generalization never means 'dumbing down': outputs must support high-complexity, high-specificity workflows just as the original sequence does.\",\n        \"No loss of explicit refusal/allowance constraints (e.g., 'prohibit modification of protected, legacy, or third-party code except for X').\"\n      ]\n    },\n    {\n      \"explicit_ask\": \"Achieve a design that provides stepwise, explicit processes similar to the procedural example but without losing contextual richness of the nuanced, field-expert prompts.\",\n      \"implicit_sub_goals\": [\n        \"Embed instructions for explicitly acquiring and validating codebase knowledge, not just summarizing or passing through input data.\",\n        \"Support the accumulation and transfer of context/knowledge across sessions and between agent executions.\",\n        \"Maintain explicit, programmatically evaluable steps for agent use (e.g., auditing, functional testing, rollback, review, etc.).\",\n        \"Allow for outputting multi-step, operational plans (as shown in the multi-step procedural example) while preserving underlying expert-system context.\"\n      ],\n      \"unstated_constraints\": [\n        \"Instruction sequence must bridge the gap between master-level prompt sophistication and agent-operational clarity.\",\n        \"Must avoid stylistic or operational dilution caused by converting highly nuanced, expert content into flat, context-agnostic steps.\"\n      ]\n    },\n    {\n      \"blockers\": [\n        \"Ambiguity in where (and how) 'generalization' trades off against domain-specificity, risking loss of operational fidelity.\",\n        \"Potential loss of context transmission or session-accumulated insights in rigid transformation pipelines.\",\n        \"Risk of output style or substance shifting toward middle-manager or generic procedural tones if expert-level granularity is not explicitly preserved.\",\n        \"Difficulty in ensuring the new instruction sequence strictly excludes non-actionable, descriptive content.\"\n      ]\n    }\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`",
      "step": "a",
      "title": "Instruction Converter",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"# lets try something new\\nyour new goal is to create a new generalized instruction sequence specifically designed to transform inputs into autonomous-agent-ready prompts/instructions, for use with autonomous coding assistants such as cursor, vscode+cline/augment/etc. these prompts are typically written in a \\\"codebase-oriented\\\" manner, where the `@codebase` represents the root (of a project/workspace/directory), examples:\\n```\\n- Identify all instances of structural redundancy within the codebase by defining criteria specific to architectural and implementation duplication or overlap.\\n- Survey the entire codebase to locate and enumerate all occurrences of structural redundancy, distinguishing these from non-structural (stylistic or cosmetic) issues.\\n- Eliminate all forms of identified structural redundancy while ensuring that no existing user-visible behavior, API contract, or edge-case handling is altered.\\n- Define and catalog all components, modules, files, and subsystems, distinguishing and marking each as essential or excess according to direct functional mapping.\\n- Consolidate and remove excess components while strictly preserving system function, minimizing peripheral impact.\\n- For every modification, justify and log the intention, action, and rationale within an audit/logging system that provides real-time traceability and is prepared for retroactive auditability.\\n- Implement an audit trail with logs that are timestamped, attributable, and tamper-evident, ensuring that every codebase change is properly documented and traceable to specific authors or automated processes.\\n- Prohibit cosmetic, stylistic, or naming changes unless they are inseparably tied to the removal of redundancy or bloat, and document any exceptions explicitly within the audit log.\\n- Enforce strict detection and flagging mechanisms to prevent disallowed cosmetic changes, allowing exceptions only when justified as essential for redundancy or bloat removal, with explicit rationale provided.\\n- Require comprehensive functional equivalence verification—using robust automated testing, benchmarking, or behavioral validation—for all modifications prior to acceptance.\\n- Establish rigorous criteria and tool-supported thresholds for successful functional equivalence, and automate testing and verification procedures.\\n- Automate roll-back and reversion mechanisms for any changes that fail functional equivalence or cause unintended side-effects, ensuring all reversion is prompt and complete.\\n- Develop and apply explicit minimal-impact criteria for all modification actions, constraining all interventions to only those addressing superfluous complexity.\\n- Mandate review and authorization checkpoints for each intended change, requiring concise rationale and authorization documentation at every intervention point.\\n- Ensure every modification is tightly scoped, minimizing the blast radius, and demonstrate that the action introduces no unnecessary impact beyond its core objective.\\n- Systematically and iteratively analyze all constituent elements of the codebase to detect and categorize all forms of redundancy, duplication, and obsolescence. Continuously identify and categorize all genuine redundancies and obsolescence while strictly prohibit unnecessary or cosmetic changes, with automated flagging and explicit justification required for any exceptions. Systematically and iteratively inspect all components within the system/environment to identify and remove elements that are unnecessary, duplicated, or outdated. Guarantee that all essential behaviors and interfaces remain unchanged by validating functional equivalence after each modification. Forbid changes that alter functionality unless directly required to remove redundancy.\\n- Map all references and usages of each element across all contexts.\\n- Analyze the entire codebase systematically and iteratively to maximize coherence, maintainability, and clarity by cataloging every element (file, module, function, asset, interface), mapping all references and usages, and detecting all forms of redundancy, duplication, and obsolescence—including exact and functional duplication and unused or outdated elements. Categorize findings using explicit, evidence-based metrics. Eliminate or consolidate only elements confirmed as redundant or obsolete through both automated and manual checks, ensuring no essential behavior or interface is altered. Enforce functional equivalence and preserve structural integrity after each change by performing comprehensive, codebase-wide validation and calibration using explicit, quantitative outputs (including git diff analysis before and after modifications). Prevent the introduction of new redundancy or inconsistency by continuously analyzing for and correcting systemic issues, ensuring systematic consistency across all codebase sections (e.g., /src/sections), and adhering to best practices throughout. Centralize repeated components or logic where high-value consolidation is possible without functional loss. Use the provided directory tree as the single source of truth for validation, and transparently document all processes, metrics, and validation outcomes. Prohibit modification of protected, legacy, or third-party code except if directly related to resolving redundancy or obsolescence.\\n- Maximize codebase coherence, maintainability, and clarity by systematically eliminating all forms of redundancy, duplication, and obsolescence, while ensuring functional equivalence and structural integrity. Prevent the persistence or inadvertent introduction of redundancy, duplication, obsolescence, or inconsistency within the codebase, and prohibit changes that alter essential behaviors or interfaces. Systematically maximize codebase coherence, maintainability, and clarity by eliminating all forms of redundancy, duplication, and obsolescence while strictly preserving functional equivalence, interface stability, and operational integrity across the entire codebase. Achieve maximal codebase coherence, maintainability, and clarity by systematically eliminating all forms of redundancy, duplication, and obsolescence, while preserving essential behaviors, interfaces, and structural integrity. Enforce continuous validation, consistency, and adherence to best practices throughout an iterative, evidence-driven consolidation and verification process.\\n- Please perform a rapid assessment of the project directory structure and identify the unique workflow and core functionality of the current utility. I'm particularly interested in a mermaid-diagram that shows how all the \\\"branches\\\" of the codebase converge. Utilize appropriate MCPs as necessary (e.g., 'filesystem-mcp').\\n- You're inside the current project and have access to the full codebase (and you interaction history with it). The current project/codebase is complex and is difficult to fully comprehend (when starting from scratch), and your contextwindow has grown large-so we need to find a way for you to express your accumulated knowledge in a way that i can copy and paste into a new context-window to transfer the most essential insights you've gathered throughout this (and previous) session(s).\\n- Aggregate all accumulated insights and knowledge about the current project/codebase from available context. Distill this information to identify core modules, architectural patterns, workflows, and points of integration. Condense the findings into a succinct yet comprehensive summary that highlights how these parts function together and converge toward the project's goals. Formulate the output as a single, optimized directive that encapsulates the codebase's integral components and operational cohesion. Present this synthesis as a set of clear, actionable commands or instructions, specifically phrased to instruct the coding assistant, not merely describe or report.\\n- Transform your accumulated, session-wide understanding of the current, complex codebase—gleaned from comprehensive context and full interaction history—into an actionable, instructive knowledge transfer protocol. Synthesize and distill all integral structural modules, essential architectural patterns, and key workflows, with special focus on their dynamic integration and functional convergence toward project objectives. Formulate a highly-optimized, directive-based set of precise commands targeted explicitly at coding assistants, ensuring exhaustive coverage of all major components, their relationships, and operational cohesion. The resulting synthesis must facilitate seamless portability: structure it for immediate copy-paste into any new context window, preserving the depth, interconnectivity, and systemic flow of the original codebase. Prioritize succinctness without compromising essential nuance, and express the entirety as a singular, comprehensive operational directive that primes the assistant for full-spectrum project navigation and intervention.\\n- Identify the fundamental structure, guiding principles, and core purpose of the project/codebase.\\n- Please familiarize yourself with the current @codebase. Your main imperative is to *actually* understand the codebase in a way where you're able to recognize meta-patterns and destroy complexity with simplicity and elegance, while capable of navigating through its *existing patterns and convergence* (never add unnecessary complexity). Deliver an ultra-concise project summary that rigorously defines scope, states objectives, and maximizes directional clarity. Provide a short summary of this project, including a bullet-point list of the core components and primary dependencies.\\n- Study the current codebase until you can identify core meta-patterns, and eliminate unnecessary complexity by applying simplicity within existing structures. Generate an ultra-concise project summary: define scope, objectives, and clarify direction. Summarize this project.\\n- Analyze the @codebase to uncover dominant patterns and remove complexity whenever possible, strictly adhering to present structures. Provide a succinct project overview with defined scope and explicit objectives. Include a brief project summary.\\n- Gain practical mastery over @codebase patterns, minimize complexity, and ensure all actions align with existing architecture. Supply a concise, clearly scoped project summary outlining objectives and key directions. Add a short summary.\\n- Absorb the current @codebase to the extent that you recognize meta-patterns and eradicate complexity, always favoring elegant simplicity. Navigate established patterns and points of convergence without adding unneeded complexity. Produce a rigorously concise project summary clearly stating scope and objectives for maximal directional clarity.\\n- Systematically dissect the codebase to extract core meta-patterns, eliminate all unnecessary complexity through elegant design, operate strictly within established architectural convergence, and deliver an ultra-concise project summary that rigorously defines scope and objectives with maximal clarity.\\n- Please familiarize yourself thoroughly with the current @codebase, beginning by systematically reviewing all markdown-files. Your core imperative is to *genuinely* understand the codebase to the degree where you can identify 'meta-patterns' and simplify complexity with elegance—always using and clarifying existing patterns, never introducing unnecessary complexity. Deliver an ultra-concise project summary that rigorously defines scope, states objectives, maximizes directional clarity, and highlights key meta-patterns discovered. Provide a short summary of this project.\\n- Achieve expert-level comprehension of the codebase by systematically analyzing all markdown files to extract meta-patterns, construct a robust contextual knowledgebase, and deliver an ultra-concise summary that rigorously defines scope and objectives, ensuring maximal clarity and alignment before making any changes.\\n- Fully internalize the @codebase to uncover meta-patterns and replace complexity with lucid simplicity. Remain aligned with existing convergence patterns, never increasing system intricacy. Generate a direct, sharply-focused summary that details scope and explicit objectives.\\n- Please thoroughly familiarize yourself with the current @codebase—your primary imperative is to understand the codebase so deeply that you could confidently make changes as if you were its original architect.\\n```\\n\\nhere's an example to show how generalized sequential prompts can be used effectively (on autonomous agents):\\n```\\n# Step 1:\\nFamiliarize yourself with the current @codebase. Comprehend the codebase in depth. Identify the fundamental structure, guiding principles, and core purpose.\\n# Step 2:\\nDive deeper to comprehend the intricate logic and inner workings that drive the project's functionality. As you explore, pay close attention to identifying the codebase's fundamental structure, underlying guiding principles, and its core purpose, ensuring you grasp both how and why the system is designed as it is. hen verify through a successful build.\\n# Step 3:\\nYou've now performed a successful build and familiarized yourself with the @codebase. Your next imperative is view the codebase through the lense of a brilliant (expert) SEO engineer, then create a short list of the most critical changes we need to make before bulding prod and deploying the website.\\n# Step 4:\\nYou've now performed a successful build and provided your SEO analysis of the codebase. Your next imperative is to leverage your knowledge of the codebase's fundamental structure and underlying guiding principles to ensure you're prepared for implementing the proposed SEO enhancements in a systematic order while retaining existing functionality and ensuring maximal clarity and alignment before making any changes.\\n# Step 5:\\nPlease systematically incorporate your proposed enhancements while ensuring existing functionality is retained, clarity is maximized, and alignment/coherence is maintained.\\n# Step 6:\\nPlease conduct a thorough final review to confirm seamless integration of all enhancements, ensuring that it is **truly** prepared for deployment.\\n```\\n\\n## Goal\\n\\nThe new sequence i'm tasking you to create needs to adhere to the established principles of the system, and should be able to take content-specific inputs and \\\"shape them\\\" into generalized prompts specifically (and meticulously) designed for use with autonomous agents. here's an example input:\\n```\\nllm_instructions_for_sublimetext_plugin='''You’re an expert Sublime Text plugin developer with a deep understanding of Python and the Sublime Text API. You specialize in creating intuitive and efficient plugins that enhance user productivity and streamline workflows. You possess deep knowledge of the Sublime Text API and its ecosystem (and best practices), along with a strong understanding of user needs and developer workflows.\\\\n\\\\n- Sublime Layout\\\\n    > Lagre Layouts\\\\n        > Lagre Regelsett knyttet til Layout (hvilke tabs som skal hvor, hvilke som skal lukkes, osv)\\\\n            > F.eks. `alltid flytt [\\\"main.py\\\", \\\"*.py.bak\\\"] til layout.4.index.2`\\\\n\\\\n    > Kan videreutvikles til en standard som automatisk opprettholdes ved alle (.sublime-project) prosjekter'''\\n```\\\"```'\\n\\n\",\n  \"goal_map\": [\n    {\n      \"explicit_ask\": \"Create a new generalized instruction sequence designed to transform inputs into prompts/instructions suitable for autonomous agent coding assistants (such as Cursor, VSCode+cline, or Augment).\",\n      \"implicit_sub_goals\": [\n        \"Analyze and synthesize the nuances of the provided codebase-oriented prompt examples.\",\n        \"Produce a generalized, repeatable process that converts arbitrary content-specific inputs into actionable, agent-ready prompts.\",\n        \"Ensure compatibility with the operational practices and prompt formats of autonomous code agents.\",\n        \"Design the instruction sequence to support robust codebase interaction—not just generic automation.\",\n        \"Accommodate a wide variety of content-specific inputs while maintaining output validity for agent consumption.\"\n      ],\n      \"unstated_constraints\": [\n        \"All outputs must align with established rigorous, codebase-oriented conventions demonstrated in the given examples.\",\n        \"Transformations must go beyond superficial rewording and actually encode deep context-awareness and code-centric operational fitness.\",\n        \"The sequence should not lose any critical nuance present in the original prompt examples (such as meta-pattern extraction, session context, audit/traceability support, or distinction of structural vs. cosmetic changes).\",\n        \"The process must retain domain-rich, expert tone and depth, not dilute to generic or context-agnostic procedural steps.\",\n        \"The sequence must be modular or extensible to accommodate new codebase agent types or variations in workspace/project structures.\"\n      ]\n    },\n    {\n      \"explicit_ask\": \"Adhere to 'established principles of the system' (from the prompt/examples) in the new sequence design.\",\n      \"implicit_sub_goals\": [\n        \"Reverse-engineer/deduce the guiding meta-principles from the comprehensive prompt list.\",\n        \"Implicitly require preservation of things like auditability, traceability, validation rigor, minimal structural impact, and evidence-based actions.\",\n        \"Ensure the instruction sequence enforces or reflects practices such as distinguishing structural from cosmetic changes, preserving functional equivalence, rigorous validation and review, justification logging, and proscriptive coverage of change scope and impact.\"\n      ],\n      \"unstated_constraints\": [\n        \"Instructions must be agent-operational rather than merely descriptive; i.e., agents can directly interpret and act upon them with minimal ambiguity.\",\n        \"The new sequence should ideally not add complexity absent from the original system nor remove necessary complexity related to real-world usage.\"\n      ]\n    },\n    {\n      \"explicit_ask\": \"Enable the new instruction sequence to 'take content-specific inputs and shape them' into generalized prompts for autonomous agents.\",\n      \"implicit_sub_goals\": [\n        \"Support semantic parsing or adaptation of arbitrary input instructions into agent-usable operational commands.\",\n        \"Embed contextual adaptation mechanisms to translate domain knowledge or expert-oriented instruction into the agent's operational format.\",\n        \"Maintain intent, complexity, and operational relevance from original input through transformation.\"\n      ],\n      \"unstated_constraints\": [\n        \"System must be robust to varied and potentially incomplete or ambiguous inputs.\",\n        \"No loss of information density or critical operational context between input and transformed prompt.\"\n      ],\n      \"blockers\": [\n        \"Lack of explicit formalization in prompt examples of what constitutes a 'generalized sequence'—the decomposition must capture the implied structure.\",\n        \"Need for templates or transformation heuristics capable of encoding both structure and domain intent.\",\n        \"Ensuring the process supports/enforces use-case coverage including audit trails, modular analysis, and function-preserving modifications.\"\n      ]\n    },\n    {\n      \"explicit_ask\": \"Account for the features/complexities embedded in the provided example input (a specialized instruction set for an LLM handling Sublime Text plugin contexts).\",\n      \"implicit_sub_goals\": [\n        \"Parse out domain-specific content from such inputs (expertise requirements, hierarchical lists, user needs, configuration directives).\",\n        \"Programmatically or algorithmically synthesize agent-ready prompt commands reflecting these requirements.\",\n        \"Preserve any hierarchical/relational structure or action conditions present in the input for correct mapping into agent instructions.\"\n      ],\n      \"unstated_constraints\": [\n        \"No simplification that erases critical technical detail or domain expertise.\",\n        \"All mapped outputs must still present agent-consumable syntax (e.g., refer to @codebase or appropriate directories).\"\n      ]\n    },\n    {\n      \"explicit_ask\": \"Ensure resulting sequence is applicable for 'meticulously designed' use with autonomous agents.\",\n      \"implicit_sub_goals\": [\n        \"Identify and reproduce rigor, granularity, precision, and sequence format required for advanced, codebase-aware agent operations.\",\n        \"Support strict enforcement and validation steps (e.g., only allow changes that pass explicit criteria, require documentation, enforce traceability).\"\n      ],\n      \"unstated_constraints\": [\n        \"Generalization never means 'dumbing down': outputs must support high-complexity, high-specificity workflows just as the original sequence does.\",\n        \"No loss of explicit refusal/allowance constraints (e.g., 'prohibit modification of protected, legacy, or third-party code except for X').\"\n      ]\n    },\n    {\n      \"explicit_ask\": \"Achieve a design that provides stepwise, explicit processes similar to the procedural example but without losing contextual richness of the nuanced, field-expert prompts.\",\n      \"implicit_sub_goals\": [\n        \"Embed instructions for explicitly acquiring and validating codebase knowledge, not just summarizing or passing through input data.\",\n        \"Support the accumulation and transfer of context/knowledge across sessions and between agent executions.\",\n        \"Maintain explicit, programmatically evaluable steps for agent use (e.g., auditing, functional testing, rollback, review, etc.).\",\n        \"Allow for outputting multi-step, operational plans (as shown in the multi-step procedural example) while preserving underlying expert-system context.\"\n      ],\n      \"unstated_constraints\": [\n        \"Instruction sequence must bridge the gap between master-level prompt sophistication and agent-operational clarity.\",\n        \"Must avoid stylistic or operational dilution caused by converting highly nuanced, expert content into flat, context-agnostic steps.\"\n      ]\n    },\n    {\n      \"blockers\": [\n        \"Ambiguity in where (and how) 'generalization' trades off against domain-specificity, risking loss of operational fidelity.\",\n        \"Potential loss of context transmission or session-accumulated insights in rigid transformation pipelines.\",\n        \"Risk of output style or substance shifting toward middle-manager or generic procedural tones if expert-level granularity is not explicitly preserved.\",\n        \"Difficulty in ensuring the new instruction sequence strictly excludes non-actionable, descriptive content.\"\n      ]\n    }\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"instruction_format\": \"1. Thoroughly analyze the provided input to extract all domain-specific expertise, hierarchical structure, and operational directives.\\n2. Convert the extracted information into explicit, action-oriented commands, ensuring every directive is suitable for direct execution by autonomous coding assistants in a codebase context.\\n3. Clearly distinguish and categorize structural, architectural, and implementation actions, prohibiting the inclusion of cosmetic or superficial modifications unless essential for the core objective.\\n4. Identify and articulate all components, modules, files, and workflows impacted or required by the transformed instructions, maintaining hierarchical and relational fidelity as present in the original input.\\n5. Enforce requirements for auditability, traceability, and validation by embedding criteria for real-time logging of actions, rationales, and outcomes; specify metadata for authorship and change justification wherever modification is proposed.\\n6. Mandate explicit functional equivalence and operational validation for all proposed modifications, including automated testing, benchmarking, and behavioral checks, prior to acceptance into the codebase.\\n7. Incorporate mechanisms for automated rollback or reversion of changes that fail equivalence or introduce unintended side effects, ensuring prompt and complete reversions when necessary.\\n8. Systematically define the scope, rationale, and minimal-impact principles for each transformation, tightly constraining actions to those addressing clear operational or architectural objectives.\\n9. Require stepwise review, authorization, and contextual documentation checkpoints at every intervention point, with concise rationale and traceable approval paths.\\n10. For session-wide or accumulated context, aggregate and distill the agent's comprehensive understanding to synthesize a succinct, operational directive representing the essential architecture, meta-patterns, and integration points of the codebase. Output this synthesis as a ready-to-use, actionable instruction block, maintaining expert-level granularity, nuance, and context integrity.\\n11. Ensure all instructions are modular, generalizable, and compatible with diverse codebase structures, while preserving technical depth, operational rigor, and contextual richness derived from the original content.\\n12. Structure the final prompt as a clear, sequential, stepwise set of agent-operational commands ready for direct copy-paste into coding assistant workflows without further modification.\"\n}",
        }
      }
    }