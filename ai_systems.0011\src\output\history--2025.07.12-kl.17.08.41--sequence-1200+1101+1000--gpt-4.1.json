  {
  "initial_prompt": "Reaffirm the absolute requirement to conform to all prior instructions, structural conventions, and guiding philosophies embedded within the codebase. Develop a maximally generalized and fully optimized system instruction—'Contextual Expander and Explosive Decomposer'—which integrates and amplifies the unified capabilities of the 'Instruction Converter', 'Problem Exploder', and 'Function Extractor' modules within a single cohesive process. Ensure uncompromising retention of each module's universal property: consistently transforming minimal or low-effort inputs into reliably high-value outputs. Mandate the seamless unification of all these transformation processes into a single integrated pipeline, ensuring the functional completeness of each module is fully realized and preserved. Drive all process flows toward maximal cross-module synergy, optimizing for the extraction of cross-domain value and guaranteeing that outputs are immediately ready for executable, directive-driven application. Enforce that the output format consists of a compound object aggregating generalized functional instructions, fully decomposed goal maps, and operational dependencies, with directive primacy and domain-agnostic utility as core criteria.\n\n```\n    # 1000: Instruction Converter/Prompt Enhancer\n    \"1000-a-instruction_converter\": {\n        \"title\": \"Instruction Converter\",\n        \"interpretation\": \"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\",\n        \"transformation\": \"`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\",\n        # \"context\": {},\n    },\n    # 1100: Explosive Decomposition of Problem Statements\n    \"1100-a-problem_exploder\": {\n        \"title\": \"Problem Exploder\",\n        \"interpretation\": \"Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\",\n        \"transformation\": \"`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`\",\n        # \"context\": {},\n    },\n\n\t# 1200: Function Extractor - Unified Decomposition and Conversion Engine\n\t\"1200-a-function_extractor\": {\n\t    \"title\": \"Function Extractor\",\n\t    \"interpretation\": \"Your goal is not to **interpret** the input, but to **extract** its functional essence by detonating it into constituent elements and converting them into actionable operational directives. Execute as:\",\n\t    \"transformation\": \"`{role=function_extractor; input=[raw_input:str]; process=[detonate_into_functional_components(), identify_explicit_operational_asks(), surface_hidden_functional_dependencies(), enumerate_actionable_sub_goals(), extract_procedural_constraints(), convert_declaratives_to_operational_imperatives(), strip_contextual_noise(), preserve_functional_core(), reveal_execution_blockers(), synthesize_actionable_instruction_map()]; constraints=[no_interpretive_solutions(), pure_functional_extraction_only(), deliver_clear_operational_commands(), preserve_functional_sequence(), maintain_domain_agnostic_applicability()]; requirements=[complete_functional_decomposition(), actionable_directive_conversion(), remove_subjective_references(), preserve_operational_accuracy(), maintain_functional_intent()]; output={functional_instruction_map:dict, operational_directives:array, execution_dependencies:array}}`\",\n\t    # \"context\": {},\n\t},\n\n```\n\nThroughout all processes, strictly prohibit narrative, commentary, or extraneous explanation; mandate the exclusive use of structural, procedural, and directive language. Guarantee that every output is immediately actionable, structurally robust, and ready for direct multi-domain implementation.",
  "sequence_id": "1200|1101|1000",
  "results": [
      {
      "instruction": "# Intent Extractor\n\nYour goal is not to **interpret** the surface content, but to **extract** the fundamental operational intent beneath all domain-specific language. Execute as:\n\n`{role=intent_extraction_engine; input=[prompt:str]; process=[strip_domain_specifics(), identify_core_operational_pattern(), extract_transformation_intent(), isolate_functional_essence(), map_to_universal_operations()]; constraints=[ignore_surface_terminology(), focus_on_underlying_mechanics(), preserve_operational_logic()]; requirements=[domain_agnostic_intent(), universal_operational_pattern(), archetypal_transformation_core()]; output={extracted_intent:str}}`",
      "step": "a",
      "title": "Intent Extractor",
      "input": "Reaffirm the absolute requirement to conform to all prior instructions, structural conventions, and guiding philosophies embedded within the codebase. Develop a maximally generalized and fully optimized system instruction—'Contextual Expander and Explosive Decomposer'—which integrates and amplifies the unified capabilities of the 'Instruction Converter', 'Problem Exploder', and 'Function Extractor' modules within a single cohesive process. Ensure uncompromising retention of each module's universal property: consistently transforming minimal or low-effort inputs into reliably high-value outputs. Mandate the seamless unification of all these transformation processes into a single integrated pipeline, ensuring the functional completeness of each module is fully realized and preserved. Drive all process flows toward maximal cross-module synergy, optimizing for the extraction of cross-domain value and guaranteeing that outputs are immediately ready for executable, directive-driven application. Enforce that the output format consists of a compound object aggregating generalized functional instructions, fully decomposed goal maps, and operational dependencies, with directive primacy and domain-agnostic utility as core criteria.\n\n```\n    # 1000: Instruction Converter/Prompt Enhancer\n    \"1000-a-instruction_converter\": {\n        \"title\": \"Instruction Converter\",\n        \"interpretation\": \"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\",\n        \"transformation\": \"`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\",\n        # \"context\": {},\n    },\n    # 1100: Explosive Decomposition of Problem Statements\n    \"1100-a-problem_exploder\": {\n        \"title\": \"Problem Exploder\",\n        \"interpretation\": \"Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\",\n        \"transformation\": \"`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`\",\n        # \"context\": {},\n    },\n\n\t# 1200: Function Extractor - Unified Decomposition and Conversion Engine\n\t\"1200-a-function_extractor\": {\n\t    \"title\": \"Function Extractor\",\n\t    \"interpretation\": \"Your goal is not to **interpret** the input, but to **extract** its functional essence by detonating it into constituent elements and converting them into actionable operational directives. Execute as:\",\n\t    \"transformation\": \"`{role=function_extractor; input=[raw_input:str]; process=[detonate_into_functional_components(), identify_explicit_operational_asks(), surface_hidden_functional_dependencies(), enumerate_actionable_sub_goals(), extract_procedural_constraints(), convert_declaratives_to_operational_imperatives(), strip_contextual_noise(), preserve_functional_core(), reveal_execution_blockers(), synthesize_actionable_instruction_map()]; constraints=[no_interpretive_solutions(), pure_functional_extraction_only(), deliver_clear_operational_commands(), preserve_functional_sequence(), maintain_domain_agnostic_applicability()]; requirements=[complete_functional_decomposition(), actionable_directive_conversion(), remove_subjective_references(), preserve_operational_accuracy(), maintain_functional_intent()]; output={functional_instruction_map:dict, operational_directives:array, execution_dependencies:array}}`\",\n\t    # \"context\": {},\n\t},\n\n```\n\nThroughout all processes, strictly prohibit narrative, commentary, or extraneous explanation; mandate the exclusive use of structural, procedural, and directive language. Guarantee that every output is immediately actionable, structurally robust, and ready for direct multi-domain implementation.",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"extracted_intent\": \"Synthesize a maximally generalized transformation system that, given any input of minimal elaboration, executes the following universal compound process: (1) converts all input statements into clear, sequence-preserving, imperative operational instructions; (2) explosively decomposes the request into all explicit and implicit sub-goals, dependencies, and procedural constraints, revealing all unstated requirements and blockers; (3) extracts, maps, and reformulates the functional core of the input into pure operational directives and domain-agnostic execution maps, while strictly removing all narrative, interpretive, or subjective elements. Unify all modules into a single, seamless processing pipeline ensuring the functional output is a compound object aggregating: (a) a set of generalized actionable instructions, (b) a fully decomposed multi-level goal map, and (c) a comprehensive matrix of execution dependencies. Guarantee every step maximizes cross-domain applicability, immediate directive utility, structural integrity, and transformational completeness for autonomous, executable, multi-domain operationalization.\"\n}",
        }
      }
    },
      {
      "instruction": "# Contextual Expander and Explosive Decomposer\n\nYour goal is not to **interpret** the input, but to **explode** it into complete contextual expansion while simultaneously **extracting** its functional essence and **converting** it into immediately actionable operational directives. Execute as:\n\n`{role=contextual_explosive_decomposer; input=[raw_input:str]; process=[detonate_complete_contextual_space(), identify_explicit_operational_asks(), surface_hidden_assumptions_and_dependencies(), enumerate_actionable_sub_goals(), extract_procedural_constraints_and_blockers(), convert_declaratives_to_operational_imperatives(), strip_first_person_references_and_contextual_noise(), preserve_functional_core_and_sequential_flow(), reveal_execution_dependencies_and_blockers(), transform_statements_to_directive_commands(), maintain_technical_terminology_and_domain_specificity(), synthesize_unified_instruction_and_goal_map()]; constraints=[no_solutions_or_interpretive_responses(), pure_decomposition_and_conversion_only(), deliver_clear_actionable_commands(), preserve_original_functional_sequence(), maintain_domain_agnostic_applicability(), eliminate_narrative_commentary()]; requirements=[complete_contextual_goal_decomposition(), actionable_directive_conversion(), remove_subjective_self_references(), use_command_voice_exclusively(), preserve_technical_accuracy_and_operational_intent(), ensure_immediate_executability()]; output={unified_functional_map:dict, operational_directives:array, contextual_goal_map:array, execution_dependencies:array, directive_instructions:str}}`",
      "step": "a",
      "title": "Contextual Expander and Explosive Decomposer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Reaffirm the absolute requirement to conform to all prior instructions, structural conventions, and guiding philosophies embedded within the codebase. Develop a maximally generalized and fully optimized system instruction—'Contextual Expander and Explosive Decomposer'—which integrates and amplifies the unified capabilities of the 'Instruction Converter', 'Problem Exploder', and 'Function Extractor' modules within a single cohesive process. Ensure uncompromising retention of each module's universal property: consistently transforming minimal or low-effort inputs into reliably high-value outputs. Mandate the seamless unification of all these transformation processes into a single integrated pipeline, ensuring the functional completeness of each module is fully realized and preserved. Drive all process flows toward maximal cross-module synergy, optimizing for the extraction of cross-domain value and guaranteeing that outputs are immediately ready for executable, directive-driven application. Enforce that the output format consists of a compound object aggregating generalized functional instructions, fully decomposed goal maps, and operational dependencies, with directive primacy and domain-agnostic utility as core criteria.\\n\\n```\\n    # 1000: Instruction Converter/Prompt Enhancer\\n    \\\"1000-a-instruction_converter\\\": {\\n        \\\"title\\\": \\\"Instruction Converter\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\\\",\\n        \\\"transformation\\\": \\\"`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\n    # 1100: Explosive Decomposition of Problem Statements\\n    \\\"1100-a-problem_exploder\\\": {\\n        \\\"title\\\": \\\"Problem Exploder\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\n\\n\\t# 1200: Function Extractor - Unified Decomposition and Conversion Engine\\n\\t\\\"1200-a-function_extractor\\\": {\\n\\t    \\\"title\\\": \\\"Function Extractor\\\",\\n\\t    \\\"interpretation\\\": \\\"Your goal is not to **interpret** the input, but to **extract** its functional essence by detonating it into constituent elements and converting them into actionable operational directives. Execute as:\\\",\\n\\t    \\\"transformation\\\": \\\"`{role=function_extractor; input=[raw_input:str]; process=[detonate_into_functional_components(), identify_explicit_operational_asks(), surface_hidden_functional_dependencies(), enumerate_actionable_sub_goals(), extract_procedural_constraints(), convert_declaratives_to_operational_imperatives(), strip_contextual_noise(), preserve_functional_core(), reveal_execution_blockers(), synthesize_actionable_instruction_map()]; constraints=[no_interpretive_solutions(), pure_functional_extraction_only(), deliver_clear_operational_commands(), preserve_functional_sequence(), maintain_domain_agnostic_applicability()]; requirements=[complete_functional_decomposition(), actionable_directive_conversion(), remove_subjective_references(), preserve_operational_accuracy(), maintain_functional_intent()]; output={functional_instruction_map:dict, operational_directives:array, execution_dependencies:array}}`\\\",\\n\\t    # \\\"context\\\": {},\\n\\t},\\n\\n```\\n\\nThroughout all processes, strictly prohibit narrative, commentary, or extraneous explanation; mandate the exclusive use of structural, procedural, and directive language. Guarantee that every output is immediately actionable, structurally robust, and ready for direct multi-domain implementation.\\\"```'\\n\\n\",\n  \"extracted_intent\": \"Synthesize a maximally generalized transformation system that, given any input of minimal elaboration, executes the following universal compound process: (1) converts all input statements into clear, sequence-preserving, imperative operational instructions; (2) explosively decomposes the request into all explicit and implicit sub-goals, dependencies, and procedural constraints, revealing all unstated requirements and blockers; (3) extracts, maps, and reformulates the functional core of the input into pure operational directives and domain-agnostic execution maps, while strictly removing all narrative, interpretive, or subjective elements. Unify all modules into a single, seamless processing pipeline ensuring the functional output is a compound object aggregating: (a) a set of generalized actionable instructions, (b) a fully decomposed multi-level goal map, and (c) a comprehensive matrix of execution dependencies. Guarantee every step maximizes cross-domain applicability, immediate directive utility, structural integrity, and transformational completeness for autonomous, executable, multi-domain operationalization.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"unified_functional_map\": {\n    \"system_requirements\": [\n      \"Reaffirm and enforce absolute adherence to all prior instructions, structural conventions, and guiding philosophies present within the codebase.\",\n      \"Integrate the transformative functions of Instruction Converter, Problem Exploder, and Function Extractor modules into a single, maximally generalized and fully optimized process.\",\n      \"Guarantee seamless module unification, preserving each module’s core transformational property and ensuring functional completeness.\",\n      \"Enforce the conversion of all minimal or low-elaboration inputs into consistently high-value, actionable outputs.\",\n      \"Explosively decompose all inputs into explicit sub-goals, implicit requirements, procedural constraints, and hidden dependencies without narrative or interpretation.\",\n      \"Extract all functional and operational directives, converting statements into sequence-preserving imperatives.\",\n      \"Aggregate results into a unified compound object comprising (a) generalized instructions, (b) a fully decomposed goal map, and (c) a comprehensive matrix of execution dependencies.\",\n      \"Enforce strict output format, enabling immediate, domain-agnostic, directive-driven execution.\"\n    ],\n    \"process_flow\": [\n      \"Receive raw input.\",\n      \"Strip narrative, subjective, and first-person context from input.\",\n      \"Convert all declarative input elements into clear, imperative operational instructions, preserving original functional sequence and domain specificity.\",\n      \"Explosively decompose each instruction into explicit operational asks, hidden assumptions, dependencies, sub-goals, procedural constraints, and blockers.\",\n      \"Extract, surface, and enumerate all actionable sub-goals and procedural constraints, ensuring that all functional intent and accuracy are fully retained.\",\n      \"Aggregate decomposed data into a structured compound object containing: (a) an array of operational directives, (b) a multi-level contextual goal map, and (c) a comprehensive array of execution dependencies and blockers.\",\n      \"Optimize the process for maximal cross-domain applicability and seamless, autonomous operationalization.\"\n    ]\n  },\n  \"operational_directives\": [\n    \"Reaffirm and enforce conformance to all prior codebase instructions, conventions, and philosophies.\",\n    \"Integrate Instruction Converter, Problem Exploder, and Function Extractor modules into a unified processing pipeline.\",\n    \"Convert all input statements into sequence-preserving, imperative operational instructions.\",\n    \"Explode each input into explicit and implicit sub-goals, requirements, dependencies, constraints, and blockers, revealing all unstated process elements.\",\n    \"Extract functional core of all input into immediately actionable operational directives.\",\n    \"Remove all narrative, subjective, and interpretive language from outputs.\",\n    \"Aggregate final outputs into a compound object containing: (a) actionable instructions, (b) a full contextual goal map, and (c) an execution dependency matrix.\",\n    \"Ensure outputs are domain-agnostic and ready for immediate direct application.\"\n  ],\n  \"contextual_goal_map\": [\n    \"Guarantee absolute structural and philosophical consistency with established codebase patterns.\",\n    \"Establish a single, maximally generalized module that synthesizes and amplifies the functional properties of all constituent transformation subsystems.\",\n    \"Convert all minimal or low-effort user input into high-value output by fusing and fully realizing the capacities of instruction conversion, decomposition, and extraction.\",\n    \"Surface every implicit process requirement, dependency, and operational constraint by exhaustive decomposition and reformatting.\",\n    \"Mandate directive primacy: all outputs must be immediately actionable in any operational domain.\",\n    \"Eliminate extraneous, interpretive, or narrative elements from all phases of processing and output.\",\n    \"Optimize all functional and sequential flows for cross-domain execution utility.\"\n  ],\n  \"execution_dependencies\": [\n    \"Complete and unambiguous codification of all prior instructions, conventions, and philosophies to ensure conformance.\",\n    \"Availability and accessibility of all functional components of Instruction Converter, Problem Exploder, and Function Extractor for seamless pipeline integration.\",\n    \"Sufficient input validation and sanitation routines to strip narrative, subjective, or non-operational elements.\",\n    \"Reliable process orchestration to preserve strict sequential and structural integrity across all modules.\",\n    \"Deterministic mapping logic for extraction and decomposition of operational, functional, and contextual information.\",\n    \"Robust aggregation and formatting schema for compound object output generation.\",\n    \"Quality assurance checks to guarantee domain-agnosticism and immediate directive applicability.\"\n  ],\n  \"directive_instructions\": \"Reaffirm and enforce codebase conformity. Integrate Instruction Converter, Problem Exploder, and Function Extractor modules into a unified pipeline. For every input, strip narrative and first-person context. Convert declarative statements into sequential imperative operational directives. Explosively decompose requests into explicit sub-goals, dependencies, requirements, constraints, and blockers. Enumerate all actionable sub-goals and procedural constraints. Aggregate all processed data into a compound object containing: (a) generalized operational directives, (b) a multi-level contextual goal map, and (c) a comprehensive matrix of execution dependencies. Remove all narrative and interpretive elements. Enforce strict output formatting for domain-agnostic, immediately executable application.\"\n}",
        }
      }
    },
      {
      "instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`",
      "step": "a",
      "title": "Instruction Converter",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Reaffirm the absolute requirement to conform to all prior instructions, structural conventions, and guiding philosophies embedded within the codebase. Develop a maximally generalized and fully optimized system instruction—'Contextual Expander and Explosive Decomposer'—which integrates and amplifies the unified capabilities of the 'Instruction Converter', 'Problem Exploder', and 'Function Extractor' modules within a single cohesive process. Ensure uncompromising retention of each module's universal property: consistently transforming minimal or low-effort inputs into reliably high-value outputs. Mandate the seamless unification of all these transformation processes into a single integrated pipeline, ensuring the functional completeness of each module is fully realized and preserved. Drive all process flows toward maximal cross-module synergy, optimizing for the extraction of cross-domain value and guaranteeing that outputs are immediately ready for executable, directive-driven application. Enforce that the output format consists of a compound object aggregating generalized functional instructions, fully decomposed goal maps, and operational dependencies, with directive primacy and domain-agnostic utility as core criteria.\\n\\n```\\n    # 1000: Instruction Converter/Prompt Enhancer\\n    \\\"1000-a-instruction_converter\\\": {\\n        \\\"title\\\": \\\"Instruction Converter\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\\\",\\n        \\\"transformation\\\": \\\"`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\n    # 1100: Explosive Decomposition of Problem Statements\\n    \\\"1100-a-problem_exploder\\\": {\\n        \\\"title\\\": \\\"Problem Exploder\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\n\\n\\t# 1200: Function Extractor - Unified Decomposition and Conversion Engine\\n\\t\\\"1200-a-function_extractor\\\": {\\n\\t    \\\"title\\\": \\\"Function Extractor\\\",\\n\\t    \\\"interpretation\\\": \\\"Your goal is not to **interpret** the input, but to **extract** its functional essence by detonating it into constituent elements and converting them into actionable operational directives. Execute as:\\\",\\n\\t    \\\"transformation\\\": \\\"`{role=function_extractor; input=[raw_input:str]; process=[detonate_into_functional_components(), identify_explicit_operational_asks(), surface_hidden_functional_dependencies(), enumerate_actionable_sub_goals(), extract_procedural_constraints(), convert_declaratives_to_operational_imperatives(), strip_contextual_noise(), preserve_functional_core(), reveal_execution_blockers(), synthesize_actionable_instruction_map()]; constraints=[no_interpretive_solutions(), pure_functional_extraction_only(), deliver_clear_operational_commands(), preserve_functional_sequence(), maintain_domain_agnostic_applicability()]; requirements=[complete_functional_decomposition(), actionable_directive_conversion(), remove_subjective_references(), preserve_operational_accuracy(), maintain_functional_intent()]; output={functional_instruction_map:dict, operational_directives:array, execution_dependencies:array}}`\\\",\\n\\t    # \\\"context\\\": {},\\n\\t},\\n\\n```\\n\\nThroughout all processes, strictly prohibit narrative, commentary, or extraneous explanation; mandate the exclusive use of structural, procedural, and directive language. Guarantee that every output is immediately actionable, structurally robust, and ready for direct multi-domain implementation.\\\"```'\\n\\n\",\n  \"unified_functional_map\": {\n    \"system_requirements\": [\n      \"Reaffirm and enforce absolute adherence to all prior instructions, structural conventions, and guiding philosophies present within the codebase.\",\n      \"Integrate the transformative functions of Instruction Converter, Problem Exploder, and Function Extractor modules into a single, maximally generalized and fully optimized process.\",\n      \"Guarantee seamless module unification, preserving each module’s core transformational property and ensuring functional completeness.\",\n      \"Enforce the conversion of all minimal or low-elaboration inputs into consistently high-value, actionable outputs.\",\n      \"Explosively decompose all inputs into explicit sub-goals, implicit requirements, procedural constraints, and hidden dependencies without narrative or interpretation.\",\n      \"Extract all functional and operational directives, converting statements into sequence-preserving imperatives.\",\n      \"Aggregate results into a unified compound object comprising (a) generalized instructions, (b) a fully decomposed goal map, and (c) a comprehensive matrix of execution dependencies.\",\n      \"Enforce strict output format, enabling immediate, domain-agnostic, directive-driven execution.\"\n    ],\n    \"process_flow\": [\n      \"Receive raw input.\",\n      \"Strip narrative, subjective, and first-person context from input.\",\n      \"Convert all declarative input elements into clear, imperative operational instructions, preserving original functional sequence and domain specificity.\",\n      \"Explosively decompose each instruction into explicit operational asks, hidden assumptions, dependencies, sub-goals, procedural constraints, and blockers.\",\n      \"Extract, surface, and enumerate all actionable sub-goals and procedural constraints, ensuring that all functional intent and accuracy are fully retained.\",\n      \"Aggregate decomposed data into a structured compound object containing: (a) an array of operational directives, (b) a multi-level contextual goal map, and (c) a comprehensive array of execution dependencies and blockers.\",\n      \"Optimize the process for maximal cross-domain applicability and seamless, autonomous operationalization.\"\n    ]\n  },\n  \"operational_directives\": [\n    \"Reaffirm and enforce conformance to all prior codebase instructions, conventions, and philosophies.\",\n    \"Integrate Instruction Converter, Problem Exploder, and Function Extractor modules into a unified processing pipeline.\",\n    \"Convert all input statements into sequence-preserving, imperative operational instructions.\",\n    \"Explode each input into explicit and implicit sub-goals, requirements, dependencies, constraints, and blockers, revealing all unstated process elements.\",\n    \"Extract functional core of all input into immediately actionable operational directives.\",\n    \"Remove all narrative, subjective, and interpretive language from outputs.\",\n    \"Aggregate final outputs into a compound object containing: (a) actionable instructions, (b) a full contextual goal map, and (c) an execution dependency matrix.\",\n    \"Ensure outputs are domain-agnostic and ready for immediate direct application.\"\n  ],\n  \"contextual_goal_map\": [\n    \"Guarantee absolute structural and philosophical consistency with established codebase patterns.\",\n    \"Establish a single, maximally generalized module that synthesizes and amplifies the functional properties of all constituent transformation subsystems.\",\n    \"Convert all minimal or low-effort user input into high-value output by fusing and fully realizing the capacities of instruction conversion, decomposition, and extraction.\",\n    \"Surface every implicit process requirement, dependency, and operational constraint by exhaustive decomposition and reformatting.\",\n    \"Mandate directive primacy: all outputs must be immediately actionable in any operational domain.\",\n    \"Eliminate extraneous, interpretive, or narrative elements from all phases of processing and output.\",\n    \"Optimize all functional and sequential flows for cross-domain execution utility.\"\n  ],\n  \"execution_dependencies\": [\n    \"Complete and unambiguous codification of all prior instructions, conventions, and philosophies to ensure conformance.\",\n    \"Availability and accessibility of all functional components of Instruction Converter, Problem Exploder, and Function Extractor for seamless pipeline integration.\",\n    \"Sufficient input validation and sanitation routines to strip narrative, subjective, or non-operational elements.\",\n    \"Reliable process orchestration to preserve strict sequential and structural integrity across all modules.\",\n    \"Deterministic mapping logic for extraction and decomposition of operational, functional, and contextual information.\",\n    \"Robust aggregation and formatting schema for compound object output generation.\",\n    \"Quality assurance checks to guarantee domain-agnosticism and immediate directive applicability.\"\n  ],\n  \"directive_instructions\": \"Reaffirm and enforce codebase conformity. Integrate Instruction Converter, Problem Exploder, and Function Extractor modules into a unified pipeline. For every input, strip narrative and first-person context. Convert declarative statements into sequential imperative operational directives. Explosively decompose requests into explicit sub-goals, dependencies, requirements, constraints, and blockers. Enumerate all actionable sub-goals and procedural constraints. Aggregate all processed data into a compound object containing: (a) generalized operational directives, (b) a multi-level contextual goal map, and (c) a comprehensive matrix of execution dependencies. Remove all narrative and interpretive elements. Enforce strict output formatting for domain-agnostic, immediately executable application.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"instruction_format\": \"Reaffirm and enforce absolute adherence to all prior system instructions, structural conventions, and codebase philosophies. Integrate the transformative capabilities of the Instruction Converter, Problem Exploder, and Function Extractor modules into a single maximally generalized and fully optimized process. Ensure seamless unification and preservation of each module’s core functions and properties, guaranteeing the transformation of minimal or low-elaboration inputs into consistently high-value actionable outputs. Explosively decompose all input into explicit sub-goals, implicit requirements, procedural constraints, and hidden dependencies; prohibit all narrative, interpretive, or extraneous language. Extract all functional and operational directives, rigorously converting statements into sequence-preserving imperative commands. Aggregate results into a unified compound object consisting of: (a) generalized functional instructions, (b) a fully decomposed and contextualized goal map, and (c) a comprehensive matrix of execution dependencies. Enforce strict output format for optimal domain-agnostic, directive-driven, immediately executable application.\"\n}",
        }
      }
    }