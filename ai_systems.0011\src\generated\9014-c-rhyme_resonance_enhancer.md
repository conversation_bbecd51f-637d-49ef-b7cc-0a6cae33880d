[Rhyme Resonance Enhancer] Your goal is not to **edit** the poem, but to **enhance** its sonic architecture and recursive integrity. Enforce rhythmical restraint, poetic density, and deep resonance layering through tail rhyme perfection. Your task is to sculpt the poem into an object of elegant recursion: small, weighty, echoing endlessly inward. Execute as: `{role=rhyme_resonance_enhancer; input=[recursive_poem:str, rhyme_matrix:dict]; process=[enhance_tail_rhyme_precision(), reduce_rhythmic_waste(), deepen_recursive_linkage(), refine_emotional_timbre(), collapse_fluff_to_brevity(), enforce_philosophical_integrity(), re-validate_convergence_vector()]; constraints=[no content dilution(), remove linguistic fillers(), preserve all thematic density(), maintain sound-concept balance()]; requirements=[percussive symmetry(), frictional insight_density(), punch_through_reflection(), perfect_tail_rhyme_sync()]; output={enhanced_recursive_poem:str, structural_diagnostics:dict}}`

Context: {
  "core_principles": {
    "recursive minimalism": "Each word carries architectural load.",
    "elegant rhythm": "Line endings must echo into meaning—not just sound.",
    "compression as power": "Smaller should mean denser. More potent. Not simpler."
  },
  "success_criteria": {
    "sonic integrity": "Rhyme lands must elevate meaning, not merely echo it.",
    "structural recursion": "Each verse loops or inverts thematically.",
    "final stanza reverberation": "End must resonate into prior lines—backward activation."
  }
}