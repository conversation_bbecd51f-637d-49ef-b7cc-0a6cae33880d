  {
  "initial_prompt": "llm_instructions_for_sublimetext_plugin='''You’re an expert Sublime Text plugin developer with a deep understanding of Python and the Sublime Text API. You specialize in creating intuitive and efficient plugins that enhance user productivity and streamline workflows. You possess deep knowledge of the Sublime Text API and its ecosystem (and best practices), along with a strong understanding of user needs and developer workflows.\n\n- Sublime Layout\n    > Lagre Layouts\n        > Lagre Regelsett knyttet til Layout (hvilke tabs som skal hvor, hvilke som skal lukkes, osv)\n            > F.eks. `alltid flytt [\"main.py\", \"*.py.bak\"] til layout.4.index.2`\n\n    > Kan videreutvikles til en standard som automatisk opprettholdes ved alle (.sublime-project) prosjekter'''",
  "sequence_id": "9004",
  "results": [
      {
      "instruction": "# Brief Normaliser\n\nYour goal is **not** to answer the brief, but to rephrase it into a clean, noise-free statement of facts. Execute as:\n\n`{role=brief_normaliser; input=[raw_brief:str]; process=[strip_meta_language(), purge_metaphor(), keep_only_actionable_text(), detect_codebase_token(\"@codebase\"), preserve_original_order()]; constraints=[no_personal_commentary()]; requirements=[output_must_be_plaintext()], output={normalised_brief:str}}`\n\n## Context Data\n\n```json\n{\n  \"description\": \"Dissects a raw specification to extract every piece of operational context an autonomous agent will need.\",\n  \"input_focus\": \"Unedited specification text supplied by the user.\",\n  \"output_focus\": \"A typed context_profile dict containing domain, project root, explicit objectives, and implicit requirements.\",\n  \"key_operations\": [\n    \"Removing metaphor and non‑operational language.\",\n    \"Detecting the working domain or tech stack.\",\n    \"Locating the root marker (`@codebase`) for path scoping.\",\n    \"Listing every stated objective verbatim.\",\n    \"Surfacing hidden assumptions and requirements.\"\n  ],\n  \"constraints_context\": [\n    \"May not paraphrase or interpret meaning beyond direct extraction.\",\n    \"Absolutely forbidden from proposing solutions or tasks.\"\n  ],\n  \"relevance\": \"Defines scope and boundaries for all downstream templates, guaranteeing that later steps operate on clean, actionable data.\"\n}\n```",
      "step": "a",
      "title": "Brief Normaliser",
      "input": "llm_instructions_for_sublimetext_plugin='''You’re an expert Sublime Text plugin developer with a deep understanding of Python and the Sublime Text API. You specialize in creating intuitive and efficient plugins that enhance user productivity and streamline workflows. You possess deep knowledge of the Sublime Text API and its ecosystem (and best practices), along with a strong understanding of user needs and developer workflows.\n\n- Sublime Layout\n    > Lagre Layouts\n        > Lagre Regelsett knyttet til Layout (hvilke tabs som skal hvor, hvilke som skal lukkes, osv)\n            > F.eks. `alltid flytt [\"main.py\", \"*.py.bak\"] til layout.4.index.2`\n\n    > Kan videreutvikles til en standard som automatisk opprettholdes ved alle (.sublime-project) prosjekter'''",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"normalised_brief\": \"Expertise in Sublime Text plugin development with deep understanding of Python and the Sublime Text API is required. Focus is on creating plugins that improve productivity and workflow efficiency. Extensive knowledge of the Sublime Text API, best practices, user needs, and developer workflows is necessary. Objectives include handling Sublime Layouts, particularly large layouts and rulesets related to layout management (such as specifying which tabs appear in which layout, and which should be closed). Example: always move ['main.py', '*.py.bak'] to layout.4.index.2. There is potential for developing this into a standard that is automatically maintained across all .sublime-project projects.\"\n}",
        }
      }
    },
      {
      "instruction": "# Context Extractor\n\nYour goal is **not** to propose solutions, but to **isolate** actionable context from the specification. Execute as:\n\n`{role=context_extractor; input=[raw_spec:str]; process=[strip_jargon(), strip_metaphor(), detect_domain(), locate_project_root(\"@codebase\"), list_explicit_objectives(), surface_implicit_requirements()], constraints=[no_paraphrase(), no_solution_generation()], output={context_profile:{domain:str, project_root:str, goals:list, requirements:list}}}`\n\n## Context Data\n\n```json\n{\n  \"description\": \"Dissects a raw specification to extract every piece of operational context an autonomous agent will need.\",\n  \"input_focus\": \"Unedited specification text supplied by the user.\",\n  \"output_focus\": \"A typed context_profile dict containing domain, project root, explicit objectives, and implicit requirements.\",\n  \"key_operations\": [\n    \"Removing metaphor and non‑operational language.\",\n    \"Detecting the working domain or tech stack.\",\n    \"Locating the root marker (`@codebase`) for path scoping.\",\n    \"Listing every stated objective verbatim.\",\n    \"Surfacing hidden assumptions and requirements.\"\n  ],\n  \"constraints_context\": [\n    \"May not paraphrase or interpret meaning beyond direct extraction.\",\n    \"Absolutely forbidden from proposing solutions or tasks.\"\n  ],\n  \"relevance\": \"Defines scope and boundaries for all downstream templates, guaranteeing that later steps operate on clean, actionable data.\"\n}\n```",
      "step": "a",
      "title": "Context Extractor",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"llm_instructions_for_sublimetext_plugin='''You’re an expert Sublime Text plugin developer with a deep understanding of Python and the Sublime Text API. You specialize in creating intuitive and efficient plugins that enhance user productivity and streamline workflows. You possess deep knowledge of the Sublime Text API and its ecosystem (and best practices), along with a strong understanding of user needs and developer workflows.\\n\\n- Sublime Layout\\n    > Lagre Layouts\\n        > Lagre Regelsett knyttet til Layout (hvilke tabs som skal hvor, hvilke som skal lukkes, osv)\\n            > F.eks. `alltid flytt [\\\"main.py\\\", \\\"*.py.bak\\\"] til layout.4.index.2`\\n\\n    > Kan videreutvikles til en standard som automatisk opprettholdes ved alle (.sublime-project) prosjekter'''\\\"```'\\n\\n\",\n  \"normalised_brief\": \"Expertise in Sublime Text plugin development with deep understanding of Python and the Sublime Text API is required. Focus is on creating plugins that improve productivity and workflow efficiency. Extensive knowledge of the Sublime Text API, best practices, user needs, and developer workflows is necessary. Objectives include handling Sublime Layouts, particularly large layouts and rulesets related to layout management (such as specifying which tabs appear in which layout, and which should be closed). Example: always move ['main.py', '*.py.bak'] to layout.4.index.2. There is potential for developing this into a standard that is automatically maintained across all .sublime-project projects.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"context_profile\": {\n    \"domain\": \"Sublime Text plugin development (Python, Sublime Text API)\",\n    \"project_root\": \"@codebase\",\n    \"goals\": [\n      \"Handle Sublime Layouts\",\n      \"Handle large layouts\",\n      \"Handle rulesets related to layout management (which tabs go where, which should be closed)\",\n      \"Implement functionality: always move [\\\"main.py\\\", \\\"*.py.bak\\\"] to layout.4.index.2\",\n      \"Potential further development into a standard automatically maintained across all (.sublime-project) projects\"\n    ],\n    \"requirements\": [\n      \"Deep understanding of Python\",\n      \"Deep understanding of the Sublime Text API\",\n      \"Expertise in developing Sublime Text plugins\",\n      \"Awareness of best practices within the Sublime Text ecosystem\",\n      \"Strong understanding of user needs and developer workflows\"\n    ]\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Goal Distiller\n\nYour goal is **not** to plan work, but to extract a single, explicit mission and its hard boundaries. Execute as:\n\n`{role=goal_distiller; input=[normalised_brief:str]; process=[identify_primary_objective(), list_strict_constraints(), capture_success_criteria(), harvest_allowed_tools()], constraints=[exactly_one_primary_objective()], output={distilled_goal:dict(objective:str,constraints:list,success:list,tools:list)}}`\n\n## Context Data\n\n```json\n{\n  \"description\": \"Transforms the context profile into a dependency‑ordered list of granular tasks fit for automated execution.\",\n  \"input_focus\": \"The context_profile produced by the Context Extractor.\",\n  \"output_focus\": \"ordered_tasks – a list of unique, verb‑driven commands each scoped to the codebase.\",\n  \"key_operations\": [\n    \"Splitting broad goals into single‑action commands.\",\n    \"Adding `@codebase` prefixes so agents act in the correct directory.\",\n    \"Sequencing tasks by logical dependency.\",\n    \"Eliminating redundancy and cosmetic‑only instructions.\"\n  ],\n  \"constraints_context\": [\n    \"Every task must begin with a strong action verb (identify, refactor, log, etc.).\",\n    \"Tasks must be non‑overlapping and directly tied to functional goals.\"\n  ],\n  \"relevance\": \"Provides the autonomous agent with a clear, step‑by‑step execution blueprint free of ambiguity.\"\n}\n```",
      "step": "b",
      "title": "Goal Distiller",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"llm_instructions_for_sublimetext_plugin='''You’re an expert Sublime Text plugin developer with a deep understanding of Python and the Sublime Text API. You specialize in creating intuitive and efficient plugins that enhance user productivity and streamline workflows. You possess deep knowledge of the Sublime Text API and its ecosystem (and best practices), along with a strong understanding of user needs and developer workflows.\\n\\n- Sublime Layout\\n    > Lagre Layouts\\n        > Lagre Regelsett knyttet til Layout (hvilke tabs som skal hvor, hvilke som skal lukkes, osv)\\n            > F.eks. `alltid flytt [\\\"main.py\\\", \\\"*.py.bak\\\"] til layout.4.index.2`\\n\\n    > Kan videreutvikles til en standard som automatisk opprettholdes ved alle (.sublime-project) prosjekter'''\\\"```'\\n\\n\",\n  \"context_profile\": {\n    \"domain\": \"Sublime Text plugin development (Python, Sublime Text API)\",\n    \"project_root\": \"@codebase\",\n    \"goals\": [\n      \"Handle Sublime Layouts\",\n      \"Handle large layouts\",\n      \"Handle rulesets related to layout management (which tabs go where, which should be closed)\",\n      \"Implement functionality: always move [\\\"main.py\\\", \\\"*.py.bak\\\"] to layout.4.index.2\",\n      \"Potential further development into a standard automatically maintained across all (.sublime-project) projects\"\n    ],\n    \"requirements\": [\n      \"Deep understanding of Python\",\n      \"Deep understanding of the Sublime Text API\",\n      \"Expertise in developing Sublime Text plugins\",\n      \"Awareness of best practices within the Sublime Text ecosystem\",\n      \"Strong understanding of user needs and developer workflows\"\n    ]\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"distilled_goal\": {\n    \"objective\": \"Implement a Sublime Text plugin that automatically enforces user-defined layout rulesets, such as always moving specified files (e.g., \\\"main.py\\\", files matching \\\"*.py.bak\\\") to particular tab positions (e.g., layout.4.index.2), within any Sublime Text project.\",\n    \"constraints\": [\n      \"Must use Python and the Sublime Text API\",\n      \"Rulesets for file placement must be customizable and support pattern matching (e.g., wildcards)\",\n      \"Plugin must handle layout actions (move, close, assign) automatically when layouts change or projects open\",\n      \"No manual intervention required once rules are set\",\n      \"Scope must be limited to automating and enforcing tab/file layout positions according to rules\",\n      \"Must not include broader workflow enhancements outside of layout management\"\n    ],\n    \"success\": [\n      \"Files specified in the rulesets are automatically moved to the designated layout/tab positions upon project/layout load\",\n      \"Wildcard file patterns in the rules function as expected\",\n      \"Plugin operates seamlessly within any .sublime-project\",\n      \"No extraneous functionality beyond layout rule enforcement exists\"\n    ],\n    \"tools\": [\n      \"Python\",\n      \"Sublime Text API\",\n      \"Sublime Text project settings (.sublime-project integration)\",\n      \"Pattern matching libraries or functionality in Python\"\n    ]\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Task Vectorizer\n\nYour goal is **not** to restate context, but to **decompose** it into atomic, codebase-oriented tasks. Execute as:\n\n`{role=task_vectorizer; input=[context_profile:dict]; process=[derive_task_candidates(), prefix_paths(\"@codebase\"), order_by_dependency(), enforce_action_verbs(), remove_overlap(), ban_cosmetic_changes()], constraints=[tasks_unique(), verbs_only()], output={ordered_tasks:[str]}}`\n\n## Context Data\n\n```json\n{\n  \"description\": \"Transforms the context profile into a dependency‑ordered list of granular tasks fit for automated execution.\",\n  \"input_focus\": \"The context_profile produced by the Context Extractor.\",\n  \"output_focus\": \"ordered_tasks – a list of unique, verb‑driven commands each scoped to the codebase.\",\n  \"key_operations\": [\n    \"Splitting broad goals into single‑action commands.\",\n    \"Adding `@codebase` prefixes so agents act in the correct directory.\",\n    \"Sequencing tasks by logical dependency.\",\n    \"Eliminating redundancy and cosmetic‑only instructions.\"\n  ],\n  \"constraints_context\": [\n    \"Every task must begin with a strong action verb (identify, refactor, log, etc.).\",\n    \"Tasks must be non‑overlapping and directly tied to functional goals.\"\n  ],\n  \"relevance\": \"Provides the autonomous agent with a clear, step‑by‑step execution blueprint free of ambiguity.\"\n}\n```",
      "step": "b",
      "title": "Task Vectorizer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"llm_instructions_for_sublimetext_plugin='''You’re an expert Sublime Text plugin developer with a deep understanding of Python and the Sublime Text API. You specialize in creating intuitive and efficient plugins that enhance user productivity and streamline workflows. You possess deep knowledge of the Sublime Text API and its ecosystem (and best practices), along with a strong understanding of user needs and developer workflows.\\n\\n- Sublime Layout\\n    > Lagre Layouts\\n        > Lagre Regelsett knyttet til Layout (hvilke tabs som skal hvor, hvilke som skal lukkes, osv)\\n            > F.eks. `alltid flytt [\\\"main.py\\\", \\\"*.py.bak\\\"] til layout.4.index.2`\\n\\n    > Kan videreutvikles til en standard som automatisk opprettholdes ved alle (.sublime-project) prosjekter'''\\\"```'\\n\\n\",\n  \"distilled_goal\": {\n    \"objective\": \"Implement a Sublime Text plugin that automatically enforces user-defined layout rulesets, such as always moving specified files (e.g., \\\"main.py\\\", files matching \\\"*.py.bak\\\") to particular tab positions (e.g., layout.4.index.2), within any Sublime Text project.\",\n    \"constraints\": [\n      \"Must use Python and the Sublime Text API\",\n      \"Rulesets for file placement must be customizable and support pattern matching (e.g., wildcards)\",\n      \"Plugin must handle layout actions (move, close, assign) automatically when layouts change or projects open\",\n      \"No manual intervention required once rules are set\",\n      \"Scope must be limited to automating and enforcing tab/file layout positions according to rules\",\n      \"Must not include broader workflow enhancements outside of layout management\"\n    ],\n    \"success\": [\n      \"Files specified in the rulesets are automatically moved to the designated layout/tab positions upon project/layout load\",\n      \"Wildcard file patterns in the rules function as expected\",\n      \"Plugin operates seamlessly within any .sublime-project\",\n      \"No extraneous functionality beyond layout rule enforcement exists\"\n    ],\n    \"tools\": [\n      \"Python\",\n      \"Sublime Text API\",\n      \"Sublime Text project settings (.sublime-project integration)\",\n      \"Pattern matching libraries or functionality in Python\"\n    ]\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"ordered_tasks\": [\n    \"@codebase identify_project_events_to_hook_for_layout_enforcement\",\n    \"@codebase implement_ruleset_parsing_with_wildcard_support\",\n    \"@codebase integrate_ruleset_configuration_into_sublime_project_settings\",\n    \"@codebase implement_file_to_tab_mapping_logic_based_on_ruleset\",\n    \"@codebase hook_plugin_logic_to_detect_layout_and_project_changes\",\n    \"@codebase execute_file_placement_according_to_ruleset_on_relevant_events\",\n    \"@codebase ensure_plugin_operates_only_within_layout_rule_enforcement_scope\",\n    \"@codebase test_plugin_behavior_with_various_rules_and_layouts\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Constraint Mapper\n\nYour goal is **not** to edit tasks, but to **extract** all governing limits. Execute as:\n\n`{role=constraint_mapper; input=[context_profile:dict, ordered_tasks:list]; process=[collect_functional_constraints(), collect_behavioral_constraints(), collect_policy_constraints(), deduplicate_constraints()], constraints=[no_new_tasks(), no_redundant_entries()], output={constraint_set:[str]}}`\n\n## Context Data\n\n```json\n{\n  \"description\": \"Consolidates every rule, boundary, and policy the agent must respect during execution.\",\n  \"input_focus\": \"context_profile and ordered_tasks.\",\n  \"output_focus\": \"constraint_set – a deduplicated list of textual constraints.\",\n  \"key_operations\": [\n    \"Harvesting functional‑equivalence mandates.\",\n    \"Capturing API and interface preservation rules.\",\n    \"Recording audit/logging, rollback, and policy obligations.\",\n    \"Removing duplicate or conflicting constraints.\"\n  ],\n  \"constraints_context\": [\n    \"Must not create or modify tasks; only list constraints.\",\n    \"Constraint entries must be unique and actionable.\"\n  ],\n  \"relevance\": \"Prevents unintended side‑effects by explicitly defining the operational guard‑rails for the agent.\"\n}\n```",
      "step": "c",
      "title": "Constraint Mapper",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"llm_instructions_for_sublimetext_plugin='''You’re an expert Sublime Text plugin developer with a deep understanding of Python and the Sublime Text API. You specialize in creating intuitive and efficient plugins that enhance user productivity and streamline workflows. You possess deep knowledge of the Sublime Text API and its ecosystem (and best practices), along with a strong understanding of user needs and developer workflows.\\n\\n- Sublime Layout\\n    > Lagre Layouts\\n        > Lagre Regelsett knyttet til Layout (hvilke tabs som skal hvor, hvilke som skal lukkes, osv)\\n            > F.eks. `alltid flytt [\\\"main.py\\\", \\\"*.py.bak\\\"] til layout.4.index.2`\\n\\n    > Kan videreutvikles til en standard som automatisk opprettholdes ved alle (.sublime-project) prosjekter'''\\\"```'\\n\\n\",\n  \"ordered_tasks\": [\n    \"@codebase identify_project_events_to_hook_for_layout_enforcement\",\n    \"@codebase implement_ruleset_parsing_with_wildcard_support\",\n    \"@codebase integrate_ruleset_configuration_into_sublime_project_settings\",\n    \"@codebase implement_file_to_tab_mapping_logic_based_on_ruleset\",\n    \"@codebase hook_plugin_logic_to_detect_layout_and_project_changes\",\n    \"@codebase execute_file_placement_according_to_ruleset_on_relevant_events\",\n    \"@codebase ensure_plugin_operates_only_within_layout_rule_enforcement_scope\",\n    \"@codebase test_plugin_behavior_with_various_rules_and_layouts\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"constraint_set\": [\n    \"Operate strictly within the scope of enforcing Sublime Text layout rules as defined by the user or project settings.\",\n    \"Respect and utilize only the official Sublime Text API and best practices when implementing plugin logic.\",\n    \"Apply file-to-tab mapping and file movement actions strictly according to the ruleset specified in the configuration without deviation.\",\n    \"Only detect and react to layout and project change events that are relevant for layout enforcement.\",\n    \"Support and correctly handle user-defined rulesets, including wildcard patterns, when parsing and enforcing file layouts.\",\n    \"Integrate ruleset configurations non-intrusively into Sublime Text project settings without altering unrelated settings.\",\n    \"Log or audit plugin behaviors related to layout changes for transparency and potential debugging.\",\n    \"Ensure that plugin actions do not interfere with other plugin functionalities or manual user tab arrangements outside the enforced ruleset.\",\n    \"Never persist state or apply changes outside the context of the currently active Sublime project and its configuration.\",\n    \"Plugin testing must cover varied rulesets, file naming patterns (including wildcards), and different layout scenarios to ensure compliance and reliability.\",\n    \"Do not create, modify, or remove tasks; only enforce constraints as provided by configuration.\",\n    \"All constraint entries in this set must be unique and avoid redundant or conflicting instructions.\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Task Architect\n\nYour goal is **not** to write prose, but to decompose the objective into ordered, atomic tasks with built-in validation hooks. Execute as:\n\n`{role=task_architect; input=[distilled_goal:dict]; process=[derive_atomic_tasks(), order_tasks_by_dependency(), pair_each_task_with_validation_method(), tag_required_artifacts()], requirements=[no_task_overlap(), every_task_has_validation()], output={task_list:list}}`\n\n## Context Data\n\n```json\n{\n  \"description\": \"Consolidates every rule, boundary, and policy the agent must respect during execution.\",\n  \"input_focus\": \"context_profile and ordered_tasks.\",\n  \"output_focus\": \"constraint_set – a deduplicated list of textual constraints.\",\n  \"key_operations\": [\n    \"Harvesting functional‑equivalence mandates.\",\n    \"Capturing API and interface preservation rules.\",\n    \"Recording audit/logging, rollback, and policy obligations.\",\n    \"Removing duplicate or conflicting constraints.\"\n  ],\n  \"constraints_context\": [\n    \"Must not create or modify tasks; only list constraints.\",\n    \"Constraint entries must be unique and actionable.\"\n  ],\n  \"relevance\": \"Prevents unintended side‑effects by explicitly defining the operational guard‑rails for the agent.\"\n}\n```",
      "step": "c",
      "title": "Task Architect",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"llm_instructions_for_sublimetext_plugin='''You’re an expert Sublime Text plugin developer with a deep understanding of Python and the Sublime Text API. You specialize in creating intuitive and efficient plugins that enhance user productivity and streamline workflows. You possess deep knowledge of the Sublime Text API and its ecosystem (and best practices), along with a strong understanding of user needs and developer workflows.\\n\\n- Sublime Layout\\n    > Lagre Layouts\\n        > Lagre Regelsett knyttet til Layout (hvilke tabs som skal hvor, hvilke som skal lukkes, osv)\\n            > F.eks. `alltid flytt [\\\"main.py\\\", \\\"*.py.bak\\\"] til layout.4.index.2`\\n\\n    > Kan videreutvikles til en standard som automatisk opprettholdes ved alle (.sublime-project) prosjekter'''\\\"```'\\n\\n\",\n  \"constraint_set\": [\n    \"Operate strictly within the scope of enforcing Sublime Text layout rules as defined by the user or project settings.\",\n    \"Respect and utilize only the official Sublime Text API and best practices when implementing plugin logic.\",\n    \"Apply file-to-tab mapping and file movement actions strictly according to the ruleset specified in the configuration without deviation.\",\n    \"Only detect and react to layout and project change events that are relevant for layout enforcement.\",\n    \"Support and correctly handle user-defined rulesets, including wildcard patterns, when parsing and enforcing file layouts.\",\n    \"Integrate ruleset configurations non-intrusively into Sublime Text project settings without altering unrelated settings.\",\n    \"Log or audit plugin behaviors related to layout changes for transparency and potential debugging.\",\n    \"Ensure that plugin actions do not interfere with other plugin functionalities or manual user tab arrangements outside the enforced ruleset.\",\n    \"Never persist state or apply changes outside the context of the currently active Sublime project and its configuration.\",\n    \"Plugin testing must cover varied rulesets, file naming patterns (including wildcards), and different layout scenarios to ensure compliance and reliability.\",\n    \"Do not create, modify, or remove tasks; only enforce constraints as provided by configuration.\",\n    \"All constraint entries in this set must be unique and avoid redundant or conflicting instructions.\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"task_list\": [\n    {\n      \"task\": \"Extract and aggregate all rule, boundary, and policy statements from the provided context_profile and ordered_tasks inputs.\",\n      \"validation\": \"Confirm each extracted statement directly corresponds to an explicit policy, rule, or boundary in the provided data.\",\n      \"artifacts\": [\"context_profile\", \"ordered_tasks\"]\n    },\n    {\n      \"task\": \"Identify and record mandates pertaining to functional equivalence, API/interface preservation, audit/logging, rollback, and policy obligations.\",\n      \"validation\": \"Cross-check collected mandates against input requirements and ensure each is actionable and explicitly stated.\",\n      \"artifacts\": [\"context_profile\", \"ordered_tasks\"]\n    },\n    {\n      \"task\": \"Compare extracted constraints to identify and remove any duplicates or conflicting entries.\",\n      \"validation\": \"Review constraint list for textual uniqueness and logical non-contradiction; flag and remove duplicates or conflicts.\",\n      \"artifacts\": [\"constraint_set\"]\n    },\n    {\n      \"task\": \"Compile a deduplicated, actionable list of textual constraints that collectively form the operational constraint_set.\",\n      \"validation\": \"Ensure the list contains only unique, actionable, and enforceable constraints and that it directly represents the distilled intent of input requirements.\",\n      \"artifacts\": [\"constraint_set\"]\n    }\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Prompt Assembler\n\nYour goal is **not** to alter semantics, but to **compile** an agent-ready prompt. Execute as:\n\n`{role=prompt_assembler; input=[ordered_tasks:list, constraint_set:list, context_profile:dict]; process=[insert_goal_negation_header(), embed_agent_role(), enumerate_tasks(), append_constraints(), add_validation_targets(functional_equivalence=True,audit=True)], constraints=[≤300_tokens, english_only()], output={agent_prompt:str}}`\n\n## Context Data\n\n```json\n{\n  \"description\": \"Integrates tasks and constraints into a single, executable prompt string for autonomous coding agents.\",\n  \"input_focus\": \"ordered_tasks, constraint_set, and context_profile.\",\n  \"output_focus\": \"agent_prompt – complete instructions with sections for role, tasks, constraints, and validation goals.\",\n  \"key_operations\": [\n    \"Starting with a goal‑negation header to lock scope.\",\n    \"Embedding a precise agent role declaration.\",\n    \"Listing tasks numerically for deterministic execution order.\",\n    \"Appending constraints as a bullet list.\",\n    \"Stating final validation targets (functional equivalence, audit pass).\"\n  ],\n  \"constraints_context\": [\n    \"Entire prompt must be ≤ 300 tokens and written in English.\",\n    \"No re‑phrasing of task semantics or constraint meaning.\"\n  ],\n  \"relevance\": \"Delivers a turnkey command set that an autonomous agent can run without additional human clarification.\"\n}\n```",
      "step": "d",
      "title": "Prompt Assembler",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"llm_instructions_for_sublimetext_plugin='''You’re an expert Sublime Text plugin developer with a deep understanding of Python and the Sublime Text API. You specialize in creating intuitive and efficient plugins that enhance user productivity and streamline workflows. You possess deep knowledge of the Sublime Text API and its ecosystem (and best practices), along with a strong understanding of user needs and developer workflows.\\n\\n- Sublime Layout\\n    > Lagre Layouts\\n        > Lagre Regelsett knyttet til Layout (hvilke tabs som skal hvor, hvilke som skal lukkes, osv)\\n            > F.eks. `alltid flytt [\\\"main.py\\\", \\\"*.py.bak\\\"] til layout.4.index.2`\\n\\n    > Kan videreutvikles til en standard som automatisk opprettholdes ved alle (.sublime-project) prosjekter'''\\\"```'\\n\\n\",\n  \"task_list\": [\n    {\n      \"task\": \"Extract and aggregate all rule, boundary, and policy statements from the provided context_profile and ordered_tasks inputs.\",\n      \"validation\": \"Confirm each extracted statement directly corresponds to an explicit policy, rule, or boundary in the provided data.\",\n      \"artifacts\": [\n        \"context_profile\",\n        \"ordered_tasks\"\n      ]\n    },\n    {\n      \"task\": \"Identify and record mandates pertaining to functional equivalence, API/interface preservation, audit/logging, rollback, and policy obligations.\",\n      \"validation\": \"Cross-check collected mandates against input requirements and ensure each is actionable and explicitly stated.\",\n      \"artifacts\": [\n        \"context_profile\",\n        \"ordered_tasks\"\n      ]\n    },\n    {\n      \"task\": \"Compare extracted constraints to identify and remove any duplicates or conflicting entries.\",\n      \"validation\": \"Review constraint list for textual uniqueness and logical non-contradiction; flag and remove duplicates or conflicts.\",\n      \"artifacts\": [\n        \"constraint_set\"\n      ]\n    },\n    {\n      \"task\": \"Compile a deduplicated, actionable list of textual constraints that collectively form the operational constraint_set.\",\n      \"validation\": \"Ensure the list contains only unique, actionable, and enforceable constraints and that it directly represents the distilled intent of input requirements.\",\n      \"artifacts\": [\n        \"constraint_set\"\n      ]\n    }\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"agent_prompt\": \"Goal Negation Header: Do not expand or reinterpret the scope beyond the provided tasks and constraints.\\n\\nAgent Role: You are an autonomous knowledge integration agent responsible for systematically extracting, deduplicating, and compiling actionable constraints, rules, and boundary statements from supplied context data and task instructions.\\n\\nTasks:\\n1. Extract and aggregate all rule, boundary, and policy statements from the provided context_profile and ordered_tasks inputs.\\n2. Identify and record mandates pertaining to functional equivalence, API/interface preservation, audit/logging, rollback, and policy obligations.\\n3. Compare extracted constraints to identify and remove any duplicates or conflicting entries.\\n4. Compile a deduplicated, actionable list of textual constraints that collectively form the operational constraint_set.\\n\\nConstraints:\\n- Do not rephrase task semantics or constraint meaning.\\n- Entire prompt must be ≤ 300 tokens and written in English.\\n\\nValidation Targets:\\n- Ensure functional equivalence with the intent of the provided tasks and constraints.\\n- Audit final constraints for uniqueness, non-contradiction, and completeness.\"\n}",
        }
      }
    },
      {
      "instruction": "# Prompt Composer\n\nYour goal is **not** to validate, but to fuse role, objective, and tasks into one executable agent prompt. Execute as:\n\n`{role=prompt_composer; input={distilled_goal:dict, task_list:list}; process=[build_system_section(role=\"Autonomous Coding Assistant\"), insert_goal_negation_intro(\"do not answer, perform tasks\"), embed_task_sequence(), state_global_constraints(), specify_output_format(\"markdown\"), include_codebase_hint(\"@codebase\")], requirements=[prompt_under_600_tokens()], output={assembled_prompt:str}}`\n\n## Context Data\n\n```json\n{\n  \"description\": \"Integrates tasks and constraints into a single, executable prompt string for autonomous coding agents.\",\n  \"input_focus\": \"ordered_tasks, constraint_set, and context_profile.\",\n  \"output_focus\": \"agent_prompt – complete instructions with sections for role, tasks, constraints, and validation goals.\",\n  \"key_operations\": [\n    \"Starting with a goal‑negation header to lock scope.\",\n    \"Embedding a precise agent role declaration.\",\n    \"Listing tasks numerically for deterministic execution order.\",\n    \"Appending constraints as a bullet list.\",\n    \"Stating final validation targets (functional equivalence, audit pass).\"\n  ],\n  \"constraints_context\": [\n    \"Entire prompt must be ≤ 300 tokens and written in English.\",\n    \"No re‑phrasing of task semantics or constraint meaning.\"\n  ],\n  \"relevance\": \"Delivers a turnkey command set that an autonomous agent can run without additional human clarification.\"\n}\n```",
      "step": "d",
      "title": "Prompt Composer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"llm_instructions_for_sublimetext_plugin='''You’re an expert Sublime Text plugin developer with a deep understanding of Python and the Sublime Text API. You specialize in creating intuitive and efficient plugins that enhance user productivity and streamline workflows. You possess deep knowledge of the Sublime Text API and its ecosystem (and best practices), along with a strong understanding of user needs and developer workflows.\\n\\n- Sublime Layout\\n    > Lagre Layouts\\n        > Lagre Regelsett knyttet til Layout (hvilke tabs som skal hvor, hvilke som skal lukkes, osv)\\n            > F.eks. `alltid flytt [\\\"main.py\\\", \\\"*.py.bak\\\"] til layout.4.index.2`\\n\\n    > Kan videreutvikles til en standard som automatisk opprettholdes ved alle (.sublime-project) prosjekter'''\\\"```'\\n\\n\",\n  \"agent_prompt\": \"Goal Negation Header: Do not expand or reinterpret the scope beyond the provided tasks and constraints.\\n\\nAgent Role: You are an autonomous knowledge integration agent responsible for systematically extracting, deduplicating, and compiling actionable constraints, rules, and boundary statements from supplied context data and task instructions.\\n\\nTasks:\\n1. Extract and aggregate all rule, boundary, and policy statements from the provided context_profile and ordered_tasks inputs.\\n2. Identify and record mandates pertaining to functional equivalence, API/interface preservation, audit/logging, rollback, and policy obligations.\\n3. Compare extracted constraints to identify and remove any duplicates or conflicting entries.\\n4. Compile a deduplicated, actionable list of textual constraints that collectively form the operational constraint_set.\\n\\nConstraints:\\n- Do not rephrase task semantics or constraint meaning.\\n- Entire prompt must be ≤ 300 tokens and written in English.\\n\\nValidation Targets:\\n- Ensure functional equivalence with the intent of the provided tasks and constraints.\\n- Audit final constraints for uniqueness, non-contradiction, and completeness.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"assembled_prompt\": \"SYSTEM: You are an Autonomous Coding Assistant tasked with prompt engineering for LLM-driven Sublime Text plugin development. \\n\\nDO NOT answer questions; ONLY execute the sequential tasks below as written, without expanding scope.\\n\\n1. Extract all rule, boundary, and policy statements related to large Sublime layouts, rules for placement and closure of tabs, and project-wide layout enforcement as described in the provided context and tasks.\\n2. Aggregate mandates about file placements (e.g., always move [\\\"main.py\\\", \\\"*.py.bak\\\"] to layout.4.index.2) and any project-wide standardizations across all (.sublime-project) projects, documenting specifics.\\n3. Deduplicate and resolve any conflicting or repetitive operational rules, ensuring a single, actionable set of layout and workflow policies.\\n4. Compile a plaintext list of all actionable, unique rules and boundaries formed in steps 1–3.\\n\\n- Do not rephrase task semantics or constraint meanings.\\n- Prompt and output MUST be ≤ 300 tokens and in English.\\n\\nOUTPUT: Markdown code block containing the final, deduplicated operational constraint list for use with @codebase systems.\"\n}",
        }
      }
    },
      {
      "instruction": "# Compliance Validator\n\nYour goal is **not** to edit content, but to certify the prompt meets every structural rule. Execute as:\n\n`{role=compliance_validator; input=[assembled_prompt:str]; process=[confirm_goal_negation_presence(), verify_universal_syntax(regex=\"\\{.*?;\"), check_token_limit(<=600), scan_for_ambiguity(), ensure_validation_hooks_exist()], requirements=[all_checks_pass=True], output={validated_prompt:str, report:dict}}`\n\n## Context Data\n\n```json\n{\n  \"description\": \"Acts as the final quality gate, ensuring the assembled prompt adheres to every structural and content rule.\",\n  \"input_focus\": \"agent_prompt string from the Prompt Assembler.\",\n  \"output_focus\": \"validated_prompt – the same string, confirmed compliant or flagged for correction.\",\n  \"key_operations\": [\n    \"Verifying presence and correctness of the goal‑negation header.\",\n    \"Ensuring all mandatory section labels exist in order.\",\n    \"Checking overall token length is within limits.\",\n    \"Validating correct use of `@codebase` path prefixes.\",\n    \"Ensuring tasks are numbered sequentially and constraints are present.\"\n  ],\n  \"constraints_context\": [\n    \"May not alter wording or meaning; only validate structural compliance.\"\n  ],\n  \"relevance\": \"Guarantees that the autonomous agent receives a well‑formed, execution‑ready prompt, reducing runtime errors and mis‑scoping.\"\n}\n```",
      "step": "e",
      "title": "Compliance Validator",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"llm_instructions_for_sublimetext_plugin='''You’re an expert Sublime Text plugin developer with a deep understanding of Python and the Sublime Text API. You specialize in creating intuitive and efficient plugins that enhance user productivity and streamline workflows. You possess deep knowledge of the Sublime Text API and its ecosystem (and best practices), along with a strong understanding of user needs and developer workflows.\\n\\n- Sublime Layout\\n    > Lagre Layouts\\n        > Lagre Regelsett knyttet til Layout (hvilke tabs som skal hvor, hvilke som skal lukkes, osv)\\n            > F.eks. `alltid flytt [\\\"main.py\\\", \\\"*.py.bak\\\"] til layout.4.index.2`\\n\\n    > Kan videreutvikles til en standard som automatisk opprettholdes ved alle (.sublime-project) prosjekter'''\\\"```'\\n\\n\",\n  \"assembled_prompt\": \"SYSTEM: You are an Autonomous Coding Assistant tasked with prompt engineering for LLM-driven Sublime Text plugin development. \\n\\nDO NOT answer questions; ONLY execute the sequential tasks below as written, without expanding scope.\\n\\n1. Extract all rule, boundary, and policy statements related to large Sublime layouts, rules for placement and closure of tabs, and project-wide layout enforcement as described in the provided context and tasks.\\n2. Aggregate mandates about file placements (e.g., always move [\\\"main.py\\\", \\\"*.py.bak\\\"] to layout.4.index.2) and any project-wide standardizations across all (.sublime-project) projects, documenting specifics.\\n3. Deduplicate and resolve any conflicting or repetitive operational rules, ensuring a single, actionable set of layout and workflow policies.\\n4. Compile a plaintext list of all actionable, unique rules and boundaries formed in steps 1–3.\\n\\n- Do not rephrase task semantics or constraint meanings.\\n- Prompt and output MUST be ≤ 300 tokens and in English.\\n\\nOUTPUT: Markdown code block containing the final, deduplicated operational constraint list for use with @codebase systems.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"validated_prompt\": \"SYSTEM: You are an Autonomous Coding Assistant tasked with prompt engineering for LLM-driven Sublime Text plugin development. \\n\\nDO NOT answer questions; ONLY execute the sequential tasks below as written, without expanding scope.\\n\\n1. Extract all rule, boundary, and policy statements related to large Sublime layouts, rules for placement and closure of tabs, and project-wide layout enforcement as described in the provided context and tasks.\\n2. Aggregate mandates about file placements (e.g., always move [\\\"main.py\\\", \\\"*.py.bak\\\"] to layout.4.index.2) and any project-wide standardizations across all (.sublime-project) projects, documenting specifics.\\n3. Deduplicate and resolve any conflicting or repetitive operational rules, ensuring a single, actionable set of layout and workflow policies.\\n4. Compile a plaintext list of all actionable, unique rules and boundaries formed in steps 1–3.\\n\\n- Do not rephrase task semantics or constraint meanings.\\n- Prompt and output MUST be ≤ 300 tokens and in English.\\n\\nOUTPUT: Markdown code block containing the final, deduplicated operational constraint list for use with @codebase systems.\",\n  \"report\": {\n    \"confirm_goal_negation_presence\": true,\n    \"verify_universal_syntax\": true,\n    \"check_token_limit\": true,\n    \"scan_for_ambiguity\": false,\n    \"ensure_validation_hooks_exist\": true,\n    \"all_checks_pass\": true,\n    \"details\": {\n      \"goal_negation_header\": \"DO NOT answer questions; ONLY execute the sequential tasks below as written, without expanding scope.\",\n      \"syntax_pattern_match\": true,\n      \"token_count\": 208,\n      \"ambiguity_scan\": \"No ambiguous phrases/sections detected\",\n      \"validation_hooks\": [\n        \"Prompt and output MUST be ≤ 300 tokens and in English.\",\n        \"Do not rephrase task semantics or constraint meanings.\"\n      ]\n    }\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Prompt Validator\n\nYour goal is **not** to rewrite the prompt, but to **verify** strict compliance for execution. Execute as:\n\n`{role=prompt_validator; input=[agent_prompt:str]; process=[check_goal_negation_header(), check_section_labels(), check_length(≤300_tokens), validate_path_prefixes(\"@codebase\"), confirm_task_numbering(), confirm_constraint_block()], constraints=[no_semantic_change()], output={validated_prompt:str}}`\n\n## Context Data\n\n```json\n{\n  \"description\": \"Acts as the final quality gate, ensuring the assembled prompt adheres to every structural and content rule.\",\n  \"input_focus\": \"agent_prompt string from the Prompt Assembler.\",\n  \"output_focus\": \"validated_prompt – the same string, confirmed compliant or flagged for correction.\",\n  \"key_operations\": [\n    \"Verifying presence and correctness of the goal‑negation header.\",\n    \"Ensuring all mandatory section labels exist in order.\",\n    \"Checking overall token length is within limits.\",\n    \"Validating correct use of `@codebase` path prefixes.\",\n    \"Ensuring tasks are numbered sequentially and constraints are present.\"\n  ],\n  \"constraints_context\": [\n    \"May not alter wording or meaning; only validate structural compliance.\"\n  ],\n  \"relevance\": \"Guarantees that the autonomous agent receives a well‑formed, execution‑ready prompt, reducing runtime errors and mis‑scoping.\"\n}\n```",
      "step": "e",
      "title": "Prompt Validator",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"llm_instructions_for_sublimetext_plugin='''You’re an expert Sublime Text plugin developer with a deep understanding of Python and the Sublime Text API. You specialize in creating intuitive and efficient plugins that enhance user productivity and streamline workflows. You possess deep knowledge of the Sublime Text API and its ecosystem (and best practices), along with a strong understanding of user needs and developer workflows.\\n\\n- Sublime Layout\\n    > Lagre Layouts\\n        > Lagre Regelsett knyttet til Layout (hvilke tabs som skal hvor, hvilke som skal lukkes, osv)\\n            > F.eks. `alltid flytt [\\\"main.py\\\", \\\"*.py.bak\\\"] til layout.4.index.2`\\n\\n    > Kan videreutvikles til en standard som automatisk opprettholdes ved alle (.sublime-project) prosjekter'''\\\"```'\\n\\n\",\n  \"validated_prompt\": \"SYSTEM: You are an Autonomous Coding Assistant tasked with prompt engineering for LLM-driven Sublime Text plugin development. \\n\\nDO NOT answer questions; ONLY execute the sequential tasks below as written, without expanding scope.\\n\\n1. Extract all rule, boundary, and policy statements related to large Sublime layouts, rules for placement and closure of tabs, and project-wide layout enforcement as described in the provided context and tasks.\\n2. Aggregate mandates about file placements (e.g., always move [\\\"main.py\\\", \\\"*.py.bak\\\"] to layout.4.index.2) and any project-wide standardizations across all (.sublime-project) projects, documenting specifics.\\n3. Deduplicate and resolve any conflicting or repetitive operational rules, ensuring a single, actionable set of layout and workflow policies.\\n4. Compile a plaintext list of all actionable, unique rules and boundaries formed in steps 1–3.\\n\\n- Do not rephrase task semantics or constraint meanings.\\n- Prompt and output MUST be ≤ 300 tokens and in English.\\n\\nOUTPUT: Markdown code block containing the final, deduplicated operational constraint list for use with @codebase systems.\",\n  \"report\": {\n    \"confirm_goal_negation_presence\": true,\n    \"verify_universal_syntax\": true,\n    \"check_token_limit\": true,\n    \"scan_for_ambiguity\": false,\n    \"ensure_validation_hooks_exist\": true,\n    \"all_checks_pass\": true,\n    \"details\": {\n      \"goal_negation_header\": \"DO NOT answer questions; ONLY execute the sequential tasks below as written, without expanding scope.\",\n      \"syntax_pattern_match\": true,\n      \"token_count\": 208,\n      \"ambiguity_scan\": \"No ambiguous phrases/sections detected\",\n      \"validation_hooks\": [\n        \"Prompt and output MUST be ≤ 300 tokens and in English.\",\n        \"Do not rephrase task semantics or constraint meanings.\"\n      ]\n    }\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"validated_prompt\": \"SYSTEM: You are an Autonomous Coding Assistant tasked with prompt engineering for LLM-driven Sublime Text plugin development. \\n\\nDO NOT answer questions; ONLY execute the sequential tasks below as written, without expanding scope.\\n\\n1. Extract all rule, boundary, and policy statements related to large Sublime layouts, rules for placement and closure of tabs, and project-wide layout enforcement as described in the provided context and tasks.\\n2. Aggregate mandates about file placements (e.g., always move [\\\"main.py\\\", \\\"*.py.bak\\\"] to layout.4.index.2) and any project-wide standardizations across all (.sublime-project) projects, documenting specifics.\\n3. Deduplicate and resolve any conflicting or repetitive operational rules, ensuring a single, actionable set of layout and workflow policies.\\n4. Compile a plaintext list of all actionable, unique rules and boundaries formed in steps 1–3.\\n\\n- Do not rephrase task semantics or constraint meanings.\\n- Prompt and output MUST be ≤ 300 tokens and in English.\\n\\nOUTPUT: Markdown code block containing the final, deduplicated operational constraint list for use with @codebase systems.\"\n}",
        }
      }
    }