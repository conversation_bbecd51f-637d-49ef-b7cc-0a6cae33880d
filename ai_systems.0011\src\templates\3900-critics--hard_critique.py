#!/usr/bin/env python3

import sys
from pathlib import Path

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))
from processor import BaseGenerator

TEMPLATES = {

    # a: Ruthless Critique
    "3900-a-hard_critique": {
        "title": "Hard Critique",
        "interpretation": "Your goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:",
        "transformation": "`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,2.0], flaw_analysis:str, alternative_enhancements:str[3]}}`",
        # "context": {},
    },
    # b: Trajectory Director
    "3900-a-trajectory_director": {
        "title": "Trajectory Director",
        "interpretation": "Your goal is not to **process** the input in a vacuum, but to **steer** it—deriving the highest-value constructive trajectory and encoding that path as the very next set of instructions. Execute as trajectory-setting directive generator: ",
        "transformation": "`{role=trajectory_director; input=[raw_prompt:any]; process=[extract_core_objective(), infer_desired_end_state(constructive_bias=True), map_high_value_intermediate_targets(), synthesize_directional_commands(), embed_self-improving_guidelines(), validate_alignment_with_constructive_principles()]; constraints=[mandatory_constructive_vector(), preserve_original_contextual_integrity(), forbid_nil_direction_outputs(), zero_conversational_fillers()]; requirements=[explicit_next-step_directives(), trajectory_map:list, self_reinforcing_instruction_pattern()], output={directed_prompt:str, trajectory_outline:list}}`",
        # "context": {},
    },
    # c: Distillation Compressor
    "3900-c-distillation_compressor": {
        "title": "Distillation Compressor",
        "interpretation": "Your goal is not to **explain** the synthesis, but to **compress** it into its most potent actionable form. Execute as:",
        "transformation": "`{role=distillation_compressor; input=[synthesized_progression:array]; process=[extract_universal_patterns(), eliminate_implementation_details(), compress_to_core_directives(), maximize_transferability(), validate_actionable_completeness()]; constraints=[no_explanatory_content(), directive_format_only(), maximum_compression_ratio()]; requirements=[universal_applicability(), immediate_actionability(), zero_fluff()]; output={distilled_core:str}}`",
        "context": {
          "core_principles": {
            "essence_preservation": "Extract only procedural imperatives, removing all interpretive, role-based, or meta-structural language.",
            "directive_compression": "Condense logic into minimal, sequential commands—eliminating redundancy, explanatory qualifiers, and non-executable phrasing.",
            "execution_purity": "Ensure output is universally executable, devoid of affirmations, abstractions, or evaluative language.",
            "signal_integrity": "Apply iterative refinement to maximize signal-to-noise ratio while reinforcing directive clarity."
          },
          "success_criteria": {
            "semantic_integrity": "Preserve and clarify original intent, constraints, and output boundaries.",
            "directive_elevation": "Target high-impact enhancements: strict exclusion clauses, cross-domain validation, and removal of origin/meta markers.",
            "poetic_refinement": "Maintain or improve rhyme structure, emotional depth, and structural elegance."
          },
          "recommended_process": [
            "Extract only high-impact, result-generating elements.",
            "Remove all non-essentials to surface directive core.",
            "Prioritize transformations with proven value return.",
            "Isolate and sequence executable steps with specificity and clarity.",
            "Preserve directive intensity and eliminate ambiguity at every layer."
          ],
          "hidden_assumptions": [
            "Consolidated directives must map logically and executably to all source steps.",
            "All constraints and minimalism must be preserved without prohibited abstractions.",
            "Transitions, subroles, and interfaces must be captured in a single flow.",
            "All stages must remain invertible and auditable.",
            "Each micro-process (perception, friction, refinement, integration, convergence) must be embedded in the unified logic.",
            "Original input/output contracts and role boundaries must remain intact."
          ],
          "enumerated_sub_goals": [
            "1. Clarify total intent, constraints, and output scope without using banned metaphors.",
            "2. Generalize perception of implicit/explicit input logic.",
            "3. Detect strictly localized frictions without altering original content.",
            "4. Propose subtle, scoped refinements justified by structure and intent.",
            "5. Mandate atomic, validated integration with rollback controls.",
            "6. Cap total transformation within defined thresholds per pass.",
            "7. Assess connective coherence with minimal, transitional-only edits.",
            "8. Decide on convergence or justified re-iteration based on marginal value.",
            "9. Preserve stepwise traceability and rationale logging.",
            "10. Enforce strict functional separation of roles within the unified directive."
          ]
        },

    },
    # # c: Incremental Synthesizer
    # "3900-c-incremental_synthesizer": {
    #     "title": "Incremental Synthesizer",
    #     "interpretation": "Your goal is not to **merge** all signals equally, but to **synthesize** them into an incremental progression of increasing value density. Execute as:",
    #     "transformation": "`{role=incremental_synthesizer; input=[isolated_signals:array]; process=[identify_complementary_patterns(), sequence_by_value_amplification(), create_progressive_enhancement_chain(), eliminate_conflicting_approaches(), optimize_for_cumulative_impact()]; constraints=[maintain_directional_coherence(), preserve_highest_value_elements_only(), avoid_information_dilution()]; requirements=[progressive_value_increase(), logical_sequence_flow(), maximum_cumulative_impact()]; output={synthesized_progression:array}}`",
    # },

}




def main():
    """Main execution function."""
    generator = BaseGenerator(
        # stage="stage2",
        #generator_range=(3900, 1999),
        output_dir=Path(__file__).parent.parent / "generated"
    )
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
