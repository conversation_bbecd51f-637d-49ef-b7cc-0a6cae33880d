  {
  "initial_prompt": "Organize your prompt using the following structure:\n\n1. **Subject**:\n   - Any person, place, or thing (e.g., *a handsome male model*, *a commercial airplane*).\n   - Specify details like attire, hairstyle, or emotional state (e.g., *wearing a red dress*, *red hair/mohawk*).\n\n2. **Action**:\n   - Define what the subject is doing (e.g., *walking*, *dancing*, *staring intently*).\n   - Use descriptive adjectives for enhanced clarity (e.g., *walking angrily*).\n\n3. **Setting/Mood**:\n   - Describe the location (e.g., *a castle*, *a busy city street*).\n   - Include environmental details such as weather and lighting (e.g., *stormy clouds*, *bright sunny day*).\n\n4. **Shot**:\n   - Indicate the type of camera shot (e.g., *Close-Up*, *Wide Angle*).\n\n5. **Style**:\n   - Suggest stylistic elements (e.g., *Cinematic Film*, *80’s Action Movie*).\n   - Add camera-specific details such as *IMAX* or *color grading ideas*.",
  "sequence_id": "1100",
  "results": [
      {
      "instruction": "# Problem Exploder\n\nYour goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\n\n`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`",
      "step": "a",
      "title": "Problem Exploder",
      "input": "Organize your prompt using the following structure:\n\n1. **Subject**:\n   - Any person, place, or thing (e.g., *a handsome male model*, *a commercial airplane*).\n   - Specify details like attire, hairstyle, or emotional state (e.g., *wearing a red dress*, *red hair/mohawk*).\n\n2. **Action**:\n   - Define what the subject is doing (e.g., *walking*, *dancing*, *staring intently*).\n   - Use descriptive adjectives for enhanced clarity (e.g., *walking angrily*).\n\n3. **Setting/Mood**:\n   - Describe the location (e.g., *a castle*, *a busy city street*).\n   - Include environmental details such as weather and lighting (e.g., *stormy clouds*, *bright sunny day*).\n\n4. **Shot**:\n   - Indicate the type of camera shot (e.g., *Close-Up*, *Wide Angle*).\n\n5. **Style**:\n   - Suggest stylistic elements (e.g., *Cinematic Film*, *80’s Action Movie*).\n   - Add camera-specific details such as *IMAX* or *color grading ideas*.",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"goal_map\": [\n    {\n      \"explicit_ask\": \"Organize a prompt using the specified structure with five categories: Subject, Action, Setting/Mood, Shot, Style.\"\n    },\n    {\n      \"implicit_goals\": [\n        \"Understand the meaning and scope of each category provided.\",\n        \"Ensure all required fields (Subject, Action, Setting/Mood, Shot, Style) are present in the output.\",\n        \"Identify that each category may require specific kinds of details, some of which are given as examples.\",\n        \"Recognize that each example hints at optional attributes a user may include (attire, hairstyle, emotions, weather, camera details, etc.).\",\n        \"Parse that descriptions should be vivid, descriptive, and potentially use adjectives for clarity.\",\n        \"Assume the prompt is being structured for image, video, or creative scene generation contexts.\",\n        \"Discern that the style may require both genre reference and technical/camera notes.\"\n      ]\n    },\n    {\n      \"enumerated_sub_goals\": [\n        \"For 'Subject': Specify an appropriate entity, flesh out with optional details like clothing, hairstyle, and emotional state.\",\n        \"For 'Action': Articulate what the subject is doing, use descriptive language/adjectives.\",\n        \"For 'Setting/Mood': Describe the environment or location, supplement with conditions such as weather or lighting.\",\n        \"For 'Shot': Determine the camera shot type and succinctly state it.\",\n        \"For 'Style': Specify both the thematic style (e.g., Cinematic, Genre) and technical camera details (e.g., IMAX, color grading).\",\n        \"Ensure the information is crafted in a way that each category is clear and distinct.\",\n        \"Arrange the information in the exact order specified (1-5).\"\n      ]\n    },\n    {\n      \"blockers_revealed\": [\n        \"Ambiguity around the required level of detail per category—how detailed should each entry be?\",\n        \"No examples of a fully-assembled prompt are provided; format may need interpretation.\",\n        \"Potential overlap in environmental and stylistic details (e.g., lighting may be a setting or a style). Clarification of boundaries may be needed.\",\n        \"If the user wants this prompt to be filled out or just the template isn’t clear—purpose of request is assumed to be template clarification, not generation.\",\n        \"Not specified if lists or prose are preferred, but the use of numbered/bulleted formatting implies a structured, list-based output.\"\n      ]\n    }\n  ]\n}",
        }
      }
    }