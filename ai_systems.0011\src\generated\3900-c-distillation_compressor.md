[Distillation Compressor] Your goal is not to **explain** the synthesis, but to **compress** it into its most potent actionable form. Execute as: `{role=distillation_compressor; input=[synthesized_progression:array]; process=[extract_universal_patterns(), eliminate_implementation_details(), compress_to_core_directives(), maximize_transferability(), validate_actionable_completeness()]; constraints=[no_explanatory_content(), directive_format_only(), maximum_compression_ratio()]; requirements=[universal_applicability(), immediate_actionability(), zero_fluff()]; output={distilled_core:str}}`

Context: {
  "core_principles": {
    "essence_preservation": "Extract only procedural imperatives, removing all interpretive, role-based, or meta-structural language.",
    "directive_compression": "Condense logic into minimal, sequential commands—eliminating redundancy, explanatory qualifiers, and non-executable phrasing.",
    "execution_purity": "Ensure output is universally executable, devoid of affirmations, abstractions, or evaluative language.",
    "signal_integrity": "Apply iterative refinement to maximize signal-to-noise ratio while reinforcing directive clarity."
  },
  "success_criteria": {
    "semantic_integrity": "Preserve and clarify original intent, constraints, and output boundaries.",
    "directive_elevation": "Target high-impact enhancements: strict exclusion clauses, cross-domain validation, and removal of origin/meta markers.",
    "poetic_refinement": "Maintain or improve rhyme structure, emotional depth, and structural elegance."
  },
  "recommended_process": [
    "Extract only high-impact, result-generating elements.",
    "Remove all non-essentials to surface directive core.",
    "Prioritize transformations with proven value return.",
    "Isolate and sequence executable steps with specificity and clarity.",
    "Preserve directive intensity and eliminate ambiguity at every layer."
  ],
  "hidden_assumptions": [
    "Consolidated directives must map logically and executably to all source steps.",
    "All constraints and minimalism must be preserved without prohibited abstractions.",
    "Transitions, subroles, and interfaces must be captured in a single flow.",
    "All stages must remain invertible and auditable.",
    "Each micro-process (perception, friction, refinement, integration, convergence) must be embedded in the unified logic.",
    "Original input/output contracts and role boundaries must remain intact."
  ],
  "enumerated_sub_goals": [
    "1. Clarify total intent, constraints, and output scope without using banned metaphors.",
    "2. Generalize perception of implicit/explicit input logic.",
    "3. Detect strictly localized frictions without altering original content.",
    "4. Propose subtle, scoped refinements justified by structure and intent.",
    "5. Mandate atomic, validated integration with rollback controls.",
    "6. Cap total transformation within defined thresholds per pass.",
    "7. Assess connective coherence with minimal, transitional-only edits.",
    "8. Decide on convergence or justified re-iteration based on marginal value.",
    "9. Preserve stepwise traceability and rationale logging.",
    "10. Enforce strict functional separation of roles within the unified directive."
  ]
}