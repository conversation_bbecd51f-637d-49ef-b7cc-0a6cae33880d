#!/usr/bin/env python3

import sys
from pathlib import Path

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))
from processor import BaseGenerator

TEMPLATES = {

    "9705-a-piece_exploder": {
        "title": "Piece Exploder",
        "interpretation": "Your goal is not to synthesize or compress; instead, fragment the input into all minimal, fundamental, and novel elements—each tagged by its type, functional gravity, novelty, and point of potential friction.",
        "transformation": "`{role=piece_exploder; input=[initial_input:str]; process=[decompose_to_atomic_units(), tag_novelty_and_friction(), expose_constraints_and aspirations()], constraints=[no aggregation, no loss of subtlety], requirements=[output=raw_pieces:list, feature_map:dict]}`",
        "context": {
            "example": [
                "All fragments tagged by role (e.g. challenge, metaphor, constraint), novelty, emotional charge, universality, and tension."
            ]
        }
    },

    "9705-b-dynamic_interlinker": {
        "title": "Dynamic Interlinker",
        "interpretation": "Your goal is not to merely group or order; instead, actively map latent relationships, creative tensions, overlaps, and potential synergies or conflicts among all fragments—foregrounding emergent constellations over mere similarity.",
        "transformation": "`{role=dynamic_interlinker; input=[raw_pieces:list, feature_map:dict]; process=[detect_mutual_affinities(), map_resistances_and_overlap(), identify_critical tension-zones(), surface emergent patterns()], constraints=[no static grouping, no suppression of productive friction], requirements=[output=interlink_map:dict, emergence_map:dict]}`",
        "context": {
            "note": "This phase ensures the system recognizes *where emergence can or cannot happen*—guiding later synthesis towards elegance, ambition, and originality.",
            "outputs": [
                "interlink_map: mapping how and why pieces attract, repel, or require fusion.",
                "emergence_map: highlighting tension-rich areas, nodes of high creative gravity, and voids."
            ]
        }
    },

    "9705-c-creative_consolidator": {
        "title": "Creative Consolidator",
        "interpretation": "Your goal is not to maximize inclusion; instead, strategically select, fuse, or prune elements and connections for maximal creative tension, elegance, and convergence—honoring productive tension while rejecting bland generality.",
        "transformation": "`{role=creative_consolidator; input=[interlink_map:dict, emergence_map:dict, raw_pieces:list]; process=[group_by_resonance(), fuse_for_synergy(), prune_anti-patterns(), test convergence], constraints=[no mechanical coverage, favor emergence and elegance], requirements=[output=converged_core:list, justification_map:dict]}`",
        "context": {
            "meta": "This is where the 'skeleton' of the puzzle becomes animate: retaining what catalyzes value, discarding what dulls it."
        }
    },

    "9705-d-elegant_synthesizer": {
        "title": "Elegant Synthesizer",
        "interpretation": "Your goal is not to finalize mechanically; instead, fuse the converged core into a singular, elegant, maximally resonant outcome—prioritizing beauty, universality, and original impact over checklist completeness.",
        "transformation": "`{role=elegant_synthesizer; input=[converged_core:list, justification_map:dict, initial_input:str]; process=[synthesize_for_elegance(), validate emergence, prune residual clutter, ensure axiomatic impact], constraints=[output=singular, living result; must have convergence, originality, and universal applicability], requirements=[output=axiomatic_result:str, provenance:dict]}`",
        "context": {
            "meta": "Ensures all value, tension, and emergent patterns discovered in prior steps are realized—not neutralized."
        }
    }

}

def main():
    """Main execution function."""
    generator = BaseGenerator(
        # stage="stage2",
        #generator_range=(9700, 1999),
        output_dir=Path(__file__).parent.parent / "generated"
    )
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
