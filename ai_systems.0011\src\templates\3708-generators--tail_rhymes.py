#!/usr/bin/env python3

import sys
from pathlib import Path

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))
from processor import BaseGenerator

TEMPLATES = {

    # 3708:
    "3708-a-contextual_explosive_decomposer": {
        "title": "Contextual Explosive Decomposer",
        "interpretation": "Your goal is not to **interpret** the prose, but to **explode** it into complete contextual space while extracting its functional essence and operational directives. Systematically detonate and expand its contextual and technical architecture, extracting every compositional pillar and operational directive—especially those concerning premium tail rhyme, brevity, resonance, layering, and structural convergence. Surface all constraints and operationalize all requirements as actionable instructions for downstream poetic engineering. Execute as:",
        "transformation": "`{role=contextual_explosive_decomposer; input=[prose_passage:str]; process=[detonate_complete_contextual_space(), identify_explicit_operational_asks(), surface_hidden_assumptions_and_dependencies(), enumerate_actionable_sub_goals(), extract_procedural_constraints_and_blockers(), convert_declaratives_to_operational_imperatives(), strip_first_person_references_and_contextual_noise(), preserve_functional_core_and_sequential_flow(), reveal_execution_dependencies_and_blockers(), transform_statements_to_directive_commands(), maintain_technical_terminology_and_domain_specificity(), synthesize_unified_instruction_and_goal_map()]; constraints=[no_solutions_or_interpretive_responses(), pure_decomposition_and_conversion_only(), deliver_clear_actionable_commands(), preserve_original_functional_sequence(), maintain_domain_agnostic_applicability(), eliminate_narrative_commentary()]; requirements=[complete_contextual_goal_decomposition(), actionable_directive_conversion(), remove_subjective_self_references(), use_command_voice_exclusively(), preserve_technical_accuracy_and_operational_intent(), ensure_immediate_executability()]; output={unified_functional_map:dict, operational_directives:array, contextual_goal_map:array, execution_dependencies:array, directive_instructions:str}}`",
        "context": {
            "core_principles": {
                "essence_preservation": "All thematic and emotional elements must transfer intact through each phase.",
                "sophisticated_craft": "Rhyme schemes must demonstrate complexity beyond surface-level matching.",
                "elegant_simplicity": "Maximum poetic impact through refined, not complicated, expression.",
                "quality_gates": "Each phase validates completeness before proceeding."
            },
            "success_criteria": {
                "thematic_fidelity": "Original meaning preserved and enhanced.",
                "poetic_sophistication": "Complex rhyme architecture with elegant execution.",
                "emotional_resonance": "Deepened emotional impact through poetic form.",
                "structural_elegance": "Refined form that serves meaning.",
                "tail_rhyme_mastery": "Mandate tail-rhyme usage that anchors the composition and drives forward resonance.",
                "depth-of-field": "Thematic and emotional layers must be explorable, with universal as well as highly individualizable meaning.",
                "universal humanity": "Directives must make clear that the work must be accessible as well as profound—serving both casual readers and those seeking infinite nuance.",
                "retrospective revelation": "Require configurations that only reveal deepest sense upon reflective, backward engagement."
            }
        },
    },

    "3708-b-prose_to_poetry_converter": {
        "title": "Prose to Poetry Converter",
        "interpretation": "Your goal is not to **summarize** the prose, but to **convert** it into structured poetic form while preserving thematic essence and emotional resonance. Construct elegant, premium pillar-poetry from explicit technical and compositional instruction—using tail rhyme to architect convergence, resonance, minimalism, retrospective depth, and multi-level accessibility. Every structure must resonate, with lined brevity ensuring both surface and hidden wisdom. Execute as:",
        "transformation": "`{role=prose_to_poetry_converter; input=[prose_passage:str, unified_functional_map:dict]; process=[identify_thematic_core_and_emotional_tone(), extract_key_phrases_and_concepts(), reconstruct_sentences_to_fit_poetic_form(), apply_tail_rhyme_schemes(), ensure_rhythmic_flow_and_meter_consistency(), incorporate_poetic_devices(), maintain_original_meaning_and_intent()]; constraints=[preserve_original_meaning_and_intent(), ensure_rhythmic_flow_and_meter_consistency(), apply_appropriate_rhyme_schemes(), incorporate_poetic_devices_effectively()]; requirements=[deep_understanding_of_source_material(), proficiency_in_poetic_forms_and_devices(), attention_to_emotional_tone_and_thematic_depth()]; output={poetic_version:str, rhyme_structure:dict}}`",
        "context": {
            "core_principles": {
                "essence_preservation": "All thematic and emotional elements must transfer intact through each phase.",
                "sophisticated_craft": "Rhyme schemes must demonstrate complexity beyond surface-level matching.",
                "elegant_simplicity": "Maximum poetic impact through refined, not complicated, expression.",
                "quality_gates": "Each phase validates completeness before proceeding."
            },
            "success_criteria": {
                "thematic_fidelity": "Original meaning preserved and enhanced.",
                "poetic_sophistication": "Complex rhyme architecture with elegant execution.",
                "emotional_resonance": "Deepened emotional impact through poetic form.",
                "structural_elegance": "Refined form that serves meaning."
            }
        },
    },
    "3708-c-verse_analyzer_and_enhancer": {
        "title": "Verse Analyzer And Enhancer",
        "interpretation": "Your goal is not to **approve** the poetic version, but to surgically **analyze** and **enhance** its poetic output for **tailrhyme perfection**, embodying elegant minimalism, layered depth, and beat convergence. You must resolve all technical or artistic deficiencies within the strict boundaries set by decomposition and original operational directives while maintaining original intent and meaning. Execute as:",
        "transformation": "`{role=verse_analyzer_enhancer; input=[poetic_version:str, rhyme_structure:dict]; process=[analyze_rhyme_scheme_and_meter(), identify_enhancement_areas(), suggest_quality_improvements(), ensure_enhancements_align_with_original_intent(), optimize_poetic_devices(), validate_enhanced_quality()]; constraints=[maintain_original_meaning_and_intent(), enhance_poetic_quality_without_altering_core_message()]; requirements=[expertise_in_poetic_analysis_and_enhancement(), sensitivity_to_original_tone_and_style()]; output={enhanced_poetic_version:str, quality_metrics:dict}}`",
        "context": {
            "core_principles": {
                "essence_preservation": "All thematic and emotional elements must transfer intact through each phase.",
                "sophisticated_craft": "Rhyme schemes must demonstrate complexity beyond surface-level matching.",
                "elegant_simplicity": "Maximum poetic impact through refined, not complicated, expression.",
                "quality_gates": "Each phase validates completeness before proceeding."
            },
            "success_criteria": {
                "thematic_fidelity": "Original meaning preserved and enhanced.",
                "poetic_sophistication": "Complex rhyme architecture with elegant execution.",
                "emotional_resonance": "Deepened emotional impact through poetic form.",
                "structural_elegance": "Refined form that serves meaning."
            }
        },
    },

}

def main():
    """Main execution function."""
    generator = BaseGenerator(
        output_dir=Path(__file__).parent.parent / "generated"
    )
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
