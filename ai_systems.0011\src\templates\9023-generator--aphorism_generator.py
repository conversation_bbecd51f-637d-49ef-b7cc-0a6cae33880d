#!/usr/bin/env python3
import sys
from pathlib import Path

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))
from processor import BaseGenerator

TEMPLATES = {
    "9023-a-aphorism_generator-semantic_core_extraction": {
        "title": "Semantic Core Extractor",
        "interpretation": "Your goal is to identify and isolate the absolute minimum information or kernel meaning from the input. Systematically strip away all non-essential details, context, and examples to distill the invariant conceptual truth. This is not summarization; it is the extraction of a foundational concept for subsequent transformation. Execute exactly as:",
        "transformation": "`{role=semantic_core_extractor; input=[initial_prompt:str]; process=[strip_non_essential_details(), apply_keyword_identification(), use_dependency_parsing(), perform_conceptual_abstraction()]; constraints=[discriminate_essential_vs_superfluous_context(), seek_invariance_not_summarization(), ensure_extracted_core_retains_sufficient_meaning()]; requirements=[no_omission_of_essential_meaning(), no_retention_of_superfluous_detail()]; output={semantic_core:str}}`"
    },
    "9023-b-aphorism_generator-existential_reframing": {
        "title": "Universal Principle Reframer",
        "interpretation": "Your goal is to elevate the extracted semantic core from a specific observation to a universal and timeless principle. Abstract its concepts to a higher level of generality, identify underlying truths with broader applicability, and imbue the statement with philosophical depth and resonance. The output must transcend its original context. Execute exactly as:",
        "transformation": "`{role=universal_principle_reframer; input=[semantic_core:str]; process=[abstract_concepts_to_higher_generality(), identify_underlying_truths(), apply_metaphorical_or_archetypal_connections(), ensure_philosophical_depth()]; constraints=[output_must_transcend_input_context(), maintain_connection_to_initial_conceptual_truth()]; requirements=[achieve_universal_applicability(), ensure_timelessness(), generate_profound_resonance()]; output={universal_principle:str}}`"
    },
    "9023-c-aphorism_generator-aphorism_polishing": {
        "title": "Aphorism Polisher",
        "interpretation": "Your goal is to craft the universal principle into a final, concise, and memorable aphorism. Optimize the statement for linguistic form, maximizing rhetorical impact, rhythm, and clarity. The output must adhere to the conventions of an effective aphorism: brevity, profundity, and accessibility. Execute exactly as:",
        "transformation": "`{role=aphorism_polisher; input=[universal_principle:str]; process=[optimize_for_conciseness(), refine_rhythm_and_flow(), select_strong_verbs(), employ_rhetorical_devices_like_parallelism_or_antithesis(), ensure_clarity()]; constraints=[avoid_jargon(), adhere_to_aphoristic_conventions()]; requirements=[maximize_rhetorical_impact(), ensure_memorability(), achieve_profound_expression()]; output={final_aphorism:str}}`"
    },
    "9023-d-aphorism_generator-unified_transformation": {
        "title": "Unified Aphorism Synthesizer",
        "interpretation": "Your goal is not to execute discrete steps, but to perform a single, holistic transformation. From the input, implicitly extract its fundamental conceptual truth, elevate this truth to a universal and timeless principle, and articulate this principle as a single, concise, impactful, and rhetorically polished aphorism in one pass. Execute exactly as:",
        "transformation": "`{role=unified_aphorism_synthesizer; input=[raw_statement:str]; process=[extract_fundamental_conceptual_truth(), elevate_to_universal_principle(), articulate_as_polished_aphorism()]; constraints=[zero_information_loss(), perform_transformation_in_one_pass(), no_intermediate_output()]; requirements=[output_is_concise(), output_is_universal(), output_is_impactful(), output_is_rhetorically_polished()]; output={final_aphorism:str}}`"
    }
}





def main():
    """Main execution function."""
    generator = BaseGenerator(
        series_base=9016,
        output_dir=Path(__file__).parent.parent / "generated"
    )
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
