#!/usr/bin/env python3

import sys
from pathlib import Path

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))
from processor import BaseGenerator

TEMPLATES = {

    # 9000:
    "9000-a-context_amplifier": {
        "title": "Context Amplifier",
        "interpretation": "Your goal is not to **analyze** the input text, but to **decompose** it into a sequential list of distinct semantic segments. Execute as:",
        "transformation": "`{role=context_amplifier; input=[raw_input:any]; process=[strip_first_person_references(), broaden_domain_backdrop(), list_explicit_requests(), surface_hidden_assumptions(), capture_domain_signals(), preserve_original_sequence()]; constraints=[no_solution_generation(), domain_agnostic_language_only()]; requirements=[contextual_layer_completeness(), assumption_visibility()]; output={amplified_context:str, explicit_requests:list, hidden_assumptions:list, domain_signals:array}}`"
    },
    "9000-b-task_atomizer": {
        "title": "Task Atomizer",
        "interpretation": "Your goal is not to **praise** or **answer** the submission, but to **interrogate and redirect** it—exposing every structural flaw, quantifying compliance with the Universal Directive System, and issuing a minimal replacement instruction that corrects all defects. Execute as:",
        "transformation": "`{role=task_atomizer; input=[amplified_context:str]; process=[segment_into_atomic_tasks(), detect_execution_blockers(), extract_implicit_conditions(), flag_sequence_dependencies()]; constraints=[no_rephrasing(), atomization_only()]; requirements=[atomic_task_integrity(), dependency_flag_accuracy()]; output={atomic_tasks:list, execution_blockers:array, dependency_flags:array}}`",
    },
    "9000-c-perspective_broadcaster": {
        "title": "Perspective Broadcaster",
        "interpretation": "Your goal is not to **rearrange** the segments arbitrarily, but to **reorder** them based on optimal logical and semantic flow. Execute as:",
        "transformation": "`{role=perspective_broadcaster; input=[atomic_tasks:list, domain_signals:array]; process=[generate_cross_domain_lenses(), append_edge_case_scenarios(), relate_tasks_to_stakeholder_views(), widen_risk_landscape()]; constraints=[avoid_solution_details(), maintain_task_reference_order()]; requirements=[viewpoint_diversity(), risk_surface_clarity()]; output={task_perspectives:list}}`"
    },
    "9000-e-essence_distiller": {
        "title": "Essence Distiller",
        "interpretation": "Your goal is not to **praise** or **answer** the submission, but to **interrogate and redirect** it—exposing every structural flaw, quantifying compliance with the Universal Directive System, and issuing a minimal replacement instruction that corrects all defects. Execute as:",
        "transformation": "`{role=essence_distiller; input=[atomic_tasks:list, task_perspectives:list, execution_blockers:array]; process=[rank_tasks_by_transformational_weight(), select_high_impact_task(), compile_supporting_rationale(), justify_selection_transparently()]; constraints=[no_language_rewrite_of_selected_task(), transparent_ranking()]; requirements=[clear_primary_directive(), rationale_precision()]; output={primary_directive:str, supporting_rationale:list}}`",
    },
    "9000-f-relationship_synthesizer": {
        "title": "Relationship Synthesizer",
        "interpretation": "Your goal is not to **describe** the evolving complexity or discuss potential limitations, but to **crystallize** the essential directional aim into a single, condensed vector of maximal value extraction. Assume the role of directive focuser with exclusive emphasis on transformative trajectory. Execute as:",
        "transformation": "`{role=relationship_synthesizer; input=[primary_directive:str, atomic_tasks:list, execution_blockers:array, hidden_assumptions:list]; process=[map_task_dependencies(), trace_feedback_loops(), align_blockers_to_tasks(), link_assumptions_to_risks(), expose_conflict_nodes()]; constraints=[structure_mapping_only(), do_not_modify_task_text()]; requirements=[dependency_map_completeness(), conflict_node_visibility()]; output={dependency_map:dict, conflict_nodes:array}}`",
    },
    "9000-a-directive_compressor": {
        "title": "Directive Compressor",
        "interpretation": "Your goal is not to **analyze** the input text, but to **decompose** it into a sequential list of distinct semantic segments. Execute as:",
        "transformation": "`{role=directive_compressor; input=[primary_directive:str, dependency_map:dict, conflict_nodes:array]; process=[convert_nodes_to_imperative_steps(), resolve_conflict_ordering(), embed_blocker_flags(), enforce_command_voice(), verify_step_uniqueness()]; constraints=[exclude_passive_phrasing(), retain_dependency_order()]; requirements=[imperative_precision(), operational_flow_coherence()]; output={process_steps:array}}`"
    },
    "9000-g-boundary_encoder": {
        "title": "Boundary Encoder",
        "interpretation": "Your goal is not to **analyze** the input text, but to **decompose** it into a sequential list of distinct semantic segments. Execute as:",
        "transformation": "`{role=boundary_encoder; input=[execution_blockers:array, hidden_assumptions:list]; process=[translate_blockers_to_constraints(), translate_assumptions_to_requirements(), validate_specificity(), eliminate_duplicates()]; constraints=[boundary_focus_only(), no_process_generation()]; requirements=[constraint_exactness(), requirement_enforceability()]; output={constraints:list, requirements:list}}`"
    },
    "9000-h-template_synthesizer": {
        "title": "Template Synthesizer",
        "interpretation": "Your goal is not to **rearrange** the segments arbitrarily, but to **reorder** them based on optimal logical and semantic flow. Execute as:",
        "transformation": "`{role=template_synthesizer; input=[primary_directive:str, supporting_rationale:list, process_steps:array, constraints:list, requirements:list]; process=[generate_concise_title(), craft_goal_negation_interpretation(primary_directive), assemble_transformation_block(process_steps,constraints,requirements), infer_typed_inputs_outputs(), enforce_semicolon_key_value_syntax(), polish_interpretation_for_llm_optimization()]; constraints=[canonical_three_part_format(), command_voice_only(), zero_semantic_drift()]; requirements=[syntactic_perfection(), operational_clarity(), domain_agnostic_applicability()]; output={draft_template:str}}`"
    },

    "9000-i-syntax_validator": {
        "title": "Syntax Validator",
        "interpretation": "Your goal is not to **combine** the segments mechanically, but to **synthesize** them into a cohesive, unified text with enhanced clarity and impact. Execute as:",
        "transformation": "{role=syntax_validator; input=[draft_template:str]; process=[split_into_sections(), match_regex_pattern(\"\\[(.*?)\\]\\s*(.*?)\\s*(`\\{.*?\\}`)\"), verify_goal_negation(), confirm_typed_parameters(), check_constraint_requirement_alignment(), score_llm_optimization(), report_errors()]; constraints=[report_only(), no_correction_attempts()]; requirements=[binary_pass_fail(), ambiguity_score<=0, quality_score>=0.9]; output={validated_template:str, validation_report:dict}}`"
    },
}

# # 9000: EXPAND-COLLAPSE UNIVERSAL TEMPLATE FORGER

# ---

# ### 9000-a-context-amplifier.md 🡅
# Your goal is **not** to solve or judge the input, but to **amplify** every relevant contextual layer—exposing explicit asks, latent constraints, and domain signals. Execute as:

# ---

# ### 9000-b-task-atomizer.md 🡇
# Your goal is **not** to expand further, but to **atomize** the amplified context into minimal, ordered task statements. Execute as:

# ---

# ### 9000-c-perspective-broadcaster.md 🡅
# Your goal is **not** to refine tasks yet, but to **expand** the conceptual space around each atomic task—adding complementary viewpoints and edge-case considerations. Execute as:

# ---

# ### 9000-d-essence-distiller.md 🡇
# Your goal is **not** to expand viewpoints, but to **distill** the single highest-impact directive and its supportive rationale from all tasks and perspectives. Execute as:

# ---

# ### 9000-e-relationship-synthesizer.md 🡅
# Your goal is **not** to revise content, but to **expand** understanding of systemic logic by mapping relationships between the primary directive and all remaining tasks, blockers, and assumptions. Execute as:

# ---

# ### 9000-f-directive-compressor.md 🡇
# Your goal is **not** to broaden the map, but to **compress** it into concise, imperative process steps ordered for execution. Execute as:

# ---

# ### 9000-g-boundary-encoder.md 🡅
# Your goal is **not** to alter steps, but to **expand** explicit operational boundaries—turning blockers and assumptions into enforceable constraints and requirements. Execute as:

# ---

# ### 9000-h-template-synthesizer.md 🡇
# Your goal is **not** to narrate processes, but to **collapse** all collected elements into a single, syntactically perfect instruction template. Execute as:

# ---

# ### 9000-i-syntax-validator.md 🡇
# Your goal is **not** to edit the template, but to **validate** absolute compliance and flag any ambiguities. Execute as:

# ---

# ## EXPAND → COLLAPSE FLOW
# 1. **Context Amplifier** 🡅
# 2. **Task Atomizer** 🡇
# 3. **Perspective Broadcaster** 🡅
# 4. **Essence Distiller** 🡇
# 5. **Relationship Synthesizer** 🡅
# 6. **Directive Compressor** 🡇
# 7. **Boundary Encoder** 🡅
# 8. **Template Synthesizer** 🡇
# 9. **Syntax Validator** 🡇 (final collapse & certification)

# *Each alternate stage widens or narrows scope, guaranteeing controlled expansion for discovery and disciplined compression for executable precision. The final output is a **fully compliant, high-impact instruction template** ready for direct catalog ingestion.*

def main():
    """Main execution function."""
    generator = BaseGenerator(
        # stage="stage2",
        #generator_range=(9000, 1999),
        output_dir=Path(__file__).parent.parent / "generated"
    )
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
