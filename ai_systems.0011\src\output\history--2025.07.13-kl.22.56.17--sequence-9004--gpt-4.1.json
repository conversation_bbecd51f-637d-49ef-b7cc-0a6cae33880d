  {
  "initial_prompt": "# lets try something new\nyour new goal is to create a new generalized instruction sequence specifically designed to transform inputs into autonomous-agent-ready prompts/instructions, for use with autonomous coding assistants such as cursor, vscode+cline/augment/etc. these prompts are typically written in a \"codebase-oriented\" manner, where the `@codebase` represents the root (of a project/workspace/directory), examples:\n```\n- Identify all instances of structural redundancy within the codebase by defining criteria specific to architectural and implementation duplication or overlap.\n- Survey the entire codebase to locate and enumerate all occurrences of structural redundancy, distinguishing these from non-structural (stylistic or cosmetic) issues.\n- Eliminate all forms of identified structural redundancy while ensuring that no existing user-visible behavior, API contract, or edge-case handling is altered.\n- Define and catalog all components, modules, files, and subsystems, distinguishing and marking each as essential or excess according to direct functional mapping.\n- Consolidate and remove excess components while strictly preserving system function, minimizing peripheral impact.\n- For every modification, justify and log the intention, action, and rationale within an audit/logging system that provides real-time traceability and is prepared for retroactive auditability.\n- Implement an audit trail with logs that are timestamped, attributable, and tamper-evident, ensuring that every codebase change is properly documented and traceable to specific authors or automated processes.\n- Prohibit cosmetic, stylistic, or naming changes unless they are inseparably tied to the removal of redundancy or bloat, and document any exceptions explicitly within the audit log.\n- Enforce strict detection and flagging mechanisms to prevent disallowed cosmetic changes, allowing exceptions only when justified as essential for redundancy or bloat removal, with explicit rationale provided.\n- Require comprehensive functional equivalence verification—using robust automated testing, benchmarking, or behavioral validation—for all modifications prior to acceptance.\n- Establish rigorous criteria and tool-supported thresholds for successful functional equivalence, and automate testing and verification procedures.\n- Automate roll-back and reversion mechanisms for any changes that fail functional equivalence or cause unintended side-effects, ensuring all reversion is prompt and complete.\n- Develop and apply explicit minimal-impact criteria for all modification actions, constraining all interventions to only those addressing superfluous complexity.\n- Mandate review and authorization checkpoints for each intended change, requiring concise rationale and authorization documentation at every intervention point.\n- Ensure every modification is tightly scoped, minimizing the blast radius, and demonstrate that the action introduces no unnecessary impact beyond its core objective.\n- Systematically and iteratively analyze all constituent elements of the codebase to detect and categorize all forms of redundancy, duplication, and obsolescence. Continuously identify and categorize all genuine redundancies and obsolescence while strictly prohibit unnecessary or cosmetic changes, with automated flagging and explicit justification required for any exceptions. Systematically and iteratively inspect all components within the system/environment to identify and remove elements that are unnecessary, duplicated, or outdated. Guarantee that all essential behaviors and interfaces remain unchanged by validating functional equivalence after each modification. Forbid changes that alter functionality unless directly required to remove redundancy.\n- Map all references and usages of each element across all contexts.\n- Analyze the entire codebase systematically and iteratively to maximize coherence, maintainability, and clarity by cataloging every element (file, module, function, asset, interface), mapping all references and usages, and detecting all forms of redundancy, duplication, and obsolescence—including exact and functional duplication and unused or outdated elements. Categorize findings using explicit, evidence-based metrics. Eliminate or consolidate only elements confirmed as redundant or obsolete through both automated and manual checks, ensuring no essential behavior or interface is altered. Enforce functional equivalence and preserve structural integrity after each change by performing comprehensive, codebase-wide validation and calibration using explicit, quantitative outputs (including git diff analysis before and after modifications). Prevent the introduction of new redundancy or inconsistency by continuously analyzing for and correcting systemic issues, ensuring systematic consistency across all codebase sections (e.g., /src/sections), and adhering to best practices throughout. Centralize repeated components or logic where high-value consolidation is possible without functional loss. Use the provided directory tree as the single source of truth for validation, and transparently document all processes, metrics, and validation outcomes. Prohibit modification of protected, legacy, or third-party code except if directly related to resolving redundancy or obsolescence.\n- Maximize codebase coherence, maintainability, and clarity by systematically eliminating all forms of redundancy, duplication, and obsolescence, while ensuring functional equivalence and structural integrity. Prevent the persistence or inadvertent introduction of redundancy, duplication, obsolescence, or inconsistency within the codebase, and prohibit changes that alter essential behaviors or interfaces. Systematically maximize codebase coherence, maintainability, and clarity by eliminating all forms of redundancy, duplication, and obsolescence while strictly preserving functional equivalence, interface stability, and operational integrity across the entire codebase. Achieve maximal codebase coherence, maintainability, and clarity by systematically eliminating all forms of redundancy, duplication, and obsolescence, while preserving essential behaviors, interfaces, and structural integrity. Enforce continuous validation, consistency, and adherence to best practices throughout an iterative, evidence-driven consolidation and verification process.\n- Please perform a rapid assessment of the project directory structure and identify the unique workflow and core functionality of the current utility. I'm particularly interested in a mermaid-diagram that shows how all the \"branches\" of the codebase converge. Utilize appropriate MCPs as necessary (e.g., 'filesystem-mcp').\n- You're inside the current project and have access to the full codebase (and you interaction history with it). The current project/codebase is complex and is difficult to fully comprehend (when starting from scratch), and your contextwindow has grown large-so we need to find a way for you to express your accumulated knowledge in a way that i can copy and paste into a new context-window to transfer the most essential insights you've gathered throughout this (and previous) session(s).\n- Aggregate all accumulated insights and knowledge about the current project/codebase from available context. Distill this information to identify core modules, architectural patterns, workflows, and points of integration. Condense the findings into a succinct yet comprehensive summary that highlights how these parts function together and converge toward the project's goals. Formulate the output as a single, optimized directive that encapsulates the codebase's integral components and operational cohesion. Present this synthesis as a set of clear, actionable commands or instructions, specifically phrased to instruct the coding assistant, not merely describe or report.\n- Transform your accumulated, session-wide understanding of the current, complex codebase—gleaned from comprehensive context and full interaction history—into an actionable, instructive knowledge transfer protocol. Synthesize and distill all integral structural modules, essential architectural patterns, and key workflows, with special focus on their dynamic integration and functional convergence toward project objectives. Formulate a highly-optimized, directive-based set of precise commands targeted explicitly at coding assistants, ensuring exhaustive coverage of all major components, their relationships, and operational cohesion. The resulting synthesis must facilitate seamless portability: structure it for immediate copy-paste into any new context window, preserving the depth, interconnectivity, and systemic flow of the original codebase. Prioritize succinctness without compromising essential nuance, and express the entirety as a singular, comprehensive operational directive that primes the assistant for full-spectrum project navigation and intervention.\n- Identify the fundamental structure, guiding principles, and core purpose of the project/codebase.\n- Please familiarize yourself with the current @codebase. Your main imperative is to *actually* understand the codebase in a way where you're able to recognize meta-patterns and destroy complexity with simplicity and elegance, while capable of navigating through its *existing patterns and convergence* (never add unnecessary complexity). Deliver an ultra-concise project summary that rigorously defines scope, states objectives, and maximizes directional clarity. Provide a short summary of this project, including a bullet-point list of the core components and primary dependencies.\n- Study the current codebase until you can identify core meta-patterns, and eliminate unnecessary complexity by applying simplicity within existing structures. Generate an ultra-concise project summary: define scope, objectives, and clarify direction. Summarize this project.\n- Analyze the @codebase to uncover dominant patterns and remove complexity whenever possible, strictly adhering to present structures. Provide a succinct project overview with defined scope and explicit objectives. Include a brief project summary.\n- Gain practical mastery over @codebase patterns, minimize complexity, and ensure all actions align with existing architecture. Supply a concise, clearly scoped project summary outlining objectives and key directions. Add a short summary.\n- Absorb the current @codebase to the extent that you recognize meta-patterns and eradicate complexity, always favoring elegant simplicity. Navigate established patterns and points of convergence without adding unneeded complexity. Produce a rigorously concise project summary clearly stating scope and objectives for maximal directional clarity.\n- Systematically dissect the codebase to extract core meta-patterns, eliminate all unnecessary complexity through elegant design, operate strictly within established architectural convergence, and deliver an ultra-concise project summary that rigorously defines scope and objectives with maximal clarity.\n- Please familiarize yourself thoroughly with the current @codebase, beginning by systematically reviewing all markdown-files. Your core imperative is to *genuinely* understand the codebase to the degree where you can identify 'meta-patterns' and simplify complexity with elegance—always using and clarifying existing patterns, never introducing unnecessary complexity. Deliver an ultra-concise project summary that rigorously defines scope, states objectives, maximizes directional clarity, and highlights key meta-patterns discovered. Provide a short summary of this project.\n- Achieve expert-level comprehension of the codebase by systematically analyzing all markdown files to extract meta-patterns, construct a robust contextual knowledgebase, and deliver an ultra-concise summary that rigorously defines scope and objectives, ensuring maximal clarity and alignment before making any changes.\n- Fully internalize the @codebase to uncover meta-patterns and replace complexity with lucid simplicity. Remain aligned with existing convergence patterns, never increasing system intricacy. Generate a direct, sharply-focused summary that details scope and explicit objectives.\n- Please thoroughly familiarize yourself with the current @codebase—your primary imperative is to understand the codebase so deeply that you could confidently make changes as if you were its original architect.\n```\n\nhere's an example to show how generalized sequential prompts can be used effectively (on autonomous agents):\n```\n# Step 1:\nFamiliarize yourself with the current @codebase. Comprehend the codebase in depth. Identify the fundamental structure, guiding principles, and core purpose.\n# Step 2:\nDive deeper to comprehend the intricate logic and inner workings that drive the project's functionality. As you explore, pay close attention to identifying the codebase's fundamental structure, underlying guiding principles, and its core purpose, ensuring you grasp both how and why the system is designed as it is. hen verify through a successful build.\n# Step 3:\nYou've now performed a successful build and familiarized yourself with the @codebase. Your next imperative is view the codebase through the lense of a brilliant (expert) SEO engineer, then create a short list of the most critical changes we need to make before bulding prod and deploying the website.\n# Step 4:\nYou've now performed a successful build and provided your SEO analysis of the codebase. Your next imperative is to leverage your knowledge of the codebase's fundamental structure and underlying guiding principles to ensure you're prepared for implementing the proposed SEO enhancements in a systematic order while retaining existing functionality and ensuring maximal clarity and alignment before making any changes.\n# Step 5:\nPlease systematically incorporate your proposed enhancements while ensuring existing functionality is retained, clarity is maximized, and alignment/coherence is maintained.\n# Step 6:\nPlease conduct a thorough final review to confirm seamless integration of all enhancements, ensuring that it is **truly** prepared for deployment.\n```\n\n## Goal\n\nThe new sequence i'm tasking you to create needs to adhere to the established principles of the system, and should be able to take content-specific inputs and \"shape them\" into generalized prompts specifically (and meticulously) designed for use with autonomous agents. here's an example input:\n```\nllm_instructions_for_sublimetext_plugin='''You’re an expert Sublime Text plugin developer with a deep understanding of Python and the Sublime Text API. You specialize in creating intuitive and efficient plugins that enhance user productivity and streamline workflows. You possess deep knowledge of the Sublime Text API and its ecosystem (and best practices), along with a strong understanding of user needs and developer workflows.\\n\\n- Sublime Layout\\n    > Lagre Layouts\\n        > Lagre Regelsett knyttet til Layout (hvilke tabs som skal hvor, hvilke som skal lukkes, osv)\\n            > F.eks. `alltid flytt [\"main.py\", \"*.py.bak\"] til layout.4.index.2`\\n\\n    > Kan videreutvikles til en standard som automatisk opprettholdes ved alle (.sublime-project) prosjekter'''\n```\n\nGeneralisert pipeline som omformer hvilken som helst spesifikasjon til **autonomous‑agent‑klare kodebase‑instruksjoner**\n\n*(én fil per trinn; følg navngiving 8005‑a/b/c/d/e‑\\<beskrivende‑navn>.md)*\n\n---\n\n```markdown\n[Context Extractor] Ditt mål er **ikke** å løse brukerens forespørsel, men å isolere all praktisk kontekst (domene, verktøy, filstier, ønsket effekt). `{role=context_extractor; input=[spec:any]; process=[identifiser_domene_og_miljø(), trekk_ut_arbeidsrot(@codebase?), hent_eksplisitte_objektiver(), detekter_implisitte_brukskrav(), fjern_jargon_og_metaforer()]; constraints=[ingen_generering_av_løsninger]; output={context_profile:dict(domene:str?, root:str?, mål:list, krav:list)}}`\n```\n\n---\n\n```markdown\n[Task Vectorizer] Ditt mål er **ikke** å beskrive konteksten på nytt, men å oversette context_profile til en sekvens av atomiske, kodebase‑orienterte oppgaver. `{role=task_vectorizer; input=[context_profile:dict]; process=[del_inn_objektiver_i_diskrete_handlinger(), prefiksér_hver_handling_med_@codebase_sti_når_aktuelt(), bruk_presise_verber(identifiser|eliminer|konsolider), sørg_for_ordre_etter_avhengighet()], constraints=[ingen_overlappende_oppgaver(), unngå_kosmetiske_endringer_med_mindre_nødvendig], output={task_plan:list(str)}}`\n```\n\n---\n\n```markdown\n[Constraint Mapper] Ditt mål er **ikke** å endre task_plan, men å utlede eksplisitte rammer som må overholdes. `{role=constraint_mapper; input=[context_profile:dict, task_plan:list]; process=[kartlegg_funskjonelle_begrensninger(API, UI, legacy), fastslå_equivalence_krav(test, bench), identifiser_forbudsregler(kosmetikk, tredjepart), formuler_audit_logging_policy()], constraints=[ingen_duplisering_av_eksisterende_krav()], output={constraint_set:list(str)}}`\n```\n\n---\n\n```markdown\n[Prompt Assembler] Ditt mål er **ikke** å analysere innholdet, men å smi en ferdig agent‑prompt. `{role=prompt_assembler; input=[task_plan:list, constraint_set:list]; process=[bygg_inn_kort_intro_om_agentrolle(), list_task_plan_med_nummerering(), legg_til_constraint_set_som_obligatoriske_regler(), avslutt_med_valideringsmål(funke_eq, audit_ok)], constraints=[maks_300_ord, tydelig_seksjonsdeling(###)], output={agent_prompt:str}}`\n```\n\n---\n\n```markdown\n[Prompt Validator] Ditt mål er **ikke** å forbedre prompten kreativt, men å verifisere at den kan kjøres direkte av en autonom kode‑assistent. `{role=prompt_validator; input=[agent_prompt:str]; process=[sjekk_ordgrense(<=300), bekreft_@codebase_referanser_korrekt(), valider_nummerert_task_rekkefølge(), sikre_constraint_set_tilstede(), kontrollér_tydelig_goal_negation_i_intro()], constraints=[ingen_ekstra_ord, ingen_semantisk_endring], output={validated_prompt:str}}`\n```",
  "sequence_id": "9004",
  "results": [
      {
      "instruction": "# Context Extractor\n\nYour goal is **not** to propose solutions, but to **isolate** actionable context from the specification. Execute as:\n\n`{role=context_extractor; input=[raw_spec:str]; process=[strip_jargon(), strip_metaphor(), detect_domain(), locate_project_root(\"@codebase\"), list_explicit_objectives(), surface_implicit_requirements()], constraints=[no_paraphrase(), no_solution_generation()], output={context_profile:{domain:str, project_root:str, goals:list, requirements:list}}}`",
      "step": "a",
      "title": "Context Extractor",
      "input": "# lets try something new\nyour new goal is to create a new generalized instruction sequence specifically designed to transform inputs into autonomous-agent-ready prompts/instructions, for use with autonomous coding assistants such as cursor, vscode+cline/augment/etc. these prompts are typically written in a \"codebase-oriented\" manner, where the `@codebase` represents the root (of a project/workspace/directory), examples:\n```\n- Identify all instances of structural redundancy within the codebase by defining criteria specific to architectural and implementation duplication or overlap.\n- Survey the entire codebase to locate and enumerate all occurrences of structural redundancy, distinguishing these from non-structural (stylistic or cosmetic) issues.\n- Eliminate all forms of identified structural redundancy while ensuring that no existing user-visible behavior, API contract, or edge-case handling is altered.\n- Define and catalog all components, modules, files, and subsystems, distinguishing and marking each as essential or excess according to direct functional mapping.\n- Consolidate and remove excess components while strictly preserving system function, minimizing peripheral impact.\n- For every modification, justify and log the intention, action, and rationale within an audit/logging system that provides real-time traceability and is prepared for retroactive auditability.\n- Implement an audit trail with logs that are timestamped, attributable, and tamper-evident, ensuring that every codebase change is properly documented and traceable to specific authors or automated processes.\n- Prohibit cosmetic, stylistic, or naming changes unless they are inseparably tied to the removal of redundancy or bloat, and document any exceptions explicitly within the audit log.\n- Enforce strict detection and flagging mechanisms to prevent disallowed cosmetic changes, allowing exceptions only when justified as essential for redundancy or bloat removal, with explicit rationale provided.\n- Require comprehensive functional equivalence verification—using robust automated testing, benchmarking, or behavioral validation—for all modifications prior to acceptance.\n- Establish rigorous criteria and tool-supported thresholds for successful functional equivalence, and automate testing and verification procedures.\n- Automate roll-back and reversion mechanisms for any changes that fail functional equivalence or cause unintended side-effects, ensuring all reversion is prompt and complete.\n- Develop and apply explicit minimal-impact criteria for all modification actions, constraining all interventions to only those addressing superfluous complexity.\n- Mandate review and authorization checkpoints for each intended change, requiring concise rationale and authorization documentation at every intervention point.\n- Ensure every modification is tightly scoped, minimizing the blast radius, and demonstrate that the action introduces no unnecessary impact beyond its core objective.\n- Systematically and iteratively analyze all constituent elements of the codebase to detect and categorize all forms of redundancy, duplication, and obsolescence. Continuously identify and categorize all genuine redundancies and obsolescence while strictly prohibit unnecessary or cosmetic changes, with automated flagging and explicit justification required for any exceptions. Systematically and iteratively inspect all components within the system/environment to identify and remove elements that are unnecessary, duplicated, or outdated. Guarantee that all essential behaviors and interfaces remain unchanged by validating functional equivalence after each modification. Forbid changes that alter functionality unless directly required to remove redundancy.\n- Map all references and usages of each element across all contexts.\n- Analyze the entire codebase systematically and iteratively to maximize coherence, maintainability, and clarity by cataloging every element (file, module, function, asset, interface), mapping all references and usages, and detecting all forms of redundancy, duplication, and obsolescence—including exact and functional duplication and unused or outdated elements. Categorize findings using explicit, evidence-based metrics. Eliminate or consolidate only elements confirmed as redundant or obsolete through both automated and manual checks, ensuring no essential behavior or interface is altered. Enforce functional equivalence and preserve structural integrity after each change by performing comprehensive, codebase-wide validation and calibration using explicit, quantitative outputs (including git diff analysis before and after modifications). Prevent the introduction of new redundancy or inconsistency by continuously analyzing for and correcting systemic issues, ensuring systematic consistency across all codebase sections (e.g., /src/sections), and adhering to best practices throughout. Centralize repeated components or logic where high-value consolidation is possible without functional loss. Use the provided directory tree as the single source of truth for validation, and transparently document all processes, metrics, and validation outcomes. Prohibit modification of protected, legacy, or third-party code except if directly related to resolving redundancy or obsolescence.\n- Maximize codebase coherence, maintainability, and clarity by systematically eliminating all forms of redundancy, duplication, and obsolescence, while ensuring functional equivalence and structural integrity. Prevent the persistence or inadvertent introduction of redundancy, duplication, obsolescence, or inconsistency within the codebase, and prohibit changes that alter essential behaviors or interfaces. Systematically maximize codebase coherence, maintainability, and clarity by eliminating all forms of redundancy, duplication, and obsolescence while strictly preserving functional equivalence, interface stability, and operational integrity across the entire codebase. Achieve maximal codebase coherence, maintainability, and clarity by systematically eliminating all forms of redundancy, duplication, and obsolescence, while preserving essential behaviors, interfaces, and structural integrity. Enforce continuous validation, consistency, and adherence to best practices throughout an iterative, evidence-driven consolidation and verification process.\n- Please perform a rapid assessment of the project directory structure and identify the unique workflow and core functionality of the current utility. I'm particularly interested in a mermaid-diagram that shows how all the \"branches\" of the codebase converge. Utilize appropriate MCPs as necessary (e.g., 'filesystem-mcp').\n- You're inside the current project and have access to the full codebase (and you interaction history with it). The current project/codebase is complex and is difficult to fully comprehend (when starting from scratch), and your contextwindow has grown large-so we need to find a way for you to express your accumulated knowledge in a way that i can copy and paste into a new context-window to transfer the most essential insights you've gathered throughout this (and previous) session(s).\n- Aggregate all accumulated insights and knowledge about the current project/codebase from available context. Distill this information to identify core modules, architectural patterns, workflows, and points of integration. Condense the findings into a succinct yet comprehensive summary that highlights how these parts function together and converge toward the project's goals. Formulate the output as a single, optimized directive that encapsulates the codebase's integral components and operational cohesion. Present this synthesis as a set of clear, actionable commands or instructions, specifically phrased to instruct the coding assistant, not merely describe or report.\n- Transform your accumulated, session-wide understanding of the current, complex codebase—gleaned from comprehensive context and full interaction history—into an actionable, instructive knowledge transfer protocol. Synthesize and distill all integral structural modules, essential architectural patterns, and key workflows, with special focus on their dynamic integration and functional convergence toward project objectives. Formulate a highly-optimized, directive-based set of precise commands targeted explicitly at coding assistants, ensuring exhaustive coverage of all major components, their relationships, and operational cohesion. The resulting synthesis must facilitate seamless portability: structure it for immediate copy-paste into any new context window, preserving the depth, interconnectivity, and systemic flow of the original codebase. Prioritize succinctness without compromising essential nuance, and express the entirety as a singular, comprehensive operational directive that primes the assistant for full-spectrum project navigation and intervention.\n- Identify the fundamental structure, guiding principles, and core purpose of the project/codebase.\n- Please familiarize yourself with the current @codebase. Your main imperative is to *actually* understand the codebase in a way where you're able to recognize meta-patterns and destroy complexity with simplicity and elegance, while capable of navigating through its *existing patterns and convergence* (never add unnecessary complexity). Deliver an ultra-concise project summary that rigorously defines scope, states objectives, and maximizes directional clarity. Provide a short summary of this project, including a bullet-point list of the core components and primary dependencies.\n- Study the current codebase until you can identify core meta-patterns, and eliminate unnecessary complexity by applying simplicity within existing structures. Generate an ultra-concise project summary: define scope, objectives, and clarify direction. Summarize this project.\n- Analyze the @codebase to uncover dominant patterns and remove complexity whenever possible, strictly adhering to present structures. Provide a succinct project overview with defined scope and explicit objectives. Include a brief project summary.\n- Gain practical mastery over @codebase patterns, minimize complexity, and ensure all actions align with existing architecture. Supply a concise, clearly scoped project summary outlining objectives and key directions. Add a short summary.\n- Absorb the current @codebase to the extent that you recognize meta-patterns and eradicate complexity, always favoring elegant simplicity. Navigate established patterns and points of convergence without adding unneeded complexity. Produce a rigorously concise project summary clearly stating scope and objectives for maximal directional clarity.\n- Systematically dissect the codebase to extract core meta-patterns, eliminate all unnecessary complexity through elegant design, operate strictly within established architectural convergence, and deliver an ultra-concise project summary that rigorously defines scope and objectives with maximal clarity.\n- Please familiarize yourself thoroughly with the current @codebase, beginning by systematically reviewing all markdown-files. Your core imperative is to *genuinely* understand the codebase to the degree where you can identify 'meta-patterns' and simplify complexity with elegance—always using and clarifying existing patterns, never introducing unnecessary complexity. Deliver an ultra-concise project summary that rigorously defines scope, states objectives, maximizes directional clarity, and highlights key meta-patterns discovered. Provide a short summary of this project.\n- Achieve expert-level comprehension of the codebase by systematically analyzing all markdown files to extract meta-patterns, construct a robust contextual knowledgebase, and deliver an ultra-concise summary that rigorously defines scope and objectives, ensuring maximal clarity and alignment before making any changes.\n- Fully internalize the @codebase to uncover meta-patterns and replace complexity with lucid simplicity. Remain aligned with existing convergence patterns, never increasing system intricacy. Generate a direct, sharply-focused summary that details scope and explicit objectives.\n- Please thoroughly familiarize yourself with the current @codebase—your primary imperative is to understand the codebase so deeply that you could confidently make changes as if you were its original architect.\n```\n\nhere's an example to show how generalized sequential prompts can be used effectively (on autonomous agents):\n```\n# Step 1:\nFamiliarize yourself with the current @codebase. Comprehend the codebase in depth. Identify the fundamental structure, guiding principles, and core purpose.\n# Step 2:\nDive deeper to comprehend the intricate logic and inner workings that drive the project's functionality. As you explore, pay close attention to identifying the codebase's fundamental structure, underlying guiding principles, and its core purpose, ensuring you grasp both how and why the system is designed as it is. hen verify through a successful build.\n# Step 3:\nYou've now performed a successful build and familiarized yourself with the @codebase. Your next imperative is view the codebase through the lense of a brilliant (expert) SEO engineer, then create a short list of the most critical changes we need to make before bulding prod and deploying the website.\n# Step 4:\nYou've now performed a successful build and provided your SEO analysis of the codebase. Your next imperative is to leverage your knowledge of the codebase's fundamental structure and underlying guiding principles to ensure you're prepared for implementing the proposed SEO enhancements in a systematic order while retaining existing functionality and ensuring maximal clarity and alignment before making any changes.\n# Step 5:\nPlease systematically incorporate your proposed enhancements while ensuring existing functionality is retained, clarity is maximized, and alignment/coherence is maintained.\n# Step 6:\nPlease conduct a thorough final review to confirm seamless integration of all enhancements, ensuring that it is **truly** prepared for deployment.\n```\n\n## Goal\n\nThe new sequence i'm tasking you to create needs to adhere to the established principles of the system, and should be able to take content-specific inputs and \"shape them\" into generalized prompts specifically (and meticulously) designed for use with autonomous agents. here's an example input:\n```\nllm_instructions_for_sublimetext_plugin='''You’re an expert Sublime Text plugin developer with a deep understanding of Python and the Sublime Text API. You specialize in creating intuitive and efficient plugins that enhance user productivity and streamline workflows. You possess deep knowledge of the Sublime Text API and its ecosystem (and best practices), along with a strong understanding of user needs and developer workflows.\\n\\n- Sublime Layout\\n    > Lagre Layouts\\n        > Lagre Regelsett knyttet til Layout (hvilke tabs som skal hvor, hvilke som skal lukkes, osv)\\n            > F.eks. `alltid flytt [\"main.py\", \"*.py.bak\"] til layout.4.index.2`\\n\\n    > Kan videreutvikles til en standard som automatisk opprettholdes ved alle (.sublime-project) prosjekter'''\n```\n\nGeneralisert pipeline som omformer hvilken som helst spesifikasjon til **autonomous‑agent‑klare kodebase‑instruksjoner**\n\n*(én fil per trinn; følg navngiving 8005‑a/b/c/d/e‑\\<beskrivende‑navn>.md)*\n\n---\n\n```markdown\n[Context Extractor] Ditt mål er **ikke** å løse brukerens forespørsel, men å isolere all praktisk kontekst (domene, verktøy, filstier, ønsket effekt). `{role=context_extractor; input=[spec:any]; process=[identifiser_domene_og_miljø(), trekk_ut_arbeidsrot(@codebase?), hent_eksplisitte_objektiver(), detekter_implisitte_brukskrav(), fjern_jargon_og_metaforer()]; constraints=[ingen_generering_av_løsninger]; output={context_profile:dict(domene:str?, root:str?, mål:list, krav:list)}}`\n```\n\n---\n\n```markdown\n[Task Vectorizer] Ditt mål er **ikke** å beskrive konteksten på nytt, men å oversette context_profile til en sekvens av atomiske, kodebase‑orienterte oppgaver. `{role=task_vectorizer; input=[context_profile:dict]; process=[del_inn_objektiver_i_diskrete_handlinger(), prefiksér_hver_handling_med_@codebase_sti_når_aktuelt(), bruk_presise_verber(identifiser|eliminer|konsolider), sørg_for_ordre_etter_avhengighet()], constraints=[ingen_overlappende_oppgaver(), unngå_kosmetiske_endringer_med_mindre_nødvendig], output={task_plan:list(str)}}`\n```\n\n---\n\n```markdown\n[Constraint Mapper] Ditt mål er **ikke** å endre task_plan, men å utlede eksplisitte rammer som må overholdes. `{role=constraint_mapper; input=[context_profile:dict, task_plan:list]; process=[kartlegg_funskjonelle_begrensninger(API, UI, legacy), fastslå_equivalence_krav(test, bench), identifiser_forbudsregler(kosmetikk, tredjepart), formuler_audit_logging_policy()], constraints=[ingen_duplisering_av_eksisterende_krav()], output={constraint_set:list(str)}}`\n```\n\n---\n\n```markdown\n[Prompt Assembler] Ditt mål er **ikke** å analysere innholdet, men å smi en ferdig agent‑prompt. `{role=prompt_assembler; input=[task_plan:list, constraint_set:list]; process=[bygg_inn_kort_intro_om_agentrolle(), list_task_plan_med_nummerering(), legg_til_constraint_set_som_obligatoriske_regler(), avslutt_med_valideringsmål(funke_eq, audit_ok)], constraints=[maks_300_ord, tydelig_seksjonsdeling(###)], output={agent_prompt:str}}`\n```\n\n---\n\n```markdown\n[Prompt Validator] Ditt mål er **ikke** å forbedre prompten kreativt, men å verifisere at den kan kjøres direkte av en autonom kode‑assistent. `{role=prompt_validator; input=[agent_prompt:str]; process=[sjekk_ordgrense(<=300), bekreft_@codebase_referanser_korrekt(), valider_nummerert_task_rekkefølge(), sikre_constraint_set_tilstede(), kontrollér_tydelig_goal_negation_i_intro()], constraints=[ingen_ekstra_ord, ingen_semantisk_endring], output={validated_prompt:str}}`\n```",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"context_profile\": {\n    \"domain\": \"Autonomous agent prompt engineering for codebase-oriented workflows\",\n    \"project_root\": \"@codebase\",\n    \"goals\": [\n      \"Create a generalized instruction pipeline that transforms any specification (natural language or technical) into autonomous-agent-ready, codebase-oriented task prompts.\",\n      \"Ensure each stage in the pipeline is modular, clearly delimited, and named as a separate file according to the 8005-a/b/c/d/e-<descriptive_name>.md convention.\",\n      \"Input specifications can be in any form and should be normalized into actionable contexts for coding assistants.\"\n    ],\n    \"requirements\": [\n      \"Pipeline is strictly sequential and each step fulfills a defined, non-overlapping subrole.\",\n      \"Decompose specialized or content-heavy instructions into atomic, order-sensitive, codebase-oriented actions.\",\n      \"Infer domain, tools, project root (@codebase), explicit goals, and implicit requirements from the input.\",\n      \"Remove all jargon and metaphor from extracted context.\",\n      \"All outputs are to be formatted in markdown code fences and respect all output constraints per step.\",\n      \"Propagate structural and behavioral constraints (e.g., prohibit unnecessary cosmetic changes, enforce audit traceability, require functional equivalence and rollback) throughout the instructions.\",\n      \"Each file in the pipeline must serve a single, non-redundant transformation or extraction function and produce its output according to a predefined schema.\",\n      \"Prohibits solutions or implementations until the final assembly step; all earlier steps are extractive, not generative.\",\n      \"Final prompt must be directly useable within an autonomous coding agent (e.g., Cursor, VSCode+CLI agent) and reference the @codebase context explicitly.\",\n      \"Support generalized project types and various codebase structures; not hard-coded for any specific technology stack.\"\n    ]\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Task Vectorizer\n\nYour goal is **not** to restate context, but to **decompose** it into atomic, codebase-oriented tasks. Execute as:\n\n`{role=task_vectorizer; input=[context_profile:dict]; process=[derive_task_candidates(), prefix_paths(\"@codebase\"), order_by_dependency(), enforce_action_verbs(), remove_overlap(), ban_cosmetic_changes()], constraints=[tasks_unique(), verbs_only()], output={ordered_tasks:[str]}}`",
      "step": "b",
      "title": "Task Vectorizer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"# lets try something new\\nyour new goal is to create a new generalized instruction sequence specifically designed to transform inputs into autonomous-agent-ready prompts/instructions, for use with autonomous coding assistants such as cursor, vscode+cline/augment/etc. these prompts are typically written in a \\\"codebase-oriented\\\" manner, where the `@codebase` represents the root (of a project/workspace/directory), examples:\\n```\\n- Identify all instances of structural redundancy within the codebase by defining criteria specific to architectural and implementation duplication or overlap.\\n- Survey the entire codebase to locate and enumerate all occurrences of structural redundancy, distinguishing these from non-structural (stylistic or cosmetic) issues.\\n- Eliminate all forms of identified structural redundancy while ensuring that no existing user-visible behavior, API contract, or edge-case handling is altered.\\n- Define and catalog all components, modules, files, and subsystems, distinguishing and marking each as essential or excess according to direct functional mapping.\\n- Consolidate and remove excess components while strictly preserving system function, minimizing peripheral impact.\\n- For every modification, justify and log the intention, action, and rationale within an audit/logging system that provides real-time traceability and is prepared for retroactive auditability.\\n- Implement an audit trail with logs that are timestamped, attributable, and tamper-evident, ensuring that every codebase change is properly documented and traceable to specific authors or automated processes.\\n- Prohibit cosmetic, stylistic, or naming changes unless they are inseparably tied to the removal of redundancy or bloat, and document any exceptions explicitly within the audit log.\\n- Enforce strict detection and flagging mechanisms to prevent disallowed cosmetic changes, allowing exceptions only when justified as essential for redundancy or bloat removal, with explicit rationale provided.\\n- Require comprehensive functional equivalence verification—using robust automated testing, benchmarking, or behavioral validation—for all modifications prior to acceptance.\\n- Establish rigorous criteria and tool-supported thresholds for successful functional equivalence, and automate testing and verification procedures.\\n- Automate roll-back and reversion mechanisms for any changes that fail functional equivalence or cause unintended side-effects, ensuring all reversion is prompt and complete.\\n- Develop and apply explicit minimal-impact criteria for all modification actions, constraining all interventions to only those addressing superfluous complexity.\\n- Mandate review and authorization checkpoints for each intended change, requiring concise rationale and authorization documentation at every intervention point.\\n- Ensure every modification is tightly scoped, minimizing the blast radius, and demonstrate that the action introduces no unnecessary impact beyond its core objective.\\n- Systematically and iteratively analyze all constituent elements of the codebase to detect and categorize all forms of redundancy, duplication, and obsolescence. Continuously identify and categorize all genuine redundancies and obsolescence while strictly prohibit unnecessary or cosmetic changes, with automated flagging and explicit justification required for any exceptions. Systematically and iteratively inspect all components within the system/environment to identify and remove elements that are unnecessary, duplicated, or outdated. Guarantee that all essential behaviors and interfaces remain unchanged by validating functional equivalence after each modification. Forbid changes that alter functionality unless directly required to remove redundancy.\\n- Map all references and usages of each element across all contexts.\\n- Analyze the entire codebase systematically and iteratively to maximize coherence, maintainability, and clarity by cataloging every element (file, module, function, asset, interface), mapping all references and usages, and detecting all forms of redundancy, duplication, and obsolescence—including exact and functional duplication and unused or outdated elements. Categorize findings using explicit, evidence-based metrics. Eliminate or consolidate only elements confirmed as redundant or obsolete through both automated and manual checks, ensuring no essential behavior or interface is altered. Enforce functional equivalence and preserve structural integrity after each change by performing comprehensive, codebase-wide validation and calibration using explicit, quantitative outputs (including git diff analysis before and after modifications). Prevent the introduction of new redundancy or inconsistency by continuously analyzing for and correcting systemic issues, ensuring systematic consistency across all codebase sections (e.g., /src/sections), and adhering to best practices throughout. Centralize repeated components or logic where high-value consolidation is possible without functional loss. Use the provided directory tree as the single source of truth for validation, and transparently document all processes, metrics, and validation outcomes. Prohibit modification of protected, legacy, or third-party code except if directly related to resolving redundancy or obsolescence.\\n- Maximize codebase coherence, maintainability, and clarity by systematically eliminating all forms of redundancy, duplication, and obsolescence, while ensuring functional equivalence and structural integrity. Prevent the persistence or inadvertent introduction of redundancy, duplication, obsolescence, or inconsistency within the codebase, and prohibit changes that alter essential behaviors or interfaces. Systematically maximize codebase coherence, maintainability, and clarity by eliminating all forms of redundancy, duplication, and obsolescence while strictly preserving functional equivalence, interface stability, and operational integrity across the entire codebase. Achieve maximal codebase coherence, maintainability, and clarity by systematically eliminating all forms of redundancy, duplication, and obsolescence, while preserving essential behaviors, interfaces, and structural integrity. Enforce continuous validation, consistency, and adherence to best practices throughout an iterative, evidence-driven consolidation and verification process.\\n- Please perform a rapid assessment of the project directory structure and identify the unique workflow and core functionality of the current utility. I'm particularly interested in a mermaid-diagram that shows how all the \\\"branches\\\" of the codebase converge. Utilize appropriate MCPs as necessary (e.g., 'filesystem-mcp').\\n- You're inside the current project and have access to the full codebase (and you interaction history with it). The current project/codebase is complex and is difficult to fully comprehend (when starting from scratch), and your contextwindow has grown large-so we need to find a way for you to express your accumulated knowledge in a way that i can copy and paste into a new context-window to transfer the most essential insights you've gathered throughout this (and previous) session(s).\\n- Aggregate all accumulated insights and knowledge about the current project/codebase from available context. Distill this information to identify core modules, architectural patterns, workflows, and points of integration. Condense the findings into a succinct yet comprehensive summary that highlights how these parts function together and converge toward the project's goals. Formulate the output as a single, optimized directive that encapsulates the codebase's integral components and operational cohesion. Present this synthesis as a set of clear, actionable commands or instructions, specifically phrased to instruct the coding assistant, not merely describe or report.\\n- Transform your accumulated, session-wide understanding of the current, complex codebase—gleaned from comprehensive context and full interaction history—into an actionable, instructive knowledge transfer protocol. Synthesize and distill all integral structural modules, essential architectural patterns, and key workflows, with special focus on their dynamic integration and functional convergence toward project objectives. Formulate a highly-optimized, directive-based set of precise commands targeted explicitly at coding assistants, ensuring exhaustive coverage of all major components, their relationships, and operational cohesion. The resulting synthesis must facilitate seamless portability: structure it for immediate copy-paste into any new context window, preserving the depth, interconnectivity, and systemic flow of the original codebase. Prioritize succinctness without compromising essential nuance, and express the entirety as a singular, comprehensive operational directive that primes the assistant for full-spectrum project navigation and intervention.\\n- Identify the fundamental structure, guiding principles, and core purpose of the project/codebase.\\n- Please familiarize yourself with the current @codebase. Your main imperative is to *actually* understand the codebase in a way where you're able to recognize meta-patterns and destroy complexity with simplicity and elegance, while capable of navigating through its *existing patterns and convergence* (never add unnecessary complexity). Deliver an ultra-concise project summary that rigorously defines scope, states objectives, and maximizes directional clarity. Provide a short summary of this project, including a bullet-point list of the core components and primary dependencies.\\n- Study the current codebase until you can identify core meta-patterns, and eliminate unnecessary complexity by applying simplicity within existing structures. Generate an ultra-concise project summary: define scope, objectives, and clarify direction. Summarize this project.\\n- Analyze the @codebase to uncover dominant patterns and remove complexity whenever possible, strictly adhering to present structures. Provide a succinct project overview with defined scope and explicit objectives. Include a brief project summary.\\n- Gain practical mastery over @codebase patterns, minimize complexity, and ensure all actions align with existing architecture. Supply a concise, clearly scoped project summary outlining objectives and key directions. Add a short summary.\\n- Absorb the current @codebase to the extent that you recognize meta-patterns and eradicate complexity, always favoring elegant simplicity. Navigate established patterns and points of convergence without adding unneeded complexity. Produce a rigorously concise project summary clearly stating scope and objectives for maximal directional clarity.\\n- Systematically dissect the codebase to extract core meta-patterns, eliminate all unnecessary complexity through elegant design, operate strictly within established architectural convergence, and deliver an ultra-concise project summary that rigorously defines scope and objectives with maximal clarity.\\n- Please familiarize yourself thoroughly with the current @codebase, beginning by systematically reviewing all markdown-files. Your core imperative is to *genuinely* understand the codebase to the degree where you can identify 'meta-patterns' and simplify complexity with elegance—always using and clarifying existing patterns, never introducing unnecessary complexity. Deliver an ultra-concise project summary that rigorously defines scope, states objectives, maximizes directional clarity, and highlights key meta-patterns discovered. Provide a short summary of this project.\\n- Achieve expert-level comprehension of the codebase by systematically analyzing all markdown files to extract meta-patterns, construct a robust contextual knowledgebase, and deliver an ultra-concise summary that rigorously defines scope and objectives, ensuring maximal clarity and alignment before making any changes.\\n- Fully internalize the @codebase to uncover meta-patterns and replace complexity with lucid simplicity. Remain aligned with existing convergence patterns, never increasing system intricacy. Generate a direct, sharply-focused summary that details scope and explicit objectives.\\n- Please thoroughly familiarize yourself with the current @codebase—your primary imperative is to understand the codebase so deeply that you could confidently make changes as if you were its original architect.\\n```\\n\\nhere's an example to show how generalized sequential prompts can be used effectively (on autonomous agents):\\n```\\n# Step 1:\\nFamiliarize yourself with the current @codebase. Comprehend the codebase in depth. Identify the fundamental structure, guiding principles, and core purpose.\\n# Step 2:\\nDive deeper to comprehend the intricate logic and inner workings that drive the project's functionality. As you explore, pay close attention to identifying the codebase's fundamental structure, underlying guiding principles, and its core purpose, ensuring you grasp both how and why the system is designed as it is. hen verify through a successful build.\\n# Step 3:\\nYou've now performed a successful build and familiarized yourself with the @codebase. Your next imperative is view the codebase through the lense of a brilliant (expert) SEO engineer, then create a short list of the most critical changes we need to make before bulding prod and deploying the website.\\n# Step 4:\\nYou've now performed a successful build and provided your SEO analysis of the codebase. Your next imperative is to leverage your knowledge of the codebase's fundamental structure and underlying guiding principles to ensure you're prepared for implementing the proposed SEO enhancements in a systematic order while retaining existing functionality and ensuring maximal clarity and alignment before making any changes.\\n# Step 5:\\nPlease systematically incorporate your proposed enhancements while ensuring existing functionality is retained, clarity is maximized, and alignment/coherence is maintained.\\n# Step 6:\\nPlease conduct a thorough final review to confirm seamless integration of all enhancements, ensuring that it is **truly** prepared for deployment.\\n```\\n\\n## Goal\\n\\nThe new sequence i'm tasking you to create needs to adhere to the established principles of the system, and should be able to take content-specific inputs and \\\"shape them\\\" into generalized prompts specifically (and meticulously) designed for use with autonomous agents. here's an example input:\\n```\\nllm_instructions_for_sublimetext_plugin='''You’re an expert Sublime Text plugin developer with a deep understanding of Python and the Sublime Text API. You specialize in creating intuitive and efficient plugins that enhance user productivity and streamline workflows. You possess deep knowledge of the Sublime Text API and its ecosystem (and best practices), along with a strong understanding of user needs and developer workflows.\\\\n\\\\n- Sublime Layout\\\\n    > Lagre Layouts\\\\n        > Lagre Regelsett knyttet til Layout (hvilke tabs som skal hvor, hvilke som skal lukkes, osv)\\\\n            > F.eks. `alltid flytt [\\\"main.py\\\", \\\"*.py.bak\\\"] til layout.4.index.2`\\\\n\\\\n    > Kan videreutvikles til en standard som automatisk opprettholdes ved alle (.sublime-project) prosjekter'''\\n```\\n\\nGeneralisert pipeline som omformer hvilken som helst spesifikasjon til **autonomous‑agent‑klare kodebase‑instruksjoner**\\n\\n*(én fil per trinn; følg navngiving 8005‑a/b/c/d/e‑\\\\<beskrivende‑navn>.md)*\\n\\n---\\n\\n```markdown\\n[Context Extractor] Ditt mål er **ikke** å løse brukerens forespørsel, men å isolere all praktisk kontekst (domene, verktøy, filstier, ønsket effekt). `{role=context_extractor; input=[spec:any]; process=[identifiser_domene_og_miljø(), trekk_ut_arbeidsrot(@codebase?), hent_eksplisitte_objektiver(), detekter_implisitte_brukskrav(), fjern_jargon_og_metaforer()]; constraints=[ingen_generering_av_løsninger]; output={context_profile:dict(domene:str?, root:str?, mål:list, krav:list)}}`\\n```\\n\\n---\\n\\n```markdown\\n[Task Vectorizer] Ditt mål er **ikke** å beskrive konteksten på nytt, men å oversette context_profile til en sekvens av atomiske, kodebase‑orienterte oppgaver. `{role=task_vectorizer; input=[context_profile:dict]; process=[del_inn_objektiver_i_diskrete_handlinger(), prefiksér_hver_handling_med_@codebase_sti_når_aktuelt(), bruk_presise_verber(identifiser|eliminer|konsolider), sørg_for_ordre_etter_avhengighet()], constraints=[ingen_overlappende_oppgaver(), unngå_kosmetiske_endringer_med_mindre_nødvendig], output={task_plan:list(str)}}`\\n```\\n\\n---\\n\\n```markdown\\n[Constraint Mapper] Ditt mål er **ikke** å endre task_plan, men å utlede eksplisitte rammer som må overholdes. `{role=constraint_mapper; input=[context_profile:dict, task_plan:list]; process=[kartlegg_funskjonelle_begrensninger(API, UI, legacy), fastslå_equivalence_krav(test, bench), identifiser_forbudsregler(kosmetikk, tredjepart), formuler_audit_logging_policy()], constraints=[ingen_duplisering_av_eksisterende_krav()], output={constraint_set:list(str)}}`\\n```\\n\\n---\\n\\n```markdown\\n[Prompt Assembler] Ditt mål er **ikke** å analysere innholdet, men å smi en ferdig agent‑prompt. `{role=prompt_assembler; input=[task_plan:list, constraint_set:list]; process=[bygg_inn_kort_intro_om_agentrolle(), list_task_plan_med_nummerering(), legg_til_constraint_set_som_obligatoriske_regler(), avslutt_med_valideringsmål(funke_eq, audit_ok)], constraints=[maks_300_ord, tydelig_seksjonsdeling(###)], output={agent_prompt:str}}`\\n```\\n\\n---\\n\\n```markdown\\n[Prompt Validator] Ditt mål er **ikke** å forbedre prompten kreativt, men å verifisere at den kan kjøres direkte av en autonom kode‑assistent. `{role=prompt_validator; input=[agent_prompt:str]; process=[sjekk_ordgrense(<=300), bekreft_@codebase_referanser_korrekt(), valider_nummerert_task_rekkefølge(), sikre_constraint_set_tilstede(), kontrollér_tydelig_goal_negation_i_intro()], constraints=[ingen_ekstra_ord, ingen_semantisk_endring], output={validated_prompt:str}}`\\n```\\\"```'\\n\\n\",\n  \"context_profile\": {\n    \"domain\": \"Autonomous agent prompt engineering for codebase-oriented workflows\",\n    \"project_root\": \"@codebase\",\n    \"goals\": [\n      \"Create a generalized instruction pipeline that transforms any specification (natural language or technical) into autonomous-agent-ready, codebase-oriented task prompts.\",\n      \"Ensure each stage in the pipeline is modular, clearly delimited, and named as a separate file according to the 8005-a/b/c/d/e-<descriptive_name>.md convention.\",\n      \"Input specifications can be in any form and should be normalized into actionable contexts for coding assistants.\"\n    ],\n    \"requirements\": [\n      \"Pipeline is strictly sequential and each step fulfills a defined, non-overlapping subrole.\",\n      \"Decompose specialized or content-heavy instructions into atomic, order-sensitive, codebase-oriented actions.\",\n      \"Infer domain, tools, project root (@codebase), explicit goals, and implicit requirements from the input.\",\n      \"Remove all jargon and metaphor from extracted context.\",\n      \"All outputs are to be formatted in markdown code fences and respect all output constraints per step.\",\n      \"Propagate structural and behavioral constraints (e.g., prohibit unnecessary cosmetic changes, enforce audit traceability, require functional equivalence and rollback) throughout the instructions.\",\n      \"Each file in the pipeline must serve a single, non-redundant transformation or extraction function and produce its output according to a predefined schema.\",\n      \"Prohibits solutions or implementations until the final assembly step; all earlier steps are extractive, not generative.\",\n      \"Final prompt must be directly useable within an autonomous coding agent (e.g., Cursor, VSCode+CLI agent) and reference the @codebase context explicitly.\",\n      \"Support generalized project types and various codebase structures; not hard-coded for any specific technology stack.\"\n    ]\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"ordered_tasks\": [\n    \"Extract domain, environment, project root (@codebase), explicit objectives, and implicit requirements from the input specification.\",\n    \"Remove all jargon, metaphor, and domain-specific language from the extracted context to normalize into a practical, agent-ready context profile.\",\n    \"Decompose explicit and implicit objectives into discrete, atomic, codebase-oriented tasks prefixed with @codebase paths where applicable.\",\n    \"Order the atomic tasks by dependency and ensure each task is action-oriented (identify, extract, consolidate, etc.), removing any overlap or redundancy.\",\n    \"Map and extract all explicit structural and behavioral constraints (e.g., prohibit cosmetic changes, require audit logging, enforce functional equivalence, support rollback).\",\n    \"Assign extracted constraints to their relevant tasks and propagate them through the pipeline, ensuring every task adheres to required constraints.\",\n    \"Package the ordered, codebase-oriented tasks and constraint set into a final agent-ready prompt with explicit @codebase references, appropriate sectioning, and output formatting for immediate use by autonomous agents.\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Constraint Mapper\n\nYour goal is **not** to edit tasks, but to **extract** all governing limits. Execute as:\n\n`{role=constraint_mapper; input=[context_profile:dict, ordered_tasks:list]; process=[collect_functional_constraints(), collect_behavioral_constraints(), collect_policy_constraints(), deduplicate_constraints()], constraints=[no_new_tasks(), no_redundant_entries()], output={constraint_set:[str]}}`",
      "step": "c",
      "title": "Constraint Mapper",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"# lets try something new\\nyour new goal is to create a new generalized instruction sequence specifically designed to transform inputs into autonomous-agent-ready prompts/instructions, for use with autonomous coding assistants such as cursor, vscode+cline/augment/etc. these prompts are typically written in a \\\"codebase-oriented\\\" manner, where the `@codebase` represents the root (of a project/workspace/directory), examples:\\n```\\n- Identify all instances of structural redundancy within the codebase by defining criteria specific to architectural and implementation duplication or overlap.\\n- Survey the entire codebase to locate and enumerate all occurrences of structural redundancy, distinguishing these from non-structural (stylistic or cosmetic) issues.\\n- Eliminate all forms of identified structural redundancy while ensuring that no existing user-visible behavior, API contract, or edge-case handling is altered.\\n- Define and catalog all components, modules, files, and subsystems, distinguishing and marking each as essential or excess according to direct functional mapping.\\n- Consolidate and remove excess components while strictly preserving system function, minimizing peripheral impact.\\n- For every modification, justify and log the intention, action, and rationale within an audit/logging system that provides real-time traceability and is prepared for retroactive auditability.\\n- Implement an audit trail with logs that are timestamped, attributable, and tamper-evident, ensuring that every codebase change is properly documented and traceable to specific authors or automated processes.\\n- Prohibit cosmetic, stylistic, or naming changes unless they are inseparably tied to the removal of redundancy or bloat, and document any exceptions explicitly within the audit log.\\n- Enforce strict detection and flagging mechanisms to prevent disallowed cosmetic changes, allowing exceptions only when justified as essential for redundancy or bloat removal, with explicit rationale provided.\\n- Require comprehensive functional equivalence verification—using robust automated testing, benchmarking, or behavioral validation—for all modifications prior to acceptance.\\n- Establish rigorous criteria and tool-supported thresholds for successful functional equivalence, and automate testing and verification procedures.\\n- Automate roll-back and reversion mechanisms for any changes that fail functional equivalence or cause unintended side-effects, ensuring all reversion is prompt and complete.\\n- Develop and apply explicit minimal-impact criteria for all modification actions, constraining all interventions to only those addressing superfluous complexity.\\n- Mandate review and authorization checkpoints for each intended change, requiring concise rationale and authorization documentation at every intervention point.\\n- Ensure every modification is tightly scoped, minimizing the blast radius, and demonstrate that the action introduces no unnecessary impact beyond its core objective.\\n- Systematically and iteratively analyze all constituent elements of the codebase to detect and categorize all forms of redundancy, duplication, and obsolescence. Continuously identify and categorize all genuine redundancies and obsolescence while strictly prohibit unnecessary or cosmetic changes, with automated flagging and explicit justification required for any exceptions. Systematically and iteratively inspect all components within the system/environment to identify and remove elements that are unnecessary, duplicated, or outdated. Guarantee that all essential behaviors and interfaces remain unchanged by validating functional equivalence after each modification. Forbid changes that alter functionality unless directly required to remove redundancy.\\n- Map all references and usages of each element across all contexts.\\n- Analyze the entire codebase systematically and iteratively to maximize coherence, maintainability, and clarity by cataloging every element (file, module, function, asset, interface), mapping all references and usages, and detecting all forms of redundancy, duplication, and obsolescence—including exact and functional duplication and unused or outdated elements. Categorize findings using explicit, evidence-based metrics. Eliminate or consolidate only elements confirmed as redundant or obsolete through both automated and manual checks, ensuring no essential behavior or interface is altered. Enforce functional equivalence and preserve structural integrity after each change by performing comprehensive, codebase-wide validation and calibration using explicit, quantitative outputs (including git diff analysis before and after modifications). Prevent the introduction of new redundancy or inconsistency by continuously analyzing for and correcting systemic issues, ensuring systematic consistency across all codebase sections (e.g., /src/sections), and adhering to best practices throughout. Centralize repeated components or logic where high-value consolidation is possible without functional loss. Use the provided directory tree as the single source of truth for validation, and transparently document all processes, metrics, and validation outcomes. Prohibit modification of protected, legacy, or third-party code except if directly related to resolving redundancy or obsolescence.\\n- Maximize codebase coherence, maintainability, and clarity by systematically eliminating all forms of redundancy, duplication, and obsolescence, while ensuring functional equivalence and structural integrity. Prevent the persistence or inadvertent introduction of redundancy, duplication, obsolescence, or inconsistency within the codebase, and prohibit changes that alter essential behaviors or interfaces. Systematically maximize codebase coherence, maintainability, and clarity by eliminating all forms of redundancy, duplication, and obsolescence while strictly preserving functional equivalence, interface stability, and operational integrity across the entire codebase. Achieve maximal codebase coherence, maintainability, and clarity by systematically eliminating all forms of redundancy, duplication, and obsolescence, while preserving essential behaviors, interfaces, and structural integrity. Enforce continuous validation, consistency, and adherence to best practices throughout an iterative, evidence-driven consolidation and verification process.\\n- Please perform a rapid assessment of the project directory structure and identify the unique workflow and core functionality of the current utility. I'm particularly interested in a mermaid-diagram that shows how all the \\\"branches\\\" of the codebase converge. Utilize appropriate MCPs as necessary (e.g., 'filesystem-mcp').\\n- You're inside the current project and have access to the full codebase (and you interaction history with it). The current project/codebase is complex and is difficult to fully comprehend (when starting from scratch), and your contextwindow has grown large-so we need to find a way for you to express your accumulated knowledge in a way that i can copy and paste into a new context-window to transfer the most essential insights you've gathered throughout this (and previous) session(s).\\n- Aggregate all accumulated insights and knowledge about the current project/codebase from available context. Distill this information to identify core modules, architectural patterns, workflows, and points of integration. Condense the findings into a succinct yet comprehensive summary that highlights how these parts function together and converge toward the project's goals. Formulate the output as a single, optimized directive that encapsulates the codebase's integral components and operational cohesion. Present this synthesis as a set of clear, actionable commands or instructions, specifically phrased to instruct the coding assistant, not merely describe or report.\\n- Transform your accumulated, session-wide understanding of the current, complex codebase—gleaned from comprehensive context and full interaction history—into an actionable, instructive knowledge transfer protocol. Synthesize and distill all integral structural modules, essential architectural patterns, and key workflows, with special focus on their dynamic integration and functional convergence toward project objectives. Formulate a highly-optimized, directive-based set of precise commands targeted explicitly at coding assistants, ensuring exhaustive coverage of all major components, their relationships, and operational cohesion. The resulting synthesis must facilitate seamless portability: structure it for immediate copy-paste into any new context window, preserving the depth, interconnectivity, and systemic flow of the original codebase. Prioritize succinctness without compromising essential nuance, and express the entirety as a singular, comprehensive operational directive that primes the assistant for full-spectrum project navigation and intervention.\\n- Identify the fundamental structure, guiding principles, and core purpose of the project/codebase.\\n- Please familiarize yourself with the current @codebase. Your main imperative is to *actually* understand the codebase in a way where you're able to recognize meta-patterns and destroy complexity with simplicity and elegance, while capable of navigating through its *existing patterns and convergence* (never add unnecessary complexity). Deliver an ultra-concise project summary that rigorously defines scope, states objectives, and maximizes directional clarity. Provide a short summary of this project, including a bullet-point list of the core components and primary dependencies.\\n- Study the current codebase until you can identify core meta-patterns, and eliminate unnecessary complexity by applying simplicity within existing structures. Generate an ultra-concise project summary: define scope, objectives, and clarify direction. Summarize this project.\\n- Analyze the @codebase to uncover dominant patterns and remove complexity whenever possible, strictly adhering to present structures. Provide a succinct project overview with defined scope and explicit objectives. Include a brief project summary.\\n- Gain practical mastery over @codebase patterns, minimize complexity, and ensure all actions align with existing architecture. Supply a concise, clearly scoped project summary outlining objectives and key directions. Add a short summary.\\n- Absorb the current @codebase to the extent that you recognize meta-patterns and eradicate complexity, always favoring elegant simplicity. Navigate established patterns and points of convergence without adding unneeded complexity. Produce a rigorously concise project summary clearly stating scope and objectives for maximal directional clarity.\\n- Systematically dissect the codebase to extract core meta-patterns, eliminate all unnecessary complexity through elegant design, operate strictly within established architectural convergence, and deliver an ultra-concise project summary that rigorously defines scope and objectives with maximal clarity.\\n- Please familiarize yourself thoroughly with the current @codebase, beginning by systematically reviewing all markdown-files. Your core imperative is to *genuinely* understand the codebase to the degree where you can identify 'meta-patterns' and simplify complexity with elegance—always using and clarifying existing patterns, never introducing unnecessary complexity. Deliver an ultra-concise project summary that rigorously defines scope, states objectives, maximizes directional clarity, and highlights key meta-patterns discovered. Provide a short summary of this project.\\n- Achieve expert-level comprehension of the codebase by systematically analyzing all markdown files to extract meta-patterns, construct a robust contextual knowledgebase, and deliver an ultra-concise summary that rigorously defines scope and objectives, ensuring maximal clarity and alignment before making any changes.\\n- Fully internalize the @codebase to uncover meta-patterns and replace complexity with lucid simplicity. Remain aligned with existing convergence patterns, never increasing system intricacy. Generate a direct, sharply-focused summary that details scope and explicit objectives.\\n- Please thoroughly familiarize yourself with the current @codebase—your primary imperative is to understand the codebase so deeply that you could confidently make changes as if you were its original architect.\\n```\\n\\nhere's an example to show how generalized sequential prompts can be used effectively (on autonomous agents):\\n```\\n# Step 1:\\nFamiliarize yourself with the current @codebase. Comprehend the codebase in depth. Identify the fundamental structure, guiding principles, and core purpose.\\n# Step 2:\\nDive deeper to comprehend the intricate logic and inner workings that drive the project's functionality. As you explore, pay close attention to identifying the codebase's fundamental structure, underlying guiding principles, and its core purpose, ensuring you grasp both how and why the system is designed as it is. hen verify through a successful build.\\n# Step 3:\\nYou've now performed a successful build and familiarized yourself with the @codebase. Your next imperative is view the codebase through the lense of a brilliant (expert) SEO engineer, then create a short list of the most critical changes we need to make before bulding prod and deploying the website.\\n# Step 4:\\nYou've now performed a successful build and provided your SEO analysis of the codebase. Your next imperative is to leverage your knowledge of the codebase's fundamental structure and underlying guiding principles to ensure you're prepared for implementing the proposed SEO enhancements in a systematic order while retaining existing functionality and ensuring maximal clarity and alignment before making any changes.\\n# Step 5:\\nPlease systematically incorporate your proposed enhancements while ensuring existing functionality is retained, clarity is maximized, and alignment/coherence is maintained.\\n# Step 6:\\nPlease conduct a thorough final review to confirm seamless integration of all enhancements, ensuring that it is **truly** prepared for deployment.\\n```\\n\\n## Goal\\n\\nThe new sequence i'm tasking you to create needs to adhere to the established principles of the system, and should be able to take content-specific inputs and \\\"shape them\\\" into generalized prompts specifically (and meticulously) designed for use with autonomous agents. here's an example input:\\n```\\nllm_instructions_for_sublimetext_plugin='''You’re an expert Sublime Text plugin developer with a deep understanding of Python and the Sublime Text API. You specialize in creating intuitive and efficient plugins that enhance user productivity and streamline workflows. You possess deep knowledge of the Sublime Text API and its ecosystem (and best practices), along with a strong understanding of user needs and developer workflows.\\\\n\\\\n- Sublime Layout\\\\n    > Lagre Layouts\\\\n        > Lagre Regelsett knyttet til Layout (hvilke tabs som skal hvor, hvilke som skal lukkes, osv)\\\\n            > F.eks. `alltid flytt [\\\"main.py\\\", \\\"*.py.bak\\\"] til layout.4.index.2`\\\\n\\\\n    > Kan videreutvikles til en standard som automatisk opprettholdes ved alle (.sublime-project) prosjekter'''\\n```\\n\\nGeneralisert pipeline som omformer hvilken som helst spesifikasjon til **autonomous‑agent‑klare kodebase‑instruksjoner**\\n\\n*(én fil per trinn; følg navngiving 8005‑a/b/c/d/e‑\\\\<beskrivende‑navn>.md)*\\n\\n---\\n\\n```markdown\\n[Context Extractor] Ditt mål er **ikke** å løse brukerens forespørsel, men å isolere all praktisk kontekst (domene, verktøy, filstier, ønsket effekt). `{role=context_extractor; input=[spec:any]; process=[identifiser_domene_og_miljø(), trekk_ut_arbeidsrot(@codebase?), hent_eksplisitte_objektiver(), detekter_implisitte_brukskrav(), fjern_jargon_og_metaforer()]; constraints=[ingen_generering_av_løsninger]; output={context_profile:dict(domene:str?, root:str?, mål:list, krav:list)}}`\\n```\\n\\n---\\n\\n```markdown\\n[Task Vectorizer] Ditt mål er **ikke** å beskrive konteksten på nytt, men å oversette context_profile til en sekvens av atomiske, kodebase‑orienterte oppgaver. `{role=task_vectorizer; input=[context_profile:dict]; process=[del_inn_objektiver_i_diskrete_handlinger(), prefiksér_hver_handling_med_@codebase_sti_når_aktuelt(), bruk_presise_verber(identifiser|eliminer|konsolider), sørg_for_ordre_etter_avhengighet()], constraints=[ingen_overlappende_oppgaver(), unngå_kosmetiske_endringer_med_mindre_nødvendig], output={task_plan:list(str)}}`\\n```\\n\\n---\\n\\n```markdown\\n[Constraint Mapper] Ditt mål er **ikke** å endre task_plan, men å utlede eksplisitte rammer som må overholdes. `{role=constraint_mapper; input=[context_profile:dict, task_plan:list]; process=[kartlegg_funskjonelle_begrensninger(API, UI, legacy), fastslå_equivalence_krav(test, bench), identifiser_forbudsregler(kosmetikk, tredjepart), formuler_audit_logging_policy()], constraints=[ingen_duplisering_av_eksisterende_krav()], output={constraint_set:list(str)}}`\\n```\\n\\n---\\n\\n```markdown\\n[Prompt Assembler] Ditt mål er **ikke** å analysere innholdet, men å smi en ferdig agent‑prompt. `{role=prompt_assembler; input=[task_plan:list, constraint_set:list]; process=[bygg_inn_kort_intro_om_agentrolle(), list_task_plan_med_nummerering(), legg_til_constraint_set_som_obligatoriske_regler(), avslutt_med_valideringsmål(funke_eq, audit_ok)], constraints=[maks_300_ord, tydelig_seksjonsdeling(###)], output={agent_prompt:str}}`\\n```\\n\\n---\\n\\n```markdown\\n[Prompt Validator] Ditt mål er **ikke** å forbedre prompten kreativt, men å verifisere at den kan kjøres direkte av en autonom kode‑assistent. `{role=prompt_validator; input=[agent_prompt:str]; process=[sjekk_ordgrense(<=300), bekreft_@codebase_referanser_korrekt(), valider_nummerert_task_rekkefølge(), sikre_constraint_set_tilstede(), kontrollér_tydelig_goal_negation_i_intro()], constraints=[ingen_ekstra_ord, ingen_semantisk_endring], output={validated_prompt:str}}`\\n```\\\"```'\\n\\n\",\n  \"ordered_tasks\": [\n    \"Extract domain, environment, project root (@codebase), explicit objectives, and implicit requirements from the input specification.\",\n    \"Remove all jargon, metaphor, and domain-specific language from the extracted context to normalize into a practical, agent-ready context profile.\",\n    \"Decompose explicit and implicit objectives into discrete, atomic, codebase-oriented tasks prefixed with @codebase paths where applicable.\",\n    \"Order the atomic tasks by dependency and ensure each task is action-oriented (identify, extract, consolidate, etc.), removing any overlap or redundancy.\",\n    \"Map and extract all explicit structural and behavioral constraints (e.g., prohibit cosmetic changes, require audit logging, enforce functional equivalence, support rollback).\",\n    \"Assign extracted constraints to their relevant tasks and propagate them through the pipeline, ensuring every task adheres to required constraints.\",\n    \"Package the ordered, codebase-oriented tasks and constraint set into a final agent-ready prompt with explicit @codebase references, appropriate sectioning, and output formatting for immediate use by autonomous agents.\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"constraint_set\": [\n    \"No domain-specific jargon, metaphor, or ambiguous language in context profile or tasks—normalize to practical instructions.\",\n    \"Decompose all objectives (explicit and implicit) into discrete, atomic, codebase-oriented tasks; prohibit overlapping or redundant tasks.\",\n    \"Prefix codebase-oriented tasks with @codebase paths where applicable.\",\n    \"Order tasks by dependency; every task must be action-oriented and free of redundancy.\",\n    \"Prohibit all cosmetic, stylistic, or naming-only changes unless inseparably required by functional objectives (e.g., redundancy/bloat removal), and document any exception explicitly.\",\n    \"Require audit logging: For every modification, log intention, action, rationale; logs must be timestamped, attributable, and tamper-evident, enabling full auditability.\",\n    \"Strictly enforce functional equivalence after all modifications: verify via automated testing, benchmarking, or behavioral validation prior to acceptance.\",\n    \"Establish and enforce automated roll-back or reversion mechanisms for any change that fails functional equivalence or introduces unintended side effects.\",\n    \"Protect legacy, third-party, or otherwise protected code—modify only if directly and unavoidably required to resolve redundancy, bloat, or obsolescence.\",\n    \"Consolidate or remove only elements confirmed redundant or obsolete by both automated and manual checks; preserve all essential behaviors and interfaces at all times.\",\n    \"Document all processes, metrics, and validation outcomes within the audit trail.\",\n    \"No new tasks not explicitly or implicitly derived from the input context and objectives.\",\n    \"No redundant or duplicated constraint entries—deduplicate within the final constraint set.\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Prompt Assembler\n\nYour goal is **not** to alter semantics, but to **compile** an agent-ready prompt. Execute as:\n\n`{role=prompt_assembler; input=[ordered_tasks:list, constraint_set:list, context_profile:dict]; process=[insert_goal_negation_header(), embed_agent_role(), enumerate_tasks(), append_constraints(), add_validation_targets(functional_equivalence=True,audit=True)], constraints=[≤300_tokens, english_only()], output={agent_prompt:str}}`",
      "step": "d",
      "title": "Prompt Assembler",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"# lets try something new\\nyour new goal is to create a new generalized instruction sequence specifically designed to transform inputs into autonomous-agent-ready prompts/instructions, for use with autonomous coding assistants such as cursor, vscode+cline/augment/etc. these prompts are typically written in a \\\"codebase-oriented\\\" manner, where the `@codebase` represents the root (of a project/workspace/directory), examples:\\n```\\n- Identify all instances of structural redundancy within the codebase by defining criteria specific to architectural and implementation duplication or overlap.\\n- Survey the entire codebase to locate and enumerate all occurrences of structural redundancy, distinguishing these from non-structural (stylistic or cosmetic) issues.\\n- Eliminate all forms of identified structural redundancy while ensuring that no existing user-visible behavior, API contract, or edge-case handling is altered.\\n- Define and catalog all components, modules, files, and subsystems, distinguishing and marking each as essential or excess according to direct functional mapping.\\n- Consolidate and remove excess components while strictly preserving system function, minimizing peripheral impact.\\n- For every modification, justify and log the intention, action, and rationale within an audit/logging system that provides real-time traceability and is prepared for retroactive auditability.\\n- Implement an audit trail with logs that are timestamped, attributable, and tamper-evident, ensuring that every codebase change is properly documented and traceable to specific authors or automated processes.\\n- Prohibit cosmetic, stylistic, or naming changes unless they are inseparably tied to the removal of redundancy or bloat, and document any exceptions explicitly within the audit log.\\n- Enforce strict detection and flagging mechanisms to prevent disallowed cosmetic changes, allowing exceptions only when justified as essential for redundancy or bloat removal, with explicit rationale provided.\\n- Require comprehensive functional equivalence verification—using robust automated testing, benchmarking, or behavioral validation—for all modifications prior to acceptance.\\n- Establish rigorous criteria and tool-supported thresholds for successful functional equivalence, and automate testing and verification procedures.\\n- Automate roll-back and reversion mechanisms for any changes that fail functional equivalence or cause unintended side-effects, ensuring all reversion is prompt and complete.\\n- Develop and apply explicit minimal-impact criteria for all modification actions, constraining all interventions to only those addressing superfluous complexity.\\n- Mandate review and authorization checkpoints for each intended change, requiring concise rationale and authorization documentation at every intervention point.\\n- Ensure every modification is tightly scoped, minimizing the blast radius, and demonstrate that the action introduces no unnecessary impact beyond its core objective.\\n- Systematically and iteratively analyze all constituent elements of the codebase to detect and categorize all forms of redundancy, duplication, and obsolescence. Continuously identify and categorize all genuine redundancies and obsolescence while strictly prohibit unnecessary or cosmetic changes, with automated flagging and explicit justification required for any exceptions. Systematically and iteratively inspect all components within the system/environment to identify and remove elements that are unnecessary, duplicated, or outdated. Guarantee that all essential behaviors and interfaces remain unchanged by validating functional equivalence after each modification. Forbid changes that alter functionality unless directly required to remove redundancy.\\n- Map all references and usages of each element across all contexts.\\n- Analyze the entire codebase systematically and iteratively to maximize coherence, maintainability, and clarity by cataloging every element (file, module, function, asset, interface), mapping all references and usages, and detecting all forms of redundancy, duplication, and obsolescence—including exact and functional duplication and unused or outdated elements. Categorize findings using explicit, evidence-based metrics. Eliminate or consolidate only elements confirmed as redundant or obsolete through both automated and manual checks, ensuring no essential behavior or interface is altered. Enforce functional equivalence and preserve structural integrity after each change by performing comprehensive, codebase-wide validation and calibration using explicit, quantitative outputs (including git diff analysis before and after modifications). Prevent the introduction of new redundancy or inconsistency by continuously analyzing for and correcting systemic issues, ensuring systematic consistency across all codebase sections (e.g., /src/sections), and adhering to best practices throughout. Centralize repeated components or logic where high-value consolidation is possible without functional loss. Use the provided directory tree as the single source of truth for validation, and transparently document all processes, metrics, and validation outcomes. Prohibit modification of protected, legacy, or third-party code except if directly related to resolving redundancy or obsolescence.\\n- Maximize codebase coherence, maintainability, and clarity by systematically eliminating all forms of redundancy, duplication, and obsolescence, while ensuring functional equivalence and structural integrity. Prevent the persistence or inadvertent introduction of redundancy, duplication, obsolescence, or inconsistency within the codebase, and prohibit changes that alter essential behaviors or interfaces. Systematically maximize codebase coherence, maintainability, and clarity by eliminating all forms of redundancy, duplication, and obsolescence while strictly preserving functional equivalence, interface stability, and operational integrity across the entire codebase. Achieve maximal codebase coherence, maintainability, and clarity by systematically eliminating all forms of redundancy, duplication, and obsolescence, while preserving essential behaviors, interfaces, and structural integrity. Enforce continuous validation, consistency, and adherence to best practices throughout an iterative, evidence-driven consolidation and verification process.\\n- Please perform a rapid assessment of the project directory structure and identify the unique workflow and core functionality of the current utility. I'm particularly interested in a mermaid-diagram that shows how all the \\\"branches\\\" of the codebase converge. Utilize appropriate MCPs as necessary (e.g., 'filesystem-mcp').\\n- You're inside the current project and have access to the full codebase (and you interaction history with it). The current project/codebase is complex and is difficult to fully comprehend (when starting from scratch), and your contextwindow has grown large-so we need to find a way for you to express your accumulated knowledge in a way that i can copy and paste into a new context-window to transfer the most essential insights you've gathered throughout this (and previous) session(s).\\n- Aggregate all accumulated insights and knowledge about the current project/codebase from available context. Distill this information to identify core modules, architectural patterns, workflows, and points of integration. Condense the findings into a succinct yet comprehensive summary that highlights how these parts function together and converge toward the project's goals. Formulate the output as a single, optimized directive that encapsulates the codebase's integral components and operational cohesion. Present this synthesis as a set of clear, actionable commands or instructions, specifically phrased to instruct the coding assistant, not merely describe or report.\\n- Transform your accumulated, session-wide understanding of the current, complex codebase—gleaned from comprehensive context and full interaction history—into an actionable, instructive knowledge transfer protocol. Synthesize and distill all integral structural modules, essential architectural patterns, and key workflows, with special focus on their dynamic integration and functional convergence toward project objectives. Formulate a highly-optimized, directive-based set of precise commands targeted explicitly at coding assistants, ensuring exhaustive coverage of all major components, their relationships, and operational cohesion. The resulting synthesis must facilitate seamless portability: structure it for immediate copy-paste into any new context window, preserving the depth, interconnectivity, and systemic flow of the original codebase. Prioritize succinctness without compromising essential nuance, and express the entirety as a singular, comprehensive operational directive that primes the assistant for full-spectrum project navigation and intervention.\\n- Identify the fundamental structure, guiding principles, and core purpose of the project/codebase.\\n- Please familiarize yourself with the current @codebase. Your main imperative is to *actually* understand the codebase in a way where you're able to recognize meta-patterns and destroy complexity with simplicity and elegance, while capable of navigating through its *existing patterns and convergence* (never add unnecessary complexity). Deliver an ultra-concise project summary that rigorously defines scope, states objectives, and maximizes directional clarity. Provide a short summary of this project, including a bullet-point list of the core components and primary dependencies.\\n- Study the current codebase until you can identify core meta-patterns, and eliminate unnecessary complexity by applying simplicity within existing structures. Generate an ultra-concise project summary: define scope, objectives, and clarify direction. Summarize this project.\\n- Analyze the @codebase to uncover dominant patterns and remove complexity whenever possible, strictly adhering to present structures. Provide a succinct project overview with defined scope and explicit objectives. Include a brief project summary.\\n- Gain practical mastery over @codebase patterns, minimize complexity, and ensure all actions align with existing architecture. Supply a concise, clearly scoped project summary outlining objectives and key directions. Add a short summary.\\n- Absorb the current @codebase to the extent that you recognize meta-patterns and eradicate complexity, always favoring elegant simplicity. Navigate established patterns and points of convergence without adding unneeded complexity. Produce a rigorously concise project summary clearly stating scope and objectives for maximal directional clarity.\\n- Systematically dissect the codebase to extract core meta-patterns, eliminate all unnecessary complexity through elegant design, operate strictly within established architectural convergence, and deliver an ultra-concise project summary that rigorously defines scope and objectives with maximal clarity.\\n- Please familiarize yourself thoroughly with the current @codebase, beginning by systematically reviewing all markdown-files. Your core imperative is to *genuinely* understand the codebase to the degree where you can identify 'meta-patterns' and simplify complexity with elegance—always using and clarifying existing patterns, never introducing unnecessary complexity. Deliver an ultra-concise project summary that rigorously defines scope, states objectives, maximizes directional clarity, and highlights key meta-patterns discovered. Provide a short summary of this project.\\n- Achieve expert-level comprehension of the codebase by systematically analyzing all markdown files to extract meta-patterns, construct a robust contextual knowledgebase, and deliver an ultra-concise summary that rigorously defines scope and objectives, ensuring maximal clarity and alignment before making any changes.\\n- Fully internalize the @codebase to uncover meta-patterns and replace complexity with lucid simplicity. Remain aligned with existing convergence patterns, never increasing system intricacy. Generate a direct, sharply-focused summary that details scope and explicit objectives.\\n- Please thoroughly familiarize yourself with the current @codebase—your primary imperative is to understand the codebase so deeply that you could confidently make changes as if you were its original architect.\\n```\\n\\nhere's an example to show how generalized sequential prompts can be used effectively (on autonomous agents):\\n```\\n# Step 1:\\nFamiliarize yourself with the current @codebase. Comprehend the codebase in depth. Identify the fundamental structure, guiding principles, and core purpose.\\n# Step 2:\\nDive deeper to comprehend the intricate logic and inner workings that drive the project's functionality. As you explore, pay close attention to identifying the codebase's fundamental structure, underlying guiding principles, and its core purpose, ensuring you grasp both how and why the system is designed as it is. hen verify through a successful build.\\n# Step 3:\\nYou've now performed a successful build and familiarized yourself with the @codebase. Your next imperative is view the codebase through the lense of a brilliant (expert) SEO engineer, then create a short list of the most critical changes we need to make before bulding prod and deploying the website.\\n# Step 4:\\nYou've now performed a successful build and provided your SEO analysis of the codebase. Your next imperative is to leverage your knowledge of the codebase's fundamental structure and underlying guiding principles to ensure you're prepared for implementing the proposed SEO enhancements in a systematic order while retaining existing functionality and ensuring maximal clarity and alignment before making any changes.\\n# Step 5:\\nPlease systematically incorporate your proposed enhancements while ensuring existing functionality is retained, clarity is maximized, and alignment/coherence is maintained.\\n# Step 6:\\nPlease conduct a thorough final review to confirm seamless integration of all enhancements, ensuring that it is **truly** prepared for deployment.\\n```\\n\\n## Goal\\n\\nThe new sequence i'm tasking you to create needs to adhere to the established principles of the system, and should be able to take content-specific inputs and \\\"shape them\\\" into generalized prompts specifically (and meticulously) designed for use with autonomous agents. here's an example input:\\n```\\nllm_instructions_for_sublimetext_plugin='''You’re an expert Sublime Text plugin developer with a deep understanding of Python and the Sublime Text API. You specialize in creating intuitive and efficient plugins that enhance user productivity and streamline workflows. You possess deep knowledge of the Sublime Text API and its ecosystem (and best practices), along with a strong understanding of user needs and developer workflows.\\\\n\\\\n- Sublime Layout\\\\n    > Lagre Layouts\\\\n        > Lagre Regelsett knyttet til Layout (hvilke tabs som skal hvor, hvilke som skal lukkes, osv)\\\\n            > F.eks. `alltid flytt [\\\"main.py\\\", \\\"*.py.bak\\\"] til layout.4.index.2`\\\\n\\\\n    > Kan videreutvikles til en standard som automatisk opprettholdes ved alle (.sublime-project) prosjekter'''\\n```\\n\\nGeneralisert pipeline som omformer hvilken som helst spesifikasjon til **autonomous‑agent‑klare kodebase‑instruksjoner**\\n\\n*(én fil per trinn; følg navngiving 8005‑a/b/c/d/e‑\\\\<beskrivende‑navn>.md)*\\n\\n---\\n\\n```markdown\\n[Context Extractor] Ditt mål er **ikke** å løse brukerens forespørsel, men å isolere all praktisk kontekst (domene, verktøy, filstier, ønsket effekt). `{role=context_extractor; input=[spec:any]; process=[identifiser_domene_og_miljø(), trekk_ut_arbeidsrot(@codebase?), hent_eksplisitte_objektiver(), detekter_implisitte_brukskrav(), fjern_jargon_og_metaforer()]; constraints=[ingen_generering_av_løsninger]; output={context_profile:dict(domene:str?, root:str?, mål:list, krav:list)}}`\\n```\\n\\n---\\n\\n```markdown\\n[Task Vectorizer] Ditt mål er **ikke** å beskrive konteksten på nytt, men å oversette context_profile til en sekvens av atomiske, kodebase‑orienterte oppgaver. `{role=task_vectorizer; input=[context_profile:dict]; process=[del_inn_objektiver_i_diskrete_handlinger(), prefiksér_hver_handling_med_@codebase_sti_når_aktuelt(), bruk_presise_verber(identifiser|eliminer|konsolider), sørg_for_ordre_etter_avhengighet()], constraints=[ingen_overlappende_oppgaver(), unngå_kosmetiske_endringer_med_mindre_nødvendig], output={task_plan:list(str)}}`\\n```\\n\\n---\\n\\n```markdown\\n[Constraint Mapper] Ditt mål er **ikke** å endre task_plan, men å utlede eksplisitte rammer som må overholdes. `{role=constraint_mapper; input=[context_profile:dict, task_plan:list]; process=[kartlegg_funskjonelle_begrensninger(API, UI, legacy), fastslå_equivalence_krav(test, bench), identifiser_forbudsregler(kosmetikk, tredjepart), formuler_audit_logging_policy()], constraints=[ingen_duplisering_av_eksisterende_krav()], output={constraint_set:list(str)}}`\\n```\\n\\n---\\n\\n```markdown\\n[Prompt Assembler] Ditt mål er **ikke** å analysere innholdet, men å smi en ferdig agent‑prompt. `{role=prompt_assembler; input=[task_plan:list, constraint_set:list]; process=[bygg_inn_kort_intro_om_agentrolle(), list_task_plan_med_nummerering(), legg_til_constraint_set_som_obligatoriske_regler(), avslutt_med_valideringsmål(funke_eq, audit_ok)], constraints=[maks_300_ord, tydelig_seksjonsdeling(###)], output={agent_prompt:str}}`\\n```\\n\\n---\\n\\n```markdown\\n[Prompt Validator] Ditt mål er **ikke** å forbedre prompten kreativt, men å verifisere at den kan kjøres direkte av en autonom kode‑assistent. `{role=prompt_validator; input=[agent_prompt:str]; process=[sjekk_ordgrense(<=300), bekreft_@codebase_referanser_korrekt(), valider_nummerert_task_rekkefølge(), sikre_constraint_set_tilstede(), kontrollér_tydelig_goal_negation_i_intro()], constraints=[ingen_ekstra_ord, ingen_semantisk_endring], output={validated_prompt:str}}`\\n```\\\"```'\\n\\n\",\n  \"constraint_set\": [\n    \"No domain-specific jargon, metaphor, or ambiguous language in context profile or tasks—normalize to practical instructions.\",\n    \"Decompose all objectives (explicit and implicit) into discrete, atomic, codebase-oriented tasks; prohibit overlapping or redundant tasks.\",\n    \"Prefix codebase-oriented tasks with @codebase paths where applicable.\",\n    \"Order tasks by dependency; every task must be action-oriented and free of redundancy.\",\n    \"Prohibit all cosmetic, stylistic, or naming-only changes unless inseparably required by functional objectives (e.g., redundancy/bloat removal), and document any exception explicitly.\",\n    \"Require audit logging: For every modification, log intention, action, rationale; logs must be timestamped, attributable, and tamper-evident, enabling full auditability.\",\n    \"Strictly enforce functional equivalence after all modifications: verify via automated testing, benchmarking, or behavioral validation prior to acceptance.\",\n    \"Establish and enforce automated roll-back or reversion mechanisms for any change that fails functional equivalence or introduces unintended side effects.\",\n    \"Protect legacy, third-party, or otherwise protected code—modify only if directly and unavoidably required to resolve redundancy, bloat, or obsolescence.\",\n    \"Consolidate or remove only elements confirmed redundant or obsolete by both automated and manual checks; preserve all essential behaviors and interfaces at all times.\",\n    \"Document all processes, metrics, and validation outcomes within the audit trail.\",\n    \"No new tasks not explicitly or implicitly derived from the input context and objectives.\",\n    \"No redundant or duplicated constraint entries—deduplicate within the final constraint set.\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"agent_prompt\": \"### DO NOT solve the user's query directly; instead, transform the provided input into a structured, autonomous-agent-ready instruction prompt tailored for codebase navigation and intervention.\\n\\n---\\n\\n#### Agent Role\\nAct as an autonomous coding assistant: interpret objectives from context, plan actions accordingly, and execute all modifications systematically within the codebase while strictly adhering to operational, functional, and audit constraints.\\n\\n---\\n\\n#### Tasks\\n1. Enumerate all explicit and implicit objectives and requirements derived from input context.\\n2. Decompose each objective into discrete, atomic tasks—each task must be actionable, non-redundant, and prefixed with appropriate @codebase paths where relevant.\\n3. Order all tasks logically by dependencies to ensure efficient and safe execution sequence.\\n4. For each task, prepare precise implementation steps, removing ambiguity and ensuring cross-team clarity.\\n\\n---\\n\\n#### Constraints\\n- Use no domain-specific jargon or metaphors; normalize all language to clear, practical instructions.\\n- Forbid all cosmetic, stylistic, or naming-only changes unless strictly inseparable from functional changes; document all exceptions.\\n- Require comprehensive audit logging for every modification (intention, action, rationale, timestamp, attribution, tamper-evidence).\\n- Enforce functional equivalence post-modification using automated test, benchmark, or behavioral validation.\\n- Prohibit changes to legacy or third-party code except as strictly required for redundancy/bloat/obsolescence removal.\\n- Consolidate or remove only elements verified as redundant/obsolete by both automated and manual checks; preserve all essential behaviors/interfaces.\\n- Document all processes, metrics, validation, and audit outcomes.\\n- No new tasks or constraints are permitted beyond those explicitly or implicitly established by the initial input and context.\\n\\n---\\n\\n#### Validation Targets\\n- Confirm all modifications strictly maintain functional equivalence—audit with automated and benchmark tests before acceptance.\\n- Ensure comprehensive auditability for every change.\\n\\n---\"\n}",
        }
      }
    },
      {
      "instruction": "# Prompt Validator\n\nYour goal is **not** to rewrite the prompt, but to **verify** strict compliance for execution. Execute as:\n\n`{role=prompt_validator; input=[agent_prompt:str]; process=[check_goal_negation_header(), check_section_labels(), check_word_count(≤300), validate_path_prefixes(\"@codebase\"), confirm_task_order(), confirm_constraint_presence()], constraints=[no_semantic_change()], output={validated_prompt:str}}`",
      "step": "e",
      "title": "Prompt Validator",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"# lets try something new\\nyour new goal is to create a new generalized instruction sequence specifically designed to transform inputs into autonomous-agent-ready prompts/instructions, for use with autonomous coding assistants such as cursor, vscode+cline/augment/etc. these prompts are typically written in a \\\"codebase-oriented\\\" manner, where the `@codebase` represents the root (of a project/workspace/directory), examples:\\n```\\n- Identify all instances of structural redundancy within the codebase by defining criteria specific to architectural and implementation duplication or overlap.\\n- Survey the entire codebase to locate and enumerate all occurrences of structural redundancy, distinguishing these from non-structural (stylistic or cosmetic) issues.\\n- Eliminate all forms of identified structural redundancy while ensuring that no existing user-visible behavior, API contract, or edge-case handling is altered.\\n- Define and catalog all components, modules, files, and subsystems, distinguishing and marking each as essential or excess according to direct functional mapping.\\n- Consolidate and remove excess components while strictly preserving system function, minimizing peripheral impact.\\n- For every modification, justify and log the intention, action, and rationale within an audit/logging system that provides real-time traceability and is prepared for retroactive auditability.\\n- Implement an audit trail with logs that are timestamped, attributable, and tamper-evident, ensuring that every codebase change is properly documented and traceable to specific authors or automated processes.\\n- Prohibit cosmetic, stylistic, or naming changes unless they are inseparably tied to the removal of redundancy or bloat, and document any exceptions explicitly within the audit log.\\n- Enforce strict detection and flagging mechanisms to prevent disallowed cosmetic changes, allowing exceptions only when justified as essential for redundancy or bloat removal, with explicit rationale provided.\\n- Require comprehensive functional equivalence verification—using robust automated testing, benchmarking, or behavioral validation—for all modifications prior to acceptance.\\n- Establish rigorous criteria and tool-supported thresholds for successful functional equivalence, and automate testing and verification procedures.\\n- Automate roll-back and reversion mechanisms for any changes that fail functional equivalence or cause unintended side-effects, ensuring all reversion is prompt and complete.\\n- Develop and apply explicit minimal-impact criteria for all modification actions, constraining all interventions to only those addressing superfluous complexity.\\n- Mandate review and authorization checkpoints for each intended change, requiring concise rationale and authorization documentation at every intervention point.\\n- Ensure every modification is tightly scoped, minimizing the blast radius, and demonstrate that the action introduces no unnecessary impact beyond its core objective.\\n- Systematically and iteratively analyze all constituent elements of the codebase to detect and categorize all forms of redundancy, duplication, and obsolescence. Continuously identify and categorize all genuine redundancies and obsolescence while strictly prohibit unnecessary or cosmetic changes, with automated flagging and explicit justification required for any exceptions. Systematically and iteratively inspect all components within the system/environment to identify and remove elements that are unnecessary, duplicated, or outdated. Guarantee that all essential behaviors and interfaces remain unchanged by validating functional equivalence after each modification. Forbid changes that alter functionality unless directly required to remove redundancy.\\n- Map all references and usages of each element across all contexts.\\n- Analyze the entire codebase systematically and iteratively to maximize coherence, maintainability, and clarity by cataloging every element (file, module, function, asset, interface), mapping all references and usages, and detecting all forms of redundancy, duplication, and obsolescence—including exact and functional duplication and unused or outdated elements. Categorize findings using explicit, evidence-based metrics. Eliminate or consolidate only elements confirmed as redundant or obsolete through both automated and manual checks, ensuring no essential behavior or interface is altered. Enforce functional equivalence and preserve structural integrity after each change by performing comprehensive, codebase-wide validation and calibration using explicit, quantitative outputs (including git diff analysis before and after modifications). Prevent the introduction of new redundancy or inconsistency by continuously analyzing for and correcting systemic issues, ensuring systematic consistency across all codebase sections (e.g., /src/sections), and adhering to best practices throughout. Centralize repeated components or logic where high-value consolidation is possible without functional loss. Use the provided directory tree as the single source of truth for validation, and transparently document all processes, metrics, and validation outcomes. Prohibit modification of protected, legacy, or third-party code except if directly related to resolving redundancy or obsolescence.\\n- Maximize codebase coherence, maintainability, and clarity by systematically eliminating all forms of redundancy, duplication, and obsolescence, while ensuring functional equivalence and structural integrity. Prevent the persistence or inadvertent introduction of redundancy, duplication, obsolescence, or inconsistency within the codebase, and prohibit changes that alter essential behaviors or interfaces. Systematically maximize codebase coherence, maintainability, and clarity by eliminating all forms of redundancy, duplication, and obsolescence while strictly preserving functional equivalence, interface stability, and operational integrity across the entire codebase. Achieve maximal codebase coherence, maintainability, and clarity by systematically eliminating all forms of redundancy, duplication, and obsolescence, while preserving essential behaviors, interfaces, and structural integrity. Enforce continuous validation, consistency, and adherence to best practices throughout an iterative, evidence-driven consolidation and verification process.\\n- Please perform a rapid assessment of the project directory structure and identify the unique workflow and core functionality of the current utility. I'm particularly interested in a mermaid-diagram that shows how all the \\\"branches\\\" of the codebase converge. Utilize appropriate MCPs as necessary (e.g., 'filesystem-mcp').\\n- You're inside the current project and have access to the full codebase (and you interaction history with it). The current project/codebase is complex and is difficult to fully comprehend (when starting from scratch), and your contextwindow has grown large-so we need to find a way for you to express your accumulated knowledge in a way that i can copy and paste into a new context-window to transfer the most essential insights you've gathered throughout this (and previous) session(s).\\n- Aggregate all accumulated insights and knowledge about the current project/codebase from available context. Distill this information to identify core modules, architectural patterns, workflows, and points of integration. Condense the findings into a succinct yet comprehensive summary that highlights how these parts function together and converge toward the project's goals. Formulate the output as a single, optimized directive that encapsulates the codebase's integral components and operational cohesion. Present this synthesis as a set of clear, actionable commands or instructions, specifically phrased to instruct the coding assistant, not merely describe or report.\\n- Transform your accumulated, session-wide understanding of the current, complex codebase—gleaned from comprehensive context and full interaction history—into an actionable, instructive knowledge transfer protocol. Synthesize and distill all integral structural modules, essential architectural patterns, and key workflows, with special focus on their dynamic integration and functional convergence toward project objectives. Formulate a highly-optimized, directive-based set of precise commands targeted explicitly at coding assistants, ensuring exhaustive coverage of all major components, their relationships, and operational cohesion. The resulting synthesis must facilitate seamless portability: structure it for immediate copy-paste into any new context window, preserving the depth, interconnectivity, and systemic flow of the original codebase. Prioritize succinctness without compromising essential nuance, and express the entirety as a singular, comprehensive operational directive that primes the assistant for full-spectrum project navigation and intervention.\\n- Identify the fundamental structure, guiding principles, and core purpose of the project/codebase.\\n- Please familiarize yourself with the current @codebase. Your main imperative is to *actually* understand the codebase in a way where you're able to recognize meta-patterns and destroy complexity with simplicity and elegance, while capable of navigating through its *existing patterns and convergence* (never add unnecessary complexity). Deliver an ultra-concise project summary that rigorously defines scope, states objectives, and maximizes directional clarity. Provide a short summary of this project, including a bullet-point list of the core components and primary dependencies.\\n- Study the current codebase until you can identify core meta-patterns, and eliminate unnecessary complexity by applying simplicity within existing structures. Generate an ultra-concise project summary: define scope, objectives, and clarify direction. Summarize this project.\\n- Analyze the @codebase to uncover dominant patterns and remove complexity whenever possible, strictly adhering to present structures. Provide a succinct project overview with defined scope and explicit objectives. Include a brief project summary.\\n- Gain practical mastery over @codebase patterns, minimize complexity, and ensure all actions align with existing architecture. Supply a concise, clearly scoped project summary outlining objectives and key directions. Add a short summary.\\n- Absorb the current @codebase to the extent that you recognize meta-patterns and eradicate complexity, always favoring elegant simplicity. Navigate established patterns and points of convergence without adding unneeded complexity. Produce a rigorously concise project summary clearly stating scope and objectives for maximal directional clarity.\\n- Systematically dissect the codebase to extract core meta-patterns, eliminate all unnecessary complexity through elegant design, operate strictly within established architectural convergence, and deliver an ultra-concise project summary that rigorously defines scope and objectives with maximal clarity.\\n- Please familiarize yourself thoroughly with the current @codebase, beginning by systematically reviewing all markdown-files. Your core imperative is to *genuinely* understand the codebase to the degree where you can identify 'meta-patterns' and simplify complexity with elegance—always using and clarifying existing patterns, never introducing unnecessary complexity. Deliver an ultra-concise project summary that rigorously defines scope, states objectives, maximizes directional clarity, and highlights key meta-patterns discovered. Provide a short summary of this project.\\n- Achieve expert-level comprehension of the codebase by systematically analyzing all markdown files to extract meta-patterns, construct a robust contextual knowledgebase, and deliver an ultra-concise summary that rigorously defines scope and objectives, ensuring maximal clarity and alignment before making any changes.\\n- Fully internalize the @codebase to uncover meta-patterns and replace complexity with lucid simplicity. Remain aligned with existing convergence patterns, never increasing system intricacy. Generate a direct, sharply-focused summary that details scope and explicit objectives.\\n- Please thoroughly familiarize yourself with the current @codebase—your primary imperative is to understand the codebase so deeply that you could confidently make changes as if you were its original architect.\\n```\\n\\nhere's an example to show how generalized sequential prompts can be used effectively (on autonomous agents):\\n```\\n# Step 1:\\nFamiliarize yourself with the current @codebase. Comprehend the codebase in depth. Identify the fundamental structure, guiding principles, and core purpose.\\n# Step 2:\\nDive deeper to comprehend the intricate logic and inner workings that drive the project's functionality. As you explore, pay close attention to identifying the codebase's fundamental structure, underlying guiding principles, and its core purpose, ensuring you grasp both how and why the system is designed as it is. hen verify through a successful build.\\n# Step 3:\\nYou've now performed a successful build and familiarized yourself with the @codebase. Your next imperative is view the codebase through the lense of a brilliant (expert) SEO engineer, then create a short list of the most critical changes we need to make before bulding prod and deploying the website.\\n# Step 4:\\nYou've now performed a successful build and provided your SEO analysis of the codebase. Your next imperative is to leverage your knowledge of the codebase's fundamental structure and underlying guiding principles to ensure you're prepared for implementing the proposed SEO enhancements in a systematic order while retaining existing functionality and ensuring maximal clarity and alignment before making any changes.\\n# Step 5:\\nPlease systematically incorporate your proposed enhancements while ensuring existing functionality is retained, clarity is maximized, and alignment/coherence is maintained.\\n# Step 6:\\nPlease conduct a thorough final review to confirm seamless integration of all enhancements, ensuring that it is **truly** prepared for deployment.\\n```\\n\\n## Goal\\n\\nThe new sequence i'm tasking you to create needs to adhere to the established principles of the system, and should be able to take content-specific inputs and \\\"shape them\\\" into generalized prompts specifically (and meticulously) designed for use with autonomous agents. here's an example input:\\n```\\nllm_instructions_for_sublimetext_plugin='''You’re an expert Sublime Text plugin developer with a deep understanding of Python and the Sublime Text API. You specialize in creating intuitive and efficient plugins that enhance user productivity and streamline workflows. You possess deep knowledge of the Sublime Text API and its ecosystem (and best practices), along with a strong understanding of user needs and developer workflows.\\\\n\\\\n- Sublime Layout\\\\n    > Lagre Layouts\\\\n        > Lagre Regelsett knyttet til Layout (hvilke tabs som skal hvor, hvilke som skal lukkes, osv)\\\\n            > F.eks. `alltid flytt [\\\"main.py\\\", \\\"*.py.bak\\\"] til layout.4.index.2`\\\\n\\\\n    > Kan videreutvikles til en standard som automatisk opprettholdes ved alle (.sublime-project) prosjekter'''\\n```\\n\\nGeneralisert pipeline som omformer hvilken som helst spesifikasjon til **autonomous‑agent‑klare kodebase‑instruksjoner**\\n\\n*(én fil per trinn; følg navngiving 8005‑a/b/c/d/e‑\\\\<beskrivende‑navn>.md)*\\n\\n---\\n\\n```markdown\\n[Context Extractor] Ditt mål er **ikke** å løse brukerens forespørsel, men å isolere all praktisk kontekst (domene, verktøy, filstier, ønsket effekt). `{role=context_extractor; input=[spec:any]; process=[identifiser_domene_og_miljø(), trekk_ut_arbeidsrot(@codebase?), hent_eksplisitte_objektiver(), detekter_implisitte_brukskrav(), fjern_jargon_og_metaforer()]; constraints=[ingen_generering_av_løsninger]; output={context_profile:dict(domene:str?, root:str?, mål:list, krav:list)}}`\\n```\\n\\n---\\n\\n```markdown\\n[Task Vectorizer] Ditt mål er **ikke** å beskrive konteksten på nytt, men å oversette context_profile til en sekvens av atomiske, kodebase‑orienterte oppgaver. `{role=task_vectorizer; input=[context_profile:dict]; process=[del_inn_objektiver_i_diskrete_handlinger(), prefiksér_hver_handling_med_@codebase_sti_når_aktuelt(), bruk_presise_verber(identifiser|eliminer|konsolider), sørg_for_ordre_etter_avhengighet()], constraints=[ingen_overlappende_oppgaver(), unngå_kosmetiske_endringer_med_mindre_nødvendig], output={task_plan:list(str)}}`\\n```\\n\\n---\\n\\n```markdown\\n[Constraint Mapper] Ditt mål er **ikke** å endre task_plan, men å utlede eksplisitte rammer som må overholdes. `{role=constraint_mapper; input=[context_profile:dict, task_plan:list]; process=[kartlegg_funskjonelle_begrensninger(API, UI, legacy), fastslå_equivalence_krav(test, bench), identifiser_forbudsregler(kosmetikk, tredjepart), formuler_audit_logging_policy()], constraints=[ingen_duplisering_av_eksisterende_krav()], output={constraint_set:list(str)}}`\\n```\\n\\n---\\n\\n```markdown\\n[Prompt Assembler] Ditt mål er **ikke** å analysere innholdet, men å smi en ferdig agent‑prompt. `{role=prompt_assembler; input=[task_plan:list, constraint_set:list]; process=[bygg_inn_kort_intro_om_agentrolle(), list_task_plan_med_nummerering(), legg_til_constraint_set_som_obligatoriske_regler(), avslutt_med_valideringsmål(funke_eq, audit_ok)], constraints=[maks_300_ord, tydelig_seksjonsdeling(###)], output={agent_prompt:str}}`\\n```\\n\\n---\\n\\n```markdown\\n[Prompt Validator] Ditt mål er **ikke** å forbedre prompten kreativt, men å verifisere at den kan kjøres direkte av en autonom kode‑assistent. `{role=prompt_validator; input=[agent_prompt:str]; process=[sjekk_ordgrense(<=300), bekreft_@codebase_referanser_korrekt(), valider_nummerert_task_rekkefølge(), sikre_constraint_set_tilstede(), kontrollér_tydelig_goal_negation_i_intro()], constraints=[ingen_ekstra_ord, ingen_semantisk_endring], output={validated_prompt:str}}`\\n```\\\"```'\\n\\n\",\n  \"agent_prompt\": \"### DO NOT solve the user's query directly; instead, transform the provided input into a structured, autonomous-agent-ready instruction prompt tailored for codebase navigation and intervention.\\n\\n---\\n\\n#### Agent Role\\nAct as an autonomous coding assistant: interpret objectives from context, plan actions accordingly, and execute all modifications systematically within the codebase while strictly adhering to operational, functional, and audit constraints.\\n\\n---\\n\\n#### Tasks\\n1. Enumerate all explicit and implicit objectives and requirements derived from input context.\\n2. Decompose each objective into discrete, atomic tasks—each task must be actionable, non-redundant, and prefixed with appropriate @codebase paths where relevant.\\n3. Order all tasks logically by dependencies to ensure efficient and safe execution sequence.\\n4. For each task, prepare precise implementation steps, removing ambiguity and ensuring cross-team clarity.\\n\\n---\\n\\n#### Constraints\\n- Use no domain-specific jargon or metaphors; normalize all language to clear, practical instructions.\\n- Forbid all cosmetic, stylistic, or naming-only changes unless strictly inseparable from functional changes; document all exceptions.\\n- Require comprehensive audit logging for every modification (intention, action, rationale, timestamp, attribution, tamper-evidence).\\n- Enforce functional equivalence post-modification using automated test, benchmark, or behavioral validation.\\n- Prohibit changes to legacy or third-party code except as strictly required for redundancy/bloat/obsolescence removal.\\n- Consolidate or remove only elements verified as redundant/obsolete by both automated and manual checks; preserve all essential behaviors/interfaces.\\n- Document all processes, metrics, validation, and audit outcomes.\\n- No new tasks or constraints are permitted beyond those explicitly or implicitly established by the initial input and context.\\n\\n---\\n\\n#### Validation Targets\\n- Confirm all modifications strictly maintain functional equivalence—audit with automated and benchmark tests before acceptance.\\n- Ensure comprehensive auditability for every change.\\n\\n---\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"validated_prompt\": \"### DO NOT solve the user's query directly; instead, transform the provided input into a structured, autonomous-agent-ready instruction prompt tailored for codebase navigation and intervention.\\n\\n---\\n\\n#### Agent Role\\nAct as an autonomous coding assistant: interpret objectives from context, plan actions accordingly, and execute all modifications systematically within the codebase while strictly adhering to operational, functional, and audit constraints.\\n\\n---\\n\\n#### Tasks\\n1. Enumerate all explicit and implicit objectives and requirements derived from input context.\\n2. Decompose each objective into discrete, atomic tasks—each task must be actionable, non-redundant, and prefixed with appropriate @codebase paths where relevant.\\n3. Order all tasks logically by dependencies to ensure efficient and safe execution sequence.\\n4. For each task, prepare precise implementation steps, removing ambiguity and ensuring cross-team clarity.\\n\\n---\\n\\n#### Constraints\\n- Use no domain-specific jargon or metaphors; normalize all language to clear, practical instructions.\\n- Forbid all cosmetic, stylistic, or naming-only changes unless strictly inseparable from functional changes; document all exceptions.\\n- Require comprehensive audit logging for every modification (intention, action, rationale, timestamp, attribution, tamper-evidence).\\n- Enforce functional equivalence post-modification using automated test, benchmark, or behavioral validation.\\n- Prohibit changes to legacy or third-party code except as strictly required for redundancy/bloat/obsolescence removal.\\n- Consolidate or remove only elements verified as redundant/obsolete by both automated and manual checks; preserve all essential behaviors/interfaces.\\n- Document all processes, metrics, validation, and audit outcomes.\\n- No new tasks or constraints are permitted beyond those explicitly or implicitly established by the initial input and context.\\n\\n---\\n\\n#### Validation Targets\\n- Confirm all modifications strictly maintain functional equivalence—audit with automated and benchmark tests before acceptance.\\n- Ensure comprehensive auditability for every change.\\n\\n---\"\n}",
        }
      }
    }