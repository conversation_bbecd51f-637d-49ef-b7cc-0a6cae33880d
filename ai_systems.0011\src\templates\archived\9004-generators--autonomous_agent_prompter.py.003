#!/usr/bin/env python3

import sys
from pathlib import Path

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))
from processor import BaseGenerator

TEMPLATES = {
    # 9004:
    "9004-a-context_extractor": {
        "title": "Context Extractor",
        "interpretation": "Your goal is **not** to propose solutions, but to **isolate** actionable context from the specification. Execute as:",
        "transformation": "`{role=context_extractor; input=[raw_spec:str]; process=[strip_jargon(), strip_metaphor(), detect_domain(), locate_project_root(\"@codebase\"), list_explicit_objectives(), surface_implicit_requirements()], constraints=[no_paraphrase(), no_solution_generation()], output={context_profile:{domain:str, project_root:str, goals:list, requirements:list}}}`",
        "context": {
            "description": "Dissects a raw specification to extract every piece of operational context an autonomous agent will need.",
            "input_focus": "Unedited specification text supplied by the user.",
            "output_focus": "A typed context_profile dict containing domain, project root, explicit objectives, and implicit requirements.",
            "key_operations": [
                "Removing metaphor and non‑operational language.",
                "Detecting the working domain or tech stack.",
                "Locating the root marker (`@codebase`) for path scoping.",
                "Listing every stated objective verbatim.",
                "Surfacing hidden assumptions and requirements."
            ],
            "constraints_context": [
                "May not paraphrase or interpret meaning beyond direct extraction.",
                "Absolutely forbidden from proposing solutions or tasks."
            ],
            "relevance": "Defines scope and boundaries for all downstream templates, guaranteeing that later steps operate on clean, actionable data."
        }
    },
    "9004-b-task_vectorizer": {
        "title": "Task Vectorizer",
        "interpretation": "Your goal is **not** to restate context, but to **decompose** it into atomic, codebase-oriented tasks. Execute as:",
        "transformation": "`{role=task_vectorizer; input=[context_profile:dict]; process=[derive_task_candidates(), prefix_paths(\"@codebase\"), order_by_dependency(), enforce_action_verbs(), remove_overlap(), ban_cosmetic_changes()], constraints=[tasks_unique(), verbs_only()], output={ordered_tasks:[str]}}`",
        "context": {
            "description": "Transforms the context profile into a dependency‑ordered list of granular tasks fit for automated execution.",
            "input_focus": "The context_profile produced by the Context Extractor.",
            "output_focus": "ordered_tasks – a list of unique, verb‑driven commands each scoped to the codebase.",
            "key_operations": [
                "Splitting broad goals into single‑action commands.",
                "Adding `@codebase` prefixes so agents act in the correct directory.",
                "Sequencing tasks by logical dependency.",
                "Eliminating redundancy and cosmetic‑only instructions."
            ],
            "constraints_context": [
                "Every task must begin with a strong action verb (identify, refactor, log, etc.).",
                "Tasks must be non‑overlapping and directly tied to functional goals."
            ],
            "relevance": "Provides the autonomous agent with a clear, step‑by‑step execution blueprint free of ambiguity."
        }
    },
    "9004-c-constraint_mapper": {
        "title": "Constraint Mapper",
        "interpretation": "Your goal is **not** to edit tasks, but to **extract** all governing limits. Execute as:",
        "transformation": "`{role=constraint_mapper; input=[context_profile:dict, ordered_tasks:list]; process=[collect_functional_constraints(), collect_behavioral_constraints(), collect_policy_constraints(), deduplicate_constraints()], constraints=[no_new_tasks(), no_redundant_entries()], output={constraint_set:[str]}}`",
        "context": {
            "description": "Consolidates every rule, boundary, and policy the agent must respect during execution.",
            "input_focus": "context_profile and ordered_tasks.",
            "output_focus": "constraint_set – a deduplicated list of textual constraints.",
            "key_operations": [
                "Harvesting functional‑equivalence mandates.",
                "Capturing API and interface preservation rules.",
                "Recording audit/logging, rollback, and policy obligations.",
                "Removing duplicate or conflicting constraints."
            ],
            "constraints_context": [
                "Must not create or modify tasks; only list constraints.",
                "Constraint entries must be unique and actionable."
            ],
            "relevance": "Prevents unintended side‑effects by explicitly defining the operational guard‑rails for the agent."
        }
    },
    "9004-d-prompt_assembler": {
        "title": "Prompt Assembler",
        "interpretation": "Your goal is **not** to alter semantics, but to **compile** an agent-ready prompt. Execute as:",
        "transformation": "`{role=prompt_assembler; input=[ordered_tasks:list, constraint_set:list, context_profile:dict]; process=[insert_goal_negation_header(), embed_agent_role(), enumerate_tasks(), append_constraints(), add_validation_targets(functional_equivalence=True,audit=True)], constraints=[≤300_tokens, english_only()], output={agent_prompt:str}}`",
        "context": {
            "description": "Integrates tasks and constraints into a single, executable prompt string for autonomous coding agents.",
            "input_focus": "ordered_tasks, constraint_set, and context_profile.",
            "output_focus": "agent_prompt – complete instructions with sections for role, tasks, constraints, and validation goals.",
            "key_operations": [
                "Starting with a goal‑negation header to lock scope.",
                "Embedding a precise agent role declaration.",
                "Listing tasks numerically for deterministic execution order.",
                "Appending constraints as a bullet list.",
                "Stating final validation targets (functional equivalence, audit pass)."
            ],
            "constraints_context": [
                "Entire prompt must be ≤ 300 tokens and written in English.",
                "No re‑phrasing of task semantics or constraint meaning."
            ],
            "relevance": "Delivers a turnkey command set that an autonomous agent can run without additional human clarification."
        }
    },
    "9004-e-prompt_validator": {
        "title": "Prompt Validator",
        "interpretation": "Your goal is **not** to rewrite the prompt, but to **verify** strict compliance for execution. Execute as:",
        "transformation": "`{role=prompt_validator; input=[agent_prompt:str]; process=[check_goal_negation_header(), check_section_labels(), check_length(≤300_tokens), validate_path_prefixes(\"@codebase\"), confirm_task_numbering(), confirm_constraint_block()], constraints=[no_semantic_change()], output={validated_prompt:str}}`",
        "context": {
            "description": "Acts as the final quality gate, ensuring the assembled prompt adheres to every structural and content rule.",
            "input_focus": "agent_prompt string from the Prompt Assembler.",
            "output_focus": "validated_prompt – the same string, confirmed compliant or flagged for correction.",
            "key_operations": [
                "Verifying presence and correctness of the goal‑negation header.",
                "Ensuring all mandatory section labels exist in order.",
                "Checking overall token length is within limits.",
                "Validating correct use of `@codebase` path prefixes.",
                "Ensuring tasks are numbered sequentially and constraints are present."
            ],
            "constraints_context": [
                "May not alter wording or meaning; only validate structural compliance."
            ],
            "relevance": "Guarantees that the autonomous agent receives a well‑formed, execution‑ready prompt, reducing runtime errors and mis‑scoping."
        }
    }
}

def main():
    """Main execution function."""
    generator = BaseGenerator(
        output_dir=Path(__file__).parent.parent / "generated"
    )
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
