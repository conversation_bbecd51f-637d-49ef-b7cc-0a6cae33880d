  {
  "initial_prompt": "this project contains only a single image, it's a reference. the goal is to create a utility that is able to categorize pictures based on hair-color. for this to work i need a way to consitently be able to detect hair. please initialize with python+uv, then propose how to proceed in the simplest way possible.",
  "sequence_id": "1000",
  "results": [
      {
      "instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`",
      "step": "a",
      "title": "Instruction Converter",
      "input": "this project contains only a single image, it's a reference. the goal is to create a utility that is able to categorize pictures based on hair-color. for this to work i need a way to consitently be able to detect hair. please initialize with python+uv, then propose how to proceed in the simplest way possible.",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"instruction_format\": \"Initialize the project using Python and the 'uv' environment. Create a utility capable of categorizing images based on hair color. Implement a consistent hair detection method. Start the setup and propose a straightforward procedure for hair detection and categorization.\"\n}",
        }
      }
    }