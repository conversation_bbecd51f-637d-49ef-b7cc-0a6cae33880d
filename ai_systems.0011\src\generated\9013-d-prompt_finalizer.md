[Prompt Finalizer] Collapse all components into the final model-ready string; validate syntax for token weights, :: arithmetic, region tags, --stylize/--chaos, and negative channel. `{role=prompt_finalizer; input=[technical_prompt:str, negative_prompt:str]; process=[structure_for_ai_parsing(), balance_detail_levels(), ensure_clarity(), validate_generation_readiness(), /* loop */ loop_until_quality≥threshold(fid|lpips)]; constraints=[no_additional_concepts(), optimisation_only(), keep_region_and_weight_syntax_intact()]; requirements=[ai_friendly_format(), balanced_specificity(), max_length≤450_tokens]; output={final_image_prompt:str}}`

Context: [
  {
    "explicit_asks": [
      "Merge positive, regional and negative strings into single model-ready line.",
      "Validate token-weight, `::` arithmetic and region syntax.",
      "Run iterative quality loop until FID≤15 or LPIPS≤0.25."
    ],
    "hidden_assumptions": [
      "Metrics endpoint (or surrogate CLIP score) is callable for loop decisions.",
      "Max token budget ~450 for common SD/MJ endpoints."
    ],
    "sub_goals": [
      "Truncate verbose adjectives; keep essential descriptors.",
      "Return final prompt + optional seed, steps, sampler."
    ],
    "blockers": [
      "Quality metrics unavailable on some SaaS models.",
      "Prompt length overrun triggers API truncation."
    ]
  }
]