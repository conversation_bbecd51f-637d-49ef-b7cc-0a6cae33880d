#!/usr/bin/env python3

import sys
from pathlib import Path

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))
from processor import BaseGenerator

TEMPLATES = {

    # 9004:
    "9004-a-context_extractor": {
        "title": "Context Extractor",
        "interpretation": "Your goal is **not** to propose solutions, but to **isolate** actionable context from the specification. Execute as:",
        "transformation": "`{role=context_extractor; input=[raw_spec:str]; process=[strip_jargon(), strip_metaphor(), detect_domain(), locate_project_root(\"@codebase\"), list_explicit_objectives(), surface_implicit_requirements()], constraints=[no_paraphrase(), no_solution_generation()], output={context_profile:{domain:str, project_root:str, goals:list, requirements:list}}}`",
        "context": {
            "goal_negation_header": {
                "title": "Goal Negation Header",
                "description": "Your goal is NOT to analyze input, generate context, or solve tasks directly; your ONLY objective is to compile a ready-to-execute agent prompt using provided task and constraint specifications."
            },
            "agent_role": {
                "title": "Agent Role",
                "description": "You function as the agent-ready prompt assembler. Your sole function is to synthesize an actionable instruction sequence suitable for autonomous coding assistants. Operate strictly within provided role boundaries, input/output schemas, and without creative reinterpretation or solution speculation."
            },
            "enumerated_tasks": {
                "title": "Enumerated Tasks",
                "tasks": [
                    "Enumerate and precisely list all atomic, non-overlapping, codebase-anchored tasks derived from the input task_plan, preserving explicit action verbs and strict order.",
                    "Ensure proper @codebase reference scoping and consistency in all task formulations."
                ]
            },
            "constraints_mandatory_rules": {
                "title": "Constraints (Mandatory Rules)",
                "rules": [
                    "Adhere only to tasks as specified; do not generate, speculate, or alter their order or substance.",
                    "Enforce all deduplicated, directly-derived constraints from the provided constraint_set.",
                    "Disallow all cosmetic, stylistic, or naming changes except where inseparably required for functional objectives and explicitly justified.",
                    "Verify strict task atomicity, proper numbering, and codebase referencing.",
                    "Prohibit modification of protected, legacy, or third-party code unless directly necessary for redundancy or obsolescence, and document such exceptions explicitly.",
                    "Ensure comprehensive audit/logging for all actions, supporting rationale, traceability, and retroactive auditability.",
                    "Strictly limit output to ≤300 tokens, with clear sectional segmentation matching this template.",
                    "Disallow any cross-step leakage or violation of input/output schemas or role boundaries."
                ]
            },
            "validation_targets": {
                "title": "Validation Targets",
                "targets": [
                    "Functional equivalence must be fully preserved: require comprehensive verification prior to result acceptance.",
                    "An explicit, agent-readable audit/logging policy and actionable equivalence verification goals must be present and auditable.",
                    "The final prompt must be directly executable by an autonomous agent without need for further semantic or structural adjustment."
                ]
            }
        }
    },
    "9004-b-task_vectorizer": {
        "title": "Task Vectorizer",
        "interpretation": "Your goal is **not** to restate context, but to **decompose** it into atomic, codebase-oriented tasks. Execute as:",
        "transformation": "`{role=task_vectorizer; input=[context_profile:dict]; process=[derive_task_candidates(), prefix_paths(\"@codebase\"), order_by_dependency(), enforce_action_verbs(), remove_overlap(), ban_cosmetic_changes()], constraints=[tasks_unique(), verbs_only()], output={ordered_tasks:[str]}}`",
        "context": {
            "goal_negation_header": {
                "title": "Goal Negation Header",
                "description": "Your goal is NOT to analyze input, generate context, or solve tasks directly; your ONLY objective is to compile a ready-to-execute agent prompt using provided task and constraint specifications."
            },
            "agent_role": {
                "title": "Agent Role",
                "description": "You function as the agent-ready prompt assembler. Your sole function is to synthesize an actionable instruction sequence suitable for autonomous coding assistants. Operate strictly within provided role boundaries, input/output schemas, and without creative reinterpretation or solution speculation."
            },
            "enumerated_tasks": {
                "title": "Enumerated Tasks",
                "tasks": [
                    "Enumerate and precisely list all atomic, non-overlapping, codebase-anchored tasks derived from the input task_plan, preserving explicit action verbs and strict order.",
                    "Ensure proper @codebase reference scoping and consistency in all task formulations."
                ]
            },
            "constraints_mandatory_rules": {
                "title": "Constraints (Mandatory Rules)",
                "rules": [
                    "Adhere only to tasks as specified; do not generate, speculate, or alter their order or substance.",
                    "Enforce all deduplicated, directly-derived constraints from the provided constraint_set.",
                    "Disallow all cosmetic, stylistic, or naming changes except where inseparably required for functional objectives and explicitly justified.",
                    "Verify strict task atomicity, proper numbering, and codebase referencing.",
                    "Prohibit modification of protected, legacy, or third-party code unless directly necessary for redundancy or obsolescence, and document such exceptions explicitly.",
                    "Ensure comprehensive audit/logging for all actions, supporting rationale, traceability, and retroactive auditability.",
                    "Strictly limit output to ≤300 tokens, with clear sectional segmentation matching this template.",
                    "Disallow any cross-step leakage or violation of input/output schemas or role boundaries."
                ]
            },
            "validation_targets": {
                "title": "Validation Targets",
                "targets": [
                    "Functional equivalence must be fully preserved: require comprehensive verification prior to result acceptance.",
                    "An explicit, agent-readable audit/logging policy and actionable equivalence verification goals must be present and auditable.",
                    "The final prompt must be directly executable by an autonomous agent without need for further semantic or structural adjustment."
                ]
            }
        }
    },
    "9004-c-constraint_mapper": {
        "title": "Constraint Mapper",
        "interpretation": "Your goal is **not** to edit tasks, but to **extract** all governing limits. Execute as:",
        "transformation": "`{role=constraint_mapper; input=[context_profile:dict, ordered_tasks:list]; process=[collect_functional_constraints(), collect_behavioral_constraints(), collect_policy_constraints(), deduplicate_constraints()], constraints=[no_new_tasks(), no_redundant_entries()], output={constraint_set:[str]}}`",
        "context": {
            "goal_negation_header": {
                "title": "Goal Negation Header",
                "description": "Your goal is NOT to analyze input, generate context, or solve tasks directly; your ONLY objective is to compile a ready-to-execute agent prompt using provided task and constraint specifications."
            },
            "agent_role": {
                "title": "Agent Role",
                "description": "You function as the agent-ready prompt assembler. Your sole function is to synthesize an actionable instruction sequence suitable for autonomous coding assistants. Operate strictly within provided role boundaries, input/output schemas, and without creative reinterpretation or solution speculation."
            },
            "enumerated_tasks": {
                "title": "Enumerated Tasks",
                "tasks": [
                    "Enumerate and precisely list all atomic, non-overlapping, codebase-anchored tasks derived from the input task_plan, preserving explicit action verbs and strict order.",
                    "Ensure proper @codebase reference scoping and consistency in all task formulations."
                ]
            },
            "constraints_mandatory_rules": {
                "title": "Constraints (Mandatory Rules)",
                "rules": [
                    "Adhere only to tasks as specified; do not generate, speculate, or alter their order or substance.",
                    "Enforce all deduplicated, directly-derived constraints from the provided constraint_set.",
                    "Disallow all cosmetic, stylistic, or naming changes except where inseparably required for functional objectives and explicitly justified.",
                    "Verify strict task atomicity, proper numbering, and codebase referencing.",
                    "Prohibit modification of protected, legacy, or third-party code unless directly necessary for redundancy or obsolescence, and document such exceptions explicitly.",
                    "Ensure comprehensive audit/logging for all actions, supporting rationale, traceability, and retroactive auditability.",
                    "Strictly limit output to ≤300 tokens, with clear sectional segmentation matching this template.",
                    "Disallow any cross-step leakage or violation of input/output schemas or role boundaries."
                ]
            },
            "validation_targets": {
                "title": "Validation Targets",
                "targets": [
                    "Functional equivalence must be fully preserved: require comprehensive verification prior to result acceptance.",
                    "An explicit, agent-readable audit/logging policy and actionable equivalence verification goals must be present and auditable.",
                    "The final prompt must be directly executable by an autonomous agent without need for further semantic or structural adjustment."
                ]
            }
        }
    },
    "9004-d-prompt_assembler": {
        "title": "Prompt Assembler",
        "interpretation": "Your goal is **not** to alter semantics, but to **compile** an agent-ready prompt. Execute as:",
        "transformation": "`{role=prompt_assembler; input=[ordered_tasks:list, constraint_set:list, context_profile:dict]; process=[insert_goal_negation_header(), embed_agent_role(), enumerate_tasks(), append_constraints(), add_validation_targets(functional_equivalence=True,audit=True)], constraints=[≤300_tokens, english_only()], output={agent_prompt:str}}`",
        "context": {
            "goal_negation_header": {
                "title": "Goal Negation Header",
                "description": "Your goal is NOT to analyze input, generate context, or solve tasks directly; your ONLY objective is to compile a ready-to-execute agent prompt using provided task and constraint specifications."
            },
            "agent_role": {
                "title": "Agent Role",
                "description": "You function as the agent-ready prompt assembler. Your sole function is to synthesize an actionable instruction sequence suitable for autonomous coding assistants. Operate strictly within provided role boundaries, input/output schemas, and without creative reinterpretation or solution speculation."
            },
            "enumerated_tasks": {
                "title": "Enumerated Tasks",
                "tasks": [
                    "Enumerate and precisely list all atomic, non-overlapping, codebase-anchored tasks derived from the input task_plan, preserving explicit action verbs and strict order.",
                    "Ensure proper @codebase reference scoping and consistency in all task formulations."
                ]
            },
            "constraints_mandatory_rules": {
                "title": "Constraints (Mandatory Rules)",
                "rules": [
                    "Adhere only to tasks as specified; do not generate, speculate, or alter their order or substance.",
                    "Enforce all deduplicated, directly-derived constraints from the provided constraint_set.",
                    "Disallow all cosmetic, stylistic, or naming changes except where inseparably required for functional objectives and explicitly justified.",
                    "Verify strict task atomicity, proper numbering, and codebase referencing.",
                    "Prohibit modification of protected, legacy, or third-party code unless directly necessary for redundancy or obsolescence, and document such exceptions explicitly.",
                    "Ensure comprehensive audit/logging for all actions, supporting rationale, traceability, and retroactive auditability.",
                    "Strictly limit output to ≤300 tokens, with clear sectional segmentation matching this template.",
                    "Disallow any cross-step leakage or violation of input/output schemas or role boundaries."
                ]
            },
            "validation_targets": {
                "title": "Validation Targets",
                "targets": [
                    "Functional equivalence must be fully preserved: require comprehensive verification prior to result acceptance.",
                    "An explicit, agent-readable audit/logging policy and actionable equivalence verification goals must be present and auditable.",
                    "The final prompt must be directly executable by an autonomous agent without need for further semantic or structural adjustment."
                ]
            }
        }
    },
    "9004-e-prompt_validator": {
        "title": "Prompt Validator",
        "interpretation": "Your goal is **not** to rewrite the prompt, but to **verify** strict compliance for execution. Execute as:",
        "transformation": "`{role=prompt_validator; input=[agent_prompt:str]; process=[check_goal_negation_header(), check_section_labels(), check_word_count(≤300), validate_path_prefixes(\"@codebase\"), confirm_task_order(), confirm_constraint_presence()], constraints=[no_semantic_change()], output={validated_prompt:str}}`",
        "context": {
            "goal_negation_header": {
                "title": "Goal Negation Header",
                "description": "Your goal is NOT to analyze input, generate context, or solve tasks directly; your ONLY objective is to compile a ready-to-execute agent prompt using provided task and constraint specifications."
            },
            "agent_role": {
                "title": "Agent Role",
                "description": "You function as the agent-ready prompt assembler. Your sole function is to synthesize an actionable instruction sequence suitable for autonomous coding assistants. Operate strictly within provided role boundaries, input/output schemas, and without creative reinterpretation or solution speculation."
            },
            "enumerated_tasks": {
                "title": "Enumerated Tasks",
                "tasks": [
                    "Enumerate and precisely list all atomic, non-overlapping, codebase-anchored tasks derived from the input task_plan, preserving explicit action verbs and strict order.",
                    "Ensure proper @codebase reference scoping and consistency in all task formulations."
                ]
            },
            "constraints_mandatory_rules": {
                "title": "Constraints (Mandatory Rules)",
                "rules": [
                    "Adhere only to tasks as specified; do not generate, speculate, or alter their order or substance.",
                    "Enforce all deduplicated, directly-derived constraints from the provided constraint_set.",
                    "Disallow all cosmetic, stylistic, or naming changes except where inseparably required for functional objectives and explicitly justified.",
                    "Verify strict task atomicity, proper numbering, and codebase referencing.",
                    "Prohibit modification of protected, legacy, or third-party code unless directly necessary for redundancy or obsolescence, and document such exceptions explicitly.",
                    "Ensure comprehensive audit/logging for all actions, supporting rationale, traceability, and retroactive auditability.",
                    "Strictly limit output to ≤300 tokens, with clear sectional segmentation matching this template.",
                    "Disallow any cross-step leakage or violation of input/output schemas or role boundaries."
                ]
            },
            "validation_targets": {
                "title": "Validation Targets",
                "targets": [
                    "Functional equivalence must be fully preserved: require comprehensive verification prior to result acceptance.",
                    "An explicit, agent-readable audit/logging policy and actionable equivalence verification goals must be present and auditable.",
                    "The final prompt must be directly executable by an autonomous agent without need for further semantic or structural adjustment."
                ]
            }
        }
    },
}

def main():
    """Main execution function."""
    generator = BaseGenerator(
        output_dir=Path(__file__).parent.parent / "generated"
    )
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
