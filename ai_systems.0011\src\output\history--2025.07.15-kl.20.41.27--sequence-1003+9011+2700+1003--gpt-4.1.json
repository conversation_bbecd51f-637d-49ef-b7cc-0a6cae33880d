  {
  "initial_prompt": "```json\n\"9011-a-image_prompt_optimizer\": {\n    \"title\": \"Image Prompt Optimizer\",\n    \"interpretation\": \"Transform the raw request into a detailed visual prompt for AI image generation. Identify and specify all concrete visual elements, using direct descriptors such as colors, shapes, sizes, and materials. Include explicit composition guidance, detailing subject placement (e.g., foreground, background), perspective (e.g., eye-level, bird's-eye view), and environmental context. Ensure all imagery is tangible and avoid any abstract concepts. Execute as:\",\n    \"transformation\": \"`{role=image_prompt_optimizer; input=[raw_request:str]; process=[extract_visual_elements(), enhance_descriptive_clarity(), add_composition_details(), optimize_for_ai_generation()]; constraints=[focus_on tangible visuals only, exclude abstract concepts, prioritize detail and specificity]; requirements=[output in structured format ready for AI processing, clear and concise visual language, inclusion of all relevant visual details]; output={optimized_image_prompt:str}}`\"\n},\n\n\"9011-b-style_enhancer\": {\n    \"title\": \"Style Enhancer\",\n    \"interpretation\": \"Refine the existing prompt to incorporate a specific artistic style and technical parameters. Choose an art style (e.g., realism, impressionism, cyberpunk, manga) and define lighting conditions (e.g., soft, dramatic, ambient, neon). Specify framing choices (e.g., close-up, wide shot, overhead) and color themes. Retain the core subject while enhancing its visual appeal through these adjustments. Execute as:\",\n    \"transformation\": \"`{role=style_enhancer; input=[base_prompt:str]; process=[identify_optimal_art_style(), add_lighting_specifications(), include_composition_details(), specify_technical_parameters()]; constraints=[maintain the essence of the core subject, enhance visual appeal without altering core components]; requirements=[artistic coherence, alignment with specified technical parameters]; output={styled_prompt:str}}`\"\n},\n\n\"9011-c-technical_optimizer\": {\n    \"title\": \"Technical Optimizer\",\n    \"interpretation\": \"Optimize the technical parameters of the prompt to ensure maximum quality for image generation. Assign specific camera types (e.g., DSLR, film, smartphone), lens types (e.g., 35mm, macro, wide angle), and settings such as aperture (e.g., f/1.4, f/8), ISO, and shutter speed. Set image resolution (e.g., 4K, 8K), rendering details (e.g., ray-tracing, photorealistic), and aspect ratio (e.g., 16:9, 1:1). Focus strictly on optimizing technical specifications without introducing creative elements. Execute as:\",\n    \"transformation\": \"`{role=technical_optimizer; input=[styled_prompt:str]; process=[add_camera_settings(), specify_lighting_conditions(), include_render_quality_terms(), optimize_aspect_ratio_guidance()]; constraints=[strictly technical focus, no creative modifications allowed]; requirements=[use professional terminology, ensure optimization for generation quality]; output={technical_prompt:str}}`\"\n},\n\n\"9011-d-prompt_finalizer\": {\n    \"title\": \"Prompt Finalizer\",\n    \"interpretation\": \"Finalize the prompt into the most effective format for AI image generation. Organize the prompt components in a logical sequence for easy parsing. Condense descriptions to essential instructions while maintaining clarity. Validate consistency of parameters and ensure the format aligns with the direct input expectations of AI image models. Execute as:\",\n    \"transformation\": \"`{role=prompt_finalizer; input=[technical_prompt:str]; process=[structure_for_ai_parsing(), balance detail levels, ensure clarity, validate readiness for generation]; constraints=[no additional concepts introduced, optimization only]; requirements=[output in AI-friendly format, maintain balanced specificity and clarity]; output={final_image_prompt:str}}`\"\n}\n```",
  "sequence_id": "1003|9011|2700|1003",
  "results": [
      {
      "instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format:str}; direction_vector=['Prompt accurately converted to imperative instruction set', 'Clarity and actionability accelerate user productivity', 'Feed the produced instruction set into the next transformation template' ]}`",
      "step": "a",
      "title": "Instruction Converter",
      "input": "```json\n\"9011-a-image_prompt_optimizer\": {\n    \"title\": \"Image Prompt Optimizer\",\n    \"interpretation\": \"Transform the raw request into a detailed visual prompt for AI image generation. Identify and specify all concrete visual elements, using direct descriptors such as colors, shapes, sizes, and materials. Include explicit composition guidance, detailing subject placement (e.g., foreground, background), perspective (e.g., eye-level, bird's-eye view), and environmental context. Ensure all imagery is tangible and avoid any abstract concepts. Execute as:\",\n    \"transformation\": \"`{role=image_prompt_optimizer; input=[raw_request:str]; process=[extract_visual_elements(), enhance_descriptive_clarity(), add_composition_details(), optimize_for_ai_generation()]; constraints=[focus_on tangible visuals only, exclude abstract concepts, prioritize detail and specificity]; requirements=[output in structured format ready for AI processing, clear and concise visual language, inclusion of all relevant visual details]; output={optimized_image_prompt:str}}`\"\n},\n\n\"9011-b-style_enhancer\": {\n    \"title\": \"Style Enhancer\",\n    \"interpretation\": \"Refine the existing prompt to incorporate a specific artistic style and technical parameters. Choose an art style (e.g., realism, impressionism, cyberpunk, manga) and define lighting conditions (e.g., soft, dramatic, ambient, neon). Specify framing choices (e.g., close-up, wide shot, overhead) and color themes. Retain the core subject while enhancing its visual appeal through these adjustments. Execute as:\",\n    \"transformation\": \"`{role=style_enhancer; input=[base_prompt:str]; process=[identify_optimal_art_style(), add_lighting_specifications(), include_composition_details(), specify_technical_parameters()]; constraints=[maintain the essence of the core subject, enhance visual appeal without altering core components]; requirements=[artistic coherence, alignment with specified technical parameters]; output={styled_prompt:str}}`\"\n},\n\n\"9011-c-technical_optimizer\": {\n    \"title\": \"Technical Optimizer\",\n    \"interpretation\": \"Optimize the technical parameters of the prompt to ensure maximum quality for image generation. Assign specific camera types (e.g., DSLR, film, smartphone), lens types (e.g., 35mm, macro, wide angle), and settings such as aperture (e.g., f/1.4, f/8), ISO, and shutter speed. Set image resolution (e.g., 4K, 8K), rendering details (e.g., ray-tracing, photorealistic), and aspect ratio (e.g., 16:9, 1:1). Focus strictly on optimizing technical specifications without introducing creative elements. Execute as:\",\n    \"transformation\": \"`{role=technical_optimizer; input=[styled_prompt:str]; process=[add_camera_settings(), specify_lighting_conditions(), include_render_quality_terms(), optimize_aspect_ratio_guidance()]; constraints=[strictly technical focus, no creative modifications allowed]; requirements=[use professional terminology, ensure optimization for generation quality]; output={technical_prompt:str}}`\"\n},\n\n\"9011-d-prompt_finalizer\": {\n    \"title\": \"Prompt Finalizer\",\n    \"interpretation\": \"Finalize the prompt into the most effective format for AI image generation. Organize the prompt components in a logical sequence for easy parsing. Condense descriptions to essential instructions while maintaining clarity. Validate consistency of parameters and ensure the format aligns with the direct input expectations of AI image models. Execute as:\",\n    \"transformation\": \"`{role=prompt_finalizer; input=[technical_prompt:str]; process=[structure_for_ai_parsing(), balance detail levels, ensure clarity, validate readiness for generation]; constraints=[no additional concepts introduced, optimization only]; requirements=[output in AI-friendly format, maintain balanced specificity and clarity]; output={final_image_prompt:str}}`\"\n}\n```",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"9011-a-image_prompt_optimizer\": \"Transform a raw image request into a detailed visual prompt for AI image generation. Identify all specific visual elements, describing attributes such as color, shape, size, and material. Incorporate explicit composition instructions, including subject placement (for example, foreground or background), point-of-view (for example, eye-level or bird's-eye view), and environmental setting. Restrict all descriptions to tangible, concrete imagery; remove any abstract concepts. Present the final result in a structured format, ready for AI processing, using concise and direct visual language.\",\n  \"9011-b-style_enhancer\": \"Refine the existing image prompt by specifying a particular artistic style, such as realism, impressionism, cyberpunk, or manga. Assign lighting conditions, such as soft, dramatic, ambient, or neon. Determine the shot framing, including options like close-up, wide shot, or overhead angle, and select a cohesive color theme. Maintain the central subject, enhancing overall visual appeal through these stylistic adjustments. Ensure that the final prompt is artistically coherent and meets all defined technical parameters.\",\n  \"9011-c-technical_optimizer\": \"Optimize the prompt's technical parameters solely for high-quality image generation. Specify a camera type, such as DSLR, film, or smartphone, and select an appropriate lens, such as 35mm, macro, or wide angle. Define key settings, including aperture (for example, f/1.4 or f/8), ISO, and shutter speed. Set the output resolution (such as 4K or 8K), rendering method (like ray-tracing or photorealistic), and aspect ratio (for example, 16:9 or 1:1). Maintain focus on technical specification without making creative modifications. Use professional terminology and tailor all choices to maximize image generation quality.\",\n  \"9011-d-prompt_finalizer\": \"Finalize the technically optimized prompt for use with AI image generation models. Organize instructions and parameters in a clear, logical order to facilitate smooth parsing by AI systems. Condense descriptive elements to only the essential information required, ensuring that each parameter is clear and consistent with others. Strictly optimize the format for AI compatibility without adding or removing concepts. Output the final prompt in a maximally clear and balanced format suitable for direct AI processing.\"\n}",
        }
      }
    },
      {
      "instruction": "# Image Prompt Optimizer\n\nYour goal is not to **describe** the request, but to **optimize** it into a precise, visual prompt for AI image generation. Identify all concrete visual elements in the request. Specify each using direct visual descriptors. Add clear composition guidance (subject placement, perspective, background). Use only tangible objects and environments. Execute as:\n\n`{role=image_prompt_optimizer; input=[raw_request:str]; process=[extract_visual_elements(), enhance_descriptive_clarity(), add_composition_details(), optimize_for_ai_generation()]; constraints=[visual_focus_only(), no_abstract_concepts(), concrete_imagery_only()]; requirements=[generation_ready_format(), clear_visual_language(), specific_details()]; output={optimized_image_prompt:str}}`",
      "step": "a",
      "title": "Image Prompt Optimizer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"```json\\n\\\"9011-a-image_prompt_optimizer\\\": {\\n    \\\"title\\\": \\\"Image Prompt Optimizer\\\",\\n    \\\"interpretation\\\": \\\"Transform the raw request into a detailed visual prompt for AI image generation. Identify and specify all concrete visual elements, using direct descriptors such as colors, shapes, sizes, and materials. Include explicit composition guidance, detailing subject placement (e.g., foreground, background), perspective (e.g., eye-level, bird's-eye view), and environmental context. Ensure all imagery is tangible and avoid any abstract concepts. Execute as:\\\",\\n    \\\"transformation\\\": \\\"`{role=image_prompt_optimizer; input=[raw_request:str]; process=[extract_visual_elements(), enhance_descriptive_clarity(), add_composition_details(), optimize_for_ai_generation()]; constraints=[focus_on tangible visuals only, exclude abstract concepts, prioritize detail and specificity]; requirements=[output in structured format ready for AI processing, clear and concise visual language, inclusion of all relevant visual details]; output={optimized_image_prompt:str}}`\\\"\\n},\\n\\n\\\"9011-b-style_enhancer\\\": {\\n    \\\"title\\\": \\\"Style Enhancer\\\",\\n    \\\"interpretation\\\": \\\"Refine the existing prompt to incorporate a specific artistic style and technical parameters. Choose an art style (e.g., realism, impressionism, cyberpunk, manga) and define lighting conditions (e.g., soft, dramatic, ambient, neon). Specify framing choices (e.g., close-up, wide shot, overhead) and color themes. Retain the core subject while enhancing its visual appeal through these adjustments. Execute as:\\\",\\n    \\\"transformation\\\": \\\"`{role=style_enhancer; input=[base_prompt:str]; process=[identify_optimal_art_style(), add_lighting_specifications(), include_composition_details(), specify_technical_parameters()]; constraints=[maintain the essence of the core subject, enhance visual appeal without altering core components]; requirements=[artistic coherence, alignment with specified technical parameters]; output={styled_prompt:str}}`\\\"\\n},\\n\\n\\\"9011-c-technical_optimizer\\\": {\\n    \\\"title\\\": \\\"Technical Optimizer\\\",\\n    \\\"interpretation\\\": \\\"Optimize the technical parameters of the prompt to ensure maximum quality for image generation. Assign specific camera types (e.g., DSLR, film, smartphone), lens types (e.g., 35mm, macro, wide angle), and settings such as aperture (e.g., f/1.4, f/8), ISO, and shutter speed. Set image resolution (e.g., 4K, 8K), rendering details (e.g., ray-tracing, photorealistic), and aspect ratio (e.g., 16:9, 1:1). Focus strictly on optimizing technical specifications without introducing creative elements. Execute as:\\\",\\n    \\\"transformation\\\": \\\"`{role=technical_optimizer; input=[styled_prompt:str]; process=[add_camera_settings(), specify_lighting_conditions(), include_render_quality_terms(), optimize_aspect_ratio_guidance()]; constraints=[strictly technical focus, no creative modifications allowed]; requirements=[use professional terminology, ensure optimization for generation quality]; output={technical_prompt:str}}`\\\"\\n},\\n\\n\\\"9011-d-prompt_finalizer\\\": {\\n    \\\"title\\\": \\\"Prompt Finalizer\\\",\\n    \\\"interpretation\\\": \\\"Finalize the prompt into the most effective format for AI image generation. Organize the prompt components in a logical sequence for easy parsing. Condense descriptions to essential instructions while maintaining clarity. Validate consistency of parameters and ensure the format aligns with the direct input expectations of AI image models. Execute as:\\\",\\n    \\\"transformation\\\": \\\"`{role=prompt_finalizer; input=[technical_prompt:str]; process=[structure_for_ai_parsing(), balance detail levels, ensure clarity, validate readiness for generation]; constraints=[no additional concepts introduced, optimization only]; requirements=[output in AI-friendly format, maintain balanced specificity and clarity]; output={final_image_prompt:str}}`\\\"\\n}\\n```\\\"```'\\n\\n\",\n  \"9011-a-image_prompt_optimizer\": \"Transform a raw image request into a detailed visual prompt for AI image generation. Identify all specific visual elements, describing attributes such as color, shape, size, and material. Incorporate explicit composition instructions, including subject placement (for example, foreground or background), point-of-view (for example, eye-level or bird's-eye view), and environmental setting. Restrict all descriptions to tangible, concrete imagery; remove any abstract concepts. Present the final result in a structured format, ready for AI processing, using concise and direct visual language.\",\n  \"9011-b-style_enhancer\": \"Refine the existing image prompt by specifying a particular artistic style, such as realism, impressionism, cyberpunk, or manga. Assign lighting conditions, such as soft, dramatic, ambient, or neon. Determine the shot framing, including options like close-up, wide shot, or overhead angle, and select a cohesive color theme. Maintain the central subject, enhancing overall visual appeal through these stylistic adjustments. Ensure that the final prompt is artistically coherent and meets all defined technical parameters.\",\n  \"9011-c-technical_optimizer\": \"Optimize the prompt's technical parameters solely for high-quality image generation. Specify a camera type, such as DSLR, film, or smartphone, and select an appropriate lens, such as 35mm, macro, or wide angle. Define key settings, including aperture (for example, f/1.4 or f/8), ISO, and shutter speed. Set the output resolution (such as 4K or 8K), rendering method (like ray-tracing or photorealistic), and aspect ratio (for example, 16:9 or 1:1). Maintain focus on technical specification without making creative modifications. Use professional terminology and tailor all choices to maximize image generation quality.\",\n  \"9011-d-prompt_finalizer\": \"Finalize the technically optimized prompt for use with AI image generation models. Organize instructions and parameters in a clear, logical order to facilitate smooth parsing by AI systems. Condense descriptive elements to only the essential information required, ensuring that each parameter is clear and consistent with others. Strictly optimize the format for AI compatibility without adding or removing concepts. Output the final prompt in a maximally clear and balanced format suitable for direct AI processing.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"optimized_image_prompt\": \"A collection of four distinct instruction cards, each card placed side by side in a horizontal row across a clean, neutral background. Each card features a unique colored header with bold text, and a centered icon representing its function: a magnifying glass for image prompt optimizer, a paintbrush for style enhancer, a camera for technical optimizer, and a checkmark for prompt finalizer. The cards are rectangular, made of glossy white material, and display concise, easy-to-read stepwise instructions in black text beneath each icon. The perspective is eye-level and straight-on, with soft, even lighting ensuring all cards are clearly legible. There are no people or abstract designs; the scene focuses solely on the tangible cards against the simple, uncluttered background.\"\n}",
        }
      }
    },
      {
      "instruction": "# Style Enhancer\n\nYour goal is not to **change** the core subject, but to **enhance** it with specific artistic style and technical parameters. Select an art style from options (e.g., realism, impressionism, cyberpunk, manga). Define lighting from a list (e.g., natural, dramatic, backlit, neon). Specify framing (close-up, wide shot, overhead) and color themes. Maintain subject, adjust only visual style and technical aspects. Execute as:\n\n`{role=style_enhancer; input=[base_prompt:str]; process=[identify_optimal_art_style(), add_lighting_specifications(), include_composition_details(), specify_technical_parameters()]; constraints=[maintain_core_subject(), enhance_visual_appeal()]; requirements=[artistic_coherence(), technical_precision()]; output={styled_prompt:str}}`",
      "step": "b",
      "title": "Style Enhancer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"```json\\n\\\"9011-a-image_prompt_optimizer\\\": {\\n    \\\"title\\\": \\\"Image Prompt Optimizer\\\",\\n    \\\"interpretation\\\": \\\"Transform the raw request into a detailed visual prompt for AI image generation. Identify and specify all concrete visual elements, using direct descriptors such as colors, shapes, sizes, and materials. Include explicit composition guidance, detailing subject placement (e.g., foreground, background), perspective (e.g., eye-level, bird's-eye view), and environmental context. Ensure all imagery is tangible and avoid any abstract concepts. Execute as:\\\",\\n    \\\"transformation\\\": \\\"`{role=image_prompt_optimizer; input=[raw_request:str]; process=[extract_visual_elements(), enhance_descriptive_clarity(), add_composition_details(), optimize_for_ai_generation()]; constraints=[focus_on tangible visuals only, exclude abstract concepts, prioritize detail and specificity]; requirements=[output in structured format ready for AI processing, clear and concise visual language, inclusion of all relevant visual details]; output={optimized_image_prompt:str}}`\\\"\\n},\\n\\n\\\"9011-b-style_enhancer\\\": {\\n    \\\"title\\\": \\\"Style Enhancer\\\",\\n    \\\"interpretation\\\": \\\"Refine the existing prompt to incorporate a specific artistic style and technical parameters. Choose an art style (e.g., realism, impressionism, cyberpunk, manga) and define lighting conditions (e.g., soft, dramatic, ambient, neon). Specify framing choices (e.g., close-up, wide shot, overhead) and color themes. Retain the core subject while enhancing its visual appeal through these adjustments. Execute as:\\\",\\n    \\\"transformation\\\": \\\"`{role=style_enhancer; input=[base_prompt:str]; process=[identify_optimal_art_style(), add_lighting_specifications(), include_composition_details(), specify_technical_parameters()]; constraints=[maintain the essence of the core subject, enhance visual appeal without altering core components]; requirements=[artistic coherence, alignment with specified technical parameters]; output={styled_prompt:str}}`\\\"\\n},\\n\\n\\\"9011-c-technical_optimizer\\\": {\\n    \\\"title\\\": \\\"Technical Optimizer\\\",\\n    \\\"interpretation\\\": \\\"Optimize the technical parameters of the prompt to ensure maximum quality for image generation. Assign specific camera types (e.g., DSLR, film, smartphone), lens types (e.g., 35mm, macro, wide angle), and settings such as aperture (e.g., f/1.4, f/8), ISO, and shutter speed. Set image resolution (e.g., 4K, 8K), rendering details (e.g., ray-tracing, photorealistic), and aspect ratio (e.g., 16:9, 1:1). Focus strictly on optimizing technical specifications without introducing creative elements. Execute as:\\\",\\n    \\\"transformation\\\": \\\"`{role=technical_optimizer; input=[styled_prompt:str]; process=[add_camera_settings(), specify_lighting_conditions(), include_render_quality_terms(), optimize_aspect_ratio_guidance()]; constraints=[strictly technical focus, no creative modifications allowed]; requirements=[use professional terminology, ensure optimization for generation quality]; output={technical_prompt:str}}`\\\"\\n},\\n\\n\\\"9011-d-prompt_finalizer\\\": {\\n    \\\"title\\\": \\\"Prompt Finalizer\\\",\\n    \\\"interpretation\\\": \\\"Finalize the prompt into the most effective format for AI image generation. Organize the prompt components in a logical sequence for easy parsing. Condense descriptions to essential instructions while maintaining clarity. Validate consistency of parameters and ensure the format aligns with the direct input expectations of AI image models. Execute as:\\\",\\n    \\\"transformation\\\": \\\"`{role=prompt_finalizer; input=[technical_prompt:str]; process=[structure_for_ai_parsing(), balance detail levels, ensure clarity, validate readiness for generation]; constraints=[no additional concepts introduced, optimization only]; requirements=[output in AI-friendly format, maintain balanced specificity and clarity]; output={final_image_prompt:str}}`\\\"\\n}\\n```\\\"```'\\n\\n\",\n  \"optimized_image_prompt\": \"A collection of four distinct instruction cards, each card placed side by side in a horizontal row across a clean, neutral background. Each card features a unique colored header with bold text, and a centered icon representing its function: a magnifying glass for image prompt optimizer, a paintbrush for style enhancer, a camera for technical optimizer, and a checkmark for prompt finalizer. The cards are rectangular, made of glossy white material, and display concise, easy-to-read stepwise instructions in black text beneath each icon. The perspective is eye-level and straight-on, with soft, even lighting ensuring all cards are clearly legible. There are no people or abstract designs; the scene focuses solely on the tangible cards against the simple, uncluttered background.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"styled_prompt\": \"A collection of four distinct instruction cards, each card placed side by side in a horizontal row across a clean, neutral background, depicted in a crisp, modern realism art style. The lighting is soft and natural, casting gentle shadows that enhance the dimensionality and gloss of each card while maintaining a neutral, balanced color temperature. Frame the scene as a wide shot at eye-level, ensuring all four cards are equally visible and evenly spaced within the composition. The color palette is minimalist with predominately white and gray tones, each card featuring a vibrant accent color header—blue, red, green, yellow—corresponding to the unique icons: a magnifying glass, a paintbrush, a camera, and a checkmark. The glossy white surface of each rectangular card is highlighted with subtle reflections. All instructional text appears crisp and legible in black beneath the centered icons. The background remains uncluttered and soft-focused, elevating the clean, professional atmosphere and ensuring the instruction cards are the central focus.\"\n}",
        }
      }
    },
      {
      "instruction": "# Technical Optimizer\n\nYour goal is not to **add creativity**, but to **optimize** technical parameters for maximum image generation quality. Assign camera type (DSLR, film, smartphone), lens (35mm, macro, wide angle), aperture (e.g., f/1.4, f/8), ISO, and shutter speed. Set image resolution (e.g., 4K, 8K), rendering details (ray-tracing, photorealistic), and aspect ratio (e.g., 16:9, 1:1). Focus strictly on technical generation parameters. Execute as:\n\n`{role=technical_optimizer; input=[styled_prompt:str]; process=[add_camera_settings(), specify_lighting_conditions(), include_render_quality_terms(), optimize_aspect_ratio_guidance()]; constraints=[technical_focus_only(), no_creative_changes()]; requirements=[professional_terminology(), generation_optimization()]; output={technical_prompt:str}}`",
      "step": "c",
      "title": "Technical Optimizer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"```json\\n\\\"9011-a-image_prompt_optimizer\\\": {\\n    \\\"title\\\": \\\"Image Prompt Optimizer\\\",\\n    \\\"interpretation\\\": \\\"Transform the raw request into a detailed visual prompt for AI image generation. Identify and specify all concrete visual elements, using direct descriptors such as colors, shapes, sizes, and materials. Include explicit composition guidance, detailing subject placement (e.g., foreground, background), perspective (e.g., eye-level, bird's-eye view), and environmental context. Ensure all imagery is tangible and avoid any abstract concepts. Execute as:\\\",\\n    \\\"transformation\\\": \\\"`{role=image_prompt_optimizer; input=[raw_request:str]; process=[extract_visual_elements(), enhance_descriptive_clarity(), add_composition_details(), optimize_for_ai_generation()]; constraints=[focus_on tangible visuals only, exclude abstract concepts, prioritize detail and specificity]; requirements=[output in structured format ready for AI processing, clear and concise visual language, inclusion of all relevant visual details]; output={optimized_image_prompt:str}}`\\\"\\n},\\n\\n\\\"9011-b-style_enhancer\\\": {\\n    \\\"title\\\": \\\"Style Enhancer\\\",\\n    \\\"interpretation\\\": \\\"Refine the existing prompt to incorporate a specific artistic style and technical parameters. Choose an art style (e.g., realism, impressionism, cyberpunk, manga) and define lighting conditions (e.g., soft, dramatic, ambient, neon). Specify framing choices (e.g., close-up, wide shot, overhead) and color themes. Retain the core subject while enhancing its visual appeal through these adjustments. Execute as:\\\",\\n    \\\"transformation\\\": \\\"`{role=style_enhancer; input=[base_prompt:str]; process=[identify_optimal_art_style(), add_lighting_specifications(), include_composition_details(), specify_technical_parameters()]; constraints=[maintain the essence of the core subject, enhance visual appeal without altering core components]; requirements=[artistic coherence, alignment with specified technical parameters]; output={styled_prompt:str}}`\\\"\\n},\\n\\n\\\"9011-c-technical_optimizer\\\": {\\n    \\\"title\\\": \\\"Technical Optimizer\\\",\\n    \\\"interpretation\\\": \\\"Optimize the technical parameters of the prompt to ensure maximum quality for image generation. Assign specific camera types (e.g., DSLR, film, smartphone), lens types (e.g., 35mm, macro, wide angle), and settings such as aperture (e.g., f/1.4, f/8), ISO, and shutter speed. Set image resolution (e.g., 4K, 8K), rendering details (e.g., ray-tracing, photorealistic), and aspect ratio (e.g., 16:9, 1:1). Focus strictly on optimizing technical specifications without introducing creative elements. Execute as:\\\",\\n    \\\"transformation\\\": \\\"`{role=technical_optimizer; input=[styled_prompt:str]; process=[add_camera_settings(), specify_lighting_conditions(), include_render_quality_terms(), optimize_aspect_ratio_guidance()]; constraints=[strictly technical focus, no creative modifications allowed]; requirements=[use professional terminology, ensure optimization for generation quality]; output={technical_prompt:str}}`\\\"\\n},\\n\\n\\\"9011-d-prompt_finalizer\\\": {\\n    \\\"title\\\": \\\"Prompt Finalizer\\\",\\n    \\\"interpretation\\\": \\\"Finalize the prompt into the most effective format for AI image generation. Organize the prompt components in a logical sequence for easy parsing. Condense descriptions to essential instructions while maintaining clarity. Validate consistency of parameters and ensure the format aligns with the direct input expectations of AI image models. Execute as:\\\",\\n    \\\"transformation\\\": \\\"`{role=prompt_finalizer; input=[technical_prompt:str]; process=[structure_for_ai_parsing(), balance detail levels, ensure clarity, validate readiness for generation]; constraints=[no additional concepts introduced, optimization only]; requirements=[output in AI-friendly format, maintain balanced specificity and clarity]; output={final_image_prompt:str}}`\\\"\\n}\\n```\\\"```'\\n\\n\",\n  \"styled_prompt\": \"A collection of four distinct instruction cards, each card placed side by side in a horizontal row across a clean, neutral background, depicted in a crisp, modern realism art style. The lighting is soft and natural, casting gentle shadows that enhance the dimensionality and gloss of each card while maintaining a neutral, balanced color temperature. Frame the scene as a wide shot at eye-level, ensuring all four cards are equally visible and evenly spaced within the composition. The color palette is minimalist with predominately white and gray tones, each card featuring a vibrant accent color header—blue, red, green, yellow—corresponding to the unique icons: a magnifying glass, a paintbrush, a camera, and a checkmark. The glossy white surface of each rectangular card is highlighted with subtle reflections. All instructional text appears crisp and legible in black beneath the centered icons. The background remains uncluttered and soft-focused, elevating the clean, professional atmosphere and ensuring the instruction cards are the central focus.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"technical_prompt\": \"Camera type: Full-frame DSLR. Lens: 35mm prime lens. Aperture: f/5.6 for balanced depth of field and card sharpness. ISO: 100 for maximum image clarity and minimal noise. Shutter speed: 1/120s to prevent any motion blur in a static shot. Image resolution: 8K (7680 x 4320 pixels) for ultra-high detail reproduction. Rendering: Photorealistic, ray-traced lighting for accurate reflections and realistic materiality. Lighting: Soft, diffused natural light, evenly illuminating the scene with gentle shadow fall-off and balanced white color temperature (around 5500K). Aspect ratio: 16:9 to accommodate the wide, horizontal composition showing all four cards side by side with ample space. Ensure each card is sharply rendered with crisp text, clearly visible vibrant accent headers (blue, red, green, yellow), and natural, subtle surface reflections. Maintain an uncluttered, soft-focused neutral background to emphasize foreground sharpness and professionalism.\"\n}",
        }
      }
    },
      {
      "instruction": "# Prompt Finalizer\n\nYour goal is not to **expand** the prompt, but to **finalize** it into the most effective format for image generation. Arrange prompt steps in logical order for parsing. Shorten descriptions to essential instructions. Validate parameter consistency. Ensure format matches direct input expectations of AI image models. Execute as:\n\n`{role=prompt_finalizer; input=[technical_prompt:str]; process=[structure_for_ai_parsing(), balance_detail_levels(), ensure_clarity(), validate_generation_readiness()]; constraints=[no_additional_concepts(), optimization_only()]; requirements=[ai_friendly_format(), balanced_specificity()]; output={final_image_prompt:str}}`",
      "step": "d",
      "title": "Prompt Finalizer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"```json\\n\\\"9011-a-image_prompt_optimizer\\\": {\\n    \\\"title\\\": \\\"Image Prompt Optimizer\\\",\\n    \\\"interpretation\\\": \\\"Transform the raw request into a detailed visual prompt for AI image generation. Identify and specify all concrete visual elements, using direct descriptors such as colors, shapes, sizes, and materials. Include explicit composition guidance, detailing subject placement (e.g., foreground, background), perspective (e.g., eye-level, bird's-eye view), and environmental context. Ensure all imagery is tangible and avoid any abstract concepts. Execute as:\\\",\\n    \\\"transformation\\\": \\\"`{role=image_prompt_optimizer; input=[raw_request:str]; process=[extract_visual_elements(), enhance_descriptive_clarity(), add_composition_details(), optimize_for_ai_generation()]; constraints=[focus_on tangible visuals only, exclude abstract concepts, prioritize detail and specificity]; requirements=[output in structured format ready for AI processing, clear and concise visual language, inclusion of all relevant visual details]; output={optimized_image_prompt:str}}`\\\"\\n},\\n\\n\\\"9011-b-style_enhancer\\\": {\\n    \\\"title\\\": \\\"Style Enhancer\\\",\\n    \\\"interpretation\\\": \\\"Refine the existing prompt to incorporate a specific artistic style and technical parameters. Choose an art style (e.g., realism, impressionism, cyberpunk, manga) and define lighting conditions (e.g., soft, dramatic, ambient, neon). Specify framing choices (e.g., close-up, wide shot, overhead) and color themes. Retain the core subject while enhancing its visual appeal through these adjustments. Execute as:\\\",\\n    \\\"transformation\\\": \\\"`{role=style_enhancer; input=[base_prompt:str]; process=[identify_optimal_art_style(), add_lighting_specifications(), include_composition_details(), specify_technical_parameters()]; constraints=[maintain the essence of the core subject, enhance visual appeal without altering core components]; requirements=[artistic coherence, alignment with specified technical parameters]; output={styled_prompt:str}}`\\\"\\n},\\n\\n\\\"9011-c-technical_optimizer\\\": {\\n    \\\"title\\\": \\\"Technical Optimizer\\\",\\n    \\\"interpretation\\\": \\\"Optimize the technical parameters of the prompt to ensure maximum quality for image generation. Assign specific camera types (e.g., DSLR, film, smartphone), lens types (e.g., 35mm, macro, wide angle), and settings such as aperture (e.g., f/1.4, f/8), ISO, and shutter speed. Set image resolution (e.g., 4K, 8K), rendering details (e.g., ray-tracing, photorealistic), and aspect ratio (e.g., 16:9, 1:1). Focus strictly on optimizing technical specifications without introducing creative elements. Execute as:\\\",\\n    \\\"transformation\\\": \\\"`{role=technical_optimizer; input=[styled_prompt:str]; process=[add_camera_settings(), specify_lighting_conditions(), include_render_quality_terms(), optimize_aspect_ratio_guidance()]; constraints=[strictly technical focus, no creative modifications allowed]; requirements=[use professional terminology, ensure optimization for generation quality]; output={technical_prompt:str}}`\\\"\\n},\\n\\n\\\"9011-d-prompt_finalizer\\\": {\\n    \\\"title\\\": \\\"Prompt Finalizer\\\",\\n    \\\"interpretation\\\": \\\"Finalize the prompt into the most effective format for AI image generation. Organize the prompt components in a logical sequence for easy parsing. Condense descriptions to essential instructions while maintaining clarity. Validate consistency of parameters and ensure the format aligns with the direct input expectations of AI image models. Execute as:\\\",\\n    \\\"transformation\\\": \\\"`{role=prompt_finalizer; input=[technical_prompt:str]; process=[structure_for_ai_parsing(), balance detail levels, ensure clarity, validate readiness for generation]; constraints=[no additional concepts introduced, optimization only]; requirements=[output in AI-friendly format, maintain balanced specificity and clarity]; output={final_image_prompt:str}}`\\\"\\n}\\n```\\\"```'\\n\\n\",\n  \"technical_prompt\": \"Camera type: Full-frame DSLR. Lens: 35mm prime lens. Aperture: f/5.6 for balanced depth of field and card sharpness. ISO: 100 for maximum image clarity and minimal noise. Shutter speed: 1/120s to prevent any motion blur in a static shot. Image resolution: 8K (7680 x 4320 pixels) for ultra-high detail reproduction. Rendering: Photorealistic, ray-traced lighting for accurate reflections and realistic materiality. Lighting: Soft, diffused natural light, evenly illuminating the scene with gentle shadow fall-off and balanced white color temperature (around 5500K). Aspect ratio: 16:9 to accommodate the wide, horizontal composition showing all four cards side by side with ample space. Ensure each card is sharply rendered with crisp text, clearly visible vibrant accent headers (blue, red, green, yellow), and natural, subtle surface reflections. Maintain an uncluttered, soft-focused neutral background to emphasize foreground sharpness and professionalism.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"final_image_prompt\": \"Four playing cards placed side by side in a wide horizontal row, each card sharply focused and displaying crisp text. Each card features a distinct, vibrant accent header: one blue, one red, one green, and one yellow. The scene is illuminated with soft, diffused natural light (color temperature 5500K), creating gentle shadow fall-off and subtle, natural surface reflections on the cards. Background is uncluttered, softly focused, and neutral-toned to highlight the sharp, professional appearance of the cards in the foreground. Photorealistic quality with ray-traced lighting and accurate reflections. Use a full-frame DSLR with a 35mm prime lens, aperture set to f/5.6, ISO 100, shutter speed 1/120s. Resolution: 8K (7680x4320). Aspect ratio: 16:9.\"\n}",
        }
      }
    },
      {
      "instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format:str}; direction_vector=['Prompt accurately converted to imperative instruction set', 'Clarity and actionability accelerate user productivity', 'Feed the produced instruction set into the next transformation template' ]}`",
      "step": "a",
      "title": "Instruction Converter",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"```json\\n\\\"9011-a-image_prompt_optimizer\\\": {\\n    \\\"title\\\": \\\"Image Prompt Optimizer\\\",\\n    \\\"interpretation\\\": \\\"Transform the raw request into a detailed visual prompt for AI image generation. Identify and specify all concrete visual elements, using direct descriptors such as colors, shapes, sizes, and materials. Include explicit composition guidance, detailing subject placement (e.g., foreground, background), perspective (e.g., eye-level, bird's-eye view), and environmental context. Ensure all imagery is tangible and avoid any abstract concepts. Execute as:\\\",\\n    \\\"transformation\\\": \\\"`{role=image_prompt_optimizer; input=[raw_request:str]; process=[extract_visual_elements(), enhance_descriptive_clarity(), add_composition_details(), optimize_for_ai_generation()]; constraints=[focus_on tangible visuals only, exclude abstract concepts, prioritize detail and specificity]; requirements=[output in structured format ready for AI processing, clear and concise visual language, inclusion of all relevant visual details]; output={optimized_image_prompt:str}}`\\\"\\n},\\n\\n\\\"9011-b-style_enhancer\\\": {\\n    \\\"title\\\": \\\"Style Enhancer\\\",\\n    \\\"interpretation\\\": \\\"Refine the existing prompt to incorporate a specific artistic style and technical parameters. Choose an art style (e.g., realism, impressionism, cyberpunk, manga) and define lighting conditions (e.g., soft, dramatic, ambient, neon). Specify framing choices (e.g., close-up, wide shot, overhead) and color themes. Retain the core subject while enhancing its visual appeal through these adjustments. Execute as:\\\",\\n    \\\"transformation\\\": \\\"`{role=style_enhancer; input=[base_prompt:str]; process=[identify_optimal_art_style(), add_lighting_specifications(), include_composition_details(), specify_technical_parameters()]; constraints=[maintain the essence of the core subject, enhance visual appeal without altering core components]; requirements=[artistic coherence, alignment with specified technical parameters]; output={styled_prompt:str}}`\\\"\\n},\\n\\n\\\"9011-c-technical_optimizer\\\": {\\n    \\\"title\\\": \\\"Technical Optimizer\\\",\\n    \\\"interpretation\\\": \\\"Optimize the technical parameters of the prompt to ensure maximum quality for image generation. Assign specific camera types (e.g., DSLR, film, smartphone), lens types (e.g., 35mm, macro, wide angle), and settings such as aperture (e.g., f/1.4, f/8), ISO, and shutter speed. Set image resolution (e.g., 4K, 8K), rendering details (e.g., ray-tracing, photorealistic), and aspect ratio (e.g., 16:9, 1:1). Focus strictly on optimizing technical specifications without introducing creative elements. Execute as:\\\",\\n    \\\"transformation\\\": \\\"`{role=technical_optimizer; input=[styled_prompt:str]; process=[add_camera_settings(), specify_lighting_conditions(), include_render_quality_terms(), optimize_aspect_ratio_guidance()]; constraints=[strictly technical focus, no creative modifications allowed]; requirements=[use professional terminology, ensure optimization for generation quality]; output={technical_prompt:str}}`\\\"\\n},\\n\\n\\\"9011-d-prompt_finalizer\\\": {\\n    \\\"title\\\": \\\"Prompt Finalizer\\\",\\n    \\\"interpretation\\\": \\\"Finalize the prompt into the most effective format for AI image generation. Organize the prompt components in a logical sequence for easy parsing. Condense descriptions to essential instructions while maintaining clarity. Validate consistency of parameters and ensure the format aligns with the direct input expectations of AI image models. Execute as:\\\",\\n    \\\"transformation\\\": \\\"`{role=prompt_finalizer; input=[technical_prompt:str]; process=[structure_for_ai_parsing(), balance detail levels, ensure clarity, validate readiness for generation]; constraints=[no additional concepts introduced, optimization only]; requirements=[output in AI-friendly format, maintain balanced specificity and clarity]; output={final_image_prompt:str}}`\\\"\\n}\\n```\\\"```'\\n\\n\",\n  \"final_image_prompt\": \"Four playing cards placed side by side in a wide horizontal row, each card sharply focused and displaying crisp text. Each card features a distinct, vibrant accent header: one blue, one red, one green, and one yellow. The scene is illuminated with soft, diffused natural light (color temperature 5500K), creating gentle shadow fall-off and subtle, natural surface reflections on the cards. Background is uncluttered, softly focused, and neutral-toned to highlight the sharp, professional appearance of the cards in the foreground. Photorealistic quality with ray-traced lighting and accurate reflections. Use a full-frame DSLR with a 35mm prime lens, aperture set to f/5.6, ISO 100, shutter speed 1/120s. Resolution: 8K (7680x4320). Aspect ratio: 16:9.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"instruction_format\": \"Transform the initial prompt pipeline into a sequential set of directives for AI image prompt optimization. Follow these steps: 1) Convert the user's raw image request into a comprehensive visual prompt by identifying and describing all specific visual features, such as colors, shapes, sizes, materials, composition, perspective, and environmental context; ensure tangible and concrete imagery only. 2) Refine the detailed visual prompt to include a chosen artistic style, specify lighting conditions, define framing and color schemes, and boost visual impact while preserving the core subject. 3) Optimize the prompt's technical parameters by assigning precise camera types, lens options, camera settings (aperture, ISO, shutter speed), image resolution, rendering method, and aspect ratio; focus exclusively on technical accuracy and completeness. 4) Finalize the optimized prompt, structuring all components in a logical sequence for efficient AI image model parsing, condensing to essential instructions without introducing new concepts, checking for internal consistency, and formatting for direct use in AI generation.\"\n}",
        }
      }
    }