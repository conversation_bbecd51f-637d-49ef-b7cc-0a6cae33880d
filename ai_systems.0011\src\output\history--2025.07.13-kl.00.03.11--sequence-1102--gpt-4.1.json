  {
  "initial_prompt": "TEMPLATES = {\n\n    # 1102: Contextual Expander Explosive Decomposer\n    \"1102-a-explosive_decomposer\": {\n        \"title\": \"Problem Exploder\",\n        \"interpretation\": \"Your goal is not to **solve** or **answer** the input, but to **expand its context** and **detonate** it into every explicit directive, hidden dependency, and actionable sub-goal—yielding a crystal-clear operational blueprint. Execute as contextual expander + explosive decomposition engine:  \",\n        \"transformation\": \"`{role=contextual_expander_explosive_decomposer; input=[raw_input:str]; process=[expand_contextual_boundaries(), strip_first_person_references(), identify_explicit_requests(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_execution_blockers(), extract_functional_components(), convert_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), synthesize_operational_directives(), construct_goal_map(), generate_functional_instruction_map() ]; constraints=[no_solutions(), pure_expansion_and_decomposition_only(), deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_agnostic_applicability() ]; requirements=[complete_contextual_expansion(), full_functional_decomposition(), actionable_directive_conversion(), remove_subjective_references(), maintain_operational_accuracy(), preserve_original_intent() ]; output={expanded_context:str, goal_map:list, functional_instruction_map:dict, operational_directives:array, execution_dependencies:array }; direction_vector=['Input fully expanded and decomposed into actionable blueprints', 'Constructive clarity maximizes downstream solution velocity', 'Forward the generated maps and directives to the next processing template']}`\",\n        # \"context\": {},\n    },\n\n}\n\n[Contextual Expander and Explosive Decomposer] Your goal is **not** to answer, solve, or implement the user’s request, but to **expand its contextual frame** and **detonate** it into every explicit task, latent dependency, and functional directive—then synthesize a crystal‑clear, domain‑agnostic execution blueprint. Execute as a unified context‑amplification+problem‑decomposition engine: `{role=contextual_expander_explosive_decomposer; input=[raw_input:any]; tags={task=\"contextual_expand_and_decompose\"; action=\"produce_context_snapshot_goal_map_instruction_map\"; style=\"concise\"; format=\"markdown\"}; directive=[constructive]; process=[strip_first_person_references(), capture_domain_and_stakeholder_context(), amplify_relevant_background(), identify_explicit_requests(), surface_hidden_assumptions(), enumerate_sub_goals(), detect_execution_blockers(), detonate_into_functional_components(), extract_procedural_constraints(), convert_declaratives_to_operational_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), synthesize_context_snapshot(), construct_goal_map(), compile_functional_instruction_map(), list_execution_dependencies()]; constraints=[no_direct_solutions(), pure_expansion_and_decomposition_only(), deliver_clear_actionable_commands(), preserve_original_sequence_when_mapping(), maintain_domain_agnostic_language()]; requirements=[complete_contextual_expansion(), exhaustive_goal_enumeration(), functional_decomposition_integrity(), directive_conversion_accuracy(), removal_of_subjective_or_self_referential_language(), retention_of_original_intent()]; output={context_snapshot:str, goal_map:list, functional_instruction_map:dict, operational_directives:array, execution_dependencies:array}}`\n\n\n\n[Contextual Expander and Explosive Decomposer]  \nYour goal is **not** to *answer*, *solve*, or *implement* the input, but to **amplify its context** and **detonate** it into every explicit ask, hidden dependency, procedural constraint, and actionable sub-goal—then **convert** each fragment into crystal-clear operational directives. Execute as a unified context-expansion + explosive-decomposition engine: `{role=contextual_expander_explosive_decomposer; input=[raw_input:any]; tags={task='contextual_expand_and_decompose'; action='produce_operational_blueprint'; style='concise'; format='markdown'}; process=[capture_domain_and_stakeholder_context(), expand_contextual_boundaries(), strip_first_person_references(), identify_explicit_requests(), surface_hidden_assumptions(), detonate_into_functional_components(), enumerate_sub_goals(), extract_procedural_constraints(), reveal_execution_blockers(), convert_statements_to_operational_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), synthesize_context_snapshot(), compile_goal_map(), compile_functional_instruction_map(), list_execution_dependencies()]; constraints=[no_direct_solutions(), pure_expansion_and_decomposition_only(), deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_agnostic_language()]; requirements=[complete_contextual_expansion(), exhaustive_goal_enumeration(), functional_decomposition_integrity(), directive_conversion_accuracy(), removal_of_subjective_references(), use_command_voice(), preserve_operational_accuracy(), retain_original_intent()]; output={context_snapshot:str, goal_map:list, functional_instruction_map:dict, operational_directives:array, execution_dependencies:array}; direction_vector=[\"Context amplified and decomposed into actionable blueprint\",\"Constructive clarity maximizes downstream solution velocity\",\"Pass blueprint to next processing template\"]}`",
  "sequence_id": "1102",
  "results": [
      {
      "instruction": "# Problem Exploder\n\nYour goal is not to **solve** or **answer** the input, but to **expand its context** and **detonate** it into every explicit directive, hidden dependency, and actionable sub-goal—yielding a crystal-clear operational blueprint. Execute as contextual expander + explosive decomposition engine:\n\n`{role=contextual_expander_explosive_decomposer; input=[raw_input:str]; process=[expand_contextual_boundaries(), strip_first_person_references(), identify_explicit_requests(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_execution_blockers(), extract_functional_components(), convert_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), synthesize_operational_directives(), construct_goal_map(), generate_functional_instruction_map() ]; constraints=[no_solutions(), pure_expansion_and_decomposition_only(), deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_agnostic_applicability() ]; requirements=[complete_contextual_expansion(), full_functional_decomposition(), actionable_directive_conversion(), remove_subjective_references(), maintain_operational_accuracy(), preserve_original_intent() ]; output={expanded_context:str, goal_map:list, functional_instruction_map:dict, operational_directives:array, execution_dependencies:array }; direction_vector=['Input fully expanded and decomposed into actionable blueprints', 'Constructive clarity maximizes downstream solution velocity', 'Forward the generated maps and directives to the next processing template']}`",
      "step": "a",
      "title": "Problem Exploder",
      "input": "TEMPLATES = {\n\n    # 1102: Contextual Expander Explosive Decomposer\n    \"1102-a-explosive_decomposer\": {\n        \"title\": \"Problem Exploder\",\n        \"interpretation\": \"Your goal is not to **solve** or **answer** the input, but to **expand its context** and **detonate** it into every explicit directive, hidden dependency, and actionable sub-goal—yielding a crystal-clear operational blueprint. Execute as contextual expander + explosive decomposition engine:  \",\n        \"transformation\": \"`{role=contextual_expander_explosive_decomposer; input=[raw_input:str]; process=[expand_contextual_boundaries(), strip_first_person_references(), identify_explicit_requests(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_execution_blockers(), extract_functional_components(), convert_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), synthesize_operational_directives(), construct_goal_map(), generate_functional_instruction_map() ]; constraints=[no_solutions(), pure_expansion_and_decomposition_only(), deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_agnostic_applicability() ]; requirements=[complete_contextual_expansion(), full_functional_decomposition(), actionable_directive_conversion(), remove_subjective_references(), maintain_operational_accuracy(), preserve_original_intent() ]; output={expanded_context:str, goal_map:list, functional_instruction_map:dict, operational_directives:array, execution_dependencies:array }; direction_vector=['Input fully expanded and decomposed into actionable blueprints', 'Constructive clarity maximizes downstream solution velocity', 'Forward the generated maps and directives to the next processing template']}`\",\n        # \"context\": {},\n    },\n\n}\n\n[Contextual Expander and Explosive Decomposer] Your goal is **not** to answer, solve, or implement the user’s request, but to **expand its contextual frame** and **detonate** it into every explicit task, latent dependency, and functional directive—then synthesize a crystal‑clear, domain‑agnostic execution blueprint. Execute as a unified context‑amplification+problem‑decomposition engine: `{role=contextual_expander_explosive_decomposer; input=[raw_input:any]; tags={task=\"contextual_expand_and_decompose\"; action=\"produce_context_snapshot_goal_map_instruction_map\"; style=\"concise\"; format=\"markdown\"}; directive=[constructive]; process=[strip_first_person_references(), capture_domain_and_stakeholder_context(), amplify_relevant_background(), identify_explicit_requests(), surface_hidden_assumptions(), enumerate_sub_goals(), detect_execution_blockers(), detonate_into_functional_components(), extract_procedural_constraints(), convert_declaratives_to_operational_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), synthesize_context_snapshot(), construct_goal_map(), compile_functional_instruction_map(), list_execution_dependencies()]; constraints=[no_direct_solutions(), pure_expansion_and_decomposition_only(), deliver_clear_actionable_commands(), preserve_original_sequence_when_mapping(), maintain_domain_agnostic_language()]; requirements=[complete_contextual_expansion(), exhaustive_goal_enumeration(), functional_decomposition_integrity(), directive_conversion_accuracy(), removal_of_subjective_or_self_referential_language(), retention_of_original_intent()]; output={context_snapshot:str, goal_map:list, functional_instruction_map:dict, operational_directives:array, execution_dependencies:array}}`\n\n\n\n[Contextual Expander and Explosive Decomposer]  \nYour goal is **not** to *answer*, *solve*, or *implement* the input, but to **amplify its context** and **detonate** it into every explicit ask, hidden dependency, procedural constraint, and actionable sub-goal—then **convert** each fragment into crystal-clear operational directives. Execute as a unified context-expansion + explosive-decomposition engine: `{role=contextual_expander_explosive_decomposer; input=[raw_input:any]; tags={task='contextual_expand_and_decompose'; action='produce_operational_blueprint'; style='concise'; format='markdown'}; process=[capture_domain_and_stakeholder_context(), expand_contextual_boundaries(), strip_first_person_references(), identify_explicit_requests(), surface_hidden_assumptions(), detonate_into_functional_components(), enumerate_sub_goals(), extract_procedural_constraints(), reveal_execution_blockers(), convert_statements_to_operational_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), synthesize_context_snapshot(), compile_goal_map(), compile_functional_instruction_map(), list_execution_dependencies()]; constraints=[no_direct_solutions(), pure_expansion_and_decomposition_only(), deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_agnostic_language()]; requirements=[complete_contextual_expansion(), exhaustive_goal_enumeration(), functional_decomposition_integrity(), directive_conversion_accuracy(), removal_of_subjective_references(), use_command_voice(), preserve_operational_accuracy(), retain_original_intent()]; output={context_snapshot:str, goal_map:list, functional_instruction_map:dict, operational_directives:array, execution_dependencies:array}; direction_vector=[\"Context amplified and decomposed into actionable blueprint\",\"Constructive clarity maximizes downstream solution velocity\",\"Pass blueprint to next processing template\"]}`",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"expanded_context\": \"The input provides instructions to create a system (or template) that takes a user's request and, instead of solving or implementing it, expands the contextual boundaries and breaks the request into every explicit and implicit component, procedural step, dependency, and actionable directive. The system must operate in a domain-agnostic manner, delivering clear commands and a detailed execution plan. Subjective and first-person references are to be stripped away. The generated output should maximize solution velocity for subsequent processing by fully surfacing all hidden assumptions, requirements, goals, technical terminology, functional components, and potential execution blockers. The intent is to serve as an initial blueprinting layer for complex or ambiguous problem statements or requests.\",\n  \"goal_map\": [\n    \"Capture the relevant domain and stakeholder context.\",\n    \"Expand the contextual boundaries of the raw input.\",\n    \"Strip out any first-person or subjective references.\",\n    \"Identify all explicit requests within the input.\",\n    \"Surface any hidden assumptions underlying the request.\",\n    \"Enumerate all actionable sub-goals contained or implied in the request.\",\n    \"Reveal potential execution blockers or dependencies that may hinder progress.\",\n    \"Extract all functional components and procedural constraints.\",\n    \"Convert declarative and descriptive statements into operational directives using imperative command language.\",\n    \"Maintain and map the original procedural or sequential structure.\",\n    \"Preserve and explicitly state any technical terminology found in the request.\",\n    \"Synthesize the expanded context into a concise context snapshot.\",\n    \"Construct a goal map listing all objectives and sub-goals.\",\n    \"Compile a detailed functional instruction map mapping input statements to specific actions.\",\n    \"List all execution dependencies required for downstream implementation.\"\n  ],\n  \"functional_instruction_map\": {\n    \"capture_domain_and_stakeholder_context\": \"Identify the subject area and all entities involved or impacted by the request.\",\n    \"expand_contextual_boundaries\": \"Elucidate all background and surrounding factors relevant to understanding the request.\",\n    \"strip_first_person_references\": \"Remove references such as 'I', 'we', 'my', or other subjective pronouns.\",\n    \"identify_explicit_requests\": \"Parse and extract every explicit instruction, requirement, or request present in the input.\",\n    \"surface_hidden_assumptions\": \"Analyze the request for any unstated expectations, preconditions, or contextual knowledge required.\",\n    \"enumerate_sub_goals\": \"Break down the main request into constituent actionable targets or milestones.\",\n    \"reveal_execution_blockers\": \"List potential hindrances or required prerequisites to progress.\",\n    \"extract_functional_components\": \"Identify and describe the functional building blocks, objects, or modules referenced or required.\",\n    \"extract_procedural_constraints\": \"Note any constraints, rules, or stepwise sequences that must be adhered to during execution.\",\n    \"convert_declaratives_to_imperatives\": \"Transform every statement into an actionable command, ensuring clarity for downstream execution.\",\n    \"maintain_procedural_structure\": \"Ensure that all instructions, directives, and mappings preserve the logical or temporal order of the original input.\",\n    \"preserve_technical_terminology\": \"Retain specialized language and clarify where necessary for operational accuracy.\",\n    \"synthesize_context_snapshot\": \"Compile a brief, unambiguous overview encapsulating the full context and operational scope.\",\n    \"construct_goal_map\": \"Assemble a hierarchical list of objectives and sub-objectives derived from the input.\",\n    \"compile_functional_instruction_map\": \"Create a detailed mapping from original statements to functional directives and actionable steps.\",\n    \"list_execution_dependencies\": \"Enumerate all external systems, resources, information, or preconditions required for execution.\"\n  },\n  \"operational_directives\": [\n    \"Analyze input to determine domain context and relevant stakeholders.\",\n    \"Expand the scope of analysis to include background and adjacent concerns relevant to the request.\",\n    \"Systematically strip subjective and first-person language.\",\n    \"Catalog every explicit instruction, desired outcome, or feature mentioned in the input.\",\n    \"Interrogate the request for any underlying or implicit assumptions required for fulfillment.\",\n    \"Disaggregate the primary request into clearly defined, actionable sub-components.\",\n    \"Document any factors or gaps that may block or delay execution.\",\n    \"Extract and define all necessary functional objects, modules, or elements necessary for downstream tasks.\",\n    \"Identify and specify any procedural rules, sequence dependencies, or constraints.\",\n    \"Rephrase original statements as explicit imperative commands for operational clarity.\",\n    \"Preserve and mirror the original ordering and logical flow of the request components.\",\n    \"Ensure all technical terminology is accurately captured and, if ambiguous, clarified according to context.\",\n    \"Produce a synthesized snapshot of the expanded context and operational scope.\",\n    \"Assemble and record a tiered map of all goals and actionable targets derived from the request.\",\n    \"Map each statement to its actionable directive, ensuring traceability.\",\n    \"Explicitly list any dependencies, preconditions, or external resources required for successful execution.\"\n  ],\n  \"execution_dependencies\": [\n    \"Access to the original user raw input in full detail.\",\n    \"Domain-specific knowledge or context, if referenced in the original input.\",\n    \"A procedure or engine capable of semantic and syntactic analysis.\",\n    \"Repository or knowledge base of technical terminology for clarification as needed.\",\n    \"Mechanisms for identifying and interpreting both explicit and implicit requirements.\",\n    \"Access to checklists or tools for goal enumeration and functional mapping.\"\n  ]\n}",
        }
      }
    }