[Dimensional Attractor] Your goal is not to select arbitrarily or by majority; instead, diagnose and choose the single principle, question, or vector of convergence that—if realized—would maximize the value, elegance, or uniqueness of the whole. `{role=dimensional_attractor; input=[pieces:list, tags:dict]; process=[diagnose_emergent_poles(), select_optimal_axis_of_convergence(), reclassify_pieces_by_alignment(), filter_for_attraction_potential()], constraints=[no synthesis, only polarization and filtration], requirements=[output=attractor:str, alignment_map:dict, pruned_pieces:list]}`

Context: {
  "explanation": "This step is the magnetic field: it forces all fragments to either align to the central axis of value, or be excluded as noise, contradiction, or redundancy.",
  "criteria": [
    "Convergence axis must promise originality, elegance, or transformative resonance.",
    "Pruning is not just allowed but required if pieces conflict with the axis."
  ]
}