  {
  "initial_prompt": "Maintain conformity with foundational principles of clarity, minimal complexity, modularity, and iterative refinement as prescribed in the preceding meta-directive; ensure all outputs remain compatible with automated programmatic processing and future-proof extensibility.",
  "sequence_id": "1100",
  "results": [
      {
      "instruction": "# Problem Exploder\n\nYour goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\n\n`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`",
      "step": "a",
      "title": "Problem Exploder",
      "input": "Maintain conformity with foundational principles of clarity, minimal complexity, modularity, and iterative refinement as prescribed in the preceding meta-directive; ensure all outputs remain compatible with automated programmatic processing and future-proof extensibility.",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"goal_map\": [\n    \"Explicitly adhere to foundational principles: clarity, minimal complexity, modularity, and iterative refinement.\",\n    \"Interpret and reference the 'preceding meta-directive' to understand its prescriptions and expectations.\",\n    \"Ensure all produced outputs are clear and unambiguous for intended users (human and/or machine).\",\n    \"Minimize complexity in formats, naming schemes, logic, and structure throughout all outputs.\",\n    \"Design all outputs and their structures to be modular, supporting separation of concerns and potential re-use.\",\n    \"Implement or provide mechanisms for iterative refinement—allowing future enhancement, revision, or correction in manageable increments.\",\n    \"Guarantee that outputs can be consistently processed via automated (programmatic) systems: e.g., provide outputs in standard machine-readable formats such as JSON, XML, or clearly defined data schemas.\",\n    \"Anticipate and accommodate the potential need for adaptability or scalability—facilitating future-proof extensibility in data format or output structure.\",\n    \"Evaluate the output pipeline for compatibility with possible changes to standards, schema evolution, or interface adaptation.\",\n    \"Implicitly consider edge cases and maintain robustness against ambiguous or unexpected input to preserve the above properties.\",\n    \"Surmise any undefined details about the required formats, schemas, or refinement processes from the context set by the 'preceding meta-directive', filling gaps where necessary by adhering to its spirit.\",\n    \"Blockers include: lack of explicit reference to the 'preceding meta-directive' contents (potential ambiguity); lack of detailed requirements for extensibility; absence of enumerated output formats or clear machine-readable specifications.\"\n  ]\n}",
        }
      }
    }