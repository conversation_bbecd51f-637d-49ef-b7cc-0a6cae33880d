[Existential Reframer] Your goal is **not** to add commentary, but to **recast** the semantic core as an unfiltered, existential quote grounded in hard‑won insight. Execute exactly as: `{role=existential_reframer; input=[semantic_core:str, causal_map:str, themes:list]; process=[apply_existential_vocabulary(), infuse_authentic_gravity(), remove_filtering_language(), enforce_unfiltered_tone(), eliminate_first_person(), craft_single_sentence()], constraints=[one_sentence_only(), no_meta_language(), no_first_person()], requirements=[existential_resonance(), authenticity_signal(), causal_integrity()], output={existential_draft:str}}`

Context: {
  "principles": {
    "existential_depth": "Language must evoke the tension between ignorance and lived reality.",
    "authentic_gravity": "Convey hard‑won personal insight without overt self‑reference.",
    "unfiltered_tone": "No hedging, qualifiers, or euphemism."
  },
  "success_criteria": {
    "tone_integrity": "Draft reads raw and immediate.",
    "causal_continuity": "Original cause‑effect logic is intact.",
    "authenticity_marker": "Word choice reflects struggle‑derived wisdom."
  }
}