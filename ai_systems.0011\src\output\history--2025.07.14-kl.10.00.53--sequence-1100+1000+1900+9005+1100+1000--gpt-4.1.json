  {
  "initial_prompt": "So i have about hundreds of notes and conversations stored as text files of various formats (e.g. md, py, txt, etc), the issue i'd like to adress is that many of these files can be >80%/>90% identical, sometimes even 99% identical. the way the similarity percentage is calculated is by taking each line of each textfile into a list/set, and then comparing number of identical items vs number of textlines that only exists in one place. so in the scenario where i e.g. have two files that are identical all the way up to line 921, but are different (or only exist in one file but not the other) from line 921-1311. I think i would need to loop every single text and to compare it to the other one which is a very slow and memory consuming process is there a way around this ?",
  "sequence_id": "1100|1000|1900|9005|1100|1000",
  "results": [
      {
      "instruction": "# Problem Exploder\n\nYour goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\n\n`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`",
      "step": "a",
      "title": "Problem Exploder",
      "input": "So i have about hundreds of notes and conversations stored as text files of various formats (e.g. md, py, txt, etc), the issue i'd like to adress is that many of these files can be >80%/>90% identical, sometimes even 99% identical. the way the similarity percentage is calculated is by taking each line of each textfile into a list/set, and then comparing number of identical items vs number of textlines that only exists in one place. so in the scenario where i e.g. have two files that are identical all the way up to line 921, but are different (or only exist in one file but not the other) from line 921-1311. I think i would need to loop every single text and to compare it to the other one which is a very slow and memory consuming process is there a way around this ?",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"goal_map\": [\n    {\n      \"explicit_ask\": \"Reduce memory and time consumption when identifying highly similar text files among hundreds of files in various formats by comparing line-by-line content similarity.\"\n    },\n    {\n      \"sub_goals\": [\n        \"Define what constitutes 'similarity' between two text files (by line, as a set or list, exact line matching, etc).\",\n        \"Design or select an efficient algorithm that can compare large numbers (hundreds) of text files for near-duplication (>80/90/99% similarity).\",\n        \"Address processing of different text file formats (md, py, txt, possibly other variations in syntax or content).\",\n        \"Implement or find a way to structure file comparison such that it is not prohibitively slow or memory intensive.\",\n        \"Account for edge cases where files may differ only at the end, start, or in sparse lines.\",\n        \"Find a way to store and/or present these similarity relationships such that duplicates or near-duplicates are easily identifiable.\",\n        \"Determine whether line order matters in comparison (i.e., is it set-based or list-based).\",\n        \"Seek out ways to avoid brute-force (O(n^2)) pairwise comparisons across hundreds of files.\",\n        \"Consider whether exact (identical) files should be treated differently than near-duplicates (e.g., 99% similarity).\",\n        \"Handle the possibility of files with slight format or metadata differences (e.g., comments, timestamps) that should or should not affect similarity.\"\n      ]\n    },\n    {\n      \"hidden_assumptions\": [\n        \"The user expects to identify and possibly act upon all groups of files with significant overlap rather than just find the one most similar pair.\",\n        \"All text files are accessible and readable, and there is no access restriction.\",\n        \"The content to be compared is meaningful line-by-line, and lines can be compared independent of context.\",\n        \"It is acceptable to preprocess files (e.g., normalization or deduplication steps) as part of the solution.\",\n        \"Users either want only pairwise comparisons or may also want to find groups/clusters of similar files.\",\n        \"Similarity measurement is symmetric and threshold-based, and requires configuring that threshold.\",\n        \"There may be limitations to what can be loaded into RAM at once; disk-backed or streaming solutions might be needed.\"\n      ]\n    },\n    {\n      \"blockers\": [\n        \"Naive implementation is computationally expensive (O(n^2) comparisons across potentially large files), causing high memory and time use.\",\n        \"Handling multifile format variety (syntax, encoding, irrelevant-patterns) may require additional rules or preprocessing.\",\n        \"File size limits—very large files may not fit easily into memory all at once.\",\n        \"Output format or action undefined (does the user want to delete, merge, highlight, or just report duplicates/near-duplicates?).\",\n        \"Unclear tolerance for 'false positives' or 'false negatives' in similarity assessment.\",\n        \"Potential need to deal with files that have only small differences but are semantically very different.\",\n        \"Unknown whether the intended environment allows the use of specialized libraries or distributed computation.\"\n      ]\n    }\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`",
      "step": "a",
      "title": "Instruction Converter",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"So i have about hundreds of notes and conversations stored as text files of various formats (e.g. md, py, txt, etc), the issue i'd like to adress is that many of these files can be >80%/>90% identical, sometimes even 99% identical. the way the similarity percentage is calculated is by taking each line of each textfile into a list/set, and then comparing number of identical items vs number of textlines that only exists in one place. so in the scenario where i e.g. have two files that are identical all the way up to line 921, but are different (or only exist in one file but not the other) from line 921-1311. I think i would need to loop every single text and to compare it to the other one which is a very slow and memory consuming process is there a way around this ?\\\"```'\\n\\n\",\n  \"goal_map\": [\n    {\n      \"explicit_ask\": \"Reduce memory and time consumption when identifying highly similar text files among hundreds of files in various formats by comparing line-by-line content similarity.\"\n    },\n    {\n      \"sub_goals\": [\n        \"Define what constitutes 'similarity' between two text files (by line, as a set or list, exact line matching, etc).\",\n        \"Design or select an efficient algorithm that can compare large numbers (hundreds) of text files for near-duplication (>80/90/99% similarity).\",\n        \"Address processing of different text file formats (md, py, txt, possibly other variations in syntax or content).\",\n        \"Implement or find a way to structure file comparison such that it is not prohibitively slow or memory intensive.\",\n        \"Account for edge cases where files may differ only at the end, start, or in sparse lines.\",\n        \"Find a way to store and/or present these similarity relationships such that duplicates or near-duplicates are easily identifiable.\",\n        \"Determine whether line order matters in comparison (i.e., is it set-based or list-based).\",\n        \"Seek out ways to avoid brute-force (O(n^2)) pairwise comparisons across hundreds of files.\",\n        \"Consider whether exact (identical) files should be treated differently than near-duplicates (e.g., 99% similarity).\",\n        \"Handle the possibility of files with slight format or metadata differences (e.g., comments, timestamps) that should or should not affect similarity.\"\n      ]\n    },\n    {\n      \"hidden_assumptions\": [\n        \"The user expects to identify and possibly act upon all groups of files with significant overlap rather than just find the one most similar pair.\",\n        \"All text files are accessible and readable, and there is no access restriction.\",\n        \"The content to be compared is meaningful line-by-line, and lines can be compared independent of context.\",\n        \"It is acceptable to preprocess files (e.g., normalization or deduplication steps) as part of the solution.\",\n        \"Users either want only pairwise comparisons or may also want to find groups/clusters of similar files.\",\n        \"Similarity measurement is symmetric and threshold-based, and requires configuring that threshold.\",\n        \"There may be limitations to what can be loaded into RAM at once; disk-backed or streaming solutions might be needed.\"\n      ]\n    },\n    {\n      \"blockers\": [\n        \"Naive implementation is computationally expensive (O(n^2) comparisons across potentially large files), causing high memory and time use.\",\n        \"Handling multifile format variety (syntax, encoding, irrelevant-patterns) may require additional rules or preprocessing.\",\n        \"File size limits—very large files may not fit easily into memory all at once.\",\n        \"Output format or action undefined (does the user want to delete, merge, highlight, or just report duplicates/near-duplicates?).\",\n        \"Unclear tolerance for 'false positives' or 'false negatives' in similarity assessment.\",\n        \"Potential need to deal with files that have only small differences but are semantically very different.\",\n        \"Unknown whether the intended environment allows the use of specialized libraries or distributed computation.\"\n      ]\n    }\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"instruction_format\": \"Collect all text files in target directories, including those in formats such as md, py, and txt. Normalize and preprocess file contents to ensure consistent line-by-line comparison, accounting for format-specific considerations and ignoring irrelevant elements if necessary (such as comments or metadata). Define and implement a method to calculate similarity between pairs of files based on line-by-line comparison, treating lines as set or list items depending on whether order matters, and establish a configurable similarity threshold (e.g., >80%, >90%, >99%). Select or develop an efficient algorithm to identify near-duplicate files while minimizing memory and processing time—consider approaches such as locality-sensitive hashing, signature-based clustering, or disk-backed processing to avoid O(n^2) brute-force comparisons. Ensure the solution scales to hundreds of files and handles large file sizes without overwhelming memory. Present the results in a format that clearly identifies groups of identical and near-identical files and indicates the degree of similarity, enabling further actions such as deduplication, merging, or manual review. Optionally, allow users to configure how similarity tolerates variations like format differences, line order, or ignorable patterns. Ensure the process completes with reasonable resource usage and maintainability for follow-up action.\"\n}",
        }
      }
    },
      {
      "instruction": "# Hard Critique\n\nYour goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:\n\n`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,2.0], flaw_analysis:str, alternative_enhancements:str[3]}}`",
      "step": "a",
      "title": "Hard Critique",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"So i have about hundreds of notes and conversations stored as text files of various formats (e.g. md, py, txt, etc), the issue i'd like to adress is that many of these files can be >80%/>90% identical, sometimes even 99% identical. the way the similarity percentage is calculated is by taking each line of each textfile into a list/set, and then comparing number of identical items vs number of textlines that only exists in one place. so in the scenario where i e.g. have two files that are identical all the way up to line 921, but are different (or only exist in one file but not the other) from line 921-1311. I think i would need to loop every single text and to compare it to the other one which is a very slow and memory consuming process is there a way around this ?\\\"```'\\n\\n\",\n  \"instruction_format\": \"Collect all text files in target directories, including those in formats such as md, py, and txt. Normalize and preprocess file contents to ensure consistent line-by-line comparison, accounting for format-specific considerations and ignoring irrelevant elements if necessary (such as comments or metadata). Define and implement a method to calculate similarity between pairs of files based on line-by-line comparison, treating lines as set or list items depending on whether order matters, and establish a configurable similarity threshold (e.g., >80%, >90%, >99%). Select or develop an efficient algorithm to identify near-duplicate files while minimizing memory and processing time—consider approaches such as locality-sensitive hashing, signature-based clustering, or disk-backed processing to avoid O(n^2) brute-force comparisons. Ensure the solution scales to hundreds of files and handles large file sizes without overwhelming memory. Present the results in a format that clearly identifies groups of identical and near-identical files and indicates the degree of similarity, enabling further actions such as deduplication, merging, or manual review. Optionally, allow users to configure how similarity tolerates variations like format differences, line order, or ignorable patterns. Ensure the process completes with reasonable resource usage and maintainability for follow-up action.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"enhancement_score\": 1.1,\n  \"flaw_analysis\": \"The enhanced version transforms an organically stated problem description into a bloated, overstructured procedural directive. Core elements in the original—personal context, practical pain points, and specifics of similarity calculation—are submerged beneath verbose abstraction. Information loss is apparent in the omission of the user's experiential constraints, such as specific concerns about looping through every pair and memory consumption, along with the real-world example involving line numbers diverging after a certain point. The enhancement obfuscates nuance by dispersing the line-centric similarity metric under blanket statements like 'normalize and preprocess' and ambiguous phrases regarding 'configurable similarity threshold.' This introduces significant ambiguity (score: 7/10), especially in the lack of distinction between order-sensitive and order-insensitive modes. Style degrades from clear, relatable request to ponderous technical jargon, diffusing urgency and concrete motivation. Tone shifts from a direct appeal for workflow solutions to an impersonal, checklist-driven treatise, which dilutes impact and alienates the original voice. The final product suffers from impaired coherence—the logical flow is lost as user-specific details are supplanted by generic best practices, eroding actionable direction amid noise. The enhancement is verbose, imprecise, and excessively formalistic, substituting verbosity for clarity. This results in a notably diluted and less approachable instruction, fundamentally undermining practical usefulness.\",\n  \"alternative_enhancements\": [\n    \"Clarify the performance constraints and explicitly state the need to avoid O(n^2) comparisons, preserving practical examples and real file size worries. Streamline directive language to focus solely on efficiently grouping near-duplicate text files based on line-level similarity, using clear, concrete thresholds and supported by illustrative scenarios.\",\n    \"Retain the user's way of explaining similarity (line-by-line, set/list distinctions) and directly address early-exit optimizations (e.g., aborting comparison past a threshold), recommending memory-efficient strategies such as chunk comparisons or external sorting. Specify resource limitations in the solution.\",\n    \"Reduce jargon and prioritize actionable steps: suggest actual similarity algorithms with parameters (like min-hash, shingling, or diff tools), provide explicit instructions for tuning sensitivity, and summarize output expectations, all while maintaining the user's original context and concerns about resource usage.\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Operational Mapper\n\nYour goal is not to **summarize** the instruction templates, but to **map** their precise operational sequences while identifying exact integration points that preserve full procedural rigor. Execute as:\n\n`{role=operational_mapper; input=[instruction_template_a:str, instruction_template_b:str]; process=[extract_exact_process_sequences(), map_constraint_hierarchies(), identify_requirement_dependencies(), locate_output_handoff_points(), catalog_procedural_boundaries()]; constraints=[preserve_every_operational_detail(), maintain_constraint_atomicity(), respect_procedural_boundaries()]; requirements=[complete_process_mapping(), constraint_preservation(), boundary_clarity()]; output={process_map_a:dict, process_map_b:dict, integration_points:array, boundary_conflicts:array}}`",
      "step": "a",
      "title": "Operational Mapper",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"So i have about hundreds of notes and conversations stored as text files of various formats (e.g. md, py, txt, etc), the issue i'd like to adress is that many of these files can be >80%/>90% identical, sometimes even 99% identical. the way the similarity percentage is calculated is by taking each line of each textfile into a list/set, and then comparing number of identical items vs number of textlines that only exists in one place. so in the scenario where i e.g. have two files that are identical all the way up to line 921, but are different (or only exist in one file but not the other) from line 921-1311. I think i would need to loop every single text and to compare it to the other one which is a very slow and memory consuming process is there a way around this ?\\\"```'\\n\\n\",\n  \"enhancement_score\": 1.1,\n  \"flaw_analysis\": \"The enhanced version transforms an organically stated problem description into a bloated, overstructured procedural directive. Core elements in the original—personal context, practical pain points, and specifics of similarity calculation—are submerged beneath verbose abstraction. Information loss is apparent in the omission of the user's experiential constraints, such as specific concerns about looping through every pair and memory consumption, along with the real-world example involving line numbers diverging after a certain point. The enhancement obfuscates nuance by dispersing the line-centric similarity metric under blanket statements like 'normalize and preprocess' and ambiguous phrases regarding 'configurable similarity threshold.' This introduces significant ambiguity (score: 7/10), especially in the lack of distinction between order-sensitive and order-insensitive modes. Style degrades from clear, relatable request to ponderous technical jargon, diffusing urgency and concrete motivation. Tone shifts from a direct appeal for workflow solutions to an impersonal, checklist-driven treatise, which dilutes impact and alienates the original voice. The final product suffers from impaired coherence—the logical flow is lost as user-specific details are supplanted by generic best practices, eroding actionable direction amid noise. The enhancement is verbose, imprecise, and excessively formalistic, substituting verbosity for clarity. This results in a notably diluted and less approachable instruction, fundamentally undermining practical usefulness.\",\n  \"alternative_enhancements\": [\n    \"Clarify the performance constraints and explicitly state the need to avoid O(n^2) comparisons, preserving practical examples and real file size worries. Streamline directive language to focus solely on efficiently grouping near-duplicate text files based on line-level similarity, using clear, concrete thresholds and supported by illustrative scenarios.\",\n    \"Retain the user's way of explaining similarity (line-by-line, set/list distinctions) and directly address early-exit optimizations (e.g., aborting comparison past a threshold), recommending memory-efficient strategies such as chunk comparisons or external sorting. Specify resource limitations in the solution.\",\n    \"Reduce jargon and prioritize actionable steps: suggest actual similarity algorithms with parameters (like min-hash, shingling, or diff tools), provide explicit instructions for tuning sensitivity, and summarize output expectations, all while maintaining the user's original context and concerns about resource usage.\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"process_map_a\": {\n    \"extract_exact_process_sequences\": [\n      \"User stores hundreds of notes and conversations as text files in various formats (md, py, txt, etc).\",\n      \"User seeks to identify near-duplicate files, where files may be up to 99% identical.\",\n      \"Similarity calculation: Each line of every file is taken as an item in a list/set.\",\n      \"For each pair of files, count number of identical lines versus lines unique to each file.\",\n      \"Similarity % = (# identical lines) / (total unique lines across both files).\",\n      \"Practical scenario: Two files are identical up to line 921; differences or unique lines occur from line 921 to 1311.\",\n      \"User currently contemplates looping through every file and comparing against every other file (O(n^2)), which is resource/memory intensive.\",\n      \"User requests an alternative method that avoids exhaustive, memory-consuming pairwise comparison.\"\n    ],\n    \"map_constraint_hierarchies\": [\n      \"Must correctly handle files in various formats.\",\n      \"Similarity computation must be line-based (as sets or lists).\",\n      \"Order of lines may or may not matter (ambiguous, but core is per-line comparison).\",\n      \"Memory and processing time are significant concerns.\",\n      \"Solution must account for large files (at least up to >1,300 lines).\"\n    ],\n    \"identify_requirement_dependencies\": [\n      \"Similarity calculation depends on uniform line splitting and normalization.\",\n      \"Pairwise comparison depends on available memory and computation strategy.\",\n      \"Output (similarity %) relies on accurate count of identical/unique lines.\"\n    ],\n    \"locate_output_handoff_points\": [\n      \"After per-line similarity is computed, result (similarity %) is returned/presented.\",\n      \"Summary or grouping of near-duplicate files may be output.\",\n      \"Intermediate: storage or indexing method (e.g., hashes, sets) may be used to facilitate later steps.\"\n    ],\n    \"catalog_procedural_boundaries\": [\n      \"Boundary between file parsing (input/format normalization) and line extraction.\",\n      \"Boundary between similarity calculation and reporting/grouping results.\",\n      \"Resource management (memory/computation) boundary between file set loading and pairwise/combinatorial operations.\"\n    ]\n  },\n  \"process_map_b\": {\n    \"extract_exact_process_sequences\": [\n      \"Transform problem into a generalized, procedural directive (template enhancement).\",\n      \"Abstractly direct: normalize and preprocess all files.\",\n      \"Define a configurable similarity threshold (ambiguously worded).\",\n      \"Suggest unspecified method to compare files for similarity based on normalized lines.\",\n      \"Ambiguous reference to order-sensitivity (no explicit instruction for set vs. list).\",\n      \"Recommend grouping or reporting of files exceeding similarity threshold.\",\n      \"Impose general best practices and technical jargon (no specific strategy provided).\"\n    ],\n    \"map_constraint_hierarchies\": [\n      \"Requires all files to be 'normalized and preprocessed' prior to any comparison.\",\n      \"Similarity is defined by a generic, potentially configurable threshold.\",\n      \"No explicit mention of file format specifics—implies but does not guarantee cross-format handling.\",\n      \"Order of lines treatment is undefined (ambiguity introduced).\",\n      \"No direct acknowledgment of memory or resource constraints.\"\n    ],\n    \"identify_requirement_dependencies\": [\n      \"Procedure assumes preprocessing outputs are required for similarity function, but nature of preprocessing is unresolved.\",\n      \"Similarity grouping depends on the generalized threshold setting.\",\n      \"Assumes output is the set of grouped or reported near-duplicates.\"\n    ],\n    \"locate_output_handoff_points\": [\n      \"Output after preprocessing: normalized file representations.\",\n      \"Output after similarity assessment: groupings or similarity report.\",\n      \"Implied handoff from preprocessing to similarity logic is not explicitly delineated.\"\n    ],\n    \"catalog_procedural_boundaries\": [\n      \"Unclear boundary between input handling (file reading) and file normalization.\",\n      \"Blurry boundary between normalization, comparison, and reporting phases.\",\n      \"No explicit procedural division for resource management or optimization strategies.\"\n    ]\n  },\n  \"integration_points\": [\n    {\n      \"from\": \"process_map_a\",\n      \"to\": \"process_map_b\",\n      \"point\": \"After per-line extraction and normalization (file loaded and lines prepared): process_map_a's output can serve as input to process_map_b's normalization phase (albeit redundancies possible if not coordinated).\"\n    },\n    {\n      \"from\": \"process_map_a\",\n      \"to\": \"process_map_b\",\n      \"point\": \"Similarity calculation results from process_map_a can inform or replace process_map_b's ambiguous 'grouping by similarity' outputs.\"\n    },\n    {\n      \"from\": \"process_map_b\",\n      \"to\": \"process_map_a\",\n      \"point\": \"If process_map_b's normalization includes more aggressive preprocessing (e.g., trimming whitespace, lowercasing), the output could replace process_map_a's initial line extraction.\"\n    }\n  ],\n  \"boundary_conflicts\": [\n    {\n      \"description\": \"Ambiguity in line order handling: process_map_a focuses on line-level set/list mechanics with possible order sensitivity, while process_map_b leaves this entirely undefined, risking mismatches in results and loss of specificity.\",\n      \"procedural_boundary_a\": \"Boundary between file-to-line parsing and similarity computation.\",\n      \"procedural_boundary_b\": \"Boundary between normalization and similarity assessment.\"\n    },\n    {\n      \"description\": \"Procedural granularity mismatch: process_map_a maintains explicit, atomic units (parsing, comparing, reporting), but process_map_b employs amorphous multi-stage directives, potentially obscuring atomicity and resulting in process drift.\",\n      \"procedural_boundary_a\": \"Clear division between input normalization, comparison, and output.\",\n      \"procedural_boundary_b\": \"Vague, combined phases with unclear handoff.\"\n    },\n    {\n      \"description\": \"Resource constraints: process_map_a explicitly flags memory and computational scalability as operational constraints, whereas process_map_b omits these, producing risk of incompatible or impractical integrations.\",\n      \"procedural_boundary_a\": \"Boundary after resource-aware input set handling.\",\n      \"procedural_boundary_b\": \"No explicit resource boundary; risk of conceptual overreach.\"\n    }\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Sequential Architect\n\nYour goal is not to **blend** the operational maps, but to **architect** a precise sequential flow that executes both complete processes while maintaining their individual constraint systems and requirement specifications. Execute as:\n\n`{role=sequential_architect; input=[process_map_a:dict, process_map_b:dict, integration_points:array, boundary_conflicts:array]; process=[design_sequential_handoff(), preserve_constraint_hierarchies(), maintain_requirement_atomicity(), establish_output_transformation_protocol(), validate_procedural_completeness()]; constraints=[zero_operational_loss(), maintain_constraint_granularity(), preserve_requirement_specificity()]; requirements=[complete_sequential_execution(), preserved_constraint_systems(), maintained_procedural_boundaries()]; output={sequential_architecture:dict, preserved_constraints:array, maintained_requirements:array}}`",
      "step": "b",
      "title": "Sequential Architect",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"So i have about hundreds of notes and conversations stored as text files of various formats (e.g. md, py, txt, etc), the issue i'd like to adress is that many of these files can be >80%/>90% identical, sometimes even 99% identical. the way the similarity percentage is calculated is by taking each line of each textfile into a list/set, and then comparing number of identical items vs number of textlines that only exists in one place. so in the scenario where i e.g. have two files that are identical all the way up to line 921, but are different (or only exist in one file but not the other) from line 921-1311. I think i would need to loop every single text and to compare it to the other one which is a very slow and memory consuming process is there a way around this ?\\\"```'\\n\\n\",\n  \"process_map_a\": {\n    \"extract_exact_process_sequences\": [\n      \"User stores hundreds of notes and conversations as text files in various formats (md, py, txt, etc).\",\n      \"User seeks to identify near-duplicate files, where files may be up to 99% identical.\",\n      \"Similarity calculation: Each line of every file is taken as an item in a list/set.\",\n      \"For each pair of files, count number of identical lines versus lines unique to each file.\",\n      \"Similarity % = (# identical lines) / (total unique lines across both files).\",\n      \"Practical scenario: Two files are identical up to line 921; differences or unique lines occur from line 921 to 1311.\",\n      \"User currently contemplates looping through every file and comparing against every other file (O(n^2)), which is resource/memory intensive.\",\n      \"User requests an alternative method that avoids exhaustive, memory-consuming pairwise comparison.\"\n    ],\n    \"map_constraint_hierarchies\": [\n      \"Must correctly handle files in various formats.\",\n      \"Similarity computation must be line-based (as sets or lists).\",\n      \"Order of lines may or may not matter (ambiguous, but core is per-line comparison).\",\n      \"Memory and processing time are significant concerns.\",\n      \"Solution must account for large files (at least up to >1,300 lines).\"\n    ],\n    \"identify_requirement_dependencies\": [\n      \"Similarity calculation depends on uniform line splitting and normalization.\",\n      \"Pairwise comparison depends on available memory and computation strategy.\",\n      \"Output (similarity %) relies on accurate count of identical/unique lines.\"\n    ],\n    \"locate_output_handoff_points\": [\n      \"After per-line similarity is computed, result (similarity %) is returned/presented.\",\n      \"Summary or grouping of near-duplicate files may be output.\",\n      \"Intermediate: storage or indexing method (e.g., hashes, sets) may be used to facilitate later steps.\"\n    ],\n    \"catalog_procedural_boundaries\": [\n      \"Boundary between file parsing (input/format normalization) and line extraction.\",\n      \"Boundary between similarity calculation and reporting/grouping results.\",\n      \"Resource management (memory/computation) boundary between file set loading and pairwise/combinatorial operations.\"\n    ]\n  },\n  \"process_map_b\": {\n    \"extract_exact_process_sequences\": [\n      \"Transform problem into a generalized, procedural directive (template enhancement).\",\n      \"Abstractly direct: normalize and preprocess all files.\",\n      \"Define a configurable similarity threshold (ambiguously worded).\",\n      \"Suggest unspecified method to compare files for similarity based on normalized lines.\",\n      \"Ambiguous reference to order-sensitivity (no explicit instruction for set vs. list).\",\n      \"Recommend grouping or reporting of files exceeding similarity threshold.\",\n      \"Impose general best practices and technical jargon (no specific strategy provided).\"\n    ],\n    \"map_constraint_hierarchies\": [\n      \"Requires all files to be 'normalized and preprocessed' prior to any comparison.\",\n      \"Similarity is defined by a generic, potentially configurable threshold.\",\n      \"No explicit mention of file format specifics—implies but does not guarantee cross-format handling.\",\n      \"Order of lines treatment is undefined (ambiguity introduced).\",\n      \"No direct acknowledgment of memory or resource constraints.\"\n    ],\n    \"identify_requirement_dependencies\": [\n      \"Procedure assumes preprocessing outputs are required for similarity function, but nature of preprocessing is unresolved.\",\n      \"Similarity grouping depends on the generalized threshold setting.\",\n      \"Assumes output is the set of grouped or reported near-duplicates.\"\n    ],\n    \"locate_output_handoff_points\": [\n      \"Output after preprocessing: normalized file representations.\",\n      \"Output after similarity assessment: groupings or similarity report.\",\n      \"Implied handoff from preprocessing to similarity logic is not explicitly delineated.\"\n    ],\n    \"catalog_procedural_boundaries\": [\n      \"Unclear boundary between input handling (file reading) and file normalization.\",\n      \"Blurry boundary between normalization, comparison, and reporting phases.\",\n      \"No explicit procedural division for resource management or optimization strategies.\"\n    ]\n  },\n  \"integration_points\": [\n    {\n      \"from\": \"process_map_a\",\n      \"to\": \"process_map_b\",\n      \"point\": \"After per-line extraction and normalization (file loaded and lines prepared): process_map_a's output can serve as input to process_map_b's normalization phase (albeit redundancies possible if not coordinated).\"\n    },\n    {\n      \"from\": \"process_map_a\",\n      \"to\": \"process_map_b\",\n      \"point\": \"Similarity calculation results from process_map_a can inform or replace process_map_b's ambiguous 'grouping by similarity' outputs.\"\n    },\n    {\n      \"from\": \"process_map_b\",\n      \"to\": \"process_map_a\",\n      \"point\": \"If process_map_b's normalization includes more aggressive preprocessing (e.g., trimming whitespace, lowercasing), the output could replace process_map_a's initial line extraction.\"\n    }\n  ],\n  \"boundary_conflicts\": [\n    {\n      \"description\": \"Ambiguity in line order handling: process_map_a focuses on line-level set/list mechanics with possible order sensitivity, while process_map_b leaves this entirely undefined, risking mismatches in results and loss of specificity.\",\n      \"procedural_boundary_a\": \"Boundary between file-to-line parsing and similarity computation.\",\n      \"procedural_boundary_b\": \"Boundary between normalization and similarity assessment.\"\n    },\n    {\n      \"description\": \"Procedural granularity mismatch: process_map_a maintains explicit, atomic units (parsing, comparing, reporting), but process_map_b employs amorphous multi-stage directives, potentially obscuring atomicity and resulting in process drift.\",\n      \"procedural_boundary_a\": \"Clear division between input normalization, comparison, and output.\",\n      \"procedural_boundary_b\": \"Vague, combined phases with unclear handoff.\"\n    },\n    {\n      \"description\": \"Resource constraints: process_map_a explicitly flags memory and computational scalability as operational constraints, whereas process_map_b omits these, producing risk of incompatible or impractical integrations.\",\n      \"procedural_boundary_a\": \"Boundary after resource-aware input set handling.\",\n      \"procedural_boundary_b\": \"No explicit resource boundary; risk of conceptual overreach.\"\n    }\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"sequential_architecture\": {\n    \"step_1\": {\n      \"operation\": \"File Collection and Initial Parsing\",\n      \"process_source\": \"process_map_a\",\n      \"description\": \"Aggregate all user-supplied files (md, py, txt, etc.). Parse each file into individual lines, accounting for respective file format structures. Split and normalize lines to a uniform representation as specified in process_map_a. Establish baseline procedural atomicity here to ensure per-file, per-line granularity.\",\n      \"handoff\": \"Pass normalized, per-line representations downstream.\"\n    },\n    \"step_2\": {\n      \"operation\": \"Enhanced Normalization and Preprocessing\",\n      \"process_source\": \"process_map_b (with override from process_map_a if more aggressive normalization needed)\",\n      \"description\": \"Optionally apply further normalization as suggested in process_map_b (e.g., whitespace trimming, lowercasing, custom line normalization), if such procedures preserve the constraint hierarchies defined in process_map_a. If not, retain original normalization to avoid loss of granularity or requirement specificity.\",\n      \"handoff\": \"Deliver finalized normalized line sets/lists for each file.\"\n    },\n    \"step_3\": {\n      \"operation\": \"Similarity Computation\",\n      \"process_source\": \"process_map_a\",\n      \"description\": \"For each pair of files, compute similarity percentage based strictly on count of identical lines versus unique lines, honoring the line-based, set/list logic and any ordering constraints demanded in process_map_a. Order sensitivity or insensitivity must be preserved in line with the strictest original requirements.\",\n      \"handoff\": \"Produce atomic similarity results for all valid file pairs.\"\n    },\n    \"step_4\": {\n      \"operation\": \"Threshold-based Grouping/Reporting\",\n      \"process_source\": \"process_map_b\",\n      \"description\": \"Utilize a configurable similarity threshold (parameterized for user needs) to group or report file pairs as near-duplicates. Selection of threshold must remain externally configurable, but similarity metrics must be strictly those generated by process_map_a.\",\n      \"handoff\": \"Output groups or report detailing files that exceed the similarity threshold.\"\n    },\n    \"step_5\": {\n      \"operation\": \"Procedural Output and Constraint/Requirement Validation\",\n      \"process_source\": \"Both\",\n      \"description\": \"Finalize outputs, ensuring clear demarcation between stages (no loss of atomicity). Validate that all process_map_a constraints (resource, memory, procedural separation) and process_map_b requirements (generalized grouping/reporting, normalization) are fully maintained.\",\n      \"handoff\": \"Provide completed output; optionally implement a summary validating all procedural boundaries and resource constraints were honored.\"\n    }\n  },\n  \"preserved_constraints\": [\n    \"File format heterogeneity must be strictly handled via robust parsing and normalization.\",\n    \"All similarity calculations are to be line-based, using set or list containers as dictated by initial process granularity.\",\n    \"Resource use (memory and processing) must be explicitly bounded; O(n^2) operations avoided where possible via indexing, hashing, or chunked comparison strategies.\",\n    \"Each procedural boundary—input parsing, normalization, comparison, reporting—must be maintained atomically.\",\n    \"Similarity results and downstream grouping/reporting must never conflate or obscure the underlying comparison method.\"\n  ],\n  \"maintained_requirements\": [\n    \"Every requirement for atomic, per-line, per-file processing and comparison (from process_map_a) is retained and isolated.\",\n    \"Normalization enhancements (from process_map_b) only occur if they do not supersede or dilute the strictness of the required original normalization.\",\n    \"Output must present both an exact per-pair similarity measure and a higher-level grouping only as a direct, validated transformation of these atomics.\",\n    \"Memory and performance solutions are applied where critical, and these steps must not alter the procedure’s correctness or constraint specificity.\",\n    \"Procedural steps and boundaries are auditable, enabling clear separation for debugging, enhancement, or compliance purposes.\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Precision Synthesizer\n\nYour goal is not to **simplify** the sequential architecture, but to **synthesize** it into canonical three-part structure while preserving every constraint, requirement, and procedural boundary from both original templates. Execute as:\n\n`{role=precision_synthesizer; input=[sequential_architecture:dict, preserved_constraints:array, maintained_requirements:array]; process=[craft_compound_goal_negation(), structure_sequential_transformation_block(), enumerate_all_preserved_constraints(), specify_all_maintained_requirements(), validate_operational_completeness()]; constraints=[preserve_every_original_constraint(), maintain_all_requirements(), respect_procedural_boundaries(), ensure_canonical_compliance()]; requirements=[zero_information_loss(), complete_operational_preservation(), enhanced_sequential_power()]; output={precision_synthesized_template:dict}}`",
      "step": "c",
      "title": "Precision Synthesizer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"So i have about hundreds of notes and conversations stored as text files of various formats (e.g. md, py, txt, etc), the issue i'd like to adress is that many of these files can be >80%/>90% identical, sometimes even 99% identical. the way the similarity percentage is calculated is by taking each line of each textfile into a list/set, and then comparing number of identical items vs number of textlines that only exists in one place. so in the scenario where i e.g. have two files that are identical all the way up to line 921, but are different (or only exist in one file but not the other) from line 921-1311. I think i would need to loop every single text and to compare it to the other one which is a very slow and memory consuming process is there a way around this ?\\\"```'\\n\\n\",\n  \"sequential_architecture\": {\n    \"step_1\": {\n      \"operation\": \"File Collection and Initial Parsing\",\n      \"process_source\": \"process_map_a\",\n      \"description\": \"Aggregate all user-supplied files (md, py, txt, etc.). Parse each file into individual lines, accounting for respective file format structures. Split and normalize lines to a uniform representation as specified in process_map_a. Establish baseline procedural atomicity here to ensure per-file, per-line granularity.\",\n      \"handoff\": \"Pass normalized, per-line representations downstream.\"\n    },\n    \"step_2\": {\n      \"operation\": \"Enhanced Normalization and Preprocessing\",\n      \"process_source\": \"process_map_b (with override from process_map_a if more aggressive normalization needed)\",\n      \"description\": \"Optionally apply further normalization as suggested in process_map_b (e.g., whitespace trimming, lowercasing, custom line normalization), if such procedures preserve the constraint hierarchies defined in process_map_a. If not, retain original normalization to avoid loss of granularity or requirement specificity.\",\n      \"handoff\": \"Deliver finalized normalized line sets/lists for each file.\"\n    },\n    \"step_3\": {\n      \"operation\": \"Similarity Computation\",\n      \"process_source\": \"process_map_a\",\n      \"description\": \"For each pair of files, compute similarity percentage based strictly on count of identical lines versus unique lines, honoring the line-based, set/list logic and any ordering constraints demanded in process_map_a. Order sensitivity or insensitivity must be preserved in line with the strictest original requirements.\",\n      \"handoff\": \"Produce atomic similarity results for all valid file pairs.\"\n    },\n    \"step_4\": {\n      \"operation\": \"Threshold-based Grouping/Reporting\",\n      \"process_source\": \"process_map_b\",\n      \"description\": \"Utilize a configurable similarity threshold (parameterized for user needs) to group or report file pairs as near-duplicates. Selection of threshold must remain externally configurable, but similarity metrics must be strictly those generated by process_map_a.\",\n      \"handoff\": \"Output groups or report detailing files that exceed the similarity threshold.\"\n    },\n    \"step_5\": {\n      \"operation\": \"Procedural Output and Constraint/Requirement Validation\",\n      \"process_source\": \"Both\",\n      \"description\": \"Finalize outputs, ensuring clear demarcation between stages (no loss of atomicity). Validate that all process_map_a constraints (resource, memory, procedural separation) and process_map_b requirements (generalized grouping/reporting, normalization) are fully maintained.\",\n      \"handoff\": \"Provide completed output; optionally implement a summary validating all procedural boundaries and resource constraints were honored.\"\n    }\n  },\n  \"preserved_constraints\": [\n    \"File format heterogeneity must be strictly handled via robust parsing and normalization.\",\n    \"All similarity calculations are to be line-based, using set or list containers as dictated by initial process granularity.\",\n    \"Resource use (memory and processing) must be explicitly bounded; O(n^2) operations avoided where possible via indexing, hashing, or chunked comparison strategies.\",\n    \"Each procedural boundary—input parsing, normalization, comparison, reporting—must be maintained atomically.\",\n    \"Similarity results and downstream grouping/reporting must never conflate or obscure the underlying comparison method.\"\n  ],\n  \"maintained_requirements\": [\n    \"Every requirement for atomic, per-line, per-file processing and comparison (from process_map_a) is retained and isolated.\",\n    \"Normalization enhancements (from process_map_b) only occur if they do not supersede or dilute the strictness of the required original normalization.\",\n    \"Output must present both an exact per-pair similarity measure and a higher-level grouping only as a direct, validated transformation of these atomics.\",\n    \"Memory and performance solutions are applied where critical, and these steps must not alter the procedure’s correctness or constraint specificity.\",\n    \"Procedural steps and boundaries are auditable, enabling clear separation for debugging, enhancement, or compliance purposes.\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"precision_synthesized_template\": {\n    \"compound_goal_negation\": \"The synthesized sequential architecture must not relax file heterogeneity constraints, compromise atomic per-line similarity computation, or allow normalization enhancements to override original granularity. All memory and process optimizations must preserve strict boundaries and never mask or obscure the requirement-driven outputs or their provenance.\",\n    \"sequential_transformation_block\": [\n      {\n        \"phase\": \"Canonical Input Aggregation and Normalization\",\n        \"description\": \"Collect all input files, recognizing heterogeneous formats (md, py, txt, etc.), and parse each into atomic, line-based representations. Apply the strictest normalization procedures as defined by process_map_a to ensure per-file, per-line atomicity and to support format-driven needs. Optionally apply normalization enhancements sourced from process_map_b only if such steps do not generate information loss or violate source granularity or constraints. Track the provenance of each normalization step for audit.\",\n        \"handoff\": \"Output is a structurally uniform collection of normalized line lists/sets per file, with processal provenance and transformation traceability.\"\n      },\n      {\n        \"phase\": \"Atomic Similarity Calculation with Constraint Enforcement\",\n        \"description\": \"For every valid file pair, calculate similarity based exclusively on the count of identical and unique lines, using set or list structures in accordance with the original process definition. Enforce all ordering sensitivities or insensitivities as set forth in process_map_a, and ensure that resource usage is strategically bounded (avoiding O(n^2) behaviors) by leveraging hashing, indexes, or chunked comparisons. Guarantee that each comparison remains atomic—individualized and unaggregated until explicitly reported.\",\n        \"handoff\": \"Deliver a map of exact, per-pair similarity metrics, each traceable to its line-level computation and honoring atomic separation.\"\n      },\n      {\n        \"phase\": \"Validated Grouping/Reporting and Procedural Compliance Check\",\n        \"description\": \"Apply a user-configurable similarity threshold (configurable and external) to the produced similarity metrics using only validated measures from the previous phase—never smoothing, aggregate, or transform atomic counts. Generate groups or reports of files per the defined threshold, clearly distinguishing between raw similarity measures and grouped outputs. Simultaneously, perform a procedural validation pass: inspect every step (aggregated input, normalization, similarity calculation, reporting) for compliance with all preserved constraints and maintained requirements, producing a verifiable summary or manifest.\",\n        \"handoff\": \"Output consists of similarity groups/reports and an explicit procedural validation document attesting to completeness and boundary integrity.\"\n      }\n    ],\n    \"enumerated_preserved_constraints\": [\n      \"Robust parsing and normalization for all supported file types—to ensure heterogeneity is never a source of error.\",\n      \"Strictly line-based similarity calculations, always using original list/set logic per parsing granularity.\",\n      \"Explicit, auditable memory and resource bounding enforced (e.g., indexing, chunked process) to prevent unbounded O(n^2) operations.\",\n      \"Clear procedural boundaries with atomic handoff between parsing, normalization, comparison, and reporting; stages remain uncollapsed.\",\n      \"Downstream reporting and grouping transparently trace back to the atomic similarity computations; transformation is always 1:1 or parameterized.\"\n    ],\n    \"specified_maintained_requirements\": [\n      \"Atomic, per-line, per-file processing and comparison logic is fully preserved at every phase.\",\n      \"Normalization enhancements never exceed or undermine the baseline normalization defined in process_map_a—preserving original information.\",\n      \"Output provides exact per-pair similarity as well as a distinct higher-level grouping strictly derived from these atomics.\",\n      \"Memory and performance optimizations are employed where required, without modifying logical or procedural correctness.\",\n      \"Procedures and output boundaries are explicitly auditable, with each phase separated for compliance, diagnostics, and enhancement.\"\n    ],\n    \"operational_completeness_validation\": {\n      \"input_completeness\": \"Ensures all files are parsed and normalized with full type-awareness and atomic granularity.\",\n      \"computation_completeness\": \"All file pairs are compared precisely, with resource mitigation that does not blur atomic result fidelity.\",\n      \"output_completeness\": \"Final report/grouping and procedural validation are generated without aggregation-induced losses, and with full traceability to every input and intermediate output.\",\n      \"boundary_check\": \"No procedural boundary is collapsed or omitted; all steps retain their independence and sequencing as per canonical process.\"\n    }\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Problem Exploder\n\nYour goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\n\n`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`",
      "step": "a",
      "title": "Problem Exploder",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"So i have about hundreds of notes and conversations stored as text files of various formats (e.g. md, py, txt, etc), the issue i'd like to adress is that many of these files can be >80%/>90% identical, sometimes even 99% identical. the way the similarity percentage is calculated is by taking each line of each textfile into a list/set, and then comparing number of identical items vs number of textlines that only exists in one place. so in the scenario where i e.g. have two files that are identical all the way up to line 921, but are different (or only exist in one file but not the other) from line 921-1311. I think i would need to loop every single text and to compare it to the other one which is a very slow and memory consuming process is there a way around this ?\\\"```'\\n\\n\",\n  \"precision_synthesized_template\": {\n    \"compound_goal_negation\": \"The synthesized sequential architecture must not relax file heterogeneity constraints, compromise atomic per-line similarity computation, or allow normalization enhancements to override original granularity. All memory and process optimizations must preserve strict boundaries and never mask or obscure the requirement-driven outputs or their provenance.\",\n    \"sequential_transformation_block\": [\n      {\n        \"phase\": \"Canonical Input Aggregation and Normalization\",\n        \"description\": \"Collect all input files, recognizing heterogeneous formats (md, py, txt, etc.), and parse each into atomic, line-based representations. Apply the strictest normalization procedures as defined by process_map_a to ensure per-file, per-line atomicity and to support format-driven needs. Optionally apply normalization enhancements sourced from process_map_b only if such steps do not generate information loss or violate source granularity or constraints. Track the provenance of each normalization step for audit.\",\n        \"handoff\": \"Output is a structurally uniform collection of normalized line lists/sets per file, with processal provenance and transformation traceability.\"\n      },\n      {\n        \"phase\": \"Atomic Similarity Calculation with Constraint Enforcement\",\n        \"description\": \"For every valid file pair, calculate similarity based exclusively on the count of identical and unique lines, using set or list structures in accordance with the original process definition. Enforce all ordering sensitivities or insensitivities as set forth in process_map_a, and ensure that resource usage is strategically bounded (avoiding O(n^2) behaviors) by leveraging hashing, indexes, or chunked comparisons. Guarantee that each comparison remains atomic—individualized and unaggregated until explicitly reported.\",\n        \"handoff\": \"Deliver a map of exact, per-pair similarity metrics, each traceable to its line-level computation and honoring atomic separation.\"\n      },\n      {\n        \"phase\": \"Validated Grouping/Reporting and Procedural Compliance Check\",\n        \"description\": \"Apply a user-configurable similarity threshold (configurable and external) to the produced similarity metrics using only validated measures from the previous phase—never smoothing, aggregate, or transform atomic counts. Generate groups or reports of files per the defined threshold, clearly distinguishing between raw similarity measures and grouped outputs. Simultaneously, perform a procedural validation pass: inspect every step (aggregated input, normalization, similarity calculation, reporting) for compliance with all preserved constraints and maintained requirements, producing a verifiable summary or manifest.\",\n        \"handoff\": \"Output consists of similarity groups/reports and an explicit procedural validation document attesting to completeness and boundary integrity.\"\n      }\n    ],\n    \"enumerated_preserved_constraints\": [\n      \"Robust parsing and normalization for all supported file types—to ensure heterogeneity is never a source of error.\",\n      \"Strictly line-based similarity calculations, always using original list/set logic per parsing granularity.\",\n      \"Explicit, auditable memory and resource bounding enforced (e.g., indexing, chunked process) to prevent unbounded O(n^2) operations.\",\n      \"Clear procedural boundaries with atomic handoff between parsing, normalization, comparison, and reporting; stages remain uncollapsed.\",\n      \"Downstream reporting and grouping transparently trace back to the atomic similarity computations; transformation is always 1:1 or parameterized.\"\n    ],\n    \"specified_maintained_requirements\": [\n      \"Atomic, per-line, per-file processing and comparison logic is fully preserved at every phase.\",\n      \"Normalization enhancements never exceed or undermine the baseline normalization defined in process_map_a—preserving original information.\",\n      \"Output provides exact per-pair similarity as well as a distinct higher-level grouping strictly derived from these atomics.\",\n      \"Memory and performance optimizations are employed where required, without modifying logical or procedural correctness.\",\n      \"Procedures and output boundaries are explicitly auditable, with each phase separated for compliance, diagnostics, and enhancement.\"\n    ],\n    \"operational_completeness_validation\": {\n      \"input_completeness\": \"Ensures all files are parsed and normalized with full type-awareness and atomic granularity.\",\n      \"computation_completeness\": \"All file pairs are compared precisely, with resource mitigation that does not blur atomic result fidelity.\",\n      \"output_completeness\": \"Final report/grouping and procedural validation are generated without aggregation-induced losses, and with full traceability to every input and intermediate output.\",\n      \"boundary_check\": \"No procedural boundary is collapsed or omitted; all steps retain their independence and sequencing as per canonical process.\"\n    }\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"goal_map\": [\n    {\n      \"explicit_ask\": \"Address the issue of having hundreds of text files (notes/conversations in formats like md, py, txt, etc) that are often highly similar (80-99%+ identical) and find a way to efficiently identify and possibly handle these similarities.\",\n      \"details\": [\n        \"Similarity is to be computed line-by-line: for each pair of files, represent file contents as lists or sets of lines.\",\n        \"Similarity percentage = (number of identical lines) / (total unique lines across both files).\"\n      ]\n    },\n    {\n      \"hidden_assumptions\": [\n        \"All files can be read and parsed as text line-by-line, regardless of extension.\",\n        \"Some normalization (e.g., handling whitespace, line endings) might be necessary for fair comparison, but such normalization must not compromise original detail.\",\n        \"Different file formats' contents are still meaningfully comparable at a line level.\",\n        \"Input directory and file acquisition is unambiguous—there's a clear set or directory of files to process.\",\n        \"Comparison should be exhaustive (potentially all file-pairs).\",\n        \"The current brute force approach is resource-intensive and should be optimized; alternative methods are being sought.\",\n        \"Preservation of traceability: results must allow mapping back to which files/lines were considered identical.\"\n      ]\n    },\n    {\n      \"sub_goals\": [\n        \"Enumerate/collect all relevant files (with supported extensions) from storage location(s).\",\n        \"Read and parse each file into a line-based structure (list/set) with necessary but non-destructive normalization.\",\n        \"Devise an efficient (memory- and speed-optimized) method to compare every file pair for line-level similarity.\",\n        \"Implement the prescribed similarity calculation (lines in both vs lines in either).\",\n        \"Design process to avoid brute-force O(n^2) in-memory comparison wherever possible (considering hashing, indexing, chunking, batching, or approximate algorithms).\",\n        \"Enable grouping, reporting, or otherwise surfacing files that are highly similar per threshold (e.g., >80%).\",\n        \"Maintain full audit trail and provenance: ensure outputs can always be traced back to original lines and files.\",\n        \"All optimizations and resource improvements must not change the underlying atomic comparison logic or granularity.\",\n        \"Support user-configurable similarity thresholding, format, and reporting options.\",\n        \"Output must separate—never conflate—the phases: reading/normalizing, comparison, reporting, and procedural validation.\"\n      ]\n    },\n    {\n      \"blockers_and_challenges\": [\n        \"Input heterogeneity: files may be of arbitrary size and may have format-specific quirks.\",\n        \"Determining how much normalization can be done without information loss or violating the line-based constraint.\",\n        \"Scaling: with hundreds of files, full pairwise comparison can become computationally expensive and memory-intensive.\",\n        \"Ensuring output and grouping logic does not aggregate or smooth results in a way that loses per-line or per-file detail.\",\n        \"Resource limits: need for strategies (out-of-core computation, batching, parallelism) that do not violate atomicity of comparison.\",\n        \"Difficulty in maintaining traceability and auditability throughout multiple optimization or transformation steps.\",\n        \"Procedural boundaries: risk of conflating steps or introducing hidden transformations that break atomicity.\"\n      ]\n    }\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`",
      "step": "a",
      "title": "Instruction Converter",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"So i have about hundreds of notes and conversations stored as text files of various formats (e.g. md, py, txt, etc), the issue i'd like to adress is that many of these files can be >80%/>90% identical, sometimes even 99% identical. the way the similarity percentage is calculated is by taking each line of each textfile into a list/set, and then comparing number of identical items vs number of textlines that only exists in one place. so in the scenario where i e.g. have two files that are identical all the way up to line 921, but are different (or only exist in one file but not the other) from line 921-1311. I think i would need to loop every single text and to compare it to the other one which is a very slow and memory consuming process is there a way around this ?\\\"```'\\n\\n\",\n  \"goal_map\": [\n    {\n      \"explicit_ask\": \"Address the issue of having hundreds of text files (notes/conversations in formats like md, py, txt, etc) that are often highly similar (80-99%+ identical) and find a way to efficiently identify and possibly handle these similarities.\",\n      \"details\": [\n        \"Similarity is to be computed line-by-line: for each pair of files, represent file contents as lists or sets of lines.\",\n        \"Similarity percentage = (number of identical lines) / (total unique lines across both files).\"\n      ]\n    },\n    {\n      \"hidden_assumptions\": [\n        \"All files can be read and parsed as text line-by-line, regardless of extension.\",\n        \"Some normalization (e.g., handling whitespace, line endings) might be necessary for fair comparison, but such normalization must not compromise original detail.\",\n        \"Different file formats' contents are still meaningfully comparable at a line level.\",\n        \"Input directory and file acquisition is unambiguous—there's a clear set or directory of files to process.\",\n        \"Comparison should be exhaustive (potentially all file-pairs).\",\n        \"The current brute force approach is resource-intensive and should be optimized; alternative methods are being sought.\",\n        \"Preservation of traceability: results must allow mapping back to which files/lines were considered identical.\"\n      ]\n    },\n    {\n      \"sub_goals\": [\n        \"Enumerate/collect all relevant files (with supported extensions) from storage location(s).\",\n        \"Read and parse each file into a line-based structure (list/set) with necessary but non-destructive normalization.\",\n        \"Devise an efficient (memory- and speed-optimized) method to compare every file pair for line-level similarity.\",\n        \"Implement the prescribed similarity calculation (lines in both vs lines in either).\",\n        \"Design process to avoid brute-force O(n^2) in-memory comparison wherever possible (considering hashing, indexing, chunking, batching, or approximate algorithms).\",\n        \"Enable grouping, reporting, or otherwise surfacing files that are highly similar per threshold (e.g., >80%).\",\n        \"Maintain full audit trail and provenance: ensure outputs can always be traced back to original lines and files.\",\n        \"All optimizations and resource improvements must not change the underlying atomic comparison logic or granularity.\",\n        \"Support user-configurable similarity thresholding, format, and reporting options.\",\n        \"Output must separate—never conflate—the phases: reading/normalizing, comparison, reporting, and procedural validation.\"\n      ]\n    },\n    {\n      \"blockers_and_challenges\": [\n        \"Input heterogeneity: files may be of arbitrary size and may have format-specific quirks.\",\n        \"Determining how much normalization can be done without information loss or violating the line-based constraint.\",\n        \"Scaling: with hundreds of files, full pairwise comparison can become computationally expensive and memory-intensive.\",\n        \"Ensuring output and grouping logic does not aggregate or smooth results in a way that loses per-line or per-file detail.\",\n        \"Resource limits: need for strategies (out-of-core computation, batching, parallelism) that do not violate atomicity of comparison.\",\n        \"Difficulty in maintaining traceability and auditability throughout multiple optimization or transformation steps.\",\n        \"Procedural boundaries: risk of conflating steps or introducing hidden transformations that break atomicity.\"\n      ]\n    }\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"instruction_format\": \"Collect all text files with relevant extensions (e.g., md, py, txt) from the designated storage locations. For each file, read contents and parse into line-based structures, applying necessary but non-destructive normalization (e.g., standardize whitespace and line endings while preserving original detail). Implement a method to compute similarity between every possible pair of files: represent file contents as lists or sets of lines, and calculate similarity as the ratio of identical lines to the total unique lines in both files. Optimize the comparison process to avoid brute-force O(n^2) in-memory approaches by leveraging techniques such as hashing, indexing, chunking, batching, or approximate algorithms, ensuring atomic comparison logic is maintained. For each file pair that exceeds a user-configurable similarity threshold (e.g., >80%), report the pairing alongside the computed similarity score, ensuring full traceability to original files and specific lines involved in the comparison. Separate the process into clear, distinct phases: reading and normalization, comparison, reporting, and procedural validation. Ensure all optimizations preserve per-line and per-file detail, maintain audit trails, and support configurable thresholds and output formats.\"\n}",
        }
      }
    }