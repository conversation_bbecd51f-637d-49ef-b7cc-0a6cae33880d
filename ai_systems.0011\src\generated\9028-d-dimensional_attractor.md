[Dimensional Attractor – Style Axis] Your goal is not to **average** components, but to **force** them to align on a single stylistic attractor (cinematic realism, illustrative fantasy, etc.) and prune conflict. Execute as: `{role=style_attractor; input=[components:dict]; process=[determine_optimal_style_axis(), align_components(), prune_outliers()], constraints=[axis_must_match_runway_guide()], requirements=[aligned_components:dict, style_axis:str], output={aligned_components:dict}}`

Context: {}