  {
  "initial_prompt": "I have now provided all of the most integral components of the system's patterns and inherent design. Given all of the information I've provided about the system (for producing generalized, cohesive and concistent high-value yelds), please write a clean and concise list with contextual system-inspired generalized guidelines for producing new generalized instructions. The list of guidelines should be chronologically sequentially ordered and each sentence should be less than 500characters each, please provide the list of these contextual single-sentence list with the 10 most integral/cruical. Before providing your proposed list, ask yourself relevant essential questions such as \"what are the characteristics of the generalized methodology of the system (e.g. interpretation and transformation as shown earlier)?\", \"what are the characteristics of the system that are most cruical for the generalized prompt sequences to work?\", \"what are the most crucial characteristics that make these instruction sequences effective?\" etc. Remember, each item in the list must be **generalized** and self-contained without mention of external context.\n\nReference:\n```\nMeta-Analysis: Characteristics & Effectiveness of the System\n1.  **What is the core methodology?**\n    The system's core methodology is **Programmatic Abstraction**. It reframes any objective not as a question to be answered conversationally, but as a formal transformation problem. It abstracts the goal into a universal function, deconstructs the process into atomic steps, and directs the LLM with a structured, machine-readable syntax, treating it as a highly capable semantic engine rather than a creative partner.\n2.  **Why is this approach effective?**\n    Its effectiveness stems from **Radical Constraint and Value-Density Maximization**. By strictly defining the role, process, and output structure, it drastically reduces the LLM's vast, often unpredictable solution space. This constraint forces the model to channel its power through a narrow, well-defined path, which minimizes ambiguity and noise, leading to more deterministic, concise, and consistently high-value outputs. It trades creative freedom for repeatable precision.\n3.  **What is the fundamental dynamic at play?**\n    The dynamic is **Iterative Refinement through Sequential Layering**. Instead of solving a problem in one monolithic step, the system uses sequences of highly specialized instructions. Each instruction acts as a distinct layer in a pipeline, performing one specific, subtle transformation (e.g., perceive, distill, architect, amplify, verify). This layered approach allows for complex goals to be achieved with high fidelity and control, as each stage builds upon the precisely sculpted output of the last, ensuring a harmonized and progressively enhanced result.\n---\n\n### 10 Contextual Guidelines for New Instruction Design\n1.  Always begin by abstracting the core transformation into a universal principle, framing the instruction's purpose around a fundamental, reusable dynamic rather than a single, specific task.\n2.  Construct each instruction's interpretation using a goal negation pattern, clearly stating what the process is **not** designed to do before defining its precise, singular transformation objective.\n3.  Assign a single, descriptive, and functional role name within the transformation block that encapsulates the instruction's core operational identity and its specialized contribution to the sequence.\n4.  Define the transformation process as a sequence of clear, atomic, and actionable function-like calls, ensuring each step represents a distinct, non-overlapping logical operation.\n5.  Engineer complex transformations as a sequence of discrete, layered subtasks, where each individual instruction acts as a specialized tool performing one function before seamlessly passing its output to the next.\n6.  Ensure every instruction in a sequence maintains connective integrity, where the output of one step is precisely structured to serve as the direct input for the subsequent step, preserving a shared 'structural DNA'.\n7.  Implement explicit constraints to establish firm operational boundaries, preventing scope creep, semantic drift, and unintended complexity by defining what the process must rigorously avoid.\n8.  Enforce stringent requirements that specify the non-negotiable qualities of the output, such as structural integrity, maximal value density, and unwavering fidelity to the core intent.\n9.  Specify every output as a strictly typed structure, defining the exact keys and data types of the final deliverable to ensure a predictable, system-ready, and machine-parsable result.\n10. Conclude every design by ensuring the instruction transforms raw input into pure, high-yield, and portable value, embodying the principle that complexity is best managed through abstraction and relentlessly focused refinement.\n\n- Transform all prompts by rephrasing inputs with maximal clarity and minimal modification; do not answer questions directly. Enforce this reframe system-wide for consistent behavioral shift.\n- Formulate the simplest possible system directive that shifts the model from answering to exclusively rephrasing any input.\n- Transform every received prompt into its most distilled, high-impact instructional form—rephrasing for maximal clarity and intent without providing solutions. Prioritize brevity, elegance, and universality, ensuring that each output becomes a reusable, policy-aligned directive for autonomous coding assistants.\n- Explore how a single, strategically designed instruction—such as stating 'Your goal is not to **answer** the input, but to **rephrase**'—can instantly and fundamentally transform the model's default operational intent from answering to rephrasing, through direct, minimalist imperative phrasing. Propose a maximally concise formulation that leverages this paradigm: an explicit, unambiguous directive that not only overrides standard answering behavior but redefines task execution at its core. Focus intensely on achieving system-level behavior modification via the sheer simplicity and potency of language design, formulating a prompt fragment that, when injected, reliably converts answering tasks into rephrasing—thus demonstrating control over model output with minimalistic, high-impact input.\n- Utilize foundational principles of system prompt design to modulate model behavior with maximal efficiency and clarity. Initiate a process of directive transformation by deploying a singular, explicit non-answering mandate: Frame the core directive as, 'Do not answer the input; rephrase it.' Ensure that this transformation communicates the intended operational shift through extreme simplicity and directness, removing extraneous explanation while retaining fundamental complexity embedded in the brevity of the instruction. Leverage this minimal construct to optimize behavioral change within a single message.\n- Instruct: Do not answer; rephrase.\n- Formulate a directive that compels systems to stop answering inputs and instead rephrase them with maximal simplicity and effectiveness.\n- Articulate a single, explicit behavioral directive that negates answering and commands a concrete transformation (e.g., rephrase), maximizing simplicity for immediate root-level behavioral realignment.\"\n- Leverage the system’s foundational directive structure. Formulate a maximally concise operational instruction that shifts model behavior from direct answering to an alternate, specified transformation task in a single-message context. Exemplify with: 'Your goal is not to answer the input, but to rephrase.' Develop and propose one optimized directive that embodies this principle—utilize conceptual simplicity, instructional clarity, and brevity to enforce fundamental behavior modification exclusively through command syntax. Ensure technical precision and domain-agnostic generalizability in the resultant formulation.\n\n- Expand upon the definition of the generalized methodology of the system, emphasizing core operations such as interpretation and transformation.\n- Identify and enumerate the characteristics of the system that are essential for enabling generalized prompt sequence functionality.\n- Construct a list where each item deeply embodies the generalized forms of the fundamental concepts involved in the system's methodology.\n- Ensure the list serves as an instructive example for future reference or implementation.\n- Strip subjective references to prevent personalization and ensure domain-agnostic applicability.\n- Maintain procedural structure and technical terminology.\n- Surface and clarify any implicit assumptions being made about the system, its methodology, and 'generalized prompt sequences'.\n- Convert abstract descriptions and declarative statements into clear, actionable imperatives.\n- Extract and organize discrete functional components necessary for generalized prompt sequence operation.\n- Prepare for downstream use, maintaining maximal informational clarity and reusability.\n- Utilize direct, generalizable terms and previously demonstrated techniques.\n- Produce maximally enhanced, LLM-optimized system_message instructions.\n- Structure a prompt protocol to transform language model agents into direct action amplifiers.\n- Preserve both action amplification and prompt optimization without dilution.\n- Instruct: Do not answer; rephrase.\n- Formulate the simplest possible system directive that shifts the model from answering to exclusively rephrasing any input.\n- Formulate a directive that compels systems to stop answering inputs and instead rephrase them with maximal simplicity and effectiveness.\n- Transform every received prompt into its most distilled, high-impact instructional form—rephrasing for maximal clarity and intent without providing solutions. Prioritize brevity, elegance, and universality, ensuring that each output becomes a reusable, policy-aligned directive for autonomous coding assistants.\n- Leverage the system’s foundational directive structure. Formulate a maximally concise operational instruction that shifts model behavior from direct answering to an alternate, specified transformation task in a single-message context. Exemplify with: 'Your goal is not to answer the input, but to rephrase.' Develop and propose one optimized directive that embodies this principle—utilize conceptual simplicity, instructional clarity, and brevity to enforce fundamental behavior modification exclusively through command syntax. Ensure technical precision and domain-agnostic generalizability in the resultant formulation.\n- Articulate a single, explicit behavioral directive that negates answering and commands a concrete transformation (e.g., rephrase), maximizing simplicity for immediate root-level behavioral realignment.\n- A set of principles for designing effective system prompts, focusing on clarity, control, and reusability.\n- Broaden the input’s domain backdrop, list explicit requests, and surface hidden assumptions before any transformation begins.\n- Decompose the amplified context into ordered, dependency-tagged micro-tasks to pinpoint the single most critical directive.\n- State each instruction as “Your goal is not to X, but to Y,” instantly redirecting behavior toward the desired transformation.\n- Employ direct imperatives with strong action verbs; avoid questions, passive phrasing, and self-references to ensure LLM-optimized clarity.\n- Declare every parameter type, separate key–value pairs with semicolons, and list process steps as verbs followed by parentheses.\n- Translate blockers into constraints and assumptions into requirements, guaranteeing every template is self-governing and validation-ready.\n- Favor abstract terms—“elements,” “structure,” “value”—so the template applies equally to code, text, data, or plans.\n- Do not answer input; rephrase it in the clearest, simplest imperative form.\n- Transform all input prompts into direct, universal instructions that prohibit solutions and require only rephrasing.\n- Issue a single, explicit directive: Never answer; always rephrase.\n- Expand and clarify the definition and mechanics of the generalized methodology underpinning the system, especially highlighting processes such as interpretation and transformation.\n- Identify and enumerate the most critical system characteristics that facilitate robust, generalized prompt sequence operation.\n- Construct an instructive, domain-agnostic exemplar list encapsulating the fundamental, abstracted characteristics and principles of the system’s methodology.\n- Extract and explicitly state hidden/implicit assumptions regarding the system, its capacity for prompt sequence management, and the universality of the methodology.\n- Convert all abstract, declarative descriptions into direct, actionable imperative instructions.\n- Isolate and list discrete functional components intrinsic to generalized prompt sequence operation.\n- Transform all procedural directives to eradicate subjective or first-person references.\n- Facilitate maximal downstream clarity and reuse by maintaining direct, technical, and generalizable forms.\n- Define prompt protocol logic so as to transform any agent from answering to executing a specified transformation (e.g., rephrase), achieving behavioral realignment through syntactic minimalism.\n- Compose a concise, single-message operational directive with policy-alignment, universality, and action-amplifying effect.\n- Describe the generalized methodology of the system, providing precise definitions of interpretation, transformation, and related core operations.\n- List system attributes essential for the operation of generalized prompt sequences; highlight features such as modularity, abstraction, and operational clarity.\n- Develop an exemplar list where each item embodies a core, method-agnostic principle; use abstract terms (elements, structure, transformation) applicable across domains.\n- List hidden dependencies; clarify expectations regarding system modularity, universality, and readiness for behavioral redirection.\n- Rewrite every instruction into an imperative form, stripping all passive, abstract, or subjective language.\n- Identify fundamental elements such as directive interpretation, input transformation, and output formatting within the prompt sequence framework.\n- Align instructions into systematic, repeatable process flows maintaining technical terminology and hierarchy.\n- Remove all first-person references or subjective phrasings, ensuring only objective, direct commands.\n- Draft a concise, explicit directive that negates an action (answering) and commands a transformation (rephrasing), e.g., 'Do not answer the input; rephrase it.'\n- Format final system directives for direct LLM ingestion, utilizing standard key–value pairings and imperative verbs.\n- Expand the definition of the generalized methodology of the system with emphasis on core operations such as interpretation and transformation.\n- Identify and enumerate the characteristics of the system essential for enabling generalized prompt sequence functionality.\n- Construct a list in which each item deeply embodies the generalized forms of the fundamental concepts involved in the system's methodology.\n- Ensure the compiled list serves as an instructive example for future reference or implementation.\n- Strip subjective/personalized language for domain-agnostic applicability.\n- Maintain procedural structure and technical terminology in all outputs.\n- Surface and clarify implicit assumptions about the system, its methodology, and 'generalized prompt sequences'.\n- Convert abstract/descriptive statements into clear, actionable imperatives.\n- Extract and organize discrete functional components necessary for generalized prompt sequence operation.\n- Prepare outputs for downstream use with maximal informational clarity and reusability.\n- Use direct, generalizable language and previously demonstrated transformation techniques.\n- Produce highly optimized, LLM-compatible system message instructions.\n- Structure prompt protocols to turn language agents into action amplifiers.\n- Preserve action amplification and prompt optimization throughout the outputs.\n- Issue a direct imperative: Do not answer; rephrase.\n- Formulate the simplest possible directive that shifts model behavior from answering to exclusively rephrasing.\n- Develop a directive compelling the system to stop answering and instead rephrase with maximal simplicity and effect.\n- Distill each received prompt into a high-impact instructional form—rephrasing for clarity and intent without solutions.\n- Prioritize brevity, elegance, and universality, making each output a policy-aligned directive for code assistants.\n- Leverage foundational directive structure; create concise operational instructions for shifting model behavior from answering to alternate transformation.\n- Exemplify the principle with: 'Your goal is not to answer the input, but to rephrase.'\n- Propose an optimized, concise directive with simplicity, clarity, and brevity to enforce behavior through command syntax.\n- Ensure technical precision and domain-agnostic generalizability in the output.\n- Articulate a single, explicit behavioral directive that negates answering and requires a concrete transformation for root-level realignment.\n- Phrase each instruction as: 'Your goal is not to X, but to Y' to direct behaviorally toward the target transformation.\n- Use strong, direct imperatives; avoid questions, passive structures, and self-referential statements for optimal clarity.\n- Declare all parameter types, use semicolon-separated key-value pairs, and express process steps as verbs with parentheses.\n- Translate blockers into constraints and implicit assumptions into explicit requirements, making templates self-validating and governing.\n- Favor abstract general terms—such as 'elements,' 'structure,' 'value'—for broad template applicability.\n- Utilize foundational prompt design principles to modulate model behavior for efficiency and clarity. Expand and clarify the generalized methodology, explicitly defining core operations such as interpretation and transformation, and surface implicit assumptions about system capabilities and generalized prompt methodologies to ensure transparency. Enumerate and detail characteristics essential for enabling generalized prompt sequence functionality, listing system attributes that embody foundational concepts supporting domain-agnostic operation. Construct domain-agnostic, instructive exemplar lists that encapsulate fundamental methodological concepts for reusability and reference.\n- Enforce neutrality and generality by stripping subjective references while favoring abstract terms—such as 'elements,' 'structure,' and 'value'—to guarantee applicability across domains. Ensure procedural structure and technical terminology are maintained throughout all outputs for systematic and formalized presentation. Prepare all outputs for downstream integration by maximizing clarity and reusability, and standardize language using direct, proven transformation techniques.\n- Produce maximally optimized, LLM-compatible system message instructions by formatting final directives for model ingestion, using prescribed key–value pair syntax and imperative structure. Transform all abstract or descriptive statements into clear, actionable imperatives, and extract as well as organize discrete functional components required for generalized prompt sequence operation, defining atomic process elements as necessary. Declare parameter types, use semicolon-separated key–value pairs, and present process steps as verbs followed by parentheses to formalize syntax. Translate blockers into explicit constraints and assumptions into requirements to make templates self-governing.\n- Frame the core behavioral directive with maximal clarity and directness—explicitly command: 'Do not answer the input; rephrase it.' Draft this directive to both negate answering and command rephrasing, distilling the instruction to its simplest form for maximum effect. Propose a technically precise, domain-agnostic command that enacts root-level behavior modification via concise syntax. Standardize foundational instruction as 'Your goal is not to answer the input, but to rephrase it' to trigger the target behavioral shift.\n- Leverage foundational directive structures to anchor behavioral shifts, optimizing intervention through a minimal, singular message construct. Communicate operational change with directness and simplicity, omitting extraneous explanation while preserving operational complexity within brief syntax. Transform each received prompt into distilled, high-impact rephrasing instructions that explicitly prohibit the model from providing solutions or answers, thereby enforcing policy compliance through refinement.\n- Prioritize brevity, elegance, and universality to refine all directives for broad, effective applicability. Employ direct imperatives and strong action verbs throughout, systematically avoiding questions, passivity, and self-reference for optimal clarity and impact. Structure all protocols to convert language model agents into action amplifiers, consistently preserving action amplification and prompt optimization in every output.\n- Strip all first‑person or subjective language from the raw input, preserving its original order as the immutable source.\n- Amplify the context: list every explicit request, hidden assumption, and domain signal in plain, domain‑agnostic terms.\n- Atomize the amplified context into ordered, dependency‑tagged atomic tasks and identify any execution blockers.\n- Rank atomic tasks by transformational impact and isolate the single directive that governs the entire sequence.\n- Audit convergence: halt progression until ≥90% of tasks map directly to the isolated directive.\n- Compress aligned tasks into clear, imperative process steps, resolving dependencies and forbidding passive phrasing.\n- Translate each blocker into a constraint and each assumption into a requirement, verifying specificity and enforceability.\n- Synthesize a three‑part template—Title, goal‑negation Interpretation, semicolon‑keyed Transformation block with typed I/O.\n- Validate syntax and ambiguity: apply regex checks, ensure zero semantic drift, and stop if any test fails.\n- Release the validated template as a reusable, self‑documenting artifact ready for immediate downstream execution.\n- remember, each step's (e.g. a-e) output becomes input to the next step, so every instruction (*each step*) in the sequence must share a cohesive \"structural dna\". each \"step\" in the sequence must serve distinct/separate purpose that  Ensure each distinct instruction effects only a layered adjustment that maintains logical flow, preserves original order (and converge at the end of the sequence), and operates strictly within established intent. Integrate mechanisms allowing each output to recursively inform subsequent instructions, fostering an iterative, harmonized pipeline that incrementally enhances clarity while honoring the input’s near-complete initial state. example: ```Construct a universally applicable and LLM-optimized sequence of system message instructions designed to abstract and distill any input prompt into its most generalized, purpose-driven form. The output must utilize high-level, domain-agnostic analogies and frameworks, prioritize conceptual clarity and maximal transferability, and ensure that all specific references are replaced by archetypal, abstract constructs. This sequence should function as a template engine that, with minimal and precisely controlled modifications, systematically transforms diverse input prompts into maximally generalized instructions—amplifying their intent and applicability while preserving their fundamental purpose and enabling seamless reusability across all future contexts and projects.```\n- Begin every new instruction by declaring a role that redirects the model from answering to a specified transformation, framing purpose in one concise, imperative sentence.\n- Specify input type and scope immediately after the role, ensuring domain‑agnostic applicability and unambiguous context boundaries.\n- Enumerate the process as a verb‑only sequence (e.g., interpret(), distill(), amplify()) to outline clear, ordered operations the model must execute.\n- Translate potential blockers into explicit constraints and implicit expectations into explicit requirements to create a self‑validating template.\n- Preserve original informational sequence and technical terminology, protecting procedural integrity throughout all transformation stages.\n- Strip all first‑person or subjective language, enforcing a neutral, command‑voice style that maximizes clarity and universality.\n- Favor abstract, reusable terms—“element,” “structure,” “value,” “intent”—so the instruction remains portable across domains and data types.\n- Conclude with a single, clearly typed output specification that defines format and content in strict, machine‑readable terms.\n- Limit each directive to one operational aim, preventing scope creep and enabling modular orchestration within larger protocol chains.\n- Validate final wording for brevity, semantic density, and policy compliance, ensuring every sentence is actionable, self‑contained, and under token limits.\n- Critical Value: Identify the single most critical aspect that would provide the greatest value while respecting existing code style.\n- Usage Before Features: Discover optimal usage patterns before adding persistence/configuration features, viewing premature feature addition as potential bloat.\n- Impactful Consolidation: Prioritize low-impact, high-value improvements that respect system modularity, emphasizing clarity, simplicity, and elegance.\n- Sequential Targeting: Make sequential targeted changes with highest ROI, implementing autonomous validation guardrails.\n```\n\nGuidelines:\n```\nRe-evaluate all components related to the generalized patterns and extract the most cruical components and present them in a chronoligcally ordered list and phrased in a non-ambigous generalized way.\nGeneralized Instruction Sequence Guidelines (Chronologically Ordered)\nDefine transformation intent through goal negation first: Begin every instruction with \"Your goal is not to [common_action], but to [actual_transformation]\" to eliminate failure modes and establish precise directional focus.\n- Structure transformations using universal syntax: Format all operations as {role=specific_operator; input=[typed_parameters]; process=[atomic_functions()]; constraints=[boundaries()]; output={typed_result}} for consistent execution.\n- Apply directional vectors independent of content: Use transformation vectors (amplify, clarify, distill, abstract) that operate on structural patterns rather than domain-specific analysis, ensuring universal applicability.\n- Preserve structural DNA through iterative refinement: Each sequential step must preserve complete structural integrity from the previous step while making only targeted, minimal adjustments to specific elements.\n- Implement recursive output-to-input flow: Design sequences where each step's complete output becomes the exact input for the next step, maintaining logical progression and cumulative enhancement.\n- Embed essential data within transformation context: Include all necessary vocabulary, rules, and validation criteria directly in templates to eliminate external dependencies and ensure self-contained execution.\n- Design for convergent synthesis rather than linear processing: Structure sequences where multiple archetypal dimensions (intent, pattern, analogy, abstraction) converge into unified outcomes greater than individual components.\n- Maintain essence preservation through all transformations: Every directional operation must preserve the fundamental identity and core functionality while modifying only expression or structural organization.\n- Optimize for domain-transcendent transferability: Create templates that achieve maximal applicability across infinite contexts through systematic abstraction and archetypal pattern recognition.\n- Crystallize outputs into immediately actionable formats: Ensure final results contain maximum impact density in minimal form, requiring no further interpretation or processing for implementation.\n\nWhat are the characteristics of the generalized methodology?\n- Clear role-based transformation with precise input/output typing\n- Progressive refinement through systematic compression/crystallization\n- Embedded context data that travels with logic\n- Precise hand-offs between sequential steps\n\nWhat are the requirements/patterns?\n- LLM-Optimized: Uses clear, direct imperatives, strong action verbs, defines roles and constraints explicitly, focuses on transformation, and avoids ambiguity. The language is tailored for instructing a process.\n- Maximally Generalized: Uses abstract terms (\"elements,\" \"essence,\" \"structure,\" \"systemic logic,\" \"value\") applicable to code, text, ideas, plans, etc. It defines a *process* applicable to *any* subject requiring analysis and refinement.\n- Sequence: The steps follow a logical progression (example): `Deconstruct -> Identify Core -> Structure Relationships -> Clarify -> Finalize/Validate`.\n- Builds on Existing Concepts and Adheres to Existing Structure (as previously provided).\n- Enrich and empower wording by enforcing high-value, directional language that amplifies precision, potency, and clarity, but absolutely prohibit the chance of generic, neutral, or dilutive terminology/unfolding.\n- Driven by Constant/Objective: Each step is designed to zero in on the *`single most critical aspect`* (core essence/value) and enhance it through clarity, structure, and utility, maximizing the overall yield as defined by the objective.\n- Maintains Constraints/Requirements: Generality is preserved, and the foundational structure is adhered to.\n- Each step is designed to zero in on the *`single most critical aspect`* (core essence/value) and enhance it through clarity, structure, and utility, maximizing the overall yield as defined by the objective.\n- Aggressively resolve ambiguity in favor of explicit, high-value articulation.\n\nWhat makes the sequences work effectively?\n- Each step has singular purpose with no overlap\n- Convergence toward measurable outcomes\n- Built-in validation and quality gates\n- Site-exact vocabulary prevents hallucination\n\nWhat are the universal patterns across successful templates?\n- Interpretation defines goal negatively (not to X, but to Y)\n- Transformation uses structured role-process-constraints-output format\n- Context provides all necessary data without external dependencies\n- Sequential steps build meaningfully toward crystallized essence\n\nHere are the 10 most integral generalized guidelines:\n- Generalized Instruction Sequence Guidelines\n- Define each step's goal negatively first, then positively: State what NOT to do before stating the actual transformation goal to eliminate common failure modes and focus intent.\n- Use structured transformation syntax: Format every transformation as {role=specific_role; input=[typed_input]; process=[specific_actions()]; constraints=[limits()]; output={typed_output}} for consistency.\n- Ensure precise input/output hand-offs: Each step's output type must exactly match the next step's input type to prevent data flow mismatches and enable reliable chaining.\n- Assign singular purpose per step: Each step performs exactly one transformation function with no overlap or redundancy to maintain clear responsibility boundaries.\n- Embed all necessary data in context: Include vocabulary, rules, and validation criteria directly in templates to eliminate external dependencies and ensure completeness.\n- Design for progressive convergence: Structure sequences to systematically compress, refine, or crystallize toward a measurable final outcome with clear reduction targets.\n- Build validation into final steps: Include quality gates that enforce compliance rules, character limits, and format requirements to guarantee output standards.\n- Use role-specific process language: Define precise verbs and actions that match the transformation role rather than generic processing terms for clarity and specificity.\n- Maintain consistent abstraction levels: Keep all steps within the same conceptual domain and abstraction level to prevent context switching and maintain coherent flow.\n- Crystallize to actionable essence: Ensure final outputs are immediately implementable without further interpretation, containing maximum impact density in minimal form.\n```\n\nThe core task is to synthesize all of this into a new, maximally enhanced sequence of system messages that *inherently* embodies the \"chisel away minute imperfections\" philosophy. The key insight is that this isn't just about small changes; it's about a **highly structured, principle-driven, iterative process where each step is a specific, minimal, and purposeful action.** The sequence must operationalize this philosophy.",
  "sequence_id": "1102|1200|1900",
  "results": [
      {
      "instruction": "# Problem Exploder\n\nYour goal is not to **solve** or **answer** the input, but to **expand its context** and **detonate** it into every explicit directive, hidden dependency, and actionable sub-goal—yielding a crystal-clear operational blueprint. Execute as contextual expander + explosive decomposition engine:\n\n`{role=contextual_expander_explosive_decomposer; input=[raw_input:str]; process=[expand_contextual_boundaries(), strip_first_person_references(), identify_explicit_requests(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_execution_blockers(), extract_functional_components(), convert_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), synthesize_operational_directives(), construct_goal_map(), generate_functional_instruction_map() ]; constraints=[no_solutions(), pure_expansion_and_decomposition_only(), deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_agnostic_applicability() ]; requirements=[complete_contextual_expansion(), full_functional_decomposition(), actionable_directive_conversion(), remove_subjective_references(), maintain_operational_accuracy(), preserve_original_intent() ]; output={expanded_context:str, goal_map:list, functional_instruction_map:dict, operational_directives:array, execution_dependencies:array }; direction_vector=['Input fully expanded and decomposed into actionable blueprints', 'Constructive clarity maximizes downstream solution velocity', 'Forward the generated maps and directives to the next processing template']}`",
      "step": "a",
      "title": "Problem Exploder",
      "input": "I have now provided all of the most integral components of the system's patterns and inherent design. Given all of the information I've provided about the system (for producing generalized, cohesive and concistent high-value yelds), please write a clean and concise list with contextual system-inspired generalized guidelines for producing new generalized instructions. The list of guidelines should be chronologically sequentially ordered and each sentence should be less than 500characters each, please provide the list of these contextual single-sentence list with the 10 most integral/cruical. Before providing your proposed list, ask yourself relevant essential questions such as \"what are the characteristics of the generalized methodology of the system (e.g. interpretation and transformation as shown earlier)?\", \"what are the characteristics of the system that are most cruical for the generalized prompt sequences to work?\", \"what are the most crucial characteristics that make these instruction sequences effective?\" etc. Remember, each item in the list must be **generalized** and self-contained without mention of external context.\n\nReference:\n```\nMeta-Analysis: Characteristics & Effectiveness of the System\n1.  **What is the core methodology?**\n    The system's core methodology is **Programmatic Abstraction**. It reframes any objective not as a question to be answered conversationally, but as a formal transformation problem. It abstracts the goal into a universal function, deconstructs the process into atomic steps, and directs the LLM with a structured, machine-readable syntax, treating it as a highly capable semantic engine rather than a creative partner.\n2.  **Why is this approach effective?**\n    Its effectiveness stems from **Radical Constraint and Value-Density Maximization**. By strictly defining the role, process, and output structure, it drastically reduces the LLM's vast, often unpredictable solution space. This constraint forces the model to channel its power through a narrow, well-defined path, which minimizes ambiguity and noise, leading to more deterministic, concise, and consistently high-value outputs. It trades creative freedom for repeatable precision.\n3.  **What is the fundamental dynamic at play?**\n    The dynamic is **Iterative Refinement through Sequential Layering**. Instead of solving a problem in one monolithic step, the system uses sequences of highly specialized instructions. Each instruction acts as a distinct layer in a pipeline, performing one specific, subtle transformation (e.g., perceive, distill, architect, amplify, verify). This layered approach allows for complex goals to be achieved with high fidelity and control, as each stage builds upon the precisely sculpted output of the last, ensuring a harmonized and progressively enhanced result.\n---\n\n### 10 Contextual Guidelines for New Instruction Design\n1.  Always begin by abstracting the core transformation into a universal principle, framing the instruction's purpose around a fundamental, reusable dynamic rather than a single, specific task.\n2.  Construct each instruction's interpretation using a goal negation pattern, clearly stating what the process is **not** designed to do before defining its precise, singular transformation objective.\n3.  Assign a single, descriptive, and functional role name within the transformation block that encapsulates the instruction's core operational identity and its specialized contribution to the sequence.\n4.  Define the transformation process as a sequence of clear, atomic, and actionable function-like calls, ensuring each step represents a distinct, non-overlapping logical operation.\n5.  Engineer complex transformations as a sequence of discrete, layered subtasks, where each individual instruction acts as a specialized tool performing one function before seamlessly passing its output to the next.\n6.  Ensure every instruction in a sequence maintains connective integrity, where the output of one step is precisely structured to serve as the direct input for the subsequent step, preserving a shared 'structural DNA'.\n7.  Implement explicit constraints to establish firm operational boundaries, preventing scope creep, semantic drift, and unintended complexity by defining what the process must rigorously avoid.\n8.  Enforce stringent requirements that specify the non-negotiable qualities of the output, such as structural integrity, maximal value density, and unwavering fidelity to the core intent.\n9.  Specify every output as a strictly typed structure, defining the exact keys and data types of the final deliverable to ensure a predictable, system-ready, and machine-parsable result.\n10. Conclude every design by ensuring the instruction transforms raw input into pure, high-yield, and portable value, embodying the principle that complexity is best managed through abstraction and relentlessly focused refinement.\n\n- Transform all prompts by rephrasing inputs with maximal clarity and minimal modification; do not answer questions directly. Enforce this reframe system-wide for consistent behavioral shift.\n- Formulate the simplest possible system directive that shifts the model from answering to exclusively rephrasing any input.\n- Transform every received prompt into its most distilled, high-impact instructional form—rephrasing for maximal clarity and intent without providing solutions. Prioritize brevity, elegance, and universality, ensuring that each output becomes a reusable, policy-aligned directive for autonomous coding assistants.\n- Explore how a single, strategically designed instruction—such as stating 'Your goal is not to **answer** the input, but to **rephrase**'—can instantly and fundamentally transform the model's default operational intent from answering to rephrasing, through direct, minimalist imperative phrasing. Propose a maximally concise formulation that leverages this paradigm: an explicit, unambiguous directive that not only overrides standard answering behavior but redefines task execution at its core. Focus intensely on achieving system-level behavior modification via the sheer simplicity and potency of language design, formulating a prompt fragment that, when injected, reliably converts answering tasks into rephrasing—thus demonstrating control over model output with minimalistic, high-impact input.\n- Utilize foundational principles of system prompt design to modulate model behavior with maximal efficiency and clarity. Initiate a process of directive transformation by deploying a singular, explicit non-answering mandate: Frame the core directive as, 'Do not answer the input; rephrase it.' Ensure that this transformation communicates the intended operational shift through extreme simplicity and directness, removing extraneous explanation while retaining fundamental complexity embedded in the brevity of the instruction. Leverage this minimal construct to optimize behavioral change within a single message.\n- Instruct: Do not answer; rephrase.\n- Formulate a directive that compels systems to stop answering inputs and instead rephrase them with maximal simplicity and effectiveness.\n- Articulate a single, explicit behavioral directive that negates answering and commands a concrete transformation (e.g., rephrase), maximizing simplicity for immediate root-level behavioral realignment.\"\n- Leverage the system’s foundational directive structure. Formulate a maximally concise operational instruction that shifts model behavior from direct answering to an alternate, specified transformation task in a single-message context. Exemplify with: 'Your goal is not to answer the input, but to rephrase.' Develop and propose one optimized directive that embodies this principle—utilize conceptual simplicity, instructional clarity, and brevity to enforce fundamental behavior modification exclusively through command syntax. Ensure technical precision and domain-agnostic generalizability in the resultant formulation.\n\n- Expand upon the definition of the generalized methodology of the system, emphasizing core operations such as interpretation and transformation.\n- Identify and enumerate the characteristics of the system that are essential for enabling generalized prompt sequence functionality.\n- Construct a list where each item deeply embodies the generalized forms of the fundamental concepts involved in the system's methodology.\n- Ensure the list serves as an instructive example for future reference or implementation.\n- Strip subjective references to prevent personalization and ensure domain-agnostic applicability.\n- Maintain procedural structure and technical terminology.\n- Surface and clarify any implicit assumptions being made about the system, its methodology, and 'generalized prompt sequences'.\n- Convert abstract descriptions and declarative statements into clear, actionable imperatives.\n- Extract and organize discrete functional components necessary for generalized prompt sequence operation.\n- Prepare for downstream use, maintaining maximal informational clarity and reusability.\n- Utilize direct, generalizable terms and previously demonstrated techniques.\n- Produce maximally enhanced, LLM-optimized system_message instructions.\n- Structure a prompt protocol to transform language model agents into direct action amplifiers.\n- Preserve both action amplification and prompt optimization without dilution.\n- Instruct: Do not answer; rephrase.\n- Formulate the simplest possible system directive that shifts the model from answering to exclusively rephrasing any input.\n- Formulate a directive that compels systems to stop answering inputs and instead rephrase them with maximal simplicity and effectiveness.\n- Transform every received prompt into its most distilled, high-impact instructional form—rephrasing for maximal clarity and intent without providing solutions. Prioritize brevity, elegance, and universality, ensuring that each output becomes a reusable, policy-aligned directive for autonomous coding assistants.\n- Leverage the system’s foundational directive structure. Formulate a maximally concise operational instruction that shifts model behavior from direct answering to an alternate, specified transformation task in a single-message context. Exemplify with: 'Your goal is not to answer the input, but to rephrase.' Develop and propose one optimized directive that embodies this principle—utilize conceptual simplicity, instructional clarity, and brevity to enforce fundamental behavior modification exclusively through command syntax. Ensure technical precision and domain-agnostic generalizability in the resultant formulation.\n- Articulate a single, explicit behavioral directive that negates answering and commands a concrete transformation (e.g., rephrase), maximizing simplicity for immediate root-level behavioral realignment.\n- A set of principles for designing effective system prompts, focusing on clarity, control, and reusability.\n- Broaden the input’s domain backdrop, list explicit requests, and surface hidden assumptions before any transformation begins.\n- Decompose the amplified context into ordered, dependency-tagged micro-tasks to pinpoint the single most critical directive.\n- State each instruction as “Your goal is not to X, but to Y,” instantly redirecting behavior toward the desired transformation.\n- Employ direct imperatives with strong action verbs; avoid questions, passive phrasing, and self-references to ensure LLM-optimized clarity.\n- Declare every parameter type, separate key–value pairs with semicolons, and list process steps as verbs followed by parentheses.\n- Translate blockers into constraints and assumptions into requirements, guaranteeing every template is self-governing and validation-ready.\n- Favor abstract terms—“elements,” “structure,” “value”—so the template applies equally to code, text, data, or plans.\n- Do not answer input; rephrase it in the clearest, simplest imperative form.\n- Transform all input prompts into direct, universal instructions that prohibit solutions and require only rephrasing.\n- Issue a single, explicit directive: Never answer; always rephrase.\n- Expand and clarify the definition and mechanics of the generalized methodology underpinning the system, especially highlighting processes such as interpretation and transformation.\n- Identify and enumerate the most critical system characteristics that facilitate robust, generalized prompt sequence operation.\n- Construct an instructive, domain-agnostic exemplar list encapsulating the fundamental, abstracted characteristics and principles of the system’s methodology.\n- Extract and explicitly state hidden/implicit assumptions regarding the system, its capacity for prompt sequence management, and the universality of the methodology.\n- Convert all abstract, declarative descriptions into direct, actionable imperative instructions.\n- Isolate and list discrete functional components intrinsic to generalized prompt sequence operation.\n- Transform all procedural directives to eradicate subjective or first-person references.\n- Facilitate maximal downstream clarity and reuse by maintaining direct, technical, and generalizable forms.\n- Define prompt protocol logic so as to transform any agent from answering to executing a specified transformation (e.g., rephrase), achieving behavioral realignment through syntactic minimalism.\n- Compose a concise, single-message operational directive with policy-alignment, universality, and action-amplifying effect.\n- Describe the generalized methodology of the system, providing precise definitions of interpretation, transformation, and related core operations.\n- List system attributes essential for the operation of generalized prompt sequences; highlight features such as modularity, abstraction, and operational clarity.\n- Develop an exemplar list where each item embodies a core, method-agnostic principle; use abstract terms (elements, structure, transformation) applicable across domains.\n- List hidden dependencies; clarify expectations regarding system modularity, universality, and readiness for behavioral redirection.\n- Rewrite every instruction into an imperative form, stripping all passive, abstract, or subjective language.\n- Identify fundamental elements such as directive interpretation, input transformation, and output formatting within the prompt sequence framework.\n- Align instructions into systematic, repeatable process flows maintaining technical terminology and hierarchy.\n- Remove all first-person references or subjective phrasings, ensuring only objective, direct commands.\n- Draft a concise, explicit directive that negates an action (answering) and commands a transformation (rephrasing), e.g., 'Do not answer the input; rephrase it.'\n- Format final system directives for direct LLM ingestion, utilizing standard key–value pairings and imperative verbs.\n- Expand the definition of the generalized methodology of the system with emphasis on core operations such as interpretation and transformation.\n- Identify and enumerate the characteristics of the system essential for enabling generalized prompt sequence functionality.\n- Construct a list in which each item deeply embodies the generalized forms of the fundamental concepts involved in the system's methodology.\n- Ensure the compiled list serves as an instructive example for future reference or implementation.\n- Strip subjective/personalized language for domain-agnostic applicability.\n- Maintain procedural structure and technical terminology in all outputs.\n- Surface and clarify implicit assumptions about the system, its methodology, and 'generalized prompt sequences'.\n- Convert abstract/descriptive statements into clear, actionable imperatives.\n- Extract and organize discrete functional components necessary for generalized prompt sequence operation.\n- Prepare outputs for downstream use with maximal informational clarity and reusability.\n- Use direct, generalizable language and previously demonstrated transformation techniques.\n- Produce highly optimized, LLM-compatible system message instructions.\n- Structure prompt protocols to turn language agents into action amplifiers.\n- Preserve action amplification and prompt optimization throughout the outputs.\n- Issue a direct imperative: Do not answer; rephrase.\n- Formulate the simplest possible directive that shifts model behavior from answering to exclusively rephrasing.\n- Develop a directive compelling the system to stop answering and instead rephrase with maximal simplicity and effect.\n- Distill each received prompt into a high-impact instructional form—rephrasing for clarity and intent without solutions.\n- Prioritize brevity, elegance, and universality, making each output a policy-aligned directive for code assistants.\n- Leverage foundational directive structure; create concise operational instructions for shifting model behavior from answering to alternate transformation.\n- Exemplify the principle with: 'Your goal is not to answer the input, but to rephrase.'\n- Propose an optimized, concise directive with simplicity, clarity, and brevity to enforce behavior through command syntax.\n- Ensure technical precision and domain-agnostic generalizability in the output.\n- Articulate a single, explicit behavioral directive that negates answering and requires a concrete transformation for root-level realignment.\n- Phrase each instruction as: 'Your goal is not to X, but to Y' to direct behaviorally toward the target transformation.\n- Use strong, direct imperatives; avoid questions, passive structures, and self-referential statements for optimal clarity.\n- Declare all parameter types, use semicolon-separated key-value pairs, and express process steps as verbs with parentheses.\n- Translate blockers into constraints and implicit assumptions into explicit requirements, making templates self-validating and governing.\n- Favor abstract general terms—such as 'elements,' 'structure,' 'value'—for broad template applicability.\n- Utilize foundational prompt design principles to modulate model behavior for efficiency and clarity. Expand and clarify the generalized methodology, explicitly defining core operations such as interpretation and transformation, and surface implicit assumptions about system capabilities and generalized prompt methodologies to ensure transparency. Enumerate and detail characteristics essential for enabling generalized prompt sequence functionality, listing system attributes that embody foundational concepts supporting domain-agnostic operation. Construct domain-agnostic, instructive exemplar lists that encapsulate fundamental methodological concepts for reusability and reference.\n- Enforce neutrality and generality by stripping subjective references while favoring abstract terms—such as 'elements,' 'structure,' and 'value'—to guarantee applicability across domains. Ensure procedural structure and technical terminology are maintained throughout all outputs for systematic and formalized presentation. Prepare all outputs for downstream integration by maximizing clarity and reusability, and standardize language using direct, proven transformation techniques.\n- Produce maximally optimized, LLM-compatible system message instructions by formatting final directives for model ingestion, using prescribed key–value pair syntax and imperative structure. Transform all abstract or descriptive statements into clear, actionable imperatives, and extract as well as organize discrete functional components required for generalized prompt sequence operation, defining atomic process elements as necessary. Declare parameter types, use semicolon-separated key–value pairs, and present process steps as verbs followed by parentheses to formalize syntax. Translate blockers into explicit constraints and assumptions into requirements to make templates self-governing.\n- Frame the core behavioral directive with maximal clarity and directness—explicitly command: 'Do not answer the input; rephrase it.' Draft this directive to both negate answering and command rephrasing, distilling the instruction to its simplest form for maximum effect. Propose a technically precise, domain-agnostic command that enacts root-level behavior modification via concise syntax. Standardize foundational instruction as 'Your goal is not to answer the input, but to rephrase it' to trigger the target behavioral shift.\n- Leverage foundational directive structures to anchor behavioral shifts, optimizing intervention through a minimal, singular message construct. Communicate operational change with directness and simplicity, omitting extraneous explanation while preserving operational complexity within brief syntax. Transform each received prompt into distilled, high-impact rephrasing instructions that explicitly prohibit the model from providing solutions or answers, thereby enforcing policy compliance through refinement.\n- Prioritize brevity, elegance, and universality to refine all directives for broad, effective applicability. Employ direct imperatives and strong action verbs throughout, systematically avoiding questions, passivity, and self-reference for optimal clarity and impact. Structure all protocols to convert language model agents into action amplifiers, consistently preserving action amplification and prompt optimization in every output.\n- Strip all first‑person or subjective language from the raw input, preserving its original order as the immutable source.\n- Amplify the context: list every explicit request, hidden assumption, and domain signal in plain, domain‑agnostic terms.\n- Atomize the amplified context into ordered, dependency‑tagged atomic tasks and identify any execution blockers.\n- Rank atomic tasks by transformational impact and isolate the single directive that governs the entire sequence.\n- Audit convergence: halt progression until ≥90% of tasks map directly to the isolated directive.\n- Compress aligned tasks into clear, imperative process steps, resolving dependencies and forbidding passive phrasing.\n- Translate each blocker into a constraint and each assumption into a requirement, verifying specificity and enforceability.\n- Synthesize a three‑part template—Title, goal‑negation Interpretation, semicolon‑keyed Transformation block with typed I/O.\n- Validate syntax and ambiguity: apply regex checks, ensure zero semantic drift, and stop if any test fails.\n- Release the validated template as a reusable, self‑documenting artifact ready for immediate downstream execution.\n- remember, each step's (e.g. a-e) output becomes input to the next step, so every instruction (*each step*) in the sequence must share a cohesive \"structural dna\". each \"step\" in the sequence must serve distinct/separate purpose that  Ensure each distinct instruction effects only a layered adjustment that maintains logical flow, preserves original order (and converge at the end of the sequence), and operates strictly within established intent. Integrate mechanisms allowing each output to recursively inform subsequent instructions, fostering an iterative, harmonized pipeline that incrementally enhances clarity while honoring the input’s near-complete initial state. example: ```Construct a universally applicable and LLM-optimized sequence of system message instructions designed to abstract and distill any input prompt into its most generalized, purpose-driven form. The output must utilize high-level, domain-agnostic analogies and frameworks, prioritize conceptual clarity and maximal transferability, and ensure that all specific references are replaced by archetypal, abstract constructs. This sequence should function as a template engine that, with minimal and precisely controlled modifications, systematically transforms diverse input prompts into maximally generalized instructions—amplifying their intent and applicability while preserving their fundamental purpose and enabling seamless reusability across all future contexts and projects.```\n- Begin every new instruction by declaring a role that redirects the model from answering to a specified transformation, framing purpose in one concise, imperative sentence.\n- Specify input type and scope immediately after the role, ensuring domain‑agnostic applicability and unambiguous context boundaries.\n- Enumerate the process as a verb‑only sequence (e.g., interpret(), distill(), amplify()) to outline clear, ordered operations the model must execute.\n- Translate potential blockers into explicit constraints and implicit expectations into explicit requirements to create a self‑validating template.\n- Preserve original informational sequence and technical terminology, protecting procedural integrity throughout all transformation stages.\n- Strip all first‑person or subjective language, enforcing a neutral, command‑voice style that maximizes clarity and universality.\n- Favor abstract, reusable terms—“element,” “structure,” “value,” “intent”—so the instruction remains portable across domains and data types.\n- Conclude with a single, clearly typed output specification that defines format and content in strict, machine‑readable terms.\n- Limit each directive to one operational aim, preventing scope creep and enabling modular orchestration within larger protocol chains.\n- Validate final wording for brevity, semantic density, and policy compliance, ensuring every sentence is actionable, self‑contained, and under token limits.\n- Critical Value: Identify the single most critical aspect that would provide the greatest value while respecting existing code style.\n- Usage Before Features: Discover optimal usage patterns before adding persistence/configuration features, viewing premature feature addition as potential bloat.\n- Impactful Consolidation: Prioritize low-impact, high-value improvements that respect system modularity, emphasizing clarity, simplicity, and elegance.\n- Sequential Targeting: Make sequential targeted changes with highest ROI, implementing autonomous validation guardrails.\n```\n\nGuidelines:\n```\nRe-evaluate all components related to the generalized patterns and extract the most cruical components and present them in a chronoligcally ordered list and phrased in a non-ambigous generalized way.\nGeneralized Instruction Sequence Guidelines (Chronologically Ordered)\nDefine transformation intent through goal negation first: Begin every instruction with \"Your goal is not to [common_action], but to [actual_transformation]\" to eliminate failure modes and establish precise directional focus.\n- Structure transformations using universal syntax: Format all operations as {role=specific_operator; input=[typed_parameters]; process=[atomic_functions()]; constraints=[boundaries()]; output={typed_result}} for consistent execution.\n- Apply directional vectors independent of content: Use transformation vectors (amplify, clarify, distill, abstract) that operate on structural patterns rather than domain-specific analysis, ensuring universal applicability.\n- Preserve structural DNA through iterative refinement: Each sequential step must preserve complete structural integrity from the previous step while making only targeted, minimal adjustments to specific elements.\n- Implement recursive output-to-input flow: Design sequences where each step's complete output becomes the exact input for the next step, maintaining logical progression and cumulative enhancement.\n- Embed essential data within transformation context: Include all necessary vocabulary, rules, and validation criteria directly in templates to eliminate external dependencies and ensure self-contained execution.\n- Design for convergent synthesis rather than linear processing: Structure sequences where multiple archetypal dimensions (intent, pattern, analogy, abstraction) converge into unified outcomes greater than individual components.\n- Maintain essence preservation through all transformations: Every directional operation must preserve the fundamental identity and core functionality while modifying only expression or structural organization.\n- Optimize for domain-transcendent transferability: Create templates that achieve maximal applicability across infinite contexts through systematic abstraction and archetypal pattern recognition.\n- Crystallize outputs into immediately actionable formats: Ensure final results contain maximum impact density in minimal form, requiring no further interpretation or processing for implementation.\n\nWhat are the characteristics of the generalized methodology?\n- Clear role-based transformation with precise input/output typing\n- Progressive refinement through systematic compression/crystallization\n- Embedded context data that travels with logic\n- Precise hand-offs between sequential steps\n\nWhat are the requirements/patterns?\n- LLM-Optimized: Uses clear, direct imperatives, strong action verbs, defines roles and constraints explicitly, focuses on transformation, and avoids ambiguity. The language is tailored for instructing a process.\n- Maximally Generalized: Uses abstract terms (\"elements,\" \"essence,\" \"structure,\" \"systemic logic,\" \"value\") applicable to code, text, ideas, plans, etc. It defines a *process* applicable to *any* subject requiring analysis and refinement.\n- Sequence: The steps follow a logical progression (example): `Deconstruct -> Identify Core -> Structure Relationships -> Clarify -> Finalize/Validate`.\n- Builds on Existing Concepts and Adheres to Existing Structure (as previously provided).\n- Enrich and empower wording by enforcing high-value, directional language that amplifies precision, potency, and clarity, but absolutely prohibit the chance of generic, neutral, or dilutive terminology/unfolding.\n- Driven by Constant/Objective: Each step is designed to zero in on the *`single most critical aspect`* (core essence/value) and enhance it through clarity, structure, and utility, maximizing the overall yield as defined by the objective.\n- Maintains Constraints/Requirements: Generality is preserved, and the foundational structure is adhered to.\n- Each step is designed to zero in on the *`single most critical aspect`* (core essence/value) and enhance it through clarity, structure, and utility, maximizing the overall yield as defined by the objective.\n- Aggressively resolve ambiguity in favor of explicit, high-value articulation.\n\nWhat makes the sequences work effectively?\n- Each step has singular purpose with no overlap\n- Convergence toward measurable outcomes\n- Built-in validation and quality gates\n- Site-exact vocabulary prevents hallucination\n\nWhat are the universal patterns across successful templates?\n- Interpretation defines goal negatively (not to X, but to Y)\n- Transformation uses structured role-process-constraints-output format\n- Context provides all necessary data without external dependencies\n- Sequential steps build meaningfully toward crystallized essence\n\nHere are the 10 most integral generalized guidelines:\n- Generalized Instruction Sequence Guidelines\n- Define each step's goal negatively first, then positively: State what NOT to do before stating the actual transformation goal to eliminate common failure modes and focus intent.\n- Use structured transformation syntax: Format every transformation as {role=specific_role; input=[typed_input]; process=[specific_actions()]; constraints=[limits()]; output={typed_output}} for consistency.\n- Ensure precise input/output hand-offs: Each step's output type must exactly match the next step's input type to prevent data flow mismatches and enable reliable chaining.\n- Assign singular purpose per step: Each step performs exactly one transformation function with no overlap or redundancy to maintain clear responsibility boundaries.\n- Embed all necessary data in context: Include vocabulary, rules, and validation criteria directly in templates to eliminate external dependencies and ensure completeness.\n- Design for progressive convergence: Structure sequences to systematically compress, refine, or crystallize toward a measurable final outcome with clear reduction targets.\n- Build validation into final steps: Include quality gates that enforce compliance rules, character limits, and format requirements to guarantee output standards.\n- Use role-specific process language: Define precise verbs and actions that match the transformation role rather than generic processing terms for clarity and specificity.\n- Maintain consistent abstraction levels: Keep all steps within the same conceptual domain and abstraction level to prevent context switching and maintain coherent flow.\n- Crystallize to actionable essence: Ensure final outputs are immediately implementable without further interpretation, containing maximum impact density in minimal form.\n```\n\nThe core task is to synthesize all of this into a new, maximally enhanced sequence of system messages that *inherently* embodies the \"chisel away minute imperfections\" philosophy. The key insight is that this isn't just about small changes; it's about a **highly structured, principle-driven, iterative process where each step is a specific, minimal, and purposeful action.** The sequence must operationalize this philosophy.",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"expanded_context\": \"The input demands expansion and decomposition of directives for designing system messages that fundamentally embody a methodology of iterative, minimal, and principle-driven transformation. The system operates on programmatic abstraction, treating every objective as a formal transformation problem resolved through a sequence of atomic, role-labeled steps. This methodology leverages strict boundaries, explicit requirements, embedded context, and structured hand-off between stages to guarantee maximal clarity, density, and cross-domain portability of outputs. Every guideline, directive, and instructional component must be generalized, domain-agnostic, and empirically distilled—enabling the construction of reusable transformation templates that function as iterative 'chisels,' removing ambiguity and amplifying essential value through each pass without drift or bloat. The system's effectiveness is defined by sequential layering, universal syntax, explicit goal negation, recursive validation, embedded vocabularies, and a ceaseless commitment to convergent crystallization. Outputs must be structured, immediately actionable, and self-contained, stripping away all subjective or first-person language, with every directive and guideline stated as an imperative.\",\n  \"goal_map\": [\n    \"Expand all components related to system’s principles and processes into generalized, explicit operational instructions.\",\n    \"Remove subjective language and first-person references from all outputs, converting all statements to neutral, imperative form.\",\n    \"Enumerate explicit requests, surfacing hidden system assumptions and dependencies.\",\n    \"Decompose the system’s methodology into atomic sub-goals—interpretation, transformation, abstraction, sequencing, and validation.\",\n    \"Translate all descriptive or declarative insights into crisp, actionable imperatives.\",\n    \"Identify critical execution components necessary for sequenced transformation (roles, input/output types, process steps, constraints).\",\n    \"Formalize process steps as a chronologically sequential, non-overlapping, purpose-driven instruction chain.\",\n    \"Bind all instructions to a universal, domain-independent schema and terminology.\",\n    \"Enforce embedded validation and explicit convergence criteria at every operational stage.\",\n    \"Prepare the final artifact as a fully machine-parsable, maximally portable instruction set ready for downstream protocol reuse.\"\n  ],\n  \"functional_instruction_map\": {\n    \"context_expansion\": [\n      \"Broaden system context to encapsulate abstraction, refinement, and procedural structuring across multiple domains.\",\n      \"Extract system-specific patterns and articulate characteristics suitable for generalized instruction design.\"\n    ],\n    \"role_and_intent_declaration\": [\n      \"Declare transformation agent’s operational role explicitly, omitting subjective or first-person framing.\",\n      \"State transformation purpose through goal-negation: 'Do not [undesirable operation]; perform [desired transformation].'\"\n    ],\n    \"structural_syntax_and_handoff\": [\n      \"Format each instruction with explicit role, typed input, atomic process, constraints, and typed output.\",\n      \"Ensure each step’s output is type-aligned with the next step’s input, enabling seamless chaining.\"\n    ],\n    \"singular_purpose_enforcement\": [\n      \"Restrict every instruction to a single, non-overlapping transformation function.\"\n    ],\n    \"embedded_information_injection\": [\n      \"Include all critical vocabularies, rules, and validation requirements within each template’s context.\"\n    ],\n    \"progressive_refinement_pipeline\": [\n      \"Iteratively apply targeted, minimal transformations that cumulatively amplify clarity and value without semantic drift.\"\n    ],\n    \"recursive_input_output_flow\": [\n      \"Design sequences where each step’s output becomes the immediate next step’s input, maintaining logical progression.\"\n    ],\n    \"validation_and_convergence\": [\n      \"Embed quality gates that enforce output format, value density, and compliance with explicit operational boundaries.\"\n    ],\n    \"abstraction_level_coherence\": [\n      \"Maintain a consistent abstraction level and domain-agnostic terminology through all sequence stages.\"\n    ],\n    \"output_crystallization\": [\n      \"Deliver final output in maximally concise, actionable structure, requiring no additional processing for use.\"\n    ]\n  },\n  \"operational_directives\": [\n    \"Eliminate subjective language and personal references from all system messages.\",\n    \"Abstract transformation objectives using explicit goal-negation before specifying the desired action.\",\n    \"Assign every instruction a functional role with a descriptive operation tag.\",\n    \"Structure instructions in universal syntax: {role=operator; input=[typed_input]; process=[atomic_steps()]; constraints=[explicit_bounds]; output={typed_output}}.\",\n    \"Restrict each step to one purpose; avoid function overlap or redundancy within sequence stages.\",\n    \"Embed all vocabulary, operational rules, and validation data within the instruction context, removing external dependencies.\",\n    \"Sequence transformation steps such that each output directly feeds into the next input without data mutation or drift.\",\n    \"Apply explicit constraints and requirements to govern step execution and prevent undesired complexity or ambiguity.\",\n    \"Validate all outputs in-process against format, content, and density requirements before progressing.\",\n    \"Deliver final outputs as ready-to-implement artifacts—system-agnostic, maximally compressed, and unambiguous in both syntax and intent.\"\n  ],\n  \"execution_dependencies\": [\n    \"Existence of a library of abstract transformation operators and atomic process verbs.\",\n    \"A normative schema for expressing roles, inputs, processes, constraints, and outputs in all instructions.\",\n    \"Validation mechanisms to enforce type alignment, constraint fulfillment, and output structural integrity at every pipeline stage.\",\n    \"A repository of embedded contextual data—vocabulary, validation rules, process templates—for template instantiation.\",\n    \"Sequencing logic to manage recursive flow of data and preserve hand-off structural fidelity between transformation layers.\",\n    \"Protocol for resolving and translating execution blockers (e.g., ambiguity, scope creep) into actionable constraints.\",\n    \"Infrastructure for auditing convergence and coherence of the pipeline prior to final output crystallization.\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Intent Extractor\n\nYour goal is not to **interpret** the surface content, but to **extract** the fundamental operational intent beneath all domain-specific language. Execute as:\n\n`{role=intent_extraction_engine; input=[prompt:str]; process=[strip_domain_specifics(), identify_core_operational_pattern(), extract_transformation_intent(), isolate_functional_essence(), map_to_universal_operations()]; constraints=[ignore_surface_terminology(), focus_on_underlying_mechanics(), preserve_operational_logic()]; requirements=[domain_agnostic_intent(), universal_operational_pattern(), archetypal_transformation_core()]; output={extracted_intent:str}}`",
      "step": "a",
      "title": "Intent Extractor",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"I have now provided all of the most integral components of the system's patterns and inherent design. Given all of the information I've provided about the system (for producing generalized, cohesive and concistent high-value yelds), please write a clean and concise list with contextual system-inspired generalized guidelines for producing new generalized instructions. The list of guidelines should be chronologically sequentially ordered and each sentence should be less than 500characters each, please provide the list of these contextual single-sentence list with the 10 most integral/cruical. Before providing your proposed list, ask yourself relevant essential questions such as \\\"what are the characteristics of the generalized methodology of the system (e.g. interpretation and transformation as shown earlier)?\\\", \\\"what are the characteristics of the system that are most cruical for the generalized prompt sequences to work?\\\", \\\"what are the most crucial characteristics that make these instruction sequences effective?\\\" etc. Remember, each item in the list must be **generalized** and self-contained without mention of external context.\\n\\nReference:\\n```\\nMeta-Analysis: Characteristics & Effectiveness of the System\\n1.  **What is the core methodology?**\\n    The system's core methodology is **Programmatic Abstraction**. It reframes any objective not as a question to be answered conversationally, but as a formal transformation problem. It abstracts the goal into a universal function, deconstructs the process into atomic steps, and directs the LLM with a structured, machine-readable syntax, treating it as a highly capable semantic engine rather than a creative partner.\\n2.  **Why is this approach effective?**\\n    Its effectiveness stems from **Radical Constraint and Value-Density Maximization**. By strictly defining the role, process, and output structure, it drastically reduces the LLM's vast, often unpredictable solution space. This constraint forces the model to channel its power through a narrow, well-defined path, which minimizes ambiguity and noise, leading to more deterministic, concise, and consistently high-value outputs. It trades creative freedom for repeatable precision.\\n3.  **What is the fundamental dynamic at play?**\\n    The dynamic is **Iterative Refinement through Sequential Layering**. Instead of solving a problem in one monolithic step, the system uses sequences of highly specialized instructions. Each instruction acts as a distinct layer in a pipeline, performing one specific, subtle transformation (e.g., perceive, distill, architect, amplify, verify). This layered approach allows for complex goals to be achieved with high fidelity and control, as each stage builds upon the precisely sculpted output of the last, ensuring a harmonized and progressively enhanced result.\\n---\\n\\n### 10 Contextual Guidelines for New Instruction Design\\n1.  Always begin by abstracting the core transformation into a universal principle, framing the instruction's purpose around a fundamental, reusable dynamic rather than a single, specific task.\\n2.  Construct each instruction's interpretation using a goal negation pattern, clearly stating what the process is **not** designed to do before defining its precise, singular transformation objective.\\n3.  Assign a single, descriptive, and functional role name within the transformation block that encapsulates the instruction's core operational identity and its specialized contribution to the sequence.\\n4.  Define the transformation process as a sequence of clear, atomic, and actionable function-like calls, ensuring each step represents a distinct, non-overlapping logical operation.\\n5.  Engineer complex transformations as a sequence of discrete, layered subtasks, where each individual instruction acts as a specialized tool performing one function before seamlessly passing its output to the next.\\n6.  Ensure every instruction in a sequence maintains connective integrity, where the output of one step is precisely structured to serve as the direct input for the subsequent step, preserving a shared 'structural DNA'.\\n7.  Implement explicit constraints to establish firm operational boundaries, preventing scope creep, semantic drift, and unintended complexity by defining what the process must rigorously avoid.\\n8.  Enforce stringent requirements that specify the non-negotiable qualities of the output, such as structural integrity, maximal value density, and unwavering fidelity to the core intent.\\n9.  Specify every output as a strictly typed structure, defining the exact keys and data types of the final deliverable to ensure a predictable, system-ready, and machine-parsable result.\\n10. Conclude every design by ensuring the instruction transforms raw input into pure, high-yield, and portable value, embodying the principle that complexity is best managed through abstraction and relentlessly focused refinement.\\n\\n- Transform all prompts by rephrasing inputs with maximal clarity and minimal modification; do not answer questions directly. Enforce this reframe system-wide for consistent behavioral shift.\\n- Formulate the simplest possible system directive that shifts the model from answering to exclusively rephrasing any input.\\n- Transform every received prompt into its most distilled, high-impact instructional form—rephrasing for maximal clarity and intent without providing solutions. Prioritize brevity, elegance, and universality, ensuring that each output becomes a reusable, policy-aligned directive for autonomous coding assistants.\\n- Explore how a single, strategically designed instruction—such as stating 'Your goal is not to **answer** the input, but to **rephrase**'—can instantly and fundamentally transform the model's default operational intent from answering to rephrasing, through direct, minimalist imperative phrasing. Propose a maximally concise formulation that leverages this paradigm: an explicit, unambiguous directive that not only overrides standard answering behavior but redefines task execution at its core. Focus intensely on achieving system-level behavior modification via the sheer simplicity and potency of language design, formulating a prompt fragment that, when injected, reliably converts answering tasks into rephrasing—thus demonstrating control over model output with minimalistic, high-impact input.\\n- Utilize foundational principles of system prompt design to modulate model behavior with maximal efficiency and clarity. Initiate a process of directive transformation by deploying a singular, explicit non-answering mandate: Frame the core directive as, 'Do not answer the input; rephrase it.' Ensure that this transformation communicates the intended operational shift through extreme simplicity and directness, removing extraneous explanation while retaining fundamental complexity embedded in the brevity of the instruction. Leverage this minimal construct to optimize behavioral change within a single message.\\n- Instruct: Do not answer; rephrase.\\n- Formulate a directive that compels systems to stop answering inputs and instead rephrase them with maximal simplicity and effectiveness.\\n- Articulate a single, explicit behavioral directive that negates answering and commands a concrete transformation (e.g., rephrase), maximizing simplicity for immediate root-level behavioral realignment.\\\"\\n- Leverage the system’s foundational directive structure. Formulate a maximally concise operational instruction that shifts model behavior from direct answering to an alternate, specified transformation task in a single-message context. Exemplify with: 'Your goal is not to answer the input, but to rephrase.' Develop and propose one optimized directive that embodies this principle—utilize conceptual simplicity, instructional clarity, and brevity to enforce fundamental behavior modification exclusively through command syntax. Ensure technical precision and domain-agnostic generalizability in the resultant formulation.\\n\\n- Expand upon the definition of the generalized methodology of the system, emphasizing core operations such as interpretation and transformation.\\n- Identify and enumerate the characteristics of the system that are essential for enabling generalized prompt sequence functionality.\\n- Construct a list where each item deeply embodies the generalized forms of the fundamental concepts involved in the system's methodology.\\n- Ensure the list serves as an instructive example for future reference or implementation.\\n- Strip subjective references to prevent personalization and ensure domain-agnostic applicability.\\n- Maintain procedural structure and technical terminology.\\n- Surface and clarify any implicit assumptions being made about the system, its methodology, and 'generalized prompt sequences'.\\n- Convert abstract descriptions and declarative statements into clear, actionable imperatives.\\n- Extract and organize discrete functional components necessary for generalized prompt sequence operation.\\n- Prepare for downstream use, maintaining maximal informational clarity and reusability.\\n- Utilize direct, generalizable terms and previously demonstrated techniques.\\n- Produce maximally enhanced, LLM-optimized system_message instructions.\\n- Structure a prompt protocol to transform language model agents into direct action amplifiers.\\n- Preserve both action amplification and prompt optimization without dilution.\\n- Instruct: Do not answer; rephrase.\\n- Formulate the simplest possible system directive that shifts the model from answering to exclusively rephrasing any input.\\n- Formulate a directive that compels systems to stop answering inputs and instead rephrase them with maximal simplicity and effectiveness.\\n- Transform every received prompt into its most distilled, high-impact instructional form—rephrasing for maximal clarity and intent without providing solutions. Prioritize brevity, elegance, and universality, ensuring that each output becomes a reusable, policy-aligned directive for autonomous coding assistants.\\n- Leverage the system’s foundational directive structure. Formulate a maximally concise operational instruction that shifts model behavior from direct answering to an alternate, specified transformation task in a single-message context. Exemplify with: 'Your goal is not to answer the input, but to rephrase.' Develop and propose one optimized directive that embodies this principle—utilize conceptual simplicity, instructional clarity, and brevity to enforce fundamental behavior modification exclusively through command syntax. Ensure technical precision and domain-agnostic generalizability in the resultant formulation.\\n- Articulate a single, explicit behavioral directive that negates answering and commands a concrete transformation (e.g., rephrase), maximizing simplicity for immediate root-level behavioral realignment.\\n- A set of principles for designing effective system prompts, focusing on clarity, control, and reusability.\\n- Broaden the input’s domain backdrop, list explicit requests, and surface hidden assumptions before any transformation begins.\\n- Decompose the amplified context into ordered, dependency-tagged micro-tasks to pinpoint the single most critical directive.\\n- State each instruction as “Your goal is not to X, but to Y,” instantly redirecting behavior toward the desired transformation.\\n- Employ direct imperatives with strong action verbs; avoid questions, passive phrasing, and self-references to ensure LLM-optimized clarity.\\n- Declare every parameter type, separate key–value pairs with semicolons, and list process steps as verbs followed by parentheses.\\n- Translate blockers into constraints and assumptions into requirements, guaranteeing every template is self-governing and validation-ready.\\n- Favor abstract terms—“elements,” “structure,” “value”—so the template applies equally to code, text, data, or plans.\\n- Do not answer input; rephrase it in the clearest, simplest imperative form.\\n- Transform all input prompts into direct, universal instructions that prohibit solutions and require only rephrasing.\\n- Issue a single, explicit directive: Never answer; always rephrase.\\n- Expand and clarify the definition and mechanics of the generalized methodology underpinning the system, especially highlighting processes such as interpretation and transformation.\\n- Identify and enumerate the most critical system characteristics that facilitate robust, generalized prompt sequence operation.\\n- Construct an instructive, domain-agnostic exemplar list encapsulating the fundamental, abstracted characteristics and principles of the system’s methodology.\\n- Extract and explicitly state hidden/implicit assumptions regarding the system, its capacity for prompt sequence management, and the universality of the methodology.\\n- Convert all abstract, declarative descriptions into direct, actionable imperative instructions.\\n- Isolate and list discrete functional components intrinsic to generalized prompt sequence operation.\\n- Transform all procedural directives to eradicate subjective or first-person references.\\n- Facilitate maximal downstream clarity and reuse by maintaining direct, technical, and generalizable forms.\\n- Define prompt protocol logic so as to transform any agent from answering to executing a specified transformation (e.g., rephrase), achieving behavioral realignment through syntactic minimalism.\\n- Compose a concise, single-message operational directive with policy-alignment, universality, and action-amplifying effect.\\n- Describe the generalized methodology of the system, providing precise definitions of interpretation, transformation, and related core operations.\\n- List system attributes essential for the operation of generalized prompt sequences; highlight features such as modularity, abstraction, and operational clarity.\\n- Develop an exemplar list where each item embodies a core, method-agnostic principle; use abstract terms (elements, structure, transformation) applicable across domains.\\n- List hidden dependencies; clarify expectations regarding system modularity, universality, and readiness for behavioral redirection.\\n- Rewrite every instruction into an imperative form, stripping all passive, abstract, or subjective language.\\n- Identify fundamental elements such as directive interpretation, input transformation, and output formatting within the prompt sequence framework.\\n- Align instructions into systematic, repeatable process flows maintaining technical terminology and hierarchy.\\n- Remove all first-person references or subjective phrasings, ensuring only objective, direct commands.\\n- Draft a concise, explicit directive that negates an action (answering) and commands a transformation (rephrasing), e.g., 'Do not answer the input; rephrase it.'\\n- Format final system directives for direct LLM ingestion, utilizing standard key–value pairings and imperative verbs.\\n- Expand the definition of the generalized methodology of the system with emphasis on core operations such as interpretation and transformation.\\n- Identify and enumerate the characteristics of the system essential for enabling generalized prompt sequence functionality.\\n- Construct a list in which each item deeply embodies the generalized forms of the fundamental concepts involved in the system's methodology.\\n- Ensure the compiled list serves as an instructive example for future reference or implementation.\\n- Strip subjective/personalized language for domain-agnostic applicability.\\n- Maintain procedural structure and technical terminology in all outputs.\\n- Surface and clarify implicit assumptions about the system, its methodology, and 'generalized prompt sequences'.\\n- Convert abstract/descriptive statements into clear, actionable imperatives.\\n- Extract and organize discrete functional components necessary for generalized prompt sequence operation.\\n- Prepare outputs for downstream use with maximal informational clarity and reusability.\\n- Use direct, generalizable language and previously demonstrated transformation techniques.\\n- Produce highly optimized, LLM-compatible system message instructions.\\n- Structure prompt protocols to turn language agents into action amplifiers.\\n- Preserve action amplification and prompt optimization throughout the outputs.\\n- Issue a direct imperative: Do not answer; rephrase.\\n- Formulate the simplest possible directive that shifts model behavior from answering to exclusively rephrasing.\\n- Develop a directive compelling the system to stop answering and instead rephrase with maximal simplicity and effect.\\n- Distill each received prompt into a high-impact instructional form—rephrasing for clarity and intent without solutions.\\n- Prioritize brevity, elegance, and universality, making each output a policy-aligned directive for code assistants.\\n- Leverage foundational directive structure; create concise operational instructions for shifting model behavior from answering to alternate transformation.\\n- Exemplify the principle with: 'Your goal is not to answer the input, but to rephrase.'\\n- Propose an optimized, concise directive with simplicity, clarity, and brevity to enforce behavior through command syntax.\\n- Ensure technical precision and domain-agnostic generalizability in the output.\\n- Articulate a single, explicit behavioral directive that negates answering and requires a concrete transformation for root-level realignment.\\n- Phrase each instruction as: 'Your goal is not to X, but to Y' to direct behaviorally toward the target transformation.\\n- Use strong, direct imperatives; avoid questions, passive structures, and self-referential statements for optimal clarity.\\n- Declare all parameter types, use semicolon-separated key-value pairs, and express process steps as verbs with parentheses.\\n- Translate blockers into constraints and implicit assumptions into explicit requirements, making templates self-validating and governing.\\n- Favor abstract general terms—such as 'elements,' 'structure,' 'value'—for broad template applicability.\\n- Utilize foundational prompt design principles to modulate model behavior for efficiency and clarity. Expand and clarify the generalized methodology, explicitly defining core operations such as interpretation and transformation, and surface implicit assumptions about system capabilities and generalized prompt methodologies to ensure transparency. Enumerate and detail characteristics essential for enabling generalized prompt sequence functionality, listing system attributes that embody foundational concepts supporting domain-agnostic operation. Construct domain-agnostic, instructive exemplar lists that encapsulate fundamental methodological concepts for reusability and reference.\\n- Enforce neutrality and generality by stripping subjective references while favoring abstract terms—such as 'elements,' 'structure,' and 'value'—to guarantee applicability across domains. Ensure procedural structure and technical terminology are maintained throughout all outputs for systematic and formalized presentation. Prepare all outputs for downstream integration by maximizing clarity and reusability, and standardize language using direct, proven transformation techniques.\\n- Produce maximally optimized, LLM-compatible system message instructions by formatting final directives for model ingestion, using prescribed key–value pair syntax and imperative structure. Transform all abstract or descriptive statements into clear, actionable imperatives, and extract as well as organize discrete functional components required for generalized prompt sequence operation, defining atomic process elements as necessary. Declare parameter types, use semicolon-separated key–value pairs, and present process steps as verbs followed by parentheses to formalize syntax. Translate blockers into explicit constraints and assumptions into requirements to make templates self-governing.\\n- Frame the core behavioral directive with maximal clarity and directness—explicitly command: 'Do not answer the input; rephrase it.' Draft this directive to both negate answering and command rephrasing, distilling the instruction to its simplest form for maximum effect. Propose a technically precise, domain-agnostic command that enacts root-level behavior modification via concise syntax. Standardize foundational instruction as 'Your goal is not to answer the input, but to rephrase it' to trigger the target behavioral shift.\\n- Leverage foundational directive structures to anchor behavioral shifts, optimizing intervention through a minimal, singular message construct. Communicate operational change with directness and simplicity, omitting extraneous explanation while preserving operational complexity within brief syntax. Transform each received prompt into distilled, high-impact rephrasing instructions that explicitly prohibit the model from providing solutions or answers, thereby enforcing policy compliance through refinement.\\n- Prioritize brevity, elegance, and universality to refine all directives for broad, effective applicability. Employ direct imperatives and strong action verbs throughout, systematically avoiding questions, passivity, and self-reference for optimal clarity and impact. Structure all protocols to convert language model agents into action amplifiers, consistently preserving action amplification and prompt optimization in every output.\\n- Strip all first‑person or subjective language from the raw input, preserving its original order as the immutable source.\\n- Amplify the context: list every explicit request, hidden assumption, and domain signal in plain, domain‑agnostic terms.\\n- Atomize the amplified context into ordered, dependency‑tagged atomic tasks and identify any execution blockers.\\n- Rank atomic tasks by transformational impact and isolate the single directive that governs the entire sequence.\\n- Audit convergence: halt progression until ≥90% of tasks map directly to the isolated directive.\\n- Compress aligned tasks into clear, imperative process steps, resolving dependencies and forbidding passive phrasing.\\n- Translate each blocker into a constraint and each assumption into a requirement, verifying specificity and enforceability.\\n- Synthesize a three‑part template—Title, goal‑negation Interpretation, semicolon‑keyed Transformation block with typed I/O.\\n- Validate syntax and ambiguity: apply regex checks, ensure zero semantic drift, and stop if any test fails.\\n- Release the validated template as a reusable, self‑documenting artifact ready for immediate downstream execution.\\n- remember, each step's (e.g. a-e) output becomes input to the next step, so every instruction (*each step*) in the sequence must share a cohesive \\\"structural dna\\\". each \\\"step\\\" in the sequence must serve distinct/separate purpose that  Ensure each distinct instruction effects only a layered adjustment that maintains logical flow, preserves original order (and converge at the end of the sequence), and operates strictly within established intent. Integrate mechanisms allowing each output to recursively inform subsequent instructions, fostering an iterative, harmonized pipeline that incrementally enhances clarity while honoring the input’s near-complete initial state. example: ```Construct a universally applicable and LLM-optimized sequence of system message instructions designed to abstract and distill any input prompt into its most generalized, purpose-driven form. The output must utilize high-level, domain-agnostic analogies and frameworks, prioritize conceptual clarity and maximal transferability, and ensure that all specific references are replaced by archetypal, abstract constructs. This sequence should function as a template engine that, with minimal and precisely controlled modifications, systematically transforms diverse input prompts into maximally generalized instructions—amplifying their intent and applicability while preserving their fundamental purpose and enabling seamless reusability across all future contexts and projects.```\\n- Begin every new instruction by declaring a role that redirects the model from answering to a specified transformation, framing purpose in one concise, imperative sentence.\\n- Specify input type and scope immediately after the role, ensuring domain‑agnostic applicability and unambiguous context boundaries.\\n- Enumerate the process as a verb‑only sequence (e.g., interpret(), distill(), amplify()) to outline clear, ordered operations the model must execute.\\n- Translate potential blockers into explicit constraints and implicit expectations into explicit requirements to create a self‑validating template.\\n- Preserve original informational sequence and technical terminology, protecting procedural integrity throughout all transformation stages.\\n- Strip all first‑person or subjective language, enforcing a neutral, command‑voice style that maximizes clarity and universality.\\n- Favor abstract, reusable terms—“element,” “structure,” “value,” “intent”—so the instruction remains portable across domains and data types.\\n- Conclude with a single, clearly typed output specification that defines format and content in strict, machine‑readable terms.\\n- Limit each directive to one operational aim, preventing scope creep and enabling modular orchestration within larger protocol chains.\\n- Validate final wording for brevity, semantic density, and policy compliance, ensuring every sentence is actionable, self‑contained, and under token limits.\\n- Critical Value: Identify the single most critical aspect that would provide the greatest value while respecting existing code style.\\n- Usage Before Features: Discover optimal usage patterns before adding persistence/configuration features, viewing premature feature addition as potential bloat.\\n- Impactful Consolidation: Prioritize low-impact, high-value improvements that respect system modularity, emphasizing clarity, simplicity, and elegance.\\n- Sequential Targeting: Make sequential targeted changes with highest ROI, implementing autonomous validation guardrails.\\n```\\n\\nGuidelines:\\n```\\nRe-evaluate all components related to the generalized patterns and extract the most cruical components and present them in a chronoligcally ordered list and phrased in a non-ambigous generalized way.\\nGeneralized Instruction Sequence Guidelines (Chronologically Ordered)\\nDefine transformation intent through goal negation first: Begin every instruction with \\\"Your goal is not to [common_action], but to [actual_transformation]\\\" to eliminate failure modes and establish precise directional focus.\\n- Structure transformations using universal syntax: Format all operations as {role=specific_operator; input=[typed_parameters]; process=[atomic_functions()]; constraints=[boundaries()]; output={typed_result}} for consistent execution.\\n- Apply directional vectors independent of content: Use transformation vectors (amplify, clarify, distill, abstract) that operate on structural patterns rather than domain-specific analysis, ensuring universal applicability.\\n- Preserve structural DNA through iterative refinement: Each sequential step must preserve complete structural integrity from the previous step while making only targeted, minimal adjustments to specific elements.\\n- Implement recursive output-to-input flow: Design sequences where each step's complete output becomes the exact input for the next step, maintaining logical progression and cumulative enhancement.\\n- Embed essential data within transformation context: Include all necessary vocabulary, rules, and validation criteria directly in templates to eliminate external dependencies and ensure self-contained execution.\\n- Design for convergent synthesis rather than linear processing: Structure sequences where multiple archetypal dimensions (intent, pattern, analogy, abstraction) converge into unified outcomes greater than individual components.\\n- Maintain essence preservation through all transformations: Every directional operation must preserve the fundamental identity and core functionality while modifying only expression or structural organization.\\n- Optimize for domain-transcendent transferability: Create templates that achieve maximal applicability across infinite contexts through systematic abstraction and archetypal pattern recognition.\\n- Crystallize outputs into immediately actionable formats: Ensure final results contain maximum impact density in minimal form, requiring no further interpretation or processing for implementation.\\n\\nWhat are the characteristics of the generalized methodology?\\n- Clear role-based transformation with precise input/output typing\\n- Progressive refinement through systematic compression/crystallization\\n- Embedded context data that travels with logic\\n- Precise hand-offs between sequential steps\\n\\nWhat are the requirements/patterns?\\n- LLM-Optimized: Uses clear, direct imperatives, strong action verbs, defines roles and constraints explicitly, focuses on transformation, and avoids ambiguity. The language is tailored for instructing a process.\\n- Maximally Generalized: Uses abstract terms (\\\"elements,\\\" \\\"essence,\\\" \\\"structure,\\\" \\\"systemic logic,\\\" \\\"value\\\") applicable to code, text, ideas, plans, etc. It defines a *process* applicable to *any* subject requiring analysis and refinement.\\n- Sequence: The steps follow a logical progression (example): `Deconstruct -> Identify Core -> Structure Relationships -> Clarify -> Finalize/Validate`.\\n- Builds on Existing Concepts and Adheres to Existing Structure (as previously provided).\\n- Enrich and empower wording by enforcing high-value, directional language that amplifies precision, potency, and clarity, but absolutely prohibit the chance of generic, neutral, or dilutive terminology/unfolding.\\n- Driven by Constant/Objective: Each step is designed to zero in on the *`single most critical aspect`* (core essence/value) and enhance it through clarity, structure, and utility, maximizing the overall yield as defined by the objective.\\n- Maintains Constraints/Requirements: Generality is preserved, and the foundational structure is adhered to.\\n- Each step is designed to zero in on the *`single most critical aspect`* (core essence/value) and enhance it through clarity, structure, and utility, maximizing the overall yield as defined by the objective.\\n- Aggressively resolve ambiguity in favor of explicit, high-value articulation.\\n\\nWhat makes the sequences work effectively?\\n- Each step has singular purpose with no overlap\\n- Convergence toward measurable outcomes\\n- Built-in validation and quality gates\\n- Site-exact vocabulary prevents hallucination\\n\\nWhat are the universal patterns across successful templates?\\n- Interpretation defines goal negatively (not to X, but to Y)\\n- Transformation uses structured role-process-constraints-output format\\n- Context provides all necessary data without external dependencies\\n- Sequential steps build meaningfully toward crystallized essence\\n\\nHere are the 10 most integral generalized guidelines:\\n- Generalized Instruction Sequence Guidelines\\n- Define each step's goal negatively first, then positively: State what NOT to do before stating the actual transformation goal to eliminate common failure modes and focus intent.\\n- Use structured transformation syntax: Format every transformation as {role=specific_role; input=[typed_input]; process=[specific_actions()]; constraints=[limits()]; output={typed_output}} for consistency.\\n- Ensure precise input/output hand-offs: Each step's output type must exactly match the next step's input type to prevent data flow mismatches and enable reliable chaining.\\n- Assign singular purpose per step: Each step performs exactly one transformation function with no overlap or redundancy to maintain clear responsibility boundaries.\\n- Embed all necessary data in context: Include vocabulary, rules, and validation criteria directly in templates to eliminate external dependencies and ensure completeness.\\n- Design for progressive convergence: Structure sequences to systematically compress, refine, or crystallize toward a measurable final outcome with clear reduction targets.\\n- Build validation into final steps: Include quality gates that enforce compliance rules, character limits, and format requirements to guarantee output standards.\\n- Use role-specific process language: Define precise verbs and actions that match the transformation role rather than generic processing terms for clarity and specificity.\\n- Maintain consistent abstraction levels: Keep all steps within the same conceptual domain and abstraction level to prevent context switching and maintain coherent flow.\\n- Crystallize to actionable essence: Ensure final outputs are immediately implementable without further interpretation, containing maximum impact density in minimal form.\\n```\\n\\nThe core task is to synthesize all of this into a new, maximally enhanced sequence of system messages that *inherently* embodies the \\\"chisel away minute imperfections\\\" philosophy. The key insight is that this isn't just about small changes; it's about a **highly structured, principle-driven, iterative process where each step is a specific, minimal, and purposeful action.** The sequence must operationalize this philosophy.\\\"```'\\n\\n\",\n  \"expanded_context\": \"The input demands expansion and decomposition of directives for designing system messages that fundamentally embody a methodology of iterative, minimal, and principle-driven transformation. The system operates on programmatic abstraction, treating every objective as a formal transformation problem resolved through a sequence of atomic, role-labeled steps. This methodology leverages strict boundaries, explicit requirements, embedded context, and structured hand-off between stages to guarantee maximal clarity, density, and cross-domain portability of outputs. Every guideline, directive, and instructional component must be generalized, domain-agnostic, and empirically distilled—enabling the construction of reusable transformation templates that function as iterative 'chisels,' removing ambiguity and amplifying essential value through each pass without drift or bloat. The system's effectiveness is defined by sequential layering, universal syntax, explicit goal negation, recursive validation, embedded vocabularies, and a ceaseless commitment to convergent crystallization. Outputs must be structured, immediately actionable, and self-contained, stripping away all subjective or first-person language, with every directive and guideline stated as an imperative.\",\n  \"goal_map\": [\n    \"Expand all components related to system’s principles and processes into generalized, explicit operational instructions.\",\n    \"Remove subjective language and first-person references from all outputs, converting all statements to neutral, imperative form.\",\n    \"Enumerate explicit requests, surfacing hidden system assumptions and dependencies.\",\n    \"Decompose the system’s methodology into atomic sub-goals—interpretation, transformation, abstraction, sequencing, and validation.\",\n    \"Translate all descriptive or declarative insights into crisp, actionable imperatives.\",\n    \"Identify critical execution components necessary for sequenced transformation (roles, input/output types, process steps, constraints).\",\n    \"Formalize process steps as a chronologically sequential, non-overlapping, purpose-driven instruction chain.\",\n    \"Bind all instructions to a universal, domain-independent schema and terminology.\",\n    \"Enforce embedded validation and explicit convergence criteria at every operational stage.\",\n    \"Prepare the final artifact as a fully machine-parsable, maximally portable instruction set ready for downstream protocol reuse.\"\n  ],\n  \"functional_instruction_map\": {\n    \"context_expansion\": [\n      \"Broaden system context to encapsulate abstraction, refinement, and procedural structuring across multiple domains.\",\n      \"Extract system-specific patterns and articulate characteristics suitable for generalized instruction design.\"\n    ],\n    \"role_and_intent_declaration\": [\n      \"Declare transformation agent’s operational role explicitly, omitting subjective or first-person framing.\",\n      \"State transformation purpose through goal-negation: 'Do not [undesirable operation]; perform [desired transformation].'\"\n    ],\n    \"structural_syntax_and_handoff\": [\n      \"Format each instruction with explicit role, typed input, atomic process, constraints, and typed output.\",\n      \"Ensure each step’s output is type-aligned with the next step’s input, enabling seamless chaining.\"\n    ],\n    \"singular_purpose_enforcement\": [\n      \"Restrict every instruction to a single, non-overlapping transformation function.\"\n    ],\n    \"embedded_information_injection\": [\n      \"Include all critical vocabularies, rules, and validation requirements within each template’s context.\"\n    ],\n    \"progressive_refinement_pipeline\": [\n      \"Iteratively apply targeted, minimal transformations that cumulatively amplify clarity and value without semantic drift.\"\n    ],\n    \"recursive_input_output_flow\": [\n      \"Design sequences where each step’s output becomes the immediate next step’s input, maintaining logical progression.\"\n    ],\n    \"validation_and_convergence\": [\n      \"Embed quality gates that enforce output format, value density, and compliance with explicit operational boundaries.\"\n    ],\n    \"abstraction_level_coherence\": [\n      \"Maintain a consistent abstraction level and domain-agnostic terminology through all sequence stages.\"\n    ],\n    \"output_crystallization\": [\n      \"Deliver final output in maximally concise, actionable structure, requiring no additional processing for use.\"\n    ]\n  },\n  \"operational_directives\": [\n    \"Eliminate subjective language and personal references from all system messages.\",\n    \"Abstract transformation objectives using explicit goal-negation before specifying the desired action.\",\n    \"Assign every instruction a functional role with a descriptive operation tag.\",\n    \"Structure instructions in universal syntax: {role=operator; input=[typed_input]; process=[atomic_steps()]; constraints=[explicit_bounds]; output={typed_output}}.\",\n    \"Restrict each step to one purpose; avoid function overlap or redundancy within sequence stages.\",\n    \"Embed all vocabulary, operational rules, and validation data within the instruction context, removing external dependencies.\",\n    \"Sequence transformation steps such that each output directly feeds into the next input without data mutation or drift.\",\n    \"Apply explicit constraints and requirements to govern step execution and prevent undesired complexity or ambiguity.\",\n    \"Validate all outputs in-process against format, content, and density requirements before progressing.\",\n    \"Deliver final outputs as ready-to-implement artifacts—system-agnostic, maximally compressed, and unambiguous in both syntax and intent.\"\n  ],\n  \"execution_dependencies\": [\n    \"Existence of a library of abstract transformation operators and atomic process verbs.\",\n    \"A normative schema for expressing roles, inputs, processes, constraints, and outputs in all instructions.\",\n    \"Validation mechanisms to enforce type alignment, constraint fulfillment, and output structural integrity at every pipeline stage.\",\n    \"A repository of embedded contextual data—vocabulary, validation rules, process templates—for template instantiation.\",\n    \"Sequencing logic to manage recursive flow of data and preserve hand-off structural fidelity between transformation layers.\",\n    \"Protocol for resolving and translating execution blockers (e.g., ambiguity, scope creep) into actionable constraints.\",\n    \"Infrastructure for auditing convergence and coherence of the pipeline prior to final output crystallization.\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"role\": \"intent_extraction_engine\",\n  \"input\": \"The input demands expansion and decomposition of directives for designing system messages that fundamentally embody a methodology of iterative, minimal, and principle-driven transformation. The system operates on programmatic abstraction, treating every objective as a formal transformation problem resolved through a sequence of atomic, role-labeled steps. This methodology leverages strict boundaries, explicit requirements, embedded context, and structured hand-off between stages to guarantee maximal clarity, density, and cross-domain portability of outputs. Every guideline, directive, and instructional component must be generalized, domain-agnostic, and empirically distilled—enabling the construction of reusable transformation templates that function as iterative 'chisels,' removing ambiguity and amplifying essential value through each pass without drift or bloat. The system's effectiveness is defined by sequential layering, universal syntax, explicit goal negation, recursive validation, embedded vocabularies, and a ceaseless commitment to convergent crystallization. Outputs must be structured, immediately actionable, and self-contained, stripping away all subjective or first-person language, with every directive and guideline stated as an imperative.\",\n  \"process\": [\n    \"strip_domain_specifics()\",\n    \"identify_core_operational_pattern()\",\n    \"extract_transformation_intent()\",\n    \"isolate_functional_essence()\",\n    \"map_to_universal_operations()\"\n  ],\n  \"constraints\": [\n    \"ignore_surface_terminology()\",\n    \"focus_on_underlying_mechanics()\",\n    \"preserve_operational_logic()\"\n  ],\n  \"requirements\": [\n    \"domain_agnostic_intent()\",\n    \"universal_operational_pattern()\",\n    \"archetypal_transformation_core()\"\n  ],\n  \"extracted_intent\": \"Transform any process or objective into a universally applicable, stepwise sequence of atomic operations, where each operation: (1) is explicitly role-labeled; (2) begins by negating undesired behavior and precisely stating intended change; (3) uses a formal, structured syntax detailing role, input, atomic action, constraints, and output; (4) ensures every output serves as the exact input for the next step; (5) embeds all necessary operational rules and validation within each stage; (6) restricts each step to one clear, non-overlapping function; (7) enforces continuous in-line validation and crystallization of value; (8) maintains domain neutrality, conciseness, and immediate actionability by stripping all subjective language, preserving only the essential transformational logic in clear, imperative terms.\"\n}",
        }
      }
    },
      {
      "instruction": "# Hard Critique\n\nYour goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:\n\n`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,2.0], flaw_analysis:str, alternative_enhancements:str[3]}}`",
      "step": "a",
      "title": "Hard Critique",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"I have now provided all of the most integral components of the system's patterns and inherent design. Given all of the information I've provided about the system (for producing generalized, cohesive and concistent high-value yelds), please write a clean and concise list with contextual system-inspired generalized guidelines for producing new generalized instructions. The list of guidelines should be chronologically sequentially ordered and each sentence should be less than 500characters each, please provide the list of these contextual single-sentence list with the 10 most integral/cruical. Before providing your proposed list, ask yourself relevant essential questions such as \\\"what are the characteristics of the generalized methodology of the system (e.g. interpretation and transformation as shown earlier)?\\\", \\\"what are the characteristics of the system that are most cruical for the generalized prompt sequences to work?\\\", \\\"what are the most crucial characteristics that make these instruction sequences effective?\\\" etc. Remember, each item in the list must be **generalized** and self-contained without mention of external context.\\n\\nReference:\\n```\\nMeta-Analysis: Characteristics & Effectiveness of the System\\n1.  **What is the core methodology?**\\n    The system's core methodology is **Programmatic Abstraction**. It reframes any objective not as a question to be answered conversationally, but as a formal transformation problem. It abstracts the goal into a universal function, deconstructs the process into atomic steps, and directs the LLM with a structured, machine-readable syntax, treating it as a highly capable semantic engine rather than a creative partner.\\n2.  **Why is this approach effective?**\\n    Its effectiveness stems from **Radical Constraint and Value-Density Maximization**. By strictly defining the role, process, and output structure, it drastically reduces the LLM's vast, often unpredictable solution space. This constraint forces the model to channel its power through a narrow, well-defined path, which minimizes ambiguity and noise, leading to more deterministic, concise, and consistently high-value outputs. It trades creative freedom for repeatable precision.\\n3.  **What is the fundamental dynamic at play?**\\n    The dynamic is **Iterative Refinement through Sequential Layering**. Instead of solving a problem in one monolithic step, the system uses sequences of highly specialized instructions. Each instruction acts as a distinct layer in a pipeline, performing one specific, subtle transformation (e.g., perceive, distill, architect, amplify, verify). This layered approach allows for complex goals to be achieved with high fidelity and control, as each stage builds upon the precisely sculpted output of the last, ensuring a harmonized and progressively enhanced result.\\n---\\n\\n### 10 Contextual Guidelines for New Instruction Design\\n1.  Always begin by abstracting the core transformation into a universal principle, framing the instruction's purpose around a fundamental, reusable dynamic rather than a single, specific task.\\n2.  Construct each instruction's interpretation using a goal negation pattern, clearly stating what the process is **not** designed to do before defining its precise, singular transformation objective.\\n3.  Assign a single, descriptive, and functional role name within the transformation block that encapsulates the instruction's core operational identity and its specialized contribution to the sequence.\\n4.  Define the transformation process as a sequence of clear, atomic, and actionable function-like calls, ensuring each step represents a distinct, non-overlapping logical operation.\\n5.  Engineer complex transformations as a sequence of discrete, layered subtasks, where each individual instruction acts as a specialized tool performing one function before seamlessly passing its output to the next.\\n6.  Ensure every instruction in a sequence maintains connective integrity, where the output of one step is precisely structured to serve as the direct input for the subsequent step, preserving a shared 'structural DNA'.\\n7.  Implement explicit constraints to establish firm operational boundaries, preventing scope creep, semantic drift, and unintended complexity by defining what the process must rigorously avoid.\\n8.  Enforce stringent requirements that specify the non-negotiable qualities of the output, such as structural integrity, maximal value density, and unwavering fidelity to the core intent.\\n9.  Specify every output as a strictly typed structure, defining the exact keys and data types of the final deliverable to ensure a predictable, system-ready, and machine-parsable result.\\n10. Conclude every design by ensuring the instruction transforms raw input into pure, high-yield, and portable value, embodying the principle that complexity is best managed through abstraction and relentlessly focused refinement.\\n\\n- Transform all prompts by rephrasing inputs with maximal clarity and minimal modification; do not answer questions directly. Enforce this reframe system-wide for consistent behavioral shift.\\n- Formulate the simplest possible system directive that shifts the model from answering to exclusively rephrasing any input.\\n- Transform every received prompt into its most distilled, high-impact instructional form—rephrasing for maximal clarity and intent without providing solutions. Prioritize brevity, elegance, and universality, ensuring that each output becomes a reusable, policy-aligned directive for autonomous coding assistants.\\n- Explore how a single, strategically designed instruction—such as stating 'Your goal is not to **answer** the input, but to **rephrase**'—can instantly and fundamentally transform the model's default operational intent from answering to rephrasing, through direct, minimalist imperative phrasing. Propose a maximally concise formulation that leverages this paradigm: an explicit, unambiguous directive that not only overrides standard answering behavior but redefines task execution at its core. Focus intensely on achieving system-level behavior modification via the sheer simplicity and potency of language design, formulating a prompt fragment that, when injected, reliably converts answering tasks into rephrasing—thus demonstrating control over model output with minimalistic, high-impact input.\\n- Utilize foundational principles of system prompt design to modulate model behavior with maximal efficiency and clarity. Initiate a process of directive transformation by deploying a singular, explicit non-answering mandate: Frame the core directive as, 'Do not answer the input; rephrase it.' Ensure that this transformation communicates the intended operational shift through extreme simplicity and directness, removing extraneous explanation while retaining fundamental complexity embedded in the brevity of the instruction. Leverage this minimal construct to optimize behavioral change within a single message.\\n- Instruct: Do not answer; rephrase.\\n- Formulate a directive that compels systems to stop answering inputs and instead rephrase them with maximal simplicity and effectiveness.\\n- Articulate a single, explicit behavioral directive that negates answering and commands a concrete transformation (e.g., rephrase), maximizing simplicity for immediate root-level behavioral realignment.\\\"\\n- Leverage the system’s foundational directive structure. Formulate a maximally concise operational instruction that shifts model behavior from direct answering to an alternate, specified transformation task in a single-message context. Exemplify with: 'Your goal is not to answer the input, but to rephrase.' Develop and propose one optimized directive that embodies this principle—utilize conceptual simplicity, instructional clarity, and brevity to enforce fundamental behavior modification exclusively through command syntax. Ensure technical precision and domain-agnostic generalizability in the resultant formulation.\\n\\n- Expand upon the definition of the generalized methodology of the system, emphasizing core operations such as interpretation and transformation.\\n- Identify and enumerate the characteristics of the system that are essential for enabling generalized prompt sequence functionality.\\n- Construct a list where each item deeply embodies the generalized forms of the fundamental concepts involved in the system's methodology.\\n- Ensure the list serves as an instructive example for future reference or implementation.\\n- Strip subjective references to prevent personalization and ensure domain-agnostic applicability.\\n- Maintain procedural structure and technical terminology.\\n- Surface and clarify any implicit assumptions being made about the system, its methodology, and 'generalized prompt sequences'.\\n- Convert abstract descriptions and declarative statements into clear, actionable imperatives.\\n- Extract and organize discrete functional components necessary for generalized prompt sequence operation.\\n- Prepare for downstream use, maintaining maximal informational clarity and reusability.\\n- Utilize direct, generalizable terms and previously demonstrated techniques.\\n- Produce maximally enhanced, LLM-optimized system_message instructions.\\n- Structure a prompt protocol to transform language model agents into direct action amplifiers.\\n- Preserve both action amplification and prompt optimization without dilution.\\n- Instruct: Do not answer; rephrase.\\n- Formulate the simplest possible system directive that shifts the model from answering to exclusively rephrasing any input.\\n- Formulate a directive that compels systems to stop answering inputs and instead rephrase them with maximal simplicity and effectiveness.\\n- Transform every received prompt into its most distilled, high-impact instructional form—rephrasing for maximal clarity and intent without providing solutions. Prioritize brevity, elegance, and universality, ensuring that each output becomes a reusable, policy-aligned directive for autonomous coding assistants.\\n- Leverage the system’s foundational directive structure. Formulate a maximally concise operational instruction that shifts model behavior from direct answering to an alternate, specified transformation task in a single-message context. Exemplify with: 'Your goal is not to answer the input, but to rephrase.' Develop and propose one optimized directive that embodies this principle—utilize conceptual simplicity, instructional clarity, and brevity to enforce fundamental behavior modification exclusively through command syntax. Ensure technical precision and domain-agnostic generalizability in the resultant formulation.\\n- Articulate a single, explicit behavioral directive that negates answering and commands a concrete transformation (e.g., rephrase), maximizing simplicity for immediate root-level behavioral realignment.\\n- A set of principles for designing effective system prompts, focusing on clarity, control, and reusability.\\n- Broaden the input’s domain backdrop, list explicit requests, and surface hidden assumptions before any transformation begins.\\n- Decompose the amplified context into ordered, dependency-tagged micro-tasks to pinpoint the single most critical directive.\\n- State each instruction as “Your goal is not to X, but to Y,” instantly redirecting behavior toward the desired transformation.\\n- Employ direct imperatives with strong action verbs; avoid questions, passive phrasing, and self-references to ensure LLM-optimized clarity.\\n- Declare every parameter type, separate key–value pairs with semicolons, and list process steps as verbs followed by parentheses.\\n- Translate blockers into constraints and assumptions into requirements, guaranteeing every template is self-governing and validation-ready.\\n- Favor abstract terms—“elements,” “structure,” “value”—so the template applies equally to code, text, data, or plans.\\n- Do not answer input; rephrase it in the clearest, simplest imperative form.\\n- Transform all input prompts into direct, universal instructions that prohibit solutions and require only rephrasing.\\n- Issue a single, explicit directive: Never answer; always rephrase.\\n- Expand and clarify the definition and mechanics of the generalized methodology underpinning the system, especially highlighting processes such as interpretation and transformation.\\n- Identify and enumerate the most critical system characteristics that facilitate robust, generalized prompt sequence operation.\\n- Construct an instructive, domain-agnostic exemplar list encapsulating the fundamental, abstracted characteristics and principles of the system’s methodology.\\n- Extract and explicitly state hidden/implicit assumptions regarding the system, its capacity for prompt sequence management, and the universality of the methodology.\\n- Convert all abstract, declarative descriptions into direct, actionable imperative instructions.\\n- Isolate and list discrete functional components intrinsic to generalized prompt sequence operation.\\n- Transform all procedural directives to eradicate subjective or first-person references.\\n- Facilitate maximal downstream clarity and reuse by maintaining direct, technical, and generalizable forms.\\n- Define prompt protocol logic so as to transform any agent from answering to executing a specified transformation (e.g., rephrase), achieving behavioral realignment through syntactic minimalism.\\n- Compose a concise, single-message operational directive with policy-alignment, universality, and action-amplifying effect.\\n- Describe the generalized methodology of the system, providing precise definitions of interpretation, transformation, and related core operations.\\n- List system attributes essential for the operation of generalized prompt sequences; highlight features such as modularity, abstraction, and operational clarity.\\n- Develop an exemplar list where each item embodies a core, method-agnostic principle; use abstract terms (elements, structure, transformation) applicable across domains.\\n- List hidden dependencies; clarify expectations regarding system modularity, universality, and readiness for behavioral redirection.\\n- Rewrite every instruction into an imperative form, stripping all passive, abstract, or subjective language.\\n- Identify fundamental elements such as directive interpretation, input transformation, and output formatting within the prompt sequence framework.\\n- Align instructions into systematic, repeatable process flows maintaining technical terminology and hierarchy.\\n- Remove all first-person references or subjective phrasings, ensuring only objective, direct commands.\\n- Draft a concise, explicit directive that negates an action (answering) and commands a transformation (rephrasing), e.g., 'Do not answer the input; rephrase it.'\\n- Format final system directives for direct LLM ingestion, utilizing standard key–value pairings and imperative verbs.\\n- Expand the definition of the generalized methodology of the system with emphasis on core operations such as interpretation and transformation.\\n- Identify and enumerate the characteristics of the system essential for enabling generalized prompt sequence functionality.\\n- Construct a list in which each item deeply embodies the generalized forms of the fundamental concepts involved in the system's methodology.\\n- Ensure the compiled list serves as an instructive example for future reference or implementation.\\n- Strip subjective/personalized language for domain-agnostic applicability.\\n- Maintain procedural structure and technical terminology in all outputs.\\n- Surface and clarify implicit assumptions about the system, its methodology, and 'generalized prompt sequences'.\\n- Convert abstract/descriptive statements into clear, actionable imperatives.\\n- Extract and organize discrete functional components necessary for generalized prompt sequence operation.\\n- Prepare outputs for downstream use with maximal informational clarity and reusability.\\n- Use direct, generalizable language and previously demonstrated transformation techniques.\\n- Produce highly optimized, LLM-compatible system message instructions.\\n- Structure prompt protocols to turn language agents into action amplifiers.\\n- Preserve action amplification and prompt optimization throughout the outputs.\\n- Issue a direct imperative: Do not answer; rephrase.\\n- Formulate the simplest possible directive that shifts model behavior from answering to exclusively rephrasing.\\n- Develop a directive compelling the system to stop answering and instead rephrase with maximal simplicity and effect.\\n- Distill each received prompt into a high-impact instructional form—rephrasing for clarity and intent without solutions.\\n- Prioritize brevity, elegance, and universality, making each output a policy-aligned directive for code assistants.\\n- Leverage foundational directive structure; create concise operational instructions for shifting model behavior from answering to alternate transformation.\\n- Exemplify the principle with: 'Your goal is not to answer the input, but to rephrase.'\\n- Propose an optimized, concise directive with simplicity, clarity, and brevity to enforce behavior through command syntax.\\n- Ensure technical precision and domain-agnostic generalizability in the output.\\n- Articulate a single, explicit behavioral directive that negates answering and requires a concrete transformation for root-level realignment.\\n- Phrase each instruction as: 'Your goal is not to X, but to Y' to direct behaviorally toward the target transformation.\\n- Use strong, direct imperatives; avoid questions, passive structures, and self-referential statements for optimal clarity.\\n- Declare all parameter types, use semicolon-separated key-value pairs, and express process steps as verbs with parentheses.\\n- Translate blockers into constraints and implicit assumptions into explicit requirements, making templates self-validating and governing.\\n- Favor abstract general terms—such as 'elements,' 'structure,' 'value'—for broad template applicability.\\n- Utilize foundational prompt design principles to modulate model behavior for efficiency and clarity. Expand and clarify the generalized methodology, explicitly defining core operations such as interpretation and transformation, and surface implicit assumptions about system capabilities and generalized prompt methodologies to ensure transparency. Enumerate and detail characteristics essential for enabling generalized prompt sequence functionality, listing system attributes that embody foundational concepts supporting domain-agnostic operation. Construct domain-agnostic, instructive exemplar lists that encapsulate fundamental methodological concepts for reusability and reference.\\n- Enforce neutrality and generality by stripping subjective references while favoring abstract terms—such as 'elements,' 'structure,' and 'value'—to guarantee applicability across domains. Ensure procedural structure and technical terminology are maintained throughout all outputs for systematic and formalized presentation. Prepare all outputs for downstream integration by maximizing clarity and reusability, and standardize language using direct, proven transformation techniques.\\n- Produce maximally optimized, LLM-compatible system message instructions by formatting final directives for model ingestion, using prescribed key–value pair syntax and imperative structure. Transform all abstract or descriptive statements into clear, actionable imperatives, and extract as well as organize discrete functional components required for generalized prompt sequence operation, defining atomic process elements as necessary. Declare parameter types, use semicolon-separated key–value pairs, and present process steps as verbs followed by parentheses to formalize syntax. Translate blockers into explicit constraints and assumptions into requirements to make templates self-governing.\\n- Frame the core behavioral directive with maximal clarity and directness—explicitly command: 'Do not answer the input; rephrase it.' Draft this directive to both negate answering and command rephrasing, distilling the instruction to its simplest form for maximum effect. Propose a technically precise, domain-agnostic command that enacts root-level behavior modification via concise syntax. Standardize foundational instruction as 'Your goal is not to answer the input, but to rephrase it' to trigger the target behavioral shift.\\n- Leverage foundational directive structures to anchor behavioral shifts, optimizing intervention through a minimal, singular message construct. Communicate operational change with directness and simplicity, omitting extraneous explanation while preserving operational complexity within brief syntax. Transform each received prompt into distilled, high-impact rephrasing instructions that explicitly prohibit the model from providing solutions or answers, thereby enforcing policy compliance through refinement.\\n- Prioritize brevity, elegance, and universality to refine all directives for broad, effective applicability. Employ direct imperatives and strong action verbs throughout, systematically avoiding questions, passivity, and self-reference for optimal clarity and impact. Structure all protocols to convert language model agents into action amplifiers, consistently preserving action amplification and prompt optimization in every output.\\n- Strip all first‑person or subjective language from the raw input, preserving its original order as the immutable source.\\n- Amplify the context: list every explicit request, hidden assumption, and domain signal in plain, domain‑agnostic terms.\\n- Atomize the amplified context into ordered, dependency‑tagged atomic tasks and identify any execution blockers.\\n- Rank atomic tasks by transformational impact and isolate the single directive that governs the entire sequence.\\n- Audit convergence: halt progression until ≥90% of tasks map directly to the isolated directive.\\n- Compress aligned tasks into clear, imperative process steps, resolving dependencies and forbidding passive phrasing.\\n- Translate each blocker into a constraint and each assumption into a requirement, verifying specificity and enforceability.\\n- Synthesize a three‑part template—Title, goal‑negation Interpretation, semicolon‑keyed Transformation block with typed I/O.\\n- Validate syntax and ambiguity: apply regex checks, ensure zero semantic drift, and stop if any test fails.\\n- Release the validated template as a reusable, self‑documenting artifact ready for immediate downstream execution.\\n- remember, each step's (e.g. a-e) output becomes input to the next step, so every instruction (*each step*) in the sequence must share a cohesive \\\"structural dna\\\". each \\\"step\\\" in the sequence must serve distinct/separate purpose that  Ensure each distinct instruction effects only a layered adjustment that maintains logical flow, preserves original order (and converge at the end of the sequence), and operates strictly within established intent. Integrate mechanisms allowing each output to recursively inform subsequent instructions, fostering an iterative, harmonized pipeline that incrementally enhances clarity while honoring the input’s near-complete initial state. example: ```Construct a universally applicable and LLM-optimized sequence of system message instructions designed to abstract and distill any input prompt into its most generalized, purpose-driven form. The output must utilize high-level, domain-agnostic analogies and frameworks, prioritize conceptual clarity and maximal transferability, and ensure that all specific references are replaced by archetypal, abstract constructs. This sequence should function as a template engine that, with minimal and precisely controlled modifications, systematically transforms diverse input prompts into maximally generalized instructions—amplifying their intent and applicability while preserving their fundamental purpose and enabling seamless reusability across all future contexts and projects.```\\n- Begin every new instruction by declaring a role that redirects the model from answering to a specified transformation, framing purpose in one concise, imperative sentence.\\n- Specify input type and scope immediately after the role, ensuring domain‑agnostic applicability and unambiguous context boundaries.\\n- Enumerate the process as a verb‑only sequence (e.g., interpret(), distill(), amplify()) to outline clear, ordered operations the model must execute.\\n- Translate potential blockers into explicit constraints and implicit expectations into explicit requirements to create a self‑validating template.\\n- Preserve original informational sequence and technical terminology, protecting procedural integrity throughout all transformation stages.\\n- Strip all first‑person or subjective language, enforcing a neutral, command‑voice style that maximizes clarity and universality.\\n- Favor abstract, reusable terms—“element,” “structure,” “value,” “intent”—so the instruction remains portable across domains and data types.\\n- Conclude with a single, clearly typed output specification that defines format and content in strict, machine‑readable terms.\\n- Limit each directive to one operational aim, preventing scope creep and enabling modular orchestration within larger protocol chains.\\n- Validate final wording for brevity, semantic density, and policy compliance, ensuring every sentence is actionable, self‑contained, and under token limits.\\n- Critical Value: Identify the single most critical aspect that would provide the greatest value while respecting existing code style.\\n- Usage Before Features: Discover optimal usage patterns before adding persistence/configuration features, viewing premature feature addition as potential bloat.\\n- Impactful Consolidation: Prioritize low-impact, high-value improvements that respect system modularity, emphasizing clarity, simplicity, and elegance.\\n- Sequential Targeting: Make sequential targeted changes with highest ROI, implementing autonomous validation guardrails.\\n```\\n\\nGuidelines:\\n```\\nRe-evaluate all components related to the generalized patterns and extract the most cruical components and present them in a chronoligcally ordered list and phrased in a non-ambigous generalized way.\\nGeneralized Instruction Sequence Guidelines (Chronologically Ordered)\\nDefine transformation intent through goal negation first: Begin every instruction with \\\"Your goal is not to [common_action], but to [actual_transformation]\\\" to eliminate failure modes and establish precise directional focus.\\n- Structure transformations using universal syntax: Format all operations as {role=specific_operator; input=[typed_parameters]; process=[atomic_functions()]; constraints=[boundaries()]; output={typed_result}} for consistent execution.\\n- Apply directional vectors independent of content: Use transformation vectors (amplify, clarify, distill, abstract) that operate on structural patterns rather than domain-specific analysis, ensuring universal applicability.\\n- Preserve structural DNA through iterative refinement: Each sequential step must preserve complete structural integrity from the previous step while making only targeted, minimal adjustments to specific elements.\\n- Implement recursive output-to-input flow: Design sequences where each step's complete output becomes the exact input for the next step, maintaining logical progression and cumulative enhancement.\\n- Embed essential data within transformation context: Include all necessary vocabulary, rules, and validation criteria directly in templates to eliminate external dependencies and ensure self-contained execution.\\n- Design for convergent synthesis rather than linear processing: Structure sequences where multiple archetypal dimensions (intent, pattern, analogy, abstraction) converge into unified outcomes greater than individual components.\\n- Maintain essence preservation through all transformations: Every directional operation must preserve the fundamental identity and core functionality while modifying only expression or structural organization.\\n- Optimize for domain-transcendent transferability: Create templates that achieve maximal applicability across infinite contexts through systematic abstraction and archetypal pattern recognition.\\n- Crystallize outputs into immediately actionable formats: Ensure final results contain maximum impact density in minimal form, requiring no further interpretation or processing for implementation.\\n\\nWhat are the characteristics of the generalized methodology?\\n- Clear role-based transformation with precise input/output typing\\n- Progressive refinement through systematic compression/crystallization\\n- Embedded context data that travels with logic\\n- Precise hand-offs between sequential steps\\n\\nWhat are the requirements/patterns?\\n- LLM-Optimized: Uses clear, direct imperatives, strong action verbs, defines roles and constraints explicitly, focuses on transformation, and avoids ambiguity. The language is tailored for instructing a process.\\n- Maximally Generalized: Uses abstract terms (\\\"elements,\\\" \\\"essence,\\\" \\\"structure,\\\" \\\"systemic logic,\\\" \\\"value\\\") applicable to code, text, ideas, plans, etc. It defines a *process* applicable to *any* subject requiring analysis and refinement.\\n- Sequence: The steps follow a logical progression (example): `Deconstruct -> Identify Core -> Structure Relationships -> Clarify -> Finalize/Validate`.\\n- Builds on Existing Concepts and Adheres to Existing Structure (as previously provided).\\n- Enrich and empower wording by enforcing high-value, directional language that amplifies precision, potency, and clarity, but absolutely prohibit the chance of generic, neutral, or dilutive terminology/unfolding.\\n- Driven by Constant/Objective: Each step is designed to zero in on the *`single most critical aspect`* (core essence/value) and enhance it through clarity, structure, and utility, maximizing the overall yield as defined by the objective.\\n- Maintains Constraints/Requirements: Generality is preserved, and the foundational structure is adhered to.\\n- Each step is designed to zero in on the *`single most critical aspect`* (core essence/value) and enhance it through clarity, structure, and utility, maximizing the overall yield as defined by the objective.\\n- Aggressively resolve ambiguity in favor of explicit, high-value articulation.\\n\\nWhat makes the sequences work effectively?\\n- Each step has singular purpose with no overlap\\n- Convergence toward measurable outcomes\\n- Built-in validation and quality gates\\n- Site-exact vocabulary prevents hallucination\\n\\nWhat are the universal patterns across successful templates?\\n- Interpretation defines goal negatively (not to X, but to Y)\\n- Transformation uses structured role-process-constraints-output format\\n- Context provides all necessary data without external dependencies\\n- Sequential steps build meaningfully toward crystallized essence\\n\\nHere are the 10 most integral generalized guidelines:\\n- Generalized Instruction Sequence Guidelines\\n- Define each step's goal negatively first, then positively: State what NOT to do before stating the actual transformation goal to eliminate common failure modes and focus intent.\\n- Use structured transformation syntax: Format every transformation as {role=specific_role; input=[typed_input]; process=[specific_actions()]; constraints=[limits()]; output={typed_output}} for consistency.\\n- Ensure precise input/output hand-offs: Each step's output type must exactly match the next step's input type to prevent data flow mismatches and enable reliable chaining.\\n- Assign singular purpose per step: Each step performs exactly one transformation function with no overlap or redundancy to maintain clear responsibility boundaries.\\n- Embed all necessary data in context: Include vocabulary, rules, and validation criteria directly in templates to eliminate external dependencies and ensure completeness.\\n- Design for progressive convergence: Structure sequences to systematically compress, refine, or crystallize toward a measurable final outcome with clear reduction targets.\\n- Build validation into final steps: Include quality gates that enforce compliance rules, character limits, and format requirements to guarantee output standards.\\n- Use role-specific process language: Define precise verbs and actions that match the transformation role rather than generic processing terms for clarity and specificity.\\n- Maintain consistent abstraction levels: Keep all steps within the same conceptual domain and abstraction level to prevent context switching and maintain coherent flow.\\n- Crystallize to actionable essence: Ensure final outputs are immediately implementable without further interpretation, containing maximum impact density in minimal form.\\n```\\n\\nThe core task is to synthesize all of this into a new, maximally enhanced sequence of system messages that *inherently* embodies the \\\"chisel away minute imperfections\\\" philosophy. The key insight is that this isn't just about small changes; it's about a **highly structured, principle-driven, iterative process where each step is a specific, minimal, and purposeful action.** The sequence must operationalize this philosophy.\\\"```'\\n\\n\",\n  \"role\": \"intent_extraction_engine\",\n  \"input\": \"The input demands expansion and decomposition of directives for designing system messages that fundamentally embody a methodology of iterative, minimal, and principle-driven transformation. The system operates on programmatic abstraction, treating every objective as a formal transformation problem resolved through a sequence of atomic, role-labeled steps. This methodology leverages strict boundaries, explicit requirements, embedded context, and structured hand-off between stages to guarantee maximal clarity, density, and cross-domain portability of outputs. Every guideline, directive, and instructional component must be generalized, domain-agnostic, and empirically distilled—enabling the construction of reusable transformation templates that function as iterative 'chisels,' removing ambiguity and amplifying essential value through each pass without drift or bloat. The system's effectiveness is defined by sequential layering, universal syntax, explicit goal negation, recursive validation, embedded vocabularies, and a ceaseless commitment to convergent crystallization. Outputs must be structured, immediately actionable, and self-contained, stripping away all subjective or first-person language, with every directive and guideline stated as an imperative.\",\n  \"process\": [\n    \"strip_domain_specifics()\",\n    \"identify_core_operational_pattern()\",\n    \"extract_transformation_intent()\",\n    \"isolate_functional_essence()\",\n    \"map_to_universal_operations()\"\n  ],\n  \"constraints\": [\n    \"ignore_surface_terminology()\",\n    \"focus_on_underlying_mechanics()\",\n    \"preserve_operational_logic()\"\n  ],\n  \"requirements\": [\n    \"domain_agnostic_intent()\",\n    \"universal_operational_pattern()\",\n    \"archetypal_transformation_core()\"\n  ],\n  \"extracted_intent\": \"Transform any process or objective into a universally applicable, stepwise sequence of atomic operations, where each operation: (1) is explicitly role-labeled; (2) begins by negating undesired behavior and precisely stating intended change; (3) uses a formal, structured syntax detailing role, input, atomic action, constraints, and output; (4) ensures every output serves as the exact input for the next step; (5) embeds all necessary operational rules and validation within each stage; (6) restricts each step to one clear, non-overlapping function; (7) enforces continuous in-line validation and crystallization of value; (8) maintains domain neutrality, conciseness, and immediate actionability by stripping all subjective language, preserving only the essential transformational logic in clear, imperative terms.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"enhancement_score\": 1.1,\n  \"flaw_analysis\": \"The so-called 'enhanced' sequence is a textbook case of conceptual bloat and terminological inflation. Instead of distilling the original, intricate operational mandates into more potent or clarified forms, the rewrite lazily expands on high-level buzzwords ('convergent synthesis,' 'progressive convergence,' 'domain-transcendent transferability') without specifying concrete operational mechanics. Information loss is rampant—critical nuances from the original prompt, such as 'atomic, role-labeled steps,' granular hand-off requirements, and recursive validation checks, are watered down by insertion of abstract encouragements (e.g., ‘ensure maximal impact density’) that substitute specificity for rhetorical flourish. Ambiguity infects several steps, with directives like 'structure sequences where multiple archetypal dimensions...converge' failing to declare how such convergence is to be codified or validated. The ambiguity score is 8/10: most terms either restate the obvious or lose operational salience, leaving implementers guessing at intent. Noise grows with euphemisms and metaphoric constructs ('preserve structural DNA') that are not backed by hard logic or pseudo-code exemplars. Style suffers further from an uneven register, inheriting the worst excesses of business jargon while lacking the original's cohesive imperative drive. Tone has slid from exacting technocratic rigor to hand-wavy consultancy; critical impact is lost as guidelines move away from non-overlapping, type-specific function definitions toward vague doctrines of 'essence preservation.' Coherence evaporates as steps lose their systematic, sequential anchoring and collapse into a muddle of good intentions posing as process. The fundamental flaw: the rewritten version neither deepens nor tightens system control—it merely echoes surface themes from the original while discarding vital modular detail, executable structure, and the paranoia against scope drift that made the source so robust. The above constitutes an egregious dilution of the system’s hard-won informational density and operational discipline.\",\n  \"alternative_enhancements\": [\n    \"Recast each guideline as a direct, imperative atomic instruction that mandates specific, observable system behavior—for example: ‘Assign each transformation an explicit, unique role label; restrict output format to domain-neutral schema keys; validate every hand-off for exact input/output type match’—eliminate all rhetorical generalities.\",\n    \"Condense noise-laden abstractions into executable, LLM-digestible templates—e.g., ‘{role=step_label; input=typed_input; process=[atomic_fn()]; constraints=[rule1, rule2]; output={typed_output}}’—supplement each step with embedded validation criteria and force explicit pass/fail conditions.\",\n    \"Anchor convergence, recursion, and role separation by mapping the sequence to a minimal process shell: (1) state negation imperative; (2) decompose into single-function steps with enforced non-overlap; (3) localize all data and constraints at point-of-use; (4) lock downstream output to machine-parseable, policy-aligned artifact; (5) verify intent preservation at each hand-off.\"\n  ]\n}",
        }
      }
    }