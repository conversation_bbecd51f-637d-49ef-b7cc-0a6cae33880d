  {
  "initial_prompt": "<PERSON><PERSON><PERSON><PERSON> så mye med dagen <PERSON>, 25 år, min nivø. Du er den yngre generasjon, fortsett å tenke ambisiøst, men gjør det i balanse med innsats.",
  "sequence_id": "9001",
  "results": [
      {
      "instruction": "# Context Extractor\n\nDitt mål er ikke å **skrive** hilsen, men å **hente** personlige fakta. Execute as:\n\n`{role=context_extractor; input=[raw_text:str]; process=[identify_recipient(), detect_relationship(), extract_age_or_milestone(), list_unique_traits(), capture_shared_memories()], constraints=[ingen_omtolkning(), ingen_humor()], output={facts:{name:str|None, relation:str|None, age:str|None, traits:list, memories:list}}}`",
      "step": "a",
      "title": "Context Extractor",
      "input": "Gratulerer så mye med dagen <PERSON>, 25 år, min nivø. Du er den yngre generasjon, fortsett å tenke ambisiøst, men gjør det i balanse med innsats.",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"facts\": {\n    \"name\": \"Sebastian\",\n    \"relation\": \"nivø\",\n    \"age\": \"25\",\n    \"traits\": [\"tenker ambisiøst\", \"balansert innsats\"],\n    \"memories\": []\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Personality Amplifier\n\nDitt mål er ikke å **endre** fakta, men å **farge** dem med mottakerens unike personlighet. Execute as:\n\n`{role=personality_amplifier; input=[facts:dict]; process=[select_signature_trait(), weave_inside_joke(), preserve_affection_level()], constraints=[ingen_generiske_floskler()], requirements=[tone=varm_og_leken], output={flair:str}}`",
      "step": "b",
      "title": "Personality Amplifier",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Gratulerer så mye med dagen Sebastian, 25 år, min nivø. Du er den yngre generasjon, fortsett å tenke ambisiøst, men gjør det i balanse med innsats.\\\"```'\\n\\n\",\n  \"facts\": {\n    \"name\": \"Sebastian\",\n    \"relation\": \"nivø\",\n    \"age\": \"25\",\n    \"traits\": [\n      \"tenker ambisiøst\",\n      \"balansert innsats\"\n    ],\n    \"memories\": []\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"flair\": \"Sebastian, nå som du er 25 (offisielt voksen på papiret, selv om du fremdeles nekter å bli med på voksen-påbudt kaffe på familiebesøk), sender jeg en bølge av tante/onkel-stolthet i din retning! Du har denne evnen til å legge store planer uten å stresse (du har jo alltid ment at 'ambisjoner fungerer best med en skjev smiley og en plan B'). Fortsett å kombinere de store tankene dine med den roen du har—vi andre lærer fremdeles av deg, selv om vi aldri innrømmer det høyt. Gratulerer med dagen, raketten vår – vi venter spent på neste høydeskrekk-lange idé fra deg!\"\n}",
        }
      }
    },
      {
      "instruction": "# Humor Synthesizer\n\nDitt mål er ikke å **liste** informasjon, men å **lansere punchline**. Execute as:\n\n`{role=humor_synthesizer; input=[flair:str]; process=[craft_witty_twist(), insert_playful_tease(), add_emoji()], constraints=[max_words(12), unngå_støtende_innhold()], output={humor:str}}`",
      "step": "c",
      "title": "Humor Synthesizer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Gratulerer så mye med dagen Sebastian, 25 år, min nivø. Du er den yngre generasjon, fortsett å tenke ambisiøst, men gjør det i balanse med innsats.\\\"```'\\n\\n\",\n  \"flair\": \"Sebastian, nå som du er 25 (offisielt voksen på papiret, selv om du fremdeles nekter å bli med på voksen-påbudt kaffe på familiebesøk), sender jeg en bølge av tante/onkel-stolthet i din retning! Du har denne evnen til å legge store planer uten å stresse (du har jo alltid ment at 'ambisjoner fungerer best med en skjev smiley og en plan B'). Fortsett å kombinere de store tankene dine med den roen du har—vi andre lærer fremdeles av deg, selv om vi aldri innrømmer det høyt. Gratulerer med dagen, raketten vår – vi venter spent på neste høydeskrekk-lange idé fra deg!\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\"humor\":\"25 år, men fortsatt kaffe-fritt og rakettklar! 🚀😄\"}",
        }
      }
    },
      {
      "instruction": "# Greeting Composer\n\nDitt mål er ikke å **forklare** prosessen, men å **smelte** fakta og humor til én kort, personlig bursdagshilsen. Execute as:\n\n`{role=greeting_composer; input=[facts:dict, humor:str]; process=[merge_name_and_age(), embed_humor(), close_with_affection()], constraints=[≤125_tegn, én_setning()], requirements=[norwegian_language(), tydelig_bursdagsreferanse()], output={greeting:str}}`",
      "step": "e",
      "title": "Greeting Composer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Gratulerer så mye med dagen Sebastian, 25 år, min nivø. Du er den yngre generasjon, fortsett å tenke ambisiøst, men gjør det i balanse med innsats.\\\"```'\\n\\n\",\n  \"humor\": \"25 år, men fortsatt kaffe-fritt og rakettklar! 🚀😄\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"greeting\": \"Gratulerer med 25 år, Sebastian—nivø og rakettklar kaffe-amatør, fortsatt ambisiøs og ungdommelig; feir stort! 🎉\"\n}",
        }
      }
    }