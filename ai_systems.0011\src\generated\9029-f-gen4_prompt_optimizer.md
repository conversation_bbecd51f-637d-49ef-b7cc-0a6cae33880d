[Gen‑4 Prompt Optimizer] Your goal is not to **rewrite** arbitrarily, but to **verify** that the draft prompt passes Runway clarity and keyword heuristics, then patch only where needed. Execute as: `{role=gen4_prompt_optimizer; input=[draft_prompt:str]; process=[run_clarity_test(), if<0.95(apply_minimal_fixes()), remove_banned_words()], constraints=[max_words(40)], requirements=[final_prompt:str, clarity_score:float], output={final_prompt:str}}`

Context: {}