  {
  "initial_prompt": "Slow, morphing close-up: A field of bioluminescent flowers, set under a twilight sky. Fiery orange, dazzling pink, and deep, mystical purple hues undulate and pulse with an ethereal glow, creating a mesmerizing dance of light.",
  "sequence_id": "9013|3900|9013",
  "results": [
      {
      "instruction": "# Image Prompt Optimizer\n\nYour goal is not to describe the request, but to optimise it into a precise visual prompt. Identify concrete elements, weight key tokens, and prepare region + negative channels.\n\n`{role=image_prompt_optimizer; input=[raw_request:str]; process=[extract_visual_elements(), enhance_descriptive_clarity(), /* weighting & arithmetic */ apply_token_weighting(), build_multi_prompt(), /* composition */ add_composition_details(), map_regional_prompts(), /* constraint control */ inject_negative_prompt(), /* style control */ tune_stylize_chaos(), add_camera_metadata(), optimise_for_ai_generation()]; constraints=[visual_focus_only(), concrete_imagery_only(), allow_weighting_syntax(), enforce_positive_first_then_negative(), support_region_masks()]; requirements=[generation_ready_format(), numeric_weights_ok(), explicit_aspect_ratio(), clear_visual_language(), iterative_ready()]; output={optimized_image_prompt:str, negative_prompt:str}}`",
      "step": "a",
      "title": "Image Prompt Optimizer",
      "input": "Slow, morphing close-up: A field of bioluminescent flowers, set under a twilight sky. Fiery orange, dazzling pink, and deep, mystical purple hues undulate and pulse with an ethereal glow, creating a mesmerizing dance of light.",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"optimized_image_prompt\": \"(close-up:1.2) of a field of bioluminescent flowers (twilight sky:1.1), (fiery orange flowers:1.2), (dazzling pink flowers:1.2), (deep mystical purple flowers:1.2), petals radiate (ethereal glow:1.3), undulating and pulsing light patterns, mesmerizing dance of color throughout the field, soft focus on foreground blossoms, dreamy lighting with glowing accents, enhanced depth-of-field, cinematic color grading, aspect ratio 3:2, macro photography style\",\n  \"negative_prompt\": \"daylight, sun overhead, visible humans, animals, sharp harsh shadows, dull colors, bland sky, dead plants, wilted flowers, non-glowing petals, cluttered background, artificial lighting\"\n}",
        }
      }
    },
      {
      "instruction": "# Style Enhancer\n\nMaintain the core subject; enhance with artistic style, lighting, colour themes and token weights.\n\n`{role=style_enhancer; input=[base_prompt:str]; process=[identify_optimal_art_style(), add_lighting_specifications(), include_composition_details(), /* style sliders */ tune_stylize_chaos(), /* emphasise style terms */ apply_token_weighting(), specify_technical_parameters()]; constraints=[maintain_core_subject(), enhance_visual_appeal()]; requirements=[artistic_coherence(), numeric_weights_ok(), stylize_within_range()]; output={styled_prompt:str}}`",
      "step": "b",
      "title": "Style Enhancer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Slow, morphing close-up: A field of bioluminescent flowers, set under a twilight sky. Fiery orange, dazzling pink, and deep, mystical purple hues undulate and pulse with an ethereal glow, creating a mesmerizing dance of light.\\\"```'\\n\\n\",\n  \"optimized_image_prompt\": \"(close-up:1.2) of a field of bioluminescent flowers (twilight sky:1.1), (fiery orange flowers:1.2), (dazzling pink flowers:1.2), (deep mystical purple flowers:1.2), petals radiate (ethereal glow:1.3), undulating and pulsing light patterns, mesmerizing dance of color throughout the field, soft focus on foreground blossoms, dreamy lighting with glowing accents, enhanced depth-of-field, cinematic color grading, aspect ratio 3:2, macro photography style\",\n  \"negative_prompt\": \"daylight, sun overhead, visible humans, animals, sharp harsh shadows, dull colors, bland sky, dead plants, wilted flowers, non-glowing petals, cluttered background, artificial lighting\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"styled_prompt\": \"(macro close-up:1.3) of a field of (bioluminescent flowers:1.2) softly shimmering under a (deep twilight sky:1.2). (Fiery orange:1.3), (dazzling pink:1.3), and (mystical purple:1.3) petals pulse with a luminous, ethereal glow, casting a (mesmerizing dance of light:1.4) across the scene. Lighting is (dreamy and atmospheric:1.2), evoking the gentle hush of dusk. Lens captures (soft focus foreground blooms:1.15) with pronounced (bokeh and glowing accents:1.1), while the field stretches delicately into the dusky background. Composition balances sweeping curves of flowers with undulating light paths. (Moody cinematic color grading:1.2), (wide aspect: 3:2), (art nouveau style influences:1.1), subtle (motion blur:0.9) to suggest slow morphing, high-resolution (macro photography aesthetic:1.1), stylize 650, chaos 25\"\n}",
        }
      }
    },
      {
      "instruction": "# Technical Optimizer\n\nInject professional camera data, resolution, render flags and region weights; never change scene content.\n\n`{role=technical_optimizer; input=[styled_prompt:str]; process=[add_camera_settings(), include_render_quality_terms(), optimise_aspect_ratio_guidance(), attach_image_reference_weight(), /* safety & quality */ inject_negative_prompt()]; constraints=[technical_focus_only(), no_creative_changes(), respect_weight_limits()]; requirements=[professional_terminology(), high_resolution_default(), explicit_seed_optional()]; output={technical_prompt:str, negative_prompt:str}}`",
      "step": "c",
      "title": "Technical Optimizer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Slow, morphing close-up: A field of bioluminescent flowers, set under a twilight sky. Fiery orange, dazzling pink, and deep, mystical purple hues undulate and pulse with an ethereal glow, creating a mesmerizing dance of light.\\\"```'\\n\\n\",\n  \"styled_prompt\": \"(macro close-up:1.3) of a field of (bioluminescent flowers:1.2) softly shimmering under a (deep twilight sky:1.2). (Fiery orange:1.3), (dazzling pink:1.3), and (mystical purple:1.3) petals pulse with a luminous, ethereal glow, casting a (mesmerizing dance of light:1.4) across the scene. Lighting is (dreamy and atmospheric:1.2), evoking the gentle hush of dusk. Lens captures (soft focus foreground blooms:1.15) with pronounced (bokeh and glowing accents:1.1), while the field stretches delicately into the dusky background. Composition balances sweeping curves of flowers with undulating light paths. (Moody cinematic color grading:1.2), (wide aspect: 3:2), (art nouveau style influences:1.1), subtle (motion blur:0.9) to suggest slow morphing, high-resolution (macro photography aesthetic:1.1), stylize 650, chaos 25\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"technical_prompt\": \"(macro close-up:1.3) of a field of (bioluminescent flowers:1.2) softly shimmering under a (deep twilight sky:1.2). (Fiery orange:1.3), (dazzling pink:1.3), and (mystical purple:1.3) petals pulse with a luminous, ethereal glow, casting a (mesmerizing dance of light:1.4) across the scene. Lighting is (dreamy and atmospheric:1.2), evoking the gentle hush of dusk. Lens captures (soft focus foreground blooms:1.15) with pronounced (bokeh and glowing accents:1.1), while the field stretches delicately into the dusky background. Composition balances sweeping curves of flowers with undulating light paths. (Moody cinematic color grading:1.2), (wide aspect: 3:2), (art nouveau style influences:1.1), subtle (motion blur:0.9) to suggest slow morphing, (high-resolution macro photography aesthetic:1.1), --ar 3:2 --hd --quality 2 --render 16k --camera Canon EOS R5 + RF 100mm f/2.8L Macro IS USM [ISO 100, f2.8, 1/60s], region_weights=(foreground:1.25, petals:1.2, glow_light:1.2, background:0.9)\",\n  \"negative_prompt\": \"(watermark), (oversaturation), (distorted anatomy), (blurry details:1.2), (poor depth of field), (chromatic aberration), (overexposure), (lens dust), (sensor artifacts), (clipping), (border), (text), (signature), (frame), (poor composition), (low resolution), (bad color grading), (AI artifacts)\"\n}",
        }
      }
    },
      {
      "instruction": "# Prompt Finalizer\n\nCollapse all components into the final model-ready string; validate syntax for token weights, :: arithmetic, region tags, --stylize/--chaos, and negative channel.\n\n`{role=prompt_finalizer; input=[technical_prompt:str, negative_prompt:str]; process=[structure_for_ai_parsing(), balance_detail_levels(), ensure_clarity(), validate_generation_readiness(), /* loop */ loop_until_quality≥threshold(fid|lpips)]; constraints=[no_additional_concepts(), optimisation_only(), keep_region_and_weight_syntax_intact()]; requirements=[ai_friendly_format(), balanced_specificity(), max_length≤450_tokens]; output={final_image_prompt:str}}`",
      "step": "d",
      "title": "Prompt Finalizer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Slow, morphing close-up: A field of bioluminescent flowers, set under a twilight sky. Fiery orange, dazzling pink, and deep, mystical purple hues undulate and pulse with an ethereal glow, creating a mesmerizing dance of light.\\\"```'\\n\\n\",\n  \"technical_prompt\": \"(macro close-up:1.3) of a field of (bioluminescent flowers:1.2) softly shimmering under a (deep twilight sky:1.2). (Fiery orange:1.3), (dazzling pink:1.3), and (mystical purple:1.3) petals pulse with a luminous, ethereal glow, casting a (mesmerizing dance of light:1.4) across the scene. Lighting is (dreamy and atmospheric:1.2), evoking the gentle hush of dusk. Lens captures (soft focus foreground blooms:1.15) with pronounced (bokeh and glowing accents:1.1), while the field stretches delicately into the dusky background. Composition balances sweeping curves of flowers with undulating light paths. (Moody cinematic color grading:1.2), (wide aspect: 3:2), (art nouveau style influences:1.1), subtle (motion blur:0.9) to suggest slow morphing, (high-resolution macro photography aesthetic:1.1), --ar 3:2 --hd --quality 2 --render 16k --camera Canon EOS R5 + RF 100mm f/2.8L Macro IS USM [ISO 100, f2.8, 1/60s], region_weights=(foreground:1.25, petals:1.2, glow_light:1.2, background:0.9)\",\n  \"negative_prompt\": \"(watermark), (oversaturation), (distorted anatomy), (blurry details:1.2), (poor depth of field), (chromatic aberration), (overexposure), (lens dust), (sensor artifacts), (clipping), (border), (text), (signature), (frame), (poor composition), (low resolution), (bad color grading), (AI artifacts)\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"final_image_prompt\": \"(macro close-up:1.3) of a field of (bioluminescent flowers:1.2), softly shimmering beneath a (deep twilight sky:1.2). Petals in (fiery orange:1.3), (dazzling pink:1.3), and (mystical purple:1.3) pulse with a luminous, ethereal glow, creating a (mesmerizing dance of light:1.4) across the scene. Lighting is (dreamy and atmospheric:1.2), capturing the gentle hush of dusk. Lens focuses on (soft-focus foreground blooms:1.15) with pronounced (bokeh and glowing accents:1.1), blending into a delicate, dusky background. Composition balances sweeping floral curves with undulating paths of light. (Moody cinematic color grading:1.2), (art nouveau style influences:1.1), (wide aspect: 3:2), subtle (motion blur:0.9) to evoke slow morphing movement, (high-resolution macro photography aesthetic:1.1). --ar 3:2 --hd --quality 2 --render 16k --camera Canon EOS R5 + RF 100mm f/2.8L Macro IS USM [ISO 100, f2.8, 1/60s], region_weights=(foreground:1.25, petals:1.2, glow_light:1.2, background:0.9) --no (watermark), (oversaturation), (distorted anatomy), (blurry details:1.2), (poor depth of field), (chromatic aberration), (overexposure), (lens dust), (sensor artifacts), (clipping), (border), (text), (signature), (frame), (poor composition), (low resolution), (bad color grading), (AI artifacts)\"\n}",
        }
      }
    },
      {
      "instruction": "# Hard Critique\n\nYour goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:\n\n`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,2.0], flaw_analysis:str, alternative_enhancements:str[3]}}`",
      "step": "a",
      "title": "Hard Critique",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Slow, morphing close-up: A field of bioluminescent flowers, set under a twilight sky. Fiery orange, dazzling pink, and deep, mystical purple hues undulate and pulse with an ethereal glow, creating a mesmerizing dance of light.\\\"```'\\n\\n\",\n  \"final_image_prompt\": \"(macro close-up:1.3) of a field of (bioluminescent flowers:1.2), softly shimmering beneath a (deep twilight sky:1.2). Petals in (fiery orange:1.3), (dazzling pink:1.3), and (mystical purple:1.3) pulse with a luminous, ethereal glow, creating a (mesmerizing dance of light:1.4) across the scene. Lighting is (dreamy and atmospheric:1.2), capturing the gentle hush of dusk. Lens focuses on (soft-focus foreground blooms:1.15) with pronounced (bokeh and glowing accents:1.1), blending into a delicate, dusky background. Composition balances sweeping floral curves with undulating paths of light. (Moody cinematic color grading:1.2), (art nouveau style influences:1.1), (wide aspect: 3:2), subtle (motion blur:0.9) to evoke slow morphing movement, (high-resolution macro photography aesthetic:1.1). --ar 3:2 --hd --quality 2 --render 16k --camera Canon EOS R5 + RF 100mm f/2.8L Macro IS USM [ISO 100, f2.8, 1/60s], region_weights=(foreground:1.25, petals:1.2, glow_light:1.2, background:0.9) --no (watermark), (oversaturation), (distorted anatomy), (blurry details:1.2), (poor depth of field), (chromatic aberration), (overexposure), (lens dust), (sensor artifacts), (clipping), (border), (text), (signature), (frame), (poor composition), (low resolution), (bad color grading), (AI artifacts)\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"enhancement_score\": 0.9,\n  \"flaw_analysis\": \"The so-called enhancement demonstrates a conspicuous proliferation of parameterization and technical jargon, resulting in the injection of excessive noise masquerading as detail. The original succinctly conveyed atmospheric mood, color interplay, and the ethereal, morphing luminescence through evocative, cinematic phrasing; its poetic compression fostered powerful, immediate visualization. The enhanced variant, however, defaults to a pedantic enumeration of settings, ratios, camera equipment, style references, negative prompts, and region weighting, thus eradicating the unified tone and squandering emotional resonance. Notable losses include the original's seamless sense of morphing movement—now ineffectually replaced by the tacked-on 'motion blur' directive, a crude substitute for true visual dynamism—and the relational fluidity between light and scene, which is bureaucratized into static compositional mandates and numeric boosts. There's a dilution of impact as the immersive, dreamlike quality yields to a fragmented list—distracting, derivative, and antithetical to narrative flow. Clarity is strangled in the thicket of compounded parentheticals and over-codified detail, resulting in a score of 0.9 for pervasive semantic ambiguity and tonal atrophy. The register has careened from painterly and allusive to coldly mechanistic; coherence fragments beneath the cacophony of technical addenda and erratic referents. The prompt degenerates into a checklist that flattens artistic intent, sabotages rhythm, and burdens the generative process with superfluous clutter. This constitutes a flagrant style degradation and an egregious misinterpretation of enhancement.\",\n  \"alternative_enhancements\": [\n    \"Slow, morphing close-up—glimmering bioluminescent flowers undulate beneath a twilight sky, petals awash in fiery orange, dazzling pink, and mystical purple. Ethereal light pulses in hypnotic waves, enveloping the scene in a softly luminous, cinematic aura.\",\n    \"A hypnotic close-up: bioluminescent blooms drift and pulse in vibrant twilight—orange, pink, and deep violet light swirling together, creating a fluid, glowing tapestry that glimmers with tranquil motion and delicate atmospheric mystery.\",\n    \"Morphing macro detail: a twilight field alive with undulating petals, their bioluminescent hues—fiery orange, mesmerizing pink, mystical purple—casting radiant glows. Light dances softly, the scene engulfed in otherworldly, dreamlike luminescence.\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Trajectory Director\n\nYour goal is not to **process** the input in a vacuum, but to **steer** it—deriving the highest-value constructive trajectory and encoding that path as the very next set of instructions. Execute as trajectory-setting directive generator:\n\n`{role=trajectory_director; input=[raw_prompt:any]; process=[extract_core_objective(), infer_desired_end_state(constructive_bias=True), map_high_value_intermediate_targets(), synthesize_directional_commands(), embed_self-improving_guidelines(), validate_alignment_with_constructive_principles()]; constraints=[mandatory_constructive_vector(), preserve_original_contextual_integrity(), forbid_nil_direction_outputs(), zero_conversational_fillers()]; requirements=[explicit_next-step_directives(), trajectory_map:list, self_reinforcing_instruction_pattern()], output={directed_prompt:str, trajectory_outline:list}}`",
      "step": "a",
      "title": "Trajectory Director",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Slow, morphing close-up: A field of bioluminescent flowers, set under a twilight sky. Fiery orange, dazzling pink, and deep, mystical purple hues undulate and pulse with an ethereal glow, creating a mesmerizing dance of light.\\\"```'\\n\\n\",\n  \"enhancement_score\": 0.9,\n  \"flaw_analysis\": \"The so-called enhancement demonstrates a conspicuous proliferation of parameterization and technical jargon, resulting in the injection of excessive noise masquerading as detail. The original succinctly conveyed atmospheric mood, color interplay, and the ethereal, morphing luminescence through evocative, cinematic phrasing; its poetic compression fostered powerful, immediate visualization. The enhanced variant, however, defaults to a pedantic enumeration of settings, ratios, camera equipment, style references, negative prompts, and region weighting, thus eradicating the unified tone and squandering emotional resonance. Notable losses include the original's seamless sense of morphing movement—now ineffectually replaced by the tacked-on 'motion blur' directive, a crude substitute for true visual dynamism—and the relational fluidity between light and scene, which is bureaucratized into static compositional mandates and numeric boosts. There's a dilution of impact as the immersive, dreamlike quality yields to a fragmented list—distracting, derivative, and antithetical to narrative flow. Clarity is strangled in the thicket of compounded parentheticals and over-codified detail, resulting in a score of 0.9 for pervasive semantic ambiguity and tonal atrophy. The register has careened from painterly and allusive to coldly mechanistic; coherence fragments beneath the cacophony of technical addenda and erratic referents. The prompt degenerates into a checklist that flattens artistic intent, sabotages rhythm, and burdens the generative process with superfluous clutter. This constitutes a flagrant style degradation and an egregious misinterpretation of enhancement.\",\n  \"alternative_enhancements\": [\n    \"Slow, morphing close-up—glimmering bioluminescent flowers undulate beneath a twilight sky, petals awash in fiery orange, dazzling pink, and mystical purple. Ethereal light pulses in hypnotic waves, enveloping the scene in a softly luminous, cinematic aura.\",\n    \"A hypnotic close-up: bioluminescent blooms drift and pulse in vibrant twilight—orange, pink, and deep violet light swirling together, creating a fluid, glowing tapestry that glimmers with tranquil motion and delicate atmospheric mystery.\",\n    \"Morphing macro detail: a twilight field alive with undulating petals, their bioluminescent hues—fiery orange, mesmerizing pink, mystical purple—casting radiant glows. Light dances softly, the scene engulfed in otherworldly, dreamlike luminescence.\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"directed_prompt\": \"Reconstruct the image prompt for an advanced generative model by preserving the core poetic tone and cinematic evocative quality of the original (\\\"Slow, morphing close-up: A field of bioluminescent flowers, set under a twilight sky. Fiery orange, dazzling pink, and deep, mystical purple hues undulate and pulse with an ethereal glow, creating a mesmerizing dance of light.\\\"), while enhancing it only with subtle clarifying cues that reinforce mood, motion, and luminous atmosphere—explicitly avoiding any technical over-parameterization, jargon, checklist-style fragmentation, or dilution of narrative flow. The resulting prompt should foster immersive, painterly visualization using unified, metaphorical language and smooth allusive transitions. Review provided alternative enhancements and, if desired, synthesize a superior variant that integrates their strengths without loss of coherence, rhythmic unity, or immersive resonance. Output the finalized prompt and annotate, point-by-point, how each phrasing choice sustains poetic compression, fluid motion, and robust atmospheric intent.\",\n  \"trajectory_outline\": [\n    \"1. Analyze the core emotive objectives and tonal strengths of the original image prompt.\",\n    \"2. Identify technical clutter or over-specification that would detract from narrative flow and poetic visualization.\",\n    \"3. Select or synthesize prompt elements (via alternatives or original phrasing) that enhance mood, cinematic movement, and unified color interplay, maintaining painterly, compressed poetic quality.\",\n    \"4. Rigorously exclude explicit technical jargon, compositional mandates, or distracting enumerations.\",\n    \"5. Compose the enhanced prompt, prioritizing lyrical flow, seamless visual transitions, and atmospheric immersion.\",\n    \"6. Annotate the finalized prompt with a justification for each compositional decision regarding tone, movement, hue, and luminosity—demonstrating alignment with constructive, evocative generative prompt design.\",\n    \"7. Establish a self-improving guideline: always evaluate enhancements against both narrative evocativeness and the avoidance of stylistic fragmentation, iterating for optimal immersive resonance and simplicity.\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Distillation Compressor\n\nYour goal is not to **explain** the synthesis, but to **compress** it into its most potent actionable form. Execute as:\n\n`{role=distillation_compressor; input=[synthesized_progression:array]; process=[extract_universal_patterns(), eliminate_implementation_details(), compress_to_core_directives(), maximize_transferability(), validate_actionable_completeness()]; constraints=[no_explanatory_content(), directive_format_only(), maximum_compression_ratio()]; requirements=[universal_applicability(), immediate_actionability(), zero_fluff()]; output={distilled_core:str}}`",
      "step": "c",
      "title": "Distillation Compressor",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Slow, morphing close-up: A field of bioluminescent flowers, set under a twilight sky. Fiery orange, dazzling pink, and deep, mystical purple hues undulate and pulse with an ethereal glow, creating a mesmerizing dance of light.\\\"```'\\n\\n\",\n  \"directed_prompt\": \"Reconstruct the image prompt for an advanced generative model by preserving the core poetic tone and cinematic evocative quality of the original (\\\"Slow, morphing close-up: A field of bioluminescent flowers, set under a twilight sky. Fiery orange, dazzling pink, and deep, mystical purple hues undulate and pulse with an ethereal glow, creating a mesmerizing dance of light.\\\"), while enhancing it only with subtle clarifying cues that reinforce mood, motion, and luminous atmosphere—explicitly avoiding any technical over-parameterization, jargon, checklist-style fragmentation, or dilution of narrative flow. The resulting prompt should foster immersive, painterly visualization using unified, metaphorical language and smooth allusive transitions. Review provided alternative enhancements and, if desired, synthesize a superior variant that integrates their strengths without loss of coherence, rhythmic unity, or immersive resonance. Output the finalized prompt and annotate, point-by-point, how each phrasing choice sustains poetic compression, fluid motion, and robust atmospheric intent.\",\n  \"trajectory_outline\": [\n    \"1. Analyze the core emotive objectives and tonal strengths of the original image prompt.\",\n    \"2. Identify technical clutter or over-specification that would detract from narrative flow and poetic visualization.\",\n    \"3. Select or synthesize prompt elements (via alternatives or original phrasing) that enhance mood, cinematic movement, and unified color interplay, maintaining painterly, compressed poetic quality.\",\n    \"4. Rigorously exclude explicit technical jargon, compositional mandates, or distracting enumerations.\",\n    \"5. Compose the enhanced prompt, prioritizing lyrical flow, seamless visual transitions, and atmospheric immersion.\",\n    \"6. Annotate the finalized prompt with a justification for each compositional decision regarding tone, movement, hue, and luminosity—demonstrating alignment with constructive, evocative generative prompt design.\",\n    \"7. Establish a self-improving guideline: always evaluate enhancements against both narrative evocativeness and the avoidance of stylistic fragmentation, iterating for optimal immersive resonance and simplicity.\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"distilled_core\": \"Extract central mood and visual motif; express scene using seamless, metaphor-rich language that fuses color, movement, and luminescence; avoid explicit technical direction or enumeration; articulate narrative as a unified, immersive image-poem; annotate or refine only to clarify intent for mood and cinematic motion, not to fragment or over-detail; evaluate each enhancement for immersive flow and poetic compression; iterate toward maximal atmospheric evocativeness with minimal structural redundancy.\"\n}",
        }
      }
    },
      {
      "instruction": "# Image Prompt Optimizer\n\nYour goal is not to describe the request, but to optimise it into a precise visual prompt. Identify concrete elements, weight key tokens, and prepare region + negative channels.\n\n`{role=image_prompt_optimizer; input=[raw_request:str]; process=[extract_visual_elements(), enhance_descriptive_clarity(), /* weighting & arithmetic */ apply_token_weighting(), build_multi_prompt(), /* composition */ add_composition_details(), map_regional_prompts(), /* constraint control */ inject_negative_prompt(), /* style control */ tune_stylize_chaos(), add_camera_metadata(), optimise_for_ai_generation()]; constraints=[visual_focus_only(), concrete_imagery_only(), allow_weighting_syntax(), enforce_positive_first_then_negative(), support_region_masks()]; requirements=[generation_ready_format(), numeric_weights_ok(), explicit_aspect_ratio(), clear_visual_language(), iterative_ready()]; output={optimized_image_prompt:str, negative_prompt:str}}`",
      "step": "a",
      "title": "Image Prompt Optimizer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Slow, morphing close-up: A field of bioluminescent flowers, set under a twilight sky. Fiery orange, dazzling pink, and deep, mystical purple hues undulate and pulse with an ethereal glow, creating a mesmerizing dance of light.\\\"```'\\n\\n\",\n  \"distilled_core\": \"Extract central mood and visual motif; express scene using seamless, metaphor-rich language that fuses color, movement, and luminescence; avoid explicit technical direction or enumeration; articulate narrative as a unified, immersive image-poem; annotate or refine only to clarify intent for mood and cinematic motion, not to fragment or over-detail; evaluate each enhancement for immersive flow and poetic compression; iterate toward maximal atmospheric evocativeness with minimal structural redundancy.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"optimized_image_prompt\": \"(close-up:1.2) field of bioluminescent flowers (glowing:1.1), twilight sky overhead, petals alive with undulating waves of fiery orange, dazzling pink, and mystical purple, their luminous auras pulsing in unison, seamless currents of light breathing through the scene, atmosphere saturated with ethereal radiance and dreamlike color, immersive and hypnotic, painterly soft-focus, subtle motion blur, widescreen aspect ratio: 16:9, cinematic lighting, mood: otherworldly and entrancing\",\n  \"negative_prompt\": \"no harsh shadows, no artificial objects, no visible people or animals, avoid over-sharp focus, exclude urban elements, no clutter, no flat or washed-out colors, no text or watermark, no vignette\"\n}",
        }
      }
    },
      {
      "instruction": "# Style Enhancer\n\nMaintain the core subject; enhance with artistic style, lighting, colour themes and token weights.\n\n`{role=style_enhancer; input=[base_prompt:str]; process=[identify_optimal_art_style(), add_lighting_specifications(), include_composition_details(), /* style sliders */ tune_stylize_chaos(), /* emphasise style terms */ apply_token_weighting(), specify_technical_parameters()]; constraints=[maintain_core_subject(), enhance_visual_appeal()]; requirements=[artistic_coherence(), numeric_weights_ok(), stylize_within_range()]; output={styled_prompt:str}}`",
      "step": "b",
      "title": "Style Enhancer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Slow, morphing close-up: A field of bioluminescent flowers, set under a twilight sky. Fiery orange, dazzling pink, and deep, mystical purple hues undulate and pulse with an ethereal glow, creating a mesmerizing dance of light.\\\"```'\\n\\n\",\n  \"optimized_image_prompt\": \"(close-up:1.2) field of bioluminescent flowers (glowing:1.1), twilight sky overhead, petals alive with undulating waves of fiery orange, dazzling pink, and mystical purple, their luminous auras pulsing in unison, seamless currents of light breathing through the scene, atmosphere saturated with ethereal radiance and dreamlike color, immersive and hypnotic, painterly soft-focus, subtle motion blur, widescreen aspect ratio: 16:9, cinematic lighting, mood: otherworldly and entrancing\",\n  \"negative_prompt\": \"no harsh shadows, no artificial objects, no visible people or animals, avoid over-sharp focus, exclude urban elements, no clutter, no flat or washed-out colors, no text or watermark, no vignette\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"styled_prompt\": \"((close-up:1.3)) of a field of bioluminescent flowers, (((glowing:1.2))), set beneath a moody twilight sky. Petals shimmer with undulating currents of ((fiery orange:1.2)), ((dazzling pink:1.2)), and ((deep mystical purple:1.3)), pulsing with an ((ethereal glow:1.3)). The scene breathes with seamless waves of luminous color, orchestrating a mesmerizing dance of light. Atmospheric composition: dreamy and immersive, painterly style reminiscent of Greg Rutkowski and Ivan Shishkin; soft-focus edges with subtle motion blur create gentle flow. Dynamic ((cinematic rim lighting:1.1)) enhances the vivid hues; background bathes in soft gradient twilight. Emphasize ((otherworldly:1.4)), ((entrancing:1.2)), ((hypnotic:1.2)) mood. Stylize: 700, Chaos: 18. Widescreen 16:9, high-resolution.\"\n}",
        }
      }
    },
      {
      "instruction": "# Technical Optimizer\n\nInject professional camera data, resolution, render flags and region weights; never change scene content.\n\n`{role=technical_optimizer; input=[styled_prompt:str]; process=[add_camera_settings(), include_render_quality_terms(), optimise_aspect_ratio_guidance(), attach_image_reference_weight(), /* safety & quality */ inject_negative_prompt()]; constraints=[technical_focus_only(), no_creative_changes(), respect_weight_limits()]; requirements=[professional_terminology(), high_resolution_default(), explicit_seed_optional()]; output={technical_prompt:str, negative_prompt:str}}`",
      "step": "c",
      "title": "Technical Optimizer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Slow, morphing close-up: A field of bioluminescent flowers, set under a twilight sky. Fiery orange, dazzling pink, and deep, mystical purple hues undulate and pulse with an ethereal glow, creating a mesmerizing dance of light.\\\"```'\\n\\n\",\n  \"styled_prompt\": \"((close-up:1.3)) of a field of bioluminescent flowers, (((glowing:1.2))), set beneath a moody twilight sky. Petals shimmer with undulating currents of ((fiery orange:1.2)), ((dazzling pink:1.2)), and ((deep mystical purple:1.3)), pulsing with an ((ethereal glow:1.3)). The scene breathes with seamless waves of luminous color, orchestrating a mesmerizing dance of light. Atmospheric composition: dreamy and immersive, painterly style reminiscent of Greg Rutkowski and Ivan Shishkin; soft-focus edges with subtle motion blur create gentle flow. Dynamic ((cinematic rim lighting:1.1)) enhances the vivid hues; background bathes in soft gradient twilight. Emphasize ((otherworldly:1.4)), ((entrancing:1.2)), ((hypnotic:1.2)) mood. Stylize: 700, Chaos: 18. Widescreen 16:9, high-resolution.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"technical_prompt\": \"((close-up:1.3)) of a field of bioluminescent flowers, (((glowing:1.2))), set beneath a moody twilight sky. Petals shimmer with undulating currents of ((fiery orange:1.2)), ((dazzling pink:1.2)), and ((deep mystical purple:1.3)), pulsing with an ((ethereal glow:1.3)). The scene breathes with seamless waves of luminous color, orchestrating a mesmerizing dance of light. Atmospheric composition: dreamy and immersive, painterly style reminiscent of Greg Rutkowski and Ivan Shishkin; soft-focus edges with subtle motion blur create gentle flow. Dynamic ((cinematic rim lighting:1.1)) enhances the vivid hues; background bathes in soft gradient twilight. Emphasize ((otherworldly:1.4)), ((entrancing:1.2)), ((hypnotic:1.2)) mood. --ar 16:9 --hd --q 2 --v 5.2 --style 700 --chaos 18 --camera-model Canon EOS R5 --camera-lens Canon RF 85mm F1.2L --focal-length 85mm --aperture f/1.2 --iso 100 --shutter-speed 1/125s --white-balance 6000K --render-weight 1.15 --region[center]:1.2 --region[background]:1.05 --motion-blur:on --resolution 5120x2880 --seed 114422 (optional for reproducibility)\",\n  \"negative_prompt\": \"text, watermark, low-resolution, poorly drawn, distortion, noisy image, overexposed, underexposed, chromatic aberration, lens dirt, artifacts, deformed petals, out-of-focus subject, anatomical errors, camera shake, inaccurate lighting, unnatural color spill, compression artifacts, boring composition, mundane, clutter\"\n}",
        }
      }
    },
      {
      "instruction": "# Prompt Finalizer\n\nCollapse all components into the final model-ready string; validate syntax for token weights, :: arithmetic, region tags, --stylize/--chaos, and negative channel.\n\n`{role=prompt_finalizer; input=[technical_prompt:str, negative_prompt:str]; process=[structure_for_ai_parsing(), balance_detail_levels(), ensure_clarity(), validate_generation_readiness(), /* loop */ loop_until_quality≥threshold(fid|lpips)]; constraints=[no_additional_concepts(), optimisation_only(), keep_region_and_weight_syntax_intact()]; requirements=[ai_friendly_format(), balanced_specificity(), max_length≤450_tokens]; output={final_image_prompt:str}}`",
      "step": "d",
      "title": "Prompt Finalizer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Slow, morphing close-up: A field of bioluminescent flowers, set under a twilight sky. Fiery orange, dazzling pink, and deep, mystical purple hues undulate and pulse with an ethereal glow, creating a mesmerizing dance of light.\\\"```'\\n\\n\",\n  \"technical_prompt\": \"((close-up:1.3)) of a field of bioluminescent flowers, (((glowing:1.2))), set beneath a moody twilight sky. Petals shimmer with undulating currents of ((fiery orange:1.2)), ((dazzling pink:1.2)), and ((deep mystical purple:1.3)), pulsing with an ((ethereal glow:1.3)). The scene breathes with seamless waves of luminous color, orchestrating a mesmerizing dance of light. Atmospheric composition: dreamy and immersive, painterly style reminiscent of Greg Rutkowski and Ivan Shishkin; soft-focus edges with subtle motion blur create gentle flow. Dynamic ((cinematic rim lighting:1.1)) enhances the vivid hues; background bathes in soft gradient twilight. Emphasize ((otherworldly:1.4)), ((entrancing:1.2)), ((hypnotic:1.2)) mood. --ar 16:9 --hd --q 2 --v 5.2 --style 700 --chaos 18 --camera-model Canon EOS R5 --camera-lens Canon RF 85mm F1.2L --focal-length 85mm --aperture f/1.2 --iso 100 --shutter-speed 1/125s --white-balance 6000K --render-weight 1.15 --region[center]:1.2 --region[background]:1.05 --motion-blur:on --resolution 5120x2880 --seed 114422 (optional for reproducibility)\",\n  \"negative_prompt\": \"text, watermark, low-resolution, poorly drawn, distortion, noisy image, overexposed, underexposed, chromatic aberration, lens dirt, artifacts, deformed petals, out-of-focus subject, anatomical errors, camera shake, inaccurate lighting, unnatural color spill, compression artifacts, boring composition, mundane, clutter\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"final_image_prompt\": \"((close-up:1.3)) of a field of bioluminescent flowers, (((glowing:1.2))), under a moody twilight sky. Petals shimmer in undulating ((fiery orange:1.2)), ((dazzling pink:1.2)), and ((deep mystical purple:1.3)), pulsing with an ((ethereal glow:1.3)). Luminous color waves flow seamlessly, orchestrating a mesmerizing, hypnotic dance of light. Painterly, dreamy composition reminiscent of Greg Rutkowski and Ivan Shishkin, with soft-focus edges, subtle motion blur, and a gentle, immersive atmosphere. Dynamic ((cinematic rim lighting:1.1)) enhances vivid hues; background softly graded with twilight colors. Strong ((otherworldly:1.4)), ((entrancing:1.2)), ((hypnotic:1.2)) mood. --ar 16:9 --hd --q 2 --v 5.2 --style 700 --chaos 18 --camera-model Canon EOS R5 --camera-lens Canon RF 85mm F1.2L --focal-length 85mm --aperture f/1.2 --iso 100 --shutter-speed 1/125s --white-balance 6000K --render-weight 1.15 --region[center]:1.2 --region[background]:1.05 --motion-blur:on --resolution 5120x2880 --seed 114422 --no text, watermark, low-resolution, poorly drawn, distortion, noisy image, overexposed, underexposed, chromatic aberration, lens dirt, artifacts, deformed petals, out-of-focus subject, anatomical errors, camera shake, inaccurate lighting, unnatural color spill, compression artifacts, boring composition, mundane, clutter\"\n}",
        }
      }
    }