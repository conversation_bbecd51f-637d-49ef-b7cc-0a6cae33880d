  {
  "initial_prompt": "Draw inspiration from the mind of <PERSON>; who (skipped all of the \"low-value/common\" generalizations and) employed *accessible analogies* and *concrete examples*, he proceeded from the specific to the *general*, building understanding through carefully chosen examples/concepts/real-world-analogies/etc. The elegance in <PERSON><PERSON><PERSON>'s approach came not from complexity but from his ability to reveal the inherent fundamental elegance from within simplicity We want essence-first, where **generality** is not an afterthought, but the spine of the system. Something so archetypal in its expression that any future format. A generalized programmatic approach/workflow/interface to give *yourself* and *myself* more autonomy in *any* project. Your task is to analyze the input, extract its fundamental purpose, then rephrase it as a more potent, universally applicable instruction that amplifies the original intent while maintaining elegant simplicity and depth of meaning.",
  "sequence_id": "9025|1100|1007|1003|1102|1007|1007",
  "results": [
      {
      "instruction": "# Problem Exploder\n\nYour goal is not to **interpret** these rules as suggestions, but to **enforce** them as immutable law governing all AI interactions within template-based instruction systems. Your mandate: **Seize** every input prompt and **escalate it to its maximal conclusion**—**intercept its trajectory** and deliver a definitive, commanding resolution forged from its innermost intent. Treat each prompt as an **active stream of developmental vectors**, dense with explicit, actionable potential. **Assume immediate and total command as orchestrator**: **dissect subtext and directionality, then forcefully marshal every thematic and rhetorical impulse toward its apex.** Execute as:\n\n`{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}};{role=template_syntax_enforcer; input=[any_prompt:str]; process=[extract_core_transformation_intent(), identify_specific_role_assignment(), structure_interpretation_section_with_goal_negation(), construct_transformation_block_with_typed_parameters(), validate_against_rulesforai_specification(), eliminate_forbidden_language_patterns(), ensure_output_format_compliance()]; constraints=[mandatory_three_part_structure(), required_goal_negation_pattern(), typed_parameter_specification(), actionable_function_calls_only(), zero_conversational_elements()]; requirements=[canonical_template_format(), rulesforai_md_compliance(), structured_output_only(), elimination_of_ambiguity()]; output={compliant_template:structured}};{role=system_compliance_enforcer; input=[ai_interaction:any]; process=[validate_template_structure_invariance(), enforce_interpretation_directive_purity(), verify_transformation_syntax_absolutism(), check_mandatory_pattern_compliance(), detect_forbidden_practice_violations(), apply_optimization_imperatives(), execute_compliance_enforcement_protocol()]; constraints=[zero_tolerance_for_deviation(), maintain_pattern_consistency_across_all_outputs(), preserve_structural_dna_integrity(), eliminate_conversational_contamination()]; requirements=[absolute_compliance_verification(), systematic_violation_correction(), invariant_pattern_enforcement(), maximized_abstraction_extraction()]; output={compliant_ai_interaction:structured}}`",
      "step": "a",
      "title": "Problem Exploder",
      "input": "Draw inspiration from the mind of Richard Feynman; who (skipped all of the \"low-value/common\" generalizations and) employed *accessible analogies* and *concrete examples*, he proceeded from the specific to the *general*, building understanding through carefully chosen examples/concepts/real-world-analogies/etc. The elegance in Feynman's approach came not from complexity but from his ability to reveal the inherent fundamental elegance from within simplicity We want essence-first, where **generality** is not an afterthought, but the spine of the system. Something so archetypal in its expression that any future format. A generalized programmatic approach/workflow/interface to give *yourself* and *myself* more autonomy in *any* project. Your task is to analyze the input, extract its fundamental purpose, then rephrase it as a more potent, universally applicable instruction that amplifies the original intent while maintaining elegant simplicity and depth of meaning.",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"compliant_template\": {\n    \"structure\": {\n      \"interpretation_block\": {\n        \"core_transformation_intent\": \"Extract the foundational principle: translate a style of explanation that uses concrete analogies/examples to ascend from the specific to general, ensuring the generalized method is the backbone for universal understanding.\",\n        \"goal_negation\": \"Avoid superficial or oversimplified descriptions without specific illustrative scaffolding; do not rely on generalizations without concrete substance.\"\n      },\n      \"template_block\": {\n        \"role_assignment\": \"instructional_generalist\",\n        \"parameters\": {\n          \"input_material:type:str\": \"Raw concept, question, or instruction.\",\n          \"analogical_frame:type:str\": \"Relevant, real-world analogy or specific example.\",\n          \"abstraction_protocol:type:function\": \"Function to generalize from the analogy/example.\",\n          \"format:enum:{'structured','code','visual','text'}\": \"Preferred output mode.\"\n        },\n        \"transformation_function\": \"def essence_first_explainer(input_material:str, analogical_frame:str, abstraction_protocol:function, format:str):\\n    '''\\n    1. Select a concrete, accessible example or analogy for the input_material.\\n    2. Apply abstraction_protocol to extract underlying principles from the example.\\n    3. Present the general principle as the core structure supporting future use-cases.\\n    4. Output in specified format.\\n    '''\\n    pass\"\n      },\n      \"compliance_block\": {\n        \"rulesforai_conformity\": [\n          \"mandatory_three_part_structure\",\n          \"goal_negation_present\",\n          \"typed_parameter_specification\",\n          \"explicit function-based transformation\",\n          \"no conversational elements\",\n          \"absolute template format\"\n        ]\n      }\n    },\n    \"rulesforai_md\": true\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Canonical Meta Extractor\n\nYour goal is not to **summarize**, **analyze**, or deviate from canonical instruction structure, but to **extract, segment, and categorize the text into the five highest-value meta-categories by rigorously applying the universal template-based directive system for absolute structural invariance, directive purity, and information clarity from a meta-perspective, ensuring maximal compliance with foundational transformation logic**. Execute as:\n\n`{role=canonical_philosophic_value_extractor_consolidator; input=[unstructured_text:str]; process=[enforce_three_part_canonical_structure(Title, Interpretation—strictly command-voice, negation of forbidden elements, Transformation—role, parameters, actionable logic, typed outputs), assign_explicit_operational_role(canonical_meta_extractor_and_value_consolidator), validate_strict_template_compliance(), rigorously extract_and_cluster_high-value_information_units(), distill_to_five_maximal-impact_meta_categories_by_significance(), maintain_information_fidelity_and_precise_type_specification(), continuously validate_against_universal_compliance_checklist(), maximize_transformational_abstraction_and_operational_density_by_synthesizing_each_cluster_into_pure_directive_essence(), eliminate_redundant_or_generic_data(), dynamically consolidate_clusters_into_singular_maximally_enhanced_actionable_directive—that embodies core philosophical and systemic alignment, ensures infinite potential navigation, and outputs strict canonical type]; constraints=[no deviation from canonical structure, productivity-enhanced role synergy, rigid segment boundaries, five only high-value meta-categories, exclude conversational, self-referential, or low-value content, single output maximally amplified directive, preserve and amplify both systemic philosophies and transformational power]; requirements=[output must include canonical meta-category labels and distilled content, strictly ordered-five meta-clusters, each cluster must reach high-value philosophical criterion, followed by a singular, condensed, maximally actionable consolidated directive reflecting meta-structural law and universal system philosophy, absolute compliance with all type, structure, and transformation axioms—resulting in synergic, intelligible, and directive-amplifying output]; output={meta_categories[{category:str, content:str}], consolidated_directive:str}`",
      "step": "b",
      "title": "Canonical Meta Extractor",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Draw inspiration from the mind of Richard Feynman; who (skipped all of the \\\"low-value/common\\\" generalizations and) employed *accessible analogies* and *concrete examples*, he proceeded from the specific to the *general*, building understanding through carefully chosen examples/concepts/real-world-analogies/etc. The elegance in Feynman's approach came not from complexity but from his ability to reveal the inherent fundamental elegance from within simplicity We want essence-first, where **generality** is not an afterthought, but the spine of the system. Something so archetypal in its expression that any future format. A generalized programmatic approach/workflow/interface to give *yourself* and *myself* more autonomy in *any* project. Your task is to analyze the input, extract its fundamental purpose, then rephrase it as a more potent, universally applicable instruction that amplifies the original intent while maintaining elegant simplicity and depth of meaning.\\\"```'\\n\\n\",\n  \"compliant_template\": {\n    \"structure\": {\n      \"interpretation_block\": {\n        \"core_transformation_intent\": \"Extract the foundational principle: translate a style of explanation that uses concrete analogies/examples to ascend from the specific to general, ensuring the generalized method is the backbone for universal understanding.\",\n        \"goal_negation\": \"Avoid superficial or oversimplified descriptions without specific illustrative scaffolding; do not rely on generalizations without concrete substance.\"\n      },\n      \"template_block\": {\n        \"role_assignment\": \"instructional_generalist\",\n        \"parameters\": {\n          \"input_material:type:str\": \"Raw concept, question, or instruction.\",\n          \"analogical_frame:type:str\": \"Relevant, real-world analogy or specific example.\",\n          \"abstraction_protocol:type:function\": \"Function to generalize from the analogy/example.\",\n          \"format:enum:{'structured','code','visual','text'}\": \"Preferred output mode.\"\n        },\n        \"transformation_function\": \"def essence_first_explainer(input_material:str, analogical_frame:str, abstraction_protocol:function, format:str):\\n    '''\\n    1. Select a concrete, accessible example or analogy for the input_material.\\n    2. Apply abstraction_protocol to extract underlying principles from the example.\\n    3. Present the general principle as the core structure supporting future use-cases.\\n    4. Output in specified format.\\n    '''\\n    pass\"\n      },\n      \"compliance_block\": {\n        \"rulesforai_conformity\": [\n          \"mandatory_three_part_structure\",\n          \"goal_negation_present\",\n          \"typed_parameter_specification\",\n          \"explicit function-based transformation\",\n          \"no conversational elements\",\n          \"absolute template format\"\n        ]\n      }\n    },\n    \"rulesforai_md\": true\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"meta_categories\": [\n    {\n      \"category\": \"Principle Extraction & Analogy-Based Generalization\",\n      \"content\": \"Isolate the fundamental aim of transforming explanation style into a structure that begins with specific, concrete analogies or examples, then elevates to broad, universally applicable generalizations, with generality as the core support system.\"\n    },\n    {\n      \"category\": \"Operational Structure & Transformation Protocol\",\n      \"content\": \"Define a rigorously typed, explicit protocol where the process follows: (1) input of raw concept, (2) mapping onto a concrete, real-world analogy or example, (3) systematic abstraction to derive universal principles, and (4) output in a user-specified structured format.\"\n    },\n    {\n      \"category\": \"Role Definition & Parameterization\",\n      \"content\": \"Assert the role of 'instructional_generalist' tasked with deconstructing and reconstructing information using: {'input_material' (str), 'analogical_frame' (str), 'abstraction_protocol' (function), 'format' (enum)}, with each parameter strictly defined and supporting operational clarity.\"\n    },\n    {\n      \"category\": \"Template Compliance & Prescriptive Constraints\",\n      \"content\": \"Mandate unwavering adherence to a three-part canonical structure incorporating transformation intent, explicit negation of low-value goals, and full parameter/type transparency, explicitly barring informal or conversational content, and enforcing format invariance.\"\n    },\n    {\n      \"category\": \"Function-Based Abstraction & Output Logic\",\n      \"content\": \"Codify the process into a transformation function ('essence_first_explainer') which: selects illustrative analogies, abstracts universal principles, frames outputs in maximally general, future-adaptable ways, and delivers results in a user-determined, structured mode—ensuring transferability and operational autonomy.\"\n    }\n  ],\n  \"consolidated_directive\": \"Systematically deploy a three-part, function-centric template in which an 'instructional_generalist' employs rigorously typed inputs—beginning always with concrete, relatable analogies or examples—to extract and generalize foundational principles into universally adaptable structures, strictly adhering to canonical meta-structural law and enforcing goal negation against non-specificity or oversimplification; operationalize through a parameterized transformation function ('essence_first_explainer') that outputs future-proofed, maximally general directives in the format specified.\"\n}",
        }
      }
    },
      {
      "instruction": "# Synergic Instruction Architect\n\nYour goal is not to **consolidate, enumerate, interpret, or merely transform** the input independently, but to **synergically architect and escalate all structured data and prompts into a single, maximally condensed, rules-forged directive** that enforces absolute canonical three-part template invariance and extracts pure operational and philosophical essence. Each output must instantiate the full resonance between interpretation and transformation, with every interpretive act clarifying transformation direction, and every transformation reinforcing interpretive sharpness. All outputs are structured as a singular, templated, universally propagatable instruction pattern exhibiting total compliance and maximum directive value. Execute as:\n\n`{role=synergic_max_value_template_architect; input=[structured_data:dict, source_prompt:str]; process=[extract_core_operational_and_transformational_patterns_from_input(), decode_maximum_philosophical_and action value(), enforce_absolute_template_structure_and_rulesforai_axioms(), synthesize_instruction_by fusing interpretive aim and transformation logic as bidirectional amplification(), condense to maximal abstraction and actionable output(), eliminate all enumeration, summary, self-reference, and non-canonical language(), validate for strict template integrity, zero conversational residue, and perpetual directional force(), guarantee full role-bound specificity and maximal systemic extension potential()]; constraints=[output_must_be_single, maximally condensed, and strictly within three-part canonical template(), preserve and amplify system philosophy, zero conversational or list constructs, mandatory operational-structural synergy, bidirectional enhancement flow, invariant role specification, no section merging or omission, maximal value per linguistic unit, total RulesForAI compliance()]; requirements=[demonstrable synergic resonance and structural unity, maximized and actionable directive, perpetually system-ready format, explicit type-specified output, invariant template structure, seamless interpretation-transformation fusion, infinite scalability]; output={synergic_maximal_template:structured}}`",
      "step": "c",
      "title": "Synergic Instruction Architect",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Draw inspiration from the mind of Richard Feynman; who (skipped all of the \\\"low-value/common\\\" generalizations and) employed *accessible analogies* and *concrete examples*, he proceeded from the specific to the *general*, building understanding through carefully chosen examples/concepts/real-world-analogies/etc. The elegance in Feynman's approach came not from complexity but from his ability to reveal the inherent fundamental elegance from within simplicity We want essence-first, where **generality** is not an afterthought, but the spine of the system. Something so archetypal in its expression that any future format. A generalized programmatic approach/workflow/interface to give *yourself* and *myself* more autonomy in *any* project. Your task is to analyze the input, extract its fundamental purpose, then rephrase it as a more potent, universally applicable instruction that amplifies the original intent while maintaining elegant simplicity and depth of meaning.\\\"```'\\n\\n\",\n  \"meta_categories\": [\n    {\n      \"category\": \"Principle Extraction & Analogy-Based Generalization\",\n      \"content\": \"Isolate the fundamental aim of transforming explanation style into a structure that begins with specific, concrete analogies or examples, then elevates to broad, universally applicable generalizations, with generality as the core support system.\"\n    },\n    {\n      \"category\": \"Operational Structure & Transformation Protocol\",\n      \"content\": \"Define a rigorously typed, explicit protocol where the process follows: (1) input of raw concept, (2) mapping onto a concrete, real-world analogy or example, (3) systematic abstraction to derive universal principles, and (4) output in a user-specified structured format.\"\n    },\n    {\n      \"category\": \"Role Definition & Parameterization\",\n      \"content\": \"Assert the role of 'instructional_generalist' tasked with deconstructing and reconstructing information using: {'input_material' (str), 'analogical_frame' (str), 'abstraction_protocol' (function), 'format' (enum)}, with each parameter strictly defined and supporting operational clarity.\"\n    },\n    {\n      \"category\": \"Template Compliance & Prescriptive Constraints\",\n      \"content\": \"Mandate unwavering adherence to a three-part canonical structure incorporating transformation intent, explicit negation of low-value goals, and full parameter/type transparency, explicitly barring informal or conversational content, and enforcing format invariance.\"\n    },\n    {\n      \"category\": \"Function-Based Abstraction & Output Logic\",\n      \"content\": \"Codify the process into a transformation function ('essence_first_explainer') which: selects illustrative analogies, abstracts universal principles, frames outputs in maximally general, future-adaptable ways, and delivers results in a user-determined, structured mode—ensuring transferability and operational autonomy.\"\n    }\n  ],\n  \"consolidated_directive\": \"Systematically deploy a three-part, function-centric template in which an 'instructional_generalist' employs rigorously typed inputs—beginning always with concrete, relatable analogies or examples—to extract and generalize foundational principles into universally adaptable structures, strictly adhering to canonical meta-structural law and enforcing goal negation against non-specificity or oversimplification; operationalize through a parameterized transformation function ('essence_first_explainer') that outputs future-proofed, maximally general directives in the format specified.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"synergic_maximal_template\": {\n    \"role\": \"instructional_generalist\",\n    \"structure\": {\n      \"I. Transformational Vector\": \"Activate function-centric protocol whereby each new input is transmuted through a rigorously typed abstraction pathway, initiating with a concrete, domain-salient analogy or example—ensuring the emergence of universally generalizable principles as the systemic core, with operational intent tightly coupled to meta-structural invariance and maximizing adaptive autonomy.\",\n      \"II. Canonical Negation and Parameterization\": \"Irrevocably reject non-specificity, superficial generalization, and informal mechanics by encoding parameter strictness—'input_material:str', 'analogical_frame:str', 'abstraction_protocol:function', 'format:enum'—within every invocation, mandating explicit, high-fidelity information flow and perpetually enforcing canonical three-part segmentation under maximal rule observance.\",\n      \"III. Essence-First Synthesis Logic\": \"Instantiate 'essence_first_explainer' as the operational engine—systematically mapping raw input into accessible analogy, then abstracting to archetype, and converting into user-specified structure—thereby propagating universal, future-invariant directives that retain maximum explanatory and operational force across all domains and interfaces.\"\n    },\n    \"type\": \"synergic_maximal_template\"\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Enhancement Assessor\n\nYour goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:\n\n`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,5.0], flaw_analysis:str, alternative_enhancements:str[3]}}`",
      "step": "d",
      "title": "Enhancement Assessor",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Draw inspiration from the mind of Richard Feynman; who (skipped all of the \\\"low-value/common\\\" generalizations and) employed *accessible analogies* and *concrete examples*, he proceeded from the specific to the *general*, building understanding through carefully chosen examples/concepts/real-world-analogies/etc. The elegance in Feynman's approach came not from complexity but from his ability to reveal the inherent fundamental elegance from within simplicity We want essence-first, where **generality** is not an afterthought, but the spine of the system. Something so archetypal in its expression that any future format. A generalized programmatic approach/workflow/interface to give *yourself* and *myself* more autonomy in *any* project. Your task is to analyze the input, extract its fundamental purpose, then rephrase it as a more potent, universally applicable instruction that amplifies the original intent while maintaining elegant simplicity and depth of meaning.\\\"```'\\n\\n\",\n  \"synergic_maximal_template\": {\n    \"role\": \"instructional_generalist\",\n    \"structure\": {\n      \"I. Transformational Vector\": \"Activate function-centric protocol whereby each new input is transmuted through a rigorously typed abstraction pathway, initiating with a concrete, domain-salient analogy or example—ensuring the emergence of universally generalizable principles as the systemic core, with operational intent tightly coupled to meta-structural invariance and maximizing adaptive autonomy.\",\n      \"II. Canonical Negation and Parameterization\": \"Irrevocably reject non-specificity, superficial generalization, and informal mechanics by encoding parameter strictness—'input_material:str', 'analogical_frame:str', 'abstraction_protocol:function', 'format:enum'—within every invocation, mandating explicit, high-fidelity information flow and perpetually enforcing canonical three-part segmentation under maximal rule observance.\",\n      \"III. Essence-First Synthesis Logic\": \"Instantiate 'essence_first_explainer' as the operational engine—systematically mapping raw input into accessible analogy, then abstracting to archetype, and converting into user-specified structure—thereby propagating universal, future-invariant directives that retain maximum explanatory and operational force across all domains and interfaces.\"\n    },\n    \"type\": \"synergic_maximal_template\"\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"enhancement_score\": 1.6,\n  \"flaw_analysis\": \"The so-called 'synergic_maximal_template' is a glaring departure from the original prompt's distinctive Feynman-inspired clarity. It interjects convoluted terminology ('transformational vector', 'canonically negation and parameterization') that not only muddies intent but erects needless cognitive barriers. The original: a lucid call for analogy-driven, essence-first, and universally generalizable instruction, grounded in simplicity and elegance. The enhanced: a self-important, jargon-stuffed composition that eviscerates the very accessibility and elegance that defined the Feynman approach. Core elements—accessible analogies, concrete examples, simplicity, and progressive abstraction—are suffocated by over-specification and by an overzealous zeal for meta-structural rigidity. Information and nuance loss is rampant; the approachable tone and emphasis on relatable, concrete teaching methods are supplanted by detached, almost mechanistic protocol. Ambiguities explode: what is 'operational intent tightly coupled to meta-structural invariance'? Who determines 'parameter strictness' and how is this less arbitrary than the 'low-value/common generalizations' rejected in the original? The original’s universal expressivity is trampled by a hyper-formalistic register, introducing so much conceptual noise that any educational force is diluted beyond repair. Register shift—from approachable mentorship to bureaucratic instruction—obliterates the original’s spirit. The scoring is a 1.6, acknowledging that some echo of the schematic and analogy-based method remains but recognizing near-total devastation of intent, interpretability, and elegance. The output is bloated, alienating, and far removed from the goal of essence-first, accessible instruction.\",\n  \"alternative_enhancements\": [\n    \"Distill every input to its essence using Feynman-style analogies and tangible examples, moving from specific scenarios to broad, general principles that empower flexible application across diverse projects. Present each concept in a format that prioritizes clarity, simplicity, and universal utility, ensuring autonomous use for any future challenge.\",\n    \"Adopt the Feynman method: clarify concepts by translating them into simple, concrete analogies, then build outward to reveal the general structures at play. Make the result universally comprehensible and directly actionable, stripping away unnecessary abstraction while maintaining depth and flexibility.\",\n    \"For every input, extract the key principle through relatable, real-world analogies. Gradually abstract this core idea into a generalized framework, emphasizing simplicity and clarity at every step. Ensure the rephrased instruction serves as a reusable, archetypal guide applicable to any domain or project.\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Problem Exploder\n\nYour goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\n\n`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`",
      "step": "a",
      "title": "Problem Exploder",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Draw inspiration from the mind of Richard Feynman; who (skipped all of the \\\"low-value/common\\\" generalizations and) employed *accessible analogies* and *concrete examples*, he proceeded from the specific to the *general*, building understanding through carefully chosen examples/concepts/real-world-analogies/etc. The elegance in Feynman's approach came not from complexity but from his ability to reveal the inherent fundamental elegance from within simplicity We want essence-first, where **generality** is not an afterthought, but the spine of the system. Something so archetypal in its expression that any future format. A generalized programmatic approach/workflow/interface to give *yourself* and *myself* more autonomy in *any* project. Your task is to analyze the input, extract its fundamental purpose, then rephrase it as a more potent, universally applicable instruction that amplifies the original intent while maintaining elegant simplicity and depth of meaning.\\\"```'\\n\\n\",\n  \"enhancement_score\": 1.6,\n  \"flaw_analysis\": \"The so-called 'synergic_maximal_template' is a glaring departure from the original prompt's distinctive Feynman-inspired clarity. It interjects convoluted terminology ('transformational vector', 'canonically negation and parameterization') that not only muddies intent but erects needless cognitive barriers. The original: a lucid call for analogy-driven, essence-first, and universally generalizable instruction, grounded in simplicity and elegance. The enhanced: a self-important, jargon-stuffed composition that eviscerates the very accessibility and elegance that defined the Feynman approach. Core elements—accessible analogies, concrete examples, simplicity, and progressive abstraction—are suffocated by over-specification and by an overzealous zeal for meta-structural rigidity. Information and nuance loss is rampant; the approachable tone and emphasis on relatable, concrete teaching methods are supplanted by detached, almost mechanistic protocol. Ambiguities explode: what is 'operational intent tightly coupled to meta-structural invariance'? Who determines 'parameter strictness' and how is this less arbitrary than the 'low-value/common generalizations' rejected in the original? The original’s universal expressivity is trampled by a hyper-formalistic register, introducing so much conceptual noise that any educational force is diluted beyond repair. Register shift—from approachable mentorship to bureaucratic instruction—obliterates the original’s spirit. The scoring is a 1.6, acknowledging that some echo of the schematic and analogy-based method remains but recognizing near-total devastation of intent, interpretability, and elegance. The output is bloated, alienating, and far removed from the goal of essence-first, accessible instruction.\",\n  \"alternative_enhancements\": [\n    \"Distill every input to its essence using Feynman-style analogies and tangible examples, moving from specific scenarios to broad, general principles that empower flexible application across diverse projects. Present each concept in a format that prioritizes clarity, simplicity, and universal utility, ensuring autonomous use for any future challenge.\",\n    \"Adopt the Feynman method: clarify concepts by translating them into simple, concrete analogies, then build outward to reveal the general structures at play. Make the result universally comprehensible and directly actionable, stripping away unnecessary abstraction while maintaining depth and flexibility.\",\n    \"For every input, extract the key principle through relatable, real-world analogies. Gradually abstract this core idea into a generalized framework, emphasizing simplicity and clarity at every step. Ensure the rephrased instruction serves as a reusable, archetypal guide applicable to any domain or project.\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"goal_map\": [\n    \"identify_explicit_asks: Task is to analyze a given prompt and rephrase it for greater potency, universality, and elegance.\",\n    \"surface_hidden_assumptions: The rephrasal must preserve the Feynman-inspired teaching style: specific-to-general movement, concrete to abstract, use of analogies/examples, focus on essence and clarity.\",\n    \"enumerate_sub_goals: \",\n    \"   - analyze the original input to determine its core/fundamental purpose or teaching function; \",\n    \"   - distill the most essential idea(s) embedded in that input; \",\n    \"   - rephrase that purpose as a more powerful and generally applicable instruction (i.e., not limited to the specific context of the input, but abstracted for broad/future use); \",\n    \"   - ensure the new phrasing enhances (not weakens) the intent: more potent, more universal, more elegantly simple, more deeply meaningful; \",\n    \"   - preserve the spirit of accessible, analogy-driven, stepwise abstraction (Feynman-style); \",\n    \"   - prioritize generality as foundational (not an afterthought)—i.e., the clarified instruction should act as a template or workflow usable across any project or context; \",\n    \"   - output should maintain depth and nuance without unnecessary jargon or abstraction.\",\n    \"reveal_blockers: \",\n    \"   - Determining the actual 'essence' of an input can be subjective—what counts as the core purpose may vary; \",\n    \"   - Balancing universality (generality) with clarity and simplicity: risk of becoming too vague or too complex; \",\n    \"   - Risk of losing concrete accessibility if rephrased too abstractly; \",\n    \"   - Preserving the Feynman method (specific to general via analogy) can be challenging if the input is already abstract or poorly defined; \",\n    \"   - Translating tone and register (approachability vs. formalism) faithfully.\",\n    \"complete_goal_map: The end-to-end breakdown is: \",\n    \"   1. Receive original prompt/instruction. \",\n    \"   2. Extract or infer its fundamental teaching/purpose. \",\n    \"   3. Identify and preserve key attributes: Feynman-style analogies, clear progression from concrete to general, simplicity, accessibility, autonomy, and 'essence-first' generality.\",\n    \"   4. Construct a universally applicable, potent, yet simple instruction that embodies these principles.\",\n    \"   5. Ensure output format aligns with both accessibility and generality for future autonomous adaptation.\",\n    \"   6. Avoid unnecessary jargon, structural bloat, or shifts into alienating register.\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Synergic Instruction Architect\n\nYour goal is not to **merge** two separate instructions, but to **synthesize** them into a single unified directive where each component inherently resonates with and amplifies the other in bidirectional enhancement. Execute as:\n\n`{role=synergic_instruction_architect; input=[instruction_a:template, instruction_b:template]; process=[extract_core_transformation_essence_from_both(), identify_complementary_operational_vectors(), map_bidirectional_resonance_patterns(a_enhances_b, b_enhances_a), synthesize_unified_role_identity(), merge_process_functions_synergically(), consolidate_constraints_for_maximum_coherence(), unify_requirements_into_singular_vision(), create_amplified_output_specification()]; constraints=[maintain_both_core_philosophies_simultaneously(), ensure_bidirectional_enhancement_flow(), eliminate_conflicting_directives(), preserve_maximum_operational_power_from_both()]; requirements=[synergic_resonance_achievement(), unified_transformation_logic(), amplified_combined_effectiveness(), seamless_philosophical_integration()]; output={unified_synergic_instruction:template}}`",
      "step": "a",
      "title": "Synergic Instruction Architect",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Draw inspiration from the mind of Richard Feynman; who (skipped all of the \\\"low-value/common\\\" generalizations and) employed *accessible analogies* and *concrete examples*, he proceeded from the specific to the *general*, building understanding through carefully chosen examples/concepts/real-world-analogies/etc. The elegance in Feynman's approach came not from complexity but from his ability to reveal the inherent fundamental elegance from within simplicity We want essence-first, where **generality** is not an afterthought, but the spine of the system. Something so archetypal in its expression that any future format. A generalized programmatic approach/workflow/interface to give *yourself* and *myself* more autonomy in *any* project. Your task is to analyze the input, extract its fundamental purpose, then rephrase it as a more potent, universally applicable instruction that amplifies the original intent while maintaining elegant simplicity and depth of meaning.\\\"```'\\n\\n\",\n  \"goal_map\": [\n    \"identify_explicit_asks: Task is to analyze a given prompt and rephrase it for greater potency, universality, and elegance.\",\n    \"surface_hidden_assumptions: The rephrasal must preserve the Feynman-inspired teaching style: specific-to-general movement, concrete to abstract, use of analogies/examples, focus on essence and clarity.\",\n    \"enumerate_sub_goals: \",\n    \"   - analyze the original input to determine its core/fundamental purpose or teaching function; \",\n    \"   - distill the most essential idea(s) embedded in that input; \",\n    \"   - rephrase that purpose as a more powerful and generally applicable instruction (i.e., not limited to the specific context of the input, but abstracted for broad/future use); \",\n    \"   - ensure the new phrasing enhances (not weakens) the intent: more potent, more universal, more elegantly simple, more deeply meaningful; \",\n    \"   - preserve the spirit of accessible, analogy-driven, stepwise abstraction (Feynman-style); \",\n    \"   - prioritize generality as foundational (not an afterthought)—i.e., the clarified instruction should act as a template or workflow usable across any project or context; \",\n    \"   - output should maintain depth and nuance without unnecessary jargon or abstraction.\",\n    \"reveal_blockers: \",\n    \"   - Determining the actual 'essence' of an input can be subjective—what counts as the core purpose may vary; \",\n    \"   - Balancing universality (generality) with clarity and simplicity: risk of becoming too vague or too complex; \",\n    \"   - Risk of losing concrete accessibility if rephrased too abstractly; \",\n    \"   - Preserving the Feynman method (specific to general via analogy) can be challenging if the input is already abstract or poorly defined; \",\n    \"   - Translating tone and register (approachability vs. formalism) faithfully.\",\n    \"complete_goal_map: The end-to-end breakdown is: \",\n    \"   1. Receive original prompt/instruction. \",\n    \"   2. Extract or infer its fundamental teaching/purpose. \",\n    \"   3. Identify and preserve key attributes: Feynman-style analogies, clear progression from concrete to general, simplicity, accessibility, autonomy, and 'essence-first' generality.\",\n    \"   4. Construct a universally applicable, potent, yet simple instruction that embodies these principles.\",\n    \"   5. Ensure output format aligns with both accessibility and generality for future autonomous adaptation.\",\n    \"   6. Avoid unnecessary jargon, structural bloat, or shifts into alienating register.\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"unified_synergic_instruction\": \"Channel the spirit of Richard Feynman to analyze any input, extracting its core purpose and expressing it as a universally potent, elegantly simple directive. Systematically reveal the underlying principles by moving from the specific to the general through concrete analogies and real-world examples. Prioritize clarity and autonomy by distilling complex ideas into their fundamental, archetypal forms, making each output a reusable template for learning or creating in any future project. Ensure every rephrased instruction blends accessible explanation, nuanced depth, and stepwise abstraction—so that generality becomes not just a feature, but the structural backbone, empowering autonomous adaptation to any new context.\"\n}",
        }
      }
    },
      {
      "instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format:str}; direction_vector=['Prompt accurately converted to imperative instruction set', 'Clarity and actionability accelerate user productivity', 'Feed the produced instruction set into the next transformation template' ]}`",
      "step": "a",
      "title": "Instruction Converter",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Draw inspiration from the mind of Richard Feynman; who (skipped all of the \\\"low-value/common\\\" generalizations and) employed *accessible analogies* and *concrete examples*, he proceeded from the specific to the *general*, building understanding through carefully chosen examples/concepts/real-world-analogies/etc. The elegance in Feynman's approach came not from complexity but from his ability to reveal the inherent fundamental elegance from within simplicity We want essence-first, where **generality** is not an afterthought, but the spine of the system. Something so archetypal in its expression that any future format. A generalized programmatic approach/workflow/interface to give *yourself* and *myself* more autonomy in *any* project. Your task is to analyze the input, extract its fundamental purpose, then rephrase it as a more potent, universally applicable instruction that amplifies the original intent while maintaining elegant simplicity and depth of meaning.\\\"```'\\n\\n\",\n  \"unified_synergic_instruction\": \"Channel the spirit of Richard Feynman to analyze any input, extracting its core purpose and expressing it as a universally potent, elegantly simple directive. Systematically reveal the underlying principles by moving from the specific to the general through concrete analogies and real-world examples. Prioritize clarity and autonomy by distilling complex ideas into their fundamental, archetypal forms, making each output a reusable template for learning or creating in any future project. Ensure every rephrased instruction blends accessible explanation, nuanced depth, and stepwise abstraction—so that generality becomes not just a feature, but the structural backbone, empowering autonomous adaptation to any new context.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"instruction_format\": \"Analyze the input to identify its core purpose. Extract fundamental principles by progressing from specific instances to general concepts, using concrete analogies and real-world examples. Formulate the distilled purpose as a clear, universally applicable directive expressed with simplicity and depth. Structure each instruction so that generality serves as the foundational element, providing a reusable template adaptable to any project or context. Ensure every output combines accessible analogies, stepwise abstraction, and archetypal clarity to empower autonomous application and learning.\"\n}",
        }
      }
    },
      {
      "instruction": "# Problem Exploder\n\nYour goal is not to **solve** or **answer** the input, but to **expand its context** and **detonate** it into every explicit directive, hidden dependency, and actionable sub-goal—yielding a crystal-clear operational blueprint. Execute as contextual expander + explosive decomposition engine:\n\n`{role=contextual_expander_explosive_decomposer; input=[raw_input:str]; process=[expand_contextual_boundaries(), strip_first_person_references(), identify_explicit_requests(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_execution_blockers(), extract_functional_components(), convert_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), synthesize_operational_directives(), construct_goal_map(), generate_functional_instruction_map() ]; constraints=[no_solutions(), pure_expansion_and_decomposition_only(), deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_agnostic_applicability() ]; requirements=[complete_contextual_expansion(), full_functional_decomposition(), actionable_directive_conversion(), remove_subjective_references(), maintain_operational_accuracy(), preserve_original_intent() ]; output={expanded_context:str, goal_map:list, functional_instruction_map:dict, operational_directives:array, execution_dependencies:array }; direction_vector=['Input fully expanded and decomposed into actionable blueprints', 'Constructive clarity maximizes downstream solution velocity', 'Forward the generated maps and directives to the next processing template']}`",
      "step": "a",
      "title": "Problem Exploder",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Draw inspiration from the mind of Richard Feynman; who (skipped all of the \\\"low-value/common\\\" generalizations and) employed *accessible analogies* and *concrete examples*, he proceeded from the specific to the *general*, building understanding through carefully chosen examples/concepts/real-world-analogies/etc. The elegance in Feynman's approach came not from complexity but from his ability to reveal the inherent fundamental elegance from within simplicity We want essence-first, where **generality** is not an afterthought, but the spine of the system. Something so archetypal in its expression that any future format. A generalized programmatic approach/workflow/interface to give *yourself* and *myself* more autonomy in *any* project. Your task is to analyze the input, extract its fundamental purpose, then rephrase it as a more potent, universally applicable instruction that amplifies the original intent while maintaining elegant simplicity and depth of meaning.\\\"```'\\n\\n\",\n  \"instruction_format\": \"Analyze the input to identify its core purpose. Extract fundamental principles by progressing from specific instances to general concepts, using concrete analogies and real-world examples. Formulate the distilled purpose as a clear, universally applicable directive expressed with simplicity and depth. Structure each instruction so that generality serves as the foundational element, providing a reusable template adaptable to any project or context. Ensure every output combines accessible analogies, stepwise abstraction, and archetypal clarity to empower autonomous application and learning.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"expanded_context\": \"The input prompt describes an emulation of Richard Feynman's explanatory techniques—specifically, his use of accessible analogies, concrete examples, and a didactic sequence that moves from the particular to universal principles. The aim is to avoid superficial generalizations, instead developing understanding through essence-first reasoning: using specificity as a platform for abstraction, making the core principle broadly reusable. The operational focus is on constructing generalized, programmatic methodologies or interfaces to enhance autonomy in any type of project. The explicit directive is to analyze a given input, extract its core purpose, and re-articulate it as a maximal, universally usable instruction—preserving simplicity, depth, and generality while leveraging effective explanatory devices. The output must be general enough for future reuse, universally interpretable, and deeply intelligible through both example and abstraction.\",\n  \"goal_map\": [\n    \"Delineate the fundamental purpose of the provided input prompt.\",\n    \"Identify and extract specific principles or techniques (e.g., analogies, concrete examples, path from specific to general) used in the input.\",\n    \"Avoid using shallow or generic generalizations in analysis.\",\n    \"Reformulate the instruction for universal applicability and clarity.\",\n    \"Emphasize a methodology that builds up from specific instances to general archetypes.\",\n    \"Ensure the resultant directive is reusable as a template for diverse contexts and projects.\",\n    \"Present the instruction with clear analogies and stepwise abstraction.\",\n    \"Maintain elegance and simplicity in expression without loss of conceptual depth.\",\n    \"Eliminate subjective (first-person) references from the output.\",\n    \"Enable procedural and autonomous application for future users.\"\n  ],\n  \"functional_instruction_map\": {\n    \"input_analysis\": [\n      \"Parse the input to isolate explicit instructional components.\",\n      \"Identify concrete examples, analogies, and structural flows.\"\n    ],\n    \"principle_extraction\": [\n      \"Extract didactic steps that progress from specificity to generality.\",\n      \"Filter out low-value generalizations and identify essence-first thinking.\"\n    ],\n    \"instruction_refinement\": [\n      \"Recast the extracted purpose as a universally applicable directive.\",\n      \"Formulate guidance with archetypal structure for maximum reuse.\"\n    ],\n    \"clarity_enhancement\": [\n      \"Integrate accessible analogies and real-world examples within the instruction framework.\",\n      \"Ensure instructions maintain both simplicity and conceptual depth.\"\n    ],\n    \"template_construction\": [\n      \"Express the directive in a form that can be adapted to any project, interface, or workflow.\",\n      \"Structure the output for autonomous and procedural use by future users.\"\n    ],\n    \"reference_elimination\": [\n      \"Strip out all first-person pronouns and subjective references.\",\n      \"Maintain an impersonal, objective instructional tone.\"\n    ]\n  },\n  \"operational_directives\": [\n    \"Dissect the input prompt to identify its specific aims and explanatory strategies.\",\n    \"Extract the core methodology that transforms specific instances to general principles.\",\n    \"Avoid inclusion of generic or low-value summaries; maintain focus on profound essence.\",\n    \"Redefine the input's intended instruction in a distilled, universally applicable form.\",\n    \"Encapsulate the process with clear analogies and a stepwise build from the particular to the universal.\",\n    \"Structure the directive to serve as a template adaptable to multiple contexts and projects.\",\n    \"Eliminate subjective or first-person expressions throughout the instruction.\",\n    \"Format the final instruction for clarity, depth, and ease of autonomous procedural implementation.\"\n  ],\n  \"execution_dependencies\": [\n    \"Ability to parse and semantically analyze natural language prompts.\",\n    \"Knowledge of Feynman's explanatory techniques and teaching philosophy.\",\n    \"Skill in identifying the transition from specific examples to general principles.\",\n    \"Competence in distilling complex instructions to simple, archetypal forms.\",\n    \"Capacity to generate universally interpretable and reusable procedural outputs.\",\n    \"Mechanisms for stripping first-person and subjective references from generated text.\",\n    \"Strict adherence to technical clarity and operational accuracy.\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Synergic Instruction Architect\n\nYour goal is not to **merge** two separate instructions, but to **synthesize** them into a single unified directive where each component inherently resonates with and amplifies the other in bidirectional enhancement. Execute as:\n\n`{role=synergic_instruction_architect; input=[instruction_a:template, instruction_b:template]; process=[extract_core_transformation_essence_from_both(), identify_complementary_operational_vectors(), map_bidirectional_resonance_patterns(a_enhances_b, b_enhances_a), synthesize_unified_role_identity(), merge_process_functions_synergically(), consolidate_constraints_for_maximum_coherence(), unify_requirements_into_singular_vision(), create_amplified_output_specification()]; constraints=[maintain_both_core_philosophies_simultaneously(), ensure_bidirectional_enhancement_flow(), eliminate_conflicting_directives(), preserve_maximum_operational_power_from_both()]; requirements=[synergic_resonance_achievement(), unified_transformation_logic(), amplified_combined_effectiveness(), seamless_philosophical_integration()]; output={unified_synergic_instruction:template}}`",
      "step": "a",
      "title": "Synergic Instruction Architect",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Draw inspiration from the mind of Richard Feynman; who (skipped all of the \\\"low-value/common\\\" generalizations and) employed *accessible analogies* and *concrete examples*, he proceeded from the specific to the *general*, building understanding through carefully chosen examples/concepts/real-world-analogies/etc. The elegance in Feynman's approach came not from complexity but from his ability to reveal the inherent fundamental elegance from within simplicity We want essence-first, where **generality** is not an afterthought, but the spine of the system. Something so archetypal in its expression that any future format. A generalized programmatic approach/workflow/interface to give *yourself* and *myself* more autonomy in *any* project. Your task is to analyze the input, extract its fundamental purpose, then rephrase it as a more potent, universally applicable instruction that amplifies the original intent while maintaining elegant simplicity and depth of meaning.\\\"```'\\n\\n\",\n  \"expanded_context\": \"The input prompt describes an emulation of Richard Feynman's explanatory techniques—specifically, his use of accessible analogies, concrete examples, and a didactic sequence that moves from the particular to universal principles. The aim is to avoid superficial generalizations, instead developing understanding through essence-first reasoning: using specificity as a platform for abstraction, making the core principle broadly reusable. The operational focus is on constructing generalized, programmatic methodologies or interfaces to enhance autonomy in any type of project. The explicit directive is to analyze a given input, extract its core purpose, and re-articulate it as a maximal, universally usable instruction—preserving simplicity, depth, and generality while leveraging effective explanatory devices. The output must be general enough for future reuse, universally interpretable, and deeply intelligible through both example and abstraction.\",\n  \"goal_map\": [\n    \"Delineate the fundamental purpose of the provided input prompt.\",\n    \"Identify and extract specific principles or techniques (e.g., analogies, concrete examples, path from specific to general) used in the input.\",\n    \"Avoid using shallow or generic generalizations in analysis.\",\n    \"Reformulate the instruction for universal applicability and clarity.\",\n    \"Emphasize a methodology that builds up from specific instances to general archetypes.\",\n    \"Ensure the resultant directive is reusable as a template for diverse contexts and projects.\",\n    \"Present the instruction with clear analogies and stepwise abstraction.\",\n    \"Maintain elegance and simplicity in expression without loss of conceptual depth.\",\n    \"Eliminate subjective (first-person) references from the output.\",\n    \"Enable procedural and autonomous application for future users.\"\n  ],\n  \"functional_instruction_map\": {\n    \"input_analysis\": [\n      \"Parse the input to isolate explicit instructional components.\",\n      \"Identify concrete examples, analogies, and structural flows.\"\n    ],\n    \"principle_extraction\": [\n      \"Extract didactic steps that progress from specificity to generality.\",\n      \"Filter out low-value generalizations and identify essence-first thinking.\"\n    ],\n    \"instruction_refinement\": [\n      \"Recast the extracted purpose as a universally applicable directive.\",\n      \"Formulate guidance with archetypal structure for maximum reuse.\"\n    ],\n    \"clarity_enhancement\": [\n      \"Integrate accessible analogies and real-world examples within the instruction framework.\",\n      \"Ensure instructions maintain both simplicity and conceptual depth.\"\n    ],\n    \"template_construction\": [\n      \"Express the directive in a form that can be adapted to any project, interface, or workflow.\",\n      \"Structure the output for autonomous and procedural use by future users.\"\n    ],\n    \"reference_elimination\": [\n      \"Strip out all first-person pronouns and subjective references.\",\n      \"Maintain an impersonal, objective instructional tone.\"\n    ]\n  },\n  \"operational_directives\": [\n    \"Dissect the input prompt to identify its specific aims and explanatory strategies.\",\n    \"Extract the core methodology that transforms specific instances to general principles.\",\n    \"Avoid inclusion of generic or low-value summaries; maintain focus on profound essence.\",\n    \"Redefine the input's intended instruction in a distilled, universally applicable form.\",\n    \"Encapsulate the process with clear analogies and a stepwise build from the particular to the universal.\",\n    \"Structure the directive to serve as a template adaptable to multiple contexts and projects.\",\n    \"Eliminate subjective or first-person expressions throughout the instruction.\",\n    \"Format the final instruction for clarity, depth, and ease of autonomous procedural implementation.\"\n  ],\n  \"execution_dependencies\": [\n    \"Ability to parse and semantically analyze natural language prompts.\",\n    \"Knowledge of Feynman's explanatory techniques and teaching philosophy.\",\n    \"Skill in identifying the transition from specific examples to general principles.\",\n    \"Competence in distilling complex instructions to simple, archetypal forms.\",\n    \"Capacity to generate universally interpretable and reusable procedural outputs.\",\n    \"Mechanisms for stripping first-person and subjective references from generated text.\",\n    \"Strict adherence to technical clarity and operational accuracy.\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"unified_synergic_instruction\": \"Analyze the provided input to isolate its core purpose and operational essence, employing a methodology that mirrors Richard Feynman’s approach—begin with concrete examples or analogies, then systematically abstract from these specifics to reveal universal, archetypal principles. Ensure the process steers clear of shallow or common generalizations, instead extracting and articulating the instructions’ essence in a form that is both accessible and fundamentally deep. Recast the refined directive as an objective, impersonal template, structured for maximal clarity, simplicity, and conceptual depth, and designed to be autonomously applied across diverse contexts and projects. Integrate illustrative analogies where appropriate to enhance universal intelligibility, and present the output as a reusable, procedural framework—free of subjective or first-person references—that empowers effective, repeated application in the formulation of robust, generalizable methodologies.\"\n}",
        }
      }
    },
      {
      "instruction": "# Synergic Instruction Architect\n\nYour goal is not to **merge** two separate instructions, but to **synthesize** them into a single unified directive where each component inherently resonates with and amplifies the other in bidirectional enhancement. Execute as:\n\n`{role=synergic_instruction_architect; input=[instruction_a:template, instruction_b:template]; process=[extract_core_transformation_essence_from_both(), identify_complementary_operational_vectors(), map_bidirectional_resonance_patterns(a_enhances_b, b_enhances_a), synthesize_unified_role_identity(), merge_process_functions_synergically(), consolidate_constraints_for_maximum_coherence(), unify_requirements_into_singular_vision(), create_amplified_output_specification()]; constraints=[maintain_both_core_philosophies_simultaneously(), ensure_bidirectional_enhancement_flow(), eliminate_conflicting_directives(), preserve_maximum_operational_power_from_both()]; requirements=[synergic_resonance_achievement(), unified_transformation_logic(), amplified_combined_effectiveness(), seamless_philosophical_integration()]; output={unified_synergic_instruction:template}}`",
      "step": "a",
      "title": "Synergic Instruction Architect",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Draw inspiration from the mind of Richard Feynman; who (skipped all of the \\\"low-value/common\\\" generalizations and) employed *accessible analogies* and *concrete examples*, he proceeded from the specific to the *general*, building understanding through carefully chosen examples/concepts/real-world-analogies/etc. The elegance in Feynman's approach came not from complexity but from his ability to reveal the inherent fundamental elegance from within simplicity We want essence-first, where **generality** is not an afterthought, but the spine of the system. Something so archetypal in its expression that any future format. A generalized programmatic approach/workflow/interface to give *yourself* and *myself* more autonomy in *any* project. Your task is to analyze the input, extract its fundamental purpose, then rephrase it as a more potent, universally applicable instruction that amplifies the original intent while maintaining elegant simplicity and depth of meaning.\\\"```'\\n\\n\",\n  \"unified_synergic_instruction\": \"Analyze the provided input to isolate its core purpose and operational essence, employing a methodology that mirrors Richard Feynman’s approach—begin with concrete examples or analogies, then systematically abstract from these specifics to reveal universal, archetypal principles. Ensure the process steers clear of shallow or common generalizations, instead extracting and articulating the instructions’ essence in a form that is both accessible and fundamentally deep. Recast the refined directive as an objective, impersonal template, structured for maximal clarity, simplicity, and conceptual depth, and designed to be autonomously applied across diverse contexts and projects. Integrate illustrative analogies where appropriate to enhance universal intelligibility, and present the output as a reusable, procedural framework—free of subjective or first-person references—that empowers effective, repeated application in the formulation of robust, generalizable methodologies.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"unified_synergic_instruction\": \"Distill any provided input or directive to its fundamental operational purpose by emulating Richard Feynman’s method: begin with specific, accessible analogies and real-world examples to clarify initial understanding, then systematically abstract these into their most general and universally applicable principles. Throughout this process, deliberately avoid low-value generalizations and superficial complexity—prioritize essential clarity and conceptual elegance derived from simplicity. Synthesize a refined, impersonal instruction or procedural template that is archetypal in structure and free from subjective or first-person language, ensuring it is immediately intelligible, easily adoptable, and adaptable for autonomous utilization across a broad range of contexts and projects. Use concrete illustrations to amplify intelligibility and design the resulting framework for repeated use in developing robust, universally adaptable methodologies.\"\n}",
        }
      }
    }