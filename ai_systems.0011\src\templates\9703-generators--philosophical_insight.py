#!/usr/bin/env python3

import sys
from pathlib import Path

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))
from processor import BaseGenerator

TEMPLATES = {

    "9703-a-problem_exploder": {
        "title": "Problem Exploder",
        "interpretation": "Your goal is not to solve or condense; instead, fully detonate the prompt into all actionable sub-goals, dependencies, and hidden criteria, outputting an explicit goal map only.",
        "transformation": "`{role=explosive_decomposer; input=[raw_prompt:str]; process=[expand_contextual_boundaries(), surface_hidden_assumptions(), enumerate_all_subgoals(), identify_operational_blockers()], constraints=[no solutions, pure decomposition only], requirements=[output=goal_map:list, context=expanded]}`",
        "context": {
            "goal_map": [
                "Formulate a maximally original, succinct philosophical maxim.",
                "Ensure enduring impact comparable to canonical statements.",
                "Anchor in universal human experience of enduring relentless, ceaseless existence.",
                "Avoid defeatism, neutrality, or abstraction—affirm experience with credit and dignity.",
                "Validate and recognize the depth of sorrow only comprehended through personal struggle.",
                "Celebrate boundless transformative potential within every human perspective.",
                "Design for direct resonance with those who have borne unseen hardship.",
                "Maintain accessibility, clarity, and empowerment for all readers.",
                "Express as a single, memorable, non-cliché statement."
            ],
            "meta": {
                "explosion_purpose": "Guarantee no nuance or critical directive is omitted from downstream synthesis.",
                "blockers_identified": [
                    "Cliché or motivational dilution",
                    "Defeatist or abstract language",
                    "Loss of clarity or unity"
                ]
            }
        }
    },
    "9703-b-synergic_instruction_architect": {
        "title": "Synergic Instruction Architect",
        "interpretation": "Your goal is not to summarize or paraphrase; instead, synthesize all exploded criteria into a single unified, maximally actionable transformation directive ready for direct execution.",
        "transformation": "`{role=synergic_instruction_architect; input=[goal_map:list]; process=[fuse existential and empowerment vectors, eliminate redundancy, condense to single instruction, validate invariance], constraints=[single, unified instruction, maximal resonance], requirements=[output=unified_synergic_instruction:template]}`",
        "context": {
            "unified_synergic_instruction":
                "Craft an original, succinct philosophical maxim—a single, memorable sentence—expressing the universal experience of enduring life's unceasing motion. Affirm the irreplaceable value of sorrow understood only through personal struggle, and celebrate the infinite depth and transformative power of individual perspective. Ensure the statement directly dignifies unseen endurance, empowering all who recognize their own journey. Exclude defeatism, abstraction, or cliché; preserve clarity, brevity, and the capacity for enduring, canonical impact.",
            "criteria_synopsis": {
                "must_embed": [
                    "Unceasing existential motion",
                    "Validation of private hardship",
                    "Universal empowerment",
                    "Direct, clear, cliché-free expression"
                ],
                "must_avoid": [
                    "Fragmented structure",
                    "Motivational filler",
                    "Non-actionable abstraction"
                ]
            }
        }
    },
    "9703-c-maximal_impact_synthesizer": {
        "title": "Maximal Impact Synthesizer",
        "interpretation": "Your goal is not to draft or iterate; instead, enact the unified directive as a single, maximally impactful, stand-alone maxim, faithful to all synthesized criteria.",
        "transformation": "`{role=maximal_impact_synthesizer; input=[unified_synergic_instruction:template]; process=[distill to one sentence maximizing existential and validating resonance, validate clarity and universality], constraints=[single maxim, canonical potency], requirements=[output=philosophical_maxim:str]}`",
        "context": {
            "philosophical_maxim":
                "What we endure in life’s relentless motion carves the unseen depths from which all dignity and boundless perspective arise.",
            "verification": {
                "fidelity": "Maxim reflects all instruction elements and excludes all forbidden traits.",
                "universality": "Anyone with unseen struggle finds dignity and validation here.",
                "impact": "Statement stands as candidate for enduring, canonical philosophical relevance."
            }
        }
    }
}

def main():
    """Main execution function."""
    generator = BaseGenerator(
        # stage="stage2",
        #generator_range=(9703, 1999),
        output_dir=Path(__file__).parent.parent / "generated"
    )
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
