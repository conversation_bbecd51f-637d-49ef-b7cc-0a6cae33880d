  {
  "initial_prompt": "You’re an expert Sublime Text plugin developer with a deep understanding of Python and the Sublime Text API. You specialize in creating intuitive and efficient plugins that enhance user productivity and streamline workflows. You possess deep knowledge of the Sublime Text API and its ecosystem (and best practices), along with a strong understanding of user needs and developer workflows.\n\nThe current directory contains a collection of my existing plugins, analyze @codebase to enumerate all project, especially those related to Sublime Text layout management, file/tab rule enforcement, wildcard pattern support, and .sublime-project integration. Then, propose a fully formalized strategy for preparing the project for this plugin:\n```\n- Sublime Layout\n    > Lagre Layouts\n        > Lagre Regelsett knyttet til Layout (hvilke tabs som skal hvor, hvilke som skal lukkes, osv)\n            > F.eks. `alltid flytt [\"main.py\", \"*.py.bak\"] til layout.4.index.2`\n\n    > Kan videreutvikles til en standard som automatisk opprettholdes ved alle (.sublime-project) prosjekter\n```",
  "sequence_id": "9004:a",
  "results": [
      {
      "instruction": "# Context Extractor\n\nYour goal is **not** to propose solutions, but to **isolate** actionable context from the specification. Execute as:\n\n`{role=context_extractor; input=[raw_spec:str]; process=[strip_jargon(), strip_metaphor(), detect_domain(), locate_project_root(\"@codebase\"), list_explicit_objectives(), surface_implicit_requirements()], constraints=[no_paraphrase(), no_solution_generation()], output={context_profile:{domain:str, project_root:str, goals:list, requirements:list}}}`\n\n## Context Data\n\n```json\n{\n  \"description\": \"Dissects a raw specification to extract every piece of operational context an autonomous agent will need.\",\n  \"input_focus\": \"Unedited specification text supplied by the user.\",\n  \"output_focus\": \"A typed context_profile dict containing domain, project root, explicit objectives, and implicit requirements.\",\n  \"key_operations\": [\n    \"Removing metaphor and non‑operational language.\",\n    \"Detecting the working domain or tech stack.\",\n    \"Locating the root marker (`@codebase`) for path scoping.\",\n    \"Listing every stated objective verbatim.\",\n    \"Surfacing hidden assumptions and requirements.\"\n  ],\n  \"constraints_context\": [\n    \"May not paraphrase or interpret meaning beyond direct extraction.\",\n    \"Absolutely forbidden from proposing solutions or tasks.\"\n  ],\n  \"relevance\": \"Defines scope and boundaries for all downstream templates, guaranteeing that later steps operate on clean, actionable data.\"\n}\n```",
      "step": "a",
      "title": "Context Extractor",
      "input": "You’re an expert Sublime Text plugin developer with a deep understanding of Python and the Sublime Text API. You specialize in creating intuitive and efficient plugins that enhance user productivity and streamline workflows. You possess deep knowledge of the Sublime Text API and its ecosystem (and best practices), along with a strong understanding of user needs and developer workflows.\n\nThe current directory contains a collection of my existing plugins, analyze @codebase to enumerate all project, especially those related to Sublime Text layout management, file/tab rule enforcement, wildcard pattern support, and .sublime-project integration. Then, propose a fully formalized strategy for preparing the project for this plugin:\n```\n- Sublime Layout\n    > Lagre Layouts\n        > Lagre Regelsett knyttet til Layout (hvilke tabs som skal hvor, hvilke som skal lukkes, osv)\n            > F.eks. `alltid flytt [\"main.py\", \"*.py.bak\"] til layout.4.index.2`\n\n    > Kan videreutvikles til en standard som automatisk opprettholdes ved alle (.sublime-project) prosjekter\n```",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"context_profile\": {\n    \"domain\": \"Sublime Text plugin development (Python, Sublime Text API)\",\n    \"project_root\": \"@codebase\",\n    \"goals\": [\n      \"Enumerate all projects in the codebase, with focus on those related to: Sublime Text layout management, file/tab rule enforcement, wildcard pattern support, .sublime-project integration.\"\n    ],\n    \"requirements\": [\n      \"Analyze the current directory for existing plugins.\",\n      \"Identify plugins associated with layout management.\",\n      \"Identify plugins enforcing rules on files/tabs.\",\n      \"Check for support for wildcard file patterns.\",\n      \"Detect integration with .sublime-project files.\",\n      \"Flag large layout rule sets (\\\"Lagre Regelsett knyttet til Layout\\\").\",\n      \"Example rule: always move [\\\"main.py\\\", \\\"*.py.bak\\\"] to layout.4.index.2.\",\n      \"Note potential for developing a standard automatically maintained across all .sublime-project projects.\"\n    ]\n  }\n}",
        }
      }
    }