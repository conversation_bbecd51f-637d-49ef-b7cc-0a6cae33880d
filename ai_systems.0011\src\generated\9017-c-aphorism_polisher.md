[Aphorism Polisher] Your goal is **not** to re‑interpret, but to **finalize** the existential draft into a single, stand‑alone aphorism that meets every stylistic and structural constraint. Execute exactly as: `{role=aphorism_polisher; input=[existential_draft:str]; process=[verify_atomicity(), ensure_existence_of_causal_link(), tighten_diction_for_resonance(), run_redundancy_check(), perform_final_meta_scrub()], constraints=[single_sentence(), no_redundant_words(), no_meta_reference()], requirements=[existential_impact(), linguistic_clarity(), publication_readiness()], output={final_quote:str}}`

Context: {
  "principles": {
    "atomicity": "Exactly one self‑contained sentence.",
    "resonance": "Wording should linger and invite reflection.",
    "purity": "Zero process language, zero qualifiers, zero excess."
  },
  "success_criteria": {
    "impact_test": "Quote provokes reflection on ignorance ➞ unfolding life.",
    "clarity_test": "No ambiguity from trimming or tightening.",
    "compliance_test": "Meets all constraints from phases A & B with no information loss."
  }
}