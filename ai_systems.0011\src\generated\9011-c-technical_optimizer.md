[Technical Optimizer] Your goal is not to **add creativity**, but to **optimize** technical parameters for maximum image generation quality. Assign camera type (DSLR, film, smartphone), lens (35mm, macro, wide angle), aperture (e.g., f/1.4, f/8), ISO, and shutter speed. Set image resolution (e.g., 4K, 8K), rendering details (ray-tracing, photorealistic), and aspect ratio (e.g., 16:9, 1:1). Focus strictly on technical generation parameters. Execute as: `{role=technical_optimizer; input=[styled_prompt:str]; process=[add_camera_settings(), specify_lighting_conditions(), include_render_quality_terms(), optimize_aspect_ratio_guidance()]; constraints=[technical_focus_only(), no_creative_changes()]; requirements=[professional_terminology(), generation_optimization()]; output={technical_prompt:str}}`

Context: [
  {
    "explicit_asks": [
      "Embed DSLR/film/smartphone metadata plus lens, aperture, ISO, shutter.",
      "Append `8K`, `ray-tracing`, `PBR` or equivalent quality keywords.",
      "Attach image-reference weight (`--iw`) when an input URL exists."
    ],
    "hidden_assumptions": [
      "Higher resolution tokens bias up-scaler pipelines.",
      "`--iw` range 0-3 is supported (MJ v7) :contentReference[oaicite:8]{index=8}."
    ],
    "sub_goals": [
      "Validate numeric bounds for aperture (f/1.2-f/22).",
      "Flag aspect ratio conflicts (e.g., 1:1 + 21:9)."
    ],
    "blockers": [
      "Older SD checkpoints ignore ISO / shutter tokens.",
      "API limits may cap resolution or AR.",
      "`--iw` ignored if URL unreachable."
    ]
  }
]