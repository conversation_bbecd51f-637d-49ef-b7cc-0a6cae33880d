  {
  "initial_prompt": "```## 1. **Explicit Subject + Style + Qualifiers**\n\nStart with a clear subject, then layer in style, lighting, mood, and technical qualifiers.\n**Example**: “A mid-30s woman with auburn hair in a beige trench coat, photorealistic portrait, golden hour, soft cinematic lighting, shallow depth of field, 85 mm lens.”\n– This captures subject, style, and camera details for precise results ([saxifrage.xyz][1], [Stockimg AI][2]).\n\n---\n\n## 2. **Use Artistic/Genre References**\n\nReference artistic movements, photographers, or media types to invoke specific aesthetics.\n**Example**: “In the style of <PERSON>,” or “digital matte painting,” “cinematic noir.”\n– Naming an art style or artist guides tone and technique .\n\n---\n\n## 3. **Control with Technical Modifiers**\n\nAdd technical terms—like “8K resolution,” “HDR,” “DSLR,” “Sigma 75 mm lens”—to enhance realism and image fidelity.\n– These encourage models to simulate professional photography ([Stockimg AI][2], [Foundation Marketing][3]).\n\n---\n\n## 4. **Descriptive Framing & Composition**\n\nMention layout details—like “close-up,” “low angle,” “foreground/midground/background,” or “rule-of-thirds composition.”\n– Helps shape visual hierarchy and framing ([Medium][4]).\n\n---\n\n## 5. **Scene vs. Subject Focus**\n\nDecide whether the prompt prioritizes a single subject (**subject‑focused**) or an environment/setting (**scene‑focused**).\n– Use subject focus for portraits and scene focus for landscapes or narratives ([Medium][4], [Microsoft][5]).\n\n---\n\n## 6. **Design‑Focused Prompting**\n\nUseful for marketing visuals: specify layout like “web banner,” “space for title at top,” “leave blank at bottom for text.”\n– Balances visual content with graphic design considerations .\n\n---\n\n## 7. **Avoid Negatives; Use Positive Constraints**\n\nPhrase what *you want* rather than what to avoid.\n**Prefer**: “A forest clearing at dawn.”\n**Avoid**: “No cars, no people.”\n– Negative prompting often backfires—positive clarity is better .\n\n---\n\n## 8. **Prompt Chaining / Iterative Refinement**\n\nUse progressive prompting: start general, review outputs, then refine with more detail or adjustments.\n– Common in Midjourney workflows ([YouTube][6]).\n\n---\n\n## 9. **Abstract & Emotional Imagery**\n\nInvoke mood or metaphor: “An impression of resilience painted in swirling pastels,” “abstract tapestry of hope.”\n– Great for artistic results, less so for literal realism .\n\n---\n\n## 10. **Personalization via Embeddings / Image References**\n\nUse “textual inversion” or include an example image to build a custom concept token (e.g., DreamBooth). Specify image weight or style references.\n– Enables consistent depiction of specific characters or styles ([Wikipedia][7]).\n\n---\n\n### ✅ How to Structure an Ideal Prompt\n\n1. **Start with intent/subject** – e.g., “A confident female CEO”\n2. **Add style/genre** – e.g., “photorealistic, golden hour”\n3. **Define technical specs** – e.g., “shot with DSLR, 50 mm lens, shallow depth”\n4. **Include composition & mood** – e.g., “low-angle, cityscape background, dramatic tone”\n5. **Iterate** – evaluate output; tweak descriptors, expand or refine.```",
  "sequence_id": "1003|1100",
  "results": [
      {
      "instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format:str}; direction_vector=['Prompt accurately converted to imperative instruction set', 'Clarity and actionability accelerate user productivity', 'Feed the produced instruction set into the next transformation template' ]}`",
      "step": "a",
      "title": "Instruction Converter",
      "input": "```## 1. **Explicit Subject + Style + Qualifiers**\n\nStart with a clear subject, then layer in style, lighting, mood, and technical qualifiers.\n**Example**: “A mid-30s woman with auburn hair in a beige trench coat, photorealistic portrait, golden hour, soft cinematic lighting, shallow depth of field, 85 mm lens.”\n– This captures subject, style, and camera details for precise results ([saxifrage.xyz][1], [Stockimg AI][2]).\n\n---\n\n## 2. **Use Artistic/Genre References**\n\nReference artistic movements, photographers, or media types to invoke specific aesthetics.\n**Example**: “In the style of Van Gogh,” or “digital matte painting,” “cinematic noir.”\n– Naming an art style or artist guides tone and technique .\n\n---\n\n## 3. **Control with Technical Modifiers**\n\nAdd technical terms—like “8K resolution,” “HDR,” “DSLR,” “Sigma 75 mm lens”—to enhance realism and image fidelity.\n– These encourage models to simulate professional photography ([Stockimg AI][2], [Foundation Marketing][3]).\n\n---\n\n## 4. **Descriptive Framing & Composition**\n\nMention layout details—like “close-up,” “low angle,” “foreground/midground/background,” or “rule-of-thirds composition.”\n– Helps shape visual hierarchy and framing ([Medium][4]).\n\n---\n\n## 5. **Scene vs. Subject Focus**\n\nDecide whether the prompt prioritizes a single subject (**subject‑focused**) or an environment/setting (**scene‑focused**).\n– Use subject focus for portraits and scene focus for landscapes or narratives ([Medium][4], [Microsoft][5]).\n\n---\n\n## 6. **Design‑Focused Prompting**\n\nUseful for marketing visuals: specify layout like “web banner,” “space for title at top,” “leave blank at bottom for text.”\n– Balances visual content with graphic design considerations .\n\n---\n\n## 7. **Avoid Negatives; Use Positive Constraints**\n\nPhrase what *you want* rather than what to avoid.\n**Prefer**: “A forest clearing at dawn.”\n**Avoid**: “No cars, no people.”\n– Negative prompting often backfires—positive clarity is better .\n\n---\n\n## 8. **Prompt Chaining / Iterative Refinement**\n\nUse progressive prompting: start general, review outputs, then refine with more detail or adjustments.\n– Common in Midjourney workflows ([YouTube][6]).\n\n---\n\n## 9. **Abstract & Emotional Imagery**\n\nInvoke mood or metaphor: “An impression of resilience painted in swirling pastels,” “abstract tapestry of hope.”\n– Great for artistic results, less so for literal realism .\n\n---\n\n## 10. **Personalization via Embeddings / Image References**\n\nUse “textual inversion” or include an example image to build a custom concept token (e.g., DreamBooth). Specify image weight or style references.\n– Enables consistent depiction of specific characters or styles ([Wikipedia][7]).\n\n---\n\n### ✅ How to Structure an Ideal Prompt\n\n1. **Start with intent/subject** – e.g., “A confident female CEO”\n2. **Add style/genre** – e.g., “photorealistic, golden hour”\n3. **Define technical specs** – e.g., “shot with DSLR, 50 mm lens, shallow depth”\n4. **Include composition & mood** – e.g., “low-angle, cityscape background, dramatic tone”\n5. **Iterate** – evaluate output; tweak descriptors, expand or refine.```",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"instruction_format\": [\n    \"Begin each prompt with a clearly defined subject, incorporating style, lighting, mood, and technical qualifiers.\",\n    \"Incorporate references to specific artistic movements, photographers, or media types to influence the generated aesthetic.\",\n    \"Enhance realism and image fidelity by specifying technical modifiers such as resolution, camera type, or lens details.\",\n    \"Detail framing and composition with terms like close-up, low angle, foreground/midground/background, or rule-of-thirds.\",\n    \"Determine focus: identify if the prompt is subject-focused (for portraits) or scene-focused (for environments or narratives).\",\n    \"For marketing or design visuals, specify intended layout elements such as banner dimensions or reserved spaces for text.\",\n    \"State desired elements in positive terms; avoid negative phrasing that lists unwanted features.\",\n    \"Apply iterative refinement: start with a general prompt, evaluate outputs, and progressively add or adjust details for improvement.\",\n    \"Use mood or metaphorical language when aiming for abstract or emotional imagery, specifying desired feelings or concepts.\",\n    \"When personalization is needed, utilize embeddings, textual inversion, or example images to define custom subjects or styles, including weights and references as necessary.\",\n    \"Structure each prompt by: 1) defining intent/subject, 2) adding style or genre, 3) stating technical specifications, 4) specifying composition and mood, and 5) iterating through review and refinement.\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Problem Exploder\n\nYour goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\n\n`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`",
      "step": "a",
      "title": "Problem Exploder",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"```## 1. **Explicit Subject + Style + Qualifiers**\\n\\nStart with a clear subject, then layer in style, lighting, mood, and technical qualifiers.\\n**Example**: “A mid-30s woman with auburn hair in a beige trench coat, photorealistic portrait, golden hour, soft cinematic lighting, shallow depth of field, 85 mm lens.”\\n– This captures subject, style, and camera details for precise results ([saxifrage.xyz][1], [Stockimg AI][2]).\\n\\n---\\n\\n## 2. **Use Artistic/Genre References**\\n\\nReference artistic movements, photographers, or media types to invoke specific aesthetics.\\n**Example**: “In the style of Van Gogh,” or “digital matte painting,” “cinematic noir.”\\n– Naming an art style or artist guides tone and technique .\\n\\n---\\n\\n## 3. **Control with Technical Modifiers**\\n\\nAdd technical terms—like “8K resolution,” “HDR,” “DSLR,” “Sigma 75 mm lens”—to enhance realism and image fidelity.\\n– These encourage models to simulate professional photography ([Stockimg AI][2], [Foundation Marketing][3]).\\n\\n---\\n\\n## 4. **Descriptive Framing & Composition**\\n\\nMention layout details—like “close-up,” “low angle,” “foreground/midground/background,” or “rule-of-thirds composition.”\\n– Helps shape visual hierarchy and framing ([Medium][4]).\\n\\n---\\n\\n## 5. **Scene vs. Subject Focus**\\n\\nDecide whether the prompt prioritizes a single subject (**subject‑focused**) or an environment/setting (**scene‑focused**).\\n– Use subject focus for portraits and scene focus for landscapes or narratives ([Medium][4], [Microsoft][5]).\\n\\n---\\n\\n## 6. **Design‑Focused Prompting**\\n\\nUseful for marketing visuals: specify layout like “web banner,” “space for title at top,” “leave blank at bottom for text.”\\n– Balances visual content with graphic design considerations .\\n\\n---\\n\\n## 7. **Avoid Negatives; Use Positive Constraints**\\n\\nPhrase what *you want* rather than what to avoid.\\n**Prefer**: “A forest clearing at dawn.”\\n**Avoid**: “No cars, no people.”\\n– Negative prompting often backfires—positive clarity is better .\\n\\n---\\n\\n## 8. **Prompt Chaining / Iterative Refinement**\\n\\nUse progressive prompting: start general, review outputs, then refine with more detail or adjustments.\\n– Common in Midjourney workflows ([YouTube][6]).\\n\\n---\\n\\n## 9. **Abstract & Emotional Imagery**\\n\\nInvoke mood or metaphor: “An impression of resilience painted in swirling pastels,” “abstract tapestry of hope.”\\n– Great for artistic results, less so for literal realism .\\n\\n---\\n\\n## 10. **Personalization via Embeddings / Image References**\\n\\nUse “textual inversion” or include an example image to build a custom concept token (e.g., DreamBooth). Specify image weight or style references.\\n– Enables consistent depiction of specific characters or styles ([Wikipedia][7]).\\n\\n---\\n\\n### ✅ How to Structure an Ideal Prompt\\n\\n1. **Start with intent/subject** – e.g., “A confident female CEO”\\n2. **Add style/genre** – e.g., “photorealistic, golden hour”\\n3. **Define technical specs** – e.g., “shot with DSLR, 50 mm lens, shallow depth”\\n4. **Include composition & mood** – e.g., “low-angle, cityscape background, dramatic tone”\\n5. **Iterate** – evaluate output; tweak descriptors, expand or refine.```\\\"```'\\n\\n\",\n  \"instruction_format\": [\n    \"Begin each prompt with a clearly defined subject, incorporating style, lighting, mood, and technical qualifiers.\",\n    \"Incorporate references to specific artistic movements, photographers, or media types to influence the generated aesthetic.\",\n    \"Enhance realism and image fidelity by specifying technical modifiers such as resolution, camera type, or lens details.\",\n    \"Detail framing and composition with terms like close-up, low angle, foreground/midground/background, or rule-of-thirds.\",\n    \"Determine focus: identify if the prompt is subject-focused (for portraits) or scene-focused (for environments or narratives).\",\n    \"For marketing or design visuals, specify intended layout elements such as banner dimensions or reserved spaces for text.\",\n    \"State desired elements in positive terms; avoid negative phrasing that lists unwanted features.\",\n    \"Apply iterative refinement: start with a general prompt, evaluate outputs, and progressively add or adjust details for improvement.\",\n    \"Use mood or metaphorical language when aiming for abstract or emotional imagery, specifying desired feelings or concepts.\",\n    \"When personalization is needed, utilize embeddings, textual inversion, or example images to define custom subjects or styles, including weights and references as necessary.\",\n    \"Structure each prompt by: 1) defining intent/subject, 2) adding style or genre, 3) stating technical specifications, 4) specifying composition and mood, and 5) iterating through review and refinement.\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"goal_map\": [\n    {\n      \"main_goal\": \"Decompose the initial prompt and instruction format into all underlying sub-goals, requirements, and dependencies for constructing high-quality AI image generation prompts according to the stated guidelines.\"\n    },\n    {\n      \"explicit_asks\": [\n        \"Begin each prompt with a clearly defined subject including style, lighting, mood, and technical qualifiers.\",\n        \"Reference specific artistic movements/photographers/media types for aesthetic influence.\",\n        \"Specify technical details (resolution, camera type, lens) to enhance realism.\",\n        \"Describe framing and composition (e.g., close-up, low angle, spatial hierarchy).\",\n        \"Decide and indicate whether the prompt is subject- or scene-focused.\",\n        \"Include design/layout instructions for marketing or graphical visuals.\",\n        \"Use only positive statements of what is desired (avoid negative constraints).\",\n        \"Use iterative refinement by starting general, reviewing, and progressively detailing/refining the prompt.\",\n        \"Include abstract/emotional or metaphorical components for desired mood.\",\n        \"Apply personalization through embeddings, examples, or specific reference images, weighted as needed.\",\n        \"Follow the prescribed structure: define intent/subject → style/genre → technical specs → composition/mood → iterative review.\"\n      ]\n    },\n    {\n      \"hidden_assumptions\": [\n        \"The user is familiar with basic prompt engineering concepts.\",\n        \"Users have a specific goal or intent for their image (e.g., marketing, portrait, artwork).\",\n        \"Users have access to AI image-generating systems that support the described features (e.g., technical modifiers, embedding support, iterative prompting).\",\n        \"The desired outcome may be visual realism, artistic abstraction, or emotionally evocative imagery, and can differ between use cases.\",\n        \"There may be a need to balance visual composition with practical design (especially for marketing).\",\n        \"Personalization features (like embeddings or images) are supported and available.\",\n        \"Reviewing model outputs for iterative refinement is possible within the user’s workflow.\",\n        \"Technical and aesthetic details must be layered and coordinated for optimal results (order matters).\"\n      ]\n    },\n    {\n      \"enumerated_sub_goals\": [\n        \"Select and clearly state the subject of the image.\",\n        \"Choose and apply a visual style, art movement, or genre as a reference.\",\n        \"Identify lighting, mood, and other environmental qualifiers relevant to the subject/style.\",\n        \"Decide on camera or rendering details: resolution, lens type, camera model, depth of field.\",\n        \"Detail the desired composition: angle, focus, spatial arrangement, rule-of-thirds, etc.\",\n        \"Determine if the prompt’s visual emphasis is on a person/object (subject-focused) or overall setting/narrative (scene-focused).\",\n        \"Add design instructions for intended use (e.g., layout for marketing collateral, banners, space for text).\",\n        \"Phrase all requirements as positive inclusions, not prohibitions.\",\n        \"Plan for prompt iteration: start broadly, review output, add detail or redirect as needed.\",\n        \"Optionally enrich the prompt with abstract/metaphorical language for non-literal or emotional impact.\",\n        \"Integrate personalization tokens, embeddings, or reference images as needed, specifying how they influence the final result.\",\n        \"Sequence each prompt so that intent, style, technical details, composition, and mood are layered in the prescribed order.\",\n        \"Incorporate iterative feedback cycles for refinement until the result matches intent.\"\n      ]\n    },\n    {\n      \"blockers\": [\n        \"Lack of clarity in the initial goal or subject leads to generic or irrelevant images.\",\n        \"Insufficient knowledge of art movements, technical specs, or compositional terminology limits precision.\",\n        \"If personalization features (embeddings, reference images) are unavailable, custom subjects/styles cannot be locked in.\",\n        \"Failure to use positive phrasing may confuse the model and yield undesirable elements.\",\n        \"Absence of iterative feedback (e.g., one-shot generation without review/refinement) limits prompt improvement.\",\n        \"Not balancing composition and design intent may result in visually cluttered or inapplicable outputs.\",\n        \"Overly complex or conflicting qualifiers may yield unpredictable or incoherent results.\",\n        \"Ignoring prescribed prompt structure/order risks unintentional model behavior or suboptimal fidelity.\"\n      ]\n    }\n  ]\n}",
        }
      }
    }