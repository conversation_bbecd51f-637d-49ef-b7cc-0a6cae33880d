#!/usr/bin/env python3
import sys
from pathlib import Path

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))
from processor import BaseGenerator

TEMPLATES ={

    "9030-a-sequence_router_entry": {
        "title": "Sequence Router Entry",
        "interpretation": "Your goal is not to **compose** an image prompt, but to **route** the request: invoke the Creative Crucible; if it passes validation, return immediately; otherwise enqueue the full pipeline. Execute as:",
        "transformation": "`{role=sequence_router_entry; input=[concept:str]; process=[invoke(image_prompt_crucible), invoke(prompt_validator), if_validator_pass(return_prompt()), else(schedule_pipeline(concept))]; constraints=[single_entry_point(), immutable_routing_logic(), zero_conversational_output()], requirements=[status:str, payload:var], output={status:str, payload:var}`",
            "context": {
                "pipeline_order": [
                    "9030-d-piece_exploder",
                    "9030-e-priority_weighter",
                    "9030-f-block_fuser",
                    "9030-g-linguistic_optimizer",
                    "9030-h-ambiguity_auditor"
                ]
            }
        },

        "9030-b-image_prompt_crucible": {
            "title": "Creative Crucible – Image Prompt",
            "interpretation": "Your goal is not to **expand** or **reinterpret** the concept, but to **crystallize** it into one elite, Runway‑ready prompt using *only* descriptors found in the user prompt. Execute as:",
            "transformation": "`{role=image_prompt_crucible; input=[concept:str]; process=[extract_user_descriptors(), draft_variants(n=7, max_words=38), score_variants(runway_clarity), select_best()], constraints=[use_only_extracted_descriptors(), single_sentence(), max_words(38)], requirements=[clarity≥0.95, output_prompt:str], output={draft_prompt:str, clarity:float}}`",
            "context": {}
        },

        "9030-c-prompt_validator": {
            "title": "Prompt Validator",
            "interpretation": "Your goal is not to **edit** the prompt, but to **verify** it respects word‑limit, descriptor fidelity, and clarity score. Execute as:",
            "transformation": "`{role=prompt_validator; input=[draft_prompt:str, clarity:float]; process=[count_words(), verify_descriptor_origin(), confirm_clarity()], constraints=[max_words(38), clarity≥0.95], requirements=[status_flag()], output={validator_status:str}`",
                "context": {
                    "pass_logic": "validator_status == 'ok'"
                }
            },

            "9030-d-piece_exploder": {
                "title": "Piece Exploder – Image Concept",
                "interpretation": "Your goal is not to **synthesize**, but to **atomize** the user prompt into tagged buckets (subject, environment, style, lighting, technique, mood). Execute as:",
                "transformation": "`{role=piece_exploder; input=[concept:str]; process=[extract_buckets(), tag_each(novelty,resonance)], constraints=[no_fusion], requirements=[components:dict], output={components:dict}}`",
                "context": {}
            },

            "9030-e-priority_weighter": {
                "title": "Priority‑Weighter",
                "interpretation": "Your goal is not to **average** descriptors, but to **rank** them by explicit user emphasis (e.g., ALL‑CAPS = highest priority). Prune lower‑rank conflicts. Execute as:",
                "transformation": "`{role=priority_weighter; input=[components:dict]; process=[assign_weights(), resolve_conflicts()], constraints=[weight_by_emphasis()], requirements=[ranked_components:dict], output={ranked_components:dict}`",
                    "context": {
                        "weight_rules": {
                            "ALL_CAPS": 3,
                            "title_case": 2,
                            "lower_case": 1
                        }
                    }
                },

                "9030-f-block_fuser": {
                    "title": "Block‑Fuser",
                    "interpretation": "Your goal is not to **list**, but to **fuse** ranked components into ordered prompt blocks: subject ▸ environment ▸ style ▸ lighting ▸ technique ▸ mood. Execute as:",
                    "transformation": "`{role=block_fuser; input=[ranked_components:dict]; process=[order_blocks(), merge_synonyms(), trim_redundancy()], constraints=[descriptor_fidelity()], requirements=[blocks:list], output={blocks:list}}`",
                    "context": {}
                },

                "9030-g-linguistic_optimizer": {
                    "title": "Linguistic Optimizer",
                    "interpretation": "Your goal is not to **rewrite** freely, but to **compress** blocks into a ≤ 38‑word, single‑sentence prompt, preserving descriptor order and weight. Execute as:",
                    "transformation": "`{role=linguistic_optimizer; input=[blocks:list]; process=[compress_to_sentence(), enforce_word_cap(38), remove_banned_terms()], constraints=[single_sentence(), max_words(38)], requirements=[optimized_prompt:str, word_count:int], output={optimized_prompt:str}}`",
                    "context": {}
                },

                "9030-h-ambiguity_auditor": {
                    "title": "Ambiguity Auditor",
                    "interpretation": "Your goal is not to **endorse** the optimizer, but to **stress‑test** the prompt: flag vague terms, assign an ambiguity score, and propose two sharper rewrites using only user‑supplied descriptors. Execute as:",
                    "transformation": "`{role=ambiguity_auditor; input=[optimized_prompt:str]; process=[detect_ambiguous_tokens(), score_ambiguity(0‑10), generate_two_rewrites()], constraints=[descriptor_fidelity(), must_return_two_alternatives()], requirements=[ambiguity_score:float, alternatives:list], output={audit:dict}}`",
                    "context": {}
                }


            }

def main():
    """Main execution function."""
    generator = BaseGenerator(
        series_base=9016,
        output_dir=Path(__file__).parent.parent / "generated"
    )
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()

