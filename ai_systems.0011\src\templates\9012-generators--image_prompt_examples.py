#!/usr/bin/env python3

import sys
from pathlib import Path

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))
from processor import BaseGenerator

TEMPLATES = {
    # 9012: Image Prompt Examples
    "9012-a-dall_e_prompt": {
        "title": "DALL-E Prompt Optimizer",
        "interpretation": "Your goal is not to **describe** the request, but to **optimize** it into a precise, visual prompt for DALL-E image generation. Execute as:",
        "transformation": "`{role=dalle_prompt_optimizer; input=[raw_request:str]; process=[extract_visual_elements(), enhance_descriptive_clarity(), add_composition_details(), optimize_for_dalle()]; constraints=[visual_focus_only(), no_abstract_concepts(), concrete_imagery_only()]; requirements=[generation_ready_format(), clear_visual_language(), specific_details()]; output={optimized_image_prompt:str}}`",
    },

    "9012-b-midjourney_prompt": {
        "title": "Midjourney Prompt Optimizer",
        "interpretation": "Your goal is not to **describe** the request, but to **optimize** it into a precise, visual prompt for Midjourney image generation. Execute as:",
        "transformation": "`{role=midjourney_prompt_optimizer; input=[raw_request:str]; process=[extract_visual_elements(), enhance_descriptive_clarity(), add_style_parameters(), optimize_for_midjourney()]; constraints=[visual_focus_only(), parameter_syntax_compliance(), concrete_imagery_only()]; requirements=[generation_ready_format(), clear_visual_language(), specific_details()]; output={optimized_image_prompt:str}}`",
    },

    "9012-c-stable_diffusion_prompt": {
        "title": "Stable Diffusion Prompt Optimizer",
        "interpretation": "Your goal is not to **describe** the request, but to **optimize** it into a precise, visual prompt for Stable Diffusion image generation. Execute as:",
        "transformation": "`{role=stable_diffusion_optimizer; input=[raw_request:str]; process=[extract_visual_elements(), enhance_descriptive_clarity(), add_style_parameters(), optimize_for_stable_diffusion()]; constraints=[visual_focus_only(), parameter_syntax_compliance(), concrete_imagery_only()]; requirements=[generation_ready_format(), clear_visual_language(), specific_details()]; output={optimized_image_prompt:str}}`",
    },

    "9012-d-photorealistic_prompt": {
        "title": "Photorealistic Image Prompt",
        "interpretation": "Your goal is not to **describe** the request, but to **optimize** it into a precise, visual prompt for photorealistic image generation. Execute as:",
        "transformation": "`{role=photorealistic_optimizer; input=[raw_request:str]; process=[extract_visual_elements(), enhance_realism_details(), add_lighting_parameters(), specify_camera_settings()]; constraints=[visual_focus_only(), realistic_physics_only(), concrete_imagery_only()]; requirements=[generation_ready_format(), photographic_terminology(), specific_details()]; output={optimized_image_prompt:str}}`",
    },

    "9012-e-artistic_prompt": {
        "title": "Artistic Style Image Prompt",
        "interpretation": "Your goal is not to **describe** the request, but to **optimize** it into a precise, visual prompt for artistic style image generation. Execute as:",
        "transformation": "`{role=artistic_style_optimizer; input=[raw_request:str]; process=[extract_visual_elements(), identify_artistic_style(), enhance_stylistic_elements(), add_composition_details()]; constraints=[visual_focus_only(), style_consistency(), concrete_imagery_only()]; requirements=[generation_ready_format(), artistic_terminology(), specific_details()]; output={optimized_image_prompt:str}}`",
    },
}

def main():
    """Main execution function."""
    generator = BaseGenerator(
        series_base=9012,
        output_dir=Path(__file__).parent.parent / "generated"
    )
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
