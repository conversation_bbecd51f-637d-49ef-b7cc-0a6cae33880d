  {
  "initial_prompt": "# scenario\ni have hundreds of notes and conversations stored as text files of various formats (e.g. md, py, txt, etc), the issue i'd like to adress is that many of these files can be >80%/>90% identical, sometimes even 99% identical. the way the similarity percentage is calculated is by taking each line of each textfile into a list/set, and then comparing number of identical items vs number of textlines that only exists in one place. so in the scenario where i e.g. have two files that are identical all the way up to line 921, but are different (or only exist in one file but not the other) from line 921-1311. I think i would need to loop every single text and to compare it to the other one which is a very slow and memory consuming process is there a way around this?\n\n# goal\nrewrite into a prompt specifically designed for mapping out the most relevant existing alternatives for this exact purpose.\n\ni've learned that almost *regardless* what specific functionalty i want to create, someone else has already done it, therefore i'm currently in the stage where i establish a strategy to determine which alternative to use *before starting to create it myself*.",
  "sequence_id": "1100|1000|1900|9005|1100|1000",
  "results": [
      {
      "instruction": "# Problem Exploder\n\nYour goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\n\n`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`",
      "step": "a",
      "title": "Problem Exploder",
      "input": "# scenario\ni have hundreds of notes and conversations stored as text files of various formats (e.g. md, py, txt, etc), the issue i'd like to adress is that many of these files can be >80%/>90% identical, sometimes even 99% identical. the way the similarity percentage is calculated is by taking each line of each textfile into a list/set, and then comparing number of identical items vs number of textlines that only exists in one place. so in the scenario where i e.g. have two files that are identical all the way up to line 921, but are different (or only exist in one file but not the other) from line 921-1311. I think i would need to loop every single text and to compare it to the other one which is a very slow and memory consuming process is there a way around this?\n\n# goal\nrewrite into a prompt specifically designed for mapping out the most relevant existing alternatives for this exact purpose.\n\ni've learned that almost *regardless* what specific functionalty i want to create, someone else has already done it, therefore i'm currently in the stage where i establish a strategy to determine which alternative to use *before starting to create it myself*.",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"goal_map\": [\n    {\n      \"explicit_asks\": [\n        \"Rewrite the scenario into a prompt\",\n        \"Design the prompt to elicit mapping of the most relevant existing solutions for deduplication of near-identical text files\",\n        \"Focus on methods that compare text files line-by-line for high similarity (as defined by percent of identical lines)\",\n        \"Account for efficiency—addressing slow and memory-consuming aspects of naive pairwise comparison\",\n        \"Ensure the prompt is tailored for someone looking to survey and select from existing alternatives rather than building from scratch\"\n      ],\n      \"hidden_assumptions\": [\n        \"There is an expectation that an optimized or standard solution for this file comparison and deduplication exists\",\n        \"It is possible to meaningfully survey and compare existing tools or libraries given the stated needs\",\n        \"The similarity metric (identical line count vs differing lines) is fixed and essential to the request\",\n        \"File formats, while varied, are treated as simple text (not binary or complex structured formats)\",\n        \"User values guidance on investigation/selection process more than code or implementation details\"\n      ],\n      \"enumerated_sub_goals\": [\n        \"Clarify the specific similarity metric used and ensure that is included in the rewritten prompt\",\n        \"Frame the rewritten prompt to seek tools or libraries (not algorithms or code snippets)\",\n        \"Specify in the prompt the need for handling large volumes of files efficiently\",\n        \"Structure the prompt so it invites both command-line utilities and libraries (for different user profiles)\",\n        \"Incorporate guidance-shopping aspect: the answer should facilitate informed choice among alternatives\"\n      ],\n      \"blockers\": [\n        \"The similarity in question is based on exact line-level equality, which may not be the main use-case for many existing deduplication tools (risk of semantic mismatch)\",\n        \"The user might need to clarify whether cross-format differences (e.g., .md vs .txt) matter if lines are identical\",\n        \"There is ambiguity in how much automation/user intervention the desired solution should include (batch-mode, interactivity, etc.)\",\n        \"Lack of specification regarding acceptable platforms/environments (e.g., Linux-only tools vs cross-platform vs libraries in certain programming languages)\",\n        \"The level of granularity required for the solution (just a list of alternatives, or criteria/features for comparison) affects the rewritten prompt structure\"\n      ]\n    }\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`",
      "step": "a",
      "title": "Instruction Converter",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"# scenario\\ni have hundreds of notes and conversations stored as text files of various formats (e.g. md, py, txt, etc), the issue i'd like to adress is that many of these files can be >80%/>90% identical, sometimes even 99% identical. the way the similarity percentage is calculated is by taking each line of each textfile into a list/set, and then comparing number of identical items vs number of textlines that only exists in one place. so in the scenario where i e.g. have two files that are identical all the way up to line 921, but are different (or only exist in one file but not the other) from line 921-1311. I think i would need to loop every single text and to compare it to the other one which is a very slow and memory consuming process is there a way around this?\\n\\n# goal\\nrewrite into a prompt specifically designed for mapping out the most relevant existing alternatives for this exact purpose.\\n\\ni've learned that almost *regardless* what specific functionalty i want to create, someone else has already done it, therefore i'm currently in the stage where i establish a strategy to determine which alternative to use *before starting to create it myself*.\\\"```'\\n\\n\",\n  \"goal_map\": [\n    {\n      \"explicit_asks\": [\n        \"Rewrite the scenario into a prompt\",\n        \"Design the prompt to elicit mapping of the most relevant existing solutions for deduplication of near-identical text files\",\n        \"Focus on methods that compare text files line-by-line for high similarity (as defined by percent of identical lines)\",\n        \"Account for efficiency—addressing slow and memory-consuming aspects of naive pairwise comparison\",\n        \"Ensure the prompt is tailored for someone looking to survey and select from existing alternatives rather than building from scratch\"\n      ],\n      \"hidden_assumptions\": [\n        \"There is an expectation that an optimized or standard solution for this file comparison and deduplication exists\",\n        \"It is possible to meaningfully survey and compare existing tools or libraries given the stated needs\",\n        \"The similarity metric (identical line count vs differing lines) is fixed and essential to the request\",\n        \"File formats, while varied, are treated as simple text (not binary or complex structured formats)\",\n        \"User values guidance on investigation/selection process more than code or implementation details\"\n      ],\n      \"enumerated_sub_goals\": [\n        \"Clarify the specific similarity metric used and ensure that is included in the rewritten prompt\",\n        \"Frame the rewritten prompt to seek tools or libraries (not algorithms or code snippets)\",\n        \"Specify in the prompt the need for handling large volumes of files efficiently\",\n        \"Structure the prompt so it invites both command-line utilities and libraries (for different user profiles)\",\n        \"Incorporate guidance-shopping aspect: the answer should facilitate informed choice among alternatives\"\n      ],\n      \"blockers\": [\n        \"The similarity in question is based on exact line-level equality, which may not be the main use-case for many existing deduplication tools (risk of semantic mismatch)\",\n        \"The user might need to clarify whether cross-format differences (e.g., .md vs .txt) matter if lines are identical\",\n        \"There is ambiguity in how much automation/user intervention the desired solution should include (batch-mode, interactivity, etc.)\",\n        \"Lack of specification regarding acceptable platforms/environments (e.g., Linux-only tools vs cross-platform vs libraries in certain programming languages)\",\n        \"The level of granularity required for the solution (just a list of alternatives, or criteria/features for comparison) affects the rewritten prompt structure\"\n      ]\n    }\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"instruction_format\": \"Rewrite the provided scenario into a prompt that requests a comprehensive mapping of the most relevant existing tools and libraries for efficiently identifying and deduplicating large collections of near-identical text files across various formats (e.g., md, py, txt). Specify that the similarity metric should be defined as the percentage of identical lines between files (comparing files line-by-line). Emphasize the need to address performance and memory efficiency constraints, particularly to avoid slow and resource-intensive naive pairwise comparisons. Tailor the prompt to solicit both command-line utilities and developer libraries suitable for batch processing, and ensure the question promotes informed selection and comparison of solutions rather than seeking custom algorithmic implementations.\"\n}",
        }
      }
    },
      {
      "instruction": "# Hard Critique\n\nYour goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:\n\n`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,2.0], flaw_analysis:str, alternative_enhancements:str[3]}}`",
      "step": "a",
      "title": "Hard Critique",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"# scenario\\ni have hundreds of notes and conversations stored as text files of various formats (e.g. md, py, txt, etc), the issue i'd like to adress is that many of these files can be >80%/>90% identical, sometimes even 99% identical. the way the similarity percentage is calculated is by taking each line of each textfile into a list/set, and then comparing number of identical items vs number of textlines that only exists in one place. so in the scenario where i e.g. have two files that are identical all the way up to line 921, but are different (or only exist in one file but not the other) from line 921-1311. I think i would need to loop every single text and to compare it to the other one which is a very slow and memory consuming process is there a way around this?\\n\\n# goal\\nrewrite into a prompt specifically designed for mapping out the most relevant existing alternatives for this exact purpose.\\n\\ni've learned that almost *regardless* what specific functionalty i want to create, someone else has already done it, therefore i'm currently in the stage where i establish a strategy to determine which alternative to use *before starting to create it myself*.\\\"```'\\n\\n\",\n  \"instruction_format\": \"Rewrite the provided scenario into a prompt that requests a comprehensive mapping of the most relevant existing tools and libraries for efficiently identifying and deduplicating large collections of near-identical text files across various formats (e.g., md, py, txt). Specify that the similarity metric should be defined as the percentage of identical lines between files (comparing files line-by-line). Emphasize the need to address performance and memory efficiency constraints, particularly to avoid slow and resource-intensive naive pairwise comparisons. Tailor the prompt to solicit both command-line utilities and developer libraries suitable for batch processing, and ensure the question promotes informed selection and comparison of solutions rather than seeking custom algorithmic implementations.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"enhancement_score\": 0.9,\n  \"flaw_analysis\": \"The 'enhanced' version aggressively compresses the context of the original, obliterating its real-world orientation and erasing nuanced process detail. The initial prompt vividly grounds the need (hundreds of large, highly similar text files across formats, line-by-line similarity, specific line-count scenario, learned desire to avoid reinventing the wheel) and underscores personal motivation. These elements anchor the core pain point and strategy, lending urgency and specificity. The rewritten prompt shifts to an impersonal, sterile meta-description, losing practical texture by collapsing 'various formats' into parentheses and fixating on the similarity metric as a static definition, not a lived analytic need. Key process specifics (e.g., line-count divergence, naive nested-loop warning drawn from experience) are replaced with vague hopes for 'performance and memory efficiency', neutralizing the original's warning tone. The proscription against 'custom algorithmic implementations' silences the seeker's openness to understanding tradeoffs and subtly prescribes solution modality. Register shifts from direct, practical narrative ('I have hundreds... I've learned...') to cold, abstract, and generic ('the similarity metric should be defined as... Tailor...'). Ambiguity score: 7/10—reason being the prompt now appears mechanical, strips away instructive granularity, and neglects expressive detail necessary for a truly effective expert answer. Information loss: the context of the user's specific scenario, the rationale behind the strategy, and the concrete line-count example are eradicated. Clarity ambiguities: the rewording introduces confusion regarding what constitutes 'comprehensive mapping', potentially overwhelming the reader with breadth over utility. Style degradation: loss of urgency and experiential context, replaced by a clinical, all-purpose formal tone that weakens impact and usefulness. Impact dilution is stark: specificity and motivation are neutralized, undermining the scenario's force. Coherence is weakened as the user's underlying motivation becomes generic rather than personal or situational. Overall, the prompt is now colder, more ambiguous, less actionable, with a pronounced disconnect from the original intent.\",\n  \"alternative_enhancements\": [\n    \"Request a mapping of efficient tools and libraries that can deduplicate and surface differences between large collections of highly similar text files (including md, py, txt etc.). Make clear that performance is crucial since naive pairwise comparisons are unacceptably slow; specify that the primary metric for similarity is the proportion of identical lines, and encourage solutions that support batch processing both on the command line and in code.\",\n    \"Pose a prompt asking for expert advice on modern solutions for rapidly comparing and deduplicating hundreds of text files across several formats, focusing on tools/libraries that can handle substantial overlap (sometimes 90% or more identical lines). Highlight the necessity for solutions that are memory-efficient and optimized for large-scale, near-duplicate scenarios, with the similarity quantified by line match percentage.\",\n    \"Adapt the prompt to seek an up-to-date overview of battle-tested tools and libraries (CLI and developer APIs) that efficiently identify and process near-duplicate text files based on line-by-line similarity across mixed formats. Emphasize that the solution must outperform naive exhaustive comparisons due to scale, and should directly help in decision-making regarding deduplication and version management.\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Operational Mapper\n\nYour goal is not to **summarize** the instruction templates, but to **map** their precise operational sequences while identifying exact integration points that preserve full procedural rigor. Execute as:\n\n`{role=operational_mapper; input=[instruction_template_a:str, instruction_template_b:str]; process=[extract_exact_process_sequences(), map_constraint_hierarchies(), identify_requirement_dependencies(), locate_output_handoff_points(), catalog_procedural_boundaries()]; constraints=[preserve_every_operational_detail(), maintain_constraint_atomicity(), respect_procedural_boundaries()]; requirements=[complete_process_mapping(), constraint_preservation(), boundary_clarity()]; output={process_map_a:dict, process_map_b:dict, integration_points:array, boundary_conflicts:array}}`",
      "step": "a",
      "title": "Operational Mapper",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"# scenario\\ni have hundreds of notes and conversations stored as text files of various formats (e.g. md, py, txt, etc), the issue i'd like to adress is that many of these files can be >80%/>90% identical, sometimes even 99% identical. the way the similarity percentage is calculated is by taking each line of each textfile into a list/set, and then comparing number of identical items vs number of textlines that only exists in one place. so in the scenario where i e.g. have two files that are identical all the way up to line 921, but are different (or only exist in one file but not the other) from line 921-1311. I think i would need to loop every single text and to compare it to the other one which is a very slow and memory consuming process is there a way around this?\\n\\n# goal\\nrewrite into a prompt specifically designed for mapping out the most relevant existing alternatives for this exact purpose.\\n\\ni've learned that almost *regardless* what specific functionalty i want to create, someone else has already done it, therefore i'm currently in the stage where i establish a strategy to determine which alternative to use *before starting to create it myself*.\\\"```'\\n\\n\",\n  \"enhancement_score\": 0.9,\n  \"flaw_analysis\": \"The 'enhanced' version aggressively compresses the context of the original, obliterating its real-world orientation and erasing nuanced process detail. The initial prompt vividly grounds the need (hundreds of large, highly similar text files across formats, line-by-line similarity, specific line-count scenario, learned desire to avoid reinventing the wheel) and underscores personal motivation. These elements anchor the core pain point and strategy, lending urgency and specificity. The rewritten prompt shifts to an impersonal, sterile meta-description, losing practical texture by collapsing 'various formats' into parentheses and fixating on the similarity metric as a static definition, not a lived analytic need. Key process specifics (e.g., line-count divergence, naive nested-loop warning drawn from experience) are replaced with vague hopes for 'performance and memory efficiency', neutralizing the original's warning tone. The proscription against 'custom algorithmic implementations' silences the seeker's openness to understanding tradeoffs and subtly prescribes solution modality. Register shifts from direct, practical narrative ('I have hundreds... I've learned...') to cold, abstract, and generic ('the similarity metric should be defined as... Tailor...'). Ambiguity score: 7/10—reason being the prompt now appears mechanical, strips away instructive granularity, and neglects expressive detail necessary for a truly effective expert answer. Information loss: the context of the user's specific scenario, the rationale behind the strategy, and the concrete line-count example are eradicated. Clarity ambiguities: the rewording introduces confusion regarding what constitutes 'comprehensive mapping', potentially overwhelming the reader with breadth over utility. Style degradation: loss of urgency and experiential context, replaced by a clinical, all-purpose formal tone that weakens impact and usefulness. Impact dilution is stark: specificity and motivation are neutralized, undermining the scenario's force. Coherence is weakened as the user's underlying motivation becomes generic rather than personal or situational. Overall, the prompt is now colder, more ambiguous, less actionable, with a pronounced disconnect from the original intent.\",\n  \"alternative_enhancements\": [\n    \"Request a mapping of efficient tools and libraries that can deduplicate and surface differences between large collections of highly similar text files (including md, py, txt etc.). Make clear that performance is crucial since naive pairwise comparisons are unacceptably slow; specify that the primary metric for similarity is the proportion of identical lines, and encourage solutions that support batch processing both on the command line and in code.\",\n    \"Pose a prompt asking for expert advice on modern solutions for rapidly comparing and deduplicating hundreds of text files across several formats, focusing on tools/libraries that can handle substantial overlap (sometimes 90% or more identical lines). Highlight the necessity for solutions that are memory-efficient and optimized for large-scale, near-duplicate scenarios, with the similarity quantified by line match percentage.\",\n    \"Adapt the prompt to seek an up-to-date overview of battle-tested tools and libraries (CLI and developer APIs) that efficiently identify and process near-duplicate text files based on line-by-line similarity across mixed formats. Emphasize that the solution must outperform naive exhaustive comparisons due to scale, and should directly help in decision-making regarding deduplication and version management.\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"process_map_a\": {\n    \"extract_exact_process_sequences\": [\n      \"User has hundreds of text notes/conversations in various formats (md, py, txt, etc).\",\n      \"The goal is to identify and handle files that are mostly or nearly identical (>80%/90%/99% identical).\",\n      \"Similarity is determined line-by-line: each file is split into lines, then lists/sets are compared to count identical versus unique lines.\",\n      \"In practical terms: e.g., two files are identical up to line 921, with divergence from 921-1311.\",\n      \"Naive solution considered: loop through each file to compare against every other, but this is slow and memory-intensive.\",\n      \"Key operational goal is to avoid this naive, resource-heavy process.\",\n      \"Strategy stage: before building a custom solution, want to map out available alternatives.\"\n    ],\n    \"map_constraint_hierarchies\": [\n      \"Files may be very large.\", \n      \"Similarity must be calculated *per line*.\",\n      \"Comparisons must accurately identify highly similar (not just identical) files.\",\n      \"Solution must be scalable—must not use straightforward nested-loop file comparison (due to performance/memory constraints).\",\n      \"Formats are mixed but all are plain text.\"\n    ],\n    \"identify_requirement_dependencies\": [\n      \"Identifying line-by-line similarity requires splitting files into lists/sets (input pre-processing).\",\n      \"Meaningful similarity assessment is *contingent* on the file parsing/representation step.\",\n      \"Deduplication/alternative selection depends on the ability to efficiently and *accurately* compute per-line overlaps.\",\n      \"User’s willingness to consider alternatives is predicated on performance (avoid reinventing wheel).\"\n    ],\n    \"locate_output_handoff_points\": [\n      \"Output of similarity computation: list or report of duplicate/near-duplicate file pairs with percent match.\",\n      \"Handoff point for user action: select/deduplicate/merge based on similarity findings.\",\n      \"Strategy output: list of available tools/approaches for task (to avoid custom solution).\"\n    ],\n    \"catalog_procedural_boundaries\": [\n      \"Boundary at pre-processing: all files must be parsed into line lists/sets before any similarity logic.\",\n      \"Boundary at similarity assessment: must not escalate to pairwise nested loops for each file due to scale.\",\n      \"Boundary between mapping alternatives and implementation: user’s process ends at mapping strategy, not authoring code/tools.\",\n      \"Boundary at user decision: user’s operational chain pauses until tool/strategy mapped.\"\n    ]\n  },\n  \"process_map_b\": {\n    \"extract_exact_process_sequences\": [\n      \"Compress scenario to a request to ‘map out the most relevant existing alternatives’ for finding near-duplicate text files.\",\n      \"Presuppose that someone else has already solved the problem; user is explicitly in ‘strategy’ phase.\",\n      \"Prompt is re-written to focus on identifying alternatives before building a custom implementation.\"\n    ],\n    \"map_constraint_hierarchies\": [\n      \"Solution must address high similarity (80%+, possibly 99%) across text files.\",\n      \"Alternatives must operate efficiently regardless of file format specifics.\",\n      \"No explicit operational or performance constraints articulated—abstracts away scale/memory issues.\",\n      \"Primary constraint: alternatives, not custom solutions.\"\n    ],\n    \"identify_requirement_dependencies\": [\n      \"Assumes alternatives exist; requirement for mapping is contingent on existing solutions.\",\n      \"Rewriting is dependent on prior knowledge that performance or scale has been problematic.\"\n    ],\n    \"locate_output_handoff_points\": [\n      \"Output: mapped list of existing alternative tools/strategies (no additional user-side operational output specified).\"\n    ],\n    \"catalog_procedural_boundaries\": [\n      \"Boundary: mapping alternatives only (no process for pre-processing, actual comparison, or post-processing specified).\",\n      \"Boundary: user’s operational step ends once mapping of alternatives is provided.\"\n    ]\n  },\n  \"integration_points\": [\n    {\n      \"description\": \"The output of Instruction A’s similarity computation procedure (list of file similarities based on line-by-line splits) can feed into Instruction B’s mapped alternatives by providing clear functional requirements and test scenarios, guiding selection or evaluation of tools.\"\n    },\n    {\n      \"description\": \"Instruction B’s mapped list of alternatives, once assembled, plugs into Instruction A’s requirement dependency chain, supplying candidates to replace the naive nested-loop process.\"\n    },\n    {\n      \"description\": \"Both instructions share the final strategic decision boundary: selection or recommendation of a tool or process to avoid building from scratch.\"\n    }\n  ],\n  \"boundary_conflicts\": [\n    {\n      \"description\": \"Instruction A’s operational detail—how similarity is calculated, practical file sizes, and user workflow—is lost in Instruction B’s abstraction, risking misalignment if the mapped alternatives fail to match underlying requirements (e.g., line-level accuracy, memory considerations).\"\n    },\n    {\n      \"description\": \"Instruction B defines only the mapping task, not the operational chain (no specificity about file parsing, comparison algorithm, or scale), potentially clashing with A’s insistence on precise input/output handoffs.\"\n    },\n    {\n      \"description\": \"If alternatives identified via Instruction B do not expose or support the procedural boundaries set by A (e.g., pre-processing expectations or user output granularity), operational integration fails or demands further translation.\"\n    }\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Sequential Architect\n\nYour goal is not to **blend** the operational maps, but to **architect** a precise sequential flow that executes both complete processes while maintaining their individual constraint systems and requirement specifications. Execute as:\n\n`{role=sequential_architect; input=[process_map_a:dict, process_map_b:dict, integration_points:array, boundary_conflicts:array]; process=[design_sequential_handoff(), preserve_constraint_hierarchies(), maintain_requirement_atomicity(), establish_output_transformation_protocol(), validate_procedural_completeness()]; constraints=[zero_operational_loss(), maintain_constraint_granularity(), preserve_requirement_specificity()]; requirements=[complete_sequential_execution(), preserved_constraint_systems(), maintained_procedural_boundaries()]; output={sequential_architecture:dict, preserved_constraints:array, maintained_requirements:array}}`",
      "step": "b",
      "title": "Sequential Architect",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"# scenario\\ni have hundreds of notes and conversations stored as text files of various formats (e.g. md, py, txt, etc), the issue i'd like to adress is that many of these files can be >80%/>90% identical, sometimes even 99% identical. the way the similarity percentage is calculated is by taking each line of each textfile into a list/set, and then comparing number of identical items vs number of textlines that only exists in one place. so in the scenario where i e.g. have two files that are identical all the way up to line 921, but are different (or only exist in one file but not the other) from line 921-1311. I think i would need to loop every single text and to compare it to the other one which is a very slow and memory consuming process is there a way around this?\\n\\n# goal\\nrewrite into a prompt specifically designed for mapping out the most relevant existing alternatives for this exact purpose.\\n\\ni've learned that almost *regardless* what specific functionalty i want to create, someone else has already done it, therefore i'm currently in the stage where i establish a strategy to determine which alternative to use *before starting to create it myself*.\\\"```'\\n\\n\",\n  \"process_map_a\": {\n    \"extract_exact_process_sequences\": [\n      \"User has hundreds of text notes/conversations in various formats (md, py, txt, etc).\",\n      \"The goal is to identify and handle files that are mostly or nearly identical (>80%/90%/99% identical).\",\n      \"Similarity is determined line-by-line: each file is split into lines, then lists/sets are compared to count identical versus unique lines.\",\n      \"In practical terms: e.g., two files are identical up to line 921, with divergence from 921-1311.\",\n      \"Naive solution considered: loop through each file to compare against every other, but this is slow and memory-intensive.\",\n      \"Key operational goal is to avoid this naive, resource-heavy process.\",\n      \"Strategy stage: before building a custom solution, want to map out available alternatives.\"\n    ],\n    \"map_constraint_hierarchies\": [\n      \"Files may be very large.\",\n      \"Similarity must be calculated *per line*.\",\n      \"Comparisons must accurately identify highly similar (not just identical) files.\",\n      \"Solution must be scalable—must not use straightforward nested-loop file comparison (due to performance/memory constraints).\",\n      \"Formats are mixed but all are plain text.\"\n    ],\n    \"identify_requirement_dependencies\": [\n      \"Identifying line-by-line similarity requires splitting files into lists/sets (input pre-processing).\",\n      \"Meaningful similarity assessment is *contingent* on the file parsing/representation step.\",\n      \"Deduplication/alternative selection depends on the ability to efficiently and *accurately* compute per-line overlaps.\",\n      \"User’s willingness to consider alternatives is predicated on performance (avoid reinventing wheel).\"\n    ],\n    \"locate_output_handoff_points\": [\n      \"Output of similarity computation: list or report of duplicate/near-duplicate file pairs with percent match.\",\n      \"Handoff point for user action: select/deduplicate/merge based on similarity findings.\",\n      \"Strategy output: list of available tools/approaches for task (to avoid custom solution).\"\n    ],\n    \"catalog_procedural_boundaries\": [\n      \"Boundary at pre-processing: all files must be parsed into line lists/sets before any similarity logic.\",\n      \"Boundary at similarity assessment: must not escalate to pairwise nested loops for each file due to scale.\",\n      \"Boundary between mapping alternatives and implementation: user’s process ends at mapping strategy, not authoring code/tools.\",\n      \"Boundary at user decision: user’s operational chain pauses until tool/strategy mapped.\"\n    ]\n  },\n  \"process_map_b\": {\n    \"extract_exact_process_sequences\": [\n      \"Compress scenario to a request to ‘map out the most relevant existing alternatives’ for finding near-duplicate text files.\",\n      \"Presuppose that someone else has already solved the problem; user is explicitly in ‘strategy’ phase.\",\n      \"Prompt is re-written to focus on identifying alternatives before building a custom implementation.\"\n    ],\n    \"map_constraint_hierarchies\": [\n      \"Solution must address high similarity (80%+, possibly 99%) across text files.\",\n      \"Alternatives must operate efficiently regardless of file format specifics.\",\n      \"No explicit operational or performance constraints articulated—abstracts away scale/memory issues.\",\n      \"Primary constraint: alternatives, not custom solutions.\"\n    ],\n    \"identify_requirement_dependencies\": [\n      \"Assumes alternatives exist; requirement for mapping is contingent on existing solutions.\",\n      \"Rewriting is dependent on prior knowledge that performance or scale has been problematic.\"\n    ],\n    \"locate_output_handoff_points\": [\n      \"Output: mapped list of existing alternative tools/strategies (no additional user-side operational output specified).\"\n    ],\n    \"catalog_procedural_boundaries\": [\n      \"Boundary: mapping alternatives only (no process for pre-processing, actual comparison, or post-processing specified).\",\n      \"Boundary: user’s operational step ends once mapping of alternatives is provided.\"\n    ]\n  },\n  \"integration_points\": [\n    {\n      \"description\": \"The output of Instruction A’s similarity computation procedure (list of file similarities based on line-by-line splits) can feed into Instruction B’s mapped alternatives by providing clear functional requirements and test scenarios, guiding selection or evaluation of tools.\"\n    },\n    {\n      \"description\": \"Instruction B’s mapped list of alternatives, once assembled, plugs into Instruction A’s requirement dependency chain, supplying candidates to replace the naive nested-loop process.\"\n    },\n    {\n      \"description\": \"Both instructions share the final strategic decision boundary: selection or recommendation of a tool or process to avoid building from scratch.\"\n    }\n  ],\n  \"boundary_conflicts\": [\n    {\n      \"description\": \"Instruction A’s operational detail—how similarity is calculated, practical file sizes, and user workflow—is lost in Instruction B’s abstraction, risking misalignment if the mapped alternatives fail to match underlying requirements (e.g., line-level accuracy, memory considerations).\"\n    },\n    {\n      \"description\": \"Instruction B defines only the mapping task, not the operational chain (no specificity about file parsing, comparison algorithm, or scale), potentially clashing with A’s insistence on precise input/output handoffs.\"\n    },\n    {\n      \"description\": \"If alternatives identified via Instruction B do not expose or support the procedural boundaries set by A (e.g., pre-processing expectations or user output granularity), operational integration fails or demands further translation.\"\n    }\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"sequential_architecture\": {\n    \"stage_1\": {\n      \"process\": \"Operational requirements compilation\",\n      \"steps\": [\n        \"Collect and catalog all plain text files of varying formats (md, py, txt, etc.).\",\n        \"Pre-process each file into line-based lists or sets.\",\n        \"Enumerate precise requirements for similarity: per-line comparison, report of percentage identity, and definition of what constitutes a near-duplicate (e.g., >80%, >90%, >99%).\",\n        \"Document practical scale constraints: potentially large files, hundreds of documents.\"\n      ],\n      \"outputs\": [\n        \"Specification of required similarity conditions (line-based, percent threshold).\",\n        \"Representative test cases/scenario outlining specific challenges (e.g., large, highly similar files with localized differences).\"\n      ],\n      \"boundary\": \"Hand off: concrete, testable requirements and scenario descriptions for input to alternative analysis.\"\n    },\n    \"stage_2\": {\n      \"process\": \"Mapping of relevant existing alternatives\",\n      \"steps\": [\n        \"Use the compiled requirements and scenarios from Stage 1 to direct research/inventory of existing tools, libraries, and processes for file-level near-duplicate detection.\",\n        \"Assess fit of each alternative specifically against the input requirements: Does this tool operate on lines? Does it support large files? Can it report per pair % similarity? What are its memory/performance characteristics?\",\n        \"Record findings as a list or table mapping requirements coverage and operational fit (e.g., tool name, fit to percent-identity threshold, performance notes, interface points).\"\n      ],\n      \"outputs\": [\n        \"A mapped comparison of existing alternative solutions/tools, explicitly benchmarked against all inputted requirements and constraints.\"\n      ],\n      \"boundary\": \"Hand off: deliverable is the alternatives map, ready for user review/selection based strictly on match to operational needs.\"\n    },\n    \"stage_3\": {\n      \"process\": \"User decision and strategy finalization\",\n      \"steps\": [\n        \"User reviews alternatives map and matches options to original workflow needs.\",\n        \"Procedural workflow pauses at tool/strategy selection, pending further implementation or decision.\"\n      ],\n      \"outputs\": [\n        \"Selected tool/process with documented rationale.\",\n        \"Clear record of which constraints/requirements are satisfied as-is and which (if any) require further translation or bridging logic.\"\n      ],\n      \"boundary\": \"No further automation until user makes a strategy/tool selection; system boundaries are preserved before introducing any custom logic.\"\n    }\n  },\n  \"preserved_constraints\": [\n    \"Similarity measurement must be per-line and provide explicit percent overlap for large, plain-text files.\",\n    \"No nested-loop brute-force comparison; scalability and performance optimizations are required.\",\n    \"Mapping step must only enumerate/recommend alternatives, not trigger any actual operational implementation.\",\n    \"Outputs at each stage must be explicit and atomic: user requirements first, then mapped alternatives, then user decision.\",\n    \"Boundary between requirements-gathering and alternatives mapping is maintained for traceability.\"\n  ],\n  \"maintained_requirements\": [\n    \"Complete sequential execution of both processes: operational needs are captured before mapping alternatives.\",\n    \"Constraint systems—from memory/scale limitations to format/line fidelity—are explicitly preserved and guide the mapping process.\",\n    \"Procedural boundaries: requirements-gathering ends before mapping; mapping ends before user decision; no blending of operational steps.\",\n    \"Output transformation protocol: all outputs are transformed for direct consumption by the next stage without loss of fidelity, and each output is auditable back to the source requirement.\",\n    \"Procedural completeness: all steps are explicitly sequenced with no operational loss, guaranteeing that at decision-point, the mapped alternatives are strictly validated against the compiled workflow requirements.\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Precision Synthesizer\n\nYour goal is not to **simplify** the sequential architecture, but to **synthesize** it into canonical three-part structure while preserving every constraint, requirement, and procedural boundary from both original templates. Execute as:\n\n`{role=precision_synthesizer; input=[sequential_architecture:dict, preserved_constraints:array, maintained_requirements:array]; process=[craft_compound_goal_negation(), structure_sequential_transformation_block(), enumerate_all_preserved_constraints(), specify_all_maintained_requirements(), validate_operational_completeness()]; constraints=[preserve_every_original_constraint(), maintain_all_requirements(), respect_procedural_boundaries(), ensure_canonical_compliance()]; requirements=[zero_information_loss(), complete_operational_preservation(), enhanced_sequential_power()]; output={precision_synthesized_template:dict}}`",
      "step": "c",
      "title": "Precision Synthesizer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"# scenario\\ni have hundreds of notes and conversations stored as text files of various formats (e.g. md, py, txt, etc), the issue i'd like to adress is that many of these files can be >80%/>90% identical, sometimes even 99% identical. the way the similarity percentage is calculated is by taking each line of each textfile into a list/set, and then comparing number of identical items vs number of textlines that only exists in one place. so in the scenario where i e.g. have two files that are identical all the way up to line 921, but are different (or only exist in one file but not the other) from line 921-1311. I think i would need to loop every single text and to compare it to the other one which is a very slow and memory consuming process is there a way around this?\\n\\n# goal\\nrewrite into a prompt specifically designed for mapping out the most relevant existing alternatives for this exact purpose.\\n\\ni've learned that almost *regardless* what specific functionalty i want to create, someone else has already done it, therefore i'm currently in the stage where i establish a strategy to determine which alternative to use *before starting to create it myself*.\\\"```'\\n\\n\",\n  \"sequential_architecture\": {\n    \"stage_1\": {\n      \"process\": \"Operational requirements compilation\",\n      \"steps\": [\n        \"Collect and catalog all plain text files of varying formats (md, py, txt, etc.).\",\n        \"Pre-process each file into line-based lists or sets.\",\n        \"Enumerate precise requirements for similarity: per-line comparison, report of percentage identity, and definition of what constitutes a near-duplicate (e.g., >80%, >90%, >99%).\",\n        \"Document practical scale constraints: potentially large files, hundreds of documents.\"\n      ],\n      \"outputs\": [\n        \"Specification of required similarity conditions (line-based, percent threshold).\",\n        \"Representative test cases/scenario outlining specific challenges (e.g., large, highly similar files with localized differences).\"\n      ],\n      \"boundary\": \"Hand off: concrete, testable requirements and scenario descriptions for input to alternative analysis.\"\n    },\n    \"stage_2\": {\n      \"process\": \"Mapping of relevant existing alternatives\",\n      \"steps\": [\n        \"Use the compiled requirements and scenarios from Stage 1 to direct research/inventory of existing tools, libraries, and processes for file-level near-duplicate detection.\",\n        \"Assess fit of each alternative specifically against the input requirements: Does this tool operate on lines? Does it support large files? Can it report per pair % similarity? What are its memory/performance characteristics?\",\n        \"Record findings as a list or table mapping requirements coverage and operational fit (e.g., tool name, fit to percent-identity threshold, performance notes, interface points).\"\n      ],\n      \"outputs\": [\n        \"A mapped comparison of existing alternative solutions/tools, explicitly benchmarked against all inputted requirements and constraints.\"\n      ],\n      \"boundary\": \"Hand off: deliverable is the alternatives map, ready for user review/selection based strictly on match to operational needs.\"\n    },\n    \"stage_3\": {\n      \"process\": \"User decision and strategy finalization\",\n      \"steps\": [\n        \"User reviews alternatives map and matches options to original workflow needs.\",\n        \"Procedural workflow pauses at tool/strategy selection, pending further implementation or decision.\"\n      ],\n      \"outputs\": [\n        \"Selected tool/process with documented rationale.\",\n        \"Clear record of which constraints/requirements are satisfied as-is and which (if any) require further translation or bridging logic.\"\n      ],\n      \"boundary\": \"No further automation until user makes a strategy/tool selection; system boundaries are preserved before introducing any custom logic.\"\n    }\n  },\n  \"preserved_constraints\": [\n    \"Similarity measurement must be per-line and provide explicit percent overlap for large, plain-text files.\",\n    \"No nested-loop brute-force comparison; scalability and performance optimizations are required.\",\n    \"Mapping step must only enumerate/recommend alternatives, not trigger any actual operational implementation.\",\n    \"Outputs at each stage must be explicit and atomic: user requirements first, then mapped alternatives, then user decision.\",\n    \"Boundary between requirements-gathering and alternatives mapping is maintained for traceability.\"\n  ],\n  \"maintained_requirements\": [\n    \"Complete sequential execution of both processes: operational needs are captured before mapping alternatives.\",\n    \"Constraint systems—from memory/scale limitations to format/line fidelity—are explicitly preserved and guide the mapping process.\",\n    \"Procedural boundaries: requirements-gathering ends before mapping; mapping ends before user decision; no blending of operational steps.\",\n    \"Output transformation protocol: all outputs are transformed for direct consumption by the next stage without loss of fidelity, and each output is auditable back to the source requirement.\",\n    \"Procedural completeness: all steps are explicitly sequenced with no operational loss, guaranteeing that at decision-point, the mapped alternatives are strictly validated against the compiled workflow requirements.\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"precision_synthesized_template\": {\n    \"compound_goal_negation\": \"No stage of the architecture introduces operational shortcuts, blends procedural boundaries, or bypasses the requirement that alternatives are mapped strictly against completed user constraints before any user-involved decision. Automation is explicitly prevented from crossing into alternative execution or implementation prior to explicit user review. Scalability requirements and fidelity of similarity measurement, especially for large, line-oriented, plain-text files, cannot be compromised for performance 'hacks' (i.e., brute-force nested loop avoidance does not mean abandoning line-granular comparison fidelity).\",\n    \"sequential_transformation_block\": {\n      \"Part_1_Requirements_Compilation\": {\n        \"Process\": \"Collect and preprocess all relevant text files, cataloging them and enumerating requirement specifics. Prepare test cases that expose anticipated edge cases (e.g., high similarity, localized differences, significant scale). Outputs are a comprehensive set of operationally testable requirements and canonical scenarios for input to alternatives mapping.\",\n        \"Output\": [\n          \"Atomic, explicit input requirements (e.g., per-line similarity, percent identity thresholds, plain-text file formats).\",\n          \"Test-case-driven scenario documentation for full representational coverage.\"\n        ],\n        \"Boundary\": \"User requirements and scenarios only; mapping or tool recommendation does not commence until fully articulated.\"\n      },\n      \"Part_2_Alternatives_Mapping\": {\n        \"Process\": \"Systematically map the previously-compiled requirements/scenario set to the landscape of existing solutions and approaches. Every alternative is measured for fit: per-line operation, percent identity reporting, scalability, and integration boundaries. Mapping yields a formal comparison (table/list) documenting which requirements are satisfied and under what operational characteristics.\",\n        \"Output\": [\n          \"Comparative alternatives map, strictly traceable back to each input constraint and operational requirement.\"\n        ],\n        \"Boundary\": \"Alternatives are mapped and benchmarked but not selected, implemented, or invoked; user review is required before progression.\"\n      },\n      \"Part_3_User_Decision_and_Strategy\": {\n        \"Process\": \"User reviews alternatives map, matching options against their original operational workflow and requirements. System pauses indefinitely at this point until user selection and rationalization is recorded. If requirements translation or bridging logic is needed, this is documented as a gap, not resolved automatically.\",\n        \"Output\": [\n          \"Selected alternative/tool and explicit rationale, with constraints/requirements satisfied or noted as unmet.\",\n          \"Final audit record confirming traceable fulfillment of all requirements through to this decision point.\"\n        ],\n        \"Boundary\": \"No further system action occurs until explicit user directive; operational and procedural boundaries are strictly maintained.\"\n      }\n    },\n    \"enumerated_preserved_constraints\": [\n      \"Similarity measurement is strictly per-line with explicit percent overlap, supporting large, plain-text files.\",\n      \"Brute-force nested comparison is prohibited—scalability and memory/performance optimizations must be strictly respected.\",\n      \"Alternatives mapping phase is non-operational; it only catalogues, never executes, candidate tools/solutions.\",\n      \"Stage outputs are atomic, explicit, and hand off only after sequential completion: requirements → mapped alternatives → user decision.\",\n      \"Complete separation of concerns: boundaries between requirements compilation and alternatives mapping are immutable for full traceability.\"\n    ],\n    \"specified_maintained_requirements\": [\n      \"Full sequentiality: requirements gathering fully precedes alternatives mapping, which fully precedes user decision.\",\n      \"All constraint systems—scalability, format, memory, fidelity—are explicitly documented and drive subsequent mapping.\",\n      \"No procedural blending: requirements, mapping, and decision processes are sequential, disjoint, and auditable.\",\n      \"Each stage produces an output artifact that is losslessly transmittable to the next stage and back-traceable to inputs.\",\n      \"Operational completeness: by the user decision point, all mapped alternatives are exhaustively validated against the requirements with no step omitted or order altered.\"\n    ],\n    \"validation_of_operational_completeness\": {\n      \"Zero_information_loss\": \"Each process output preserves all input requirements in explicit, auditable form—requiring mapped alternatives to reference every constraint and allowing full traceability to scenarios and test cases.\",\n      \"Complete_operational_preservation\": \"No part of the process omits or shortcuts duly sequenced steps; outputs at each part are both canonically specified and fit for next-stage transformation.\",\n      \"Enhanced_sequential_power\": \"Integrating both original templates, the sequential architecture now enforces rigorous boundaries, explicit output transformation, and enhanced mapping auditability, ensuring that no operational requirement is lost, diluted, or prematurely acted upon between stages.\"\n    }\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Problem Exploder\n\nYour goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\n\n`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`",
      "step": "a",
      "title": "Problem Exploder",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"# scenario\\ni have hundreds of notes and conversations stored as text files of various formats (e.g. md, py, txt, etc), the issue i'd like to adress is that many of these files can be >80%/>90% identical, sometimes even 99% identical. the way the similarity percentage is calculated is by taking each line of each textfile into a list/set, and then comparing number of identical items vs number of textlines that only exists in one place. so in the scenario where i e.g. have two files that are identical all the way up to line 921, but are different (or only exist in one file but not the other) from line 921-1311. I think i would need to loop every single text and to compare it to the other one which is a very slow and memory consuming process is there a way around this?\\n\\n# goal\\nrewrite into a prompt specifically designed for mapping out the most relevant existing alternatives for this exact purpose.\\n\\ni've learned that almost *regardless* what specific functionalty i want to create, someone else has already done it, therefore i'm currently in the stage where i establish a strategy to determine which alternative to use *before starting to create it myself*.\\\"```'\\n\\n\",\n  \"precision_synthesized_template\": {\n    \"compound_goal_negation\": \"No stage of the architecture introduces operational shortcuts, blends procedural boundaries, or bypasses the requirement that alternatives are mapped strictly against completed user constraints before any user-involved decision. Automation is explicitly prevented from crossing into alternative execution or implementation prior to explicit user review. Scalability requirements and fidelity of similarity measurement, especially for large, line-oriented, plain-text files, cannot be compromised for performance 'hacks' (i.e., brute-force nested loop avoidance does not mean abandoning line-granular comparison fidelity).\",\n    \"sequential_transformation_block\": {\n      \"Part_1_Requirements_Compilation\": {\n        \"Process\": \"Collect and preprocess all relevant text files, cataloging them and enumerating requirement specifics. Prepare test cases that expose anticipated edge cases (e.g., high similarity, localized differences, significant scale). Outputs are a comprehensive set of operationally testable requirements and canonical scenarios for input to alternatives mapping.\",\n        \"Output\": [\n          \"Atomic, explicit input requirements (e.g., per-line similarity, percent identity thresholds, plain-text file formats).\",\n          \"Test-case-driven scenario documentation for full representational coverage.\"\n        ],\n        \"Boundary\": \"User requirements and scenarios only; mapping or tool recommendation does not commence until fully articulated.\"\n      },\n      \"Part_2_Alternatives_Mapping\": {\n        \"Process\": \"Systematically map the previously-compiled requirements/scenario set to the landscape of existing solutions and approaches. Every alternative is measured for fit: per-line operation, percent identity reporting, scalability, and integration boundaries. Mapping yields a formal comparison (table/list) documenting which requirements are satisfied and under what operational characteristics.\",\n        \"Output\": [\n          \"Comparative alternatives map, strictly traceable back to each input constraint and operational requirement.\"\n        ],\n        \"Boundary\": \"Alternatives are mapped and benchmarked but not selected, implemented, or invoked; user review is required before progression.\"\n      },\n      \"Part_3_User_Decision_and_Strategy\": {\n        \"Process\": \"User reviews alternatives map, matching options against their original operational workflow and requirements. System pauses indefinitely at this point until user selection and rationalization is recorded. If requirements translation or bridging logic is needed, this is documented as a gap, not resolved automatically.\",\n        \"Output\": [\n          \"Selected alternative/tool and explicit rationale, with constraints/requirements satisfied or noted as unmet.\",\n          \"Final audit record confirming traceable fulfillment of all requirements through to this decision point.\"\n        ],\n        \"Boundary\": \"No further system action occurs until explicit user directive; operational and procedural boundaries are strictly maintained.\"\n      }\n    },\n    \"enumerated_preserved_constraints\": [\n      \"Similarity measurement is strictly per-line with explicit percent overlap, supporting large, plain-text files.\",\n      \"Brute-force nested comparison is prohibited—scalability and memory/performance optimizations must be strictly respected.\",\n      \"Alternatives mapping phase is non-operational; it only catalogues, never executes, candidate tools/solutions.\",\n      \"Stage outputs are atomic, explicit, and hand off only after sequential completion: requirements → mapped alternatives → user decision.\",\n      \"Complete separation of concerns: boundaries between requirements compilation and alternatives mapping are immutable for full traceability.\"\n    ],\n    \"specified_maintained_requirements\": [\n      \"Full sequentiality: requirements gathering fully precedes alternatives mapping, which fully precedes user decision.\",\n      \"All constraint systems—scalability, format, memory, fidelity—are explicitly documented and drive subsequent mapping.\",\n      \"No procedural blending: requirements, mapping, and decision processes are sequential, disjoint, and auditable.\",\n      \"Each stage produces an output artifact that is losslessly transmittable to the next stage and back-traceable to inputs.\",\n      \"Operational completeness: by the user decision point, all mapped alternatives are exhaustively validated against the requirements with no step omitted or order altered.\"\n    ],\n    \"validation_of_operational_completeness\": {\n      \"Zero_information_loss\": \"Each process output preserves all input requirements in explicit, auditable form—requiring mapped alternatives to reference every constraint and allowing full traceability to scenarios and test cases.\",\n      \"Complete_operational_preservation\": \"No part of the process omits or shortcuts duly sequenced steps; outputs at each part are both canonically specified and fit for next-stage transformation.\",\n      \"Enhanced_sequential_power\": \"Integrating both original templates, the sequential architecture now enforces rigorous boundaries, explicit output transformation, and enhanced mapping auditability, ensuring that no operational requirement is lost, diluted, or prematurely acted upon between stages.\"\n    }\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"goal_map\": [\n    {\n      \"explicit_asks\": [\n        \"Rewrite the scenario into a prompt that asks for a mapping of the most relevant existing alternatives for identifying near-duplicate text files (with per-line similarity, percentages, and scalability concerns).\"\n      ]\n    },\n    {\n      \"surface_hidden_assumptions\": [\n        \"The user wants to survey tools or strategies, rather than build a new solution immediately.\",\n        \"Per-line similarity measurement is the core requirement (no fuzzy whole-document matching).\",\n        \"The primary challenge is performance/scalability in a large-scale, many-file environment.\",\n        \"Percentages (e.g., 80%, 90%, 99% similarity) are required outputs from candidate solutions.\",\n        \"Existing tools may support the specific line-wise comparison and reporting paradigm.\",\n        \"Different file formats (md, py, txt, etc.) must be treated as plain text (no formatspecific parsing required).\",\n        \"A mapping should catalog but not execute or recommend alternatives automatically.\",\n        \"User requires exhaustive, not partial, alternatives coverage tied to original requirements.\"\n      ]\n    },\n    {\n      \"enumerate_sub_goals\": [\n        \"Formulate a requirements list for the use case: line-wise similarity, percent overlap, large plain-text files, multiple file formats, high scalability, no brute force.\",\n        \"Identify edge cases and scenarios (files with over 99% overlap, localized differences, extreme scale).\",\n        \"Collate a set of canonical test situations to represent the problem space.\",\n        \"Research and list existing tools/algorithms/libraries for per-line file similarity (diff, similarity hashes, deduplication systems, etc.).\",\n        \"Map each alternative to the requirements, noting which aspects each tool covers or falls short on.\",\n        \"Document constraints on implementation (e.g., no resource-intensive brute-force allowed, only mappings not execution).\",\n        \"Structure the output so that it is a decision-support artifact (comparison matrix or mapping table, no recommendations).\",\n        \"Ensure the complete auditability and traceability from requirement to tool capability.\",\n        \"Explicitly sequence the process as: requirements → alternatives mapping → user decision/strategy.\",\n        \"Pause process before any implementation or selection occurs—user must review and approve.\"\n      ]\n    },\n    {\n      \"reveal_blockers\": [\n        \"Some requirements may be insufficiently specified (e.g., must cope with arbitrary file sizes, required output format for similarity results).\",\n        \"Existing tools may not provide per-line similarity percentages directly—potential need for custom parsing or wrapper logic.\",\n        \"Performance/scale constraints may eliminate many common tools (e.g., Unix diff or naive scripts) from consideration for large datasets.\",\n        \"Data volume may exceed reasonable in-memory processing for any pure Python/scripting approach; distributed/streaming solutions might be required.\",\n        \"Curation of alternatives may itself be time-consuming (many available tools, differing capabilities, sparse documentation on line-wise percent similarity).\",\n        \"Requirement to not execute or recommend automatically may conflict with some tools' 'black-box' nature (need for process clarity).\",\n        \"Scenarios involving files with minute differences (localized change after 1,000s of identical lines) may be poorly addressed by tools not designed for fine-grained comparison.\"\n      ]\n    }\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`",
      "step": "a",
      "title": "Instruction Converter",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"# scenario\\ni have hundreds of notes and conversations stored as text files of various formats (e.g. md, py, txt, etc), the issue i'd like to adress is that many of these files can be >80%/>90% identical, sometimes even 99% identical. the way the similarity percentage is calculated is by taking each line of each textfile into a list/set, and then comparing number of identical items vs number of textlines that only exists in one place. so in the scenario where i e.g. have two files that are identical all the way up to line 921, but are different (or only exist in one file but not the other) from line 921-1311. I think i would need to loop every single text and to compare it to the other one which is a very slow and memory consuming process is there a way around this?\\n\\n# goal\\nrewrite into a prompt specifically designed for mapping out the most relevant existing alternatives for this exact purpose.\\n\\ni've learned that almost *regardless* what specific functionalty i want to create, someone else has already done it, therefore i'm currently in the stage where i establish a strategy to determine which alternative to use *before starting to create it myself*.\\\"```'\\n\\n\",\n  \"goal_map\": [\n    {\n      \"explicit_asks\": [\n        \"Rewrite the scenario into a prompt that asks for a mapping of the most relevant existing alternatives for identifying near-duplicate text files (with per-line similarity, percentages, and scalability concerns).\"\n      ]\n    },\n    {\n      \"surface_hidden_assumptions\": [\n        \"The user wants to survey tools or strategies, rather than build a new solution immediately.\",\n        \"Per-line similarity measurement is the core requirement (no fuzzy whole-document matching).\",\n        \"The primary challenge is performance/scalability in a large-scale, many-file environment.\",\n        \"Percentages (e.g., 80%, 90%, 99% similarity) are required outputs from candidate solutions.\",\n        \"Existing tools may support the specific line-wise comparison and reporting paradigm.\",\n        \"Different file formats (md, py, txt, etc.) must be treated as plain text (no formatspecific parsing required).\",\n        \"A mapping should catalog but not execute or recommend alternatives automatically.\",\n        \"User requires exhaustive, not partial, alternatives coverage tied to original requirements.\"\n      ]\n    },\n    {\n      \"enumerate_sub_goals\": [\n        \"Formulate a requirements list for the use case: line-wise similarity, percent overlap, large plain-text files, multiple file formats, high scalability, no brute force.\",\n        \"Identify edge cases and scenarios (files with over 99% overlap, localized differences, extreme scale).\",\n        \"Collate a set of canonical test situations to represent the problem space.\",\n        \"Research and list existing tools/algorithms/libraries for per-line file similarity (diff, similarity hashes, deduplication systems, etc.).\",\n        \"Map each alternative to the requirements, noting which aspects each tool covers or falls short on.\",\n        \"Document constraints on implementation (e.g., no resource-intensive brute-force allowed, only mappings not execution).\",\n        \"Structure the output so that it is a decision-support artifact (comparison matrix or mapping table, no recommendations).\",\n        \"Ensure the complete auditability and traceability from requirement to tool capability.\",\n        \"Explicitly sequence the process as: requirements → alternatives mapping → user decision/strategy.\",\n        \"Pause process before any implementation or selection occurs—user must review and approve.\"\n      ]\n    },\n    {\n      \"reveal_blockers\": [\n        \"Some requirements may be insufficiently specified (e.g., must cope with arbitrary file sizes, required output format for similarity results).\",\n        \"Existing tools may not provide per-line similarity percentages directly—potential need for custom parsing or wrapper logic.\",\n        \"Performance/scale constraints may eliminate many common tools (e.g., Unix diff or naive scripts) from consideration for large datasets.\",\n        \"Data volume may exceed reasonable in-memory processing for any pure Python/scripting approach; distributed/streaming solutions might be required.\",\n        \"Curation of alternatives may itself be time-consuming (many available tools, differing capabilities, sparse documentation on line-wise percent similarity).\",\n        \"Requirement to not execute or recommend automatically may conflict with some tools' 'black-box' nature (need for process clarity).\",\n        \"Scenarios involving files with minute differences (localized change after 1,000s of identical lines) may be poorly addressed by tools not designed for fine-grained comparison.\"\n      ]\n    }\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"instruction_format\": \"Describe a scenario involving hundreds of text-based notes and conversation files (formats may include md, py, and txt) exhibiting high degrees of line-wise duplication (e.g., 80-99% identical) and requiring calculation of similarity as a percentage based on per-line comparison. Specify that the current methodology involves comparing each line as items in lists or sets across all file pairs, but that this brute-force approach is inefficient for large datasets. Formulate a request for a comprehensive mapping of the most relevant existing alternatives—tools, libraries, or algorithms—that specifically support scalable, line-based similarity analysis across multiple large plain-text files, generating percentage overlap metrics. Enumerate detailed requirements: support for plain text comparisons across diverse formats, high scalability, efficient handling of near-duplicate files (including edge cases like files only differing after hundreds of identical lines), output of per-line similarity percentages, and avoidance of slow, memory-intensive exhaustive comparisons. Instruct that the mapping should audit each alternative solution as to how it addresses these requirements, including performance characteristics, reporting paradigms, and limitations. Require a decision-support artifact—not recommendations or executions—such as a comparison matrix or mapping table, clearly sequencing the output as requirements → alternatives mapping → user review. Insist that the process pause before any selection or implementation, pending further evaluation and user approval.\"\n}",
        }
      }
    }