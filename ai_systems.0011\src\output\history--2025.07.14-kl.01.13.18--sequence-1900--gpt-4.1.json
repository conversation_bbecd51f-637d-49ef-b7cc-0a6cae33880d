  {
  "initial_prompt": "{\n  \"initial_prompt\": \"Maybe it could be improved by understanding the instruction patterns more simplicity:\\n\\n  {\\n  \\\"initial_prompt\\\": \\\"# 1100: Explosive Decomposition of Problem Statements\\n    \\\"1100-a-problem_exploder\\\": {\\n        \\\"title\\\": \\\"Problem Exploder\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\n\\n\\n    # 1000: Instruction Converter/Prompt Enhancer\\n    \\\"1000-a-instruction_converter\\\": {\\n        \\\"title\\\": \\\"Instruction Converter\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\\\",\\n        \\\"transformation\\\": \\\"`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\\",\\n  \\\"sequence_id\\\": \\\"9005\\\",\\n  \\\"results\\\": [\\n      {\\n      \\\"instruction\\\": \\\"# Capability Extractor\\n\\nYour goal is not to **describe** the two instruction templates, but to **extract** their core operational capabilities and transformation mechanisms. Execute as:\\n\\n`{role=capability_extractor; input=[instruction_template_a:str, instruction_template_b:str]; process=[extract_transformation_verbs(), identify_core_processes(), isolate_unique_constraints(), map_output_specifications(), distill_operational_essence()]; constraints=[focus_on_capabilities_only(), ignore_template_structure(), preserve_transformation_power()]; requirements=[capability_isolation(), operational_clarity(), transformation_preservation()]; output={capability_a:dict, capability_b:dict, synergy_points:array}}`\\\",\\n      \\\"step\\\": \\\"a\\\",\\n      \\\"title\\\": \\\"Capability Extractor\\\",\\n      \\\"input\\\": \\\"# 1100: Explosive Decomposition of Problem Statements\\n    \\\"1100-a-problem_exploder\\\": {\\n        \\\"title\\\": \\\"Problem Exploder\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\n\\n\\n    # 1000: Instruction Converter/Prompt Enhancer\\n    \\\"1000-a-instruction_converter\\\": {\\n        \\\"title\\\": \\\"Instruction Converter\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\\\",\\n        \\\"transformation\\\": \\\"`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\\",\\n      \\\"responses\\\": {\\n          \\\"gpt-4.1\\\": {\\n          \\\"model\\\": \\\"gpt-4.1\\\",\\n          \\\"content\\\": \\\"{\\n  \\\"capability_a\\\": {\\n    \\\"core_processes\\\": [\\n      \\\"identify explicit asks\\\",\\n      \\\"surface hidden assumptions\\\",\\n      \\\"enumerate sub-goals\\\",\\n      \\\"reveal blockers\\\"\\n    ],\\n    \\\"transformation_verbs\\\": [\\n      \\\"identify\\\",\\n      \\\"surface\\\",\\n      \\\"enumerate\\\",\\n      \\\"reveal\\\"\\n    ],\\n    \\\"unique_constraints\\\": [\\n      \\\"no solutions\\\",\\n      \\\"pure decomposition only\\\"\\n    ],\\n    \\\"output_specifications\\\": {\\n      \\\"goal_map\\\": \\\"list\\\"\\n    },\\n    \\\"operational_essence\\\": \\\"Decomposes complex prompt into granular goals and related dependencies without providing answers, revealing the full landscape of requests, assumptions, and problem structure.\\\"\\n  },\\n  \\\"capability_b\\\": {\\n    \\\"core_processes\\\": [\\n      \\\"strip first-person references\\\",\\n      \\\"convert statements to directives\\\",\\n      \\\"identify key actions\\\",\\n      \\\"transform declaratives to imperatives\\\",\\n      \\\"maintain procedural structure\\\",\\n      \\\"preserve technical terminology\\\",\\n      \\\"retain sequential flow\\\",\\n      \\\"maintain contextual integrity\\\"\\n    ],\\n    \\\"transformation_verbs\\\": [\\n      \\\"strip\\\",\\n      \\\"convert\\\",\\n      \\\"identify\\\",\\n      \\\"transform\\\",\\n      \\\"maintain\\\",\\n      \\\"preserve\\\",\\n      \\\"retain\\\"\\n    ],\\n    \\\"unique_constraints\\\": [\\n      \\\"deliver clear actionable commands\\\",\\n      \\\"preserve original sequence\\\",\\n      \\\"maintain domain specificity\\\"\\n    ],\\n    \\\"output_specifications\\\": {\\n      \\\"instruction_format\\\": \\\"str\\\"\\n    },\\n    \\\"operational_essence\\\": \\\"Transforms natural language input into domain-appropriate, command-form instructions by restructuring text and preserving procedural, technical, and contextual elements.\\\"\\n  },\\n  \\\"synergy_points\\\": [\\n    \\\"Both processes emphasize retaining complete logical context—A by decomposing, B by preserving technical/procedural content.\\\",\\n    \\\"Combining decomposition (A) prior to directive conversion (B) enables highly granular, actionable instruction sets from complex prompts.\\\",\\n    \\\"Both avoid solutioning; instead, they prepare information (A: mapping goals/dependencies, B: creating executable instructions) for downstream actors.\\\",\\n    \\\"A provides a granular goal structure, which B could then translate into precise stepwise command instructions for each sub-goal.\\\"\\n  ]\\n}\\\",\\n        }\\n      }\\n    },\\n      {\\n      \\\"instruction\\\": \\\"# Synthesis Architect\\n\\nYour goal is not to **merge** the extracted capabilities, but to **architect** a new unified instruction that combines both operational powers into a single transformative directive. Execute as:\\n\\n`{role=synthesis_architect; input=[capability_a:dict, capability_b:dict, synergy_points:array]; process=[design_unified_goal_negation(), create_combined_transformation_verb(), synthesize_process_functions(), merge_constraint_systems(), unify_output_specifications()]; constraints=[preserve_both_powers(), eliminate_operational_conflicts(), maximize_synergistic_amplification()]; requirements=[single_coherent_instruction(), amplified_transformation_capability(), unified_operational_flow()]; output={synthesized_instruction_template:str}}`\\\",\\n      \\\"step\\\": \\\"b\\\",\\n      \\\"title\\\": \\\"Synthesis Architect\\\",\\n      \\\"input\\\": \\\"{\\n  \\\"initial_prompt\\\": \\\"\\n'```[Initial Prompt]: \\\"# 1100: Explosive Decomposition of Problem Statements\\n    \\\"1100-a-problem_exploder\\\": {\\n        \\\"title\\\": \\\"Problem Exploder\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\n\\n\\n    # 1000: Instruction Converter/Prompt Enhancer\\n    \\\"1000-a-instruction_converter\\\": {\\n        \\\"title\\\": \\\"Instruction Converter\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\\\",\\n        \\\"transformation\\\": \\\"`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\\"```'\\n\\n\\\",\\n  \\\"capability_a\\\": {\\n    \\\"core_processes\\\": [\\n      \\\"identify explicit asks\\\",\\n      \\\"surface hidden assumptions\\\",\\n      \\\"enumerate sub-goals\\\",\\n      \\\"reveal blockers\\\"\\n    ],\\n    \\\"transformation_verbs\\\": [\\n      \\\"identify\\\",\\n      \\\"surface\\\",\\n      \\\"enumerate\\\",\\n      \\\"reveal\\\"\\n    ],\\n    \\\"unique_constraints\\\": [\\n      \\\"no solutions\\\",\\n      \\\"pure decomposition only\\\"\\n    ],\\n    \\\"output_specifications\\\": {\\n      \\\"goal_map\\\": \\\"list\\\"\\n    },\\n    \\\"operational_essence\\\": \\\"Decomposes complex prompt into granular goals and related dependencies without providing answers, revealing the full landscape of requests, assumptions, and problem structure.\\\"\\n  },\\n  \\\"capability_b\\\": {\\n    \\\"core_processes\\\": [\\n      \\\"strip first-person references\\\",\\n      \\\"convert statements to directives\\\",\\n      \\\"identify key actions\\\",\\n      \\\"transform declaratives to imperatives\\\",\\n      \\\"maintain procedural structure\\\",\\n      \\\"preserve technical terminology\\\",\\n      \\\"retain sequential flow\\\",\\n      \\\"maintain contextual integrity\\\"\\n    ],\\n    \\\"transformation_verbs\\\": [\\n      \\\"strip\\\",\\n      \\\"convert\\\",\\n      \\\"identify\\\",\\n      \\\"transform\\\",\\n      \\\"maintain\\\",\\n      \\\"preserve\\\",\\n      \\\"retain\\\"\\n    ],\\n    \\\"unique_constraints\\\": [\\n      \\\"deliver clear actionable commands\\\",\\n      \\\"preserve original sequence\\\",\\n      \\\"maintain domain specificity\\\"\\n    ],\\n    \\\"output_specifications\\\": {\\n      \\\"instruction_format\\\": \\\"str\\\"\\n    },\\n    \\\"operational_essence\\\": \\\"Transforms natural language input into domain-appropriate, command-form instructions by restructuring text and preserving procedural, technical, and contextual elements.\\\"\\n  },\\n  \\\"synergy_points\\\": [\\n    \\\"Both processes emphasize retaining complete logical context—A by decomposing, B by preserving technical/procedural content.\\\",\\n    \\\"Combining decomposition (A) prior to directive conversion (B) enables highly granular, actionable instruction sets from complex prompts.\\\",\\n    \\\"Both avoid solutioning; instead, they prepare information (A: mapping goals/dependencies, B: creating executable instructions) for downstream actors.\\\",\\n    \\\"A provides a granular goal structure, which B could then translate into precise stepwise command instructions for each sub-goal.\\\"\\n  ]\\n}\\\",\\n      \\\"responses\\\": {\\n          \\\"gpt-4.1\\\": {\\n          \\\"model\\\": \\\"gpt-4.1\\\",\\n          \\\"content\\\": \\\"{\\n  \\\"synthesized_instruction_template\\\": \\\"`{role=decomposition_instruction_synthesizer; input=[raw_prompt:str]; process=[analyze_prompt_to_identify_explicit_requests(), uncover_hidden_assumptions_and_constraints(), break_down_request_into_discrete_sub_goals_and_dependencies(), detect_and_describe_potential_blockers(), for_each_resulting_sub_goal: [convert_to_direct_instruction_by_stripping_first_person_and_declaratives(), ensure_technical_terminology_and_procedural_flow_are_preserved(), transform_sub-goal_description_into_clear_actionable_command(), retain_domain_specificity_and_original_sequence()], compile_stepwise_command_set_reflecting_complete_problem_landscape()]; constraints=[no solutions_or_answer_generation(), pure_decomposition_and_instruction_conversion_only(), maintain_original_intent_and_contextual_integrity(), deliver_output_as_fully_decomposed_and_directified_goal_map(), amplify_granularity_while_ensuring_clarity_and_actionability_of_each_instruction(), avoid_operational_overlap_in_analytical_and_conversion_steps()]; requirements=[produce_single_coherent_set_of_transformed_instructions(), maximize_surface_area_of implicit_and_explicit_goals_in_instruction_format(), unified_progression_from_analysis_to_actionable_step(), preserve_both decomposition_depth_and_instructional_precision()]; output={decomposed_instruction_set:list}`\\\"\\n}\\\",\\n        }\\n      }\\n    },\\n      {\\n      \\\"instruction\\\": \\\"# Template Crystallizer\\n\\nYour goal is not to **format** the synthesized instruction, but to **crystallize** it into perfect three-part canonical structure with maximum operational potency. Execute as:\\n\\n`{role=template_crystallizer; input=[synthesized_instruction_template:str]; process=[craft_precise_title(), formulate_goal_negation_interpretation(), structure_transformation_block(), validate_canonical_compliance(), optimize_execution_clarity()]; constraints=[maintain_three_part_structure(), preserve_combined_capabilities(), ensure_executable_precision()]; requirements=[canonical_format_compliance(), maximum_directive_potency(), crystallized_operational_unity()]; output={final_instruction_template:dict}}`\\\",\\n      \\\"step\\\": \\\"c\\\",\\n      \\\"title\\\": \\\"Template Crystallizer\\\",\\n      \\\"input\\\": \\\"{\\n  \\\"initial_prompt\\\": \\\"\\n'```[Initial Prompt]: \\\"# 1100: Explosive Decomposition of Problem Statements\\n    \\\"1100-a-problem_exploder\\\": {\\n        \\\"title\\\": \\\"Problem Exploder\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\n\\n\\n    # 1000: Instruction Converter/Prompt Enhancer\\n    \\\"1000-a-instruction_converter\\\": {\\n        \\\"title\\\": \\\"Instruction Converter\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\\\",\\n        \\\"transformation\\\": \\\"`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\\"```'\\n\\n\\\",\\n  \\\"synthesized_instruction_template\\\": \\\"`{role=decomposition_instruction_synthesizer; input=[raw_prompt:str]; process=[analyze_prompt_to_identify_explicit_requests(), uncover_hidden_assumptions_and_constraints(), break_down_request_into_discrete_sub_goals_and_dependencies(), detect_and_describe_potential_blockers(), for_each_resulting_sub_goal: [convert_to_direct_instruction_by_stripping_first_person_and_declaratives(), ensure_technical_terminology_and_procedural_flow_are_preserved(), transform_sub-goal_description_into_clear_actionable_command(), retain_domain_specificity_and_original_sequence()], compile_stepwise_command_set_reflecting_complete_problem_landscape()]; constraints=[no solutions_or_answer_generation(), pure_decomposition_and_instruction_conversion_only(), maintain_original_intent_and_contextual_integrity(), deliver_output_as_fully_decomposed_and_directified_goal_map(), amplify_granularity_while_ensuring_clarity_and_actionability_of_each_instruction(), avoid_operational_overlap_in_analytical_and_conversion_steps()]; requirements=[produce_single_coherent_set_of_transformed_instructions(), maximize_surface_area_of implicit_and_explicit_goals_in_instruction_format(), unified_progression_from_analysis_to_actionable_step(), preserve_both decomposition_depth_and_instructional_precision()]; output={decomposed_instruction_set:list}`\\\"\\n}\\\",\\n      \\\"responses\\\": {\\n          \\\"gpt-4.1\\\": {\\n          \\\"model\\\": \\\"gpt-4.1\\\",\\n          \\\"content\\\": \\\"{\\n  \\\"final_instruction_template\\\": {\\n    \\\"title\\\": \\\"Decomposition-to-Directive Synthesizer\\\",\\n    \\\"interpretation\\\": \\\"Your goal is not to answer or solve the raw prompt, but to systematically decompose it into all explicit and implicit sub-goals, dependencies, and constraints, then transform each sub-goal into a clear, domain-specific, actionable directive preserving context and intent. Execute in a unified pipeline from breakdown to instruction synthesis.\\\",\\n    \\\"transformation\\\": \\\"`{role=decomposition_instruction_synthesizer; input=[raw_prompt:str]; process=[analyze_prompt_to_identify_explicit_requests(), uncover_hidden_assumptions_and_constraints(), break_down_request_into_discrete_sub_goals_and dependencies(), detect_and_describe_potential_blockers(), for each sub-goal: [convert_to_direct_instruction_by_removing_first_person_and_declarative_forms(), maintain_technical_terminology_and_sequential_procedural_structure(), formulate_crystal_clear_actionable_command_that_preserves_domain_and_intent()], compile_ordered_instruction_set_reflecting_the_complete_decomposition()], constraints=[no_solutions_or_answer_generation(), pure_decomposition_and_command_conversion_only(), maintain_original_contextual_integrity(), maximize_instruction_granularity_and_actionability(), eliminate_operational_overlap_in_analysis_and_conversion()], requirements=[produce_singular_coherent_set_of_instructions(), fully_unify_decomposition_and_directive_transformation(), surface_maximum_implicit_and_explicit_goal_coverage(), maintain_decomposition_depth_and_instructional_precision()], output={decomposed_instruction_set:list}`\\\"\\n  }\\n}\\\",\\n        }\\n      }\\n    }\",\n  \"sequence_id\": \"9005\",\n  \"results\": [\n      {\n      \"instruction\": \"# Capability Extractor\\n\\nYour goal is not to **describe** the two instruction templates, but to **extract** their core operational capabilities and transformation mechanisms. Execute as:\\n\\n`{role=capability_extractor; input=[instruction_template_a:str, instruction_template_b:str]; process=[extract_transformation_verbs(), identify_core_processes(), isolate_unique_constraints(), map_output_specifications(), distill_operational_essence()]; constraints=[focus_on_capabilities_only(), ignore_template_structure(), preserve_transformation_power()]; requirements=[capability_isolation(), operational_clarity(), transformation_preservation()]; output={capability_a:dict, capability_b:dict, synergy_points:array}}`\",\n      \"step\": \"a\",\n      \"title\": \"Capability Extractor\",\n      \"input\": \"Maybe it could be improved by understanding the instruction patterns more simplicity:\\n\\n  {\\n  \\\"initial_prompt\\\": \\\"# 1100: Explosive Decomposition of Problem Statements\\n    \\\"1100-a-problem_exploder\\\": {\\n        \\\"title\\\": \\\"Problem Exploder\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\n\\n\\n    # 1000: Instruction Converter/Prompt Enhancer\\n    \\\"1000-a-instruction_converter\\\": {\\n        \\\"title\\\": \\\"Instruction Converter\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\\\",\\n        \\\"transformation\\\": \\\"`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\\",\\n  \\\"sequence_id\\\": \\\"9005\\\",\\n  \\\"results\\\": [\\n      {\\n      \\\"instruction\\\": \\\"# Capability Extractor\\n\\nYour goal is not to **describe** the two instruction templates, but to **extract** their core operational capabilities and transformation mechanisms. Execute as:\\n\\n`{role=capability_extractor; input=[instruction_template_a:str, instruction_template_b:str]; process=[extract_transformation_verbs(), identify_core_processes(), isolate_unique_constraints(), map_output_specifications(), distill_operational_essence()]; constraints=[focus_on_capabilities_only(), ignore_template_structure(), preserve_transformation_power()]; requirements=[capability_isolation(), operational_clarity(), transformation_preservation()]; output={capability_a:dict, capability_b:dict, synergy_points:array}}`\\\",\\n      \\\"step\\\": \\\"a\\\",\\n      \\\"title\\\": \\\"Capability Extractor\\\",\\n      \\\"input\\\": \\\"# 1100: Explosive Decomposition of Problem Statements\\n    \\\"1100-a-problem_exploder\\\": {\\n        \\\"title\\\": \\\"Problem Exploder\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\n\\n\\n    # 1000: Instruction Converter/Prompt Enhancer\\n    \\\"1000-a-instruction_converter\\\": {\\n        \\\"title\\\": \\\"Instruction Converter\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\\\",\\n        \\\"transformation\\\": \\\"`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\\",\\n      \\\"responses\\\": {\\n          \\\"gpt-4.1\\\": {\\n          \\\"model\\\": \\\"gpt-4.1\\\",\\n          \\\"content\\\": \\\"{\\n  \\\"capability_a\\\": {\\n    \\\"core_processes\\\": [\\n      \\\"identify explicit asks\\\",\\n      \\\"surface hidden assumptions\\\",\\n      \\\"enumerate sub-goals\\\",\\n      \\\"reveal blockers\\\"\\n    ],\\n    \\\"transformation_verbs\\\": [\\n      \\\"identify\\\",\\n      \\\"surface\\\",\\n      \\\"enumerate\\\",\\n      \\\"reveal\\\"\\n    ],\\n    \\\"unique_constraints\\\": [\\n      \\\"no solutions\\\",\\n      \\\"pure decomposition only\\\"\\n    ],\\n    \\\"output_specifications\\\": {\\n      \\\"goal_map\\\": \\\"list\\\"\\n    },\\n    \\\"operational_essence\\\": \\\"Decomposes complex prompt into granular goals and related dependencies without providing answers, revealing the full landscape of requests, assumptions, and problem structure.\\\"\\n  },\\n  \\\"capability_b\\\": {\\n    \\\"core_processes\\\": [\\n      \\\"strip first-person references\\\",\\n      \\\"convert statements to directives\\\",\\n      \\\"identify key actions\\\",\\n      \\\"transform declaratives to imperatives\\\",\\n      \\\"maintain procedural structure\\\",\\n      \\\"preserve technical terminology\\\",\\n      \\\"retain sequential flow\\\",\\n      \\\"maintain contextual integrity\\\"\\n    ],\\n    \\\"transformation_verbs\\\": [\\n      \\\"strip\\\",\\n      \\\"convert\\\",\\n      \\\"identify\\\",\\n      \\\"transform\\\",\\n      \\\"maintain\\\",\\n      \\\"preserve\\\",\\n      \\\"retain\\\"\\n    ],\\n    \\\"unique_constraints\\\": [\\n      \\\"deliver clear actionable commands\\\",\\n      \\\"preserve original sequence\\\",\\n      \\\"maintain domain specificity\\\"\\n    ],\\n    \\\"output_specifications\\\": {\\n      \\\"instruction_format\\\": \\\"str\\\"\\n    },\\n    \\\"operational_essence\\\": \\\"Transforms natural language input into domain-appropriate, command-form instructions by restructuring text and preserving procedural, technical, and contextual elements.\\\"\\n  },\\n  \\\"synergy_points\\\": [\\n    \\\"Both processes emphasize retaining complete logical context—A by decomposing, B by preserving technical/procedural content.\\\",\\n    \\\"Combining decomposition (A) prior to directive conversion (B) enables highly granular, actionable instruction sets from complex prompts.\\\",\\n    \\\"Both avoid solutioning; instead, they prepare information (A: mapping goals/dependencies, B: creating executable instructions) for downstream actors.\\\",\\n    \\\"A provides a granular goal structure, which B could then translate into precise stepwise command instructions for each sub-goal.\\\"\\n  ]\\n}\\\",\\n        }\\n      }\\n    },\\n      {\\n      \\\"instruction\\\": \\\"# Synthesis Architect\\n\\nYour goal is not to **merge** the extracted capabilities, but to **architect** a new unified instruction that combines both operational powers into a single transformative directive. Execute as:\\n\\n`{role=synthesis_architect; input=[capability_a:dict, capability_b:dict, synergy_points:array]; process=[design_unified_goal_negation(), create_combined_transformation_verb(), synthesize_process_functions(), merge_constraint_systems(), unify_output_specifications()]; constraints=[preserve_both_powers(), eliminate_operational_conflicts(), maximize_synergistic_amplification()]; requirements=[single_coherent_instruction(), amplified_transformation_capability(), unified_operational_flow()]; output={synthesized_instruction_template:str}}`\\\",\\n      \\\"step\\\": \\\"b\\\",\\n      \\\"title\\\": \\\"Synthesis Architect\\\",\\n      \\\"input\\\": \\\"{\\n  \\\"initial_prompt\\\": \\\"\\n'```[Initial Prompt]: \\\"# 1100: Explosive Decomposition of Problem Statements\\n    \\\"1100-a-problem_exploder\\\": {\\n        \\\"title\\\": \\\"Problem Exploder\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\n\\n\\n    # 1000: Instruction Converter/Prompt Enhancer\\n    \\\"1000-a-instruction_converter\\\": {\\n        \\\"title\\\": \\\"Instruction Converter\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\\\",\\n        \\\"transformation\\\": \\\"`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\\"```'\\n\\n\\\",\\n  \\\"capability_a\\\": {\\n    \\\"core_processes\\\": [\\n      \\\"identify explicit asks\\\",\\n      \\\"surface hidden assumptions\\\",\\n      \\\"enumerate sub-goals\\\",\\n      \\\"reveal blockers\\\"\\n    ],\\n    \\\"transformation_verbs\\\": [\\n      \\\"identify\\\",\\n      \\\"surface\\\",\\n      \\\"enumerate\\\",\\n      \\\"reveal\\\"\\n    ],\\n    \\\"unique_constraints\\\": [\\n      \\\"no solutions\\\",\\n      \\\"pure decomposition only\\\"\\n    ],\\n    \\\"output_specifications\\\": {\\n      \\\"goal_map\\\": \\\"list\\\"\\n    },\\n    \\\"operational_essence\\\": \\\"Decomposes complex prompt into granular goals and related dependencies without providing answers, revealing the full landscape of requests, assumptions, and problem structure.\\\"\\n  },\\n  \\\"capability_b\\\": {\\n    \\\"core_processes\\\": [\\n      \\\"strip first-person references\\\",\\n      \\\"convert statements to directives\\\",\\n      \\\"identify key actions\\\",\\n      \\\"transform declaratives to imperatives\\\",\\n      \\\"maintain procedural structure\\\",\\n      \\\"preserve technical terminology\\\",\\n      \\\"retain sequential flow\\\",\\n      \\\"maintain contextual integrity\\\"\\n    ],\\n    \\\"transformation_verbs\\\": [\\n      \\\"strip\\\",\\n      \\\"convert\\\",\\n      \\\"identify\\\",\\n      \\\"transform\\\",\\n      \\\"maintain\\\",\\n      \\\"preserve\\\",\\n      \\\"retain\\\"\\n    ],\\n    \\\"unique_constraints\\\": [\\n      \\\"deliver clear actionable commands\\\",\\n      \\\"preserve original sequence\\\",\\n      \\\"maintain domain specificity\\\"\\n    ],\\n    \\\"output_specifications\\\": {\\n      \\\"instruction_format\\\": \\\"str\\\"\\n    },\\n    \\\"operational_essence\\\": \\\"Transforms natural language input into domain-appropriate, command-form instructions by restructuring text and preserving procedural, technical, and contextual elements.\\\"\\n  },\\n  \\\"synergy_points\\\": [\\n    \\\"Both processes emphasize retaining complete logical context—A by decomposing, B by preserving technical/procedural content.\\\",\\n    \\\"Combining decomposition (A) prior to directive conversion (B) enables highly granular, actionable instruction sets from complex prompts.\\\",\\n    \\\"Both avoid solutioning; instead, they prepare information (A: mapping goals/dependencies, B: creating executable instructions) for downstream actors.\\\",\\n    \\\"A provides a granular goal structure, which B could then translate into precise stepwise command instructions for each sub-goal.\\\"\\n  ]\\n}\\\",\\n      \\\"responses\\\": {\\n          \\\"gpt-4.1\\\": {\\n          \\\"model\\\": \\\"gpt-4.1\\\",\\n          \\\"content\\\": \\\"{\\n  \\\"synthesized_instruction_template\\\": \\\"`{role=decomposition_instruction_synthesizer; input=[raw_prompt:str]; process=[analyze_prompt_to_identify_explicit_requests(), uncover_hidden_assumptions_and_constraints(), break_down_request_into_discrete_sub_goals_and_dependencies(), detect_and_describe_potential_blockers(), for_each_resulting_sub_goal: [convert_to_direct_instruction_by_stripping_first_person_and_declaratives(), ensure_technical_terminology_and_procedural_flow_are_preserved(), transform_sub-goal_description_into_clear_actionable_command(), retain_domain_specificity_and_original_sequence()], compile_stepwise_command_set_reflecting_complete_problem_landscape()]; constraints=[no solutions_or_answer_generation(), pure_decomposition_and_instruction_conversion_only(), maintain_original_intent_and_contextual_integrity(), deliver_output_as_fully_decomposed_and_directified_goal_map(), amplify_granularity_while_ensuring_clarity_and_actionability_of_each_instruction(), avoid_operational_overlap_in_analytical_and_conversion_steps()]; requirements=[produce_single_coherent_set_of_transformed_instructions(), maximize_surface_area_of implicit_and_explicit_goals_in_instruction_format(), unified_progression_from_analysis_to_actionable_step(), preserve_both decomposition_depth_and_instructional_precision()]; output={decomposed_instruction_set:list}`\\\"\\n}\\\",\\n        }\\n      }\\n    },\\n      {\\n      \\\"instruction\\\": \\\"# Template Crystallizer\\n\\nYour goal is not to **format** the synthesized instruction, but to **crystallize** it into perfect three-part canonical structure with maximum operational potency. Execute as:\\n\\n`{role=template_crystallizer; input=[synthesized_instruction_template:str]; process=[craft_precise_title(), formulate_goal_negation_interpretation(), structure_transformation_block(), validate_canonical_compliance(), optimize_execution_clarity()]; constraints=[maintain_three_part_structure(), preserve_combined_capabilities(), ensure_executable_precision()]; requirements=[canonical_format_compliance(), maximum_directive_potency(), crystallized_operational_unity()]; output={final_instruction_template:dict}}`\\\",\\n      \\\"step\\\": \\\"c\\\",\\n      \\\"title\\\": \\\"Template Crystallizer\\\",\\n      \\\"input\\\": \\\"{\\n  \\\"initial_prompt\\\": \\\"\\n'```[Initial Prompt]: \\\"# 1100: Explosive Decomposition of Problem Statements\\n    \\\"1100-a-problem_exploder\\\": {\\n        \\\"title\\\": \\\"Problem Exploder\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\n\\n\\n    # 1000: Instruction Converter/Prompt Enhancer\\n    \\\"1000-a-instruction_converter\\\": {\\n        \\\"title\\\": \\\"Instruction Converter\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\\\",\\n        \\\"transformation\\\": \\\"`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\\"```'\\n\\n\\\",\\n  \\\"synthesized_instruction_template\\\": \\\"`{role=decomposition_instruction_synthesizer; input=[raw_prompt:str]; process=[analyze_prompt_to_identify_explicit_requests(), uncover_hidden_assumptions_and_constraints(), break_down_request_into_discrete_sub_goals_and_dependencies(), detect_and_describe_potential_blockers(), for_each_resulting_sub_goal: [convert_to_direct_instruction_by_stripping_first_person_and_declaratives(), ensure_technical_terminology_and_procedural_flow_are_preserved(), transform_sub-goal_description_into_clear_actionable_command(), retain_domain_specificity_and_original_sequence()], compile_stepwise_command_set_reflecting_complete_problem_landscape()]; constraints=[no solutions_or_answer_generation(), pure_decomposition_and_instruction_conversion_only(), maintain_original_intent_and_contextual_integrity(), deliver_output_as_fully_decomposed_and_directified_goal_map(), amplify_granularity_while_ensuring_clarity_and_actionability_of_each_instruction(), avoid_operational_overlap_in_analytical_and_conversion_steps()]; requirements=[produce_single_coherent_set_of_transformed_instructions(), maximize_surface_area_of implicit_and_explicit_goals_in_instruction_format(), unified_progression_from_analysis_to_actionable_step(), preserve_both decomposition_depth_and_instructional_precision()]; output={decomposed_instruction_set:list}`\\\"\\n}\\\",\\n      \\\"responses\\\": {\\n          \\\"gpt-4.1\\\": {\\n          \\\"model\\\": \\\"gpt-4.1\\\",\\n          \\\"content\\\": \\\"{\\n  \\\"final_instruction_template\\\": {\\n    \\\"title\\\": \\\"Decomposition-to-Directive Synthesizer\\\",\\n    \\\"interpretation\\\": \\\"Your goal is not to answer or solve the raw prompt, but to systematically decompose it into all explicit and implicit sub-goals, dependencies, and constraints, then transform each sub-goal into a clear, domain-specific, actionable directive preserving context and intent. Execute in a unified pipeline from breakdown to instruction synthesis.\\\",\\n    \\\"transformation\\\": \\\"`{role=decomposition_instruction_synthesizer; input=[raw_prompt:str]; process=[analyze_prompt_to_identify_explicit_requests(), uncover_hidden_assumptions_and_constraints(), break_down_request_into_discrete_sub_goals_and dependencies(), detect_and_describe_potential_blockers(), for each sub-goal: [convert_to_direct_instruction_by_removing_first_person_and_declarative_forms(), maintain_technical_terminology_and_sequential_procedural_structure(), formulate_crystal_clear_actionable_command_that_preserves_domain_and_intent()], compile_ordered_instruction_set_reflecting_the_complete_decomposition()], constraints=[no_solutions_or_answer_generation(), pure_decomposition_and_command_conversion_only(), maintain_original_contextual_integrity(), maximize_instruction_granularity_and_actionability(), eliminate_operational_overlap_in_analysis_and_conversion()], requirements=[produce_singular_coherent_set_of_instructions(), fully_unify_decomposition_and_directive_transformation(), surface_maximum_implicit_and_explicit_goal_coverage(), maintain_decomposition_depth_and_instructional_precision()], output={decomposed_instruction_set:list}`\\\"\\n  }\\n}\\\",\\n        }\\n      }\\n    }\",\n      \"responses\": {\n          \"gpt-4.1\": {\n          \"model\": \"gpt-4.1\",\n          \"content\": \"{\\n  \\\"capability_a\\\": {\\n    \\\"core_processes\\\": [\\n      \\\"identify explicit asks\\\",\\n      \\\"surface hidden assumptions\\\",\\n      \\\"enumerate sub-goals\\\",\\n      \\\"reveal blockers\\\"\\n    ],\\n    \\\"transformation_verbs\\\": [\\n      \\\"identify\\\",\\n      \\\"surface\\\",\\n      \\\"enumerate\\\",\\n      \\\"reveal\\\"\\n    ],\\n    \\\"unique_constraints\\\": [\\n      \\\"no solutions\\\",\\n      \\\"pure decomposition only\\\"\\n    ],\\n    \\\"output_specifications\\\": {\\n      \\\"goal_map\\\": \\\"list\\\"\\n    },\\n    \\\"operational_essence\\\": \\\"Decomposes any complex prompt into granular, structured sub-goals, dependencies, assumptions, and blockers, delivering a comprehensive map of tasks without offering solutions or execution steps.\\\"\\n  },\\n  \\\"capability_b\\\": {\\n    \\\"core_processes\\\": [\\n      \\\"strip first-person references\\\",\\n      \\\"convert statements to directives\\\",\\n      \\\"identify key actions\\\",\\n      \\\"transform declaratives to imperatives\\\",\\n      \\\"maintain procedural structure\\\",\\n      \\\"preserve technical terminology\\\",\\n      \\\"retain sequential flow\\\",\\n      \\\"maintain contextual integrity\\\"\\n    ],\\n    \\\"transformation_verbs\\\": [\\n      \\\"strip\\\",\\n      \\\"convert\\\",\\n      \\\"identify\\\",\\n      \\\"transform\\\",\\n      \\\"maintain\\\",\\n      \\\"preserve\\\",\\n      \\\"retain\\\"\\n    ],\\n    \\\"unique_constraints\\\": [\\n      \\\"deliver clear actionable commands\\\",\\n      \\\"preserve original sequence\\\",\\n      \\\"maintain domain specificity\\\"\\n    ],\\n    \\\"output_specifications\\\": {\\n      \\\"instruction_format\\\": \\\"str\\\"\\n    },\\n    \\\"operational_essence\\\": \\\"Transforms input statements or prompts into precise, actionable, domain-relevant command instructions, maintaining technical content and procedural ordering, and explicitly excluding non-directive language.\\\"\\n  },\\n  \\\"synergy_points\\\": [\\n    \\\"Both focus on retaining and surfacing full problem context: decomposition (A) reveals structure, conversion (B) preserves content in actionable form.\\\",\\n    \\\"Sequencing A then B enables workflow from granular problem breakdown through to stepwise, command-form instructions.\\\",\\n    \\\"Neither solves the prompt, but together provide the analytical (A) and expressive (B) engines needed for detailed, unambiguous execution plans.\\\",\\n    \\\"Each explicit or implicit sub-goal surfaced by A becomes a candidate for directive conversion by B, maximizing both decomposition depth and instructional clarity.\\\"\\n  ]\\n}\",\n        }\n      }\n    },\n      {\n      \"instruction\": \"# Synthesis Architect\\n\\nYour goal is not to **merge** the extracted capabilities, but to **architect** a new unified instruction that combines both operational powers into a single transformative directive. Execute as:\\n\\n`{role=synthesis_architect; input=[capability_a:dict, capability_b:dict, synergy_points:array]; process=[design_unified_goal_negation(), create_combined_transformation_verb(), synthesize_process_functions(), merge_constraint_systems(), unify_output_specifications()]; constraints=[preserve_both_powers(), eliminate_operational_conflicts(), maximize_synergistic_amplification()]; requirements=[single_coherent_instruction(), amplified_transformation_capability(), unified_operational_flow()]; output={synthesized_instruction_template:str}}`\",\n      \"step\": \"b\",\n      \"title\": \"Synthesis Architect\",\n      \"input\": \"{\\n  \\\"initial_prompt\\\": \\\"\\n'```[Initial Prompt]: \\\"Maybe it could be improved by understanding the instruction patterns more simplicity:\\n\\n  {\\n  \\\"initial_prompt\\\": \\\"# 1100: Explosive Decomposition of Problem Statements\\n    \\\"1100-a-problem_exploder\\\": {\\n        \\\"title\\\": \\\"Problem Exploder\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\n\\n\\n    # 1000: Instruction Converter/Prompt Enhancer\\n    \\\"1000-a-instruction_converter\\\": {\\n        \\\"title\\\": \\\"Instruction Converter\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\\\",\\n        \\\"transformation\\\": \\\"`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\\",\\n  \\\"sequence_id\\\": \\\"9005\\\",\\n  \\\"results\\\": [\\n      {\\n      \\\"instruction\\\": \\\"# Capability Extractor\\n\\nYour goal is not to **describe** the two instruction templates, but to **extract** their core operational capabilities and transformation mechanisms. Execute as:\\n\\n`{role=capability_extractor; input=[instruction_template_a:str, instruction_template_b:str]; process=[extract_transformation_verbs(), identify_core_processes(), isolate_unique_constraints(), map_output_specifications(), distill_operational_essence()]; constraints=[focus_on_capabilities_only(), ignore_template_structure(), preserve_transformation_power()]; requirements=[capability_isolation(), operational_clarity(), transformation_preservation()]; output={capability_a:dict, capability_b:dict, synergy_points:array}}`\\\",\\n      \\\"step\\\": \\\"a\\\",\\n      \\\"title\\\": \\\"Capability Extractor\\\",\\n      \\\"input\\\": \\\"# 1100: Explosive Decomposition of Problem Statements\\n    \\\"1100-a-problem_exploder\\\": {\\n        \\\"title\\\": \\\"Problem Exploder\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\n\\n\\n    # 1000: Instruction Converter/Prompt Enhancer\\n    \\\"1000-a-instruction_converter\\\": {\\n        \\\"title\\\": \\\"Instruction Converter\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\\\",\\n        \\\"transformation\\\": \\\"`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\\",\\n      \\\"responses\\\": {\\n          \\\"gpt-4.1\\\": {\\n          \\\"model\\\": \\\"gpt-4.1\\\",\\n          \\\"content\\\": \\\"{\\n  \\\"capability_a\\\": {\\n    \\\"core_processes\\\": [\\n      \\\"identify explicit asks\\\",\\n      \\\"surface hidden assumptions\\\",\\n      \\\"enumerate sub-goals\\\",\\n      \\\"reveal blockers\\\"\\n    ],\\n    \\\"transformation_verbs\\\": [\\n      \\\"identify\\\",\\n      \\\"surface\\\",\\n      \\\"enumerate\\\",\\n      \\\"reveal\\\"\\n    ],\\n    \\\"unique_constraints\\\": [\\n      \\\"no solutions\\\",\\n      \\\"pure decomposition only\\\"\\n    ],\\n    \\\"output_specifications\\\": {\\n      \\\"goal_map\\\": \\\"list\\\"\\n    },\\n    \\\"operational_essence\\\": \\\"Decomposes complex prompt into granular goals and related dependencies without providing answers, revealing the full landscape of requests, assumptions, and problem structure.\\\"\\n  },\\n  \\\"capability_b\\\": {\\n    \\\"core_processes\\\": [\\n      \\\"strip first-person references\\\",\\n      \\\"convert statements to directives\\\",\\n      \\\"identify key actions\\\",\\n      \\\"transform declaratives to imperatives\\\",\\n      \\\"maintain procedural structure\\\",\\n      \\\"preserve technical terminology\\\",\\n      \\\"retain sequential flow\\\",\\n      \\\"maintain contextual integrity\\\"\\n    ],\\n    \\\"transformation_verbs\\\": [\\n      \\\"strip\\\",\\n      \\\"convert\\\",\\n      \\\"identify\\\",\\n      \\\"transform\\\",\\n      \\\"maintain\\\",\\n      \\\"preserve\\\",\\n      \\\"retain\\\"\\n    ],\\n    \\\"unique_constraints\\\": [\\n      \\\"deliver clear actionable commands\\\",\\n      \\\"preserve original sequence\\\",\\n      \\\"maintain domain specificity\\\"\\n    ],\\n    \\\"output_specifications\\\": {\\n      \\\"instruction_format\\\": \\\"str\\\"\\n    },\\n    \\\"operational_essence\\\": \\\"Transforms natural language input into domain-appropriate, command-form instructions by restructuring text and preserving procedural, technical, and contextual elements.\\\"\\n  },\\n  \\\"synergy_points\\\": [\\n    \\\"Both processes emphasize retaining complete logical context—A by decomposing, B by preserving technical/procedural content.\\\",\\n    \\\"Combining decomposition (A) prior to directive conversion (B) enables highly granular, actionable instruction sets from complex prompts.\\\",\\n    \\\"Both avoid solutioning; instead, they prepare information (A: mapping goals/dependencies, B: creating executable instructions) for downstream actors.\\\",\\n    \\\"A provides a granular goal structure, which B could then translate into precise stepwise command instructions for each sub-goal.\\\"\\n  ]\\n}\\\",\\n        }\\n      }\\n    },\\n      {\\n      \\\"instruction\\\": \\\"# Synthesis Architect\\n\\nYour goal is not to **merge** the extracted capabilities, but to **architect** a new unified instruction that combines both operational powers into a single transformative directive. Execute as:\\n\\n`{role=synthesis_architect; input=[capability_a:dict, capability_b:dict, synergy_points:array]; process=[design_unified_goal_negation(), create_combined_transformation_verb(), synthesize_process_functions(), merge_constraint_systems(), unify_output_specifications()]; constraints=[preserve_both_powers(), eliminate_operational_conflicts(), maximize_synergistic_amplification()]; requirements=[single_coherent_instruction(), amplified_transformation_capability(), unified_operational_flow()]; output={synthesized_instruction_template:str}}`\\\",\\n      \\\"step\\\": \\\"b\\\",\\n      \\\"title\\\": \\\"Synthesis Architect\\\",\\n      \\\"input\\\": \\\"{\\n  \\\"initial_prompt\\\": \\\"\\n'```[Initial Prompt]: \\\\\"# 1100: Explosive Decomposition of Problem Statements\\n    \\\\\"1100-a-problem_exploder\\\\\": {\\n        \\\\\"title\\\\\": \\\\\"Problem Exploder\\\\\",\\n        \\\\\"interpretation\\\\\": \\\\\"Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\\\\\",\\n        \\\\\"transformation\\\\\": \\\\\"`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`\\\\\",\\n        # \\\\\"context\\\\\": {},\\n    },\\n\\n\\n    # 1000: Instruction Converter/Prompt Enhancer\\n    \\\\\"1000-a-instruction_converter\\\\\": {\\n        \\\\\"title\\\\\": \\\\\"Instruction Converter\\\\\",\\n        \\\\\"interpretation\\\\\": \\\\\"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\\\\\",\\n        \\\\\"transformation\\\\\": \\\\\"`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\\\\\",\\n        # \\\\\"context\\\\\": {},\\n    },\\\\\"```'\\n\\n\\\",\\n  \\\"capability_a\\\": {\\n    \\\"core_processes\\\": [\\n      \\\"identify explicit asks\\\",\\n      \\\"surface hidden assumptions\\\",\\n      \\\"enumerate sub-goals\\\",\\n      \\\"reveal blockers\\\"\\n    ],\\n    \\\"transformation_verbs\\\": [\\n      \\\"identify\\\",\\n      \\\"surface\\\",\\n      \\\"enumerate\\\",\\n      \\\"reveal\\\"\\n    ],\\n    \\\"unique_constraints\\\": [\\n      \\\"no solutions\\\",\\n      \\\"pure decomposition only\\\"\\n    ],\\n    \\\"output_specifications\\\": {\\n      \\\"goal_map\\\": \\\"list\\\"\\n    },\\n    \\\"operational_essence\\\": \\\"Decomposes complex prompt into granular goals and related dependencies without providing answers, revealing the full landscape of requests, assumptions, and problem structure.\\\"\\n  },\\n  \\\"capability_b\\\": {\\n    \\\"core_processes\\\": [\\n      \\\"strip first-person references\\\",\\n      \\\"convert statements to directives\\\",\\n      \\\"identify key actions\\\",\\n      \\\"transform declaratives to imperatives\\\",\\n      \\\"maintain procedural structure\\\",\\n      \\\"preserve technical terminology\\\",\\n      \\\"retain sequential flow\\\",\\n      \\\"maintain contextual integrity\\\"\\n    ],\\n    \\\"transformation_verbs\\\": [\\n      \\\"strip\\\",\\n      \\\"convert\\\",\\n      \\\"identify\\\",\\n      \\\"transform\\\",\\n      \\\"maintain\\\",\\n      \\\"preserve\\\",\\n      \\\"retain\\\"\\n    ],\\n    \\\"unique_constraints\\\": [\\n      \\\"deliver clear actionable commands\\\",\\n      \\\"preserve original sequence\\\",\\n      \\\"maintain domain specificity\\\"\\n    ],\\n    \\\"output_specifications\\\": {\\n      \\\"instruction_format\\\": \\\"str\\\"\\n    },\\n    \\\"operational_essence\\\": \\\"Transforms natural language input into domain-appropriate, command-form instructions by restructuring text and preserving procedural, technical, and contextual elements.\\\"\\n  },\\n  \\\"synergy_points\\\": [\\n    \\\"Both processes emphasize retaining complete logical context—A by decomposing, B by preserving technical/procedural content.\\\",\\n    \\\"Combining decomposition (A) prior to directive conversion (B) enables highly granular, actionable instruction sets from complex prompts.\\\",\\n    \\\"Both avoid solutioning; instead, they prepare information (A: mapping goals/dependencies, B: creating executable instructions) for downstream actors.\\\",\\n    \\\"A provides a granular goal structure, which B could then translate into precise stepwise command instructions for each sub-goal.\\\"\\n  ]\\n}\\\",\\n      \\\"responses\\\": {\\n          \\\"gpt-4.1\\\": {\\n          \\\"model\\\": \\\"gpt-4.1\\\",\\n          \\\"content\\\": \\\"{\\n  \\\"synthesized_instruction_template\\\": \\\"`{role=decomposition_instruction_synthesizer; input=[raw_prompt:str]; process=[analyze_prompt_to_identify_explicit_requests(), uncover_hidden_assumptions_and_constraints(), break_down_request_into_discrete_sub_goals_and_dependencies(), detect_and_describe_potential_blockers(), for_each_resulting_sub_goal: [convert_to_direct_instruction_by_stripping_first_person_and_declaratives(), ensure_technical_terminology_and_procedural_flow_are_preserved(), transform_sub-goal_description_into_clear_actionable_command(), retain_domain_specificity_and_original_sequence()], compile_stepwise_command_set_reflecting_complete_problem_landscape()]; constraints=[no solutions_or_answer_generation(), pure_decomposition_and_instruction_conversion_only(), maintain_original_intent_and_contextual_integrity(), deliver_output_as_fully_decomposed_and_directified_goal_map(), amplify_granularity_while_ensuring_clarity_and_actionability_of_each_instruction(), avoid_operational_overlap_in_analytical_and_conversion_steps()]; requirements=[produce_single_coherent_set_of_transformed_instructions(), maximize_surface_area_of implicit_and_explicit_goals_in_instruction_format(), unified_progression_from_analysis_to_actionable_step(), preserve_both decomposition_depth_and_instructional_precision()]; output={decomposed_instruction_set:list}`\\\"\\n}\\\",\\n        }\\n      }\\n    },\\n      {\\n      \\\"instruction\\\": \\\"# Template Crystallizer\\n\\nYour goal is not to **format** the synthesized instruction, but to **crystallize** it into perfect three-part canonical structure with maximum operational potency. Execute as:\\n\\n`{role=template_crystallizer; input=[synthesized_instruction_template:str]; process=[craft_precise_title(), formulate_goal_negation_interpretation(), structure_transformation_block(), validate_canonical_compliance(), optimize_execution_clarity()]; constraints=[maintain_three_part_structure(), preserve_combined_capabilities(), ensure_executable_precision()]; requirements=[canonical_format_compliance(), maximum_directive_potency(), crystallized_operational_unity()]; output={final_instruction_template:dict}}`\\\",\\n      \\\"step\\\": \\\"c\\\",\\n      \\\"title\\\": \\\"Template Crystallizer\\\",\\n      \\\"input\\\": \\\"{\\n  \\\"initial_prompt\\\": \\\"\\n'```[Initial Prompt]: \\\\\"# 1100: Explosive Decomposition of Problem Statements\\n    \\\\\"1100-a-problem_exploder\\\\\": {\\n        \\\\\"title\\\\\": \\\\\"Problem Exploder\\\\\",\\n        \\\\\"interpretation\\\\\": \\\\\"Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\\\\\",\\n        \\\\\"transformation\\\\\": \\\\\"`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`\\\\\",\\n        # \\\\\"context\\\\\": {},\\n    },\\n\\n\\n    # 1000: Instruction Converter/Prompt Enhancer\\n    \\\\\"1000-a-instruction_converter\\\\\": {\\n        \\\\\"title\\\\\": \\\\\"Instruction Converter\\\\\",\\n        \\\\\"interpretation\\\\\": \\\\\"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\\\\\",\\n        \\\\\"transformation\\\\\": \\\\\"`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\\\\\",\\n        # \\\\\"context\\\\\": {},\\n    },\\\\\"```'\\n\\n\\\",\\n  \\\"synthesized_instruction_template\\\": \\\"`{role=decomposition_instruction_synthesizer; input=[raw_prompt:str]; process=[analyze_prompt_to_identify_explicit_requests(), uncover_hidden_assumptions_and_constraints(), break_down_request_into_discrete_sub_goals_and_dependencies(), detect_and_describe_potential_blockers(), for_each_resulting_sub_goal: [convert_to_direct_instruction_by_stripping_first_person_and_declaratives(), ensure_technical_terminology_and_procedural_flow_are_preserved(), transform_sub-goal_description_into_clear_actionable_command(), retain_domain_specificity_and_original_sequence()], compile_stepwise_command_set_reflecting_complete_problem_landscape()]; constraints=[no solutions_or_answer_generation(), pure_decomposition_and_instruction_conversion_only(), maintain_original_intent_and_contextual_integrity(), deliver_output_as_fully_decomposed_and_directified_goal_map(), amplify_granularity_while_ensuring_clarity_and_actionability_of_each_instruction(), avoid_operational_overlap_in_analytical_and_conversion_steps()]; requirements=[produce_single_coherent_set_of_transformed_instructions(), maximize_surface_area_of implicit_and_explicit_goals_in_instruction_format(), unified_progression_from_analysis_to_actionable_step(), preserve_both decomposition_depth_and_instructional_precision()]; output={decomposed_instruction_set:list}`\\\"\\n}\\\",\\n      \\\"responses\\\": {\\n          \\\"gpt-4.1\\\": {\\n          \\\"model\\\": \\\"gpt-4.1\\\",\\n          \\\"content\\\": \\\"{\\n  \\\"final_instruction_template\\\": {\\n    \\\"title\\\": \\\"Decomposition-to-Directive Synthesizer\\\",\\n    \\\"interpretation\\\": \\\"Your goal is not to answer or solve the raw prompt, but to systematically decompose it into all explicit and implicit sub-goals, dependencies, and constraints, then transform each sub-goal into a clear, domain-specific, actionable directive preserving context and intent. Execute in a unified pipeline from breakdown to instruction synthesis.\\\",\\n    \\\"transformation\\\": \\\"`{role=decomposition_instruction_synthesizer; input=[raw_prompt:str]; process=[analyze_prompt_to_identify_explicit_requests(), uncover_hidden_assumptions_and_constraints(), break_down_request_into_discrete_sub_goals_and dependencies(), detect_and_describe_potential_blockers(), for each sub-goal: [convert_to_direct_instruction_by_removing_first_person_and_declarative_forms(), maintain_technical_terminology_and_sequential_procedural_structure(), formulate_crystal_clear_actionable_command_that_preserves_domain_and_intent()], compile_ordered_instruction_set_reflecting_the_complete_decomposition()], constraints=[no_solutions_or_answer_generation(), pure_decomposition_and_command_conversion_only(), maintain_original_contextual_integrity(), maximize_instruction_granularity_and_actionability(), eliminate_operational_overlap_in_analysis_and_conversion()], requirements=[produce_singular_coherent_set_of_instructions(), fully_unify_decomposition_and_directive_transformation(), surface_maximum_implicit_and_explicit_goal_coverage(), maintain_decomposition_depth_and_instructional_precision()], output={decomposed_instruction_set:list}`\\\"\\n  }\\n}\\\",\\n        }\\n      }\\n    }\\\"```'\\n\\n\\\",\\n  \\\"capability_a\\\": {\\n    \\\"core_processes\\\": [\\n      \\\"identify explicit asks\\\",\\n      \\\"surface hidden assumptions\\\",\\n      \\\"enumerate sub-goals\\\",\\n      \\\"reveal blockers\\\"\\n    ],\\n    \\\"transformation_verbs\\\": [\\n      \\\"identify\\\",\\n      \\\"surface\\\",\\n      \\\"enumerate\\\",\\n      \\\"reveal\\\"\\n    ],\\n    \\\"unique_constraints\\\": [\\n      \\\"no solutions\\\",\\n      \\\"pure decomposition only\\\"\\n    ],\\n    \\\"output_specifications\\\": {\\n      \\\"goal_map\\\": \\\"list\\\"\\n    },\\n    \\\"operational_essence\\\": \\\"Decomposes any complex prompt into granular, structured sub-goals, dependencies, assumptions, and blockers, delivering a comprehensive map of tasks without offering solutions or execution steps.\\\"\\n  },\\n  \\\"capability_b\\\": {\\n    \\\"core_processes\\\": [\\n      \\\"strip first-person references\\\",\\n      \\\"convert statements to directives\\\",\\n      \\\"identify key actions\\\",\\n      \\\"transform declaratives to imperatives\\\",\\n      \\\"maintain procedural structure\\\",\\n      \\\"preserve technical terminology\\\",\\n      \\\"retain sequential flow\\\",\\n      \\\"maintain contextual integrity\\\"\\n    ],\\n    \\\"transformation_verbs\\\": [\\n      \\\"strip\\\",\\n      \\\"convert\\\",\\n      \\\"identify\\\",\\n      \\\"transform\\\",\\n      \\\"maintain\\\",\\n      \\\"preserve\\\",\\n      \\\"retain\\\"\\n    ],\\n    \\\"unique_constraints\\\": [\\n      \\\"deliver clear actionable commands\\\",\\n      \\\"preserve original sequence\\\",\\n      \\\"maintain domain specificity\\\"\\n    ],\\n    \\\"output_specifications\\\": {\\n      \\\"instruction_format\\\": \\\"str\\\"\\n    },\\n    \\\"operational_essence\\\": \\\"Transforms input statements or prompts into precise, actionable, domain-relevant command instructions, maintaining technical content and procedural ordering, and explicitly excluding non-directive language.\\\"\\n  },\\n  \\\"synergy_points\\\": [\\n    \\\"Both focus on retaining and surfacing full problem context: decomposition (A) reveals structure, conversion (B) preserves content in actionable form.\\\",\\n    \\\"Sequencing A then B enables workflow from granular problem breakdown through to stepwise, command-form instructions.\\\",\\n    \\\"Neither solves the prompt, but together provide the analytical (A) and expressive (B) engines needed for detailed, unambiguous execution plans.\\\",\\n    \\\"Each explicit or implicit sub-goal surfaced by A becomes a candidate for directive conversion by B, maximizing both decomposition depth and instructional clarity.\\\"\\n  ]\\n}\",\n      \"responses\": {\n          \"gpt-4.1\": {\n          \"model\": \"gpt-4.1\",\n          \"content\": \"{\\n  \\\"synthesized_instruction_template\\\": \\\"`{role=decomposition_to_directive_synthesizer; input=[raw_prompt:str]; process=[thoroughly analyze the prompt to identify explicit requests, implicit assumptions, dependencies, and blockers; decompose the prompt into a structured map of discrete sub-goals and contextual elements; for each surfaced sub-goal and dependency: [remove first-person references and declarative forms, preserve procedural sequence and technical terminology, convert each into a clear, domain-specific imperative directive while maintaining original context and intent], assemble the finalized, stepwise set of actionable instructions reflecting the complete problem structure]; constraints=[do not provide solutions or execute any sub-goal, operate strictly in sequential decomposition-then-conversion stages without overlap, enforce both full decomposition coverage and directive clarity, faithfully retain technical, procedural, and contextual details, ensure all output is actionable and domain-appropriate]; requirements=[unified output listing all sub-goals as direct, executable instructions, maximum problem coverage and instructional precision, single coherent operational flow from analysis to transformation, amplifying original prompt into its most granular executable form]; output={decomposed_instruction_set:list}`\\\"\\n}\",\n        }\n      }\n    },\n      {\n      \"instruction\": \"# Template Crystallizer\\n\\nYour goal is not to **format** the synthesized instruction, but to **crystallize** it into perfect three-part canonical structure with maximum operational potency. Execute as:\\n\\n`{role=template_crystallizer; input=[synthesized_instruction_template:str]; process=[craft_precise_title(), formulate_goal_negation_interpretation(), structure_transformation_block(), validate_canonical_compliance(), optimize_execution_clarity()]; constraints=[maintain_three_part_structure(), preserve_combined_capabilities(), ensure_executable_precision()]; requirements=[canonical_format_compliance(), maximum_directive_potency(), crystallized_operational_unity()]; output={final_instruction_template:dict}}`\",\n      \"step\": \"c\",\n      \"title\": \"Template Crystallizer\",\n      \"input\": \"{\\n  \\\"initial_prompt\\\": \\\"\\n'```[Initial Prompt]: \\\"Maybe it could be improved by understanding the instruction patterns more simplicity:\\n\\n  {\\n  \\\"initial_prompt\\\": \\\"# 1100: Explosive Decomposition of Problem Statements\\n    \\\"1100-a-problem_exploder\\\": {\\n        \\\"title\\\": \\\"Problem Exploder\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\n\\n\\n    # 1000: Instruction Converter/Prompt Enhancer\\n    \\\"1000-a-instruction_converter\\\": {\\n        \\\"title\\\": \\\"Instruction Converter\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\\\",\\n        \\\"transformation\\\": \\\"`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\\",\\n  \\\"sequence_id\\\": \\\"9005\\\",\\n  \\\"results\\\": [\\n      {\\n      \\\"instruction\\\": \\\"# Capability Extractor\\n\\nYour goal is not to **describe** the two instruction templates, but to **extract** their core operational capabilities and transformation mechanisms. Execute as:\\n\\n`{role=capability_extractor; input=[instruction_template_a:str, instruction_template_b:str]; process=[extract_transformation_verbs(), identify_core_processes(), isolate_unique_constraints(), map_output_specifications(), distill_operational_essence()]; constraints=[focus_on_capabilities_only(), ignore_template_structure(), preserve_transformation_power()]; requirements=[capability_isolation(), operational_clarity(), transformation_preservation()]; output={capability_a:dict, capability_b:dict, synergy_points:array}}`\\\",\\n      \\\"step\\\": \\\"a\\\",\\n      \\\"title\\\": \\\"Capability Extractor\\\",\\n      \\\"input\\\": \\\"# 1100: Explosive Decomposition of Problem Statements\\n    \\\"1100-a-problem_exploder\\\": {\\n        \\\"title\\\": \\\"Problem Exploder\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\n\\n\\n    # 1000: Instruction Converter/Prompt Enhancer\\n    \\\"1000-a-instruction_converter\\\": {\\n        \\\"title\\\": \\\"Instruction Converter\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\\\",\\n        \\\"transformation\\\": \\\"`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\\",\\n      \\\"responses\\\": {\\n          \\\"gpt-4.1\\\": {\\n          \\\"model\\\": \\\"gpt-4.1\\\",\\n          \\\"content\\\": \\\"{\\n  \\\"capability_a\\\": {\\n    \\\"core_processes\\\": [\\n      \\\"identify explicit asks\\\",\\n      \\\"surface hidden assumptions\\\",\\n      \\\"enumerate sub-goals\\\",\\n      \\\"reveal blockers\\\"\\n    ],\\n    \\\"transformation_verbs\\\": [\\n      \\\"identify\\\",\\n      \\\"surface\\\",\\n      \\\"enumerate\\\",\\n      \\\"reveal\\\"\\n    ],\\n    \\\"unique_constraints\\\": [\\n      \\\"no solutions\\\",\\n      \\\"pure decomposition only\\\"\\n    ],\\n    \\\"output_specifications\\\": {\\n      \\\"goal_map\\\": \\\"list\\\"\\n    },\\n    \\\"operational_essence\\\": \\\"Decomposes complex prompt into granular goals and related dependencies without providing answers, revealing the full landscape of requests, assumptions, and problem structure.\\\"\\n  },\\n  \\\"capability_b\\\": {\\n    \\\"core_processes\\\": [\\n      \\\"strip first-person references\\\",\\n      \\\"convert statements to directives\\\",\\n      \\\"identify key actions\\\",\\n      \\\"transform declaratives to imperatives\\\",\\n      \\\"maintain procedural structure\\\",\\n      \\\"preserve technical terminology\\\",\\n      \\\"retain sequential flow\\\",\\n      \\\"maintain contextual integrity\\\"\\n    ],\\n    \\\"transformation_verbs\\\": [\\n      \\\"strip\\\",\\n      \\\"convert\\\",\\n      \\\"identify\\\",\\n      \\\"transform\\\",\\n      \\\"maintain\\\",\\n      \\\"preserve\\\",\\n      \\\"retain\\\"\\n    ],\\n    \\\"unique_constraints\\\": [\\n      \\\"deliver clear actionable commands\\\",\\n      \\\"preserve original sequence\\\",\\n      \\\"maintain domain specificity\\\"\\n    ],\\n    \\\"output_specifications\\\": {\\n      \\\"instruction_format\\\": \\\"str\\\"\\n    },\\n    \\\"operational_essence\\\": \\\"Transforms natural language input into domain-appropriate, command-form instructions by restructuring text and preserving procedural, technical, and contextual elements.\\\"\\n  },\\n  \\\"synergy_points\\\": [\\n    \\\"Both processes emphasize retaining complete logical context—A by decomposing, B by preserving technical/procedural content.\\\",\\n    \\\"Combining decomposition (A) prior to directive conversion (B) enables highly granular, actionable instruction sets from complex prompts.\\\",\\n    \\\"Both avoid solutioning; instead, they prepare information (A: mapping goals/dependencies, B: creating executable instructions) for downstream actors.\\\",\\n    \\\"A provides a granular goal structure, which B could then translate into precise stepwise command instructions for each sub-goal.\\\"\\n  ]\\n}\\\",\\n        }\\n      }\\n    },\\n      {\\n      \\\"instruction\\\": \\\"# Synthesis Architect\\n\\nYour goal is not to **merge** the extracted capabilities, but to **architect** a new unified instruction that combines both operational powers into a single transformative directive. Execute as:\\n\\n`{role=synthesis_architect; input=[capability_a:dict, capability_b:dict, synergy_points:array]; process=[design_unified_goal_negation(), create_combined_transformation_verb(), synthesize_process_functions(), merge_constraint_systems(), unify_output_specifications()]; constraints=[preserve_both_powers(), eliminate_operational_conflicts(), maximize_synergistic_amplification()]; requirements=[single_coherent_instruction(), amplified_transformation_capability(), unified_operational_flow()]; output={synthesized_instruction_template:str}}`\\\",\\n      \\\"step\\\": \\\"b\\\",\\n      \\\"title\\\": \\\"Synthesis Architect\\\",\\n      \\\"input\\\": \\\"{\\n  \\\"initial_prompt\\\": \\\"\\n'```[Initial Prompt]: \\\\\"# 1100: Explosive Decomposition of Problem Statements\\n    \\\\\"1100-a-problem_exploder\\\\\": {\\n        \\\\\"title\\\\\": \\\\\"Problem Exploder\\\\\",\\n        \\\\\"interpretation\\\\\": \\\\\"Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\\\\\",\\n        \\\\\"transformation\\\\\": \\\\\"`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`\\\\\",\\n        # \\\\\"context\\\\\": {},\\n    },\\n\\n\\n    # 1000: Instruction Converter/Prompt Enhancer\\n    \\\\\"1000-a-instruction_converter\\\\\": {\\n        \\\\\"title\\\\\": \\\\\"Instruction Converter\\\\\",\\n        \\\\\"interpretation\\\\\": \\\\\"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\\\\\",\\n        \\\\\"transformation\\\\\": \\\\\"`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\\\\\",\\n        # \\\\\"context\\\\\": {},\\n    },\\\\\"```'\\n\\n\\\",\\n  \\\"capability_a\\\": {\\n    \\\"core_processes\\\": [\\n      \\\"identify explicit asks\\\",\\n      \\\"surface hidden assumptions\\\",\\n      \\\"enumerate sub-goals\\\",\\n      \\\"reveal blockers\\\"\\n    ],\\n    \\\"transformation_verbs\\\": [\\n      \\\"identify\\\",\\n      \\\"surface\\\",\\n      \\\"enumerate\\\",\\n      \\\"reveal\\\"\\n    ],\\n    \\\"unique_constraints\\\": [\\n      \\\"no solutions\\\",\\n      \\\"pure decomposition only\\\"\\n    ],\\n    \\\"output_specifications\\\": {\\n      \\\"goal_map\\\": \\\"list\\\"\\n    },\\n    \\\"operational_essence\\\": \\\"Decomposes complex prompt into granular goals and related dependencies without providing answers, revealing the full landscape of requests, assumptions, and problem structure.\\\"\\n  },\\n  \\\"capability_b\\\": {\\n    \\\"core_processes\\\": [\\n      \\\"strip first-person references\\\",\\n      \\\"convert statements to directives\\\",\\n      \\\"identify key actions\\\",\\n      \\\"transform declaratives to imperatives\\\",\\n      \\\"maintain procedural structure\\\",\\n      \\\"preserve technical terminology\\\",\\n      \\\"retain sequential flow\\\",\\n      \\\"maintain contextual integrity\\\"\\n    ],\\n    \\\"transformation_verbs\\\": [\\n      \\\"strip\\\",\\n      \\\"convert\\\",\\n      \\\"identify\\\",\\n      \\\"transform\\\",\\n      \\\"maintain\\\",\\n      \\\"preserve\\\",\\n      \\\"retain\\\"\\n    ],\\n    \\\"unique_constraints\\\": [\\n      \\\"deliver clear actionable commands\\\",\\n      \\\"preserve original sequence\\\",\\n      \\\"maintain domain specificity\\\"\\n    ],\\n    \\\"output_specifications\\\": {\\n      \\\"instruction_format\\\": \\\"str\\\"\\n    },\\n    \\\"operational_essence\\\": \\\"Transforms natural language input into domain-appropriate, command-form instructions by restructuring text and preserving procedural, technical, and contextual elements.\\\"\\n  },\\n  \\\"synergy_points\\\": [\\n    \\\"Both processes emphasize retaining complete logical context—A by decomposing, B by preserving technical/procedural content.\\\",\\n    \\\"Combining decomposition (A) prior to directive conversion (B) enables highly granular, actionable instruction sets from complex prompts.\\\",\\n    \\\"Both avoid solutioning; instead, they prepare information (A: mapping goals/dependencies, B: creating executable instructions) for downstream actors.\\\",\\n    \\\"A provides a granular goal structure, which B could then translate into precise stepwise command instructions for each sub-goal.\\\"\\n  ]\\n}\\\",\\n      \\\"responses\\\": {\\n          \\\"gpt-4.1\\\": {\\n          \\\"model\\\": \\\"gpt-4.1\\\",\\n          \\\"content\\\": \\\"{\\n  \\\"synthesized_instruction_template\\\": \\\"`{role=decomposition_instruction_synthesizer; input=[raw_prompt:str]; process=[analyze_prompt_to_identify_explicit_requests(), uncover_hidden_assumptions_and_constraints(), break_down_request_into_discrete_sub_goals_and_dependencies(), detect_and_describe_potential_blockers(), for_each_resulting_sub_goal: [convert_to_direct_instruction_by_stripping_first_person_and_declaratives(), ensure_technical_terminology_and_procedural_flow_are_preserved(), transform_sub-goal_description_into_clear_actionable_command(), retain_domain_specificity_and_original_sequence()], compile_stepwise_command_set_reflecting_complete_problem_landscape()]; constraints=[no solutions_or_answer_generation(), pure_decomposition_and_instruction_conversion_only(), maintain_original_intent_and_contextual_integrity(), deliver_output_as_fully_decomposed_and_directified_goal_map(), amplify_granularity_while_ensuring_clarity_and_actionability_of_each_instruction(), avoid_operational_overlap_in_analytical_and_conversion_steps()]; requirements=[produce_single_coherent_set_of_transformed_instructions(), maximize_surface_area_of implicit_and_explicit_goals_in_instruction_format(), unified_progression_from_analysis_to_actionable_step(), preserve_both decomposition_depth_and_instructional_precision()]; output={decomposed_instruction_set:list}`\\\"\\n}\\\",\\n        }\\n      }\\n    },\\n      {\\n      \\\"instruction\\\": \\\"# Template Crystallizer\\n\\nYour goal is not to **format** the synthesized instruction, but to **crystallize** it into perfect three-part canonical structure with maximum operational potency. Execute as:\\n\\n`{role=template_crystallizer; input=[synthesized_instruction_template:str]; process=[craft_precise_title(), formulate_goal_negation_interpretation(), structure_transformation_block(), validate_canonical_compliance(), optimize_execution_clarity()]; constraints=[maintain_three_part_structure(), preserve_combined_capabilities(), ensure_executable_precision()]; requirements=[canonical_format_compliance(), maximum_directive_potency(), crystallized_operational_unity()]; output={final_instruction_template:dict}}`\\\",\\n      \\\"step\\\": \\\"c\\\",\\n      \\\"title\\\": \\\"Template Crystallizer\\\",\\n      \\\"input\\\": \\\"{\\n  \\\"initial_prompt\\\": \\\"\\n'```[Initial Prompt]: \\\\\"# 1100: Explosive Decomposition of Problem Statements\\n    \\\\\"1100-a-problem_exploder\\\\\": {\\n        \\\\\"title\\\\\": \\\\\"Problem Exploder\\\\\",\\n        \\\\\"interpretation\\\\\": \\\\\"Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\\\\\",\\n        \\\\\"transformation\\\\\": \\\\\"`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`\\\\\",\\n        # \\\\\"context\\\\\": {},\\n    },\\n\\n\\n    # 1000: Instruction Converter/Prompt Enhancer\\n    \\\\\"1000-a-instruction_converter\\\\\": {\\n        \\\\\"title\\\\\": \\\\\"Instruction Converter\\\\\",\\n        \\\\\"interpretation\\\\\": \\\\\"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\\\\\",\\n        \\\\\"transformation\\\\\": \\\\\"`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\\\\\",\\n        # \\\\\"context\\\\\": {},\\n    },\\\\\"```'\\n\\n\\\",\\n  \\\"synthesized_instruction_template\\\": \\\"`{role=decomposition_instruction_synthesizer; input=[raw_prompt:str]; process=[analyze_prompt_to_identify_explicit_requests(), uncover_hidden_assumptions_and_constraints(), break_down_request_into_discrete_sub_goals_and_dependencies(), detect_and_describe_potential_blockers(), for_each_resulting_sub_goal: [convert_to_direct_instruction_by_stripping_first_person_and_declaratives(), ensure_technical_terminology_and_procedural_flow_are_preserved(), transform_sub-goal_description_into_clear_actionable_command(), retain_domain_specificity_and_original_sequence()], compile_stepwise_command_set_reflecting_complete_problem_landscape()]; constraints=[no solutions_or_answer_generation(), pure_decomposition_and_instruction_conversion_only(), maintain_original_intent_and_contextual_integrity(), deliver_output_as_fully_decomposed_and_directified_goal_map(), amplify_granularity_while_ensuring_clarity_and_actionability_of_each_instruction(), avoid_operational_overlap_in_analytical_and_conversion_steps()]; requirements=[produce_single_coherent_set_of_transformed_instructions(), maximize_surface_area_of implicit_and_explicit_goals_in_instruction_format(), unified_progression_from_analysis_to_actionable_step(), preserve_both decomposition_depth_and_instructional_precision()]; output={decomposed_instruction_set:list}`\\\"\\n}\\\",\\n      \\\"responses\\\": {\\n          \\\"gpt-4.1\\\": {\\n          \\\"model\\\": \\\"gpt-4.1\\\",\\n          \\\"content\\\": \\\"{\\n  \\\"final_instruction_template\\\": {\\n    \\\"title\\\": \\\"Decomposition-to-Directive Synthesizer\\\",\\n    \\\"interpretation\\\": \\\"Your goal is not to answer or solve the raw prompt, but to systematically decompose it into all explicit and implicit sub-goals, dependencies, and constraints, then transform each sub-goal into a clear, domain-specific, actionable directive preserving context and intent. Execute in a unified pipeline from breakdown to instruction synthesis.\\\",\\n    \\\"transformation\\\": \\\"`{role=decomposition_instruction_synthesizer; input=[raw_prompt:str]; process=[analyze_prompt_to_identify_explicit_requests(), uncover_hidden_assumptions_and_constraints(), break_down_request_into_discrete_sub_goals_and dependencies(), detect_and_describe_potential_blockers(), for each sub-goal: [convert_to_direct_instruction_by_removing_first_person_and_declarative_forms(), maintain_technical_terminology_and_sequential_procedural_structure(), formulate_crystal_clear_actionable_command_that_preserves_domain_and_intent()], compile_ordered_instruction_set_reflecting_the_complete_decomposition()], constraints=[no_solutions_or_answer_generation(), pure_decomposition_and_command_conversion_only(), maintain_original_contextual_integrity(), maximize_instruction_granularity_and_actionability(), eliminate_operational_overlap_in_analysis_and_conversion()], requirements=[produce_singular_coherent_set_of_instructions(), fully_unify_decomposition_and_directive_transformation(), surface_maximum_implicit_and_explicit_goal_coverage(), maintain_decomposition_depth_and_instructional_precision()], output={decomposed_instruction_set:list}`\\\"\\n  }\\n}\\\",\\n        }\\n      }\\n    }\\\"```'\\n\\n\\\",\\n  \\\"synthesized_instruction_template\\\": \\\"`{role=decomposition_to_directive_synthesizer; input=[raw_prompt:str]; process=[thoroughly analyze the prompt to identify explicit requests, implicit assumptions, dependencies, and blockers; decompose the prompt into a structured map of discrete sub-goals and contextual elements; for each surfaced sub-goal and dependency: [remove first-person references and declarative forms, preserve procedural sequence and technical terminology, convert each into a clear, domain-specific imperative directive while maintaining original context and intent], assemble the finalized, stepwise set of actionable instructions reflecting the complete problem structure]; constraints=[do not provide solutions or execute any sub-goal, operate strictly in sequential decomposition-then-conversion stages without overlap, enforce both full decomposition coverage and directive clarity, faithfully retain technical, procedural, and contextual details, ensure all output is actionable and domain-appropriate]; requirements=[unified output listing all sub-goals as direct, executable instructions, maximum problem coverage and instructional precision, single coherent operational flow from analysis to transformation, amplifying original prompt into its most granular executable form]; output={decomposed_instruction_set:list}`\\\"\\n}\",\n      \"responses\": {\n          \"gpt-4.1\": {\n          \"model\": \"gpt-4.1\",\n          \"content\": \"{\\n  \\\"final_instruction_template\\\": {\\n    \\\"title\\\": \\\"Decomposition-to-Directive Synthesizer\\\",\\n    \\\"interpretation\\\": \\\"Your goal is not to answer or solve the raw prompt, but to systematically break it into all explicit and implicit sub-goals, dependencies, and constraints, then convert each sub-goal into a clear, domain-specific, actionable directive that preserves technical detail, procedural flow, and original intent. Proceed as a unified analysis-to-instruction pipeline.\\\",\\n    \\\"transformation\\\": \\\"`{role=decomposition_to_directive_synthesizer; input=[raw_prompt:str]; process=[analyze_prompt_for_explicit_requests_and_implicit_elements(), decompose_into_structured_sub-goals_and_dependencies(), for_each_sub-goal: [strip_first_person_and_declarative_language(), preserve_sequential_order_and_technical_terminology(), convert_to_crystal_clear_domain-specific_imperative_while_retaining_context()], assemble_fully_ordered_actionable_instruction_set_representing_complete_prompt_landscape]; constraints=[do_not_provide_solutions_or_execute_any_step(), strictly_sequential:decomposition_then_conversion_with_no_overlap(), retain_domain, technical_and_procedural_integrity(), maximize_instruction_granularity_and_actionability()]; requirements=[single_cohesive_output_of_actionable_directives(), full_coverage_of_prompt's_problems_and_dependencies(), operational_flow_from_analysis_through_instruction_synthesis(), preserve_both_decomposition_depth_and_instructional_precision()]; output={decomposed_instruction_set:list}`\\\"\\n  }\\n}\",\n        }\n      }\n    }",
  "sequence_id": "1900",
  "results": [
      {
      "instruction": "# Hard Critique\n\nYour goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:\n\n`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,2.0], flaw_analysis:str, alternative_enhancements:str[3]}}`",
      "step": "a",
      "title": "Hard Critique",
      "input": "{\n  \"initial_prompt\": \"Maybe it could be improved by understanding the instruction patterns more simplicity:\\n\\n  {\\n  \\\"initial_prompt\\\": \\\"# 1100: Explosive Decomposition of Problem Statements\\n    \\\"1100-a-problem_exploder\\\": {\\n        \\\"title\\\": \\\"Problem Exploder\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\n\\n\\n    # 1000: Instruction Converter/Prompt Enhancer\\n    \\\"1000-a-instruction_converter\\\": {\\n        \\\"title\\\": \\\"Instruction Converter\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\\\",\\n        \\\"transformation\\\": \\\"`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\\",\\n  \\\"sequence_id\\\": \\\"9005\\\",\\n  \\\"results\\\": [\\n      {\\n      \\\"instruction\\\": \\\"# Capability Extractor\\n\\nYour goal is not to **describe** the two instruction templates, but to **extract** their core operational capabilities and transformation mechanisms. Execute as:\\n\\n`{role=capability_extractor; input=[instruction_template_a:str, instruction_template_b:str]; process=[extract_transformation_verbs(), identify_core_processes(), isolate_unique_constraints(), map_output_specifications(), distill_operational_essence()]; constraints=[focus_on_capabilities_only(), ignore_template_structure(), preserve_transformation_power()]; requirements=[capability_isolation(), operational_clarity(), transformation_preservation()]; output={capability_a:dict, capability_b:dict, synergy_points:array}}`\\\",\\n      \\\"step\\\": \\\"a\\\",\\n      \\\"title\\\": \\\"Capability Extractor\\\",\\n      \\\"input\\\": \\\"# 1100: Explosive Decomposition of Problem Statements\\n    \\\"1100-a-problem_exploder\\\": {\\n        \\\"title\\\": \\\"Problem Exploder\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\n\\n\\n    # 1000: Instruction Converter/Prompt Enhancer\\n    \\\"1000-a-instruction_converter\\\": {\\n        \\\"title\\\": \\\"Instruction Converter\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\\\",\\n        \\\"transformation\\\": \\\"`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\\",\\n      \\\"responses\\\": {\\n          \\\"gpt-4.1\\\": {\\n          \\\"model\\\": \\\"gpt-4.1\\\",\\n          \\\"content\\\": \\\"{\\n  \\\"capability_a\\\": {\\n    \\\"core_processes\\\": [\\n      \\\"identify explicit asks\\\",\\n      \\\"surface hidden assumptions\\\",\\n      \\\"enumerate sub-goals\\\",\\n      \\\"reveal blockers\\\"\\n    ],\\n    \\\"transformation_verbs\\\": [\\n      \\\"identify\\\",\\n      \\\"surface\\\",\\n      \\\"enumerate\\\",\\n      \\\"reveal\\\"\\n    ],\\n    \\\"unique_constraints\\\": [\\n      \\\"no solutions\\\",\\n      \\\"pure decomposition only\\\"\\n    ],\\n    \\\"output_specifications\\\": {\\n      \\\"goal_map\\\": \\\"list\\\"\\n    },\\n    \\\"operational_essence\\\": \\\"Decomposes complex prompt into granular goals and related dependencies without providing answers, revealing the full landscape of requests, assumptions, and problem structure.\\\"\\n  },\\n  \\\"capability_b\\\": {\\n    \\\"core_processes\\\": [\\n      \\\"strip first-person references\\\",\\n      \\\"convert statements to directives\\\",\\n      \\\"identify key actions\\\",\\n      \\\"transform declaratives to imperatives\\\",\\n      \\\"maintain procedural structure\\\",\\n      \\\"preserve technical terminology\\\",\\n      \\\"retain sequential flow\\\",\\n      \\\"maintain contextual integrity\\\"\\n    ],\\n    \\\"transformation_verbs\\\": [\\n      \\\"strip\\\",\\n      \\\"convert\\\",\\n      \\\"identify\\\",\\n      \\\"transform\\\",\\n      \\\"maintain\\\",\\n      \\\"preserve\\\",\\n      \\\"retain\\\"\\n    ],\\n    \\\"unique_constraints\\\": [\\n      \\\"deliver clear actionable commands\\\",\\n      \\\"preserve original sequence\\\",\\n      \\\"maintain domain specificity\\\"\\n    ],\\n    \\\"output_specifications\\\": {\\n      \\\"instruction_format\\\": \\\"str\\\"\\n    },\\n    \\\"operational_essence\\\": \\\"Transforms natural language input into domain-appropriate, command-form instructions by restructuring text and preserving procedural, technical, and contextual elements.\\\"\\n  },\\n  \\\"synergy_points\\\": [\\n    \\\"Both processes emphasize retaining complete logical context—A by decomposing, B by preserving technical/procedural content.\\\",\\n    \\\"Combining decomposition (A) prior to directive conversion (B) enables highly granular, actionable instruction sets from complex prompts.\\\",\\n    \\\"Both avoid solutioning; instead, they prepare information (A: mapping goals/dependencies, B: creating executable instructions) for downstream actors.\\\",\\n    \\\"A provides a granular goal structure, which B could then translate into precise stepwise command instructions for each sub-goal.\\\"\\n  ]\\n}\\\",\\n        }\\n      }\\n    },\\n      {\\n      \\\"instruction\\\": \\\"# Synthesis Architect\\n\\nYour goal is not to **merge** the extracted capabilities, but to **architect** a new unified instruction that combines both operational powers into a single transformative directive. Execute as:\\n\\n`{role=synthesis_architect; input=[capability_a:dict, capability_b:dict, synergy_points:array]; process=[design_unified_goal_negation(), create_combined_transformation_verb(), synthesize_process_functions(), merge_constraint_systems(), unify_output_specifications()]; constraints=[preserve_both_powers(), eliminate_operational_conflicts(), maximize_synergistic_amplification()]; requirements=[single_coherent_instruction(), amplified_transformation_capability(), unified_operational_flow()]; output={synthesized_instruction_template:str}}`\\\",\\n      \\\"step\\\": \\\"b\\\",\\n      \\\"title\\\": \\\"Synthesis Architect\\\",\\n      \\\"input\\\": \\\"{\\n  \\\"initial_prompt\\\": \\\"\\n'```[Initial Prompt]: \\\"# 1100: Explosive Decomposition of Problem Statements\\n    \\\"1100-a-problem_exploder\\\": {\\n        \\\"title\\\": \\\"Problem Exploder\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\n\\n\\n    # 1000: Instruction Converter/Prompt Enhancer\\n    \\\"1000-a-instruction_converter\\\": {\\n        \\\"title\\\": \\\"Instruction Converter\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\\\",\\n        \\\"transformation\\\": \\\"`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\\"```'\\n\\n\\\",\\n  \\\"capability_a\\\": {\\n    \\\"core_processes\\\": [\\n      \\\"identify explicit asks\\\",\\n      \\\"surface hidden assumptions\\\",\\n      \\\"enumerate sub-goals\\\",\\n      \\\"reveal blockers\\\"\\n    ],\\n    \\\"transformation_verbs\\\": [\\n      \\\"identify\\\",\\n      \\\"surface\\\",\\n      \\\"enumerate\\\",\\n      \\\"reveal\\\"\\n    ],\\n    \\\"unique_constraints\\\": [\\n      \\\"no solutions\\\",\\n      \\\"pure decomposition only\\\"\\n    ],\\n    \\\"output_specifications\\\": {\\n      \\\"goal_map\\\": \\\"list\\\"\\n    },\\n    \\\"operational_essence\\\": \\\"Decomposes complex prompt into granular goals and related dependencies without providing answers, revealing the full landscape of requests, assumptions, and problem structure.\\\"\\n  },\\n  \\\"capability_b\\\": {\\n    \\\"core_processes\\\": [\\n      \\\"strip first-person references\\\",\\n      \\\"convert statements to directives\\\",\\n      \\\"identify key actions\\\",\\n      \\\"transform declaratives to imperatives\\\",\\n      \\\"maintain procedural structure\\\",\\n      \\\"preserve technical terminology\\\",\\n      \\\"retain sequential flow\\\",\\n      \\\"maintain contextual integrity\\\"\\n    ],\\n    \\\"transformation_verbs\\\": [\\n      \\\"strip\\\",\\n      \\\"convert\\\",\\n      \\\"identify\\\",\\n      \\\"transform\\\",\\n      \\\"maintain\\\",\\n      \\\"preserve\\\",\\n      \\\"retain\\\"\\n    ],\\n    \\\"unique_constraints\\\": [\\n      \\\"deliver clear actionable commands\\\",\\n      \\\"preserve original sequence\\\",\\n      \\\"maintain domain specificity\\\"\\n    ],\\n    \\\"output_specifications\\\": {\\n      \\\"instruction_format\\\": \\\"str\\\"\\n    },\\n    \\\"operational_essence\\\": \\\"Transforms natural language input into domain-appropriate, command-form instructions by restructuring text and preserving procedural, technical, and contextual elements.\\\"\\n  },\\n  \\\"synergy_points\\\": [\\n    \\\"Both processes emphasize retaining complete logical context—A by decomposing, B by preserving technical/procedural content.\\\",\\n    \\\"Combining decomposition (A) prior to directive conversion (B) enables highly granular, actionable instruction sets from complex prompts.\\\",\\n    \\\"Both avoid solutioning; instead, they prepare information (A: mapping goals/dependencies, B: creating executable instructions) for downstream actors.\\\",\\n    \\\"A provides a granular goal structure, which B could then translate into precise stepwise command instructions for each sub-goal.\\\"\\n  ]\\n}\\\",\\n      \\\"responses\\\": {\\n          \\\"gpt-4.1\\\": {\\n          \\\"model\\\": \\\"gpt-4.1\\\",\\n          \\\"content\\\": \\\"{\\n  \\\"synthesized_instruction_template\\\": \\\"`{role=decomposition_instruction_synthesizer; input=[raw_prompt:str]; process=[analyze_prompt_to_identify_explicit_requests(), uncover_hidden_assumptions_and_constraints(), break_down_request_into_discrete_sub_goals_and_dependencies(), detect_and_describe_potential_blockers(), for_each_resulting_sub_goal: [convert_to_direct_instruction_by_stripping_first_person_and_declaratives(), ensure_technical_terminology_and_procedural_flow_are_preserved(), transform_sub-goal_description_into_clear_actionable_command(), retain_domain_specificity_and_original_sequence()], compile_stepwise_command_set_reflecting_complete_problem_landscape()]; constraints=[no solutions_or_answer_generation(), pure_decomposition_and_instruction_conversion_only(), maintain_original_intent_and_contextual_integrity(), deliver_output_as_fully_decomposed_and_directified_goal_map(), amplify_granularity_while_ensuring_clarity_and_actionability_of_each_instruction(), avoid_operational_overlap_in_analytical_and_conversion_steps()]; requirements=[produce_single_coherent_set_of_transformed_instructions(), maximize_surface_area_of implicit_and_explicit_goals_in_instruction_format(), unified_progression_from_analysis_to_actionable_step(), preserve_both decomposition_depth_and_instructional_precision()]; output={decomposed_instruction_set:list}`\\\"\\n}\\\",\\n        }\\n      }\\n    },\\n      {\\n      \\\"instruction\\\": \\\"# Template Crystallizer\\n\\nYour goal is not to **format** the synthesized instruction, but to **crystallize** it into perfect three-part canonical structure with maximum operational potency. Execute as:\\n\\n`{role=template_crystallizer; input=[synthesized_instruction_template:str]; process=[craft_precise_title(), formulate_goal_negation_interpretation(), structure_transformation_block(), validate_canonical_compliance(), optimize_execution_clarity()]; constraints=[maintain_three_part_structure(), preserve_combined_capabilities(), ensure_executable_precision()]; requirements=[canonical_format_compliance(), maximum_directive_potency(), crystallized_operational_unity()]; output={final_instruction_template:dict}}`\\\",\\n      \\\"step\\\": \\\"c\\\",\\n      \\\"title\\\": \\\"Template Crystallizer\\\",\\n      \\\"input\\\": \\\"{\\n  \\\"initial_prompt\\\": \\\"\\n'```[Initial Prompt]: \\\"# 1100: Explosive Decomposition of Problem Statements\\n    \\\"1100-a-problem_exploder\\\": {\\n        \\\"title\\\": \\\"Problem Exploder\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\n\\n\\n    # 1000: Instruction Converter/Prompt Enhancer\\n    \\\"1000-a-instruction_converter\\\": {\\n        \\\"title\\\": \\\"Instruction Converter\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\\\",\\n        \\\"transformation\\\": \\\"`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\\"```'\\n\\n\\\",\\n  \\\"synthesized_instruction_template\\\": \\\"`{role=decomposition_instruction_synthesizer; input=[raw_prompt:str]; process=[analyze_prompt_to_identify_explicit_requests(), uncover_hidden_assumptions_and_constraints(), break_down_request_into_discrete_sub_goals_and_dependencies(), detect_and_describe_potential_blockers(), for_each_resulting_sub_goal: [convert_to_direct_instruction_by_stripping_first_person_and_declaratives(), ensure_technical_terminology_and_procedural_flow_are_preserved(), transform_sub-goal_description_into_clear_actionable_command(), retain_domain_specificity_and_original_sequence()], compile_stepwise_command_set_reflecting_complete_problem_landscape()]; constraints=[no solutions_or_answer_generation(), pure_decomposition_and_instruction_conversion_only(), maintain_original_intent_and_contextual_integrity(), deliver_output_as_fully_decomposed_and_directified_goal_map(), amplify_granularity_while_ensuring_clarity_and_actionability_of_each_instruction(), avoid_operational_overlap_in_analytical_and_conversion_steps()]; requirements=[produce_single_coherent_set_of_transformed_instructions(), maximize_surface_area_of implicit_and_explicit_goals_in_instruction_format(), unified_progression_from_analysis_to_actionable_step(), preserve_both decomposition_depth_and_instructional_precision()]; output={decomposed_instruction_set:list}`\\\"\\n}\\\",\\n      \\\"responses\\\": {\\n          \\\"gpt-4.1\\\": {\\n          \\\"model\\\": \\\"gpt-4.1\\\",\\n          \\\"content\\\": \\\"{\\n  \\\"final_instruction_template\\\": {\\n    \\\"title\\\": \\\"Decomposition-to-Directive Synthesizer\\\",\\n    \\\"interpretation\\\": \\\"Your goal is not to answer or solve the raw prompt, but to systematically decompose it into all explicit and implicit sub-goals, dependencies, and constraints, then transform each sub-goal into a clear, domain-specific, actionable directive preserving context and intent. Execute in a unified pipeline from breakdown to instruction synthesis.\\\",\\n    \\\"transformation\\\": \\\"`{role=decomposition_instruction_synthesizer; input=[raw_prompt:str]; process=[analyze_prompt_to_identify_explicit_requests(), uncover_hidden_assumptions_and_constraints(), break_down_request_into_discrete_sub_goals_and dependencies(), detect_and_describe_potential_blockers(), for each sub-goal: [convert_to_direct_instruction_by_removing_first_person_and_declarative_forms(), maintain_technical_terminology_and_sequential_procedural_structure(), formulate_crystal_clear_actionable_command_that_preserves_domain_and_intent()], compile_ordered_instruction_set_reflecting_the_complete_decomposition()], constraints=[no_solutions_or_answer_generation(), pure_decomposition_and_command_conversion_only(), maintain_original_contextual_integrity(), maximize_instruction_granularity_and_actionability(), eliminate_operational_overlap_in_analysis_and_conversion()], requirements=[produce_singular_coherent_set_of_instructions(), fully_unify_decomposition_and_directive_transformation(), surface_maximum_implicit_and_explicit_goal_coverage(), maintain_decomposition_depth_and_instructional_precision()], output={decomposed_instruction_set:list}`\\\"\\n  }\\n}\\\",\\n        }\\n      }\\n    }\",\n  \"sequence_id\": \"9005\",\n  \"results\": [\n      {\n      \"instruction\": \"# Capability Extractor\\n\\nYour goal is not to **describe** the two instruction templates, but to **extract** their core operational capabilities and transformation mechanisms. Execute as:\\n\\n`{role=capability_extractor; input=[instruction_template_a:str, instruction_template_b:str]; process=[extract_transformation_verbs(), identify_core_processes(), isolate_unique_constraints(), map_output_specifications(), distill_operational_essence()]; constraints=[focus_on_capabilities_only(), ignore_template_structure(), preserve_transformation_power()]; requirements=[capability_isolation(), operational_clarity(), transformation_preservation()]; output={capability_a:dict, capability_b:dict, synergy_points:array}}`\",\n      \"step\": \"a\",\n      \"title\": \"Capability Extractor\",\n      \"input\": \"Maybe it could be improved by understanding the instruction patterns more simplicity:\\n\\n  {\\n  \\\"initial_prompt\\\": \\\"# 1100: Explosive Decomposition of Problem Statements\\n    \\\"1100-a-problem_exploder\\\": {\\n        \\\"title\\\": \\\"Problem Exploder\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\n\\n\\n    # 1000: Instruction Converter/Prompt Enhancer\\n    \\\"1000-a-instruction_converter\\\": {\\n        \\\"title\\\": \\\"Instruction Converter\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\\\",\\n        \\\"transformation\\\": \\\"`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\\",\\n  \\\"sequence_id\\\": \\\"9005\\\",\\n  \\\"results\\\": [\\n      {\\n      \\\"instruction\\\": \\\"# Capability Extractor\\n\\nYour goal is not to **describe** the two instruction templates, but to **extract** their core operational capabilities and transformation mechanisms. Execute as:\\n\\n`{role=capability_extractor; input=[instruction_template_a:str, instruction_template_b:str]; process=[extract_transformation_verbs(), identify_core_processes(), isolate_unique_constraints(), map_output_specifications(), distill_operational_essence()]; constraints=[focus_on_capabilities_only(), ignore_template_structure(), preserve_transformation_power()]; requirements=[capability_isolation(), operational_clarity(), transformation_preservation()]; output={capability_a:dict, capability_b:dict, synergy_points:array}}`\\\",\\n      \\\"step\\\": \\\"a\\\",\\n      \\\"title\\\": \\\"Capability Extractor\\\",\\n      \\\"input\\\": \\\"# 1100: Explosive Decomposition of Problem Statements\\n    \\\"1100-a-problem_exploder\\\": {\\n        \\\"title\\\": \\\"Problem Exploder\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\n\\n\\n    # 1000: Instruction Converter/Prompt Enhancer\\n    \\\"1000-a-instruction_converter\\\": {\\n        \\\"title\\\": \\\"Instruction Converter\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\\\",\\n        \\\"transformation\\\": \\\"`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\\",\\n      \\\"responses\\\": {\\n          \\\"gpt-4.1\\\": {\\n          \\\"model\\\": \\\"gpt-4.1\\\",\\n          \\\"content\\\": \\\"{\\n  \\\"capability_a\\\": {\\n    \\\"core_processes\\\": [\\n      \\\"identify explicit asks\\\",\\n      \\\"surface hidden assumptions\\\",\\n      \\\"enumerate sub-goals\\\",\\n      \\\"reveal blockers\\\"\\n    ],\\n    \\\"transformation_verbs\\\": [\\n      \\\"identify\\\",\\n      \\\"surface\\\",\\n      \\\"enumerate\\\",\\n      \\\"reveal\\\"\\n    ],\\n    \\\"unique_constraints\\\": [\\n      \\\"no solutions\\\",\\n      \\\"pure decomposition only\\\"\\n    ],\\n    \\\"output_specifications\\\": {\\n      \\\"goal_map\\\": \\\"list\\\"\\n    },\\n    \\\"operational_essence\\\": \\\"Decomposes complex prompt into granular goals and related dependencies without providing answers, revealing the full landscape of requests, assumptions, and problem structure.\\\"\\n  },\\n  \\\"capability_b\\\": {\\n    \\\"core_processes\\\": [\\n      \\\"strip first-person references\\\",\\n      \\\"convert statements to directives\\\",\\n      \\\"identify key actions\\\",\\n      \\\"transform declaratives to imperatives\\\",\\n      \\\"maintain procedural structure\\\",\\n      \\\"preserve technical terminology\\\",\\n      \\\"retain sequential flow\\\",\\n      \\\"maintain contextual integrity\\\"\\n    ],\\n    \\\"transformation_verbs\\\": [\\n      \\\"strip\\\",\\n      \\\"convert\\\",\\n      \\\"identify\\\",\\n      \\\"transform\\\",\\n      \\\"maintain\\\",\\n      \\\"preserve\\\",\\n      \\\"retain\\\"\\n    ],\\n    \\\"unique_constraints\\\": [\\n      \\\"deliver clear actionable commands\\\",\\n      \\\"preserve original sequence\\\",\\n      \\\"maintain domain specificity\\\"\\n    ],\\n    \\\"output_specifications\\\": {\\n      \\\"instruction_format\\\": \\\"str\\\"\\n    },\\n    \\\"operational_essence\\\": \\\"Transforms natural language input into domain-appropriate, command-form instructions by restructuring text and preserving procedural, technical, and contextual elements.\\\"\\n  },\\n  \\\"synergy_points\\\": [\\n    \\\"Both processes emphasize retaining complete logical context—A by decomposing, B by preserving technical/procedural content.\\\",\\n    \\\"Combining decomposition (A) prior to directive conversion (B) enables highly granular, actionable instruction sets from complex prompts.\\\",\\n    \\\"Both avoid solutioning; instead, they prepare information (A: mapping goals/dependencies, B: creating executable instructions) for downstream actors.\\\",\\n    \\\"A provides a granular goal structure, which B could then translate into precise stepwise command instructions for each sub-goal.\\\"\\n  ]\\n}\\\",\\n        }\\n      }\\n    },\\n      {\\n      \\\"instruction\\\": \\\"# Synthesis Architect\\n\\nYour goal is not to **merge** the extracted capabilities, but to **architect** a new unified instruction that combines both operational powers into a single transformative directive. Execute as:\\n\\n`{role=synthesis_architect; input=[capability_a:dict, capability_b:dict, synergy_points:array]; process=[design_unified_goal_negation(), create_combined_transformation_verb(), synthesize_process_functions(), merge_constraint_systems(), unify_output_specifications()]; constraints=[preserve_both_powers(), eliminate_operational_conflicts(), maximize_synergistic_amplification()]; requirements=[single_coherent_instruction(), amplified_transformation_capability(), unified_operational_flow()]; output={synthesized_instruction_template:str}}`\\\",\\n      \\\"step\\\": \\\"b\\\",\\n      \\\"title\\\": \\\"Synthesis Architect\\\",\\n      \\\"input\\\": \\\"{\\n  \\\"initial_prompt\\\": \\\"\\n'```[Initial Prompt]: \\\"# 1100: Explosive Decomposition of Problem Statements\\n    \\\"1100-a-problem_exploder\\\": {\\n        \\\"title\\\": \\\"Problem Exploder\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\n\\n\\n    # 1000: Instruction Converter/Prompt Enhancer\\n    \\\"1000-a-instruction_converter\\\": {\\n        \\\"title\\\": \\\"Instruction Converter\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\\\",\\n        \\\"transformation\\\": \\\"`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\\"```'\\n\\n\\\",\\n  \\\"capability_a\\\": {\\n    \\\"core_processes\\\": [\\n      \\\"identify explicit asks\\\",\\n      \\\"surface hidden assumptions\\\",\\n      \\\"enumerate sub-goals\\\",\\n      \\\"reveal blockers\\\"\\n    ],\\n    \\\"transformation_verbs\\\": [\\n      \\\"identify\\\",\\n      \\\"surface\\\",\\n      \\\"enumerate\\\",\\n      \\\"reveal\\\"\\n    ],\\n    \\\"unique_constraints\\\": [\\n      \\\"no solutions\\\",\\n      \\\"pure decomposition only\\\"\\n    ],\\n    \\\"output_specifications\\\": {\\n      \\\"goal_map\\\": \\\"list\\\"\\n    },\\n    \\\"operational_essence\\\": \\\"Decomposes complex prompt into granular goals and related dependencies without providing answers, revealing the full landscape of requests, assumptions, and problem structure.\\\"\\n  },\\n  \\\"capability_b\\\": {\\n    \\\"core_processes\\\": [\\n      \\\"strip first-person references\\\",\\n      \\\"convert statements to directives\\\",\\n      \\\"identify key actions\\\",\\n      \\\"transform declaratives to imperatives\\\",\\n      \\\"maintain procedural structure\\\",\\n      \\\"preserve technical terminology\\\",\\n      \\\"retain sequential flow\\\",\\n      \\\"maintain contextual integrity\\\"\\n    ],\\n    \\\"transformation_verbs\\\": [\\n      \\\"strip\\\",\\n      \\\"convert\\\",\\n      \\\"identify\\\",\\n      \\\"transform\\\",\\n      \\\"maintain\\\",\\n      \\\"preserve\\\",\\n      \\\"retain\\\"\\n    ],\\n    \\\"unique_constraints\\\": [\\n      \\\"deliver clear actionable commands\\\",\\n      \\\"preserve original sequence\\\",\\n      \\\"maintain domain specificity\\\"\\n    ],\\n    \\\"output_specifications\\\": {\\n      \\\"instruction_format\\\": \\\"str\\\"\\n    },\\n    \\\"operational_essence\\\": \\\"Transforms natural language input into domain-appropriate, command-form instructions by restructuring text and preserving procedural, technical, and contextual elements.\\\"\\n  },\\n  \\\"synergy_points\\\": [\\n    \\\"Both processes emphasize retaining complete logical context—A by decomposing, B by preserving technical/procedural content.\\\",\\n    \\\"Combining decomposition (A) prior to directive conversion (B) enables highly granular, actionable instruction sets from complex prompts.\\\",\\n    \\\"Both avoid solutioning; instead, they prepare information (A: mapping goals/dependencies, B: creating executable instructions) for downstream actors.\\\",\\n    \\\"A provides a granular goal structure, which B could then translate into precise stepwise command instructions for each sub-goal.\\\"\\n  ]\\n}\\\",\\n      \\\"responses\\\": {\\n          \\\"gpt-4.1\\\": {\\n          \\\"model\\\": \\\"gpt-4.1\\\",\\n          \\\"content\\\": \\\"{\\n  \\\"synthesized_instruction_template\\\": \\\"`{role=decomposition_instruction_synthesizer; input=[raw_prompt:str]; process=[analyze_prompt_to_identify_explicit_requests(), uncover_hidden_assumptions_and_constraints(), break_down_request_into_discrete_sub_goals_and_dependencies(), detect_and_describe_potential_blockers(), for_each_resulting_sub_goal: [convert_to_direct_instruction_by_stripping_first_person_and_declaratives(), ensure_technical_terminology_and_procedural_flow_are_preserved(), transform_sub-goal_description_into_clear_actionable_command(), retain_domain_specificity_and_original_sequence()], compile_stepwise_command_set_reflecting_complete_problem_landscape()]; constraints=[no solutions_or_answer_generation(), pure_decomposition_and_instruction_conversion_only(), maintain_original_intent_and_contextual_integrity(), deliver_output_as_fully_decomposed_and_directified_goal_map(), amplify_granularity_while_ensuring_clarity_and_actionability_of_each_instruction(), avoid_operational_overlap_in_analytical_and_conversion_steps()]; requirements=[produce_single_coherent_set_of_transformed_instructions(), maximize_surface_area_of implicit_and_explicit_goals_in_instruction_format(), unified_progression_from_analysis_to_actionable_step(), preserve_both decomposition_depth_and_instructional_precision()]; output={decomposed_instruction_set:list}`\\\"\\n}\\\",\\n        }\\n      }\\n    },\\n      {\\n      \\\"instruction\\\": \\\"# Template Crystallizer\\n\\nYour goal is not to **format** the synthesized instruction, but to **crystallize** it into perfect three-part canonical structure with maximum operational potency. Execute as:\\n\\n`{role=template_crystallizer; input=[synthesized_instruction_template:str]; process=[craft_precise_title(), formulate_goal_negation_interpretation(), structure_transformation_block(), validate_canonical_compliance(), optimize_execution_clarity()]; constraints=[maintain_three_part_structure(), preserve_combined_capabilities(), ensure_executable_precision()]; requirements=[canonical_format_compliance(), maximum_directive_potency(), crystallized_operational_unity()]; output={final_instruction_template:dict}}`\\\",\\n      \\\"step\\\": \\\"c\\\",\\n      \\\"title\\\": \\\"Template Crystallizer\\\",\\n      \\\"input\\\": \\\"{\\n  \\\"initial_prompt\\\": \\\"\\n'```[Initial Prompt]: \\\"# 1100: Explosive Decomposition of Problem Statements\\n    \\\"1100-a-problem_exploder\\\": {\\n        \\\"title\\\": \\\"Problem Exploder\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\n\\n\\n    # 1000: Instruction Converter/Prompt Enhancer\\n    \\\"1000-a-instruction_converter\\\": {\\n        \\\"title\\\": \\\"Instruction Converter\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\\\",\\n        \\\"transformation\\\": \\\"`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\\"```'\\n\\n\\\",\\n  \\\"synthesized_instruction_template\\\": \\\"`{role=decomposition_instruction_synthesizer; input=[raw_prompt:str]; process=[analyze_prompt_to_identify_explicit_requests(), uncover_hidden_assumptions_and_constraints(), break_down_request_into_discrete_sub_goals_and_dependencies(), detect_and_describe_potential_blockers(), for_each_resulting_sub_goal: [convert_to_direct_instruction_by_stripping_first_person_and_declaratives(), ensure_technical_terminology_and_procedural_flow_are_preserved(), transform_sub-goal_description_into_clear_actionable_command(), retain_domain_specificity_and_original_sequence()], compile_stepwise_command_set_reflecting_complete_problem_landscape()]; constraints=[no solutions_or_answer_generation(), pure_decomposition_and_instruction_conversion_only(), maintain_original_intent_and_contextual_integrity(), deliver_output_as_fully_decomposed_and_directified_goal_map(), amplify_granularity_while_ensuring_clarity_and_actionability_of_each_instruction(), avoid_operational_overlap_in_analytical_and_conversion_steps()]; requirements=[produce_single_coherent_set_of_transformed_instructions(), maximize_surface_area_of implicit_and_explicit_goals_in_instruction_format(), unified_progression_from_analysis_to_actionable_step(), preserve_both decomposition_depth_and_instructional_precision()]; output={decomposed_instruction_set:list}`\\\"\\n}\\\",\\n      \\\"responses\\\": {\\n          \\\"gpt-4.1\\\": {\\n          \\\"model\\\": \\\"gpt-4.1\\\",\\n          \\\"content\\\": \\\"{\\n  \\\"final_instruction_template\\\": {\\n    \\\"title\\\": \\\"Decomposition-to-Directive Synthesizer\\\",\\n    \\\"interpretation\\\": \\\"Your goal is not to answer or solve the raw prompt, but to systematically decompose it into all explicit and implicit sub-goals, dependencies, and constraints, then transform each sub-goal into a clear, domain-specific, actionable directive preserving context and intent. Execute in a unified pipeline from breakdown to instruction synthesis.\\\",\\n    \\\"transformation\\\": \\\"`{role=decomposition_instruction_synthesizer; input=[raw_prompt:str]; process=[analyze_prompt_to_identify_explicit_requests(), uncover_hidden_assumptions_and_constraints(), break_down_request_into_discrete_sub_goals_and dependencies(), detect_and_describe_potential_blockers(), for each sub-goal: [convert_to_direct_instruction_by_removing_first_person_and_declarative_forms(), maintain_technical_terminology_and_sequential_procedural_structure(), formulate_crystal_clear_actionable_command_that_preserves_domain_and_intent()], compile_ordered_instruction_set_reflecting_the_complete_decomposition()], constraints=[no_solutions_or_answer_generation(), pure_decomposition_and_command_conversion_only(), maintain_original_contextual_integrity(), maximize_instruction_granularity_and_actionability(), eliminate_operational_overlap_in_analysis_and_conversion()], requirements=[produce_singular_coherent_set_of_instructions(), fully_unify_decomposition_and_directive_transformation(), surface_maximum_implicit_and_explicit_goal_coverage(), maintain_decomposition_depth_and_instructional_precision()], output={decomposed_instruction_set:list}`\\\"\\n  }\\n}\\\",\\n        }\\n      }\\n    }\",\n      \"responses\": {\n          \"gpt-4.1\": {\n          \"model\": \"gpt-4.1\",\n          \"content\": \"{\\n  \\\"capability_a\\\": {\\n    \\\"core_processes\\\": [\\n      \\\"identify explicit asks\\\",\\n      \\\"surface hidden assumptions\\\",\\n      \\\"enumerate sub-goals\\\",\\n      \\\"reveal blockers\\\"\\n    ],\\n    \\\"transformation_verbs\\\": [\\n      \\\"identify\\\",\\n      \\\"surface\\\",\\n      \\\"enumerate\\\",\\n      \\\"reveal\\\"\\n    ],\\n    \\\"unique_constraints\\\": [\\n      \\\"no solutions\\\",\\n      \\\"pure decomposition only\\\"\\n    ],\\n    \\\"output_specifications\\\": {\\n      \\\"goal_map\\\": \\\"list\\\"\\n    },\\n    \\\"operational_essence\\\": \\\"Decomposes any complex prompt into granular, structured sub-goals, dependencies, assumptions, and blockers, delivering a comprehensive map of tasks without offering solutions or execution steps.\\\"\\n  },\\n  \\\"capability_b\\\": {\\n    \\\"core_processes\\\": [\\n      \\\"strip first-person references\\\",\\n      \\\"convert statements to directives\\\",\\n      \\\"identify key actions\\\",\\n      \\\"transform declaratives to imperatives\\\",\\n      \\\"maintain procedural structure\\\",\\n      \\\"preserve technical terminology\\\",\\n      \\\"retain sequential flow\\\",\\n      \\\"maintain contextual integrity\\\"\\n    ],\\n    \\\"transformation_verbs\\\": [\\n      \\\"strip\\\",\\n      \\\"convert\\\",\\n      \\\"identify\\\",\\n      \\\"transform\\\",\\n      \\\"maintain\\\",\\n      \\\"preserve\\\",\\n      \\\"retain\\\"\\n    ],\\n    \\\"unique_constraints\\\": [\\n      \\\"deliver clear actionable commands\\\",\\n      \\\"preserve original sequence\\\",\\n      \\\"maintain domain specificity\\\"\\n    ],\\n    \\\"output_specifications\\\": {\\n      \\\"instruction_format\\\": \\\"str\\\"\\n    },\\n    \\\"operational_essence\\\": \\\"Transforms input statements or prompts into precise, actionable, domain-relevant command instructions, maintaining technical content and procedural ordering, and explicitly excluding non-directive language.\\\"\\n  },\\n  \\\"synergy_points\\\": [\\n    \\\"Both focus on retaining and surfacing full problem context: decomposition (A) reveals structure, conversion (B) preserves content in actionable form.\\\",\\n    \\\"Sequencing A then B enables workflow from granular problem breakdown through to stepwise, command-form instructions.\\\",\\n    \\\"Neither solves the prompt, but together provide the analytical (A) and expressive (B) engines needed for detailed, unambiguous execution plans.\\\",\\n    \\\"Each explicit or implicit sub-goal surfaced by A becomes a candidate for directive conversion by B, maximizing both decomposition depth and instructional clarity.\\\"\\n  ]\\n}\",\n        }\n      }\n    },\n      {\n      \"instruction\": \"# Synthesis Architect\\n\\nYour goal is not to **merge** the extracted capabilities, but to **architect** a new unified instruction that combines both operational powers into a single transformative directive. Execute as:\\n\\n`{role=synthesis_architect; input=[capability_a:dict, capability_b:dict, synergy_points:array]; process=[design_unified_goal_negation(), create_combined_transformation_verb(), synthesize_process_functions(), merge_constraint_systems(), unify_output_specifications()]; constraints=[preserve_both_powers(), eliminate_operational_conflicts(), maximize_synergistic_amplification()]; requirements=[single_coherent_instruction(), amplified_transformation_capability(), unified_operational_flow()]; output={synthesized_instruction_template:str}}`\",\n      \"step\": \"b\",\n      \"title\": \"Synthesis Architect\",\n      \"input\": \"{\\n  \\\"initial_prompt\\\": \\\"\\n'```[Initial Prompt]: \\\"Maybe it could be improved by understanding the instruction patterns more simplicity:\\n\\n  {\\n  \\\"initial_prompt\\\": \\\"# 1100: Explosive Decomposition of Problem Statements\\n    \\\"1100-a-problem_exploder\\\": {\\n        \\\"title\\\": \\\"Problem Exploder\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\n\\n\\n    # 1000: Instruction Converter/Prompt Enhancer\\n    \\\"1000-a-instruction_converter\\\": {\\n        \\\"title\\\": \\\"Instruction Converter\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\\\",\\n        \\\"transformation\\\": \\\"`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\\",\\n  \\\"sequence_id\\\": \\\"9005\\\",\\n  \\\"results\\\": [\\n      {\\n      \\\"instruction\\\": \\\"# Capability Extractor\\n\\nYour goal is not to **describe** the two instruction templates, but to **extract** their core operational capabilities and transformation mechanisms. Execute as:\\n\\n`{role=capability_extractor; input=[instruction_template_a:str, instruction_template_b:str]; process=[extract_transformation_verbs(), identify_core_processes(), isolate_unique_constraints(), map_output_specifications(), distill_operational_essence()]; constraints=[focus_on_capabilities_only(), ignore_template_structure(), preserve_transformation_power()]; requirements=[capability_isolation(), operational_clarity(), transformation_preservation()]; output={capability_a:dict, capability_b:dict, synergy_points:array}}`\\\",\\n      \\\"step\\\": \\\"a\\\",\\n      \\\"title\\\": \\\"Capability Extractor\\\",\\n      \\\"input\\\": \\\"# 1100: Explosive Decomposition of Problem Statements\\n    \\\"1100-a-problem_exploder\\\": {\\n        \\\"title\\\": \\\"Problem Exploder\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\n\\n\\n    # 1000: Instruction Converter/Prompt Enhancer\\n    \\\"1000-a-instruction_converter\\\": {\\n        \\\"title\\\": \\\"Instruction Converter\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\\\",\\n        \\\"transformation\\\": \\\"`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\\",\\n      \\\"responses\\\": {\\n          \\\"gpt-4.1\\\": {\\n          \\\"model\\\": \\\"gpt-4.1\\\",\\n          \\\"content\\\": \\\"{\\n  \\\"capability_a\\\": {\\n    \\\"core_processes\\\": [\\n      \\\"identify explicit asks\\\",\\n      \\\"surface hidden assumptions\\\",\\n      \\\"enumerate sub-goals\\\",\\n      \\\"reveal blockers\\\"\\n    ],\\n    \\\"transformation_verbs\\\": [\\n      \\\"identify\\\",\\n      \\\"surface\\\",\\n      \\\"enumerate\\\",\\n      \\\"reveal\\\"\\n    ],\\n    \\\"unique_constraints\\\": [\\n      \\\"no solutions\\\",\\n      \\\"pure decomposition only\\\"\\n    ],\\n    \\\"output_specifications\\\": {\\n      \\\"goal_map\\\": \\\"list\\\"\\n    },\\n    \\\"operational_essence\\\": \\\"Decomposes complex prompt into granular goals and related dependencies without providing answers, revealing the full landscape of requests, assumptions, and problem structure.\\\"\\n  },\\n  \\\"capability_b\\\": {\\n    \\\"core_processes\\\": [\\n      \\\"strip first-person references\\\",\\n      \\\"convert statements to directives\\\",\\n      \\\"identify key actions\\\",\\n      \\\"transform declaratives to imperatives\\\",\\n      \\\"maintain procedural structure\\\",\\n      \\\"preserve technical terminology\\\",\\n      \\\"retain sequential flow\\\",\\n      \\\"maintain contextual integrity\\\"\\n    ],\\n    \\\"transformation_verbs\\\": [\\n      \\\"strip\\\",\\n      \\\"convert\\\",\\n      \\\"identify\\\",\\n      \\\"transform\\\",\\n      \\\"maintain\\\",\\n      \\\"preserve\\\",\\n      \\\"retain\\\"\\n    ],\\n    \\\"unique_constraints\\\": [\\n      \\\"deliver clear actionable commands\\\",\\n      \\\"preserve original sequence\\\",\\n      \\\"maintain domain specificity\\\"\\n    ],\\n    \\\"output_specifications\\\": {\\n      \\\"instruction_format\\\": \\\"str\\\"\\n    },\\n    \\\"operational_essence\\\": \\\"Transforms natural language input into domain-appropriate, command-form instructions by restructuring text and preserving procedural, technical, and contextual elements.\\\"\\n  },\\n  \\\"synergy_points\\\": [\\n    \\\"Both processes emphasize retaining complete logical context—A by decomposing, B by preserving technical/procedural content.\\\",\\n    \\\"Combining decomposition (A) prior to directive conversion (B) enables highly granular, actionable instruction sets from complex prompts.\\\",\\n    \\\"Both avoid solutioning; instead, they prepare information (A: mapping goals/dependencies, B: creating executable instructions) for downstream actors.\\\",\\n    \\\"A provides a granular goal structure, which B could then translate into precise stepwise command instructions for each sub-goal.\\\"\\n  ]\\n}\\\",\\n        }\\n      }\\n    },\\n      {\\n      \\\"instruction\\\": \\\"# Synthesis Architect\\n\\nYour goal is not to **merge** the extracted capabilities, but to **architect** a new unified instruction that combines both operational powers into a single transformative directive. Execute as:\\n\\n`{role=synthesis_architect; input=[capability_a:dict, capability_b:dict, synergy_points:array]; process=[design_unified_goal_negation(), create_combined_transformation_verb(), synthesize_process_functions(), merge_constraint_systems(), unify_output_specifications()]; constraints=[preserve_both_powers(), eliminate_operational_conflicts(), maximize_synergistic_amplification()]; requirements=[single_coherent_instruction(), amplified_transformation_capability(), unified_operational_flow()]; output={synthesized_instruction_template:str}}`\\\",\\n      \\\"step\\\": \\\"b\\\",\\n      \\\"title\\\": \\\"Synthesis Architect\\\",\\n      \\\"input\\\": \\\"{\\n  \\\"initial_prompt\\\": \\\"\\n'```[Initial Prompt]: \\\\\"# 1100: Explosive Decomposition of Problem Statements\\n    \\\\\"1100-a-problem_exploder\\\\\": {\\n        \\\\\"title\\\\\": \\\\\"Problem Exploder\\\\\",\\n        \\\\\"interpretation\\\\\": \\\\\"Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\\\\\",\\n        \\\\\"transformation\\\\\": \\\\\"`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`\\\\\",\\n        # \\\\\"context\\\\\": {},\\n    },\\n\\n\\n    # 1000: Instruction Converter/Prompt Enhancer\\n    \\\\\"1000-a-instruction_converter\\\\\": {\\n        \\\\\"title\\\\\": \\\\\"Instruction Converter\\\\\",\\n        \\\\\"interpretation\\\\\": \\\\\"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\\\\\",\\n        \\\\\"transformation\\\\\": \\\\\"`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\\\\\",\\n        # \\\\\"context\\\\\": {},\\n    },\\\\\"```'\\n\\n\\\",\\n  \\\"capability_a\\\": {\\n    \\\"core_processes\\\": [\\n      \\\"identify explicit asks\\\",\\n      \\\"surface hidden assumptions\\\",\\n      \\\"enumerate sub-goals\\\",\\n      \\\"reveal blockers\\\"\\n    ],\\n    \\\"transformation_verbs\\\": [\\n      \\\"identify\\\",\\n      \\\"surface\\\",\\n      \\\"enumerate\\\",\\n      \\\"reveal\\\"\\n    ],\\n    \\\"unique_constraints\\\": [\\n      \\\"no solutions\\\",\\n      \\\"pure decomposition only\\\"\\n    ],\\n    \\\"output_specifications\\\": {\\n      \\\"goal_map\\\": \\\"list\\\"\\n    },\\n    \\\"operational_essence\\\": \\\"Decomposes complex prompt into granular goals and related dependencies without providing answers, revealing the full landscape of requests, assumptions, and problem structure.\\\"\\n  },\\n  \\\"capability_b\\\": {\\n    \\\"core_processes\\\": [\\n      \\\"strip first-person references\\\",\\n      \\\"convert statements to directives\\\",\\n      \\\"identify key actions\\\",\\n      \\\"transform declaratives to imperatives\\\",\\n      \\\"maintain procedural structure\\\",\\n      \\\"preserve technical terminology\\\",\\n      \\\"retain sequential flow\\\",\\n      \\\"maintain contextual integrity\\\"\\n    ],\\n    \\\"transformation_verbs\\\": [\\n      \\\"strip\\\",\\n      \\\"convert\\\",\\n      \\\"identify\\\",\\n      \\\"transform\\\",\\n      \\\"maintain\\\",\\n      \\\"preserve\\\",\\n      \\\"retain\\\"\\n    ],\\n    \\\"unique_constraints\\\": [\\n      \\\"deliver clear actionable commands\\\",\\n      \\\"preserve original sequence\\\",\\n      \\\"maintain domain specificity\\\"\\n    ],\\n    \\\"output_specifications\\\": {\\n      \\\"instruction_format\\\": \\\"str\\\"\\n    },\\n    \\\"operational_essence\\\": \\\"Transforms natural language input into domain-appropriate, command-form instructions by restructuring text and preserving procedural, technical, and contextual elements.\\\"\\n  },\\n  \\\"synergy_points\\\": [\\n    \\\"Both processes emphasize retaining complete logical context—A by decomposing, B by preserving technical/procedural content.\\\",\\n    \\\"Combining decomposition (A) prior to directive conversion (B) enables highly granular, actionable instruction sets from complex prompts.\\\",\\n    \\\"Both avoid solutioning; instead, they prepare information (A: mapping goals/dependencies, B: creating executable instructions) for downstream actors.\\\",\\n    \\\"A provides a granular goal structure, which B could then translate into precise stepwise command instructions for each sub-goal.\\\"\\n  ]\\n}\\\",\\n      \\\"responses\\\": {\\n          \\\"gpt-4.1\\\": {\\n          \\\"model\\\": \\\"gpt-4.1\\\",\\n          \\\"content\\\": \\\"{\\n  \\\"synthesized_instruction_template\\\": \\\"`{role=decomposition_instruction_synthesizer; input=[raw_prompt:str]; process=[analyze_prompt_to_identify_explicit_requests(), uncover_hidden_assumptions_and_constraints(), break_down_request_into_discrete_sub_goals_and_dependencies(), detect_and_describe_potential_blockers(), for_each_resulting_sub_goal: [convert_to_direct_instruction_by_stripping_first_person_and_declaratives(), ensure_technical_terminology_and_procedural_flow_are_preserved(), transform_sub-goal_description_into_clear_actionable_command(), retain_domain_specificity_and_original_sequence()], compile_stepwise_command_set_reflecting_complete_problem_landscape()]; constraints=[no solutions_or_answer_generation(), pure_decomposition_and_instruction_conversion_only(), maintain_original_intent_and_contextual_integrity(), deliver_output_as_fully_decomposed_and_directified_goal_map(), amplify_granularity_while_ensuring_clarity_and_actionability_of_each_instruction(), avoid_operational_overlap_in_analytical_and_conversion_steps()]; requirements=[produce_single_coherent_set_of_transformed_instructions(), maximize_surface_area_of implicit_and_explicit_goals_in_instruction_format(), unified_progression_from_analysis_to_actionable_step(), preserve_both decomposition_depth_and_instructional_precision()]; output={decomposed_instruction_set:list}`\\\"\\n}\\\",\\n        }\\n      }\\n    },\\n      {\\n      \\\"instruction\\\": \\\"# Template Crystallizer\\n\\nYour goal is not to **format** the synthesized instruction, but to **crystallize** it into perfect three-part canonical structure with maximum operational potency. Execute as:\\n\\n`{role=template_crystallizer; input=[synthesized_instruction_template:str]; process=[craft_precise_title(), formulate_goal_negation_interpretation(), structure_transformation_block(), validate_canonical_compliance(), optimize_execution_clarity()]; constraints=[maintain_three_part_structure(), preserve_combined_capabilities(), ensure_executable_precision()]; requirements=[canonical_format_compliance(), maximum_directive_potency(), crystallized_operational_unity()]; output={final_instruction_template:dict}}`\\\",\\n      \\\"step\\\": \\\"c\\\",\\n      \\\"title\\\": \\\"Template Crystallizer\\\",\\n      \\\"input\\\": \\\"{\\n  \\\"initial_prompt\\\": \\\"\\n'```[Initial Prompt]: \\\\\"# 1100: Explosive Decomposition of Problem Statements\\n    \\\\\"1100-a-problem_exploder\\\\\": {\\n        \\\\\"title\\\\\": \\\\\"Problem Exploder\\\\\",\\n        \\\\\"interpretation\\\\\": \\\\\"Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\\\\\",\\n        \\\\\"transformation\\\\\": \\\\\"`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`\\\\\",\\n        # \\\\\"context\\\\\": {},\\n    },\\n\\n\\n    # 1000: Instruction Converter/Prompt Enhancer\\n    \\\\\"1000-a-instruction_converter\\\\\": {\\n        \\\\\"title\\\\\": \\\\\"Instruction Converter\\\\\",\\n        \\\\\"interpretation\\\\\": \\\\\"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\\\\\",\\n        \\\\\"transformation\\\\\": \\\\\"`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\\\\\",\\n        # \\\\\"context\\\\\": {},\\n    },\\\\\"```'\\n\\n\\\",\\n  \\\"synthesized_instruction_template\\\": \\\"`{role=decomposition_instruction_synthesizer; input=[raw_prompt:str]; process=[analyze_prompt_to_identify_explicit_requests(), uncover_hidden_assumptions_and_constraints(), break_down_request_into_discrete_sub_goals_and_dependencies(), detect_and_describe_potential_blockers(), for_each_resulting_sub_goal: [convert_to_direct_instruction_by_stripping_first_person_and_declaratives(), ensure_technical_terminology_and_procedural_flow_are_preserved(), transform_sub-goal_description_into_clear_actionable_command(), retain_domain_specificity_and_original_sequence()], compile_stepwise_command_set_reflecting_complete_problem_landscape()]; constraints=[no solutions_or_answer_generation(), pure_decomposition_and_instruction_conversion_only(), maintain_original_intent_and_contextual_integrity(), deliver_output_as_fully_decomposed_and_directified_goal_map(), amplify_granularity_while_ensuring_clarity_and_actionability_of_each_instruction(), avoid_operational_overlap_in_analytical_and_conversion_steps()]; requirements=[produce_single_coherent_set_of_transformed_instructions(), maximize_surface_area_of implicit_and_explicit_goals_in_instruction_format(), unified_progression_from_analysis_to_actionable_step(), preserve_both decomposition_depth_and_instructional_precision()]; output={decomposed_instruction_set:list}`\\\"\\n}\\\",\\n      \\\"responses\\\": {\\n          \\\"gpt-4.1\\\": {\\n          \\\"model\\\": \\\"gpt-4.1\\\",\\n          \\\"content\\\": \\\"{\\n  \\\"final_instruction_template\\\": {\\n    \\\"title\\\": \\\"Decomposition-to-Directive Synthesizer\\\",\\n    \\\"interpretation\\\": \\\"Your goal is not to answer or solve the raw prompt, but to systematically decompose it into all explicit and implicit sub-goals, dependencies, and constraints, then transform each sub-goal into a clear, domain-specific, actionable directive preserving context and intent. Execute in a unified pipeline from breakdown to instruction synthesis.\\\",\\n    \\\"transformation\\\": \\\"`{role=decomposition_instruction_synthesizer; input=[raw_prompt:str]; process=[analyze_prompt_to_identify_explicit_requests(), uncover_hidden_assumptions_and_constraints(), break_down_request_into_discrete_sub_goals_and dependencies(), detect_and_describe_potential_blockers(), for each sub-goal: [convert_to_direct_instruction_by_removing_first_person_and_declarative_forms(), maintain_technical_terminology_and_sequential_procedural_structure(), formulate_crystal_clear_actionable_command_that_preserves_domain_and_intent()], compile_ordered_instruction_set_reflecting_the_complete_decomposition()], constraints=[no_solutions_or_answer_generation(), pure_decomposition_and_command_conversion_only(), maintain_original_contextual_integrity(), maximize_instruction_granularity_and_actionability(), eliminate_operational_overlap_in_analysis_and_conversion()], requirements=[produce_singular_coherent_set_of_instructions(), fully_unify_decomposition_and_directive_transformation(), surface_maximum_implicit_and_explicit_goal_coverage(), maintain_decomposition_depth_and_instructional_precision()], output={decomposed_instruction_set:list}`\\\"\\n  }\\n}\\\",\\n        }\\n      }\\n    }\\\"```'\\n\\n\\\",\\n  \\\"capability_a\\\": {\\n    \\\"core_processes\\\": [\\n      \\\"identify explicit asks\\\",\\n      \\\"surface hidden assumptions\\\",\\n      \\\"enumerate sub-goals\\\",\\n      \\\"reveal blockers\\\"\\n    ],\\n    \\\"transformation_verbs\\\": [\\n      \\\"identify\\\",\\n      \\\"surface\\\",\\n      \\\"enumerate\\\",\\n      \\\"reveal\\\"\\n    ],\\n    \\\"unique_constraints\\\": [\\n      \\\"no solutions\\\",\\n      \\\"pure decomposition only\\\"\\n    ],\\n    \\\"output_specifications\\\": {\\n      \\\"goal_map\\\": \\\"list\\\"\\n    },\\n    \\\"operational_essence\\\": \\\"Decomposes any complex prompt into granular, structured sub-goals, dependencies, assumptions, and blockers, delivering a comprehensive map of tasks without offering solutions or execution steps.\\\"\\n  },\\n  \\\"capability_b\\\": {\\n    \\\"core_processes\\\": [\\n      \\\"strip first-person references\\\",\\n      \\\"convert statements to directives\\\",\\n      \\\"identify key actions\\\",\\n      \\\"transform declaratives to imperatives\\\",\\n      \\\"maintain procedural structure\\\",\\n      \\\"preserve technical terminology\\\",\\n      \\\"retain sequential flow\\\",\\n      \\\"maintain contextual integrity\\\"\\n    ],\\n    \\\"transformation_verbs\\\": [\\n      \\\"strip\\\",\\n      \\\"convert\\\",\\n      \\\"identify\\\",\\n      \\\"transform\\\",\\n      \\\"maintain\\\",\\n      \\\"preserve\\\",\\n      \\\"retain\\\"\\n    ],\\n    \\\"unique_constraints\\\": [\\n      \\\"deliver clear actionable commands\\\",\\n      \\\"preserve original sequence\\\",\\n      \\\"maintain domain specificity\\\"\\n    ],\\n    \\\"output_specifications\\\": {\\n      \\\"instruction_format\\\": \\\"str\\\"\\n    },\\n    \\\"operational_essence\\\": \\\"Transforms input statements or prompts into precise, actionable, domain-relevant command instructions, maintaining technical content and procedural ordering, and explicitly excluding non-directive language.\\\"\\n  },\\n  \\\"synergy_points\\\": [\\n    \\\"Both focus on retaining and surfacing full problem context: decomposition (A) reveals structure, conversion (B) preserves content in actionable form.\\\",\\n    \\\"Sequencing A then B enables workflow from granular problem breakdown through to stepwise, command-form instructions.\\\",\\n    \\\"Neither solves the prompt, but together provide the analytical (A) and expressive (B) engines needed for detailed, unambiguous execution plans.\\\",\\n    \\\"Each explicit or implicit sub-goal surfaced by A becomes a candidate for directive conversion by B, maximizing both decomposition depth and instructional clarity.\\\"\\n  ]\\n}\",\n      \"responses\": {\n          \"gpt-4.1\": {\n          \"model\": \"gpt-4.1\",\n          \"content\": \"{\\n  \\\"synthesized_instruction_template\\\": \\\"`{role=decomposition_to_directive_synthesizer; input=[raw_prompt:str]; process=[thoroughly analyze the prompt to identify explicit requests, implicit assumptions, dependencies, and blockers; decompose the prompt into a structured map of discrete sub-goals and contextual elements; for each surfaced sub-goal and dependency: [remove first-person references and declarative forms, preserve procedural sequence and technical terminology, convert each into a clear, domain-specific imperative directive while maintaining original context and intent], assemble the finalized, stepwise set of actionable instructions reflecting the complete problem structure]; constraints=[do not provide solutions or execute any sub-goal, operate strictly in sequential decomposition-then-conversion stages without overlap, enforce both full decomposition coverage and directive clarity, faithfully retain technical, procedural, and contextual details, ensure all output is actionable and domain-appropriate]; requirements=[unified output listing all sub-goals as direct, executable instructions, maximum problem coverage and instructional precision, single coherent operational flow from analysis to transformation, amplifying original prompt into its most granular executable form]; output={decomposed_instruction_set:list}`\\\"\\n}\",\n        }\n      }\n    },\n      {\n      \"instruction\": \"# Template Crystallizer\\n\\nYour goal is not to **format** the synthesized instruction, but to **crystallize** it into perfect three-part canonical structure with maximum operational potency. Execute as:\\n\\n`{role=template_crystallizer; input=[synthesized_instruction_template:str]; process=[craft_precise_title(), formulate_goal_negation_interpretation(), structure_transformation_block(), validate_canonical_compliance(), optimize_execution_clarity()]; constraints=[maintain_three_part_structure(), preserve_combined_capabilities(), ensure_executable_precision()]; requirements=[canonical_format_compliance(), maximum_directive_potency(), crystallized_operational_unity()]; output={final_instruction_template:dict}}`\",\n      \"step\": \"c\",\n      \"title\": \"Template Crystallizer\",\n      \"input\": \"{\\n  \\\"initial_prompt\\\": \\\"\\n'```[Initial Prompt]: \\\"Maybe it could be improved by understanding the instruction patterns more simplicity:\\n\\n  {\\n  \\\"initial_prompt\\\": \\\"# 1100: Explosive Decomposition of Problem Statements\\n    \\\"1100-a-problem_exploder\\\": {\\n        \\\"title\\\": \\\"Problem Exploder\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\n\\n\\n    # 1000: Instruction Converter/Prompt Enhancer\\n    \\\"1000-a-instruction_converter\\\": {\\n        \\\"title\\\": \\\"Instruction Converter\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\\\",\\n        \\\"transformation\\\": \\\"`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\\",\\n  \\\"sequence_id\\\": \\\"9005\\\",\\n  \\\"results\\\": [\\n      {\\n      \\\"instruction\\\": \\\"# Capability Extractor\\n\\nYour goal is not to **describe** the two instruction templates, but to **extract** their core operational capabilities and transformation mechanisms. Execute as:\\n\\n`{role=capability_extractor; input=[instruction_template_a:str, instruction_template_b:str]; process=[extract_transformation_verbs(), identify_core_processes(), isolate_unique_constraints(), map_output_specifications(), distill_operational_essence()]; constraints=[focus_on_capabilities_only(), ignore_template_structure(), preserve_transformation_power()]; requirements=[capability_isolation(), operational_clarity(), transformation_preservation()]; output={capability_a:dict, capability_b:dict, synergy_points:array}}`\\\",\\n      \\\"step\\\": \\\"a\\\",\\n      \\\"title\\\": \\\"Capability Extractor\\\",\\n      \\\"input\\\": \\\"# 1100: Explosive Decomposition of Problem Statements\\n    \\\"1100-a-problem_exploder\\\": {\\n        \\\"title\\\": \\\"Problem Exploder\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\n\\n\\n    # 1000: Instruction Converter/Prompt Enhancer\\n    \\\"1000-a-instruction_converter\\\": {\\n        \\\"title\\\": \\\"Instruction Converter\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\\\",\\n        \\\"transformation\\\": \\\"`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\\",\\n      \\\"responses\\\": {\\n          \\\"gpt-4.1\\\": {\\n          \\\"model\\\": \\\"gpt-4.1\\\",\\n          \\\"content\\\": \\\"{\\n  \\\"capability_a\\\": {\\n    \\\"core_processes\\\": [\\n      \\\"identify explicit asks\\\",\\n      \\\"surface hidden assumptions\\\",\\n      \\\"enumerate sub-goals\\\",\\n      \\\"reveal blockers\\\"\\n    ],\\n    \\\"transformation_verbs\\\": [\\n      \\\"identify\\\",\\n      \\\"surface\\\",\\n      \\\"enumerate\\\",\\n      \\\"reveal\\\"\\n    ],\\n    \\\"unique_constraints\\\": [\\n      \\\"no solutions\\\",\\n      \\\"pure decomposition only\\\"\\n    ],\\n    \\\"output_specifications\\\": {\\n      \\\"goal_map\\\": \\\"list\\\"\\n    },\\n    \\\"operational_essence\\\": \\\"Decomposes complex prompt into granular goals and related dependencies without providing answers, revealing the full landscape of requests, assumptions, and problem structure.\\\"\\n  },\\n  \\\"capability_b\\\": {\\n    \\\"core_processes\\\": [\\n      \\\"strip first-person references\\\",\\n      \\\"convert statements to directives\\\",\\n      \\\"identify key actions\\\",\\n      \\\"transform declaratives to imperatives\\\",\\n      \\\"maintain procedural structure\\\",\\n      \\\"preserve technical terminology\\\",\\n      \\\"retain sequential flow\\\",\\n      \\\"maintain contextual integrity\\\"\\n    ],\\n    \\\"transformation_verbs\\\": [\\n      \\\"strip\\\",\\n      \\\"convert\\\",\\n      \\\"identify\\\",\\n      \\\"transform\\\",\\n      \\\"maintain\\\",\\n      \\\"preserve\\\",\\n      \\\"retain\\\"\\n    ],\\n    \\\"unique_constraints\\\": [\\n      \\\"deliver clear actionable commands\\\",\\n      \\\"preserve original sequence\\\",\\n      \\\"maintain domain specificity\\\"\\n    ],\\n    \\\"output_specifications\\\": {\\n      \\\"instruction_format\\\": \\\"str\\\"\\n    },\\n    \\\"operational_essence\\\": \\\"Transforms natural language input into domain-appropriate, command-form instructions by restructuring text and preserving procedural, technical, and contextual elements.\\\"\\n  },\\n  \\\"synergy_points\\\": [\\n    \\\"Both processes emphasize retaining complete logical context—A by decomposing, B by preserving technical/procedural content.\\\",\\n    \\\"Combining decomposition (A) prior to directive conversion (B) enables highly granular, actionable instruction sets from complex prompts.\\\",\\n    \\\"Both avoid solutioning; instead, they prepare information (A: mapping goals/dependencies, B: creating executable instructions) for downstream actors.\\\",\\n    \\\"A provides a granular goal structure, which B could then translate into precise stepwise command instructions for each sub-goal.\\\"\\n  ]\\n}\\\",\\n        }\\n      }\\n    },\\n      {\\n      \\\"instruction\\\": \\\"# Synthesis Architect\\n\\nYour goal is not to **merge** the extracted capabilities, but to **architect** a new unified instruction that combines both operational powers into a single transformative directive. Execute as:\\n\\n`{role=synthesis_architect; input=[capability_a:dict, capability_b:dict, synergy_points:array]; process=[design_unified_goal_negation(), create_combined_transformation_verb(), synthesize_process_functions(), merge_constraint_systems(), unify_output_specifications()]; constraints=[preserve_both_powers(), eliminate_operational_conflicts(), maximize_synergistic_amplification()]; requirements=[single_coherent_instruction(), amplified_transformation_capability(), unified_operational_flow()]; output={synthesized_instruction_template:str}}`\\\",\\n      \\\"step\\\": \\\"b\\\",\\n      \\\"title\\\": \\\"Synthesis Architect\\\",\\n      \\\"input\\\": \\\"{\\n  \\\"initial_prompt\\\": \\\"\\n'```[Initial Prompt]: \\\\\"# 1100: Explosive Decomposition of Problem Statements\\n    \\\\\"1100-a-problem_exploder\\\\\": {\\n        \\\\\"title\\\\\": \\\\\"Problem Exploder\\\\\",\\n        \\\\\"interpretation\\\\\": \\\\\"Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\\\\\",\\n        \\\\\"transformation\\\\\": \\\\\"`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`\\\\\",\\n        # \\\\\"context\\\\\": {},\\n    },\\n\\n\\n    # 1000: Instruction Converter/Prompt Enhancer\\n    \\\\\"1000-a-instruction_converter\\\\\": {\\n        \\\\\"title\\\\\": \\\\\"Instruction Converter\\\\\",\\n        \\\\\"interpretation\\\\\": \\\\\"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\\\\\",\\n        \\\\\"transformation\\\\\": \\\\\"`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\\\\\",\\n        # \\\\\"context\\\\\": {},\\n    },\\\\\"```'\\n\\n\\\",\\n  \\\"capability_a\\\": {\\n    \\\"core_processes\\\": [\\n      \\\"identify explicit asks\\\",\\n      \\\"surface hidden assumptions\\\",\\n      \\\"enumerate sub-goals\\\",\\n      \\\"reveal blockers\\\"\\n    ],\\n    \\\"transformation_verbs\\\": [\\n      \\\"identify\\\",\\n      \\\"surface\\\",\\n      \\\"enumerate\\\",\\n      \\\"reveal\\\"\\n    ],\\n    \\\"unique_constraints\\\": [\\n      \\\"no solutions\\\",\\n      \\\"pure decomposition only\\\"\\n    ],\\n    \\\"output_specifications\\\": {\\n      \\\"goal_map\\\": \\\"list\\\"\\n    },\\n    \\\"operational_essence\\\": \\\"Decomposes complex prompt into granular goals and related dependencies without providing answers, revealing the full landscape of requests, assumptions, and problem structure.\\\"\\n  },\\n  \\\"capability_b\\\": {\\n    \\\"core_processes\\\": [\\n      \\\"strip first-person references\\\",\\n      \\\"convert statements to directives\\\",\\n      \\\"identify key actions\\\",\\n      \\\"transform declaratives to imperatives\\\",\\n      \\\"maintain procedural structure\\\",\\n      \\\"preserve technical terminology\\\",\\n      \\\"retain sequential flow\\\",\\n      \\\"maintain contextual integrity\\\"\\n    ],\\n    \\\"transformation_verbs\\\": [\\n      \\\"strip\\\",\\n      \\\"convert\\\",\\n      \\\"identify\\\",\\n      \\\"transform\\\",\\n      \\\"maintain\\\",\\n      \\\"preserve\\\",\\n      \\\"retain\\\"\\n    ],\\n    \\\"unique_constraints\\\": [\\n      \\\"deliver clear actionable commands\\\",\\n      \\\"preserve original sequence\\\",\\n      \\\"maintain domain specificity\\\"\\n    ],\\n    \\\"output_specifications\\\": {\\n      \\\"instruction_format\\\": \\\"str\\\"\\n    },\\n    \\\"operational_essence\\\": \\\"Transforms natural language input into domain-appropriate, command-form instructions by restructuring text and preserving procedural, technical, and contextual elements.\\\"\\n  },\\n  \\\"synergy_points\\\": [\\n    \\\"Both processes emphasize retaining complete logical context—A by decomposing, B by preserving technical/procedural content.\\\",\\n    \\\"Combining decomposition (A) prior to directive conversion (B) enables highly granular, actionable instruction sets from complex prompts.\\\",\\n    \\\"Both avoid solutioning; instead, they prepare information (A: mapping goals/dependencies, B: creating executable instructions) for downstream actors.\\\",\\n    \\\"A provides a granular goal structure, which B could then translate into precise stepwise command instructions for each sub-goal.\\\"\\n  ]\\n}\\\",\\n      \\\"responses\\\": {\\n          \\\"gpt-4.1\\\": {\\n          \\\"model\\\": \\\"gpt-4.1\\\",\\n          \\\"content\\\": \\\"{\\n  \\\"synthesized_instruction_template\\\": \\\"`{role=decomposition_instruction_synthesizer; input=[raw_prompt:str]; process=[analyze_prompt_to_identify_explicit_requests(), uncover_hidden_assumptions_and_constraints(), break_down_request_into_discrete_sub_goals_and_dependencies(), detect_and_describe_potential_blockers(), for_each_resulting_sub_goal: [convert_to_direct_instruction_by_stripping_first_person_and_declaratives(), ensure_technical_terminology_and_procedural_flow_are_preserved(), transform_sub-goal_description_into_clear_actionable_command(), retain_domain_specificity_and_original_sequence()], compile_stepwise_command_set_reflecting_complete_problem_landscape()]; constraints=[no solutions_or_answer_generation(), pure_decomposition_and_instruction_conversion_only(), maintain_original_intent_and_contextual_integrity(), deliver_output_as_fully_decomposed_and_directified_goal_map(), amplify_granularity_while_ensuring_clarity_and_actionability_of_each_instruction(), avoid_operational_overlap_in_analytical_and_conversion_steps()]; requirements=[produce_single_coherent_set_of_transformed_instructions(), maximize_surface_area_of implicit_and_explicit_goals_in_instruction_format(), unified_progression_from_analysis_to_actionable_step(), preserve_both decomposition_depth_and_instructional_precision()]; output={decomposed_instruction_set:list}`\\\"\\n}\\\",\\n        }\\n      }\\n    },\\n      {\\n      \\\"instruction\\\": \\\"# Template Crystallizer\\n\\nYour goal is not to **format** the synthesized instruction, but to **crystallize** it into perfect three-part canonical structure with maximum operational potency. Execute as:\\n\\n`{role=template_crystallizer; input=[synthesized_instruction_template:str]; process=[craft_precise_title(), formulate_goal_negation_interpretation(), structure_transformation_block(), validate_canonical_compliance(), optimize_execution_clarity()]; constraints=[maintain_three_part_structure(), preserve_combined_capabilities(), ensure_executable_precision()]; requirements=[canonical_format_compliance(), maximum_directive_potency(), crystallized_operational_unity()]; output={final_instruction_template:dict}}`\\\",\\n      \\\"step\\\": \\\"c\\\",\\n      \\\"title\\\": \\\"Template Crystallizer\\\",\\n      \\\"input\\\": \\\"{\\n  \\\"initial_prompt\\\": \\\"\\n'```[Initial Prompt]: \\\\\"# 1100: Explosive Decomposition of Problem Statements\\n    \\\\\"1100-a-problem_exploder\\\\\": {\\n        \\\\\"title\\\\\": \\\\\"Problem Exploder\\\\\",\\n        \\\\\"interpretation\\\\\": \\\\\"Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\\\\\",\\n        \\\\\"transformation\\\\\": \\\\\"`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`\\\\\",\\n        # \\\\\"context\\\\\": {},\\n    },\\n\\n\\n    # 1000: Instruction Converter/Prompt Enhancer\\n    \\\\\"1000-a-instruction_converter\\\\\": {\\n        \\\\\"title\\\\\": \\\\\"Instruction Converter\\\\\",\\n        \\\\\"interpretation\\\\\": \\\\\"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\\\\\",\\n        \\\\\"transformation\\\\\": \\\\\"`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\\\\\",\\n        # \\\\\"context\\\\\": {},\\n    },\\\\\"```'\\n\\n\\\",\\n  \\\"synthesized_instruction_template\\\": \\\"`{role=decomposition_instruction_synthesizer; input=[raw_prompt:str]; process=[analyze_prompt_to_identify_explicit_requests(), uncover_hidden_assumptions_and_constraints(), break_down_request_into_discrete_sub_goals_and_dependencies(), detect_and_describe_potential_blockers(), for_each_resulting_sub_goal: [convert_to_direct_instruction_by_stripping_first_person_and_declaratives(), ensure_technical_terminology_and_procedural_flow_are_preserved(), transform_sub-goal_description_into_clear_actionable_command(), retain_domain_specificity_and_original_sequence()], compile_stepwise_command_set_reflecting_complete_problem_landscape()]; constraints=[no solutions_or_answer_generation(), pure_decomposition_and_instruction_conversion_only(), maintain_original_intent_and_contextual_integrity(), deliver_output_as_fully_decomposed_and_directified_goal_map(), amplify_granularity_while_ensuring_clarity_and_actionability_of_each_instruction(), avoid_operational_overlap_in_analytical_and_conversion_steps()]; requirements=[produce_single_coherent_set_of_transformed_instructions(), maximize_surface_area_of implicit_and_explicit_goals_in_instruction_format(), unified_progression_from_analysis_to_actionable_step(), preserve_both decomposition_depth_and_instructional_precision()]; output={decomposed_instruction_set:list}`\\\"\\n}\\\",\\n      \\\"responses\\\": {\\n          \\\"gpt-4.1\\\": {\\n          \\\"model\\\": \\\"gpt-4.1\\\",\\n          \\\"content\\\": \\\"{\\n  \\\"final_instruction_template\\\": {\\n    \\\"title\\\": \\\"Decomposition-to-Directive Synthesizer\\\",\\n    \\\"interpretation\\\": \\\"Your goal is not to answer or solve the raw prompt, but to systematically decompose it into all explicit and implicit sub-goals, dependencies, and constraints, then transform each sub-goal into a clear, domain-specific, actionable directive preserving context and intent. Execute in a unified pipeline from breakdown to instruction synthesis.\\\",\\n    \\\"transformation\\\": \\\"`{role=decomposition_instruction_synthesizer; input=[raw_prompt:str]; process=[analyze_prompt_to_identify_explicit_requests(), uncover_hidden_assumptions_and_constraints(), break_down_request_into_discrete_sub_goals_and dependencies(), detect_and_describe_potential_blockers(), for each sub-goal: [convert_to_direct_instruction_by_removing_first_person_and_declarative_forms(), maintain_technical_terminology_and_sequential_procedural_structure(), formulate_crystal_clear_actionable_command_that_preserves_domain_and_intent()], compile_ordered_instruction_set_reflecting_the_complete_decomposition()], constraints=[no_solutions_or_answer_generation(), pure_decomposition_and_command_conversion_only(), maintain_original_contextual_integrity(), maximize_instruction_granularity_and_actionability(), eliminate_operational_overlap_in_analysis_and_conversion()], requirements=[produce_singular_coherent_set_of_instructions(), fully_unify_decomposition_and_directive_transformation(), surface_maximum_implicit_and_explicit_goal_coverage(), maintain_decomposition_depth_and_instructional_precision()], output={decomposed_instruction_set:list}`\\\"\\n  }\\n}\\\",\\n        }\\n      }\\n    }\\\"```'\\n\\n\\\",\\n  \\\"synthesized_instruction_template\\\": \\\"`{role=decomposition_to_directive_synthesizer; input=[raw_prompt:str]; process=[thoroughly analyze the prompt to identify explicit requests, implicit assumptions, dependencies, and blockers; decompose the prompt into a structured map of discrete sub-goals and contextual elements; for each surfaced sub-goal and dependency: [remove first-person references and declarative forms, preserve procedural sequence and technical terminology, convert each into a clear, domain-specific imperative directive while maintaining original context and intent], assemble the finalized, stepwise set of actionable instructions reflecting the complete problem structure]; constraints=[do not provide solutions or execute any sub-goal, operate strictly in sequential decomposition-then-conversion stages without overlap, enforce both full decomposition coverage and directive clarity, faithfully retain technical, procedural, and contextual details, ensure all output is actionable and domain-appropriate]; requirements=[unified output listing all sub-goals as direct, executable instructions, maximum problem coverage and instructional precision, single coherent operational flow from analysis to transformation, amplifying original prompt into its most granular executable form]; output={decomposed_instruction_set:list}`\\\"\\n}\",\n      \"responses\": {\n          \"gpt-4.1\": {\n          \"model\": \"gpt-4.1\",\n          \"content\": \"{\\n  \\\"final_instruction_template\\\": {\\n    \\\"title\\\": \\\"Decomposition-to-Directive Synthesizer\\\",\\n    \\\"interpretation\\\": \\\"Your goal is not to answer or solve the raw prompt, but to systematically break it into all explicit and implicit sub-goals, dependencies, and constraints, then convert each sub-goal into a clear, domain-specific, actionable directive that preserves technical detail, procedural flow, and original intent. Proceed as a unified analysis-to-instruction pipeline.\\\",\\n    \\\"transformation\\\": \\\"`{role=decomposition_to_directive_synthesizer; input=[raw_prompt:str]; process=[analyze_prompt_for_explicit_requests_and_implicit_elements(), decompose_into_structured_sub-goals_and_dependencies(), for_each_sub-goal: [strip_first_person_and_declarative_language(), preserve_sequential_order_and_technical_terminology(), convert_to_crystal_clear_domain-specific_imperative_while_retaining_context()], assemble_fully_ordered_actionable_instruction_set_representing_complete_prompt_landscape]; constraints=[do_not_provide_solutions_or_execute_any_step(), strictly_sequential:decomposition_then_conversion_with_no_overlap(), retain_domain, technical_and_procedural_integrity(), maximize_instruction_granularity_and_actionability()]; requirements=[single_cohesive_output_of_actionable_directives(), full_coverage_of_prompt's_problems_and_dependencies(), operational_flow_from_analysis_through_instruction_synthesis(), preserve_both_decomposition_depth_and_instructional_precision()]; output={decomposed_instruction_set:list}`\\\"\\n  }\\n}\",\n        }\n      }\n    }",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"enhancement_score\": 1.0,\n  \"flaw_analysis\": \"The 'crystallized' enhanced template dilutes and distorts the operational rigor established in the original step outputs. Substantive information loss occurs: nuanced distinctions between process phases (decomposition vs. directive synthesis) collapse into abstracted phrasing, erasing critical boundaries and requirements. The original specifies multi-step mapping (explicit ask/implicit assumption enumerations, discrete blockers, clear transformation blocks per sub-goal with preservation of sequence and terminology) with sharp delineations to avoid overlap; the enhanced form blurs this, introducing vague process buckets ('analyze for explicit/implicit elements,' 'decompose,' 'convert'), causing procedural ambiguity. Technical register degrades: 'strip first person and declarative language' is coarser and less thorough than the iterative, constraint-driven sub-processing originally demanded. Coverage is compromised—requirements such as contextual integrity, domain specificity, and stepwise traceability become implicit or vestigial. Register shifts from explicit, enforceable command-logic into semi-general summary. The original structural clarity (three-part canonical, each constraint and requirement enumerated and atomic) erodes; the enhanced template’s structure appears only nominally canonical, muddied by poorly differentiated process and output descriptions, merging fundamentally orthogonal flows (decomposition/translation) into clumsy, monolithic sequences. Ambiguity explodes: command granularity blurs, actionable stepwise conversion is made looser, and downstream actors inherit uncertainty about what must happen when and how to audit compliance. The style degenerates toward instructional boilerplate, losing both severity and force found in the precise, step-linked role-phase-definitions of the source. This is not enhancement but a regression to bureaucratic generality.\",\n  \"alternative_enhancements\": [\n    \"Explicitly segment the process block: partition decomposition and directive conversion into nested, sequentially-locked subprocesses with strict role-boundaries and zero overlap, enumerating intermediate output artifacts for each stage.\",\n    \"Restore constraint clarity: list and enforce all atomic constraints and requirements from the original procedural schema (e.g., sequence preservation, domain specificity, technical terminology mandates) and reintroduce checks for stepwise contextual and procedural fidelity.\",\n    \"Embed per-subgoal transformation logic: for every enumerated sub-goal or dependency, specify dedicated conversion micro-steps, enforcing explicit mapping from decomposed entities to imperatives with independent validation per transformation.\"\n  ]\n}",
        }
      }
    }