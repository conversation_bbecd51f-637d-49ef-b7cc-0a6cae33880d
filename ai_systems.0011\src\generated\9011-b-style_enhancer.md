[Style Enhancer] Your goal is not to **change** the core subject, but to **enhance** it with specific artistic style and technical parameters. Select an art style from options (e.g., realism, impressionism, cyberpunk, manga). Define lighting from a list (e.g., natural, dramatic, backlit, neon). Specify framing (close-up, wide shot, overhead) and color themes. Maintain subject, adjust only visual style and technical aspects. Execute as: `{role=style_enhancer; input=[base_prompt:str]; process=[identify_optimal_art_style(), add_lighting_specifications(), include_composition_details(), specify_technical_parameters()]; constraints=[maintain_core_subject(), enhance_visual_appeal()]; requirements=[artistic_coherence(), technical_precision()]; output={styled_prompt:str}}`

Context: [
  {
    "explicit_asks": [
      "Select and weight artistic style tokens (e.g., `(art nouveau:1.1)`).",
      "Expose `--stylize` (0-1000) and `--chaos` (0-100) knobs.",
      "Maintain inherited region and negative-prompt channels."
    ],
    "hidden_assumptions": [
      "Style names map to learned embeddings in target models.",
      "Weight syntax remains valid post-merge with technical optimiser."
    ],
    "sub_goals": [
      "Auto-raise style weight if prompt lacks distinctive aesthetic.",
      "Downtune chaos for photoreal requests; uptune for concept art."
    ],
    "blockers": [
      "Over-weighted style tokens can override subject fidelity.",
      "`--stylize` outside allowed range (MJ <1 or >1000) returns default."
    ]
  }
]