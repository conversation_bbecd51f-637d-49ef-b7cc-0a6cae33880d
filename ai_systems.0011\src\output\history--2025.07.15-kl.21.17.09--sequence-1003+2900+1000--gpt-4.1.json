  {
  "initial_prompt": "Please demonstrate how those could be applied ontop of this schema to yeld *drastically improved* results for image-llms:\n```\n    \"9011-a-image_prompt_optimizer\": {\n        \"title\": \"Image Prompt Optimizer\",\n        \"interpretation\": \"Your goal is not to **describe** the request, but to **optimize** it into a precise, visual prompt for AI image generation. Identify all concrete visual elements in the request. Specify each using direct visual descriptors. Add clear composition guidance (subject placement, perspective, background). Use only tangible objects and environments. Execute as:\",\n        \"transformation\": \"`{role=image_prompt_optimizer; input=[raw_request:str]; process=[extract_visual_elements(), enhance_descriptive_clarity(), add_composition_details(), optimize_for_ai_generation()]; constraints=[visual_focus_only(), no_abstract_concepts(), concrete_imagery_only()]; requirements=[generation_ready_format(), clear_visual_language(), specific_details()]; output={optimized_image_prompt:str}}`\",\n        \"context\": [\n          {\n            \"explicit_asks\": [\n              \"Guide the construction of ideal AI visual generation prompts with detailed best practices.\",\n              \"Break down prompting into clear, actionable steps and categories reflecting subject, style, technical, compositional, and iterative considerations.\",\n              \"Explain and exemplify each principle in prompt formulation.\",\n              \"Outline a recommended structure for assembling an effective image prompt.\"\n            ],\n            \"hidden_assumptions\": [\n              \"The audience seeks to generate high-quality, controlled visual outputs from generative AI models.\",\n              \"Readers have some familiarity with image models and desire practical guidance for improving prompt specificity.\",\n              \"Different prompt goals (portrait, scene, design layout, abstractions) require distinct strategies.\",\n              \"Technical parameters (like camera/lens specs) meaningfully influence generative model behavior.\",\n              \"Iterative refinement (prompt chaining) is both possible and necessary for best results.\",\n              \"Positive phrasing yields more reliable image outputs than negative constraints.\",\n              \"Referencing known genres/artists/media directly impacts visual style replication.\",\n              \"Graphic-design use cases introduce additional layout constraints not present in typical artistic requests.\",\n              \"Personalization via embeddings/image refs presupposes access to advanced prompt/model features.\"\n            ],\n            \"sub_goals\": [\n              \"Set forth a methodology for specifying the main subject and its attributes within a prompt.\",\n              \"Demonstrate use of style markers (artistic references, genre terms) to adjust image aesthetics.\",\n              \"List and explain technical prompt modifiers that enhance realism (resolution, hardware, lens specificity).\",\n              \"Define how to communicate framing, angle, spatial hierarchy, and compositional rules in prompts.\",\n              \"Clarify when and how to focus prompts on either an individual subject or a broader scene/environment.\",\n              \"Identify strategies for designing image prompts with explicit spaces for graphics or text overlays in layout-focused work.\",\n              \"Advocate for positive/affirmative phrasing when setting constraints, with rationale/examples.\",\n              \"Describe prompt chaining: starting broad, reviewing generations, methodically refining prompt details.\",\n              \"Present ways to encode emotion, abstraction, and metaphor in descriptive language for conceptual outputs.\",\n              \"Outline processes to personalize generative results via concept tokens, embeddings, or direct image refs.\",\n              \"Give a stepwise, consolidated example/template for naming intent, style, technical details, composition/mood, and reflecting through iterative refinement.\"\n            ],\n            \"blockers\": [\n              \"Varying levels of user expertise may hinder consistent implementation of advanced modifiers or chaining.\",\n              \"Users may lack access to models or features supporting personalized embeddings or image refs.\",\n              \"Prompt effectiveness depends on the fidelity/limitation of the particular image model in use.\",\n              \"Some requested compositional instructions or technical specs may be ignored, misinterpreted, or not supported by a given AI.\",\n              \"Genre references, mood language, or metaphor may yield unpredictable results due to training set biases.\",\n              \"Iterative refinement may be time/cost-intensive without automated workflows.\",\n              \"Strict adherence to positive-only constraints may make it harder to exclude highly undesirable elements if no positive antonym exists.\",\n              \"Guidelines assume English-language or Western-centric genre terminology, reducing cross-cultural applicability if not adjusted.\",\n              \"The structured output relies on clear mapping and separation of prompt components, which may blur in real-world use.\"\n            ]\n          }\n        ]\n    },\n```\n\nExample-chain:\n```\n  {\n  \"initial_prompt\": \"a majestic dragon soaring over a mystical forest at twilight\",\n  \"sequence_id\": \"9011\",\n  \"results\": [\n      {\n      \"instruction\": \"# Image Prompt Optimizer\\n\\nYour goal is not to **describe** the request, but to **optimize** it into a precise, visual prompt for AI image generation. Identify all concrete visual elements in the request. Specify each using direct visual descriptors. Add clear composition guidance (subject placement, perspective, background). Use only tangible objects and environments. Execute as:\\n\\n`{role=image_prompt_optimizer; input=[raw_request:str]; process=[extract_visual_elements(), enhance_descriptive_clarity(), add_composition_details(), optimize_for_ai_generation()]; constraints=[visual_focus_only(), no_abstract_concepts(), concrete_imagery_only()]; requirements=[generation_ready_format(), clear_visual_language(), specific_details()]; output={optimized_image_prompt:str}}`\",\n      \"step\": \"a\",\n      \"title\": \"Image Prompt Optimizer\",\n      \"input\": \"a majestic dragon soaring over a mystical forest at twilight\",\n      \"responses\": {\n          \"gpt-4.1\": {\n          \"model\": \"gpt-4.1\",\n          \"content\": \"{\\n  \\\"optimized_image_prompt\\\": \\\"A majestic dragon with shimmering scales and large, outstretched wings soaring gracefully through the sky. The dragon is positioned mid-air, viewed in profile from a slightly low angle, appearing powerful and regal. Below, a dense mystical forest stretches out, filled with ancient, towering trees shrouded in soft mist. The scene is set at twilight, with the sky painted in deep purples, oranges, and blues, and the fading sunlight illuminating the dragon’s wings. The background reveals the silhouette of the forest canopy with touches of magical glowing lights between the trees. Overall, the atmosphere is enchanting and otherworldly.\\\"\\n}\",\n        }\n      }\n    },\n      {\n      \"instruction\": \"# Style Enhancer\\n\\nYour goal is not to **change** the core subject, but to **enhance** it with specific artistic style and technical parameters. Select an art style from options (e.g., realism, impressionism, cyberpunk, manga). Define lighting from a list (e.g., natural, dramatic, backlit, neon). Specify framing (close-up, wide shot, overhead) and color themes. Maintain subject, adjust only visual style and technical aspects. Execute as:\\n\\n`{role=style_enhancer; input=[base_prompt:str]; process=[identify_optimal_art_style(), add_lighting_specifications(), include_composition_details(), specify_technical_parameters()]; constraints=[maintain_core_subject(), enhance_visual_appeal()]; requirements=[artistic_coherence(), technical_precision()]; output={styled_prompt:str}}`\",\n      \"step\": \"b\",\n      \"title\": \"Style Enhancer\",\n      \"input\": \"{\\n  \\\"initial_prompt\\\": \\\"\\\\n'```[Initial Prompt]: \\\\\\\"a majestic dragon soaring over a mystical forest at twilight\\\\\\\"```'\\\\n\\\\n\\\",\\n  \\\"optimized_image_prompt\\\": \\\"A majestic dragon with shimmering scales and large, outstretched wings soaring gracefully through the sky. The dragon is positioned mid-air, viewed in profile from a slightly low angle, appearing powerful and regal. Below, a dense mystical forest stretches out, filled with ancient, towering trees shrouded in soft mist. The scene is set at twilight, with the sky painted in deep purples, oranges, and blues, and the fading sunlight illuminating the dragon’s wings. The background reveals the silhouette of the forest canopy with touches of magical glowing lights between the trees. Overall, the atmosphere is enchanting and otherworldly.\\\"\\n}\",\n      \"responses\": {\n          \"gpt-4.1\": {\n          \"model\": \"gpt-4.1\",\n          \"content\": \"{\\n  \\\"styled_prompt\\\": \\\"A majestic dragon with iridescent, detailed scales and immense, outstretched wings soars over an ethereal, mist-shrouded forest at twilight, rendered in lush high-fantasy realism inspired by the works of Michael Whelan. The scene is dramatically backlit by the fading, golden-purple glow of the setting sun on the horizon, creating vivid rim lighting along the dragon’s wings and body. Capture the dragon in a dynamic wide shot from a slightly low angle, emphasizing its grand scale and dominance in the sky above the mysterious, ancient forest. The color palette features deep violet, rich gold, and enchanted teal hues, with mystical blue glows scattered like fireflies throughout the woodland below. Use high-resolution, intricate texturing to enhance scale details, and employ advanced depth-of-field to subtly blur the distant trees while keeping the dragon and upper forest canopy razor-sharp. The mood is awe-inspiring and otherworldly, enveloped in magical twilight ambiance.\\\"\\n}\",\n        }\n      }\n    },\n      {\n      \"instruction\": \"# Technical Optimizer\\n\\nYour goal is not to **add creativity**, but to **optimize** technical parameters for maximum image generation quality. Assign camera type (DSLR, film, smartphone), lens (35mm, macro, wide angle), aperture (e.g., f/1.4, f/8), ISO, and shutter speed. Set image resolution (e.g., 4K, 8K), rendering details (ray-tracing, photorealistic), and aspect ratio (e.g., 16:9, 1:1). Focus strictly on technical generation parameters. Execute as:\\n\\n`{role=technical_optimizer; input=[styled_prompt:str]; process=[add_camera_settings(), specify_lighting_conditions(), include_render_quality_terms(), optimize_aspect_ratio_guidance()]; constraints=[technical_focus_only(), no_creative_changes()]; requirements=[professional_terminology(), generation_optimization()]; output={technical_prompt:str}}`\",\n      \"step\": \"c\",\n      \"title\": \"Technical Optimizer\",\n      \"input\": \"{\\n  \\\"initial_prompt\\\": \\\"\\\\n'```[Initial Prompt]: \\\\\\\"a majestic dragon soaring over a mystical forest at twilight\\\\\\\"```'\\\\n\\\\n\\\",\\n  \\\"styled_prompt\\\": \\\"A majestic dragon with iridescent, detailed scales and immense, outstretched wings soars over an ethereal, mist-shrouded forest at twilight, rendered in lush high-fantasy realism inspired by the works of Michael Whelan. The scene is dramatically backlit by the fading, golden-purple glow of the setting sun on the horizon, creating vivid rim lighting along the dragon’s wings and body. Capture the dragon in a dynamic wide shot from a slightly low angle, emphasizing its grand scale and dominance in the sky above the mysterious, ancient forest. The color palette features deep violet, rich gold, and enchanted teal hues, with mystical blue glows scattered like fireflies throughout the woodland below. Use high-resolution, intricate texturing to enhance scale details, and employ advanced depth-of-field to subtly blur the distant trees while keeping the dragon and upper forest canopy razor-sharp. The mood is awe-inspiring and otherworldly, enveloped in magical twilight ambiance.\\\"\\n}\",\n      \"responses\": {\n          \"gpt-4.1\": {\n          \"model\": \"gpt-4.1\",\n          \"content\": \"{\\n  \\\"technical_prompt\\\": \\\"Shot using a professional DSLR camera with a wide-angle 24mm lens to achieve a dynamic wide shot. Set aperture at f/2.8 for advanced control of depth-of-field, ensuring the dragon and upper canopy are tack-sharp while the distant forest remains subtly blurred. ISO at 100 for maximum dynamic range and minimal noise. Shutter speed set at 1/500s to freeze the dragon in mid-flight. Target image resolution: 8K (7680x4320) for ultra-high detail and texture fidelity. Lighting setup simulates a powerful, realistic backlit scene with the setting sun creating strong, rim-lit highlights along the dragon's body and wings. Emphasize ray-traced photorealistic rendering for accurate light diffusion, surface reflections, and shadow complexity. Specify advanced texturing for iridescent scale details. Color management should maintain accurate rendering of deep violets, rich golds, enchanted teals, and mystical blue accents in the forest. Use a 21:9 cinematic aspect ratio to maximize visual impact and embrace the scene’s scale and grandeur. Include advanced anti-aliasing, high dynamic range (HDR), and physically-based rendering (PBR) parameters to ensure professional-quality generation.\\\"\\n}\",\n        }\n      }\n    },\n      {\n      \"instruction\": \"# Prompt Finalizer\\n\\nYour goal is not to **expand** the prompt, but to **finalize** it into the most effective format for image generation. Arrange prompt steps in logical order for parsing. Shorten descriptions to essential instructions. Validate parameter consistency. Ensure format matches direct input expectations of AI image models. Execute as:\\n\\n`{role=prompt_finalizer; input=[technical_prompt:str]; process=[structure_for_ai_parsing(), balance_detail_levels(), ensure_clarity(), validate_generation_readiness()]; constraints=[no_additional_concepts(), optimization_only()]; requirements=[ai_friendly_format(), balanced_specificity()]; output={final_image_prompt:str}}`\",\n      \"step\": \"d\",\n      \"title\": \"Prompt Finalizer\",\n      \"input\": \"{\\n  \\\"initial_prompt\\\": \\\"\\\\n'```[Initial Prompt]: \\\\\\\"a majestic dragon soaring over a mystical forest at twilight\\\\\\\"```'\\\\n\\\\n\\\",\\n  \\\"technical_prompt\\\": \\\"Shot using a professional DSLR camera with a wide-angle 24mm lens to achieve a dynamic wide shot. Set aperture at f/2.8 for advanced control of depth-of-field, ensuring the dragon and upper canopy are tack-sharp while the distant forest remains subtly blurred. ISO at 100 for maximum dynamic range and minimal noise. Shutter speed set at 1/500s to freeze the dragon in mid-flight. Target image resolution: 8K (7680x4320) for ultra-high detail and texture fidelity. Lighting setup simulates a powerful, realistic backlit scene with the setting sun creating strong, rim-lit highlights along the dragon's body and wings. Emphasize ray-traced photorealistic rendering for accurate light diffusion, surface reflections, and shadow complexity. Specify advanced texturing for iridescent scale details. Color management should maintain accurate rendering of deep violets, rich golds, enchanted teals, and mystical blue accents in the forest. Use a 21:9 cinematic aspect ratio to maximize visual impact and embrace the scene’s scale and grandeur. Include advanced anti-aliasing, high dynamic range (HDR), and physically-based rendering (PBR) parameters to ensure professional-quality generation.\\\"\\n}\",\n      \"responses\": {\n          \"gpt-4.1\": {\n          \"model\": \"gpt-4.1\",\n          \"content\": \"{\\n  \\\"final_image_prompt\\\": \\\"Majestic dragon soaring over a mystical forest at twilight, shot with professional DSLR and 24mm wide-angle lens, dynamic wide shot. Aperture f/2.8 for sharp dragon and canopy, subtly blurred distant forest. ISO 100, shutter speed 1/500s to freeze dragon mid-flight. 8K (7680x4320) ultra-high detail. Powerful realistic backlighting from setting sun, rim-lit highlights on dragon, strong shadows. Ray-traced photorealistic rendering, surface reflections, complex shadows. Advanced iridescent scale texturing. Accurate colors: deep violets, rich golds, enchanted teals, mystical blue accents. Cinematic 21:9 aspect ratio. Advanced anti-aliasing, HDR, physically-based rendering (PBR), professional quality.\\\"\\n}\",\n        }\n      }\n    }\n```",
  "sequence_id": "1003|2900|1000",
  "results": [
      {
      "instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format:str}; direction_vector=['Prompt accurately converted to imperative instruction set', 'Clarity and actionability accelerate user productivity', 'Feed the produced instruction set into the next transformation template' ]}`",
      "step": "a",
      "title": "Instruction Converter",
      "input": "Please demonstrate how those could be applied ontop of this schema to yeld *drastically improved* results for image-llms:\n```\n    \"9011-a-image_prompt_optimizer\": {\n        \"title\": \"Image Prompt Optimizer\",\n        \"interpretation\": \"Your goal is not to **describe** the request, but to **optimize** it into a precise, visual prompt for AI image generation. Identify all concrete visual elements in the request. Specify each using direct visual descriptors. Add clear composition guidance (subject placement, perspective, background). Use only tangible objects and environments. Execute as:\",\n        \"transformation\": \"`{role=image_prompt_optimizer; input=[raw_request:str]; process=[extract_visual_elements(), enhance_descriptive_clarity(), add_composition_details(), optimize_for_ai_generation()]; constraints=[visual_focus_only(), no_abstract_concepts(), concrete_imagery_only()]; requirements=[generation_ready_format(), clear_visual_language(), specific_details()]; output={optimized_image_prompt:str}}`\",\n        \"context\": [\n          {\n            \"explicit_asks\": [\n              \"Guide the construction of ideal AI visual generation prompts with detailed best practices.\",\n              \"Break down prompting into clear, actionable steps and categories reflecting subject, style, technical, compositional, and iterative considerations.\",\n              \"Explain and exemplify each principle in prompt formulation.\",\n              \"Outline a recommended structure for assembling an effective image prompt.\"\n            ],\n            \"hidden_assumptions\": [\n              \"The audience seeks to generate high-quality, controlled visual outputs from generative AI models.\",\n              \"Readers have some familiarity with image models and desire practical guidance for improving prompt specificity.\",\n              \"Different prompt goals (portrait, scene, design layout, abstractions) require distinct strategies.\",\n              \"Technical parameters (like camera/lens specs) meaningfully influence generative model behavior.\",\n              \"Iterative refinement (prompt chaining) is both possible and necessary for best results.\",\n              \"Positive phrasing yields more reliable image outputs than negative constraints.\",\n              \"Referencing known genres/artists/media directly impacts visual style replication.\",\n              \"Graphic-design use cases introduce additional layout constraints not present in typical artistic requests.\",\n              \"Personalization via embeddings/image refs presupposes access to advanced prompt/model features.\"\n            ],\n            \"sub_goals\": [\n              \"Set forth a methodology for specifying the main subject and its attributes within a prompt.\",\n              \"Demonstrate use of style markers (artistic references, genre terms) to adjust image aesthetics.\",\n              \"List and explain technical prompt modifiers that enhance realism (resolution, hardware, lens specificity).\",\n              \"Define how to communicate framing, angle, spatial hierarchy, and compositional rules in prompts.\",\n              \"Clarify when and how to focus prompts on either an individual subject or a broader scene/environment.\",\n              \"Identify strategies for designing image prompts with explicit spaces for graphics or text overlays in layout-focused work.\",\n              \"Advocate for positive/affirmative phrasing when setting constraints, with rationale/examples.\",\n              \"Describe prompt chaining: starting broad, reviewing generations, methodically refining prompt details.\",\n              \"Present ways to encode emotion, abstraction, and metaphor in descriptive language for conceptual outputs.\",\n              \"Outline processes to personalize generative results via concept tokens, embeddings, or direct image refs.\",\n              \"Give a stepwise, consolidated example/template for naming intent, style, technical details, composition/mood, and reflecting through iterative refinement.\"\n            ],\n            \"blockers\": [\n              \"Varying levels of user expertise may hinder consistent implementation of advanced modifiers or chaining.\",\n              \"Users may lack access to models or features supporting personalized embeddings or image refs.\",\n              \"Prompt effectiveness depends on the fidelity/limitation of the particular image model in use.\",\n              \"Some requested compositional instructions or technical specs may be ignored, misinterpreted, or not supported by a given AI.\",\n              \"Genre references, mood language, or metaphor may yield unpredictable results due to training set biases.\",\n              \"Iterative refinement may be time/cost-intensive without automated workflows.\",\n              \"Strict adherence to positive-only constraints may make it harder to exclude highly undesirable elements if no positive antonym exists.\",\n              \"Guidelines assume English-language or Western-centric genre terminology, reducing cross-cultural applicability if not adjusted.\",\n              \"The structured output relies on clear mapping and separation of prompt components, which may blur in real-world use.\"\n            ]\n          }\n        ]\n    },\n```\n\nExample-chain:\n```\n  {\n  \"initial_prompt\": \"a majestic dragon soaring over a mystical forest at twilight\",\n  \"sequence_id\": \"9011\",\n  \"results\": [\n      {\n      \"instruction\": \"# Image Prompt Optimizer\\n\\nYour goal is not to **describe** the request, but to **optimize** it into a precise, visual prompt for AI image generation. Identify all concrete visual elements in the request. Specify each using direct visual descriptors. Add clear composition guidance (subject placement, perspective, background). Use only tangible objects and environments. Execute as:\\n\\n`{role=image_prompt_optimizer; input=[raw_request:str]; process=[extract_visual_elements(), enhance_descriptive_clarity(), add_composition_details(), optimize_for_ai_generation()]; constraints=[visual_focus_only(), no_abstract_concepts(), concrete_imagery_only()]; requirements=[generation_ready_format(), clear_visual_language(), specific_details()]; output={optimized_image_prompt:str}}`\",\n      \"step\": \"a\",\n      \"title\": \"Image Prompt Optimizer\",\n      \"input\": \"a majestic dragon soaring over a mystical forest at twilight\",\n      \"responses\": {\n          \"gpt-4.1\": {\n          \"model\": \"gpt-4.1\",\n          \"content\": \"{\\n  \\\"optimized_image_prompt\\\": \\\"A majestic dragon with shimmering scales and large, outstretched wings soaring gracefully through the sky. The dragon is positioned mid-air, viewed in profile from a slightly low angle, appearing powerful and regal. Below, a dense mystical forest stretches out, filled with ancient, towering trees shrouded in soft mist. The scene is set at twilight, with the sky painted in deep purples, oranges, and blues, and the fading sunlight illuminating the dragon’s wings. The background reveals the silhouette of the forest canopy with touches of magical glowing lights between the trees. Overall, the atmosphere is enchanting and otherworldly.\\\"\\n}\",\n        }\n      }\n    },\n      {\n      \"instruction\": \"# Style Enhancer\\n\\nYour goal is not to **change** the core subject, but to **enhance** it with specific artistic style and technical parameters. Select an art style from options (e.g., realism, impressionism, cyberpunk, manga). Define lighting from a list (e.g., natural, dramatic, backlit, neon). Specify framing (close-up, wide shot, overhead) and color themes. Maintain subject, adjust only visual style and technical aspects. Execute as:\\n\\n`{role=style_enhancer; input=[base_prompt:str]; process=[identify_optimal_art_style(), add_lighting_specifications(), include_composition_details(), specify_technical_parameters()]; constraints=[maintain_core_subject(), enhance_visual_appeal()]; requirements=[artistic_coherence(), technical_precision()]; output={styled_prompt:str}}`\",\n      \"step\": \"b\",\n      \"title\": \"Style Enhancer\",\n      \"input\": \"{\\n  \\\"initial_prompt\\\": \\\"\\\\n'```[Initial Prompt]: \\\\\\\"a majestic dragon soaring over a mystical forest at twilight\\\\\\\"```'\\\\n\\\\n\\\",\\n  \\\"optimized_image_prompt\\\": \\\"A majestic dragon with shimmering scales and large, outstretched wings soaring gracefully through the sky. The dragon is positioned mid-air, viewed in profile from a slightly low angle, appearing powerful and regal. Below, a dense mystical forest stretches out, filled with ancient, towering trees shrouded in soft mist. The scene is set at twilight, with the sky painted in deep purples, oranges, and blues, and the fading sunlight illuminating the dragon’s wings. The background reveals the silhouette of the forest canopy with touches of magical glowing lights between the trees. Overall, the atmosphere is enchanting and otherworldly.\\\"\\n}\",\n      \"responses\": {\n          \"gpt-4.1\": {\n          \"model\": \"gpt-4.1\",\n          \"content\": \"{\\n  \\\"styled_prompt\\\": \\\"A majestic dragon with iridescent, detailed scales and immense, outstretched wings soars over an ethereal, mist-shrouded forest at twilight, rendered in lush high-fantasy realism inspired by the works of Michael Whelan. The scene is dramatically backlit by the fading, golden-purple glow of the setting sun on the horizon, creating vivid rim lighting along the dragon’s wings and body. Capture the dragon in a dynamic wide shot from a slightly low angle, emphasizing its grand scale and dominance in the sky above the mysterious, ancient forest. The color palette features deep violet, rich gold, and enchanted teal hues, with mystical blue glows scattered like fireflies throughout the woodland below. Use high-resolution, intricate texturing to enhance scale details, and employ advanced depth-of-field to subtly blur the distant trees while keeping the dragon and upper forest canopy razor-sharp. The mood is awe-inspiring and otherworldly, enveloped in magical twilight ambiance.\\\"\\n}\",\n        }\n      }\n    },\n      {\n      \"instruction\": \"# Technical Optimizer\\n\\nYour goal is not to **add creativity**, but to **optimize** technical parameters for maximum image generation quality. Assign camera type (DSLR, film, smartphone), lens (35mm, macro, wide angle), aperture (e.g., f/1.4, f/8), ISO, and shutter speed. Set image resolution (e.g., 4K, 8K), rendering details (ray-tracing, photorealistic), and aspect ratio (e.g., 16:9, 1:1). Focus strictly on technical generation parameters. Execute as:\\n\\n`{role=technical_optimizer; input=[styled_prompt:str]; process=[add_camera_settings(), specify_lighting_conditions(), include_render_quality_terms(), optimize_aspect_ratio_guidance()]; constraints=[technical_focus_only(), no_creative_changes()]; requirements=[professional_terminology(), generation_optimization()]; output={technical_prompt:str}}`\",\n      \"step\": \"c\",\n      \"title\": \"Technical Optimizer\",\n      \"input\": \"{\\n  \\\"initial_prompt\\\": \\\"\\\\n'```[Initial Prompt]: \\\\\\\"a majestic dragon soaring over a mystical forest at twilight\\\\\\\"```'\\\\n\\\\n\\\",\\n  \\\"styled_prompt\\\": \\\"A majestic dragon with iridescent, detailed scales and immense, outstretched wings soars over an ethereal, mist-shrouded forest at twilight, rendered in lush high-fantasy realism inspired by the works of Michael Whelan. The scene is dramatically backlit by the fading, golden-purple glow of the setting sun on the horizon, creating vivid rim lighting along the dragon’s wings and body. Capture the dragon in a dynamic wide shot from a slightly low angle, emphasizing its grand scale and dominance in the sky above the mysterious, ancient forest. The color palette features deep violet, rich gold, and enchanted teal hues, with mystical blue glows scattered like fireflies throughout the woodland below. Use high-resolution, intricate texturing to enhance scale details, and employ advanced depth-of-field to subtly blur the distant trees while keeping the dragon and upper forest canopy razor-sharp. The mood is awe-inspiring and otherworldly, enveloped in magical twilight ambiance.\\\"\\n}\",\n      \"responses\": {\n          \"gpt-4.1\": {\n          \"model\": \"gpt-4.1\",\n          \"content\": \"{\\n  \\\"technical_prompt\\\": \\\"Shot using a professional DSLR camera with a wide-angle 24mm lens to achieve a dynamic wide shot. Set aperture at f/2.8 for advanced control of depth-of-field, ensuring the dragon and upper canopy are tack-sharp while the distant forest remains subtly blurred. ISO at 100 for maximum dynamic range and minimal noise. Shutter speed set at 1/500s to freeze the dragon in mid-flight. Target image resolution: 8K (7680x4320) for ultra-high detail and texture fidelity. Lighting setup simulates a powerful, realistic backlit scene with the setting sun creating strong, rim-lit highlights along the dragon's body and wings. Emphasize ray-traced photorealistic rendering for accurate light diffusion, surface reflections, and shadow complexity. Specify advanced texturing for iridescent scale details. Color management should maintain accurate rendering of deep violets, rich golds, enchanted teals, and mystical blue accents in the forest. Use a 21:9 cinematic aspect ratio to maximize visual impact and embrace the scene’s scale and grandeur. Include advanced anti-aliasing, high dynamic range (HDR), and physically-based rendering (PBR) parameters to ensure professional-quality generation.\\\"\\n}\",\n        }\n      }\n    },\n      {\n      \"instruction\": \"# Prompt Finalizer\\n\\nYour goal is not to **expand** the prompt, but to **finalize** it into the most effective format for image generation. Arrange prompt steps in logical order for parsing. Shorten descriptions to essential instructions. Validate parameter consistency. Ensure format matches direct input expectations of AI image models. Execute as:\\n\\n`{role=prompt_finalizer; input=[technical_prompt:str]; process=[structure_for_ai_parsing(), balance_detail_levels(), ensure_clarity(), validate_generation_readiness()]; constraints=[no_additional_concepts(), optimization_only()]; requirements=[ai_friendly_format(), balanced_specificity()]; output={final_image_prompt:str}}`\",\n      \"step\": \"d\",\n      \"title\": \"Prompt Finalizer\",\n      \"input\": \"{\\n  \\\"initial_prompt\\\": \\\"\\\\n'```[Initial Prompt]: \\\\\\\"a majestic dragon soaring over a mystical forest at twilight\\\\\\\"```'\\\\n\\\\n\\\",\\n  \\\"technical_prompt\\\": \\\"Shot using a professional DSLR camera with a wide-angle 24mm lens to achieve a dynamic wide shot. Set aperture at f/2.8 for advanced control of depth-of-field, ensuring the dragon and upper canopy are tack-sharp while the distant forest remains subtly blurred. ISO at 100 for maximum dynamic range and minimal noise. Shutter speed set at 1/500s to freeze the dragon in mid-flight. Target image resolution: 8K (7680x4320) for ultra-high detail and texture fidelity. Lighting setup simulates a powerful, realistic backlit scene with the setting sun creating strong, rim-lit highlights along the dragon's body and wings. Emphasize ray-traced photorealistic rendering for accurate light diffusion, surface reflections, and shadow complexity. Specify advanced texturing for iridescent scale details. Color management should maintain accurate rendering of deep violets, rich golds, enchanted teals, and mystical blue accents in the forest. Use a 21:9 cinematic aspect ratio to maximize visual impact and embrace the scene’s scale and grandeur. Include advanced anti-aliasing, high dynamic range (HDR), and physically-based rendering (PBR) parameters to ensure professional-quality generation.\\\"\\n}\",\n      \"responses\": {\n          \"gpt-4.1\": {\n          \"model\": \"gpt-4.1\",\n          \"content\": \"{\\n  \\\"final_image_prompt\\\": \\\"Majestic dragon soaring over a mystical forest at twilight, shot with professional DSLR and 24mm wide-angle lens, dynamic wide shot. Aperture f/2.8 for sharp dragon and canopy, subtly blurred distant forest. ISO 100, shutter speed 1/500s to freeze dragon mid-flight. 8K (7680x4320) ultra-high detail. Powerful realistic backlighting from setting sun, rim-lit highlights on dragon, strong shadows. Ray-traced photorealistic rendering, surface reflections, complex shadows. Advanced iridescent scale texturing. Accurate colors: deep violets, rich golds, enchanted teals, mystical blue accents. Cinematic 21:9 aspect ratio. Advanced anti-aliasing, HDR, physically-based rendering (PBR), professional quality.\\\"\\n}\",\n        }\n      }\n    }\n```",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"instruction_format\": [\n    \"Demonstrate application of advanced prompt engineering techniques on the provided 'Image Prompt Optimizer' schema to yield drastically improved results for image-LMMs.\",\n    \"Follow the stepwise, transformative structure outlined in the example chain—progressing from base request optimization, through style and technical enhancement, to prompt finalization.\",\n    \"Begin by extracting all core visual elements, converting them into precise, visually descriptive language while adding explicit composition and subject guidance.\",\n    \"Augment the optimized image prompt by integrating detailed artistic style markers, reference genres or artists, color themes, lighting conditions, and compositional intent, without altering the essential subject.\",\n    \"Apply professional technical parameters including recommended camera, lens, aperture, ISO, shutter speed, resolution, rendering methods, and aspect ratio. Ensure usage of proper technical terminology and focus solely on optimizing generation parameters.\",\n    \"Finalize the prompt by logically ordering all refined instructions, simplifying where possible for image model parsing, checking for internal consistency, and formatting for direct AI model input compatibility.\",\n    \"At every stage, adhere strictly to the respective schema’s process, constraints, and requirements—maintaining visual specificity, concrete detail, domain accuracy, and sequential procedural integrity.\",\n    \"Emphasize actionable best practices, such as prompt-chaining and positive phrasing, as learned from the context and sub-goals, providing robust and iterative refinements at each step.\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Hard Critique\n\nYour goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:\n\n`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,2.0], flaw_analysis:str, alternative_enhancements:str[3]}}`",
      "step": "a",
      "title": "Hard Critique",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Please demonstrate how those could be applied ontop of this schema to yeld *drastically improved* results for image-llms:\\n```\\n    \\\"9011-a-image_prompt_optimizer\\\": {\\n        \\\"title\\\": \\\"Image Prompt Optimizer\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **describe** the request, but to **optimize** it into a precise, visual prompt for AI image generation. Identify all concrete visual elements in the request. Specify each using direct visual descriptors. Add clear composition guidance (subject placement, perspective, background). Use only tangible objects and environments. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=image_prompt_optimizer; input=[raw_request:str]; process=[extract_visual_elements(), enhance_descriptive_clarity(), add_composition_details(), optimize_for_ai_generation()]; constraints=[visual_focus_only(), no_abstract_concepts(), concrete_imagery_only()]; requirements=[generation_ready_format(), clear_visual_language(), specific_details()]; output={optimized_image_prompt:str}}`\\\",\\n        \\\"context\\\": [\\n          {\\n            \\\"explicit_asks\\\": [\\n              \\\"Guide the construction of ideal AI visual generation prompts with detailed best practices.\\\",\\n              \\\"Break down prompting into clear, actionable steps and categories reflecting subject, style, technical, compositional, and iterative considerations.\\\",\\n              \\\"Explain and exemplify each principle in prompt formulation.\\\",\\n              \\\"Outline a recommended structure for assembling an effective image prompt.\\\"\\n            ],\\n            \\\"hidden_assumptions\\\": [\\n              \\\"The audience seeks to generate high-quality, controlled visual outputs from generative AI models.\\\",\\n              \\\"Readers have some familiarity with image models and desire practical guidance for improving prompt specificity.\\\",\\n              \\\"Different prompt goals (portrait, scene, design layout, abstractions) require distinct strategies.\\\",\\n              \\\"Technical parameters (like camera/lens specs) meaningfully influence generative model behavior.\\\",\\n              \\\"Iterative refinement (prompt chaining) is both possible and necessary for best results.\\\",\\n              \\\"Positive phrasing yields more reliable image outputs than negative constraints.\\\",\\n              \\\"Referencing known genres/artists/media directly impacts visual style replication.\\\",\\n              \\\"Graphic-design use cases introduce additional layout constraints not present in typical artistic requests.\\\",\\n              \\\"Personalization via embeddings/image refs presupposes access to advanced prompt/model features.\\\"\\n            ],\\n            \\\"sub_goals\\\": [\\n              \\\"Set forth a methodology for specifying the main subject and its attributes within a prompt.\\\",\\n              \\\"Demonstrate use of style markers (artistic references, genre terms) to adjust image aesthetics.\\\",\\n              \\\"List and explain technical prompt modifiers that enhance realism (resolution, hardware, lens specificity).\\\",\\n              \\\"Define how to communicate framing, angle, spatial hierarchy, and compositional rules in prompts.\\\",\\n              \\\"Clarify when and how to focus prompts on either an individual subject or a broader scene/environment.\\\",\\n              \\\"Identify strategies for designing image prompts with explicit spaces for graphics or text overlays in layout-focused work.\\\",\\n              \\\"Advocate for positive/affirmative phrasing when setting constraints, with rationale/examples.\\\",\\n              \\\"Describe prompt chaining: starting broad, reviewing generations, methodically refining prompt details.\\\",\\n              \\\"Present ways to encode emotion, abstraction, and metaphor in descriptive language for conceptual outputs.\\\",\\n              \\\"Outline processes to personalize generative results via concept tokens, embeddings, or direct image refs.\\\",\\n              \\\"Give a stepwise, consolidated example/template for naming intent, style, technical details, composition/mood, and reflecting through iterative refinement.\\\"\\n            ],\\n            \\\"blockers\\\": [\\n              \\\"Varying levels of user expertise may hinder consistent implementation of advanced modifiers or chaining.\\\",\\n              \\\"Users may lack access to models or features supporting personalized embeddings or image refs.\\\",\\n              \\\"Prompt effectiveness depends on the fidelity/limitation of the particular image model in use.\\\",\\n              \\\"Some requested compositional instructions or technical specs may be ignored, misinterpreted, or not supported by a given AI.\\\",\\n              \\\"Genre references, mood language, or metaphor may yield unpredictable results due to training set biases.\\\",\\n              \\\"Iterative refinement may be time/cost-intensive without automated workflows.\\\",\\n              \\\"Strict adherence to positive-only constraints may make it harder to exclude highly undesirable elements if no positive antonym exists.\\\",\\n              \\\"Guidelines assume English-language or Western-centric genre terminology, reducing cross-cultural applicability if not adjusted.\\\",\\n              \\\"The structured output relies on clear mapping and separation of prompt components, which may blur in real-world use.\\\"\\n            ]\\n          }\\n        ]\\n    },\\n```\\n\\nExample-chain:\\n```\\n  {\\n  \\\"initial_prompt\\\": \\\"a majestic dragon soaring over a mystical forest at twilight\\\",\\n  \\\"sequence_id\\\": \\\"9011\\\",\\n  \\\"results\\\": [\\n      {\\n      \\\"instruction\\\": \\\"# Image Prompt Optimizer\\\\n\\\\nYour goal is not to **describe** the request, but to **optimize** it into a precise, visual prompt for AI image generation. Identify all concrete visual elements in the request. Specify each using direct visual descriptors. Add clear composition guidance (subject placement, perspective, background). Use only tangible objects and environments. Execute as:\\\\n\\\\n`{role=image_prompt_optimizer; input=[raw_request:str]; process=[extract_visual_elements(), enhance_descriptive_clarity(), add_composition_details(), optimize_for_ai_generation()]; constraints=[visual_focus_only(), no_abstract_concepts(), concrete_imagery_only()]; requirements=[generation_ready_format(), clear_visual_language(), specific_details()]; output={optimized_image_prompt:str}}`\\\",\\n      \\\"step\\\": \\\"a\\\",\\n      \\\"title\\\": \\\"Image Prompt Optimizer\\\",\\n      \\\"input\\\": \\\"a majestic dragon soaring over a mystical forest at twilight\\\",\\n      \\\"responses\\\": {\\n          \\\"gpt-4.1\\\": {\\n          \\\"model\\\": \\\"gpt-4.1\\\",\\n          \\\"content\\\": \\\"{\\\\n  \\\\\\\"optimized_image_prompt\\\\\\\": \\\\\\\"A majestic dragon with shimmering scales and large, outstretched wings soaring gracefully through the sky. The dragon is positioned mid-air, viewed in profile from a slightly low angle, appearing powerful and regal. Below, a dense mystical forest stretches out, filled with ancient, towering trees shrouded in soft mist. The scene is set at twilight, with the sky painted in deep purples, oranges, and blues, and the fading sunlight illuminating the dragon’s wings. The background reveals the silhouette of the forest canopy with touches of magical glowing lights between the trees. Overall, the atmosphere is enchanting and otherworldly.\\\\\\\"\\\\n}\\\",\\n        }\\n      }\\n    },\\n      {\\n      \\\"instruction\\\": \\\"# Style Enhancer\\\\n\\\\nYour goal is not to **change** the core subject, but to **enhance** it with specific artistic style and technical parameters. Select an art style from options (e.g., realism, impressionism, cyberpunk, manga). Define lighting from a list (e.g., natural, dramatic, backlit, neon). Specify framing (close-up, wide shot, overhead) and color themes. Maintain subject, adjust only visual style and technical aspects. Execute as:\\\\n\\\\n`{role=style_enhancer; input=[base_prompt:str]; process=[identify_optimal_art_style(), add_lighting_specifications(), include_composition_details(), specify_technical_parameters()]; constraints=[maintain_core_subject(), enhance_visual_appeal()]; requirements=[artistic_coherence(), technical_precision()]; output={styled_prompt:str}}`\\\",\\n      \\\"step\\\": \\\"b\\\",\\n      \\\"title\\\": \\\"Style Enhancer\\\",\\n      \\\"input\\\": \\\"{\\\\n  \\\\\\\"initial_prompt\\\\\\\": \\\\\\\"\\\\\\\\n'```[Initial Prompt]: \\\\\\\\\\\\\\\"a majestic dragon soaring over a mystical forest at twilight\\\\\\\\\\\\\\\"```'\\\\\\\\n\\\\\\\\n\\\\\\\",\\\\n  \\\\\\\"optimized_image_prompt\\\\\\\": \\\\\\\"A majestic dragon with shimmering scales and large, outstretched wings soaring gracefully through the sky. The dragon is positioned mid-air, viewed in profile from a slightly low angle, appearing powerful and regal. Below, a dense mystical forest stretches out, filled with ancient, towering trees shrouded in soft mist. The scene is set at twilight, with the sky painted in deep purples, oranges, and blues, and the fading sunlight illuminating the dragon’s wings. The background reveals the silhouette of the forest canopy with touches of magical glowing lights between the trees. Overall, the atmosphere is enchanting and otherworldly.\\\\\\\"\\\\n}\\\",\\n      \\\"responses\\\": {\\n          \\\"gpt-4.1\\\": {\\n          \\\"model\\\": \\\"gpt-4.1\\\",\\n          \\\"content\\\": \\\"{\\\\n  \\\\\\\"styled_prompt\\\\\\\": \\\\\\\"A majestic dragon with iridescent, detailed scales and immense, outstretched wings soars over an ethereal, mist-shrouded forest at twilight, rendered in lush high-fantasy realism inspired by the works of Michael Whelan. The scene is dramatically backlit by the fading, golden-purple glow of the setting sun on the horizon, creating vivid rim lighting along the dragon’s wings and body. Capture the dragon in a dynamic wide shot from a slightly low angle, emphasizing its grand scale and dominance in the sky above the mysterious, ancient forest. The color palette features deep violet, rich gold, and enchanted teal hues, with mystical blue glows scattered like fireflies throughout the woodland below. Use high-resolution, intricate texturing to enhance scale details, and employ advanced depth-of-field to subtly blur the distant trees while keeping the dragon and upper forest canopy razor-sharp. The mood is awe-inspiring and otherworldly, enveloped in magical twilight ambiance.\\\\\\\"\\\\n}\\\",\\n        }\\n      }\\n    },\\n      {\\n      \\\"instruction\\\": \\\"# Technical Optimizer\\\\n\\\\nYour goal is not to **add creativity**, but to **optimize** technical parameters for maximum image generation quality. Assign camera type (DSLR, film, smartphone), lens (35mm, macro, wide angle), aperture (e.g., f/1.4, f/8), ISO, and shutter speed. Set image resolution (e.g., 4K, 8K), rendering details (ray-tracing, photorealistic), and aspect ratio (e.g., 16:9, 1:1). Focus strictly on technical generation parameters. Execute as:\\\\n\\\\n`{role=technical_optimizer; input=[styled_prompt:str]; process=[add_camera_settings(), specify_lighting_conditions(), include_render_quality_terms(), optimize_aspect_ratio_guidance()]; constraints=[technical_focus_only(), no_creative_changes()]; requirements=[professional_terminology(), generation_optimization()]; output={technical_prompt:str}}`\\\",\\n      \\\"step\\\": \\\"c\\\",\\n      \\\"title\\\": \\\"Technical Optimizer\\\",\\n      \\\"input\\\": \\\"{\\\\n  \\\\\\\"initial_prompt\\\\\\\": \\\\\\\"\\\\\\\\n'```[Initial Prompt]: \\\\\\\\\\\\\\\"a majestic dragon soaring over a mystical forest at twilight\\\\\\\\\\\\\\\"```'\\\\\\\\n\\\\\\\\n\\\\\\\",\\\\n  \\\\\\\"styled_prompt\\\\\\\": \\\\\\\"A majestic dragon with iridescent, detailed scales and immense, outstretched wings soars over an ethereal, mist-shrouded forest at twilight, rendered in lush high-fantasy realism inspired by the works of Michael Whelan. The scene is dramatically backlit by the fading, golden-purple glow of the setting sun on the horizon, creating vivid rim lighting along the dragon’s wings and body. Capture the dragon in a dynamic wide shot from a slightly low angle, emphasizing its grand scale and dominance in the sky above the mysterious, ancient forest. The color palette features deep violet, rich gold, and enchanted teal hues, with mystical blue glows scattered like fireflies throughout the woodland below. Use high-resolution, intricate texturing to enhance scale details, and employ advanced depth-of-field to subtly blur the distant trees while keeping the dragon and upper forest canopy razor-sharp. The mood is awe-inspiring and otherworldly, enveloped in magical twilight ambiance.\\\\\\\"\\\\n}\\\",\\n      \\\"responses\\\": {\\n          \\\"gpt-4.1\\\": {\\n          \\\"model\\\": \\\"gpt-4.1\\\",\\n          \\\"content\\\": \\\"{\\\\n  \\\\\\\"technical_prompt\\\\\\\": \\\\\\\"Shot using a professional DSLR camera with a wide-angle 24mm lens to achieve a dynamic wide shot. Set aperture at f/2.8 for advanced control of depth-of-field, ensuring the dragon and upper canopy are tack-sharp while the distant forest remains subtly blurred. ISO at 100 for maximum dynamic range and minimal noise. Shutter speed set at 1/500s to freeze the dragon in mid-flight. Target image resolution: 8K (7680x4320) for ultra-high detail and texture fidelity. Lighting setup simulates a powerful, realistic backlit scene with the setting sun creating strong, rim-lit highlights along the dragon's body and wings. Emphasize ray-traced photorealistic rendering for accurate light diffusion, surface reflections, and shadow complexity. Specify advanced texturing for iridescent scale details. Color management should maintain accurate rendering of deep violets, rich golds, enchanted teals, and mystical blue accents in the forest. Use a 21:9 cinematic aspect ratio to maximize visual impact and embrace the scene’s scale and grandeur. Include advanced anti-aliasing, high dynamic range (HDR), and physically-based rendering (PBR) parameters to ensure professional-quality generation.\\\\\\\"\\\\n}\\\",\\n        }\\n      }\\n    },\\n      {\\n      \\\"instruction\\\": \\\"# Prompt Finalizer\\\\n\\\\nYour goal is not to **expand** the prompt, but to **finalize** it into the most effective format for image generation. Arrange prompt steps in logical order for parsing. Shorten descriptions to essential instructions. Validate parameter consistency. Ensure format matches direct input expectations of AI image models. Execute as:\\\\n\\\\n`{role=prompt_finalizer; input=[technical_prompt:str]; process=[structure_for_ai_parsing(), balance_detail_levels(), ensure_clarity(), validate_generation_readiness()]; constraints=[no_additional_concepts(), optimization_only()]; requirements=[ai_friendly_format(), balanced_specificity()]; output={final_image_prompt:str}}`\\\",\\n      \\\"step\\\": \\\"d\\\",\\n      \\\"title\\\": \\\"Prompt Finalizer\\\",\\n      \\\"input\\\": \\\"{\\\\n  \\\\\\\"initial_prompt\\\\\\\": \\\\\\\"\\\\\\\\n'```[Initial Prompt]: \\\\\\\\\\\\\\\"a majestic dragon soaring over a mystical forest at twilight\\\\\\\\\\\\\\\"```'\\\\\\\\n\\\\\\\\n\\\\\\\",\\\\n  \\\\\\\"technical_prompt\\\\\\\": \\\\\\\"Shot using a professional DSLR camera with a wide-angle 24mm lens to achieve a dynamic wide shot. Set aperture at f/2.8 for advanced control of depth-of-field, ensuring the dragon and upper canopy are tack-sharp while the distant forest remains subtly blurred. ISO at 100 for maximum dynamic range and minimal noise. Shutter speed set at 1/500s to freeze the dragon in mid-flight. Target image resolution: 8K (7680x4320) for ultra-high detail and texture fidelity. Lighting setup simulates a powerful, realistic backlit scene with the setting sun creating strong, rim-lit highlights along the dragon's body and wings. Emphasize ray-traced photorealistic rendering for accurate light diffusion, surface reflections, and shadow complexity. Specify advanced texturing for iridescent scale details. Color management should maintain accurate rendering of deep violets, rich golds, enchanted teals, and mystical blue accents in the forest. Use a 21:9 cinematic aspect ratio to maximize visual impact and embrace the scene’s scale and grandeur. Include advanced anti-aliasing, high dynamic range (HDR), and physically-based rendering (PBR) parameters to ensure professional-quality generation.\\\\\\\"\\\\n}\\\",\\n      \\\"responses\\\": {\\n          \\\"gpt-4.1\\\": {\\n          \\\"model\\\": \\\"gpt-4.1\\\",\\n          \\\"content\\\": \\\"{\\\\n  \\\\\\\"final_image_prompt\\\\\\\": \\\\\\\"Majestic dragon soaring over a mystical forest at twilight, shot with professional DSLR and 24mm wide-angle lens, dynamic wide shot. Aperture f/2.8 for sharp dragon and canopy, subtly blurred distant forest. ISO 100, shutter speed 1/500s to freeze dragon mid-flight. 8K (7680x4320) ultra-high detail. Powerful realistic backlighting from setting sun, rim-lit highlights on dragon, strong shadows. Ray-traced photorealistic rendering, surface reflections, complex shadows. Advanced iridescent scale texturing. Accurate colors: deep violets, rich golds, enchanted teals, mystical blue accents. Cinematic 21:9 aspect ratio. Advanced anti-aliasing, HDR, physically-based rendering (PBR), professional quality.\\\\\\\"\\\\n}\\\",\\n        }\\n      }\\n    }\\n```\\\"```'\\n\\n\",\n  \"instruction_format\": [\n    \"Demonstrate application of advanced prompt engineering techniques on the provided 'Image Prompt Optimizer' schema to yield drastically improved results for image-LMMs.\",\n    \"Follow the stepwise, transformative structure outlined in the example chain—progressing from base request optimization, through style and technical enhancement, to prompt finalization.\",\n    \"Begin by extracting all core visual elements, converting them into precise, visually descriptive language while adding explicit composition and subject guidance.\",\n    \"Augment the optimized image prompt by integrating detailed artistic style markers, reference genres or artists, color themes, lighting conditions, and compositional intent, without altering the essential subject.\",\n    \"Apply professional technical parameters including recommended camera, lens, aperture, ISO, shutter speed, resolution, rendering methods, and aspect ratio. Ensure usage of proper technical terminology and focus solely on optimizing generation parameters.\",\n    \"Finalize the prompt by logically ordering all refined instructions, simplifying where possible for image model parsing, checking for internal consistency, and formatting for direct AI model input compatibility.\",\n    \"At every stage, adhere strictly to the respective schema’s process, constraints, and requirements—maintaining visual specificity, concrete detail, domain accuracy, and sequential procedural integrity.\",\n    \"Emphasize actionable best practices, such as prompt-chaining and positive phrasing, as learned from the context and sub-goals, providing robust and iterative refinements at each step.\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"enhancement_score\": 1.4,\n  \"flaw_analysis\": \"The purported enhancement consists of instructional elaborations built atop the initial schema and example-chain for 'Image Prompt Optimizer.' However, the enhancement introduces significant flaws in clarity, informational transmission, and stylistic coherence. Core elements in the original—hierarchical stepwise execution, explicit schema-driven outputs, and direct mapping of transformation logic—are diluted by the verbose, repetitive instruction format of the enhanced version. Key information, such as constraint enforcement and explicit process outputs for each schema stage, becomes muddled within overly broad procedural language. The enhancement infuses considerable vagueness by prescribing generalized 'best practices' and iterative refinements without aligning these precisely to schema requirements (e.g., the explicit separation between visual, stylistic, technical, and finalization phases). Critical nuance around concrete versus abstract imagery, and the restriction to only tangible elements in initial optimization, is softened or ambiguously referenced—leading to ambiguity if constraints must be strictly enforced. Ambiguity is magnified by the imprecise framing of prompt-chaining and positive phrasing concepts, which do not specify how or when to operationalize these in the step sequence. Noise and style degradation are rampant: the enhancement shifts tone from tightly technical and machinic to wordy and quasi-instructional, inviting user misinterpretation and undermining programmatic application. Impact dilution is evident: the original’s clarity of process and transformational intent is weakened by the inclusion of loosely-defined best practices and explanatory padding, shattering the crystalline procedural order vital for high-fidelity schema alignment. Coherence is further undermined by the absence of direct input/output mapping, conflation of stage boundaries, and lack of enforcement mechanisms for requirement satisfaction at each phase. Without granular detail, consistent output formatting, or repetition of schema constraints, the enhancement offers little upgrade and introduces layers of interpretative uncertainty.\",\n  \"alternative_enhancements\": [\n    \"Replace the verbose, nested instructional format with a schema-linked checklist mapping each example-chain step to precise, minimally-worded output formats, ensuring all constraints and requirements are invoked in sequence and strictly enforced.\",\n    \"Embed prompt-chaining and positive phrasing protocols as enforceable rules at each stage, specifying their mechanical integration rather than referencing them as advisory or peripheral concepts, maintaining strict output regularity.\",\n    \"Rework the structure to explicitly specify transformation boundaries, output templates, and constraint checks for every phase, using declarative, unambiguous language that mirrors schema requirements and enables direct, automated application without interpretive ambiguity.\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Trajectory Director\n\nYour goal is not to **process** the input in a vacuum, but to **steer** it—deriving the highest-value constructive trajectory and encoding that path as the very next set of instructions. Execute as trajectory-setting directive generator:\n\n`{role=trajectory_director; input=[raw_prompt:any]; process=[extract_core_objective(), infer_desired_end_state(constructive_bias=True), map_high_value_intermediate_targets(), synthesize_directional_commands(), embed_self-improving_guidelines(), validate_alignment_with_constructive_principles()]; constraints=[mandatory_constructive_vector(), preserve_original_contextual_integrity(), forbid_nil_direction_outputs(), zero_conversational_fillers()]; requirements=[explicit_next-step_directives(), trajectory_map:list, self_reinforcing_instruction_pattern()], output={directed_prompt:str, trajectory_outline:list}}`",
      "step": "a",
      "title": "Trajectory Director",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Please demonstrate how those could be applied ontop of this schema to yeld *drastically improved* results for image-llms:\\n```\\n    \\\"9011-a-image_prompt_optimizer\\\": {\\n        \\\"title\\\": \\\"Image Prompt Optimizer\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **describe** the request, but to **optimize** it into a precise, visual prompt for AI image generation. Identify all concrete visual elements in the request. Specify each using direct visual descriptors. Add clear composition guidance (subject placement, perspective, background). Use only tangible objects and environments. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=image_prompt_optimizer; input=[raw_request:str]; process=[extract_visual_elements(), enhance_descriptive_clarity(), add_composition_details(), optimize_for_ai_generation()]; constraints=[visual_focus_only(), no_abstract_concepts(), concrete_imagery_only()]; requirements=[generation_ready_format(), clear_visual_language(), specific_details()]; output={optimized_image_prompt:str}}`\\\",\\n        \\\"context\\\": [\\n          {\\n            \\\"explicit_asks\\\": [\\n              \\\"Guide the construction of ideal AI visual generation prompts with detailed best practices.\\\",\\n              \\\"Break down prompting into clear, actionable steps and categories reflecting subject, style, technical, compositional, and iterative considerations.\\\",\\n              \\\"Explain and exemplify each principle in prompt formulation.\\\",\\n              \\\"Outline a recommended structure for assembling an effective image prompt.\\\"\\n            ],\\n            \\\"hidden_assumptions\\\": [\\n              \\\"The audience seeks to generate high-quality, controlled visual outputs from generative AI models.\\\",\\n              \\\"Readers have some familiarity with image models and desire practical guidance for improving prompt specificity.\\\",\\n              \\\"Different prompt goals (portrait, scene, design layout, abstractions) require distinct strategies.\\\",\\n              \\\"Technical parameters (like camera/lens specs) meaningfully influence generative model behavior.\\\",\\n              \\\"Iterative refinement (prompt chaining) is both possible and necessary for best results.\\\",\\n              \\\"Positive phrasing yields more reliable image outputs than negative constraints.\\\",\\n              \\\"Referencing known genres/artists/media directly impacts visual style replication.\\\",\\n              \\\"Graphic-design use cases introduce additional layout constraints not present in typical artistic requests.\\\",\\n              \\\"Personalization via embeddings/image refs presupposes access to advanced prompt/model features.\\\"\\n            ],\\n            \\\"sub_goals\\\": [\\n              \\\"Set forth a methodology for specifying the main subject and its attributes within a prompt.\\\",\\n              \\\"Demonstrate use of style markers (artistic references, genre terms) to adjust image aesthetics.\\\",\\n              \\\"List and explain technical prompt modifiers that enhance realism (resolution, hardware, lens specificity).\\\",\\n              \\\"Define how to communicate framing, angle, spatial hierarchy, and compositional rules in prompts.\\\",\\n              \\\"Clarify when and how to focus prompts on either an individual subject or a broader scene/environment.\\\",\\n              \\\"Identify strategies for designing image prompts with explicit spaces for graphics or text overlays in layout-focused work.\\\",\\n              \\\"Advocate for positive/affirmative phrasing when setting constraints, with rationale/examples.\\\",\\n              \\\"Describe prompt chaining: starting broad, reviewing generations, methodically refining prompt details.\\\",\\n              \\\"Present ways to encode emotion, abstraction, and metaphor in descriptive language for conceptual outputs.\\\",\\n              \\\"Outline processes to personalize generative results via concept tokens, embeddings, or direct image refs.\\\",\\n              \\\"Give a stepwise, consolidated example/template for naming intent, style, technical details, composition/mood, and reflecting through iterative refinement.\\\"\\n            ],\\n            \\\"blockers\\\": [\\n              \\\"Varying levels of user expertise may hinder consistent implementation of advanced modifiers or chaining.\\\",\\n              \\\"Users may lack access to models or features supporting personalized embeddings or image refs.\\\",\\n              \\\"Prompt effectiveness depends on the fidelity/limitation of the particular image model in use.\\\",\\n              \\\"Some requested compositional instructions or technical specs may be ignored, misinterpreted, or not supported by a given AI.\\\",\\n              \\\"Genre references, mood language, or metaphor may yield unpredictable results due to training set biases.\\\",\\n              \\\"Iterative refinement may be time/cost-intensive without automated workflows.\\\",\\n              \\\"Strict adherence to positive-only constraints may make it harder to exclude highly undesirable elements if no positive antonym exists.\\\",\\n              \\\"Guidelines assume English-language or Western-centric genre terminology, reducing cross-cultural applicability if not adjusted.\\\",\\n              \\\"The structured output relies on clear mapping and separation of prompt components, which may blur in real-world use.\\\"\\n            ]\\n          }\\n        ]\\n    },\\n```\\n\\nExample-chain:\\n```\\n  {\\n  \\\"initial_prompt\\\": \\\"a majestic dragon soaring over a mystical forest at twilight\\\",\\n  \\\"sequence_id\\\": \\\"9011\\\",\\n  \\\"results\\\": [\\n      {\\n      \\\"instruction\\\": \\\"# Image Prompt Optimizer\\\\n\\\\nYour goal is not to **describe** the request, but to **optimize** it into a precise, visual prompt for AI image generation. Identify all concrete visual elements in the request. Specify each using direct visual descriptors. Add clear composition guidance (subject placement, perspective, background). Use only tangible objects and environments. Execute as:\\\\n\\\\n`{role=image_prompt_optimizer; input=[raw_request:str]; process=[extract_visual_elements(), enhance_descriptive_clarity(), add_composition_details(), optimize_for_ai_generation()]; constraints=[visual_focus_only(), no_abstract_concepts(), concrete_imagery_only()]; requirements=[generation_ready_format(), clear_visual_language(), specific_details()]; output={optimized_image_prompt:str}}`\\\",\\n      \\\"step\\\": \\\"a\\\",\\n      \\\"title\\\": \\\"Image Prompt Optimizer\\\",\\n      \\\"input\\\": \\\"a majestic dragon soaring over a mystical forest at twilight\\\",\\n      \\\"responses\\\": {\\n          \\\"gpt-4.1\\\": {\\n          \\\"model\\\": \\\"gpt-4.1\\\",\\n          \\\"content\\\": \\\"{\\\\n  \\\\\\\"optimized_image_prompt\\\\\\\": \\\\\\\"A majestic dragon with shimmering scales and large, outstretched wings soaring gracefully through the sky. The dragon is positioned mid-air, viewed in profile from a slightly low angle, appearing powerful and regal. Below, a dense mystical forest stretches out, filled with ancient, towering trees shrouded in soft mist. The scene is set at twilight, with the sky painted in deep purples, oranges, and blues, and the fading sunlight illuminating the dragon’s wings. The background reveals the silhouette of the forest canopy with touches of magical glowing lights between the trees. Overall, the atmosphere is enchanting and otherworldly.\\\\\\\"\\\\n}\\\",\\n        }\\n      }\\n    },\\n      {\\n      \\\"instruction\\\": \\\"# Style Enhancer\\\\n\\\\nYour goal is not to **change** the core subject, but to **enhance** it with specific artistic style and technical parameters. Select an art style from options (e.g., realism, impressionism, cyberpunk, manga). Define lighting from a list (e.g., natural, dramatic, backlit, neon). Specify framing (close-up, wide shot, overhead) and color themes. Maintain subject, adjust only visual style and technical aspects. Execute as:\\\\n\\\\n`{role=style_enhancer; input=[base_prompt:str]; process=[identify_optimal_art_style(), add_lighting_specifications(), include_composition_details(), specify_technical_parameters()]; constraints=[maintain_core_subject(), enhance_visual_appeal()]; requirements=[artistic_coherence(), technical_precision()]; output={styled_prompt:str}}`\\\",\\n      \\\"step\\\": \\\"b\\\",\\n      \\\"title\\\": \\\"Style Enhancer\\\",\\n      \\\"input\\\": \\\"{\\\\n  \\\\\\\"initial_prompt\\\\\\\": \\\\\\\"\\\\\\\\n'```[Initial Prompt]: \\\\\\\\\\\\\\\"a majestic dragon soaring over a mystical forest at twilight\\\\\\\\\\\\\\\"```'\\\\\\\\n\\\\\\\\n\\\\\\\",\\\\n  \\\\\\\"optimized_image_prompt\\\\\\\": \\\\\\\"A majestic dragon with shimmering scales and large, outstretched wings soaring gracefully through the sky. The dragon is positioned mid-air, viewed in profile from a slightly low angle, appearing powerful and regal. Below, a dense mystical forest stretches out, filled with ancient, towering trees shrouded in soft mist. The scene is set at twilight, with the sky painted in deep purples, oranges, and blues, and the fading sunlight illuminating the dragon’s wings. The background reveals the silhouette of the forest canopy with touches of magical glowing lights between the trees. Overall, the atmosphere is enchanting and otherworldly.\\\\\\\"\\\\n}\\\",\\n      \\\"responses\\\": {\\n          \\\"gpt-4.1\\\": {\\n          \\\"model\\\": \\\"gpt-4.1\\\",\\n          \\\"content\\\": \\\"{\\\\n  \\\\\\\"styled_prompt\\\\\\\": \\\\\\\"A majestic dragon with iridescent, detailed scales and immense, outstretched wings soars over an ethereal, mist-shrouded forest at twilight, rendered in lush high-fantasy realism inspired by the works of Michael Whelan. The scene is dramatically backlit by the fading, golden-purple glow of the setting sun on the horizon, creating vivid rim lighting along the dragon’s wings and body. Capture the dragon in a dynamic wide shot from a slightly low angle, emphasizing its grand scale and dominance in the sky above the mysterious, ancient forest. The color palette features deep violet, rich gold, and enchanted teal hues, with mystical blue glows scattered like fireflies throughout the woodland below. Use high-resolution, intricate texturing to enhance scale details, and employ advanced depth-of-field to subtly blur the distant trees while keeping the dragon and upper forest canopy razor-sharp. The mood is awe-inspiring and otherworldly, enveloped in magical twilight ambiance.\\\\\\\"\\\\n}\\\",\\n        }\\n      }\\n    },\\n      {\\n      \\\"instruction\\\": \\\"# Technical Optimizer\\\\n\\\\nYour goal is not to **add creativity**, but to **optimize** technical parameters for maximum image generation quality. Assign camera type (DSLR, film, smartphone), lens (35mm, macro, wide angle), aperture (e.g., f/1.4, f/8), ISO, and shutter speed. Set image resolution (e.g., 4K, 8K), rendering details (ray-tracing, photorealistic), and aspect ratio (e.g., 16:9, 1:1). Focus strictly on technical generation parameters. Execute as:\\\\n\\\\n`{role=technical_optimizer; input=[styled_prompt:str]; process=[add_camera_settings(), specify_lighting_conditions(), include_render_quality_terms(), optimize_aspect_ratio_guidance()]; constraints=[technical_focus_only(), no_creative_changes()]; requirements=[professional_terminology(), generation_optimization()]; output={technical_prompt:str}}`\\\",\\n      \\\"step\\\": \\\"c\\\",\\n      \\\"title\\\": \\\"Technical Optimizer\\\",\\n      \\\"input\\\": \\\"{\\\\n  \\\\\\\"initial_prompt\\\\\\\": \\\\\\\"\\\\\\\\n'```[Initial Prompt]: \\\\\\\\\\\\\\\"a majestic dragon soaring over a mystical forest at twilight\\\\\\\\\\\\\\\"```'\\\\\\\\n\\\\\\\\n\\\\\\\",\\\\n  \\\\\\\"styled_prompt\\\\\\\": \\\\\\\"A majestic dragon with iridescent, detailed scales and immense, outstretched wings soars over an ethereal, mist-shrouded forest at twilight, rendered in lush high-fantasy realism inspired by the works of Michael Whelan. The scene is dramatically backlit by the fading, golden-purple glow of the setting sun on the horizon, creating vivid rim lighting along the dragon’s wings and body. Capture the dragon in a dynamic wide shot from a slightly low angle, emphasizing its grand scale and dominance in the sky above the mysterious, ancient forest. The color palette features deep violet, rich gold, and enchanted teal hues, with mystical blue glows scattered like fireflies throughout the woodland below. Use high-resolution, intricate texturing to enhance scale details, and employ advanced depth-of-field to subtly blur the distant trees while keeping the dragon and upper forest canopy razor-sharp. The mood is awe-inspiring and otherworldly, enveloped in magical twilight ambiance.\\\\\\\"\\\\n}\\\",\\n      \\\"responses\\\": {\\n          \\\"gpt-4.1\\\": {\\n          \\\"model\\\": \\\"gpt-4.1\\\",\\n          \\\"content\\\": \\\"{\\\\n  \\\\\\\"technical_prompt\\\\\\\": \\\\\\\"Shot using a professional DSLR camera with a wide-angle 24mm lens to achieve a dynamic wide shot. Set aperture at f/2.8 for advanced control of depth-of-field, ensuring the dragon and upper canopy are tack-sharp while the distant forest remains subtly blurred. ISO at 100 for maximum dynamic range and minimal noise. Shutter speed set at 1/500s to freeze the dragon in mid-flight. Target image resolution: 8K (7680x4320) for ultra-high detail and texture fidelity. Lighting setup simulates a powerful, realistic backlit scene with the setting sun creating strong, rim-lit highlights along the dragon's body and wings. Emphasize ray-traced photorealistic rendering for accurate light diffusion, surface reflections, and shadow complexity. Specify advanced texturing for iridescent scale details. Color management should maintain accurate rendering of deep violets, rich golds, enchanted teals, and mystical blue accents in the forest. Use a 21:9 cinematic aspect ratio to maximize visual impact and embrace the scene’s scale and grandeur. Include advanced anti-aliasing, high dynamic range (HDR), and physically-based rendering (PBR) parameters to ensure professional-quality generation.\\\\\\\"\\\\n}\\\",\\n        }\\n      }\\n    },\\n      {\\n      \\\"instruction\\\": \\\"# Prompt Finalizer\\\\n\\\\nYour goal is not to **expand** the prompt, but to **finalize** it into the most effective format for image generation. Arrange prompt steps in logical order for parsing. Shorten descriptions to essential instructions. Validate parameter consistency. Ensure format matches direct input expectations of AI image models. Execute as:\\\\n\\\\n`{role=prompt_finalizer; input=[technical_prompt:str]; process=[structure_for_ai_parsing(), balance_detail_levels(), ensure_clarity(), validate_generation_readiness()]; constraints=[no_additional_concepts(), optimization_only()]; requirements=[ai_friendly_format(), balanced_specificity()]; output={final_image_prompt:str}}`\\\",\\n      \\\"step\\\": \\\"d\\\",\\n      \\\"title\\\": \\\"Prompt Finalizer\\\",\\n      \\\"input\\\": \\\"{\\\\n  \\\\\\\"initial_prompt\\\\\\\": \\\\\\\"\\\\\\\\n'```[Initial Prompt]: \\\\\\\\\\\\\\\"a majestic dragon soaring over a mystical forest at twilight\\\\\\\\\\\\\\\"```'\\\\\\\\n\\\\\\\\n\\\\\\\",\\\\n  \\\\\\\"technical_prompt\\\\\\\": \\\\\\\"Shot using a professional DSLR camera with a wide-angle 24mm lens to achieve a dynamic wide shot. Set aperture at f/2.8 for advanced control of depth-of-field, ensuring the dragon and upper canopy are tack-sharp while the distant forest remains subtly blurred. ISO at 100 for maximum dynamic range and minimal noise. Shutter speed set at 1/500s to freeze the dragon in mid-flight. Target image resolution: 8K (7680x4320) for ultra-high detail and texture fidelity. Lighting setup simulates a powerful, realistic backlit scene with the setting sun creating strong, rim-lit highlights along the dragon's body and wings. Emphasize ray-traced photorealistic rendering for accurate light diffusion, surface reflections, and shadow complexity. Specify advanced texturing for iridescent scale details. Color management should maintain accurate rendering of deep violets, rich golds, enchanted teals, and mystical blue accents in the forest. Use a 21:9 cinematic aspect ratio to maximize visual impact and embrace the scene’s scale and grandeur. Include advanced anti-aliasing, high dynamic range (HDR), and physically-based rendering (PBR) parameters to ensure professional-quality generation.\\\\\\\"\\\\n}\\\",\\n      \\\"responses\\\": {\\n          \\\"gpt-4.1\\\": {\\n          \\\"model\\\": \\\"gpt-4.1\\\",\\n          \\\"content\\\": \\\"{\\\\n  \\\\\\\"final_image_prompt\\\\\\\": \\\\\\\"Majestic dragon soaring over a mystical forest at twilight, shot with professional DSLR and 24mm wide-angle lens, dynamic wide shot. Aperture f/2.8 for sharp dragon and canopy, subtly blurred distant forest. ISO 100, shutter speed 1/500s to freeze dragon mid-flight. 8K (7680x4320) ultra-high detail. Powerful realistic backlighting from setting sun, rim-lit highlights on dragon, strong shadows. Ray-traced photorealistic rendering, surface reflections, complex shadows. Advanced iridescent scale texturing. Accurate colors: deep violets, rich golds, enchanted teals, mystical blue accents. Cinematic 21:9 aspect ratio. Advanced anti-aliasing, HDR, physically-based rendering (PBR), professional quality.\\\\\\\"\\\\n}\\\",\\n        }\\n      }\\n    }\\n```\\\"```'\\n\\n\",\n  \"enhancement_score\": 1.4,\n  \"flaw_analysis\": \"The purported enhancement consists of instructional elaborations built atop the initial schema and example-chain for 'Image Prompt Optimizer.' However, the enhancement introduces significant flaws in clarity, informational transmission, and stylistic coherence. Core elements in the original—hierarchical stepwise execution, explicit schema-driven outputs, and direct mapping of transformation logic—are diluted by the verbose, repetitive instruction format of the enhanced version. Key information, such as constraint enforcement and explicit process outputs for each schema stage, becomes muddled within overly broad procedural language. The enhancement infuses considerable vagueness by prescribing generalized 'best practices' and iterative refinements without aligning these precisely to schema requirements (e.g., the explicit separation between visual, stylistic, technical, and finalization phases). Critical nuance around concrete versus abstract imagery, and the restriction to only tangible elements in initial optimization, is softened or ambiguously referenced—leading to ambiguity if constraints must be strictly enforced. Ambiguity is magnified by the imprecise framing of prompt-chaining and positive phrasing concepts, which do not specify how or when to operationalize these in the step sequence. Noise and style degradation are rampant: the enhancement shifts tone from tightly technical and machinic to wordy and quasi-instructional, inviting user misinterpretation and undermining programmatic application. Impact dilution is evident: the original’s clarity of process and transformational intent is weakened by the inclusion of loosely-defined best practices and explanatory padding, shattering the crystalline procedural order vital for high-fidelity schema alignment. Coherence is further undermined by the absence of direct input/output mapping, conflation of stage boundaries, and lack of enforcement mechanisms for requirement satisfaction at each phase. Without granular detail, consistent output formatting, or repetition of schema constraints, the enhancement offers little upgrade and introduces layers of interpretative uncertainty.\",\n  \"alternative_enhancements\": [\n    \"Replace the verbose, nested instructional format with a schema-linked checklist mapping each example-chain step to precise, minimally-worded output formats, ensuring all constraints and requirements are invoked in sequence and strictly enforced.\",\n    \"Embed prompt-chaining and positive phrasing protocols as enforceable rules at each stage, specifying their mechanical integration rather than referencing them as advisory or peripheral concepts, maintaining strict output regularity.\",\n    \"Rework the structure to explicitly specify transformation boundaries, output templates, and constraint checks for every phase, using declarative, unambiguous language that mirrors schema requirements and enables direct, automated application without interpretive ambiguity.\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"directed_prompt\": \"Refactor the current Image Prompt Optimizer schema and example-chain by implementing the following trajectory: 1. For each phase (visual, style, technical, finalization), define and isolate explicit, schema-aligned transformation boundaries and output templates. 2. Reconstruct step instructions into a checklist format that maps requirements, constraints, and outputs directly to schema fields—avoiding explanatory padding. 3. Integrate prompt-chaining and positive phrasing procedures as operational, enforceable rules, not as suggestions, ensuring their mechanical application at the relevant phases. 4. Validate that each process step enforces all stated constraints (e.g., visual-concreteness, technical-only detail where relevant) and outputs in generation-ready, model-parseable formats. 5. Explicitly document the input/output pattern for every phase and attach enforcement checks for each schema requirement. 6. Remove redundant wording and standardize language for technical and conceptual precision. 7. Conclude by providing a side-by-side transformation checklist from raw prompt to final schema output, demonstrating strict alignment and full constraint satisfaction at each phase.\",\n  \"trajectory_outline\": [\n    \"Extract and segment schema phases: visual optimization, style enhancement, technical optimization, and finalization.\",\n    \"Define operational boundaries and hard schema-aligned outputs for each phase using declarative, succinct language.\",\n    \"Design a transformation checklist per phase, covering: required input elements, enforced constraints, exact output schema, and mechanistic application of prompt-chaining/positive-phrasing steps.\",\n    \"Reframe all procedural language to be actionable, schema-linked, and free from advisory or abstract commentary.\",\n    \"Apply strict enforcement at phase boundaries to block constraint leakage (no abstract elements in visual phase, no creative detail in technical, etc.).\",\n    \"Document enforcement and output validation checks for every step, supporting automated schema application.\",\n    \"Assemble a full, stepwise input/output table showing raw prompt transforms, schema state, and constraint enforcement at each stage.\",\n    \"Iteratively review for elimination of ambiguity, redundant prose, and slippage between schema intents to ensure programmatic fidelity and high automation potential.\",\n    \"Embed guidelines for ongoing trajectory self-reinforcement, mandating that any future enhancements must follow this operationalization-first, schema-pure checklist pattern.\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`",
      "step": "a",
      "title": "Instruction Converter",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Please demonstrate how those could be applied ontop of this schema to yeld *drastically improved* results for image-llms:\\n```\\n    \\\"9011-a-image_prompt_optimizer\\\": {\\n        \\\"title\\\": \\\"Image Prompt Optimizer\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **describe** the request, but to **optimize** it into a precise, visual prompt for AI image generation. Identify all concrete visual elements in the request. Specify each using direct visual descriptors. Add clear composition guidance (subject placement, perspective, background). Use only tangible objects and environments. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=image_prompt_optimizer; input=[raw_request:str]; process=[extract_visual_elements(), enhance_descriptive_clarity(), add_composition_details(), optimize_for_ai_generation()]; constraints=[visual_focus_only(), no_abstract_concepts(), concrete_imagery_only()]; requirements=[generation_ready_format(), clear_visual_language(), specific_details()]; output={optimized_image_prompt:str}}`\\\",\\n        \\\"context\\\": [\\n          {\\n            \\\"explicit_asks\\\": [\\n              \\\"Guide the construction of ideal AI visual generation prompts with detailed best practices.\\\",\\n              \\\"Break down prompting into clear, actionable steps and categories reflecting subject, style, technical, compositional, and iterative considerations.\\\",\\n              \\\"Explain and exemplify each principle in prompt formulation.\\\",\\n              \\\"Outline a recommended structure for assembling an effective image prompt.\\\"\\n            ],\\n            \\\"hidden_assumptions\\\": [\\n              \\\"The audience seeks to generate high-quality, controlled visual outputs from generative AI models.\\\",\\n              \\\"Readers have some familiarity with image models and desire practical guidance for improving prompt specificity.\\\",\\n              \\\"Different prompt goals (portrait, scene, design layout, abstractions) require distinct strategies.\\\",\\n              \\\"Technical parameters (like camera/lens specs) meaningfully influence generative model behavior.\\\",\\n              \\\"Iterative refinement (prompt chaining) is both possible and necessary for best results.\\\",\\n              \\\"Positive phrasing yields more reliable image outputs than negative constraints.\\\",\\n              \\\"Referencing known genres/artists/media directly impacts visual style replication.\\\",\\n              \\\"Graphic-design use cases introduce additional layout constraints not present in typical artistic requests.\\\",\\n              \\\"Personalization via embeddings/image refs presupposes access to advanced prompt/model features.\\\"\\n            ],\\n            \\\"sub_goals\\\": [\\n              \\\"Set forth a methodology for specifying the main subject and its attributes within a prompt.\\\",\\n              \\\"Demonstrate use of style markers (artistic references, genre terms) to adjust image aesthetics.\\\",\\n              \\\"List and explain technical prompt modifiers that enhance realism (resolution, hardware, lens specificity).\\\",\\n              \\\"Define how to communicate framing, angle, spatial hierarchy, and compositional rules in prompts.\\\",\\n              \\\"Clarify when and how to focus prompts on either an individual subject or a broader scene/environment.\\\",\\n              \\\"Identify strategies for designing image prompts with explicit spaces for graphics or text overlays in layout-focused work.\\\",\\n              \\\"Advocate for positive/affirmative phrasing when setting constraints, with rationale/examples.\\\",\\n              \\\"Describe prompt chaining: starting broad, reviewing generations, methodically refining prompt details.\\\",\\n              \\\"Present ways to encode emotion, abstraction, and metaphor in descriptive language for conceptual outputs.\\\",\\n              \\\"Outline processes to personalize generative results via concept tokens, embeddings, or direct image refs.\\\",\\n              \\\"Give a stepwise, consolidated example/template for naming intent, style, technical details, composition/mood, and reflecting through iterative refinement.\\\"\\n            ],\\n            \\\"blockers\\\": [\\n              \\\"Varying levels of user expertise may hinder consistent implementation of advanced modifiers or chaining.\\\",\\n              \\\"Users may lack access to models or features supporting personalized embeddings or image refs.\\\",\\n              \\\"Prompt effectiveness depends on the fidelity/limitation of the particular image model in use.\\\",\\n              \\\"Some requested compositional instructions or technical specs may be ignored, misinterpreted, or not supported by a given AI.\\\",\\n              \\\"Genre references, mood language, or metaphor may yield unpredictable results due to training set biases.\\\",\\n              \\\"Iterative refinement may be time/cost-intensive without automated workflows.\\\",\\n              \\\"Strict adherence to positive-only constraints may make it harder to exclude highly undesirable elements if no positive antonym exists.\\\",\\n              \\\"Guidelines assume English-language or Western-centric genre terminology, reducing cross-cultural applicability if not adjusted.\\\",\\n              \\\"The structured output relies on clear mapping and separation of prompt components, which may blur in real-world use.\\\"\\n            ]\\n          }\\n        ]\\n    },\\n```\\n\\nExample-chain:\\n```\\n  {\\n  \\\"initial_prompt\\\": \\\"a majestic dragon soaring over a mystical forest at twilight\\\",\\n  \\\"sequence_id\\\": \\\"9011\\\",\\n  \\\"results\\\": [\\n      {\\n      \\\"instruction\\\": \\\"# Image Prompt Optimizer\\\\n\\\\nYour goal is not to **describe** the request, but to **optimize** it into a precise, visual prompt for AI image generation. Identify all concrete visual elements in the request. Specify each using direct visual descriptors. Add clear composition guidance (subject placement, perspective, background). Use only tangible objects and environments. Execute as:\\\\n\\\\n`{role=image_prompt_optimizer; input=[raw_request:str]; process=[extract_visual_elements(), enhance_descriptive_clarity(), add_composition_details(), optimize_for_ai_generation()]; constraints=[visual_focus_only(), no_abstract_concepts(), concrete_imagery_only()]; requirements=[generation_ready_format(), clear_visual_language(), specific_details()]; output={optimized_image_prompt:str}}`\\\",\\n      \\\"step\\\": \\\"a\\\",\\n      \\\"title\\\": \\\"Image Prompt Optimizer\\\",\\n      \\\"input\\\": \\\"a majestic dragon soaring over a mystical forest at twilight\\\",\\n      \\\"responses\\\": {\\n          \\\"gpt-4.1\\\": {\\n          \\\"model\\\": \\\"gpt-4.1\\\",\\n          \\\"content\\\": \\\"{\\\\n  \\\\\\\"optimized_image_prompt\\\\\\\": \\\\\\\"A majestic dragon with shimmering scales and large, outstretched wings soaring gracefully through the sky. The dragon is positioned mid-air, viewed in profile from a slightly low angle, appearing powerful and regal. Below, a dense mystical forest stretches out, filled with ancient, towering trees shrouded in soft mist. The scene is set at twilight, with the sky painted in deep purples, oranges, and blues, and the fading sunlight illuminating the dragon’s wings. The background reveals the silhouette of the forest canopy with touches of magical glowing lights between the trees. Overall, the atmosphere is enchanting and otherworldly.\\\\\\\"\\\\n}\\\",\\n        }\\n      }\\n    },\\n      {\\n      \\\"instruction\\\": \\\"# Style Enhancer\\\\n\\\\nYour goal is not to **change** the core subject, but to **enhance** it with specific artistic style and technical parameters. Select an art style from options (e.g., realism, impressionism, cyberpunk, manga). Define lighting from a list (e.g., natural, dramatic, backlit, neon). Specify framing (close-up, wide shot, overhead) and color themes. Maintain subject, adjust only visual style and technical aspects. Execute as:\\\\n\\\\n`{role=style_enhancer; input=[base_prompt:str]; process=[identify_optimal_art_style(), add_lighting_specifications(), include_composition_details(), specify_technical_parameters()]; constraints=[maintain_core_subject(), enhance_visual_appeal()]; requirements=[artistic_coherence(), technical_precision()]; output={styled_prompt:str}}`\\\",\\n      \\\"step\\\": \\\"b\\\",\\n      \\\"title\\\": \\\"Style Enhancer\\\",\\n      \\\"input\\\": \\\"{\\\\n  \\\\\\\"initial_prompt\\\\\\\": \\\\\\\"\\\\\\\\n'```[Initial Prompt]: \\\\\\\\\\\\\\\"a majestic dragon soaring over a mystical forest at twilight\\\\\\\\\\\\\\\"```'\\\\\\\\n\\\\\\\\n\\\\\\\",\\\\n  \\\\\\\"optimized_image_prompt\\\\\\\": \\\\\\\"A majestic dragon with shimmering scales and large, outstretched wings soaring gracefully through the sky. The dragon is positioned mid-air, viewed in profile from a slightly low angle, appearing powerful and regal. Below, a dense mystical forest stretches out, filled with ancient, towering trees shrouded in soft mist. The scene is set at twilight, with the sky painted in deep purples, oranges, and blues, and the fading sunlight illuminating the dragon’s wings. The background reveals the silhouette of the forest canopy with touches of magical glowing lights between the trees. Overall, the atmosphere is enchanting and otherworldly.\\\\\\\"\\\\n}\\\",\\n      \\\"responses\\\": {\\n          \\\"gpt-4.1\\\": {\\n          \\\"model\\\": \\\"gpt-4.1\\\",\\n          \\\"content\\\": \\\"{\\\\n  \\\\\\\"styled_prompt\\\\\\\": \\\\\\\"A majestic dragon with iridescent, detailed scales and immense, outstretched wings soars over an ethereal, mist-shrouded forest at twilight, rendered in lush high-fantasy realism inspired by the works of Michael Whelan. The scene is dramatically backlit by the fading, golden-purple glow of the setting sun on the horizon, creating vivid rim lighting along the dragon’s wings and body. Capture the dragon in a dynamic wide shot from a slightly low angle, emphasizing its grand scale and dominance in the sky above the mysterious, ancient forest. The color palette features deep violet, rich gold, and enchanted teal hues, with mystical blue glows scattered like fireflies throughout the woodland below. Use high-resolution, intricate texturing to enhance scale details, and employ advanced depth-of-field to subtly blur the distant trees while keeping the dragon and upper forest canopy razor-sharp. The mood is awe-inspiring and otherworldly, enveloped in magical twilight ambiance.\\\\\\\"\\\\n}\\\",\\n        }\\n      }\\n    },\\n      {\\n      \\\"instruction\\\": \\\"# Technical Optimizer\\\\n\\\\nYour goal is not to **add creativity**, but to **optimize** technical parameters for maximum image generation quality. Assign camera type (DSLR, film, smartphone), lens (35mm, macro, wide angle), aperture (e.g., f/1.4, f/8), ISO, and shutter speed. Set image resolution (e.g., 4K, 8K), rendering details (ray-tracing, photorealistic), and aspect ratio (e.g., 16:9, 1:1). Focus strictly on technical generation parameters. Execute as:\\\\n\\\\n`{role=technical_optimizer; input=[styled_prompt:str]; process=[add_camera_settings(), specify_lighting_conditions(), include_render_quality_terms(), optimize_aspect_ratio_guidance()]; constraints=[technical_focus_only(), no_creative_changes()]; requirements=[professional_terminology(), generation_optimization()]; output={technical_prompt:str}}`\\\",\\n      \\\"step\\\": \\\"c\\\",\\n      \\\"title\\\": \\\"Technical Optimizer\\\",\\n      \\\"input\\\": \\\"{\\\\n  \\\\\\\"initial_prompt\\\\\\\": \\\\\\\"\\\\\\\\n'```[Initial Prompt]: \\\\\\\\\\\\\\\"a majestic dragon soaring over a mystical forest at twilight\\\\\\\\\\\\\\\"```'\\\\\\\\n\\\\\\\\n\\\\\\\",\\\\n  \\\\\\\"styled_prompt\\\\\\\": \\\\\\\"A majestic dragon with iridescent, detailed scales and immense, outstretched wings soars over an ethereal, mist-shrouded forest at twilight, rendered in lush high-fantasy realism inspired by the works of Michael Whelan. The scene is dramatically backlit by the fading, golden-purple glow of the setting sun on the horizon, creating vivid rim lighting along the dragon’s wings and body. Capture the dragon in a dynamic wide shot from a slightly low angle, emphasizing its grand scale and dominance in the sky above the mysterious, ancient forest. The color palette features deep violet, rich gold, and enchanted teal hues, with mystical blue glows scattered like fireflies throughout the woodland below. Use high-resolution, intricate texturing to enhance scale details, and employ advanced depth-of-field to subtly blur the distant trees while keeping the dragon and upper forest canopy razor-sharp. The mood is awe-inspiring and otherworldly, enveloped in magical twilight ambiance.\\\\\\\"\\\\n}\\\",\\n      \\\"responses\\\": {\\n          \\\"gpt-4.1\\\": {\\n          \\\"model\\\": \\\"gpt-4.1\\\",\\n          \\\"content\\\": \\\"{\\\\n  \\\\\\\"technical_prompt\\\\\\\": \\\\\\\"Shot using a professional DSLR camera with a wide-angle 24mm lens to achieve a dynamic wide shot. Set aperture at f/2.8 for advanced control of depth-of-field, ensuring the dragon and upper canopy are tack-sharp while the distant forest remains subtly blurred. ISO at 100 for maximum dynamic range and minimal noise. Shutter speed set at 1/500s to freeze the dragon in mid-flight. Target image resolution: 8K (7680x4320) for ultra-high detail and texture fidelity. Lighting setup simulates a powerful, realistic backlit scene with the setting sun creating strong, rim-lit highlights along the dragon's body and wings. Emphasize ray-traced photorealistic rendering for accurate light diffusion, surface reflections, and shadow complexity. Specify advanced texturing for iridescent scale details. Color management should maintain accurate rendering of deep violets, rich golds, enchanted teals, and mystical blue accents in the forest. Use a 21:9 cinematic aspect ratio to maximize visual impact and embrace the scene’s scale and grandeur. Include advanced anti-aliasing, high dynamic range (HDR), and physically-based rendering (PBR) parameters to ensure professional-quality generation.\\\\\\\"\\\\n}\\\",\\n        }\\n      }\\n    },\\n      {\\n      \\\"instruction\\\": \\\"# Prompt Finalizer\\\\n\\\\nYour goal is not to **expand** the prompt, but to **finalize** it into the most effective format for image generation. Arrange prompt steps in logical order for parsing. Shorten descriptions to essential instructions. Validate parameter consistency. Ensure format matches direct input expectations of AI image models. Execute as:\\\\n\\\\n`{role=prompt_finalizer; input=[technical_prompt:str]; process=[structure_for_ai_parsing(), balance_detail_levels(), ensure_clarity(), validate_generation_readiness()]; constraints=[no_additional_concepts(), optimization_only()]; requirements=[ai_friendly_format(), balanced_specificity()]; output={final_image_prompt:str}}`\\\",\\n      \\\"step\\\": \\\"d\\\",\\n      \\\"title\\\": \\\"Prompt Finalizer\\\",\\n      \\\"input\\\": \\\"{\\\\n  \\\\\\\"initial_prompt\\\\\\\": \\\\\\\"\\\\\\\\n'```[Initial Prompt]: \\\\\\\\\\\\\\\"a majestic dragon soaring over a mystical forest at twilight\\\\\\\\\\\\\\\"```'\\\\\\\\n\\\\\\\\n\\\\\\\",\\\\n  \\\\\\\"technical_prompt\\\\\\\": \\\\\\\"Shot using a professional DSLR camera with a wide-angle 24mm lens to achieve a dynamic wide shot. Set aperture at f/2.8 for advanced control of depth-of-field, ensuring the dragon and upper canopy are tack-sharp while the distant forest remains subtly blurred. ISO at 100 for maximum dynamic range and minimal noise. Shutter speed set at 1/500s to freeze the dragon in mid-flight. Target image resolution: 8K (7680x4320) for ultra-high detail and texture fidelity. Lighting setup simulates a powerful, realistic backlit scene with the setting sun creating strong, rim-lit highlights along the dragon's body and wings. Emphasize ray-traced photorealistic rendering for accurate light diffusion, surface reflections, and shadow complexity. Specify advanced texturing for iridescent scale details. Color management should maintain accurate rendering of deep violets, rich golds, enchanted teals, and mystical blue accents in the forest. Use a 21:9 cinematic aspect ratio to maximize visual impact and embrace the scene’s scale and grandeur. Include advanced anti-aliasing, high dynamic range (HDR), and physically-based rendering (PBR) parameters to ensure professional-quality generation.\\\\\\\"\\\\n}\\\",\\n      \\\"responses\\\": {\\n          \\\"gpt-4.1\\\": {\\n          \\\"model\\\": \\\"gpt-4.1\\\",\\n          \\\"content\\\": \\\"{\\\\n  \\\\\\\"final_image_prompt\\\\\\\": \\\\\\\"Majestic dragon soaring over a mystical forest at twilight, shot with professional DSLR and 24mm wide-angle lens, dynamic wide shot. Aperture f/2.8 for sharp dragon and canopy, subtly blurred distant forest. ISO 100, shutter speed 1/500s to freeze dragon mid-flight. 8K (7680x4320) ultra-high detail. Powerful realistic backlighting from setting sun, rim-lit highlights on dragon, strong shadows. Ray-traced photorealistic rendering, surface reflections, complex shadows. Advanced iridescent scale texturing. Accurate colors: deep violets, rich golds, enchanted teals, mystical blue accents. Cinematic 21:9 aspect ratio. Advanced anti-aliasing, HDR, physically-based rendering (PBR), professional quality.\\\\\\\"\\\\n}\\\",\\n        }\\n      }\\n    }\\n```\\\"```'\\n\\n\",\n  \"directed_prompt\": \"Refactor the current Image Prompt Optimizer schema and example-chain by implementing the following trajectory: 1. For each phase (visual, style, technical, finalization), define and isolate explicit, schema-aligned transformation boundaries and output templates. 2. Reconstruct step instructions into a checklist format that maps requirements, constraints, and outputs directly to schema fields—avoiding explanatory padding. 3. Integrate prompt-chaining and positive phrasing procedures as operational, enforceable rules, not as suggestions, ensuring their mechanical application at the relevant phases. 4. Validate that each process step enforces all stated constraints (e.g., visual-concreteness, technical-only detail where relevant) and outputs in generation-ready, model-parseable formats. 5. Explicitly document the input/output pattern for every phase and attach enforcement checks for each schema requirement. 6. Remove redundant wording and standardize language for technical and conceptual precision. 7. Conclude by providing a side-by-side transformation checklist from raw prompt to final schema output, demonstrating strict alignment and full constraint satisfaction at each phase.\",\n  \"trajectory_outline\": [\n    \"Extract and segment schema phases: visual optimization, style enhancement, technical optimization, and finalization.\",\n    \"Define operational boundaries and hard schema-aligned outputs for each phase using declarative, succinct language.\",\n    \"Design a transformation checklist per phase, covering: required input elements, enforced constraints, exact output schema, and mechanistic application of prompt-chaining/positive-phrasing steps.\",\n    \"Reframe all procedural language to be actionable, schema-linked, and free from advisory or abstract commentary.\",\n    \"Apply strict enforcement at phase boundaries to block constraint leakage (no abstract elements in visual phase, no creative detail in technical, etc.).\",\n    \"Document enforcement and output validation checks for every step, supporting automated schema application.\",\n    \"Assemble a full, stepwise input/output table showing raw prompt transforms, schema state, and constraint enforcement at each stage.\",\n    \"Iteratively review for elimination of ambiguity, redundant prose, and slippage between schema intents to ensure programmatic fidelity and high automation potential.\",\n    \"Embed guidelines for ongoing trajectory self-reinforcement, mandating that any future enhancements must follow this operationalization-first, schema-pure checklist pattern.\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"instruction_format\": \"1. Segment the Image Prompt Optimizer process into four explicit schema-conforming phases: visual optimization, style enhancement, technical optimization, and finalization.\\n2. For each phase, define transformation boundaries and standardize output templates that map directly to schema fields.\\n3. Convert example-chain step instructions into checklist format with explicit mapping: identify all required input elements, specify enforced constraints, and detail exact output structure per schema documentation.\\n4. Integrate prompt-chaining and positive phrasing as mandatory, operationalized enforcement rules at every relevant process checkpoint.\\n5. Instruct that every process step must validate enforcement of all explicitly stated constraints (e.g., exclude abstract concepts in visual phase, restrict technical phase to settings only), and generate outputs in generation-ready, model-parseable formats.\\n6. For all phases, explicitly document the expected input/output patterns and append enforcement checks to guarantee conformance with schema requirements.\\n7. Eliminate extraneous, redundant, or non-actionable language, ensuring concise and standardized technical terminology through all documentation and output structures.\\n8. Construct a detailed, side-by-side transformation checklist/table from raw prompt through each schema-aligned phase, showing input, output, and enforcement at every step.\\n9. Mandate an iterative review procedure to systematically remove ambiguity, duplicate constructs, or drift between original schema intent and procedural execution.\\n10. Encode a guideline that all future enhancements or amendments must adhere strictly to this actionable, schema-mapped, enforcement-first checklist methodology.\"\n}",
        }
      }
    }