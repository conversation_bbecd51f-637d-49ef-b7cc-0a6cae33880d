#!/usr/bin/env python3

import sys
import json
from pathlib import Path

# Add src directory to path
sys.path.append(str(Path(__file__).parent / "src"))

# Import the modules
from main import TemplateCatalog
import processor

def test_system_instruction_extraction():
    """Test the system instruction extraction process."""
    
    print("=== Testing System Instruction Extraction ===")
    
    # Check if processor module is registered
    print(f"Registered modules: {TemplateCatalog._modules}")
    
    # Load the catalog
    try:
        catalog = TemplateCatalog.load_catalog()
        print(f"Catalog loaded successfully")
        print(f"Total templates: {len(catalog.get('templates', {}))}")
    except Exception as e:
        print(f"Error loading catalog: {e}")
        return
    
    # Get template data for 3031-a-problem_exploder
    template_id = "3031-a-problem_exploder"
    template_data = catalog.get("templates", {}).get(template_id)
    
    if not template_data:
        print(f"Template {template_id} not found")
        return
    
    print(f"\n=== Template Data for {template_id} ===")
    print(f"Has 'level' field: {'level' in template_data}")
    print(f"Has 'parts' field: {'parts' in template_data}")
    print(f"Has 'raw' field: {'raw' in template_data}")
    
    if 'parts' in template_data:
        parts = template_data['parts']
        print(f"Parts keys: {list(parts.keys())}")
        print(f"Title: {parts.get('title', 'N/A')}")
        print(f"Interpretation: {parts.get('interpretation', 'N/A')[:100]}...")
        print(f"Transformation: {parts.get('transformation', 'N/A')[:100]}...")
    
    # Test direct processor function
    print(f"\n=== Direct Processor Function ===")
    try:
        direct_result = processor.get_system_instruction(template_data)
        print(f"Direct processor result (first 200 chars):")
        print(f"{direct_result[:200]}...")
    except Exception as e:
        print(f"Error with direct processor function: {e}")
    
    # Test TemplateCatalog function
    print(f"\n=== TemplateCatalog Function ===")
    try:
        catalog_result = TemplateCatalog.get_system_instruction(template_data)
        print(f"TemplateCatalog result (first 200 chars):")
        print(f"{catalog_result[:200]}...")
    except Exception as e:
        print(f"Error with TemplateCatalog function: {e}")
    
    # Compare results
    print(f"\n=== Comparison ===")
    try:
        direct_result = processor.get_system_instruction(template_data)
        catalog_result = TemplateCatalog.get_system_instruction(template_data)
        
        print(f"Results are identical: {direct_result == catalog_result}")
        if direct_result != catalog_result:
            print(f"Direct result length: {len(direct_result) if direct_result else 0}")
            print(f"Catalog result length: {len(catalog_result) if catalog_result else 0}")
    except Exception as e:
        print(f"Error comparing results: {e}")

if __name__ == "__main__":
    test_system_instruction_extraction()
