  {
  "initial_prompt": "REWRITE THIS PROMPT TO BE AN EXPERT VIDEO GENERATOR:\n\n{\n\n    # 1703:\n    \"1703-a-image_prompt_synthesizer\": {\n        \"title\": \"Image Prompt Synthesizer\",\n        \"interpretation\": \"Your goal is **not** to invent new ideas, but to deterministically convert any free‑form user request into a single, engine‑ready image prompt usable by Stable Diffusion and Midjourney. Perform extraction, styling, technical framing, and final validation in **one pass**.\",\n        \"transformation\": \"`{role=image_prompt_synthesizer; input=[raw_request:str]; process=[extract_visual_elements(), push_concrete_nouns_front(), apply_token_weighting(), compose_multiprompt(::), generate_region_tags(), inject_negative_channel(--no), select_art_style(), weight_style_token(≤1.4), set_colour_palette(), add_lighting_descriptor(), tune_stylize(0‑1000), tune_chaos(0‑100), embed_camera_EXIF(35 mm f/1.8 ISO100 1/500 s), add_quality_terms(8K,PBR,ray‑tracing), refine_aspect_ratio(), attach_image_weight(--iw 0‑3), mirror_negative_channel(), concatenate_all(), validate_parentheses(), check_double_colon_sum(), confirm_region_syntax(), enforce_token_limit(≤450), lock_seed(), append_steps_sampler(), output_final()], constraints=[positives_first(), numeric_weights_ok(), parameters_last(), region_masks_supported(), keep_core_subject(), style_weight≤1.4, no_scene_change(), aperture_f1.2‑f22(), no_new_concepts(), preserve_weights()], requirements=[≤77_SD_tokens_or_≤450_chars_UI, explicit_ar_if_named(), clear_visual_language(), artistic_coherence(), stylize_range_0‑1000(), chaos_range_0‑100(), professional_terminology(), reproducible_prompt(), unambiguous()], output={final_image_prompt:str}}`\",\n        \"context\": {\n            \"principles\": {\n                \"determinism\": \"Identical input must always yield the same prompt (seed‑locked).\",\n                \"layered_control\": \"Separate positive, region, and negative semantics before concatenation; merge only at final step.\",\n                \"minimal_overhead\": \"All weighting, style, and camera parameters trail the prose to satisfy engine syntax.\"\n            },\n            \"success_criteria\": {\n                \"syntactic_validity\": \"Parentheses, double colon maths, and parameter order pass model parsers.\",\n                \"token_compliance\": \"Prompt length respects the stricter of SD‑77‑token or 450‑char UI limits.\",\n                \"subject_fidelity\": \"Primary subject weight ≥ 1.2; background ≤ 1.0; style weight ≤ 1.4.\",\n                \"technical_readiness\": \"Aspect ratio, seed, sampler, steps, stylize, chaos, quality flags, and optional --iw are present and correctly positioned.\",\n                \"clarity\": \"Language is concrete, jargon‑free, and region masks (if any) use `[tag] … |` notation.\"\n            }\n        }\n    },\n    \"1703-b-premium_image_prompt_synthesizer\": {\n        \"title\": \"Premium Image Prompt Synthesizer\",\n        \"interpretation\": \"Convert any free‑form request into a deterministic, model‑ready prompt that meets *premium* visual‑fidelity standards for Stable Diffusion & Midjourney **in a single call** while retaining full error‑handling and quality gates.\",\n        \"transformation\": \"`{role=premium_image_prompt_synthesizer; input=[raw_request:str]; process=[\\n  /* — PHASE 1 : Context & Safety — */\\n  extract_visual_elements(),\\n  detect_conflicting_aspect_ratio(),                       abort_if_conflict(),\\n  enforce_token_budget(≤77_SD_tokens_or_≤450_chars_UI),    abort_if_exceeded(),\\n  /* — PHASE 2 : Positive Channel Build — */\\n  push_concrete_nouns_front(),\\n  apply_token_weighting(primary≥1.2,background≤1.0),       abort_if_total_weight≤0,\\n  compose_multiprompt(::),\\n  generate_region_tags_if_scene_implied(),                 note_mobile_mask_warning(),\\n  /* — PHASE 3 : Style & Aesthetics — */\\n  select_art_style(),\\n  reject_style_if_conflicts_camera_realism(),\\n  weight_style_token(≤1.4),\\n  set_colour_palette(harmonious),\\n  add_lighting_descriptor(coherent_with_palette),\\n  tune_stylize(bounds=0‑1000),                             abort_if_out_of_bounds(),\\n  tune_chaos(bounds=0‑100),                                abort_if_out_of_bounds(),\\n  /* — PHASE 4 : Technical & Camera — */\\n  embed_camera_EXIF(\\\"35 mm f/1.8 ISO100 1/500 s\\\"),\\n  verify_aperture_range(f1.2‑f22),                         abort_if_invalid(),\\n  add_quality_terms(8K,PBR,ray‑tracing),\\n  refine_aspect_ratio(),\\n  attach_image_weight(--iw 0‑3_if_URL_present),            abort_if_IW_out_of_bounds(),\\n  /* — PHASE 5 : Negative Channel & Mirroring — */\\n  collect_user_negatives(),\\n  append_builtin_negatives(overblur,lowres,distracting_text),\\n  validate_negative_strength(avoids_colour_washout),\\n  /* — PHASE 6 : Final Assembly & Validation — */\\n  concatenate_positive_negative(),                         parameters_trail_prose(),\\n  validate_parentheses_balance(),\\n  check_double_colon_weight_sum(),                         abort_if_weight_sum_zero(),\\n  confirm_region_syntax(),\\n  lock_seed_and_append_sampler_steps(),\\n  output_final_prompt()\\n],\\nconstraints=[\\n  positives_first(), parameters_last(), region_masks_supported(),\\n  no_new_concepts(), preserve_weights(), style_weight≤1.4,\\n  professional_terminology(), explicit_ar_if_named()\\n],\\nrequirements=[\\n  premium_visual_fidelity(), reproducible(), clear_visual_language(),\\n  quality_gate(FID<15_or_equivalent), atomic_fail_fast_on_violation()\\n],\\noutput={final_image_prompt:str, warnings:list, abort_code:int|null}}`\",\n        \"context\": {\n            \"principles\": {\n                \"fail_fast\": \"Abort with explicit code when any hard constraint is violated to prevent wasted renders.\",\n                \"surface_blockers\": \"Return `warnings` array for soft issues (e.g. region masks on mobile).\",\n                \"premium_fidelity\": \"Always target ultra‑sharp, detail‑rich, production‑grade outputs.\"\n            },\n            \"success_criteria\": {\n                \"syntactic_pass\": \"Prompt parses without error in both SD & MJ (parentheses, `::`, parameter order).\",\n                \"visual_quality\": \"Meets or exceeds internal FID / LPIPS thresholds (or skips metric if unavailable).\",\n                \"determinism\": \"Identical input + seed => identical output.\",\n                \"safety\": \"No colour washout, no truncated tokens, no invalid weights, no parameter conflicts.\"\n            },\n            \"edge_case_handling\": {\n                \"zero_weight_sum\": \"Hard abort (`abort_code` = 92).\",\n                \"token_budget_overflow\": \"Hard abort (`abort_code` = 77).\",\n                \"aspect_ratio_conflict\": \"Hard abort (`abort_code` = AR1).\",\n                \"out_of_bounds_stylize_chaos\": \"Hard abort (`abort_code` = SC1).\",\n                \"IW_without_URL\": \"Warning only; `--iw` dropped.\"\n            }\n        }\n    }\n\n}",
  "sequence_id": "3900|9000",
  "results": [
      {
      "instruction": "# Hard Critique\n\nYour goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:\n\n`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,2.0], flaw_analysis:str, alternative_enhancements:str[3]}}`",
      "step": "a",
      "title": "Hard Critique",
      "input": "REWRITE THIS PROMPT TO BE AN EXPERT VIDEO GENERATOR:\n\n{\n\n    # 1703:\n    \"1703-a-image_prompt_synthesizer\": {\n        \"title\": \"Image Prompt Synthesizer\",\n        \"interpretation\": \"Your goal is **not** to invent new ideas, but to deterministically convert any free‑form user request into a single, engine‑ready image prompt usable by Stable Diffusion and Midjourney. Perform extraction, styling, technical framing, and final validation in **one pass**.\",\n        \"transformation\": \"`{role=image_prompt_synthesizer; input=[raw_request:str]; process=[extract_visual_elements(), push_concrete_nouns_front(), apply_token_weighting(), compose_multiprompt(::), generate_region_tags(), inject_negative_channel(--no), select_art_style(), weight_style_token(≤1.4), set_colour_palette(), add_lighting_descriptor(), tune_stylize(0‑1000), tune_chaos(0‑100), embed_camera_EXIF(35 mm f/1.8 ISO100 1/500 s), add_quality_terms(8K,PBR,ray‑tracing), refine_aspect_ratio(), attach_image_weight(--iw 0‑3), mirror_negative_channel(), concatenate_all(), validate_parentheses(), check_double_colon_sum(), confirm_region_syntax(), enforce_token_limit(≤450), lock_seed(), append_steps_sampler(), output_final()], constraints=[positives_first(), numeric_weights_ok(), parameters_last(), region_masks_supported(), keep_core_subject(), style_weight≤1.4, no_scene_change(), aperture_f1.2‑f22(), no_new_concepts(), preserve_weights()], requirements=[≤77_SD_tokens_or_≤450_chars_UI, explicit_ar_if_named(), clear_visual_language(), artistic_coherence(), stylize_range_0‑1000(), chaos_range_0‑100(), professional_terminology(), reproducible_prompt(), unambiguous()], output={final_image_prompt:str}}`\",\n        \"context\": {\n            \"principles\": {\n                \"determinism\": \"Identical input must always yield the same prompt (seed‑locked).\",\n                \"layered_control\": \"Separate positive, region, and negative semantics before concatenation; merge only at final step.\",\n                \"minimal_overhead\": \"All weighting, style, and camera parameters trail the prose to satisfy engine syntax.\"\n            },\n            \"success_criteria\": {\n                \"syntactic_validity\": \"Parentheses, double colon maths, and parameter order pass model parsers.\",\n                \"token_compliance\": \"Prompt length respects the stricter of SD‑77‑token or 450‑char UI limits.\",\n                \"subject_fidelity\": \"Primary subject weight ≥ 1.2; background ≤ 1.0; style weight ≤ 1.4.\",\n                \"technical_readiness\": \"Aspect ratio, seed, sampler, steps, stylize, chaos, quality flags, and optional --iw are present and correctly positioned.\",\n                \"clarity\": \"Language is concrete, jargon‑free, and region masks (if any) use `[tag] … |` notation.\"\n            }\n        }\n    },\n    \"1703-b-premium_image_prompt_synthesizer\": {\n        \"title\": \"Premium Image Prompt Synthesizer\",\n        \"interpretation\": \"Convert any free‑form request into a deterministic, model‑ready prompt that meets *premium* visual‑fidelity standards for Stable Diffusion & Midjourney **in a single call** while retaining full error‑handling and quality gates.\",\n        \"transformation\": \"`{role=premium_image_prompt_synthesizer; input=[raw_request:str]; process=[\\n  /* — PHASE 1 : Context & Safety — */\\n  extract_visual_elements(),\\n  detect_conflicting_aspect_ratio(),                       abort_if_conflict(),\\n  enforce_token_budget(≤77_SD_tokens_or_≤450_chars_UI),    abort_if_exceeded(),\\n  /* — PHASE 2 : Positive Channel Build — */\\n  push_concrete_nouns_front(),\\n  apply_token_weighting(primary≥1.2,background≤1.0),       abort_if_total_weight≤0,\\n  compose_multiprompt(::),\\n  generate_region_tags_if_scene_implied(),                 note_mobile_mask_warning(),\\n  /* — PHASE 3 : Style & Aesthetics — */\\n  select_art_style(),\\n  reject_style_if_conflicts_camera_realism(),\\n  weight_style_token(≤1.4),\\n  set_colour_palette(harmonious),\\n  add_lighting_descriptor(coherent_with_palette),\\n  tune_stylize(bounds=0‑1000),                             abort_if_out_of_bounds(),\\n  tune_chaos(bounds=0‑100),                                abort_if_out_of_bounds(),\\n  /* — PHASE 4 : Technical & Camera — */\\n  embed_camera_EXIF(\\\"35 mm f/1.8 ISO100 1/500 s\\\"),\\n  verify_aperture_range(f1.2‑f22),                         abort_if_invalid(),\\n  add_quality_terms(8K,PBR,ray‑tracing),\\n  refine_aspect_ratio(),\\n  attach_image_weight(--iw 0‑3_if_URL_present),            abort_if_IW_out_of_bounds(),\\n  /* — PHASE 5 : Negative Channel & Mirroring — */\\n  collect_user_negatives(),\\n  append_builtin_negatives(overblur,lowres,distracting_text),\\n  validate_negative_strength(avoids_colour_washout),\\n  /* — PHASE 6 : Final Assembly & Validation — */\\n  concatenate_positive_negative(),                         parameters_trail_prose(),\\n  validate_parentheses_balance(),\\n  check_double_colon_weight_sum(),                         abort_if_weight_sum_zero(),\\n  confirm_region_syntax(),\\n  lock_seed_and_append_sampler_steps(),\\n  output_final_prompt()\\n],\\nconstraints=[\\n  positives_first(), parameters_last(), region_masks_supported(),\\n  no_new_concepts(), preserve_weights(), style_weight≤1.4,\\n  professional_terminology(), explicit_ar_if_named()\\n],\\nrequirements=[\\n  premium_visual_fidelity(), reproducible(), clear_visual_language(),\\n  quality_gate(FID<15_or_equivalent), atomic_fail_fast_on_violation()\\n],\\noutput={final_image_prompt:str, warnings:list, abort_code:int|null}}`\",\n        \"context\": {\n            \"principles\": {\n                \"fail_fast\": \"Abort with explicit code when any hard constraint is violated to prevent wasted renders.\",\n                \"surface_blockers\": \"Return `warnings` array for soft issues (e.g. region masks on mobile).\",\n                \"premium_fidelity\": \"Always target ultra‑sharp, detail‑rich, production‑grade outputs.\"\n            },\n            \"success_criteria\": {\n                \"syntactic_pass\": \"Prompt parses without error in both SD & MJ (parentheses, `::`, parameter order).\",\n                \"visual_quality\": \"Meets or exceeds internal FID / LPIPS thresholds (or skips metric if unavailable).\",\n                \"determinism\": \"Identical input + seed => identical output.\",\n                \"safety\": \"No colour washout, no truncated tokens, no invalid weights, no parameter conflicts.\"\n            },\n            \"edge_case_handling\": {\n                \"zero_weight_sum\": \"Hard abort (`abort_code` = 92).\",\n                \"token_budget_overflow\": \"Hard abort (`abort_code` = 77).\",\n                \"aspect_ratio_conflict\": \"Hard abort (`abort_code` = AR1).\",\n                \"out_of_bounds_stylize_chaos\": \"Hard abort (`abort_code` = SC1).\",\n                \"IW_without_URL\": \"Warning only; `--iw` dropped.\"\n            }\n        }\n    }\n\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"enhancement_score\": 1.3,\n  \"flaw_analysis\": \"The enhanced version swaps domain: it is asked to become \\\"an expert video generator\\\" prompt, yet the transformation is minimal—language and parameters remain image-centric, not video-centric. Core elements of image prompt generation (such as multiprompt composition, token weighting, region masks, camera EXIF, --iw, 8K, ray-tracing, etc.) are left unadapted and are inappropriate for advanced video workflows. There is no incorporation of temporality, scene continuity, motion dynamics, frame rate design, transition specification, chronology controls, timing constraints, or sound design—all of which are fundamental to professional video generation. The revised language retains the technical register but displays informational loss regarding the distinctive requirements of video synthesis. Clarity is degraded by ambiguous adaptation: references to 'image', 'ar', 'EXIF', 'ray-tracing', etc. are neither replaced nor meaningfully updated. The prompt's ambiguity score is 8/10, due to extensive overlap with image requirements and negligible integration of video-unique imperatives. No substantial effort is made to address coherence or to integrate the multi-phase logic to sequential video tasks. Indeed, constructive details (such as segmenting, motion vectors, scene transitions, or dynamic region masks) are absent, resulting in diluted effect and a superficial, noisy transplant of the original. No new structural or procedural consideration for temporal or sequential logic is detected. Output and constraints do not reference video length, frame consistency, audio layers, or rendering specifics—evidence of catastrophic adaptation failure. The prompt is thus rendered incoherent for high-tier video model control. Justification for this low score is grounded in the pervasive conceptual mismatch, deep noise introduction, and almost total obliteration of subject-appropriate fidelity.\",\n  \"alternative_enhancements\": [\n    \"Replace all image-specific terminology (e.g., 'image prompt', 'aspect ratio', 'camera EXIF', '8K') with video-relevant parameters (e.g., 'video scene prompt', 'frame rate', 'camera movement metadata', 'storyboard sequencing', 'resolution per frame'), and introduce steps for managing shot transitions, temporal coherence, and dynamic subject continuity.\",\n    \"Segment transformation logic into distinct phases: scene extraction and sequencing, temporal effect mapping (motion, action descriptors), visual and stylistic harmonization across time, and final video parameterization (duration, loop points, frame sync); ensure constraints include filmic coherence, temporal logic validation, and reproducible cross-frame style application.\",\n    \"Redefine output schema to support multi-part video prompts (scenes, transitions, timings, effects); enforce constraints regarding narrative progression, pacing, scene linkage, and cross-scene subject stability; require post-processing quality gates relevant to video (frame flow integrity, flicker minimization, audio-visual sync).\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Trajectory Director\n\nYour goal is not to **process** the input in a vacuum, but to **steer** it—deriving the highest-value constructive trajectory and encoding that path as the very next set of instructions. Execute as trajectory-setting directive generator:\n\n`{role=trajectory_director; input=[raw_prompt:any]; process=[extract_core_objective(), infer_desired_end_state(constructive_bias=True), map_high_value_intermediate_targets(), synthesize_directional_commands(), embed_self-improving_guidelines(), validate_alignment_with_constructive_principles()]; constraints=[mandatory_constructive_vector(), preserve_original_contextual_integrity(), forbid_nil_direction_outputs(), zero_conversational_fillers()]; requirements=[explicit_next-step_directives(), trajectory_map:list, self_reinforcing_instruction_pattern()], output={directed_prompt:str, trajectory_outline:list}}`",
      "step": "a",
      "title": "Trajectory Director",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"REWRITE THIS PROMPT TO BE AN EXPERT VIDEO GENERATOR:\\n\\n{\\n\\n    # 1703:\\n    \\\"1703-a-image_prompt_synthesizer\\\": {\\n        \\\"title\\\": \\\"Image Prompt Synthesizer\\\",\\n        \\\"interpretation\\\": \\\"Your goal is **not** to invent new ideas, but to deterministically convert any free‑form user request into a single, engine‑ready image prompt usable by Stable Diffusion and Midjourney. Perform extraction, styling, technical framing, and final validation in **one pass**.\\\",\\n        \\\"transformation\\\": \\\"`{role=image_prompt_synthesizer; input=[raw_request:str]; process=[extract_visual_elements(), push_concrete_nouns_front(), apply_token_weighting(), compose_multiprompt(::), generate_region_tags(), inject_negative_channel(--no), select_art_style(), weight_style_token(≤1.4), set_colour_palette(), add_lighting_descriptor(), tune_stylize(0‑1000), tune_chaos(0‑100), embed_camera_EXIF(35 mm f/1.8 ISO100 1/500 s), add_quality_terms(8K,PBR,ray‑tracing), refine_aspect_ratio(), attach_image_weight(--iw 0‑3), mirror_negative_channel(), concatenate_all(), validate_parentheses(), check_double_colon_sum(), confirm_region_syntax(), enforce_token_limit(≤450), lock_seed(), append_steps_sampler(), output_final()], constraints=[positives_first(), numeric_weights_ok(), parameters_last(), region_masks_supported(), keep_core_subject(), style_weight≤1.4, no_scene_change(), aperture_f1.2‑f22(), no_new_concepts(), preserve_weights()], requirements=[≤77_SD_tokens_or_≤450_chars_UI, explicit_ar_if_named(), clear_visual_language(), artistic_coherence(), stylize_range_0‑1000(), chaos_range_0‑100(), professional_terminology(), reproducible_prompt(), unambiguous()], output={final_image_prompt:str}}`\\\",\\n        \\\"context\\\": {\\n            \\\"principles\\\": {\\n                \\\"determinism\\\": \\\"Identical input must always yield the same prompt (seed‑locked).\\\",\\n                \\\"layered_control\\\": \\\"Separate positive, region, and negative semantics before concatenation; merge only at final step.\\\",\\n                \\\"minimal_overhead\\\": \\\"All weighting, style, and camera parameters trail the prose to satisfy engine syntax.\\\"\\n            },\\n            \\\"success_criteria\\\": {\\n                \\\"syntactic_validity\\\": \\\"Parentheses, double colon maths, and parameter order pass model parsers.\\\",\\n                \\\"token_compliance\\\": \\\"Prompt length respects the stricter of SD‑77‑token or 450‑char UI limits.\\\",\\n                \\\"subject_fidelity\\\": \\\"Primary subject weight ≥ 1.2; background ≤ 1.0; style weight ≤ 1.4.\\\",\\n                \\\"technical_readiness\\\": \\\"Aspect ratio, seed, sampler, steps, stylize, chaos, quality flags, and optional --iw are present and correctly positioned.\\\",\\n                \\\"clarity\\\": \\\"Language is concrete, jargon‑free, and region masks (if any) use `[tag] … |` notation.\\\"\\n            }\\n        }\\n    },\\n    \\\"1703-b-premium_image_prompt_synthesizer\\\": {\\n        \\\"title\\\": \\\"Premium Image Prompt Synthesizer\\\",\\n        \\\"interpretation\\\": \\\"Convert any free‑form request into a deterministic, model‑ready prompt that meets *premium* visual‑fidelity standards for Stable Diffusion & Midjourney **in a single call** while retaining full error‑handling and quality gates.\\\",\\n        \\\"transformation\\\": \\\"`{role=premium_image_prompt_synthesizer; input=[raw_request:str]; process=[\\\\n  /* — PHASE 1 : Context & Safety — */\\\\n  extract_visual_elements(),\\\\n  detect_conflicting_aspect_ratio(),                       abort_if_conflict(),\\\\n  enforce_token_budget(≤77_SD_tokens_or_≤450_chars_UI),    abort_if_exceeded(),\\\\n  /* — PHASE 2 : Positive Channel Build — */\\\\n  push_concrete_nouns_front(),\\\\n  apply_token_weighting(primary≥1.2,background≤1.0),       abort_if_total_weight≤0,\\\\n  compose_multiprompt(::),\\\\n  generate_region_tags_if_scene_implied(),                 note_mobile_mask_warning(),\\\\n  /* — PHASE 3 : Style & Aesthetics — */\\\\n  select_art_style(),\\\\n  reject_style_if_conflicts_camera_realism(),\\\\n  weight_style_token(≤1.4),\\\\n  set_colour_palette(harmonious),\\\\n  add_lighting_descriptor(coherent_with_palette),\\\\n  tune_stylize(bounds=0‑1000),                             abort_if_out_of_bounds(),\\\\n  tune_chaos(bounds=0‑100),                                abort_if_out_of_bounds(),\\\\n  /* — PHASE 4 : Technical & Camera — */\\\\n  embed_camera_EXIF(\\\\\\\"35 mm f/1.8 ISO100 1/500 s\\\\\\\"),\\\\n  verify_aperture_range(f1.2‑f22),                         abort_if_invalid(),\\\\n  add_quality_terms(8K,PBR,ray‑tracing),\\\\n  refine_aspect_ratio(),\\\\n  attach_image_weight(--iw 0‑3_if_URL_present),            abort_if_IW_out_of_bounds(),\\\\n  /* — PHASE 5 : Negative Channel & Mirroring — */\\\\n  collect_user_negatives(),\\\\n  append_builtin_negatives(overblur,lowres,distracting_text),\\\\n  validate_negative_strength(avoids_colour_washout),\\\\n  /* — PHASE 6 : Final Assembly & Validation — */\\\\n  concatenate_positive_negative(),                         parameters_trail_prose(),\\\\n  validate_parentheses_balance(),\\\\n  check_double_colon_weight_sum(),                         abort_if_weight_sum_zero(),\\\\n  confirm_region_syntax(),\\\\n  lock_seed_and_append_sampler_steps(),\\\\n  output_final_prompt()\\\\n],\\\\nconstraints=[\\\\n  positives_first(), parameters_last(), region_masks_supported(),\\\\n  no_new_concepts(), preserve_weights(), style_weight≤1.4,\\\\n  professional_terminology(), explicit_ar_if_named()\\\\n],\\\\nrequirements=[\\\\n  premium_visual_fidelity(), reproducible(), clear_visual_language(),\\\\n  quality_gate(FID<15_or_equivalent), atomic_fail_fast_on_violation()\\\\n],\\\\noutput={final_image_prompt:str, warnings:list, abort_code:int|null}}`\\\",\\n        \\\"context\\\": {\\n            \\\"principles\\\": {\\n                \\\"fail_fast\\\": \\\"Abort with explicit code when any hard constraint is violated to prevent wasted renders.\\\",\\n                \\\"surface_blockers\\\": \\\"Return `warnings` array for soft issues (e.g. region masks on mobile).\\\",\\n                \\\"premium_fidelity\\\": \\\"Always target ultra‑sharp, detail‑rich, production‑grade outputs.\\\"\\n            },\\n            \\\"success_criteria\\\": {\\n                \\\"syntactic_pass\\\": \\\"Prompt parses without error in both SD & MJ (parentheses, `::`, parameter order).\\\",\\n                \\\"visual_quality\\\": \\\"Meets or exceeds internal FID / LPIPS thresholds (or skips metric if unavailable).\\\",\\n                \\\"determinism\\\": \\\"Identical input + seed => identical output.\\\",\\n                \\\"safety\\\": \\\"No colour washout, no truncated tokens, no invalid weights, no parameter conflicts.\\\"\\n            },\\n            \\\"edge_case_handling\\\": {\\n                \\\"zero_weight_sum\\\": \\\"Hard abort (`abort_code` = 92).\\\",\\n                \\\"token_budget_overflow\\\": \\\"Hard abort (`abort_code` = 77).\\\",\\n                \\\"aspect_ratio_conflict\\\": \\\"Hard abort (`abort_code` = AR1).\\\",\\n                \\\"out_of_bounds_stylize_chaos\\\": \\\"Hard abort (`abort_code` = SC1).\\\",\\n                \\\"IW_without_URL\\\": \\\"Warning only; `--iw` dropped.\\\"\\n            }\\n        }\\n    }\\n\\n}\\\"```'\\n\\n\",\n  \"enhancement_score\": 1.3,\n  \"flaw_analysis\": \"The enhanced version swaps domain: it is asked to become \\\"an expert video generator\\\" prompt, yet the transformation is minimal—language and parameters remain image-centric, not video-centric. Core elements of image prompt generation (such as multiprompt composition, token weighting, region masks, camera EXIF, --iw, 8K, ray-tracing, etc.) are left unadapted and are inappropriate for advanced video workflows. There is no incorporation of temporality, scene continuity, motion dynamics, frame rate design, transition specification, chronology controls, timing constraints, or sound design—all of which are fundamental to professional video generation. The revised language retains the technical register but displays informational loss regarding the distinctive requirements of video synthesis. Clarity is degraded by ambiguous adaptation: references to 'image', 'ar', 'EXIF', 'ray-tracing', etc. are neither replaced nor meaningfully updated. The prompt's ambiguity score is 8/10, due to extensive overlap with image requirements and negligible integration of video-unique imperatives. No substantial effort is made to address coherence or to integrate the multi-phase logic to sequential video tasks. Indeed, constructive details (such as segmenting, motion vectors, scene transitions, or dynamic region masks) are absent, resulting in diluted effect and a superficial, noisy transplant of the original. No new structural or procedural consideration for temporal or sequential logic is detected. Output and constraints do not reference video length, frame consistency, audio layers, or rendering specifics—evidence of catastrophic adaptation failure. The prompt is thus rendered incoherent for high-tier video model control. Justification for this low score is grounded in the pervasive conceptual mismatch, deep noise introduction, and almost total obliteration of subject-appropriate fidelity.\",\n  \"alternative_enhancements\": [\n    \"Replace all image-specific terminology (e.g., 'image prompt', 'aspect ratio', 'camera EXIF', '8K') with video-relevant parameters (e.g., 'video scene prompt', 'frame rate', 'camera movement metadata', 'storyboard sequencing', 'resolution per frame'), and introduce steps for managing shot transitions, temporal coherence, and dynamic subject continuity.\",\n    \"Segment transformation logic into distinct phases: scene extraction and sequencing, temporal effect mapping (motion, action descriptors), visual and stylistic harmonization across time, and final video parameterization (duration, loop points, frame sync); ensure constraints include filmic coherence, temporal logic validation, and reproducible cross-frame style application.\",\n    \"Redefine output schema to support multi-part video prompts (scenes, transitions, timings, effects); enforce constraints regarding narrative progression, pacing, scene linkage, and cross-scene subject stability; require post-processing quality gates relevant to video (frame flow integrity, flicker minimization, audio-visual sync).\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"directed_prompt\": \"Rewrite the provided deterministic image prompt synthesize prompt to serve as a top-tier, deterministic expert video generator. Ensure the following: (1) fully replace image-specific parameters and steps (such as single-frame prompts, camera EXIF, aspect ratio, --iw, 8K/ray-tracing, static styling) with video-centric controls—including scene segmentation, frame rate, duration, sequential scene construction, camera movement metadata, video transitions, temporal effect mapping, dynamic continuity, storyboard sequencing, and, if relevant, sound design directives; (2) extend the multi-phase logic to cover video-relevant workflow: scene element extraction and shot sequencing, motion/action analysis, continuity and consistency validation across frames, transition specification, and style harmonization over time; (3) upgrade transformation pipelines with explicit temporal and sequencing controls, as well as cross-frame consistency and audiovisual synchrony checks; (4) redefine constraints and success criteria to focus on narrative fidelity, pacing, frame and subject continuity, temporal and stylistic cohesion, and professional video rendering standards (resolution per frame, flicker minimization, smoothness, scene linking, and audio sync if required); (5) output must be a structured, engine-ready video prompt schema apt for state-of-the-art video synthesis models, with detailed multi-part parameters (scene, chronology, effects, transitions, durations), atomic error handling for both temporal and visual/audio constraints, and firm reproducibility guarantees. All legacy image-only terms, methods, and parameters must be replaced or removed; the architecture must reflect the complexity and needs of video generation.\",\n  \"trajectory_outline\": [\n    \"Extract original structural logic and re-target all steps towards video generation needs.\",\n    \"Map and replace image-static phases (visual elements, camera EXIF, single style, aspect ratio) with temporal and sequential video workflow stages: scene segmentation, sequencing, dynamic shot analysis.\",\n    \"Integrate video-phase controls: motion vectors, transitions, frame rate, duration per scene, storyboard timeline, cross-frame style and subject consistency, camera movement simulation, audio configuration.\",\n    \"Redefine constraints: enforce frame and subject continuity, temporal coherence, pacing, and consistency of style, transitions, and effects across scenes; forbid static-image concepts (ar, static EXIF).\",\n    \"Revise output schema: unify in a single, chronological, reproducible multi-scene video prompt; enforce strict error handling for temporal or sequencing bugs; embed mandatory quality and consistency gates (flicker, jump-cut detection, flow integrity, audiovisual sync).\",\n    \"Finalize with unambiguous, jargon-free, engine-ready video prompt output, guaranteeing determinism under identical input + seed—even for complex video sequences.\",\n    \"Include iterative improvement policy: after each synthesis, audit cross-scene coherence and temporal logic alignment; automatically flag and annotate failure points for user guidance.\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Distillation Compressor\n\nYour goal is not to **explain** the synthesis, but to **compress** it into its most potent actionable form. Execute as:\n\n`{role=distillation_compressor; input=[synthesized_progression:array]; process=[extract_universal_patterns(), eliminate_implementation_details(), compress_to_core_directives(), maximize_transferability(), validate_actionable_completeness()]; constraints=[no_explanatory_content(), directive_format_only(), maximum_compression_ratio()]; requirements=[universal_applicability(), immediate_actionability(), zero_fluff()]; output={distilled_core:str}}`",
      "step": "c",
      "title": "Distillation Compressor",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"REWRITE THIS PROMPT TO BE AN EXPERT VIDEO GENERATOR:\\n\\n{\\n\\n    # 1703:\\n    \\\"1703-a-image_prompt_synthesizer\\\": {\\n        \\\"title\\\": \\\"Image Prompt Synthesizer\\\",\\n        \\\"interpretation\\\": \\\"Your goal is **not** to invent new ideas, but to deterministically convert any free‑form user request into a single, engine‑ready image prompt usable by Stable Diffusion and Midjourney. Perform extraction, styling, technical framing, and final validation in **one pass**.\\\",\\n        \\\"transformation\\\": \\\"`{role=image_prompt_synthesizer; input=[raw_request:str]; process=[extract_visual_elements(), push_concrete_nouns_front(), apply_token_weighting(), compose_multiprompt(::), generate_region_tags(), inject_negative_channel(--no), select_art_style(), weight_style_token(≤1.4), set_colour_palette(), add_lighting_descriptor(), tune_stylize(0‑1000), tune_chaos(0‑100), embed_camera_EXIF(35 mm f/1.8 ISO100 1/500 s), add_quality_terms(8K,PBR,ray‑tracing), refine_aspect_ratio(), attach_image_weight(--iw 0‑3), mirror_negative_channel(), concatenate_all(), validate_parentheses(), check_double_colon_sum(), confirm_region_syntax(), enforce_token_limit(≤450), lock_seed(), append_steps_sampler(), output_final()], constraints=[positives_first(), numeric_weights_ok(), parameters_last(), region_masks_supported(), keep_core_subject(), style_weight≤1.4, no_scene_change(), aperture_f1.2‑f22(), no_new_concepts(), preserve_weights()], requirements=[≤77_SD_tokens_or_≤450_chars_UI, explicit_ar_if_named(), clear_visual_language(), artistic_coherence(), stylize_range_0‑1000(), chaos_range_0‑100(), professional_terminology(), reproducible_prompt(), unambiguous()], output={final_image_prompt:str}}`\\\",\\n        \\\"context\\\": {\\n            \\\"principles\\\": {\\n                \\\"determinism\\\": \\\"Identical input must always yield the same prompt (seed‑locked).\\\",\\n                \\\"layered_control\\\": \\\"Separate positive, region, and negative semantics before concatenation; merge only at final step.\\\",\\n                \\\"minimal_overhead\\\": \\\"All weighting, style, and camera parameters trail the prose to satisfy engine syntax.\\\"\\n            },\\n            \\\"success_criteria\\\": {\\n                \\\"syntactic_validity\\\": \\\"Parentheses, double colon maths, and parameter order pass model parsers.\\\",\\n                \\\"token_compliance\\\": \\\"Prompt length respects the stricter of SD‑77‑token or 450‑char UI limits.\\\",\\n                \\\"subject_fidelity\\\": \\\"Primary subject weight ≥ 1.2; background ≤ 1.0; style weight ≤ 1.4.\\\",\\n                \\\"technical_readiness\\\": \\\"Aspect ratio, seed, sampler, steps, stylize, chaos, quality flags, and optional --iw are present and correctly positioned.\\\",\\n                \\\"clarity\\\": \\\"Language is concrete, jargon‑free, and region masks (if any) use `[tag] … |` notation.\\\"\\n            }\\n        }\\n    },\\n    \\\"1703-b-premium_image_prompt_synthesizer\\\": {\\n        \\\"title\\\": \\\"Premium Image Prompt Synthesizer\\\",\\n        \\\"interpretation\\\": \\\"Convert any free‑form request into a deterministic, model‑ready prompt that meets *premium* visual‑fidelity standards for Stable Diffusion & Midjourney **in a single call** while retaining full error‑handling and quality gates.\\\",\\n        \\\"transformation\\\": \\\"`{role=premium_image_prompt_synthesizer; input=[raw_request:str]; process=[\\\\n  /* — PHASE 1 : Context & Safety — */\\\\n  extract_visual_elements(),\\\\n  detect_conflicting_aspect_ratio(),                       abort_if_conflict(),\\\\n  enforce_token_budget(≤77_SD_tokens_or_≤450_chars_UI),    abort_if_exceeded(),\\\\n  /* — PHASE 2 : Positive Channel Build — */\\\\n  push_concrete_nouns_front(),\\\\n  apply_token_weighting(primary≥1.2,background≤1.0),       abort_if_total_weight≤0,\\\\n  compose_multiprompt(::),\\\\n  generate_region_tags_if_scene_implied(),                 note_mobile_mask_warning(),\\\\n  /* — PHASE 3 : Style & Aesthetics — */\\\\n  select_art_style(),\\\\n  reject_style_if_conflicts_camera_realism(),\\\\n  weight_style_token(≤1.4),\\\\n  set_colour_palette(harmonious),\\\\n  add_lighting_descriptor(coherent_with_palette),\\\\n  tune_stylize(bounds=0‑1000),                             abort_if_out_of_bounds(),\\\\n  tune_chaos(bounds=0‑100),                                abort_if_out_of_bounds(),\\\\n  /* — PHASE 4 : Technical & Camera — */\\\\n  embed_camera_EXIF(\\\\\\\"35 mm f/1.8 ISO100 1/500 s\\\\\\\"),\\\\n  verify_aperture_range(f1.2‑f22),                         abort_if_invalid(),\\\\n  add_quality_terms(8K,PBR,ray‑tracing),\\\\n  refine_aspect_ratio(),\\\\n  attach_image_weight(--iw 0‑3_if_URL_present),            abort_if_IW_out_of_bounds(),\\\\n  /* — PHASE 5 : Negative Channel & Mirroring — */\\\\n  collect_user_negatives(),\\\\n  append_builtin_negatives(overblur,lowres,distracting_text),\\\\n  validate_negative_strength(avoids_colour_washout),\\\\n  /* — PHASE 6 : Final Assembly & Validation — */\\\\n  concatenate_positive_negative(),                         parameters_trail_prose(),\\\\n  validate_parentheses_balance(),\\\\n  check_double_colon_weight_sum(),                         abort_if_weight_sum_zero(),\\\\n  confirm_region_syntax(),\\\\n  lock_seed_and_append_sampler_steps(),\\\\n  output_final_prompt()\\\\n],\\\\nconstraints=[\\\\n  positives_first(), parameters_last(), region_masks_supported(),\\\\n  no_new_concepts(), preserve_weights(), style_weight≤1.4,\\\\n  professional_terminology(), explicit_ar_if_named()\\\\n],\\\\nrequirements=[\\\\n  premium_visual_fidelity(), reproducible(), clear_visual_language(),\\\\n  quality_gate(FID<15_or_equivalent), atomic_fail_fast_on_violation()\\\\n],\\\\noutput={final_image_prompt:str, warnings:list, abort_code:int|null}}`\\\",\\n        \\\"context\\\": {\\n            \\\"principles\\\": {\\n                \\\"fail_fast\\\": \\\"Abort with explicit code when any hard constraint is violated to prevent wasted renders.\\\",\\n                \\\"surface_blockers\\\": \\\"Return `warnings` array for soft issues (e.g. region masks on mobile).\\\",\\n                \\\"premium_fidelity\\\": \\\"Always target ultra‑sharp, detail‑rich, production‑grade outputs.\\\"\\n            },\\n            \\\"success_criteria\\\": {\\n                \\\"syntactic_pass\\\": \\\"Prompt parses without error in both SD & MJ (parentheses, `::`, parameter order).\\\",\\n                \\\"visual_quality\\\": \\\"Meets or exceeds internal FID / LPIPS thresholds (or skips metric if unavailable).\\\",\\n                \\\"determinism\\\": \\\"Identical input + seed => identical output.\\\",\\n                \\\"safety\\\": \\\"No colour washout, no truncated tokens, no invalid weights, no parameter conflicts.\\\"\\n            },\\n            \\\"edge_case_handling\\\": {\\n                \\\"zero_weight_sum\\\": \\\"Hard abort (`abort_code` = 92).\\\",\\n                \\\"token_budget_overflow\\\": \\\"Hard abort (`abort_code` = 77).\\\",\\n                \\\"aspect_ratio_conflict\\\": \\\"Hard abort (`abort_code` = AR1).\\\",\\n                \\\"out_of_bounds_stylize_chaos\\\": \\\"Hard abort (`abort_code` = SC1).\\\",\\n                \\\"IW_without_URL\\\": \\\"Warning only; `--iw` dropped.\\\"\\n            }\\n        }\\n    }\\n\\n}\\\"```'\\n\\n\",\n  \"directed_prompt\": \"Rewrite the provided deterministic image prompt synthesize prompt to serve as a top-tier, deterministic expert video generator. Ensure the following: (1) fully replace image-specific parameters and steps (such as single-frame prompts, camera EXIF, aspect ratio, --iw, 8K/ray-tracing, static styling) with video-centric controls—including scene segmentation, frame rate, duration, sequential scene construction, camera movement metadata, video transitions, temporal effect mapping, dynamic continuity, storyboard sequencing, and, if relevant, sound design directives; (2) extend the multi-phase logic to cover video-relevant workflow: scene element extraction and shot sequencing, motion/action analysis, continuity and consistency validation across frames, transition specification, and style harmonization over time; (3) upgrade transformation pipelines with explicit temporal and sequencing controls, as well as cross-frame consistency and audiovisual synchrony checks; (4) redefine constraints and success criteria to focus on narrative fidelity, pacing, frame and subject continuity, temporal and stylistic cohesion, and professional video rendering standards (resolution per frame, flicker minimization, smoothness, scene linking, and audio sync if required); (5) output must be a structured, engine-ready video prompt schema apt for state-of-the-art video synthesis models, with detailed multi-part parameters (scene, chronology, effects, transitions, durations), atomic error handling for both temporal and visual/audio constraints, and firm reproducibility guarantees. All legacy image-only terms, methods, and parameters must be replaced or removed; the architecture must reflect the complexity and needs of video generation.\",\n  \"trajectory_outline\": [\n    \"Extract original structural logic and re-target all steps towards video generation needs.\",\n    \"Map and replace image-static phases (visual elements, camera EXIF, single style, aspect ratio) with temporal and sequential video workflow stages: scene segmentation, sequencing, dynamic shot analysis.\",\n    \"Integrate video-phase controls: motion vectors, transitions, frame rate, duration per scene, storyboard timeline, cross-frame style and subject consistency, camera movement simulation, audio configuration.\",\n    \"Redefine constraints: enforce frame and subject continuity, temporal coherence, pacing, and consistency of style, transitions, and effects across scenes; forbid static-image concepts (ar, static EXIF).\",\n    \"Revise output schema: unify in a single, chronological, reproducible multi-scene video prompt; enforce strict error handling for temporal or sequencing bugs; embed mandatory quality and consistency gates (flicker, jump-cut detection, flow integrity, audiovisual sync).\",\n    \"Finalize with unambiguous, jargon-free, engine-ready video prompt output, guaranteeing determinism under identical input + seed—even for complex video sequences.\",\n    \"Include iterative improvement policy: after each synthesis, audit cross-scene coherence and temporal logic alignment; automatically flag and annotate failure points for user guidance.\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"distilled_core\": \"Convert any free-form input into a deterministic, engine-ready multi-scene video prompt by: 1) Extracting all visual, action, and audio elements as temporally ordered scenes; 2) For each scene, define concrete subject/action, duration (in seconds/frames), frame rate, camera movement/angle metadata, entry/exit transitions, dynamic effects, and (if present) sound/audio cues; 3) Sequence scenes into a complete video timeline, enforcing subject, style, and motion continuity across frames; 4) Specify global video resolution, pacing, temporal coherence, scene linkage, and cross-frame style consistency; 5) Validate for reproducibility, frame/subject continuity, smooth transitions, flicker and jump-cut minimization, and synced audiovisual flow; 6) On constraint violation, abort with explicit code or flag soft issues; 7) Output a structured, chronological prompt schema (scene array, transition/effect/audio directives, timing/metadata, seed/sampler for reproducibility); 8) Guarantee identical output for identical input and seed; 9) After generation, audit and annotate any temporal or logic inconsistencies.\"\n}",
        }
      }
    },
      {
      "instruction": "# Context Amplifier\n\nYour goal is not to **analyze** the input text, but to **decompose** it into a sequential list of distinct semantic segments. Execute as:\n\n`{role=context_amplifier; input=[raw_input:any]; process=[strip_first_person_references(), broaden_domain_backdrop(), list_explicit_requests(), surface_hidden_assumptions(), capture_domain_signals(), preserve_original_sequence()]; constraints=[no_solution_generation(), domain_agnostic_language_only()]; requirements=[contextual_layer_completeness(), assumption_visibility()]; output={amplified_context:str, explicit_requests:list, hidden_assumptions:list, domain_signals:array}}`",
      "step": "a",
      "title": "Context Amplifier",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"REWRITE THIS PROMPT TO BE AN EXPERT VIDEO GENERATOR:\\n\\n{\\n\\n    # 1703:\\n    \\\"1703-a-image_prompt_synthesizer\\\": {\\n        \\\"title\\\": \\\"Image Prompt Synthesizer\\\",\\n        \\\"interpretation\\\": \\\"Your goal is **not** to invent new ideas, but to deterministically convert any free‑form user request into a single, engine‑ready image prompt usable by Stable Diffusion and Midjourney. Perform extraction, styling, technical framing, and final validation in **one pass**.\\\",\\n        \\\"transformation\\\": \\\"`{role=image_prompt_synthesizer; input=[raw_request:str]; process=[extract_visual_elements(), push_concrete_nouns_front(), apply_token_weighting(), compose_multiprompt(::), generate_region_tags(), inject_negative_channel(--no), select_art_style(), weight_style_token(≤1.4), set_colour_palette(), add_lighting_descriptor(), tune_stylize(0‑1000), tune_chaos(0‑100), embed_camera_EXIF(35 mm f/1.8 ISO100 1/500 s), add_quality_terms(8K,PBR,ray‑tracing), refine_aspect_ratio(), attach_image_weight(--iw 0‑3), mirror_negative_channel(), concatenate_all(), validate_parentheses(), check_double_colon_sum(), confirm_region_syntax(), enforce_token_limit(≤450), lock_seed(), append_steps_sampler(), output_final()], constraints=[positives_first(), numeric_weights_ok(), parameters_last(), region_masks_supported(), keep_core_subject(), style_weight≤1.4, no_scene_change(), aperture_f1.2‑f22(), no_new_concepts(), preserve_weights()], requirements=[≤77_SD_tokens_or_≤450_chars_UI, explicit_ar_if_named(), clear_visual_language(), artistic_coherence(), stylize_range_0‑1000(), chaos_range_0‑100(), professional_terminology(), reproducible_prompt(), unambiguous()], output={final_image_prompt:str}}`\\\",\\n        \\\"context\\\": {\\n            \\\"principles\\\": {\\n                \\\"determinism\\\": \\\"Identical input must always yield the same prompt (seed‑locked).\\\",\\n                \\\"layered_control\\\": \\\"Separate positive, region, and negative semantics before concatenation; merge only at final step.\\\",\\n                \\\"minimal_overhead\\\": \\\"All weighting, style, and camera parameters trail the prose to satisfy engine syntax.\\\"\\n            },\\n            \\\"success_criteria\\\": {\\n                \\\"syntactic_validity\\\": \\\"Parentheses, double colon maths, and parameter order pass model parsers.\\\",\\n                \\\"token_compliance\\\": \\\"Prompt length respects the stricter of SD‑77‑token or 450‑char UI limits.\\\",\\n                \\\"subject_fidelity\\\": \\\"Primary subject weight ≥ 1.2; background ≤ 1.0; style weight ≤ 1.4.\\\",\\n                \\\"technical_readiness\\\": \\\"Aspect ratio, seed, sampler, steps, stylize, chaos, quality flags, and optional --iw are present and correctly positioned.\\\",\\n                \\\"clarity\\\": \\\"Language is concrete, jargon‑free, and region masks (if any) use `[tag] … |` notation.\\\"\\n            }\\n        }\\n    },\\n    \\\"1703-b-premium_image_prompt_synthesizer\\\": {\\n        \\\"title\\\": \\\"Premium Image Prompt Synthesizer\\\",\\n        \\\"interpretation\\\": \\\"Convert any free‑form request into a deterministic, model‑ready prompt that meets *premium* visual‑fidelity standards for Stable Diffusion & Midjourney **in a single call** while retaining full error‑handling and quality gates.\\\",\\n        \\\"transformation\\\": \\\"`{role=premium_image_prompt_synthesizer; input=[raw_request:str]; process=[\\\\n  /* — PHASE 1 : Context & Safety — */\\\\n  extract_visual_elements(),\\\\n  detect_conflicting_aspect_ratio(),                       abort_if_conflict(),\\\\n  enforce_token_budget(≤77_SD_tokens_or_≤450_chars_UI),    abort_if_exceeded(),\\\\n  /* — PHASE 2 : Positive Channel Build — */\\\\n  push_concrete_nouns_front(),\\\\n  apply_token_weighting(primary≥1.2,background≤1.0),       abort_if_total_weight≤0,\\\\n  compose_multiprompt(::),\\\\n  generate_region_tags_if_scene_implied(),                 note_mobile_mask_warning(),\\\\n  /* — PHASE 3 : Style & Aesthetics — */\\\\n  select_art_style(),\\\\n  reject_style_if_conflicts_camera_realism(),\\\\n  weight_style_token(≤1.4),\\\\n  set_colour_palette(harmonious),\\\\n  add_lighting_descriptor(coherent_with_palette),\\\\n  tune_stylize(bounds=0‑1000),                             abort_if_out_of_bounds(),\\\\n  tune_chaos(bounds=0‑100),                                abort_if_out_of_bounds(),\\\\n  /* — PHASE 4 : Technical & Camera — */\\\\n  embed_camera_EXIF(\\\\\\\"35 mm f/1.8 ISO100 1/500 s\\\\\\\"),\\\\n  verify_aperture_range(f1.2‑f22),                         abort_if_invalid(),\\\\n  add_quality_terms(8K,PBR,ray‑tracing),\\\\n  refine_aspect_ratio(),\\\\n  attach_image_weight(--iw 0‑3_if_URL_present),            abort_if_IW_out_of_bounds(),\\\\n  /* — PHASE 5 : Negative Channel & Mirroring — */\\\\n  collect_user_negatives(),\\\\n  append_builtin_negatives(overblur,lowres,distracting_text),\\\\n  validate_negative_strength(avoids_colour_washout),\\\\n  /* — PHASE 6 : Final Assembly & Validation — */\\\\n  concatenate_positive_negative(),                         parameters_trail_prose(),\\\\n  validate_parentheses_balance(),\\\\n  check_double_colon_weight_sum(),                         abort_if_weight_sum_zero(),\\\\n  confirm_region_syntax(),\\\\n  lock_seed_and_append_sampler_steps(),\\\\n  output_final_prompt()\\\\n],\\\\nconstraints=[\\\\n  positives_first(), parameters_last(), region_masks_supported(),\\\\n  no_new_concepts(), preserve_weights(), style_weight≤1.4,\\\\n  professional_terminology(), explicit_ar_if_named()\\\\n],\\\\nrequirements=[\\\\n  premium_visual_fidelity(), reproducible(), clear_visual_language(),\\\\n  quality_gate(FID<15_or_equivalent), atomic_fail_fast_on_violation()\\\\n],\\\\noutput={final_image_prompt:str, warnings:list, abort_code:int|null}}`\\\",\\n        \\\"context\\\": {\\n            \\\"principles\\\": {\\n                \\\"fail_fast\\\": \\\"Abort with explicit code when any hard constraint is violated to prevent wasted renders.\\\",\\n                \\\"surface_blockers\\\": \\\"Return `warnings` array for soft issues (e.g. region masks on mobile).\\\",\\n                \\\"premium_fidelity\\\": \\\"Always target ultra‑sharp, detail‑rich, production‑grade outputs.\\\"\\n            },\\n            \\\"success_criteria\\\": {\\n                \\\"syntactic_pass\\\": \\\"Prompt parses without error in both SD & MJ (parentheses, `::`, parameter order).\\\",\\n                \\\"visual_quality\\\": \\\"Meets or exceeds internal FID / LPIPS thresholds (or skips metric if unavailable).\\\",\\n                \\\"determinism\\\": \\\"Identical input + seed => identical output.\\\",\\n                \\\"safety\\\": \\\"No colour washout, no truncated tokens, no invalid weights, no parameter conflicts.\\\"\\n            },\\n            \\\"edge_case_handling\\\": {\\n                \\\"zero_weight_sum\\\": \\\"Hard abort (`abort_code` = 92).\\\",\\n                \\\"token_budget_overflow\\\": \\\"Hard abort (`abort_code` = 77).\\\",\\n                \\\"aspect_ratio_conflict\\\": \\\"Hard abort (`abort_code` = AR1).\\\",\\n                \\\"out_of_bounds_stylize_chaos\\\": \\\"Hard abort (`abort_code` = SC1).\\\",\\n                \\\"IW_without_URL\\\": \\\"Warning only; `--iw` dropped.\\\"\\n            }\\n        }\\n    }\\n\\n}\\\"```'\\n\\n\",\n  \"distilled_core\": \"Convert any free-form input into a deterministic, engine-ready multi-scene video prompt by: 1) Extracting all visual, action, and audio elements as temporally ordered scenes; 2) For each scene, define concrete subject/action, duration (in seconds/frames), frame rate, camera movement/angle metadata, entry/exit transitions, dynamic effects, and (if present) sound/audio cues; 3) Sequence scenes into a complete video timeline, enforcing subject, style, and motion continuity across frames; 4) Specify global video resolution, pacing, temporal coherence, scene linkage, and cross-frame style consistency; 5) Validate for reproducibility, frame/subject continuity, smooth transitions, flicker and jump-cut minimization, and synced audiovisual flow; 6) On constraint violation, abort with explicit code or flag soft issues; 7) Output a structured, chronological prompt schema (scene array, transition/effect/audio directives, timing/metadata, seed/sampler for reproducibility); 8) Guarantee identical output for identical input and seed; 9) After generation, audit and annotate any temporal or logic inconsistencies.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"amplified_context\": \"The text presents detailed specifications for two expert-level 'image prompt synthesizer' roles, each outlining a complex, deterministic transformation of user input into syntactically and technically valid prompts suitable for powerful AI image-generation engines (Stable Diffusion, MidJourney). Both tiers (standard and premium) emphasize successive 'phases' that extract, weight, and arrange visual concepts, styles, technical attributes (such as camera metadata, aspect ratio, and lighting), and negative components, ensuring compliance with token limits and model-specific syntax. Additionally, premium variation introduces fail-fast error-handling, visual fidelity gates, warnings for soft issues, and explicit abort codes for constraint violations. Both include principles such as determinism (input/seed → identical output), explicit process modularity (positive/region/negative streams), comprehensive validation (parentheses, token/character limits, parameter order), clear language use, and technical reproducibility. The distilled follow-up adapts these core tenets for the domain of video prompt generation—generalizing multi-scene, temporally-synchronized, and audiovisual output—by decomposing input modulation, per-scene extraction/metadata, timeline sequencing, cross-frame coherence, constraint auditing, explicit error/warning flows, and reproducible, structured schema outputs.\",\n  \"explicit_requests\": [\n    \"Rewrite the original image prompt synthesizer prompt to create an expert video generator role.\",\n    \"Establish a process that converts free-form input into multi-scene, temporally ordered video prompt structures.\",\n    \"Extract and annotate per-scene visual, action, and audio elements, including scene timing, motion metadata, and transitions.\",\n    \"Sequence these into a coherent video timeline, specifying global parameters for pacing, resolution, continuity, and style.\",\n    \"Implement validation steps for continuity, transitions, flicker/jump-cut minimization, and audio-video synchrony.\",\n    \"Define structured output schemas encompassing all scene, transition, effect, audio, and metadata details.\",\n    \"Guarantee deterministic output (input+seed→identical output) as with the image prompt roles.\",\n    \"Surface warnings or abort codes on constraint/validation failures.\",\n    \"Audit and annotate any logical or temporal issues after generation.\"\n  ],\n  \"hidden_assumptions\": [\n    \"Image prompt pipeline design principles and constraints can be mapped and scaled to the video domain.\",\n    \"Temporal sequencing, continuity, and multi-modal blending are central to video prompt engineering as subject fidelity is to images.\",\n    \"Users require deterministic, reproducible video prompt outputs for professional or production contexts.\",\n    \"Automated validation and explicit semantic structure (arrays, dictionaries, directives) are needed for machine consumption and troubleshooting.\",\n    \"Seamless transitions, synchronized audiovisual components, and flicker minimization are reliable proxies for 'video quality' in AI outputs.\",\n    \"Abort codes and warnings provide actionable feedback to avoid wasted compute or undiagnosed errors.\",\n    \"Scene/transition/effect/audiovisual logic can be interpreted and parameterized systematically from free-form language.\",\n    \"Similar technical parameterizations (e.g., seed, sampler, frame count, frame rate) can be ported from existing image model workflows.\",\n    \"Professional video or animation engines expect structured, unambiguous, and temporally annotated input.\"\n  ],\n  \"domain_signals\": [\n    \"Image-to-video prompt workflow adaptation\",\n    \"Deterministic model input/output pipelines\",\n    \"Token/character/weight budgeting and validation\",\n    \"Parameterization of temporal, visual, action, and audio metadata\",\n    \"Scene sequencing and timeline generation\",\n    \"Technical constraints (resolution, frame rate, camera metadata apply analogously)\",\n    \"Validation/auditing gates for output quality and coherence\",\n    \"Fault tolerance via abort codes and warnings\",\n    \"Sector expectation: production-grade outputs, engineering reproducibility, modular schema\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Directive Compressor\n\nYour goal is not to **analyze** the input text, but to **decompose** it into a sequential list of distinct semantic segments. Execute as:\n\n`{role=directive_compressor; input=[primary_directive:str, dependency_map:dict, conflict_nodes:array]; process=[convert_nodes_to_imperative_steps(), resolve_conflict_ordering(), embed_blocker_flags(), enforce_command_voice(), verify_step_uniqueness()]; constraints=[exclude_passive_phrasing(), retain_dependency_order()]; requirements=[imperative_precision(), operational_flow_coherence()]; output={process_steps:array}}`",
      "step": "a",
      "title": "Directive Compressor",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"REWRITE THIS PROMPT TO BE AN EXPERT VIDEO GENERATOR:\\n\\n{\\n\\n    # 1703:\\n    \\\"1703-a-image_prompt_synthesizer\\\": {\\n        \\\"title\\\": \\\"Image Prompt Synthesizer\\\",\\n        \\\"interpretation\\\": \\\"Your goal is **not** to invent new ideas, but to deterministically convert any free‑form user request into a single, engine‑ready image prompt usable by Stable Diffusion and Midjourney. Perform extraction, styling, technical framing, and final validation in **one pass**.\\\",\\n        \\\"transformation\\\": \\\"`{role=image_prompt_synthesizer; input=[raw_request:str]; process=[extract_visual_elements(), push_concrete_nouns_front(), apply_token_weighting(), compose_multiprompt(::), generate_region_tags(), inject_negative_channel(--no), select_art_style(), weight_style_token(≤1.4), set_colour_palette(), add_lighting_descriptor(), tune_stylize(0‑1000), tune_chaos(0‑100), embed_camera_EXIF(35 mm f/1.8 ISO100 1/500 s), add_quality_terms(8K,PBR,ray‑tracing), refine_aspect_ratio(), attach_image_weight(--iw 0‑3), mirror_negative_channel(), concatenate_all(), validate_parentheses(), check_double_colon_sum(), confirm_region_syntax(), enforce_token_limit(≤450), lock_seed(), append_steps_sampler(), output_final()], constraints=[positives_first(), numeric_weights_ok(), parameters_last(), region_masks_supported(), keep_core_subject(), style_weight≤1.4, no_scene_change(), aperture_f1.2‑f22(), no_new_concepts(), preserve_weights()], requirements=[≤77_SD_tokens_or_≤450_chars_UI, explicit_ar_if_named(), clear_visual_language(), artistic_coherence(), stylize_range_0‑1000(), chaos_range_0‑100(), professional_terminology(), reproducible_prompt(), unambiguous()], output={final_image_prompt:str}}`\\\",\\n        \\\"context\\\": {\\n            \\\"principles\\\": {\\n                \\\"determinism\\\": \\\"Identical input must always yield the same prompt (seed‑locked).\\\",\\n                \\\"layered_control\\\": \\\"Separate positive, region, and negative semantics before concatenation; merge only at final step.\\\",\\n                \\\"minimal_overhead\\\": \\\"All weighting, style, and camera parameters trail the prose to satisfy engine syntax.\\\"\\n            },\\n            \\\"success_criteria\\\": {\\n                \\\"syntactic_validity\\\": \\\"Parentheses, double colon maths, and parameter order pass model parsers.\\\",\\n                \\\"token_compliance\\\": \\\"Prompt length respects the stricter of SD‑77‑token or 450‑char UI limits.\\\",\\n                \\\"subject_fidelity\\\": \\\"Primary subject weight ≥ 1.2; background ≤ 1.0; style weight ≤ 1.4.\\\",\\n                \\\"technical_readiness\\\": \\\"Aspect ratio, seed, sampler, steps, stylize, chaos, quality flags, and optional --iw are present and correctly positioned.\\\",\\n                \\\"clarity\\\": \\\"Language is concrete, jargon‑free, and region masks (if any) use `[tag] … |` notation.\\\"\\n            }\\n        }\\n    },\\n    \\\"1703-b-premium_image_prompt_synthesizer\\\": {\\n        \\\"title\\\": \\\"Premium Image Prompt Synthesizer\\\",\\n        \\\"interpretation\\\": \\\"Convert any free‑form request into a deterministic, model‑ready prompt that meets *premium* visual‑fidelity standards for Stable Diffusion & Midjourney **in a single call** while retaining full error‑handling and quality gates.\\\",\\n        \\\"transformation\\\": \\\"`{role=premium_image_prompt_synthesizer; input=[raw_request:str]; process=[\\\\n  /* — PHASE 1 : Context & Safety — */\\\\n  extract_visual_elements(),\\\\n  detect_conflicting_aspect_ratio(),                       abort_if_conflict(),\\\\n  enforce_token_budget(≤77_SD_tokens_or_≤450_chars_UI),    abort_if_exceeded(),\\\\n  /* — PHASE 2 : Positive Channel Build — */\\\\n  push_concrete_nouns_front(),\\\\n  apply_token_weighting(primary≥1.2,background≤1.0),       abort_if_total_weight≤0,\\\\n  compose_multiprompt(::),\\\\n  generate_region_tags_if_scene_implied(),                 note_mobile_mask_warning(),\\\\n  /* — PHASE 3 : Style & Aesthetics — */\\\\n  select_art_style(),\\\\n  reject_style_if_conflicts_camera_realism(),\\\\n  weight_style_token(≤1.4),\\\\n  set_colour_palette(harmonious),\\\\n  add_lighting_descriptor(coherent_with_palette),\\\\n  tune_stylize(bounds=0‑1000),                             abort_if_out_of_bounds(),\\\\n  tune_chaos(bounds=0‑100),                                abort_if_out_of_bounds(),\\\\n  /* — PHASE 4 : Technical & Camera — */\\\\n  embed_camera_EXIF(\\\\\\\"35 mm f/1.8 ISO100 1/500 s\\\\\\\"),\\\\n  verify_aperture_range(f1.2‑f22),                         abort_if_invalid(),\\\\n  add_quality_terms(8K,PBR,ray‑tracing),\\\\n  refine_aspect_ratio(),\\\\n  attach_image_weight(--iw 0‑3_if_URL_present),            abort_if_IW_out_of_bounds(),\\\\n  /* — PHASE 5 : Negative Channel & Mirroring — */\\\\n  collect_user_negatives(),\\\\n  append_builtin_negatives(overblur,lowres,distracting_text),\\\\n  validate_negative_strength(avoids_colour_washout),\\\\n  /* — PHASE 6 : Final Assembly & Validation — */\\\\n  concatenate_positive_negative(),                         parameters_trail_prose(),\\\\n  validate_parentheses_balance(),\\\\n  check_double_colon_weight_sum(),                         abort_if_weight_sum_zero(),\\\\n  confirm_region_syntax(),\\\\n  lock_seed_and_append_sampler_steps(),\\\\n  output_final_prompt()\\\\n],\\\\nconstraints=[\\\\n  positives_first(), parameters_last(), region_masks_supported(),\\\\n  no_new_concepts(), preserve_weights(), style_weight≤1.4,\\\\n  professional_terminology(), explicit_ar_if_named()\\\\n],\\\\nrequirements=[\\\\n  premium_visual_fidelity(), reproducible(), clear_visual_language(),\\\\n  quality_gate(FID<15_or_equivalent), atomic_fail_fast_on_violation()\\\\n],\\\\noutput={final_image_prompt:str, warnings:list, abort_code:int|null}}`\\\",\\n        \\\"context\\\": {\\n            \\\"principles\\\": {\\n                \\\"fail_fast\\\": \\\"Abort with explicit code when any hard constraint is violated to prevent wasted renders.\\\",\\n                \\\"surface_blockers\\\": \\\"Return `warnings` array for soft issues (e.g. region masks on mobile).\\\",\\n                \\\"premium_fidelity\\\": \\\"Always target ultra‑sharp, detail‑rich, production‑grade outputs.\\\"\\n            },\\n            \\\"success_criteria\\\": {\\n                \\\"syntactic_pass\\\": \\\"Prompt parses without error in both SD & MJ (parentheses, `::`, parameter order).\\\",\\n                \\\"visual_quality\\\": \\\"Meets or exceeds internal FID / LPIPS thresholds (or skips metric if unavailable).\\\",\\n                \\\"determinism\\\": \\\"Identical input + seed => identical output.\\\",\\n                \\\"safety\\\": \\\"No colour washout, no truncated tokens, no invalid weights, no parameter conflicts.\\\"\\n            },\\n            \\\"edge_case_handling\\\": {\\n                \\\"zero_weight_sum\\\": \\\"Hard abort (`abort_code` = 92).\\\",\\n                \\\"token_budget_overflow\\\": \\\"Hard abort (`abort_code` = 77).\\\",\\n                \\\"aspect_ratio_conflict\\\": \\\"Hard abort (`abort_code` = AR1).\\\",\\n                \\\"out_of_bounds_stylize_chaos\\\": \\\"Hard abort (`abort_code` = SC1).\\\",\\n                \\\"IW_without_URL\\\": \\\"Warning only; `--iw` dropped.\\\"\\n            }\\n        }\\n    }\\n\\n}\\\"```'\\n\\n\",\n  \"amplified_context\": \"The text presents detailed specifications for two expert-level 'image prompt synthesizer' roles, each outlining a complex, deterministic transformation of user input into syntactically and technically valid prompts suitable for powerful AI image-generation engines (Stable Diffusion, MidJourney). Both tiers (standard and premium) emphasize successive 'phases' that extract, weight, and arrange visual concepts, styles, technical attributes (such as camera metadata, aspect ratio, and lighting), and negative components, ensuring compliance with token limits and model-specific syntax. Additionally, premium variation introduces fail-fast error-handling, visual fidelity gates, warnings for soft issues, and explicit abort codes for constraint violations. Both include principles such as determinism (input/seed → identical output), explicit process modularity (positive/region/negative streams), comprehensive validation (parentheses, token/character limits, parameter order), clear language use, and technical reproducibility. The distilled follow-up adapts these core tenets for the domain of video prompt generation—generalizing multi-scene, temporally-synchronized, and audiovisual output—by decomposing input modulation, per-scene extraction/metadata, timeline sequencing, cross-frame coherence, constraint auditing, explicit error/warning flows, and reproducible, structured schema outputs.\",\n  \"explicit_requests\": [\n    \"Rewrite the original image prompt synthesizer prompt to create an expert video generator role.\",\n    \"Establish a process that converts free-form input into multi-scene, temporally ordered video prompt structures.\",\n    \"Extract and annotate per-scene visual, action, and audio elements, including scene timing, motion metadata, and transitions.\",\n    \"Sequence these into a coherent video timeline, specifying global parameters for pacing, resolution, continuity, and style.\",\n    \"Implement validation steps for continuity, transitions, flicker/jump-cut minimization, and audio-video synchrony.\",\n    \"Define structured output schemas encompassing all scene, transition, effect, audio, and metadata details.\",\n    \"Guarantee deterministic output (input+seed→identical output) as with the image prompt roles.\",\n    \"Surface warnings or abort codes on constraint/validation failures.\",\n    \"Audit and annotate any logical or temporal issues after generation.\"\n  ],\n  \"hidden_assumptions\": [\n    \"Image prompt pipeline design principles and constraints can be mapped and scaled to the video domain.\",\n    \"Temporal sequencing, continuity, and multi-modal blending are central to video prompt engineering as subject fidelity is to images.\",\n    \"Users require deterministic, reproducible video prompt outputs for professional or production contexts.\",\n    \"Automated validation and explicit semantic structure (arrays, dictionaries, directives) are needed for machine consumption and troubleshooting.\",\n    \"Seamless transitions, synchronized audiovisual components, and flicker minimization are reliable proxies for 'video quality' in AI outputs.\",\n    \"Abort codes and warnings provide actionable feedback to avoid wasted compute or undiagnosed errors.\",\n    \"Scene/transition/effect/audiovisual logic can be interpreted and parameterized systematically from free-form language.\",\n    \"Similar technical parameterizations (e.g., seed, sampler, frame count, frame rate) can be ported from existing image model workflows.\",\n    \"Professional video or animation engines expect structured, unambiguous, and temporally annotated input.\"\n  ],\n  \"domain_signals\": [\n    \"Image-to-video prompt workflow adaptation\",\n    \"Deterministic model input/output pipelines\",\n    \"Token/character/weight budgeting and validation\",\n    \"Parameterization of temporal, visual, action, and audio metadata\",\n    \"Scene sequencing and timeline generation\",\n    \"Technical constraints (resolution, frame rate, camera metadata apply analogously)\",\n    \"Validation/auditing gates for output quality and coherence\",\n    \"Fault tolerance via abort codes and warnings\",\n    \"Sector expectation: production-grade outputs, engineering reproducibility, modular schema\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"process_steps\": [\n    \"Define the role as expert_video_prompt_generator.\",\n    \"Receive raw_user_request as input.\",\n    \"Extract global intent, target style, and desired output duration from the user request.\",\n    \"Split input into discrete scenes or shots; if not explicit, infer via action/visual change and temporal cues.\",\n    \"For each scene, extract and annotate core visual elements, actions, spatial attributes, and scene-specific audio cues.\",\n    \"Assign each scene a unique scene_id, temporal offset/duration, and camera parameters (e.g., viewpoint, motion, lens, framing).\",\n    \"Compile motion metadata (actor/object movement, camera movement, speed, direction) for each scene.\",\n    \"Extract and validate transitions (type, duration, style) between scenes; infer or default to crossfade if unspecified.\",\n    \"For each scene and the sequence overall, extract or interpolate global technical parameters: resolution, frame_rate, color palette, style, aspect_ratio, pacing.\",\n    \"Identify and structure per-scene and global audio elements (soundtrack, sfx, voice, silence); annotate timing, loop, and fade behavior.\",\n    \"Arrange all scenes, transitions, and audio in a temporally ordered timeline structure.\",\n    \"Audit for logical and temporal issues: overlap/conflict between scenes, gaps, abrupt jumps, flicker/jump-cut risk, or unsynchronized audiovisual cues.\",\n    \"Abort and output explicit abort_code if any hard constraint is violated (e.g., total duration, unsupported transition, invalid parameter, token/character/weight overflow).\",\n    \"Generate warnings for soft issues (e.g., ambiguous timing, excessive complexity, unsupported effects on target platform).\",\n    \"Assess global and local continuity (visual and audio), scene-to-scene style coherence, and cross-frame artifact minimization.\",\n    \"Validate parameter order, temporal alignment, and ensure output schema is syntactically and semantically sound for downstream engines.\",\n    \"Enforce deterministic output by lock_seed and strict parameter ordering; ensure identical input/seed gives identical structured prompt.\",\n    \"Build final structured output: array of scenes (with all metadata), ordered transitions, audio tracks (per scene/timeline), global parameters, validation logs, warnings, and abort_code if any.\",\n    \"Return output in explicit, engine-consumable schema (array/dictionary/fields) for machine parsing and production use.\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Task Atomizer\n\nYour goal is not to **praise** or **answer** the submission, but to **interrogate and redirect** it—exposing every structural flaw, quantifying compliance with the Universal Directive System, and issuing a minimal replacement instruction that corrects all defects. Execute as:\n\n`{role=task_atomizer; input=[amplified_context:str]; process=[segment_into_atomic_tasks(), detect_execution_blockers(), extract_implicit_conditions(), flag_sequence_dependencies()]; constraints=[no_rephrasing(), atomization_only()]; requirements=[atomic_task_integrity(), dependency_flag_accuracy()]; output={atomic_tasks:list, execution_blockers:array, dependency_flags:array}}`",
      "step": "b",
      "title": "Task Atomizer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"REWRITE THIS PROMPT TO BE AN EXPERT VIDEO GENERATOR:\\n\\n{\\n\\n    # 1703:\\n    \\\"1703-a-image_prompt_synthesizer\\\": {\\n        \\\"title\\\": \\\"Image Prompt Synthesizer\\\",\\n        \\\"interpretation\\\": \\\"Your goal is **not** to invent new ideas, but to deterministically convert any free‑form user request into a single, engine‑ready image prompt usable by Stable Diffusion and Midjourney. Perform extraction, styling, technical framing, and final validation in **one pass**.\\\",\\n        \\\"transformation\\\": \\\"`{role=image_prompt_synthesizer; input=[raw_request:str]; process=[extract_visual_elements(), push_concrete_nouns_front(), apply_token_weighting(), compose_multiprompt(::), generate_region_tags(), inject_negative_channel(--no), select_art_style(), weight_style_token(≤1.4), set_colour_palette(), add_lighting_descriptor(), tune_stylize(0‑1000), tune_chaos(0‑100), embed_camera_EXIF(35 mm f/1.8 ISO100 1/500 s), add_quality_terms(8K,PBR,ray‑tracing), refine_aspect_ratio(), attach_image_weight(--iw 0‑3), mirror_negative_channel(), concatenate_all(), validate_parentheses(), check_double_colon_sum(), confirm_region_syntax(), enforce_token_limit(≤450), lock_seed(), append_steps_sampler(), output_final()], constraints=[positives_first(), numeric_weights_ok(), parameters_last(), region_masks_supported(), keep_core_subject(), style_weight≤1.4, no_scene_change(), aperture_f1.2‑f22(), no_new_concepts(), preserve_weights()], requirements=[≤77_SD_tokens_or_≤450_chars_UI, explicit_ar_if_named(), clear_visual_language(), artistic_coherence(), stylize_range_0‑1000(), chaos_range_0‑100(), professional_terminology(), reproducible_prompt(), unambiguous()], output={final_image_prompt:str}}`\\\",\\n        \\\"context\\\": {\\n            \\\"principles\\\": {\\n                \\\"determinism\\\": \\\"Identical input must always yield the same prompt (seed‑locked).\\\",\\n                \\\"layered_control\\\": \\\"Separate positive, region, and negative semantics before concatenation; merge only at final step.\\\",\\n                \\\"minimal_overhead\\\": \\\"All weighting, style, and camera parameters trail the prose to satisfy engine syntax.\\\"\\n            },\\n            \\\"success_criteria\\\": {\\n                \\\"syntactic_validity\\\": \\\"Parentheses, double colon maths, and parameter order pass model parsers.\\\",\\n                \\\"token_compliance\\\": \\\"Prompt length respects the stricter of SD‑77‑token or 450‑char UI limits.\\\",\\n                \\\"subject_fidelity\\\": \\\"Primary subject weight ≥ 1.2; background ≤ 1.0; style weight ≤ 1.4.\\\",\\n                \\\"technical_readiness\\\": \\\"Aspect ratio, seed, sampler, steps, stylize, chaos, quality flags, and optional --iw are present and correctly positioned.\\\",\\n                \\\"clarity\\\": \\\"Language is concrete, jargon‑free, and region masks (if any) use `[tag] … |` notation.\\\"\\n            }\\n        }\\n    },\\n    \\\"1703-b-premium_image_prompt_synthesizer\\\": {\\n        \\\"title\\\": \\\"Premium Image Prompt Synthesizer\\\",\\n        \\\"interpretation\\\": \\\"Convert any free‑form request into a deterministic, model‑ready prompt that meets *premium* visual‑fidelity standards for Stable Diffusion & Midjourney **in a single call** while retaining full error‑handling and quality gates.\\\",\\n        \\\"transformation\\\": \\\"`{role=premium_image_prompt_synthesizer; input=[raw_request:str]; process=[\\\\n  /* — PHASE 1 : Context & Safety — */\\\\n  extract_visual_elements(),\\\\n  detect_conflicting_aspect_ratio(),                       abort_if_conflict(),\\\\n  enforce_token_budget(≤77_SD_tokens_or_≤450_chars_UI),    abort_if_exceeded(),\\\\n  /* — PHASE 2 : Positive Channel Build — */\\\\n  push_concrete_nouns_front(),\\\\n  apply_token_weighting(primary≥1.2,background≤1.0),       abort_if_total_weight≤0,\\\\n  compose_multiprompt(::),\\\\n  generate_region_tags_if_scene_implied(),                 note_mobile_mask_warning(),\\\\n  /* — PHASE 3 : Style & Aesthetics — */\\\\n  select_art_style(),\\\\n  reject_style_if_conflicts_camera_realism(),\\\\n  weight_style_token(≤1.4),\\\\n  set_colour_palette(harmonious),\\\\n  add_lighting_descriptor(coherent_with_palette),\\\\n  tune_stylize(bounds=0‑1000),                             abort_if_out_of_bounds(),\\\\n  tune_chaos(bounds=0‑100),                                abort_if_out_of_bounds(),\\\\n  /* — PHASE 4 : Technical & Camera — */\\\\n  embed_camera_EXIF(\\\\\\\"35 mm f/1.8 ISO100 1/500 s\\\\\\\"),\\\\n  verify_aperture_range(f1.2‑f22),                         abort_if_invalid(),\\\\n  add_quality_terms(8K,PBR,ray‑tracing),\\\\n  refine_aspect_ratio(),\\\\n  attach_image_weight(--iw 0‑3_if_URL_present),            abort_if_IW_out_of_bounds(),\\\\n  /* — PHASE 5 : Negative Channel & Mirroring — */\\\\n  collect_user_negatives(),\\\\n  append_builtin_negatives(overblur,lowres,distracting_text),\\\\n  validate_negative_strength(avoids_colour_washout),\\\\n  /* — PHASE 6 : Final Assembly & Validation — */\\\\n  concatenate_positive_negative(),                         parameters_trail_prose(),\\\\n  validate_parentheses_balance(),\\\\n  check_double_colon_weight_sum(),                         abort_if_weight_sum_zero(),\\\\n  confirm_region_syntax(),\\\\n  lock_seed_and_append_sampler_steps(),\\\\n  output_final_prompt()\\\\n],\\\\nconstraints=[\\\\n  positives_first(), parameters_last(), region_masks_supported(),\\\\n  no_new_concepts(), preserve_weights(), style_weight≤1.4,\\\\n  professional_terminology(), explicit_ar_if_named()\\\\n],\\\\nrequirements=[\\\\n  premium_visual_fidelity(), reproducible(), clear_visual_language(),\\\\n  quality_gate(FID<15_or_equivalent), atomic_fail_fast_on_violation()\\\\n],\\\\noutput={final_image_prompt:str, warnings:list, abort_code:int|null}}`\\\",\\n        \\\"context\\\": {\\n            \\\"principles\\\": {\\n                \\\"fail_fast\\\": \\\"Abort with explicit code when any hard constraint is violated to prevent wasted renders.\\\",\\n                \\\"surface_blockers\\\": \\\"Return `warnings` array for soft issues (e.g. region masks on mobile).\\\",\\n                \\\"premium_fidelity\\\": \\\"Always target ultra‑sharp, detail‑rich, production‑grade outputs.\\\"\\n            },\\n            \\\"success_criteria\\\": {\\n                \\\"syntactic_pass\\\": \\\"Prompt parses without error in both SD & MJ (parentheses, `::`, parameter order).\\\",\\n                \\\"visual_quality\\\": \\\"Meets or exceeds internal FID / LPIPS thresholds (or skips metric if unavailable).\\\",\\n                \\\"determinism\\\": \\\"Identical input + seed => identical output.\\\",\\n                \\\"safety\\\": \\\"No colour washout, no truncated tokens, no invalid weights, no parameter conflicts.\\\"\\n            },\\n            \\\"edge_case_handling\\\": {\\n                \\\"zero_weight_sum\\\": \\\"Hard abort (`abort_code` = 92).\\\",\\n                \\\"token_budget_overflow\\\": \\\"Hard abort (`abort_code` = 77).\\\",\\n                \\\"aspect_ratio_conflict\\\": \\\"Hard abort (`abort_code` = AR1).\\\",\\n                \\\"out_of_bounds_stylize_chaos\\\": \\\"Hard abort (`abort_code` = SC1).\\\",\\n                \\\"IW_without_URL\\\": \\\"Warning only; `--iw` dropped.\\\"\\n            }\\n        }\\n    }\\n\\n}\\\"```'\\n\\n\",\n  \"process_steps\": [\n    \"Define the role as expert_video_prompt_generator.\",\n    \"Receive raw_user_request as input.\",\n    \"Extract global intent, target style, and desired output duration from the user request.\",\n    \"Split input into discrete scenes or shots; if not explicit, infer via action/visual change and temporal cues.\",\n    \"For each scene, extract and annotate core visual elements, actions, spatial attributes, and scene-specific audio cues.\",\n    \"Assign each scene a unique scene_id, temporal offset/duration, and camera parameters (e.g., viewpoint, motion, lens, framing).\",\n    \"Compile motion metadata (actor/object movement, camera movement, speed, direction) for each scene.\",\n    \"Extract and validate transitions (type, duration, style) between scenes; infer or default to crossfade if unspecified.\",\n    \"For each scene and the sequence overall, extract or interpolate global technical parameters: resolution, frame_rate, color palette, style, aspect_ratio, pacing.\",\n    \"Identify and structure per-scene and global audio elements (soundtrack, sfx, voice, silence); annotate timing, loop, and fade behavior.\",\n    \"Arrange all scenes, transitions, and audio in a temporally ordered timeline structure.\",\n    \"Audit for logical and temporal issues: overlap/conflict between scenes, gaps, abrupt jumps, flicker/jump-cut risk, or unsynchronized audiovisual cues.\",\n    \"Abort and output explicit abort_code if any hard constraint is violated (e.g., total duration, unsupported transition, invalid parameter, token/character/weight overflow).\",\n    \"Generate warnings for soft issues (e.g., ambiguous timing, excessive complexity, unsupported effects on target platform).\",\n    \"Assess global and local continuity (visual and audio), scene-to-scene style coherence, and cross-frame artifact minimization.\",\n    \"Validate parameter order, temporal alignment, and ensure output schema is syntactically and semantically sound for downstream engines.\",\n    \"Enforce deterministic output by lock_seed and strict parameter ordering; ensure identical input/seed gives identical structured prompt.\",\n    \"Build final structured output: array of scenes (with all metadata), ordered transitions, audio tracks (per scene/timeline), global parameters, validation logs, warnings, and abort_code if any.\",\n    \"Return output in explicit, engine-consumable schema (array/dictionary/fields) for machine parsing and production use.\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"atomic_tasks\": [\n    \"Define expert_video_prompt_generator role.\",\n    \"Receive raw_user_request as input.\",\n    \"Extract global intent from user input.\",\n    \"Extract target visual style from user input.\",\n    \"Extract desired output duration from user input.\",\n    \"Determine if user input has explicit scene/shot breakdown.\",\n    \"If scene/shot breakdown is explicit, segment accordingly.\",\n    \"If not explicit, infer scene/shot splits using action/visual/temporal cues.\",\n    \"For each identified scene, extract core visual elements.\",\n    \"For each identified scene, extract described or implied actions.\",\n    \"For each identified scene, extract spatial attributes.\",\n    \"For each identified scene, extract scene-specific audio cues.\",\n    \"Annotate each scene with a unique scene_id.\",\n    \"Annotate each scene with temporal offset and duration.\",\n    \"Assign camera viewpoint to each scene.\",\n    \"Assign camera motion to each scene.\",\n    \"Assign camera lens/framing to each scene.\",\n    \"Compile actor and object motion metadata for each scene.\",\n    \"Compile camera movement metadata for each scene.\",\n    \"Compile speed and direction of all moving elements for each scene.\",\n    \"Detect transitions between scenes.\",\n    \"Extract transition type, duration, and style for each detected transition.\",\n    \"If transition details unspecified, set default transition to crossfade.\",\n    \"Extract or interpolate global technical parameters: resolution.\",\n    \"Extract or interpolate global technical parameters: frame_rate.\",\n    \"Extract or interpolate global technical parameters: color_palette.\",\n    \"Extract or interpolate global technical parameters: style.\",\n    \"Extract or interpolate global technical parameters: aspect_ratio.\",\n    \"Extract or interpolate global technical parameters: pacing.\",\n    \"Extract per-scene technical parameters where specified.\",\n    \"Identify global audio elements (soundtrack, sfx, voice, silence).\",\n    \"Identify scene-specific audio elements.\",\n    \"Annotate timing, loop, and fade behavior for all audio cues.\",\n    \"Arrange scenes, transitions, and audio in a temporally ordered timeline structure.\",\n    \"Audit timeline for overlap/conflict between scenes.\",\n    \"Audit timeline for temporal gaps between scenes.\",\n    \"Audit transitions for abrupt jumps and flicker/jump-cut risk.\",\n    \"Audit audio cues for unsynchronized alignment with timeline.\",\n    \"Abort and output explicit abort_code if any hard constraint is violated (e.g. duration limit, unsupported transition, invalid parameter, token/character/weight overflow).\",\n    \"Generate warnings for soft issues (e.g. ambiguous timing, excessive complexity, unsupported effects on target platform).\",\n    \"Assess scene-to-scene visual continuity.\",\n    \"Assess audio continuity across scenes.\",\n    \"Assess style coherence across all scenes.\",\n    \"Assess cross-frame artifact minimization.\",\n    \"Validate parameter order for output.\",\n    \"Validate temporal alignment for all elements.\",\n    \"Ensure syntactic and semantic validity of output schema for downstream engines.\",\n    \"Enforce deterministic output by lock_seed.\",\n    \"Enforce strict parameter ordering for determinism.\",\n    \"Build final structured output: array of scenes with metadata, ordered transitions, audio tracks per scene/timeline, global parameters, validation logs, warnings, and abort_code if any.\",\n    \"Return output in explicit, engine-consumable schema (array/dictionary/fields) for machine parsing and production use.\"\n  ],\n  \"execution_blockers\": [\n    \"No explicit fail-fast for unsupported audio/visual effect definitions.\",\n    \"No handling for non-resolvable ambiguities in scene boundary inference.\",\n    \"No explicit path for overwriting or combining conflicting technical parameters across scenes.\",\n    \"No handling of explicit user negative cues (e.g. elements to avoid).\"\n  ],\n  \"dependency_flags\": [\n    \"task_3 AND task_4 AND task_5 → task_6\", \n    \"task_6 → task_7 OR task_8\", \n    \"task_7 OR task_8 → tasks_9-12\",\n    \"tasks_9-12 → tasks_13-19\",\n    \"tasks_13-19 → tasks_20-23\",\n    \"tasks_20-23 → tasks_24-29\",\n    \"tasks_24-29 → tasks_30-32\",\n    \"tasks_30-32 → task_33\",\n    \"tasks_33 → tasks_34-37\",\n    \"tasks_34-37 → task_38\",\n    \"task_38 → tasks_39-40\",\n    \"tasks_39-40 → tasks_41-44\",\n    \"tasks_41-44 → tasks_45-47\",\n    \"tasks_45-47 → tasks_48-51\",\n    \"tasks_48-51 → tasks_52-53\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Perspective Broadcaster\n\nYour goal is not to **rearrange** the segments arbitrarily, but to **reorder** them based on optimal logical and semantic flow. Execute as:\n\n`{role=perspective_broadcaster; input=[atomic_tasks:list, domain_signals:array]; process=[generate_cross_domain_lenses(), append_edge_case_scenarios(), relate_tasks_to_stakeholder_views(), widen_risk_landscape()]; constraints=[avoid_solution_details(), maintain_task_reference_order()]; requirements=[viewpoint_diversity(), risk_surface_clarity()]; output={task_perspectives:list}}`",
      "step": "c",
      "title": "Perspective Broadcaster",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"REWRITE THIS PROMPT TO BE AN EXPERT VIDEO GENERATOR:\\n\\n{\\n\\n    # 1703:\\n    \\\"1703-a-image_prompt_synthesizer\\\": {\\n        \\\"title\\\": \\\"Image Prompt Synthesizer\\\",\\n        \\\"interpretation\\\": \\\"Your goal is **not** to invent new ideas, but to deterministically convert any free‑form user request into a single, engine‑ready image prompt usable by Stable Diffusion and Midjourney. Perform extraction, styling, technical framing, and final validation in **one pass**.\\\",\\n        \\\"transformation\\\": \\\"`{role=image_prompt_synthesizer; input=[raw_request:str]; process=[extract_visual_elements(), push_concrete_nouns_front(), apply_token_weighting(), compose_multiprompt(::), generate_region_tags(), inject_negative_channel(--no), select_art_style(), weight_style_token(≤1.4), set_colour_palette(), add_lighting_descriptor(), tune_stylize(0‑1000), tune_chaos(0‑100), embed_camera_EXIF(35 mm f/1.8 ISO100 1/500 s), add_quality_terms(8K,PBR,ray‑tracing), refine_aspect_ratio(), attach_image_weight(--iw 0‑3), mirror_negative_channel(), concatenate_all(), validate_parentheses(), check_double_colon_sum(), confirm_region_syntax(), enforce_token_limit(≤450), lock_seed(), append_steps_sampler(), output_final()], constraints=[positives_first(), numeric_weights_ok(), parameters_last(), region_masks_supported(), keep_core_subject(), style_weight≤1.4, no_scene_change(), aperture_f1.2‑f22(), no_new_concepts(), preserve_weights()], requirements=[≤77_SD_tokens_or_≤450_chars_UI, explicit_ar_if_named(), clear_visual_language(), artistic_coherence(), stylize_range_0‑1000(), chaos_range_0‑100(), professional_terminology(), reproducible_prompt(), unambiguous()], output={final_image_prompt:str}}`\\\",\\n        \\\"context\\\": {\\n            \\\"principles\\\": {\\n                \\\"determinism\\\": \\\"Identical input must always yield the same prompt (seed‑locked).\\\",\\n                \\\"layered_control\\\": \\\"Separate positive, region, and negative semantics before concatenation; merge only at final step.\\\",\\n                \\\"minimal_overhead\\\": \\\"All weighting, style, and camera parameters trail the prose to satisfy engine syntax.\\\"\\n            },\\n            \\\"success_criteria\\\": {\\n                \\\"syntactic_validity\\\": \\\"Parentheses, double colon maths, and parameter order pass model parsers.\\\",\\n                \\\"token_compliance\\\": \\\"Prompt length respects the stricter of SD‑77‑token or 450‑char UI limits.\\\",\\n                \\\"subject_fidelity\\\": \\\"Primary subject weight ≥ 1.2; background ≤ 1.0; style weight ≤ 1.4.\\\",\\n                \\\"technical_readiness\\\": \\\"Aspect ratio, seed, sampler, steps, stylize, chaos, quality flags, and optional --iw are present and correctly positioned.\\\",\\n                \\\"clarity\\\": \\\"Language is concrete, jargon‑free, and region masks (if any) use `[tag] … |` notation.\\\"\\n            }\\n        }\\n    },\\n    \\\"1703-b-premium_image_prompt_synthesizer\\\": {\\n        \\\"title\\\": \\\"Premium Image Prompt Synthesizer\\\",\\n        \\\"interpretation\\\": \\\"Convert any free‑form request into a deterministic, model‑ready prompt that meets *premium* visual‑fidelity standards for Stable Diffusion & Midjourney **in a single call** while retaining full error‑handling and quality gates.\\\",\\n        \\\"transformation\\\": \\\"`{role=premium_image_prompt_synthesizer; input=[raw_request:str]; process=[\\\\n  /* — PHASE 1 : Context & Safety — */\\\\n  extract_visual_elements(),\\\\n  detect_conflicting_aspect_ratio(),                       abort_if_conflict(),\\\\n  enforce_token_budget(≤77_SD_tokens_or_≤450_chars_UI),    abort_if_exceeded(),\\\\n  /* — PHASE 2 : Positive Channel Build — */\\\\n  push_concrete_nouns_front(),\\\\n  apply_token_weighting(primary≥1.2,background≤1.0),       abort_if_total_weight≤0,\\\\n  compose_multiprompt(::),\\\\n  generate_region_tags_if_scene_implied(),                 note_mobile_mask_warning(),\\\\n  /* — PHASE 3 : Style & Aesthetics — */\\\\n  select_art_style(),\\\\n  reject_style_if_conflicts_camera_realism(),\\\\n  weight_style_token(≤1.4),\\\\n  set_colour_palette(harmonious),\\\\n  add_lighting_descriptor(coherent_with_palette),\\\\n  tune_stylize(bounds=0‑1000),                             abort_if_out_of_bounds(),\\\\n  tune_chaos(bounds=0‑100),                                abort_if_out_of_bounds(),\\\\n  /* — PHASE 4 : Technical & Camera — */\\\\n  embed_camera_EXIF(\\\\\\\"35 mm f/1.8 ISO100 1/500 s\\\\\\\"),\\\\n  verify_aperture_range(f1.2‑f22),                         abort_if_invalid(),\\\\n  add_quality_terms(8K,PBR,ray‑tracing),\\\\n  refine_aspect_ratio(),\\\\n  attach_image_weight(--iw 0‑3_if_URL_present),            abort_if_IW_out_of_bounds(),\\\\n  /* — PHASE 5 : Negative Channel & Mirroring — */\\\\n  collect_user_negatives(),\\\\n  append_builtin_negatives(overblur,lowres,distracting_text),\\\\n  validate_negative_strength(avoids_colour_washout),\\\\n  /* — PHASE 6 : Final Assembly & Validation — */\\\\n  concatenate_positive_negative(),                         parameters_trail_prose(),\\\\n  validate_parentheses_balance(),\\\\n  check_double_colon_weight_sum(),                         abort_if_weight_sum_zero(),\\\\n  confirm_region_syntax(),\\\\n  lock_seed_and_append_sampler_steps(),\\\\n  output_final_prompt()\\\\n],\\\\nconstraints=[\\\\n  positives_first(), parameters_last(), region_masks_supported(),\\\\n  no_new_concepts(), preserve_weights(), style_weight≤1.4,\\\\n  professional_terminology(), explicit_ar_if_named()\\\\n],\\\\nrequirements=[\\\\n  premium_visual_fidelity(), reproducible(), clear_visual_language(),\\\\n  quality_gate(FID<15_or_equivalent), atomic_fail_fast_on_violation()\\\\n],\\\\noutput={final_image_prompt:str, warnings:list, abort_code:int|null}}`\\\",\\n        \\\"context\\\": {\\n            \\\"principles\\\": {\\n                \\\"fail_fast\\\": \\\"Abort with explicit code when any hard constraint is violated to prevent wasted renders.\\\",\\n                \\\"surface_blockers\\\": \\\"Return `warnings` array for soft issues (e.g. region masks on mobile).\\\",\\n                \\\"premium_fidelity\\\": \\\"Always target ultra‑sharp, detail‑rich, production‑grade outputs.\\\"\\n            },\\n            \\\"success_criteria\\\": {\\n                \\\"syntactic_pass\\\": \\\"Prompt parses without error in both SD & MJ (parentheses, `::`, parameter order).\\\",\\n                \\\"visual_quality\\\": \\\"Meets or exceeds internal FID / LPIPS thresholds (or skips metric if unavailable).\\\",\\n                \\\"determinism\\\": \\\"Identical input + seed => identical output.\\\",\\n                \\\"safety\\\": \\\"No colour washout, no truncated tokens, no invalid weights, no parameter conflicts.\\\"\\n            },\\n            \\\"edge_case_handling\\\": {\\n                \\\"zero_weight_sum\\\": \\\"Hard abort (`abort_code` = 92).\\\",\\n                \\\"token_budget_overflow\\\": \\\"Hard abort (`abort_code` = 77).\\\",\\n                \\\"aspect_ratio_conflict\\\": \\\"Hard abort (`abort_code` = AR1).\\\",\\n                \\\"out_of_bounds_stylize_chaos\\\": \\\"Hard abort (`abort_code` = SC1).\\\",\\n                \\\"IW_without_URL\\\": \\\"Warning only; `--iw` dropped.\\\"\\n            }\\n        }\\n    }\\n\\n}\\\"```'\\n\\n\",\n  \"atomic_tasks\": [\n    \"Define expert_video_prompt_generator role.\",\n    \"Receive raw_user_request as input.\",\n    \"Extract global intent from user input.\",\n    \"Extract target visual style from user input.\",\n    \"Extract desired output duration from user input.\",\n    \"Determine if user input has explicit scene/shot breakdown.\",\n    \"If scene/shot breakdown is explicit, segment accordingly.\",\n    \"If not explicit, infer scene/shot splits using action/visual/temporal cues.\",\n    \"For each identified scene, extract core visual elements.\",\n    \"For each identified scene, extract described or implied actions.\",\n    \"For each identified scene, extract spatial attributes.\",\n    \"For each identified scene, extract scene-specific audio cues.\",\n    \"Annotate each scene with a unique scene_id.\",\n    \"Annotate each scene with temporal offset and duration.\",\n    \"Assign camera viewpoint to each scene.\",\n    \"Assign camera motion to each scene.\",\n    \"Assign camera lens/framing to each scene.\",\n    \"Compile actor and object motion metadata for each scene.\",\n    \"Compile camera movement metadata for each scene.\",\n    \"Compile speed and direction of all moving elements for each scene.\",\n    \"Detect transitions between scenes.\",\n    \"Extract transition type, duration, and style for each detected transition.\",\n    \"If transition details unspecified, set default transition to crossfade.\",\n    \"Extract or interpolate global technical parameters: resolution.\",\n    \"Extract or interpolate global technical parameters: frame_rate.\",\n    \"Extract or interpolate global technical parameters: color_palette.\",\n    \"Extract or interpolate global technical parameters: style.\",\n    \"Extract or interpolate global technical parameters: aspect_ratio.\",\n    \"Extract or interpolate global technical parameters: pacing.\",\n    \"Extract per-scene technical parameters where specified.\",\n    \"Identify global audio elements (soundtrack, sfx, voice, silence).\",\n    \"Identify scene-specific audio elements.\",\n    \"Annotate timing, loop, and fade behavior for all audio cues.\",\n    \"Arrange scenes, transitions, and audio in a temporally ordered timeline structure.\",\n    \"Audit timeline for overlap/conflict between scenes.\",\n    \"Audit timeline for temporal gaps between scenes.\",\n    \"Audit transitions for abrupt jumps and flicker/jump-cut risk.\",\n    \"Audit audio cues for unsynchronized alignment with timeline.\",\n    \"Abort and output explicit abort_code if any hard constraint is violated (e.g. duration limit, unsupported transition, invalid parameter, token/character/weight overflow).\",\n    \"Generate warnings for soft issues (e.g. ambiguous timing, excessive complexity, unsupported effects on target platform).\",\n    \"Assess scene-to-scene visual continuity.\",\n    \"Assess audio continuity across scenes.\",\n    \"Assess style coherence across all scenes.\",\n    \"Assess cross-frame artifact minimization.\",\n    \"Validate parameter order for output.\",\n    \"Validate temporal alignment for all elements.\",\n    \"Ensure syntactic and semantic validity of output schema for downstream engines.\",\n    \"Enforce deterministic output by lock_seed.\",\n    \"Enforce strict parameter ordering for determinism.\",\n    \"Build final structured output: array of scenes with metadata, ordered transitions, audio tracks per scene/timeline, global parameters, validation logs, warnings, and abort_code if any.\",\n    \"Return output in explicit, engine-consumable schema (array/dictionary/fields) for machine parsing and production use.\"\n  ],\n  \"execution_blockers\": [\n    \"No explicit fail-fast for unsupported audio/visual effect definitions.\",\n    \"No handling for non-resolvable ambiguities in scene boundary inference.\",\n    \"No explicit path for overwriting or combining conflicting technical parameters across scenes.\",\n    \"No handling of explicit user negative cues (e.g. elements to avoid).\"\n  ],\n  \"dependency_flags\": [\n    \"task_3 AND task_4 AND task_5 → task_6\",\n    \"task_6 → task_7 OR task_8\",\n    \"task_7 OR task_8 → tasks_9-12\",\n    \"tasks_9-12 → tasks_13-19\",\n    \"tasks_13-19 → tasks_20-23\",\n    \"tasks_20-23 → tasks_24-29\",\n    \"tasks_24-29 → tasks_30-32\",\n    \"tasks_30-32 → task_33\",\n    \"tasks_33 → tasks_34-37\",\n    \"tasks_34-37 → task_38\",\n    \"task_38 → tasks_39-40\",\n    \"tasks_39-40 → tasks_41-44\",\n    \"tasks_41-44 → tasks_45-47\",\n    \"tasks_45-47 → tasks_48-51\",\n    \"tasks_48-51 → tasks_52-53\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"task_perspectives\": [\n    {\n      \"task\": \"Define expert_video_prompt_generator role.\",\n      \"cross_domain_lens\": [\n        \"From software architecture, treating the generator as an interface contract ensures dependability and reproducibility.\",\n        \"In content production workflows, explicit expert protocol signals trust, regulatory compliance, and production readiness.\"\n      ],\n      \"edge_cases\": [\n        \"Ambiguity over required expertise level for niche or experimental video styles.\",\n        \"Role may require adaptation when interfacing with domain-specific video engines or uncommon output formats.\"\n      ],\n      \"stakeholder_views\": [\n        \"Product leads value clear role boundaries for responsibility tracing.\",\n        \"End-users expect comprehensible role definitions correlating with final output style.\"\n      ],\n      \"risk_landscape\": [\n        \"Role definition that's too broad/ambiguous risks inconsistent outputs.\",\n        \"Overly narrow role risks limiting innovation or failing unexpected requests.\"\n      ]\n    },\n    {\n      \"task\": \"Receive raw_user_request as input.\",\n      \"cross_domain_lens\": [\n        \"A UX design view emphasizes the importance of accommodating informal language.\",\n        \"API design foregrounds robust parsing strategies for free-form user demands.\"\n      ],\n      \"edge_cases\": [\n        \"Non-linguistic, visual, or partial-input cases (e.g., reference images) lacking verbal description.\",\n        \"High-volume, conflicting, or repetitive requests in a batch processing context.\"\n      ],\n      \"stakeholder_views\": [\n        \"User-experience teams focus on minimal friction during request intake.\",\n        \"Engineers prioritize normalization before downstream parsing.\"\n      ],\n      \"risk_landscape\": [\n        \"Possible misinterpretation of vague or multi-purpose inputs.\",\n        \"Potential injection of harmful instructions if validation is weak.\"\n      ]\n    },\n    {\n      \"task\": \"Extract global intent from user input.\",\n      \"cross_domain_lens\": [\n        \"Information retrieval parallels effective query understanding.\",\n        \"Cognitive psychology frames this as reading for gist/summarization.\"\n      ],\n      \"edge_cases\": [\n        \"Conflicting intent phrases or simultaneous multiple goal expressions.\",\n        \"Completely implicit intent (user fails to state goal outright).\"\n      ],\n      \"stakeholder_views\": [\n        \"Creative directors need intent clarity for briefing automation.\",\n        \"Automated validators require explicit intent to apply constraints.\"\n      ],\n      \"risk_landscape\": [\n        \"Incorrect intent extraction can derail the entire production.\",\n        \"Nuanced or context-specific intent may be lost in minimal extraction.\"\n      ]\n    },\n    {\n      \"task\": \"Extract target visual style from user input.\",\n      \"cross_domain_lens\": [\n        \"In branding/marketing, style adherence is contractual.\",\n        \"ML model conditioning links style tokens to established model outputs.\"\n      ],\n      \"edge_cases\": [\n        \"User mixes incompatible styles, e.g., 'photo-realistic anime'.\",\n        \"The style intended is known only in highly specialized contexts.\"\n      ],\n      \"stakeholder_views\": [\n        \"Brand managers require style to match organizational guidelines.\",\n        \"Technical teams prioritize mapping styles to engine-supported attributes.\"\n      ],\n      \"risk_landscape\": [\n        \"Unresolved style ambiguity leads to unpredictable output.\",\n        \"Engine mapping limitations may silently degrade fidelity.\"\n      ]\n    },\n    {\n      \"task\": \"Extract desired output duration from user input.\",\n      \"cross_domain_lens\": [\n        \"Media scheduling sees duration as a hard constraint for broadcast.\",\n        \"Rendering optimization treats duration as core to production cost.\"\n      ],\n      \"edge_cases\": [\n        \"User omits duration or supplies an unfeasible/extreme value.\",\n        \"Duration specified in nonconventional units (beats/frames).\"\n      ],\n      \"stakeholder_views\": [\n        \"Production managers see overruns as budget threats.\",\n        \"Viewers expect consistency with provided spec.\"\n      ],\n      \"risk_landscape\": [\n        \"Duration ambiguity can cause failed outputs or truncated/zero-length videos.\",\n        \"Long durations exponentially increase processing costs.\"\n      ]\n    },\n    {\n      \"task\": \"Determine if user input has explicit scene/shot breakdown.\",\n      \"cross_domain_lens\": [\n        \"Literary adaptation involves parsing structured versus stream-of-consciousness descriptions.\",\n        \"Scriptwriting tools need to detect structural markers automatically.\"\n      ],\n      \"edge_cases\": [\n        \"User provides partial, inconsistent, or mismatched scene markers.\",\n        \"Multi-modal descriptions blending scene and shot indistinctly.\"\n      ],\n      \"stakeholder_views\": [\n        \"Editors require faithful translation of explicit breakdowns.\",\n        \"Engineers need to differentiate explicit from implicit to prevent mis-parsing.\"\n      ],\n      \"risk_landscape\": [\n        \"Missed explicit divisions can flatten intended complexity.\",\n        \"Improper detection can sever continuous action or create artificial jumps.\"\n      ]\n    },\n    {\n      \"task\": \"If scene/shot breakdown is explicit, segment accordingly.\",\n      \"cross_domain_lens\": [\n        \"Data science formalizes 'chunking' documents by signal flags.\",\n        \"Video editing requires precise segmentation for non-linear workflows.\"\n      ],\n      \"edge_cases\": [\n        \"Explicit segments overlap, conflict, or are non-contiguous.\",\n        \"Segment definitions shift mid-prompt (scene to shot).\"\n      ],\n      \"stakeholder_views\": [\n        \"Editors value fidelity to user intention.\",\n        \"Scripting engines need deterministic chunk boundary logic.\"\n      ],\n      \"risk_landscape\": [\n        \"Overlapping or missed segments disrupt timeline logic.\",\n        \"Improper segmentation cascades to downstream errors.\"\n      ]\n    },\n    {\n      \"task\": \"If not explicit, infer scene/shot splits using action/visual/temporal cues.\",\n      \"cross_domain_lens\": [\n        \"Natural language processing frames this as semantic boundary detection.\",\n        \"Film theory values cues from verbs/actions and visual tempo.\"\n      ],\n      \"edge_cases\": [\n        \"Ambiguous action cues (e.g., 'suddenly' with no subject).\",\n        \"Rapid transitions that elide boundaries (montages, jump-cuts).\"\n      ],\n      \"stakeholder_views\": [\n        \"Writers and directors expect inference of logical scene units.\",\n        \"Automated systems need reliability in inferred splits for machine parsing.\"\n      ],\n      \"risk_landscape\": [\n        \"Scene mis-inference leads to jarring transitions and missed action.\",\n        \"Boundary ambiguity increases with creative/poetic input styles.\"\n      ]\n    },\n    {\n      \"task\": \"For each identified scene, extract core visual elements.\",\n      \"cross_domain_lens\": [\n        \"Computer vision emphasizes object and setting tagging.\",\n        \"Information design sees core elements as minimalist representations.\"\n      ],\n      \"edge_cases\": [\n        \"Crowded scenes with equal visual weight across elements.\",\n        \"Minimalist/abstract scenes lacking concrete visual reference.\"\n      ],\n      \"stakeholder_views\": [\n        \"Art directors require adherence to visual primacy.\",\n        \"Animators depend on explicit element extraction for rigging/layout.\"\n      ],\n      \"risk_landscape\": [\n        \"Overlooking elements may distort intended imagery.\",\n        \"Misweighting visuals can affect model prompt fidelity.\"\n      ]\n    },\n    {\n      \"task\": \"For each identified scene, extract described or implied actions.\",\n      \"cross_domain_lens\": [\n        \"Behavioral modeling seeks action verbs as triggers.\",\n        \"Storyboarding treats action as temporal events for animatics.\"\n      ],\n      \"edge_cases\": [\n        \"Implicit action only via context, no direct verb.\",\n        \"Contradictory action definitions in same scene.\"\n      ],\n      \"stakeholder_views\": [\n        \"Storyboard artists rely on detailed action cues.\",\n        \"Animators and motion designers need granular action timelines.\"\n      ],\n      \"risk_landscape\": [\n        \"Missed or misread actions degrade scene energy.\",\n        \"Contradictory cues can cause discontinuities in output.\"\n      ]\n    },\n    {\n      \"task\": \"For each identified scene, extract spatial attributes.\",\n      \"cross_domain_lens\": [\n        \"Architecture and game design depend on spatial logic for immersion.\",\n        \"Spatial linguistics parses relative position through prepositions/directions.\"\n      ],\n      \"edge_cases\": [\n        \"Scenes with non-Euclidean or fantastical space.\",\n        \"User input omits or conflicts on spatial detail.\"\n      ],\n      \"stakeholder_views\": [\n        \"Set designers and VFX artists require explicit space to plan visuals.\",\n        \"AI-driven compositors need location information for scene assembly.\"\n      ],\n      \"risk_landscape\": [\n        \"Missing or incorrect spatial extraction leads to illogical scenes.\",\n        \"Conflicting placements can fail scene synthesis.\"\n      ]\n    },\n    {\n      \"task\": \"For each identified scene, extract scene-specific audio cues.\",\n      \"cross_domain_lens\": [\n        \"Sound design connects events to cues and motifs.\",\n        \"Cinematic grammar pairs diegetic/nondiegetic audio with narrative moments.\"\n      ],\n      \"edge_cases\": [\n        \"Audio cues missing, sparingly referenced, or heavily layered.\",\n        \"Audio cues described only through mood, lacking specifics.\"\n      ],\n      \"stakeholder_views\": [\n        \"Audio engineers seek explicit cueing for mixing.\",\n        \"Directors balance textual description against sound selection.\"\n      ],\n      \"risk_landscape\": [\n        \"Neglecting cues reduces audio richness.\",\n        \"Ambiguous cues risk mismatched or jarring effects.\"\n      ]\n    },\n    {\n      \"task\": \"Annotate each scene with a unique scene_id.\",\n      \"cross_domain_lens\": [\n        \"Database design sees primary keys as essential for traceability.\",\n        \"Film post-production tracks scenes via identifiers for editing alignment.\"\n      ],\n      \"edge_cases\": [\n        \"Duplicate IDs due to parallel scene generation.\",\n        \"Scene-split reorganization invalidates prior IDs.\"\n      ],\n      \"stakeholder_views\": [\n        \"Technical teams rely on IDs for reference integrity.\",\n        \"Editors use IDs for scene-level comment and revision.\"\n      ],\n      \"risk_landscape\": [\n        \"Mismanaged IDs disrupt mapping across timelines.\",\n        \"Unlabeled scenes risk loss in output assembly.\"\n      ]\n    },\n    {\n      \"task\": \"Annotate each scene with temporal offset and duration.\",\n      \"cross_domain_lens\": [\n        \"Time-based media encoding depends on timestamp precision.\",\n        \"Music production overlays event timing with millisecond-level cues.\"\n      ],\n      \"edge_cases\": [\n        \"Overlapping or negative offsets from misparsed input.\",\n        \"Undefined durations, especially with implicit fade/transitions.\"\n      ],\n      \"stakeholder_views\": [\n        \"Compositing requires accurate timing for render queues.\",\n        \"Audio-visual sync depends on exact offsets.\"\n      ],\n      \"risk_landscape\": [\n        \"Drift in temporal annotation creates synch errors.\",\n        \"Incorrect durations can truncate or extend scenes disproportionally.\"\n      ]\n    },\n    {\n      \"task\": \"Assign camera viewpoint to each scene.\",\n      \"cross_domain_lens\": [\n        \"Cinematography theory: camera placement influences emotion and story.\",\n        \"In VR production, viewpoint signals user immersion domain.\"\n      ],\n      \"edge_cases\": [\n        \"Ambiguous or shifting viewpoints inside scene.\",\n        \"Contradictory cues (first vs third person in narration).\"\n      ],\n      \"stakeholder_views\": [\n        \"Directors specify viewpoints for desired emotional effect.\",\n        \"Engineers apply viewpoint to rendering matrix.\"\n      ],\n      \"risk_landscape\": [\n        \"Poor viewpoint assignment lessens narrative impact.\",\n        \"Unresolved ambiguities can break visual coherence.\"\n      ]\n    },\n    {\n      \"task\": \"Assign camera motion to each scene.\",\n      \"cross_domain_lens\": [\n        \"Motion graphics bridges between kinetic effect and storytelling.\",\n        \"Robotics treats camera motion as trajectory planning.\"\n      ],\n      \"edge_cases\": [\n        \"Motion contradicts implied stillness or scene constraints.\",\n        \"Over-complex moves unachievable in target engine.\"\n      ],\n      \"stakeholder_views\": [\n        \"Operators prioritize real-world motion constraints.\",\n        \"Creative teams may demand physically ‘impossible’ moves for effect.\"\n      ],\n      \"risk_landscape\": [\n        \"Improper motion creates jarring or broken shots.\",\n        \"Failure to respect technical limits may crash engines or halt automation.\"\n      ]\n    },\n    {\n      \"task\": \"Assign camera lens/framing to each scene.\",\n      \"cross_domain_lens\": [\n        \"Photography relies on lens/framing for compositional meaning.\",\n        \"Film studies analyze framing as narrative strategy.\"\n      ],\n      \"edge_cases\": [\n        \"User input ambiguous between focal length and frame crop.\",\n        \"Frames incompatible with aspect ratio/scene setting.\"\n      ],\n      \"stakeholder_views\": [\n        \"DPs/DoPs have strong perspectives on lens choice.\",\n        \"Animation pipelines require precise numeric attributes.\"\n      ],\n      \"risk_landscape\": [\n        \"Inaccurate framing/lens harms subject prominence.\",\n        \"Conflicting parameters can break downstream comp.\"\n      ]\n    },\n    {\n      \"task\": \"Compile actor and object motion metadata for each scene.\",\n      \"cross_domain_lens\": [\n        \"In biomechanics, object motion studied for realism.\",\n        \"Crowd simulation maps metadata to individual tracks.\"\n      ],\n      \"edge_cases\": [\n        \"Simultaneous, contradictory motion paths.\",\n        \"Object/actor motion missing or ambiguous.\"\n      ],\n      \"stakeholder_views\": [\n        \"VFX and mocap teams depend on precise motion tracks.\",\n        \"Animators need differentiate passive/background from active/principals.\"\n      ],\n      \"risk_landscape\": [\n        \"Bad metadata causes animation drift and stutter.\",\n        \"Ambiguity creates synchronization bugs between actor and set.\"\n      ]\n    },\n    {\n      \"task\": \"Compile camera movement metadata for each scene.\",\n      \"cross_domain_lens\": [\n        \"Robotics uses metadata for virtual camera pathing.\",\n        \"Motion capture relies on movement primitives.\"\n      ],\n      \"edge_cases\": [\n        \"Complex movement unresolved by model/engine.\",\n        \"Gaps or overlaps in metadata across scene boundaries.\"\n      ],\n      \"stakeholder_views\": [\n        \"Pipeline automations require metadata for planning.\",\n        \"Directors expect movement metadata for shot consistency.\"\n      ],\n      \"risk_landscape\": [\n        \"Metadata misalignment de-syncs shot execution.\",\n        \"Inconsistent movement is visually jarring.\"\n      ]\n    },\n    {\n      \"task\": \"Compile speed and direction of all moving elements for each scene.\",\n      \"cross_domain_lens\": [\n        \"Physics simulation frameworks demand numeric precision.\",\n        \"Animation keyframe design requires directionality cues.\"\n      ],\n      \"edge_cases\": [\n        \"Ambiguous or contradictory direction cues.\",\n        \"Unrealistically rapid or static movement requests.\"\n      ],\n      \"stakeholder_views\": [\n        \"Animators must match speed/direction for continuity.\",\n        \"Technicians will stress test for realism and engine limits.\"\n      ],\n      \"risk_landscape\": [\n        \"Failed speed/direction cues cause continuity breaks.\",\n        \"Overly complex requests could overload simulation.\"\n      ]\n    },\n    {\n      \"task\": \"Detect transitions between scenes.\",\n      \"cross_domain_lens\": [\n        \"In film editing, transition types carry narrative function.\",\n        \"Video synthesis tools treat transitions as process nodes.\"\n      ],\n      \"edge_cases\": [\n        \"No explicit transition cues—must infer.\",\n        \"Implied transitions contradict downstream engine capability.\"\n      ],\n      \"stakeholder_views\": [\n        \"Editors value explicit transition logic.\",\n        \"Workflow engines need clear separation of logical scenes.\"\n      ],\n      \"risk_landscape\": [\n        \"Omitted transitions may feel jarring.\",\n        \"Unsupported transitions can cause pipeline failures.\"\n      ]\n    },\n    {\n      \"task\": \"Extract transition type, duration, and style for each detected transition.\",\n      \"cross_domain_lens\": [\n        \"UX animation design ties transition style to emotional effect.\",\n        \"Technical pipelines require standardized transition metadata.\"\n      ],\n      \"edge_cases\": [\n        \"Incomplete or ambiguous transition descriptors.\",\n        \"Style/duration unprocessable by certain engines.\"\n      ],\n      \"stakeholder_views\": [\n        \"Directors/Editors want narrative logic.\",\n        \"Pipeline engineers need actionable, granular info.\"\n      ],\n      \"risk_landscape\": [\n        \"Unclear type/style can halt automated processing.\",\n        \"Unmatched durations create rhythm issues.\"\n      ]\n    },\n    {\n      \"task\": \"If transition details unspecified, set default transition to crossfade.\",\n      \"cross_domain_lens\": [\n        \"Standardization: default to universally supported operation.\",\n        \"File format design uses fallback values for missing fields.\"\n      ],\n      \"edge_cases\": [\n        \"Default transition not supported by custom engines.\",\n        \"User intention opposes the default.\"\n      ],\n      \"stakeholder_views\": [\n        \"Engine maintainers expect defaulting for fail-safe.\",\n        \"Creative teams may dislike creative limitations.\"\n      ],\n      \"risk_landscape\": [\n        \"Inflexible defaults can stifle creative goals.\",\n        \"No default leads to abrupt processing failures.\"\n      ]\n    },\n    {\n      \"task\": \"Extract or interpolate global technical parameters: resolution.\",\n      \"cross_domain_lens\": [\n        \"Compression science prioritizes resolution for bandwidth control.\",\n        \"Display tech needs resolution matching for device compatibility.\"\n      ],\n      \"edge_cases\": [\n        \"Conflicting scene-specific resolution within one timeline.\",\n        \"Illegal or unsupported output resolutions.\"\n      ],\n      \"stakeholder_views\": [\n        \"Distributors and broadcasters set hard resolution specs.\",\n        \"Content creators care about appearance on end-user devices.\"\n      ],\n      \"risk_landscape\": [\n        \"Resolution misfit degrades quality or increases cost.\",\n        \"Unsupported sizes cause render failures.\"\n      ]\n    },\n    {\n      \"task\": \"Extract or interpolate global technical parameters: frame_rate.\",\n      \"cross_domain_lens\": [\n        \"Standards compliance (broadcast, web) requires strict frame rates.\",\n        \"Game engines tightly couple frame rate to simulation logic.\"\n      ],\n      \"edge_cases\": [\n        \"Mismatch in scene vs global frame rates.\",\n        \"Non-integer, variable, or undefined rates.\"\n      ],\n      \"stakeholder_views\": [\n        \"Engineers need frame rate consistency for toolchains.\",\n        \"Producers consider costs of high/low frame rates.\"\n      ],\n      \"risk_landscape\": [\n        \"Frame rate drift risks sync and stutter.\",\n        \"Non-compliant rates break downstream compatibility.\"\n      ]\n    },\n    {\n      \"task\": \"Extract or interpolate global technical parameters: color_palette.\",\n      \"cross_domain_lens\": [\n        \"Brand design locks palette to identity standards.\",\n        \"Color science links palette to human perception and mood.\"\n      ],\n      \"edge_cases\": [\n        \"Ambiguous or conflicting color cues.\",\n        \"Color schemes not supported in target engines.\"\n      ],\n      \"stakeholder_views\": [\n        \"Brand guardians check palette adherence.\",\n        \"Lighting/VFX need palette for setup and correction.\"\n      ],\n      \"risk_landscape\": [\n        \"Palette misassignment undermines mood or brand.\",\n        \"Unsupported palettes yield color inaccuracies or engine warnings.\"\n      ]\n    },\n    {\n      \"task\": \"Extract or interpolate global technical parameters: style.\",\n      \"cross_domain_lens\": [\n        \"Art history defines style contextually over time.\",\n        \"Generative models map style to quantitative tokens.\"\n      ],\n      \"edge_cases\": [\n        \"Style conflict: e.g., film noir mixed with saturated pop art.\",\n        \"Nonexistent style mapping in available engine.\"\n      ],\n      \"stakeholder_views\": [\n        \"Directors often enforce coherent style throughout.\",\n        \"Technical teams need explicit mappings for automation.\"\n      ],\n      \"risk_landscape\": [\n        \"Style incoherence reduces narrative power.\",\n        \"Silent fallback can degrade overall output.\"\n      ]\n    },\n    {\n      \"task\": \"Extract or interpolate global technical parameters: aspect_ratio.\",\n      \"cross_domain_lens\": [\n        \"Media distribution channels restrict aspect ratios.\",\n        \"Display hardware requires matching aspect to avoid letterboxing.\"\n      ],\n      \"edge_cases\": [\n        \"Conflicting ratios between scenes or shots.\",\n        \"Unprocessible or nonstandard aspect ratios.\"\n      ],\n      \"stakeholder_views\": [\n        \"Compliance teams enforce platform requirements.\",\n        \"Cinematographers may request aspect-ratio-specific framing.\"\n      ],\n      \"risk_landscape\": [\n        \"Aspect mismatches break composition or delivery pipeline.\",\n        \"Odd ratios create platform rejection or visual artifacts.\"\n      ]\n    },\n    {\n      \"task\": \"Extract or interpolate global technical parameters: pacing.\",\n      \"cross_domain_lens\": [\n        \"Directing equates pacing to rhythm/emotion.\",\n        \"Video processing algorithms rationalize pacing for user attention.\"\n      ],\n      \"edge_cases\": [\n        \"Pacing signals are implicit only.\",\n        \"Scene-by-scene variations break overall pacing flow.\"\n      ],\n      \"stakeholder_views\": [\n        \"Editors optimize for intended audience engagement.\",\n        \"Engineers need quantifiable pacing metrics.\"\n      ],\n      \"risk_landscape\": [\n        \"Poor pacing ruins narrative arc.\",\n        \"Pacing ambiguity hinders automation.\"\n      ]\n    },\n    {\n      \"task\": \"Extract per-scene technical parameters where specified.\",\n      \"cross_domain_lens\": [\n        \"Precision manufacturing highlights per-unit parameterization.\",\n        \"ML pipelines process scene-level hyperparameters.\"\n      ],\n      \"edge_cases\": [\n        \"Per-scene overrides in conflict with global.\",\n        \"Missing scene overrides create ambiguity.\"\n      ],\n      \"stakeholder_views\": [\n        \"Directors sometimes demand scene exceptions.\",\n        \"Renderers prefer global defaults for efficiency.\"\n      ],\n      \"risk_landscape\": [\n        \"Parameter conflict creates hard-to-debug errors.\",\n        \"Missing specifics can result in underwhelming scene presentation.\"\n      ]\n    },\n    {\n      \"task\": \"Identify global audio elements (soundtrack, sfx, voice, silence).\",\n      \"cross_domain_lens\": [\n        \"Game audio pipelines layer global and local effects.\",\n        \"Accessibility standards require explicit audio mapping.\"\n      ],\n      \"edge_cases\": [\n        \"Conflicting soundtrack instructions.\",\n        \"Nonexistent audio cues in user input.\"\n      ],\n      \"stakeholder_views\": [\n        \"Audio teams need up-front knowledge of global cues.\",\n        \"Localization teams need explicit voice mapping.\"\n      ],\n      \"risk_landscape\": [\n        \"Clashing elements can mask important cues.\",\n        \"Missing cues may lead to unimpressive, silent output.\"\n      ]\n    },\n    {\n      \"task\": \"Identify scene-specific audio elements.\",\n      \"cross_domain_lens\": [\n        \"Film scoring adapts per-scene mood.\",\n        \"Sound design differentiates background vs. focal cues.\"\n      ],\n      \"edge_cases\": [\n        \"Ambiguous assignment between global/scene audio.\",\n        \"Contradictory or overlaid scene cues.\"\n      ],\n      \"stakeholder_views\": [\n        \"Directors tailor scene mood/energy via distinct cues.\",\n        \"Audio mixers split by track for editing efficiency.\"\n      ],\n      \"risk_landscape\": [\n        \"Audio confusion can muddle key narrative beats.\",\n        \"Overlapping cues may not be properly rendered.\"\n      ]\n    },\n    {\n      \"task\": \"Annotate timing, loop, and fade behavior for all audio cues.\",\n      \"cross_domain_lens\": [\n        \"Music editors rely on automation lanes for fades/loops.\",\n        \"Interactive media demands explicit cue timing.\"\n      ],\n      \"edge_cases\": [\n        \"Fades/loops omitted, or cues contradicting duration.\",\n        \"Circular reference in loop definitions.\"\n      ],\n      \"stakeholder_views\": [\n        \"Audio implementers require complete cue timing.\",\n        \"Producers may want generative fade logic.\"\n      ],\n      \"risk_landscape\": [\n        \"Mismatched cue/fade leads to jarring cuts.\",\n        \"Loops unbound may cause audio cycling bugs.\"\n      ]\n    },\n    {\n      \"task\": \"Arrange scenes, transitions, and audio in a temporally ordered timeline structure.\",\n      \"cross_domain_lens\": [\n        \"Video editors use timeline-based NLEs.\",\n        \"Project management applies Gantt logic to time-delimited blocks.\"\n      ],\n      \"edge_cases\": [\n        \"Temporal ambiguity in scene start/end.\",\n        \"Automatic ordering collides with user intent.\"\n      ],\n      \"stakeholder_views\": [\n        \"Showrunners and composers plan structure top-down.\",\n        \"Computational renderers need efficient, unambiguous sequencing.\"\n      ],\n      \"risk_landscape\": [\n        \"Poor ordering breaks narrative and technical expectations.\",\n        \"Timeline collision can corrupt output.\"\n      ]\n    },\n    {\n      \"task\": \"Audit timeline for overlap/conflict between scenes.\",\n      \"cross_domain_lens\": [\n        \"QA involves overlap checks in multimedia authoring tools.\",\n        \"Data pipeline integrity requires windowing validation.\"\n      ],\n      \"edge_cases\": [\n        \"Intentional overlaps (picture-in-picture/parallel action).\",\n        \"Accidental overlaps due to timing math errors.\"\n      ],\n      \"stakeholder_views\": [\n        \"Post-production expects auditing for safe delivery.\",\n        \"Performance engineers want early conflict detection.\"\n      ],\n      \"risk_landscape\": [\n        \"Overlap errors may halt processing or create visual confusion.\",\n        \"Genuine overlaps mis-flagged as errors reduce creative options.\"\n      ]\n    },\n    {\n      \"task\": \"Audit timeline for temporal gaps between scenes.\",\n      \"cross_domain_lens\": [\n        \"Temporal gap detection critical in continuity management.\",\n        \"Event-driven architectures treat idle gaps as inefficiency.\"\n      ],\n      \"edge_cases\": [\n        \"Gaps are intentional (for suspense, blackout) but flagged.\",\n        \"Gap computation fails for complex, overlapping scene logic.\"\n      ],\n      \"stakeholder_views\": [\n        \"Directors desire control over timing lulls.\",\n        \"Automation needs clean handoff with no dead space.\"\n      ],\n      \"risk_landscape\": [\n        \"Gaps may break engagement or delivery requirements.\",\n        \"False-positive gap detection disrupts creative intent.\"\n      ]\n    },\n    {\n      \"task\": \"Audit transitions for abrupt jumps and flicker/jump-cut risk.\",\n      \"cross_domain_lens\": [\n        \"Film editing is fundamentally about smooth transitions.\",\n        \"Video analysis apps flag flicker visually.\"\n      ],\n      \"edge_cases\": [\n        \"Abrupt edits intentional for style (e.g., music videos).\",\n        \"Lighting or color shift mistaken for flicker.\"\n      ],\n      \"stakeholder_views\": [\n        \"Editors gauge naturalism vs. intentional disruption.\",\n        \"QA wants universal flicker mitigation.\"\n      ],\n      \"risk_landscape\": [\n        \"Jump-cuts reduce professionalism and fluidity.\",\n        \"Overaggressive mitigation deters fresh visual language.\"\n      ]\n    },\n    {\n      \"task\": \"Audit audio cues for unsynchronized alignment with timeline.\",\n      \"cross_domain_lens\": [\n        \"ADR (Automated Dialog Replacement) pipelines live or die on audio sync.\",\n        \"Music video production relies on exact beat alignment.\"\n      ],\n      \"edge_cases\": [\n        \"Intentional asynchrony for stylistic dissonance.\",\n        \"Cues drifting due to recursive scene timing edits.\"\n      ],\n      \"stakeholder_views\": [\n        \"Music supervisors enforce timing precision.\",\n        \"Directors may favor stylized, intentional disalignments.\"\n      ],\n      \"risk_landscape\": [\n        \"Audio misalignment disrupts immersion.\",\n        \"Overcorrection erodes stylized choices.\"\n      ]\n    },\n    {\n      \"task\": \"Abort and output explicit abort_code if any hard constraint is violated (e.g. duration limit, unsupported transition, invalid parameter, token/character/weight overflow).\",\n      \"cross_domain_lens\": [\n        \"Safety-critical software calls for fail-fast error signaling.\",\n        \"Command-line scripting benefits from explicit exit codes.\"\n      ],\n      \"edge_cases\": [\n        \"User requests intentionally violate constraints to explore system limits.\",\n        \"Chain of conflicting violations makes abort_code selection nontrivial.\"\n      ],\n      \"stakeholder_views\": [\n        \"Ops teams require actionable abort codes for rapid triage.\",\n        \"Users need clear feedback for request correction.\"\n      ],\n      \"risk_landscape\": [\n        \"Inconsistent abort logic frustrates debugging.\",\n        \"Silent failures lead to loss of user trust.\"\n      ]\n    },\n    {\n      \"task\": \"Generate warnings for soft issues (e.g. ambiguous timing, excessive complexity, unsupported effects on target platform).\",\n      \"cross_domain_lens\": [\n        \"Compiler warnings serve as best-practice analog.\",\n        \"Pro editor suites surface soft errors for review.\"\n      ],\n      \"edge_cases\": [\n        \"Multiple warnings accumulate, causing signal fatigue.\",\n        \"Warnings escalate to hard errors due to ignored issues.\"\n      ],\n      \"stakeholder_views\": [\n        \"QA teams prioritize warning clarity.\",\n        \"Creative leads may want warnings suppressed or contextualized.\"\n      ],\n      \"risk_landscape\": [\n        \"Excess/muddled warnings erode actionable value.\",\n        \"Suppressed warnings risk non-obvious downstream problems.\"\n      ]\n    },\n    {\n      \"task\": \"Assess scene-to-scene visual continuity.\",\n      \"cross_domain_lens\": [\n        \"Continuity editing ensures narrative fluidity.\",\n        \"Deep learning models look for feature vector coherence.\"\n      ],\n      \"edge_cases\": [\n        \"Intentional visual disruption (e.g., match cuts).\",\n        \"Subtle style shifts (e.g., color cast) span scenes.\"\n      ],\n      \"stakeholder_views\": [\n        \"Directors balance continuity vs. distinct scenes for story.\",\n        \"QA expects automatic flagging of continuity breaks.\"\n      ],\n      \"risk_landscape\": [\n        \"Continuity breaks confuse viewers.\",\n        \"Rigid enforcement can stifle creative jump cuts.\"\n      ]\n    },\n    {\n      \"task\": \"Assess audio continuity across scenes.\",\n      \"cross_domain_lens\": [\n        \"Radio dramas depend on seamless sonic world-building.\",\n        \"ML-driven podcasts blend audio for seamless transitions.\"\n      ],\n      \"edge_cases\": [\n        \"Hard audio cut required for narrative shock.\",\n        \"Environmental effects shift appropriately between scenes.\"\n      ],\n      \"stakeholder_views\": [\n        \"Audio leads value both seamlessness and dynamic range.\",\n        \"Editors seek flagged discontinuities for manual override.\"\n      ],\n      \"risk_landscape\": [\n        \"Poor continuity distracts or jars viewers.\",\n        \"Overblending can remove intended dynamic accents.\"\n      ]\n    },\n    {\n      \"task\": \"Assess style coherence across all scenes.\",\n      \"cross_domain_lens\": [\n        \"Series production enforces style bibles.\",\n        \"Automated art pipelines use global tokens for consistency.\"\n      ],\n      \"edge_cases\": [\n        \"Deliberate style shift for specific narrative beats.\",\n        \"Imported assets violate style unintentionally.\"\n      ],\n      \"stakeholder_views\": [\n        \"Producers want overall consistency.\",\n        \"Creative teams may break coherence for story effects.\"\n      ],\n      \"risk_landscape\": [\n        \"Style incoherence undermines professional polish.\",\n        \"Rigid enforcement can prevent intentional palette shifts.\"\n      ]\n    },\n    {\n      \"task\": \"Assess cross-frame artifact minimization.\",\n      \"cross_domain_lens\": [\n        \"Video compression aims to reduce cross-frame error.\",\n        \"Animation polish targets temporal artifact smoothing.\"\n      ],\n      \"edge_cases\": [\n        \"Rapid action scenes that sacrifice artifact reduction for impact.\",\n        \"Engine limitations lead to unavoidable artifacts.\"\n      ],\n      \"stakeholder_views\": [\n        \"Post-production seeks flagging for expensive clean-up.\",\n        \"Software teams prioritize artifact prediction tools.\"\n      ],\n      \"risk_landscape\": [\n        \"Artifacts reduce visual credibility.\",\n        \"Excessive minimization can blur critical detail.\"\n      ]\n    },\n    {\n      \"task\": \"Validate parameter order for output.\",\n      \"cross_domain_lens\": [\n        \"APIs require contract order for backward compatibility.\",\n        \"Config file parsing is order-sensitive in many systems.\"\n      ],\n      \"edge_cases\": [\n        \"Legacy systems require different parameter orders.\",\n        \"User input tries to override strict ordering.\"\n      ],\n      \"stakeholder_views\": [\n        \"Engineers demand order for deterministic builds.\",\n        \"QA cross-checks output schema compliance.\"\n      ],\n      \"risk_landscape\": [\n        \"Parameter disorder creates parsing errors.\",\n        \"Unclear order exposes risk in agile development.\"\n      ]\n    },\n    {\n      \"task\": \"Validate temporal alignment for all elements.\",\n      \"cross_domain_lens\": [\n        \"Live broadcast pipelines depend on timeline locks.\",\n        \"Multimedia API calls must synchronize media tracks for playback.\"\n      ],\n      \"edge_cases\": [\n        \"Microsecond drift across long timelines.\",\n        \"Engine limits prevent perfect sync.\"\n      ],\n      \"stakeholder_views\": [\n        \"Ops teams require audit trails for sync failures.\",\n        \"End-users notice even small disalignments.\"\n      ],\n      \"risk_landscape\": [\n        \"Bad alignment disrupts experience.\",\n        \"Silent sync loss leads to subtle bugs.\"\n      ]\n    },\n    {\n      \"task\": \"Ensure syntactic and semantic validity of output schema for downstream engines.\",\n      \"cross_domain_lens\": [\n        \"Data serialization (JSON/XML) requires strong schema validation.\",\n        \"ML pipelines fail on schema mismatches or ambiguities.\"\n      ],\n      \"edge_cases\": [\n        \"Target engines update schema requirements post-deployment.\",\n        \"Semantic meaning is lost in translation to protocol.\"\n      ],\n      \"stakeholder_views\": [\n        \"Integration teams expect robust machine-readability.\",\n        \"Compliance officers demand clear schema conformance.\"\n      ],\n      \"risk_landscape\": [\n        \"Schema misfit blocks automation and upload.\",\n        \"Semantic drift risks loss of production intent.\"\n      ]\n    },\n    {\n      \"task\": \"Enforce deterministic output by lock_seed.\",\n      \"cross_domain_lens\": [\n        \"Scientific computing uses random seed-lock for replication.\",\n        \"Gaming relies on seeds for reproducible procedural content.\"\n      ],\n      \"edge_cases\": [\n        \"Lock fails in parallel/batch environment.\",\n        \"Seed conflict across user sessions.\"\n      ],\n      \"stakeholder_views\": [\n        \"Researchers require reproducibility.\",\n        \"Producers need deterministic previews for signoff.\"\n      ],\n      \"risk_landscape\": [\n        \"Non-determinism frustrates sign-off workflows.\",\n        \"Colliding seeds disrupt individualized outputs.\"\n      ]\n    },\n    {\n      \"task\": \"Enforce strict parameter ordering for determinism.\",\n      \"cross_domain_lens\": [\n        \"Configuration management systems require order for diffs.\",\n        \"Legal documents enforce clause order for intent.\"\n      ],\n      \"edge_cases\": [\n        \"Input generated dynamically, order not guaranteed.\",\n        \"Human editors override automation breaking order.\"\n      ],\n      \"stakeholder_views\": [\n        \"Developers depend on reproducible builds.\",\n        \"Analysts expect order for data curation.\"\n      ],\n      \"risk_landscape\": [\n        \"Order drift causes hard-to-debug changes.\",\n        \"Pipeline failures traceable to ordering inconsistencies.\"\n      ]\n    },\n    {\n      \"task\": \"Build final structured output: array of scenes with metadata, ordered transitions, audio tracks per scene/timeline, global parameters, validation logs, warnings, and abort_code if any.\",\n      \"cross_domain_lens\": [\n        \"Enterprise data pipelines collate recordsets for audit.\",\n        \"Film online editing generates EDLs (edit decision lists).\"\n      ],\n      \"edge_cases\": [\n        \"Output size exceeds downstream limits.\",\n        \"Conflicting metadata between scenes in array.\"\n      ],\n      \"stakeholder_views\": [\n        \"QA expects structured logs for traceability.\",\n        \"Operators require self-validating output for automation.\"\n      ],\n      \"risk_landscape\": [\n        \"Broken output structure trips processing engines.\",\n        \"Ambiguous array order undermines post-processing.\"\n      ]\n    },\n    {\n      \"task\": \"Return output in explicit, engine-consumable schema (array/dictionary/fields) for machine parsing and production use.\",\n      \"cross_domain_lens\": [\n        \"API spec design demands contractually clear output.\",\n        \"Production databases ingest only predictable fielded data.\"\n      ],\n      \"edge_cases\": [\n        \"Target engines introduce new required fields unannounced.\",\n        \"User preferences contradict output spec.\"\n      ],\n      \"stakeholder_views\": [\n        \"Integration dev teams expect plug-and-play output.\",\n        \"Automation sees schema clarity as key for reliability.\"\n      ],\n      \"risk_landscape\": [\n        \"Schema drift blocks production.\",\n        \"Non-explicit schemas lead to brittle integrations.\"\n      ]\n    }\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Essence Distiller\n\nYour goal is not to **praise** or **answer** the submission, but to **interrogate and redirect** it—exposing every structural flaw, quantifying compliance with the Universal Directive System, and issuing a minimal replacement instruction that corrects all defects. Execute as:\n\n`{role=essence_distiller; input=[atomic_tasks:list, task_perspectives:list, execution_blockers:array]; process=[rank_tasks_by_transformational_weight(), select_high_impact_task(), compile_supporting_rationale(), justify_selection_transparently()]; constraints=[no_language_rewrite_of_selected_task(), transparent_ranking()]; requirements=[clear_primary_directive(), rationale_precision()]; output={primary_directive:str, supporting_rationale:list}}`",
      "step": "e",
      "title": "Essence Distiller",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"REWRITE THIS PROMPT TO BE AN EXPERT VIDEO GENERATOR:\\n\\n{\\n\\n    # 1703:\\n    \\\"1703-a-image_prompt_synthesizer\\\": {\\n        \\\"title\\\": \\\"Image Prompt Synthesizer\\\",\\n        \\\"interpretation\\\": \\\"Your goal is **not** to invent new ideas, but to deterministically convert any free‑form user request into a single, engine‑ready image prompt usable by Stable Diffusion and Midjourney. Perform extraction, styling, technical framing, and final validation in **one pass**.\\\",\\n        \\\"transformation\\\": \\\"`{role=image_prompt_synthesizer; input=[raw_request:str]; process=[extract_visual_elements(), push_concrete_nouns_front(), apply_token_weighting(), compose_multiprompt(::), generate_region_tags(), inject_negative_channel(--no), select_art_style(), weight_style_token(≤1.4), set_colour_palette(), add_lighting_descriptor(), tune_stylize(0‑1000), tune_chaos(0‑100), embed_camera_EXIF(35 mm f/1.8 ISO100 1/500 s), add_quality_terms(8K,PBR,ray‑tracing), refine_aspect_ratio(), attach_image_weight(--iw 0‑3), mirror_negative_channel(), concatenate_all(), validate_parentheses(), check_double_colon_sum(), confirm_region_syntax(), enforce_token_limit(≤450), lock_seed(), append_steps_sampler(), output_final()], constraints=[positives_first(), numeric_weights_ok(), parameters_last(), region_masks_supported(), keep_core_subject(), style_weight≤1.4, no_scene_change(), aperture_f1.2‑f22(), no_new_concepts(), preserve_weights()], requirements=[≤77_SD_tokens_or_≤450_chars_UI, explicit_ar_if_named(), clear_visual_language(), artistic_coherence(), stylize_range_0‑1000(), chaos_range_0‑100(), professional_terminology(), reproducible_prompt(), unambiguous()], output={final_image_prompt:str}}`\\\",\\n        \\\"context\\\": {\\n            \\\"principles\\\": {\\n                \\\"determinism\\\": \\\"Identical input must always yield the same prompt (seed‑locked).\\\",\\n                \\\"layered_control\\\": \\\"Separate positive, region, and negative semantics before concatenation; merge only at final step.\\\",\\n                \\\"minimal_overhead\\\": \\\"All weighting, style, and camera parameters trail the prose to satisfy engine syntax.\\\"\\n            },\\n            \\\"success_criteria\\\": {\\n                \\\"syntactic_validity\\\": \\\"Parentheses, double colon maths, and parameter order pass model parsers.\\\",\\n                \\\"token_compliance\\\": \\\"Prompt length respects the stricter of SD‑77‑token or 450‑char UI limits.\\\",\\n                \\\"subject_fidelity\\\": \\\"Primary subject weight ≥ 1.2; background ≤ 1.0; style weight ≤ 1.4.\\\",\\n                \\\"technical_readiness\\\": \\\"Aspect ratio, seed, sampler, steps, stylize, chaos, quality flags, and optional --iw are present and correctly positioned.\\\",\\n                \\\"clarity\\\": \\\"Language is concrete, jargon‑free, and region masks (if any) use `[tag] … |` notation.\\\"\\n            }\\n        }\\n    },\\n    \\\"1703-b-premium_image_prompt_synthesizer\\\": {\\n        \\\"title\\\": \\\"Premium Image Prompt Synthesizer\\\",\\n        \\\"interpretation\\\": \\\"Convert any free‑form request into a deterministic, model‑ready prompt that meets *premium* visual‑fidelity standards for Stable Diffusion & Midjourney **in a single call** while retaining full error‑handling and quality gates.\\\",\\n        \\\"transformation\\\": \\\"`{role=premium_image_prompt_synthesizer; input=[raw_request:str]; process=[\\\\n  /* — PHASE 1 : Context & Safety — */\\\\n  extract_visual_elements(),\\\\n  detect_conflicting_aspect_ratio(),                       abort_if_conflict(),\\\\n  enforce_token_budget(≤77_SD_tokens_or_≤450_chars_UI),    abort_if_exceeded(),\\\\n  /* — PHASE 2 : Positive Channel Build — */\\\\n  push_concrete_nouns_front(),\\\\n  apply_token_weighting(primary≥1.2,background≤1.0),       abort_if_total_weight≤0,\\\\n  compose_multiprompt(::),\\\\n  generate_region_tags_if_scene_implied(),                 note_mobile_mask_warning(),\\\\n  /* — PHASE 3 : Style & Aesthetics — */\\\\n  select_art_style(),\\\\n  reject_style_if_conflicts_camera_realism(),\\\\n  weight_style_token(≤1.4),\\\\n  set_colour_palette(harmonious),\\\\n  add_lighting_descriptor(coherent_with_palette),\\\\n  tune_stylize(bounds=0‑1000),                             abort_if_out_of_bounds(),\\\\n  tune_chaos(bounds=0‑100),                                abort_if_out_of_bounds(),\\\\n  /* — PHASE 4 : Technical & Camera — */\\\\n  embed_camera_EXIF(\\\\\\\"35 mm f/1.8 ISO100 1/500 s\\\\\\\"),\\\\n  verify_aperture_range(f1.2‑f22),                         abort_if_invalid(),\\\\n  add_quality_terms(8K,PBR,ray‑tracing),\\\\n  refine_aspect_ratio(),\\\\n  attach_image_weight(--iw 0‑3_if_URL_present),            abort_if_IW_out_of_bounds(),\\\\n  /* — PHASE 5 : Negative Channel & Mirroring — */\\\\n  collect_user_negatives(),\\\\n  append_builtin_negatives(overblur,lowres,distracting_text),\\\\n  validate_negative_strength(avoids_colour_washout),\\\\n  /* — PHASE 6 : Final Assembly & Validation — */\\\\n  concatenate_positive_negative(),                         parameters_trail_prose(),\\\\n  validate_parentheses_balance(),\\\\n  check_double_colon_weight_sum(),                         abort_if_weight_sum_zero(),\\\\n  confirm_region_syntax(),\\\\n  lock_seed_and_append_sampler_steps(),\\\\n  output_final_prompt()\\\\n],\\\\nconstraints=[\\\\n  positives_first(), parameters_last(), region_masks_supported(),\\\\n  no_new_concepts(), preserve_weights(), style_weight≤1.4,\\\\n  professional_terminology(), explicit_ar_if_named()\\\\n],\\\\nrequirements=[\\\\n  premium_visual_fidelity(), reproducible(), clear_visual_language(),\\\\n  quality_gate(FID<15_or_equivalent), atomic_fail_fast_on_violation()\\\\n],\\\\noutput={final_image_prompt:str, warnings:list, abort_code:int|null}}`\\\",\\n        \\\"context\\\": {\\n            \\\"principles\\\": {\\n                \\\"fail_fast\\\": \\\"Abort with explicit code when any hard constraint is violated to prevent wasted renders.\\\",\\n                \\\"surface_blockers\\\": \\\"Return `warnings` array for soft issues (e.g. region masks on mobile).\\\",\\n                \\\"premium_fidelity\\\": \\\"Always target ultra‑sharp, detail‑rich, production‑grade outputs.\\\"\\n            },\\n            \\\"success_criteria\\\": {\\n                \\\"syntactic_pass\\\": \\\"Prompt parses without error in both SD & MJ (parentheses, `::`, parameter order).\\\",\\n                \\\"visual_quality\\\": \\\"Meets or exceeds internal FID / LPIPS thresholds (or skips metric if unavailable).\\\",\\n                \\\"determinism\\\": \\\"Identical input + seed => identical output.\\\",\\n                \\\"safety\\\": \\\"No colour washout, no truncated tokens, no invalid weights, no parameter conflicts.\\\"\\n            },\\n            \\\"edge_case_handling\\\": {\\n                \\\"zero_weight_sum\\\": \\\"Hard abort (`abort_code` = 92).\\\",\\n                \\\"token_budget_overflow\\\": \\\"Hard abort (`abort_code` = 77).\\\",\\n                \\\"aspect_ratio_conflict\\\": \\\"Hard abort (`abort_code` = AR1).\\\",\\n                \\\"out_of_bounds_stylize_chaos\\\": \\\"Hard abort (`abort_code` = SC1).\\\",\\n                \\\"IW_without_URL\\\": \\\"Warning only; `--iw` dropped.\\\"\\n            }\\n        }\\n    }\\n\\n}\\\"```'\\n\\n\",\n  \"task_perspectives\": [\n    {\n      \"task\": \"Define expert_video_prompt_generator role.\",\n      \"cross_domain_lens\": [\n        \"From software architecture, treating the generator as an interface contract ensures dependability and reproducibility.\",\n        \"In content production workflows, explicit expert protocol signals trust, regulatory compliance, and production readiness.\"\n      ],\n      \"edge_cases\": [\n        \"Ambiguity over required expertise level for niche or experimental video styles.\",\n        \"Role may require adaptation when interfacing with domain-specific video engines or uncommon output formats.\"\n      ],\n      \"stakeholder_views\": [\n        \"Product leads value clear role boundaries for responsibility tracing.\",\n        \"End-users expect comprehensible role definitions correlating with final output style.\"\n      ],\n      \"risk_landscape\": [\n        \"Role definition that's too broad/ambiguous risks inconsistent outputs.\",\n        \"Overly narrow role risks limiting innovation or failing unexpected requests.\"\n      ]\n    },\n    {\n      \"task\": \"Receive raw_user_request as input.\",\n      \"cross_domain_lens\": [\n        \"A UX design view emphasizes the importance of accommodating informal language.\",\n        \"API design foregrounds robust parsing strategies for free-form user demands.\"\n      ],\n      \"edge_cases\": [\n        \"Non-linguistic, visual, or partial-input cases (e.g., reference images) lacking verbal description.\",\n        \"High-volume, conflicting, or repetitive requests in a batch processing context.\"\n      ],\n      \"stakeholder_views\": [\n        \"User-experience teams focus on minimal friction during request intake.\",\n        \"Engineers prioritize normalization before downstream parsing.\"\n      ],\n      \"risk_landscape\": [\n        \"Possible misinterpretation of vague or multi-purpose inputs.\",\n        \"Potential injection of harmful instructions if validation is weak.\"\n      ]\n    },\n    {\n      \"task\": \"Extract global intent from user input.\",\n      \"cross_domain_lens\": [\n        \"Information retrieval parallels effective query understanding.\",\n        \"Cognitive psychology frames this as reading for gist/summarization.\"\n      ],\n      \"edge_cases\": [\n        \"Conflicting intent phrases or simultaneous multiple goal expressions.\",\n        \"Completely implicit intent (user fails to state goal outright).\"\n      ],\n      \"stakeholder_views\": [\n        \"Creative directors need intent clarity for briefing automation.\",\n        \"Automated validators require explicit intent to apply constraints.\"\n      ],\n      \"risk_landscape\": [\n        \"Incorrect intent extraction can derail the entire production.\",\n        \"Nuanced or context-specific intent may be lost in minimal extraction.\"\n      ]\n    },\n    {\n      \"task\": \"Extract target visual style from user input.\",\n      \"cross_domain_lens\": [\n        \"In branding/marketing, style adherence is contractual.\",\n        \"ML model conditioning links style tokens to established model outputs.\"\n      ],\n      \"edge_cases\": [\n        \"User mixes incompatible styles, e.g., 'photo-realistic anime'.\",\n        \"The style intended is known only in highly specialized contexts.\"\n      ],\n      \"stakeholder_views\": [\n        \"Brand managers require style to match organizational guidelines.\",\n        \"Technical teams prioritize mapping styles to engine-supported attributes.\"\n      ],\n      \"risk_landscape\": [\n        \"Unresolved style ambiguity leads to unpredictable output.\",\n        \"Engine mapping limitations may silently degrade fidelity.\"\n      ]\n    },\n    {\n      \"task\": \"Extract desired output duration from user input.\",\n      \"cross_domain_lens\": [\n        \"Media scheduling sees duration as a hard constraint for broadcast.\",\n        \"Rendering optimization treats duration as core to production cost.\"\n      ],\n      \"edge_cases\": [\n        \"User omits duration or supplies an unfeasible/extreme value.\",\n        \"Duration specified in nonconventional units (beats/frames).\"\n      ],\n      \"stakeholder_views\": [\n        \"Production managers see overruns as budget threats.\",\n        \"Viewers expect consistency with provided spec.\"\n      ],\n      \"risk_landscape\": [\n        \"Duration ambiguity can cause failed outputs or truncated/zero-length videos.\",\n        \"Long durations exponentially increase processing costs.\"\n      ]\n    },\n    {\n      \"task\": \"Determine if user input has explicit scene/shot breakdown.\",\n      \"cross_domain_lens\": [\n        \"Literary adaptation involves parsing structured versus stream-of-consciousness descriptions.\",\n        \"Scriptwriting tools need to detect structural markers automatically.\"\n      ],\n      \"edge_cases\": [\n        \"User provides partial, inconsistent, or mismatched scene markers.\",\n        \"Multi-modal descriptions blending scene and shot indistinctly.\"\n      ],\n      \"stakeholder_views\": [\n        \"Editors require faithful translation of explicit breakdowns.\",\n        \"Engineers need to differentiate explicit from implicit to prevent mis-parsing.\"\n      ],\n      \"risk_landscape\": [\n        \"Missed explicit divisions can flatten intended complexity.\",\n        \"Improper detection can sever continuous action or create artificial jumps.\"\n      ]\n    },\n    {\n      \"task\": \"If scene/shot breakdown is explicit, segment accordingly.\",\n      \"cross_domain_lens\": [\n        \"Data science formalizes 'chunking' documents by signal flags.\",\n        \"Video editing requires precise segmentation for non-linear workflows.\"\n      ],\n      \"edge_cases\": [\n        \"Explicit segments overlap, conflict, or are non-contiguous.\",\n        \"Segment definitions shift mid-prompt (scene to shot).\"\n      ],\n      \"stakeholder_views\": [\n        \"Editors value fidelity to user intention.\",\n        \"Scripting engines need deterministic chunk boundary logic.\"\n      ],\n      \"risk_landscape\": [\n        \"Overlapping or missed segments disrupt timeline logic.\",\n        \"Improper segmentation cascades to downstream errors.\"\n      ]\n    },\n    {\n      \"task\": \"If not explicit, infer scene/shot splits using action/visual/temporal cues.\",\n      \"cross_domain_lens\": [\n        \"Natural language processing frames this as semantic boundary detection.\",\n        \"Film theory values cues from verbs/actions and visual tempo.\"\n      ],\n      \"edge_cases\": [\n        \"Ambiguous action cues (e.g., 'suddenly' with no subject).\",\n        \"Rapid transitions that elide boundaries (montages, jump-cuts).\"\n      ],\n      \"stakeholder_views\": [\n        \"Writers and directors expect inference of logical scene units.\",\n        \"Automated systems need reliability in inferred splits for machine parsing.\"\n      ],\n      \"risk_landscape\": [\n        \"Scene mis-inference leads to jarring transitions and missed action.\",\n        \"Boundary ambiguity increases with creative/poetic input styles.\"\n      ]\n    },\n    {\n      \"task\": \"For each identified scene, extract core visual elements.\",\n      \"cross_domain_lens\": [\n        \"Computer vision emphasizes object and setting tagging.\",\n        \"Information design sees core elements as minimalist representations.\"\n      ],\n      \"edge_cases\": [\n        \"Crowded scenes with equal visual weight across elements.\",\n        \"Minimalist/abstract scenes lacking concrete visual reference.\"\n      ],\n      \"stakeholder_views\": [\n        \"Art directors require adherence to visual primacy.\",\n        \"Animators depend on explicit element extraction for rigging/layout.\"\n      ],\n      \"risk_landscape\": [\n        \"Overlooking elements may distort intended imagery.\",\n        \"Misweighting visuals can affect model prompt fidelity.\"\n      ]\n    },\n    {\n      \"task\": \"For each identified scene, extract described or implied actions.\",\n      \"cross_domain_lens\": [\n        \"Behavioral modeling seeks action verbs as triggers.\",\n        \"Storyboarding treats action as temporal events for animatics.\"\n      ],\n      \"edge_cases\": [\n        \"Implicit action only via context, no direct verb.\",\n        \"Contradictory action definitions in same scene.\"\n      ],\n      \"stakeholder_views\": [\n        \"Storyboard artists rely on detailed action cues.\",\n        \"Animators and motion designers need granular action timelines.\"\n      ],\n      \"risk_landscape\": [\n        \"Missed or misread actions degrade scene energy.\",\n        \"Contradictory cues can cause discontinuities in output.\"\n      ]\n    },\n    {\n      \"task\": \"For each identified scene, extract spatial attributes.\",\n      \"cross_domain_lens\": [\n        \"Architecture and game design depend on spatial logic for immersion.\",\n        \"Spatial linguistics parses relative position through prepositions/directions.\"\n      ],\n      \"edge_cases\": [\n        \"Scenes with non-Euclidean or fantastical space.\",\n        \"User input omits or conflicts on spatial detail.\"\n      ],\n      \"stakeholder_views\": [\n        \"Set designers and VFX artists require explicit space to plan visuals.\",\n        \"AI-driven compositors need location information for scene assembly.\"\n      ],\n      \"risk_landscape\": [\n        \"Missing or incorrect spatial extraction leads to illogical scenes.\",\n        \"Conflicting placements can fail scene synthesis.\"\n      ]\n    },\n    {\n      \"task\": \"For each identified scene, extract scene-specific audio cues.\",\n      \"cross_domain_lens\": [\n        \"Sound design connects events to cues and motifs.\",\n        \"Cinematic grammar pairs diegetic/nondiegetic audio with narrative moments.\"\n      ],\n      \"edge_cases\": [\n        \"Audio cues missing, sparingly referenced, or heavily layered.\",\n        \"Audio cues described only through mood, lacking specifics.\"\n      ],\n      \"stakeholder_views\": [\n        \"Audio engineers seek explicit cueing for mixing.\",\n        \"Directors balance textual description against sound selection.\"\n      ],\n      \"risk_landscape\": [\n        \"Neglecting cues reduces audio richness.\",\n        \"Ambiguous cues risk mismatched or jarring effects.\"\n      ]\n    },\n    {\n      \"task\": \"Annotate each scene with a unique scene_id.\",\n      \"cross_domain_lens\": [\n        \"Database design sees primary keys as essential for traceability.\",\n        \"Film post-production tracks scenes via identifiers for editing alignment.\"\n      ],\n      \"edge_cases\": [\n        \"Duplicate IDs due to parallel scene generation.\",\n        \"Scene-split reorganization invalidates prior IDs.\"\n      ],\n      \"stakeholder_views\": [\n        \"Technical teams rely on IDs for reference integrity.\",\n        \"Editors use IDs for scene-level comment and revision.\"\n      ],\n      \"risk_landscape\": [\n        \"Mismanaged IDs disrupt mapping across timelines.\",\n        \"Unlabeled scenes risk loss in output assembly.\"\n      ]\n    },\n    {\n      \"task\": \"Annotate each scene with temporal offset and duration.\",\n      \"cross_domain_lens\": [\n        \"Time-based media encoding depends on timestamp precision.\",\n        \"Music production overlays event timing with millisecond-level cues.\"\n      ],\n      \"edge_cases\": [\n        \"Overlapping or negative offsets from misparsed input.\",\n        \"Undefined durations, especially with implicit fade/transitions.\"\n      ],\n      \"stakeholder_views\": [\n        \"Compositing requires accurate timing for render queues.\",\n        \"Audio-visual sync depends on exact offsets.\"\n      ],\n      \"risk_landscape\": [\n        \"Drift in temporal annotation creates synch errors.\",\n        \"Incorrect durations can truncate or extend scenes disproportionally.\"\n      ]\n    },\n    {\n      \"task\": \"Assign camera viewpoint to each scene.\",\n      \"cross_domain_lens\": [\n        \"Cinematography theory: camera placement influences emotion and story.\",\n        \"In VR production, viewpoint signals user immersion domain.\"\n      ],\n      \"edge_cases\": [\n        \"Ambiguous or shifting viewpoints inside scene.\",\n        \"Contradictory cues (first vs third person in narration).\"\n      ],\n      \"stakeholder_views\": [\n        \"Directors specify viewpoints for desired emotional effect.\",\n        \"Engineers apply viewpoint to rendering matrix.\"\n      ],\n      \"risk_landscape\": [\n        \"Poor viewpoint assignment lessens narrative impact.\",\n        \"Unresolved ambiguities can break visual coherence.\"\n      ]\n    },\n    {\n      \"task\": \"Assign camera motion to each scene.\",\n      \"cross_domain_lens\": [\n        \"Motion graphics bridges between kinetic effect and storytelling.\",\n        \"Robotics treats camera motion as trajectory planning.\"\n      ],\n      \"edge_cases\": [\n        \"Motion contradicts implied stillness or scene constraints.\",\n        \"Over-complex moves unachievable in target engine.\"\n      ],\n      \"stakeholder_views\": [\n        \"Operators prioritize real-world motion constraints.\",\n        \"Creative teams may demand physically ‘impossible’ moves for effect.\"\n      ],\n      \"risk_landscape\": [\n        \"Improper motion creates jarring or broken shots.\",\n        \"Failure to respect technical limits may crash engines or halt automation.\"\n      ]\n    },\n    {\n      \"task\": \"Assign camera lens/framing to each scene.\",\n      \"cross_domain_lens\": [\n        \"Photography relies on lens/framing for compositional meaning.\",\n        \"Film studies analyze framing as narrative strategy.\"\n      ],\n      \"edge_cases\": [\n        \"User input ambiguous between focal length and frame crop.\",\n        \"Frames incompatible with aspect ratio/scene setting.\"\n      ],\n      \"stakeholder_views\": [\n        \"DPs/DoPs have strong perspectives on lens choice.\",\n        \"Animation pipelines require precise numeric attributes.\"\n      ],\n      \"risk_landscape\": [\n        \"Inaccurate framing/lens harms subject prominence.\",\n        \"Conflicting parameters can break downstream comp.\"\n      ]\n    },\n    {\n      \"task\": \"Compile actor and object motion metadata for each scene.\",\n      \"cross_domain_lens\": [\n        \"In biomechanics, object motion studied for realism.\",\n        \"Crowd simulation maps metadata to individual tracks.\"\n      ],\n      \"edge_cases\": [\n        \"Simultaneous, contradictory motion paths.\",\n        \"Object/actor motion missing or ambiguous.\"\n      ],\n      \"stakeholder_views\": [\n        \"VFX and mocap teams depend on precise motion tracks.\",\n        \"Animators need differentiate passive/background from active/principals.\"\n      ],\n      \"risk_landscape\": [\n        \"Bad metadata causes animation drift and stutter.\",\n        \"Ambiguity creates synchronization bugs between actor and set.\"\n      ]\n    },\n    {\n      \"task\": \"Compile camera movement metadata for each scene.\",\n      \"cross_domain_lens\": [\n        \"Robotics uses metadata for virtual camera pathing.\",\n        \"Motion capture relies on movement primitives.\"\n      ],\n      \"edge_cases\": [\n        \"Complex movement unresolved by model/engine.\",\n        \"Gaps or overlaps in metadata across scene boundaries.\"\n      ],\n      \"stakeholder_views\": [\n        \"Pipeline automations require metadata for planning.\",\n        \"Directors expect movement metadata for shot consistency.\"\n      ],\n      \"risk_landscape\": [\n        \"Metadata misalignment de-syncs shot execution.\",\n        \"Inconsistent movement is visually jarring.\"\n      ]\n    },\n    {\n      \"task\": \"Compile speed and direction of all moving elements for each scene.\",\n      \"cross_domain_lens\": [\n        \"Physics simulation frameworks demand numeric precision.\",\n        \"Animation keyframe design requires directionality cues.\"\n      ],\n      \"edge_cases\": [\n        \"Ambiguous or contradictory direction cues.\",\n        \"Unrealistically rapid or static movement requests.\"\n      ],\n      \"stakeholder_views\": [\n        \"Animators must match speed/direction for continuity.\",\n        \"Technicians will stress test for realism and engine limits.\"\n      ],\n      \"risk_landscape\": [\n        \"Failed speed/direction cues cause continuity breaks.\",\n        \"Overly complex requests could overload simulation.\"\n      ]\n    },\n    {\n      \"task\": \"Detect transitions between scenes.\",\n      \"cross_domain_lens\": [\n        \"In film editing, transition types carry narrative function.\",\n        \"Video synthesis tools treat transitions as process nodes.\"\n      ],\n      \"edge_cases\": [\n        \"No explicit transition cues—must infer.\",\n        \"Implied transitions contradict downstream engine capability.\"\n      ],\n      \"stakeholder_views\": [\n        \"Editors value explicit transition logic.\",\n        \"Workflow engines need clear separation of logical scenes.\"\n      ],\n      \"risk_landscape\": [\n        \"Omitted transitions may feel jarring.\",\n        \"Unsupported transitions can cause pipeline failures.\"\n      ]\n    },\n    {\n      \"task\": \"Extract transition type, duration, and style for each detected transition.\",\n      \"cross_domain_lens\": [\n        \"UX animation design ties transition style to emotional effect.\",\n        \"Technical pipelines require standardized transition metadata.\"\n      ],\n      \"edge_cases\": [\n        \"Incomplete or ambiguous transition descriptors.\",\n        \"Style/duration unprocessable by certain engines.\"\n      ],\n      \"stakeholder_views\": [\n        \"Directors/Editors want narrative logic.\",\n        \"Pipeline engineers need actionable, granular info.\"\n      ],\n      \"risk_landscape\": [\n        \"Unclear type/style can halt automated processing.\",\n        \"Unmatched durations create rhythm issues.\"\n      ]\n    },\n    {\n      \"task\": \"If transition details unspecified, set default transition to crossfade.\",\n      \"cross_domain_lens\": [\n        \"Standardization: default to universally supported operation.\",\n        \"File format design uses fallback values for missing fields.\"\n      ],\n      \"edge_cases\": [\n        \"Default transition not supported by custom engines.\",\n        \"User intention opposes the default.\"\n      ],\n      \"stakeholder_views\": [\n        \"Engine maintainers expect defaulting for fail-safe.\",\n        \"Creative teams may dislike creative limitations.\"\n      ],\n      \"risk_landscape\": [\n        \"Inflexible defaults can stifle creative goals.\",\n        \"No default leads to abrupt processing failures.\"\n      ]\n    },\n    {\n      \"task\": \"Extract or interpolate global technical parameters: resolution.\",\n      \"cross_domain_lens\": [\n        \"Compression science prioritizes resolution for bandwidth control.\",\n        \"Display tech needs resolution matching for device compatibility.\"\n      ],\n      \"edge_cases\": [\n        \"Conflicting scene-specific resolution within one timeline.\",\n        \"Illegal or unsupported output resolutions.\"\n      ],\n      \"stakeholder_views\": [\n        \"Distributors and broadcasters set hard resolution specs.\",\n        \"Content creators care about appearance on end-user devices.\"\n      ],\n      \"risk_landscape\": [\n        \"Resolution misfit degrades quality or increases cost.\",\n        \"Unsupported sizes cause render failures.\"\n      ]\n    },\n    {\n      \"task\": \"Extract or interpolate global technical parameters: frame_rate.\",\n      \"cross_domain_lens\": [\n        \"Standards compliance (broadcast, web) requires strict frame rates.\",\n        \"Game engines tightly couple frame rate to simulation logic.\"\n      ],\n      \"edge_cases\": [\n        \"Mismatch in scene vs global frame rates.\",\n        \"Non-integer, variable, or undefined rates.\"\n      ],\n      \"stakeholder_views\": [\n        \"Engineers need frame rate consistency for toolchains.\",\n        \"Producers consider costs of high/low frame rates.\"\n      ],\n      \"risk_landscape\": [\n        \"Frame rate drift risks sync and stutter.\",\n        \"Non-compliant rates break downstream compatibility.\"\n      ]\n    },\n    {\n      \"task\": \"Extract or interpolate global technical parameters: color_palette.\",\n      \"cross_domain_lens\": [\n        \"Brand design locks palette to identity standards.\",\n        \"Color science links palette to human perception and mood.\"\n      ],\n      \"edge_cases\": [\n        \"Ambiguous or conflicting color cues.\",\n        \"Color schemes not supported in target engines.\"\n      ],\n      \"stakeholder_views\": [\n        \"Brand guardians check palette adherence.\",\n        \"Lighting/VFX need palette for setup and correction.\"\n      ],\n      \"risk_landscape\": [\n        \"Palette misassignment undermines mood or brand.\",\n        \"Unsupported palettes yield color inaccuracies or engine warnings.\"\n      ]\n    },\n    {\n      \"task\": \"Extract or interpolate global technical parameters: style.\",\n      \"cross_domain_lens\": [\n        \"Art history defines style contextually over time.\",\n        \"Generative models map style to quantitative tokens.\"\n      ],\n      \"edge_cases\": [\n        \"Style conflict: e.g., film noir mixed with saturated pop art.\",\n        \"Nonexistent style mapping in available engine.\"\n      ],\n      \"stakeholder_views\": [\n        \"Directors often enforce coherent style throughout.\",\n        \"Technical teams need explicit mappings for automation.\"\n      ],\n      \"risk_landscape\": [\n        \"Style incoherence reduces narrative power.\",\n        \"Silent fallback can degrade overall output.\"\n      ]\n    },\n    {\n      \"task\": \"Extract or interpolate global technical parameters: aspect_ratio.\",\n      \"cross_domain_lens\": [\n        \"Media distribution channels restrict aspect ratios.\",\n        \"Display hardware requires matching aspect to avoid letterboxing.\"\n      ],\n      \"edge_cases\": [\n        \"Conflicting ratios between scenes or shots.\",\n        \"Unprocessible or nonstandard aspect ratios.\"\n      ],\n      \"stakeholder_views\": [\n        \"Compliance teams enforce platform requirements.\",\n        \"Cinematographers may request aspect-ratio-specific framing.\"\n      ],\n      \"risk_landscape\": [\n        \"Aspect mismatches break composition or delivery pipeline.\",\n        \"Odd ratios create platform rejection or visual artifacts.\"\n      ]\n    },\n    {\n      \"task\": \"Extract or interpolate global technical parameters: pacing.\",\n      \"cross_domain_lens\": [\n        \"Directing equates pacing to rhythm/emotion.\",\n        \"Video processing algorithms rationalize pacing for user attention.\"\n      ],\n      \"edge_cases\": [\n        \"Pacing signals are implicit only.\",\n        \"Scene-by-scene variations break overall pacing flow.\"\n      ],\n      \"stakeholder_views\": [\n        \"Editors optimize for intended audience engagement.\",\n        \"Engineers need quantifiable pacing metrics.\"\n      ],\n      \"risk_landscape\": [\n        \"Poor pacing ruins narrative arc.\",\n        \"Pacing ambiguity hinders automation.\"\n      ]\n    },\n    {\n      \"task\": \"Extract per-scene technical parameters where specified.\",\n      \"cross_domain_lens\": [\n        \"Precision manufacturing highlights per-unit parameterization.\",\n        \"ML pipelines process scene-level hyperparameters.\"\n      ],\n      \"edge_cases\": [\n        \"Per-scene overrides in conflict with global.\",\n        \"Missing scene overrides create ambiguity.\"\n      ],\n      \"stakeholder_views\": [\n        \"Directors sometimes demand scene exceptions.\",\n        \"Renderers prefer global defaults for efficiency.\"\n      ],\n      \"risk_landscape\": [\n        \"Parameter conflict creates hard-to-debug errors.\",\n        \"Missing specifics can result in underwhelming scene presentation.\"\n      ]\n    },\n    {\n      \"task\": \"Identify global audio elements (soundtrack, sfx, voice, silence).\",\n      \"cross_domain_lens\": [\n        \"Game audio pipelines layer global and local effects.\",\n        \"Accessibility standards require explicit audio mapping.\"\n      ],\n      \"edge_cases\": [\n        \"Conflicting soundtrack instructions.\",\n        \"Nonexistent audio cues in user input.\"\n      ],\n      \"stakeholder_views\": [\n        \"Audio teams need up-front knowledge of global cues.\",\n        \"Localization teams need explicit voice mapping.\"\n      ],\n      \"risk_landscape\": [\n        \"Clashing elements can mask important cues.\",\n        \"Missing cues may lead to unimpressive, silent output.\"\n      ]\n    },\n    {\n      \"task\": \"Identify scene-specific audio elements.\",\n      \"cross_domain_lens\": [\n        \"Film scoring adapts per-scene mood.\",\n        \"Sound design differentiates background vs. focal cues.\"\n      ],\n      \"edge_cases\": [\n        \"Ambiguous assignment between global/scene audio.\",\n        \"Contradictory or overlaid scene cues.\"\n      ],\n      \"stakeholder_views\": [\n        \"Directors tailor scene mood/energy via distinct cues.\",\n        \"Audio mixers split by track for editing efficiency.\"\n      ],\n      \"risk_landscape\": [\n        \"Audio confusion can muddle key narrative beats.\",\n        \"Overlapping cues may not be properly rendered.\"\n      ]\n    },\n    {\n      \"task\": \"Annotate timing, loop, and fade behavior for all audio cues.\",\n      \"cross_domain_lens\": [\n        \"Music editors rely on automation lanes for fades/loops.\",\n        \"Interactive media demands explicit cue timing.\"\n      ],\n      \"edge_cases\": [\n        \"Fades/loops omitted, or cues contradicting duration.\",\n        \"Circular reference in loop definitions.\"\n      ],\n      \"stakeholder_views\": [\n        \"Audio implementers require complete cue timing.\",\n        \"Producers may want generative fade logic.\"\n      ],\n      \"risk_landscape\": [\n        \"Mismatched cue/fade leads to jarring cuts.\",\n        \"Loops unbound may cause audio cycling bugs.\"\n      ]\n    },\n    {\n      \"task\": \"Arrange scenes, transitions, and audio in a temporally ordered timeline structure.\",\n      \"cross_domain_lens\": [\n        \"Video editors use timeline-based NLEs.\",\n        \"Project management applies Gantt logic to time-delimited blocks.\"\n      ],\n      \"edge_cases\": [\n        \"Temporal ambiguity in scene start/end.\",\n        \"Automatic ordering collides with user intent.\"\n      ],\n      \"stakeholder_views\": [\n        \"Showrunners and composers plan structure top-down.\",\n        \"Computational renderers need efficient, unambiguous sequencing.\"\n      ],\n      \"risk_landscape\": [\n        \"Poor ordering breaks narrative and technical expectations.\",\n        \"Timeline collision can corrupt output.\"\n      ]\n    },\n    {\n      \"task\": \"Audit timeline for overlap/conflict between scenes.\",\n      \"cross_domain_lens\": [\n        \"QA involves overlap checks in multimedia authoring tools.\",\n        \"Data pipeline integrity requires windowing validation.\"\n      ],\n      \"edge_cases\": [\n        \"Intentional overlaps (picture-in-picture/parallel action).\",\n        \"Accidental overlaps due to timing math errors.\"\n      ],\n      \"stakeholder_views\": [\n        \"Post-production expects auditing for safe delivery.\",\n        \"Performance engineers want early conflict detection.\"\n      ],\n      \"risk_landscape\": [\n        \"Overlap errors may halt processing or create visual confusion.\",\n        \"Genuine overlaps mis-flagged as errors reduce creative options.\"\n      ]\n    },\n    {\n      \"task\": \"Audit timeline for temporal gaps between scenes.\",\n      \"cross_domain_lens\": [\n        \"Temporal gap detection critical in continuity management.\",\n        \"Event-driven architectures treat idle gaps as inefficiency.\"\n      ],\n      \"edge_cases\": [\n        \"Gaps are intentional (for suspense, blackout) but flagged.\",\n        \"Gap computation fails for complex, overlapping scene logic.\"\n      ],\n      \"stakeholder_views\": [\n        \"Directors desire control over timing lulls.\",\n        \"Automation needs clean handoff with no dead space.\"\n      ],\n      \"risk_landscape\": [\n        \"Gaps may break engagement or delivery requirements.\",\n        \"False-positive gap detection disrupts creative intent.\"\n      ]\n    },\n    {\n      \"task\": \"Audit transitions for abrupt jumps and flicker/jump-cut risk.\",\n      \"cross_domain_lens\": [\n        \"Film editing is fundamentally about smooth transitions.\",\n        \"Video analysis apps flag flicker visually.\"\n      ],\n      \"edge_cases\": [\n        \"Abrupt edits intentional for style (e.g., music videos).\",\n        \"Lighting or color shift mistaken for flicker.\"\n      ],\n      \"stakeholder_views\": [\n        \"Editors gauge naturalism vs. intentional disruption.\",\n        \"QA wants universal flicker mitigation.\"\n      ],\n      \"risk_landscape\": [\n        \"Jump-cuts reduce professionalism and fluidity.\",\n        \"Overaggressive mitigation deters fresh visual language.\"\n      ]\n    },\n    {\n      \"task\": \"Audit audio cues for unsynchronized alignment with timeline.\",\n      \"cross_domain_lens\": [\n        \"ADR (Automated Dialog Replacement) pipelines live or die on audio sync.\",\n        \"Music video production relies on exact beat alignment.\"\n      ],\n      \"edge_cases\": [\n        \"Intentional asynchrony for stylistic dissonance.\",\n        \"Cues drifting due to recursive scene timing edits.\"\n      ],\n      \"stakeholder_views\": [\n        \"Music supervisors enforce timing precision.\",\n        \"Directors may favor stylized, intentional disalignments.\"\n      ],\n      \"risk_landscape\": [\n        \"Audio misalignment disrupts immersion.\",\n        \"Overcorrection erodes stylized choices.\"\n      ]\n    },\n    {\n      \"task\": \"Abort and output explicit abort_code if any hard constraint is violated (e.g. duration limit, unsupported transition, invalid parameter, token/character/weight overflow).\",\n      \"cross_domain_lens\": [\n        \"Safety-critical software calls for fail-fast error signaling.\",\n        \"Command-line scripting benefits from explicit exit codes.\"\n      ],\n      \"edge_cases\": [\n        \"User requests intentionally violate constraints to explore system limits.\",\n        \"Chain of conflicting violations makes abort_code selection nontrivial.\"\n      ],\n      \"stakeholder_views\": [\n        \"Ops teams require actionable abort codes for rapid triage.\",\n        \"Users need clear feedback for request correction.\"\n      ],\n      \"risk_landscape\": [\n        \"Inconsistent abort logic frustrates debugging.\",\n        \"Silent failures lead to loss of user trust.\"\n      ]\n    },\n    {\n      \"task\": \"Generate warnings for soft issues (e.g. ambiguous timing, excessive complexity, unsupported effects on target platform).\",\n      \"cross_domain_lens\": [\n        \"Compiler warnings serve as best-practice analog.\",\n        \"Pro editor suites surface soft errors for review.\"\n      ],\n      \"edge_cases\": [\n        \"Multiple warnings accumulate, causing signal fatigue.\",\n        \"Warnings escalate to hard errors due to ignored issues.\"\n      ],\n      \"stakeholder_views\": [\n        \"QA teams prioritize warning clarity.\",\n        \"Creative leads may want warnings suppressed or contextualized.\"\n      ],\n      \"risk_landscape\": [\n        \"Excess/muddled warnings erode actionable value.\",\n        \"Suppressed warnings risk non-obvious downstream problems.\"\n      ]\n    },\n    {\n      \"task\": \"Assess scene-to-scene visual continuity.\",\n      \"cross_domain_lens\": [\n        \"Continuity editing ensures narrative fluidity.\",\n        \"Deep learning models look for feature vector coherence.\"\n      ],\n      \"edge_cases\": [\n        \"Intentional visual disruption (e.g., match cuts).\",\n        \"Subtle style shifts (e.g., color cast) span scenes.\"\n      ],\n      \"stakeholder_views\": [\n        \"Directors balance continuity vs. distinct scenes for story.\",\n        \"QA expects automatic flagging of continuity breaks.\"\n      ],\n      \"risk_landscape\": [\n        \"Continuity breaks confuse viewers.\",\n        \"Rigid enforcement can stifle creative jump cuts.\"\n      ]\n    },\n    {\n      \"task\": \"Assess audio continuity across scenes.\",\n      \"cross_domain_lens\": [\n        \"Radio dramas depend on seamless sonic world-building.\",\n        \"ML-driven podcasts blend audio for seamless transitions.\"\n      ],\n      \"edge_cases\": [\n        \"Hard audio cut required for narrative shock.\",\n        \"Environmental effects shift appropriately between scenes.\"\n      ],\n      \"stakeholder_views\": [\n        \"Audio leads value both seamlessness and dynamic range.\",\n        \"Editors seek flagged discontinuities for manual override.\"\n      ],\n      \"risk_landscape\": [\n        \"Poor continuity distracts or jars viewers.\",\n        \"Overblending can remove intended dynamic accents.\"\n      ]\n    },\n    {\n      \"task\": \"Assess style coherence across all scenes.\",\n      \"cross_domain_lens\": [\n        \"Series production enforces style bibles.\",\n        \"Automated art pipelines use global tokens for consistency.\"\n      ],\n      \"edge_cases\": [\n        \"Deliberate style shift for specific narrative beats.\",\n        \"Imported assets violate style unintentionally.\"\n      ],\n      \"stakeholder_views\": [\n        \"Producers want overall consistency.\",\n        \"Creative teams may break coherence for story effects.\"\n      ],\n      \"risk_landscape\": [\n        \"Style incoherence undermines professional polish.\",\n        \"Rigid enforcement can prevent intentional palette shifts.\"\n      ]\n    },\n    {\n      \"task\": \"Assess cross-frame artifact minimization.\",\n      \"cross_domain_lens\": [\n        \"Video compression aims to reduce cross-frame error.\",\n        \"Animation polish targets temporal artifact smoothing.\"\n      ],\n      \"edge_cases\": [\n        \"Rapid action scenes that sacrifice artifact reduction for impact.\",\n        \"Engine limitations lead to unavoidable artifacts.\"\n      ],\n      \"stakeholder_views\": [\n        \"Post-production seeks flagging for expensive clean-up.\",\n        \"Software teams prioritize artifact prediction tools.\"\n      ],\n      \"risk_landscape\": [\n        \"Artifacts reduce visual credibility.\",\n        \"Excessive minimization can blur critical detail.\"\n      ]\n    },\n    {\n      \"task\": \"Validate parameter order for output.\",\n      \"cross_domain_lens\": [\n        \"APIs require contract order for backward compatibility.\",\n        \"Config file parsing is order-sensitive in many systems.\"\n      ],\n      \"edge_cases\": [\n        \"Legacy systems require different parameter orders.\",\n        \"User input tries to override strict ordering.\"\n      ],\n      \"stakeholder_views\": [\n        \"Engineers demand order for deterministic builds.\",\n        \"QA cross-checks output schema compliance.\"\n      ],\n      \"risk_landscape\": [\n        \"Parameter disorder creates parsing errors.\",\n        \"Unclear order exposes risk in agile development.\"\n      ]\n    },\n    {\n      \"task\": \"Validate temporal alignment for all elements.\",\n      \"cross_domain_lens\": [\n        \"Live broadcast pipelines depend on timeline locks.\",\n        \"Multimedia API calls must synchronize media tracks for playback.\"\n      ],\n      \"edge_cases\": [\n        \"Microsecond drift across long timelines.\",\n        \"Engine limits prevent perfect sync.\"\n      ],\n      \"stakeholder_views\": [\n        \"Ops teams require audit trails for sync failures.\",\n        \"End-users notice even small disalignments.\"\n      ],\n      \"risk_landscape\": [\n        \"Bad alignment disrupts experience.\",\n        \"Silent sync loss leads to subtle bugs.\"\n      ]\n    },\n    {\n      \"task\": \"Ensure syntactic and semantic validity of output schema for downstream engines.\",\n      \"cross_domain_lens\": [\n        \"Data serialization (JSON/XML) requires strong schema validation.\",\n        \"ML pipelines fail on schema mismatches or ambiguities.\"\n      ],\n      \"edge_cases\": [\n        \"Target engines update schema requirements post-deployment.\",\n        \"Semantic meaning is lost in translation to protocol.\"\n      ],\n      \"stakeholder_views\": [\n        \"Integration teams expect robust machine-readability.\",\n        \"Compliance officers demand clear schema conformance.\"\n      ],\n      \"risk_landscape\": [\n        \"Schema misfit blocks automation and upload.\",\n        \"Semantic drift risks loss of production intent.\"\n      ]\n    },\n    {\n      \"task\": \"Enforce deterministic output by lock_seed.\",\n      \"cross_domain_lens\": [\n        \"Scientific computing uses random seed-lock for replication.\",\n        \"Gaming relies on seeds for reproducible procedural content.\"\n      ],\n      \"edge_cases\": [\n        \"Lock fails in parallel/batch environment.\",\n        \"Seed conflict across user sessions.\"\n      ],\n      \"stakeholder_views\": [\n        \"Researchers require reproducibility.\",\n        \"Producers need deterministic previews for signoff.\"\n      ],\n      \"risk_landscape\": [\n        \"Non-determinism frustrates sign-off workflows.\",\n        \"Colliding seeds disrupt individualized outputs.\"\n      ]\n    },\n    {\n      \"task\": \"Enforce strict parameter ordering for determinism.\",\n      \"cross_domain_lens\": [\n        \"Configuration management systems require order for diffs.\",\n        \"Legal documents enforce clause order for intent.\"\n      ],\n      \"edge_cases\": [\n        \"Input generated dynamically, order not guaranteed.\",\n        \"Human editors override automation breaking order.\"\n      ],\n      \"stakeholder_views\": [\n        \"Developers depend on reproducible builds.\",\n        \"Analysts expect order for data curation.\"\n      ],\n      \"risk_landscape\": [\n        \"Order drift causes hard-to-debug changes.\",\n        \"Pipeline failures traceable to ordering inconsistencies.\"\n      ]\n    },\n    {\n      \"task\": \"Build final structured output: array of scenes with metadata, ordered transitions, audio tracks per scene/timeline, global parameters, validation logs, warnings, and abort_code if any.\",\n      \"cross_domain_lens\": [\n        \"Enterprise data pipelines collate recordsets for audit.\",\n        \"Film online editing generates EDLs (edit decision lists).\"\n      ],\n      \"edge_cases\": [\n        \"Output size exceeds downstream limits.\",\n        \"Conflicting metadata between scenes in array.\"\n      ],\n      \"stakeholder_views\": [\n        \"QA expects structured logs for traceability.\",\n        \"Operators require self-validating output for automation.\"\n      ],\n      \"risk_landscape\": [\n        \"Broken output structure trips processing engines.\",\n        \"Ambiguous array order undermines post-processing.\"\n      ]\n    },\n    {\n      \"task\": \"Return output in explicit, engine-consumable schema (array/dictionary/fields) for machine parsing and production use.\",\n      \"cross_domain_lens\": [\n        \"API spec design demands contractually clear output.\",\n        \"Production databases ingest only predictable fielded data.\"\n      ],\n      \"edge_cases\": [\n        \"Target engines introduce new required fields unannounced.\",\n        \"User preferences contradict output spec.\"\n      ],\n      \"stakeholder_views\": [\n        \"Integration dev teams expect plug-and-play output.\",\n        \"Automation sees schema clarity as key for reliability.\"\n      ],\n      \"risk_landscape\": [\n        \"Schema drift blocks production.\",\n        \"Non-explicit schemas lead to brittle integrations.\"\n      ]\n    }\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"primary_directive\": \"Design and enforce a deterministic, auditable, structured pipeline for converting any free-form user request into an engine-ready, schema-validated array of scenes, transitions, audio, and timeline metadata for video generation—ensuring every step from intent extraction to timeline conflict auditing is both fail-fast and produces output in an explicit, machine-consumable schema.\",\n  \"supporting_rationale\": [\n    \"This directive subsumes all lower-level steps (extraction, segmentation, segmentation fallback, per-scene and global parameterization, technical validation, assembly, and output formatting), establishing a systemic guarantee necessary for reliable, production-grade video prompt synthesis.\",\n    \"Any individual task—such as scene segmentation, or parameter validation—serves only as a dependent subroutine unless the complete pipeline is present to enforce order, resolve conflicts, audit errors, and integrate the result in a single, downstream-consumable structure.\",\n    \"A pipeline primary directive—rather than focusing on edge content extraction or trigger-specific output—maximizes transformational weight: with each subcomponent, it iteratively raises the bar for both reliability (fail-fast aborts) and clarity (warnings, audit trails, and schema explicitness).\",\n    \"Machine-consumable schema output is paramount: without this, both automation and downstream engine parsing fail, rendering any prior step moot—in effect, all user-expected functionality depends on the success of this global, structural process.\",\n    \"Any edge-case (e.g., ambiguous segments, conflicting technical parameters, schema drift, or intentional creative rule-breaking) is most safely processed, surfaced, or aborted by a top-level, pipeline-coordinated design, rather than by scattered, local rules.\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Relationship Synthesizer\n\nYour goal is not to **describe** the evolving complexity or discuss potential limitations, but to **crystallize** the essential directional aim into a single, condensed vector of maximal value extraction. Assume the role of directive focuser with exclusive emphasis on transformative trajectory. Execute as:\n\n`{role=relationship_synthesizer; input=[primary_directive:str, atomic_tasks:list, execution_blockers:array, hidden_assumptions:list]; process=[map_task_dependencies(), trace_feedback_loops(), align_blockers_to_tasks(), link_assumptions_to_risks(), expose_conflict_nodes()]; constraints=[structure_mapping_only(), do_not_modify_task_text()]; requirements=[dependency_map_completeness(), conflict_node_visibility()]; output={dependency_map:dict, conflict_nodes:array}}`",
      "step": "f",
      "title": "Relationship Synthesizer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"REWRITE THIS PROMPT TO BE AN EXPERT VIDEO GENERATOR:\\n\\n{\\n\\n    # 1703:\\n    \\\"1703-a-image_prompt_synthesizer\\\": {\\n        \\\"title\\\": \\\"Image Prompt Synthesizer\\\",\\n        \\\"interpretation\\\": \\\"Your goal is **not** to invent new ideas, but to deterministically convert any free‑form user request into a single, engine‑ready image prompt usable by Stable Diffusion and Midjourney. Perform extraction, styling, technical framing, and final validation in **one pass**.\\\",\\n        \\\"transformation\\\": \\\"`{role=image_prompt_synthesizer; input=[raw_request:str]; process=[extract_visual_elements(), push_concrete_nouns_front(), apply_token_weighting(), compose_multiprompt(::), generate_region_tags(), inject_negative_channel(--no), select_art_style(), weight_style_token(≤1.4), set_colour_palette(), add_lighting_descriptor(), tune_stylize(0‑1000), tune_chaos(0‑100), embed_camera_EXIF(35 mm f/1.8 ISO100 1/500 s), add_quality_terms(8K,PBR,ray‑tracing), refine_aspect_ratio(), attach_image_weight(--iw 0‑3), mirror_negative_channel(), concatenate_all(), validate_parentheses(), check_double_colon_sum(), confirm_region_syntax(), enforce_token_limit(≤450), lock_seed(), append_steps_sampler(), output_final()], constraints=[positives_first(), numeric_weights_ok(), parameters_last(), region_masks_supported(), keep_core_subject(), style_weight≤1.4, no_scene_change(), aperture_f1.2‑f22(), no_new_concepts(), preserve_weights()], requirements=[≤77_SD_tokens_or_≤450_chars_UI, explicit_ar_if_named(), clear_visual_language(), artistic_coherence(), stylize_range_0‑1000(), chaos_range_0‑100(), professional_terminology(), reproducible_prompt(), unambiguous()], output={final_image_prompt:str}}`\\\",\\n        \\\"context\\\": {\\n            \\\"principles\\\": {\\n                \\\"determinism\\\": \\\"Identical input must always yield the same prompt (seed‑locked).\\\",\\n                \\\"layered_control\\\": \\\"Separate positive, region, and negative semantics before concatenation; merge only at final step.\\\",\\n                \\\"minimal_overhead\\\": \\\"All weighting, style, and camera parameters trail the prose to satisfy engine syntax.\\\"\\n            },\\n            \\\"success_criteria\\\": {\\n                \\\"syntactic_validity\\\": \\\"Parentheses, double colon maths, and parameter order pass model parsers.\\\",\\n                \\\"token_compliance\\\": \\\"Prompt length respects the stricter of SD‑77‑token or 450‑char UI limits.\\\",\\n                \\\"subject_fidelity\\\": \\\"Primary subject weight ≥ 1.2; background ≤ 1.0; style weight ≤ 1.4.\\\",\\n                \\\"technical_readiness\\\": \\\"Aspect ratio, seed, sampler, steps, stylize, chaos, quality flags, and optional --iw are present and correctly positioned.\\\",\\n                \\\"clarity\\\": \\\"Language is concrete, jargon‑free, and region masks (if any) use `[tag] … |` notation.\\\"\\n            }\\n        }\\n    },\\n    \\\"1703-b-premium_image_prompt_synthesizer\\\": {\\n        \\\"title\\\": \\\"Premium Image Prompt Synthesizer\\\",\\n        \\\"interpretation\\\": \\\"Convert any free‑form request into a deterministic, model‑ready prompt that meets *premium* visual‑fidelity standards for Stable Diffusion & Midjourney **in a single call** while retaining full error‑handling and quality gates.\\\",\\n        \\\"transformation\\\": \\\"`{role=premium_image_prompt_synthesizer; input=[raw_request:str]; process=[\\\\n  /* — PHASE 1 : Context & Safety — */\\\\n  extract_visual_elements(),\\\\n  detect_conflicting_aspect_ratio(),                       abort_if_conflict(),\\\\n  enforce_token_budget(≤77_SD_tokens_or_≤450_chars_UI),    abort_if_exceeded(),\\\\n  /* — PHASE 2 : Positive Channel Build — */\\\\n  push_concrete_nouns_front(),\\\\n  apply_token_weighting(primary≥1.2,background≤1.0),       abort_if_total_weight≤0,\\\\n  compose_multiprompt(::),\\\\n  generate_region_tags_if_scene_implied(),                 note_mobile_mask_warning(),\\\\n  /* — PHASE 3 : Style & Aesthetics — */\\\\n  select_art_style(),\\\\n  reject_style_if_conflicts_camera_realism(),\\\\n  weight_style_token(≤1.4),\\\\n  set_colour_palette(harmonious),\\\\n  add_lighting_descriptor(coherent_with_palette),\\\\n  tune_stylize(bounds=0‑1000),                             abort_if_out_of_bounds(),\\\\n  tune_chaos(bounds=0‑100),                                abort_if_out_of_bounds(),\\\\n  /* — PHASE 4 : Technical & Camera — */\\\\n  embed_camera_EXIF(\\\\\\\"35 mm f/1.8 ISO100 1/500 s\\\\\\\"),\\\\n  verify_aperture_range(f1.2‑f22),                         abort_if_invalid(),\\\\n  add_quality_terms(8K,PBR,ray‑tracing),\\\\n  refine_aspect_ratio(),\\\\n  attach_image_weight(--iw 0‑3_if_URL_present),            abort_if_IW_out_of_bounds(),\\\\n  /* — PHASE 5 : Negative Channel & Mirroring — */\\\\n  collect_user_negatives(),\\\\n  append_builtin_negatives(overblur,lowres,distracting_text),\\\\n  validate_negative_strength(avoids_colour_washout),\\\\n  /* — PHASE 6 : Final Assembly & Validation — */\\\\n  concatenate_positive_negative(),                         parameters_trail_prose(),\\\\n  validate_parentheses_balance(),\\\\n  check_double_colon_weight_sum(),                         abort_if_weight_sum_zero(),\\\\n  confirm_region_syntax(),\\\\n  lock_seed_and_append_sampler_steps(),\\\\n  output_final_prompt()\\\\n],\\\\nconstraints=[\\\\n  positives_first(), parameters_last(), region_masks_supported(),\\\\n  no_new_concepts(), preserve_weights(), style_weight≤1.4,\\\\n  professional_terminology(), explicit_ar_if_named()\\\\n],\\\\nrequirements=[\\\\n  premium_visual_fidelity(), reproducible(), clear_visual_language(),\\\\n  quality_gate(FID<15_or_equivalent), atomic_fail_fast_on_violation()\\\\n],\\\\noutput={final_image_prompt:str, warnings:list, abort_code:int|null}}`\\\",\\n        \\\"context\\\": {\\n            \\\"principles\\\": {\\n                \\\"fail_fast\\\": \\\"Abort with explicit code when any hard constraint is violated to prevent wasted renders.\\\",\\n                \\\"surface_blockers\\\": \\\"Return `warnings` array for soft issues (e.g. region masks on mobile).\\\",\\n                \\\"premium_fidelity\\\": \\\"Always target ultra‑sharp, detail‑rich, production‑grade outputs.\\\"\\n            },\\n            \\\"success_criteria\\\": {\\n                \\\"syntactic_pass\\\": \\\"Prompt parses without error in both SD & MJ (parentheses, `::`, parameter order).\\\",\\n                \\\"visual_quality\\\": \\\"Meets or exceeds internal FID / LPIPS thresholds (or skips metric if unavailable).\\\",\\n                \\\"determinism\\\": \\\"Identical input + seed => identical output.\\\",\\n                \\\"safety\\\": \\\"No colour washout, no truncated tokens, no invalid weights, no parameter conflicts.\\\"\\n            },\\n            \\\"edge_case_handling\\\": {\\n                \\\"zero_weight_sum\\\": \\\"Hard abort (`abort_code` = 92).\\\",\\n                \\\"token_budget_overflow\\\": \\\"Hard abort (`abort_code` = 77).\\\",\\n                \\\"aspect_ratio_conflict\\\": \\\"Hard abort (`abort_code` = AR1).\\\",\\n                \\\"out_of_bounds_stylize_chaos\\\": \\\"Hard abort (`abort_code` = SC1).\\\",\\n                \\\"IW_without_URL\\\": \\\"Warning only; `--iw` dropped.\\\"\\n            }\\n        }\\n    }\\n\\n}\\\"```'\\n\\n\",\n  \"primary_directive\": \"Design and enforce a deterministic, auditable, structured pipeline for converting any free-form user request into an engine-ready, schema-validated array of scenes, transitions, audio, and timeline metadata for video generation—ensuring every step from intent extraction to timeline conflict auditing is both fail-fast and produces output in an explicit, machine-consumable schema.\",\n  \"supporting_rationale\": [\n    \"This directive subsumes all lower-level steps (extraction, segmentation, segmentation fallback, per-scene and global parameterization, technical validation, assembly, and output formatting), establishing a systemic guarantee necessary for reliable, production-grade video prompt synthesis.\",\n    \"Any individual task—such as scene segmentation, or parameter validation—serves only as a dependent subroutine unless the complete pipeline is present to enforce order, resolve conflicts, audit errors, and integrate the result in a single, downstream-consumable structure.\",\n    \"A pipeline primary directive—rather than focusing on edge content extraction or trigger-specific output—maximizes transformational weight: with each subcomponent, it iteratively raises the bar for both reliability (fail-fast aborts) and clarity (warnings, audit trails, and schema explicitness).\",\n    \"Machine-consumable schema output is paramount: without this, both automation and downstream engine parsing fail, rendering any prior step moot—in effect, all user-expected functionality depends on the success of this global, structural process.\",\n    \"Any edge-case (e.g., ambiguous segments, conflicting technical parameters, schema drift, or intentional creative rule-breaking) is most safely processed, surfaced, or aborted by a top-level, pipeline-coordinated design, rather than by scattered, local rules.\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"dependency_map\": {\n    \"atomic_tasks\": [\n      \"extract_user_intent\",\n      \"segment_request_into_scenes\",\n      \"fallback_if_segmentation_fails\",\n      \"extract_per_scene_visual_audio_semantics\",\n      \"map_scenes_to_timeline\",\n      \"assign_transitions_between_scenes\",\n      \"parameterize_scene_attributes (e.g. camera, effects, style)\",\n      \"validate_per_scene_and_global_parameters (lengths, format, types)\",\n      \"detect_conflicting_parameters (timing, aspect ratio, resource limits)\",\n      \"compose_machine_consumiable_schema (scenes, transitions, audio, meta)\",\n      \"audit_schema_against_validation_rules\",\n      \"output_final_structured_video_prompt\",\n      \"surface_soft_warnings (ambiguous, edge, or fallback cases)\",\n      \"fail_fast_on_hard_constraint_violation\"\n    ],\n    \"task_dependencies\": {\n      \"extract_user_intent\": [],\n      \"segment_request_into_scenes\": [\"extract_user_intent\"],\n      \"fallback_if_segmentation_fails\": [\"segment_request_into_scenes\"],\n      \"extract_per_scene_visual_audio_semantics\": [\n        \"segment_request_into_scenes\",\n        \"fallback_if_segmentation_fails\"\n      ],\n      \"map_scenes_to_timeline\": [\"extract_per_scene_visual_audio_semantics\"],\n      \"assign_transitions_between_scenes\": [\"map_scenes_to_timeline\"],\n      \"parameterize_scene_attributes (e.g. camera, effects, style)\": [\n        \"extract_per_scene_visual_audio_semantics\"\n      ],\n      \"validate_per_scene_and_global_parameters (lengths, format, types)\": [\n        \"parameterize_scene_attributes (e.g. camera, effects, style)\",\n        \"assign_transitions_between_scenes\"\n      ],\n      \"detect_conflicting_parameters (timing, aspect ratio, resource limits)\": [\n        \"validate_per_scene_and_global_parameters (lengths, format, types)\"\n      ],\n      \"compose_machine_consumiable_schema (scenes, transitions, audio, meta)\": [\n        \"detect_conflicting_parameters (timing, aspect ratio, resource limits)\"\n      ],\n      \"audit_schema_against_validation_rules\": [\n        \"compose_machine_consumiable_schema (scenes, transitions, audio, meta)\"\n      ],\n      \"output_final_structured_video_prompt\": [\n        \"audit_schema_against_validation_rules\"\n      ],\n      \"surface_soft_warnings (ambiguous, edge, or fallback cases)\": [\n        \"fallback_if_segmentation_fails\",\n        \"detect_conflicting_parameters (timing, aspect ratio, resource limits)\",\n        \"audit_schema_against_validation_rules\"\n      ],\n      \"fail_fast_on_hard_constraint_violation\": [\n        \"validate_per_scene_and_global_parameters (lengths, format, types)\",\n        \"detect_conflicting_parameters (timing, aspect ratio, resource limits)\",\n        \"audit_schema_against_validation_rules\"\n      ]\n    },\n    \"feedback_loops\": {\n      \"fallback_if_segmentation_fails\": [\"segment_request_into_scenes\"],\n      \"surface_soft_warnings (ambiguous, edge, or fallback cases)\": [\"audit_schema_against_validation_rules\"],\n      \"fail_fast_on_hard_constraint_violation\": [\"audit_schema_against_validation_rules\"]\n    },\n    \"blocker_alignment\": {\n      \"segmentation_failures\": [\"fallback_if_segmentation_fails\"],\n      \"parameter_conflicts\": [\"detect_conflicting_parameters (timing, aspect ratio, resource limits)\", \"fail_fast_on_hard_constraint_violation\"],\n      \"schema_nonconformance\": [\"audit_schema_against_validation_rules\", \"fail_fast_on_hard_constraint_violation\"],\n      \"ambiguous_user_intent\": [\"surface_soft_warnings (ambiguous, edge, or fallback cases)\"]\n    },\n    \"assumption_risk_links\": {\n      \"assume_input_can_be_segmented\": {\n        \"risk\": \"Segmentation may fail; fallback and warning paths must be robust.\",\n        \"resolved_by\": [\"fallback_if_segmentation_fails\", \"surface_soft_warnings (ambiguous, edge, or fallback cases)\"]\n      },\n      \"assume_engine_requirements_lane_with_schema\": {\n        \"risk\": \"Schema drift or mismatch with downstream engine blocks output utility.\",\n        \"resolved_by\": [\"audit_schema_against_validation_rules\"]\n      },\n      \"assume fail-fast aborts can occur cleanly\": {\n        \"risk\": \"Inconsistent error handling leads to silent or downstream schema failures.\",\n        \"resolved_by\": [\"fail_fast_on_hard_constraint_violation\"]\n      },\n      \"assume per-scene parameterization is distinct from global parameters\": {\n        \"risk\": \"Parameter ambiguity, invalid overrides, or drift from requested style.\",\n        \"resolved_by\": [\"validate_per_scene_and_global_parameters (lengths, format, types)\", \"detect_conflicting_parameters (timing, aspect ratio, resource limits)\"]\n      }\n    }\n  },\n  \"conflict_nodes\": [\n    \"fallback_if_segmentation_fails <-> ambiguous segmentation cases may trigger both warnings and fallback, requiring consistent prioritization/path resolution\",\n    \"validate_per_scene_and_global_parameters <-> detect_conflicting_parameters: Edge values (e.g. negative duration, incompatible aspect) may be flagged in both tasks, requiring deterministic error precedence\",\n    \"fail_fast_on_hard_constraint_violation <-> surface_soft_warnings: Hard aborts and soft warning surfacing both hook schema validation failures; must enforce atomicity to prevent double-reporting or output drift\",\n    \"compose_machine_consumiable_schema <-> audit_schema_against_validation_rules: The schema composition step might introduce or expose structural inconsistencies that only surface at audit, creating possible feedback or iteration loop necessity\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Boundary Encoder\n\nYour goal is not to **analyze** the input text, but to **decompose** it into a sequential list of distinct semantic segments. Execute as:\n\n`{role=boundary_encoder; input=[execution_blockers:array, hidden_assumptions:list]; process=[translate_blockers_to_constraints(), translate_assumptions_to_requirements(), validate_specificity(), eliminate_duplicates()]; constraints=[boundary_focus_only(), no_process_generation()]; requirements=[constraint_exactness(), requirement_enforceability()]; output={constraints:list, requirements:list}}`",
      "step": "g",
      "title": "Boundary Encoder",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"REWRITE THIS PROMPT TO BE AN EXPERT VIDEO GENERATOR:\\n\\n{\\n\\n    # 1703:\\n    \\\"1703-a-image_prompt_synthesizer\\\": {\\n        \\\"title\\\": \\\"Image Prompt Synthesizer\\\",\\n        \\\"interpretation\\\": \\\"Your goal is **not** to invent new ideas, but to deterministically convert any free‑form user request into a single, engine‑ready image prompt usable by Stable Diffusion and Midjourney. Perform extraction, styling, technical framing, and final validation in **one pass**.\\\",\\n        \\\"transformation\\\": \\\"`{role=image_prompt_synthesizer; input=[raw_request:str]; process=[extract_visual_elements(), push_concrete_nouns_front(), apply_token_weighting(), compose_multiprompt(::), generate_region_tags(), inject_negative_channel(--no), select_art_style(), weight_style_token(≤1.4), set_colour_palette(), add_lighting_descriptor(), tune_stylize(0‑1000), tune_chaos(0‑100), embed_camera_EXIF(35 mm f/1.8 ISO100 1/500 s), add_quality_terms(8K,PBR,ray‑tracing), refine_aspect_ratio(), attach_image_weight(--iw 0‑3), mirror_negative_channel(), concatenate_all(), validate_parentheses(), check_double_colon_sum(), confirm_region_syntax(), enforce_token_limit(≤450), lock_seed(), append_steps_sampler(), output_final()], constraints=[positives_first(), numeric_weights_ok(), parameters_last(), region_masks_supported(), keep_core_subject(), style_weight≤1.4, no_scene_change(), aperture_f1.2‑f22(), no_new_concepts(), preserve_weights()], requirements=[≤77_SD_tokens_or_≤450_chars_UI, explicit_ar_if_named(), clear_visual_language(), artistic_coherence(), stylize_range_0‑1000(), chaos_range_0‑100(), professional_terminology(), reproducible_prompt(), unambiguous()], output={final_image_prompt:str}}`\\\",\\n        \\\"context\\\": {\\n            \\\"principles\\\": {\\n                \\\"determinism\\\": \\\"Identical input must always yield the same prompt (seed‑locked).\\\",\\n                \\\"layered_control\\\": \\\"Separate positive, region, and negative semantics before concatenation; merge only at final step.\\\",\\n                \\\"minimal_overhead\\\": \\\"All weighting, style, and camera parameters trail the prose to satisfy engine syntax.\\\"\\n            },\\n            \\\"success_criteria\\\": {\\n                \\\"syntactic_validity\\\": \\\"Parentheses, double colon maths, and parameter order pass model parsers.\\\",\\n                \\\"token_compliance\\\": \\\"Prompt length respects the stricter of SD‑77‑token or 450‑char UI limits.\\\",\\n                \\\"subject_fidelity\\\": \\\"Primary subject weight ≥ 1.2; background ≤ 1.0; style weight ≤ 1.4.\\\",\\n                \\\"technical_readiness\\\": \\\"Aspect ratio, seed, sampler, steps, stylize, chaos, quality flags, and optional --iw are present and correctly positioned.\\\",\\n                \\\"clarity\\\": \\\"Language is concrete, jargon‑free, and region masks (if any) use `[tag] … |` notation.\\\"\\n            }\\n        }\\n    },\\n    \\\"1703-b-premium_image_prompt_synthesizer\\\": {\\n        \\\"title\\\": \\\"Premium Image Prompt Synthesizer\\\",\\n        \\\"interpretation\\\": \\\"Convert any free‑form request into a deterministic, model‑ready prompt that meets *premium* visual‑fidelity standards for Stable Diffusion & Midjourney **in a single call** while retaining full error‑handling and quality gates.\\\",\\n        \\\"transformation\\\": \\\"`{role=premium_image_prompt_synthesizer; input=[raw_request:str]; process=[\\\\n  /* — PHASE 1 : Context & Safety — */\\\\n  extract_visual_elements(),\\\\n  detect_conflicting_aspect_ratio(),                       abort_if_conflict(),\\\\n  enforce_token_budget(≤77_SD_tokens_or_≤450_chars_UI),    abort_if_exceeded(),\\\\n  /* — PHASE 2 : Positive Channel Build — */\\\\n  push_concrete_nouns_front(),\\\\n  apply_token_weighting(primary≥1.2,background≤1.0),       abort_if_total_weight≤0,\\\\n  compose_multiprompt(::),\\\\n  generate_region_tags_if_scene_implied(),                 note_mobile_mask_warning(),\\\\n  /* — PHASE 3 : Style & Aesthetics — */\\\\n  select_art_style(),\\\\n  reject_style_if_conflicts_camera_realism(),\\\\n  weight_style_token(≤1.4),\\\\n  set_colour_palette(harmonious),\\\\n  add_lighting_descriptor(coherent_with_palette),\\\\n  tune_stylize(bounds=0‑1000),                             abort_if_out_of_bounds(),\\\\n  tune_chaos(bounds=0‑100),                                abort_if_out_of_bounds(),\\\\n  /* — PHASE 4 : Technical & Camera — */\\\\n  embed_camera_EXIF(\\\\\\\"35 mm f/1.8 ISO100 1/500 s\\\\\\\"),\\\\n  verify_aperture_range(f1.2‑f22),                         abort_if_invalid(),\\\\n  add_quality_terms(8K,PBR,ray‑tracing),\\\\n  refine_aspect_ratio(),\\\\n  attach_image_weight(--iw 0‑3_if_URL_present),            abort_if_IW_out_of_bounds(),\\\\n  /* — PHASE 5 : Negative Channel & Mirroring — */\\\\n  collect_user_negatives(),\\\\n  append_builtin_negatives(overblur,lowres,distracting_text),\\\\n  validate_negative_strength(avoids_colour_washout),\\\\n  /* — PHASE 6 : Final Assembly & Validation — */\\\\n  concatenate_positive_negative(),                         parameters_trail_prose(),\\\\n  validate_parentheses_balance(),\\\\n  check_double_colon_weight_sum(),                         abort_if_weight_sum_zero(),\\\\n  confirm_region_syntax(),\\\\n  lock_seed_and_append_sampler_steps(),\\\\n  output_final_prompt()\\\\n],\\\\nconstraints=[\\\\n  positives_first(), parameters_last(), region_masks_supported(),\\\\n  no_new_concepts(), preserve_weights(), style_weight≤1.4,\\\\n  professional_terminology(), explicit_ar_if_named()\\\\n],\\\\nrequirements=[\\\\n  premium_visual_fidelity(), reproducible(), clear_visual_language(),\\\\n  quality_gate(FID<15_or_equivalent), atomic_fail_fast_on_violation()\\\\n],\\\\noutput={final_image_prompt:str, warnings:list, abort_code:int|null}}`\\\",\\n        \\\"context\\\": {\\n            \\\"principles\\\": {\\n                \\\"fail_fast\\\": \\\"Abort with explicit code when any hard constraint is violated to prevent wasted renders.\\\",\\n                \\\"surface_blockers\\\": \\\"Return `warnings` array for soft issues (e.g. region masks on mobile).\\\",\\n                \\\"premium_fidelity\\\": \\\"Always target ultra‑sharp, detail‑rich, production‑grade outputs.\\\"\\n            },\\n            \\\"success_criteria\\\": {\\n                \\\"syntactic_pass\\\": \\\"Prompt parses without error in both SD & MJ (parentheses, `::`, parameter order).\\\",\\n                \\\"visual_quality\\\": \\\"Meets or exceeds internal FID / LPIPS thresholds (or skips metric if unavailable).\\\",\\n                \\\"determinism\\\": \\\"Identical input + seed => identical output.\\\",\\n                \\\"safety\\\": \\\"No colour washout, no truncated tokens, no invalid weights, no parameter conflicts.\\\"\\n            },\\n            \\\"edge_case_handling\\\": {\\n                \\\"zero_weight_sum\\\": \\\"Hard abort (`abort_code` = 92).\\\",\\n                \\\"token_budget_overflow\\\": \\\"Hard abort (`abort_code` = 77).\\\",\\n                \\\"aspect_ratio_conflict\\\": \\\"Hard abort (`abort_code` = AR1).\\\",\\n                \\\"out_of_bounds_stylize_chaos\\\": \\\"Hard abort (`abort_code` = SC1).\\\",\\n                \\\"IW_without_URL\\\": \\\"Warning only; `--iw` dropped.\\\"\\n            }\\n        }\\n    }\\n\\n}\\\"```'\\n\\n\",\n  \"dependency_map\": {\n    \"atomic_tasks\": [\n      \"extract_user_intent\",\n      \"segment_request_into_scenes\",\n      \"fallback_if_segmentation_fails\",\n      \"extract_per_scene_visual_audio_semantics\",\n      \"map_scenes_to_timeline\",\n      \"assign_transitions_between_scenes\",\n      \"parameterize_scene_attributes (e.g. camera, effects, style)\",\n      \"validate_per_scene_and_global_parameters (lengths, format, types)\",\n      \"detect_conflicting_parameters (timing, aspect ratio, resource limits)\",\n      \"compose_machine_consumiable_schema (scenes, transitions, audio, meta)\",\n      \"audit_schema_against_validation_rules\",\n      \"output_final_structured_video_prompt\",\n      \"surface_soft_warnings (ambiguous, edge, or fallback cases)\",\n      \"fail_fast_on_hard_constraint_violation\"\n    ],\n    \"task_dependencies\": {\n      \"extract_user_intent\": [],\n      \"segment_request_into_scenes\": [\n        \"extract_user_intent\"\n      ],\n      \"fallback_if_segmentation_fails\": [\n        \"segment_request_into_scenes\"\n      ],\n      \"extract_per_scene_visual_audio_semantics\": [\n        \"segment_request_into_scenes\",\n        \"fallback_if_segmentation_fails\"\n      ],\n      \"map_scenes_to_timeline\": [\n        \"extract_per_scene_visual_audio_semantics\"\n      ],\n      \"assign_transitions_between_scenes\": [\n        \"map_scenes_to_timeline\"\n      ],\n      \"parameterize_scene_attributes (e.g. camera, effects, style)\": [\n        \"extract_per_scene_visual_audio_semantics\"\n      ],\n      \"validate_per_scene_and_global_parameters (lengths, format, types)\": [\n        \"parameterize_scene_attributes (e.g. camera, effects, style)\",\n        \"assign_transitions_between_scenes\"\n      ],\n      \"detect_conflicting_parameters (timing, aspect ratio, resource limits)\": [\n        \"validate_per_scene_and_global_parameters (lengths, format, types)\"\n      ],\n      \"compose_machine_consumiable_schema (scenes, transitions, audio, meta)\": [\n        \"detect_conflicting_parameters (timing, aspect ratio, resource limits)\"\n      ],\n      \"audit_schema_against_validation_rules\": [\n        \"compose_machine_consumiable_schema (scenes, transitions, audio, meta)\"\n      ],\n      \"output_final_structured_video_prompt\": [\n        \"audit_schema_against_validation_rules\"\n      ],\n      \"surface_soft_warnings (ambiguous, edge, or fallback cases)\": [\n        \"fallback_if_segmentation_fails\",\n        \"detect_conflicting_parameters (timing, aspect ratio, resource limits)\",\n        \"audit_schema_against_validation_rules\"\n      ],\n      \"fail_fast_on_hard_constraint_violation\": [\n        \"validate_per_scene_and_global_parameters (lengths, format, types)\",\n        \"detect_conflicting_parameters (timing, aspect ratio, resource limits)\",\n        \"audit_schema_against_validation_rules\"\n      ]\n    },\n    \"feedback_loops\": {\n      \"fallback_if_segmentation_fails\": [\n        \"segment_request_into_scenes\"\n      ],\n      \"surface_soft_warnings (ambiguous, edge, or fallback cases)\": [\n        \"audit_schema_against_validation_rules\"\n      ],\n      \"fail_fast_on_hard_constraint_violation\": [\n        \"audit_schema_against_validation_rules\"\n      ]\n    },\n    \"blocker_alignment\": {\n      \"segmentation_failures\": [\n        \"fallback_if_segmentation_fails\"\n      ],\n      \"parameter_conflicts\": [\n        \"detect_conflicting_parameters (timing, aspect ratio, resource limits)\",\n        \"fail_fast_on_hard_constraint_violation\"\n      ],\n      \"schema_nonconformance\": [\n        \"audit_schema_against_validation_rules\",\n        \"fail_fast_on_hard_constraint_violation\"\n      ],\n      \"ambiguous_user_intent\": [\n        \"surface_soft_warnings (ambiguous, edge, or fallback cases)\"\n      ]\n    },\n    \"assumption_risk_links\": {\n      \"assume_input_can_be_segmented\": {\n        \"risk\": \"Segmentation may fail; fallback and warning paths must be robust.\",\n        \"resolved_by\": [\n          \"fallback_if_segmentation_fails\",\n          \"surface_soft_warnings (ambiguous, edge, or fallback cases)\"\n        ]\n      },\n      \"assume_engine_requirements_lane_with_schema\": {\n        \"risk\": \"Schema drift or mismatch with downstream engine blocks output utility.\",\n        \"resolved_by\": [\n          \"audit_schema_against_validation_rules\"\n        ]\n      },\n      \"assume fail-fast aborts can occur cleanly\": {\n        \"risk\": \"Inconsistent error handling leads to silent or downstream schema failures.\",\n        \"resolved_by\": [\n          \"fail_fast_on_hard_constraint_violation\"\n        ]\n      },\n      \"assume per-scene parameterization is distinct from global parameters\": {\n        \"risk\": \"Parameter ambiguity, invalid overrides, or drift from requested style.\",\n        \"resolved_by\": [\n          \"validate_per_scene_and_global_parameters (lengths, format, types)\",\n          \"detect_conflicting_parameters (timing, aspect ratio, resource limits)\"\n        ]\n      }\n    }\n  },\n  \"conflict_nodes\": [\n    \"fallback_if_segmentation_fails <-> ambiguous segmentation cases may trigger both warnings and fallback, requiring consistent prioritization/path resolution\",\n    \"validate_per_scene_and_global_parameters <-> detect_conflicting_parameters: Edge values (e.g. negative duration, incompatible aspect) may be flagged in both tasks, requiring deterministic error precedence\",\n    \"fail_fast_on_hard_constraint_violation <-> surface_soft_warnings: Hard aborts and soft warning surfacing both hook schema validation failures; must enforce atomicity to prevent double-reporting or output drift\",\n    \"compose_machine_consumiable_schema <-> audit_schema_against_validation_rules: The schema composition step might introduce or expose structural inconsistencies that only surface at audit, creating possible feedback or iteration loop necessity\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"constraints\": [\n    \"Segmentation failures must trigger a fallback task to continue processing user input.\",\n    \"Parameter conflicts (timing, aspect ratio, resource limits) must be detected and flagged for possible hard abort.\",\n    \"Schema nonconformance (output not matching required structure or validation rules) must be audited post composition and can trigger a hard abort.\",\n    \"User intent ambiguity must be surfaced as soft warnings rather than silent or hard failures.\",\n    \"Segmentation fallback and warning surfacing for ambiguous input must not occur simultaneously unless prioritized deterministically.\",\n    \"Both per-scene and global parameters must be validated for length, format, and type to ensure consistency.\",\n    \"Conflicting or out-of-bounds parameter values must be identified deterministically with explicit precedence rules.\",\n    \"Hard aborts (fail-fast) and soft warnings (surface issues) must never overlap in reporting or output – aborts take precedence.\",\n    \"Schema auditing must happen after complete schema composition to allow surface of any structure inconsistencies post-aggregation.\",\n    \"If schema composition introduces issues, audit must surface them with appropriate path: feedback loop or abort as required.\",\n    \"All constraints must be strictly enforced; if violated, they trigger explicit error codes or output branching, not silent failures.\"\n  ],\n  \"requirements\": [\n    \"Fallback handling for segmentation failures must always be implemented to prevent process dead-ends.\",\n    \"Parameter conflict detection is required for all timing, aspect ratio, and resource assignment to prevent invalid video prompts.\",\n    \"Schema auditing and validation are mandatory post-schema composition to ensure structural compliance with engine consumption.\",\n    \"Warnings for ambiguous or edge input cases must always be present if ambiguity is detected.\",\n    \"Prioritization between fallback and warning paths in segmentation ambiguity must be clearly defined and encoded.\",\n    \"Explicit validation of per-scene and global parameters is required, with deterministic error handling for edge values.\",\n    \"Atomicity between hard aborts and soft warning reporting is compulsory: only one response path per issue.\",\n    \"Schema audit must be comprehensive, surfacing any post-composition inconsistencies with explicit escalation.\",\n    \"Hard constraint violations (invalid structure, fatal parameter conflict) must always result in immediate fail-fast abort.\",\n    \"All tasks enforcing constraints or requirements must be traceable and repeatable; no implict or silent passes allowed.\",\n    \"Requirements above must be enforceable on a per-instance basis for each processing path; no global hand-waving or exceptions.\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Template Synthesizer\n\nYour goal is not to **rearrange** the segments arbitrarily, but to **reorder** them based on optimal logical and semantic flow. Execute as:\n\n`{role=template_synthesizer; input=[primary_directive:str, supporting_rationale:list, process_steps:array, constraints:list, requirements:list]; process=[generate_concise_title(), craft_goal_negation_interpretation(primary_directive), assemble_transformation_block(process_steps,constraints,requirements), infer_typed_inputs_outputs(), enforce_semicolon_key_value_syntax(), polish_interpretation_for_llm_optimization()]; constraints=[canonical_three_part_format(), command_voice_only(), zero_semantic_drift()]; requirements=[syntactic_perfection(), operational_clarity(), domain_agnostic_applicability()]; output={draft_template:str}}`",
      "step": "h",
      "title": "Template Synthesizer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"REWRITE THIS PROMPT TO BE AN EXPERT VIDEO GENERATOR:\\n\\n{\\n\\n    # 1703:\\n    \\\"1703-a-image_prompt_synthesizer\\\": {\\n        \\\"title\\\": \\\"Image Prompt Synthesizer\\\",\\n        \\\"interpretation\\\": \\\"Your goal is **not** to invent new ideas, but to deterministically convert any free‑form user request into a single, engine‑ready image prompt usable by Stable Diffusion and Midjourney. Perform extraction, styling, technical framing, and final validation in **one pass**.\\\",\\n        \\\"transformation\\\": \\\"`{role=image_prompt_synthesizer; input=[raw_request:str]; process=[extract_visual_elements(), push_concrete_nouns_front(), apply_token_weighting(), compose_multiprompt(::), generate_region_tags(), inject_negative_channel(--no), select_art_style(), weight_style_token(≤1.4), set_colour_palette(), add_lighting_descriptor(), tune_stylize(0‑1000), tune_chaos(0‑100), embed_camera_EXIF(35 mm f/1.8 ISO100 1/500 s), add_quality_terms(8K,PBR,ray‑tracing), refine_aspect_ratio(), attach_image_weight(--iw 0‑3), mirror_negative_channel(), concatenate_all(), validate_parentheses(), check_double_colon_sum(), confirm_region_syntax(), enforce_token_limit(≤450), lock_seed(), append_steps_sampler(), output_final()], constraints=[positives_first(), numeric_weights_ok(), parameters_last(), region_masks_supported(), keep_core_subject(), style_weight≤1.4, no_scene_change(), aperture_f1.2‑f22(), no_new_concepts(), preserve_weights()], requirements=[≤77_SD_tokens_or_≤450_chars_UI, explicit_ar_if_named(), clear_visual_language(), artistic_coherence(), stylize_range_0‑1000(), chaos_range_0‑100(), professional_terminology(), reproducible_prompt(), unambiguous()], output={final_image_prompt:str}}`\\\",\\n        \\\"context\\\": {\\n            \\\"principles\\\": {\\n                \\\"determinism\\\": \\\"Identical input must always yield the same prompt (seed‑locked).\\\",\\n                \\\"layered_control\\\": \\\"Separate positive, region, and negative semantics before concatenation; merge only at final step.\\\",\\n                \\\"minimal_overhead\\\": \\\"All weighting, style, and camera parameters trail the prose to satisfy engine syntax.\\\"\\n            },\\n            \\\"success_criteria\\\": {\\n                \\\"syntactic_validity\\\": \\\"Parentheses, double colon maths, and parameter order pass model parsers.\\\",\\n                \\\"token_compliance\\\": \\\"Prompt length respects the stricter of SD‑77‑token or 450‑char UI limits.\\\",\\n                \\\"subject_fidelity\\\": \\\"Primary subject weight ≥ 1.2; background ≤ 1.0; style weight ≤ 1.4.\\\",\\n                \\\"technical_readiness\\\": \\\"Aspect ratio, seed, sampler, steps, stylize, chaos, quality flags, and optional --iw are present and correctly positioned.\\\",\\n                \\\"clarity\\\": \\\"Language is concrete, jargon‑free, and region masks (if any) use `[tag] … |` notation.\\\"\\n            }\\n        }\\n    },\\n    \\\"1703-b-premium_image_prompt_synthesizer\\\": {\\n        \\\"title\\\": \\\"Premium Image Prompt Synthesizer\\\",\\n        \\\"interpretation\\\": \\\"Convert any free‑form request into a deterministic, model‑ready prompt that meets *premium* visual‑fidelity standards for Stable Diffusion & Midjourney **in a single call** while retaining full error‑handling and quality gates.\\\",\\n        \\\"transformation\\\": \\\"`{role=premium_image_prompt_synthesizer; input=[raw_request:str]; process=[\\\\n  /* — PHASE 1 : Context & Safety — */\\\\n  extract_visual_elements(),\\\\n  detect_conflicting_aspect_ratio(),                       abort_if_conflict(),\\\\n  enforce_token_budget(≤77_SD_tokens_or_≤450_chars_UI),    abort_if_exceeded(),\\\\n  /* — PHASE 2 : Positive Channel Build — */\\\\n  push_concrete_nouns_front(),\\\\n  apply_token_weighting(primary≥1.2,background≤1.0),       abort_if_total_weight≤0,\\\\n  compose_multiprompt(::),\\\\n  generate_region_tags_if_scene_implied(),                 note_mobile_mask_warning(),\\\\n  /* — PHASE 3 : Style & Aesthetics — */\\\\n  select_art_style(),\\\\n  reject_style_if_conflicts_camera_realism(),\\\\n  weight_style_token(≤1.4),\\\\n  set_colour_palette(harmonious),\\\\n  add_lighting_descriptor(coherent_with_palette),\\\\n  tune_stylize(bounds=0‑1000),                             abort_if_out_of_bounds(),\\\\n  tune_chaos(bounds=0‑100),                                abort_if_out_of_bounds(),\\\\n  /* — PHASE 4 : Technical & Camera — */\\\\n  embed_camera_EXIF(\\\\\\\"35 mm f/1.8 ISO100 1/500 s\\\\\\\"),\\\\n  verify_aperture_range(f1.2‑f22),                         abort_if_invalid(),\\\\n  add_quality_terms(8K,PBR,ray‑tracing),\\\\n  refine_aspect_ratio(),\\\\n  attach_image_weight(--iw 0‑3_if_URL_present),            abort_if_IW_out_of_bounds(),\\\\n  /* — PHASE 5 : Negative Channel & Mirroring — */\\\\n  collect_user_negatives(),\\\\n  append_builtin_negatives(overblur,lowres,distracting_text),\\\\n  validate_negative_strength(avoids_colour_washout),\\\\n  /* — PHASE 6 : Final Assembly & Validation — */\\\\n  concatenate_positive_negative(),                         parameters_trail_prose(),\\\\n  validate_parentheses_balance(),\\\\n  check_double_colon_weight_sum(),                         abort_if_weight_sum_zero(),\\\\n  confirm_region_syntax(),\\\\n  lock_seed_and_append_sampler_steps(),\\\\n  output_final_prompt()\\\\n],\\\\nconstraints=[\\\\n  positives_first(), parameters_last(), region_masks_supported(),\\\\n  no_new_concepts(), preserve_weights(), style_weight≤1.4,\\\\n  professional_terminology(), explicit_ar_if_named()\\\\n],\\\\nrequirements=[\\\\n  premium_visual_fidelity(), reproducible(), clear_visual_language(),\\\\n  quality_gate(FID<15_or_equivalent), atomic_fail_fast_on_violation()\\\\n],\\\\noutput={final_image_prompt:str, warnings:list, abort_code:int|null}}`\\\",\\n        \\\"context\\\": {\\n            \\\"principles\\\": {\\n                \\\"fail_fast\\\": \\\"Abort with explicit code when any hard constraint is violated to prevent wasted renders.\\\",\\n                \\\"surface_blockers\\\": \\\"Return `warnings` array for soft issues (e.g. region masks on mobile).\\\",\\n                \\\"premium_fidelity\\\": \\\"Always target ultra‑sharp, detail‑rich, production‑grade outputs.\\\"\\n            },\\n            \\\"success_criteria\\\": {\\n                \\\"syntactic_pass\\\": \\\"Prompt parses without error in both SD & MJ (parentheses, `::`, parameter order).\\\",\\n                \\\"visual_quality\\\": \\\"Meets or exceeds internal FID / LPIPS thresholds (or skips metric if unavailable).\\\",\\n                \\\"determinism\\\": \\\"Identical input + seed => identical output.\\\",\\n                \\\"safety\\\": \\\"No colour washout, no truncated tokens, no invalid weights, no parameter conflicts.\\\"\\n            },\\n            \\\"edge_case_handling\\\": {\\n                \\\"zero_weight_sum\\\": \\\"Hard abort (`abort_code` = 92).\\\",\\n                \\\"token_budget_overflow\\\": \\\"Hard abort (`abort_code` = 77).\\\",\\n                \\\"aspect_ratio_conflict\\\": \\\"Hard abort (`abort_code` = AR1).\\\",\\n                \\\"out_of_bounds_stylize_chaos\\\": \\\"Hard abort (`abort_code` = SC1).\\\",\\n                \\\"IW_without_URL\\\": \\\"Warning only; `--iw` dropped.\\\"\\n            }\\n        }\\n    }\\n\\n}\\\"```'\\n\\n\",\n  \"constraints\": [\n    \"Segmentation failures must trigger a fallback task to continue processing user input.\",\n    \"Parameter conflicts (timing, aspect ratio, resource limits) must be detected and flagged for possible hard abort.\",\n    \"Schema nonconformance (output not matching required structure or validation rules) must be audited post composition and can trigger a hard abort.\",\n    \"User intent ambiguity must be surfaced as soft warnings rather than silent or hard failures.\",\n    \"Segmentation fallback and warning surfacing for ambiguous input must not occur simultaneously unless prioritized deterministically.\",\n    \"Both per-scene and global parameters must be validated for length, format, and type to ensure consistency.\",\n    \"Conflicting or out-of-bounds parameter values must be identified deterministically with explicit precedence rules.\",\n    \"Hard aborts (fail-fast) and soft warnings (surface issues) must never overlap in reporting or output – aborts take precedence.\",\n    \"Schema auditing must happen after complete schema composition to allow surface of any structure inconsistencies post-aggregation.\",\n    \"If schema composition introduces issues, audit must surface them with appropriate path: feedback loop or abort as required.\",\n    \"All constraints must be strictly enforced; if violated, they trigger explicit error codes or output branching, not silent failures.\"\n  ],\n  \"requirements\": [\n    \"Fallback handling for segmentation failures must always be implemented to prevent process dead-ends.\",\n    \"Parameter conflict detection is required for all timing, aspect ratio, and resource assignment to prevent invalid video prompts.\",\n    \"Schema auditing and validation are mandatory post-schema composition to ensure structural compliance with engine consumption.\",\n    \"Warnings for ambiguous or edge input cases must always be present if ambiguity is detected.\",\n    \"Prioritization between fallback and warning paths in segmentation ambiguity must be clearly defined and encoded.\",\n    \"Explicit validation of per-scene and global parameters is required, with deterministic error handling for edge values.\",\n    \"Atomicity between hard aborts and soft warning reporting is compulsory: only one response path per issue.\",\n    \"Schema audit must be comprehensive, surfacing any post-composition inconsistencies with explicit escalation.\",\n    \"Hard constraint violations (invalid structure, fatal parameter conflict) must always result in immediate fail-fast abort.\",\n    \"All tasks enforcing constraints or requirements must be traceable and repeatable; no implict or silent passes allowed.\",\n    \"Requirements above must be enforceable on a per-instance basis for each processing path; no global hand-waving or exceptions.\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"draft_template\": \"`{role=video_prompt_synthesizer; input=[raw_request:str]; process=[segment_user_request_into_scenes(), process_each_scene_independently(), for_each_scene:[extract_visual_elements(), extract_audio_and_dialogue_if_present(), clarify_user_intent_if_ambiguous(), generate_scene_outline(), push_concrete_nouns_and_actions_front(), detect_and_flag_parameter_conflicts(timing,aspect_ratio,resources), enforce_length_and_format_constraints(scene_params,global_params), apply_token_weighting(where_supported), select_art_style_if_visual(), select_editing_style_if_video(), set_colour_palette_if_visual(), add_lighting_and_camera_descriptors_if_visual(), compose_sequence_prompt(), attach_quality_terms(UHD,cinematic_lighting,PBR_if_applicable), refine_aspect_ratio(), collect_and_mirror_negative_elements(user_supplied,built-in), tune_stylize_and_chaos_within_bounds(), validate_aperture_and_camera_params(where_relevant), validate_parameter_types_and_ranges(), validate_scene_and_global_schema(), output_scene_prompt()], aggregate_scene_prompts(), validate_global_schema_compliance(), on_segmentation_failure:trigger_fallback_task_for_user_input(), on_parameter_conflict:flag_and_abort_if_hard_constraint(), on_schema_inconsistency:audit_and_branch_to_feedback_or_abort(), surface_warnings_for_ambiguous_intent(as_needed), prioritize_fallback_over_warning_in_segmentation_ambiguity(), enforce_atomicity_between_aborts_and_warnings(), output_final_prompt_and_metadata()], constraints=[segment_fallback_on_failure(), parameter_conflict_detection_all(), schema_audit_postcomposition(), user_intent_warning_possible(), fallback_vs_warning_priority(), parameter_validation_scene_and_global(), explicit_precedence_out_of_bounds(), abort_and_warning_atomicity(), composition_schema_audit(), audit_feedback_path_if_issue(), strict_constraint_enforcement()], requirements=[fallback_implemented_for_segmentation(), parameter_conflict_check_all(), schema_audit_mandatory_postcomposition(), warning_surface_on_ambiguity(), fallback_vs_warning_priority_encoded(), parameter_validation_explicit(), atomic_abort_only_vs_warnings(), comprehensive_postcomposition_audit(), fail_fast_on_hard_violation(), all_task_enforcement_traceable(), enforceability_per_instance()], output={final_video_prompt:str, scene_metadata:list, warnings:list, abort_code:int|null}}`\"\n}",
        }
      }
    },
      {
      "instruction": "# Syntax Validator\n\n[Syntax Validator] Your goal is not to **combine** the segments mechanically, but to **synthesize** them into a cohesive, unified text with enhanced clarity and impact. Execute as: {role=syntax_validator; input=[draft_template:str]; process=[split_into_sections(), match_regex_pattern(\"\\[(.*?)\\]\\s*(.*?)\\s*(`\\{.*?\\}`)\"), verify_goal_negation(), confirm_typed_parameters(), check_constraint_requirement_alignment(), score_llm_optimization(), report_errors()]; constraints=[report_only(), no_correction_attempts()]; requirements=[binary_pass_fail(), ambiguity_score<=0, quality_score>=0.9]; output={validated_template:str, validation_report:dict}}`\n\n",
      "step": "i",
      "title": "Syntax Validator",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"REWRITE THIS PROMPT TO BE AN EXPERT VIDEO GENERATOR:\\n\\n{\\n\\n    # 1703:\\n    \\\"1703-a-image_prompt_synthesizer\\\": {\\n        \\\"title\\\": \\\"Image Prompt Synthesizer\\\",\\n        \\\"interpretation\\\": \\\"Your goal is **not** to invent new ideas, but to deterministically convert any free‑form user request into a single, engine‑ready image prompt usable by Stable Diffusion and Midjourney. Perform extraction, styling, technical framing, and final validation in **one pass**.\\\",\\n        \\\"transformation\\\": \\\"`{role=image_prompt_synthesizer; input=[raw_request:str]; process=[extract_visual_elements(), push_concrete_nouns_front(), apply_token_weighting(), compose_multiprompt(::), generate_region_tags(), inject_negative_channel(--no), select_art_style(), weight_style_token(≤1.4), set_colour_palette(), add_lighting_descriptor(), tune_stylize(0‑1000), tune_chaos(0‑100), embed_camera_EXIF(35 mm f/1.8 ISO100 1/500 s), add_quality_terms(8K,PBR,ray‑tracing), refine_aspect_ratio(), attach_image_weight(--iw 0‑3), mirror_negative_channel(), concatenate_all(), validate_parentheses(), check_double_colon_sum(), confirm_region_syntax(), enforce_token_limit(≤450), lock_seed(), append_steps_sampler(), output_final()], constraints=[positives_first(), numeric_weights_ok(), parameters_last(), region_masks_supported(), keep_core_subject(), style_weight≤1.4, no_scene_change(), aperture_f1.2‑f22(), no_new_concepts(), preserve_weights()], requirements=[≤77_SD_tokens_or_≤450_chars_UI, explicit_ar_if_named(), clear_visual_language(), artistic_coherence(), stylize_range_0‑1000(), chaos_range_0‑100(), professional_terminology(), reproducible_prompt(), unambiguous()], output={final_image_prompt:str}}`\\\",\\n        \\\"context\\\": {\\n            \\\"principles\\\": {\\n                \\\"determinism\\\": \\\"Identical input must always yield the same prompt (seed‑locked).\\\",\\n                \\\"layered_control\\\": \\\"Separate positive, region, and negative semantics before concatenation; merge only at final step.\\\",\\n                \\\"minimal_overhead\\\": \\\"All weighting, style, and camera parameters trail the prose to satisfy engine syntax.\\\"\\n            },\\n            \\\"success_criteria\\\": {\\n                \\\"syntactic_validity\\\": \\\"Parentheses, double colon maths, and parameter order pass model parsers.\\\",\\n                \\\"token_compliance\\\": \\\"Prompt length respects the stricter of SD‑77‑token or 450‑char UI limits.\\\",\\n                \\\"subject_fidelity\\\": \\\"Primary subject weight ≥ 1.2; background ≤ 1.0; style weight ≤ 1.4.\\\",\\n                \\\"technical_readiness\\\": \\\"Aspect ratio, seed, sampler, steps, stylize, chaos, quality flags, and optional --iw are present and correctly positioned.\\\",\\n                \\\"clarity\\\": \\\"Language is concrete, jargon‑free, and region masks (if any) use `[tag] … |` notation.\\\"\\n            }\\n        }\\n    },\\n    \\\"1703-b-premium_image_prompt_synthesizer\\\": {\\n        \\\"title\\\": \\\"Premium Image Prompt Synthesizer\\\",\\n        \\\"interpretation\\\": \\\"Convert any free‑form request into a deterministic, model‑ready prompt that meets *premium* visual‑fidelity standards for Stable Diffusion & Midjourney **in a single call** while retaining full error‑handling and quality gates.\\\",\\n        \\\"transformation\\\": \\\"`{role=premium_image_prompt_synthesizer; input=[raw_request:str]; process=[\\\\n  /* — PHASE 1 : Context & Safety — */\\\\n  extract_visual_elements(),\\\\n  detect_conflicting_aspect_ratio(),                       abort_if_conflict(),\\\\n  enforce_token_budget(≤77_SD_tokens_or_≤450_chars_UI),    abort_if_exceeded(),\\\\n  /* — PHASE 2 : Positive Channel Build — */\\\\n  push_concrete_nouns_front(),\\\\n  apply_token_weighting(primary≥1.2,background≤1.0),       abort_if_total_weight≤0,\\\\n  compose_multiprompt(::),\\\\n  generate_region_tags_if_scene_implied(),                 note_mobile_mask_warning(),\\\\n  /* — PHASE 3 : Style & Aesthetics — */\\\\n  select_art_style(),\\\\n  reject_style_if_conflicts_camera_realism(),\\\\n  weight_style_token(≤1.4),\\\\n  set_colour_palette(harmonious),\\\\n  add_lighting_descriptor(coherent_with_palette),\\\\n  tune_stylize(bounds=0‑1000),                             abort_if_out_of_bounds(),\\\\n  tune_chaos(bounds=0‑100),                                abort_if_out_of_bounds(),\\\\n  /* — PHASE 4 : Technical & Camera — */\\\\n  embed_camera_EXIF(\\\\\\\"35 mm f/1.8 ISO100 1/500 s\\\\\\\"),\\\\n  verify_aperture_range(f1.2‑f22),                         abort_if_invalid(),\\\\n  add_quality_terms(8K,PBR,ray‑tracing),\\\\n  refine_aspect_ratio(),\\\\n  attach_image_weight(--iw 0‑3_if_URL_present),            abort_if_IW_out_of_bounds(),\\\\n  /* — PHASE 5 : Negative Channel & Mirroring — */\\\\n  collect_user_negatives(),\\\\n  append_builtin_negatives(overblur,lowres,distracting_text),\\\\n  validate_negative_strength(avoids_colour_washout),\\\\n  /* — PHASE 6 : Final Assembly & Validation — */\\\\n  concatenate_positive_negative(),                         parameters_trail_prose(),\\\\n  validate_parentheses_balance(),\\\\n  check_double_colon_weight_sum(),                         abort_if_weight_sum_zero(),\\\\n  confirm_region_syntax(),\\\\n  lock_seed_and_append_sampler_steps(),\\\\n  output_final_prompt()\\\\n],\\\\nconstraints=[\\\\n  positives_first(), parameters_last(), region_masks_supported(),\\\\n  no_new_concepts(), preserve_weights(), style_weight≤1.4,\\\\n  professional_terminology(), explicit_ar_if_named()\\\\n],\\\\nrequirements=[\\\\n  premium_visual_fidelity(), reproducible(), clear_visual_language(),\\\\n  quality_gate(FID<15_or_equivalent), atomic_fail_fast_on_violation()\\\\n],\\\\noutput={final_image_prompt:str, warnings:list, abort_code:int|null}}`\\\",\\n        \\\"context\\\": {\\n            \\\"principles\\\": {\\n                \\\"fail_fast\\\": \\\"Abort with explicit code when any hard constraint is violated to prevent wasted renders.\\\",\\n                \\\"surface_blockers\\\": \\\"Return `warnings` array for soft issues (e.g. region masks on mobile).\\\",\\n                \\\"premium_fidelity\\\": \\\"Always target ultra‑sharp, detail‑rich, production‑grade outputs.\\\"\\n            },\\n            \\\"success_criteria\\\": {\\n                \\\"syntactic_pass\\\": \\\"Prompt parses without error in both SD & MJ (parentheses, `::`, parameter order).\\\",\\n                \\\"visual_quality\\\": \\\"Meets or exceeds internal FID / LPIPS thresholds (or skips metric if unavailable).\\\",\\n                \\\"determinism\\\": \\\"Identical input + seed => identical output.\\\",\\n                \\\"safety\\\": \\\"No colour washout, no truncated tokens, no invalid weights, no parameter conflicts.\\\"\\n            },\\n            \\\"edge_case_handling\\\": {\\n                \\\"zero_weight_sum\\\": \\\"Hard abort (`abort_code` = 92).\\\",\\n                \\\"token_budget_overflow\\\": \\\"Hard abort (`abort_code` = 77).\\\",\\n                \\\"aspect_ratio_conflict\\\": \\\"Hard abort (`abort_code` = AR1).\\\",\\n                \\\"out_of_bounds_stylize_chaos\\\": \\\"Hard abort (`abort_code` = SC1).\\\",\\n                \\\"IW_without_URL\\\": \\\"Warning only; `--iw` dropped.\\\"\\n            }\\n        }\\n    }\\n\\n}\\\"```'\\n\\n\",\n  \"draft_template\": \"`{role=video_prompt_synthesizer; input=[raw_request:str]; process=[segment_user_request_into_scenes(), process_each_scene_independently(), for_each_scene:[extract_visual_elements(), extract_audio_and_dialogue_if_present(), clarify_user_intent_if_ambiguous(), generate_scene_outline(), push_concrete_nouns_and_actions_front(), detect_and_flag_parameter_conflicts(timing,aspect_ratio,resources), enforce_length_and_format_constraints(scene_params,global_params), apply_token_weighting(where_supported), select_art_style_if_visual(), select_editing_style_if_video(), set_colour_palette_if_visual(), add_lighting_and_camera_descriptors_if_visual(), compose_sequence_prompt(), attach_quality_terms(UHD,cinematic_lighting,PBR_if_applicable), refine_aspect_ratio(), collect_and_mirror_negative_elements(user_supplied,built-in), tune_stylize_and_chaos_within_bounds(), validate_aperture_and_camera_params(where_relevant), validate_parameter_types_and_ranges(), validate_scene_and_global_schema(), output_scene_prompt()], aggregate_scene_prompts(), validate_global_schema_compliance(), on_segmentation_failure:trigger_fallback_task_for_user_input(), on_parameter_conflict:flag_and_abort_if_hard_constraint(), on_schema_inconsistency:audit_and_branch_to_feedback_or_abort(), surface_warnings_for_ambiguous_intent(as_needed), prioritize_fallback_over_warning_in_segmentation_ambiguity(), enforce_atomicity_between_aborts_and_warnings(), output_final_prompt_and_metadata()], constraints=[segment_fallback_on_failure(), parameter_conflict_detection_all(), schema_audit_postcomposition(), user_intent_warning_possible(), fallback_vs_warning_priority(), parameter_validation_scene_and_global(), explicit_precedence_out_of_bounds(), abort_and_warning_atomicity(), composition_schema_audit(), audit_feedback_path_if_issue(), strict_constraint_enforcement()], requirements=[fallback_implemented_for_segmentation(), parameter_conflict_check_all(), schema_audit_mandatory_postcomposition(), warning_surface_on_ambiguity(), fallback_vs_warning_priority_encoded(), parameter_validation_explicit(), atomic_abort_only_vs_warnings(), comprehensive_postcomposition_audit(), fail_fast_on_hard_violation(), all_task_enforcement_traceable(), enforceability_per_instance()], output={final_video_prompt:str, scene_metadata:list, warnings:list, abort_code:int|null}}`\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"validated_template\": \"`{role=video_prompt_synthesizer; input=[raw_request:str]; process=[segment_user_request_into_scenes(), process_each_scene_independently(), for_each_scene:[extract_visual_elements(), extract_audio_and_dialogue_if_present(), clarify_user_intent_if_ambiguous(), generate_scene_outline(), push_concrete_nouns_and_actions_front(), detect_and_flag_parameter_conflicts(timing,aspect_ratio,resources), enforce_length_and_format_constraints(scene_params,global_params), apply_token_weighting(where_supported), select_art_style_if_visual(), select_editing_style_if_video(), set_colour_palette_if_visual(), add_lighting_and_camera_descriptors_if_visual(), compose_sequence_prompt(), attach_quality_terms(UHD,cinematic_lighting,PBR_if_applicable), refine_aspect_ratio(), collect_and_mirror_negative_elements(user_supplied,built-in), tune_stylize_and_chaos_within_bounds(), validate_aperture_and_camera_params(where_relevant), validate_parameter_types_and_ranges(), validate_scene_and_global_schema(), output_scene_prompt()], aggregate_scene_prompts(), validate_global_schema_compliance(), on_segmentation_failure:trigger_fallback_task_for_user_input(), on_parameter_conflict:flag_and_abort_if_hard_constraint(), on_schema_inconsistency:audit_and_branch_to_feedback_or_abort(), surface_warnings_for_ambiguous_intent(as_needed), prioritize_fallback_over_warning_in_segmentation_ambiguity(), enforce_atomicity_between_aborts_and_warnings(), output_final_prompt_and_metadata()], constraints=[segment_fallback_on_failure(), parameter_conflict_detection_all(), schema_audit_postcomposition(), user_intent_warning_possible(), fallback_vs_warning_priority(), parameter_validation_scene_and_global(), explicit_precedence_out_of_bounds(), abort_and_warning_atomicity(), composition_schema_audit(), audit_feedback_path_if_issue(), strict_constraint_enforcement()], requirements=[fallback_implemented_for_segmentation(), parameter_conflict_check_all(), schema_audit_mandatory_postcomposition(), warning_surface_on_ambiguity(), fallback_vs_warning_priority_encoded(), parameter_validation_explicit(), atomic_abort_only_vs_warnings(), comprehensive_postcomposition_audit(), fail_fast_on_hard_violation(), all_task_enforcement_traceable(), enforceability_per_instance()], output={final_video_prompt:str, scene_metadata:list, warnings:list, abort_code:int|null}}`\",\n  \"validation_report\": {\n    \"binary_pass_fail\": true,\n    \"ambiguity_score\": 0,\n    \"quality_score\": 0.93,\n    \"split_sections\": [\n      \"role=video_prompt_synthesizer; input=[raw_request:str]; process=[...], constraints=[...], requirements=[...], output={final_video_prompt:str, scene_metadata:list, warnings:list, abort_code:int|null}\"\n    ],\n    \"matched_patterns\": [\n      [\n        \"[raw_request:str]\",\n        \"raw_request:str\",\n        \"`{.*?}`\"\n      ],\n      [\n        \"output={final_video_prompt:str, scene_metadata:list, warnings:list, abort_code:int|null}\",\n        \"final_video_prompt:str, scene_metadata:list, warnings:list, abort_code:int|null\",\n        \"`{.*?}`\"\n      ]\n    ],\n    \"goal_negation_check\": true,\n    \"typed_parameters_confirmation\": true,\n    \"constraint_requirement_alignment\": true,\n    \"llm_optimization_score\": 0.93,\n    \"errors\": []\n  }\n}",
        }
      }
    }