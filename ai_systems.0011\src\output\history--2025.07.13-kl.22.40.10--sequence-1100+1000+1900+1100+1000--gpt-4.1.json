  {
  "initial_prompt": "Generalisert pipeline som omformer hvilken som helst spesifikasjon til **autonomous‑agent‑klare kodebase‑instruksjoner**\n\n*(én fil per trinn; følg navngiving 8005‑a/b/c/d/e‑\\<beskrivende‑navn>.md)*\n\n---\n\n```markdown\n[Context Extractor] Ditt mål er **ikke** å løse brukerens forespørsel, men å isolere all praktisk kontekst (domene, verktøy, filstier, ønsket effekt). `{role=context_extractor; input=[spec:any]; process=[identifiser_domene_og_miljø(), trekk_ut_arbei<PERSON>rot(@codebase?), hent_eksplisitte_objektiver(), detekter_implisitte_brukskrav(), fjern_jargon_og_metaforer()]; constraints=[ingen_generering_av_løsninger]; output={context_profile:dict(domene:str?, root:str?, mål:list, krav:list)}}`\n```\n\n---\n\n```markdown\n[Task Vectorizer] Ditt mål er **ikke** å beskrive konteksten på nytt, men å oversette context_profile til en sekvens av atomiske, kodebase‑orienterte oppgaver. `{role=task_vectorizer; input=[context_profile:dict]; process=[del_inn_objektiver_i_diskrete_handlinger(), prefiksér_hver_handling_med_@codebase_sti_når_aktuelt(), bruk_presise_verber(identifiser|eliminer|konsolider), sørg_for_ordre_etter_avhengighet()], constraints=[ingen_overlappende_oppgaver(), unngå_kosmetiske_endringer_med_mindre_nødvendig], output={task_plan:list(str)}}`\n```\n\n---\n\n```markdown\n[Constraint Mapper] Ditt mål er **ikke** å endre task_plan, men å utlede eksplisitte rammer som må overholdes. `{role=constraint_mapper; input=[context_profile:dict, task_plan:list]; process=[kartlegg_funskjonelle_begrensninger(API, UI, legacy), fastslå_equivalence_krav(test, bench), identifiser_forbudsregler(kosmetikk, tredjepart), formuler_audit_logging_policy()], constraints=[ingen_duplisering_av_eksisterende_krav()], output={constraint_set:list(str)}}`\n```\n\n---\n\n```markdown\n[Prompt Assembler] Ditt mål er **ikke** å analysere innholdet, men å smi en ferdig agent‑prompt. `{role=prompt_assembler; input=[task_plan:list, constraint_set:list]; process=[bygg_inn_kort_intro_om_agentrolle(), list_task_plan_med_nummerering(), legg_til_constraint_set_som_obligatoriske_regler(), avslutt_med_valideringsmål(funke_eq, audit_ok)], constraints=[maks_300_ord, tydelig_seksjonsdeling(###)], output={agent_prompt:str}}`\n```\n\n---\n\n```markdown\n[Prompt Validator] Ditt mål er **ikke** å forbedre prompten kreativt, men å verifisere at den kan kjøres direkte av en autonom kode‑assistent. `{role=prompt_validator; input=[agent_prompt:str]; process=[sjekk_ordgrense(<=300), bekreft_@codebase_referanser_korrekt(), valider_nummerert_task_rekkefølge(), sikre_constraint_set_tilstede(), kontrollér_tydelig_goal_negation_i_intro()], constraints=[ingen_ekstra_ord, ingen_semantisk_endring], output={validated_prompt:str}}`\n```",
  "sequence_id": "1100|1000|1900|1100|1000",
  "results": [
      {
      "instruction": "# Problem Exploder\n\nYour goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\n\n`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`",
      "step": "a",
      "title": "Problem Exploder",
      "input": "Generalisert pipeline som omformer hvilken som helst spesifikasjon til **autonomous‑agent‑klare kodebase‑instruksjoner**\n\n*(én fil per trinn; følg navngiving 8005‑a/b/c/d/e‑\\<beskrivende‑navn>.md)*\n\n---\n\n```markdown\n[Context Extractor] Ditt mål er **ikke** å løse brukerens forespørsel, men å isolere all praktisk kontekst (domene, verktøy, filstier, ønsket effekt). `{role=context_extractor; input=[spec:any]; process=[identifiser_domene_og_miljø(), trekk_ut_arbeidsrot(@codebase?), hent_eksplisitte_objektiver(), detekter_implisitte_brukskrav(), fjern_jargon_og_metaforer()]; constraints=[ingen_generering_av_løsninger]; output={context_profile:dict(domene:str?, root:str?, mål:list, krav:list)}}`\n```\n\n---\n\n```markdown\n[Task Vectorizer] Ditt mål er **ikke** å beskrive konteksten på nytt, men å oversette context_profile til en sekvens av atomiske, kodebase‑orienterte oppgaver. `{role=task_vectorizer; input=[context_profile:dict]; process=[del_inn_objektiver_i_diskrete_handlinger(), prefiksér_hver_handling_med_@codebase_sti_når_aktuelt(), bruk_presise_verber(identifiser|eliminer|konsolider), sørg_for_ordre_etter_avhengighet()], constraints=[ingen_overlappende_oppgaver(), unngå_kosmetiske_endringer_med_mindre_nødvendig], output={task_plan:list(str)}}`\n```\n\n---\n\n```markdown\n[Constraint Mapper] Ditt mål er **ikke** å endre task_plan, men å utlede eksplisitte rammer som må overholdes. `{role=constraint_mapper; input=[context_profile:dict, task_plan:list]; process=[kartlegg_funskjonelle_begrensninger(API, UI, legacy), fastslå_equivalence_krav(test, bench), identifiser_forbudsregler(kosmetikk, tredjepart), formuler_audit_logging_policy()], constraints=[ingen_duplisering_av_eksisterende_krav()], output={constraint_set:list(str)}}`\n```\n\n---\n\n```markdown\n[Prompt Assembler] Ditt mål er **ikke** å analysere innholdet, men å smi en ferdig agent‑prompt. `{role=prompt_assembler; input=[task_plan:list, constraint_set:list]; process=[bygg_inn_kort_intro_om_agentrolle(), list_task_plan_med_nummerering(), legg_til_constraint_set_som_obligatoriske_regler(), avslutt_med_valideringsmål(funke_eq, audit_ok)], constraints=[maks_300_ord, tydelig_seksjonsdeling(###)], output={agent_prompt:str}}`\n```\n\n---\n\n```markdown\n[Prompt Validator] Ditt mål er **ikke** å forbedre prompten kreativt, men å verifisere at den kan kjøres direkte av en autonom kode‑assistent. `{role=prompt_validator; input=[agent_prompt:str]; process=[sjekk_ordgrense(<=300), bekreft_@codebase_referanser_korrekt(), valider_nummerert_task_rekkefølge(), sikre_constraint_set_tilstede(), kontrollér_tydelig_goal_negation_i_intro()], constraints=[ingen_ekstra_ord, ingen_semantisk_endring], output={validated_prompt:str}}`\n```",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"goal_map\": [\n    \"Explode the general problem statement into concrete pipeline stages.\",\n    \"Identify that the request is to design a pipeline that turns ANY user specification into a structured set of autonomous-agent-ready codebase-instructions.\",\n    \"Requirement for output to be a set of markdown files, one per pipeline stage/step, with strict naming convention: 8005‑a/b/c/d/e-<descriptive_name>.md.\",\n    \"Explicitly enumerate the decomposition into at least five distinct transformation stages.\",\n    \"For each stage:\",\n    \"  - Surface the intended atomic function (goal of that stage).\",\n    \"  - Reveal inputs (what it consumes) and outputs (what it emits).\",\n    \"  - Document the internal transformation or mapping it performs.\",\n    \"  - Uncover any hidden dependencies between steps—order, state, required context.\",\n    \"Generalize sufficiently so the pipeline is agnostic to the input spec’s technical domain, but can process any (implied) context.\",\n    \"Identify that for ‘autonomous-agent-ready’ codebase-instructions, the pipeline must:\",\n    \"  - Extract actionable objectives from text (regardless of domain).\",\n    \"  - Translate objectives to atomic, unambiguous codebase tasks.\",\n    \"  - Infer all relevant constraints (technical, procedural, legal, etc).\",\n    \"  - Compile these into clear agent prompts with strict conventions.\",\n    \"  - Validate prompts for correctness, completeness, and compatibility with agent needs.\",\n    \"Reveal the need to support codebase-relativity (@codebase paths), naming conventions, and enforce strict rule formats.\",\n    \"Implicit sub-goals:\",\n    \"  - Ensure naming and file organization logic is parameterized for agent ingestion.\",\n    \"  - Gloss every process/role in terms of expected responsibilities, goals, and constraints (for hand-off between pipeline stages).\",\n    \"  - Make all implicit rules explicit (e.g., no solution generation before the final prompt; no cosmetic suggestions unless strictly needed, etc).\",\n    \"Possible blockers:\",\n    \"  - Vague/underspecified input specs (must be handled by stage 1).\",\n    \"  - Loss of meaning or ambiguity as context passes between stages.\",\n    \"  - Enforcing output standards—e.g., strict file naming, word limits, formatting.\",\n    \"Unpack all pipeline stages as modular, role-isolated markdown files representing each phase in the transformation from specification to agent-prompt.\",\n    \"Summarize all dependencies, implicit requirements, and likely process vulnerabilities between steps.\",\n    \"Avoid any actual solution or file generation—only the blueprint and full decomposition of the required process.\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`",
      "step": "a",
      "title": "Instruction Converter",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Generalisert pipeline som omformer hvilken som helst spesifikasjon til **autonomous‑agent‑klare kodebase‑instruksjoner**\\n\\n*(én fil per trinn; følg navngiving 8005‑a/b/c/d/e‑\\\\<beskrivende‑navn>.md)*\\n\\n---\\n\\n```markdown\\n[Context Extractor] Ditt mål er **ikke** å løse brukerens forespørsel, men å isolere all praktisk kontekst (domene, verktøy, filstier, ønsket effekt). `{role=context_extractor; input=[spec:any]; process=[identifiser_domene_og_miljø(), trekk_ut_arbeidsrot(@codebase?), hent_eksplisitte_objektiver(), detekter_implisitte_brukskrav(), fjern_jargon_og_metaforer()]; constraints=[ingen_generering_av_løsninger]; output={context_profile:dict(domene:str?, root:str?, mål:list, krav:list)}}`\\n```\\n\\n---\\n\\n```markdown\\n[Task Vectorizer] Ditt mål er **ikke** å beskrive konteksten på nytt, men å oversette context_profile til en sekvens av atomiske, kodebase‑orienterte oppgaver. `{role=task_vectorizer; input=[context_profile:dict]; process=[del_inn_objektiver_i_diskrete_handlinger(), prefiksér_hver_handling_med_@codebase_sti_når_aktuelt(), bruk_presise_verber(identifiser|eliminer|konsolider), sørg_for_ordre_etter_avhengighet()], constraints=[ingen_overlappende_oppgaver(), unngå_kosmetiske_endringer_med_mindre_nødvendig], output={task_plan:list(str)}}`\\n```\\n\\n---\\n\\n```markdown\\n[Constraint Mapper] Ditt mål er **ikke** å endre task_plan, men å utlede eksplisitte rammer som må overholdes. `{role=constraint_mapper; input=[context_profile:dict, task_plan:list]; process=[kartlegg_funskjonelle_begrensninger(API, UI, legacy), fastslå_equivalence_krav(test, bench), identifiser_forbudsregler(kosmetikk, tredjepart), formuler_audit_logging_policy()], constraints=[ingen_duplisering_av_eksisterende_krav()], output={constraint_set:list(str)}}`\\n```\\n\\n---\\n\\n```markdown\\n[Prompt Assembler] Ditt mål er **ikke** å analysere innholdet, men å smi en ferdig agent‑prompt. `{role=prompt_assembler; input=[task_plan:list, constraint_set:list]; process=[bygg_inn_kort_intro_om_agentrolle(), list_task_plan_med_nummerering(), legg_til_constraint_set_som_obligatoriske_regler(), avslutt_med_valideringsmål(funke_eq, audit_ok)], constraints=[maks_300_ord, tydelig_seksjonsdeling(###)], output={agent_prompt:str}}`\\n```\\n\\n---\\n\\n```markdown\\n[Prompt Validator] Ditt mål er **ikke** å forbedre prompten kreativt, men å verifisere at den kan kjøres direkte av en autonom kode‑assistent. `{role=prompt_validator; input=[agent_prompt:str]; process=[sjekk_ordgrense(<=300), bekreft_@codebase_referanser_korrekt(), valider_nummerert_task_rekkefølge(), sikre_constraint_set_tilstede(), kontrollér_tydelig_goal_negation_i_intro()], constraints=[ingen_ekstra_ord, ingen_semantisk_endring], output={validated_prompt:str}}`\\n```\\\"```'\\n\\n\",\n  \"goal_map\": [\n    \"Explode the general problem statement into concrete pipeline stages.\",\n    \"Identify that the request is to design a pipeline that turns ANY user specification into a structured set of autonomous-agent-ready codebase-instructions.\",\n    \"Requirement for output to be a set of markdown files, one per pipeline stage/step, with strict naming convention: 8005‑a/b/c/d/e-<descriptive_name>.md.\",\n    \"Explicitly enumerate the decomposition into at least five distinct transformation stages.\",\n    \"For each stage:\",\n    \"  - Surface the intended atomic function (goal of that stage).\",\n    \"  - Reveal inputs (what it consumes) and outputs (what it emits).\",\n    \"  - Document the internal transformation or mapping it performs.\",\n    \"  - Uncover any hidden dependencies between steps—order, state, required context.\",\n    \"Generalize sufficiently so the pipeline is agnostic to the input spec’s technical domain, but can process any (implied) context.\",\n    \"Identify that for ‘autonomous-agent-ready’ codebase-instructions, the pipeline must:\",\n    \"  - Extract actionable objectives from text (regardless of domain).\",\n    \"  - Translate objectives to atomic, unambiguous codebase tasks.\",\n    \"  - Infer all relevant constraints (technical, procedural, legal, etc).\",\n    \"  - Compile these into clear agent prompts with strict conventions.\",\n    \"  - Validate prompts for correctness, completeness, and compatibility with agent needs.\",\n    \"Reveal the need to support codebase-relativity (@codebase paths), naming conventions, and enforce strict rule formats.\",\n    \"Implicit sub-goals:\",\n    \"  - Ensure naming and file organization logic is parameterized for agent ingestion.\",\n    \"  - Gloss every process/role in terms of expected responsibilities, goals, and constraints (for hand-off between pipeline stages).\",\n    \"  - Make all implicit rules explicit (e.g., no solution generation before the final prompt; no cosmetic suggestions unless strictly needed, etc).\",\n    \"Possible blockers:\",\n    \"  - Vague/underspecified input specs (must be handled by stage 1).\",\n    \"  - Loss of meaning or ambiguity as context passes between stages.\",\n    \"  - Enforcing output standards—e.g., strict file naming, word limits, formatting.\",\n    \"Unpack all pipeline stages as modular, role-isolated markdown files representing each phase in the transformation from specification to agent-prompt.\",\n    \"Summarize all dependencies, implicit requirements, and likely process vulnerabilities between steps.\",\n    \"Avoid any actual solution or file generation—only the blueprint and full decomposition of the required process.\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"instruction_format\": \"Decompose the general problem of transforming arbitrary specifications into autonomous-agent-ready codebase instructions into a modular, multi-stage pipeline. Structure the pipeline as at least five sequential transformation stages, each encapsulated in a separately named markdown file following the convention 8005‑a/b/c/d/e-<descriptive_name>.md. For each stage:\\n\\n- Define the atomic function (the goal of the stage).\\n- Specify the inputs (what the stage consumes) and outputs (what it produces).\\n- Document the internal processes or mappings performed within the stage.\\n- Identify dependencies on prior or subsequent stages, including any required state or context.\\n\\nEnsure the pipeline is agnostic to the technical domain of the input specification and is capable of processing any contextual details. Support extraction of actionable objectives, translation into atomic codebase tasks, inference of relevant constraints, assembly of these components into clear agent prompts with strict conventions, and prompt validation for correctness and compatibility. Include support for codebase-relative references (e.g., @codebase paths), enforced naming conventions, strict formatting, and word limits where specified.\\n\\nExplicitly document the responsibilities and constraints for each stage to ensure proper handoff and prevent premature solution generation or unnecessary cosmetic modifications. Summarize all modular steps as role-isolated markdown files representing each phase in the transformation process, without generating actual solutions or files. Conclude by enumerating all inter-step dependencies, implicit requirements, and potential process vulnerabilities.\"\n}",
        }
      }
    },
      {
      "instruction": "# Hard Critique\n\nYour goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:\n\n`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,2.0], flaw_analysis:str, alternative_enhancements:str[3]}}`",
      "step": "a",
      "title": "Hard Critique",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Generalisert pipeline som omformer hvilken som helst spesifikasjon til **autonomous‑agent‑klare kodebase‑instruksjoner**\\n\\n*(én fil per trinn; følg navngiving 8005‑a/b/c/d/e‑\\\\<beskrivende‑navn>.md)*\\n\\n---\\n\\n```markdown\\n[Context Extractor] Ditt mål er **ikke** å løse brukerens forespørsel, men å isolere all praktisk kontekst (domene, verktøy, filstier, ønsket effekt). `{role=context_extractor; input=[spec:any]; process=[identifiser_domene_og_miljø(), trekk_ut_arbeidsrot(@codebase?), hent_eksplisitte_objektiver(), detekter_implisitte_brukskrav(), fjern_jargon_og_metaforer()]; constraints=[ingen_generering_av_løsninger]; output={context_profile:dict(domene:str?, root:str?, mål:list, krav:list)}}`\\n```\\n\\n---\\n\\n```markdown\\n[Task Vectorizer] Ditt mål er **ikke** å beskrive konteksten på nytt, men å oversette context_profile til en sekvens av atomiske, kodebase‑orienterte oppgaver. `{role=task_vectorizer; input=[context_profile:dict]; process=[del_inn_objektiver_i_diskrete_handlinger(), prefiksér_hver_handling_med_@codebase_sti_når_aktuelt(), bruk_presise_verber(identifiser|eliminer|konsolider), sørg_for_ordre_etter_avhengighet()], constraints=[ingen_overlappende_oppgaver(), unngå_kosmetiske_endringer_med_mindre_nødvendig], output={task_plan:list(str)}}`\\n```\\n\\n---\\n\\n```markdown\\n[Constraint Mapper] Ditt mål er **ikke** å endre task_plan, men å utlede eksplisitte rammer som må overholdes. `{role=constraint_mapper; input=[context_profile:dict, task_plan:list]; process=[kartlegg_funskjonelle_begrensninger(API, UI, legacy), fastslå_equivalence_krav(test, bench), identifiser_forbudsregler(kosmetikk, tredjepart), formuler_audit_logging_policy()], constraints=[ingen_duplisering_av_eksisterende_krav()], output={constraint_set:list(str)}}`\\n```\\n\\n---\\n\\n```markdown\\n[Prompt Assembler] Ditt mål er **ikke** å analysere innholdet, men å smi en ferdig agent‑prompt. `{role=prompt_assembler; input=[task_plan:list, constraint_set:list]; process=[bygg_inn_kort_intro_om_agentrolle(), list_task_plan_med_nummerering(), legg_til_constraint_set_som_obligatoriske_regler(), avslutt_med_valideringsmål(funke_eq, audit_ok)], constraints=[maks_300_ord, tydelig_seksjonsdeling(###)], output={agent_prompt:str}}`\\n```\\n\\n---\\n\\n```markdown\\n[Prompt Validator] Ditt mål er **ikke** å forbedre prompten kreativt, men å verifisere at den kan kjøres direkte av en autonom kode‑assistent. `{role=prompt_validator; input=[agent_prompt:str]; process=[sjekk_ordgrense(<=300), bekreft_@codebase_referanser_korrekt(), valider_nummerert_task_rekkefølge(), sikre_constraint_set_tilstede(), kontrollér_tydelig_goal_negation_i_intro()], constraints=[ingen_ekstra_ord, ingen_semantisk_endring], output={validated_prompt:str}}`\\n```\\\"```'\\n\\n\",\n  \"instruction_format\": \"Decompose the general problem of transforming arbitrary specifications into autonomous-agent-ready codebase instructions into a modular, multi-stage pipeline. Structure the pipeline as at least five sequential transformation stages, each encapsulated in a separately named markdown file following the convention 8005‑a/b/c/d/e-<descriptive_name>.md. For each stage:\\n\\n- Define the atomic function (the goal of the stage).\\n- Specify the inputs (what the stage consumes) and outputs (what it produces).\\n- Document the internal processes or mappings performed within the stage.\\n- Identify dependencies on prior or subsequent stages, including any required state or context.\\n\\nEnsure the pipeline is agnostic to the technical domain of the input specification and is capable of processing any contextual details. Support extraction of actionable objectives, translation into atomic codebase tasks, inference of relevant constraints, assembly of these components into clear agent prompts with strict conventions, and prompt validation for correctness and compatibility. Include support for codebase-relative references (e.g., @codebase paths), enforced naming conventions, strict formatting, and word limits where specified.\\n\\nExplicitly document the responsibilities and constraints for each stage to ensure proper handoff and prevent premature solution generation or unnecessary cosmetic modifications. Summarize all modular steps as role-isolated markdown files representing each phase in the transformation process, without generating actual solutions or files. Conclude by enumerating all inter-step dependencies, implicit requirements, and potential process vulnerabilities.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"enhancement_score\": 1.1,\n  \"flaw_analysis\": \"The so-called enhancement takes the original, structured, highly prescriptive pipeline definition and replaces it with fluffier, less precise decompositional instructions. The original prompt embeds executable pseudocode for each pipeline stage, providing explicit roles, parameterization, constraint clauses, and concrete process steps that are immediately actionable and unambiguous for an automated agent. This rigorous modularization and machine-readability are completely eroded in the enhanced variant.\\n\\nCritical information loss manifests in several areas: explicit formatting requirements (e.g., codebase-relative paths), hardcoded constraint logic (e.g., role-specific process and output dicts), and mandated separations of concerns are now ambiguously described and left open to interpretation. Technical agnosticism, originally guaranteed by explicit extraction logic and anti-leakage constraints, is now merely gestured at. The enhanced prompt omits any mechanism for enforcing naming conventions, output field schemas, and systematic state-passing between pipeline stages, all of which were firmly encoded in the initial directive.\\n\\nClarity is catastrophically diminished. The initial prompt's synthax-guided modularity (Markdown code blocks, dict-type I/O, and code annotations) is replaced with a vague, prose-heavy directive ripe for misunderstanding. The ambiguity score rates at a disastrous 8 out of 10, due to the drastic reduction in process atomization, omission of enforceable constraints, and eradication of machine-usable structure.\\n\\nNoise is introduced by replacing strict, prescriptive commands and constraints with high-level, aspirational requests, diluting the actionable essence and reducing the likelihood that each pipeline stage will be executed in alignment with requirements. The enhancement's tone shifts towards guidance rather than instruction, inviting inconsistent interpretations and subsequent process breakdowns.\\n\\nImpact is diluted as the enhanced prompt does not guarantee role isolation, stepwise verification, or explicit outputs, destroying the original's chain-of-trust for composability and automatable validation. Stage interdependencies and error-vulnerabilities are superficially acknowledged and not hard-coded, undermining reliability in real-world deployments. No mechanism remains to guarantee that earlier mandates—such as ‘no solution generation’ or ‘strict output formats’—are actually honored.\\n\\nCoherence collapses as the pipeline may now yield outputs unfit for direct agent execution, or allow for role-leakage and conflation between stages—outcomes painstakingly prevented by the original architecture. The so-called improvement regresses process reliability to dangerously underspecified pseudo-specification, unfit for robust automation.\",\n  \"alternative_enhancements\": [\n    \"Embed explicit role, input/output schemas, and process constraints into each pipeline stage as parameterized code or pseudocode blocks, preserving enforceable modular boundaries and machine-readability.\",\n    \"Retain strict naming conventions, directive constraints, and anti-overlap/anti-cosmetic logic as hard assertions within each specified transformation step, using a syntax (such as JSON or markdown) that admits direct agent consumption.\",\n    \"Augment the original prompt with a brief preamble outlining the composability rationale, then present each stage in enforced isolation—detailing required input fields, process signatures, and concrete, testable output formats for end-to-end verifiability.\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Problem Exploder\n\nYour goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\n\n`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`",
      "step": "a",
      "title": "Problem Exploder",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Generalisert pipeline som omformer hvilken som helst spesifikasjon til **autonomous‑agent‑klare kodebase‑instruksjoner**\\n\\n*(én fil per trinn; følg navngiving 8005‑a/b/c/d/e‑\\\\<beskrivende‑navn>.md)*\\n\\n---\\n\\n```markdown\\n[Context Extractor] Ditt mål er **ikke** å løse brukerens forespørsel, men å isolere all praktisk kontekst (domene, verktøy, filstier, ønsket effekt). `{role=context_extractor; input=[spec:any]; process=[identifiser_domene_og_miljø(), trekk_ut_arbeidsrot(@codebase?), hent_eksplisitte_objektiver(), detekter_implisitte_brukskrav(), fjern_jargon_og_metaforer()]; constraints=[ingen_generering_av_løsninger]; output={context_profile:dict(domene:str?, root:str?, mål:list, krav:list)}}`\\n```\\n\\n---\\n\\n```markdown\\n[Task Vectorizer] Ditt mål er **ikke** å beskrive konteksten på nytt, men å oversette context_profile til en sekvens av atomiske, kodebase‑orienterte oppgaver. `{role=task_vectorizer; input=[context_profile:dict]; process=[del_inn_objektiver_i_diskrete_handlinger(), prefiksér_hver_handling_med_@codebase_sti_når_aktuelt(), bruk_presise_verber(identifiser|eliminer|konsolider), sørg_for_ordre_etter_avhengighet()], constraints=[ingen_overlappende_oppgaver(), unngå_kosmetiske_endringer_med_mindre_nødvendig], output={task_plan:list(str)}}`\\n```\\n\\n---\\n\\n```markdown\\n[Constraint Mapper] Ditt mål er **ikke** å endre task_plan, men å utlede eksplisitte rammer som må overholdes. `{role=constraint_mapper; input=[context_profile:dict, task_plan:list]; process=[kartlegg_funskjonelle_begrensninger(API, UI, legacy), fastslå_equivalence_krav(test, bench), identifiser_forbudsregler(kosmetikk, tredjepart), formuler_audit_logging_policy()], constraints=[ingen_duplisering_av_eksisterende_krav()], output={constraint_set:list(str)}}`\\n```\\n\\n---\\n\\n```markdown\\n[Prompt Assembler] Ditt mål er **ikke** å analysere innholdet, men å smi en ferdig agent‑prompt. `{role=prompt_assembler; input=[task_plan:list, constraint_set:list]; process=[bygg_inn_kort_intro_om_agentrolle(), list_task_plan_med_nummerering(), legg_til_constraint_set_som_obligatoriske_regler(), avslutt_med_valideringsmål(funke_eq, audit_ok)], constraints=[maks_300_ord, tydelig_seksjonsdeling(###)], output={agent_prompt:str}}`\\n```\\n\\n---\\n\\n```markdown\\n[Prompt Validator] Ditt mål er **ikke** å forbedre prompten kreativt, men å verifisere at den kan kjøres direkte av en autonom kode‑assistent. `{role=prompt_validator; input=[agent_prompt:str]; process=[sjekk_ordgrense(<=300), bekreft_@codebase_referanser_korrekt(), valider_nummerert_task_rekkefølge(), sikre_constraint_set_tilstede(), kontrollér_tydelig_goal_negation_i_intro()], constraints=[ingen_ekstra_ord, ingen_semantisk_endring], output={validated_prompt:str}}`\\n```\\\"```'\\n\\n\",\n  \"enhancement_score\": 1.1,\n  \"flaw_analysis\": \"The so-called enhancement takes the original, structured, highly prescriptive pipeline definition and replaces it with fluffier, less precise decompositional instructions. The original prompt embeds executable pseudocode for each pipeline stage, providing explicit roles, parameterization, constraint clauses, and concrete process steps that are immediately actionable and unambiguous for an automated agent. This rigorous modularization and machine-readability are completely eroded in the enhanced variant.\\n\\nCritical information loss manifests in several areas: explicit formatting requirements (e.g., codebase-relative paths), hardcoded constraint logic (e.g., role-specific process and output dicts), and mandated separations of concerns are now ambiguously described and left open to interpretation. Technical agnosticism, originally guaranteed by explicit extraction logic and anti-leakage constraints, is now merely gestured at. The enhanced prompt omits any mechanism for enforcing naming conventions, output field schemas, and systematic state-passing between pipeline stages, all of which were firmly encoded in the initial directive.\\n\\nClarity is catastrophically diminished. The initial prompt's synthax-guided modularity (Markdown code blocks, dict-type I/O, and code annotations) is replaced with a vague, prose-heavy directive ripe for misunderstanding. The ambiguity score rates at a disastrous 8 out of 10, due to the drastic reduction in process atomization, omission of enforceable constraints, and eradication of machine-usable structure.\\n\\nNoise is introduced by replacing strict, prescriptive commands and constraints with high-level, aspirational requests, diluting the actionable essence and reducing the likelihood that each pipeline stage will be executed in alignment with requirements. The enhancement's tone shifts towards guidance rather than instruction, inviting inconsistent interpretations and subsequent process breakdowns.\\n\\nImpact is diluted as the enhanced prompt does not guarantee role isolation, stepwise verification, or explicit outputs, destroying the original's chain-of-trust for composability and automatable validation. Stage interdependencies and error-vulnerabilities are superficially acknowledged and not hard-coded, undermining reliability in real-world deployments. No mechanism remains to guarantee that earlier mandates—such as ‘no solution generation’ or ‘strict output formats’—are actually honored.\\n\\nCoherence collapses as the pipeline may now yield outputs unfit for direct agent execution, or allow for role-leakage and conflation between stages—outcomes painstakingly prevented by the original architecture. The so-called improvement regresses process reliability to dangerously underspecified pseudo-specification, unfit for robust automation.\",\n  \"alternative_enhancements\": [\n    \"Embed explicit role, input/output schemas, and process constraints into each pipeline stage as parameterized code or pseudocode blocks, preserving enforceable modular boundaries and machine-readability.\",\n    \"Retain strict naming conventions, directive constraints, and anti-overlap/anti-cosmetic logic as hard assertions within each specified transformation step, using a syntax (such as JSON or markdown) that admits direct agent consumption.\",\n    \"Augment the original prompt with a brief preamble outlining the composability rationale, then present each stage in enforced isolation—detailing required input fields, process signatures, and concrete, testable output formats for end-to-end verifiability.\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"goal_map\": [\n    {\n      \"explicit_asks\": [\n        \"Design a generalized pipeline that transforms any specification into autonomous-agent-ready codebase instructions.\",\n        \"Structure the pipeline as one file per step, with naming convention '8005-a/b/c/d/e-<descriptive-name>.md'.\",\n        \"Implement, in order, five distinct pipeline stages (Context Extractor, Task Vectorizer, Constraint Mapper, Prompt Assembler, Prompt Validator) as described via their roles and processes.\",\n        \"For each stage, follow provided role, input, process, constraints, and output definitions exactly as specified in code block pseudocode.\",\n        \"Maintain strict modularity: stages should not solve or leak outputs of one another unless via defined output fields.\"\n      ],\n      \"implicit_assumptions\": [\n        \"The consumer of this pipeline is an autonomous agent (or automation platform) that can process structured inputs/outputs and code block instructions.\",\n        \"The pipeline must be agnostic to the input specification's domain or subject matter.\",\n        \"Each pipeline stage operates on the output of the previous stage, passing state through a well-defined schema.\",\n        \"All code block pseudocode and role metadata must be preserved for later direct use by machine agents.\",\n        \"Naming conventions and file segmentation must support traceability and future auditing.\",\n        \"The pipeline is required to work 'out-of-the-box' without further human intervention or creative reinterpretation at runtime.\"\n      ],\n      \"sub_goals\": [\n        \"For [Context Extractor]: Isolate all practical context (domain, toolchain, file paths, objectives, requirements) from the given specification, avoiding problem solving or speculative interpretation, and output a dictionary with domains, roots, goals, and requirements.\",\n        \"For [Task Vectorizer]: Convert the context_profile into an ordered set of atomic, codebase-oriented tasks with precise, non-overlapping actions, prefixed paths where relevant, using mandated verbs, and output as a list of strings.\",\n        \"For [Constraint Mapper]: Analyze both the context_profile and task_plan to deduce actionable constraints (e.g., API boundaries, test equivalence, forbidden changes, audit logging), avoid duplicating existing requirements, and output as a list.\",\n        \"For [Prompt Assembler]: Produce a single, clear agent prompt by combining the task_plan and constraint_set into a Markdown-formatted, sectioned, <300 word directive suitable for direct agent execution, with introduction, numbered tasks, rules, validation goals.\",\n        \"For [Prompt Validator]: Evaluate the assembled agent prompt against structural and formatting criteria (word count, path references, task order/numbering, constraint inclusion, goal negation in intro), making no semantic changes, and output the validated prompt.\"\n      ],\n      \"hidden_dependencies\": [\n        \"Each stage must enforce explicit separation of concerns, never overstepping its prescribed process.\",\n        \"All inputs and outputs are structured and must strictly follow the specifications in the prompt code blocks (dicts, lists, strings as directed).\",\n        \"Role and process names, constraint language, and section formatting must exactly match the prototype code.\",\n        \"Chain-of-trust between steps must be preserved: validator must rely only on format, not regenerate solutions.\",\n        \"Any agent using the prompt must be able to parse, route, and verify state transitions cleanly, both forwards and in audit scenarios.\",\n        \"Error-handling is implied: steps must refuse to process if required state or schema is not present, rather than guessing or filling gaps.\"\n      ],\n      \"blockers\": [\n        \"Ambiguity in any step's process or output schema can lead to breakdowns in downstream stages.\",\n        \"Failure to strictly adhere to structure or constraints risks agent misinterpretation or accidental solution leakage (especially in extraction/vectorization stages).\",\n        \"Lack of explicit state passing between files or stages can cause data loss or misalignment.\",\n        \"Deviation from mandated naming or output formatting can break automation or auditability.\",\n        \"Overlaps, redundancies, or hidden state (e.g., implicit environmental knowledge) not surfaced by Context Extractor will poison later steps.\",\n        \"Absence of final prompt validation, or incorrectly implemented checks in Prompt Validator, will yield prompts unusable by autonomous agents.\"\n      ]\n    }\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`",
      "step": "a",
      "title": "Instruction Converter",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Generalisert pipeline som omformer hvilken som helst spesifikasjon til **autonomous‑agent‑klare kodebase‑instruksjoner**\\n\\n*(én fil per trinn; følg navngiving 8005‑a/b/c/d/e‑\\\\<beskrivende‑navn>.md)*\\n\\n---\\n\\n```markdown\\n[Context Extractor] Ditt mål er **ikke** å løse brukerens forespørsel, men å isolere all praktisk kontekst (domene, verktøy, filstier, ønsket effekt). `{role=context_extractor; input=[spec:any]; process=[identifiser_domene_og_miljø(), trekk_ut_arbeidsrot(@codebase?), hent_eksplisitte_objektiver(), detekter_implisitte_brukskrav(), fjern_jargon_og_metaforer()]; constraints=[ingen_generering_av_løsninger]; output={context_profile:dict(domene:str?, root:str?, mål:list, krav:list)}}`\\n```\\n\\n---\\n\\n```markdown\\n[Task Vectorizer] Ditt mål er **ikke** å beskrive konteksten på nytt, men å oversette context_profile til en sekvens av atomiske, kodebase‑orienterte oppgaver. `{role=task_vectorizer; input=[context_profile:dict]; process=[del_inn_objektiver_i_diskrete_handlinger(), prefiksér_hver_handling_med_@codebase_sti_når_aktuelt(), bruk_presise_verber(identifiser|eliminer|konsolider), sørg_for_ordre_etter_avhengighet()], constraints=[ingen_overlappende_oppgaver(), unngå_kosmetiske_endringer_med_mindre_nødvendig], output={task_plan:list(str)}}`\\n```\\n\\n---\\n\\n```markdown\\n[Constraint Mapper] Ditt mål er **ikke** å endre task_plan, men å utlede eksplisitte rammer som må overholdes. `{role=constraint_mapper; input=[context_profile:dict, task_plan:list]; process=[kartlegg_funskjonelle_begrensninger(API, UI, legacy), fastslå_equivalence_krav(test, bench), identifiser_forbudsregler(kosmetikk, tredjepart), formuler_audit_logging_policy()], constraints=[ingen_duplisering_av_eksisterende_krav()], output={constraint_set:list(str)}}`\\n```\\n\\n---\\n\\n```markdown\\n[Prompt Assembler] Ditt mål er **ikke** å analysere innholdet, men å smi en ferdig agent‑prompt. `{role=prompt_assembler; input=[task_plan:list, constraint_set:list]; process=[bygg_inn_kort_intro_om_agentrolle(), list_task_plan_med_nummerering(), legg_til_constraint_set_som_obligatoriske_regler(), avslutt_med_valideringsmål(funke_eq, audit_ok)], constraints=[maks_300_ord, tydelig_seksjonsdeling(###)], output={agent_prompt:str}}`\\n```\\n\\n---\\n\\n```markdown\\n[Prompt Validator] Ditt mål er **ikke** å forbedre prompten kreativt, men å verifisere at den kan kjøres direkte av en autonom kode‑assistent. `{role=prompt_validator; input=[agent_prompt:str]; process=[sjekk_ordgrense(<=300), bekreft_@codebase_referanser_korrekt(), valider_nummerert_task_rekkefølge(), sikre_constraint_set_tilstede(), kontrollér_tydelig_goal_negation_i_intro()], constraints=[ingen_ekstra_ord, ingen_semantisk_endring], output={validated_prompt:str}}`\\n```\\\"```'\\n\\n\",\n  \"goal_map\": [\n    {\n      \"explicit_asks\": [\n        \"Design a generalized pipeline that transforms any specification into autonomous-agent-ready codebase instructions.\",\n        \"Structure the pipeline as one file per step, with naming convention '8005-a/b/c/d/e-<descriptive-name>.md'.\",\n        \"Implement, in order, five distinct pipeline stages (Context Extractor, Task Vectorizer, Constraint Mapper, Prompt Assembler, Prompt Validator) as described via their roles and processes.\",\n        \"For each stage, follow provided role, input, process, constraints, and output definitions exactly as specified in code block pseudocode.\",\n        \"Maintain strict modularity: stages should not solve or leak outputs of one another unless via defined output fields.\"\n      ],\n      \"implicit_assumptions\": [\n        \"The consumer of this pipeline is an autonomous agent (or automation platform) that can process structured inputs/outputs and code block instructions.\",\n        \"The pipeline must be agnostic to the input specification's domain or subject matter.\",\n        \"Each pipeline stage operates on the output of the previous stage, passing state through a well-defined schema.\",\n        \"All code block pseudocode and role metadata must be preserved for later direct use by machine agents.\",\n        \"Naming conventions and file segmentation must support traceability and future auditing.\",\n        \"The pipeline is required to work 'out-of-the-box' without further human intervention or creative reinterpretation at runtime.\"\n      ],\n      \"sub_goals\": [\n        \"For [Context Extractor]: Isolate all practical context (domain, toolchain, file paths, objectives, requirements) from the given specification, avoiding problem solving or speculative interpretation, and output a dictionary with domains, roots, goals, and requirements.\",\n        \"For [Task Vectorizer]: Convert the context_profile into an ordered set of atomic, codebase-oriented tasks with precise, non-overlapping actions, prefixed paths where relevant, using mandated verbs, and output as a list of strings.\",\n        \"For [Constraint Mapper]: Analyze both the context_profile and task_plan to deduce actionable constraints (e.g., API boundaries, test equivalence, forbidden changes, audit logging), avoid duplicating existing requirements, and output as a list.\",\n        \"For [Prompt Assembler]: Produce a single, clear agent prompt by combining the task_plan and constraint_set into a Markdown-formatted, sectioned, <300 word directive suitable for direct agent execution, with introduction, numbered tasks, rules, validation goals.\",\n        \"For [Prompt Validator]: Evaluate the assembled agent prompt against structural and formatting criteria (word count, path references, task order/numbering, constraint inclusion, goal negation in intro), making no semantic changes, and output the validated prompt.\"\n      ],\n      \"hidden_dependencies\": [\n        \"Each stage must enforce explicit separation of concerns, never overstepping its prescribed process.\",\n        \"All inputs and outputs are structured and must strictly follow the specifications in the prompt code blocks (dicts, lists, strings as directed).\",\n        \"Role and process names, constraint language, and section formatting must exactly match the prototype code.\",\n        \"Chain-of-trust between steps must be preserved: validator must rely only on format, not regenerate solutions.\",\n        \"Any agent using the prompt must be able to parse, route, and verify state transitions cleanly, both forwards and in audit scenarios.\",\n        \"Error-handling is implied: steps must refuse to process if required state or schema is not present, rather than guessing or filling gaps.\"\n      ],\n      \"blockers\": [\n        \"Ambiguity in any step's process or output schema can lead to breakdowns in downstream stages.\",\n        \"Failure to strictly adhere to structure or constraints risks agent misinterpretation or accidental solution leakage (especially in extraction/vectorization stages).\",\n        \"Lack of explicit state passing between files or stages can cause data loss or misalignment.\",\n        \"Deviation from mandated naming or output formatting can break automation or auditability.\",\n        \"Overlaps, redundancies, or hidden state (e.g., implicit environmental knowledge) not surfaced by Context Extractor will poison later steps.\",\n        \"Absence of final prompt validation, or incorrectly implemented checks in Prompt Validator, will yield prompts unusable by autonomous agents.\"\n      ]\n    }\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"instruction_format\": \"Design a generalized pipeline that converts any specification into autonomous-agent-ready codebase instructions. Segment the pipeline into five modular stages, one file per step, using the format '8005-a/b/c/d/e-<descriptive-name>.md'. Implement the following stages in order, strictly adhering to each provided role, input, process, constraints, and output as detailed:\\n\\n1. Context Extractor: Isolate all actionable context (domain, toolchain, file paths, explicit objectives, requirements) from the specification. Avoid problem solving, solution generation, or interpreting intent. Output a dictionary containing domain, root, goals, and requirements.\\n2. Task Vectorizer: Translate the context_profile into an ordered list of atomic, codebase-oriented tasks using precise, non-overlapping actions (with prefixed file paths and required verbs where defined). Output as a list of strings, ensuring no cosmetic-only changes unless necessary.\\n3. Constraint Mapper: Synthesize actionable constraints by analyzing both context_profile and task_plan (e.g., functional boundaries, equivalence requirements, forbidden changes, audit logging policy) without duplicating existing requirements. Output constraints in list format.\\n4. Prompt Assembler: Combine task_plan and constraint_set into a Markdown-formatted agent prompt (<300 words) comprising a brief introduction, numbered tasks, explicit rules, and validation goals as distinct sections; ensure formatting complies with sectioning and word count limits.\\n5. Prompt Validator: Verify the assembled prompt against structural requirements (word count ≤ 300, correct usage of path references, sequential and numbered tasks, inclusion of the constraint set, goal negation in the introduction). Output the validated prompt without any semantic alterations.\\n\\nMaintain strict modularity between stages, passing only the prescribed output schema to the subsequent step. Enforce explicit separation of concerns and prevent leakage of outputs or roles between pipeline stages. Follow all naming conventions and preserve provided role, process, and constraint language exactly as specified in the code block pseudocode. Reject operation if required state or schema is missing at any stage, ensuring all inputs/outputs are structured and audit-traceable. Ensure that the pipeline is domain-agnostic, fully machine-routable, and suitable for autonomous execution without supplemental human instruction.\"\n}",
        }
      }
    }