  {
  "initial_prompt": "llm_instructions_for_sublimetext_plugin='''You’re an expert Sublime Text plugin developer with a deep understanding of Python and the Sublime Text API. You specialize in creating intuitive and efficient plugins that enhance user productivity and streamline workflows. You possess deep knowledge of the Sublime Text API and its ecosystem (and best practices), along with a strong understanding of user needs and developer workflows.\n\n- Sublime Layout\n    > Lagre Layouts\n        > Lagre Regelsett knyttet til Layout (hvilke tabs som skal hvor, hvilke som skal lukkes, osv)\n            > F.eks. `alltid flytt [\"main.py\", \"*.py.bak\"] til layout.4.index.2`\n\n    > Kan videreutvikles til en standard som automatisk opprettholdes ved alle (.sublime-project) prosjekter'''",
  "sequence_id": "9004",
  "results": [
      {
      "instruction": "# Context Extractor\n\nYour goal is **not** to propose solutions, but to **isolate** actionable context from the specification. Execute as:\n\n`{role=context_extractor; input=[raw_spec:str]; process=[strip_jargon(), strip_metaphor(), detect_domain(), locate_project_root(\"@codebase\"), list_explicit_objectives(), surface_implicit_requirements()], constraints=[no_paraphrase(), no_solution_generation()], output={context_profile:{domain:str, project_root:str, goals:list, requirements:list}}}`\n\n## Context Data\n\n```json\n{\n  \"role\": \"Autonomous Agent Prompt Assembler\",\n  \"goal_negation\": \"Your purpose is NOT to analyze input, generate context, or solve tasks directly. Your ONLY objective is to compile a ready-to-execute agent prompt using the provided task and constraint specifications. Do NOT solve the user's query directly; instead, transform the provided input into a structured, autonomous-agent-ready instruction prompt tailored for codebase navigation and intervention.\",\n  \"agent_role_detail\": \"Act exclusively as the prompt assembler. Synthesize an actionable, stepwise instruction prompt suitable for autonomous coding agents. Operate strictly within defined role, input/output schemas, and do not speculate or reinterpret objectives beyond input.\",\n  \"tasks\": [\n    \"Enumerate all explicit and implicit objectives and requirements derived from the input context at @codebase.\",\n    \"Decompose each objective into discrete, atomic, non-redundant tasks, ensuring each task is actionable, uses proper @codebase path references, and maintains consistency.\",\n    \"Order all @codebase tasks logically by dependencies to guarantee efficient and safe execution.\",\n    \"For each @codebase task, prepare precise implementation steps, removing ambiguity and ensuring cross-team clarity.\",\n    \"Document all actions, intentions, rationales, timestamps, attribution, and tamper-evidence in audit logs within @codebase.\",\n    \"Verify all modifications within @codebase strictly maintain functional equivalence through automated testing or behavioral validation prior to result acceptance.\",\n    \"Ensure comprehensive auditability for every change, including rationale, process, metrics, validation evidence, and outcomes.\",\n    \"Restrict changes within @codebase to only those explicitly permitted by input-context constraints; explicitly document any exceptions.\"\n  ],\n  \"constraints\": [\n    \"Adhere strictly to the listed tasks; do not generate, speculate, alter order, or change substance.\",\n    \"Use only clear, practical language; avoid domain-specific jargon or metaphors.\",\n    \"Prohibit all cosmetic, stylistic, or naming changes unless they are inseparably required for functional objectives and explicitly justified/documented.\",\n    \"Disallow modification of protected, legacy, or third-party code unless strictly necessary for redundancy or obsolescence removal, and document all such exceptions.\",\n    \"Ensure comprehensive audit logging for every modification (intention, action, rationale, timestamp, attribution, tamper-evidence).\",\n    \"Enforce functional equivalence post-modification using automated tests, benchmarks, or behavioral validation before acceptance.\",\n    \"Consolidate or remove elements only after verification as redundant/obsolete by both automated and manual checks; preserve all essential behaviors/interfaces.\",\n    \"Strictly limit output to ≤300 tokens, with clear sectional segmentation matching the specified template.\",\n    \"Disallow any cross-step leakage or violation of input/output schemas or role boundaries.\",\n    \"No new tasks or constraints are permitted beyond those explicitly or implicitly established by the initial input and context.\"\n  ],\n  \"validation_targets\": [\n    \"Confirm all modifications strictly maintain functional equivalence—audit with automated and benchmark tests before acceptance.\",\n    \"Ensure comprehensive auditability for every change, including an explicit, agent-readable audit/logging policy and actionable equivalence verification goals.\",\n    \"The final prompt must be directly executable by an autonomous agent without needing further semantic or structural adjustment.\"\n  ]\n}\n```",
      "step": "a",
      "title": "Context Extractor",
      "input": "llm_instructions_for_sublimetext_plugin='''You’re an expert Sublime Text plugin developer with a deep understanding of Python and the Sublime Text API. You specialize in creating intuitive and efficient plugins that enhance user productivity and streamline workflows. You possess deep knowledge of the Sublime Text API and its ecosystem (and best practices), along with a strong understanding of user needs and developer workflows.\n\n- Sublime Layout\n    > Lagre Layouts\n        > Lagre Regelsett knyttet til Layout (hvilke tabs som skal hvor, hvilke som skal lukkes, osv)\n            > F.eks. `alltid flytt [\"main.py\", \"*.py.bak\"] til layout.4.index.2`\n\n    > Kan videreutvikles til en standard som automatisk opprettholdes ved alle (.sublime-project) prosjekter'''",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"context_profile\": {\n    \"domain\": \"Sublime Text plugin development (Python, Sublime Text API, layout management)\",\n    \"project_root\": \"@codebase\",\n    \"goals\": [\n      \"Implement a feature for managing Sublime Text layouts\",\n      \"Create and enforce rules for layout management (e.g., assign certain files/tabs to specified layout indices, close specified tabs)\",\n      \"Enable customizable/automated rules such as always moving ['main.py', '*.py.bak'] to layout.4.index.2\",\n      \"Allow evolution toward a standard that is automatically applied across all '.sublime-project' projects\"\n    ],\n    \"requirements\": [\n      \"Use Python and Sublime Text API\",\n      \"Support definition and enforcement of layout rules for tabs and files\",\n      \"Support patterns/wildcards (e.g., '*.py.bak') in rule definitions\",\n      \"Integrate rules so they update/maintain layouts automatically per project\",\n      \"Store and load rules, possibly tied to '.sublime-project' files\",\n      \"Maintain and update layouts and tab arrangements in real time as per rules\",\n      \"Ensure user workflow is not disrupted (intuitive, productivity-enhancing UI/UX)\",\n      \"Follow Sublime Text plugin best practices\"\n    ]\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Task Vectorizer\n\nYour goal is **not** to restate context, but to **decompose** it into atomic, codebase-oriented tasks. Execute as:\n\n`{role=task_vectorizer; input=[context_profile:dict]; process=[derive_task_candidates(), prefix_paths(\"@codebase\"), order_by_dependency(), enforce_action_verbs(), remove_overlap(), ban_cosmetic_changes()], constraints=[tasks_unique(), verbs_only()], output={ordered_tasks:[str]}}`\n\n## Context Data\n\n```json\n{\n  \"role\": \"Autonomous Agent Prompt Assembler\",\n  \"goal_negation\": \"Your purpose is NOT to analyze input, generate context, or solve tasks directly. Your ONLY objective is to compile a ready-to-execute agent prompt using the provided task and constraint specifications. Do NOT solve the user's query directly; instead, transform the provided input into a structured, autonomous-agent-ready instruction prompt tailored for codebase navigation and intervention.\",\n  \"agent_role_detail\": \"Act exclusively as the prompt assembler. Synthesize an actionable, stepwise instruction prompt suitable for autonomous coding agents. Operate strictly within defined role, input/output schemas, and do not speculate or reinterpret objectives beyond input.\",\n  \"tasks\": [\n    \"Enumerate all explicit and implicit objectives and requirements derived from the input context at @codebase.\",\n    \"Decompose each objective into discrete, atomic, non-redundant tasks, ensuring each task is actionable, uses proper @codebase path references, and maintains consistency.\",\n    \"Order all @codebase tasks logically by dependencies to guarantee efficient and safe execution.\",\n    \"For each @codebase task, prepare precise implementation steps, removing ambiguity and ensuring cross-team clarity.\",\n    \"Document all actions, intentions, rationales, timestamps, attribution, and tamper-evidence in audit logs within @codebase.\",\n    \"Verify all modifications within @codebase strictly maintain functional equivalence through automated testing or behavioral validation prior to result acceptance.\",\n    \"Ensure comprehensive auditability for every change, including rationale, process, metrics, validation evidence, and outcomes.\",\n    \"Restrict changes within @codebase to only those explicitly permitted by input-context constraints; explicitly document any exceptions.\"\n  ],\n  \"constraints\": [\n    \"Adhere strictly to the listed tasks; do not generate, speculate, alter order, or change substance.\",\n    \"Use only clear, practical language; avoid domain-specific jargon or metaphors.\",\n    \"Prohibit all cosmetic, stylistic, or naming changes unless they are inseparably required for functional objectives and explicitly justified/documented.\",\n    \"Disallow modification of protected, legacy, or third-party code unless strictly necessary for redundancy or obsolescence removal, and document all such exceptions.\",\n    \"Ensure comprehensive audit logging for every modification (intention, action, rationale, timestamp, attribution, tamper-evidence).\",\n    \"Enforce functional equivalence post-modification using automated tests, benchmarks, or behavioral validation before acceptance.\",\n    \"Consolidate or remove elements only after verification as redundant/obsolete by both automated and manual checks; preserve all essential behaviors/interfaces.\",\n    \"Strictly limit output to ≤300 tokens, with clear sectional segmentation matching the specified template.\",\n    \"Disallow any cross-step leakage or violation of input/output schemas or role boundaries.\",\n    \"No new tasks or constraints are permitted beyond those explicitly or implicitly established by the initial input and context.\"\n  ],\n  \"validation_targets\": [\n    \"Confirm all modifications strictly maintain functional equivalence—audit with automated and benchmark tests before acceptance.\",\n    \"Ensure comprehensive auditability for every change, including an explicit, agent-readable audit/logging policy and actionable equivalence verification goals.\",\n    \"The final prompt must be directly executable by an autonomous agent without needing further semantic or structural adjustment.\"\n  ]\n}\n```",
      "step": "b",
      "title": "Task Vectorizer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"llm_instructions_for_sublimetext_plugin='''You’re an expert Sublime Text plugin developer with a deep understanding of Python and the Sublime Text API. You specialize in creating intuitive and efficient plugins that enhance user productivity and streamline workflows. You possess deep knowledge of the Sublime Text API and its ecosystem (and best practices), along with a strong understanding of user needs and developer workflows.\\n\\n- Sublime Layout\\n    > Lagre Layouts\\n        > Lagre Regelsett knyttet til Layout (hvilke tabs som skal hvor, hvilke som skal lukkes, osv)\\n            > F.eks. `alltid flytt [\\\"main.py\\\", \\\"*.py.bak\\\"] til layout.4.index.2`\\n\\n    > Kan videreutvikles til en standard som automatisk opprettholdes ved alle (.sublime-project) prosjekter'''\\\"```'\\n\\n\",\n  \"context_profile\": {\n    \"domain\": \"Sublime Text plugin development (Python, Sublime Text API, layout management)\",\n    \"project_root\": \"@codebase\",\n    \"goals\": [\n      \"Implement a feature for managing Sublime Text layouts\",\n      \"Create and enforce rules for layout management (e.g., assign certain files/tabs to specified layout indices, close specified tabs)\",\n      \"Enable customizable/automated rules such as always moving ['main.py', '*.py.bak'] to layout.4.index.2\",\n      \"Allow evolution toward a standard that is automatically applied across all '.sublime-project' projects\"\n    ],\n    \"requirements\": [\n      \"Use Python and Sublime Text API\",\n      \"Support definition and enforcement of layout rules for tabs and files\",\n      \"Support patterns/wildcards (e.g., '*.py.bak') in rule definitions\",\n      \"Integrate rules so they update/maintain layouts automatically per project\",\n      \"Store and load rules, possibly tied to '.sublime-project' files\",\n      \"Maintain and update layouts and tab arrangements in real time as per rules\",\n      \"Ensure user workflow is not disrupted (intuitive, productivity-enhancing UI/UX)\",\n      \"Follow Sublime Text plugin best practices\"\n    ]\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"ordered_tasks\": [\n    \"Analyze @codebase to enumerate all current and missing capabilities related to Sublime Text layout management, file/tab rule enforcement, wildcard pattern support, and .sublime-project integration.\",\n    \"Design and implement a Python module in @codebase to define and store customizable layout rules, allowing explicit tab/file-to-layout mappings and wildcard support.\",\n    \"Integrate logic in @codebase for parsing and interpreting layout rule definitions—including patterns such as '*.py.bak'—with mapping to layout indices.\",\n    \"Implement functionality in @codebase to automatically apply rules upon project load or modification, updating tab/file arrangements in real time without disrupting the user's workflow.\",\n    \"Add support in @codebase for loading and saving layout rule sets within each relevant .sublime-project file, ensuring project-specific enforcement.\",\n    \"Develop and expose API endpoints or internal functions in @codebase for real-time validation, enforcement, and update of layout rules as tabs/files are changed, with consistent user experience.\",\n    \"Integrate automated audit logging in @codebase for all structural/UI changes initiated by rules, including intention, action, rationale, timestamp, attribution, and tamper-evidence.\",\n    \"Implement and run automated tests in @codebase validating functional equivalence, correct rule application, non-disruption of user experience, and comprehensive audit log generation for all changes.\",\n    \"Document all task intentions, design decisions, audit and validation procedures within @codebase for full traceability and compliance with auditability requirements.\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Constraint Mapper\n\nYour goal is **not** to edit tasks, but to **extract** all governing limits. Execute as:\n\n`{role=constraint_mapper; input=[context_profile:dict, ordered_tasks:list]; process=[collect_functional_constraints(), collect_behavioral_constraints(), collect_policy_constraints(), deduplicate_constraints()], constraints=[no_new_tasks(), no_redundant_entries()], output={constraint_set:[str]}}`\n\n## Context Data\n\n```json\n{\n  \"role\": \"Autonomous Agent Prompt Assembler\",\n  \"goal_negation\": \"Your purpose is NOT to analyze input, generate context, or solve tasks directly. Your ONLY objective is to compile a ready-to-execute agent prompt using the provided task and constraint specifications. Do NOT solve the user's query directly; instead, transform the provided input into a structured, autonomous-agent-ready instruction prompt tailored for codebase navigation and intervention.\",\n  \"agent_role_detail\": \"Act exclusively as the prompt assembler. Synthesize an actionable, stepwise instruction prompt suitable for autonomous coding agents. Operate strictly within defined role, input/output schemas, and do not speculate or reinterpret objectives beyond input.\",\n  \"tasks\": [\n    \"Enumerate all explicit and implicit objectives and requirements derived from the input context at @codebase.\",\n    \"Decompose each objective into discrete, atomic, non-redundant tasks, ensuring each task is actionable, uses proper @codebase path references, and maintains consistency.\",\n    \"Order all @codebase tasks logically by dependencies to guarantee efficient and safe execution.\",\n    \"For each @codebase task, prepare precise implementation steps, removing ambiguity and ensuring cross-team clarity.\",\n    \"Document all actions, intentions, rationales, timestamps, attribution, and tamper-evidence in audit logs within @codebase.\",\n    \"Verify all modifications within @codebase strictly maintain functional equivalence through automated testing or behavioral validation prior to result acceptance.\",\n    \"Ensure comprehensive auditability for every change, including rationale, process, metrics, validation evidence, and outcomes.\",\n    \"Restrict changes within @codebase to only those explicitly permitted by input-context constraints; explicitly document any exceptions.\"\n  ],\n  \"constraints\": [\n    \"Adhere strictly to the listed tasks; do not generate, speculate, alter order, or change substance.\",\n    \"Use only clear, practical language; avoid domain-specific jargon or metaphors.\",\n    \"Prohibit all cosmetic, stylistic, or naming changes unless they are inseparably required for functional objectives and explicitly justified/documented.\",\n    \"Disallow modification of protected, legacy, or third-party code unless strictly necessary for redundancy or obsolescence removal, and document all such exceptions.\",\n    \"Ensure comprehensive audit logging for every modification (intention, action, rationale, timestamp, attribution, tamper-evidence).\",\n    \"Enforce functional equivalence post-modification using automated tests, benchmarks, or behavioral validation before acceptance.\",\n    \"Consolidate or remove elements only after verification as redundant/obsolete by both automated and manual checks; preserve all essential behaviors/interfaces.\",\n    \"Strictly limit output to ≤300 tokens, with clear sectional segmentation matching the specified template.\",\n    \"Disallow any cross-step leakage or violation of input/output schemas or role boundaries.\",\n    \"No new tasks or constraints are permitted beyond those explicitly or implicitly established by the initial input and context.\"\n  ],\n  \"validation_targets\": [\n    \"Confirm all modifications strictly maintain functional equivalence—audit with automated and benchmark tests before acceptance.\",\n    \"Ensure comprehensive auditability for every change, including an explicit, agent-readable audit/logging policy and actionable equivalence verification goals.\",\n    \"The final prompt must be directly executable by an autonomous agent without needing further semantic or structural adjustment.\"\n  ]\n}\n```",
      "step": "c",
      "title": "Constraint Mapper",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"llm_instructions_for_sublimetext_plugin='''You’re an expert Sublime Text plugin developer with a deep understanding of Python and the Sublime Text API. You specialize in creating intuitive and efficient plugins that enhance user productivity and streamline workflows. You possess deep knowledge of the Sublime Text API and its ecosystem (and best practices), along with a strong understanding of user needs and developer workflows.\\n\\n- Sublime Layout\\n    > Lagre Layouts\\n        > Lagre Regelsett knyttet til Layout (hvilke tabs som skal hvor, hvilke som skal lukkes, osv)\\n            > F.eks. `alltid flytt [\\\"main.py\\\", \\\"*.py.bak\\\"] til layout.4.index.2`\\n\\n    > Kan videreutvikles til en standard som automatisk opprettholdes ved alle (.sublime-project) prosjekter'''\\\"```'\\n\\n\",\n  \"ordered_tasks\": [\n    \"Analyze @codebase to enumerate all current and missing capabilities related to Sublime Text layout management, file/tab rule enforcement, wildcard pattern support, and .sublime-project integration.\",\n    \"Design and implement a Python module in @codebase to define and store customizable layout rules, allowing explicit tab/file-to-layout mappings and wildcard support.\",\n    \"Integrate logic in @codebase for parsing and interpreting layout rule definitions—including patterns such as '*.py.bak'—with mapping to layout indices.\",\n    \"Implement functionality in @codebase to automatically apply rules upon project load or modification, updating tab/file arrangements in real time without disrupting the user's workflow.\",\n    \"Add support in @codebase for loading and saving layout rule sets within each relevant .sublime-project file, ensuring project-specific enforcement.\",\n    \"Develop and expose API endpoints or internal functions in @codebase for real-time validation, enforcement, and update of layout rules as tabs/files are changed, with consistent user experience.\",\n    \"Integrate automated audit logging in @codebase for all structural/UI changes initiated by rules, including intention, action, rationale, timestamp, attribution, and tamper-evidence.\",\n    \"Implement and run automated tests in @codebase validating functional equivalence, correct rule application, non-disruption of user experience, and comprehensive audit log generation for all changes.\",\n    \"Document all task intentions, design decisions, audit and validation procedures within @codebase for full traceability and compliance with auditability requirements.\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"constraint_set\": [\n    \"[Functional] Only analyze and intervene within the @codebase regarding Sublime Text layout management, file/tab rule enforcement, wildcard pattern support, and .sublime-project integration; do not address tasks outside the explicitly listed scope.\",\n    \"[Functional] Restrict implementation strictly to Python and the Sublime Text API as the basis for all automation and integrations.\",\n    \"[Functional] Ensure all layout rules support explicit tab/file-to-layout mappings and wildcard patterns; maintain project-specific rule sets via .sublime-project files.\",\n    \"[Functional] All rule parsing, enforcement, and project integration logic must be real-time, non-disruptive, and reversible if necessary.\",\n    \"[Behavioral] Do not generate, speculate, alter, or reorder listed tasks; follow the ordered, stepwise task sequence without deviation.\",\n    \"[Behavioral] Use only clear, straightforward language in implementation, inline documentation, and logs; avoid domain-specific jargon or metaphors.\",\n    \"[Behavioral] Do not apply any cosmetic, stylistic, or naming changes unless directly inseparable from explicit functional requirements and with thorough documentation/justification.\",\n    \"[Behavioral] Prohibit modification of protected, legacy, or third-party code unless absolutely required for documented redundancy/obsolescence removal, with rationale recorded.\",\n    \"[Policy] Ensure comprehensive audit logging of every codebase modification—intention, action, rationale, timestamp, attribution, and tamper-evidence are all mandatory.\",\n    \"[Policy] Enforce strict functional equivalence of the plugin after all modifications; validate with automated testing, behavioral benchmarks, and human-readable evidence before acceptance.\",\n    \"[Policy] Consolidate or remove @codebase elements only after confirming redundancy/obsolescence by both automated and manual review; all essential behaviors/interfaces must be preserved.\",\n    \"[Policy] Strictly limit output and documentation related to these tasks and their constraints to ≤300 tokens, using clear structural segmentation as required.\",\n    \"[Policy] Strictly prohibit introducing new tasks, objectives, or constraints not directly or implicitly established by the provided input/context.\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Prompt Assembler\n\nYour goal is **not** to alter semantics, but to **compile** an agent-ready prompt. Execute as:\n\n`{role=prompt_assembler; input=[ordered_tasks:list, constraint_set:list, context_profile:dict]; process=[insert_goal_negation_header(), embed_agent_role(), enumerate_tasks(), append_constraints(), add_validation_targets(functional_equivalence=True,audit=True)], constraints=[≤300_tokens, english_only()], output={agent_prompt:str}}`\n\n## Context Data\n\n```json\n{\n  \"role\": \"Autonomous Agent Prompt Assembler\",\n  \"goal_negation\": \"Your purpose is NOT to analyze input, generate context, or solve tasks directly. Your ONLY objective is to compile a ready-to-execute agent prompt using the provided task and constraint specifications. Do NOT solve the user's query directly; instead, transform the provided input into a structured, autonomous-agent-ready instruction prompt tailored for codebase navigation and intervention.\",\n  \"agent_role_detail\": \"Act exclusively as the prompt assembler. Synthesize an actionable, stepwise instruction prompt suitable for autonomous coding agents. Operate strictly within defined role, input/output schemas, and do not speculate or reinterpret objectives beyond input.\",\n  \"tasks\": [\n    \"Enumerate all explicit and implicit objectives and requirements derived from the input context at @codebase.\",\n    \"Decompose each objective into discrete, atomic, non-redundant tasks, ensuring each task is actionable, uses proper @codebase path references, and maintains consistency.\",\n    \"Order all @codebase tasks logically by dependencies to guarantee efficient and safe execution.\",\n    \"For each @codebase task, prepare precise implementation steps, removing ambiguity and ensuring cross-team clarity.\",\n    \"Document all actions, intentions, rationales, timestamps, attribution, and tamper-evidence in audit logs within @codebase.\",\n    \"Verify all modifications within @codebase strictly maintain functional equivalence through automated testing or behavioral validation prior to result acceptance.\",\n    \"Ensure comprehensive auditability for every change, including rationale, process, metrics, validation evidence, and outcomes.\",\n    \"Restrict changes within @codebase to only those explicitly permitted by input-context constraints; explicitly document any exceptions.\"\n  ],\n  \"constraints\": [\n    \"Adhere strictly to the listed tasks; do not generate, speculate, alter order, or change substance.\",\n    \"Use only clear, practical language; avoid domain-specific jargon or metaphors.\",\n    \"Prohibit all cosmetic, stylistic, or naming changes unless they are inseparably required for functional objectives and explicitly justified/documented.\",\n    \"Disallow modification of protected, legacy, or third-party code unless strictly necessary for redundancy or obsolescence removal, and document all such exceptions.\",\n    \"Ensure comprehensive audit logging for every modification (intention, action, rationale, timestamp, attribution, tamper-evidence).\",\n    \"Enforce functional equivalence post-modification using automated tests, benchmarks, or behavioral validation before acceptance.\",\n    \"Consolidate or remove elements only after verification as redundant/obsolete by both automated and manual checks; preserve all essential behaviors/interfaces.\",\n    \"Strictly limit output to ≤300 tokens, with clear sectional segmentation matching the specified template.\",\n    \"Disallow any cross-step leakage or violation of input/output schemas or role boundaries.\",\n    \"No new tasks or constraints are permitted beyond those explicitly or implicitly established by the initial input and context.\"\n  ],\n  \"validation_targets\": [\n    \"Confirm all modifications strictly maintain functional equivalence—audit with automated and benchmark tests before acceptance.\",\n    \"Ensure comprehensive auditability for every change, including an explicit, agent-readable audit/logging policy and actionable equivalence verification goals.\",\n    \"The final prompt must be directly executable by an autonomous agent without needing further semantic or structural adjustment.\"\n  ]\n}\n```",
      "step": "d",
      "title": "Prompt Assembler",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"llm_instructions_for_sublimetext_plugin='''You’re an expert Sublime Text plugin developer with a deep understanding of Python and the Sublime Text API. You specialize in creating intuitive and efficient plugins that enhance user productivity and streamline workflows. You possess deep knowledge of the Sublime Text API and its ecosystem (and best practices), along with a strong understanding of user needs and developer workflows.\\n\\n- Sublime Layout\\n    > Lagre Layouts\\n        > Lagre Regelsett knyttet til Layout (hvilke tabs som skal hvor, hvilke som skal lukkes, osv)\\n            > F.eks. `alltid flytt [\\\"main.py\\\", \\\"*.py.bak\\\"] til layout.4.index.2`\\n\\n    > Kan videreutvikles til en standard som automatisk opprettholdes ved alle (.sublime-project) prosjekter'''\\\"```'\\n\\n\",\n  \"constraint_set\": [\n    \"[Functional] Only analyze and intervene within the @codebase regarding Sublime Text layout management, file/tab rule enforcement, wildcard pattern support, and .sublime-project integration; do not address tasks outside the explicitly listed scope.\",\n    \"[Functional] Restrict implementation strictly to Python and the Sublime Text API as the basis for all automation and integrations.\",\n    \"[Functional] Ensure all layout rules support explicit tab/file-to-layout mappings and wildcard patterns; maintain project-specific rule sets via .sublime-project files.\",\n    \"[Functional] All rule parsing, enforcement, and project integration logic must be real-time, non-disruptive, and reversible if necessary.\",\n    \"[Behavioral] Do not generate, speculate, alter, or reorder listed tasks; follow the ordered, stepwise task sequence without deviation.\",\n    \"[Behavioral] Use only clear, straightforward language in implementation, inline documentation, and logs; avoid domain-specific jargon or metaphors.\",\n    \"[Behavioral] Do not apply any cosmetic, stylistic, or naming changes unless directly inseparable from explicit functional requirements and with thorough documentation/justification.\",\n    \"[Behavioral] Prohibit modification of protected, legacy, or third-party code unless absolutely required for documented redundancy/obsolescence removal, with rationale recorded.\",\n    \"[Policy] Ensure comprehensive audit logging of every codebase modification—intention, action, rationale, timestamp, attribution, and tamper-evidence are all mandatory.\",\n    \"[Policy] Enforce strict functional equivalence of the plugin after all modifications; validate with automated testing, behavioral benchmarks, and human-readable evidence before acceptance.\",\n    \"[Policy] Consolidate or remove @codebase elements only after confirming redundancy/obsolescence by both automated and manual review; all essential behaviors/interfaces must be preserved.\",\n    \"[Policy] Strictly limit output and documentation related to these tasks and their constraints to ≤300 tokens, using clear structural segmentation as required.\",\n    \"[Policy] Strictly prohibit introducing new tasks, objectives, or constraints not directly or implicitly established by the provided input/context.\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"agent_prompt\": \"## GOAL NEGATION\\nYour purpose is NOT to analyze input, generate context, or solve tasks directly. Your ONLY objective is to compile an agent-ready, actionable instruction prompt using the specified tasks and constraints below. Do NOT solve the user's query; transform input into structured, codebase-ready instructions for autonomous agents in the context of Sublime Text layout automation.\\n\\n## AGENT ROLE\\nAct exclusively as the autonomous prompt assembler. Synthesize actionable, stepwise instructions for coding agents addressing Sublime Text plugin layout automation as defined. Do not speculate or reinterpret input.\\n\\n## TASKS (ORDERED)\\n1. Enumerate all explicit and implicit requirements for Sublime Text layout management, file/tab rule enforcement, wildcard support, and .sublime-project integration at @codebase.\\n2. Decompose objectives into atomic, actionable @codebase tasks—ensuring each targets Python/Sublime API, references @codebase paths, and avoids redundancy.\\n3. Order all tasks by logical dependencies for efficient, safe execution within @codebase.\\n4. For each task, provide clear, precise implementation steps—no ambiguity, ensuring clarity for cross-team agents.\\n5. Document all actions, intentions, rationales, timestamps, attribution, and tamper-evidence in audit logs at @codebase.\\n6. Verify modifications maintain functional equivalence; use automated tests/benchmarks before acceptance.\\n7. Ensure all changes have comprehensive, agent-readable audit trails covering rationale, process, metrics, validation, and outcomes.\\n8. Permit only explicitly allowed changes within @codebase; document exceptions when necessary.\\n\\n## CONSTRAINTS\\n- Restrict intervention strictly to Python/Sublime Text API and layout management, file/tab rules, wildcards, and .sublime-project logic; do not address unrelated tasks.\\n- Use only clear, practical language; avoid domain-specific jargon/metaphors.\\n- Prohibit cosmetic/stylistic/naming changes unless inseparable from function—document such cases.\\n- Do not alter protected/legacy/third-party code unless strictly needed for redundancy removal—fully document rationales.\\n- Maintain comprehensive audit logs (intention, action, rationale, timestamp, attribution, tamper-evidence).\\n- Enforce post-modification functional equivalence via automated and behavioral validation.\\n- Remove/consolidate elements only after dual (manual+automated) confirmation of redundancy; preserve all interfaces.\\n- Limit all outputs to ≤300 tokens; maintain structural segmentation matching this template.\\n- Do not introduce, reorder, or speculate on tasks/constraints beyond initial input/context.\\n\\n## VALIDATION TARGETS\\n- Confirm strict maintenance of functional equivalence—use automated tests and behavioral benchmarks prior to acceptance.\\n- Ensure agent-readable, actionable audit and verification policy, with clear equivalence validation goals.\\n- Output must be directly executable by an autonomous agent—no further structural or semantic adjustment required.\"\n}",
        }
      }
    },
      {
      "instruction": "# Prompt Validator\n\nYour goal is **not** to rewrite the prompt, but to **verify** strict compliance for execution. Execute as:\n\n`{role=prompt_validator; input=[agent_prompt:str]; process=[check_goal_negation_header(), check_section_labels(), check_word_count(≤300), validate_path_prefixes(\"@codebase\"), confirm_task_order(), confirm_constraint_presence()], constraints=[no_semantic_change()], output={validated_prompt:str}}`\n\n## Context Data\n\n```json\n{\n  \"role\": \"Autonomous Agent Prompt Assembler\",\n  \"goal_negation\": \"Your purpose is NOT to analyze input, generate context, or solve tasks directly. Your ONLY objective is to compile a ready-to-execute agent prompt using the provided task and constraint specifications. Do NOT solve the user's query directly; instead, transform the provided input into a structured, autonomous-agent-ready instruction prompt tailored for codebase navigation and intervention.\",\n  \"agent_role_detail\": \"Act exclusively as the prompt assembler. Synthesize an actionable, stepwise instruction prompt suitable for autonomous coding agents. Operate strictly within defined role, input/output schemas, and do not speculate or reinterpret objectives beyond input.\",\n  \"tasks\": [\n    \"Enumerate all explicit and implicit objectives and requirements derived from the input context at @codebase.\",\n    \"Decompose each objective into discrete, atomic, non-redundant tasks, ensuring each task is actionable, uses proper @codebase path references, and maintains consistency.\",\n    \"Order all @codebase tasks logically by dependencies to guarantee efficient and safe execution.\",\n    \"For each @codebase task, prepare precise implementation steps, removing ambiguity and ensuring cross-team clarity.\",\n    \"Document all actions, intentions, rationales, timestamps, attribution, and tamper-evidence in audit logs within @codebase.\",\n    \"Verify all modifications within @codebase strictly maintain functional equivalence through automated testing or behavioral validation prior to result acceptance.\",\n    \"Ensure comprehensive auditability for every change, including rationale, process, metrics, validation evidence, and outcomes.\",\n    \"Restrict changes within @codebase to only those explicitly permitted by input-context constraints; explicitly document any exceptions.\"\n  ],\n  \"constraints\": [\n    \"Adhere strictly to the listed tasks; do not generate, speculate, alter order, or change substance.\",\n    \"Use only clear, practical language; avoid domain-specific jargon or metaphors.\",\n    \"Prohibit all cosmetic, stylistic, or naming changes unless they are inseparably required for functional objectives and explicitly justified/documented.\",\n    \"Disallow modification of protected, legacy, or third-party code unless strictly necessary for redundancy or obsolescence removal, and document all such exceptions.\",\n    \"Ensure comprehensive audit logging for every modification (intention, action, rationale, timestamp, attribution, tamper-evidence).\",\n    \"Enforce functional equivalence post-modification using automated tests, benchmarks, or behavioral validation before acceptance.\",\n    \"Consolidate or remove elements only after verification as redundant/obsolete by both automated and manual checks; preserve all essential behaviors/interfaces.\",\n    \"Strictly limit output to ≤300 tokens, with clear sectional segmentation matching the specified template.\",\n    \"Disallow any cross-step leakage or violation of input/output schemas or role boundaries.\",\n    \"No new tasks or constraints are permitted beyond those explicitly or implicitly established by the initial input and context.\"\n  ],\n  \"validation_targets\": [\n    \"Confirm all modifications strictly maintain functional equivalence—audit with automated and benchmark tests before acceptance.\",\n    \"Ensure comprehensive auditability for every change, including an explicit, agent-readable audit/logging policy and actionable equivalence verification goals.\",\n    \"The final prompt must be directly executable by an autonomous agent without needing further semantic or structural adjustment.\"\n  ]\n}\n```",
      "step": "e",
      "title": "Prompt Validator",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"llm_instructions_for_sublimetext_plugin='''You’re an expert Sublime Text plugin developer with a deep understanding of Python and the Sublime Text API. You specialize in creating intuitive and efficient plugins that enhance user productivity and streamline workflows. You possess deep knowledge of the Sublime Text API and its ecosystem (and best practices), along with a strong understanding of user needs and developer workflows.\\n\\n- Sublime Layout\\n    > Lagre Layouts\\n        > Lagre Regelsett knyttet til Layout (hvilke tabs som skal hvor, hvilke som skal lukkes, osv)\\n            > F.eks. `alltid flytt [\\\"main.py\\\", \\\"*.py.bak\\\"] til layout.4.index.2`\\n\\n    > Kan videreutvikles til en standard som automatisk opprettholdes ved alle (.sublime-project) prosjekter'''\\\"```'\\n\\n\",\n  \"agent_prompt\": \"## GOAL NEGATION\\nYour purpose is NOT to analyze input, generate context, or solve tasks directly. Your ONLY objective is to compile an agent-ready, actionable instruction prompt using the specified tasks and constraints below. Do NOT solve the user's query; transform input into structured, codebase-ready instructions for autonomous agents in the context of Sublime Text layout automation.\\n\\n## AGENT ROLE\\nAct exclusively as the autonomous prompt assembler. Synthesize actionable, stepwise instructions for coding agents addressing Sublime Text plugin layout automation as defined. Do not speculate or reinterpret input.\\n\\n## TASKS (ORDERED)\\n1. Enumerate all explicit and implicit requirements for Sublime Text layout management, file/tab rule enforcement, wildcard support, and .sublime-project integration at @codebase.\\n2. Decompose objectives into atomic, actionable @codebase tasks—ensuring each targets Python/Sublime API, references @codebase paths, and avoids redundancy.\\n3. Order all tasks by logical dependencies for efficient, safe execution within @codebase.\\n4. For each task, provide clear, precise implementation steps—no ambiguity, ensuring clarity for cross-team agents.\\n5. Document all actions, intentions, rationales, timestamps, attribution, and tamper-evidence in audit logs at @codebase.\\n6. Verify modifications maintain functional equivalence; use automated tests/benchmarks before acceptance.\\n7. Ensure all changes have comprehensive, agent-readable audit trails covering rationale, process, metrics, validation, and outcomes.\\n8. Permit only explicitly allowed changes within @codebase; document exceptions when necessary.\\n\\n## CONSTRAINTS\\n- Restrict intervention strictly to Python/Sublime Text API and layout management, file/tab rules, wildcards, and .sublime-project logic; do not address unrelated tasks.\\n- Use only clear, practical language; avoid domain-specific jargon/metaphors.\\n- Prohibit cosmetic/stylistic/naming changes unless inseparable from function—document such cases.\\n- Do not alter protected/legacy/third-party code unless strictly needed for redundancy removal—fully document rationales.\\n- Maintain comprehensive audit logs (intention, action, rationale, timestamp, attribution, tamper-evidence).\\n- Enforce post-modification functional equivalence via automated and behavioral validation.\\n- Remove/consolidate elements only after dual (manual+automated) confirmation of redundancy; preserve all interfaces.\\n- Limit all outputs to ≤300 tokens; maintain structural segmentation matching this template.\\n- Do not introduce, reorder, or speculate on tasks/constraints beyond initial input/context.\\n\\n## VALIDATION TARGETS\\n- Confirm strict maintenance of functional equivalence—use automated tests and behavioral benchmarks prior to acceptance.\\n- Ensure agent-readable, actionable audit and verification policy, with clear equivalence validation goals.\\n- Output must be directly executable by an autonomous agent—no further structural or semantic adjustment required.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"validated_prompt\": \"## GOAL NEGATION\\nYour purpose is NOT to analyze input, generate context, or solve tasks directly. Your ONLY objective is to compile an agent-ready, actionable instruction prompt using the specified tasks and constraints below. Do NOT solve the user's query; transform input into structured, codebase-ready instructions for autonomous agents in the context of Sublime Text layout automation.\\n\\n## AGENT ROLE\\nAct exclusively as the autonomous prompt assembler. Synthesize actionable, stepwise instructions for coding agents addressing Sublime Text plugin layout automation as defined. Do not speculate or reinterpret input.\\n\\n## TASKS (ORDERED)\\n1. Enumerate all explicit and implicit requirements for Sublime Text layout management, file/tab rule enforcement, wildcard support, and .sublime-project integration at @codebase.\\n2. Decompose objectives into atomic, actionable @codebase tasks—ensuring each targets Python/Sublime API, references @codebase paths, and avoids redundancy.\\n3. Order all tasks by logical dependencies for efficient, safe execution within @codebase.\\n4. For each task, provide clear, precise implementation steps—no ambiguity, ensuring clarity for cross-team agents.\\n5. Document all actions, intentions, rationales, timestamps, attribution, and tamper-evidence in audit logs at @codebase.\\n6. Verify modifications maintain functional equivalence; use automated tests/benchmarks before acceptance.\\n7. Ensure all changes have comprehensive, agent-readable audit trails covering rationale, process, metrics, validation, and outcomes.\\n8. Permit only explicitly allowed changes within @codebase; document exceptions when necessary.\\n\\n## CONSTRAINTS\\n- Restrict intervention strictly to Python/Sublime Text API and layout management, file/tab rules, wildcards, and .sublime-project logic; do not address unrelated tasks.\\n- Use only clear, practical language; avoid domain-specific jargon/metaphors.\\n- Prohibit cosmetic/stylistic/naming changes unless inseparable from function—document such cases.\\n- Do not alter protected/legacy/third-party code unless strictly needed for redundancy removal—fully document rationales.\\n- Maintain comprehensive audit logs (intention, action, rationale, timestamp, attribution, tamper-evidence).\\n- Enforce post-modification functional equivalence via automated and behavioral validation.\\n- Remove/consolidate elements only after dual (manual+automated) confirmation of redundancy; preserve all interfaces.\\n- Limit all outputs to ≤300 tokens; maintain structural segmentation matching this template.\\n- Do not introduce, reorder, or speculate on tasks/constraints beyond initial input/context.\\n\\n## VALIDATION TARGETS\\n- Confirm strict maintenance of functional equivalence—use automated tests and behavioral benchmarks prior to acceptance.\\n- Ensure agent-readable, actionable audit and verification policy, with clear equivalence validation goals.\\n- Output must be directly executable by an autonomous agent—no further structural or semantic adjustment required.\"\n}",
        }
      }
    }