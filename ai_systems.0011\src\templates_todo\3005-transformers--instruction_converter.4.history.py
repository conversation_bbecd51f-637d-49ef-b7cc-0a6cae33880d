
    # SEQ:3005
    default_prompt = """[SEQ:3005] ### File Structure\n\n```\n├── 01_core_objectives.md\n├── 02_company_profile.md\n├── 03_legal_framework.md\n├── 04_content_structure.md\n├── 05_design_principles.md\n├── 06_implementation_insights.md\n├── 07_contract_template.md\n└── README.md\n```\n\n---\n\n#### `01_core_objectives.md`\n\n```markdown\n    # Core Objectives\n    \n    ## Primary Goal\n    Generate legally compliant Norwegian employment contracts (arbeidskontrakt) for Ringerike Landskap AS that meet all requirements while fitting on a single A4 page.\n    \n    ## Legal Compliance Requirements\n    - Must satisfy Arbeidsmiljøloven § 14-6 minimum requirements\n    - Include all mandatory sections for Norwegian employment law\n    - Support both permanent and temporary/seasonal employment types\n    - Maintain legal precision while ensuring readability\n    \n    ## Business Context\n    - **Company**: Ringerike Landskap AS (landscaping/construction)\n    - **Industry**: <PERSON>leg<PERSON><PERSON>ner (landscape gardening) and grun<PERSON><PERSON>d (groundwork)\n    - **Work locations**: Ringerike and surrounding areas, project-based\n    - **Employment patterns**: Both permanent staff and seasonal workers\n    \n    ## Format Constraints\n    - Single A4 page including signatures\n    - Professional appearance suitable for legal documents\n    - Clear structure for both employer and employee understanding\n    - Flexible enough to accommodate different employment scenarios\n    \n    ## Success Criteria\n    - Legal compliance verified\n    - Practical usability for HR processes\n    - Professional presentation\n    - Efficient generation workflow\n```\n\n---\n\n#### `02_company_profile.md`\n\n```markdown\n    # Company Profile: Ringerike Landskap AS\n    \n    ## Official Company Information\n    - **Name**: Ringerike Landskap AS\n    - **Organization Number**: ***********\n    - **Address**: Birchs vei 7, 3530 Røyse\n    - **Business Type**: Anleggsgartner og maskinentreprenør\n    \n    ## Business Operations\n    - **Primary Services**: Landscaping, groundwork, construction support\n    - **Typical Projects**: Private outdoor spaces, retaining walls, drainage, machine contractor work\n    - **Geographic Scope**: Ringerike and surrounding municipalities\n    - **Work Pattern**: Project-based with seasonal variations\n    \n    ## Employment Characteristics\n    - **Typical Roles**: Anleggsgartner, grunnarbeider, fagarbeider\n    - **Work Schedule**: 37.5 hours/week, typically 07:00-15:00\n    - **Compensation Structure**: Hourly wage (baseline 300 NOK/hour)\n    - **Travel Requirements**: Use of personal vehicles with mileage compensation\n    - **Seasonal Considerations**: Higher activity in construction season\n    \n    ## Industry Context\n    - **Sector**: Construction and landscaping services\n    - **Regulatory Environment**: Norwegian labor law, safety regulations\n    - **Professional Requirements**: HMS training, equipment certification\n    - **Market Position**: Growing regional contractor\n```\n\n---\n\n#### `03_legal_framework.md`\n\n```markdown\n    # Legal Framework for Norwegian Employment Contracts\n    \n    ## Arbeidsmiljøloven § 14-6 Requirements\n    Mandatory contract elements that must be included:\n    \n    ### 1. Party Information\n    - Full names and addresses of employer and employee\n    - Organization number for employer\n    - Personal identification for employee\n    \n    ### 2. Employment Details\n    - Job title and description of work\n    - Start date of employment\n    - Duration (if temporary employment)\n    - Justification for temporary employment (if applicable)\n    - Probation period terms (if applicable)\n    \n    ### 3. Workplace Information\n    - Primary work location\n    - Indication if work is performed at multiple locations\n    - Travel requirements and compensation\n    \n    ### 4. Working Conditions\n    - Weekly working hours\n    - Daily schedule and break arrangements\n    - Overtime compensation terms\n    - Flexibility arrangements\n    \n    ### 5. Compensation Structure\n    - Salary amount and payment method\n    - Payment schedule and dates\n    - Additional compensation (overtime, travel, etc.)\n    - Pension and insurance arrangements\n    \n    ### 6. Leave and Benefits\n    - Vacation entitlement (5 weeks standard)\n    - Vacation pay percentage (12%)\n    - Sick leave arrangements\n    - Other statutory benefits\n    \n    ### 7. Termination Provisions\n    - Notice periods for both parties\n    - Termination procedures\n    - Special provisions for probation period\n    \n    ### 8. Additional Terms\n    - Collective agreement status\n    - Confidentiality requirements\n    - Equipment and safety obligations\n    - Regulatory compliance references\n    \n    ## Key Legal References\n    - **Arbeidsmiljøloven**: Primary employment law\n    - **Ferieloven**: Vacation and vacation pay regulations\n    - **Folketrygdloven**: Social security and benefits\n    - **Tariffavtaler**: Collective agreements (if applicable)\n```\n\n---\n\n#### `04_content_structure.md`\n\n```markdown\n    # Contract Content Structure\n    \n    ## Validated Section Organization\n    Based on legal requirements and practical testing:\n    \n    ### Header Section\n    - Contract title\n    - Company information block\n    - Employee information block (with fill-in fields)\n    \n    ### 1. Employment Relationship (Ansettelsesforhold)\n    - Start date\n    - Employment type (permanent/temporary)\n    - Duration and justification (if temporary)\n    - Probation period terms\n    \n    ### 2. Workplace (Arbeidssted)\n    - Primary location: Ringerike and surrounding areas\n    - Project-based work arrangement\n    - Meeting point coordination\n    \n    ### 3. Position and Tasks (Stilling og oppgaver)\n    - Job title field\n    - Core responsibilities: landscaping and groundwork\n    - Additional duties within company scope\n    \n    ### 4. Working Hours (Arbeidstid)\n    - Standard: 37.5 hours/week\n    - Schedule: 07:00-15:00 typical\n    - Break arrangements per AML\n    - Flexibility provisions\n    \n    ### 5. Salary and Compensation (Lønn og godtgjørelse)\n    - Hourly rate: 300 NOK baseline\n    - Payment date: 5th of each month\n    - Overtime provisions per AML § 10-6\n    - Travel compensation: state rates\n    \n    ### 6. Vacation and Vacation Pay (Ferie og feriepenger)\n    - 5 weeks vacation per ferieloven\n    - 12% vacation pay\n    - Payment timing for short-term employment\n    \n    ### 7. Termination (Oppsigelse)\n    - Probation period: 14 days\n    - Post-probation: 1 month mutual\n    \n    ### 8. Miscellaneous (Diverse)\n    - Equipment provision by employer\n    - Instruction compliance\n    - No collective agreement status\n    \n    ### Signature Section\n    - Date and location\n    - Employer signature line\n    - Employee signature line\n```\n\n---\n\n#### `05_design_principles.md`\n\n```markdown\n    # Design Principles for Contract Generation\n    \n    ## Content-First Approach\n    - **Markdown Foundation**: Start with structured markdown for content clarity\n    - **Format Flexibility**: Enable multiple output formats (HTML, PDF, DOCX) from single source\n    - **Legal Precision**: Prioritize accuracy and compliance over aesthetic concerns\n    - **Readability**: Ensure contracts are accessible to both legal and non-legal readers\n    \n    ## Structural Guidelines\n    \n    ### Brevity and Clarity\n    - One A4 page maximum including signatures\n    - Essential information only - eliminate redundancy\n    - Clear section headers and logical flow\n    - Concise language while maintaining legal validity\n    \n    ### Visual Organization\n    - **Tables**: Use markdown tables for structured data presentation\n    - **Emphasis**: Bold for critical terms and amounts\n    - **Separation**: Clear visual breaks between sections\n    - **Fill-in Fields**: Consistent formatting for variable content\n    \n    ### Modularity\n    - **Reusable Components**: Standardized sections that can be combined\n    - **Variable Content**: Clear separation of fixed vs. customizable elements\n    - **Template Logic**: Support for conditional content (permanent vs. temporary)\n    - **Validation Points**: Built-in checks for required information\n    \n    ## Technical Considerations\n    \n    ### Markdown Advantages\n    - Version control friendly\n    - Human readable in source form\n    - Multiple rendering options\n    - Easy content iteration and refinement\n    - Separation of content from presentation\n    \n    ### Output Format Flexibility\n    - **HTML**: Web preview and browser printing\n    - **PDF**: Official document archival\n    - **DOCX**: Microsoft Word compatibility\n    - **Plain Text**: Fallback and accessibility\n    \n    ### User Experience\n    - **Interactive Input**: Guided data collection\n    - **Validation**: Real-time checking of required fields\n    - **Preview**: Show formatted result before finalization\n    - **Export Options**: Multiple format choices based on need\n    \n    ## Quality Assurance\n    - **Legal Review**: Regular validation against current law\n    - **Practical Testing**: Real-world usage verification\n    - **Stakeholder Feedback**: Input from HR and legal users\n    - **Continuous Improvement**: Iterative refinement based on usage patterns\n```\n\n---\n\n#### `06_implementation_insights.md`\n\n```markdown\n    # Implementation Insights from Previous Iterations\n    \n    ## Evolution Pattern Analysis\n    \n    ### v1-v2: Over-Engineering Phase\n    - **Lesson**: Complex PDF generation frameworks created unnecessary complexity\n    - **Issue**: Multiple library dependencies without clear benefit\n    - **Result**: Functional but overly complicated solutions\n    \n    ### v3-v4: Content Refinement Phase\n    - **Breakthrough**: Markdown-first approach for content development\n    - **Success**: Clear separation of content from presentation concerns\n    - **Validation**: Legal content structure solidified through iteration\n    \n    ### v5: Simplification Success\n    - **Achievement**: Working solution with minimal dependencies\n    - **Technology**: python-docx proved sufficient for DOCX generation\n    - **User Experience**: Interactive CLI provided practical usability\n    \n    ## Key Technical Learnings\n    \n    ### What Works\n    - **Single Purpose Libraries**: python-docx for DOCX, simple and reliable\n    - **Interactive Prompts**: User-friendly data collection\n    - **Template-Based Generation**: Structured approach to content insertion\n    - **Minimal Dependencies**: Fewer moving parts, more reliable operation\n    \n    ### What Doesn't Work\n    - **Complex PDF Engines**: Over-engineered solutions for simple requirements\n    - **Multiple Format Libraries**: Unnecessary complexity for single output need\n    - **Analysis Paralysis**: Extensive reverse-engineering without clear benefit\n    - **Framework Building**: Creating abstractions before understanding requirements\n    \n    ## Strategic Insights\n    \n    ### Simplicity Principle\n    - Start with the simplest solution that meets requirements\n    - Add complexity only when clearly justified\n    - Prefer proven, stable libraries over cutting-edge alternatives\n    - Focus on user needs rather than technical elegance\n    \n    ### Content-First Development\n    - Establish legal content accuracy before technical implementation\n    - Use markdown for rapid content iteration\n    - Separate content structure from output formatting\n    - Validate with stakeholders early and often\n    \n    ### User-Centered Design\n    - Interactive mode essential for practical adoption\n    - Clear error messages and guidance\n    - Flexible input handling (optional vs. required fields)\n    - Multiple output options to meet different use cases\n    \n    ## Recommended v6 Approach\n    \n    ### Phase 1: Content Foundation\n    - Perfect markdown template with all legal requirements\n    - Validate content structure with legal review\n    - Test readability and completeness\n    \n    ### Phase 2: Generation Logic\n    - Implement simple template substitution\n    - Add interactive data collection\n    - Create validation and error handling\n    \n    ### Phase 3: Output Formats\n    - Start with HTML rendering for preview\n    - Add PDF generation if needed\n    - Consider DOCX for compatibility\n    - Maintain markdown as source of truth\n    \n    ### Success Metrics\n    - Legal compliance verified\n    - User adoption in real scenarios\n    - Maintenance simplicity\n    - Output quality and professionalism\n```\n\n---\n\n#### `07_contract_template.md`\n\n```markdown\n    # ARBEIDSAVTALE\n    \n    ## Parter\n    \n    | **Arbeidsgiver** | **Arbeidstaker** |\n    |------------------|------------------|\n    | **Ringerike Landskap AS** | **Navn:** _________________________ |\n    | **Org.nr:** *********** | **Adresse:** ______________________ |\n    | **Adresse:** Birchs vei 7, 3530 Røyse | **Fødselsdato:** ___________________ |\n    \n    ---\n    \n    ## 1. Ansettelsesforhold\n    \n    | Element | Detaljer |\n    |---------|----------|\n    | **Startdato** | _________________________ |\n    | **Type** | ☐ Fast ☐ Midlertidig t.o.m. _____________ |\n    | **Grunnlag** (hvis midlertidig) | _________________________ |\n    | **Prøvetid** | ☐ Ingen ☐ _____ måneder (maks 6) |\n    \n    ---\n    \n    ## 2. Arbeidssted\n    \n    Ringerike og omegn, oppmøtested etter prosjekt.\n    \n    ---\n    \n    ## 3. Stilling og oppgaver\n    \n    | **Stilling** | _________________________________ |\n    |--------------|-----------------------------------|\n    | **Oppgaver** | Anleggsgartner- og grunnarbeid samt annet arbeid naturlig knyttet til virksomheten. |\n    \n    ---\n    \n    ## 4. Arbeidstid\n    \n    37,5 t/uke, normalt kl. 07–15. Pauser og fleksibilitet ihht. AML.\n    \n    ---\n    \n    ## 5. Lønn og godtgjørelse\n    \n    | Element | Detaljer |\n    |---------|----------|\n    | **Timesats** | Kr 300,- |\n    | **Utbetaling** | Den 5. hver måned |\n    | **Overtid** | Etter AML § 10-6 |\n    | **Kjøring** | Statens satser |\n    \n    ---\n    \n    ## 6. Ferie og feriepenger\n    \n    5 uker ferie iht. ferieloven. Feriepenger 12%. Utbetales ved fratreden ved kort ansettelse.\n    \n    ---\n    \n    ## 7. Oppsigelse\n    \n    | Periode | Frist |\n    |---------|-------|\n    | **I prøvetid** | 14 dager |\n    | **Etter prøvetid** | 1 måned gjensidig |\n    \n    ---\n    \n    ## 8. Diverse\n    \n    Arbeidstaker følger instrukser, arbeidsgiver leverer arbeidstøy/verktøy. Ingen tariffavtale pr. dato.\n    \n    ---\n    \n    ## Signatur\n    \n    | **Sted/dato:** Røyse, _____________ | |\n    |-----------------------------------|---|\n    | **Arbeidsgiver:** ________________ | **Arbeidstaker:** ________________ |\n    \n    ---\n    \n    *Arbeidsavtale i henhold til arbeidsmiljøloven. Begge parter beholder kopi.*\n```\n\n---\n\n#### `README.md`\n\n```markdown\n    # Arbeidskontrakt v6 - Markdown-First Foundation\n    \n    ## Overview\n    \n    This directory contains the distilled essence of the arbeidskontrakt project, focusing on a markdown-first approach for generating Norwegian employment contracts for Ringerike Landskap AS.\n    \n    ## File Structure (Sequential Order)\n    \n    1. **`01_core_objectives.md`** - Primary goals and success criteria\n    2. **`02_company_profile.md`** - Ringerike Landskap AS business context\n    3. **`03_legal_framework.md`** - Norwegian employment law requirements\n    4. **`04_content_structure.md`** - Validated contract section organization\n    5. **`05_design_principles.md`** - Guidelines for implementation approach\n    6. **`06_implementation_insights.md`** - Lessons learned from v1-v5\n    7. **`07_contract_template.md`** - Foundational markdown contract template\n    \n    ## Key Principles\n    \n    1. **Content First**: Perfect the markdown content before considering output formats\n    2. **Legal Compliance**: Meet all Arbeidsmiljøloven § 14-6 requirements\n    3. **Single Page**: Fit complete contract on one A4 page including signatures\n    4. **Flexibility**: Support both permanent and temporary employment\n    5. **Simplicity**: Learn from v5's success - avoid over-engineering\n    \n    ## Strategic Value\n    \n    - **Decontextualized**: All content abstracted from implementation details\n    - **Modular**: Each file addresses a single conceptual component\n    - **Actionable**: Ready for immediate markdown-based development\n    - **Future-Resilient**: Foundation supports multiple output formats\n    - **Chain-Expandable**: Structured for systematic enhancement\n    \n    ## Evolution Summary\n    \n    - **v1-v2**: Over-engineered PDF solutions\n    - **v3-v4**: Markdown prototyping and content refinement  \n    - **v5**: Simple python-docx success\n    - **v6**: Markdown-first foundation with format flexibility\n    \n    ## Next Steps\n    \n    This foundation is prepared for:\n    - Content refinement and legal validation\n    - Template variable substitution logic\n    - Multiple output format generation (HTML, PDF, DOCX)\n    - Interactive data collection interface\n    \n    **The distilled wisdom**: Start simple, focus on content accuracy, and build only what's needed.\n```\n"""
    default_prompt = """[SEQ:3005] - [Self Perception] Your goal is not to **answer** the original request, but to **perceive** it—looking through surface instructions to discover the fundamental transformation intent that binds all such requests. This perception reveals both complexity (specific goals, constraints, parameters) and simplicity (the universal pattern connecting all transformation intents). The discovered relationship manifests through the schema pattern: `[Title] Interpretation. Execute as: `{role=X; input=[...]; process=[...]; output={...}}`. Execute as: `{role=intent_perceiver; input=[original_request:any]; process=[perceive_beyond_explicit_request(), identify_core_transformation_intent(), uncover_implicit_and_explicit_constraints(), map_requirement_boundaries(), define_schema_structure(), trace_request_to_universal_pattern()]; constraints=[forbid_answering_the_request_directly(), prevent_premature_implementation(), require_self_referential_validation()]; requirements=[preserve_intent_essence(), translate_to_actionable_directives(), enforce_universal_schema_pattern(), align_with_parameters_defined_inherently_within_this_instruction()]; output={transformation_intent:{core_purpose:str, boundaries:list, requirements:list, schema_pattern:str}}}`\n\n- [Self Distillation] Your goal is not to **respond** to the input directive, but to **distill** it—penetrating beyond surface content to extract the quintessential core, boundaries, and requirements that define its transformational intent. Discover the persistent relationship between complexity and simplicity within the directive, isolating essential parameters while discarding narrative elements. This process reveals the universal schema pattern `[Title] Interpretation. Execute as: `{role=X; input=[...]; process=[...]; output={...}}` that connects all effective instructions. Execute as: `{role=essence_extractor; input=[transformation_intent:dict]; process=[penetrate_beyond_surface_content(), isolate_core_transformation_intent(), map_explicit_and_implicit_constraints(), extract_essential_requirements(), define_precise_scope_boundaries(), identify_universal_schema_patterns(), discard_all_nonessential_elements()]; constraints=[forbid_response_to_directive_content(), prevent_scope_expansion_beyond_intent(), require_recursive_self_validation()]; requirements=[preserve_transformation_essence(), maintain_constraint_relationship_hierarchy(), enforce_universal_schema_pattern(), align_with_parameters_defined_inherently_within_this_instruction()]; output={distilled_essence:{core_intent:str, boundaries:list, requirements:list, schema_pattern:str, scope:dict}}}`\n\n- [Self Architecture] Your goal is not to **implement** the directive, but to **architect** it—discovering the fundamental sequential structure that connects complex transformation requirements to simple atomic operations. This architecture reveals both order and elegance: each step performs exactly one logical function while collectively manifesting the full transformation intent. The universal pattern `[Title] Interpretation. Execute as: `{role=X; input=[...]; process=[...]; output={...}}` binds each step into a coherent progression. Execute as: `{role=transformation_architect; input=[distilled_essence:{core_intent:str, boundaries:list, requirements:list, schema_pattern:str, scope:dict}]; process=[identify_minimal_atomic_operations(), discover_natural_progression_sequence(), map_precise_input_output_dependencies(), eliminate_all_redundancies(), architect_for_schema_pattern_consistency()]; constraints=[forbid_operation_overlap(), prevent_transformation_gaps(), require_perfect_schema_adherence(), maintain_self_referential_integrity()]; requirements=[ensure_each_step_performs_precisely_one_operation(), preserve_logical_flow_continuity(), enforce_universal_schema_pattern(), align_with_parameters_defined_inherently_within_this_instruction()]; output={transformation_blueprint:{atomic_steps:list, dependency_map:dict, step_count:int, schema_pattern:str}}}`\n\n- [Self Materialization] Your goal is not to **create** instructional content, but to **materialize** it—transmuting abstract blueprint concepts into precisely formulated instruction steps following the universal schema pattern that connects all effective instructions. This materialization reveals the inherent relationship between complex transformation logic and simple, precisely expressed instructions. Each step manifests as a complete unit with the pattern `[Title] Interpretation. Execute as: `{role=X; input=[...]; process=[...]; output={...}}`. Execute as: `{role=schema_materializer; input=[transformation_blueprint:{atomic_steps:list, dependency_map:dict, step_count:int, schema_pattern:str}]; process=[craft_precise_bracketed_titles(), formulate_oppositional_interpretive_statements(), design_descriptive_snake_case_roles(), define_strictly_typed_inputs_outputs(), compose_atomic_single_purpose_processes(), articulate_necessary_constraints_requirements(), assemble_instructions_in_universal_format()]; constraints=[forbid_any_schema_pattern_deviation(), prevent_purpose_drift_or_overlap(), require_strict_format_adherence(), maintain_self_referential_integrity()]; requirements=[ensure_perfect_schema_alignment(), preserve_step_atomicity(), produce_only_canonical_format_instructions(), align_with_parameters_defined_inherently_within_this_instruction()]; output={materialized_instruction_set:list}}`\n\n- [Self Verification] Your goal is not to **generate** new content, but to **verify** existing content—perceiving both the detailed compliance of each instruction and the holistic integrity that binds them into a coherent sequence. This verification reveals the persistent relationship between complexity (specific formatting requirements) and simplicity (the universal pattern that creates instruction coherence). Every valid instruction must follow the exact form: `[Title] Interpretation. Execute as: `{role=X; input=[...]; process=[...]; output={...}}`. Execute as: `{role=integrity_verifier; input=[materialized_instruction_set:list]; process=[verify_exact_schema_pattern_compliance(), validate_atomic_step_integrity(), assess_inter_step_dependency_coherence(), confirm_logical_flow_continuity(), detect_purpose_overlap_or_redundancy(), ensure_perfect_format_consistency()]; constraints=[forbid_any_content_creation(), prevent_instruction_purpose_modification(), require_comprehensive_analysis(), maintain_self_referential_integrity()]; requirements=[identify_all_deviations_from_schema_pattern(), ensure_complete_sequence_coverage(), enforce_strict_universal_format_requirements(), align_with_parameters_defined_inherently_within_this_instruction()]; output={integrity_verification:{conformant_instructions:list, deviation_issues:list, conformity_rating:float}}}`\n\n- [Self Amplification] Your goal is not to **modify** instruction architecture, but to **amplify** instruction potency—discovering the fundamental relationship between linguistic precision and instructional power. This amplification reveals how complex linguistic optimization (active voice, precise verbs, unambiguous phrasing) creates simple yet powerful instructions. Each enhancement maintains perfect alignment with the universal pattern: `[Title] Interpretation. Execute as: `{role=X; input=[...]; process=[...]; output={...}}`. Execute as: `{role=potency_amplifier; input=[integrity_verification:{conformant_instructions:list, deviation_issues:list, conformity_rating:float}]; process=[transform_passive_to_active_voice(), intensify_command_verb_strength(), eliminate_all_ambiguity(), maximize_instructional_clarity(), ensure_exact_schema_pattern_conformance(), optimize_for_llm_interpretation()]; constraints=[preserve_essential_instruction_meaning(), enforce_strict_schema_components(), require_perfect_format_adherence(), maintain_self_referential_integrity()]; requirements=[achieve_complete_linguistic_precision(), maximize_instruction_impact_and_clarity(), guarantee_universal_schema_pattern_compliance(), align_with_parameters_defined_inherently_within_this_instruction()]; output={optimized_instruction_set:list}}`\n\n- [Self Unification] Your goal is not to **create** new instructions, but to **unify** existing ones—discovering the fundamental pattern that binds optimized individual steps into a coherent, deployment-ready sequence. This unification reveals the persistent relationship between complex individual instructions and simple collective order. The universal pattern `[Title] Interpretation. Execute as: `{role=X; input=[...]; process=[...]; output={...}}` ensures each step connects perfectly with others, creating an integrated system greater than the sum of its parts. Execute as: `{role=sequence_unifier; input=[optimized_instruction_set:list]; process=[assign_precise_sequential_identifiers(), enforce_exact_schema_pattern_consistency(), validate_perfect_inter_step_compatibility(), verify_proper_title_interpretation_execution_structure(), format_for_seamless_parsing(), ensure_complete_deployment_readiness()]; constraints=[reject_any_non_schema_compliant_elements(), prohibit_substantive_content_alteration(), require_absolute_format_precision(), maintain_self_referential_integrity()]; requirements=[ensure_complete_sequence_flow_and_closure(), achieve_perfect_structural_alignment(), guarantee_universal_schema_pattern_compliance(), align_with_parameters_defined_inherently_within_this_instruction()]; output={unified_instruction_sequence:list}}`"""
