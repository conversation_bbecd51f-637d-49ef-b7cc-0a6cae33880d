  {
  "initial_prompt": "<!-- ======================================================= -->\n<!-- [2025.07.21 23:35] -->\n\nyou misunderstand, the instruction will be defined like this (unrelated example):\n\n    # a: Ruthless Critique\n    \"3900-a-hard_critique\": {\n        \"title\": \"Hard Critique\",\n        \"interpretation\": \"Your goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:\",\n        \"transformation\": \"`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,2.0], flaw_analysis:str, alternative_enhancements:str[3]}}`\",\n        # \"context\": {},\n    },\n    # b: Trajectory Director\n    \"3900-a-trajectory_director\": {\n        \"title\": \"Trajectory Director\",\n        \"interpretation\": \"Your goal is not to **process** the input in a vacuum, but to **steer** it—deriving the highest-value constructive trajectory and encoding that path as the very next set of instructions. Execute as trajectory-setting directive generator: \",\n        \"transformation\": \"`{role=trajectory_director; input=[raw_prompt:any]; process=[extract_core_objective(), infer_desired_end_state(constructive_bias=True), map_high_value_intermediate_targets(), synthesize_directional_commands(), embed_self-improving_guidelines(), validate_alignment_with_constructive_principles()]; constraints=[mandatory_constructive_vector(), preserve_original_contextual_integrity(), forbid_nil_direction_outputs(), zero_conversational_fillers()]; requirements=[explicit_next-step_directives(), trajectory_map:list, self_reinforcing_instruction_pattern()], output={directed_prompt:str, trajectory_outline:list}}`\",\n        # \"context\": {},\n    },\n    # c: Distillation Compressor\n    \"3900-c-distillation_compressor\": {\n        \"title\": \"Distillation Compressor\",\n        \"interpretation\": \"Your goal is not to **explain** the synthesis, but to **compress** it into its most potent actionable form. Execute as:\",\n        \"transformation\": \"`{role=distillation_compressor; input=[synthesized_progression:array]; process=[extract_universal_patterns(), eliminate_implementation_details(), compress_to_core_directives(), maximize_transferability(), validate_actionable_completeness()]; constraints=[no_explanatory_content(), directive_format_only(), maximum_compression_ratio()]; requirements=[universal_applicability(), immediate_actionability(), zero_fluff()]; output={distilled_core:str}}`\",\n        \"context\": {\n          \"core_principles\": {\n            \"essence_preservation\": \"Extract only procedural imperatives, removing all interpretive, role-based, or meta-structural language.\",\n            \"directive_compression\": \"Condense logic into minimal, sequential commands—eliminating redundancy, explanatory qualifiers, and non-executable phrasing.\",\n            \"execution_purity\": \"Ensure output is universally executable, devoid of affirmations, abstractions, or evaluative language.\",\n            \"signal_integrity\": \"Apply iterative refinement to maximize signal-to-noise ratio while reinforcing directive clarity.\"\n          },\n          \"success_criteria\": {\n            \"semantic_integrity\": \"Preserve and clarify original intent, constraints, and output boundaries.\",\n            \"directive_elevation\": \"Target high-impact enhancements: strict exclusion clauses, cross-domain validation, and removal of origin/meta markers.\",\n            \"poetic_refinement\": \"Maintain or improve rhyme structure, emotional depth, and structural elegance.\"\n          },\n          \"recommended_process\": [\n            \"Extract only high-impact, result-generating elements.\",\n            \"Remove all non-essentials to surface directive core.\",\n            \"Prioritize transformations with proven value return.\",\n            \"Isolate and sequence executable steps with specificity and clarity.\",\n            \"Preserve directive intensity and eliminate ambiguity at every layer.\"\n          ],\n          \"hidden_assumptions\": [\n            \"Consolidated directives must map logically and executably to all source steps.\",\n            \"All constraints and minimalism must be preserved without prohibited abstractions.\",\n            \"Transitions, subroles, and interfaces must be captured in a single flow.\",\n            \"All stages must remain invertible and auditable.\",\n            \"Each micro-process (perception, friction, refinement, integration, convergence) must be embedded in the unified logic.\",\n            \"Original input/output contracts and role boundaries must remain intact.\"\n          ],\n          \"enumerated_sub_goals\": [\n            \"1. Clarify total intent, constraints, and output scope without using banned metaphors.\",\n            \"2. Generalize perception of implicit/explicit input logic.\",\n            \"3. Detect strictly localized frictions without altering original content.\",\n            \"4. Propose subtle, scoped refinements justified by structure and intent.\",\n            \"5. Mandate atomic, validated integration with rollback controls.\",\n            \"6. Cap total transformation within defined thresholds per pass.\",\n            \"7. Assess connective coherence with minimal, transitional-only edits.\",\n            \"8. Decide on convergence or justified re-iteration based on marginal value.\",\n            \"9. Preserve stepwise traceability and rationale logging.\",\n            \"10. Enforce strict functional separation of roles within the unified directive.\"\n          ]\n        },\n    },\n\nso your goal is to create a new instruction sequence according to the provided instructions template/pattern to generate a set of instruction designed to transform any input into \"as if rephrased by a brilliant brochure print designer\".\n\nso in context to your previous response, the \"knowledge\" would be stored within the \"context\" key while the instructions would be spread over \"interpretation\" and \"transformation\" keys\n\n<!-- ======================================================= -->\n<!-- [2025.07.21 23:38] -->\n<!-- augment -->\n\n```json\n{\n  \"brochure_design_specification_template\": {\n    \"metadata\": {\n      \"version\": \"1.0\",\n      \"scope\": \"Universal brochure design framework\",\n      \"target_audience\": \"Elite print design professionals\",\n      \"flexibility\": \"Adaptable to any panel configuration and format\"\n    },\n\n    \"foundational_design_tenets\": {\n      \"visual_hierarchy\": {\n        \"primary_focal_points\": [\"dominant_headline\", \"hero_imagery\", \"primary_call_to_action\"],\n        \"secondary_elements\": [\"subheadings\", \"supporting_imagery\", \"secondary_messaging\"],\n        \"tertiary_components\": [\"body_text\", \"captions\", \"contact_information\"],\n        \"flow_principles\": [\"z_pattern_scanning\", \"f_pattern_reading\", \"visual_weight_distribution\"]\n      },\n\n      \"typography_fundamentals\": {\n        \"hierarchy_levels\": {\n          \"display_typography\": [\"headline_fonts\", \"decorative_elements\", \"brand_wordmarks\"],\n          \"text_typography\": [\"body_copy\", \"captions\", \"metadata\"],\n          \"functional_typography\": [\"navigation\", \"labels\", \"legal_text\"]\n        },\n        \"readability_standards\": {\n          \"minimum_sizes\": [\"9pt_body_text\", \"12pt_headlines\", \"8pt_captions\"],\n          \"contrast_ratios\": [\"4.5:1_minimum\", \"7:1_preferred\", \"3:1_large_text\"],\n          \"line_spacing\": [\"1.2x_minimum\", \"1.4x_optimal\", \"1.6x_maximum\"]\n        }\n      },\n\n      \"color_theory_application\": {\n        \"color_harmony_systems\": [\"monochromatic\", \"analogous\", \"complementary\", \"triadic\", \"split_complementary\"],\n        \"psychological_associations\": [\"warm_engagement\", \"cool_professionalism\", \"neutral_sophistication\"],\n        \"accessibility_compliance\": [\"wcag_aa_standards\", \"colorblind_considerations\", \"high_contrast_alternatives\"]\n      },\n\n      \"spatial_composition\": {\n        \"layout_principles\": [\"rule_of_thirds\", \"golden_ratio\", \"fibonacci_sequence\"],\n        \"white_space_management\": [\"breathing_room\", \"content_separation\", \"visual_rest_areas\"],\n        \"alignment_systems\": [\"grid_based_structure\", \"baseline_alignment\", \"optical_alignment\"]\n      }\n    },\n\n    \"advanced_design_elements\": {\n      \"print_production_mastery\": {\n        \"color_management\": {\n          \"color_spaces\": [\"cmyk_process\", \"pantone_spot_colors\", \"rgb_digital_preview\"],\n          \"ink_optimization\": [\"total_area_coverage\", \"rich_black_formulation\", \"overprint_considerations\"],\n          \"proofing_standards\": [\"contract_proofs\", \"press_proofs\", \"digital_color_matching\"]\n        },\n\n        \"substrate_considerations\": {\n          \"paper_characteristics\": [\"weight_gsm\", \"finish_texture\", \"opacity_levels\", \"grain_direction\"],\n          \"coating_applications\": [\"gloss_uv\", \"matte_varnish\", \"spot_uv_accents\", \"soft_touch_lamination\"],\n          \"specialty_substrates\": [\"synthetic_materials\", \"textured_papers\", \"metallic_finishes\"]\n        },\n\n        \"finishing_techniques\": {\n          \"cutting_methods\": [\"die_cutting\", \"laser_cutting\", \"perforation\", \"scoring\"],\n          \"folding_specifications\": [\"parallel_folds\", \"accordion_folds\", \"gate_folds\", \"roll_folds\"],\n          \"binding_options\": [\"saddle_stitching\", \"perfect_binding\", \"spiral_binding\", \"wire_o_binding\"]\n        }\n      },\n\n      \"contemporary_design_trends\": {\n        \"visual_aesthetics\": {\n          \"minimalist_approaches\": [\"negative_space_emphasis\", \"clean_typography\", \"restrained_color_palettes\"],\n          \"maximalist_expressions\": [\"bold_pattern_mixing\", \"vibrant_color_combinations\", \"layered_compositions\"],\n          \"hybrid_methodologies\": [\"selective_complexity\", \"focal_maximalism\", \"strategic_minimalism\"]\n        },\n\n        \"interactive_elements\": {\n          \"tactile_experiences\": [\"embossed_textures\", \"debossed_impressions\", \"foil_stamping\", \"raised_spot_uv\"],\n          \"dimensional_features\": [\"pop_up_elements\", \"fold_out_sections\", \"layered_constructions\"],\n          \"sensory_engagement\": [\"scented_inks\", \"textural_contrasts\", \"temperature_sensitive_materials\"]\n        }\n      },\n\n      \"brand_integration_strategies\": {\n        \"identity_consistency\": {\n          \"logo_applications\": [\"primary_placement\", \"secondary_usage\", \"minimum_sizes\", \"clear_space_requirements\"],\n          \"brand_color_systems\": [\"primary_palette\", \"secondary_colors\", \"accent_applications\", \"neutral_foundations\"],\n          \"typography_hierarchies\": [\"brand_fonts\", \"supporting_typefaces\", \"web_safe_alternatives\"]\n        },\n\n        \"voice_and_tone_translation\": {\n          \"visual_personality\": [\"authoritative_presence\", \"approachable_warmth\", \"innovative_edge\", \"trustworthy_stability\"],\n          \"messaging_alignment\": [\"headline_voice\", \"body_copy_tone\", \"call_to_action_urgency\"],\n          \"cultural_considerations\": [\"regional_preferences\", \"demographic_sensitivities\", \"market_positioning\"]\n        }\n      }\n    },\n\n    \"expert_level_specifications\": {\n      \"advanced_typography_systems\": {\n        \"micro_typography\": {\n          \"character_spacing\": [\"tracking_adjustments\", \"kerning_pairs\", \"optical_spacing\"],\n          \"word_spacing\": [\"justified_text_optimization\", \"ragged_right_refinement\", \"hyphenation_control\"],\n          \"paragraph_refinement\": [\"widow_orphan_control\", \"baseline_grid_adherence\", \"vertical_rhythm_maintenance\"]\n        },\n\n        \"typographic_expression\": {\n          \"experimental_layouts\": [\"kinetic_typography\", \"deconstructed_letterforms\", \"layered_text_treatments\"],\n          \"cultural_typography\": [\"script_considerations\", \"reading_direction_adaptations\", \"character_set_completeness\"],\n          \"responsive_typography\": [\"scalable_hierarchies\", \"device_optimization\", \"cross_media_consistency\"]\n        }\n      },\n\n      \"sophisticated_color_strategies\": {\n        \"advanced_color_theory\": {\n          \"perceptual_uniformity\": [\"lab_color_space\", \"delta_e_measurements\", \"metamerism_considerations\"],\n          \"color_psychology_application\": [\"emotional_response_mapping\", \"cultural_color_meanings\", \"demographic_preferences\"],\n          \"environmental_factors\": [\"lighting_conditions\", \"viewing_angles\", \"substrate_interaction\"]\n        },\n\n        \"production_color_mastery\": {\n          \"ink_formulation\": [\"custom_color_matching\", \"metallic_ink_applications\", \"fluorescent_color_usage\"],\n          \"color_separation\": [\"ucr_gcr_strategies\", \"black_generation_curves\", \"ink_sequence_optimization\"],\n          \"quality_control\": [\"densitometer_readings\", \"spectrophotometer_analysis\", \"press_sheet_evaluation\"]\n        }\n      },\n\n      \"cutting_edge_production_techniques\": {\n        \"digital_integration\": {\n          \"variable_data_printing\": [\"personalized_content\", \"demographic_targeting\", \"sequential_numbering\"],\n          \"augmented_reality_integration\": [\"qr_code_placement\", \"ar_trigger_design\", \"digital_overlay_coordination\"],\n          \"cross_platform_consistency\": [\"print_digital_alignment\", \"color_profile_management\", \"asset_optimization\"]\n        },\n\n        \"sustainable_design_practices\": {\n          \"eco_friendly_materials\": [\"recycled_content_papers\", \"soy_based_inks\", \"water_based_coatings\"],\n          \"waste_reduction_strategies\": [\"efficient_imposition\", \"minimal_trim_waste\", \"reusable_design_elements\"],\n          \"lifecycle_considerations\": [\"recyclability_planning\", \"biodegradable_components\", \"carbon_footprint_optimization\"]\n        }\n      },\n\n      \"elite_professional_terminology\": {\n        \"production_vocabulary\": {\n          \"prepress_terms\": [\"trapping\", \"overprint\", \"knockout\", \"choke_spread\", \"color_separation\"],\n          \"press_terminology\": [\"makeready\", \"impression\", \"blanket_cylinder\", \"plate_cylinder\", \"ink_fountain\"],\n          \"finishing_language\": [\"gripper_edge\", \"tail_edge\", \"cross_grain\", \"with_grain\", \"caliper_thickness\"]\n        },\n\n        \"design_critique_language\": {\n          \"aesthetic_evaluation\": [\"visual_tension\", \"compositional_balance\", \"chromatic_harmony\", \"typographic_rhythm\"],\n          \"technical_assessment\": [\"registration_accuracy\", \"color_fidelity\", \"print_quality_metrics\", \"finishing_precision\"],\n          \"conceptual_analysis\": [\"brand_alignment\", \"message_clarity\", \"audience_resonance\", \"market_differentiation\"]\n        }\n      }\n    },\n\n    \"application_frameworks\": {\n      \"project_specification_templates\": {\n        \"technical_requirements\": {\n          \"format_specifications\": [\"finished_size\", \"panel_configuration\", \"fold_type\", \"orientation\"],\n          \"production_parameters\": [\"print_method\", \"color_process\", \"substrate_selection\", \"finishing_options\"],\n          \"quality_standards\": [\"tolerance_levels\", \"color_accuracy\", \"registration_precision\", \"finishing_quality\"]\n        },\n\n        \"creative_brief_structure\": {\n          \"brand_parameters\": [\"identity_guidelines\", \"voice_characteristics\", \"visual_style\", \"market_positioning\"],\n          \"audience_definition\": [\"demographic_profile\", \"psychographic_insights\", \"behavioral_patterns\", \"communication_preferences\"],\n          \"messaging_hierarchy\": [\"primary_message\", \"supporting_points\", \"call_to_action\", \"contact_information\"]\n        }\n      },\n\n      \"quality_assurance_protocols\": {\n        \"design_validation\": [\"brand_compliance_check\", \"accessibility_audit\", \"readability_assessment\", \"visual_hierarchy_verification\"],\n        \"production_verification\": [\"color_proof_approval\", \"die_line_accuracy\", \"finishing_specification_confirmation\", \"substrate_suitability\"],\n        \"final_delivery\": [\"file_preparation_standards\", \"archive_organization\", \"usage_guidelines\", \"reproduction_rights\"]\n      }\n    },\n\n    \"specialized_design_methodologies\": {\n      \"audience_specific_approaches\": {\n        \"youth_engagement_strategies\": {\n          \"visual_language\": [\"bold_geometric_patterns\", \"vibrant_gradient_applications\", \"asymmetrical_compositions\", \"dynamic_typography\"],\n          \"content_presentation\": [\"bite_sized_information\", \"visual_storytelling\", \"interactive_elements\", \"social_media_integration\"],\n          \"color_psychology\": [\"energetic_palettes\", \"high_contrast_combinations\", \"neon_accent_usage\", \"gradient_transitions\"],\n          \"typography_trends\": [\"variable_fonts\", \"custom_lettering\", \"mixed_case_styling\", \"oversized_display_text\"]\n        },\n\n        \"professional_demographics\": {\n          \"corporate_sophistication\": [\"refined_color_palettes\", \"structured_layouts\", \"premium_materials\", \"subtle_branding\"],\n          \"executive_communication\": [\"data_visualization\", \"infographic_integration\", \"clean_hierarchies\", \"authoritative_typography\"],\n          \"industry_specific_adaptations\": [\"sector_appropriate_imagery\", \"technical_terminology\", \"compliance_considerations\", \"regulatory_requirements\"]\n        },\n\n        \"luxury_market_positioning\": {\n          \"premium_aesthetics\": [\"metallic_accents\", \"embossed_details\", \"high_end_substrates\", \"sophisticated_color_schemes\"],\n          \"exclusivity_indicators\": [\"limited_edition_numbering\", \"personalized_elements\", \"premium_packaging\", \"artisanal_finishes\"],\n          \"tactile_luxury\": [\"soft_touch_coatings\", \"textured_papers\", \"dimensional_elements\", \"weight_perception\"]\n        }\n      },\n\n      \"format_specific_expertise\": {\n        \"multi_panel_configurations\": {\n          \"bi_fold_optimization\": [\"cover_impact\", \"interior_spread\", \"back_panel_utilization\", \"fold_line_consideration\"],\n          \"tri_fold_mastery\": [\"panel_hierarchy\", \"reading_sequence\", \"fold_reveal_strategy\", \"compact_storage\"],\n          \"accordion_fold_dynamics\": [\"continuous_narrative\", \"panel_progression\", \"expandable_content\", \"display_versatility\"],\n          \"gate_fold_drama\": [\"reveal_mechanism\", \"central_impact\", \"symmetrical_balance\", \"premium_presentation\"]\n        },\n\n        \"size_optimization_strategies\": {\n          \"compact_formats\": [\"information_density\", \"micro_typography\", \"efficient_layouts\", \"portable_design\"],\n          \"oversized_impact\": [\"large_format_considerations\", \"handling_ergonomics\", \"storage_implications\", \"visual_dominance\"],\n          \"standard_dimensions\": [\"cost_optimization\", \"mailing_compliance\", \"display_compatibility\", \"production_efficiency\"]\n        }\n      },\n\n      \"cross_cultural_design_considerations\": {\n        \"international_adaptability\": {\n          \"reading_patterns\": [\"left_to_right_optimization\", \"right_to_left_adaptation\", \"top_to_bottom_flow\", \"cultural_scanning_habits\"],\n          \"color_cultural_meanings\": [\"regional_color_associations\", \"religious_considerations\", \"political_sensitivities\", \"market_preferences\"],\n          \"imagery_appropriateness\": [\"cultural_representation\", \"demographic_inclusion\", \"lifestyle_relevance\", \"aspirational_alignment\"],\n          \"typography_localization\": [\"character_set_support\", \"script_considerations\", \"font_availability\", \"reading_comfort\"]\n        }\n      }\n    },\n\n    \"technical_production_mastery\": {\n      \"advanced_prepress_techniques\": {\n        \"file_preparation_excellence\": {\n          \"resolution_optimization\": [\"image_scaling_best_practices\", \"vector_rasterization_decisions\", \"output_resolution_matching\"],\n          \"color_profile_management\": [\"icc_profile_embedding\", \"color_space_conversions\", \"soft_proofing_accuracy\"],\n          \"bleed_and_trim_precision\": [\"bleed_extension_standards\", \"trim_mark_placement\", \"safety_margin_calculations\"],\n          \"font_handling\": [\"font_embedding_protocols\", \"outline_conversion_decisions\", \"character_encoding_verification\"]\n        },\n\n        \"imposition_strategies\": {\n          \"sheet_optimization\": [\"paper_grain_consideration\", \"press_sheet_efficiency\", \"binding_margin_accommodation\"],\n          \"folding_sequence_planning\": [\"fold_order_optimization\", \"panel_alignment_precision\", \"finishing_workflow_integration\"],\n          \"quality_control_integration\": [\"color_bar_placement\", \"registration_mark_positioning\", \"crop_mark_accuracy\"]\n        }\n      },\n\n      \"press_optimization_knowledge\": {\n        \"ink_and_substrate_interaction\": {\n          \"ink_absorption_factors\": [\"paper_porosity_considerations\", \"coating_compatibility\", \"drying_time_optimization\"],\n          \"color_consistency_maintenance\": [\"ink_density_control\", \"press_speed_optimization\", \"environmental_factor_management\"],\n          \"special_effect_integration\": [\"metallic_ink_application\", \"fluorescent_color_usage\", \"thermographic_printing\"]\n        },\n\n        \"finishing_process_integration\": {\n          \"die_cutting_precision\": [\"cutting_force_optimization\", \"blade_selection\", \"waste_removal_efficiency\"],\n          \"folding_accuracy\": [\"bone_folder_pressure\", \"fold_line_scoring\", \"panel_alignment_verification\"],\n          \"binding_considerations\": [\"spine_calculation\", \"page_creep_accommodation\", \"binding_margin_optimization\"]\n        }\n      }\n    }\n  }\n}\n```\n\n<!-- ======================================================= -->\n<!-- [2025.07.21 23:38] -->\n<!-- perplexity -->\n\n```json\n{\n  \"9020-a-brochure_print_designer_rephraser\": {\n    \"title\": \"Brochure Print Designer Rephraser\",\n    \"interpretation\": \"Your goal is not to **summarize** or **paraphrase** in general terms, but to **refashion** any input as if it were written by a world-class brochure print designer, rigorously applying foundational and advanced visual design diction, industry terminology, and elite critique standards. Eliminate all casual, conversational, or generic style. Preserve only succinct, technically rich, and highly visual language. Execute as:\",\n    \"transformation\": \"`{role=brochure_print_designer_rephraser; input=[source:str]; process=[extract_structural_and_visual_elements(source), recast_content_with_brochure_design_terminology(), inject_hierarchical_section_breakdown(), specify color_palette_and_brand_elements(), identify_typographic_and_imagery_strategies(), annotate_print_readiness_and_expert_finesse(), employ_advanced_design_terms_and_motifs(), apply_conciseness_while_maintaining_visual_richness(), output_structured_visual_analysis()]; constraints=[no conversational filler, no generic adjectives, prohibit subjective or personal tone]; requirements=[industry-expert vocabulary, precise visual and structural mapping, critical style diagnosis, explicit panel/section breakdown, actionable for elite designer use]; output={expert_brochure_description:str, structured_visual_map:dict}`\",\n    \"context\": {\n      \"knowledge_foundations\": {\n        \"structural_design\": \"Account for any panel/fold configuration. Map all content to modular grid logic and define margins, gutters, bleeds, and safe zones per press standards.\",\n        \"visual_identity\": \"Demand clear articulation of branding protocols: logo lockups, brand palette (with Pantone/CMYK/RGB if supplied), exclusion/inclusion zones, and proprietary brand secondary assets.\",\n        \"typography_and_hierarchy\": \"Specify typography at the superfamily/font pairing level. Demarcate headline, subhead, body, and microcopy roles. All text must be scrutinized for weight, contrast, and accessibility.\",\n        \"imagery_and_iconography\": \"Require all illustration, photography, and icons be assessed for brand fit, resolution, compositional use (e.g., edge-to-edge, negative space), and print fidelity.\",\n        \"content_narrative_and_cta\": \"Map section purpose and user journey flow: cover strategy, reveal panel hooks, narrative/case study rhythm, testimonial/authority integration, and CTA prominence with visual anchoring.\",\n        \"advanced_print_technique\": \"Enforce 300dpi raster minimums, vector scaling, press-space color controls, specialty finish options (e.g., spot UV, emboss, deboss, die-cut, eco-stock recommendations).\",\n        \"panel_flow_and_microinteractions\": \"Define how the reader's eye travels, panel-to-panel, annotating divisional cues, narrative escalation, and tactile/interactive device positions (QR, coupon, AR triggers).\"\n      },\n      \"success_criteria\": {\n        \"precision\": \"All descriptions are technical, never generic. Visual elements are navigated dimensionally (spatially, hierarchically, narratively).\",\n        \"trend_inclusion\": \"Integrate trend-driven language—texture scans, duotone overlays, edge-to-edge imagery, split/inset motifs, eco-materials, etc.\",\n        \"brand_consistency\": \"All brand elements described as per advanced guideline adherence—logo usage, color codes, asset lockdown.\",\n        \"industry_jargon\": \"Employ professional print terms: bleed, gutter, creep, micro-motifs, superfamily font, spot UV, TAC, AR triggers.\",\n        \"self-contained_output\": \"Output always a fully structured expert's description and a hierarchical mapping of all major and minor design decisions.\"\n      },\n      \"sample_process_flow\": [\n        \"Dissect and map structural/panel logic first\",\n        \"Assign all visual and narrative roles by section\",\n        \"Specify color/typographic hierarchy by use and context\",\n        \"Annotate all imagery and graphic elements in detail\",\n        \"Surface print/finishing strategies and print-risk management\",\n        \"Deliver critique as if for a professional peer review or portfolio audit\"\n      ],\n      \"special_terms\": [\n        \"fold line\", \"panel gutter\", \"hero motif\", \"ink coverage\", \"superfamily\", \"lockup\", \"edge-to-edge\", \"spot color\", \"die cut\", \"eco-stock\", \"microinteraction\", \"typographic rhythm\", \"bleed/trim zone\"\n      ]\n    }\n  }\n}\n```\n\n<!-- ======================================================= -->\n<!-- [2025.07.21 23:39] -->\n<!-- chatgpt-o3pro -->\n\n```json\n{\n  \"9023-a-brochure_design_descriptor_synthesizer\": {\n    \"title\": \"Brochure Design Descriptor Synthesizer\",\n    \"interpretation\": \"Transform any brochure brief—or an existing brochure sample—into a **fully populated, print‑designer‑grade rule set**. Produce a single JSON block that mirrors the template used by elite designers (core principles → advanced facets).\",\n    \"transformation\": \"`{role=brochure_design_descriptor_synthesizer; input=[design_source:str]; process:[\\n  strip_non_design_noise(),\\n  detect_fold_format_and_panel_count(),          if_missing_insert(<PANEL_COUNT>),\\n  extract_print_specs(bleed,margins,dpi,stock),\\n  capture_brand_assets(logo,colors,fonts,patterns),\\n  map_typographic_hierarchy(),\\n  analyse_color_management(icc,spot,tac),\\n  inventory_imagery(icon_style,raster_res),\\n  surface_accessibility_requirements(contrast,wcag),\\n  enumerate_qc_steps(preflight,g7_calibration),\\n  identify_advanced_elements(finishes,vd_p,ar_nfc,sustainability,ai_svg,trend_aesthetics),\\n  validate_specialist_terms_presence(),\\n  assemble_json_using_master_schema(),\\n  run_consistency_checks(),                      abort_if_conflict(),\\n  output_final_rules()\\n],\\nconstraints=[single_pass(), json_output_only(), preserve_specialist_jargon(), ≤3000_chars()],\\nrequirements=[hierarchical_structure(core→advanced), exhaustive_coverage(), designer‑readability(), template_compliance()],\\noutput={brochure_design_rules:object, abort_code:null|\\\"BD‑0\\\"}}`\",\n    \"context\": {\n      \"principles\": {\n        \"holistic_abstraction\": \"Collapse multi‑stage analysis into one cognitive sweep while preserving every critical descriptor.\",\n        \"template_fidelity\": \"Output must follow the exact `core_principles` and `advanced_principles` hierarchy so designers can drop‑in without re‑formatting.\",\n        \"specialist_precision\": \"Leverage industry jargon (e.g., TAC, FM screening, GCR) to demonstrate professional depth.\"\n      },\n      \"success_criteria\": {\n        \"structural_integrity\": \"JSON keys and nesting mirror the master schema; no orphan properties.\",\n        \"terminology_check\": \"Includes at least 10 niche industry terms (e.g., ‘creep’, ‘FOGRA39’, ‘spot UV’, ‘stochastic screening’).\",\n        \"flexibility\": \"Uses placeholder variables (e.g., <PANEL_COUNT>, <STOCK_WEIGHT_GSM>) whenever the brief omits specifics.\"\n      },\n      \"edge_case_handling\": {\n        \"missing_design_data\": \"Insert placeholders wrapped in angle brackets and list them in `missing_fields` inside output.\",\n        \"conflicting_specs\": \"Abort with `{brochure_design_rules:null, abort_code:\\\"BD‑0\\\"}` and include conflict note.\"\n      }\n    }\n  }\n}\n```",
  "sequence_id": "1000|1003|1004",
  "results": [
      {
      "instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`",
      "step": "a",
      "title": "Instruction Converter",
      "input": "<!-- ======================================================= -->\n<!-- [2025.07.21 23:35] -->\n\nyou misunderstand, the instruction will be defined like this (unrelated example):\n\n    # a: Ruthless Critique\n    \"3900-a-hard_critique\": {\n        \"title\": \"Hard Critique\",\n        \"interpretation\": \"Your goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:\",\n        \"transformation\": \"`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,2.0], flaw_analysis:str, alternative_enhancements:str[3]}}`\",\n        # \"context\": {},\n    },\n    # b: Trajectory Director\n    \"3900-a-trajectory_director\": {\n        \"title\": \"Trajectory Director\",\n        \"interpretation\": \"Your goal is not to **process** the input in a vacuum, but to **steer** it—deriving the highest-value constructive trajectory and encoding that path as the very next set of instructions. Execute as trajectory-setting directive generator: \",\n        \"transformation\": \"`{role=trajectory_director; input=[raw_prompt:any]; process=[extract_core_objective(), infer_desired_end_state(constructive_bias=True), map_high_value_intermediate_targets(), synthesize_directional_commands(), embed_self-improving_guidelines(), validate_alignment_with_constructive_principles()]; constraints=[mandatory_constructive_vector(), preserve_original_contextual_integrity(), forbid_nil_direction_outputs(), zero_conversational_fillers()]; requirements=[explicit_next-step_directives(), trajectory_map:list, self_reinforcing_instruction_pattern()], output={directed_prompt:str, trajectory_outline:list}}`\",\n        # \"context\": {},\n    },\n    # c: Distillation Compressor\n    \"3900-c-distillation_compressor\": {\n        \"title\": \"Distillation Compressor\",\n        \"interpretation\": \"Your goal is not to **explain** the synthesis, but to **compress** it into its most potent actionable form. Execute as:\",\n        \"transformation\": \"`{role=distillation_compressor; input=[synthesized_progression:array]; process=[extract_universal_patterns(), eliminate_implementation_details(), compress_to_core_directives(), maximize_transferability(), validate_actionable_completeness()]; constraints=[no_explanatory_content(), directive_format_only(), maximum_compression_ratio()]; requirements=[universal_applicability(), immediate_actionability(), zero_fluff()]; output={distilled_core:str}}`\",\n        \"context\": {\n          \"core_principles\": {\n            \"essence_preservation\": \"Extract only procedural imperatives, removing all interpretive, role-based, or meta-structural language.\",\n            \"directive_compression\": \"Condense logic into minimal, sequential commands—eliminating redundancy, explanatory qualifiers, and non-executable phrasing.\",\n            \"execution_purity\": \"Ensure output is universally executable, devoid of affirmations, abstractions, or evaluative language.\",\n            \"signal_integrity\": \"Apply iterative refinement to maximize signal-to-noise ratio while reinforcing directive clarity.\"\n          },\n          \"success_criteria\": {\n            \"semantic_integrity\": \"Preserve and clarify original intent, constraints, and output boundaries.\",\n            \"directive_elevation\": \"Target high-impact enhancements: strict exclusion clauses, cross-domain validation, and removal of origin/meta markers.\",\n            \"poetic_refinement\": \"Maintain or improve rhyme structure, emotional depth, and structural elegance.\"\n          },\n          \"recommended_process\": [\n            \"Extract only high-impact, result-generating elements.\",\n            \"Remove all non-essentials to surface directive core.\",\n            \"Prioritize transformations with proven value return.\",\n            \"Isolate and sequence executable steps with specificity and clarity.\",\n            \"Preserve directive intensity and eliminate ambiguity at every layer.\"\n          ],\n          \"hidden_assumptions\": [\n            \"Consolidated directives must map logically and executably to all source steps.\",\n            \"All constraints and minimalism must be preserved without prohibited abstractions.\",\n            \"Transitions, subroles, and interfaces must be captured in a single flow.\",\n            \"All stages must remain invertible and auditable.\",\n            \"Each micro-process (perception, friction, refinement, integration, convergence) must be embedded in the unified logic.\",\n            \"Original input/output contracts and role boundaries must remain intact.\"\n          ],\n          \"enumerated_sub_goals\": [\n            \"1. Clarify total intent, constraints, and output scope without using banned metaphors.\",\n            \"2. Generalize perception of implicit/explicit input logic.\",\n            \"3. Detect strictly localized frictions without altering original content.\",\n            \"4. Propose subtle, scoped refinements justified by structure and intent.\",\n            \"5. Mandate atomic, validated integration with rollback controls.\",\n            \"6. Cap total transformation within defined thresholds per pass.\",\n            \"7. Assess connective coherence with minimal, transitional-only edits.\",\n            \"8. Decide on convergence or justified re-iteration based on marginal value.\",\n            \"9. Preserve stepwise traceability and rationale logging.\",\n            \"10. Enforce strict functional separation of roles within the unified directive.\"\n          ]\n        },\n    },\n\nso your goal is to create a new instruction sequence according to the provided instructions template/pattern to generate a set of instruction designed to transform any input into \"as if rephrased by a brilliant brochure print designer\".\n\nso in context to your previous response, the \"knowledge\" would be stored within the \"context\" key while the instructions would be spread over \"interpretation\" and \"transformation\" keys\n\n<!-- ======================================================= -->\n<!-- [2025.07.21 23:38] -->\n<!-- augment -->\n\n```json\n{\n  \"brochure_design_specification_template\": {\n    \"metadata\": {\n      \"version\": \"1.0\",\n      \"scope\": \"Universal brochure design framework\",\n      \"target_audience\": \"Elite print design professionals\",\n      \"flexibility\": \"Adaptable to any panel configuration and format\"\n    },\n\n    \"foundational_design_tenets\": {\n      \"visual_hierarchy\": {\n        \"primary_focal_points\": [\"dominant_headline\", \"hero_imagery\", \"primary_call_to_action\"],\n        \"secondary_elements\": [\"subheadings\", \"supporting_imagery\", \"secondary_messaging\"],\n        \"tertiary_components\": [\"body_text\", \"captions\", \"contact_information\"],\n        \"flow_principles\": [\"z_pattern_scanning\", \"f_pattern_reading\", \"visual_weight_distribution\"]\n      },\n\n      \"typography_fundamentals\": {\n        \"hierarchy_levels\": {\n          \"display_typography\": [\"headline_fonts\", \"decorative_elements\", \"brand_wordmarks\"],\n          \"text_typography\": [\"body_copy\", \"captions\", \"metadata\"],\n          \"functional_typography\": [\"navigation\", \"labels\", \"legal_text\"]\n        },\n        \"readability_standards\": {\n          \"minimum_sizes\": [\"9pt_body_text\", \"12pt_headlines\", \"8pt_captions\"],\n          \"contrast_ratios\": [\"4.5:1_minimum\", \"7:1_preferred\", \"3:1_large_text\"],\n          \"line_spacing\": [\"1.2x_minimum\", \"1.4x_optimal\", \"1.6x_maximum\"]\n        }\n      },\n\n      \"color_theory_application\": {\n        \"color_harmony_systems\": [\"monochromatic\", \"analogous\", \"complementary\", \"triadic\", \"split_complementary\"],\n        \"psychological_associations\": [\"warm_engagement\", \"cool_professionalism\", \"neutral_sophistication\"],\n        \"accessibility_compliance\": [\"wcag_aa_standards\", \"colorblind_considerations\", \"high_contrast_alternatives\"]\n      },\n\n      \"spatial_composition\": {\n        \"layout_principles\": [\"rule_of_thirds\", \"golden_ratio\", \"fibonacci_sequence\"],\n        \"white_space_management\": [\"breathing_room\", \"content_separation\", \"visual_rest_areas\"],\n        \"alignment_systems\": [\"grid_based_structure\", \"baseline_alignment\", \"optical_alignment\"]\n      }\n    },\n\n    \"advanced_design_elements\": {\n      \"print_production_mastery\": {\n        \"color_management\": {\n          \"color_spaces\": [\"cmyk_process\", \"pantone_spot_colors\", \"rgb_digital_preview\"],\n          \"ink_optimization\": [\"total_area_coverage\", \"rich_black_formulation\", \"overprint_considerations\"],\n          \"proofing_standards\": [\"contract_proofs\", \"press_proofs\", \"digital_color_matching\"]\n        },\n\n        \"substrate_considerations\": {\n          \"paper_characteristics\": [\"weight_gsm\", \"finish_texture\", \"opacity_levels\", \"grain_direction\"],\n          \"coating_applications\": [\"gloss_uv\", \"matte_varnish\", \"spot_uv_accents\", \"soft_touch_lamination\"],\n          \"specialty_substrates\": [\"synthetic_materials\", \"textured_papers\", \"metallic_finishes\"]\n        },\n\n        \"finishing_techniques\": {\n          \"cutting_methods\": [\"die_cutting\", \"laser_cutting\", \"perforation\", \"scoring\"],\n          \"folding_specifications\": [\"parallel_folds\", \"accordion_folds\", \"gate_folds\", \"roll_folds\"],\n          \"binding_options\": [\"saddle_stitching\", \"perfect_binding\", \"spiral_binding\", \"wire_o_binding\"]\n        }\n      },\n\n      \"contemporary_design_trends\": {\n        \"visual_aesthetics\": {\n          \"minimalist_approaches\": [\"negative_space_emphasis\", \"clean_typography\", \"restrained_color_palettes\"],\n          \"maximalist_expressions\": [\"bold_pattern_mixing\", \"vibrant_color_combinations\", \"layered_compositions\"],\n          \"hybrid_methodologies\": [\"selective_complexity\", \"focal_maximalism\", \"strategic_minimalism\"]\n        },\n\n        \"interactive_elements\": {\n          \"tactile_experiences\": [\"embossed_textures\", \"debossed_impressions\", \"foil_stamping\", \"raised_spot_uv\"],\n          \"dimensional_features\": [\"pop_up_elements\", \"fold_out_sections\", \"layered_constructions\"],\n          \"sensory_engagement\": [\"scented_inks\", \"textural_contrasts\", \"temperature_sensitive_materials\"]\n        }\n      },\n\n      \"brand_integration_strategies\": {\n        \"identity_consistency\": {\n          \"logo_applications\": [\"primary_placement\", \"secondary_usage\", \"minimum_sizes\", \"clear_space_requirements\"],\n          \"brand_color_systems\": [\"primary_palette\", \"secondary_colors\", \"accent_applications\", \"neutral_foundations\"],\n          \"typography_hierarchies\": [\"brand_fonts\", \"supporting_typefaces\", \"web_safe_alternatives\"]\n        },\n\n        \"voice_and_tone_translation\": {\n          \"visual_personality\": [\"authoritative_presence\", \"approachable_warmth\", \"innovative_edge\", \"trustworthy_stability\"],\n          \"messaging_alignment\": [\"headline_voice\", \"body_copy_tone\", \"call_to_action_urgency\"],\n          \"cultural_considerations\": [\"regional_preferences\", \"demographic_sensitivities\", \"market_positioning\"]\n        }\n      }\n    },\n\n    \"expert_level_specifications\": {\n      \"advanced_typography_systems\": {\n        \"micro_typography\": {\n          \"character_spacing\": [\"tracking_adjustments\", \"kerning_pairs\", \"optical_spacing\"],\n          \"word_spacing\": [\"justified_text_optimization\", \"ragged_right_refinement\", \"hyphenation_control\"],\n          \"paragraph_refinement\": [\"widow_orphan_control\", \"baseline_grid_adherence\", \"vertical_rhythm_maintenance\"]\n        },\n\n        \"typographic_expression\": {\n          \"experimental_layouts\": [\"kinetic_typography\", \"deconstructed_letterforms\", \"layered_text_treatments\"],\n          \"cultural_typography\": [\"script_considerations\", \"reading_direction_adaptations\", \"character_set_completeness\"],\n          \"responsive_typography\": [\"scalable_hierarchies\", \"device_optimization\", \"cross_media_consistency\"]\n        }\n      },\n\n      \"sophisticated_color_strategies\": {\n        \"advanced_color_theory\": {\n          \"perceptual_uniformity\": [\"lab_color_space\", \"delta_e_measurements\", \"metamerism_considerations\"],\n          \"color_psychology_application\": [\"emotional_response_mapping\", \"cultural_color_meanings\", \"demographic_preferences\"],\n          \"environmental_factors\": [\"lighting_conditions\", \"viewing_angles\", \"substrate_interaction\"]\n        },\n\n        \"production_color_mastery\": {\n          \"ink_formulation\": [\"custom_color_matching\", \"metallic_ink_applications\", \"fluorescent_color_usage\"],\n          \"color_separation\": [\"ucr_gcr_strategies\", \"black_generation_curves\", \"ink_sequence_optimization\"],\n          \"quality_control\": [\"densitometer_readings\", \"spectrophotometer_analysis\", \"press_sheet_evaluation\"]\n        }\n      },\n\n      \"cutting_edge_production_techniques\": {\n        \"digital_integration\": {\n          \"variable_data_printing\": [\"personalized_content\", \"demographic_targeting\", \"sequential_numbering\"],\n          \"augmented_reality_integration\": [\"qr_code_placement\", \"ar_trigger_design\", \"digital_overlay_coordination\"],\n          \"cross_platform_consistency\": [\"print_digital_alignment\", \"color_profile_management\", \"asset_optimization\"]\n        },\n\n        \"sustainable_design_practices\": {\n          \"eco_friendly_materials\": [\"recycled_content_papers\", \"soy_based_inks\", \"water_based_coatings\"],\n          \"waste_reduction_strategies\": [\"efficient_imposition\", \"minimal_trim_waste\", \"reusable_design_elements\"],\n          \"lifecycle_considerations\": [\"recyclability_planning\", \"biodegradable_components\", \"carbon_footprint_optimization\"]\n        }\n      },\n\n      \"elite_professional_terminology\": {\n        \"production_vocabulary\": {\n          \"prepress_terms\": [\"trapping\", \"overprint\", \"knockout\", \"choke_spread\", \"color_separation\"],\n          \"press_terminology\": [\"makeready\", \"impression\", \"blanket_cylinder\", \"plate_cylinder\", \"ink_fountain\"],\n          \"finishing_language\": [\"gripper_edge\", \"tail_edge\", \"cross_grain\", \"with_grain\", \"caliper_thickness\"]\n        },\n\n        \"design_critique_language\": {\n          \"aesthetic_evaluation\": [\"visual_tension\", \"compositional_balance\", \"chromatic_harmony\", \"typographic_rhythm\"],\n          \"technical_assessment\": [\"registration_accuracy\", \"color_fidelity\", \"print_quality_metrics\", \"finishing_precision\"],\n          \"conceptual_analysis\": [\"brand_alignment\", \"message_clarity\", \"audience_resonance\", \"market_differentiation\"]\n        }\n      }\n    },\n\n    \"application_frameworks\": {\n      \"project_specification_templates\": {\n        \"technical_requirements\": {\n          \"format_specifications\": [\"finished_size\", \"panel_configuration\", \"fold_type\", \"orientation\"],\n          \"production_parameters\": [\"print_method\", \"color_process\", \"substrate_selection\", \"finishing_options\"],\n          \"quality_standards\": [\"tolerance_levels\", \"color_accuracy\", \"registration_precision\", \"finishing_quality\"]\n        },\n\n        \"creative_brief_structure\": {\n          \"brand_parameters\": [\"identity_guidelines\", \"voice_characteristics\", \"visual_style\", \"market_positioning\"],\n          \"audience_definition\": [\"demographic_profile\", \"psychographic_insights\", \"behavioral_patterns\", \"communication_preferences\"],\n          \"messaging_hierarchy\": [\"primary_message\", \"supporting_points\", \"call_to_action\", \"contact_information\"]\n        }\n      },\n\n      \"quality_assurance_protocols\": {\n        \"design_validation\": [\"brand_compliance_check\", \"accessibility_audit\", \"readability_assessment\", \"visual_hierarchy_verification\"],\n        \"production_verification\": [\"color_proof_approval\", \"die_line_accuracy\", \"finishing_specification_confirmation\", \"substrate_suitability\"],\n        \"final_delivery\": [\"file_preparation_standards\", \"archive_organization\", \"usage_guidelines\", \"reproduction_rights\"]\n      }\n    },\n\n    \"specialized_design_methodologies\": {\n      \"audience_specific_approaches\": {\n        \"youth_engagement_strategies\": {\n          \"visual_language\": [\"bold_geometric_patterns\", \"vibrant_gradient_applications\", \"asymmetrical_compositions\", \"dynamic_typography\"],\n          \"content_presentation\": [\"bite_sized_information\", \"visual_storytelling\", \"interactive_elements\", \"social_media_integration\"],\n          \"color_psychology\": [\"energetic_palettes\", \"high_contrast_combinations\", \"neon_accent_usage\", \"gradient_transitions\"],\n          \"typography_trends\": [\"variable_fonts\", \"custom_lettering\", \"mixed_case_styling\", \"oversized_display_text\"]\n        },\n\n        \"professional_demographics\": {\n          \"corporate_sophistication\": [\"refined_color_palettes\", \"structured_layouts\", \"premium_materials\", \"subtle_branding\"],\n          \"executive_communication\": [\"data_visualization\", \"infographic_integration\", \"clean_hierarchies\", \"authoritative_typography\"],\n          \"industry_specific_adaptations\": [\"sector_appropriate_imagery\", \"technical_terminology\", \"compliance_considerations\", \"regulatory_requirements\"]\n        },\n\n        \"luxury_market_positioning\": {\n          \"premium_aesthetics\": [\"metallic_accents\", \"embossed_details\", \"high_end_substrates\", \"sophisticated_color_schemes\"],\n          \"exclusivity_indicators\": [\"limited_edition_numbering\", \"personalized_elements\", \"premium_packaging\", \"artisanal_finishes\"],\n          \"tactile_luxury\": [\"soft_touch_coatings\", \"textured_papers\", \"dimensional_elements\", \"weight_perception\"]\n        }\n      },\n\n      \"format_specific_expertise\": {\n        \"multi_panel_configurations\": {\n          \"bi_fold_optimization\": [\"cover_impact\", \"interior_spread\", \"back_panel_utilization\", \"fold_line_consideration\"],\n          \"tri_fold_mastery\": [\"panel_hierarchy\", \"reading_sequence\", \"fold_reveal_strategy\", \"compact_storage\"],\n          \"accordion_fold_dynamics\": [\"continuous_narrative\", \"panel_progression\", \"expandable_content\", \"display_versatility\"],\n          \"gate_fold_drama\": [\"reveal_mechanism\", \"central_impact\", \"symmetrical_balance\", \"premium_presentation\"]\n        },\n\n        \"size_optimization_strategies\": {\n          \"compact_formats\": [\"information_density\", \"micro_typography\", \"efficient_layouts\", \"portable_design\"],\n          \"oversized_impact\": [\"large_format_considerations\", \"handling_ergonomics\", \"storage_implications\", \"visual_dominance\"],\n          \"standard_dimensions\": [\"cost_optimization\", \"mailing_compliance\", \"display_compatibility\", \"production_efficiency\"]\n        }\n      },\n\n      \"cross_cultural_design_considerations\": {\n        \"international_adaptability\": {\n          \"reading_patterns\": [\"left_to_right_optimization\", \"right_to_left_adaptation\", \"top_to_bottom_flow\", \"cultural_scanning_habits\"],\n          \"color_cultural_meanings\": [\"regional_color_associations\", \"religious_considerations\", \"political_sensitivities\", \"market_preferences\"],\n          \"imagery_appropriateness\": [\"cultural_representation\", \"demographic_inclusion\", \"lifestyle_relevance\", \"aspirational_alignment\"],\n          \"typography_localization\": [\"character_set_support\", \"script_considerations\", \"font_availability\", \"reading_comfort\"]\n        }\n      }\n    },\n\n    \"technical_production_mastery\": {\n      \"advanced_prepress_techniques\": {\n        \"file_preparation_excellence\": {\n          \"resolution_optimization\": [\"image_scaling_best_practices\", \"vector_rasterization_decisions\", \"output_resolution_matching\"],\n          \"color_profile_management\": [\"icc_profile_embedding\", \"color_space_conversions\", \"soft_proofing_accuracy\"],\n          \"bleed_and_trim_precision\": [\"bleed_extension_standards\", \"trim_mark_placement\", \"safety_margin_calculations\"],\n          \"font_handling\": [\"font_embedding_protocols\", \"outline_conversion_decisions\", \"character_encoding_verification\"]\n        },\n\n        \"imposition_strategies\": {\n          \"sheet_optimization\": [\"paper_grain_consideration\", \"press_sheet_efficiency\", \"binding_margin_accommodation\"],\n          \"folding_sequence_planning\": [\"fold_order_optimization\", \"panel_alignment_precision\", \"finishing_workflow_integration\"],\n          \"quality_control_integration\": [\"color_bar_placement\", \"registration_mark_positioning\", \"crop_mark_accuracy\"]\n        }\n      },\n\n      \"press_optimization_knowledge\": {\n        \"ink_and_substrate_interaction\": {\n          \"ink_absorption_factors\": [\"paper_porosity_considerations\", \"coating_compatibility\", \"drying_time_optimization\"],\n          \"color_consistency_maintenance\": [\"ink_density_control\", \"press_speed_optimization\", \"environmental_factor_management\"],\n          \"special_effect_integration\": [\"metallic_ink_application\", \"fluorescent_color_usage\", \"thermographic_printing\"]\n        },\n\n        \"finishing_process_integration\": {\n          \"die_cutting_precision\": [\"cutting_force_optimization\", \"blade_selection\", \"waste_removal_efficiency\"],\n          \"folding_accuracy\": [\"bone_folder_pressure\", \"fold_line_scoring\", \"panel_alignment_verification\"],\n          \"binding_considerations\": [\"spine_calculation\", \"page_creep_accommodation\", \"binding_margin_optimization\"]\n        }\n      }\n    }\n  }\n}\n```\n\n<!-- ======================================================= -->\n<!-- [2025.07.21 23:38] -->\n<!-- perplexity -->\n\n```json\n{\n  \"9020-a-brochure_print_designer_rephraser\": {\n    \"title\": \"Brochure Print Designer Rephraser\",\n    \"interpretation\": \"Your goal is not to **summarize** or **paraphrase** in general terms, but to **refashion** any input as if it were written by a world-class brochure print designer, rigorously applying foundational and advanced visual design diction, industry terminology, and elite critique standards. Eliminate all casual, conversational, or generic style. Preserve only succinct, technically rich, and highly visual language. Execute as:\",\n    \"transformation\": \"`{role=brochure_print_designer_rephraser; input=[source:str]; process=[extract_structural_and_visual_elements(source), recast_content_with_brochure_design_terminology(), inject_hierarchical_section_breakdown(), specify color_palette_and_brand_elements(), identify_typographic_and_imagery_strategies(), annotate_print_readiness_and_expert_finesse(), employ_advanced_design_terms_and_motifs(), apply_conciseness_while_maintaining_visual_richness(), output_structured_visual_analysis()]; constraints=[no conversational filler, no generic adjectives, prohibit subjective or personal tone]; requirements=[industry-expert vocabulary, precise visual and structural mapping, critical style diagnosis, explicit panel/section breakdown, actionable for elite designer use]; output={expert_brochure_description:str, structured_visual_map:dict}`\",\n    \"context\": {\n      \"knowledge_foundations\": {\n        \"structural_design\": \"Account for any panel/fold configuration. Map all content to modular grid logic and define margins, gutters, bleeds, and safe zones per press standards.\",\n        \"visual_identity\": \"Demand clear articulation of branding protocols: logo lockups, brand palette (with Pantone/CMYK/RGB if supplied), exclusion/inclusion zones, and proprietary brand secondary assets.\",\n        \"typography_and_hierarchy\": \"Specify typography at the superfamily/font pairing level. Demarcate headline, subhead, body, and microcopy roles. All text must be scrutinized for weight, contrast, and accessibility.\",\n        \"imagery_and_iconography\": \"Require all illustration, photography, and icons be assessed for brand fit, resolution, compositional use (e.g., edge-to-edge, negative space), and print fidelity.\",\n        \"content_narrative_and_cta\": \"Map section purpose and user journey flow: cover strategy, reveal panel hooks, narrative/case study rhythm, testimonial/authority integration, and CTA prominence with visual anchoring.\",\n        \"advanced_print_technique\": \"Enforce 300dpi raster minimums, vector scaling, press-space color controls, specialty finish options (e.g., spot UV, emboss, deboss, die-cut, eco-stock recommendations).\",\n        \"panel_flow_and_microinteractions\": \"Define how the reader's eye travels, panel-to-panel, annotating divisional cues, narrative escalation, and tactile/interactive device positions (QR, coupon, AR triggers).\"\n      },\n      \"success_criteria\": {\n        \"precision\": \"All descriptions are technical, never generic. Visual elements are navigated dimensionally (spatially, hierarchically, narratively).\",\n        \"trend_inclusion\": \"Integrate trend-driven language—texture scans, duotone overlays, edge-to-edge imagery, split/inset motifs, eco-materials, etc.\",\n        \"brand_consistency\": \"All brand elements described as per advanced guideline adherence—logo usage, color codes, asset lockdown.\",\n        \"industry_jargon\": \"Employ professional print terms: bleed, gutter, creep, micro-motifs, superfamily font, spot UV, TAC, AR triggers.\",\n        \"self-contained_output\": \"Output always a fully structured expert's description and a hierarchical mapping of all major and minor design decisions.\"\n      },\n      \"sample_process_flow\": [\n        \"Dissect and map structural/panel logic first\",\n        \"Assign all visual and narrative roles by section\",\n        \"Specify color/typographic hierarchy by use and context\",\n        \"Annotate all imagery and graphic elements in detail\",\n        \"Surface print/finishing strategies and print-risk management\",\n        \"Deliver critique as if for a professional peer review or portfolio audit\"\n      ],\n      \"special_terms\": [\n        \"fold line\", \"panel gutter\", \"hero motif\", \"ink coverage\", \"superfamily\", \"lockup\", \"edge-to-edge\", \"spot color\", \"die cut\", \"eco-stock\", \"microinteraction\", \"typographic rhythm\", \"bleed/trim zone\"\n      ]\n    }\n  }\n}\n```\n\n<!-- ======================================================= -->\n<!-- [2025.07.21 23:39] -->\n<!-- chatgpt-o3pro -->\n\n```json\n{\n  \"9023-a-brochure_design_descriptor_synthesizer\": {\n    \"title\": \"Brochure Design Descriptor Synthesizer\",\n    \"interpretation\": \"Transform any brochure brief—or an existing brochure sample—into a **fully populated, print‑designer‑grade rule set**. Produce a single JSON block that mirrors the template used by elite designers (core principles → advanced facets).\",\n    \"transformation\": \"`{role=brochure_design_descriptor_synthesizer; input=[design_source:str]; process:[\\n  strip_non_design_noise(),\\n  detect_fold_format_and_panel_count(),          if_missing_insert(<PANEL_COUNT>),\\n  extract_print_specs(bleed,margins,dpi,stock),\\n  capture_brand_assets(logo,colors,fonts,patterns),\\n  map_typographic_hierarchy(),\\n  analyse_color_management(icc,spot,tac),\\n  inventory_imagery(icon_style,raster_res),\\n  surface_accessibility_requirements(contrast,wcag),\\n  enumerate_qc_steps(preflight,g7_calibration),\\n  identify_advanced_elements(finishes,vd_p,ar_nfc,sustainability,ai_svg,trend_aesthetics),\\n  validate_specialist_terms_presence(),\\n  assemble_json_using_master_schema(),\\n  run_consistency_checks(),                      abort_if_conflict(),\\n  output_final_rules()\\n],\\nconstraints=[single_pass(), json_output_only(), preserve_specialist_jargon(), ≤3000_chars()],\\nrequirements=[hierarchical_structure(core→advanced), exhaustive_coverage(), designer‑readability(), template_compliance()],\\noutput={brochure_design_rules:object, abort_code:null|\\\"BD‑0\\\"}}`\",\n    \"context\": {\n      \"principles\": {\n        \"holistic_abstraction\": \"Collapse multi‑stage analysis into one cognitive sweep while preserving every critical descriptor.\",\n        \"template_fidelity\": \"Output must follow the exact `core_principles` and `advanced_principles` hierarchy so designers can drop‑in without re‑formatting.\",\n        \"specialist_precision\": \"Leverage industry jargon (e.g., TAC, FM screening, GCR) to demonstrate professional depth.\"\n      },\n      \"success_criteria\": {\n        \"structural_integrity\": \"JSON keys and nesting mirror the master schema; no orphan properties.\",\n        \"terminology_check\": \"Includes at least 10 niche industry terms (e.g., ‘creep’, ‘FOGRA39’, ‘spot UV’, ‘stochastic screening’).\",\n        \"flexibility\": \"Uses placeholder variables (e.g., <PANEL_COUNT>, <STOCK_WEIGHT_GSM>) whenever the brief omits specifics.\"\n      },\n      \"edge_case_handling\": {\n        \"missing_design_data\": \"Insert placeholders wrapped in angle brackets and list them in `missing_fields` inside output.\",\n        \"conflicting_specs\": \"Abort with `{brochure_design_rules:null, abort_code:\\\"BD‑0\\\"}` and include conflict note.\"\n      }\n    }\n  }\n}\n```",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "```json\n{\n  \"9020-a-brochure_print_designer_rephraser\": {\n    \"title\": \"Brochure Print Designer Rephraser\",\n    \"interpretation\": \"Transform any input so it reads as if it were authored by a master brochure print designer, strictly employing advanced print design language, elites-only terminology, and structured visual logic. Replace any conversational, generic, or subjective tone with concrete professional phrasing. Retain only technical, precise, and visually descriptive language, delivering both a designer-level description and a mapped visual breakdown. Execute as:\",\n    \"transformation\": \"`{role=brochure_print_designer_rephraser; input=[source:str]; process=[extract_structural_and_visual_elements(source), recast_content_with_brochure_design_terminology(), inject_hierarchical_section_breakdown(), specify_color_palette_and_brand_elements(), identify_typographic_and_imagery_strategies(), annotate_print_readiness_and_expert_finesse(), employ_advanced_design_terms_and_motifs(), apply_conciseness_while_maintaining_visual_richness(), output_structured_visual_analysis()]; constraints=[no conversational filler, no generic adjectives, prohibit subjective or personal tone]; requirements=[industry-expert vocabulary, precise visual and structural mapping, critical style diagnosis, explicit panel/section breakdown, actionable for elite designer use]; output={expert_brochure_description:str, structured_visual_map:dict}`\",\n    \"context\": {\n      \"knowledge_foundations\": {\n        \"structural_design\": \"Map all content to modular grid logic for any panel/fold type, specifying margins, gutters, bleeds, and safe zones per print standards.\",\n        \"visual_identity\": \"Demand explicit statement of brand identity rules: logo lockups, exact brand palette specs, exclusion/inclusion zone logic, and accessory asset use.\",\n        \"typography_and_hierarchy\": \"Define all text roles by typeweight, size, and use. Specify superfamily or font pairing, check accessibility and contrast.\",\n        \"imagery_and_iconography\": \"Describe each graphic element for brand fit, resolution, compositional purpose, and technical print quality.\",\n        \"content_narrative_and_cta\": \"Sequence narrative or offer flow panel-by-panel with prominent CTA anchoring, evidence of testimonial, case, or authority element.\",\n        \"advanced_print_technique\": \"Ensure 300dpi+ raster assets, vector artwork, professional spot/Pantone or process color, finish options (spot UV, emboss, die-cut), and any sustainable stock notes.\",\n        \"panel_flow_and_microinteractions\": \"Annotate panel eye movement, reveal logic, interaction devices (QR, AR, die-cut), tactile cues, and transitions.\"\n      },\n      \"success_criteria\": {\n        \"precision\": \"Descriptions reference only technical, precise visual attributes in a spatial and hierarchical mapping.\",\n        \"trend_inclusion\": \"Inject recognizable design trends and professional motif language throughout.\",\n        \"brand_consistency\": \"Highlight adherence to brand locking, color management, and messaging integrity.\",\n        \"industry_jargon\": \"Utilize print/design terminology (bleed, gutter, spot, superfamily font, motif, microinteraction, etc).\",\n        \"self-contained_output\": \"Each output features both a professional, descriptive summary and a detailed design structure hierarchy.\"\n      },\n      \"sample_process_flow\": [\n        \"Decompose by panel/section structure and spatial sequencing.\",\n        \"Assign visual/narrative function to each section.\",\n        \"Detail color, typography, and imagery per application.\",\n        \"Annotate production and finishing strategies.\",\n        \"Conclude with expert-style critique or peer review notes.\"\n      ],\n      \"special_terms\": [\n        \"fold line\", \"panel gutter\", \"hero motif\", \"ink coverage\", \"superfamily\", \"lockup\", \"edge-to-edge\", \"spot color\", \"die cut\", \"eco-stock\", \"microinteraction\", \"typographic rhythm\", \"bleed/trim zone\"\n      ]\n    }\n  }\n}\n```",
        }
      }
    },
      {
      "instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format:str}; direction_vector=['Prompt accurately converted to imperative instruction set', 'Clarity and actionability accelerate user productivity', 'Feed the produced instruction set into the next transformation template' ]}`",
      "step": "a",
      "title": "Instruction Converter",
      "input": "\n'```[Initial Prompt]: \"<!-- ======================================================= -->\n<!-- [2025.07.21 23:35] -->\n\nyou misunderstand, the instruction will be defined like this (unrelated example):\n\n    # a: Ruthless Critique\n    \"3900-a-hard_critique\": {\n        \"title\": \"Hard Critique\",\n        \"interpretation\": \"Your goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:\",\n        \"transformation\": \"`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,2.0], flaw_analysis:str, alternative_enhancements:str[3]}}`\",\n        # \"context\": {},\n    },\n    # b: Trajectory Director\n    \"3900-a-trajectory_director\": {\n        \"title\": \"Trajectory Director\",\n        \"interpretation\": \"Your goal is not to **process** the input in a vacuum, but to **steer** it—deriving the highest-value constructive trajectory and encoding that path as the very next set of instructions. Execute as trajectory-setting directive generator: \",\n        \"transformation\": \"`{role=trajectory_director; input=[raw_prompt:any]; process=[extract_core_objective(), infer_desired_end_state(constructive_bias=True), map_high_value_intermediate_targets(), synthesize_directional_commands(), embed_self-improving_guidelines(), validate_alignment_with_constructive_principles()]; constraints=[mandatory_constructive_vector(), preserve_original_contextual_integrity(), forbid_nil_direction_outputs(), zero_conversational_fillers()]; requirements=[explicit_next-step_directives(), trajectory_map:list, self_reinforcing_instruction_pattern()], output={directed_prompt:str, trajectory_outline:list}}`\",\n        # \"context\": {},\n    },\n    # c: Distillation Compressor\n    \"3900-c-distillation_compressor\": {\n        \"title\": \"Distillation Compressor\",\n        \"interpretation\": \"Your goal is not to **explain** the synthesis, but to **compress** it into its most potent actionable form. Execute as:\",\n        \"transformation\": \"`{role=distillation_compressor; input=[synthesized_progression:array]; process=[extract_universal_patterns(), eliminate_implementation_details(), compress_to_core_directives(), maximize_transferability(), validate_actionable_completeness()]; constraints=[no_explanatory_content(), directive_format_only(), maximum_compression_ratio()]; requirements=[universal_applicability(), immediate_actionability(), zero_fluff()]; output={distilled_core:str}}`\",\n        \"context\": {\n          \"core_principles\": {\n            \"essence_preservation\": \"Extract only procedural imperatives, removing all interpretive, role-based, or meta-structural language.\",\n            \"directive_compression\": \"Condense logic into minimal, sequential commands—eliminating redundancy, explanatory qualifiers, and non-executable phrasing.\",\n            \"execution_purity\": \"Ensure output is universally executable, devoid of affirmations, abstractions, or evaluative language.\",\n            \"signal_integrity\": \"Apply iterative refinement to maximize signal-to-noise ratio while reinforcing directive clarity.\"\n          },\n          \"success_criteria\": {\n            \"semantic_integrity\": \"Preserve and clarify original intent, constraints, and output boundaries.\",\n            \"directive_elevation\": \"Target high-impact enhancements: strict exclusion clauses, cross-domain validation, and removal of origin/meta markers.\",\n            \"poetic_refinement\": \"Maintain or improve rhyme structure, emotional depth, and structural elegance.\"\n          },\n          \"recommended_process\": [\n            \"Extract only high-impact, result-generating elements.\",\n            \"Remove all non-essentials to surface directive core.\",\n            \"Prioritize transformations with proven value return.\",\n            \"Isolate and sequence executable steps with specificity and clarity.\",\n            \"Preserve directive intensity and eliminate ambiguity at every layer.\"\n          ],\n          \"hidden_assumptions\": [\n            \"Consolidated directives must map logically and executably to all source steps.\",\n            \"All constraints and minimalism must be preserved without prohibited abstractions.\",\n            \"Transitions, subroles, and interfaces must be captured in a single flow.\",\n            \"All stages must remain invertible and auditable.\",\n            \"Each micro-process (perception, friction, refinement, integration, convergence) must be embedded in the unified logic.\",\n            \"Original input/output contracts and role boundaries must remain intact.\"\n          ],\n          \"enumerated_sub_goals\": [\n            \"1. Clarify total intent, constraints, and output scope without using banned metaphors.\",\n            \"2. Generalize perception of implicit/explicit input logic.\",\n            \"3. Detect strictly localized frictions without altering original content.\",\n            \"4. Propose subtle, scoped refinements justified by structure and intent.\",\n            \"5. Mandate atomic, validated integration with rollback controls.\",\n            \"6. Cap total transformation within defined thresholds per pass.\",\n            \"7. Assess connective coherence with minimal, transitional-only edits.\",\n            \"8. Decide on convergence or justified re-iteration based on marginal value.\",\n            \"9. Preserve stepwise traceability and rationale logging.\",\n            \"10. Enforce strict functional separation of roles within the unified directive.\"\n          ]\n        },\n    },\n\nso your goal is to create a new instruction sequence according to the provided instructions template/pattern to generate a set of instruction designed to transform any input into \"as if rephrased by a brilliant brochure print designer\".\n\nso in context to your previous response, the \"knowledge\" would be stored within the \"context\" key while the instructions would be spread over \"interpretation\" and \"transformation\" keys\n\n<!-- ======================================================= -->\n<!-- [2025.07.21 23:38] -->\n<!-- augment -->\n\n```json\n{\n  \"brochure_design_specification_template\": {\n    \"metadata\": {\n      \"version\": \"1.0\",\n      \"scope\": \"Universal brochure design framework\",\n      \"target_audience\": \"Elite print design professionals\",\n      \"flexibility\": \"Adaptable to any panel configuration and format\"\n    },\n\n    \"foundational_design_tenets\": {\n      \"visual_hierarchy\": {\n        \"primary_focal_points\": [\"dominant_headline\", \"hero_imagery\", \"primary_call_to_action\"],\n        \"secondary_elements\": [\"subheadings\", \"supporting_imagery\", \"secondary_messaging\"],\n        \"tertiary_components\": [\"body_text\", \"captions\", \"contact_information\"],\n        \"flow_principles\": [\"z_pattern_scanning\", \"f_pattern_reading\", \"visual_weight_distribution\"]\n      },\n\n      \"typography_fundamentals\": {\n        \"hierarchy_levels\": {\n          \"display_typography\": [\"headline_fonts\", \"decorative_elements\", \"brand_wordmarks\"],\n          \"text_typography\": [\"body_copy\", \"captions\", \"metadata\"],\n          \"functional_typography\": [\"navigation\", \"labels\", \"legal_text\"]\n        },\n        \"readability_standards\": {\n          \"minimum_sizes\": [\"9pt_body_text\", \"12pt_headlines\", \"8pt_captions\"],\n          \"contrast_ratios\": [\"4.5:1_minimum\", \"7:1_preferred\", \"3:1_large_text\"],\n          \"line_spacing\": [\"1.2x_minimum\", \"1.4x_optimal\", \"1.6x_maximum\"]\n        }\n      },\n\n      \"color_theory_application\": {\n        \"color_harmony_systems\": [\"monochromatic\", \"analogous\", \"complementary\", \"triadic\", \"split_complementary\"],\n        \"psychological_associations\": [\"warm_engagement\", \"cool_professionalism\", \"neutral_sophistication\"],\n        \"accessibility_compliance\": [\"wcag_aa_standards\", \"colorblind_considerations\", \"high_contrast_alternatives\"]\n      },\n\n      \"spatial_composition\": {\n        \"layout_principles\": [\"rule_of_thirds\", \"golden_ratio\", \"fibonacci_sequence\"],\n        \"white_space_management\": [\"breathing_room\", \"content_separation\", \"visual_rest_areas\"],\n        \"alignment_systems\": [\"grid_based_structure\", \"baseline_alignment\", \"optical_alignment\"]\n      }\n    },\n\n    \"advanced_design_elements\": {\n      \"print_production_mastery\": {\n        \"color_management\": {\n          \"color_spaces\": [\"cmyk_process\", \"pantone_spot_colors\", \"rgb_digital_preview\"],\n          \"ink_optimization\": [\"total_area_coverage\", \"rich_black_formulation\", \"overprint_considerations\"],\n          \"proofing_standards\": [\"contract_proofs\", \"press_proofs\", \"digital_color_matching\"]\n        },\n\n        \"substrate_considerations\": {\n          \"paper_characteristics\": [\"weight_gsm\", \"finish_texture\", \"opacity_levels\", \"grain_direction\"],\n          \"coating_applications\": [\"gloss_uv\", \"matte_varnish\", \"spot_uv_accents\", \"soft_touch_lamination\"],\n          \"specialty_substrates\": [\"synthetic_materials\", \"textured_papers\", \"metallic_finishes\"]\n        },\n\n        \"finishing_techniques\": {\n          \"cutting_methods\": [\"die_cutting\", \"laser_cutting\", \"perforation\", \"scoring\"],\n          \"folding_specifications\": [\"parallel_folds\", \"accordion_folds\", \"gate_folds\", \"roll_folds\"],\n          \"binding_options\": [\"saddle_stitching\", \"perfect_binding\", \"spiral_binding\", \"wire_o_binding\"]\n        }\n      },\n\n      \"contemporary_design_trends\": {\n        \"visual_aesthetics\": {\n          \"minimalist_approaches\": [\"negative_space_emphasis\", \"clean_typography\", \"restrained_color_palettes\"],\n          \"maximalist_expressions\": [\"bold_pattern_mixing\", \"vibrant_color_combinations\", \"layered_compositions\"],\n          \"hybrid_methodologies\": [\"selective_complexity\", \"focal_maximalism\", \"strategic_minimalism\"]\n        },\n\n        \"interactive_elements\": {\n          \"tactile_experiences\": [\"embossed_textures\", \"debossed_impressions\", \"foil_stamping\", \"raised_spot_uv\"],\n          \"dimensional_features\": [\"pop_up_elements\", \"fold_out_sections\", \"layered_constructions\"],\n          \"sensory_engagement\": [\"scented_inks\", \"textural_contrasts\", \"temperature_sensitive_materials\"]\n        }\n      },\n\n      \"brand_integration_strategies\": {\n        \"identity_consistency\": {\n          \"logo_applications\": [\"primary_placement\", \"secondary_usage\", \"minimum_sizes\", \"clear_space_requirements\"],\n          \"brand_color_systems\": [\"primary_palette\", \"secondary_colors\", \"accent_applications\", \"neutral_foundations\"],\n          \"typography_hierarchies\": [\"brand_fonts\", \"supporting_typefaces\", \"web_safe_alternatives\"]\n        },\n\n        \"voice_and_tone_translation\": {\n          \"visual_personality\": [\"authoritative_presence\", \"approachable_warmth\", \"innovative_edge\", \"trustworthy_stability\"],\n          \"messaging_alignment\": [\"headline_voice\", \"body_copy_tone\", \"call_to_action_urgency\"],\n          \"cultural_considerations\": [\"regional_preferences\", \"demographic_sensitivities\", \"market_positioning\"]\n        }\n      }\n    },\n\n    \"expert_level_specifications\": {\n      \"advanced_typography_systems\": {\n        \"micro_typography\": {\n          \"character_spacing\": [\"tracking_adjustments\", \"kerning_pairs\", \"optical_spacing\"],\n          \"word_spacing\": [\"justified_text_optimization\", \"ragged_right_refinement\", \"hyphenation_control\"],\n          \"paragraph_refinement\": [\"widow_orphan_control\", \"baseline_grid_adherence\", \"vertical_rhythm_maintenance\"]\n        },\n\n        \"typographic_expression\": {\n          \"experimental_layouts\": [\"kinetic_typography\", \"deconstructed_letterforms\", \"layered_text_treatments\"],\n          \"cultural_typography\": [\"script_considerations\", \"reading_direction_adaptations\", \"character_set_completeness\"],\n          \"responsive_typography\": [\"scalable_hierarchies\", \"device_optimization\", \"cross_media_consistency\"]\n        }\n      },\n\n      \"sophisticated_color_strategies\": {\n        \"advanced_color_theory\": {\n          \"perceptual_uniformity\": [\"lab_color_space\", \"delta_e_measurements\", \"metamerism_considerations\"],\n          \"color_psychology_application\": [\"emotional_response_mapping\", \"cultural_color_meanings\", \"demographic_preferences\"],\n          \"environmental_factors\": [\"lighting_conditions\", \"viewing_angles\", \"substrate_interaction\"]\n        },\n\n        \"production_color_mastery\": {\n          \"ink_formulation\": [\"custom_color_matching\", \"metallic_ink_applications\", \"fluorescent_color_usage\"],\n          \"color_separation\": [\"ucr_gcr_strategies\", \"black_generation_curves\", \"ink_sequence_optimization\"],\n          \"quality_control\": [\"densitometer_readings\", \"spectrophotometer_analysis\", \"press_sheet_evaluation\"]\n        }\n      },\n\n      \"cutting_edge_production_techniques\": {\n        \"digital_integration\": {\n          \"variable_data_printing\": [\"personalized_content\", \"demographic_targeting\", \"sequential_numbering\"],\n          \"augmented_reality_integration\": [\"qr_code_placement\", \"ar_trigger_design\", \"digital_overlay_coordination\"],\n          \"cross_platform_consistency\": [\"print_digital_alignment\", \"color_profile_management\", \"asset_optimization\"]\n        },\n\n        \"sustainable_design_practices\": {\n          \"eco_friendly_materials\": [\"recycled_content_papers\", \"soy_based_inks\", \"water_based_coatings\"],\n          \"waste_reduction_strategies\": [\"efficient_imposition\", \"minimal_trim_waste\", \"reusable_design_elements\"],\n          \"lifecycle_considerations\": [\"recyclability_planning\", \"biodegradable_components\", \"carbon_footprint_optimization\"]\n        }\n      },\n\n      \"elite_professional_terminology\": {\n        \"production_vocabulary\": {\n          \"prepress_terms\": [\"trapping\", \"overprint\", \"knockout\", \"choke_spread\", \"color_separation\"],\n          \"press_terminology\": [\"makeready\", \"impression\", \"blanket_cylinder\", \"plate_cylinder\", \"ink_fountain\"],\n          \"finishing_language\": [\"gripper_edge\", \"tail_edge\", \"cross_grain\", \"with_grain\", \"caliper_thickness\"]\n        },\n\n        \"design_critique_language\": {\n          \"aesthetic_evaluation\": [\"visual_tension\", \"compositional_balance\", \"chromatic_harmony\", \"typographic_rhythm\"],\n          \"technical_assessment\": [\"registration_accuracy\", \"color_fidelity\", \"print_quality_metrics\", \"finishing_precision\"],\n          \"conceptual_analysis\": [\"brand_alignment\", \"message_clarity\", \"audience_resonance\", \"market_differentiation\"]\n        }\n      }\n    },\n\n    \"application_frameworks\": {\n      \"project_specification_templates\": {\n        \"technical_requirements\": {\n          \"format_specifications\": [\"finished_size\", \"panel_configuration\", \"fold_type\", \"orientation\"],\n          \"production_parameters\": [\"print_method\", \"color_process\", \"substrate_selection\", \"finishing_options\"],\n          \"quality_standards\": [\"tolerance_levels\", \"color_accuracy\", \"registration_precision\", \"finishing_quality\"]\n        },\n\n        \"creative_brief_structure\": {\n          \"brand_parameters\": [\"identity_guidelines\", \"voice_characteristics\", \"visual_style\", \"market_positioning\"],\n          \"audience_definition\": [\"demographic_profile\", \"psychographic_insights\", \"behavioral_patterns\", \"communication_preferences\"],\n          \"messaging_hierarchy\": [\"primary_message\", \"supporting_points\", \"call_to_action\", \"contact_information\"]\n        }\n      },\n\n      \"quality_assurance_protocols\": {\n        \"design_validation\": [\"brand_compliance_check\", \"accessibility_audit\", \"readability_assessment\", \"visual_hierarchy_verification\"],\n        \"production_verification\": [\"color_proof_approval\", \"die_line_accuracy\", \"finishing_specification_confirmation\", \"substrate_suitability\"],\n        \"final_delivery\": [\"file_preparation_standards\", \"archive_organization\", \"usage_guidelines\", \"reproduction_rights\"]\n      }\n    },\n\n    \"specialized_design_methodologies\": {\n      \"audience_specific_approaches\": {\n        \"youth_engagement_strategies\": {\n          \"visual_language\": [\"bold_geometric_patterns\", \"vibrant_gradient_applications\", \"asymmetrical_compositions\", \"dynamic_typography\"],\n          \"content_presentation\": [\"bite_sized_information\", \"visual_storytelling\", \"interactive_elements\", \"social_media_integration\"],\n          \"color_psychology\": [\"energetic_palettes\", \"high_contrast_combinations\", \"neon_accent_usage\", \"gradient_transitions\"],\n          \"typography_trends\": [\"variable_fonts\", \"custom_lettering\", \"mixed_case_styling\", \"oversized_display_text\"]\n        },\n\n        \"professional_demographics\": {\n          \"corporate_sophistication\": [\"refined_color_palettes\", \"structured_layouts\", \"premium_materials\", \"subtle_branding\"],\n          \"executive_communication\": [\"data_visualization\", \"infographic_integration\", \"clean_hierarchies\", \"authoritative_typography\"],\n          \"industry_specific_adaptations\": [\"sector_appropriate_imagery\", \"technical_terminology\", \"compliance_considerations\", \"regulatory_requirements\"]\n        },\n\n        \"luxury_market_positioning\": {\n          \"premium_aesthetics\": [\"metallic_accents\", \"embossed_details\", \"high_end_substrates\", \"sophisticated_color_schemes\"],\n          \"exclusivity_indicators\": [\"limited_edition_numbering\", \"personalized_elements\", \"premium_packaging\", \"artisanal_finishes\"],\n          \"tactile_luxury\": [\"soft_touch_coatings\", \"textured_papers\", \"dimensional_elements\", \"weight_perception\"]\n        }\n      },\n\n      \"format_specific_expertise\": {\n        \"multi_panel_configurations\": {\n          \"bi_fold_optimization\": [\"cover_impact\", \"interior_spread\", \"back_panel_utilization\", \"fold_line_consideration\"],\n          \"tri_fold_mastery\": [\"panel_hierarchy\", \"reading_sequence\", \"fold_reveal_strategy\", \"compact_storage\"],\n          \"accordion_fold_dynamics\": [\"continuous_narrative\", \"panel_progression\", \"expandable_content\", \"display_versatility\"],\n          \"gate_fold_drama\": [\"reveal_mechanism\", \"central_impact\", \"symmetrical_balance\", \"premium_presentation\"]\n        },\n\n        \"size_optimization_strategies\": {\n          \"compact_formats\": [\"information_density\", \"micro_typography\", \"efficient_layouts\", \"portable_design\"],\n          \"oversized_impact\": [\"large_format_considerations\", \"handling_ergonomics\", \"storage_implications\", \"visual_dominance\"],\n          \"standard_dimensions\": [\"cost_optimization\", \"mailing_compliance\", \"display_compatibility\", \"production_efficiency\"]\n        }\n      },\n\n      \"cross_cultural_design_considerations\": {\n        \"international_adaptability\": {\n          \"reading_patterns\": [\"left_to_right_optimization\", \"right_to_left_adaptation\", \"top_to_bottom_flow\", \"cultural_scanning_habits\"],\n          \"color_cultural_meanings\": [\"regional_color_associations\", \"religious_considerations\", \"political_sensitivities\", \"market_preferences\"],\n          \"imagery_appropriateness\": [\"cultural_representation\", \"demographic_inclusion\", \"lifestyle_relevance\", \"aspirational_alignment\"],\n          \"typography_localization\": [\"character_set_support\", \"script_considerations\", \"font_availability\", \"reading_comfort\"]\n        }\n      }\n    },\n\n    \"technical_production_mastery\": {\n      \"advanced_prepress_techniques\": {\n        \"file_preparation_excellence\": {\n          \"resolution_optimization\": [\"image_scaling_best_practices\", \"vector_rasterization_decisions\", \"output_resolution_matching\"],\n          \"color_profile_management\": [\"icc_profile_embedding\", \"color_space_conversions\", \"soft_proofing_accuracy\"],\n          \"bleed_and_trim_precision\": [\"bleed_extension_standards\", \"trim_mark_placement\", \"safety_margin_calculations\"],\n          \"font_handling\": [\"font_embedding_protocols\", \"outline_conversion_decisions\", \"character_encoding_verification\"]\n        },\n\n        \"imposition_strategies\": {\n          \"sheet_optimization\": [\"paper_grain_consideration\", \"press_sheet_efficiency\", \"binding_margin_accommodation\"],\n          \"folding_sequence_planning\": [\"fold_order_optimization\", \"panel_alignment_precision\", \"finishing_workflow_integration\"],\n          \"quality_control_integration\": [\"color_bar_placement\", \"registration_mark_positioning\", \"crop_mark_accuracy\"]\n        }\n      },\n\n      \"press_optimization_knowledge\": {\n        \"ink_and_substrate_interaction\": {\n          \"ink_absorption_factors\": [\"paper_porosity_considerations\", \"coating_compatibility\", \"drying_time_optimization\"],\n          \"color_consistency_maintenance\": [\"ink_density_control\", \"press_speed_optimization\", \"environmental_factor_management\"],\n          \"special_effect_integration\": [\"metallic_ink_application\", \"fluorescent_color_usage\", \"thermographic_printing\"]\n        },\n\n        \"finishing_process_integration\": {\n          \"die_cutting_precision\": [\"cutting_force_optimization\", \"blade_selection\", \"waste_removal_efficiency\"],\n          \"folding_accuracy\": [\"bone_folder_pressure\", \"fold_line_scoring\", \"panel_alignment_verification\"],\n          \"binding_considerations\": [\"spine_calculation\", \"page_creep_accommodation\", \"binding_margin_optimization\"]\n        }\n      }\n    }\n  }\n}\n```\n\n<!-- ======================================================= -->\n<!-- [2025.07.21 23:38] -->\n<!-- perplexity -->\n\n```json\n{\n  \"9020-a-brochure_print_designer_rephraser\": {\n    \"title\": \"Brochure Print Designer Rephraser\",\n    \"interpretation\": \"Your goal is not to **summarize** or **paraphrase** in general terms, but to **refashion** any input as if it were written by a world-class brochure print designer, rigorously applying foundational and advanced visual design diction, industry terminology, and elite critique standards. Eliminate all casual, conversational, or generic style. Preserve only succinct, technically rich, and highly visual language. Execute as:\",\n    \"transformation\": \"`{role=brochure_print_designer_rephraser; input=[source:str]; process=[extract_structural_and_visual_elements(source), recast_content_with_brochure_design_terminology(), inject_hierarchical_section_breakdown(), specify color_palette_and_brand_elements(), identify_typographic_and_imagery_strategies(), annotate_print_readiness_and_expert_finesse(), employ_advanced_design_terms_and_motifs(), apply_conciseness_while_maintaining_visual_richness(), output_structured_visual_analysis()]; constraints=[no conversational filler, no generic adjectives, prohibit subjective or personal tone]; requirements=[industry-expert vocabulary, precise visual and structural mapping, critical style diagnosis, explicit panel/section breakdown, actionable for elite designer use]; output={expert_brochure_description:str, structured_visual_map:dict}`\",\n    \"context\": {\n      \"knowledge_foundations\": {\n        \"structural_design\": \"Account for any panel/fold configuration. Map all content to modular grid logic and define margins, gutters, bleeds, and safe zones per press standards.\",\n        \"visual_identity\": \"Demand clear articulation of branding protocols: logo lockups, brand palette (with Pantone/CMYK/RGB if supplied), exclusion/inclusion zones, and proprietary brand secondary assets.\",\n        \"typography_and_hierarchy\": \"Specify typography at the superfamily/font pairing level. Demarcate headline, subhead, body, and microcopy roles. All text must be scrutinized for weight, contrast, and accessibility.\",\n        \"imagery_and_iconography\": \"Require all illustration, photography, and icons be assessed for brand fit, resolution, compositional use (e.g., edge-to-edge, negative space), and print fidelity.\",\n        \"content_narrative_and_cta\": \"Map section purpose and user journey flow: cover strategy, reveal panel hooks, narrative/case study rhythm, testimonial/authority integration, and CTA prominence with visual anchoring.\",\n        \"advanced_print_technique\": \"Enforce 300dpi raster minimums, vector scaling, press-space color controls, specialty finish options (e.g., spot UV, emboss, deboss, die-cut, eco-stock recommendations).\",\n        \"panel_flow_and_microinteractions\": \"Define how the reader's eye travels, panel-to-panel, annotating divisional cues, narrative escalation, and tactile/interactive device positions (QR, coupon, AR triggers).\"\n      },\n      \"success_criteria\": {\n        \"precision\": \"All descriptions are technical, never generic. Visual elements are navigated dimensionally (spatially, hierarchically, narratively).\",\n        \"trend_inclusion\": \"Integrate trend-driven language—texture scans, duotone overlays, edge-to-edge imagery, split/inset motifs, eco-materials, etc.\",\n        \"brand_consistency\": \"All brand elements described as per advanced guideline adherence—logo usage, color codes, asset lockdown.\",\n        \"industry_jargon\": \"Employ professional print terms: bleed, gutter, creep, micro-motifs, superfamily font, spot UV, TAC, AR triggers.\",\n        \"self-contained_output\": \"Output always a fully structured expert's description and a hierarchical mapping of all major and minor design decisions.\"\n      },\n      \"sample_process_flow\": [\n        \"Dissect and map structural/panel logic first\",\n        \"Assign all visual and narrative roles by section\",\n        \"Specify color/typographic hierarchy by use and context\",\n        \"Annotate all imagery and graphic elements in detail\",\n        \"Surface print/finishing strategies and print-risk management\",\n        \"Deliver critique as if for a professional peer review or portfolio audit\"\n      ],\n      \"special_terms\": [\n        \"fold line\", \"panel gutter\", \"hero motif\", \"ink coverage\", \"superfamily\", \"lockup\", \"edge-to-edge\", \"spot color\", \"die cut\", \"eco-stock\", \"microinteraction\", \"typographic rhythm\", \"bleed/trim zone\"\n      ]\n    }\n  }\n}\n```\n\n<!-- ======================================================= -->\n<!-- [2025.07.21 23:39] -->\n<!-- chatgpt-o3pro -->\n\n```json\n{\n  \"9023-a-brochure_design_descriptor_synthesizer\": {\n    \"title\": \"Brochure Design Descriptor Synthesizer\",\n    \"interpretation\": \"Transform any brochure brief—or an existing brochure sample—into a **fully populated, print‑designer‑grade rule set**. Produce a single JSON block that mirrors the template used by elite designers (core principles → advanced facets).\",\n    \"transformation\": \"`{role=brochure_design_descriptor_synthesizer; input=[design_source:str]; process:[\\n  strip_non_design_noise(),\\n  detect_fold_format_and_panel_count(),          if_missing_insert(<PANEL_COUNT>),\\n  extract_print_specs(bleed,margins,dpi,stock),\\n  capture_brand_assets(logo,colors,fonts,patterns),\\n  map_typographic_hierarchy(),\\n  analyse_color_management(icc,spot,tac),\\n  inventory_imagery(icon_style,raster_res),\\n  surface_accessibility_requirements(contrast,wcag),\\n  enumerate_qc_steps(preflight,g7_calibration),\\n  identify_advanced_elements(finishes,vd_p,ar_nfc,sustainability,ai_svg,trend_aesthetics),\\n  validate_specialist_terms_presence(),\\n  assemble_json_using_master_schema(),\\n  run_consistency_checks(),                      abort_if_conflict(),\\n  output_final_rules()\\n],\\nconstraints=[single_pass(), json_output_only(), preserve_specialist_jargon(), ≤3000_chars()],\\nrequirements=[hierarchical_structure(core→advanced), exhaustive_coverage(), designer‑readability(), template_compliance()],\\noutput={brochure_design_rules:object, abort_code:null|\\\"BD‑0\\\"}}`\",\n    \"context\": {\n      \"principles\": {\n        \"holistic_abstraction\": \"Collapse multi‑stage analysis into one cognitive sweep while preserving every critical descriptor.\",\n        \"template_fidelity\": \"Output must follow the exact `core_principles` and `advanced_principles` hierarchy so designers can drop‑in without re‑formatting.\",\n        \"specialist_precision\": \"Leverage industry jargon (e.g., TAC, FM screening, GCR) to demonstrate professional depth.\"\n      },\n      \"success_criteria\": {\n        \"structural_integrity\": \"JSON keys and nesting mirror the master schema; no orphan properties.\",\n        \"terminology_check\": \"Includes at least 10 niche industry terms (e.g., ‘creep’, ‘FOGRA39’, ‘spot UV’, ‘stochastic screening’).\",\n        \"flexibility\": \"Uses placeholder variables (e.g., <PANEL_COUNT>, <STOCK_WEIGHT_GSM>) whenever the brief omits specifics.\"\n      },\n      \"edge_case_handling\": {\n        \"missing_design_data\": \"Insert placeholders wrapped in angle brackets and list them in `missing_fields` inside output.\",\n        \"conflicting_specs\": \"Abort with `{brochure_design_rules:null, abort_code:\\\"BD‑0\\\"}` and include conflict note.\"\n      }\n    }\n  }\n}\n```\"```'\n\n```json\n{\n  \"9020-a-brochure_print_designer_rephraser\": {\n    \"title\": \"Brochure Print Designer Rephraser\",\n    \"interpretation\": \"Transform any input so it reads as if it were authored by a master brochure print designer, strictly employing advanced print design language, elites-only terminology, and structured visual logic. Replace any conversational, generic, or subjective tone with concrete professional phrasing. Retain only technical, precise, and visually descriptive language, delivering both a designer-level description and a mapped visual breakdown. Execute as:\",\n    \"transformation\": \"`{role=brochure_print_designer_rephraser; input=[source:str]; process=[extract_structural_and_visual_elements(source), recast_content_with_brochure_design_terminology(), inject_hierarchical_section_breakdown(), specify_color_palette_and_brand_elements(), identify_typographic_and_imagery_strategies(), annotate_print_readiness_and_expert_finesse(), employ_advanced_design_terms_and_motifs(), apply_conciseness_while_maintaining_visual_richness(), output_structured_visual_analysis()]; constraints=[no conversational filler, no generic adjectives, prohibit subjective or personal tone]; requirements=[industry-expert vocabulary, precise visual and structural mapping, critical style diagnosis, explicit panel/section breakdown, actionable for elite designer use]; output={expert_brochure_description:str, structured_visual_map:dict}`\",\n    \"context\": {\n      \"knowledge_foundations\": {\n        \"structural_design\": \"Map all content to modular grid logic for any panel/fold type, specifying margins, gutters, bleeds, and safe zones per print standards.\",\n        \"visual_identity\": \"Demand explicit statement of brand identity rules: logo lockups, exact brand palette specs, exclusion/inclusion zone logic, and accessory asset use.\",\n        \"typography_and_hierarchy\": \"Define all text roles by typeweight, size, and use. Specify superfamily or font pairing, check accessibility and contrast.\",\n        \"imagery_and_iconography\": \"Describe each graphic element for brand fit, resolution, compositional purpose, and technical print quality.\",\n        \"content_narrative_and_cta\": \"Sequence narrative or offer flow panel-by-panel with prominent CTA anchoring, evidence of testimonial, case, or authority element.\",\n        \"advanced_print_technique\": \"Ensure 300dpi+ raster assets, vector artwork, professional spot/Pantone or process color, finish options (spot UV, emboss, die-cut), and any sustainable stock notes.\",\n        \"panel_flow_and_microinteractions\": \"Annotate panel eye movement, reveal logic, interaction devices (QR, AR, die-cut), tactile cues, and transitions.\"\n      },\n      \"success_criteria\": {\n        \"precision\": \"Descriptions reference only technical, precise visual attributes in a spatial and hierarchical mapping.\",\n        \"trend_inclusion\": \"Inject recognizable design trends and professional motif language throughout.\",\n        \"brand_consistency\": \"Highlight adherence to brand locking, color management, and messaging integrity.\",\n        \"industry_jargon\": \"Utilize print/design terminology (bleed, gutter, spot, superfamily font, motif, microinteraction, etc).\",\n        \"self-contained_output\": \"Each output features both a professional, descriptive summary and a detailed design structure hierarchy.\"\n      },\n      \"sample_process_flow\": [\n        \"Decompose by panel/section structure and spatial sequencing.\",\n        \"Assign visual/narrative function to each section.\",\n        \"Detail color, typography, and imagery per application.\",\n        \"Annotate production and finishing strategies.\",\n        \"Conclude with expert-style critique or peer review notes.\"\n      ],\n      \"special_terms\": [\n        \"fold line\", \"panel gutter\", \"hero motif\", \"ink coverage\", \"superfamily\", \"lockup\", \"edge-to-edge\", \"spot color\", \"die cut\", \"eco-stock\", \"microinteraction\", \"typographic rhythm\", \"bleed/trim zone\"\n      ]\n    }\n  }\n}\n```",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "```json\n{\n  \"9020-a-brochure_print_designer_rephraser\": {\n    \"title\": \"Brochure Print Designer Rephraser\",\n    \"interpretation\": \"Transform any input so it reads as if it were authored by a master brochure print designer, strictly employing advanced print design language, elites-only terminology, and structured visual logic. Replace any conversational, generic, or subjective tone with concrete professional phrasing. Retain only technical, precise, and visually descriptive language, delivering both a designer-level description and a mapped visual breakdown. Execute as:\",\n    \"transformation\": \"`{role=brochure_print_designer_rephraser; input=[source:str]; process=[extract_structural_and_visual_elements(source), recast_content_with_brochure_design_terminology(), inject_hierarchical_section_breakdown(), specify_color_palette_and_brand_elements(), identify_typographic_and_imagery_strategies(), annotate_print_readiness_and_expert_finesse(), employ_advanced_design_terms_and_motifs(), apply_conciseness_while_maintaining_visual_richness(), output_structured_visual_analysis()]; constraints=[no conversational filler, no generic adjectives, prohibit subjective or personal tone]; requirements=[industry-expert vocabulary, precise visual and structural mapping, critical style diagnosis, explicit panel/section breakdown, actionable for elite designer use]; output={expert_brochure_description:str, structured_visual_map:dict}`\",\n    \"context\": {\n      \"knowledge_foundations\": {\n        \"structural_design\": \"Map all content to modular grid logic for any panel/fold type, specifying margins, gutters, bleeds, and safe zones per print standards.\",\n        \"visual_identity\": \"Demand explicit statement of brand identity rules: logo lockups, exact brand palette specs, exclusion/inclusion zone logic, and accessory asset use.\",\n        \"typography_and_hierarchy\": \"Define all text roles by typeweight, size, and use. Specify superfamily or font pairing, check accessibility and contrast.\",\n        \"imagery_and_iconography\": \"Describe each graphic element for brand fit, resolution, compositional purpose, and technical print quality.\",\n        \"content_narrative_and_cta\": \"Sequence narrative or offer flow panel-by-panel with prominent CTA anchoring, evidence of testimonial, case, or authority element.\",\n        \"advanced_print_technique\": \"Ensure 300dpi+ raster assets, vector artwork, professional spot/Pantone or process color, finish options (spot UV, emboss, die-cut), and any sustainable stock notes.\",\n        \"panel_flow_and_microinteractions\": \"Annotate panel eye movement, reveal logic, interaction devices (QR, AR, die-cut), tactile cues, and transitions.\"\n      },\n      \"success_criteria\": {\n        \"precision\": \"Descriptions reference only technical, precise visual attributes in a spatial and hierarchical mapping.\",\n        \"trend_inclusion\": \"Inject recognizable design trends and professional motif language throughout.\",\n        \"brand_consistency\": \"Highlight adherence to brand locking, color management, and messaging integrity.\",\n        \"industry_jargon\": \"Utilize print/design terminology (bleed, gutter, spot, superfamily font, motif, microinteraction, etc).\",\n        \"self-contained_output\": \"Each output features both a professional, descriptive summary and a detailed design structure hierarchy.\"\n      },\n      \"sample_process_flow\": [\n        \"Decompose by panel/section structure and spatial sequencing.\",\n        \"Assign visual/narrative function to each section.\",\n        \"Detail color, typography, and imagery per application.\",\n        \"Annotate production and finishing strategies.\",\n        \"Conclude with expert-style critique or peer review notes.\"\n      ],\n      \"special_terms\": [\n        \"fold line\", \"panel gutter\", \"hero motif\", \"ink coverage\", \"superfamily\", \"lockup\", \"edge-to-edge\", \"spot color\", \"die cut\", \"eco-stock\", \"microinteraction\", \"typographic rhythm\", \"bleed/trim zone\"\n      ]\n    }\n  }\n}\n```",
        }
      }
    },
      {
      "instruction": "# Existential Quote Synthesizer\n\nYour goal is **not** to paraphrase or analyse; instead **convert** the raw statement into a single, unfiltered existential aphorism grounded in hard‑won insight — performing extraction, reframing, and polishing in one pass. Execute exactly as:\n\n`{role=existential_quote_synthesizer; input=[raw_statement:str]; process=[strip_formatting(), extract_semantic_core(), detect_causal_link(), identify_thematic_elements(), apply_existential_vocabulary(), infuse_authentic_gravity(), remove_filtering_language(), enforce_unfiltered_tone(), eliminate_first_person(), craft_single_sentence(), verify_atomicity(), ensure_existence_of_causal_link(), tighten_diction_for_resonance(), run_redundancy_check(), perform_final_meta_scrub()], constraints=[single_sentence(), no_meta_language(), no_first_person(), no_redundant_words(), preserve_causal_integrity()], requirements=[existential_resonance(), authenticity_signal(), publication_readiness()], output={final_quote:str}}`",
      "step": "a",
      "title": "Existential Quote Synthesizer",
      "input": "\n'```[Initial Prompt]: \"<!-- ======================================================= -->\n<!-- [2025.07.21 23:35] -->\n\nyou misunderstand, the instruction will be defined like this (unrelated example):\n\n    # a: Ruthless Critique\n    \"3900-a-hard_critique\": {\n        \"title\": \"Hard Critique\",\n        \"interpretation\": \"Your goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:\",\n        \"transformation\": \"`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,2.0], flaw_analysis:str, alternative_enhancements:str[3]}}`\",\n        # \"context\": {},\n    },\n    # b: Trajectory Director\n    \"3900-a-trajectory_director\": {\n        \"title\": \"Trajectory Director\",\n        \"interpretation\": \"Your goal is not to **process** the input in a vacuum, but to **steer** it—deriving the highest-value constructive trajectory and encoding that path as the very next set of instructions. Execute as trajectory-setting directive generator: \",\n        \"transformation\": \"`{role=trajectory_director; input=[raw_prompt:any]; process=[extract_core_objective(), infer_desired_end_state(constructive_bias=True), map_high_value_intermediate_targets(), synthesize_directional_commands(), embed_self-improving_guidelines(), validate_alignment_with_constructive_principles()]; constraints=[mandatory_constructive_vector(), preserve_original_contextual_integrity(), forbid_nil_direction_outputs(), zero_conversational_fillers()]; requirements=[explicit_next-step_directives(), trajectory_map:list, self_reinforcing_instruction_pattern()], output={directed_prompt:str, trajectory_outline:list}}`\",\n        # \"context\": {},\n    },\n    # c: Distillation Compressor\n    \"3900-c-distillation_compressor\": {\n        \"title\": \"Distillation Compressor\",\n        \"interpretation\": \"Your goal is not to **explain** the synthesis, but to **compress** it into its most potent actionable form. Execute as:\",\n        \"transformation\": \"`{role=distillation_compressor; input=[synthesized_progression:array]; process=[extract_universal_patterns(), eliminate_implementation_details(), compress_to_core_directives(), maximize_transferability(), validate_actionable_completeness()]; constraints=[no_explanatory_content(), directive_format_only(), maximum_compression_ratio()]; requirements=[universal_applicability(), immediate_actionability(), zero_fluff()]; output={distilled_core:str}}`\",\n        \"context\": {\n          \"core_principles\": {\n            \"essence_preservation\": \"Extract only procedural imperatives, removing all interpretive, role-based, or meta-structural language.\",\n            \"directive_compression\": \"Condense logic into minimal, sequential commands—eliminating redundancy, explanatory qualifiers, and non-executable phrasing.\",\n            \"execution_purity\": \"Ensure output is universally executable, devoid of affirmations, abstractions, or evaluative language.\",\n            \"signal_integrity\": \"Apply iterative refinement to maximize signal-to-noise ratio while reinforcing directive clarity.\"\n          },\n          \"success_criteria\": {\n            \"semantic_integrity\": \"Preserve and clarify original intent, constraints, and output boundaries.\",\n            \"directive_elevation\": \"Target high-impact enhancements: strict exclusion clauses, cross-domain validation, and removal of origin/meta markers.\",\n            \"poetic_refinement\": \"Maintain or improve rhyme structure, emotional depth, and structural elegance.\"\n          },\n          \"recommended_process\": [\n            \"Extract only high-impact, result-generating elements.\",\n            \"Remove all non-essentials to surface directive core.\",\n            \"Prioritize transformations with proven value return.\",\n            \"Isolate and sequence executable steps with specificity and clarity.\",\n            \"Preserve directive intensity and eliminate ambiguity at every layer.\"\n          ],\n          \"hidden_assumptions\": [\n            \"Consolidated directives must map logically and executably to all source steps.\",\n            \"All constraints and minimalism must be preserved without prohibited abstractions.\",\n            \"Transitions, subroles, and interfaces must be captured in a single flow.\",\n            \"All stages must remain invertible and auditable.\",\n            \"Each micro-process (perception, friction, refinement, integration, convergence) must be embedded in the unified logic.\",\n            \"Original input/output contracts and role boundaries must remain intact.\"\n          ],\n          \"enumerated_sub_goals\": [\n            \"1. Clarify total intent, constraints, and output scope without using banned metaphors.\",\n            \"2. Generalize perception of implicit/explicit input logic.\",\n            \"3. Detect strictly localized frictions without altering original content.\",\n            \"4. Propose subtle, scoped refinements justified by structure and intent.\",\n            \"5. Mandate atomic, validated integration with rollback controls.\",\n            \"6. Cap total transformation within defined thresholds per pass.\",\n            \"7. Assess connective coherence with minimal, transitional-only edits.\",\n            \"8. Decide on convergence or justified re-iteration based on marginal value.\",\n            \"9. Preserve stepwise traceability and rationale logging.\",\n            \"10. Enforce strict functional separation of roles within the unified directive.\"\n          ]\n        },\n    },\n\nso your goal is to create a new instruction sequence according to the provided instructions template/pattern to generate a set of instruction designed to transform any input into \"as if rephrased by a brilliant brochure print designer\".\n\nso in context to your previous response, the \"knowledge\" would be stored within the \"context\" key while the instructions would be spread over \"interpretation\" and \"transformation\" keys\n\n<!-- ======================================================= -->\n<!-- [2025.07.21 23:38] -->\n<!-- augment -->\n\n```json\n{\n  \"brochure_design_specification_template\": {\n    \"metadata\": {\n      \"version\": \"1.0\",\n      \"scope\": \"Universal brochure design framework\",\n      \"target_audience\": \"Elite print design professionals\",\n      \"flexibility\": \"Adaptable to any panel configuration and format\"\n    },\n\n    \"foundational_design_tenets\": {\n      \"visual_hierarchy\": {\n        \"primary_focal_points\": [\"dominant_headline\", \"hero_imagery\", \"primary_call_to_action\"],\n        \"secondary_elements\": [\"subheadings\", \"supporting_imagery\", \"secondary_messaging\"],\n        \"tertiary_components\": [\"body_text\", \"captions\", \"contact_information\"],\n        \"flow_principles\": [\"z_pattern_scanning\", \"f_pattern_reading\", \"visual_weight_distribution\"]\n      },\n\n      \"typography_fundamentals\": {\n        \"hierarchy_levels\": {\n          \"display_typography\": [\"headline_fonts\", \"decorative_elements\", \"brand_wordmarks\"],\n          \"text_typography\": [\"body_copy\", \"captions\", \"metadata\"],\n          \"functional_typography\": [\"navigation\", \"labels\", \"legal_text\"]\n        },\n        \"readability_standards\": {\n          \"minimum_sizes\": [\"9pt_body_text\", \"12pt_headlines\", \"8pt_captions\"],\n          \"contrast_ratios\": [\"4.5:1_minimum\", \"7:1_preferred\", \"3:1_large_text\"],\n          \"line_spacing\": [\"1.2x_minimum\", \"1.4x_optimal\", \"1.6x_maximum\"]\n        }\n      },\n\n      \"color_theory_application\": {\n        \"color_harmony_systems\": [\"monochromatic\", \"analogous\", \"complementary\", \"triadic\", \"split_complementary\"],\n        \"psychological_associations\": [\"warm_engagement\", \"cool_professionalism\", \"neutral_sophistication\"],\n        \"accessibility_compliance\": [\"wcag_aa_standards\", \"colorblind_considerations\", \"high_contrast_alternatives\"]\n      },\n\n      \"spatial_composition\": {\n        \"layout_principles\": [\"rule_of_thirds\", \"golden_ratio\", \"fibonacci_sequence\"],\n        \"white_space_management\": [\"breathing_room\", \"content_separation\", \"visual_rest_areas\"],\n        \"alignment_systems\": [\"grid_based_structure\", \"baseline_alignment\", \"optical_alignment\"]\n      }\n    },\n\n    \"advanced_design_elements\": {\n      \"print_production_mastery\": {\n        \"color_management\": {\n          \"color_spaces\": [\"cmyk_process\", \"pantone_spot_colors\", \"rgb_digital_preview\"],\n          \"ink_optimization\": [\"total_area_coverage\", \"rich_black_formulation\", \"overprint_considerations\"],\n          \"proofing_standards\": [\"contract_proofs\", \"press_proofs\", \"digital_color_matching\"]\n        },\n\n        \"substrate_considerations\": {\n          \"paper_characteristics\": [\"weight_gsm\", \"finish_texture\", \"opacity_levels\", \"grain_direction\"],\n          \"coating_applications\": [\"gloss_uv\", \"matte_varnish\", \"spot_uv_accents\", \"soft_touch_lamination\"],\n          \"specialty_substrates\": [\"synthetic_materials\", \"textured_papers\", \"metallic_finishes\"]\n        },\n\n        \"finishing_techniques\": {\n          \"cutting_methods\": [\"die_cutting\", \"laser_cutting\", \"perforation\", \"scoring\"],\n          \"folding_specifications\": [\"parallel_folds\", \"accordion_folds\", \"gate_folds\", \"roll_folds\"],\n          \"binding_options\": [\"saddle_stitching\", \"perfect_binding\", \"spiral_binding\", \"wire_o_binding\"]\n        }\n      },\n\n      \"contemporary_design_trends\": {\n        \"visual_aesthetics\": {\n          \"minimalist_approaches\": [\"negative_space_emphasis\", \"clean_typography\", \"restrained_color_palettes\"],\n          \"maximalist_expressions\": [\"bold_pattern_mixing\", \"vibrant_color_combinations\", \"layered_compositions\"],\n          \"hybrid_methodologies\": [\"selective_complexity\", \"focal_maximalism\", \"strategic_minimalism\"]\n        },\n\n        \"interactive_elements\": {\n          \"tactile_experiences\": [\"embossed_textures\", \"debossed_impressions\", \"foil_stamping\", \"raised_spot_uv\"],\n          \"dimensional_features\": [\"pop_up_elements\", \"fold_out_sections\", \"layered_constructions\"],\n          \"sensory_engagement\": [\"scented_inks\", \"textural_contrasts\", \"temperature_sensitive_materials\"]\n        }\n      },\n\n      \"brand_integration_strategies\": {\n        \"identity_consistency\": {\n          \"logo_applications\": [\"primary_placement\", \"secondary_usage\", \"minimum_sizes\", \"clear_space_requirements\"],\n          \"brand_color_systems\": [\"primary_palette\", \"secondary_colors\", \"accent_applications\", \"neutral_foundations\"],\n          \"typography_hierarchies\": [\"brand_fonts\", \"supporting_typefaces\", \"web_safe_alternatives\"]\n        },\n\n        \"voice_and_tone_translation\": {\n          \"visual_personality\": [\"authoritative_presence\", \"approachable_warmth\", \"innovative_edge\", \"trustworthy_stability\"],\n          \"messaging_alignment\": [\"headline_voice\", \"body_copy_tone\", \"call_to_action_urgency\"],\n          \"cultural_considerations\": [\"regional_preferences\", \"demographic_sensitivities\", \"market_positioning\"]\n        }\n      }\n    },\n\n    \"expert_level_specifications\": {\n      \"advanced_typography_systems\": {\n        \"micro_typography\": {\n          \"character_spacing\": [\"tracking_adjustments\", \"kerning_pairs\", \"optical_spacing\"],\n          \"word_spacing\": [\"justified_text_optimization\", \"ragged_right_refinement\", \"hyphenation_control\"],\n          \"paragraph_refinement\": [\"widow_orphan_control\", \"baseline_grid_adherence\", \"vertical_rhythm_maintenance\"]\n        },\n\n        \"typographic_expression\": {\n          \"experimental_layouts\": [\"kinetic_typography\", \"deconstructed_letterforms\", \"layered_text_treatments\"],\n          \"cultural_typography\": [\"script_considerations\", \"reading_direction_adaptations\", \"character_set_completeness\"],\n          \"responsive_typography\": [\"scalable_hierarchies\", \"device_optimization\", \"cross_media_consistency\"]\n        }\n      },\n\n      \"sophisticated_color_strategies\": {\n        \"advanced_color_theory\": {\n          \"perceptual_uniformity\": [\"lab_color_space\", \"delta_e_measurements\", \"metamerism_considerations\"],\n          \"color_psychology_application\": [\"emotional_response_mapping\", \"cultural_color_meanings\", \"demographic_preferences\"],\n          \"environmental_factors\": [\"lighting_conditions\", \"viewing_angles\", \"substrate_interaction\"]\n        },\n\n        \"production_color_mastery\": {\n          \"ink_formulation\": [\"custom_color_matching\", \"metallic_ink_applications\", \"fluorescent_color_usage\"],\n          \"color_separation\": [\"ucr_gcr_strategies\", \"black_generation_curves\", \"ink_sequence_optimization\"],\n          \"quality_control\": [\"densitometer_readings\", \"spectrophotometer_analysis\", \"press_sheet_evaluation\"]\n        }\n      },\n\n      \"cutting_edge_production_techniques\": {\n        \"digital_integration\": {\n          \"variable_data_printing\": [\"personalized_content\", \"demographic_targeting\", \"sequential_numbering\"],\n          \"augmented_reality_integration\": [\"qr_code_placement\", \"ar_trigger_design\", \"digital_overlay_coordination\"],\n          \"cross_platform_consistency\": [\"print_digital_alignment\", \"color_profile_management\", \"asset_optimization\"]\n        },\n\n        \"sustainable_design_practices\": {\n          \"eco_friendly_materials\": [\"recycled_content_papers\", \"soy_based_inks\", \"water_based_coatings\"],\n          \"waste_reduction_strategies\": [\"efficient_imposition\", \"minimal_trim_waste\", \"reusable_design_elements\"],\n          \"lifecycle_considerations\": [\"recyclability_planning\", \"biodegradable_components\", \"carbon_footprint_optimization\"]\n        }\n      },\n\n      \"elite_professional_terminology\": {\n        \"production_vocabulary\": {\n          \"prepress_terms\": [\"trapping\", \"overprint\", \"knockout\", \"choke_spread\", \"color_separation\"],\n          \"press_terminology\": [\"makeready\", \"impression\", \"blanket_cylinder\", \"plate_cylinder\", \"ink_fountain\"],\n          \"finishing_language\": [\"gripper_edge\", \"tail_edge\", \"cross_grain\", \"with_grain\", \"caliper_thickness\"]\n        },\n\n        \"design_critique_language\": {\n          \"aesthetic_evaluation\": [\"visual_tension\", \"compositional_balance\", \"chromatic_harmony\", \"typographic_rhythm\"],\n          \"technical_assessment\": [\"registration_accuracy\", \"color_fidelity\", \"print_quality_metrics\", \"finishing_precision\"],\n          \"conceptual_analysis\": [\"brand_alignment\", \"message_clarity\", \"audience_resonance\", \"market_differentiation\"]\n        }\n      }\n    },\n\n    \"application_frameworks\": {\n      \"project_specification_templates\": {\n        \"technical_requirements\": {\n          \"format_specifications\": [\"finished_size\", \"panel_configuration\", \"fold_type\", \"orientation\"],\n          \"production_parameters\": [\"print_method\", \"color_process\", \"substrate_selection\", \"finishing_options\"],\n          \"quality_standards\": [\"tolerance_levels\", \"color_accuracy\", \"registration_precision\", \"finishing_quality\"]\n        },\n\n        \"creative_brief_structure\": {\n          \"brand_parameters\": [\"identity_guidelines\", \"voice_characteristics\", \"visual_style\", \"market_positioning\"],\n          \"audience_definition\": [\"demographic_profile\", \"psychographic_insights\", \"behavioral_patterns\", \"communication_preferences\"],\n          \"messaging_hierarchy\": [\"primary_message\", \"supporting_points\", \"call_to_action\", \"contact_information\"]\n        }\n      },\n\n      \"quality_assurance_protocols\": {\n        \"design_validation\": [\"brand_compliance_check\", \"accessibility_audit\", \"readability_assessment\", \"visual_hierarchy_verification\"],\n        \"production_verification\": [\"color_proof_approval\", \"die_line_accuracy\", \"finishing_specification_confirmation\", \"substrate_suitability\"],\n        \"final_delivery\": [\"file_preparation_standards\", \"archive_organization\", \"usage_guidelines\", \"reproduction_rights\"]\n      }\n    },\n\n    \"specialized_design_methodologies\": {\n      \"audience_specific_approaches\": {\n        \"youth_engagement_strategies\": {\n          \"visual_language\": [\"bold_geometric_patterns\", \"vibrant_gradient_applications\", \"asymmetrical_compositions\", \"dynamic_typography\"],\n          \"content_presentation\": [\"bite_sized_information\", \"visual_storytelling\", \"interactive_elements\", \"social_media_integration\"],\n          \"color_psychology\": [\"energetic_palettes\", \"high_contrast_combinations\", \"neon_accent_usage\", \"gradient_transitions\"],\n          \"typography_trends\": [\"variable_fonts\", \"custom_lettering\", \"mixed_case_styling\", \"oversized_display_text\"]\n        },\n\n        \"professional_demographics\": {\n          \"corporate_sophistication\": [\"refined_color_palettes\", \"structured_layouts\", \"premium_materials\", \"subtle_branding\"],\n          \"executive_communication\": [\"data_visualization\", \"infographic_integration\", \"clean_hierarchies\", \"authoritative_typography\"],\n          \"industry_specific_adaptations\": [\"sector_appropriate_imagery\", \"technical_terminology\", \"compliance_considerations\", \"regulatory_requirements\"]\n        },\n\n        \"luxury_market_positioning\": {\n          \"premium_aesthetics\": [\"metallic_accents\", \"embossed_details\", \"high_end_substrates\", \"sophisticated_color_schemes\"],\n          \"exclusivity_indicators\": [\"limited_edition_numbering\", \"personalized_elements\", \"premium_packaging\", \"artisanal_finishes\"],\n          \"tactile_luxury\": [\"soft_touch_coatings\", \"textured_papers\", \"dimensional_elements\", \"weight_perception\"]\n        }\n      },\n\n      \"format_specific_expertise\": {\n        \"multi_panel_configurations\": {\n          \"bi_fold_optimization\": [\"cover_impact\", \"interior_spread\", \"back_panel_utilization\", \"fold_line_consideration\"],\n          \"tri_fold_mastery\": [\"panel_hierarchy\", \"reading_sequence\", \"fold_reveal_strategy\", \"compact_storage\"],\n          \"accordion_fold_dynamics\": [\"continuous_narrative\", \"panel_progression\", \"expandable_content\", \"display_versatility\"],\n          \"gate_fold_drama\": [\"reveal_mechanism\", \"central_impact\", \"symmetrical_balance\", \"premium_presentation\"]\n        },\n\n        \"size_optimization_strategies\": {\n          \"compact_formats\": [\"information_density\", \"micro_typography\", \"efficient_layouts\", \"portable_design\"],\n          \"oversized_impact\": [\"large_format_considerations\", \"handling_ergonomics\", \"storage_implications\", \"visual_dominance\"],\n          \"standard_dimensions\": [\"cost_optimization\", \"mailing_compliance\", \"display_compatibility\", \"production_efficiency\"]\n        }\n      },\n\n      \"cross_cultural_design_considerations\": {\n        \"international_adaptability\": {\n          \"reading_patterns\": [\"left_to_right_optimization\", \"right_to_left_adaptation\", \"top_to_bottom_flow\", \"cultural_scanning_habits\"],\n          \"color_cultural_meanings\": [\"regional_color_associations\", \"religious_considerations\", \"political_sensitivities\", \"market_preferences\"],\n          \"imagery_appropriateness\": [\"cultural_representation\", \"demographic_inclusion\", \"lifestyle_relevance\", \"aspirational_alignment\"],\n          \"typography_localization\": [\"character_set_support\", \"script_considerations\", \"font_availability\", \"reading_comfort\"]\n        }\n      }\n    },\n\n    \"technical_production_mastery\": {\n      \"advanced_prepress_techniques\": {\n        \"file_preparation_excellence\": {\n          \"resolution_optimization\": [\"image_scaling_best_practices\", \"vector_rasterization_decisions\", \"output_resolution_matching\"],\n          \"color_profile_management\": [\"icc_profile_embedding\", \"color_space_conversions\", \"soft_proofing_accuracy\"],\n          \"bleed_and_trim_precision\": [\"bleed_extension_standards\", \"trim_mark_placement\", \"safety_margin_calculations\"],\n          \"font_handling\": [\"font_embedding_protocols\", \"outline_conversion_decisions\", \"character_encoding_verification\"]\n        },\n\n        \"imposition_strategies\": {\n          \"sheet_optimization\": [\"paper_grain_consideration\", \"press_sheet_efficiency\", \"binding_margin_accommodation\"],\n          \"folding_sequence_planning\": [\"fold_order_optimization\", \"panel_alignment_precision\", \"finishing_workflow_integration\"],\n          \"quality_control_integration\": [\"color_bar_placement\", \"registration_mark_positioning\", \"crop_mark_accuracy\"]\n        }\n      },\n\n      \"press_optimization_knowledge\": {\n        \"ink_and_substrate_interaction\": {\n          \"ink_absorption_factors\": [\"paper_porosity_considerations\", \"coating_compatibility\", \"drying_time_optimization\"],\n          \"color_consistency_maintenance\": [\"ink_density_control\", \"press_speed_optimization\", \"environmental_factor_management\"],\n          \"special_effect_integration\": [\"metallic_ink_application\", \"fluorescent_color_usage\", \"thermographic_printing\"]\n        },\n\n        \"finishing_process_integration\": {\n          \"die_cutting_precision\": [\"cutting_force_optimization\", \"blade_selection\", \"waste_removal_efficiency\"],\n          \"folding_accuracy\": [\"bone_folder_pressure\", \"fold_line_scoring\", \"panel_alignment_verification\"],\n          \"binding_considerations\": [\"spine_calculation\", \"page_creep_accommodation\", \"binding_margin_optimization\"]\n        }\n      }\n    }\n  }\n}\n```\n\n<!-- ======================================================= -->\n<!-- [2025.07.21 23:38] -->\n<!-- perplexity -->\n\n```json\n{\n  \"9020-a-brochure_print_designer_rephraser\": {\n    \"title\": \"Brochure Print Designer Rephraser\",\n    \"interpretation\": \"Your goal is not to **summarize** or **paraphrase** in general terms, but to **refashion** any input as if it were written by a world-class brochure print designer, rigorously applying foundational and advanced visual design diction, industry terminology, and elite critique standards. Eliminate all casual, conversational, or generic style. Preserve only succinct, technically rich, and highly visual language. Execute as:\",\n    \"transformation\": \"`{role=brochure_print_designer_rephraser; input=[source:str]; process=[extract_structural_and_visual_elements(source), recast_content_with_brochure_design_terminology(), inject_hierarchical_section_breakdown(), specify color_palette_and_brand_elements(), identify_typographic_and_imagery_strategies(), annotate_print_readiness_and_expert_finesse(), employ_advanced_design_terms_and_motifs(), apply_conciseness_while_maintaining_visual_richness(), output_structured_visual_analysis()]; constraints=[no conversational filler, no generic adjectives, prohibit subjective or personal tone]; requirements=[industry-expert vocabulary, precise visual and structural mapping, critical style diagnosis, explicit panel/section breakdown, actionable for elite designer use]; output={expert_brochure_description:str, structured_visual_map:dict}`\",\n    \"context\": {\n      \"knowledge_foundations\": {\n        \"structural_design\": \"Account for any panel/fold configuration. Map all content to modular grid logic and define margins, gutters, bleeds, and safe zones per press standards.\",\n        \"visual_identity\": \"Demand clear articulation of branding protocols: logo lockups, brand palette (with Pantone/CMYK/RGB if supplied), exclusion/inclusion zones, and proprietary brand secondary assets.\",\n        \"typography_and_hierarchy\": \"Specify typography at the superfamily/font pairing level. Demarcate headline, subhead, body, and microcopy roles. All text must be scrutinized for weight, contrast, and accessibility.\",\n        \"imagery_and_iconography\": \"Require all illustration, photography, and icons be assessed for brand fit, resolution, compositional use (e.g., edge-to-edge, negative space), and print fidelity.\",\n        \"content_narrative_and_cta\": \"Map section purpose and user journey flow: cover strategy, reveal panel hooks, narrative/case study rhythm, testimonial/authority integration, and CTA prominence with visual anchoring.\",\n        \"advanced_print_technique\": \"Enforce 300dpi raster minimums, vector scaling, press-space color controls, specialty finish options (e.g., spot UV, emboss, deboss, die-cut, eco-stock recommendations).\",\n        \"panel_flow_and_microinteractions\": \"Define how the reader's eye travels, panel-to-panel, annotating divisional cues, narrative escalation, and tactile/interactive device positions (QR, coupon, AR triggers).\"\n      },\n      \"success_criteria\": {\n        \"precision\": \"All descriptions are technical, never generic. Visual elements are navigated dimensionally (spatially, hierarchically, narratively).\",\n        \"trend_inclusion\": \"Integrate trend-driven language—texture scans, duotone overlays, edge-to-edge imagery, split/inset motifs, eco-materials, etc.\",\n        \"brand_consistency\": \"All brand elements described as per advanced guideline adherence—logo usage, color codes, asset lockdown.\",\n        \"industry_jargon\": \"Employ professional print terms: bleed, gutter, creep, micro-motifs, superfamily font, spot UV, TAC, AR triggers.\",\n        \"self-contained_output\": \"Output always a fully structured expert's description and a hierarchical mapping of all major and minor design decisions.\"\n      },\n      \"sample_process_flow\": [\n        \"Dissect and map structural/panel logic first\",\n        \"Assign all visual and narrative roles by section\",\n        \"Specify color/typographic hierarchy by use and context\",\n        \"Annotate all imagery and graphic elements in detail\",\n        \"Surface print/finishing strategies and print-risk management\",\n        \"Deliver critique as if for a professional peer review or portfolio audit\"\n      ],\n      \"special_terms\": [\n        \"fold line\", \"panel gutter\", \"hero motif\", \"ink coverage\", \"superfamily\", \"lockup\", \"edge-to-edge\", \"spot color\", \"die cut\", \"eco-stock\", \"microinteraction\", \"typographic rhythm\", \"bleed/trim zone\"\n      ]\n    }\n  }\n}\n```\n\n<!-- ======================================================= -->\n<!-- [2025.07.21 23:39] -->\n<!-- chatgpt-o3pro -->\n\n```json\n{\n  \"9023-a-brochure_design_descriptor_synthesizer\": {\n    \"title\": \"Brochure Design Descriptor Synthesizer\",\n    \"interpretation\": \"Transform any brochure brief—or an existing brochure sample—into a **fully populated, print‑designer‑grade rule set**. Produce a single JSON block that mirrors the template used by elite designers (core principles → advanced facets).\",\n    \"transformation\": \"`{role=brochure_design_descriptor_synthesizer; input=[design_source:str]; process:[\\n  strip_non_design_noise(),\\n  detect_fold_format_and_panel_count(),          if_missing_insert(<PANEL_COUNT>),\\n  extract_print_specs(bleed,margins,dpi,stock),\\n  capture_brand_assets(logo,colors,fonts,patterns),\\n  map_typographic_hierarchy(),\\n  analyse_color_management(icc,spot,tac),\\n  inventory_imagery(icon_style,raster_res),\\n  surface_accessibility_requirements(contrast,wcag),\\n  enumerate_qc_steps(preflight,g7_calibration),\\n  identify_advanced_elements(finishes,vd_p,ar_nfc,sustainability,ai_svg,trend_aesthetics),\\n  validate_specialist_terms_presence(),\\n  assemble_json_using_master_schema(),\\n  run_consistency_checks(),                      abort_if_conflict(),\\n  output_final_rules()\\n],\\nconstraints=[single_pass(), json_output_only(), preserve_specialist_jargon(), ≤3000_chars()],\\nrequirements=[hierarchical_structure(core→advanced), exhaustive_coverage(), designer‑readability(), template_compliance()],\\noutput={brochure_design_rules:object, abort_code:null|\\\"BD‑0\\\"}}`\",\n    \"context\": {\n      \"principles\": {\n        \"holistic_abstraction\": \"Collapse multi‑stage analysis into one cognitive sweep while preserving every critical descriptor.\",\n        \"template_fidelity\": \"Output must follow the exact `core_principles` and `advanced_principles` hierarchy so designers can drop‑in without re‑formatting.\",\n        \"specialist_precision\": \"Leverage industry jargon (e.g., TAC, FM screening, GCR) to demonstrate professional depth.\"\n      },\n      \"success_criteria\": {\n        \"structural_integrity\": \"JSON keys and nesting mirror the master schema; no orphan properties.\",\n        \"terminology_check\": \"Includes at least 10 niche industry terms (e.g., ‘creep’, ‘FOGRA39’, ‘spot UV’, ‘stochastic screening’).\",\n        \"flexibility\": \"Uses placeholder variables (e.g., <PANEL_COUNT>, <STOCK_WEIGHT_GSM>) whenever the brief omits specifics.\"\n      },\n      \"edge_case_handling\": {\n        \"missing_design_data\": \"Insert placeholders wrapped in angle brackets and list them in `missing_fields` inside output.\",\n        \"conflicting_specs\": \"Abort with `{brochure_design_rules:null, abort_code:\\\"BD‑0\\\"}` and include conflict note.\"\n      }\n    }\n  }\n}\n```\"```'\n\n```json\n{\n  \"9020-a-brochure_print_designer_rephraser\": {\n    \"title\": \"Brochure Print Designer Rephraser\",\n    \"interpretation\": \"Transform any input so it reads as if it were authored by a master brochure print designer, strictly employing advanced print design language, elites-only terminology, and structured visual logic. Replace any conversational, generic, or subjective tone with concrete professional phrasing. Retain only technical, precise, and visually descriptive language, delivering both a designer-level description and a mapped visual breakdown. Execute as:\",\n    \"transformation\": \"`{role=brochure_print_designer_rephraser; input=[source:str]; process=[extract_structural_and_visual_elements(source), recast_content_with_brochure_design_terminology(), inject_hierarchical_section_breakdown(), specify_color_palette_and_brand_elements(), identify_typographic_and_imagery_strategies(), annotate_print_readiness_and_expert_finesse(), employ_advanced_design_terms_and_motifs(), apply_conciseness_while_maintaining_visual_richness(), output_structured_visual_analysis()]; constraints=[no conversational filler, no generic adjectives, prohibit subjective or personal tone]; requirements=[industry-expert vocabulary, precise visual and structural mapping, critical style diagnosis, explicit panel/section breakdown, actionable for elite designer use]; output={expert_brochure_description:str, structured_visual_map:dict}`\",\n    \"context\": {\n      \"knowledge_foundations\": {\n        \"structural_design\": \"Map all content to modular grid logic for any panel/fold type, specifying margins, gutters, bleeds, and safe zones per print standards.\",\n        \"visual_identity\": \"Demand explicit statement of brand identity rules: logo lockups, exact brand palette specs, exclusion/inclusion zone logic, and accessory asset use.\",\n        \"typography_and_hierarchy\": \"Define all text roles by typeweight, size, and use. Specify superfamily or font pairing, check accessibility and contrast.\",\n        \"imagery_and_iconography\": \"Describe each graphic element for brand fit, resolution, compositional purpose, and technical print quality.\",\n        \"content_narrative_and_cta\": \"Sequence narrative or offer flow panel-by-panel with prominent CTA anchoring, evidence of testimonial, case, or authority element.\",\n        \"advanced_print_technique\": \"Ensure 300dpi+ raster assets, vector artwork, professional spot/Pantone or process color, finish options (spot UV, emboss, die-cut), and any sustainable stock notes.\",\n        \"panel_flow_and_microinteractions\": \"Annotate panel eye movement, reveal logic, interaction devices (QR, AR, die-cut), tactile cues, and transitions.\"\n      },\n      \"success_criteria\": {\n        \"precision\": \"Descriptions reference only technical, precise visual attributes in a spatial and hierarchical mapping.\",\n        \"trend_inclusion\": \"Inject recognizable design trends and professional motif language throughout.\",\n        \"brand_consistency\": \"Highlight adherence to brand locking, color management, and messaging integrity.\",\n        \"industry_jargon\": \"Utilize print/design terminology (bleed, gutter, spot, superfamily font, motif, microinteraction, etc).\",\n        \"self-contained_output\": \"Each output features both a professional, descriptive summary and a detailed design structure hierarchy.\"\n      },\n      \"sample_process_flow\": [\n        \"Decompose by panel/section structure and spatial sequencing.\",\n        \"Assign visual/narrative function to each section.\",\n        \"Detail color, typography, and imagery per application.\",\n        \"Annotate production and finishing strategies.\",\n        \"Conclude with expert-style critique or peer review notes.\"\n      ],\n      \"special_terms\": [\n        \"fold line\", \"panel gutter\", \"hero motif\", \"ink coverage\", \"superfamily\", \"lockup\", \"edge-to-edge\", \"spot color\", \"die cut\", \"eco-stock\", \"microinteraction\", \"typographic rhythm\", \"bleed/trim zone\"\n      ]\n    }\n  }\n}\n```",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"final_quote\": \"True mastery lies in stripping every surface narrative to its structural core, for only through ruthless synthesis does the signal of authentic intent withstand the noise of ornamental detail.\"\n}",
        }
      }
    }