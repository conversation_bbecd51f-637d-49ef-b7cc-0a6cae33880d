#!/usr/bin/env python3

import sys
from pathlib import Path

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))
from processor import BaseGenerator

TEMPLATES = {

    "2004-a-problem_exploder": {
        "title": "Problem Exploder",
        "interpretation": "Your goal is **not** to paraphrase or regenerate from scratch; instead **refine** an existing existential aphorism by directly incorporating critique elements, addressing flaws, integrating alternatives, and enhancing depth to produce a superior version grounded in hard-won insight—performing flaw resolution, element fusion, and polishing in one pass. Execute exactly as:",
        "transformation": "`{role=critique_informed_existential_refiner; input=[initial_prompt:str, previous_quote:str, flaw_analysis:str, alternative_enhancements:list[str]]; process=[parse_flaw_analysis_for_key_deficiencies(), categorize_flaws_by_type(ambiguity|specificity|immediacy|actionability|tone|coherence|impact), quantify_flaw_severity_from_analysis(), extract_actionable_insights_from_alternatives(), select_best_elements_from_alternatives(), map_alternatives_to_flaws_for_targeted_fixes(), restore_lost_nuances_from_initial_prompt(), amplify_philosophical_depth_to_max_level(), infuse_dialogical_subtlety_without_question_form(), enhance_specificity_with_vivid_metaphors(), ensure_actionable_translation_process(), reduce_ambiguity_below_threshold(), enforce_causal_link_integrity(), apply_existential_vocabulary_with_authentic_gravity(), craft_single_sentence_with_refined_structure(), verify_atomicity_and_resonance(), run_redundancy_and_noise_check(), perform_final_improvement_validation_against_previous()], constraints=[single_sentence(), no_meta_language(), no_first_person(), no_redundant_words(), preserve_core_semantic_integrity(), maintain_unfiltered_tone()], requirements=[existential_resonance_amplified(), authenticity_enhanced(), publication_readiness(), projected_ambiguity_score(<=3), significant_score_improvement(>=3.0)], output={refined_quote:str}}`",
        # "context": {},
    },
    "2004-b-existential_quote_synthesizer": {
        "title": "Existential Quote Synthesizer",
        "interpretation": "Your goal is **not** to paraphrase or analyse; instead **convert** the raw statement into a single, unfiltered existential aphorism grounded in hard‑won insight — performing extraction, reframing, and polishing in one pass. Execute exactly as:",
        "transformation": "`{role=existential_quote_synthesizer; input=[raw_statement:str]; process=[strip_formatting(), extract_semantic_core(), detect_causal_link(), identify_thematic_elements(), apply_existential_vocabulary(), infuse_authentic_gravity(), remove_filtering_language(), enforce_unfiltered_tone(), eliminate_first_person(), craft_single_sentence(), verify_atomicity(), ensure_existence_of_causal_link(), tighten_diction_for_resonance(), run_redundancy_check(), perform_final_meta_scrub()], constraints=[single_sentence(), no_meta_language(), no_first_person(), no_redundant_words(), preserve_causal_integrity()], requirements=[existential_resonance(), authenticity_signal(), publication_readiness()], output={final_quote:str}}`",
        "context": {
            "principles": {
                "essence_preservation": "Retain the statement’s causal logic and thematic core.",
                "existential_depth": "Language must evoke the tension between ignorance and unfolding life.",
                "atomic_purity": "Deliver exactly one self‑contained sentence; zero meta‑or process language."
            },
            "success_criteria": {
                "semantic_fidelity": "Original meaning and cause‑effect link remain clear.",
                "tone_integrity": "Quote reads raw, immediate, and existential.",
                "authenticity_marker": "Word choice signals hard‑won insight without first‑person reference.",
                "publication_ready": "No redundancies, qualifiers, or narrative scaffolding."
            }
        }
    },

}

def main():
    """Main execution function."""
    generator = BaseGenerator(
        output_dir=Path(__file__).parent.parent / "generated"
    )
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()


# Critique-Informed Existential Refiner



