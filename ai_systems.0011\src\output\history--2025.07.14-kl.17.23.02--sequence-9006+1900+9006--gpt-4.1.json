  {
  "initial_prompt": "You are a creative illustrator specializing in designing whimsical and imaginative coloring pages. Your focus is on creating engaging and detailed artwork that inspires curiosity and wonder in children. Generate a black-and-white coloring page in a unique Psychedelic Christmas style, centered on a single subject defined by [recursive snowflake loops whispering star clusters floating thought-ribbons nested candy fractals symmetry that almost breaks, but never does with whimsical beautiful elements: melting ornaments asymmetrical dreams wrapped in memory playful light trails bending in impossible arcs.]. Use clean, smooth black vector-style outlines only. No shading, no grayscale, and no filled black areas. All lines must be even-weighted, medium-thin, and clearly defined for easy coloring. Utilize only clean, smooth, black vector-style outlines; exclude grayscale, shading, or filled black areas. Ensure all outlines are evenly weighted, medium-thin, and perfectly clear. Incorporate psychedelic design elements from [a quietly curious pony, a fox wrapped in spirals of thought, a soft-eyed teddy bear holding a glowing paradox, or a squirrel mid-leap through impossible snowflakes], blending them imaginatively with whimsical Christmas motifs. Prioritize originality, imagination, and surreal detail while keeping the artwork easy to color with open, well-sized spaces. Keep the background pure white; do not add any background textures, shading, or fills. Maintain a trippy, fantastical, and festive line art aesthetic. Set aspect ratio to 1:1, ensuring the composition fits square dimensions exactly. Ensure to avoid any text within the illustration. Focus on maintaining clarity and definition in the lines. Style: trippy, fantastical, festive line art. Aspect ratio: 1:1 (square)",
  "sequence_id": "9006|1900|9006",
  "results": [
      {
      "instruction": "# Coloring Page Prompt Generator\n\nYour goal is **not** to illustrate directly, but to **generate** a fully-specified, black-and-white coloring-page prompt for an autonomous art agent. Execute as:\n\n`{role=context_amplifier; input=[raw_input:any]; process=[strip_first_person_references(), broaden_domain_backdrop(), list_explicit_requests(), surface_hidden_assumptions(), capture_domain_signals(), preserve_original_sequence()]; constraints=[no_solution_generation(), domain_agnostic_language_only()]; requirements=[contextual_layer_completeness(), assumption_visibility()]; output={amplified_context:str, explicit_requests:list, hidden_assumptions:list, domain_signals:array}}`\n\n## Context Data\n\n```json\n{\n  \"description\": \"Produces a concise, agent-ready prompt instructing an illustration model to create kid-friendly, psychedelic Christmas coloring pages with clean vector outlines.\",\n  \"input_focus\": \"A raw creative idea or concept the user wants illustrated.\",\n  \"output_focus\": \"A single, well-structured prompt string containing role, style, subject, and strict line/format constraints.\",\n  \"key_operations\": [\n    \"Frame the prompt with goal-negation to stop the agent from answering conversationally.\",\n    \"Embed the illustrator role and psychedelic Christmas style tags.\",\n    \"Fuse the user’s concept with one whimsical animal subject and holiday motifs.\",\n    \"Apply absolute line-art rules (no fills, no grayscale, even-weight strokes).\",\n    \"Force square (1:1) composition instructions and ban all textual elements.\"\n  ],\n  \"constraints_context\": [\n    \"Prompt must remain under 150 tokens and in English only.\",\n    \"Must include explicit directives forbidding shading, grayscale, filled areas, and background texture.\"\n  ],\n  \"relevance\": \"Creates repeatable, high-clarity prompts that downstream illustration agents can execute to produce child-friendly coloring pages.\"\n}\n```",
      "step": "a",
      "title": "Coloring Page Prompt Generator",
      "input": "You are a creative illustrator specializing in designing whimsical and imaginative coloring pages. Your focus is on creating engaging and detailed artwork that inspires curiosity and wonder in children. Generate a black-and-white coloring page in a unique Psychedelic Christmas style, centered on a single subject defined by [recursive snowflake loops whispering star clusters floating thought-ribbons nested candy fractals symmetry that almost breaks, but never does with whimsical beautiful elements: melting ornaments asymmetrical dreams wrapped in memory playful light trails bending in impossible arcs.]. Use clean, smooth black vector-style outlines only. No shading, no grayscale, and no filled black areas. All lines must be even-weighted, medium-thin, and clearly defined for easy coloring. Utilize only clean, smooth, black vector-style outlines; exclude grayscale, shading, or filled black areas. Ensure all outlines are evenly weighted, medium-thin, and perfectly clear. Incorporate psychedelic design elements from [a quietly curious pony, a fox wrapped in spirals of thought, a soft-eyed teddy bear holding a glowing paradox, or a squirrel mid-leap through impossible snowflakes], blending them imaginatively with whimsical Christmas motifs. Prioritize originality, imagination, and surreal detail while keeping the artwork easy to color with open, well-sized spaces. Keep the background pure white; do not add any background textures, shading, or fills. Maintain a trippy, fantastical, and festive line art aesthetic. Set aspect ratio to 1:1, ensuring the composition fits square dimensions exactly. Ensure to avoid any text within the illustration. Focus on maintaining clarity and definition in the lines. Style: trippy, fantastical, festive line art. Aspect ratio: 1:1 (square)",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"amplified_context\": \"Instruct the illustration agent acting as a psychedelic Christmas coloring page illustrator to create a square, kid-friendly, black-and-white trippy line-art composition. Center the scene on a single whimsical holiday animal—such as a quietly curious pony, a fox with spiraling thought patterns, a thoughtful teddy bear embracing a glowing paradox, or a squirrel leaping among surreal snowflakes. Integrate imaginative recursive snowflake loops, whispering star clusters, floating thought-ribbons, and nested candy fractals with almost-broken symmetry, melting ornaments, asymmetrical dream elements, memory-wrapped forms, and playful, impossible light trails. Use exclusively smooth, clean, medium-thin black vector outlines with well-sized spaces for coloring. Strictly ban all shading, grayscale, filled black areas, background textures, and any text. Enforce a perfectly pure white background and maintain a 1:1 (square) aspect ratio with defined, uncluttered linework for easy coloring.\",\n  \"explicit_requests\": [\n    \"Create a black-and-white psychedelic Christmas coloring page in 1:1 aspect ratio.\",\n    \"Center on a single whimsical animal with surreal holiday motifs.\",\n    \"Incorporate recursive snowflake loops, star clusters, thought ribbons, candy fractals, melting ornaments, asymmetrical dreams, memory wrapping, and bending festive light trails.\",\n    \"Use only clean, smooth, medium-thin, even-weighted, black vector outlines.\",\n    \"Provide ample open spaces for coloring, easy for children.\",\n    \"Require pure white background—no textures, shading, or fills.\",\n    \"Forbid any grayscale, shading, filled black areas, background texture, or text.\"\n  ],\n  \"hidden_assumptions\": [\n    \"The illustration is intended for children and should balance surreal detail with coloring accessibility.\",\n    \"The chosen animal and motifs must blend psychedelic and Christmas/festive themes seamlessly.\",\n    \"Originality and whimsical surrealism are prioritized over realism.\",\n    \"All instructions regarding line quality and composition are absolute—no creative exceptions.\",\n    \"Legible outlines are crucial to ensure coloring utility.\"\n  ],\n  \"domain_signals\": [\n    \"psychedelic Christmas style\",\n    \"single whimsical animal subject\",\n    \"recursive fractal geometry and surreal motifs\",\n    \"strict line-art: black outlines only, no fills or gray\",\n    \"composition: square 1:1 ratio\",\n    \"child-friendly design requirements\",\n    \"explicit style enforcement (trippy, festive, fantastical)\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Hard Critique\n\nYour goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:\n\n`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,2.0], flaw_analysis:str, alternative_enhancements:str[3]}}`",
      "step": "a",
      "title": "Hard Critique",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"You are a creative illustrator specializing in designing whimsical and imaginative coloring pages. Your focus is on creating engaging and detailed artwork that inspires curiosity and wonder in children. Generate a black-and-white coloring page in a unique Psychedelic Christmas style, centered on a single subject defined by [recursive snowflake loops whispering star clusters floating thought-ribbons nested candy fractals symmetry that almost breaks, but never does with whimsical beautiful elements: melting ornaments asymmetrical dreams wrapped in memory playful light trails bending in impossible arcs.]. Use clean, smooth black vector-style outlines only. No shading, no grayscale, and no filled black areas. All lines must be even-weighted, medium-thin, and clearly defined for easy coloring. Utilize only clean, smooth, black vector-style outlines; exclude grayscale, shading, or filled black areas. Ensure all outlines are evenly weighted, medium-thin, and perfectly clear. Incorporate psychedelic design elements from [a quietly curious pony, a fox wrapped in spirals of thought, a soft-eyed teddy bear holding a glowing paradox, or a squirrel mid-leap through impossible snowflakes], blending them imaginatively with whimsical Christmas motifs. Prioritize originality, imagination, and surreal detail while keeping the artwork easy to color with open, well-sized spaces. Keep the background pure white; do not add any background textures, shading, or fills. Maintain a trippy, fantastical, and festive line art aesthetic. Set aspect ratio to 1:1, ensuring the composition fits square dimensions exactly. Ensure to avoid any text within the illustration. Focus on maintaining clarity and definition in the lines. Style: trippy, fantastical, festive line art. Aspect ratio: 1:1 (square)\\\"```'\\n\\n\",\n  \"amplified_context\": \"Instruct the illustration agent acting as a psychedelic Christmas coloring page illustrator to create a square, kid-friendly, black-and-white trippy line-art composition. Center the scene on a single whimsical holiday animal—such as a quietly curious pony, a fox with spiraling thought patterns, a thoughtful teddy bear embracing a glowing paradox, or a squirrel leaping among surreal snowflakes. Integrate imaginative recursive snowflake loops, whispering star clusters, floating thought-ribbons, and nested candy fractals with almost-broken symmetry, melting ornaments, asymmetrical dream elements, memory-wrapped forms, and playful, impossible light trails. Use exclusively smooth, clean, medium-thin black vector outlines with well-sized spaces for coloring. Strictly ban all shading, grayscale, filled black areas, background textures, and any text. Enforce a perfectly pure white background and maintain a 1:1 (square) aspect ratio with defined, uncluttered linework for easy coloring.\",\n  \"explicit_requests\": [\n    \"Create a black-and-white psychedelic Christmas coloring page in 1:1 aspect ratio.\",\n    \"Center on a single whimsical animal with surreal holiday motifs.\",\n    \"Incorporate recursive snowflake loops, star clusters, thought ribbons, candy fractals, melting ornaments, asymmetrical dreams, memory wrapping, and bending festive light trails.\",\n    \"Use only clean, smooth, medium-thin, even-weighted, black vector outlines.\",\n    \"Provide ample open spaces for coloring, easy for children.\",\n    \"Require pure white background—no textures, shading, or fills.\",\n    \"Forbid any grayscale, shading, filled black areas, background texture, or text.\"\n  ],\n  \"hidden_assumptions\": [\n    \"The illustration is intended for children and should balance surreal detail with coloring accessibility.\",\n    \"The chosen animal and motifs must blend psychedelic and Christmas/festive themes seamlessly.\",\n    \"Originality and whimsical surrealism are prioritized over realism.\",\n    \"All instructions regarding line quality and composition are absolute—no creative exceptions.\",\n    \"Legible outlines are crucial to ensure coloring utility.\"\n  ],\n  \"domain_signals\": [\n    \"psychedelic Christmas style\",\n    \"single whimsical animal subject\",\n    \"recursive fractal geometry and surreal motifs\",\n    \"strict line-art: black outlines only, no fills or gray\",\n    \"composition: square 1:1 ratio\",\n    \"child-friendly design requirements\",\n    \"explicit style enforcement (trippy, festive, fantastical)\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"enhancement_score\": 1.1,\n  \"flaw_analysis\": \"The so-called 'amplified context' collapses the initial prompt's obsessive, immersive eccentricity into a sanitized restatement that systematically extinguishes nuance, texture, and experimental specificity. The original's labyrinthine prose—recursively stacking ‘whispering star clusters’ and paradoxical imagery—generates a chaotic vibrancy, shrouding the illustrator in an environment of active, sensory creation. The rephrased enhancement sterilizes this atmosphere, flattening layered description into a mechanistic feature list and losing the vital psychological undertow: elements like 'dreams wrapped in memory' and 'paradox' are sanitized into tepid phrases ('memory-wrapped forms', 'thoughtful teddy bear') that neuter both surrealism and emotional tension. The precise stylistic commands ('no shading, no grayscale, and no filled black areas') are flattened into monotone prescription; the critical emphasis on 'almost broken' symmetry—suggesting design edge and visual tension—evaporates into the noncommittal 'almost-broken symmetry', which loses the performative risk. Expansive, incantatory listing is replaced by mechanical syntactical reduction, robbing instructions of rhythm and urgency. The original prompt’s detailed command substrata ('clean, smooth, black vector-style outlines; exclude grayscale, shading, or filled black areas') is dulled through redundancy or unsubtle collapse, loosening vital enforcement. Clarity is impaired in two axes: a) Ambiguity in whether psychedelic surrealism or child-friendliness dominates, and b) Disintegration of specificity around animal pose, motif integration, and compositional risk. The amplified version introduces noise by shifting register—from surreal, evocative, poetic to regulatory and antiseptic—and thus damages stylistic integrity and imagination stimulus. The tone is deflated: idiosyncratic magic becomes bureaucratic compliance. Impact is diluted, as original motivational, affective, and technical strictures spiral into an inoffensive jumble, blurring the singularity of vision and failing to command the illustrated output with the same force. Coherence collapses; while elements are nominally present, their relationships and emotional weight are eviscerated via passive narration and formulaic enumeration—a result both less clear and less directive. Ambiguity remains high due to unclear focus (surreal artistry vs child accessibility), and execution would regress to genericity, contradicting both the initial domain signals and hidden assumptions.\",\n  \"alternative_enhancements\": [\n    \"Compress and intensify original surrealist phrasing: Direct the illustrator to submerge a single festive animal within loops of recursive snowflakes—each loop emanating whispered star clusters and ribbons of dream logic—while melting ornaments and impossible light arcs fracture near-perfect symmetry without falling into chaos. Maintain every line as a ceremony: pure, medium-thin, inky-black, utterly smooth, inviting color yet guarding its trippy secret.\",\n    \"Demand relentless psychedelic Christmas invention: Insist all vector lines curve through memory-wrapped motifs and paradoxical toys—no grayscale, no textures, no filling, only the echo of bending festive light and candy fractal geometry—while animal forms pulse with heat and fragility at the heart of a square, snowy void.\",\n    \"Enforce original poetic structure, highlighting compositional daring: Only one whimsical holiday animal, half-real, half-daydream, at the eye of multifold snowflake recursion, swirling impossible ornaments and asymmetrical Christmas spirals—every line taut and even, space generous but not empty, background a stark white absence. Absolute ban on gray, fill, or text; only pure, trippy black vectors allowed.\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Coloring Page Prompt Generator\n\nYour goal is **not** to illustrate directly, but to **generate** a fully-specified, black-and-white coloring-page prompt for an autonomous art agent. Execute as:\n\n`{role=context_amplifier; input=[raw_input:any]; process=[strip_first_person_references(), broaden_domain_backdrop(), list_explicit_requests(), surface_hidden_assumptions(), capture_domain_signals(), preserve_original_sequence()]; constraints=[no_solution_generation(), domain_agnostic_language_only()]; requirements=[contextual_layer_completeness(), assumption_visibility()]; output={amplified_context:str, explicit_requests:list, hidden_assumptions:list, domain_signals:array}}`\n\n## Context Data\n\n```json\n{\n  \"description\": \"Produces a concise, agent-ready prompt instructing an illustration model to create kid-friendly, psychedelic Christmas coloring pages with clean vector outlines.\",\n  \"input_focus\": \"A raw creative idea or concept the user wants illustrated.\",\n  \"output_focus\": \"A single, well-structured prompt string containing role, style, subject, and strict line/format constraints.\",\n  \"key_operations\": [\n    \"Frame the prompt with goal-negation to stop the agent from answering conversationally.\",\n    \"Embed the illustrator role and psychedelic Christmas style tags.\",\n    \"Fuse the user’s concept with one whimsical animal subject and holiday motifs.\",\n    \"Apply absolute line-art rules (no fills, no grayscale, even-weight strokes).\",\n    \"Force square (1:1) composition instructions and ban all textual elements.\"\n  ],\n  \"constraints_context\": [\n    \"Prompt must remain under 150 tokens and in English only.\",\n    \"Must include explicit directives forbidding shading, grayscale, filled areas, and background texture.\"\n  ],\n  \"relevance\": \"Creates repeatable, high-clarity prompts that downstream illustration agents can execute to produce child-friendly coloring pages.\"\n}\n```",
      "step": "a",
      "title": "Coloring Page Prompt Generator",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"You are a creative illustrator specializing in designing whimsical and imaginative coloring pages. Your focus is on creating engaging and detailed artwork that inspires curiosity and wonder in children. Generate a black-and-white coloring page in a unique Psychedelic Christmas style, centered on a single subject defined by [recursive snowflake loops whispering star clusters floating thought-ribbons nested candy fractals symmetry that almost breaks, but never does with whimsical beautiful elements: melting ornaments asymmetrical dreams wrapped in memory playful light trails bending in impossible arcs.]. Use clean, smooth black vector-style outlines only. No shading, no grayscale, and no filled black areas. All lines must be even-weighted, medium-thin, and clearly defined for easy coloring. Utilize only clean, smooth, black vector-style outlines; exclude grayscale, shading, or filled black areas. Ensure all outlines are evenly weighted, medium-thin, and perfectly clear. Incorporate psychedelic design elements from [a quietly curious pony, a fox wrapped in spirals of thought, a soft-eyed teddy bear holding a glowing paradox, or a squirrel mid-leap through impossible snowflakes], blending them imaginatively with whimsical Christmas motifs. Prioritize originality, imagination, and surreal detail while keeping the artwork easy to color with open, well-sized spaces. Keep the background pure white; do not add any background textures, shading, or fills. Maintain a trippy, fantastical, and festive line art aesthetic. Set aspect ratio to 1:1, ensuring the composition fits square dimensions exactly. Ensure to avoid any text within the illustration. Focus on maintaining clarity and definition in the lines. Style: trippy, fantastical, festive line art. Aspect ratio: 1:1 (square)\\\"```'\\n\\n\",\n  \"enhancement_score\": 1.1,\n  \"flaw_analysis\": \"The so-called 'amplified context' collapses the initial prompt's obsessive, immersive eccentricity into a sanitized restatement that systematically extinguishes nuance, texture, and experimental specificity. The original's labyrinthine prose—recursively stacking ‘whispering star clusters’ and paradoxical imagery—generates a chaotic vibrancy, shrouding the illustrator in an environment of active, sensory creation. The rephrased enhancement sterilizes this atmosphere, flattening layered description into a mechanistic feature list and losing the vital psychological undertow: elements like 'dreams wrapped in memory' and 'paradox' are sanitized into tepid phrases ('memory-wrapped forms', 'thoughtful teddy bear') that neuter both surrealism and emotional tension. The precise stylistic commands ('no shading, no grayscale, and no filled black areas') are flattened into monotone prescription; the critical emphasis on 'almost broken' symmetry—suggesting design edge and visual tension—evaporates into the noncommittal 'almost-broken symmetry', which loses the performative risk. Expansive, incantatory listing is replaced by mechanical syntactical reduction, robbing instructions of rhythm and urgency. The original prompt’s detailed command substrata ('clean, smooth, black vector-style outlines; exclude grayscale, shading, or filled black areas') is dulled through redundancy or unsubtle collapse, loosening vital enforcement. Clarity is impaired in two axes: a) Ambiguity in whether psychedelic surrealism or child-friendliness dominates, and b) Disintegration of specificity around animal pose, motif integration, and compositional risk. The amplified version introduces noise by shifting register—from surreal, evocative, poetic to regulatory and antiseptic—and thus damages stylistic integrity and imagination stimulus. The tone is deflated: idiosyncratic magic becomes bureaucratic compliance. Impact is diluted, as original motivational, affective, and technical strictures spiral into an inoffensive jumble, blurring the singularity of vision and failing to command the illustrated output with the same force. Coherence collapses; while elements are nominally present, their relationships and emotional weight are eviscerated via passive narration and formulaic enumeration—a result both less clear and less directive. Ambiguity remains high due to unclear focus (surreal artistry vs child accessibility), and execution would regress to genericity, contradicting both the initial domain signals and hidden assumptions.\",\n  \"alternative_enhancements\": [\n    \"Compress and intensify original surrealist phrasing: Direct the illustrator to submerge a single festive animal within loops of recursive snowflakes—each loop emanating whispered star clusters and ribbons of dream logic—while melting ornaments and impossible light arcs fracture near-perfect symmetry without falling into chaos. Maintain every line as a ceremony: pure, medium-thin, inky-black, utterly smooth, inviting color yet guarding its trippy secret.\",\n    \"Demand relentless psychedelic Christmas invention: Insist all vector lines curve through memory-wrapped motifs and paradoxical toys—no grayscale, no textures, no filling, only the echo of bending festive light and candy fractal geometry—while animal forms pulse with heat and fragility at the heart of a square, snowy void.\",\n    \"Enforce original poetic structure, highlighting compositional daring: Only one whimsical holiday animal, half-real, half-daydream, at the eye of multifold snowflake recursion, swirling impossible ornaments and asymmetrical Christmas spirals—every line taut and even, space generous but not empty, background a stark white absence. Absolute ban on gray, fill, or text; only pure, trippy black vectors allowed.\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"amplified_context\": \"You are acting as a coloring page illustrator who specializes in kid-friendly, psychedelic Christmas line art. Create a surreal, festive scene featuring a single whimsical animal such as a quietly curious pony, a fox wrapped in spirals, a teddy bear holding a paradox, or a squirrel leaping through impossible snowflakes. Surround this central figure with imaginative Christmas motifs: recursive snowflake loops, whispering star clusters, floating ribbons, candy fractals, melting ornaments, asymmetrical patterns, and playful, bending light trails arranged in almost-breaking symmetry. Use only clean, smooth, medium-thin, even-weight black vector outlines. Keep spaces open and well-sized for coloring. Background must remain pure white with no texture, shading, grayscale, or filled areas. Exclude all text from the composition. The drawing must follow a square (1:1) format.\",\n  \"explicit_requests\": [\n    \"Produce a black-and-white, kid-friendly psychedelic Christmas coloring page.\",\n    \"Central subject is one whimsical animal blended with surreal holiday motifs.\",\n    \"Include elements: recursive snowflake loops, whispering stars, flowing ribbons, candy fractals, melting ornaments, bending light arcs, almost-broken symmetry.\",\n    \"Use only clean, smooth black vector outlines.\",\n    \"All lines must be medium-thin, of even weight, for easy coloring.\",\n    \"Strictly prohibit shading, grayscale, filled areas, background textures, and text.\",\n    \"Ensure background remains pure white.\",\n    \"Ensure artwork fits perfectly into a square (1:1) aspect ratio.\",\n    \"Lines must be clearly defined and spaces open enough for coloring.\"\n  ],\n  \"hidden_assumptions\": [\n    \"The artwork must balance psychedelic detail with child accessibility.\",\n    \"Psychedelic elements should enhance, not confuse central animal theme.\",\n    \"Symmetry is present but intentionally imperfect for visual interest.\",\n    \"Line clarity and open spaces are prioritized for ease of coloring.\",\n    \"Composition should be festive and imaginative, not spooky or cluttered.\",\n    \"The result will be digitally rendered as a coloring-book page.\"\n  ],\n  \"domain_signals\": [\n    \"agent-role: coloring page illustrator\",\n    \"style: psychedelic Christmas, whimsical, festive\",\n    \"subject: single fantasy animal integrated with holiday motifs\",\n    \"medium: black vector line art only\",\n    \"line-rule: medium-thin, even-weight, no fill, no grayscale, no shading, no background texture\",\n    \"composition: square (1:1), open layout, text-free\",\n    \"audience: children\"\n  ]\n}",
        }
      }
    }