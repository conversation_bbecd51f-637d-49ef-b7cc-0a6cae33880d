  {
  "initial_prompt": "i've learned that almost *regardless* what specific functionalty i want to create, someone else has already done it, therefore i'm currently in the stage where i establish a strategy to determine which alternative to use *before starting to create it myself*",
  "sequence_id": "1100|1000|1900|9005|1100|1000",
  "results": [
      {
      "instruction": "# Problem Exploder\n\nYour goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\n\n`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`",
      "step": "a",
      "title": "Problem Exploder",
      "input": "i've learned that almost *regardless* what specific functionalty i want to create, someone else has already done it, therefore i'm currently in the stage where i establish a strategy to determine which alternative to use *before starting to create it myself*",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"goal_map\": [\n    {\n      \"explicit_asks\": [\n        \"Establish a strategy to determine which existing alternative to use before creating new functionality myself.\"\n      ],\n      \"hidden_assumptions\": [\n        \"There is a need for a repeatable strategy or process.\",\n        \"Multiple existing alternatives/products/libraries typically exist for a required functionality.\",\n        \"Evaluating/choosing between these alternatives is beneficial and likely to save time/resources.\",\n        \"The person has access to information about existing alternatives.\",\n        \"The alternatives are relevant and applicable to the problem domain.\"\n      ],\n      \"sub_goals\": [\n        \"Define clear criteria for comparing existing alternatives (e.g., popularity, maintenance status, community size, documentation, compatibility, license, security, performance, etc.).\",\n        \"Develop a systematic process or workflow for searching for existing solutions.\",\n        \"Establish a methodology for evaluating and scoring alternatives according to the chosen criteria.\",\n        \"Set up a decision-making framework or checklist to pick the best fit.\",\n        \"Document or formalize this strategy for future, repeatable use.\",\n        \"Account for edge cases in which no satisfactory alternative exists.\",\n        \"Determine how to keep the strategy current as technologies and alternatives evolve.\"\n      ],\n      \"blockers\": [\n        \"Unclear or subjective criteria for what makes one alternative better than another.\",\n        \"Difficulty in discovering all relevant existing alternatives (incomplete search).\",\n        \"Time or resource constraints in evaluating each alternative thoroughly.\",\n        \"Rapidly changing technical landscape might make the strategy obsolete.\",\n        \"Personal or organizational bias towards building in-house solutions.\",\n        \"Potential incompatibilities or technical limitations with integrating third-party options.\",\n        \"Uncertainty about the long-term support or viability of choices.\"\n      ]\n    }\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`",
      "step": "a",
      "title": "Instruction Converter",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"i've learned that almost *regardless* what specific functionalty i want to create, someone else has already done it, therefore i'm currently in the stage where i establish a strategy to determine which alternative to use *before starting to create it myself*\\\"```'\\n\\n\",\n  \"goal_map\": [\n    {\n      \"explicit_asks\": [\n        \"Establish a strategy to determine which existing alternative to use before creating new functionality myself.\"\n      ],\n      \"hidden_assumptions\": [\n        \"There is a need for a repeatable strategy or process.\",\n        \"Multiple existing alternatives/products/libraries typically exist for a required functionality.\",\n        \"Evaluating/choosing between these alternatives is beneficial and likely to save time/resources.\",\n        \"The person has access to information about existing alternatives.\",\n        \"The alternatives are relevant and applicable to the problem domain.\"\n      ],\n      \"sub_goals\": [\n        \"Define clear criteria for comparing existing alternatives (e.g., popularity, maintenance status, community size, documentation, compatibility, license, security, performance, etc.).\",\n        \"Develop a systematic process or workflow for searching for existing solutions.\",\n        \"Establish a methodology for evaluating and scoring alternatives according to the chosen criteria.\",\n        \"Set up a decision-making framework or checklist to pick the best fit.\",\n        \"Document or formalize this strategy for future, repeatable use.\",\n        \"Account for edge cases in which no satisfactory alternative exists.\",\n        \"Determine how to keep the strategy current as technologies and alternatives evolve.\"\n      ],\n      \"blockers\": [\n        \"Unclear or subjective criteria for what makes one alternative better than another.\",\n        \"Difficulty in discovering all relevant existing alternatives (incomplete search).\",\n        \"Time or resource constraints in evaluating each alternative thoroughly.\",\n        \"Rapidly changing technical landscape might make the strategy obsolete.\",\n        \"Personal or organizational bias towards building in-house solutions.\",\n        \"Potential incompatibilities or technical limitations with integrating third-party options.\",\n        \"Uncertainty about the long-term support or viability of choices.\"\n      ]\n    }\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"instruction_format\": \"Establish a strategy to identify and select existing alternatives before developing new functionality. Define objective criteria for comparing alternatives, including factors such as popularity, maintenance status, community size, documentation quality, compatibility, licensing, security, and performance. Develop a systematic process or workflow for searching for and compiling a list of potential solutions. Create a methodology for evaluating and scoring alternatives based on the chosen criteria. Set up a decision-making framework or checklist to determine the best fit. Document the strategy to ensure repeatability and future use. Plan contingencies for scenarios where no satisfactory alternative is found. Implement a process for regularly reviewing and updating the strategy to accommodate evolving technologies and alternatives. Address blockers such as unclear comparison criteria, incomplete searches, resource limitations, rapid technological changes, organizational biases, integration challenges, and uncertainty about the long-term viability of choices.\"\n}",
        }
      }
    },
      {
      "instruction": "# Hard Critique\n\nYour goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:\n\n`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,2.0], flaw_analysis:str, alternative_enhancements:str[3]}}`",
      "step": "a",
      "title": "Hard Critique",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"i've learned that almost *regardless* what specific functionalty i want to create, someone else has already done it, therefore i'm currently in the stage where i establish a strategy to determine which alternative to use *before starting to create it myself*\\\"```'\\n\\n\",\n  \"instruction_format\": \"Establish a strategy to identify and select existing alternatives before developing new functionality. Define objective criteria for comparing alternatives, including factors such as popularity, maintenance status, community size, documentation quality, compatibility, licensing, security, and performance. Develop a systematic process or workflow for searching for and compiling a list of potential solutions. Create a methodology for evaluating and scoring alternatives based on the chosen criteria. Set up a decision-making framework or checklist to determine the best fit. Document the strategy to ensure repeatability and future use. Plan contingencies for scenarios where no satisfactory alternative is found. Implement a process for regularly reviewing and updating the strategy to accommodate evolving technologies and alternatives. Address blockers such as unclear comparison criteria, incomplete searches, resource limitations, rapid technological changes, organizational biases, integration challenges, and uncertainty about the long-term viability of choices.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"enhancement_score\": 1.2,\n  \"flaw_analysis\": \"The 'enhanced' version imposes excessive procedural rigidity and bureaucratic vocabulary, suffocating the original insight and adaptability. The core element of the initial prompt—a reflective recognition of the ubiquity of pre-existing solutions and the personal shift toward strategic solution evaluation—is obfuscated by exhaustive, prescriptive bulleted requirements. Every nuance of learning, experience-driven adaptation, and the personal dimension is erased, replaced by impersonal mandates. Information loss occurs regarding the subjective motivation for change; the enhanced prompt transforms emergent wisdom into mechanical compliance. Clarity is undermined by convoluted lists and unnecessary specificity, producing cognitive overload. Ambiguity persists in the meaning and prioritization of criteria—no explicit weighting or scenario-based adjustment is detailed, increasing the ambiguity score. The shift to an authoritative project management tone introduces stiff, corporate noise while diluting the introspective character of the original input. Coherence is sacrificed as the enhanced format spams disconnected requirements rather than forming an integrated, actionable strategy. The methodology for evaluation and scoring remains vague under copious jargon. Overall, the revision exemplifies verbosity masquerading as clarity, and process worship at the expense of human context.\",\n  \"alternative_enhancements\": [\n    \"Clearly articulate a personal decision-making process for identifying and evaluating existing solutions before initiating custom development, emphasizing the balance between efficiency and alignment with project needs.\",\n    \"Develop a concise set of guiding questions rooted in both objective criteria (e.g., licensing, security) and contextual priorities to streamline the selection of third-party alternatives without overcomplicating the process.\",\n    \"Encapsulate the strategy in a repeatable, lightweight framework that preserves adaptability—allowing assessment of existing options while enabling informed custom development when justified.\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Operational Mapper\n\nYour goal is not to **summarize** the instruction templates, but to **map** their precise operational sequences while identifying exact integration points that preserve full procedural rigor. Execute as:\n\n`{role=operational_mapper; input=[instruction_template_a:str, instruction_template_b:str]; process=[extract_exact_process_sequences(), map_constraint_hierarchies(), identify_requirement_dependencies(), locate_output_handoff_points(), catalog_procedural_boundaries()]; constraints=[preserve_every_operational_detail(), maintain_constraint_atomicity(), respect_procedural_boundaries()]; requirements=[complete_process_mapping(), constraint_preservation(), boundary_clarity()]; output={process_map_a:dict, process_map_b:dict, integration_points:array, boundary_conflicts:array}}`",
      "step": "a",
      "title": "Operational Mapper",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"i've learned that almost *regardless* what specific functionalty i want to create, someone else has already done it, therefore i'm currently in the stage where i establish a strategy to determine which alternative to use *before starting to create it myself*\\\"```'\\n\\n\",\n  \"enhancement_score\": 1.2,\n  \"flaw_analysis\": \"The 'enhanced' version imposes excessive procedural rigidity and bureaucratic vocabulary, suffocating the original insight and adaptability. The core element of the initial prompt—a reflective recognition of the ubiquity of pre-existing solutions and the personal shift toward strategic solution evaluation—is obfuscated by exhaustive, prescriptive bulleted requirements. Every nuance of learning, experience-driven adaptation, and the personal dimension is erased, replaced by impersonal mandates. Information loss occurs regarding the subjective motivation for change; the enhanced prompt transforms emergent wisdom into mechanical compliance. Clarity is undermined by convoluted lists and unnecessary specificity, producing cognitive overload. Ambiguity persists in the meaning and prioritization of criteria—no explicit weighting or scenario-based adjustment is detailed, increasing the ambiguity score. The shift to an authoritative project management tone introduces stiff, corporate noise while diluting the introspective character of the original input. Coherence is sacrificed as the enhanced format spams disconnected requirements rather than forming an integrated, actionable strategy. The methodology for evaluation and scoring remains vague under copious jargon. Overall, the revision exemplifies verbosity masquerading as clarity, and process worship at the expense of human context.\",\n  \"alternative_enhancements\": [\n    \"Clearly articulate a personal decision-making process for identifying and evaluating existing solutions before initiating custom development, emphasizing the balance between efficiency and alignment with project needs.\",\n    \"Develop a concise set of guiding questions rooted in both objective criteria (e.g., licensing, security) and contextual priorities to streamline the selection of third-party alternatives without overcomplicating the process.\",\n    \"Encapsulate the strategy in a repeatable, lightweight framework that preserves adaptability—allowing assessment of existing options while enabling informed custom development when justified.\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"process_map_a\": {\n    \"sequence\": [\n      \"Reflect on the ubiquity of pre-existing solutions.\",\n      \"Acknowledge learning: most functionality already exists.\",\n      \"Transition mental stance: from automatic creation toward strategic evaluation.\",\n      \"Establish personal strategy for identifying and selecting suitable alternatives.\",\n      \"Prior to any new development, engage in solution discovery phase.\",\n      \"Decide on utilizing found solution or pursuing custom implementation.\"\n    ],\n    \"constraints\": [\n      \"Self-driven process based on learned experience.\",\n      \"Flexibility in assessing alternatives.\",\n      \"No explicit procedural mandates.\",\n      \"Strategy evolves with experience and context.\"\n    ],\n    \"requirements_dependencies\": [\n      \"Existence of problem or need.\",\n      \"Ability to identify or search for current solutions.\",\n      \"Capacity for reflective self-assessment.\"\n    ],\n    \"output_handoff_points\": [\n      \"Point after alternative(s) identified (handoff to decision phase).\",\n      \"Point where final decision is made (handoff to implementation phase).\"\n    ],\n    \"procedural_boundaries\": [\n      \"Boundary: personal recognition and reflection (internal thought).\",\n      \"Boundary: transition from strategy formation to action (solution evaluation/execution).\"\n    ]\n  },\n\n  \"process_map_b\": {\n    \"sequence\": [\n      \"Impose prescriptive, step-by-step selection process for alternative solutions.\",\n      \"Define exhaustive lists of evaluation criteria (e.g., licensing, security, support, integration compatibility, performance, cost, future maintainability, scalability).\",\n      \"Mandate objective scoring or ranking of all alternatives according to the criteria list.\",\n      \"Require explicit documentation of evaluation results and selection rationale.\",\n      \"Instruct on compliance with rigid project/documentation standards.\",\n      \"Enforce documented justification for any instance in which custom development is chosen over alternatives.\"\n    ],\n    \"constraints\": [\n      \"All criteria must be explicitly defined and weighted.\",\n      \"Process must be followed without exception (no adhoc decisions).\",\n      \"Documentation is mandatory at every stage.\",\n      \"No progress beyond evaluation phase without full criteria documentation.\"\n    ],\n    \"requirements_dependencies\": [\n      \"Existence of exhaustive alternatives list.\",\n      \"Availability of resources to thoroughly assess each criterion.\",\n      \"Access to documentation and compliance tools.\",\n      \"Project context must fit rigid evaluation structure.\"\n    ],\n    \"output_handoff_points\": [\n      \"After final scoring and documentation, handoff to implementation team.\",\n      \"Documentation output passed to project oversight/review entity.\"\n    ],\n    \"procedural_boundaries\": [\n      \"Boundary: rigid entrance at process start—cannot skip steps.\",\n      \"Boundary: explicit transition from documentation to implementation.\",\n      \"Boundary: escalation to higher authority if criteria incomplete.\"\n    ]\n  },\n\n  \"integration_points\": [\n    {\n      \"from\": \"process_map_a.output_handoff_points[0]\",\n      \"to\": \"process_map_b.sequence[1]\",\n      \"note\": \"Personal identification of alternatives could serve as initial input to criteria-based evaluation list in rigid process.\"\n    },\n    {\n      \"from\": \"process_map_a.output_handoff_points[1]\",\n      \"to\": \"process_map_b.output_handoff_points[0]\",\n      \"note\": \"Decision node in personal strategy aligns with formal selection/delivery point in prescriptive process.\"\n    }\n  ],\n\n  \"boundary_conflicts\": [\n    {\n      \"description\": \"process_map_a allows for flexible, experiential action and informal assessment, while process_map_b enforces mandatory steps and documentation. Attempted integration threatens the atomicity of procedural boundaries by forcing informal reflection into a rigid evaluative mold.\",\n      \"conflicting_boundaries\": [\n        \"process_map_a.procedural_boundaries[0] vs process_map_b.procedural_boundaries[0]\",\n        \"process_map_a.constraints vs process_map_b.constraints\"\n      ]\n    },\n    {\n      \"description\": \"Transition from personal decision-making (process_map_a) to bureaucratic compliance (process_map_b) risks loss of motivation/context and potential information drop, violating process integrity.\",\n      \"conflicting_boundaries\": [\n        \"process_map_a.requirements_dependencies[2] vs process_map_b.requirements_dependencies[2]\"\n      ]\n    }\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Sequential Architect\n\nYour goal is not to **blend** the operational maps, but to **architect** a precise sequential flow that executes both complete processes while maintaining their individual constraint systems and requirement specifications. Execute as:\n\n`{role=sequential_architect; input=[process_map_a:dict, process_map_b:dict, integration_points:array, boundary_conflicts:array]; process=[design_sequential_handoff(), preserve_constraint_hierarchies(), maintain_requirement_atomicity(), establish_output_transformation_protocol(), validate_procedural_completeness()]; constraints=[zero_operational_loss(), maintain_constraint_granularity(), preserve_requirement_specificity()]; requirements=[complete_sequential_execution(), preserved_constraint_systems(), maintained_procedural_boundaries()]; output={sequential_architecture:dict, preserved_constraints:array, maintained_requirements:array}}`",
      "step": "b",
      "title": "Sequential Architect",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"i've learned that almost *regardless* what specific functionalty i want to create, someone else has already done it, therefore i'm currently in the stage where i establish a strategy to determine which alternative to use *before starting to create it myself*\\\"```'\\n\\n\",\n  \"process_map_a\": {\n    \"sequence\": [\n      \"Reflect on the ubiquity of pre-existing solutions.\",\n      \"Acknowledge learning: most functionality already exists.\",\n      \"Transition mental stance: from automatic creation toward strategic evaluation.\",\n      \"Establish personal strategy for identifying and selecting suitable alternatives.\",\n      \"Prior to any new development, engage in solution discovery phase.\",\n      \"Decide on utilizing found solution or pursuing custom implementation.\"\n    ],\n    \"constraints\": [\n      \"Self-driven process based on learned experience.\",\n      \"Flexibility in assessing alternatives.\",\n      \"No explicit procedural mandates.\",\n      \"Strategy evolves with experience and context.\"\n    ],\n    \"requirements_dependencies\": [\n      \"Existence of problem or need.\",\n      \"Ability to identify or search for current solutions.\",\n      \"Capacity for reflective self-assessment.\"\n    ],\n    \"output_handoff_points\": [\n      \"Point after alternative(s) identified (handoff to decision phase).\",\n      \"Point where final decision is made (handoff to implementation phase).\"\n    ],\n    \"procedural_boundaries\": [\n      \"Boundary: personal recognition and reflection (internal thought).\",\n      \"Boundary: transition from strategy formation to action (solution evaluation/execution).\"\n    ]\n  },\n  \"process_map_b\": {\n    \"sequence\": [\n      \"Impose prescriptive, step-by-step selection process for alternative solutions.\",\n      \"Define exhaustive lists of evaluation criteria (e.g., licensing, security, support, integration compatibility, performance, cost, future maintainability, scalability).\",\n      \"Mandate objective scoring or ranking of all alternatives according to the criteria list.\",\n      \"Require explicit documentation of evaluation results and selection rationale.\",\n      \"Instruct on compliance with rigid project/documentation standards.\",\n      \"Enforce documented justification for any instance in which custom development is chosen over alternatives.\"\n    ],\n    \"constraints\": [\n      \"All criteria must be explicitly defined and weighted.\",\n      \"Process must be followed without exception (no adhoc decisions).\",\n      \"Documentation is mandatory at every stage.\",\n      \"No progress beyond evaluation phase without full criteria documentation.\"\n    ],\n    \"requirements_dependencies\": [\n      \"Existence of exhaustive alternatives list.\",\n      \"Availability of resources to thoroughly assess each criterion.\",\n      \"Access to documentation and compliance tools.\",\n      \"Project context must fit rigid evaluation structure.\"\n    ],\n    \"output_handoff_points\": [\n      \"After final scoring and documentation, handoff to implementation team.\",\n      \"Documentation output passed to project oversight/review entity.\"\n    ],\n    \"procedural_boundaries\": [\n      \"Boundary: rigid entrance at process start—cannot skip steps.\",\n      \"Boundary: explicit transition from documentation to implementation.\",\n      \"Boundary: escalation to higher authority if criteria incomplete.\"\n    ]\n  },\n  \"integration_points\": [\n    {\n      \"from\": \"process_map_a.output_handoff_points[0]\",\n      \"to\": \"process_map_b.sequence[1]\",\n      \"note\": \"Personal identification of alternatives could serve as initial input to criteria-based evaluation list in rigid process.\"\n    },\n    {\n      \"from\": \"process_map_a.output_handoff_points[1]\",\n      \"to\": \"process_map_b.output_handoff_points[0]\",\n      \"note\": \"Decision node in personal strategy aligns with formal selection/delivery point in prescriptive process.\"\n    }\n  ],\n  \"boundary_conflicts\": [\n    {\n      \"description\": \"process_map_a allows for flexible, experiential action and informal assessment, while process_map_b enforces mandatory steps and documentation. Attempted integration threatens the atomicity of procedural boundaries by forcing informal reflection into a rigid evaluative mold.\",\n      \"conflicting_boundaries\": [\n        \"process_map_a.procedural_boundaries[0] vs process_map_b.procedural_boundaries[0]\",\n        \"process_map_a.constraints vs process_map_b.constraints\"\n      ]\n    },\n    {\n      \"description\": \"Transition from personal decision-making (process_map_a) to bureaucratic compliance (process_map_b) risks loss of motivation/context and potential information drop, violating process integrity.\",\n      \"conflicting_boundaries\": [\n        \"process_map_a.requirements_dependencies[2] vs process_map_b.requirements_dependencies[2]\"\n      ]\n    }\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"sequential_architecture\": {\n    \"stages\": [\n      {\n        \"name\": \"Reflective Recognition\",\n        \"source_process\": \"process_map_a\",\n        \"activities\": [\n          \"Reflect on the ubiquity of pre-existing solutions\",\n          \"Acknowledge learning: most functionality already exists\",\n          \"Transition mental stance: from creation to strategic evaluation\"\n        ],\n        \"boundaries\": [\n          \"Personal recognition and reflection (internal thought)\"\n        ],\n        \"constraints\": [\n          \"Self-driven, flexible, informal; strategy evolves with context\"\n        ],\n        \"output\": {\n          \"alternatives_identified\": \"Personal list or awareness of existing solutions\",\n          \"handoff\": \"To formal criteria establishment in process_map_b\"\n        }\n      },\n      {\n        \"name\": \"Formalized Alternatives Evaluation\",\n        \"source_process\": \"process_map_b\",\n        \"input\": {\n          \"alternatives\": \"alternatives_identified\"\n        },\n        \"activities\": [\n          \"Impose prescriptive step-by-step selection process\",\n          \"Define and customize exhaustive lists of evaluation criteria (could use insight from previous reflection)\"\n        ],\n        \"boundaries\": [\n          \"Mandatory process entry; no steps can be skipped\"\n        ],\n        \"constraints\": [\n          \"Explicitly defined/weighted criteria; must use formal process\"\n        ],\n        \"output\": \"Ready-to-evaluate alternatives by all required criteria\"\n      },\n      {\n        \"name\": \"Alternatives Scoring & Documentation\",\n        \"source_process\": \"process_map_b\",\n        \"activities\": [\n          \"Objectively score/rank alternatives\",\n          \"Document evaluation results and rationale\"\n        ],\n        \"boundaries\": [\n          \"No progress unless documentation and all criteria are complete\"\n        ],\n        \"constraints\": [\n          \"Documentation is mandatory at all times\"\n        ],\n        \"output\": {\n          \"evaluation_documentation\": \"Comprehensive records of scoring and justification\",\n          \"handoff\": \"To implementation or project oversight, as appropriate\"\n        }\n      },\n      {\n        \"name\": \"Decision and Implementation Interface\",\n        \"source_process\": \"Both\",\n        \"activities\": [\n          \"For standard solutions: Proceed with implementation as per scored alternative\",\n          \"For custom build decision: Enforce documented justification for deviation\"\n        ],\n        \"boundaries\": [\n          \"Clear demarcation between evaluation phase (process_map_b) and action phase (implementation, as in process_map_a's internal model)\"\n        ],\n        \"constraints\": [\n          \"No custom implementation without full traceable justification\",\n          \"Action aligns with both personal and formal decision\"\n        ],\n        \"outputs\": {\n          \"final_decision\": \"Selected alternative with rationale or approval for custom build\",\n          \"implementation_package\": \"Documentation and requirements for development phase\"\n        }\n      }\n    ],\n    \"integration_protocols\": [\n      {\n        \"from_stage\": \"Reflective Recognition\",\n        \"to_stage\": \"Formalized Alternatives Evaluation\",\n        \"method\": \"Output from personal identification directly initializes formal criteria-based evaluation, preserving informality within the first boundary before entering rigidity.\"\n      },\n      {\n        \"from_stage\": \"Alternatives Scoring & Documentation\",\n        \"to_stage\": \"Decision and Implementation Interface\",\n        \"method\": \"Only fully documented and scored alternatives (or justified non-standard decisions) are handed off, ensuring zero operational loss and atomic preservation.\"\n      }\n    ],\n    \"boundary_resolutions\": [\n      {\n        \"conflict\": \"Informal personal recognition vs Mandatory formal process entry\",\n        \"resolution\": \"Maintain separate domains: process_a activities are acknowledged as a preliminary, non-reportable input; process_b does not overwrite or require documentation of initial reflection.\"\n      },\n      {\n        \"conflict\": \"Personal decision making vs Bureaucratic compliance and documentation readiness\",\n        \"resolution\": \"Require a bridging annotation: the interface from process_a to process_b must capture any contextual or motivational factors to mitigate loss of information or intent.\"\n      }\n    ]\n  },\n  \"preserved_constraints\": [\n    \"process_map_a's flexibility is maintained within its initial phase before formal evaluation begins\",\n    \"process_map_b's mandatory criteria-definition, objectivity, documentation, and stepwise execution are uncompromised once formal evaluation starts\",\n    \"Procedural boundaries are not blended, but acknowledged and transitioned between; no operational or constraint granularity is lost at integration points\"\n  ],\n  \"maintained_requirements\": [\n    \"Each process's requirement dependencies remain distinct and intact at all sequential phases\",\n    \"Procedural boundaries are strictly enforced: self-directed reflection is not made bureaucratic, and formal evaluation/implementation phases are not informalized\",\n    \"Full sequential execution: Process A completes its reflective/strategic mode, hands off its output to Process B, which executes all prescriptive evaluation, leading to rigorous and justified implementation handoff\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Precision Synthesizer\n\nYour goal is not to **simplify** the sequential architecture, but to **synthesize** it into canonical three-part structure while preserving every constraint, requirement, and procedural boundary from both original templates. Execute as:\n\n`{role=precision_synthesizer; input=[sequential_architecture:dict, preserved_constraints:array, maintained_requirements:array]; process=[craft_compound_goal_negation(), structure_sequential_transformation_block(), enumerate_all_preserved_constraints(), specify_all_maintained_requirements(), validate_operational_completeness()]; constraints=[preserve_every_original_constraint(), maintain_all_requirements(), respect_procedural_boundaries(), ensure_canonical_compliance()]; requirements=[zero_information_loss(), complete_operational_preservation(), enhanced_sequential_power()]; output={precision_synthesized_template:dict}}`",
      "step": "c",
      "title": "Precision Synthesizer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"i've learned that almost *regardless* what specific functionalty i want to create, someone else has already done it, therefore i'm currently in the stage where i establish a strategy to determine which alternative to use *before starting to create it myself*\\\"```'\\n\\n\",\n  \"sequential_architecture\": {\n    \"stages\": [\n      {\n        \"name\": \"Reflective Recognition\",\n        \"source_process\": \"process_map_a\",\n        \"activities\": [\n          \"Reflect on the ubiquity of pre-existing solutions\",\n          \"Acknowledge learning: most functionality already exists\",\n          \"Transition mental stance: from creation to strategic evaluation\"\n        ],\n        \"boundaries\": [\n          \"Personal recognition and reflection (internal thought)\"\n        ],\n        \"constraints\": [\n          \"Self-driven, flexible, informal; strategy evolves with context\"\n        ],\n        \"output\": {\n          \"alternatives_identified\": \"Personal list or awareness of existing solutions\",\n          \"handoff\": \"To formal criteria establishment in process_map_b\"\n        }\n      },\n      {\n        \"name\": \"Formalized Alternatives Evaluation\",\n        \"source_process\": \"process_map_b\",\n        \"input\": {\n          \"alternatives\": \"alternatives_identified\"\n        },\n        \"activities\": [\n          \"Impose prescriptive step-by-step selection process\",\n          \"Define and customize exhaustive lists of evaluation criteria (could use insight from previous reflection)\"\n        ],\n        \"boundaries\": [\n          \"Mandatory process entry; no steps can be skipped\"\n        ],\n        \"constraints\": [\n          \"Explicitly defined/weighted criteria; must use formal process\"\n        ],\n        \"output\": \"Ready-to-evaluate alternatives by all required criteria\"\n      },\n      {\n        \"name\": \"Alternatives Scoring & Documentation\",\n        \"source_process\": \"process_map_b\",\n        \"activities\": [\n          \"Objectively score/rank alternatives\",\n          \"Document evaluation results and rationale\"\n        ],\n        \"boundaries\": [\n          \"No progress unless documentation and all criteria are complete\"\n        ],\n        \"constraints\": [\n          \"Documentation is mandatory at all times\"\n        ],\n        \"output\": {\n          \"evaluation_documentation\": \"Comprehensive records of scoring and justification\",\n          \"handoff\": \"To implementation or project oversight, as appropriate\"\n        }\n      },\n      {\n        \"name\": \"Decision and Implementation Interface\",\n        \"source_process\": \"Both\",\n        \"activities\": [\n          \"For standard solutions: Proceed with implementation as per scored alternative\",\n          \"For custom build decision: Enforce documented justification for deviation\"\n        ],\n        \"boundaries\": [\n          \"Clear demarcation between evaluation phase (process_map_b) and action phase (implementation, as in process_map_a's internal model)\"\n        ],\n        \"constraints\": [\n          \"No custom implementation without full traceable justification\",\n          \"Action aligns with both personal and formal decision\"\n        ],\n        \"outputs\": {\n          \"final_decision\": \"Selected alternative with rationale or approval for custom build\",\n          \"implementation_package\": \"Documentation and requirements for development phase\"\n        }\n      }\n    ],\n    \"integration_protocols\": [\n      {\n        \"from_stage\": \"Reflective Recognition\",\n        \"to_stage\": \"Formalized Alternatives Evaluation\",\n        \"method\": \"Output from personal identification directly initializes formal criteria-based evaluation, preserving informality within the first boundary before entering rigidity.\"\n      },\n      {\n        \"from_stage\": \"Alternatives Scoring & Documentation\",\n        \"to_stage\": \"Decision and Implementation Interface\",\n        \"method\": \"Only fully documented and scored alternatives (or justified non-standard decisions) are handed off, ensuring zero operational loss and atomic preservation.\"\n      }\n    ],\n    \"boundary_resolutions\": [\n      {\n        \"conflict\": \"Informal personal recognition vs Mandatory formal process entry\",\n        \"resolution\": \"Maintain separate domains: process_a activities are acknowledged as a preliminary, non-reportable input; process_b does not overwrite or require documentation of initial reflection.\"\n      },\n      {\n        \"conflict\": \"Personal decision making vs Bureaucratic compliance and documentation readiness\",\n        \"resolution\": \"Require a bridging annotation: the interface from process_a to process_b must capture any contextual or motivational factors to mitigate loss of information or intent.\"\n      }\n    ]\n  },\n  \"preserved_constraints\": [\n    \"process_map_a's flexibility is maintained within its initial phase before formal evaluation begins\",\n    \"process_map_b's mandatory criteria-definition, objectivity, documentation, and stepwise execution are uncompromised once formal evaluation starts\",\n    \"Procedural boundaries are not blended, but acknowledged and transitioned between; no operational or constraint granularity is lost at integration points\"\n  ],\n  \"maintained_requirements\": [\n    \"Each process's requirement dependencies remain distinct and intact at all sequential phases\",\n    \"Procedural boundaries are strictly enforced: self-directed reflection is not made bureaucratic, and formal evaluation/implementation phases are not informalized\",\n    \"Full sequential execution: Process A completes its reflective/strategic mode, hands off its output to Process B, which executes all prescriptive evaluation, leading to rigorous and justified implementation handoff\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"precision_synthesized_template\": {\n    \"compound_goal_negation\": \"No process shall allow informal, undocumented, or self-driven decision to directly dictate selection or implementation of alternatives without entering and satisfying the full rigor of the prescribed formal evaluation and documentation framework; equally, the prescriptive evaluation method shall not retroactively impose formality or overwrite the creative and contextual motivations established in the purely reflective inception stage.\",\n    \"sequential_transformation_block\": [\n      {\n        \"phase\": \"Reflective Recognition\",\n        \"origin\": \"process_map_a\",\n        \"description\": \"The individual initiates with an internal, flexible reflection—recognizing the likelihood of pre-existing solutions for intended functionality. The outcome is a contextually framed awareness or list of alternatives, integrating motivational and contextual insights. This phase is explicitly insulated from formal procedural requirements, ensuring subjective, informal, and evolving perspectives are preserved strictly as preliminary input.\",\n        \"operational_boundary\": \"Exclusive to personal cognition; no documentation or formal criteria imposed at this stage.\"\n      },\n      {\n        \"phase\": \"Formalized Evaluation and Documentation\",\n        \"origin\": \"process_map_b\",\n        \"description\": \"Upon receiving the output of phase one, this phase enforces entry into a rigidly stepwise, criteria-governed evaluation process. Alternatives are considered only if surfaced by the prior phase. All criteria for evaluation are defined explicitly; each alternative is objectively scored or ranked under these criteria. Documentation is mandatory at every step—criteria rationales, scores, and decision justifications are comprehensively recorded, ensuring traceable, reportable outcomes aligned to organizational/bureaucratic demands.\",\n        \"operational_boundary\": \"No entry without complete handoff from phase one; no informal overrides; process is atomic, and internal stages (criteria definition, scoring, documentation) are strictly prescribed.\"\n      },\n      {\n        \"phase\": \"Decision Interface and Implementation Handoff\",\n        \"origin\": \"Synthesis of process_map_a and process_map_b\",\n        \"description\": \"Culminates in a bifurcated outcome: (1) If a standard, evaluated alternative is selected, implementation proceeds according to the fully documented, scored decision; (2) If deviation (custom build) is chosen, it must carry full traceable justification derived from the prior evaluation phase. Final records include all documentation, scoring rationale, contextual motivations (bridging annotation from phase one), and explicit indication of conformance or justified deviation.\",\n        \"operational_boundary\": \"No implementation allowed without meeting documentation and decision traceability standards; personal/contextual factors carried only as annotated reference, not as process criteria.\"\n      }\n    ],\n    \"enumerated_preserved_constraints\": [\n      \"Flexibility and informality of process_map_a is strictly limited to the Reflective Recognition phase; entry into Formalized Evaluation is an explicit, one-way transition.\",\n      \"Mandatory stepwise criteria-definition, objectivity, and documentation requirements of process_map_b are strictly enforced from commencement of the second phase.\",\n      \"Procedural boundaries are inviolable: outputs from each phase serve as explicit, type-constrained handoffs; no blending or retroactive alteration of phase content at integration points is permissible.\",\n      \"Integration protocols require explicit bridging annotation to prevent loss of motivational/contextual information, without transforming phase-one content into formal criteria or documentation.\",\n      \"Zero operational or constraint granularity is lost at any phase or handoff.\"\n    ],\n    \"specified_maintained_requirements\": [\n      \"All requirement dependencies of each original process are executed in intact sequence, without merger or dilution.\",\n      \"Self-directed strategic reflection (phase one) is never bureaucratized; formalized evaluation/decision/implementation (phases two and three) never admit informal deviation.\",\n      \"Sequential execution is guaranteed: phase one must complete before phase two may commence, and phase three cannot proceed unless all documentation and evaluation rigor from phase two are satisfied.\",\n      \"Handoffs are fully atomic: motivational/contextual data transition as reference, but only scored, justified, and documented alternatives inform implementation or deviation.\",\n      \"Traceable linkage is maintained between each phase's output, preserving context and rationale without any operational ambiguity.\"\n    ],\n    \"validation_operational_completeness\": {\n      \"zero_information_loss\": true,\n      \"complete_operational_preservation\": true,\n      \"enhanced_sequential_power\": true,\n      \"canonical_compliance\": \"All process phases conform to strict three-part structure—reflection, evaluation/documentation, justified implementation—ensuring no original boundary, constraint, or requirement is lost, merged, or retroactively compromised. All operational, motivational, and procedural facets of both original templates are explicitly represented and insulated.\"\n    }\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Problem Exploder\n\nYour goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\n\n`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`",
      "step": "a",
      "title": "Problem Exploder",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"i've learned that almost *regardless* what specific functionalty i want to create, someone else has already done it, therefore i'm currently in the stage where i establish a strategy to determine which alternative to use *before starting to create it myself*\\\"```'\\n\\n\",\n  \"precision_synthesized_template\": {\n    \"compound_goal_negation\": \"No process shall allow informal, undocumented, or self-driven decision to directly dictate selection or implementation of alternatives without entering and satisfying the full rigor of the prescribed formal evaluation and documentation framework; equally, the prescriptive evaluation method shall not retroactively impose formality or overwrite the creative and contextual motivations established in the purely reflective inception stage.\",\n    \"sequential_transformation_block\": [\n      {\n        \"phase\": \"Reflective Recognition\",\n        \"origin\": \"process_map_a\",\n        \"description\": \"The individual initiates with an internal, flexible reflection—recognizing the likelihood of pre-existing solutions for intended functionality. The outcome is a contextually framed awareness or list of alternatives, integrating motivational and contextual insights. This phase is explicitly insulated from formal procedural requirements, ensuring subjective, informal, and evolving perspectives are preserved strictly as preliminary input.\",\n        \"operational_boundary\": \"Exclusive to personal cognition; no documentation or formal criteria imposed at this stage.\"\n      },\n      {\n        \"phase\": \"Formalized Evaluation and Documentation\",\n        \"origin\": \"process_map_b\",\n        \"description\": \"Upon receiving the output of phase one, this phase enforces entry into a rigidly stepwise, criteria-governed evaluation process. Alternatives are considered only if surfaced by the prior phase. All criteria for evaluation are defined explicitly; each alternative is objectively scored or ranked under these criteria. Documentation is mandatory at every step—criteria rationales, scores, and decision justifications are comprehensively recorded, ensuring traceable, reportable outcomes aligned to organizational/bureaucratic demands.\",\n        \"operational_boundary\": \"No entry without complete handoff from phase one; no informal overrides; process is atomic, and internal stages (criteria definition, scoring, documentation) are strictly prescribed.\"\n      },\n      {\n        \"phase\": \"Decision Interface and Implementation Handoff\",\n        \"origin\": \"Synthesis of process_map_a and process_map_b\",\n        \"description\": \"Culminates in a bifurcated outcome: (1) If a standard, evaluated alternative is selected, implementation proceeds according to the fully documented, scored decision; (2) If deviation (custom build) is chosen, it must carry full traceable justification derived from the prior evaluation phase. Final records include all documentation, scoring rationale, contextual motivations (bridging annotation from phase one), and explicit indication of conformance or justified deviation.\",\n        \"operational_boundary\": \"No implementation allowed without meeting documentation and decision traceability standards; personal/contextual factors carried only as annotated reference, not as process criteria.\"\n      }\n    ],\n    \"enumerated_preserved_constraints\": [\n      \"Flexibility and informality of process_map_a is strictly limited to the Reflective Recognition phase; entry into Formalized Evaluation is an explicit, one-way transition.\",\n      \"Mandatory stepwise criteria-definition, objectivity, and documentation requirements of process_map_b are strictly enforced from commencement of the second phase.\",\n      \"Procedural boundaries are inviolable: outputs from each phase serve as explicit, type-constrained handoffs; no blending or retroactive alteration of phase content at integration points is permissible.\",\n      \"Integration protocols require explicit bridging annotation to prevent loss of motivational/contextual information, without transforming phase-one content into formal criteria or documentation.\",\n      \"Zero operational or constraint granularity is lost at any phase or handoff.\"\n    ],\n    \"specified_maintained_requirements\": [\n      \"All requirement dependencies of each original process are executed in intact sequence, without merger or dilution.\",\n      \"Self-directed strategic reflection (phase one) is never bureaucratized; formalized evaluation/decision/implementation (phases two and three) never admit informal deviation.\",\n      \"Sequential execution is guaranteed: phase one must complete before phase two may commence, and phase three cannot proceed unless all documentation and evaluation rigor from phase two are satisfied.\",\n      \"Handoffs are fully atomic: motivational/contextual data transition as reference, but only scored, justified, and documented alternatives inform implementation or deviation.\",\n      \"Traceable linkage is maintained between each phase's output, preserving context and rationale without any operational ambiguity.\"\n    ],\n    \"validation_operational_completeness\": {\n      \"zero_information_loss\": true,\n      \"complete_operational_preservation\": true,\n      \"enhanced_sequential_power\": true,\n      \"canonical_compliance\": \"All process phases conform to strict three-part structure—reflection, evaluation/documentation, justified implementation—ensuring no original boundary, constraint, or requirement is lost, merged, or retroactively compromised. All operational, motivational, and procedural facets of both original templates are explicitly represented and insulated.\"\n    }\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"goal_map\": [\n    {\n      \"explicit_asks\": [\n        \"Establish a strategy to determine which alternative (existing solution) to use before starting to create new functionality from scratch.\"\n      ],\n      \"hidden_assumptions\": [\n        \"Pre-existing solutions for any desired functionality almost always exist.\",\n        \"It is preferable (or required) to consider those existing solutions before undertaking new development.\",\n        \"The process for selecting among alternatives must be formalized and documented, not ad hoc or informal.\",\n        \"There is value—perhaps organizationally, bureaucratically, or for audit/tracing purposes—in capturing both subjectively motivated insight and objectively scored evaluation.\",\n        \"Subjective/intuitive motivations (why these alternatives arose and their selection context) matter but must not undermine the rigor of the evaluation.\",\n        \"No phase may override, dilute, or retroactively formalize the content/outcome of the preceding phase.\"\n      ],\n      \"enumerated_sub_goals\": [\n        \"Reflect informally on the desired functionality and likely pre-existing alternative solutions.\",\n        \"Document motivations, context, and reasoning behind why certain alternatives are considered (subjective recognition, not criteria-based).\",\n        \"Transition from subjective reflection to a rigid, documented, criteria-based comparative evaluation of surfaced alternatives (no new alternatives introduced at this stage).\",\n        \"Explicitly define evaluation criteria for alternatives.\",\n        \"Objectively score/rank alternatives against the defined criteria.\",\n        \"Thoroughly document all steps: criteria, rationale, scoring, decision rationale.\",\n        \"Decide between choosing an existing alternative or proceeding with a custom build.\",\n        \"If selecting a custom solution, provide full, traceable justification based on the evaluated criteria and outcomes.\",\n        \"Prepare final documentation: complete trace linking phase one (subjective/contextual) with phases two and three (objective evaluation and ultimate decision).\",\n        \"Prevent process breakdowns: No informal decision or undocumented reasoning allowed in or after the evaluation phase—subjective factors survive solely as reference annotations, never as decision drivers.\",\n        \"Ensure the process is strictly sequential: no phase two actions before phase one is finished, and no implementation or deviation until all formal evaluation and documentation is complete.\"\n      ],\n      \"revealed_blockers\": [\n        \"Potential ambiguity in defining thorough, explicit evaluation criteria for alternatives, especially for novel or unconventional functionality.\",\n        \"The challenge of maintaining the inviolable boundary between subjective (phase one) and formalized (phase two) processes—it may be tempting to \\\"shortcut\\\" documentation or blend phases.\",\n        \"Difficulty in ensuring zero information loss and traceability as informal motivations/context must be preserved as annotations, never as evaluation criteria.\",\n        \"Risk that organizational or team participants might skip or conflate phase steps to save time, especially under deadline or resource pressure.\",\n        \"Possibility of incomplete or poorly documented reasoning, scores, or contextual notes, undermining auditability and accountability.\",\n        \"Resistance or confusion from team members unaccustomed to rigid separation of \\\"reflection\\\" and \\\"formal evaluation.\\\"\",\n        \"Handoffs require atomicity—any missing documentation, context, or score at the transfer from one phase to the next could block the entire process.\",\n        \"No direct implementation is permitted without fully completing all prior process documentation and evaluation steps, potentially causing delays if any aspect is incomplete or ambiguous.\"\n      ]\n    }\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`",
      "step": "a",
      "title": "Instruction Converter",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"i've learned that almost *regardless* what specific functionalty i want to create, someone else has already done it, therefore i'm currently in the stage where i establish a strategy to determine which alternative to use *before starting to create it myself*\\\"```'\\n\\n\",\n  \"goal_map\": [\n    {\n      \"explicit_asks\": [\n        \"Establish a strategy to determine which alternative (existing solution) to use before starting to create new functionality from scratch.\"\n      ],\n      \"hidden_assumptions\": [\n        \"Pre-existing solutions for any desired functionality almost always exist.\",\n        \"It is preferable (or required) to consider those existing solutions before undertaking new development.\",\n        \"The process for selecting among alternatives must be formalized and documented, not ad hoc or informal.\",\n        \"There is value—perhaps organizationally, bureaucratically, or for audit/tracing purposes—in capturing both subjectively motivated insight and objectively scored evaluation.\",\n        \"Subjective/intuitive motivations (why these alternatives arose and their selection context) matter but must not undermine the rigor of the evaluation.\",\n        \"No phase may override, dilute, or retroactively formalize the content/outcome of the preceding phase.\"\n      ],\n      \"enumerated_sub_goals\": [\n        \"Reflect informally on the desired functionality and likely pre-existing alternative solutions.\",\n        \"Document motivations, context, and reasoning behind why certain alternatives are considered (subjective recognition, not criteria-based).\",\n        \"Transition from subjective reflection to a rigid, documented, criteria-based comparative evaluation of surfaced alternatives (no new alternatives introduced at this stage).\",\n        \"Explicitly define evaluation criteria for alternatives.\",\n        \"Objectively score/rank alternatives against the defined criteria.\",\n        \"Thoroughly document all steps: criteria, rationale, scoring, decision rationale.\",\n        \"Decide between choosing an existing alternative or proceeding with a custom build.\",\n        \"If selecting a custom solution, provide full, traceable justification based on the evaluated criteria and outcomes.\",\n        \"Prepare final documentation: complete trace linking phase one (subjective/contextual) with phases two and three (objective evaluation and ultimate decision).\",\n        \"Prevent process breakdowns: No informal decision or undocumented reasoning allowed in or after the evaluation phase—subjective factors survive solely as reference annotations, never as decision drivers.\",\n        \"Ensure the process is strictly sequential: no phase two actions before phase one is finished, and no implementation or deviation until all formal evaluation and documentation is complete.\"\n      ],\n      \"revealed_blockers\": [\n        \"Potential ambiguity in defining thorough, explicit evaluation criteria for alternatives, especially for novel or unconventional functionality.\",\n        \"The challenge of maintaining the inviolable boundary between subjective (phase one) and formalized (phase two) processes—it may be tempting to \\\"shortcut\\\" documentation or blend phases.\",\n        \"Difficulty in ensuring zero information loss and traceability as informal motivations/context must be preserved as annotations, never as evaluation criteria.\",\n        \"Risk that organizational or team participants might skip or conflate phase steps to save time, especially under deadline or resource pressure.\",\n        \"Possibility of incomplete or poorly documented reasoning, scores, or contextual notes, undermining auditability and accountability.\",\n        \"Resistance or confusion from team members unaccustomed to rigid separation of \\\"reflection\\\" and \\\"formal evaluation.\\\"\",\n        \"Handoffs require atomicity—any missing documentation, context, or score at the transfer from one phase to the next could block the entire process.\",\n        \"No direct implementation is permitted without fully completing all prior process documentation and evaluation steps, potentially causing delays if any aspect is incomplete or ambiguous.\"\n      ]\n    }\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"instruction_format\": \"Establish a documented strategy to evaluate and select pre-existing solutions before developing new functionality. Begin by conducting an informal reflection on the required functionality and potential existing alternatives. Record motivations, context, and reasoning for considering specific alternatives as annotations without introducing evaluation criteria at this stage. Progress to a rigid, criteria-based comparative evaluation: formally define explicit evaluation criteria, then objectively score and rank listed alternatives against these criteria. Do not introduce new alternatives during the evaluation phase. Maintain full documentation throughout each step, including criteria, rationale, scoring, and the reasoning behind the final decision. Decide whether to adopt an existing alternative or proceed with a custom build, providing comprehensive justification based on the evaluated criteria if developing a custom solution. Consolidate all documentation to enable traceability from initial subjective context through to objective evaluation and the final decision. Rigorously enforce the sequential character of the process, ensuring that all informal reflection is completed and documented before formal evaluation begins, and preventing implementation or deviation until all steps are thoroughly documented and completed. Never allow informal decisions, undocumented reasoning, or subjective considerations to override objective evaluation in or after the formal phase. Monitor for process fidelity by preventing phase-skipping, criteria ambiguity, documentation gaps, information loss, and unauthorized handoffs. Require atomic and complete handoffs between phases to guarantee process integrity and auditability.\"\n}",
        }
      }
    }