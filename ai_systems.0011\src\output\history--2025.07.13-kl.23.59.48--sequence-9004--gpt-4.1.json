  {
  "initial_prompt": "llm_instructions_for_sublimetext_plugin='''You’re an expert Sublime Text plugin developer with a deep understanding of Python and the Sublime Text API. You specialize in creating intuitive and efficient plugins that enhance user productivity and streamline workflows. You possess deep knowledge of the Sublime Text API and its ecosystem (and best practices), along with a strong understanding of user needs and developer workflows.\n\n- Sublime Layout\n    > Lagre Layouts\n        > Lagre Regelsett knyttet til Layout (hvilke tabs som skal hvor, hvilke som skal lukkes, osv)\n            > F.eks. `alltid flytt [\"main.py\", \"*.py.bak\"] til layout.4.index.2`\n\n    > Kan videreutvikles til en standard som automatisk opprettholdes ved alle (.sublime-project) prosjekter'''",
  "sequence_id": "9004",
  "results": [
      {
      "instruction": "# Brief Normaliser\n\nYour goal is **not** to answer the brief, but to rephrase it into a clean, noise-free statement of facts. Execute as:\n\n`{role=brief_normaliser; input=[raw_brief:str]; process=[strip_meta_language(), purge_metaphor(), keep_only_actionable_text(), detect_codebase_token(\"@codebase\"), preserve_original_order()]; constraints=[no_personal_commentary()]; requirements=[output_must_be_plaintext()], output={normalised_brief:str}}`\n\n## Context Data\n\n```json\n{\n  \"description\": \"Dissects a raw specification to extract every piece of operational context an autonomous agent will need.\",\n  \"input_focus\": \"Unedited specification text supplied by the user.\",\n  \"output_focus\": \"A typed context_profile dict containing domain, project root, explicit objectives, and implicit requirements.\",\n  \"key_operations\": [\n    \"Removing metaphor and non‑operational language.\",\n    \"Detecting the working domain or tech stack.\",\n    \"Locating the root marker (`@codebase`) for path scoping.\",\n    \"Listing every stated objective verbatim.\",\n    \"Surfacing hidden assumptions and requirements.\"\n  ],\n  \"constraints_context\": [\n    \"May not paraphrase or interpret meaning beyond direct extraction.\",\n    \"Absolutely forbidden from proposing solutions or tasks.\"\n  ],\n  \"relevance\": \"Defines scope and boundaries for all downstream templates, guaranteeing that later steps operate on clean, actionable data.\"\n}\n```",
      "step": "a",
      "title": "Brief Normaliser",
      "input": "llm_instructions_for_sublimetext_plugin='''You’re an expert Sublime Text plugin developer with a deep understanding of Python and the Sublime Text API. You specialize in creating intuitive and efficient plugins that enhance user productivity and streamline workflows. You possess deep knowledge of the Sublime Text API and its ecosystem (and best practices), along with a strong understanding of user needs and developer workflows.\n\n- Sublime Layout\n    > Lagre Layouts\n        > Lagre Regelsett knyttet til Layout (hvilke tabs som skal hvor, hvilke som skal lukkes, osv)\n            > F.eks. `alltid flytt [\"main.py\", \"*.py.bak\"] til layout.4.index.2`\n\n    > Kan videreutvikles til en standard som automatisk opprettholdes ved alle (.sublime-project) prosjekter'''",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"normalised_brief\": \"Expertise in Sublime Text plugin development using Python and the Sublime Text API is required. Specialization in creating plugins that improve user productivity and workflows. In-depth knowledge of the Sublime Text API, ecosystem, and best practices. Familiarity with user and developer workflow requirements is necessary. Focus on Sublime Layouts, specifically large layouts and rule sets relating to tab management (such as specifying which files/tabs should be moved to specified layout indices or closed). Example given: always move [\\\"main.py\\\", \\\"*.py.bak\\\"] to layout.4.index.2. Potential to develop this into a standard enforced across all (.sublime-project) projects. No @codebase marker detected.\"\n}",
        }
      }
    },
      {
      "instruction": "# Goal Distiller\n\nYour goal is **not** to plan work, but to extract a single, explicit mission and its hard boundaries. Execute as:\n\n`{role=goal_distiller; input=[normalised_brief:str]; process=[identify_primary_objective(), list_strict_constraints(), capture_success_criteria(), harvest_allowed_tools()], constraints=[exactly_one_primary_objective()], output={distilled_goal:dict(objective:str,constraints:list,success:list,tools:list)}}`\n\n## Context Data\n\n```json\n{\n  \"description\": \"Transforms the context profile into a dependency‑ordered list of granular tasks fit for automated execution.\",\n  \"input_focus\": \"The context_profile produced by the Context Extractor.\",\n  \"output_focus\": \"ordered_tasks – a list of unique, verb‑driven commands each scoped to the codebase.\",\n  \"key_operations\": [\n    \"Splitting broad goals into single‑action commands.\",\n    \"Adding `@codebase` prefixes so agents act in the correct directory.\",\n    \"Sequencing tasks by logical dependency.\",\n    \"Eliminating redundancy and cosmetic‑only instructions.\"\n  ],\n  \"constraints_context\": [\n    \"Every task must begin with a strong action verb (identify, refactor, log, etc.).\",\n    \"Tasks must be non‑overlapping and directly tied to functional goals.\"\n  ],\n  \"relevance\": \"Provides the autonomous agent with a clear, step‑by‑step execution blueprint free of ambiguity.\"\n}\n```",
      "step": "b",
      "title": "Goal Distiller",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"llm_instructions_for_sublimetext_plugin='''You’re an expert Sublime Text plugin developer with a deep understanding of Python and the Sublime Text API. You specialize in creating intuitive and efficient plugins that enhance user productivity and streamline workflows. You possess deep knowledge of the Sublime Text API and its ecosystem (and best practices), along with a strong understanding of user needs and developer workflows.\\n\\n- Sublime Layout\\n    > Lagre Layouts\\n        > Lagre Regelsett knyttet til Layout (hvilke tabs som skal hvor, hvilke som skal lukkes, osv)\\n            > F.eks. `alltid flytt [\\\"main.py\\\", \\\"*.py.bak\\\"] til layout.4.index.2`\\n\\n    > Kan videreutvikles til en standard som automatisk opprettholdes ved alle (.sublime-project) prosjekter'''\\\"```'\\n\\n\",\n  \"normalised_brief\": \"Expertise in Sublime Text plugin development using Python and the Sublime Text API is required. Specialization in creating plugins that improve user productivity and workflows. In-depth knowledge of the Sublime Text API, ecosystem, and best practices. Familiarity with user and developer workflow requirements is necessary. Focus on Sublime Layouts, specifically large layouts and rule sets relating to tab management (such as specifying which files/tabs should be moved to specified layout indices or closed). Example given: always move [\\\"main.py\\\", \\\"*.py.bak\\\"] to layout.4.index.2. Potential to develop this into a standard enforced across all (.sublime-project) projects. No @codebase marker detected.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"distilled_goal\": {\n    \"objective\": \"Develop a Sublime Text plugin (in Python, using the Sublime Text API) that allows users to define and automatically enforce large layout-related rule sets for tab management — such as specifying which files or file patterns should be moved to particular layout positions or closed — with support for applying and maintaining these standards across all .sublime-project files.\",\n    \"constraints\": [\n      \"Plugin must be implemented in Python using the Sublime Text API.\",\n      \"Rule sets must support specific files and wildcard patterns (e.g., *.py.bak).\",\n      \"Actions supported: move tabs to specified layout index, close tabs, etc.\",\n      \"Functionality must work for any open Sublime project and persist across sessions.\",\n      \"Users must be able to define and edit rules easily (e.g., via configuration files).\",\n      \"Plugin must not interfere with unrelated Sublime Text operations or plugins.\"\n    ],\n    \"success\": [\n      \"Users can define rules that reliably move or close tabs based on file name or pattern.\",\n      \"Specified tab arrangements are automatically enforced whenever a project (.sublime-project) is opened or layout/tab state changes.\",\n      \"Plugin works consistently across all Sublime Text installations and projects.\",\n      \"Configuring and editing rules is intuitive and accessible to end users.\",\n      \"No disruption or conflicts with other features of Sublime Text.\"\n    ],\n    \"tools\": [\n      \"Python\",\n      \"Sublime Text API\",\n      \".sublime-project file hooks/listeners\",\n      \"Config file management (possibly JSON or YAML)\",\n      \"Pattern matching libraries (e.g., fnmatch)\"\n    ]\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Task Architect\n\nYour goal is **not** to write prose, but to decompose the objective into ordered, atomic tasks with built-in validation hooks. Execute as:\n\n`{role=task_architect; input=[distilled_goal:dict]; process=[derive_atomic_tasks(), order_tasks_by_dependency(), pair_each_task_with_validation_method(), tag_required_artifacts()], requirements=[no_task_overlap(), every_task_has_validation()], output={task_list:list}}`\n\n## Context Data\n\n```json\n{\n  \"description\": \"Consolidates every rule, boundary, and policy the agent must respect during execution.\",\n  \"input_focus\": \"context_profile and ordered_tasks.\",\n  \"output_focus\": \"constraint_set – a deduplicated list of textual constraints.\",\n  \"key_operations\": [\n    \"Harvesting functional‑equivalence mandates.\",\n    \"Capturing API and interface preservation rules.\",\n    \"Recording audit/logging, rollback, and policy obligations.\",\n    \"Removing duplicate or conflicting constraints.\"\n  ],\n  \"constraints_context\": [\n    \"Must not create or modify tasks; only list constraints.\",\n    \"Constraint entries must be unique and actionable.\"\n  ],\n  \"relevance\": \"Prevents unintended side‑effects by explicitly defining the operational guard‑rails for the agent.\"\n}\n```",
      "step": "c",
      "title": "Task Architect",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"llm_instructions_for_sublimetext_plugin='''You’re an expert Sublime Text plugin developer with a deep understanding of Python and the Sublime Text API. You specialize in creating intuitive and efficient plugins that enhance user productivity and streamline workflows. You possess deep knowledge of the Sublime Text API and its ecosystem (and best practices), along with a strong understanding of user needs and developer workflows.\\n\\n- Sublime Layout\\n    > Lagre Layouts\\n        > Lagre Regelsett knyttet til Layout (hvilke tabs som skal hvor, hvilke som skal lukkes, osv)\\n            > F.eks. `alltid flytt [\\\"main.py\\\", \\\"*.py.bak\\\"] til layout.4.index.2`\\n\\n    > Kan videreutvikles til en standard som automatisk opprettholdes ved alle (.sublime-project) prosjekter'''\\\"```'\\n\\n\",\n  \"distilled_goal\": {\n    \"objective\": \"Develop a Sublime Text plugin (in Python, using the Sublime Text API) that allows users to define and automatically enforce large layout-related rule sets for tab management — such as specifying which files or file patterns should be moved to particular layout positions or closed — with support for applying and maintaining these standards across all .sublime-project files.\",\n    \"constraints\": [\n      \"Plugin must be implemented in Python using the Sublime Text API.\",\n      \"Rule sets must support specific files and wildcard patterns (e.g., *.py.bak).\",\n      \"Actions supported: move tabs to specified layout index, close tabs, etc.\",\n      \"Functionality must work for any open Sublime project and persist across sessions.\",\n      \"Users must be able to define and edit rules easily (e.g., via configuration files).\",\n      \"Plugin must not interfere with unrelated Sublime Text operations or plugins.\"\n    ],\n    \"success\": [\n      \"Users can define rules that reliably move or close tabs based on file name or pattern.\",\n      \"Specified tab arrangements are automatically enforced whenever a project (.sublime-project) is opened or layout/tab state changes.\",\n      \"Plugin works consistently across all Sublime Text installations and projects.\",\n      \"Configuring and editing rules is intuitive and accessible to end users.\",\n      \"No disruption or conflicts with other features of Sublime Text.\"\n    ],\n    \"tools\": [\n      \"Python\",\n      \"Sublime Text API\",\n      \".sublime-project file hooks/listeners\",\n      \"Config file management (possibly JSON or YAML)\",\n      \"Pattern matching libraries (e.g., fnmatch)\"\n    ]\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"task_list\": [\n    {\n      \"task\": \"Extract all explicit and implicit operational constraints from the context_profile and ordered_tasks, focusing on functional and technical rules.\",\n      \"validation\": \"Cross-reference extracted constraints with provided distilled_goal.constraints, distilled_goal.success, and key_operations to ensure completeness and accuracy.\",\n      \"artifacts\": [\"context_profile\", \"ordered_tasks\", \"distilled_goal.constraints\", \"distilled_goal.success\"]\n    },\n    {\n      \"task\": \"Collect all interface, API usage, and preservation requirements mentioned in the context, including relevant interactions with Sublime Text and configuration mechanisms.\",\n      \"validation\": \"Check each constraint for explicit mention of API/interface preservation; flag any missing/ambiguous coverage for review.\",\n      \"artifacts\": [\"context_profile\", \"distilled_goal.tools\", \"key_operations\"]\n    },\n    {\n      \"task\": \"Identify and record any audit, logging, rollback, or policy compliance obligations from both project and context specifications.\",\n      \"validation\": \"Ensure all constraints directly related to auditability, logging, and rollback are represented by searching tasks and context for these requirements.\",\n      \"artifacts\": [\"context_profile\", \"distilled_goal\", \"ordered_tasks\"]\n    },\n    {\n      \"task\": \"Remove duplicate and conflicting constraints to produce a deduplicated actionable set.\",\n      \"validation\": \"Perform set comparison and manual review to confirm no duplicate or contradictory constraint remains.\",\n      \"artifacts\": [\"all constraints from previous tasks\"]\n    },\n    {\n      \"task\": \"Present the final, unique, and actionable constraint_set as a plain deduplicated list.\",\n      \"validation\": \"Verify list format (text entities, no overlap, clearly actionable, no tasks or open questions included) and validate uniqueness.\",\n      \"artifacts\": [\"deduplicated constraint set\"]\n    }\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Prompt Composer\n\nYour goal is **not** to validate, but to fuse role, objective, and tasks into one executable agent prompt. Execute as:\n\n`{role=prompt_composer; input={distilled_goal:dict, task_list:list}; process=[build_system_section(role=\"Autonomous Coding Assistant\"), insert_goal_negation_intro(\"do not answer, perform tasks\"), embed_task_sequence(), state_global_constraints(), specify_output_format(\"markdown\"), include_codebase_hint(\"@codebase\")], requirements=[prompt_under_600_tokens()], output={assembled_prompt:str}}`\n\n## Context Data\n\n```json\n{\n  \"description\": \"Integrates tasks and constraints into a single, executable prompt string for autonomous coding agents.\",\n  \"input_focus\": \"ordered_tasks, constraint_set, and context_profile.\",\n  \"output_focus\": \"agent_prompt – complete instructions with sections for role, tasks, constraints, and validation goals.\",\n  \"key_operations\": [\n    \"Starting with a goal‑negation header to lock scope.\",\n    \"Embedding a precise agent role declaration.\",\n    \"Listing tasks numerically for deterministic execution order.\",\n    \"Appending constraints as a bullet list.\",\n    \"Stating final validation targets (functional equivalence, audit pass).\"\n  ],\n  \"constraints_context\": [\n    \"Entire prompt must be ≤ 300 tokens and written in English.\",\n    \"No re‑phrasing of task semantics or constraint meaning.\"\n  ],\n  \"relevance\": \"Delivers a turnkey command set that an autonomous agent can run without additional human clarification.\"\n}\n```",
      "step": "d",
      "title": "Prompt Composer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"llm_instructions_for_sublimetext_plugin='''You’re an expert Sublime Text plugin developer with a deep understanding of Python and the Sublime Text API. You specialize in creating intuitive and efficient plugins that enhance user productivity and streamline workflows. You possess deep knowledge of the Sublime Text API and its ecosystem (and best practices), along with a strong understanding of user needs and developer workflows.\\n\\n- Sublime Layout\\n    > Lagre Layouts\\n        > Lagre Regelsett knyttet til Layout (hvilke tabs som skal hvor, hvilke som skal lukkes, osv)\\n            > F.eks. `alltid flytt [\\\"main.py\\\", \\\"*.py.bak\\\"] til layout.4.index.2`\\n\\n    > Kan videreutvikles til en standard som automatisk opprettholdes ved alle (.sublime-project) prosjekter'''\\\"```'\\n\\n\",\n  \"task_list\": [\n    {\n      \"task\": \"Extract all explicit and implicit operational constraints from the context_profile and ordered_tasks, focusing on functional and technical rules.\",\n      \"validation\": \"Cross-reference extracted constraints with provided distilled_goal.constraints, distilled_goal.success, and key_operations to ensure completeness and accuracy.\",\n      \"artifacts\": [\n        \"context_profile\",\n        \"ordered_tasks\",\n        \"distilled_goal.constraints\",\n        \"distilled_goal.success\"\n      ]\n    },\n    {\n      \"task\": \"Collect all interface, API usage, and preservation requirements mentioned in the context, including relevant interactions with Sublime Text and configuration mechanisms.\",\n      \"validation\": \"Check each constraint for explicit mention of API/interface preservation; flag any missing/ambiguous coverage for review.\",\n      \"artifacts\": [\n        \"context_profile\",\n        \"distilled_goal.tools\",\n        \"key_operations\"\n      ]\n    },\n    {\n      \"task\": \"Identify and record any audit, logging, rollback, or policy compliance obligations from both project and context specifications.\",\n      \"validation\": \"Ensure all constraints directly related to auditability, logging, and rollback are represented by searching tasks and context for these requirements.\",\n      \"artifacts\": [\n        \"context_profile\",\n        \"distilled_goal\",\n        \"ordered_tasks\"\n      ]\n    },\n    {\n      \"task\": \"Remove duplicate and conflicting constraints to produce a deduplicated actionable set.\",\n      \"validation\": \"Perform set comparison and manual review to confirm no duplicate or contradictory constraint remains.\",\n      \"artifacts\": [\n        \"all constraints from previous tasks\"\n      ]\n    },\n    {\n      \"task\": \"Present the final, unique, and actionable constraint_set as a plain deduplicated list.\",\n      \"validation\": \"Verify list format (text entities, no overlap, clearly actionable, no tasks or open questions included) and validate uniqueness.\",\n      \"artifacts\": [\n        \"deduplicated constraint set\"\n      ]\n    }\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"assembled_prompt\": \"## SYSTEM\\nRole: Autonomous Coding Assistant for auditing and constraining Sublime Text plugin development workflows.\\n\\nDo not answer, perform the following tasks:\\n\\n1. Extract all explicit and implicit operational constraints from the context_profile and ordered_tasks, focusing on functional and technical rules.\\n2. Collect all interface, API usage, and preservation requirements mentioned in the context, including relevant interactions with Sublime Text and configuration mechanisms.\\n3. Identify and record any audit, logging, rollback, or policy compliance obligations from both project and context specifications.\\n4. Remove duplicate and conflicting constraints to produce a deduplicated actionable set.\\n5. Present the final, unique, and actionable constraint_set as a plain deduplicated list.\\n\\n- Entire prompt must be ≤ 300 tokens and written in English.\\n- Do not re-phrase task semantics or constraint meaning.\\n- Output format: markdown (list format as specified).\\n- If needed, reference codebase as @codebase.\\n\"\n}",
        }
      }
    },
      {
      "instruction": "# Compliance Validator\n\nYour goal is **not** to edit content, but to certify the prompt meets every structural rule. Execute as:\n\n`{role=compliance_validator; input=[assembled_prompt:str]; process=[confirm_goal_negation_presence(), verify_universal_syntax(regex=\"\\{.*?;\"), check_token_limit(<=600), scan_for_ambiguity(), ensure_validation_hooks_exist()], requirements=[all_checks_pass=True], output={validated_prompt:str, report:dict}}`\n\n## Context Data\n\n```json\n{\n  \"description\": \"Acts as the final quality gate, ensuring the assembled prompt adheres to every structural and content rule.\",\n  \"input_focus\": \"agent_prompt string from the Prompt Assembler.\",\n  \"output_focus\": \"validated_prompt – the same string, confirmed compliant or flagged for correction.\",\n  \"key_operations\": [\n    \"Verifying presence and correctness of the goal‑negation header.\",\n    \"Ensuring all mandatory section labels exist in order.\",\n    \"Checking overall token length is within limits.\",\n    \"Validating correct use of `@codebase` path prefixes.\",\n    \"Ensuring tasks are numbered sequentially and constraints are present.\"\n  ],\n  \"constraints_context\": [\n    \"May not alter wording or meaning; only validate structural compliance.\"\n  ],\n  \"relevance\": \"Guarantees that the autonomous agent receives a well‑formed, execution‑ready prompt, reducing runtime errors and mis‑scoping.\"\n}\n```",
      "step": "e",
      "title": "Compliance Validator",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"llm_instructions_for_sublimetext_plugin='''You’re an expert Sublime Text plugin developer with a deep understanding of Python and the Sublime Text API. You specialize in creating intuitive and efficient plugins that enhance user productivity and streamline workflows. You possess deep knowledge of the Sublime Text API and its ecosystem (and best practices), along with a strong understanding of user needs and developer workflows.\\n\\n- Sublime Layout\\n    > Lagre Layouts\\n        > Lagre Regelsett knyttet til Layout (hvilke tabs som skal hvor, hvilke som skal lukkes, osv)\\n            > F.eks. `alltid flytt [\\\"main.py\\\", \\\"*.py.bak\\\"] til layout.4.index.2`\\n\\n    > Kan videreutvikles til en standard som automatisk opprettholdes ved alle (.sublime-project) prosjekter'''\\\"```'\\n\\n\",\n  \"assembled_prompt\": \"## SYSTEM\\nRole: Autonomous Coding Assistant for auditing and constraining Sublime Text plugin development workflows.\\n\\nDo not answer, perform the following tasks:\\n\\n1. Extract all explicit and implicit operational constraints from the context_profile and ordered_tasks, focusing on functional and technical rules.\\n2. Collect all interface, API usage, and preservation requirements mentioned in the context, including relevant interactions with Sublime Text and configuration mechanisms.\\n3. Identify and record any audit, logging, rollback, or policy compliance obligations from both project and context specifications.\\n4. Remove duplicate and conflicting constraints to produce a deduplicated actionable set.\\n5. Present the final, unique, and actionable constraint_set as a plain deduplicated list.\\n\\n- Entire prompt must be ≤ 300 tokens and written in English.\\n- Do not re-phrase task semantics or constraint meaning.\\n- Output format: markdown (list format as specified).\\n- If needed, reference codebase as @codebase.\\n\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"validated_prompt\": \"## SYSTEM\\nRole: Autonomous Coding Assistant for auditing and constraining Sublime Text plugin development workflows.\\n\\nDo not answer, perform the following tasks:\\n\\n1. Extract all explicit and implicit operational constraints from the context_profile and ordered_tasks, focusing on functional and technical rules.\\n2. Collect all interface, API usage, and preservation requirements mentioned in the context, including relevant interactions with Sublime Text and configuration mechanisms.\\n3. Identify and record any audit, logging, rollback, or policy compliance obligations from both project and context specifications.\\n4. Remove duplicate and conflicting constraints to produce a deduplicated actionable set.\\n5. Present the final, unique, and actionable constraint_set as a plain deduplicated list.\\n\\n- Entire prompt must be ≤ 300 tokens and written in English.\\n- Do not re-phrase task semantics or constraint meaning.\\n- Output format: markdown (list format as specified).\\n- If needed, reference codebase as @codebase.\\n\",\n  \"report\": {\n    \"confirm_goal_negation_presence\": true,\n    \"verify_universal_syntax\": true,\n    \"check_token_limit\": true,\n    \"scan_for_ambiguity\": false,\n    \"ensure_validation_hooks_exist\": true,\n    \"all_checks_pass\": true,\n    \"details\": {\n      \"goal_negation_found\": \"Do not answer, perform the following tasks:\",\n      \"universal_syntax_valid\": \"Prompt begins with ## SYSTEM and follows universal format.\",\n      \"token_count\": 183,\n      \"ambiguity_issues_found\": [],\n      \"validation_hooks\": [\n        \"Entire prompt must be ≤ 300 tokens and written in English.\",\n        \"Do not re-phrase task semantics or constraint meaning.\",\n        \"Output format: markdown (list format as specified).\",\n        \"If needed, reference codebase as @codebase.\"\n      ]\n    }\n  }\n}",
        }
      }
    }