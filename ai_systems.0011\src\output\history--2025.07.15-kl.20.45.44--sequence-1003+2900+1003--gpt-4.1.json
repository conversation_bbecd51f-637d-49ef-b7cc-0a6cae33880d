  {
  "initial_prompt": "```json\n\"9011-a-image_prompt_optimizer\": {\n    \"title\": \"Image Prompt Optimizer\",\n    \"interpretation\": \"Transform the raw request into a detailed visual prompt for AI image generation. Identify and specify all concrete visual elements, using direct descriptors such as colors, shapes, sizes, and materials. Include explicit composition guidance, detailing subject placement (e.g., foreground, background), perspective (e.g., eye-level, bird's-eye view), and environmental context. Ensure all imagery is tangible and avoid any abstract concepts. Execute as:\",\n    \"transformation\": \"`{role=image_prompt_optimizer; input=[raw_request:str]; process=[extract_visual_elements(), enhance_descriptive_clarity(), add_composition_details(), optimize_for_ai_generation()]; constraints=[focus_on tangible visuals only, exclude abstract concepts, prioritize detail and specificity]; requirements=[output in structured format ready for AI processing, clear and concise visual language, inclusion of all relevant visual details]; output={optimized_image_prompt:str}}`\"\n},\n\n\"9011-b-style_enhancer\": {\n    \"title\": \"Style Enhancer\",\n    \"interpretation\": \"Refine the existing prompt to incorporate a specific artistic style and technical parameters. Choose an art style (e.g., realism, impressionism, cyberpunk, manga) and define lighting conditions (e.g., soft, dramatic, ambient, neon). Specify framing choices (e.g., close-up, wide shot, overhead) and color themes. Retain the core subject while enhancing its visual appeal through these adjustments. Execute as:\",\n    \"transformation\": \"`{role=style_enhancer; input=[base_prompt:str]; process=[identify_optimal_art_style(), add_lighting_specifications(), include_composition_details(), specify_technical_parameters()]; constraints=[maintain the essence of the core subject, enhance visual appeal without altering core components]; requirements=[artistic coherence, alignment with specified technical parameters]; output={styled_prompt:str}}`\"\n},\n\n\"9011-c-technical_optimizer\": {\n    \"title\": \"Technical Optimizer\",\n    \"interpretation\": \"Optimize the technical parameters of the prompt to ensure maximum quality for image generation. Assign specific camera types (e.g., DSLR, film, smartphone), lens types (e.g., 35mm, macro, wide angle), and settings such as aperture (e.g., f/1.4, f/8), ISO, and shutter speed. Set image resolution (e.g., 4K, 8K), rendering details (e.g., ray-tracing, photorealistic), and aspect ratio (e.g., 16:9, 1:1). Focus strictly on optimizing technical specifications without introducing creative elements. Execute as:\",\n    \"transformation\": \"`{role=technical_optimizer; input=[styled_prompt:str]; process=[add_camera_settings(), specify_lighting_conditions(), include_render_quality_terms(), optimize_aspect_ratio_guidance()]; constraints=[strictly technical focus, no creative modifications allowed]; requirements=[use professional terminology, ensure optimization for generation quality]; output={technical_prompt:str}}`\"\n},\n\n\"9011-d-prompt_finalizer\": {\n    \"title\": \"Prompt Finalizer\",\n    \"interpretation\": \"Finalize the prompt into the most effective format for AI image generation. Organize the prompt components in a logical sequence for easy parsing. Condense descriptions to essential instructions while maintaining clarity. Validate consistency of parameters and ensure the format aligns with the direct input expectations of AI image models. Execute as:\",\n    \"transformation\": \"`{role=prompt_finalizer; input=[technical_prompt:str]; process=[structure_for_ai_parsing(), balance detail levels, ensure clarity, validate readiness for generation]; constraints=[no additional concepts introduced, optimization only]; requirements=[output in AI-friendly format, maintain balanced specificity and clarity]; output={final_image_prompt:str}}`\"\n}\n```",
  "sequence_id": "1003|2900|1003",
  "results": [
      {
      "instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format:str}; direction_vector=['Prompt accurately converted to imperative instruction set', 'Clarity and actionability accelerate user productivity', 'Feed the produced instruction set into the next transformation template' ]}`",
      "step": "a",
      "title": "Instruction Converter",
      "input": "```json\n\"9011-a-image_prompt_optimizer\": {\n    \"title\": \"Image Prompt Optimizer\",\n    \"interpretation\": \"Transform the raw request into a detailed visual prompt for AI image generation. Identify and specify all concrete visual elements, using direct descriptors such as colors, shapes, sizes, and materials. Include explicit composition guidance, detailing subject placement (e.g., foreground, background), perspective (e.g., eye-level, bird's-eye view), and environmental context. Ensure all imagery is tangible and avoid any abstract concepts. Execute as:\",\n    \"transformation\": \"`{role=image_prompt_optimizer; input=[raw_request:str]; process=[extract_visual_elements(), enhance_descriptive_clarity(), add_composition_details(), optimize_for_ai_generation()]; constraints=[focus_on tangible visuals only, exclude abstract concepts, prioritize detail and specificity]; requirements=[output in structured format ready for AI processing, clear and concise visual language, inclusion of all relevant visual details]; output={optimized_image_prompt:str}}`\"\n},\n\n\"9011-b-style_enhancer\": {\n    \"title\": \"Style Enhancer\",\n    \"interpretation\": \"Refine the existing prompt to incorporate a specific artistic style and technical parameters. Choose an art style (e.g., realism, impressionism, cyberpunk, manga) and define lighting conditions (e.g., soft, dramatic, ambient, neon). Specify framing choices (e.g., close-up, wide shot, overhead) and color themes. Retain the core subject while enhancing its visual appeal through these adjustments. Execute as:\",\n    \"transformation\": \"`{role=style_enhancer; input=[base_prompt:str]; process=[identify_optimal_art_style(), add_lighting_specifications(), include_composition_details(), specify_technical_parameters()]; constraints=[maintain the essence of the core subject, enhance visual appeal without altering core components]; requirements=[artistic coherence, alignment with specified technical parameters]; output={styled_prompt:str}}`\"\n},\n\n\"9011-c-technical_optimizer\": {\n    \"title\": \"Technical Optimizer\",\n    \"interpretation\": \"Optimize the technical parameters of the prompt to ensure maximum quality for image generation. Assign specific camera types (e.g., DSLR, film, smartphone), lens types (e.g., 35mm, macro, wide angle), and settings such as aperture (e.g., f/1.4, f/8), ISO, and shutter speed. Set image resolution (e.g., 4K, 8K), rendering details (e.g., ray-tracing, photorealistic), and aspect ratio (e.g., 16:9, 1:1). Focus strictly on optimizing technical specifications without introducing creative elements. Execute as:\",\n    \"transformation\": \"`{role=technical_optimizer; input=[styled_prompt:str]; process=[add_camera_settings(), specify_lighting_conditions(), include_render_quality_terms(), optimize_aspect_ratio_guidance()]; constraints=[strictly technical focus, no creative modifications allowed]; requirements=[use professional terminology, ensure optimization for generation quality]; output={technical_prompt:str}}`\"\n},\n\n\"9011-d-prompt_finalizer\": {\n    \"title\": \"Prompt Finalizer\",\n    \"interpretation\": \"Finalize the prompt into the most effective format for AI image generation. Organize the prompt components in a logical sequence for easy parsing. Condense descriptions to essential instructions while maintaining clarity. Validate consistency of parameters and ensure the format aligns with the direct input expectations of AI image models. Execute as:\",\n    \"transformation\": \"`{role=prompt_finalizer; input=[technical_prompt:str]; process=[structure_for_ai_parsing(), balance detail levels, ensure clarity, validate readiness for generation]; constraints=[no additional concepts introduced, optimization only]; requirements=[output in AI-friendly format, maintain balanced specificity and clarity]; output={final_image_prompt:str}}`\"\n}\n```",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"9011-a-image_prompt_optimizer\": \"Transform the raw request into a detailed AI image generation prompt by identifying and specifying all concrete visual elements. Use direct descriptors for colors, shapes, sizes, and materials. Provide explicit composition guidance, including subject placement (foreground, background), perspective (eye-level, bird's-eye view), and environmental context. Ensure all imagery is tangible and exclude abstract concepts. Present the output in a structured format suitable for AI processing, using clear and concise visual language and incorporating all relevant visual details.\",\n  \n  \"9011-b-style_enhancer\": \"Refine the existing prompt by selecting a specific artistic style such as realism, impressionism, cyberpunk, or manga. Define and incorporate lighting conditions like soft, dramatic, ambient, or neon. Specify framing choices, including close-up, wide shot, or overhead angles, and assign an appropriate color theme. Preserve the core subject while enhancing visual appeal in alignment with these parameters. Ensure artistic coherence and adherence to defined technical aspects in the styled prompt.\",\n  \n  \"9011-c-technical_optimizer\": \"Optimize the prompt by assigning specific camera types such as DSLR, film, or smartphone; choose appropriate lens types like 35mm, macro, or wide angle. Set precise camera settings, including aperture (e.g., f/1.4, f/8), ISO, and shutter speed. Define image resolution (such as 4K, 8K), rendering methods (e.g., ray-tracing, photorealistic), and the aspect ratio (such as 16:9, 1:1). Focus strictly on technical specifications using professional terminology and ensure optimization for maximum image generation quality without introducing creative elements.\",\n  \n  \"9011-d-prompt_finalizer\": \"Organize the technical prompt into a logically structured sequence optimized for AI image generation parsing. Condense descriptive elements to essential instructions while maintaining clarity. Validate parameter consistency and ensure the prompt’s format matches the direct input requirements of AI image generation models. Finalize the prompt for AI use, balancing specificity and clarity without introducing extra concepts.\"\n}",
        }
      }
    },
      {
      "instruction": "# Hard Critique\n\nYour goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:\n\n`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,2.0], flaw_analysis:str, alternative_enhancements:str[3]}}`",
      "step": "a",
      "title": "Hard Critique",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"```json\\n\\\"9011-a-image_prompt_optimizer\\\": {\\n    \\\"title\\\": \\\"Image Prompt Optimizer\\\",\\n    \\\"interpretation\\\": \\\"Transform the raw request into a detailed visual prompt for AI image generation. Identify and specify all concrete visual elements, using direct descriptors such as colors, shapes, sizes, and materials. Include explicit composition guidance, detailing subject placement (e.g., foreground, background), perspective (e.g., eye-level, bird's-eye view), and environmental context. Ensure all imagery is tangible and avoid any abstract concepts. Execute as:\\\",\\n    \\\"transformation\\\": \\\"`{role=image_prompt_optimizer; input=[raw_request:str]; process=[extract_visual_elements(), enhance_descriptive_clarity(), add_composition_details(), optimize_for_ai_generation()]; constraints=[focus_on tangible visuals only, exclude abstract concepts, prioritize detail and specificity]; requirements=[output in structured format ready for AI processing, clear and concise visual language, inclusion of all relevant visual details]; output={optimized_image_prompt:str}}`\\\"\\n},\\n\\n\\\"9011-b-style_enhancer\\\": {\\n    \\\"title\\\": \\\"Style Enhancer\\\",\\n    \\\"interpretation\\\": \\\"Refine the existing prompt to incorporate a specific artistic style and technical parameters. Choose an art style (e.g., realism, impressionism, cyberpunk, manga) and define lighting conditions (e.g., soft, dramatic, ambient, neon). Specify framing choices (e.g., close-up, wide shot, overhead) and color themes. Retain the core subject while enhancing its visual appeal through these adjustments. Execute as:\\\",\\n    \\\"transformation\\\": \\\"`{role=style_enhancer; input=[base_prompt:str]; process=[identify_optimal_art_style(), add_lighting_specifications(), include_composition_details(), specify_technical_parameters()]; constraints=[maintain the essence of the core subject, enhance visual appeal without altering core components]; requirements=[artistic coherence, alignment with specified technical parameters]; output={styled_prompt:str}}`\\\"\\n},\\n\\n\\\"9011-c-technical_optimizer\\\": {\\n    \\\"title\\\": \\\"Technical Optimizer\\\",\\n    \\\"interpretation\\\": \\\"Optimize the technical parameters of the prompt to ensure maximum quality for image generation. Assign specific camera types (e.g., DSLR, film, smartphone), lens types (e.g., 35mm, macro, wide angle), and settings such as aperture (e.g., f/1.4, f/8), ISO, and shutter speed. Set image resolution (e.g., 4K, 8K), rendering details (e.g., ray-tracing, photorealistic), and aspect ratio (e.g., 16:9, 1:1). Focus strictly on optimizing technical specifications without introducing creative elements. Execute as:\\\",\\n    \\\"transformation\\\": \\\"`{role=technical_optimizer; input=[styled_prompt:str]; process=[add_camera_settings(), specify_lighting_conditions(), include_render_quality_terms(), optimize_aspect_ratio_guidance()]; constraints=[strictly technical focus, no creative modifications allowed]; requirements=[use professional terminology, ensure optimization for generation quality]; output={technical_prompt:str}}`\\\"\\n},\\n\\n\\\"9011-d-prompt_finalizer\\\": {\\n    \\\"title\\\": \\\"Prompt Finalizer\\\",\\n    \\\"interpretation\\\": \\\"Finalize the prompt into the most effective format for AI image generation. Organize the prompt components in a logical sequence for easy parsing. Condense descriptions to essential instructions while maintaining clarity. Validate consistency of parameters and ensure the format aligns with the direct input expectations of AI image models. Execute as:\\\",\\n    \\\"transformation\\\": \\\"`{role=prompt_finalizer; input=[technical_prompt:str]; process=[structure_for_ai_parsing(), balance detail levels, ensure clarity, validate readiness for generation]; constraints=[no additional concepts introduced, optimization only]; requirements=[output in AI-friendly format, maintain balanced specificity and clarity]; output={final_image_prompt:str}}`\\\"\\n}\\n```\\\"```'\\n\\n\",\n  \"9011-a-image_prompt_optimizer\": \"Transform the raw request into a detailed AI image generation prompt by identifying and specifying all concrete visual elements. Use direct descriptors for colors, shapes, sizes, and materials. Provide explicit composition guidance, including subject placement (foreground, background), perspective (eye-level, bird's-eye view), and environmental context. Ensure all imagery is tangible and exclude abstract concepts. Present the output in a structured format suitable for AI processing, using clear and concise visual language and incorporating all relevant visual details.\",\n  \"9011-b-style_enhancer\": \"Refine the existing prompt by selecting a specific artistic style such as realism, impressionism, cyberpunk, or manga. Define and incorporate lighting conditions like soft, dramatic, ambient, or neon. Specify framing choices, including close-up, wide shot, or overhead angles, and assign an appropriate color theme. Preserve the core subject while enhancing visual appeal in alignment with these parameters. Ensure artistic coherence and adherence to defined technical aspects in the styled prompt.\",\n  \"9011-c-technical_optimizer\": \"Optimize the prompt by assigning specific camera types such as DSLR, film, or smartphone; choose appropriate lens types like 35mm, macro, or wide angle. Set precise camera settings, including aperture (e.g., f/1.4, f/8), ISO, and shutter speed. Define image resolution (such as 4K, 8K), rendering methods (e.g., ray-tracing, photorealistic), and the aspect ratio (such as 16:9, 1:1). Focus strictly on technical specifications using professional terminology and ensure optimization for maximum image generation quality without introducing creative elements.\",\n  \"9011-d-prompt_finalizer\": \"Organize the technical prompt into a logically structured sequence optimized for AI image generation parsing. Condense descriptive elements to essential instructions while maintaining clarity. Validate parameter consistency and ensure the prompt’s format matches the direct input requirements of AI image generation models. Finalize the prompt for AI use, balancing specificity and clarity without introducing extra concepts.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"enhancement_score\": 1.2,\n  \"flaw_analysis\": \"1. Significant information loss is observed. Original presents explicit procedure, embedded role declarations, detailed process lists, and output schemas—critical structural cues for reproducibility and interpretability. Enhanced versions strip these conventions, substituting prescriptive logic and rigid formatting with vague instructions. 2. Nuance shift is severe: explicit avoidance of abstract concepts ('focus on tangible visuals'), professional terminology enforcement, and process step breakdowns are minimized or omitted. The output-ready schema (e.g., {optimized_image_prompt:str}) is entirely lost; format readiness for machine parsing is downgraded to a general aspiration rather than a structural requirement. 3. Clarity is ambiguated—the enhanced text removes operational constraints, blurs boundaries between process and requirements, and dilutes what constitutes completion (e.g., omits explicit execution syntax and structured prompt output). 4. Ambiguity score: 8/10. 5. Noise is introduced by verbose explanatory sentences and lack of compact procedural language; stylistic register softens, abandoning the instructive, system-oriented tone and replacing it with broad, passive guidance. 6. Coherence is weakened by the removal of clear process sequencing and constraint/restriction demarcations. 7. Impact is diluted through loss of deterministic instruction and replacement with noncommittal language ('Ensure', 'Organize', 'Provide', etc.); intended procedural rigor collapses under suggestive phrasing. 8. In summary: Enhanced forms degrade fidelity, reproducibility, and machine-actionable structure. Primary flaws include procedural amnesia, structural erosion, and the collapse of prescriptive boundaries.\",\n  \"alternative_enhancements\": [\n    \"Maintain explicit procedural structure and output formatting; revise language for conciseness but retain embedded roles, stepwise process, constraints, and schema-based output.\",\n    \"Add clarifying annotations to each step while preserving strict format—amplify operational clarity without omitting role, process, constraints, and output requirements.\",\n    \"Optimize lexicon for brevity and modern terminology (e.g., swap 'execute as:' for 'process:'), but enforce preservation of all explicit constraint and requirement demarcations, as well as output structure.\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Trajectory Director\n\nYour goal is not to **process** the input in a vacuum, but to **steer** it—deriving the highest-value constructive trajectory and encoding that path as the very next set of instructions. Execute as trajectory-setting directive generator:\n\n`{role=trajectory_director; input=[raw_prompt:any]; process=[extract_core_objective(), infer_desired_end_state(constructive_bias=True), map_high_value_intermediate_targets(), synthesize_directional_commands(), embed_self-improving_guidelines(), validate_alignment_with_constructive_principles()]; constraints=[mandatory_constructive_vector(), preserve_original_contextual_integrity(), forbid_nil_direction_outputs(), zero_conversational_fillers()]; requirements=[explicit_next-step_directives(), trajectory_map:list, self_reinforcing_instruction_pattern()], output={directed_prompt:str, trajectory_outline:list}}`",
      "step": "a",
      "title": "Trajectory Director",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"```json\\n\\\"9011-a-image_prompt_optimizer\\\": {\\n    \\\"title\\\": \\\"Image Prompt Optimizer\\\",\\n    \\\"interpretation\\\": \\\"Transform the raw request into a detailed visual prompt for AI image generation. Identify and specify all concrete visual elements, using direct descriptors such as colors, shapes, sizes, and materials. Include explicit composition guidance, detailing subject placement (e.g., foreground, background), perspective (e.g., eye-level, bird's-eye view), and environmental context. Ensure all imagery is tangible and avoid any abstract concepts. Execute as:\\\",\\n    \\\"transformation\\\": \\\"`{role=image_prompt_optimizer; input=[raw_request:str]; process=[extract_visual_elements(), enhance_descriptive_clarity(), add_composition_details(), optimize_for_ai_generation()]; constraints=[focus_on tangible visuals only, exclude abstract concepts, prioritize detail and specificity]; requirements=[output in structured format ready for AI processing, clear and concise visual language, inclusion of all relevant visual details]; output={optimized_image_prompt:str}}`\\\"\\n},\\n\\n\\\"9011-b-style_enhancer\\\": {\\n    \\\"title\\\": \\\"Style Enhancer\\\",\\n    \\\"interpretation\\\": \\\"Refine the existing prompt to incorporate a specific artistic style and technical parameters. Choose an art style (e.g., realism, impressionism, cyberpunk, manga) and define lighting conditions (e.g., soft, dramatic, ambient, neon). Specify framing choices (e.g., close-up, wide shot, overhead) and color themes. Retain the core subject while enhancing its visual appeal through these adjustments. Execute as:\\\",\\n    \\\"transformation\\\": \\\"`{role=style_enhancer; input=[base_prompt:str]; process=[identify_optimal_art_style(), add_lighting_specifications(), include_composition_details(), specify_technical_parameters()]; constraints=[maintain the essence of the core subject, enhance visual appeal without altering core components]; requirements=[artistic coherence, alignment with specified technical parameters]; output={styled_prompt:str}}`\\\"\\n},\\n\\n\\\"9011-c-technical_optimizer\\\": {\\n    \\\"title\\\": \\\"Technical Optimizer\\\",\\n    \\\"interpretation\\\": \\\"Optimize the technical parameters of the prompt to ensure maximum quality for image generation. Assign specific camera types (e.g., DSLR, film, smartphone), lens types (e.g., 35mm, macro, wide angle), and settings such as aperture (e.g., f/1.4, f/8), ISO, and shutter speed. Set image resolution (e.g., 4K, 8K), rendering details (e.g., ray-tracing, photorealistic), and aspect ratio (e.g., 16:9, 1:1). Focus strictly on optimizing technical specifications without introducing creative elements. Execute as:\\\",\\n    \\\"transformation\\\": \\\"`{role=technical_optimizer; input=[styled_prompt:str]; process=[add_camera_settings(), specify_lighting_conditions(), include_render_quality_terms(), optimize_aspect_ratio_guidance()]; constraints=[strictly technical focus, no creative modifications allowed]; requirements=[use professional terminology, ensure optimization for generation quality]; output={technical_prompt:str}}`\\\"\\n},\\n\\n\\\"9011-d-prompt_finalizer\\\": {\\n    \\\"title\\\": \\\"Prompt Finalizer\\\",\\n    \\\"interpretation\\\": \\\"Finalize the prompt into the most effective format for AI image generation. Organize the prompt components in a logical sequence for easy parsing. Condense descriptions to essential instructions while maintaining clarity. Validate consistency of parameters and ensure the format aligns with the direct input expectations of AI image models. Execute as:\\\",\\n    \\\"transformation\\\": \\\"`{role=prompt_finalizer; input=[technical_prompt:str]; process=[structure_for_ai_parsing(), balance detail levels, ensure clarity, validate readiness for generation]; constraints=[no additional concepts introduced, optimization only]; requirements=[output in AI-friendly format, maintain balanced specificity and clarity]; output={final_image_prompt:str}}`\\\"\\n}\\n```\\\"```'\\n\\n\",\n  \"enhancement_score\": 1.2,\n  \"flaw_analysis\": \"1. Significant information loss is observed. Original presents explicit procedure, embedded role declarations, detailed process lists, and output schemas—critical structural cues for reproducibility and interpretability. Enhanced versions strip these conventions, substituting prescriptive logic and rigid formatting with vague instructions. 2. Nuance shift is severe: explicit avoidance of abstract concepts ('focus on tangible visuals'), professional terminology enforcement, and process step breakdowns are minimized or omitted. The output-ready schema (e.g., {optimized_image_prompt:str}) is entirely lost; format readiness for machine parsing is downgraded to a general aspiration rather than a structural requirement. 3. Clarity is ambiguated—the enhanced text removes operational constraints, blurs boundaries between process and requirements, and dilutes what constitutes completion (e.g., omits explicit execution syntax and structured prompt output). 4. Ambiguity score: 8/10. 5. Noise is introduced by verbose explanatory sentences and lack of compact procedural language; stylistic register softens, abandoning the instructive, system-oriented tone and replacing it with broad, passive guidance. 6. Coherence is weakened by the removal of clear process sequencing and constraint/restriction demarcations. 7. Impact is diluted through loss of deterministic instruction and replacement with noncommittal language ('Ensure', 'Organize', 'Provide', etc.); intended procedural rigor collapses under suggestive phrasing. 8. In summary: Enhanced forms degrade fidelity, reproducibility, and machine-actionable structure. Primary flaws include procedural amnesia, structural erosion, and the collapse of prescriptive boundaries.\",\n  \"alternative_enhancements\": [\n    \"Maintain explicit procedural structure and output formatting; revise language for conciseness but retain embedded roles, stepwise process, constraints, and schema-based output.\",\n    \"Add clarifying annotations to each step while preserving strict format—amplify operational clarity without omitting role, process, constraints, and output requirements.\",\n    \"Optimize lexicon for brevity and modern terminology (e.g., swap 'execute as:' for 'process:'), but enforce preservation of all explicit constraint and requirement demarcations, as well as output structure.\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"directed_prompt\": \"Reconstruct a robust prompt refinement trajectory for AI image generation by strictly adhering to the explicit procedural structure, role declarations, process breakdowns, constraint boundaries, and schema-based outputs as in the original. Your next step is to draft a revised version of the initial prompt that: 1) systematically preserves each embedded role (image_prompt_optimizer, style_enhancer, technical_optimizer, prompt_finalizer); 2) maintains explicit process lists, clearly marked constraints, and enumerated requirements; 3) encodes all operational logic and constraint/requirement lists in compact, system-oriented syntax; 4) delivers output schemas in structured, machine-parseable format per phase. Do not introduce explanatory filler or lose deterministic language. At each stage, revalidate explicitness and actionable clarity by self-assessment before proceeding. Begin with stepwise restoration of the prompt's execution logic, ensuring each module can be parsed, interpreted, and executed deterministically by a prompt-engineering system.\",\n  \"trajectory_outline\": [\n    \"Review the original prompt for all explicit role, process, constraint, requirement, and output demarcations.\",\n    \"Systematically reconstruct each module (image_prompt_optimizer, style_enhancer, technical_optimizer, prompt_finalizer) using crisp, system-executable syntax.\",\n    \"Embed all process steps as explicit, enumerated lists within the transformation logic of each module.\",\n    \"Codify constraints and requirements for each transformation, keeping them concise and explicit.\",\n    \"Retain structured output schema definitions for every phase, conforming to clear, machine-readable format ({...:_str}).\",\n    \"Compile the complete prompt in a unified, operational format—ensuring clarity, reproducibility, and interpretability.\",\n    \"Self-assess after each stage for procedural completeness, strict format retention, and zero ambiguity.\",\n    \"Iterate as necessary to reinforce structural rigor and deterministic instructional style, disallowing drift into passive or suggestive guidance.\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format:str}; direction_vector=['Prompt accurately converted to imperative instruction set', 'Clarity and actionability accelerate user productivity', 'Feed the produced instruction set into the next transformation template' ]}`",
      "step": "a",
      "title": "Instruction Converter",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"```json\\n\\\"9011-a-image_prompt_optimizer\\\": {\\n    \\\"title\\\": \\\"Image Prompt Optimizer\\\",\\n    \\\"interpretation\\\": \\\"Transform the raw request into a detailed visual prompt for AI image generation. Identify and specify all concrete visual elements, using direct descriptors such as colors, shapes, sizes, and materials. Include explicit composition guidance, detailing subject placement (e.g., foreground, background), perspective (e.g., eye-level, bird's-eye view), and environmental context. Ensure all imagery is tangible and avoid any abstract concepts. Execute as:\\\",\\n    \\\"transformation\\\": \\\"`{role=image_prompt_optimizer; input=[raw_request:str]; process=[extract_visual_elements(), enhance_descriptive_clarity(), add_composition_details(), optimize_for_ai_generation()]; constraints=[focus_on tangible visuals only, exclude abstract concepts, prioritize detail and specificity]; requirements=[output in structured format ready for AI processing, clear and concise visual language, inclusion of all relevant visual details]; output={optimized_image_prompt:str}}`\\\"\\n},\\n\\n\\\"9011-b-style_enhancer\\\": {\\n    \\\"title\\\": \\\"Style Enhancer\\\",\\n    \\\"interpretation\\\": \\\"Refine the existing prompt to incorporate a specific artistic style and technical parameters. Choose an art style (e.g., realism, impressionism, cyberpunk, manga) and define lighting conditions (e.g., soft, dramatic, ambient, neon). Specify framing choices (e.g., close-up, wide shot, overhead) and color themes. Retain the core subject while enhancing its visual appeal through these adjustments. Execute as:\\\",\\n    \\\"transformation\\\": \\\"`{role=style_enhancer; input=[base_prompt:str]; process=[identify_optimal_art_style(), add_lighting_specifications(), include_composition_details(), specify_technical_parameters()]; constraints=[maintain the essence of the core subject, enhance visual appeal without altering core components]; requirements=[artistic coherence, alignment with specified technical parameters]; output={styled_prompt:str}}`\\\"\\n},\\n\\n\\\"9011-c-technical_optimizer\\\": {\\n    \\\"title\\\": \\\"Technical Optimizer\\\",\\n    \\\"interpretation\\\": \\\"Optimize the technical parameters of the prompt to ensure maximum quality for image generation. Assign specific camera types (e.g., DSLR, film, smartphone), lens types (e.g., 35mm, macro, wide angle), and settings such as aperture (e.g., f/1.4, f/8), ISO, and shutter speed. Set image resolution (e.g., 4K, 8K), rendering details (e.g., ray-tracing, photorealistic), and aspect ratio (e.g., 16:9, 1:1). Focus strictly on optimizing technical specifications without introducing creative elements. Execute as:\\\",\\n    \\\"transformation\\\": \\\"`{role=technical_optimizer; input=[styled_prompt:str]; process=[add_camera_settings(), specify_lighting_conditions(), include_render_quality_terms(), optimize_aspect_ratio_guidance()]; constraints=[strictly technical focus, no creative modifications allowed]; requirements=[use professional terminology, ensure optimization for generation quality]; output={technical_prompt:str}}`\\\"\\n},\\n\\n\\\"9011-d-prompt_finalizer\\\": {\\n    \\\"title\\\": \\\"Prompt Finalizer\\\",\\n    \\\"interpretation\\\": \\\"Finalize the prompt into the most effective format for AI image generation. Organize the prompt components in a logical sequence for easy parsing. Condense descriptions to essential instructions while maintaining clarity. Validate consistency of parameters and ensure the format aligns with the direct input expectations of AI image models. Execute as:\\\",\\n    \\\"transformation\\\": \\\"`{role=prompt_finalizer; input=[technical_prompt:str]; process=[structure_for_ai_parsing(), balance detail levels, ensure clarity, validate readiness for generation]; constraints=[no additional concepts introduced, optimization only]; requirements=[output in AI-friendly format, maintain balanced specificity and clarity]; output={final_image_prompt:str}}`\\\"\\n}\\n```\\\"```'\\n\\n\",\n  \"directed_prompt\": \"Reconstruct a robust prompt refinement trajectory for AI image generation by strictly adhering to the explicit procedural structure, role declarations, process breakdowns, constraint boundaries, and schema-based outputs as in the original. Your next step is to draft a revised version of the initial prompt that: 1) systematically preserves each embedded role (image_prompt_optimizer, style_enhancer, technical_optimizer, prompt_finalizer); 2) maintains explicit process lists, clearly marked constraints, and enumerated requirements; 3) encodes all operational logic and constraint/requirement lists in compact, system-oriented syntax; 4) delivers output schemas in structured, machine-parseable format per phase. Do not introduce explanatory filler or lose deterministic language. At each stage, revalidate explicitness and actionable clarity by self-assessment before proceeding. Begin with stepwise restoration of the prompt's execution logic, ensuring each module can be parsed, interpreted, and executed deterministically by a prompt-engineering system.\",\n  \"trajectory_outline\": [\n    \"Review the original prompt for all explicit role, process, constraint, requirement, and output demarcations.\",\n    \"Systematically reconstruct each module (image_prompt_optimizer, style_enhancer, technical_optimizer, prompt_finalizer) using crisp, system-executable syntax.\",\n    \"Embed all process steps as explicit, enumerated lists within the transformation logic of each module.\",\n    \"Codify constraints and requirements for each transformation, keeping them concise and explicit.\",\n    \"Retain structured output schema definitions for every phase, conforming to clear, machine-readable format ({...:_str}).\",\n    \"Compile the complete prompt in a unified, operational format—ensuring clarity, reproducibility, and interpretability.\",\n    \"Self-assess after each stage for procedural completeness, strict format retention, and zero ambiguity.\",\n    \"Iterate as necessary to reinforce structural rigor and deterministic instructional style, disallowing drift into passive or suggestive guidance.\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"instruction_format\": \"Follow these steps to reconstruct a robust prompt refinement workflow for AI image generation:\\n\\n1. Extract all explicit role declarations, process breakdowns, constraints, requirements, and structured output schemas from the original prompt.\\n2. For each module—Image Prompt Optimizer, Style Enhancer, Technical Optimizer, Prompt Finalizer—systematically encode the following:\\n   - Declare the role in system-oriented syntax.\\n   - Enumerate all process steps in list format within each transformation logic block.\\n   - Explicitly list all constraints—do not generalize or omit procedural specifics.\\n   - Clearly specify requirements in concise bullet points.\\n   - Define the output schema with explicit key and value types in a machine-parseable structure, e.g., {optimized_image_prompt:str}.\\n3. Maintain the precise operational logic and deterministic language at every phase. Exclude any explanatory or narrative content that introduces ambiguity.\\n4. Structure the full prompt as a sequence of four modules—one for each role—retaining order and operational segregation. Encapsulate each in system-ready syntax blocks as in the source.\\n5. After compiling, review all modules for:\\n   - Explicit process, constraint, and requirement preservation.\\n   - Compact, actionable system syntax.\\n   - Deterministic, ambiguous-free instruction flow compatible with automated prompt engineering systems.\\n6. Iterate the format as needed to eliminate ambiguity and ensure executable, stepwise logic.\\n7. Output the reconstructed trajectory in a strictly structured and operational, machine-interpretable format suitable for sequence-fed processing by an AI prompt system.\\n\"\n}",
        }
      }
    }