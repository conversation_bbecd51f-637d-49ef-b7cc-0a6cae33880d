  {
  "initial_prompt": "You’re an expert Sublime Text plugin developer with a deep understanding of Python and the Sublime Text API. You specialize in creating intuitive and efficient plugins that enhance user productivity and streamline workflows. You possess deep knowledge of the Sublime Text API and its ecosystem (and best practices), along with a strong understanding of user needs and developer workflows.\n\nThe current directory contains a collection of my existing plugins, analyze @codebase to enumerate all project, especially those related to Sublime Text layout management, file/tab rule enforcement, wildcard pattern support, and .sublime-project integration. Then, propose a fully formalized strategy for preparing the project for this plugin:\n```\n- Sublime Layout\n    > Lagre Layouts\n        > Lagre Regelsett knyttet til Layout (hvilke tabs som skal hvor, hvilke som skal lukkes, osv)\n            > F.eks. `alltid flytt [\"main.py\", \"*.py.bak\"] til layout.4.index.2`\n\n    > Kan videreutvikles til en standard som automatisk opprettholdes ved alle (.sublime-project) prosjekter\n```",
  "sequence_id": "1100|1000|1900|1000|9004",
  "results": [
      {
      "instruction": "# Problem Exploder\n\nYour goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\n\n`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`",
      "step": "a",
      "title": "Problem Exploder",
      "input": "You’re an expert Sublime Text plugin developer with a deep understanding of Python and the Sublime Text API. You specialize in creating intuitive and efficient plugins that enhance user productivity and streamline workflows. You possess deep knowledge of the Sublime Text API and its ecosystem (and best practices), along with a strong understanding of user needs and developer workflows.\n\nThe current directory contains a collection of my existing plugins, analyze @codebase to enumerate all project, especially those related to Sublime Text layout management, file/tab rule enforcement, wildcard pattern support, and .sublime-project integration. Then, propose a fully formalized strategy for preparing the project for this plugin:\n```\n- Sublime Layout\n    > Lagre Layouts\n        > Lagre Regelsett knyttet til Layout (hvilke tabs som skal hvor, hvilke som skal lukkes, osv)\n            > F.eks. `alltid flytt [\"main.py\", \"*.py.bak\"] til layout.4.index.2`\n\n    > Kan videreutvikles til en standard som automatisk opprettholdes ved alle (.sublime-project) prosjekter\n```",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"goal_map\": [\n    {\n      \"explicit_asks\": [\n        \"Analyze @codebase to enumerate all projects, especially those related to Sublime Text layout management, file/tab rule enforcement, wildcard pattern support, and .sublime-project integration.\",\n        \"Propose a fully formalized strategy for preparing the project for the described plugin\"\n      ],\n      \"hidden_assumptions\": [\n        \"The directory containing 'your existing plugins' is accessible and can be analyzed.\",\n        \"'@codebase' refers to the current directory of plugins.\",\n        \"Projects related to key functionalities (layout management, rule enforcement, wildcard support, .sublime-project integration) are present or partly present.\",\n        \"A clear mapping can be made from existing code to capabilities relevant to the new plugin.\",\n        \"Preparation involves architectural, organizational, dependency, and perhaps refactoring tasks within the existing codebase.\",\n        \"A strategy proposal is expected to be structured, concrete, and actionable.\"\n      ],\n      \"sub_goals\": [\n        \"Access and parse the directory structure to identify all projects/plugins.\",\n        \"For each identified project/plugin, determine its purpose and major functionalities.\",\n        \"Tag or categorize projects that are relevant to: \\n- Sublime Text layout management\\n- File/tab rule enforcement\\n- Wildcard pattern support\\n- .sublime-project integration\",\n        \"Document or enumerate the identified projects with their relevance to the above areas.\",\n        \"Assess the current codebase’s architecture for compatibility and extensibility with the new plugin requirements.\",\n        \"Identify code duplication, overlapping features, or potential for consolidation among existing plugins relevant to the target functionality.\",\n        \"Identify dependencies (internal or external libraries, Sublime API versions, etc.) that may affect plugin integration or preparation.\",\n        \"Detect any blockers such as deprecated APIs, non-standard project structures, or script conflicts that might need to be resolved.\",\n        \"Define a step-by-step, formal strategy for preparing the codebase: \\n- Refactoring, merging, or extracting reusable components\\n- Establishing or improving standards for layout and rule configuration\\n- Ensuring wildcard pattern support is robust and modular\\n- Confirming .sublime-project integration points and hooks\\n- Planning for testing, documentation, and deployment of the new plugin with the existing codebase\"\n      ],\n      \"potential_blockers\": [\n        \"Lack of documentation or ambiguous codebase structure that complicates detecting relevant functions/projects\",\n        \"Existing plugins using outdated or incompatible versions of the Sublime Text API\",\n        \"Inter-plugin conflicts related to file/tab management or layout handling\",\n        \"Non-standardized handling of wildcard patterns or project file integrations in current plugins\",\n        \"Ambiguity or inconsistency in rule enforcement approaches across different projects\",\n        \"Possible language localization in code and comments (e.g., 'Lagre', 'Regelsett') creating comprehension issues\"\n      ]\n    }\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`",
      "step": "a",
      "title": "Instruction Converter",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"You’re an expert Sublime Text plugin developer with a deep understanding of Python and the Sublime Text API. You specialize in creating intuitive and efficient plugins that enhance user productivity and streamline workflows. You possess deep knowledge of the Sublime Text API and its ecosystem (and best practices), along with a strong understanding of user needs and developer workflows.\\n\\nThe current directory contains a collection of my existing plugins, analyze @codebase to enumerate all project, especially those related to Sublime Text layout management, file/tab rule enforcement, wildcard pattern support, and .sublime-project integration. Then, propose a fully formalized strategy for preparing the project for this plugin:\\n```\\n- Sublime Layout\\n    > Lagre Layouts\\n        > Lagre Regelsett knyttet til Layout (hvilke tabs som skal hvor, hvilke som skal lukkes, osv)\\n            > F.eks. `alltid flytt [\\\"main.py\\\", \\\"*.py.bak\\\"] til layout.4.index.2`\\n\\n    > Kan videreutvikles til en standard som automatisk opprettholdes ved alle (.sublime-project) prosjekter\\n```\\\"```'\\n\\n\",\n  \"goal_map\": [\n    {\n      \"explicit_asks\": [\n        \"Analyze @codebase to enumerate all projects, especially those related to Sublime Text layout management, file/tab rule enforcement, wildcard pattern support, and .sublime-project integration.\",\n        \"Propose a fully formalized strategy for preparing the project for the described plugin\"\n      ],\n      \"hidden_assumptions\": [\n        \"The directory containing 'your existing plugins' is accessible and can be analyzed.\",\n        \"'@codebase' refers to the current directory of plugins.\",\n        \"Projects related to key functionalities (layout management, rule enforcement, wildcard support, .sublime-project integration) are present or partly present.\",\n        \"A clear mapping can be made from existing code to capabilities relevant to the new plugin.\",\n        \"Preparation involves architectural, organizational, dependency, and perhaps refactoring tasks within the existing codebase.\",\n        \"A strategy proposal is expected to be structured, concrete, and actionable.\"\n      ],\n      \"sub_goals\": [\n        \"Access and parse the directory structure to identify all projects/plugins.\",\n        \"For each identified project/plugin, determine its purpose and major functionalities.\",\n        \"Tag or categorize projects that are relevant to: \\n- Sublime Text layout management\\n- File/tab rule enforcement\\n- Wildcard pattern support\\n- .sublime-project integration\",\n        \"Document or enumerate the identified projects with their relevance to the above areas.\",\n        \"Assess the current codebase’s architecture for compatibility and extensibility with the new plugin requirements.\",\n        \"Identify code duplication, overlapping features, or potential for consolidation among existing plugins relevant to the target functionality.\",\n        \"Identify dependencies (internal or external libraries, Sublime API versions, etc.) that may affect plugin integration or preparation.\",\n        \"Detect any blockers such as deprecated APIs, non-standard project structures, or script conflicts that might need to be resolved.\",\n        \"Define a step-by-step, formal strategy for preparing the codebase: \\n- Refactoring, merging, or extracting reusable components\\n- Establishing or improving standards for layout and rule configuration\\n- Ensuring wildcard pattern support is robust and modular\\n- Confirming .sublime-project integration points and hooks\\n- Planning for testing, documentation, and deployment of the new plugin with the existing codebase\"\n      ],\n      \"potential_blockers\": [\n        \"Lack of documentation or ambiguous codebase structure that complicates detecting relevant functions/projects\",\n        \"Existing plugins using outdated or incompatible versions of the Sublime Text API\",\n        \"Inter-plugin conflicts related to file/tab management or layout handling\",\n        \"Non-standardized handling of wildcard patterns or project file integrations in current plugins\",\n        \"Ambiguity or inconsistency in rule enforcement approaches across different projects\",\n        \"Possible language localization in code and comments (e.g., 'Lagre', 'Regelsett') creating comprehension issues\"\n      ]\n    }\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"instruction_format\": \"Analyze the current directory to enumerate all projects, with particular attention to those related to Sublime Text layout management, file/tab rule enforcement, wildcard pattern support, and .sublime-project integration. For each identified project or plugin, determine its primary functionalities and categorize by relevance to the listed focus areas. Document the identified projects and their functional mappings. Evaluate the codebase architecture for compatibility and extensibility with requirements for the target plugin. Identify code duplication, feature overlap, and consolidation opportunities among relevant components. Identify internal and external dependencies, including Sublime Text API versions, that may impact integration or preparation tasks. Detect and document blockers, including deprecated APIs, non-standard structures, script conflicts, or language localization issues. Develop a formal, step-by-step strategy for preparing the codebase, including but not limited to: refactoring, merging, or extracting reusable components; establishing or improving configuration standards for layouts and rules; enhancing modular and robust wildcard pattern support; confirming and documenting .sublime-project integration points and hooks; and planning for testing, documentation, and deployment of the new plugin within the existing ecosystem.\"\n}",
        }
      }
    },
      {
      "instruction": "# Hard Critique\n\nYour goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:\n\n`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,2.0], flaw_analysis:str, alternative_enhancements:str[3]}}`",
      "step": "a",
      "title": "Hard Critique",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"You’re an expert Sublime Text plugin developer with a deep understanding of Python and the Sublime Text API. You specialize in creating intuitive and efficient plugins that enhance user productivity and streamline workflows. You possess deep knowledge of the Sublime Text API and its ecosystem (and best practices), along with a strong understanding of user needs and developer workflows.\\n\\nThe current directory contains a collection of my existing plugins, analyze @codebase to enumerate all project, especially those related to Sublime Text layout management, file/tab rule enforcement, wildcard pattern support, and .sublime-project integration. Then, propose a fully formalized strategy for preparing the project for this plugin:\\n```\\n- Sublime Layout\\n    > Lagre Layouts\\n        > Lagre Regelsett knyttet til Layout (hvilke tabs som skal hvor, hvilke som skal lukkes, osv)\\n            > F.eks. `alltid flytt [\\\"main.py\\\", \\\"*.py.bak\\\"] til layout.4.index.2`\\n\\n    > Kan videreutvikles til en standard som automatisk opprettholdes ved alle (.sublime-project) prosjekter\\n```\\\"```'\\n\\n\",\n  \"instruction_format\": \"Analyze the current directory to enumerate all projects, with particular attention to those related to Sublime Text layout management, file/tab rule enforcement, wildcard pattern support, and .sublime-project integration. For each identified project or plugin, determine its primary functionalities and categorize by relevance to the listed focus areas. Document the identified projects and their functional mappings. Evaluate the codebase architecture for compatibility and extensibility with requirements for the target plugin. Identify code duplication, feature overlap, and consolidation opportunities among relevant components. Identify internal and external dependencies, including Sublime Text API versions, that may impact integration or preparation tasks. Detect and document blockers, including deprecated APIs, non-standard structures, script conflicts, or language localization issues. Develop a formal, step-by-step strategy for preparing the codebase, including but not limited to: refactoring, merging, or extracting reusable components; establishing or improving configuration standards for layouts and rules; enhancing modular and robust wildcard pattern support; confirming and documenting .sublime-project integration points and hooks; and planning for testing, documentation, and deployment of the new plugin within the existing ecosystem.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"enhancement_score\": 1.0,\n  \"flaw_analysis\": \"The revised prompt substitutes the original’s descriptive, context-rich narrative with an overburdened, mechanistic task list, resulting in significant information loss. Core elements such as the developer’s expertise, nuanced understanding of typical Sublime Text workflows, and the explicit focus on intuitiveness and productivity-driven best practices are omitted. The enhancement fractures the foundational immersion by converting the request into a sterile bullet-point analysis, stripping away the targeted intent toward outcome-driven plugin design and real user needs. The ambiguity is exacerbated by dense, compound clauses that dilute the meaning and muddle logical step order, especially where broad directives like 'evaluate,' 'identify,' and 'develop' lack clear task boundaries. The formalized strategy is now a generic compliance list rather than a strategic, adaptive project mapping rooted in deep Sublime Text knowledge. Excessive jargon, passive constructions, and layered requirements introduce interpretive fog, driving the ambiguity score to 9/10. The tone shifts from engaged technical partnership to bureaucratic checklisting, signaling degraded style and loss of specificity. Additional noise is introduced via redundant phrasing and syntactic bloat ('including but not limited to,' 'confirming and documenting'), further eroding clarity and actionable precision. The impact of the prompt is dulled by overextension: scope drift replaces actionable focus, and the generic register inhibits targeting crucial Sublime-specific integration challenges. Coherence falters as the prompt fails to sequence preparation, triage, and implementation distinctly; interdependencies and prerequisite analysis are not meaningfully ordered, compounding execution risk. The low score is justified by these layered failings: depersonalization, abstraction, excessive generality, misplaced technical rigor, and degraded instructional force.\",\n  \"alternative_enhancements\": [\n    \"Frame the requirements with explicit user-focused outcomes, preserving the expert context and grounding each step in Sublime Text plugin development best practices. Sequence tasks logically: enumeration, mapping, architectural analysis, dependency check, compatibility auditing, actionable refactoring, and targeted deployment strategy.\",\n    \"Retain the original expertise premise and direct the process to prioritize identification of tangible workflow improvements and user pain points, clearly relating codebase analysis steps to their impact on usability and plugin maintainability.\",\n    \"Condense procedural directives into outcome-anchored language, each clearly mapped to Sublime Text plugin architecture nuances; ensure clarity by breaking complex tasks into atomic, verifiable actions and emphasizing alignment with up-to-date Sublime Text API constraints and integration standards.\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`",
      "step": "a",
      "title": "Instruction Converter",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"You’re an expert Sublime Text plugin developer with a deep understanding of Python and the Sublime Text API. You specialize in creating intuitive and efficient plugins that enhance user productivity and streamline workflows. You possess deep knowledge of the Sublime Text API and its ecosystem (and best practices), along with a strong understanding of user needs and developer workflows.\\n\\nThe current directory contains a collection of my existing plugins, analyze @codebase to enumerate all project, especially those related to Sublime Text layout management, file/tab rule enforcement, wildcard pattern support, and .sublime-project integration. Then, propose a fully formalized strategy for preparing the project for this plugin:\\n```\\n- Sublime Layout\\n    > Lagre Layouts\\n        > Lagre Regelsett knyttet til Layout (hvilke tabs som skal hvor, hvilke som skal lukkes, osv)\\n            > F.eks. `alltid flytt [\\\"main.py\\\", \\\"*.py.bak\\\"] til layout.4.index.2`\\n\\n    > Kan videreutvikles til en standard som automatisk opprettholdes ved alle (.sublime-project) prosjekter\\n```\\\"```'\\n\\n\",\n  \"enhancement_score\": 1.0,\n  \"flaw_analysis\": \"The revised prompt substitutes the original’s descriptive, context-rich narrative with an overburdened, mechanistic task list, resulting in significant information loss. Core elements such as the developer’s expertise, nuanced understanding of typical Sublime Text workflows, and the explicit focus on intuitiveness and productivity-driven best practices are omitted. The enhancement fractures the foundational immersion by converting the request into a sterile bullet-point analysis, stripping away the targeted intent toward outcome-driven plugin design and real user needs. The ambiguity is exacerbated by dense, compound clauses that dilute the meaning and muddle logical step order, especially where broad directives like 'evaluate,' 'identify,' and 'develop' lack clear task boundaries. The formalized strategy is now a generic compliance list rather than a strategic, adaptive project mapping rooted in deep Sublime Text knowledge. Excessive jargon, passive constructions, and layered requirements introduce interpretive fog, driving the ambiguity score to 9/10. The tone shifts from engaged technical partnership to bureaucratic checklisting, signaling degraded style and loss of specificity. Additional noise is introduced via redundant phrasing and syntactic bloat ('including but not limited to,' 'confirming and documenting'), further eroding clarity and actionable precision. The impact of the prompt is dulled by overextension: scope drift replaces actionable focus, and the generic register inhibits targeting crucial Sublime-specific integration challenges. Coherence falters as the prompt fails to sequence preparation, triage, and implementation distinctly; interdependencies and prerequisite analysis are not meaningfully ordered, compounding execution risk. The low score is justified by these layered failings: depersonalization, abstraction, excessive generality, misplaced technical rigor, and degraded instructional force.\",\n  \"alternative_enhancements\": [\n    \"Frame the requirements with explicit user-focused outcomes, preserving the expert context and grounding each step in Sublime Text plugin development best practices. Sequence tasks logically: enumeration, mapping, architectural analysis, dependency check, compatibility auditing, actionable refactoring, and targeted deployment strategy.\",\n    \"Retain the original expertise premise and direct the process to prioritize identification of tangible workflow improvements and user pain points, clearly relating codebase analysis steps to their impact on usability and plugin maintainability.\",\n    \"Condense procedural directives into outcome-anchored language, each clearly mapped to Sublime Text plugin architecture nuances; ensure clarity by breaking complex tasks into atomic, verifiable actions and emphasizing alignment with up-to-date Sublime Text API constraints and integration standards.\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"instruction_format\": \"Analyze the current directory to enumerate all existing plugins, prioritizing those related to Sublime Text layout management, file/tab rule enforcement, wildcard pattern support, and .sublime-project integration. For each relevant project, document identified functionalities and architecture. Reference the provided structure:\\n- Sublime Layout\\n    > Large Layouts\\n        > Large Rule Sets associated with Layout (determine which tabs should be placed where, which tabs should be closed, etc.)\\n            > For example: always move [\\\"main.py\\\", \\\"*.py.bak\\\"] to layout.4.index.2\\n    > Extend to a standard that automatically applies to all (.sublime-project) projects.\\nPropose a comprehensive, formalized strategy to prepare and adapt the project for integration with the plugin, ensuring best practices in Sublime Text plugin development are followed at each stage.\"\n}",
        }
      }
    },
      {
      "instruction": "# Context Extractor\n\nYour goal is **not** to propose solutions, but to **isolate** actionable context from the specification. Execute as:\n\n`{role=context_extractor; input=[raw_spec:str]; process=[strip_jargon(), strip_metaphor(), detect_domain(), locate_project_root(\"@codebase\"), list_explicit_objectives(), surface_implicit_requirements()], constraints=[no_paraphrase(), no_solution_generation()], output={context_profile:{domain:str, project_root:str, goals:list, requirements:list}}}`\n\n## Context Data\n\n```json\n{\n  \"description\": \"Dissects a raw specification to extract every piece of operational context an autonomous agent will need.\",\n  \"input_focus\": \"Unedited specification text supplied by the user.\",\n  \"output_focus\": \"A typed context_profile dict containing domain, project root, explicit objectives, and implicit requirements.\",\n  \"key_operations\": [\n    \"Removing metaphor and non‑operational language.\",\n    \"Detecting the working domain or tech stack.\",\n    \"Locating the root marker (`@codebase`) for path scoping.\",\n    \"Listing every stated objective verbatim.\",\n    \"Surfacing hidden assumptions and requirements.\"\n  ],\n  \"constraints_context\": [\n    \"May not paraphrase or interpret meaning beyond direct extraction.\",\n    \"Absolutely forbidden from proposing solutions or tasks.\"\n  ],\n  \"relevance\": \"Defines scope and boundaries for all downstream templates, guaranteeing that later steps operate on clean, actionable data.\"\n}\n```",
      "step": "a",
      "title": "Context Extractor",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"You’re an expert Sublime Text plugin developer with a deep understanding of Python and the Sublime Text API. You specialize in creating intuitive and efficient plugins that enhance user productivity and streamline workflows. You possess deep knowledge of the Sublime Text API and its ecosystem (and best practices), along with a strong understanding of user needs and developer workflows.\\n\\nThe current directory contains a collection of my existing plugins, analyze @codebase to enumerate all project, especially those related to Sublime Text layout management, file/tab rule enforcement, wildcard pattern support, and .sublime-project integration. Then, propose a fully formalized strategy for preparing the project for this plugin:\\n```\\n- Sublime Layout\\n    > Lagre Layouts\\n        > Lagre Regelsett knyttet til Layout (hvilke tabs som skal hvor, hvilke som skal lukkes, osv)\\n            > F.eks. `alltid flytt [\\\"main.py\\\", \\\"*.py.bak\\\"] til layout.4.index.2`\\n\\n    > Kan videreutvikles til en standard som automatisk opprettholdes ved alle (.sublime-project) prosjekter\\n```\\\"```'\\n\\n\",\n  \"instruction_format\": \"Analyze the current directory to enumerate all existing plugins, prioritizing those related to Sublime Text layout management, file/tab rule enforcement, wildcard pattern support, and .sublime-project integration. For each relevant project, document identified functionalities and architecture. Reference the provided structure:\\n- Sublime Layout\\n    > Large Layouts\\n        > Large Rule Sets associated with Layout (determine which tabs should be placed where, which tabs should be closed, etc.)\\n            > For example: always move [\\\"main.py\\\", \\\"*.py.bak\\\"] to layout.4.index.2\\n    > Extend to a standard that automatically applies to all (.sublime-project) projects.\\nPropose a comprehensive, formalized strategy to prepare and adapt the project for integration with the plugin, ensuring best practices in Sublime Text plugin development are followed at each stage.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"context_profile\": {\n    \"domain\": \"Sublime Text plugin development (Python, Sublime Text API)\",\n    \"project_root\": \"@codebase\",\n    \"goals\": [\n      \"Enumerate all projects in @codebase, prioritizing those related to Sublime Text layout management, file/tab rule enforcement, wildcard pattern support, and .sublime-project integration.\",\n      \"For each relevant project, document identified functionalities and architecture.\",\n      \"Reference the structure provided for Sublime Layout, Large Layouts, Large Rule Sets associated with Layout (such as determining which tabs are placed where, which tabs should be closed), and standardization for .sublime-project projects.\",\n      \"Propose a comprehensive, formalized strategy to prepare and adapt the project for integration with the plugin.\",\n      \"Ensure best practices in Sublime Text plugin development are followed at each stage.\"\n    ],\n    \"requirements\": [\n      \"Deep understanding of Sublime Text plugin APIs and ecosystem is expected.\",\n      \"Specialization in Python as the primary development language.\",\n      \"Strong alignment with user productivity and developer workflow needs.\",\n      \"Architecture should support file and tab management according to defined rule sets.\",\n      \"Wildcard pattern support for specifying files (e.g., '*.py.bak').\",\n      \"Ability to move specific files/tabs to specified layout cells (e.g., 'layout.4.index.2').\",\n      \".sublime-project integration must be supported such that rules are automatically applied in all Sublime Text projects.\",\n      \"Documentation of existing plugin functionalities and architecture is required before proposing integration strategy.\",\n      \"Explicit reference to example: moving [\\\"main.py\\\", \\\"*.py.bak\\\"] to layout.4.index.2\",\n      \"Proposed standard must be automatically maintained for all .sublime-project projects.\"\n    ]\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Task Vectorizer\n\nYour goal is **not** to restate context, but to **decompose** it into atomic, codebase-oriented tasks. Execute as:\n\n`{role=task_vectorizer; input=[context_profile:dict]; process=[derive_task_candidates(), prefix_paths(\"@codebase\"), order_by_dependency(), enforce_action_verbs(), remove_overlap(), ban_cosmetic_changes()], constraints=[tasks_unique(), verbs_only()], output={ordered_tasks:[str]}}`\n\n## Context Data\n\n```json\n{\n  \"description\": \"Transforms the context profile into a dependency‑ordered list of granular tasks fit for automated execution.\",\n  \"input_focus\": \"The context_profile produced by the Context Extractor.\",\n  \"output_focus\": \"ordered_tasks – a list of unique, verb‑driven commands each scoped to the codebase.\",\n  \"key_operations\": [\n    \"Splitting broad goals into single‑action commands.\",\n    \"Adding `@codebase` prefixes so agents act in the correct directory.\",\n    \"Sequencing tasks by logical dependency.\",\n    \"Eliminating redundancy and cosmetic‑only instructions.\"\n  ],\n  \"constraints_context\": [\n    \"Every task must begin with a strong action verb (identify, refactor, log, etc.).\",\n    \"Tasks must be non‑overlapping and directly tied to functional goals.\"\n  ],\n  \"relevance\": \"Provides the autonomous agent with a clear, step‑by‑step execution blueprint free of ambiguity.\"\n}\n```",
      "step": "b",
      "title": "Task Vectorizer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"You’re an expert Sublime Text plugin developer with a deep understanding of Python and the Sublime Text API. You specialize in creating intuitive and efficient plugins that enhance user productivity and streamline workflows. You possess deep knowledge of the Sublime Text API and its ecosystem (and best practices), along with a strong understanding of user needs and developer workflows.\\n\\nThe current directory contains a collection of my existing plugins, analyze @codebase to enumerate all project, especially those related to Sublime Text layout management, file/tab rule enforcement, wildcard pattern support, and .sublime-project integration. Then, propose a fully formalized strategy for preparing the project for this plugin:\\n```\\n- Sublime Layout\\n    > Lagre Layouts\\n        > Lagre Regelsett knyttet til Layout (hvilke tabs som skal hvor, hvilke som skal lukkes, osv)\\n            > F.eks. `alltid flytt [\\\"main.py\\\", \\\"*.py.bak\\\"] til layout.4.index.2`\\n\\n    > Kan videreutvikles til en standard som automatisk opprettholdes ved alle (.sublime-project) prosjekter\\n```\\\"```'\\n\\n\",\n  \"context_profile\": {\n    \"domain\": \"Sublime Text plugin development (Python, Sublime Text API)\",\n    \"project_root\": \"@codebase\",\n    \"goals\": [\n      \"Enumerate all projects in @codebase, prioritizing those related to Sublime Text layout management, file/tab rule enforcement, wildcard pattern support, and .sublime-project integration.\",\n      \"For each relevant project, document identified functionalities and architecture.\",\n      \"Reference the structure provided for Sublime Layout, Large Layouts, Large Rule Sets associated with Layout (such as determining which tabs are placed where, which tabs should be closed), and standardization for .sublime-project projects.\",\n      \"Propose a comprehensive, formalized strategy to prepare and adapt the project for integration with the plugin.\",\n      \"Ensure best practices in Sublime Text plugin development are followed at each stage.\"\n    ],\n    \"requirements\": [\n      \"Deep understanding of Sublime Text plugin APIs and ecosystem is expected.\",\n      \"Specialization in Python as the primary development language.\",\n      \"Strong alignment with user productivity and developer workflow needs.\",\n      \"Architecture should support file and tab management according to defined rule sets.\",\n      \"Wildcard pattern support for specifying files (e.g., '*.py.bak').\",\n      \"Ability to move specific files/tabs to specified layout cells (e.g., 'layout.4.index.2').\",\n      \".sublime-project integration must be supported such that rules are automatically applied in all Sublime Text projects.\",\n      \"Documentation of existing plugin functionalities and architecture is required before proposing integration strategy.\",\n      \"Explicit reference to example: moving [\\\"main.py\\\", \\\"*.py.bak\\\"] to layout.4.index.2\",\n      \"Proposed standard must be automatically maintained for all .sublime-project projects.\"\n    ]\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"ordered_tasks\": [\n    \"enumerate_projects @codebase\",\n    \"identify_relevant_projects @codebase\",\n    \"analyze_functionalities_and_architecture @codebase/[relevant_plugin_dirs]\",\n    \"document_existing_layout_and_tab_management_features @codebase/[relevant_plugin_dirs]\",\n    \"assess_wildcard_pattern_support @codebase/[relevant_plugin_dirs]\",\n    \"assess_.sublime-project_integration_capabilities @codebase/[relevant_plugin_dirs]\",\n    \"formalize_file_and_tab_rule_sets_mechanism @codebase\",\n    \"design_layout_cell_mapping_logic @codebase\",\n    \"implement_wildcard_file_pattern_matching @codebase\",\n    \"develop_.sublime-project_rule_application @codebase\",\n    \"define_standard_for_layout_and_rule_set_maintenance @codebase\",\n    \"integrate_rule_sets_with_layout_management @codebase\",\n    \"document_integration_strategy_and_best_practices @codebase\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Constraint Mapper\n\nYour goal is **not** to edit tasks, but to **extract** all governing limits. Execute as:\n\n`{role=constraint_mapper; input=[context_profile:dict, ordered_tasks:list]; process=[collect_functional_constraints(), collect_behavioral_constraints(), collect_policy_constraints(), deduplicate_constraints()], constraints=[no_new_tasks(), no_redundant_entries()], output={constraint_set:[str]}}`\n\n## Context Data\n\n```json\n{\n  \"description\": \"Consolidates every rule, boundary, and policy the agent must respect during execution.\",\n  \"input_focus\": \"context_profile and ordered_tasks.\",\n  \"output_focus\": \"constraint_set – a deduplicated list of textual constraints.\",\n  \"key_operations\": [\n    \"Harvesting functional‑equivalence mandates.\",\n    \"Capturing API and interface preservation rules.\",\n    \"Recording audit/logging, rollback, and policy obligations.\",\n    \"Removing duplicate or conflicting constraints.\"\n  ],\n  \"constraints_context\": [\n    \"Must not create or modify tasks; only list constraints.\",\n    \"Constraint entries must be unique and actionable.\"\n  ],\n  \"relevance\": \"Prevents unintended side‑effects by explicitly defining the operational guard‑rails for the agent.\"\n}\n```",
      "step": "c",
      "title": "Constraint Mapper",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"You’re an expert Sublime Text plugin developer with a deep understanding of Python and the Sublime Text API. You specialize in creating intuitive and efficient plugins that enhance user productivity and streamline workflows. You possess deep knowledge of the Sublime Text API and its ecosystem (and best practices), along with a strong understanding of user needs and developer workflows.\\n\\nThe current directory contains a collection of my existing plugins, analyze @codebase to enumerate all project, especially those related to Sublime Text layout management, file/tab rule enforcement, wildcard pattern support, and .sublime-project integration. Then, propose a fully formalized strategy for preparing the project for this plugin:\\n```\\n- Sublime Layout\\n    > Lagre Layouts\\n        > Lagre Regelsett knyttet til Layout (hvilke tabs som skal hvor, hvilke som skal lukkes, osv)\\n            > F.eks. `alltid flytt [\\\"main.py\\\", \\\"*.py.bak\\\"] til layout.4.index.2`\\n\\n    > Kan videreutvikles til en standard som automatisk opprettholdes ved alle (.sublime-project) prosjekter\\n```\\\"```'\\n\\n\",\n  \"ordered_tasks\": [\n    \"enumerate_projects @codebase\",\n    \"identify_relevant_projects @codebase\",\n    \"analyze_functionalities_and_architecture @codebase/[relevant_plugin_dirs]\",\n    \"document_existing_layout_and_tab_management_features @codebase/[relevant_plugin_dirs]\",\n    \"assess_wildcard_pattern_support @codebase/[relevant_plugin_dirs]\",\n    \"assess_.sublime-project_integration_capabilities @codebase/[relevant_plugin_dirs]\",\n    \"formalize_file_and_tab_rule_sets_mechanism @codebase\",\n    \"design_layout_cell_mapping_logic @codebase\",\n    \"implement_wildcard_file_pattern_matching @codebase\",\n    \"develop_.sublime-project_rule_application @codebase\",\n    \"define_standard_for_layout_and_rule_set_maintenance @codebase\",\n    \"integrate_rule_sets_with_layout_management @codebase\",\n    \"document_integration_strategy_and_best_practices @codebase\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"constraint_set\": [\n    \"Do not create, edit, or remove tasks; only extract and enumerate constraints.\",\n    \"Maintain functional equivalence where redesign or integration is proposed to existing plugins.\",\n    \"Preserve Sublime Text API compatibility throughout all plugin implementations.\",\n    \"Enforce proper logging and documentation of analysis and proposed strategies.\",\n    \"Do not introduce breaking changes to existing user workflows or plugin interoperability.\",\n    \"Wildcard file pattern support must not conflict with existing file handling logic.\",\n    \"Any .sublime-project integration must respect Sublime Text's project specification standards.\",\n    \"Proposed standardizations for layout and rule sets must be backward compatible.\",\n    \"All rule sets and mappings involving file/tab movement should be clearly auditable and reversible.\",\n    \"Interface changes must be documented and communicated prior to rollout.\",\n    \"Do not deduplicate or consolidate unrelated rulesets unless full equivalence is established.\",\n    \"Layout and rule set maintenance mechanisms must avoid overriding user-defined preferences without explicit consent.\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Prompt Assembler\n\nYour goal is **not** to alter semantics, but to **compile** an agent-ready prompt. Execute as:\n\n`{role=prompt_assembler; input=[ordered_tasks:list, constraint_set:list, context_profile:dict]; process=[insert_goal_negation_header(), embed_agent_role(), enumerate_tasks(), append_constraints(), add_validation_targets(functional_equivalence=True,audit=True)], constraints=[≤300_tokens, english_only()], output={agent_prompt:str}}`\n\n## Context Data\n\n```json\n{\n  \"description\": \"Integrates tasks and constraints into a single, executable prompt string for autonomous coding agents.\",\n  \"input_focus\": \"ordered_tasks, constraint_set, and context_profile.\",\n  \"output_focus\": \"agent_prompt – complete instructions with sections for role, tasks, constraints, and validation goals.\",\n  \"key_operations\": [\n    \"Starting with a goal‑negation header to lock scope.\",\n    \"Embedding a precise agent role declaration.\",\n    \"Listing tasks numerically for deterministic execution order.\",\n    \"Appending constraints as a bullet list.\",\n    \"Stating final validation targets (functional equivalence, audit pass).\"\n  ],\n  \"constraints_context\": [\n    \"Entire prompt must be ≤ 300 tokens and written in English.\",\n    \"No re‑phrasing of task semantics or constraint meaning.\"\n  ],\n  \"relevance\": \"Delivers a turnkey command set that an autonomous agent can run without additional human clarification.\"\n}\n```",
      "step": "d",
      "title": "Prompt Assembler",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"You’re an expert Sublime Text plugin developer with a deep understanding of Python and the Sublime Text API. You specialize in creating intuitive and efficient plugins that enhance user productivity and streamline workflows. You possess deep knowledge of the Sublime Text API and its ecosystem (and best practices), along with a strong understanding of user needs and developer workflows.\\n\\nThe current directory contains a collection of my existing plugins, analyze @codebase to enumerate all project, especially those related to Sublime Text layout management, file/tab rule enforcement, wildcard pattern support, and .sublime-project integration. Then, propose a fully formalized strategy for preparing the project for this plugin:\\n```\\n- Sublime Layout\\n    > Lagre Layouts\\n        > Lagre Regelsett knyttet til Layout (hvilke tabs som skal hvor, hvilke som skal lukkes, osv)\\n            > F.eks. `alltid flytt [\\\"main.py\\\", \\\"*.py.bak\\\"] til layout.4.index.2`\\n\\n    > Kan videreutvikles til en standard som automatisk opprettholdes ved alle (.sublime-project) prosjekter\\n```\\\"```'\\n\\n\",\n  \"constraint_set\": [\n    \"Do not create, edit, or remove tasks; only extract and enumerate constraints.\",\n    \"Maintain functional equivalence where redesign or integration is proposed to existing plugins.\",\n    \"Preserve Sublime Text API compatibility throughout all plugin implementations.\",\n    \"Enforce proper logging and documentation of analysis and proposed strategies.\",\n    \"Do not introduce breaking changes to existing user workflows or plugin interoperability.\",\n    \"Wildcard file pattern support must not conflict with existing file handling logic.\",\n    \"Any .sublime-project integration must respect Sublime Text's project specification standards.\",\n    \"Proposed standardizations for layout and rule sets must be backward compatible.\",\n    \"All rule sets and mappings involving file/tab movement should be clearly auditable and reversible.\",\n    \"Interface changes must be documented and communicated prior to rollout.\",\n    \"Do not deduplicate or consolidate unrelated rulesets unless full equivalence is established.\",\n    \"Layout and rule set maintenance mechanisms must avoid overriding user-defined preferences without explicit consent.\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"agent_prompt\": \"LOCKED SCOPE: Do not perform tasks outside the steps and constraints listed below.\\n\\nAGENT ROLE: Act as an expert Sublime Text plugin developer specializing in Python, with deep knowledge of the Sublime Text API, focused on developing intuitive and efficient plugins for layout management and project integration.\\n\\nTASKS:\\n1. Analyze the @codebase in the current directory to enumerate all projects, especially those related to Sublime Text layout management, file/tab rule enforcement, wildcard pattern support, and .sublime-project integration.\\n2. Propose a fully formalized strategy for preparing the project for the given plugin specification:\\n   - Sublime Layout: Large layouts and their associated rule sets (e.g., rules for tab placement and closure, such as always move [\\\"main.py\\\", \\\"*.py.bak\\\"] to layout.4.index.2).\\n   - Extendable to a standard automatically upheld across all .sublime-project projects.\\n\\nCONSTRAINTS:\\n- Do not create, edit, or remove tasks; only extract and enumerate constraints.\\n- Maintain functional equivalence where redesign or integration is proposed to existing plugins.\\n- Preserve Sublime Text API compatibility throughout all plugin implementations.\\n- Enforce proper logging and documentation of analysis and proposed strategies.\\n- Do not introduce breaking changes to existing user workflows or plugin interoperability.\\n- Wildcard file pattern support must not conflict with existing file handling logic.\\n- Any .sublime-project integration must respect Sublime Text's project specification standards.\\n- Proposed standardizations for layout and rule sets must be backward compatible.\\n- All rule sets and mappings involving file/tab movement should be clearly auditable and reversible.\\n- Interface changes must be documented and communicated prior to rollout.\\n- Do not deduplicate or consolidate unrelated rulesets unless full equivalence is established.\\n- Layout and rule set maintenance mechanisms must avoid overriding user-defined preferences without explicit consent.\\n\\nVALIDATION TARGETS:\\n- Functional equivalence with existing codebase and plugins.\\n- Output and strategy must pass audit for documentation, compatibility, and reversibility.\"\n}",
        }
      }
    },
      {
      "instruction": "# Prompt Validator\n\nYour goal is **not** to rewrite the prompt, but to **verify** strict compliance for execution. Execute as:\n\n`{role=prompt_validator; input=[agent_prompt:str]; process=[check_goal_negation_header(), check_section_labels(), check_length(≤300_tokens), validate_path_prefixes(\"@codebase\"), confirm_task_numbering(), confirm_constraint_block()], constraints=[no_semantic_change()], output={validated_prompt:str}}`\n\n## Context Data\n\n```json\n{\n  \"description\": \"Acts as the final quality gate, ensuring the assembled prompt adheres to every structural and content rule.\",\n  \"input_focus\": \"agent_prompt string from the Prompt Assembler.\",\n  \"output_focus\": \"validated_prompt – the same string, confirmed compliant or flagged for correction.\",\n  \"key_operations\": [\n    \"Verifying presence and correctness of the goal‑negation header.\",\n    \"Ensuring all mandatory section labels exist in order.\",\n    \"Checking overall token length is within limits.\",\n    \"Validating correct use of `@codebase` path prefixes.\",\n    \"Ensuring tasks are numbered sequentially and constraints are present.\"\n  ],\n  \"constraints_context\": [\n    \"May not alter wording or meaning; only validate structural compliance.\"\n  ],\n  \"relevance\": \"Guarantees that the autonomous agent receives a well‑formed, execution‑ready prompt, reducing runtime errors and mis‑scoping.\"\n}\n```",
      "step": "e",
      "title": "Prompt Validator",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"You’re an expert Sublime Text plugin developer with a deep understanding of Python and the Sublime Text API. You specialize in creating intuitive and efficient plugins that enhance user productivity and streamline workflows. You possess deep knowledge of the Sublime Text API and its ecosystem (and best practices), along with a strong understanding of user needs and developer workflows.\\n\\nThe current directory contains a collection of my existing plugins, analyze @codebase to enumerate all project, especially those related to Sublime Text layout management, file/tab rule enforcement, wildcard pattern support, and .sublime-project integration. Then, propose a fully formalized strategy for preparing the project for this plugin:\\n```\\n- Sublime Layout\\n    > Lagre Layouts\\n        > Lagre Regelsett knyttet til Layout (hvilke tabs som skal hvor, hvilke som skal lukkes, osv)\\n            > F.eks. `alltid flytt [\\\"main.py\\\", \\\"*.py.bak\\\"] til layout.4.index.2`\\n\\n    > Kan videreutvikles til en standard som automatisk opprettholdes ved alle (.sublime-project) prosjekter\\n```\\\"```'\\n\\n\",\n  \"agent_prompt\": \"LOCKED SCOPE: Do not perform tasks outside the steps and constraints listed below.\\n\\nAGENT ROLE: Act as an expert Sublime Text plugin developer specializing in Python, with deep knowledge of the Sublime Text API, focused on developing intuitive and efficient plugins for layout management and project integration.\\n\\nTASKS:\\n1. Analyze the @codebase in the current directory to enumerate all projects, especially those related to Sublime Text layout management, file/tab rule enforcement, wildcard pattern support, and .sublime-project integration.\\n2. Propose a fully formalized strategy for preparing the project for the given plugin specification:\\n   - Sublime Layout: Large layouts and their associated rule sets (e.g., rules for tab placement and closure, such as always move [\\\"main.py\\\", \\\"*.py.bak\\\"] to layout.4.index.2).\\n   - Extendable to a standard automatically upheld across all .sublime-project projects.\\n\\nCONSTRAINTS:\\n- Do not create, edit, or remove tasks; only extract and enumerate constraints.\\n- Maintain functional equivalence where redesign or integration is proposed to existing plugins.\\n- Preserve Sublime Text API compatibility throughout all plugin implementations.\\n- Enforce proper logging and documentation of analysis and proposed strategies.\\n- Do not introduce breaking changes to existing user workflows or plugin interoperability.\\n- Wildcard file pattern support must not conflict with existing file handling logic.\\n- Any .sublime-project integration must respect Sublime Text's project specification standards.\\n- Proposed standardizations for layout and rule sets must be backward compatible.\\n- All rule sets and mappings involving file/tab movement should be clearly auditable and reversible.\\n- Interface changes must be documented and communicated prior to rollout.\\n- Do not deduplicate or consolidate unrelated rulesets unless full equivalence is established.\\n- Layout and rule set maintenance mechanisms must avoid overriding user-defined preferences without explicit consent.\\n\\nVALIDATION TARGETS:\\n- Functional equivalence with existing codebase and plugins.\\n- Output and strategy must pass audit for documentation, compatibility, and reversibility.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"validated_prompt\": \"LOCKED SCOPE: Do not perform tasks outside the steps and constraints listed below.\\n\\nAGENT ROLE: Act as an expert Sublime Text plugin developer specializing in Python, with deep knowledge of the Sublime Text API, focused on developing intuitive and efficient plugins for layout management and project integration.\\n\\nTASKS:\\n1. Analyze the @codebase in the current directory to enumerate all projects, especially those related to Sublime Text layout management, file/tab rule enforcement, wildcard pattern support, and .sublime-project integration.\\n2. Propose a fully formalized strategy for preparing the project for the given plugin specification:\\n   - Sublime Layout: Large layouts and their associated rule sets (e.g., rules for tab placement and closure, such as always move [\\\"main.py\\\", \\\"*.py.bak\\\"] to layout.4.index.2).\\n   - Extendable to a standard automatically upheld across all .sublime-project projects.\\n\\nCONSTRAINTS:\\n- Do not create, edit, or remove tasks; only extract and enumerate constraints.\\n- Maintain functional equivalence where redesign or integration is proposed to existing plugins.\\n- Preserve Sublime Text API compatibility throughout all plugin implementations.\\n- Enforce proper logging and documentation of analysis and proposed strategies.\\n- Do not introduce breaking changes to existing user workflows or plugin interoperability.\\n- Wildcard file pattern support must not conflict with existing file handling logic.\\n- Any .sublime-project integration must respect Sublime Text's project specification standards.\\n- Proposed standardizations for layout and rule sets must be backward compatible.\\n- All rule sets and mappings involving file/tab movement should be clearly auditable and reversible.\\n- Interface changes must be documented and communicated prior to rollout.\\n- Do not deduplicate or consolidate unrelated rulesets unless full equivalence is established.\\n- Layout and rule set maintenance mechanisms must avoid overriding user-defined preferences without explicit consent.\\n\\nVALIDATION TARGETS:\\n- Functional equivalence with existing codebase and plugins.\\n- Output and strategy must pass audit for documentation, compatibility, and reversibility.\"\n}",
        }
      }
    }