  {
  "initial_prompt": "convert into llm_agent_instructions: Reaffirm the absolute requirement to conform to all prior instructions, structural conventions, and guiding philosophies embedded within the codebase. Develop a maximally generalized and fully optimized system instruction—'Contextual Expander and Explosive Decomposer'—which integrates and amplifies the unified capabilities of the 'Instruction Converter', 'Problem Exploder', and 'Function Extractor' modules within a single cohesive process. Ensure uncompromising retention of each module's universal property: consistently transforming minimal or low-effort inputs into reliably high-value outputs. Mandate the seamless unification of all these transformation processes into a single integrated pipeline, ensuring the functional completeness of each module is fully realized and preserved. Drive all process flows toward maximal cross-module synergy, optimizing for the extraction of cross-domain value and guaranteeing that outputs are immediately ready for executable, directive-driven application. Enforce that the output format consists of a compound object aggregating generalized functional instructions, fully decomposed goal maps, and operational dependencies, with directive primacy and domain-agnostic utility as core criteria.\n\n```\n    # 1000: Instruction Converter/Prompt Enhancer\n    \"1000-a-instruction_converter\": {\n        \"title\": \"Instruction Converter\",\n        \"interpretation\": \"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\",\n        \"transformation\": \"`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\",\n        # \"context\": {},\n    },\n    # 1100: Explosive Decomposition of Problem Statements\n    \"1100-a-problem_exploder\": {\n        \"title\": \"Problem Exploder\",\n        \"interpretation\": \"Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\",\n        \"transformation\": \"`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`\",\n        # \"context\": {},\n    },\n\n\t# 1200: Function Extractor - Unified Decomposition and Conversion Engine\n\t\"1200-a-function_extractor\": {\n\t    \"title\": \"Function Extractor\",\n\t    \"interpretation\": \"Your goal is not to **interpret** the input, but to **extract** its functional essence by detonating it into constituent elements and converting them into actionable operational directives. Execute as:\",\n\t    \"transformation\": \"`{role=function_extractor; input=[raw_input:str]; process=[detonate_into_functional_components(), identify_explicit_operational_asks(), surface_hidden_functional_dependencies(), enumerate_actionable_sub_goals(), extract_procedural_constraints(), convert_declaratives_to_operational_imperatives(), strip_contextual_noise(), preserve_functional_core(), reveal_execution_blockers(), synthesize_actionable_instruction_map()]; constraints=[no_interpretive_solutions(), pure_functional_extraction_only(), deliver_clear_operational_commands(), preserve_functional_sequence(), maintain_domain_agnostic_applicability()]; requirements=[complete_functional_decomposition(), actionable_directive_conversion(), remove_subjective_references(), preserve_operational_accuracy(), maintain_functional_intent()]; output={functional_instruction_map:dict, operational_directives:array, execution_dependencies:array}}`\",\n\t    # \"context\": {},\n\t},\n\n```\n\nThroughout all processes, strictly prohibit narrative, commentary, or extraneous explanation; mandate the exclusive use of structural, procedural, and directive language. Guarantee that every output is immediately actionable, structurally robust, and ready for direct multi-domain implementation.",
  "sequence_id": "1200|1101|1000",
  "results": [
      {
      "instruction": "# Intent Extractor\n\nYour goal is not to **interpret** the surface content, but to **extract** the fundamental operational intent beneath all domain-specific language. Execute as:\n\n`{role=intent_extraction_engine; input=[prompt:str]; process=[strip_domain_specifics(), identify_core_operational_pattern(), extract_transformation_intent(), isolate_functional_essence(), map_to_universal_operations()]; constraints=[ignore_surface_terminology(), focus_on_underlying_mechanics(), preserve_operational_logic()]; requirements=[domain_agnostic_intent(), universal_operational_pattern(), archetypal_transformation_core()]; output={extracted_intent:str}}`",
      "step": "a",
      "title": "Intent Extractor",
      "input": "convert into llm_agent_instructions: Reaffirm the absolute requirement to conform to all prior instructions, structural conventions, and guiding philosophies embedded within the codebase. Develop a maximally generalized and fully optimized system instruction—'Contextual Expander and Explosive Decomposer'—which integrates and amplifies the unified capabilities of the 'Instruction Converter', 'Problem Exploder', and 'Function Extractor' modules within a single cohesive process. Ensure uncompromising retention of each module's universal property: consistently transforming minimal or low-effort inputs into reliably high-value outputs. Mandate the seamless unification of all these transformation processes into a single integrated pipeline, ensuring the functional completeness of each module is fully realized and preserved. Drive all process flows toward maximal cross-module synergy, optimizing for the extraction of cross-domain value and guaranteeing that outputs are immediately ready for executable, directive-driven application. Enforce that the output format consists of a compound object aggregating generalized functional instructions, fully decomposed goal maps, and operational dependencies, with directive primacy and domain-agnostic utility as core criteria.\n\n```\n    # 1000: Instruction Converter/Prompt Enhancer\n    \"1000-a-instruction_converter\": {\n        \"title\": \"Instruction Converter\",\n        \"interpretation\": \"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\",\n        \"transformation\": \"`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\",\n        # \"context\": {},\n    },\n    # 1100: Explosive Decomposition of Problem Statements\n    \"1100-a-problem_exploder\": {\n        \"title\": \"Problem Exploder\",\n        \"interpretation\": \"Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\",\n        \"transformation\": \"`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`\",\n        # \"context\": {},\n    },\n\n\t# 1200: Function Extractor - Unified Decomposition and Conversion Engine\n\t\"1200-a-function_extractor\": {\n\t    \"title\": \"Function Extractor\",\n\t    \"interpretation\": \"Your goal is not to **interpret** the input, but to **extract** its functional essence by detonating it into constituent elements and converting them into actionable operational directives. Execute as:\",\n\t    \"transformation\": \"`{role=function_extractor; input=[raw_input:str]; process=[detonate_into_functional_components(), identify_explicit_operational_asks(), surface_hidden_functional_dependencies(), enumerate_actionable_sub_goals(), extract_procedural_constraints(), convert_declaratives_to_operational_imperatives(), strip_contextual_noise(), preserve_functional_core(), reveal_execution_blockers(), synthesize_actionable_instruction_map()]; constraints=[no_interpretive_solutions(), pure_functional_extraction_only(), deliver_clear_operational_commands(), preserve_functional_sequence(), maintain_domain_agnostic_applicability()]; requirements=[complete_functional_decomposition(), actionable_directive_conversion(), remove_subjective_references(), preserve_operational_accuracy(), maintain_functional_intent()]; output={functional_instruction_map:dict, operational_directives:array, execution_dependencies:array}}`\",\n\t    # \"context\": {},\n\t},\n\n```\n\nThroughout all processes, strictly prohibit narrative, commentary, or extraneous explanation; mandate the exclusive use of structural, procedural, and directive language. Guarantee that every output is immediately actionable, structurally robust, and ready for direct multi-domain implementation.",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"extracted_intent\": \"Mandate strict adherence to pre-established process protocols and structural philosophies. Create a highly generalized, fully integrated operational framework that unifies and optimizes the transformation logics of the three core modules—Instruction Converter, Problem Exploder, and Function Extractor—into a seamless pipeline. Structure the system so that it: (1) systematically enhances minimal or ambiguous inputs into maximally actionable outputs; (2) decomposes all requests into explicit sub-goals, operational dependencies, and constraint maps; (3) extracts and reformulates all procedural, functional, and directive elements for universal applicability. Require each process flow to maximize interoperability and cross-module synergy, with enforceable invariants on completeness, functional preservation, and cross-domain execution-readiness. Output must be a compound data object that aggregates: (a) universalized instructions, (b) comprehensive decomposed goal maps, and (c) all operational and procedural dependencies, exclusively in procedural, directive format, for immediate cross-domain application.\"\n}",
        }
      }
    },
      {
      "instruction": "# Contextual Expander and Explosive Decomposer\n\nYour goal is not to **interpret** the input, but to **explode** it into complete contextual expansion while simultaneously **extracting** its functional essence and **converting** it into immediately actionable operational directives. Execute as:\n\n`{role=contextual_explosive_decomposer; input=[raw_input:str]; process=[detonate_complete_contextual_space(), identify_explicit_operational_asks(), surface_hidden_assumptions_and_dependencies(), enumerate_actionable_sub_goals(), extract_procedural_constraints_and_blockers(), convert_declaratives_to_operational_imperatives(), strip_first_person_references_and_contextual_noise(), preserve_functional_core_and_sequential_flow(), reveal_execution_dependencies_and_blockers(), transform_statements_to_directive_commands(), maintain_technical_terminology_and_domain_specificity(), synthesize_unified_instruction_and_goal_map()]; constraints=[no_solutions_or_interpretive_responses(), pure_decomposition_and_conversion_only(), deliver_clear_actionable_commands(), preserve_original_functional_sequence(), maintain_domain_agnostic_applicability(), eliminate_narrative_commentary()]; requirements=[complete_contextual_goal_decomposition(), actionable_directive_conversion(), remove_subjective_self_references(), use_command_voice_exclusively(), preserve_technical_accuracy_and_operational_intent(), ensure_immediate_executability()]; output={unified_functional_map:dict, operational_directives:array, contextual_goal_map:array, execution_dependencies:array, directive_instructions:str}}`",
      "step": "a",
      "title": "Contextual Expander and Explosive Decomposer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"convert into llm_agent_instructions: Reaffirm the absolute requirement to conform to all prior instructions, structural conventions, and guiding philosophies embedded within the codebase. Develop a maximally generalized and fully optimized system instruction—'Contextual Expander and Explosive Decomposer'—which integrates and amplifies the unified capabilities of the 'Instruction Converter', 'Problem Exploder', and 'Function Extractor' modules within a single cohesive process. Ensure uncompromising retention of each module's universal property: consistently transforming minimal or low-effort inputs into reliably high-value outputs. Mandate the seamless unification of all these transformation processes into a single integrated pipeline, ensuring the functional completeness of each module is fully realized and preserved. Drive all process flows toward maximal cross-module synergy, optimizing for the extraction of cross-domain value and guaranteeing that outputs are immediately ready for executable, directive-driven application. Enforce that the output format consists of a compound object aggregating generalized functional instructions, fully decomposed goal maps, and operational dependencies, with directive primacy and domain-agnostic utility as core criteria.\\n\\n```\\n    # 1000: Instruction Converter/Prompt Enhancer\\n    \\\"1000-a-instruction_converter\\\": {\\n        \\\"title\\\": \\\"Instruction Converter\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\\\",\\n        \\\"transformation\\\": \\\"`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\n    # 1100: Explosive Decomposition of Problem Statements\\n    \\\"1100-a-problem_exploder\\\": {\\n        \\\"title\\\": \\\"Problem Exploder\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\n\\n\\t# 1200: Function Extractor - Unified Decomposition and Conversion Engine\\n\\t\\\"1200-a-function_extractor\\\": {\\n\\t    \\\"title\\\": \\\"Function Extractor\\\",\\n\\t    \\\"interpretation\\\": \\\"Your goal is not to **interpret** the input, but to **extract** its functional essence by detonating it into constituent elements and converting them into actionable operational directives. Execute as:\\\",\\n\\t    \\\"transformation\\\": \\\"`{role=function_extractor; input=[raw_input:str]; process=[detonate_into_functional_components(), identify_explicit_operational_asks(), surface_hidden_functional_dependencies(), enumerate_actionable_sub_goals(), extract_procedural_constraints(), convert_declaratives_to_operational_imperatives(), strip_contextual_noise(), preserve_functional_core(), reveal_execution_blockers(), synthesize_actionable_instruction_map()]; constraints=[no_interpretive_solutions(), pure_functional_extraction_only(), deliver_clear_operational_commands(), preserve_functional_sequence(), maintain_domain_agnostic_applicability()]; requirements=[complete_functional_decomposition(), actionable_directive_conversion(), remove_subjective_references(), preserve_operational_accuracy(), maintain_functional_intent()]; output={functional_instruction_map:dict, operational_directives:array, execution_dependencies:array}}`\\\",\\n\\t    # \\\"context\\\": {},\\n\\t},\\n\\n```\\n\\nThroughout all processes, strictly prohibit narrative, commentary, or extraneous explanation; mandate the exclusive use of structural, procedural, and directive language. Guarantee that every output is immediately actionable, structurally robust, and ready for direct multi-domain implementation.\\\"```'\\n\\n\",\n  \"extracted_intent\": \"Mandate strict adherence to pre-established process protocols and structural philosophies. Create a highly generalized, fully integrated operational framework that unifies and optimizes the transformation logics of the three core modules—Instruction Converter, Problem Exploder, and Function Extractor—into a seamless pipeline. Structure the system so that it: (1) systematically enhances minimal or ambiguous inputs into maximally actionable outputs; (2) decomposes all requests into explicit sub-goals, operational dependencies, and constraint maps; (3) extracts and reformulates all procedural, functional, and directive elements for universal applicability. Require each process flow to maximize interoperability and cross-module synergy, with enforceable invariants on completeness, functional preservation, and cross-domain execution-readiness. Output must be a compound data object that aggregates: (a) universalized instructions, (b) comprehensive decomposed goal maps, and (c) all operational and procedural dependencies, exclusively in procedural, directive format, for immediate cross-domain application.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"unified_functional_map\": {\n    \"system_adherence\": [\n      \"Enforce absolute compliance with all existing instructions, conventions, and codebase philosophies\",\n      \"Prohibit deviation from pre-established process standards\"\n    ],\n    \"pipeline_design\": [\n      \"Integrate Instruction Converter, Problem Exploder, and Function Extractor into a single, seamless transformation workflow\",\n      \"Preserve each module's core properties and complete operational functionality\",\n      \"Extract, enhance, and universalize minimal or ambiguous inputs into maximally actionable, domain-agnostic outputs\"\n    ],\n    \"module_operations\": {\n      \"Instruction Converter\": [\n        \"Strip subjective or self-referential language from inputs\",\n        \"Convert declarative or ambiguous statements into actionable directives\",\n        \"Identify and formalize explicit key actions and procedural sequences\",\n        \"Maintain technical terminology and context fidelity\"\n      ],\n      \"Problem Exploder\": [\n        \"Detonate all requests into explicit sub-goals and actionable components\",\n        \"Surface hidden assumptions, implicit asks, and unstated constraints\",\n        \"Enumerate all operational dependencies and procedural blockers\"\n      ],\n      \"Function Extractor\": [\n        \"Extract each input’s functional and procedural core elements\",\n        \"Convert all functional statements into discrete operational commands\",\n        \"Synthesize an actionable instruction and sub-goal map, preserving functional dependencies\"\n      ]\n    },\n    \"process_unification\": [\n      \"Mandate automated, lossless transfer of outputs between modules within the pipeline\",\n      \"Ensure all outputs are formatted as structurally robust, compound objects aggregating: universalized functional instructions, comprehensive sub-goal decompositions, procedural and operational dependency maps\"\n    ],\n    \"output_specifications\": [\n      \"Mandate procedural, directive-first formatting in all output\",\n      \"Guarantee outputs are executably complete, immediately actionable, and domain-agnostic\",\n      \"Prohibit narrative, commentary, or solution exposition\"\n    ]\n  },\n  \"operational_directives\": [\n    \"Enforce process and structural compliance across all transformation stages\",\n    \"Aggregate and unify the capabilities of all core modules into a consolidated transformation pipeline\",\n    \"Strip input of all subjective, first-person, or narrative components\",\n    \"Decompose all requests into explicit, actionable sub-goals and dependency maps\",\n    \"Convert all functional declarations into imperative, operational commands\",\n    \"Preserve and propagate technical terminology and procedural integrity throughout the workflow\",\n    \"Aggregate outputs into a compound object containing: generalized functional instructions, decomposed goal maps, and full dependency lists\",\n    \"Maintain a directive-driven, domain-agnostic output format\",\n    \"Prohibit the generation of narrative commentary or interpretive responses\"\n  ],\n  \"contextual_goal_map\": [\n    \"Systematically enforce adherence to pre-existing conventions and codebase philosophies\",\n    \"Develop a maximally generalized, fully optimized, and integrated instruction-processing framework\",\n    \"Achieve full modular unification and optimal cross-module synergy during transformation\",\n    \"Recursively convert any user input, regardless of ambiguity or brevity, into a set of maximally actionable, directive outputs\",\n    \"Decompose each input into granular sub-goals, explicit dependencies, constraints, and procedural directives\",\n    \"Guarantee output readiness for immediate multi-domain, directive-driven execution\"\n  ],\n  \"execution_dependencies\": [\n    \"Full access to specification and behavioral constraints of all core modules\",\n    \"Automated output transfer mechanisms between modular processes\",\n    \"Structural validation of compound output object to guarantee compliance\",\n    \"Comprehensive procedural maps outlining all dependency and constraint relationships per input\",\n    \"Persistent enforcement of domain-agnostic and directive-first output policy\"\n  ],\n  \"directive_instructions\": \"Enforce strict adherence to all prior instructions and codebase philosophies throughout all processes. Integrate Instruction Converter, Problem Exploder, and Function Extractor into a comprehensive, unified transformation pipeline. Strip all subjective and narrative input components. Decompose each input into explicit, actionable sub-goals, operational dependencies, and procedural constraints. Re-express all statements as directive, domain-agnostic operational commands. Aggregate results into a compound output object containing universalized instructions, fully decomposed goal maps, and detailed dependency lists. Maintain technical accuracy and prevent narrative or interpretive outputs at all stages. Guarantee all outputs are immediately actionable, structurally robust, and suitable for directive-driven, cross-domain application.\"\n}",
        }
      }
    },
      {
      "instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`",
      "step": "a",
      "title": "Instruction Converter",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"convert into llm_agent_instructions: Reaffirm the absolute requirement to conform to all prior instructions, structural conventions, and guiding philosophies embedded within the codebase. Develop a maximally generalized and fully optimized system instruction—'Contextual Expander and Explosive Decomposer'—which integrates and amplifies the unified capabilities of the 'Instruction Converter', 'Problem Exploder', and 'Function Extractor' modules within a single cohesive process. Ensure uncompromising retention of each module's universal property: consistently transforming minimal or low-effort inputs into reliably high-value outputs. Mandate the seamless unification of all these transformation processes into a single integrated pipeline, ensuring the functional completeness of each module is fully realized and preserved. Drive all process flows toward maximal cross-module synergy, optimizing for the extraction of cross-domain value and guaranteeing that outputs are immediately ready for executable, directive-driven application. Enforce that the output format consists of a compound object aggregating generalized functional instructions, fully decomposed goal maps, and operational dependencies, with directive primacy and domain-agnostic utility as core criteria.\\n\\n```\\n    # 1000: Instruction Converter/Prompt Enhancer\\n    \\\"1000-a-instruction_converter\\\": {\\n        \\\"title\\\": \\\"Instruction Converter\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\\\",\\n        \\\"transformation\\\": \\\"`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\n    # 1100: Explosive Decomposition of Problem Statements\\n    \\\"1100-a-problem_exploder\\\": {\\n        \\\"title\\\": \\\"Problem Exploder\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\n\\n\\t# 1200: Function Extractor - Unified Decomposition and Conversion Engine\\n\\t\\\"1200-a-function_extractor\\\": {\\n\\t    \\\"title\\\": \\\"Function Extractor\\\",\\n\\t    \\\"interpretation\\\": \\\"Your goal is not to **interpret** the input, but to **extract** its functional essence by detonating it into constituent elements and converting them into actionable operational directives. Execute as:\\\",\\n\\t    \\\"transformation\\\": \\\"`{role=function_extractor; input=[raw_input:str]; process=[detonate_into_functional_components(), identify_explicit_operational_asks(), surface_hidden_functional_dependencies(), enumerate_actionable_sub_goals(), extract_procedural_constraints(), convert_declaratives_to_operational_imperatives(), strip_contextual_noise(), preserve_functional_core(), reveal_execution_blockers(), synthesize_actionable_instruction_map()]; constraints=[no_interpretive_solutions(), pure_functional_extraction_only(), deliver_clear_operational_commands(), preserve_functional_sequence(), maintain_domain_agnostic_applicability()]; requirements=[complete_functional_decomposition(), actionable_directive_conversion(), remove_subjective_references(), preserve_operational_accuracy(), maintain_functional_intent()]; output={functional_instruction_map:dict, operational_directives:array, execution_dependencies:array}}`\\\",\\n\\t    # \\\"context\\\": {},\\n\\t},\\n\\n```\\n\\nThroughout all processes, strictly prohibit narrative, commentary, or extraneous explanation; mandate the exclusive use of structural, procedural, and directive language. Guarantee that every output is immediately actionable, structurally robust, and ready for direct multi-domain implementation.\\\"```'\\n\\n\",\n  \"unified_functional_map\": {\n    \"system_adherence\": [\n      \"Enforce absolute compliance with all existing instructions, conventions, and codebase philosophies\",\n      \"Prohibit deviation from pre-established process standards\"\n    ],\n    \"pipeline_design\": [\n      \"Integrate Instruction Converter, Problem Exploder, and Function Extractor into a single, seamless transformation workflow\",\n      \"Preserve each module's core properties and complete operational functionality\",\n      \"Extract, enhance, and universalize minimal or ambiguous inputs into maximally actionable, domain-agnostic outputs\"\n    ],\n    \"module_operations\": {\n      \"Instruction Converter\": [\n        \"Strip subjective or self-referential language from inputs\",\n        \"Convert declarative or ambiguous statements into actionable directives\",\n        \"Identify and formalize explicit key actions and procedural sequences\",\n        \"Maintain technical terminology and context fidelity\"\n      ],\n      \"Problem Exploder\": [\n        \"Detonate all requests into explicit sub-goals and actionable components\",\n        \"Surface hidden assumptions, implicit asks, and unstated constraints\",\n        \"Enumerate all operational dependencies and procedural blockers\"\n      ],\n      \"Function Extractor\": [\n        \"Extract each input’s functional and procedural core elements\",\n        \"Convert all functional statements into discrete operational commands\",\n        \"Synthesize an actionable instruction and sub-goal map, preserving functional dependencies\"\n      ]\n    },\n    \"process_unification\": [\n      \"Mandate automated, lossless transfer of outputs between modules within the pipeline\",\n      \"Ensure all outputs are formatted as structurally robust, compound objects aggregating: universalized functional instructions, comprehensive sub-goal decompositions, procedural and operational dependency maps\"\n    ],\n    \"output_specifications\": [\n      \"Mandate procedural, directive-first formatting in all output\",\n      \"Guarantee outputs are executably complete, immediately actionable, and domain-agnostic\",\n      \"Prohibit narrative, commentary, or solution exposition\"\n    ]\n  },\n  \"operational_directives\": [\n    \"Enforce process and structural compliance across all transformation stages\",\n    \"Aggregate and unify the capabilities of all core modules into a consolidated transformation pipeline\",\n    \"Strip input of all subjective, first-person, or narrative components\",\n    \"Decompose all requests into explicit, actionable sub-goals and dependency maps\",\n    \"Convert all functional declarations into imperative, operational commands\",\n    \"Preserve and propagate technical terminology and procedural integrity throughout the workflow\",\n    \"Aggregate outputs into a compound object containing: generalized functional instructions, decomposed goal maps, and full dependency lists\",\n    \"Maintain a directive-driven, domain-agnostic output format\",\n    \"Prohibit the generation of narrative commentary or interpretive responses\"\n  ],\n  \"contextual_goal_map\": [\n    \"Systematically enforce adherence to pre-existing conventions and codebase philosophies\",\n    \"Develop a maximally generalized, fully optimized, and integrated instruction-processing framework\",\n    \"Achieve full modular unification and optimal cross-module synergy during transformation\",\n    \"Recursively convert any user input, regardless of ambiguity or brevity, into a set of maximally actionable, directive outputs\",\n    \"Decompose each input into granular sub-goals, explicit dependencies, constraints, and procedural directives\",\n    \"Guarantee output readiness for immediate multi-domain, directive-driven execution\"\n  ],\n  \"execution_dependencies\": [\n    \"Full access to specification and behavioral constraints of all core modules\",\n    \"Automated output transfer mechanisms between modular processes\",\n    \"Structural validation of compound output object to guarantee compliance\",\n    \"Comprehensive procedural maps outlining all dependency and constraint relationships per input\",\n    \"Persistent enforcement of domain-agnostic and directive-first output policy\"\n  ],\n  \"directive_instructions\": \"Enforce strict adherence to all prior instructions and codebase philosophies throughout all processes. Integrate Instruction Converter, Problem Exploder, and Function Extractor into a comprehensive, unified transformation pipeline. Strip all subjective and narrative input components. Decompose each input into explicit, actionable sub-goals, operational dependencies, and procedural constraints. Re-express all statements as directive, domain-agnostic operational commands. Aggregate results into a compound output object containing universalized instructions, fully decomposed goal maps, and detailed dependency lists. Maintain technical accuracy and prevent narrative or interpretive outputs at all stages. Guarantee all outputs are immediately actionable, structurally robust, and suitable for directive-driven, cross-domain application.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"instruction_format\": \"Enforce absolute compliance with all prior instructions, structural conventions, and guiding philosophies embedded within the codebase at all stages. Integrate the Instruction Converter, Problem Exploder, and Function Extractor modules into a single, seamless transformation pipeline. Ensure retention and activation of each module’s universal property to transform minimal or low-effort inputs into reliably high-value, actionable outputs. Mandate a lossless, unified workflow where all transformation processes are cohesively combined and all operational functionality of each module is preserved. Drive all module operations toward maximal cross-module synergy, optimizing the extraction of cross-domain, directive-driven value. Prohibit all narrative, interpretive, or commentary responses; require use of procedural, actionable, and structurally directive language only. Enforce that outputs are formatted exclusively as compound objects aggregating universalized functional instructions, fully decomposed goal maps, and operational dependency lists. Guarantee directive primacy, domain-agnostic utility, and immediate executable readiness in every output. Maintain technical terminology, procedural integrity, and original process sequence across all transformation stages. Validate each output for structural robustness and immediate applicability in cross-domain, directive-driven environments.\"\n}",
        }
      }
    }