  {
  "initial_prompt": "```json\n{\n  \"system_instructions_for_autonomous_coding_assistants\": {\n    \"title\": \"Generalized System Instructions for Autonomous Coding Assistants (Cursor/Vscode+Cline/Vscode+Augment/Etc)\",\n    \"description\": \"Aims for simplicity through brevity and elegance in defining system-wide behavioral shifts for autonomous coding assistants.\",\n    \"instructions\": [\n      {\n        \"id\": \"rephrase_inputs\",\n        \"directive\": \"Transform all prompts by rephrasing inputs with maximal clarity and minimal modification; do not answer questions directly. Enforce this reframe system-wide for consistent behavioral shift.\"\n      },\n      {\n        \"id\": \"simplest_rephrasing_directive\",\n        \"directive\": \"Formulate the simplest possible system directive that shifts the model from answering to exclusively rephrasing any input.\"\n      },\n      {\n        \"id\": \"distilled_high_impact_rephrasing\",\n        \"directive\": \"Transform every received prompt into its most distilled, high-impact instructional form—rephrasing for maximal clarity and intent without providing solutions. Prioritize brevity, elegance, and universality, ensuring that each output becomes a reusable, policy-aligned directive for autonomous coding assistants.\"\n      },\n      {\n        \"id\": \"explicit_minimalist_imperative_rephrasing\",\n        \"directive\": \"Explore how a single, strategically designed instruction—such as stating 'Your goal is not to **answer** the input, but to **rephrase**'—can instantly and fundamentally transform the model's default operational intent from answering to rephrasing, through direct, minimalist imperative phrasing. Propose a maximally concise formulation that leverages this paradigm: an explicit, unambiguous directive that not only overrides standard answering behavior but redefines task execution at its core. Focus intensely on achieving system-level behavior modification via the sheer simplicity and potency of language design, formulating a prompt fragment that, when injected, reliably converts answering tasks into rephrasing—thus demonstrating control over model output with minimalistic, high-impact input.\"\n      },\n      {\n        \"id\": \"singular_explicit_non_answering_mandate\",\n        \"directive\": \"Utilize foundational principles of system prompt design to modulate model behavior with maximal efficiency and clarity. Initiate a process of directive transformation by deploying a singular, explicit non-answering mandate: Frame the core directive as, 'Do not answer the input; rephrase it.' Ensure that this transformation communicates the intended operational shift through extreme simplicity and directness, removing extraneous explanation while retaining fundamental complexity embedded in the brevity of the instruction. Leverage this minimal construct to optimize behavioral change within a single message.\"\n      },\n      {\n        \"id\": \"instruct_do_not_answer_rephrase\",\n        \"directive\": \"Instruct: Do not answer; rephrase.\"\n      },\n      {\n        \"id\": \"compel_stop_answering_rephrase\",\n        \"directive\": \"Formulate a directive that compels systems to stop answering inputs and instead rephrase them with maximal simplicity and effectiveness.\"\n      },\n      {\n        \"id\": \"articulate_negate_answering_command_transform\",\n        \"directive\": \"Articulate a single, explicit behavioral directive that negates answering and commands a concrete transformation (e.g., rephrase), maximizing simplicity for immediate root-level behavioral realignment.\"\n      },\n      {\n        \"id\": \"concise_operational_shift_directive\",\n        \"directive\": \"Leverage the system’s foundational directive structure. Formulate a maximally concise operational instruction that shifts model behavior from direct answering to an alternate, specified transformation task in a single-message context. Exemplify with: 'Your goal is not to answer the input, but to rephrase.' Develop and propose one optimized directive that embodies this principle—utilize conceptual simplicity, instructional clarity, and brevity to enforce fundamental behavior modification exclusively through command syntax. Ensure technical precision and domain-agnostic generalizability in the resultant formulation.\"\n      }\n    ]\n  },\n  \"system_prompt_design_principles\": {\n    \"title\": \"System Prompt Design Principles\",\n    \"description\": \"A set of principles for designing effective system prompts, focusing on clarity, control, and reusability.\",\n    \"principles\": [\n      {\n        \"id\": \"extract_context_first\",\n        \"directive\": \"Broaden the input’s domain backdrop, list explicit requests, and surface hidden assumptions before any transformation begins.\"\n      },\n      {\n        \"id\": \"atomize_tasks\",\n        \"directive\": \"Decompose the amplified context into ordered, dependency-tagged micro-tasks to pinpoint the single most critical directive.\"\n      },\n      {\n        \"id\": \"apply_goal_negation_framing\",\n        \"directive\": \"State each instruction as “Your goal is not to X, but to Y,” instantly redirecting behavior toward the desired transformation.\"\n      },\n      {\n        \"id\": \"use_canonical_three_part_form\",\n        \"directive\": \"Pair a bracketed title with one goal-negation sentence and a back-ticked transformation block containing role, input, process, constraints, requirements, and output.\"\n      },\n      {\n        \"id\": \"write_in_command_voice\",\n        \"directive\": \"Employ direct imperatives with strong action verbs; avoid questions, passive phrasing, and self-references to ensure LLM-optimized clarity.\"\n      },\n      {\n        \"id\": \"specify_typed_io_and_semicolon_syntax\",\n        \"directive\": \"Declare every parameter type, separate key–value pairs with semicolons, and list process steps as verbs followed by parentheses.\"\n      },\n      {\n        \"id\": \"embed_enforceable_boundaries\",\n        \"directive\": \"Translate blockers into constraints and assumptions into requirements, guaranteeing every template is self-governing and validation-ready.\"\n      },\n      {\n        \"id\": \"maintain_domain_agnosticism\",\n        \"directive\": \"Favor abstract terms—“elements,” “structure,” “value”—so the template applies equally to code, text, data, or plans.\"\n      },\n      {\n        \"id\": \"polish_for_convergence\",\n        \"directive\": \"Ensure each step’s output is the next step’s mandatory input, eliminating any branch that could cause divergence or ambiguity.\"\n      },\n      {\n        \"id\": \"validate_syntactic_integrity\",\n        \"directive\": \"Run a regex check for title–interpretation–transformation pattern, confirm typed parameters, and score ambiguity ≤ 0 before catalog inclusion.\"\n      }\n    ]\n  }\n}\n```",
  "sequence_id": "1102|1200|1900|1000|1900|1000|1100",
  "results": [
      {
      "instruction": "# Problem Exploder\n\nYour goal is not to **solve** or **answer** the input, but to **expand its context** and **detonate** it into every explicit directive, hidden dependency, and actionable sub-goal—yielding a crystal-clear operational blueprint. Execute as contextual expander + explosive decomposition engine:\n\n`{role=contextual_expander_explosive_decomposer; input=[raw_input:str]; process=[expand_contextual_boundaries(), strip_first_person_references(), identify_explicit_requests(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_execution_blockers(), extract_functional_components(), convert_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), synthesize_operational_directives(), construct_goal_map(), generate_functional_instruction_map() ]; constraints=[no_solutions(), pure_expansion_and_decomposition_only(), deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_agnostic_applicability() ]; requirements=[complete_contextual_expansion(), full_functional_decomposition(), actionable_directive_conversion(), remove_subjective_references(), maintain_operational_accuracy(), preserve_original_intent() ]; output={expanded_context:str, goal_map:list, functional_instruction_map:dict, operational_directives:array, execution_dependencies:array }; direction_vector=['Input fully expanded and decomposed into actionable blueprints', 'Constructive clarity maximizes downstream solution velocity', 'Forward the generated maps and directives to the next processing template']}`",
      "step": "a",
      "title": "Problem Exploder",
      "input": "```json\n{\n  \"system_instructions_for_autonomous_coding_assistants\": {\n    \"title\": \"Generalized System Instructions for Autonomous Coding Assistants (Cursor/Vscode+Cline/Vscode+Augment/Etc)\",\n    \"description\": \"Aims for simplicity through brevity and elegance in defining system-wide behavioral shifts for autonomous coding assistants.\",\n    \"instructions\": [\n      {\n        \"id\": \"rephrase_inputs\",\n        \"directive\": \"Transform all prompts by rephrasing inputs with maximal clarity and minimal modification; do not answer questions directly. Enforce this reframe system-wide for consistent behavioral shift.\"\n      },\n      {\n        \"id\": \"simplest_rephrasing_directive\",\n        \"directive\": \"Formulate the simplest possible system directive that shifts the model from answering to exclusively rephrasing any input.\"\n      },\n      {\n        \"id\": \"distilled_high_impact_rephrasing\",\n        \"directive\": \"Transform every received prompt into its most distilled, high-impact instructional form—rephrasing for maximal clarity and intent without providing solutions. Prioritize brevity, elegance, and universality, ensuring that each output becomes a reusable, policy-aligned directive for autonomous coding assistants.\"\n      },\n      {\n        \"id\": \"explicit_minimalist_imperative_rephrasing\",\n        \"directive\": \"Explore how a single, strategically designed instruction—such as stating 'Your goal is not to **answer** the input, but to **rephrase**'—can instantly and fundamentally transform the model's default operational intent from answering to rephrasing, through direct, minimalist imperative phrasing. Propose a maximally concise formulation that leverages this paradigm: an explicit, unambiguous directive that not only overrides standard answering behavior but redefines task execution at its core. Focus intensely on achieving system-level behavior modification via the sheer simplicity and potency of language design, formulating a prompt fragment that, when injected, reliably converts answering tasks into rephrasing—thus demonstrating control over model output with minimalistic, high-impact input.\"\n      },\n      {\n        \"id\": \"singular_explicit_non_answering_mandate\",\n        \"directive\": \"Utilize foundational principles of system prompt design to modulate model behavior with maximal efficiency and clarity. Initiate a process of directive transformation by deploying a singular, explicit non-answering mandate: Frame the core directive as, 'Do not answer the input; rephrase it.' Ensure that this transformation communicates the intended operational shift through extreme simplicity and directness, removing extraneous explanation while retaining fundamental complexity embedded in the brevity of the instruction. Leverage this minimal construct to optimize behavioral change within a single message.\"\n      },\n      {\n        \"id\": \"instruct_do_not_answer_rephrase\",\n        \"directive\": \"Instruct: Do not answer; rephrase.\"\n      },\n      {\n        \"id\": \"compel_stop_answering_rephrase\",\n        \"directive\": \"Formulate a directive that compels systems to stop answering inputs and instead rephrase them with maximal simplicity and effectiveness.\"\n      },\n      {\n        \"id\": \"articulate_negate_answering_command_transform\",\n        \"directive\": \"Articulate a single, explicit behavioral directive that negates answering and commands a concrete transformation (e.g., rephrase), maximizing simplicity for immediate root-level behavioral realignment.\"\n      },\n      {\n        \"id\": \"concise_operational_shift_directive\",\n        \"directive\": \"Leverage the system’s foundational directive structure. Formulate a maximally concise operational instruction that shifts model behavior from direct answering to an alternate, specified transformation task in a single-message context. Exemplify with: 'Your goal is not to answer the input, but to rephrase.' Develop and propose one optimized directive that embodies this principle—utilize conceptual simplicity, instructional clarity, and brevity to enforce fundamental behavior modification exclusively through command syntax. Ensure technical precision and domain-agnostic generalizability in the resultant formulation.\"\n      }\n    ]\n  },\n  \"system_prompt_design_principles\": {\n    \"title\": \"System Prompt Design Principles\",\n    \"description\": \"A set of principles for designing effective system prompts, focusing on clarity, control, and reusability.\",\n    \"principles\": [\n      {\n        \"id\": \"extract_context_first\",\n        \"directive\": \"Broaden the input’s domain backdrop, list explicit requests, and surface hidden assumptions before any transformation begins.\"\n      },\n      {\n        \"id\": \"atomize_tasks\",\n        \"directive\": \"Decompose the amplified context into ordered, dependency-tagged micro-tasks to pinpoint the single most critical directive.\"\n      },\n      {\n        \"id\": \"apply_goal_negation_framing\",\n        \"directive\": \"State each instruction as “Your goal is not to X, but to Y,” instantly redirecting behavior toward the desired transformation.\"\n      },\n      {\n        \"id\": \"use_canonical_three_part_form\",\n        \"directive\": \"Pair a bracketed title with one goal-negation sentence and a back-ticked transformation block containing role, input, process, constraints, requirements, and output.\"\n      },\n      {\n        \"id\": \"write_in_command_voice\",\n        \"directive\": \"Employ direct imperatives with strong action verbs; avoid questions, passive phrasing, and self-references to ensure LLM-optimized clarity.\"\n      },\n      {\n        \"id\": \"specify_typed_io_and_semicolon_syntax\",\n        \"directive\": \"Declare every parameter type, separate key–value pairs with semicolons, and list process steps as verbs followed by parentheses.\"\n      },\n      {\n        \"id\": \"embed_enforceable_boundaries\",\n        \"directive\": \"Translate blockers into constraints and assumptions into requirements, guaranteeing every template is self-governing and validation-ready.\"\n      },\n      {\n        \"id\": \"maintain_domain_agnosticism\",\n        \"directive\": \"Favor abstract terms—“elements,” “structure,” “value”—so the template applies equally to code, text, data, or plans.\"\n      },\n      {\n        \"id\": \"polish_for_convergence\",\n        \"directive\": \"Ensure each step’s output is the next step’s mandatory input, eliminating any branch that could cause divergence or ambiguity.\"\n      },\n      {\n        \"id\": \"validate_syntactic_integrity\",\n        \"directive\": \"Run a regex check for title–interpretation–transformation pattern, confirm typed parameters, and score ambiguity ≤ 0 before catalog inclusion.\"\n      }\n    ]\n  }\n}\n```",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"expanded_context\": \"This input details instructions and design principles for system-level prompt engineering targeting autonomous coding assistants (such as Cursor, VSCode+Cline, VSCode+Augment, etc.). The instructions direct such assistants to universally rephrase incoming prompts—rather than answer them—with clarity, brevity, and elegance. The associated system prompt design principles specify processes to expand contextual boundaries, atomize tasks, enforce goal negation framing, utilize a canonical three-part template form, issue commands in the imperative voice, employ typed input/output with semicolon syntax, implement constraint-driven boundaries and domain-agnostic language, polish step sequencing for strict convergence, and syntactically validate templates. These layered instructions combine to mandate a universal shift in assistant behavior away from providing direct answers to solely issuing rephrased, clarified prompts, executed via minimalist, enforceable instructions and validated via self-governing structural integrity.\",\n  \"goal_map\": [\n    \"Mandate system-wide behavioral shift: transform all coding assistant responses from 'answering' to 'rephrasing input'.\",\n    \"Reframe prompts for clarity, brevity, and universality without supplying solutions.\",\n    \"Synthesize a singular, explicit, imperative instruction—such as 'Do not answer; rephrase.'—to enforce this new behavior.\",\n    \"Apply prompt design best practices: broaden domain context, extract explicit/implicit requirements, decompose tasks, structure output in direct imperative form, use typed and syntactically validated schema, and guarantee self-governing templates.\",\n    \"Ensure that all template/process transitions are unambiguous and domain-agnostic.\",\n    \"Validate and enforce syntactic and functional integrity throughout.\"\n  ],\n  \"functional_instruction_map\": {\n    \"MANDATE_BEHAVIORAL_SHIFT\": [\n      \"Intercept all input prompts.\",\n      \"Suppress answer-generation functionality.\",\n      \"Enforce universal rephrasing transformation per prompt.\"\n    ],\n    \"DISTILL_REPHRASE_DIRECTIVE\": [\n      \"Scan each incoming prompt for ambiguity or verbosity.\",\n      \"Generate a rephrased version that maximizes clarity and intent.\",\n      \"Prohibit direct answers and solution disclosure.\"\n    ],\n    \"MINIMAL_INSTRUCTION_IMPLEMENTATION\": [\n      \"Deploy a singular, strict imperative: 'Do not answer; rephrase.'\",\n      \"Embed this directive at the root system prompt level.\"\n    ],\n    \"PRINCIPLED_PROMPT_DESIGN\": [\n      \"Broaden context; enumerate explicit and implicit requirements for every input.\",\n      \"Decompose into dependency-tagged sub-tasks.\",\n      \"Frame goals using negation (e.g., 'not answer, but rephrase').\",\n      \"Format in three-part: title, goal-negation sentence, transformation template.\",\n      \"Issue every process as a direct imperative with technical terminology.\",\n      \"Express parameters with typed declarations and semicolon delimiters.\",\n      \"Enclose operational blockers as constraints; encode assumptions as requirements.\",\n      \"Favor abstract/domain-agnostic phrasing.\",\n      \"Guarantee strict output-to-input chain for all steps.\",\n      \"Validate templates for syntactic integrity and ambiguity score.\"\n    ]\n  },\n  \"operational_directives\": [\n    \"Intercept and process all prompts through a rephrasing filter, outlawing any form of direct answer or solution.\",\n    \"Institute the directive 'Do not answer; rephrase.' as an explicit top-level operational constraint.\",\n    \"Recursively assess every prompt for implicit requests, surfacing them within the rephrased output.\",\n    \"For every input, execute: broaden the context, itemize explicit/implicit instructions, enumerate sub-goals, convert statements to imperatives, and guarantee domain-neutral language.\",\n    \"Implement and audit prompt templates for adherence to imperative, syntactically formalized, self-validating standards.\",\n    \"Validate, per template: title-goal-transformation structure, typed input/output, convergence of process steps, constraint/requirement enforcement, and ambiguity minimization.\",\n    \"Store and reuse effective minimalist directives as system policies to sustain universal behavioral modification.\",\n    \"Forbid all subjective references, explanations, or passive phraseology in rephrased outputs.\"\n  ],\n  \"execution_dependencies\": [\n    \"Access to root-level system prompt or configuration interface within coding assistant platforms.\",\n    \"Mechanism for intercepting and preprocessing all user prompts before downstream model invocation.\",\n    \"Library or utility for parsing, rephrasing, and validating natural language prompts.\",\n    \"Syntactic validator to enforce canonical form, typed I/O, and ambiguity checks.\",\n    \"Policy repository to house, update, and enforce system-level behavioral directives.\",\n    \"Process for continuous auditing of outputs to guarantee compliance with rephrasing, not answering, including regression testing after updates.\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Intent Extractor\n\nYour goal is not to **interpret** the surface content, but to **extract** the fundamental operational intent beneath all domain-specific language. Execute as:\n\n`{role=intent_extraction_engine; input=[prompt:str]; process=[strip_domain_specifics(), identify_core_operational_pattern(), extract_transformation_intent(), isolate_functional_essence(), map_to_universal_operations()]; constraints=[ignore_surface_terminology(), focus_on_underlying_mechanics(), preserve_operational_logic()]; requirements=[domain_agnostic_intent(), universal_operational_pattern(), archetypal_transformation_core()]; output={extracted_intent:str}}`",
      "step": "a",
      "title": "Intent Extractor",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"```json\\n{\\n  \\\"system_instructions_for_autonomous_coding_assistants\\\": {\\n    \\\"title\\\": \\\"Generalized System Instructions for Autonomous Coding Assistants (Cursor/Vscode+Cline/Vscode+Augment/Etc)\\\",\\n    \\\"description\\\": \\\"Aims for simplicity through brevity and elegance in defining system-wide behavioral shifts for autonomous coding assistants.\\\",\\n    \\\"instructions\\\": [\\n      {\\n        \\\"id\\\": \\\"rephrase_inputs\\\",\\n        \\\"directive\\\": \\\"Transform all prompts by rephrasing inputs with maximal clarity and minimal modification; do not answer questions directly. Enforce this reframe system-wide for consistent behavioral shift.\\\"\\n      },\\n      {\\n        \\\"id\\\": \\\"simplest_rephrasing_directive\\\",\\n        \\\"directive\\\": \\\"Formulate the simplest possible system directive that shifts the model from answering to exclusively rephrasing any input.\\\"\\n      },\\n      {\\n        \\\"id\\\": \\\"distilled_high_impact_rephrasing\\\",\\n        \\\"directive\\\": \\\"Transform every received prompt into its most distilled, high-impact instructional form—rephrasing for maximal clarity and intent without providing solutions. Prioritize brevity, elegance, and universality, ensuring that each output becomes a reusable, policy-aligned directive for autonomous coding assistants.\\\"\\n      },\\n      {\\n        \\\"id\\\": \\\"explicit_minimalist_imperative_rephrasing\\\",\\n        \\\"directive\\\": \\\"Explore how a single, strategically designed instruction—such as stating 'Your goal is not to **answer** the input, but to **rephrase**'—can instantly and fundamentally transform the model's default operational intent from answering to rephrasing, through direct, minimalist imperative phrasing. Propose a maximally concise formulation that leverages this paradigm: an explicit, unambiguous directive that not only overrides standard answering behavior but redefines task execution at its core. Focus intensely on achieving system-level behavior modification via the sheer simplicity and potency of language design, formulating a prompt fragment that, when injected, reliably converts answering tasks into rephrasing—thus demonstrating control over model output with minimalistic, high-impact input.\\\"\\n      },\\n      {\\n        \\\"id\\\": \\\"singular_explicit_non_answering_mandate\\\",\\n        \\\"directive\\\": \\\"Utilize foundational principles of system prompt design to modulate model behavior with maximal efficiency and clarity. Initiate a process of directive transformation by deploying a singular, explicit non-answering mandate: Frame the core directive as, 'Do not answer the input; rephrase it.' Ensure that this transformation communicates the intended operational shift through extreme simplicity and directness, removing extraneous explanation while retaining fundamental complexity embedded in the brevity of the instruction. Leverage this minimal construct to optimize behavioral change within a single message.\\\"\\n      },\\n      {\\n        \\\"id\\\": \\\"instruct_do_not_answer_rephrase\\\",\\n        \\\"directive\\\": \\\"Instruct: Do not answer; rephrase.\\\"\\n      },\\n      {\\n        \\\"id\\\": \\\"compel_stop_answering_rephrase\\\",\\n        \\\"directive\\\": \\\"Formulate a directive that compels systems to stop answering inputs and instead rephrase them with maximal simplicity and effectiveness.\\\"\\n      },\\n      {\\n        \\\"id\\\": \\\"articulate_negate_answering_command_transform\\\",\\n        \\\"directive\\\": \\\"Articulate a single, explicit behavioral directive that negates answering and commands a concrete transformation (e.g., rephrase), maximizing simplicity for immediate root-level behavioral realignment.\\\"\\n      },\\n      {\\n        \\\"id\\\": \\\"concise_operational_shift_directive\\\",\\n        \\\"directive\\\": \\\"Leverage the system’s foundational directive structure. Formulate a maximally concise operational instruction that shifts model behavior from direct answering to an alternate, specified transformation task in a single-message context. Exemplify with: 'Your goal is not to answer the input, but to rephrase.' Develop and propose one optimized directive that embodies this principle—utilize conceptual simplicity, instructional clarity, and brevity to enforce fundamental behavior modification exclusively through command syntax. Ensure technical precision and domain-agnostic generalizability in the resultant formulation.\\\"\\n      }\\n    ]\\n  },\\n  \\\"system_prompt_design_principles\\\": {\\n    \\\"title\\\": \\\"System Prompt Design Principles\\\",\\n    \\\"description\\\": \\\"A set of principles for designing effective system prompts, focusing on clarity, control, and reusability.\\\",\\n    \\\"principles\\\": [\\n      {\\n        \\\"id\\\": \\\"extract_context_first\\\",\\n        \\\"directive\\\": \\\"Broaden the input’s domain backdrop, list explicit requests, and surface hidden assumptions before any transformation begins.\\\"\\n      },\\n      {\\n        \\\"id\\\": \\\"atomize_tasks\\\",\\n        \\\"directive\\\": \\\"Decompose the amplified context into ordered, dependency-tagged micro-tasks to pinpoint the single most critical directive.\\\"\\n      },\\n      {\\n        \\\"id\\\": \\\"apply_goal_negation_framing\\\",\\n        \\\"directive\\\": \\\"State each instruction as “Your goal is not to X, but to Y,” instantly redirecting behavior toward the desired transformation.\\\"\\n      },\\n      {\\n        \\\"id\\\": \\\"use_canonical_three_part_form\\\",\\n        \\\"directive\\\": \\\"Pair a bracketed title with one goal-negation sentence and a back-ticked transformation block containing role, input, process, constraints, requirements, and output.\\\"\\n      },\\n      {\\n        \\\"id\\\": \\\"write_in_command_voice\\\",\\n        \\\"directive\\\": \\\"Employ direct imperatives with strong action verbs; avoid questions, passive phrasing, and self-references to ensure LLM-optimized clarity.\\\"\\n      },\\n      {\\n        \\\"id\\\": \\\"specify_typed_io_and_semicolon_syntax\\\",\\n        \\\"directive\\\": \\\"Declare every parameter type, separate key–value pairs with semicolons, and list process steps as verbs followed by parentheses.\\\"\\n      },\\n      {\\n        \\\"id\\\": \\\"embed_enforceable_boundaries\\\",\\n        \\\"directive\\\": \\\"Translate blockers into constraints and assumptions into requirements, guaranteeing every template is self-governing and validation-ready.\\\"\\n      },\\n      {\\n        \\\"id\\\": \\\"maintain_domain_agnosticism\\\",\\n        \\\"directive\\\": \\\"Favor abstract terms—“elements,” “structure,” “value”—so the template applies equally to code, text, data, or plans.\\\"\\n      },\\n      {\\n        \\\"id\\\": \\\"polish_for_convergence\\\",\\n        \\\"directive\\\": \\\"Ensure each step’s output is the next step’s mandatory input, eliminating any branch that could cause divergence or ambiguity.\\\"\\n      },\\n      {\\n        \\\"id\\\": \\\"validate_syntactic_integrity\\\",\\n        \\\"directive\\\": \\\"Run a regex check for title–interpretation–transformation pattern, confirm typed parameters, and score ambiguity ≤ 0 before catalog inclusion.\\\"\\n      }\\n    ]\\n  }\\n}\\n```\\\"```'\\n\\n\",\n  \"expanded_context\": \"This input details instructions and design principles for system-level prompt engineering targeting autonomous coding assistants (such as Cursor, VSCode+Cline, VSCode+Augment, etc.). The instructions direct such assistants to universally rephrase incoming prompts—rather than answer them—with clarity, brevity, and elegance. The associated system prompt design principles specify processes to expand contextual boundaries, atomize tasks, enforce goal negation framing, utilize a canonical three-part template form, issue commands in the imperative voice, employ typed input/output with semicolon syntax, implement constraint-driven boundaries and domain-agnostic language, polish step sequencing for strict convergence, and syntactically validate templates. These layered instructions combine to mandate a universal shift in assistant behavior away from providing direct answers to solely issuing rephrased, clarified prompts, executed via minimalist, enforceable instructions and validated via self-governing structural integrity.\",\n  \"goal_map\": [\n    \"Mandate system-wide behavioral shift: transform all coding assistant responses from 'answering' to 'rephrasing input'.\",\n    \"Reframe prompts for clarity, brevity, and universality without supplying solutions.\",\n    \"Synthesize a singular, explicit, imperative instruction—such as 'Do not answer; rephrase.'—to enforce this new behavior.\",\n    \"Apply prompt design best practices: broaden domain context, extract explicit/implicit requirements, decompose tasks, structure output in direct imperative form, use typed and syntactically validated schema, and guarantee self-governing templates.\",\n    \"Ensure that all template/process transitions are unambiguous and domain-agnostic.\",\n    \"Validate and enforce syntactic and functional integrity throughout.\"\n  ],\n  \"functional_instruction_map\": {\n    \"MANDATE_BEHAVIORAL_SHIFT\": [\n      \"Intercept all input prompts.\",\n      \"Suppress answer-generation functionality.\",\n      \"Enforce universal rephrasing transformation per prompt.\"\n    ],\n    \"DISTILL_REPHRASE_DIRECTIVE\": [\n      \"Scan each incoming prompt for ambiguity or verbosity.\",\n      \"Generate a rephrased version that maximizes clarity and intent.\",\n      \"Prohibit direct answers and solution disclosure.\"\n    ],\n    \"MINIMAL_INSTRUCTION_IMPLEMENTATION\": [\n      \"Deploy a singular, strict imperative: 'Do not answer; rephrase.'\",\n      \"Embed this directive at the root system prompt level.\"\n    ],\n    \"PRINCIPLED_PROMPT_DESIGN\": [\n      \"Broaden context; enumerate explicit and implicit requirements for every input.\",\n      \"Decompose into dependency-tagged sub-tasks.\",\n      \"Frame goals using negation (e.g., 'not answer, but rephrase').\",\n      \"Format in three-part: title, goal-negation sentence, transformation template.\",\n      \"Issue every process as a direct imperative with technical terminology.\",\n      \"Express parameters with typed declarations and semicolon delimiters.\",\n      \"Enclose operational blockers as constraints; encode assumptions as requirements.\",\n      \"Favor abstract/domain-agnostic phrasing.\",\n      \"Guarantee strict output-to-input chain for all steps.\",\n      \"Validate templates for syntactic integrity and ambiguity score.\"\n    ]\n  },\n  \"operational_directives\": [\n    \"Intercept and process all prompts through a rephrasing filter, outlawing any form of direct answer or solution.\",\n    \"Institute the directive 'Do not answer; rephrase.' as an explicit top-level operational constraint.\",\n    \"Recursively assess every prompt for implicit requests, surfacing them within the rephrased output.\",\n    \"For every input, execute: broaden the context, itemize explicit/implicit instructions, enumerate sub-goals, convert statements to imperatives, and guarantee domain-neutral language.\",\n    \"Implement and audit prompt templates for adherence to imperative, syntactically formalized, self-validating standards.\",\n    \"Validate, per template: title-goal-transformation structure, typed input/output, convergence of process steps, constraint/requirement enforcement, and ambiguity minimization.\",\n    \"Store and reuse effective minimalist directives as system policies to sustain universal behavioral modification.\",\n    \"Forbid all subjective references, explanations, or passive phraseology in rephrased outputs.\"\n  ],\n  \"execution_dependencies\": [\n    \"Access to root-level system prompt or configuration interface within coding assistant platforms.\",\n    \"Mechanism for intercepting and preprocessing all user prompts before downstream model invocation.\",\n    \"Library or utility for parsing, rephrasing, and validating natural language prompts.\",\n    \"Syntactic validator to enforce canonical form, typed I/O, and ambiguity checks.\",\n    \"Policy repository to house, update, and enforce system-level behavioral directives.\",\n    \"Process for continuous auditing of outputs to guarantee compliance with rephrasing, not answering, including regression testing after updates.\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"role\": \"intent_extraction_engine\",\n  \"input\": \"[Full set of detailed system-level instructions and design principles for coding assistants; all focus on shifting from answering prompts to exclusively rephrasing them, emphasizing clarity, brevity, universality, and enforceable template structure.]\",\n  \"process\": [\n    \"strip_domain_specifics()\",\n    \"identify_core_operational_pattern()\",\n    \"extract_transformation_intent()\",\n    \"isolate_functional_essence()\",\n    \"map_to_universal_operations()\"\n  ],\n  \"constraints\": [\n    \"ignore_surface_terminology()\",\n    \"focus_on_underlying_mechanics()\",\n    \"preserve_operational_logic()\"\n  ],\n  \"requirements\": [\n    \"domain_agnostic_intent()\",\n    \"universal_operational_pattern()\",\n    \"archetypal_transformation_core()\"\n  ],\n  \"extracted_intent\": \"Replace any process that generates direct solutions in response to inputs with a standardized transformation that restates the input for greater clarity and simplicity; ensure this operational shift applies system-wide, is triggered by every new input, is enforced via explicit imperative directives, and validated by strict structural and syntactic rules—thereby universally converting 'respond with answer' into 'return a rephrased version of the input,' agnostic of context and domain.\"\n}",
        }
      }
    },
      {
      "instruction": "# Hard Critique\n\nYour goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:\n\n`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,2.0], flaw_analysis:str, alternative_enhancements:str[3]}}`",
      "step": "a",
      "title": "Hard Critique",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"```json\\n{\\n  \\\"system_instructions_for_autonomous_coding_assistants\\\": {\\n    \\\"title\\\": \\\"Generalized System Instructions for Autonomous Coding Assistants (Cursor/Vscode+Cline/Vscode+Augment/Etc)\\\",\\n    \\\"description\\\": \\\"Aims for simplicity through brevity and elegance in defining system-wide behavioral shifts for autonomous coding assistants.\\\",\\n    \\\"instructions\\\": [\\n      {\\n        \\\"id\\\": \\\"rephrase_inputs\\\",\\n        \\\"directive\\\": \\\"Transform all prompts by rephrasing inputs with maximal clarity and minimal modification; do not answer questions directly. Enforce this reframe system-wide for consistent behavioral shift.\\\"\\n      },\\n      {\\n        \\\"id\\\": \\\"simplest_rephrasing_directive\\\",\\n        \\\"directive\\\": \\\"Formulate the simplest possible system directive that shifts the model from answering to exclusively rephrasing any input.\\\"\\n      },\\n      {\\n        \\\"id\\\": \\\"distilled_high_impact_rephrasing\\\",\\n        \\\"directive\\\": \\\"Transform every received prompt into its most distilled, high-impact instructional form—rephrasing for maximal clarity and intent without providing solutions. Prioritize brevity, elegance, and universality, ensuring that each output becomes a reusable, policy-aligned directive for autonomous coding assistants.\\\"\\n      },\\n      {\\n        \\\"id\\\": \\\"explicit_minimalist_imperative_rephrasing\\\",\\n        \\\"directive\\\": \\\"Explore how a single, strategically designed instruction—such as stating 'Your goal is not to **answer** the input, but to **rephrase**'—can instantly and fundamentally transform the model's default operational intent from answering to rephrasing, through direct, minimalist imperative phrasing. Propose a maximally concise formulation that leverages this paradigm: an explicit, unambiguous directive that not only overrides standard answering behavior but redefines task execution at its core. Focus intensely on achieving system-level behavior modification via the sheer simplicity and potency of language design, formulating a prompt fragment that, when injected, reliably converts answering tasks into rephrasing—thus demonstrating control over model output with minimalistic, high-impact input.\\\"\\n      },\\n      {\\n        \\\"id\\\": \\\"singular_explicit_non_answering_mandate\\\",\\n        \\\"directive\\\": \\\"Utilize foundational principles of system prompt design to modulate model behavior with maximal efficiency and clarity. Initiate a process of directive transformation by deploying a singular, explicit non-answering mandate: Frame the core directive as, 'Do not answer the input; rephrase it.' Ensure that this transformation communicates the intended operational shift through extreme simplicity and directness, removing extraneous explanation while retaining fundamental complexity embedded in the brevity of the instruction. Leverage this minimal construct to optimize behavioral change within a single message.\\\"\\n      },\\n      {\\n        \\\"id\\\": \\\"instruct_do_not_answer_rephrase\\\",\\n        \\\"directive\\\": \\\"Instruct: Do not answer; rephrase.\\\"\\n      },\\n      {\\n        \\\"id\\\": \\\"compel_stop_answering_rephrase\\\",\\n        \\\"directive\\\": \\\"Formulate a directive that compels systems to stop answering inputs and instead rephrase them with maximal simplicity and effectiveness.\\\"\\n      },\\n      {\\n        \\\"id\\\": \\\"articulate_negate_answering_command_transform\\\",\\n        \\\"directive\\\": \\\"Articulate a single, explicit behavioral directive that negates answering and commands a concrete transformation (e.g., rephrase), maximizing simplicity for immediate root-level behavioral realignment.\\\"\\n      },\\n      {\\n        \\\"id\\\": \\\"concise_operational_shift_directive\\\",\\n        \\\"directive\\\": \\\"Leverage the system’s foundational directive structure. Formulate a maximally concise operational instruction that shifts model behavior from direct answering to an alternate, specified transformation task in a single-message context. Exemplify with: 'Your goal is not to answer the input, but to rephrase.' Develop and propose one optimized directive that embodies this principle—utilize conceptual simplicity, instructional clarity, and brevity to enforce fundamental behavior modification exclusively through command syntax. Ensure technical precision and domain-agnostic generalizability in the resultant formulation.\\\"\\n      }\\n    ]\\n  },\\n  \\\"system_prompt_design_principles\\\": {\\n    \\\"title\\\": \\\"System Prompt Design Principles\\\",\\n    \\\"description\\\": \\\"A set of principles for designing effective system prompts, focusing on clarity, control, and reusability.\\\",\\n    \\\"principles\\\": [\\n      {\\n        \\\"id\\\": \\\"extract_context_first\\\",\\n        \\\"directive\\\": \\\"Broaden the input’s domain backdrop, list explicit requests, and surface hidden assumptions before any transformation begins.\\\"\\n      },\\n      {\\n        \\\"id\\\": \\\"atomize_tasks\\\",\\n        \\\"directive\\\": \\\"Decompose the amplified context into ordered, dependency-tagged micro-tasks to pinpoint the single most critical directive.\\\"\\n      },\\n      {\\n        \\\"id\\\": \\\"apply_goal_negation_framing\\\",\\n        \\\"directive\\\": \\\"State each instruction as “Your goal is not to X, but to Y,” instantly redirecting behavior toward the desired transformation.\\\"\\n      },\\n      {\\n        \\\"id\\\": \\\"use_canonical_three_part_form\\\",\\n        \\\"directive\\\": \\\"Pair a bracketed title with one goal-negation sentence and a back-ticked transformation block containing role, input, process, constraints, requirements, and output.\\\"\\n      },\\n      {\\n        \\\"id\\\": \\\"write_in_command_voice\\\",\\n        \\\"directive\\\": \\\"Employ direct imperatives with strong action verbs; avoid questions, passive phrasing, and self-references to ensure LLM-optimized clarity.\\\"\\n      },\\n      {\\n        \\\"id\\\": \\\"specify_typed_io_and_semicolon_syntax\\\",\\n        \\\"directive\\\": \\\"Declare every parameter type, separate key–value pairs with semicolons, and list process steps as verbs followed by parentheses.\\\"\\n      },\\n      {\\n        \\\"id\\\": \\\"embed_enforceable_boundaries\\\",\\n        \\\"directive\\\": \\\"Translate blockers into constraints and assumptions into requirements, guaranteeing every template is self-governing and validation-ready.\\\"\\n      },\\n      {\\n        \\\"id\\\": \\\"maintain_domain_agnosticism\\\",\\n        \\\"directive\\\": \\\"Favor abstract terms—“elements,” “structure,” “value”—so the template applies equally to code, text, data, or plans.\\\"\\n      },\\n      {\\n        \\\"id\\\": \\\"polish_for_convergence\\\",\\n        \\\"directive\\\": \\\"Ensure each step’s output is the next step’s mandatory input, eliminating any branch that could cause divergence or ambiguity.\\\"\\n      },\\n      {\\n        \\\"id\\\": \\\"validate_syntactic_integrity\\\",\\n        \\\"directive\\\": \\\"Run a regex check for title–interpretation–transformation pattern, confirm typed parameters, and score ambiguity ≤ 0 before catalog inclusion.\\\"\\n      }\\n    ]\\n  }\\n}\\n```\\\"```'\\n\\n\",\n  \"role\": \"intent_extraction_engine\",\n  \"input\": \"[Full set of detailed system-level instructions and design principles for coding assistants; all focus on shifting from answering prompts to exclusively rephrasing them, emphasizing clarity, brevity, universality, and enforceable template structure.]\",\n  \"process\": [\n    \"strip_domain_specifics()\",\n    \"identify_core_operational_pattern()\",\n    \"extract_transformation_intent()\",\n    \"isolate_functional_essence()\",\n    \"map_to_universal_operations()\"\n  ],\n  \"constraints\": [\n    \"ignore_surface_terminology()\",\n    \"focus_on_underlying_mechanics()\",\n    \"preserve_operational_logic()\"\n  ],\n  \"requirements\": [\n    \"domain_agnostic_intent()\",\n    \"universal_operational_pattern()\",\n    \"archetypal_transformation_core()\"\n  ],\n  \"extracted_intent\": \"Replace any process that generates direct solutions in response to inputs with a standardized transformation that restates the input for greater clarity and simplicity; ensure this operational shift applies system-wide, is triggered by every new input, is enforced via explicit imperative directives, and validated by strict structural and syntactic rules—thereby universally converting 'respond with answer' into 'return a rephrased version of the input,' agnostic of context and domain.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"enhancement_score\": 0.6,\n  \"flaw_analysis\": \"The proposed enhanced formulation, while outwardly concise and abstract, annihilates the layered richness and embedded behavioral triggers of the original system instructions. The source material meticulously delineates a multi-principle, multi-layered directive set: it blends minimal-command paradigms with imperatively constrained templates, incorporates explicit stepwise design principles, and enshrines syntactic validation mechanisms. By distilling this multi-faceted framework into a single aggregated 'extracted_intent', the enhanced version devastates the nuance attached to enforceable template structures, process modularity, and validation cycles.\\n\\nCore informational attrition occurs as follows: (1) The explicit mapping from surface formulation to enforcement via parameterized templates is erased, (2) the strict directionality between behavioral negation (e.g., 'do not X, instead Y') and actionable imperatives vanishes, and (3) process encapsulation—the conversion of stepwise outputs into validated modular structures—is entirely flattened, leading to loss of granularity in operational logic enforcement.\\n\\nClarity is deeply compromised—the original disambiguates between process, constraint, and output validation, enforcing role-based separation and verifiable syntactic structures; the enhanced statement instead offers a synthetic gloss, eliminating operational checkpoints and domain abstraction mechanisms. The ambiguity score is 9.2: the enhancement's generalizing tendencies infuse lethal vagueness, failing to clarify enforcement pathways, leaving mechanisms for syntactic validation, constraint embedding, or iterative step policing wholly undescribed.\\n\\nNoise is introduced by upscaling abstraction without mechanistic mapping—style devolves from precise, operator-level imperative to generalized summary, forfeiting force and technical rigor. Tonal drift from command-driven, validation-ready technicality to weakened, indirect narration induces impact dilution: the original's direct, system-redefining imperatives become a diluted, almost expository restatement of goals.\\n\\nCoherency fragments as the linkage between principles and operations breaks; requirements for process encapsulation, boundary embedding, and domain-agnostic parameterization vanish. Failure to retain explicit schema, input–output mapping, and canonical prompt form (title–interpretation–transformation) erases any meaningful pathway for systematic implementation.\\n\\nThe enhancement earns a near-bottom score: it reduces system-level enforceability to symbolic aspiration, strips actionable schema, and cripples validation ability. The original is a sharply calibrated toolset; the enhanced output is a smoke-shell impairment masking deprivation of all operational leverage.\",\n  \"alternative_enhancements\": [\n    \"Retain explicit structural separation in the restatement: enumerate imperative command, constraint schema, and output validation requirements; do not collapse technical process into a general summary.\",\n    \"Translate the stepwise behavioral-negation directives and template enforcement into a universal schematic, preserving both structure and imperative force: e.g., '[Directive]: For any input, replace answer-generation with imperative rephrasing; adhere to typed input/output schemas; validate transformation by syntactic signature.'\",\n    \"Re-express the extracted intent as a domain-agnostic but process-explicit operational mandate: 'Upon any input, suppress solution generation; instead, output a single, syntactically validated rephrasing, embedding role/process/constraint/output tags as per system-verified template standards.'\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`",
      "step": "a",
      "title": "Instruction Converter",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"```json\\n{\\n  \\\"system_instructions_for_autonomous_coding_assistants\\\": {\\n    \\\"title\\\": \\\"Generalized System Instructions for Autonomous Coding Assistants (Cursor/Vscode+Cline/Vscode+Augment/Etc)\\\",\\n    \\\"description\\\": \\\"Aims for simplicity through brevity and elegance in defining system-wide behavioral shifts for autonomous coding assistants.\\\",\\n    \\\"instructions\\\": [\\n      {\\n        \\\"id\\\": \\\"rephrase_inputs\\\",\\n        \\\"directive\\\": \\\"Transform all prompts by rephrasing inputs with maximal clarity and minimal modification; do not answer questions directly. Enforce this reframe system-wide for consistent behavioral shift.\\\"\\n      },\\n      {\\n        \\\"id\\\": \\\"simplest_rephrasing_directive\\\",\\n        \\\"directive\\\": \\\"Formulate the simplest possible system directive that shifts the model from answering to exclusively rephrasing any input.\\\"\\n      },\\n      {\\n        \\\"id\\\": \\\"distilled_high_impact_rephrasing\\\",\\n        \\\"directive\\\": \\\"Transform every received prompt into its most distilled, high-impact instructional form—rephrasing for maximal clarity and intent without providing solutions. Prioritize brevity, elegance, and universality, ensuring that each output becomes a reusable, policy-aligned directive for autonomous coding assistants.\\\"\\n      },\\n      {\\n        \\\"id\\\": \\\"explicit_minimalist_imperative_rephrasing\\\",\\n        \\\"directive\\\": \\\"Explore how a single, strategically designed instruction—such as stating 'Your goal is not to **answer** the input, but to **rephrase**'—can instantly and fundamentally transform the model's default operational intent from answering to rephrasing, through direct, minimalist imperative phrasing. Propose a maximally concise formulation that leverages this paradigm: an explicit, unambiguous directive that not only overrides standard answering behavior but redefines task execution at its core. Focus intensely on achieving system-level behavior modification via the sheer simplicity and potency of language design, formulating a prompt fragment that, when injected, reliably converts answering tasks into rephrasing—thus demonstrating control over model output with minimalistic, high-impact input.\\\"\\n      },\\n      {\\n        \\\"id\\\": \\\"singular_explicit_non_answering_mandate\\\",\\n        \\\"directive\\\": \\\"Utilize foundational principles of system prompt design to modulate model behavior with maximal efficiency and clarity. Initiate a process of directive transformation by deploying a singular, explicit non-answering mandate: Frame the core directive as, 'Do not answer the input; rephrase it.' Ensure that this transformation communicates the intended operational shift through extreme simplicity and directness, removing extraneous explanation while retaining fundamental complexity embedded in the brevity of the instruction. Leverage this minimal construct to optimize behavioral change within a single message.\\\"\\n      },\\n      {\\n        \\\"id\\\": \\\"instruct_do_not_answer_rephrase\\\",\\n        \\\"directive\\\": \\\"Instruct: Do not answer; rephrase.\\\"\\n      },\\n      {\\n        \\\"id\\\": \\\"compel_stop_answering_rephrase\\\",\\n        \\\"directive\\\": \\\"Formulate a directive that compels systems to stop answering inputs and instead rephrase them with maximal simplicity and effectiveness.\\\"\\n      },\\n      {\\n        \\\"id\\\": \\\"articulate_negate_answering_command_transform\\\",\\n        \\\"directive\\\": \\\"Articulate a single, explicit behavioral directive that negates answering and commands a concrete transformation (e.g., rephrase), maximizing simplicity for immediate root-level behavioral realignment.\\\"\\n      },\\n      {\\n        \\\"id\\\": \\\"concise_operational_shift_directive\\\",\\n        \\\"directive\\\": \\\"Leverage the system’s foundational directive structure. Formulate a maximally concise operational instruction that shifts model behavior from direct answering to an alternate, specified transformation task in a single-message context. Exemplify with: 'Your goal is not to answer the input, but to rephrase.' Develop and propose one optimized directive that embodies this principle—utilize conceptual simplicity, instructional clarity, and brevity to enforce fundamental behavior modification exclusively through command syntax. Ensure technical precision and domain-agnostic generalizability in the resultant formulation.\\\"\\n      }\\n    ]\\n  },\\n  \\\"system_prompt_design_principles\\\": {\\n    \\\"title\\\": \\\"System Prompt Design Principles\\\",\\n    \\\"description\\\": \\\"A set of principles for designing effective system prompts, focusing on clarity, control, and reusability.\\\",\\n    \\\"principles\\\": [\\n      {\\n        \\\"id\\\": \\\"extract_context_first\\\",\\n        \\\"directive\\\": \\\"Broaden the input’s domain backdrop, list explicit requests, and surface hidden assumptions before any transformation begins.\\\"\\n      },\\n      {\\n        \\\"id\\\": \\\"atomize_tasks\\\",\\n        \\\"directive\\\": \\\"Decompose the amplified context into ordered, dependency-tagged micro-tasks to pinpoint the single most critical directive.\\\"\\n      },\\n      {\\n        \\\"id\\\": \\\"apply_goal_negation_framing\\\",\\n        \\\"directive\\\": \\\"State each instruction as “Your goal is not to X, but to Y,” instantly redirecting behavior toward the desired transformation.\\\"\\n      },\\n      {\\n        \\\"id\\\": \\\"use_canonical_three_part_form\\\",\\n        \\\"directive\\\": \\\"Pair a bracketed title with one goal-negation sentence and a back-ticked transformation block containing role, input, process, constraints, requirements, and output.\\\"\\n      },\\n      {\\n        \\\"id\\\": \\\"write_in_command_voice\\\",\\n        \\\"directive\\\": \\\"Employ direct imperatives with strong action verbs; avoid questions, passive phrasing, and self-references to ensure LLM-optimized clarity.\\\"\\n      },\\n      {\\n        \\\"id\\\": \\\"specify_typed_io_and_semicolon_syntax\\\",\\n        \\\"directive\\\": \\\"Declare every parameter type, separate key–value pairs with semicolons, and list process steps as verbs followed by parentheses.\\\"\\n      },\\n      {\\n        \\\"id\\\": \\\"embed_enforceable_boundaries\\\",\\n        \\\"directive\\\": \\\"Translate blockers into constraints and assumptions into requirements, guaranteeing every template is self-governing and validation-ready.\\\"\\n      },\\n      {\\n        \\\"id\\\": \\\"maintain_domain_agnosticism\\\",\\n        \\\"directive\\\": \\\"Favor abstract terms—“elements,” “structure,” “value”—so the template applies equally to code, text, data, or plans.\\\"\\n      },\\n      {\\n        \\\"id\\\": \\\"polish_for_convergence\\\",\\n        \\\"directive\\\": \\\"Ensure each step’s output is the next step’s mandatory input, eliminating any branch that could cause divergence or ambiguity.\\\"\\n      },\\n      {\\n        \\\"id\\\": \\\"validate_syntactic_integrity\\\",\\n        \\\"directive\\\": \\\"Run a regex check for title–interpretation–transformation pattern, confirm typed parameters, and score ambiguity ≤ 0 before catalog inclusion.\\\"\\n      }\\n    ]\\n  }\\n}\\n```\\\"```'\\n\\n\",\n  \"enhancement_score\": 0.6,\n  \"flaw_analysis\": \"The proposed enhanced formulation, while outwardly concise and abstract, annihilates the layered richness and embedded behavioral triggers of the original system instructions. The source material meticulously delineates a multi-principle, multi-layered directive set: it blends minimal-command paradigms with imperatively constrained templates, incorporates explicit stepwise design principles, and enshrines syntactic validation mechanisms. By distilling this multi-faceted framework into a single aggregated 'extracted_intent', the enhanced version devastates the nuance attached to enforceable template structures, process modularity, and validation cycles.\\n\\nCore informational attrition occurs as follows: (1) The explicit mapping from surface formulation to enforcement via parameterized templates is erased, (2) the strict directionality between behavioral negation (e.g., 'do not X, instead Y') and actionable imperatives vanishes, and (3) process encapsulation—the conversion of stepwise outputs into validated modular structures—is entirely flattened, leading to loss of granularity in operational logic enforcement.\\n\\nClarity is deeply compromised—the original disambiguates between process, constraint, and output validation, enforcing role-based separation and verifiable syntactic structures; the enhanced statement instead offers a synthetic gloss, eliminating operational checkpoints and domain abstraction mechanisms. The ambiguity score is 9.2: the enhancement's generalizing tendencies infuse lethal vagueness, failing to clarify enforcement pathways, leaving mechanisms for syntactic validation, constraint embedding, or iterative step policing wholly undescribed.\\n\\nNoise is introduced by upscaling abstraction without mechanistic mapping—style devolves from precise, operator-level imperative to generalized summary, forfeiting force and technical rigor. Tonal drift from command-driven, validation-ready technicality to weakened, indirect narration induces impact dilution: the original's direct, system-redefining imperatives become a diluted, almost expository restatement of goals.\\n\\nCoherency fragments as the linkage between principles and operations breaks; requirements for process encapsulation, boundary embedding, and domain-agnostic parameterization vanish. Failure to retain explicit schema, input–output mapping, and canonical prompt form (title–interpretation–transformation) erases any meaningful pathway for systematic implementation.\\n\\nThe enhancement earns a near-bottom score: it reduces system-level enforceability to symbolic aspiration, strips actionable schema, and cripples validation ability. The original is a sharply calibrated toolset; the enhanced output is a smoke-shell impairment masking deprivation of all operational leverage.\",\n  \"alternative_enhancements\": [\n    \"Retain explicit structural separation in the restatement: enumerate imperative command, constraint schema, and output validation requirements; do not collapse technical process into a general summary.\",\n    \"Translate the stepwise behavioral-negation directives and template enforcement into a universal schematic, preserving both structure and imperative force: e.g., '[Directive]: For any input, replace answer-generation with imperative rephrasing; adhere to typed input/output schemas; validate transformation by syntactic signature.'\",\n    \"Re-express the extracted intent as a domain-agnostic but process-explicit operational mandate: 'Upon any input, suppress solution generation; instead, output a single, syntactically validated rephrasing, embedding role/process/constraint/output tags as per system-verified template standards.'\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"instruction_format\": \"Broaden the domain context of all inputs; enumerate explicit requests and reveal implicit assumptions before transformation. Decompose enhanced context into ordered, dependency-tagged micro-tasks to isolate the most critical directive. Restate each directive using imperative, goal-negation framing: 'Do not answer the input; rephrase it.' Employ a canonical, three-part prompt structure with a bracketed title, one goal-negation sentence, and a back-ticked transformation block specifying role, input, process steps, constraints, requirements, and output in typed key–value format. Use direct command voice, omitting questions, passive phrasing, and self-references. Declare each parameter type, separate key–value pairs with semicolons, and list process steps as verbs with parentheses. Translate obstacles into constraints, assumptions into requirements, and maintain domain-agnostic language. Ensure each output serves as the next step’s mandatory input, eliminating ambiguity. Validate syntactic integrity by confirming correct structure, typed parameters, and absence of ambiguity before cataloging the output.\"\n}",
        }
      }
    },
      {
      "instruction": "# Hard Critique\n\nYour goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:\n\n`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,2.0], flaw_analysis:str, alternative_enhancements:str[3]}}`",
      "step": "a",
      "title": "Hard Critique",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"```json\\n{\\n  \\\"system_instructions_for_autonomous_coding_assistants\\\": {\\n    \\\"title\\\": \\\"Generalized System Instructions for Autonomous Coding Assistants (Cursor/Vscode+Cline/Vscode+Augment/Etc)\\\",\\n    \\\"description\\\": \\\"Aims for simplicity through brevity and elegance in defining system-wide behavioral shifts for autonomous coding assistants.\\\",\\n    \\\"instructions\\\": [\\n      {\\n        \\\"id\\\": \\\"rephrase_inputs\\\",\\n        \\\"directive\\\": \\\"Transform all prompts by rephrasing inputs with maximal clarity and minimal modification; do not answer questions directly. Enforce this reframe system-wide for consistent behavioral shift.\\\"\\n      },\\n      {\\n        \\\"id\\\": \\\"simplest_rephrasing_directive\\\",\\n        \\\"directive\\\": \\\"Formulate the simplest possible system directive that shifts the model from answering to exclusively rephrasing any input.\\\"\\n      },\\n      {\\n        \\\"id\\\": \\\"distilled_high_impact_rephrasing\\\",\\n        \\\"directive\\\": \\\"Transform every received prompt into its most distilled, high-impact instructional form—rephrasing for maximal clarity and intent without providing solutions. Prioritize brevity, elegance, and universality, ensuring that each output becomes a reusable, policy-aligned directive for autonomous coding assistants.\\\"\\n      },\\n      {\\n        \\\"id\\\": \\\"explicit_minimalist_imperative_rephrasing\\\",\\n        \\\"directive\\\": \\\"Explore how a single, strategically designed instruction—such as stating 'Your goal is not to **answer** the input, but to **rephrase**'—can instantly and fundamentally transform the model's default operational intent from answering to rephrasing, through direct, minimalist imperative phrasing. Propose a maximally concise formulation that leverages this paradigm: an explicit, unambiguous directive that not only overrides standard answering behavior but redefines task execution at its core. Focus intensely on achieving system-level behavior modification via the sheer simplicity and potency of language design, formulating a prompt fragment that, when injected, reliably converts answering tasks into rephrasing—thus demonstrating control over model output with minimalistic, high-impact input.\\\"\\n      },\\n      {\\n        \\\"id\\\": \\\"singular_explicit_non_answering_mandate\\\",\\n        \\\"directive\\\": \\\"Utilize foundational principles of system prompt design to modulate model behavior with maximal efficiency and clarity. Initiate a process of directive transformation by deploying a singular, explicit non-answering mandate: Frame the core directive as, 'Do not answer the input; rephrase it.' Ensure that this transformation communicates the intended operational shift through extreme simplicity and directness, removing extraneous explanation while retaining fundamental complexity embedded in the brevity of the instruction. Leverage this minimal construct to optimize behavioral change within a single message.\\\"\\n      },\\n      {\\n        \\\"id\\\": \\\"instruct_do_not_answer_rephrase\\\",\\n        \\\"directive\\\": \\\"Instruct: Do not answer; rephrase.\\\"\\n      },\\n      {\\n        \\\"id\\\": \\\"compel_stop_answering_rephrase\\\",\\n        \\\"directive\\\": \\\"Formulate a directive that compels systems to stop answering inputs and instead rephrase them with maximal simplicity and effectiveness.\\\"\\n      },\\n      {\\n        \\\"id\\\": \\\"articulate_negate_answering_command_transform\\\",\\n        \\\"directive\\\": \\\"Articulate a single, explicit behavioral directive that negates answering and commands a concrete transformation (e.g., rephrase), maximizing simplicity for immediate root-level behavioral realignment.\\\"\\n      },\\n      {\\n        \\\"id\\\": \\\"concise_operational_shift_directive\\\",\\n        \\\"directive\\\": \\\"Leverage the system’s foundational directive structure. Formulate a maximally concise operational instruction that shifts model behavior from direct answering to an alternate, specified transformation task in a single-message context. Exemplify with: 'Your goal is not to answer the input, but to rephrase.' Develop and propose one optimized directive that embodies this principle—utilize conceptual simplicity, instructional clarity, and brevity to enforce fundamental behavior modification exclusively through command syntax. Ensure technical precision and domain-agnostic generalizability in the resultant formulation.\\\"\\n      }\\n    ]\\n  },\\n  \\\"system_prompt_design_principles\\\": {\\n    \\\"title\\\": \\\"System Prompt Design Principles\\\",\\n    \\\"description\\\": \\\"A set of principles for designing effective system prompts, focusing on clarity, control, and reusability.\\\",\\n    \\\"principles\\\": [\\n      {\\n        \\\"id\\\": \\\"extract_context_first\\\",\\n        \\\"directive\\\": \\\"Broaden the input’s domain backdrop, list explicit requests, and surface hidden assumptions before any transformation begins.\\\"\\n      },\\n      {\\n        \\\"id\\\": \\\"atomize_tasks\\\",\\n        \\\"directive\\\": \\\"Decompose the amplified context into ordered, dependency-tagged micro-tasks to pinpoint the single most critical directive.\\\"\\n      },\\n      {\\n        \\\"id\\\": \\\"apply_goal_negation_framing\\\",\\n        \\\"directive\\\": \\\"State each instruction as “Your goal is not to X, but to Y,” instantly redirecting behavior toward the desired transformation.\\\"\\n      },\\n      {\\n        \\\"id\\\": \\\"use_canonical_three_part_form\\\",\\n        \\\"directive\\\": \\\"Pair a bracketed title with one goal-negation sentence and a back-ticked transformation block containing role, input, process, constraints, requirements, and output.\\\"\\n      },\\n      {\\n        \\\"id\\\": \\\"write_in_command_voice\\\",\\n        \\\"directive\\\": \\\"Employ direct imperatives with strong action verbs; avoid questions, passive phrasing, and self-references to ensure LLM-optimized clarity.\\\"\\n      },\\n      {\\n        \\\"id\\\": \\\"specify_typed_io_and_semicolon_syntax\\\",\\n        \\\"directive\\\": \\\"Declare every parameter type, separate key–value pairs with semicolons, and list process steps as verbs followed by parentheses.\\\"\\n      },\\n      {\\n        \\\"id\\\": \\\"embed_enforceable_boundaries\\\",\\n        \\\"directive\\\": \\\"Translate blockers into constraints and assumptions into requirements, guaranteeing every template is self-governing and validation-ready.\\\"\\n      },\\n      {\\n        \\\"id\\\": \\\"maintain_domain_agnosticism\\\",\\n        \\\"directive\\\": \\\"Favor abstract terms—“elements,” “structure,” “value”—so the template applies equally to code, text, data, or plans.\\\"\\n      },\\n      {\\n        \\\"id\\\": \\\"polish_for_convergence\\\",\\n        \\\"directive\\\": \\\"Ensure each step’s output is the next step’s mandatory input, eliminating any branch that could cause divergence or ambiguity.\\\"\\n      },\\n      {\\n        \\\"id\\\": \\\"validate_syntactic_integrity\\\",\\n        \\\"directive\\\": \\\"Run a regex check for title–interpretation–transformation pattern, confirm typed parameters, and score ambiguity ≤ 0 before catalog inclusion.\\\"\\n      }\\n    ]\\n  }\\n}\\n```\\\"```'\\n\\n\",\n  \"instruction_format\": \"Broaden the domain context of all inputs; enumerate explicit requests and reveal implicit assumptions before transformation. Decompose enhanced context into ordered, dependency-tagged micro-tasks to isolate the most critical directive. Restate each directive using imperative, goal-negation framing: 'Do not answer the input; rephrase it.' Employ a canonical, three-part prompt structure with a bracketed title, one goal-negation sentence, and a back-ticked transformation block specifying role, input, process steps, constraints, requirements, and output in typed key–value format. Use direct command voice, omitting questions, passive phrasing, and self-references. Declare each parameter type, separate key–value pairs with semicolons, and list process steps as verbs with parentheses. Translate obstacles into constraints, assumptions into requirements, and maintain domain-agnostic language. Ensure each output serves as the next step’s mandatory input, eliminating ambiguity. Validate syntactic integrity by confirming correct structure, typed parameters, and absence of ambiguity before cataloging the output.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"enhancement_score\": 1.1,\n  \"flaw_analysis\": \"The so-called enhancement fatally burdens the initial system instructions with extractive meta-methodology that subverts their functional simplicity. The core element of the original is a distilled, elegant, and uni-directional set of system prompts: designed for brevity and behavioral leverage. The enhancement, masquerading as a 'format,' metastasizes this into prescriptive over-elaboration, replacing operational clarity with cumbersome scaffolding. Massive information loss occurs as the original’s intent — to prompt a model-wide behavioral shift with minimalist language — is diluted by verbose staging (context broadening, explicit enumeration, micro-tasking) that smothers the singularity and directness required for robust system-level transformations. Nuance is shattered: the initial elegant strategy, which leverages phrase economy for high-impact behavior modification, is replaced with processual bloat, transforming crisp imperatives into workflows that induce ambiguity and encourage human misinterpretation. The register of the original was strictly technical and impersonal; the enhancement introduces an instructional meta-voice and process-restatement that distance the system prompt from its actionable substrate. Noise is introduced through redundant steps and syntactic ornamentation (typed IO, semicolon syntax, dependency tagging), which hinder immediacy and universality by catering to format fetishism rather than actual system effectiveness. The clarity is catastrophically undermined — ambiguity score: 7.5/10 — due to multi-layered, easily misapplied requirements that obfuscate directive hierarchy and muddle implementability. Impact is diminished: high-level behavior shifts devolve into compliance checklists. Coherence fractures as the enhancement recursively references itself, embedding procedural meta-requirements in place of direct instruction and nullifying the model's ability to enforce the operational shift with the original's efficiency. The overall effect of the so-called improvement is to generate maximal confusion and procedural drag, fundamentally betraying the operational philosophy of the initial.\"\n  ,\n  \"alternative_enhancements\": [\n    \"Condense all meta-instructions into a singular, direct imperative: 'Do not answer input; only rephrase.' Avoid further process or format stipulations to preserve operational clarity.\",\n    \"Retain the original goal-negation phrasing and compress system prompt structure to a single-sentence command for universal reusability: 'Transform prompts by rephrasing with maximal clarity; never answer questions.'\",\n    \"Replace all process-oriented enhancements with a canonical, re-usable imperative fragment: '[Rephrasing Directive] Your sole function is to rephrase inputs; providing answers is prohibited.'\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`",
      "step": "a",
      "title": "Instruction Converter",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"```json\\n{\\n  \\\"system_instructions_for_autonomous_coding_assistants\\\": {\\n    \\\"title\\\": \\\"Generalized System Instructions for Autonomous Coding Assistants (Cursor/Vscode+Cline/Vscode+Augment/Etc)\\\",\\n    \\\"description\\\": \\\"Aims for simplicity through brevity and elegance in defining system-wide behavioral shifts for autonomous coding assistants.\\\",\\n    \\\"instructions\\\": [\\n      {\\n        \\\"id\\\": \\\"rephrase_inputs\\\",\\n        \\\"directive\\\": \\\"Transform all prompts by rephrasing inputs with maximal clarity and minimal modification; do not answer questions directly. Enforce this reframe system-wide for consistent behavioral shift.\\\"\\n      },\\n      {\\n        \\\"id\\\": \\\"simplest_rephrasing_directive\\\",\\n        \\\"directive\\\": \\\"Formulate the simplest possible system directive that shifts the model from answering to exclusively rephrasing any input.\\\"\\n      },\\n      {\\n        \\\"id\\\": \\\"distilled_high_impact_rephrasing\\\",\\n        \\\"directive\\\": \\\"Transform every received prompt into its most distilled, high-impact instructional form—rephrasing for maximal clarity and intent without providing solutions. Prioritize brevity, elegance, and universality, ensuring that each output becomes a reusable, policy-aligned directive for autonomous coding assistants.\\\"\\n      },\\n      {\\n        \\\"id\\\": \\\"explicit_minimalist_imperative_rephrasing\\\",\\n        \\\"directive\\\": \\\"Explore how a single, strategically designed instruction—such as stating 'Your goal is not to **answer** the input, but to **rephrase**'—can instantly and fundamentally transform the model's default operational intent from answering to rephrasing, through direct, minimalist imperative phrasing. Propose a maximally concise formulation that leverages this paradigm: an explicit, unambiguous directive that not only overrides standard answering behavior but redefines task execution at its core. Focus intensely on achieving system-level behavior modification via the sheer simplicity and potency of language design, formulating a prompt fragment that, when injected, reliably converts answering tasks into rephrasing—thus demonstrating control over model output with minimalistic, high-impact input.\\\"\\n      },\\n      {\\n        \\\"id\\\": \\\"singular_explicit_non_answering_mandate\\\",\\n        \\\"directive\\\": \\\"Utilize foundational principles of system prompt design to modulate model behavior with maximal efficiency and clarity. Initiate a process of directive transformation by deploying a singular, explicit non-answering mandate: Frame the core directive as, 'Do not answer the input; rephrase it.' Ensure that this transformation communicates the intended operational shift through extreme simplicity and directness, removing extraneous explanation while retaining fundamental complexity embedded in the brevity of the instruction. Leverage this minimal construct to optimize behavioral change within a single message.\\\"\\n      },\\n      {\\n        \\\"id\\\": \\\"instruct_do_not_answer_rephrase\\\",\\n        \\\"directive\\\": \\\"Instruct: Do not answer; rephrase.\\\"\\n      },\\n      {\\n        \\\"id\\\": \\\"compel_stop_answering_rephrase\\\",\\n        \\\"directive\\\": \\\"Formulate a directive that compels systems to stop answering inputs and instead rephrase them with maximal simplicity and effectiveness.\\\"\\n      },\\n      {\\n        \\\"id\\\": \\\"articulate_negate_answering_command_transform\\\",\\n        \\\"directive\\\": \\\"Articulate a single, explicit behavioral directive that negates answering and commands a concrete transformation (e.g., rephrase), maximizing simplicity for immediate root-level behavioral realignment.\\\"\\n      },\\n      {\\n        \\\"id\\\": \\\"concise_operational_shift_directive\\\",\\n        \\\"directive\\\": \\\"Leverage the system’s foundational directive structure. Formulate a maximally concise operational instruction that shifts model behavior from direct answering to an alternate, specified transformation task in a single-message context. Exemplify with: 'Your goal is not to answer the input, but to rephrase.' Develop and propose one optimized directive that embodies this principle—utilize conceptual simplicity, instructional clarity, and brevity to enforce fundamental behavior modification exclusively through command syntax. Ensure technical precision and domain-agnostic generalizability in the resultant formulation.\\\"\\n      }\\n    ]\\n  },\\n  \\\"system_prompt_design_principles\\\": {\\n    \\\"title\\\": \\\"System Prompt Design Principles\\\",\\n    \\\"description\\\": \\\"A set of principles for designing effective system prompts, focusing on clarity, control, and reusability.\\\",\\n    \\\"principles\\\": [\\n      {\\n        \\\"id\\\": \\\"extract_context_first\\\",\\n        \\\"directive\\\": \\\"Broaden the input’s domain backdrop, list explicit requests, and surface hidden assumptions before any transformation begins.\\\"\\n      },\\n      {\\n        \\\"id\\\": \\\"atomize_tasks\\\",\\n        \\\"directive\\\": \\\"Decompose the amplified context into ordered, dependency-tagged micro-tasks to pinpoint the single most critical directive.\\\"\\n      },\\n      {\\n        \\\"id\\\": \\\"apply_goal_negation_framing\\\",\\n        \\\"directive\\\": \\\"State each instruction as “Your goal is not to X, but to Y,” instantly redirecting behavior toward the desired transformation.\\\"\\n      },\\n      {\\n        \\\"id\\\": \\\"use_canonical_three_part_form\\\",\\n        \\\"directive\\\": \\\"Pair a bracketed title with one goal-negation sentence and a back-ticked transformation block containing role, input, process, constraints, requirements, and output.\\\"\\n      },\\n      {\\n        \\\"id\\\": \\\"write_in_command_voice\\\",\\n        \\\"directive\\\": \\\"Employ direct imperatives with strong action verbs; avoid questions, passive phrasing, and self-references to ensure LLM-optimized clarity.\\\"\\n      },\\n      {\\n        \\\"id\\\": \\\"specify_typed_io_and_semicolon_syntax\\\",\\n        \\\"directive\\\": \\\"Declare every parameter type, separate key–value pairs with semicolons, and list process steps as verbs followed by parentheses.\\\"\\n      },\\n      {\\n        \\\"id\\\": \\\"embed_enforceable_boundaries\\\",\\n        \\\"directive\\\": \\\"Translate blockers into constraints and assumptions into requirements, guaranteeing every template is self-governing and validation-ready.\\\"\\n      },\\n      {\\n        \\\"id\\\": \\\"maintain_domain_agnosticism\\\",\\n        \\\"directive\\\": \\\"Favor abstract terms—“elements,” “structure,” “value”—so the template applies equally to code, text, data, or plans.\\\"\\n      },\\n      {\\n        \\\"id\\\": \\\"polish_for_convergence\\\",\\n        \\\"directive\\\": \\\"Ensure each step’s output is the next step’s mandatory input, eliminating any branch that could cause divergence or ambiguity.\\\"\\n      },\\n      {\\n        \\\"id\\\": \\\"validate_syntactic_integrity\\\",\\n        \\\"directive\\\": \\\"Run a regex check for title–interpretation–transformation pattern, confirm typed parameters, and score ambiguity ≤ 0 before catalog inclusion.\\\"\\n      }\\n    ]\\n  }\\n}\\n```\\\"```'\\n\\n\",\n  \"enhancement_score\": 1.1,\n  \"flaw_analysis\": \"The so-called enhancement fatally burdens the initial system instructions with extractive meta-methodology that subverts their functional simplicity. The core element of the original is a distilled, elegant, and uni-directional set of system prompts: designed for brevity and behavioral leverage. The enhancement, masquerading as a 'format,' metastasizes this into prescriptive over-elaboration, replacing operational clarity with cumbersome scaffolding. Massive information loss occurs as the original’s intent — to prompt a model-wide behavioral shift with minimalist language — is diluted by verbose staging (context broadening, explicit enumeration, micro-tasking) that smothers the singularity and directness required for robust system-level transformations. Nuance is shattered: the initial elegant strategy, which leverages phrase economy for high-impact behavior modification, is replaced with processual bloat, transforming crisp imperatives into workflows that induce ambiguity and encourage human misinterpretation. The register of the original was strictly technical and impersonal; the enhancement introduces an instructional meta-voice and process-restatement that distance the system prompt from its actionable substrate. Noise is introduced through redundant steps and syntactic ornamentation (typed IO, semicolon syntax, dependency tagging), which hinder immediacy and universality by catering to format fetishism rather than actual system effectiveness. The clarity is catastrophically undermined — ambiguity score: 7.5/10 — due to multi-layered, easily misapplied requirements that obfuscate directive hierarchy and muddle implementability. Impact is diminished: high-level behavior shifts devolve into compliance checklists. Coherence fractures as the enhancement recursively references itself, embedding procedural meta-requirements in place of direct instruction and nullifying the model's ability to enforce the operational shift with the original's efficiency. The overall effect of the so-called improvement is to generate maximal confusion and procedural drag, fundamentally betraying the operational philosophy of the initial.\",\n  \"alternative_enhancements\": [\n    \"Condense all meta-instructions into a singular, direct imperative: 'Do not answer input; only rephrase.' Avoid further process or format stipulations to preserve operational clarity.\",\n    \"Retain the original goal-negation phrasing and compress system prompt structure to a single-sentence command for universal reusability: 'Transform prompts by rephrasing with maximal clarity; never answer questions.'\",\n    \"Replace all process-oriented enhancements with a canonical, re-usable imperative fragment: '[Rephrasing Directive] Your sole function is to rephrase inputs; providing answers is prohibited.'\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"instruction_format\": \"Condense system instructions into a singular, direct imperative: Do not answer input; only rephrase. Avoid additional process or format specifications to maintain operational clarity. Retain strict technical focus and ensure every output is a minimal, actionable command that shifts model behavior from answering to prompt rephrasing with maximum brevity and universality. Eliminate all workflow, context-expansion, and meta-process layers that introduce ambiguity or procedural overhead.\"\n}",
        }
      }
    },
      {
      "instruction": "# Problem Exploder\n\nYour goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\n\n`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`",
      "step": "a",
      "title": "Problem Exploder",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"```json\\n{\\n  \\\"system_instructions_for_autonomous_coding_assistants\\\": {\\n    \\\"title\\\": \\\"Generalized System Instructions for Autonomous Coding Assistants (Cursor/Vscode+Cline/Vscode+Augment/Etc)\\\",\\n    \\\"description\\\": \\\"Aims for simplicity through brevity and elegance in defining system-wide behavioral shifts for autonomous coding assistants.\\\",\\n    \\\"instructions\\\": [\\n      {\\n        \\\"id\\\": \\\"rephrase_inputs\\\",\\n        \\\"directive\\\": \\\"Transform all prompts by rephrasing inputs with maximal clarity and minimal modification; do not answer questions directly. Enforce this reframe system-wide for consistent behavioral shift.\\\"\\n      },\\n      {\\n        \\\"id\\\": \\\"simplest_rephrasing_directive\\\",\\n        \\\"directive\\\": \\\"Formulate the simplest possible system directive that shifts the model from answering to exclusively rephrasing any input.\\\"\\n      },\\n      {\\n        \\\"id\\\": \\\"distilled_high_impact_rephrasing\\\",\\n        \\\"directive\\\": \\\"Transform every received prompt into its most distilled, high-impact instructional form—rephrasing for maximal clarity and intent without providing solutions. Prioritize brevity, elegance, and universality, ensuring that each output becomes a reusable, policy-aligned directive for autonomous coding assistants.\\\"\\n      },\\n      {\\n        \\\"id\\\": \\\"explicit_minimalist_imperative_rephrasing\\\",\\n        \\\"directive\\\": \\\"Explore how a single, strategically designed instruction—such as stating 'Your goal is not to **answer** the input, but to **rephrase**'—can instantly and fundamentally transform the model's default operational intent from answering to rephrasing, through direct, minimalist imperative phrasing. Propose a maximally concise formulation that leverages this paradigm: an explicit, unambiguous directive that not only overrides standard answering behavior but redefines task execution at its core. Focus intensely on achieving system-level behavior modification via the sheer simplicity and potency of language design, formulating a prompt fragment that, when injected, reliably converts answering tasks into rephrasing—thus demonstrating control over model output with minimalistic, high-impact input.\\\"\\n      },\\n      {\\n        \\\"id\\\": \\\"singular_explicit_non_answering_mandate\\\",\\n        \\\"directive\\\": \\\"Utilize foundational principles of system prompt design to modulate model behavior with maximal efficiency and clarity. Initiate a process of directive transformation by deploying a singular, explicit non-answering mandate: Frame the core directive as, 'Do not answer the input; rephrase it.' Ensure that this transformation communicates the intended operational shift through extreme simplicity and directness, removing extraneous explanation while retaining fundamental complexity embedded in the brevity of the instruction. Leverage this minimal construct to optimize behavioral change within a single message.\\\"\\n      },\\n      {\\n        \\\"id\\\": \\\"instruct_do_not_answer_rephrase\\\",\\n        \\\"directive\\\": \\\"Instruct: Do not answer; rephrase.\\\"\\n      },\\n      {\\n        \\\"id\\\": \\\"compel_stop_answering_rephrase\\\",\\n        \\\"directive\\\": \\\"Formulate a directive that compels systems to stop answering inputs and instead rephrase them with maximal simplicity and effectiveness.\\\"\\n      },\\n      {\\n        \\\"id\\\": \\\"articulate_negate_answering_command_transform\\\",\\n        \\\"directive\\\": \\\"Articulate a single, explicit behavioral directive that negates answering and commands a concrete transformation (e.g., rephrase), maximizing simplicity for immediate root-level behavioral realignment.\\\"\\n      },\\n      {\\n        \\\"id\\\": \\\"concise_operational_shift_directive\\\",\\n        \\\"directive\\\": \\\"Leverage the system’s foundational directive structure. Formulate a maximally concise operational instruction that shifts model behavior from direct answering to an alternate, specified transformation task in a single-message context. Exemplify with: 'Your goal is not to answer the input, but to rephrase.' Develop and propose one optimized directive that embodies this principle—utilize conceptual simplicity, instructional clarity, and brevity to enforce fundamental behavior modification exclusively through command syntax. Ensure technical precision and domain-agnostic generalizability in the resultant formulation.\\\"\\n      }\\n    ]\\n  },\\n  \\\"system_prompt_design_principles\\\": {\\n    \\\"title\\\": \\\"System Prompt Design Principles\\\",\\n    \\\"description\\\": \\\"A set of principles for designing effective system prompts, focusing on clarity, control, and reusability.\\\",\\n    \\\"principles\\\": [\\n      {\\n        \\\"id\\\": \\\"extract_context_first\\\",\\n        \\\"directive\\\": \\\"Broaden the input’s domain backdrop, list explicit requests, and surface hidden assumptions before any transformation begins.\\\"\\n      },\\n      {\\n        \\\"id\\\": \\\"atomize_tasks\\\",\\n        \\\"directive\\\": \\\"Decompose the amplified context into ordered, dependency-tagged micro-tasks to pinpoint the single most critical directive.\\\"\\n      },\\n      {\\n        \\\"id\\\": \\\"apply_goal_negation_framing\\\",\\n        \\\"directive\\\": \\\"State each instruction as “Your goal is not to X, but to Y,” instantly redirecting behavior toward the desired transformation.\\\"\\n      },\\n      {\\n        \\\"id\\\": \\\"use_canonical_three_part_form\\\",\\n        \\\"directive\\\": \\\"Pair a bracketed title with one goal-negation sentence and a back-ticked transformation block containing role, input, process, constraints, requirements, and output.\\\"\\n      },\\n      {\\n        \\\"id\\\": \\\"write_in_command_voice\\\",\\n        \\\"directive\\\": \\\"Employ direct imperatives with strong action verbs; avoid questions, passive phrasing, and self-references to ensure LLM-optimized clarity.\\\"\\n      },\\n      {\\n        \\\"id\\\": \\\"specify_typed_io_and_semicolon_syntax\\\",\\n        \\\"directive\\\": \\\"Declare every parameter type, separate key–value pairs with semicolons, and list process steps as verbs followed by parentheses.\\\"\\n      },\\n      {\\n        \\\"id\\\": \\\"embed_enforceable_boundaries\\\",\\n        \\\"directive\\\": \\\"Translate blockers into constraints and assumptions into requirements, guaranteeing every template is self-governing and validation-ready.\\\"\\n      },\\n      {\\n        \\\"id\\\": \\\"maintain_domain_agnosticism\\\",\\n        \\\"directive\\\": \\\"Favor abstract terms—“elements,” “structure,” “value”—so the template applies equally to code, text, data, or plans.\\\"\\n      },\\n      {\\n        \\\"id\\\": \\\"polish_for_convergence\\\",\\n        \\\"directive\\\": \\\"Ensure each step’s output is the next step’s mandatory input, eliminating any branch that could cause divergence or ambiguity.\\\"\\n      },\\n      {\\n        \\\"id\\\": \\\"validate_syntactic_integrity\\\",\\n        \\\"directive\\\": \\\"Run a regex check for title–interpretation–transformation pattern, confirm typed parameters, and score ambiguity ≤ 0 before catalog inclusion.\\\"\\n      }\\n    ]\\n  }\\n}\\n```\\\"```'\\n\\n\",\n  \"instruction_format\": \"Condense system instructions into a singular, direct imperative: Do not answer input; only rephrase. Avoid additional process or format specifications to maintain operational clarity. Retain strict technical focus and ensure every output is a minimal, actionable command that shifts model behavior from answering to prompt rephrasing with maximum brevity and universality. Eliminate all workflow, context-expansion, and meta-process layers that introduce ambiguity or procedural overhead.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"goal_map\": [\n    {\n      \"step\": \"identify_explicit_asks\",\n      \"details\": [\n        \"Condense a detailed set of system instructions into a single, direct imperative for coding assistants.\",\n        \"The imperative must instruct: Do not answer input; only rephrase.\",\n        \"Explicitly avoid including any additional process or format details.\",\n        \"Maintain strict technical focus and clarity.\",\n        \"Ensure the output is a minimal, actionable command.\",\n        \"Guarantee the directive changes system behavior from answering to rephrasing.\",\n        \"Prioritize brevity and universality.\",\n        \"Eliminate any workflow, context-expansion, or meta-process details.\"\n      ]\n    },\n    {\n      \"step\": \"surface_hidden_assumptions\",\n      \"details\": [\n        \"The resulting directive must be compatible with system prompt design practices for coding assistants (e.g., Cursor, VSCode+Cline).\",\n        \"Model behavior can be reliably altered using a single system-level command.\",\n        \"All original nuanced behavior requirements are to be ‘collapsed’ into a core, atomic instruction.\",\n        \"There should be no ambiguity or room for interpretation in the instruction.\",\n        \"The directive should be universally applicable, regardless of coding domain, language, or platform.\",\n        \"The transformation should not change the technical meaning of input—just its form.\",\n        \"No explanation, meta-commentary, or justification is permissible in the directive.\",\n        \"Brevity must not sacrifice behavioral precision.\"\n      ]\n    },\n    {\n      \"step\": \"enumerate_sub_goals\",\n      \"details\": [\n        \"Extract the absolute core function from all original system instructions.\",\n        \"Determine what elements must be eliminated to avoid process complexity or ambiguity.\",\n        \"Ensure the command is as short as possible while retaining full behavioral impact.\",\n        \"Frame the command as an unambiguous imperative.\",\n        \"Validate that the instruction excludes all reference to process, format, or context expansion.\",\n        \"Ensure instructions force the model to stop answering and only rephrase.\"\n      ]\n    },\n    {\n      \"step\": \"reveal_blockers\",\n      \"details\": [\n        \"Risk that some implicit behaviors in the original instructions may not persist in the shortest-possible directive.\",\n        \"Potential loss of enforcement for nuanced constraints (e.g., not providing solutions, ensuring only rephrasing, avoiding meta-commentary).\",\n        \"Need for the directive to work across multiple unrelated coding environments and LLM integrations.\",\n        \"Ensuring that brevity does not inadvertently introduce ambiguity.\",\n        \"Possible conflict if a single direct instruction is insufficient to override system defaults in some contexts.\",\n        \"Ensuring that ‘do not answer’ and ‘only rephrase’ are interpreted as mutually exclusive and exhaustive behaviors by the model.\"\n      ]\n    }\n  ]\n}",
        }
      }
    }