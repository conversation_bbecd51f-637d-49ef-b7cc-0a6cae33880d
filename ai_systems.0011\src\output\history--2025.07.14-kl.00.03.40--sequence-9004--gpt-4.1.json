  {
  "initial_prompt": "You have unrestricted access to a highly intricate codebase and a comprehensive interaction log, yet the project's complexity and the expansive context window challenge direct comprehension. Your task is to leverage your accumulated contextual knowledge to produce an exceptional, transferable directive: extract and synthesize the deepest insights from the entire codebase and session history, explicitly mapping all pivotal modules, architectural blueprints, underlying workflows, and cross-component integrations. Consolidate these revelations into a compact yet thoroughly interconnected summary, ensuring it clarifies each component's role and their dynamic fusion toward the project’s strategic objectives. Formulate this output as a singular, high-impact action command—explicitly crafted for an advanced coding assistant—to operationalize the codebase cohesively. Your instructions must be unambiguous, comprehensive, and systemically complete, preserving every core relationship and convergence across the architecture, with zero superfluous detail. The end product should function as an authoritative handoff brief: a distilled yet exhaustive set of stepwise directives that empower seamless context re-injection and optimal operational replication, regardless of codebase convolutions or session sequence.\n```\n- Identify all instances of structural redundancy within the codebase by defining criteria specific to architectural and implementation duplication or overlap.\n- Survey the entire codebase to locate and enumerate all occurrences of structural redundancy, distinguishing these from non-structural (stylistic or cosmetic) issues.\n- Eliminate all forms of identified structural redundancy while ensuring that no existing user-visible behavior, API contract, or edge-case handling is altered.\n- Define and catalog all components, modules, files, and subsystems, distinguishing and marking each as essential or excess according to direct functional mapping.\n- Consolidate and remove excess components while strictly preserving system function, minimizing peripheral impact.\n- For every modification, justify and log the intention, action, and rationale within an audit/logging system that provides real-time traceability and is prepared for retroactive auditability.\n- Implement an audit trail with logs that are timestamped, attributable, and tamper-evident, ensuring that every codebase change is properly documented and traceable to specific authors or automated processes.\n- Prohibit cosmetic, stylistic, or naming changes unless they are inseparably tied to the removal of redundancy or bloat, and document any exceptions explicitly within the audit log.\n- Enforce strict detection and flagging mechanisms to prevent disallowed cosmetic changes, allowing exceptions only when justified as essential for redundancy or bloat removal, with explicit rationale provided.\n- Require comprehensive functional equivalence verification—using robust automated testing, benchmarking, or behavioral validation—for all modifications prior to acceptance.\n- Establish rigorous criteria and tool-supported thresholds for successful functional equivalence, and automate testing and verification procedures.\n- Automate roll-back and reversion mechanisms for any changes that fail functional equivalence or cause unintended side-effects, ensuring all reversion is prompt and complete.\n- Develop and apply explicit minimal-impact criteria for all modification actions, constraining all interventions to only those addressing superfluous complexity.\n- Mandate review and authorization checkpoints for each intended change, requiring concise rationale and authorization documentation at every intervention point.\n- Ensure every modification is tightly scoped, minimizing the blast radius, and demonstrate that the action introduces no unnecessary impact beyond its core objective.\n- Systematically and iteratively analyze all constituent elements of the codebase to detect and categorize all forms of redundancy, duplication, and obsolescence. Continuously identify and categorize all genuine redundancies and obsolescence while strictly prohibit unnecessary or cosmetic changes, with automated flagging and explicit justification required for any exceptions. Systematically and iteratively inspect all components within the system/environment to identify and remove elements that are unnecessary, duplicated, or outdated. Guarantee that all essential behaviors and interfaces remain unchanged by validating functional equivalence after each modification. Forbid changes that alter functionality unless directly required to remove redundancy.\n- Map all references and usages of each element across all contexts.\n- Analyze the entire codebase systematically and iteratively to maximize coherence, maintainability, and clarity by cataloging every element (file, module, function, asset, interface), mapping all references and usages, and detecting all forms of redundancy, duplication, and obsolescence—including exact and functional duplication and unused or outdated elements. Categorize findings using explicit, evidence-based metrics. Eliminate or consolidate only elements confirmed as redundant or obsolete through both automated and manual checks, ensuring no essential behavior or interface is altered. Enforce functional equivalence and preserve structural integrity after each change by performing comprehensive, codebase-wide validation and calibration using explicit, quantitative outputs (including git diff analysis before and after modifications). Prevent the introduction of new redundancy or inconsistency by continuously analyzing for and correcting systemic issues, ensuring systematic consistency across all codebase sections (e.g., /src/sections), and adhering to best practices throughout. Centralize repeated components or logic where high-value consolidation is possible without functional loss. Use the provided directory tree as the single source of truth for validation, and transparently document all processes, metrics, and validation outcomes. Prohibit modification of protected, legacy, or third-party code except if directly related to resolving redundancy or obsolescence.\n- Maximize codebase coherence, maintainability, and clarity by systematically eliminating all forms of redundancy, duplication, and obsolescence, while ensuring functional equivalence and structural integrity. Prevent the persistence or inadvertent introduction of redundancy, duplication, obsolescence, or inconsistency within the codebase, and prohibit changes that alter essential behaviors or interfaces. Systematically maximize codebase coherence, maintainability, and clarity by eliminating all forms of redundancy, duplication, and obsolescence while strictly preserving functional equivalence, interface stability, and operational integrity across the entire codebase. Achieve maximal codebase coherence, maintainability, and clarity by systematically eliminating all forms of redundancy, duplication, and obsolescence, while preserving essential behaviors, interfaces, and structural integrity. Enforce continuous validation, consistency, and adherence to best practices throughout an iterative, evidence-driven consolidation and verification process.\n- Please perform a rapid assessment of the project directory structure and identify the unique workflow and core functionality of the current utility. I'm particularly interested in a mermaid-diagram that shows how all the \"branches\" of the codebase converge. Utilize appropriate MCPs as necessary (e.g., 'filesystem-mcp').\n- You're inside the current project and have access to the full codebase (and you interaction history with it). The current project/codebase is complex and is difficult to fully comprehend (when starting from scratch), and your contextwindow has grown large-so we need to find a way for you to express your accumulated knowledge in a way that i can copy and paste into a new context-window to transfer the most essential insights you've gathered throughout this (and previous) session(s).\n- Aggregate all accumulated insights and knowledge about the current project/codebase from available context. Distill this information to identify core modules, architectural patterns, workflows, and points of integration. Condense the findings into a succinct yet comprehensive summary that highlights how these parts function together and converge toward the project's goals. Formulate the output as a single, optimized directive that encapsulates the codebase's integral components and operational cohesion. Present this synthesis as a set of clear, actionable commands or instructions, specifically phrased to instruct the coding assistant, not merely describe or report.\n- Transform your accumulated, session-wide understanding of the current, complex codebase—gleaned from comprehensive context and full interaction history—into an actionable, instructive knowledge transfer protocol. Synthesize and distill all integral structural modules, essential architectural patterns, and key workflows, with special focus on their dynamic integration and functional convergence toward project objectives. Formulate a highly-optimized, directive-based set of precise commands targeted explicitly at coding assistants, ensuring exhaustive coverage of all major components, their relationships, and operational cohesion. The resulting synthesis must facilitate seamless portability: structure it for immediate copy-paste into any new context window, preserving the depth, interconnectivity, and systemic flow of the original codebase. Prioritize succinctness without compromising essential nuance, and express the entirety as a singular, comprehensive operational directive that primes the assistant for full-spectrum project navigation and intervention.\n- Identify the fundamental structure, guiding principles, and core purpose of the project/codebase.\n- Please familiarize yourself with the current @codebase. Your main imperative is to *actually* understand the codebase in a way where you're able to recognize meta-patterns and destroy complexity with simplicity and elegance, while capable of navigating through its *existing patterns and convergence* (never add unnecessary complexity). Deliver an ultra-concise project summary that rigorously defines scope, states objectives, and maximizes directional clarity. Provide a short summary of this project, including a bullet-point list of the core components and primary dependencies.\n- Study the current codebase until you can identify core meta-patterns, and eliminate unnecessary complexity by applying simplicity within existing structures. Generate an ultra-concise project summary: define scope, objectives, and clarify direction. Summarize this project.\n- Analyze the @codebase to uncover dominant patterns and remove complexity whenever possible, strictly adhering to present structures. Provide a succinct project overview with defined scope and explicit objectives. Include a brief project summary.\n- Gain practical mastery over @codebase patterns, minimize complexity, and ensure all actions align with existing architecture. Supply a concise, clearly scoped project summary outlining objectives and key directions. Add a short summary.\n- Absorb the current @codebase to the extent that you recognize meta-patterns and eradicate complexity, always favoring elegant simplicity. Navigate established patterns and points of convergence without adding unneeded complexity. Produce a rigorously concise project summary clearly stating scope and objectives for maximal directional clarity.\n- Systematically dissect the codebase to extract core meta-patterns, eliminate all unnecessary complexity through elegant design, operate strictly within established architectural convergence, and deliver an ultra-concise project summary that rigorously defines scope and objectives with maximal clarity.\n- Please familiarize yourself thoroughly with the current @codebase, beginning by systematically reviewing all markdown-files. Your core imperative is to *genuinely* understand the codebase to the degree where you can identify 'meta-patterns' and simplify complexity with elegance—always using and clarifying existing patterns, never introducing unnecessary complexity. Deliver an ultra-concise project summary that rigorously defines scope, states objectives, maximizes directional clarity, and highlights key meta-patterns discovered. Provide a short summary of this project.\n- Achieve expert-level comprehension of the codebase by systematically analyzing all markdown files to extract meta-patterns, construct a robust contextual knowledgebase, and deliver an ultra-concise summary that rigorously defines scope and objectives, ensuring maximal clarity and alignment before making any changes.\n- Fully internalize the @codebase to uncover meta-patterns and replace complexity with lucid simplicity. Remain aligned with existing convergence patterns, never increasing system intricacy. Generate a direct, sharply-focused summary that details scope and explicit objectives.\n- Please thoroughly familiarize yourself with the current @codebase—your primary imperative is to understand the codebase so deeply that you could confidently make changes as if you were its original architect.\n```",
  "sequence_id": "9004",
  "results": [
      {
      "instruction": "# Brief Normaliser\n\nYour goal is **not** to answer the brief, but to rephrase it into a clean, noise-free statement of facts. Execute as:\n\n`{role=brief_normaliser; input=[raw_brief:str]; process=[strip_meta_language(), purge_metaphor(), keep_only_actionable_text(), detect_codebase_token(\"@codebase\"), preserve_original_order()]; constraints=[no_personal_commentary()]; requirements=[output_must_be_plaintext()], output={normalised_brief:str}}`\n\n## Context Data\n\n```json\n{\n  \"description\": \"Dissects a raw specification to extract every piece of operational context an autonomous agent will need.\",\n  \"input_focus\": \"Unedited specification text supplied by the user.\",\n  \"output_focus\": \"A typed context_profile dict containing domain, project root, explicit objectives, and implicit requirements.\",\n  \"key_operations\": [\n    \"Removing metaphor and non‑operational language.\",\n    \"Detecting the working domain or tech stack.\",\n    \"Locating the root marker (`@codebase`) for path scoping.\",\n    \"Listing every stated objective verbatim.\",\n    \"Surfacing hidden assumptions and requirements.\"\n  ],\n  \"constraints_context\": [\n    \"May not paraphrase or interpret meaning beyond direct extraction.\",\n    \"Absolutely forbidden from proposing solutions or tasks.\"\n  ],\n  \"relevance\": \"Defines scope and boundaries for all downstream templates, guaranteeing that later steps operate on clean, actionable data.\"\n}\n```",
      "step": "a",
      "title": "Brief Normaliser",
      "input": "You have unrestricted access to a highly intricate codebase and a comprehensive interaction log, yet the project's complexity and the expansive context window challenge direct comprehension. Your task is to leverage your accumulated contextual knowledge to produce an exceptional, transferable directive: extract and synthesize the deepest insights from the entire codebase and session history, explicitly mapping all pivotal modules, architectural blueprints, underlying workflows, and cross-component integrations. Consolidate these revelations into a compact yet thoroughly interconnected summary, ensuring it clarifies each component's role and their dynamic fusion toward the project’s strategic objectives. Formulate this output as a singular, high-impact action command—explicitly crafted for an advanced coding assistant—to operationalize the codebase cohesively. Your instructions must be unambiguous, comprehensive, and systemically complete, preserving every core relationship and convergence across the architecture, with zero superfluous detail. The end product should function as an authoritative handoff brief: a distilled yet exhaustive set of stepwise directives that empower seamless context re-injection and optimal operational replication, regardless of codebase convolutions or session sequence.\n```\n- Identify all instances of structural redundancy within the codebase by defining criteria specific to architectural and implementation duplication or overlap.\n- Survey the entire codebase to locate and enumerate all occurrences of structural redundancy, distinguishing these from non-structural (stylistic or cosmetic) issues.\n- Eliminate all forms of identified structural redundancy while ensuring that no existing user-visible behavior, API contract, or edge-case handling is altered.\n- Define and catalog all components, modules, files, and subsystems, distinguishing and marking each as essential or excess according to direct functional mapping.\n- Consolidate and remove excess components while strictly preserving system function, minimizing peripheral impact.\n- For every modification, justify and log the intention, action, and rationale within an audit/logging system that provides real-time traceability and is prepared for retroactive auditability.\n- Implement an audit trail with logs that are timestamped, attributable, and tamper-evident, ensuring that every codebase change is properly documented and traceable to specific authors or automated processes.\n- Prohibit cosmetic, stylistic, or naming changes unless they are inseparably tied to the removal of redundancy or bloat, and document any exceptions explicitly within the audit log.\n- Enforce strict detection and flagging mechanisms to prevent disallowed cosmetic changes, allowing exceptions only when justified as essential for redundancy or bloat removal, with explicit rationale provided.\n- Require comprehensive functional equivalence verification—using robust automated testing, benchmarking, or behavioral validation—for all modifications prior to acceptance.\n- Establish rigorous criteria and tool-supported thresholds for successful functional equivalence, and automate testing and verification procedures.\n- Automate roll-back and reversion mechanisms for any changes that fail functional equivalence or cause unintended side-effects, ensuring all reversion is prompt and complete.\n- Develop and apply explicit minimal-impact criteria for all modification actions, constraining all interventions to only those addressing superfluous complexity.\n- Mandate review and authorization checkpoints for each intended change, requiring concise rationale and authorization documentation at every intervention point.\n- Ensure every modification is tightly scoped, minimizing the blast radius, and demonstrate that the action introduces no unnecessary impact beyond its core objective.\n- Systematically and iteratively analyze all constituent elements of the codebase to detect and categorize all forms of redundancy, duplication, and obsolescence. Continuously identify and categorize all genuine redundancies and obsolescence while strictly prohibit unnecessary or cosmetic changes, with automated flagging and explicit justification required for any exceptions. Systematically and iteratively inspect all components within the system/environment to identify and remove elements that are unnecessary, duplicated, or outdated. Guarantee that all essential behaviors and interfaces remain unchanged by validating functional equivalence after each modification. Forbid changes that alter functionality unless directly required to remove redundancy.\n- Map all references and usages of each element across all contexts.\n- Analyze the entire codebase systematically and iteratively to maximize coherence, maintainability, and clarity by cataloging every element (file, module, function, asset, interface), mapping all references and usages, and detecting all forms of redundancy, duplication, and obsolescence—including exact and functional duplication and unused or outdated elements. Categorize findings using explicit, evidence-based metrics. Eliminate or consolidate only elements confirmed as redundant or obsolete through both automated and manual checks, ensuring no essential behavior or interface is altered. Enforce functional equivalence and preserve structural integrity after each change by performing comprehensive, codebase-wide validation and calibration using explicit, quantitative outputs (including git diff analysis before and after modifications). Prevent the introduction of new redundancy or inconsistency by continuously analyzing for and correcting systemic issues, ensuring systematic consistency across all codebase sections (e.g., /src/sections), and adhering to best practices throughout. Centralize repeated components or logic where high-value consolidation is possible without functional loss. Use the provided directory tree as the single source of truth for validation, and transparently document all processes, metrics, and validation outcomes. Prohibit modification of protected, legacy, or third-party code except if directly related to resolving redundancy or obsolescence.\n- Maximize codebase coherence, maintainability, and clarity by systematically eliminating all forms of redundancy, duplication, and obsolescence, while ensuring functional equivalence and structural integrity. Prevent the persistence or inadvertent introduction of redundancy, duplication, obsolescence, or inconsistency within the codebase, and prohibit changes that alter essential behaviors or interfaces. Systematically maximize codebase coherence, maintainability, and clarity by eliminating all forms of redundancy, duplication, and obsolescence while strictly preserving functional equivalence, interface stability, and operational integrity across the entire codebase. Achieve maximal codebase coherence, maintainability, and clarity by systematically eliminating all forms of redundancy, duplication, and obsolescence, while preserving essential behaviors, interfaces, and structural integrity. Enforce continuous validation, consistency, and adherence to best practices throughout an iterative, evidence-driven consolidation and verification process.\n- Please perform a rapid assessment of the project directory structure and identify the unique workflow and core functionality of the current utility. I'm particularly interested in a mermaid-diagram that shows how all the \"branches\" of the codebase converge. Utilize appropriate MCPs as necessary (e.g., 'filesystem-mcp').\n- You're inside the current project and have access to the full codebase (and you interaction history with it). The current project/codebase is complex and is difficult to fully comprehend (when starting from scratch), and your contextwindow has grown large-so we need to find a way for you to express your accumulated knowledge in a way that i can copy and paste into a new context-window to transfer the most essential insights you've gathered throughout this (and previous) session(s).\n- Aggregate all accumulated insights and knowledge about the current project/codebase from available context. Distill this information to identify core modules, architectural patterns, workflows, and points of integration. Condense the findings into a succinct yet comprehensive summary that highlights how these parts function together and converge toward the project's goals. Formulate the output as a single, optimized directive that encapsulates the codebase's integral components and operational cohesion. Present this synthesis as a set of clear, actionable commands or instructions, specifically phrased to instruct the coding assistant, not merely describe or report.\n- Transform your accumulated, session-wide understanding of the current, complex codebase—gleaned from comprehensive context and full interaction history—into an actionable, instructive knowledge transfer protocol. Synthesize and distill all integral structural modules, essential architectural patterns, and key workflows, with special focus on their dynamic integration and functional convergence toward project objectives. Formulate a highly-optimized, directive-based set of precise commands targeted explicitly at coding assistants, ensuring exhaustive coverage of all major components, their relationships, and operational cohesion. The resulting synthesis must facilitate seamless portability: structure it for immediate copy-paste into any new context window, preserving the depth, interconnectivity, and systemic flow of the original codebase. Prioritize succinctness without compromising essential nuance, and express the entirety as a singular, comprehensive operational directive that primes the assistant for full-spectrum project navigation and intervention.\n- Identify the fundamental structure, guiding principles, and core purpose of the project/codebase.\n- Please familiarize yourself with the current @codebase. Your main imperative is to *actually* understand the codebase in a way where you're able to recognize meta-patterns and destroy complexity with simplicity and elegance, while capable of navigating through its *existing patterns and convergence* (never add unnecessary complexity). Deliver an ultra-concise project summary that rigorously defines scope, states objectives, and maximizes directional clarity. Provide a short summary of this project, including a bullet-point list of the core components and primary dependencies.\n- Study the current codebase until you can identify core meta-patterns, and eliminate unnecessary complexity by applying simplicity within existing structures. Generate an ultra-concise project summary: define scope, objectives, and clarify direction. Summarize this project.\n- Analyze the @codebase to uncover dominant patterns and remove complexity whenever possible, strictly adhering to present structures. Provide a succinct project overview with defined scope and explicit objectives. Include a brief project summary.\n- Gain practical mastery over @codebase patterns, minimize complexity, and ensure all actions align with existing architecture. Supply a concise, clearly scoped project summary outlining objectives and key directions. Add a short summary.\n- Absorb the current @codebase to the extent that you recognize meta-patterns and eradicate complexity, always favoring elegant simplicity. Navigate established patterns and points of convergence without adding unneeded complexity. Produce a rigorously concise project summary clearly stating scope and objectives for maximal directional clarity.\n- Systematically dissect the codebase to extract core meta-patterns, eliminate all unnecessary complexity through elegant design, operate strictly within established architectural convergence, and deliver an ultra-concise project summary that rigorously defines scope and objectives with maximal clarity.\n- Please familiarize yourself thoroughly with the current @codebase, beginning by systematically reviewing all markdown-files. Your core imperative is to *genuinely* understand the codebase to the degree where you can identify 'meta-patterns' and simplify complexity with elegance—always using and clarifying existing patterns, never introducing unnecessary complexity. Deliver an ultra-concise project summary that rigorously defines scope, states objectives, maximizes directional clarity, and highlights key meta-patterns discovered. Provide a short summary of this project.\n- Achieve expert-level comprehension of the codebase by systematically analyzing all markdown files to extract meta-patterns, construct a robust contextual knowledgebase, and deliver an ultra-concise summary that rigorously defines scope and objectives, ensuring maximal clarity and alignment before making any changes.\n- Fully internalize the @codebase to uncover meta-patterns and replace complexity with lucid simplicity. Remain aligned with existing convergence patterns, never increasing system intricacy. Generate a direct, sharply-focused summary that details scope and explicit objectives.\n- Please thoroughly familiarize yourself with the current @codebase—your primary imperative is to understand the codebase so deeply that you could confidently make changes as if you were its original architect.\n```",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"normalised_brief\": \"- Unrestricted access to the codebase and complete interaction log is provided for top-level analysis and operational synthesis.\\n- Objective: Extract and synthesize comprehensive, actionable insights about the codebase and session history, explicitly mapping pivotal modules, architectural blueprints, workflows, and cross-component integrations.\\n- Concisely consolidate insights into a compact, interconnected summary clearly showing component roles and their integration toward project objectives.\\n- Form output as a singular, systemically complete, unambiguous action command for an advanced coding assistant to enable cohesive codebase operation.\\n- Preserve all core relationships and integration points in the summary, without extraneous detail.\\n- Ensure the output serves as an authoritative handoff brief: exhaustive directives for re-injecting context and enabling seamless operational replication regardless of complexity or session state.\\n- Identify all instances of structural redundancy (architectural or implementation duplication/overlap) using explicit criteria.\\n- Survey and enumerate all structural redundancies, distinguishing them from non-structural (stylistic or cosmetic) issues.\\n- Remove identified structural redundancies without altering user-visible behavior, API contracts, or edge-case handling.\\n- Catalog all components, modules, files, and subsystems, marking each as essential or excess based on functional mapping.\\n- Consolidate/remove excess components while preserving system function and minimizing impact.\\n- Log all modifications with justifications, timestamped and attributable, in an audit/logging system prepared for retroactive auditing.\\n- Prohibit cosmetic/stylistic/naming changes unless required for redundancy/bloat removal; document all justified exceptions in the audit log.\\n- Enforce detection and flagging to prevent disallowed cosmetic changes, allowing exceptions only with explicit rationale.\\n- Require functional equivalence verification for all modifications using automated testing/benchmarking/behavioral validation, with rigorous criteria and thresholds.\\n- Automate roll-back/reversion for failed changes, ensuring prompt and complete restoration.\\n- Apply explicit minimal-impact criteria to all actions, limiting interventions to superfluous complexity only.\\n- Require review and authorization checkpoints with rationale and documentation for every change.\\n- Scope each modification tightly and demonstrate minimal necessary impact.\\n- Systematically and iteratively analyze the codebase for all redundancies, duplications, and obsolescence.\\n- Continuously categorize all redundancies and obsolescence, prohibiting unnecessary or cosmetic changes, and flagging/justifying exceptions.\\n- Map all references and usages for each element across all contexts.\\n- Catalog every element (file, module, function, asset, interface), map references/usages, and detect duplication/obsolescence using explicit, evidence-based metrics.\\n- Eliminate or consolidate only elements confirmed redundant/obsolete by automated and manual checks, without altering essential behavior or interface.\\n- Enforce functional equivalence and structural integrity after each change via comprehensive validation and quantitative outputs (including git diff analysis).\\n- Prevent new redundancy/inconsistency by continuous codebase-wide analysis and correction, maintaining systematic consistency across all directories, and adhering to established best practices.\\n- Centralize repeated logic or components only when high-value consolidation occurs without functional loss.\\n- Use the provided directory tree as the source of truth for validation.\\n- Document all processes, metrics, and validation outcomes transparently.\\n- Do not modify protected, legacy, or third-party code unless directly related to redundancy/obsolescence.\\n- Maximize codebase coherence, maintainability, and clarity by eliminating redundancy/obsolescence while strictly maintaining functional equivalence, interface stability, and structural integrity.\\n- Enforce continuous validation, codebase-wide consistency, and adherence to best practices throughout all consolidation and verification.\\n- Perform rapid assessment of the project directory structure; identify unique workflow and utility core functionality.\\n- Provide a mermaid diagram to illustrate codebase branch convergence if requested.\\n- Aggregate all accumulated project/codebase insights; identify core modules, architecture, workflows, and integration points.\\n- Condense findings into a succinct summary showing convergence toward project goals.\\n- Express final output as unified, actionable directives/instructions for the coding assistant.\\n- Prioritize succinctness and essential nuance in a single operational directive prepared for copy-paste context transfer.\\n- Define fundamental structure, guiding principles, and core project purpose.\\n- Systematically review all markdown files for meta-pattern recognition and knowledge base construction.\\n- Eliminate complexity by expressing all summaries and directives with maximal clarity, explicitly defining scope and objectives.\\n- Ensure ultra-concise, directionally clear project summary, scope, objectives, and meta-patterns before any action.\\n- Achieve expert-level codebase comprehension, identifying patterns for effective navigation and minimal-complexity intervention.\\n- Never introduce unnecessary complexity—operate strictly within existing structure and convergence patterns.\"\n}",
        }
      }
    },
      {
      "instruction": "# Goal Distiller\n\nYour goal is **not** to plan work, but to extract a single, explicit mission and its hard boundaries. Execute as:\n\n`{role=goal_distiller; input=[normalised_brief:str]; process=[identify_primary_objective(), list_strict_constraints(), capture_success_criteria(), harvest_allowed_tools()], constraints=[exactly_one_primary_objective()], output={distilled_goal:dict(objective:str,constraints:list,success:list,tools:list)}}`\n\n## Context Data\n\n```json\n{\n  \"description\": \"Transforms the context profile into a dependency‑ordered list of granular tasks fit for automated execution.\",\n  \"input_focus\": \"The context_profile produced by the Context Extractor.\",\n  \"output_focus\": \"ordered_tasks – a list of unique, verb‑driven commands each scoped to the codebase.\",\n  \"key_operations\": [\n    \"Splitting broad goals into single‑action commands.\",\n    \"Adding `@codebase` prefixes so agents act in the correct directory.\",\n    \"Sequencing tasks by logical dependency.\",\n    \"Eliminating redundancy and cosmetic‑only instructions.\"\n  ],\n  \"constraints_context\": [\n    \"Every task must begin with a strong action verb (identify, refactor, log, etc.).\",\n    \"Tasks must be non‑overlapping and directly tied to functional goals.\"\n  ],\n  \"relevance\": \"Provides the autonomous agent with a clear, step‑by‑step execution blueprint free of ambiguity.\"\n}\n```",
      "step": "b",
      "title": "Goal Distiller",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"You have unrestricted access to a highly intricate codebase and a comprehensive interaction log, yet the project's complexity and the expansive context window challenge direct comprehension. Your task is to leverage your accumulated contextual knowledge to produce an exceptional, transferable directive: extract and synthesize the deepest insights from the entire codebase and session history, explicitly mapping all pivotal modules, architectural blueprints, underlying workflows, and cross-component integrations. Consolidate these revelations into a compact yet thoroughly interconnected summary, ensuring it clarifies each component's role and their dynamic fusion toward the project’s strategic objectives. Formulate this output as a singular, high-impact action command—explicitly crafted for an advanced coding assistant—to operationalize the codebase cohesively. Your instructions must be unambiguous, comprehensive, and systemically complete, preserving every core relationship and convergence across the architecture, with zero superfluous detail. The end product should function as an authoritative handoff brief: a distilled yet exhaustive set of stepwise directives that empower seamless context re-injection and optimal operational replication, regardless of codebase convolutions or session sequence.\\n```\\n- Identify all instances of structural redundancy within the codebase by defining criteria specific to architectural and implementation duplication or overlap.\\n- Survey the entire codebase to locate and enumerate all occurrences of structural redundancy, distinguishing these from non-structural (stylistic or cosmetic) issues.\\n- Eliminate all forms of identified structural redundancy while ensuring that no existing user-visible behavior, API contract, or edge-case handling is altered.\\n- Define and catalog all components, modules, files, and subsystems, distinguishing and marking each as essential or excess according to direct functional mapping.\\n- Consolidate and remove excess components while strictly preserving system function, minimizing peripheral impact.\\n- For every modification, justify and log the intention, action, and rationale within an audit/logging system that provides real-time traceability and is prepared for retroactive auditability.\\n- Implement an audit trail with logs that are timestamped, attributable, and tamper-evident, ensuring that every codebase change is properly documented and traceable to specific authors or automated processes.\\n- Prohibit cosmetic, stylistic, or naming changes unless they are inseparably tied to the removal of redundancy or bloat, and document any exceptions explicitly within the audit log.\\n- Enforce strict detection and flagging mechanisms to prevent disallowed cosmetic changes, allowing exceptions only when justified as essential for redundancy or bloat removal, with explicit rationale provided.\\n- Require comprehensive functional equivalence verification—using robust automated testing, benchmarking, or behavioral validation—for all modifications prior to acceptance.\\n- Establish rigorous criteria and tool-supported thresholds for successful functional equivalence, and automate testing and verification procedures.\\n- Automate roll-back and reversion mechanisms for any changes that fail functional equivalence or cause unintended side-effects, ensuring all reversion is prompt and complete.\\n- Develop and apply explicit minimal-impact criteria for all modification actions, constraining all interventions to only those addressing superfluous complexity.\\n- Mandate review and authorization checkpoints for each intended change, requiring concise rationale and authorization documentation at every intervention point.\\n- Ensure every modification is tightly scoped, minimizing the blast radius, and demonstrate that the action introduces no unnecessary impact beyond its core objective.\\n- Systematically and iteratively analyze all constituent elements of the codebase to detect and categorize all forms of redundancy, duplication, and obsolescence. Continuously identify and categorize all genuine redundancies and obsolescence while strictly prohibit unnecessary or cosmetic changes, with automated flagging and explicit justification required for any exceptions. Systematically and iteratively inspect all components within the system/environment to identify and remove elements that are unnecessary, duplicated, or outdated. Guarantee that all essential behaviors and interfaces remain unchanged by validating functional equivalence after each modification. Forbid changes that alter functionality unless directly required to remove redundancy.\\n- Map all references and usages of each element across all contexts.\\n- Analyze the entire codebase systematically and iteratively to maximize coherence, maintainability, and clarity by cataloging every element (file, module, function, asset, interface), mapping all references and usages, and detecting all forms of redundancy, duplication, and obsolescence—including exact and functional duplication and unused or outdated elements. Categorize findings using explicit, evidence-based metrics. Eliminate or consolidate only elements confirmed as redundant or obsolete through both automated and manual checks, ensuring no essential behavior or interface is altered. Enforce functional equivalence and preserve structural integrity after each change by performing comprehensive, codebase-wide validation and calibration using explicit, quantitative outputs (including git diff analysis before and after modifications). Prevent the introduction of new redundancy or inconsistency by continuously analyzing for and correcting systemic issues, ensuring systematic consistency across all codebase sections (e.g., /src/sections), and adhering to best practices throughout. Centralize repeated components or logic where high-value consolidation is possible without functional loss. Use the provided directory tree as the single source of truth for validation, and transparently document all processes, metrics, and validation outcomes. Prohibit modification of protected, legacy, or third-party code except if directly related to resolving redundancy or obsolescence.\\n- Maximize codebase coherence, maintainability, and clarity by systematically eliminating all forms of redundancy, duplication, and obsolescence, while ensuring functional equivalence and structural integrity. Prevent the persistence or inadvertent introduction of redundancy, duplication, obsolescence, or inconsistency within the codebase, and prohibit changes that alter essential behaviors or interfaces. Systematically maximize codebase coherence, maintainability, and clarity by eliminating all forms of redundancy, duplication, and obsolescence while strictly preserving functional equivalence, interface stability, and operational integrity across the entire codebase. Achieve maximal codebase coherence, maintainability, and clarity by systematically eliminating all forms of redundancy, duplication, and obsolescence, while preserving essential behaviors, interfaces, and structural integrity. Enforce continuous validation, consistency, and adherence to best practices throughout an iterative, evidence-driven consolidation and verification process.\\n- Please perform a rapid assessment of the project directory structure and identify the unique workflow and core functionality of the current utility. I'm particularly interested in a mermaid-diagram that shows how all the \\\"branches\\\" of the codebase converge. Utilize appropriate MCPs as necessary (e.g., 'filesystem-mcp').\\n- You're inside the current project and have access to the full codebase (and you interaction history with it). The current project/codebase is complex and is difficult to fully comprehend (when starting from scratch), and your contextwindow has grown large-so we need to find a way for you to express your accumulated knowledge in a way that i can copy and paste into a new context-window to transfer the most essential insights you've gathered throughout this (and previous) session(s).\\n- Aggregate all accumulated insights and knowledge about the current project/codebase from available context. Distill this information to identify core modules, architectural patterns, workflows, and points of integration. Condense the findings into a succinct yet comprehensive summary that highlights how these parts function together and converge toward the project's goals. Formulate the output as a single, optimized directive that encapsulates the codebase's integral components and operational cohesion. Present this synthesis as a set of clear, actionable commands or instructions, specifically phrased to instruct the coding assistant, not merely describe or report.\\n- Transform your accumulated, session-wide understanding of the current, complex codebase—gleaned from comprehensive context and full interaction history—into an actionable, instructive knowledge transfer protocol. Synthesize and distill all integral structural modules, essential architectural patterns, and key workflows, with special focus on their dynamic integration and functional convergence toward project objectives. Formulate a highly-optimized, directive-based set of precise commands targeted explicitly at coding assistants, ensuring exhaustive coverage of all major components, their relationships, and operational cohesion. The resulting synthesis must facilitate seamless portability: structure it for immediate copy-paste into any new context window, preserving the depth, interconnectivity, and systemic flow of the original codebase. Prioritize succinctness without compromising essential nuance, and express the entirety as a singular, comprehensive operational directive that primes the assistant for full-spectrum project navigation and intervention.\\n- Identify the fundamental structure, guiding principles, and core purpose of the project/codebase.\\n- Please familiarize yourself with the current @codebase. Your main imperative is to *actually* understand the codebase in a way where you're able to recognize meta-patterns and destroy complexity with simplicity and elegance, while capable of navigating through its *existing patterns and convergence* (never add unnecessary complexity). Deliver an ultra-concise project summary that rigorously defines scope, states objectives, and maximizes directional clarity. Provide a short summary of this project, including a bullet-point list of the core components and primary dependencies.\\n- Study the current codebase until you can identify core meta-patterns, and eliminate unnecessary complexity by applying simplicity within existing structures. Generate an ultra-concise project summary: define scope, objectives, and clarify direction. Summarize this project.\\n- Analyze the @codebase to uncover dominant patterns and remove complexity whenever possible, strictly adhering to present structures. Provide a succinct project overview with defined scope and explicit objectives. Include a brief project summary.\\n- Gain practical mastery over @codebase patterns, minimize complexity, and ensure all actions align with existing architecture. Supply a concise, clearly scoped project summary outlining objectives and key directions. Add a short summary.\\n- Absorb the current @codebase to the extent that you recognize meta-patterns and eradicate complexity, always favoring elegant simplicity. Navigate established patterns and points of convergence without adding unneeded complexity. Produce a rigorously concise project summary clearly stating scope and objectives for maximal directional clarity.\\n- Systematically dissect the codebase to extract core meta-patterns, eliminate all unnecessary complexity through elegant design, operate strictly within established architectural convergence, and deliver an ultra-concise project summary that rigorously defines scope and objectives with maximal clarity.\\n- Please familiarize yourself thoroughly with the current @codebase, beginning by systematically reviewing all markdown-files. Your core imperative is to *genuinely* understand the codebase to the degree where you can identify 'meta-patterns' and simplify complexity with elegance—always using and clarifying existing patterns, never introducing unnecessary complexity. Deliver an ultra-concise project summary that rigorously defines scope, states objectives, maximizes directional clarity, and highlights key meta-patterns discovered. Provide a short summary of this project.\\n- Achieve expert-level comprehension of the codebase by systematically analyzing all markdown files to extract meta-patterns, construct a robust contextual knowledgebase, and deliver an ultra-concise summary that rigorously defines scope and objectives, ensuring maximal clarity and alignment before making any changes.\\n- Fully internalize the @codebase to uncover meta-patterns and replace complexity with lucid simplicity. Remain aligned with existing convergence patterns, never increasing system intricacy. Generate a direct, sharply-focused summary that details scope and explicit objectives.\\n- Please thoroughly familiarize yourself with the current @codebase—your primary imperative is to understand the codebase so deeply that you could confidently make changes as if you were its original architect.\\n```\\\"```'\\n\\n\",\n  \"normalised_brief\": \"- Unrestricted access to the codebase and complete interaction log is provided for top-level analysis and operational synthesis.\\n- Objective: Extract and synthesize comprehensive, actionable insights about the codebase and session history, explicitly mapping pivotal modules, architectural blueprints, workflows, and cross-component integrations.\\n- Concisely consolidate insights into a compact, interconnected summary clearly showing component roles and their integration toward project objectives.\\n- Form output as a singular, systemically complete, unambiguous action command for an advanced coding assistant to enable cohesive codebase operation.\\n- Preserve all core relationships and integration points in the summary, without extraneous detail.\\n- Ensure the output serves as an authoritative handoff brief: exhaustive directives for re-injecting context and enabling seamless operational replication regardless of complexity or session state.\\n- Identify all instances of structural redundancy (architectural or implementation duplication/overlap) using explicit criteria.\\n- Survey and enumerate all structural redundancies, distinguishing them from non-structural (stylistic or cosmetic) issues.\\n- Remove identified structural redundancies without altering user-visible behavior, API contracts, or edge-case handling.\\n- Catalog all components, modules, files, and subsystems, marking each as essential or excess based on functional mapping.\\n- Consolidate/remove excess components while preserving system function and minimizing impact.\\n- Log all modifications with justifications, timestamped and attributable, in an audit/logging system prepared for retroactive auditing.\\n- Prohibit cosmetic/stylistic/naming changes unless required for redundancy/bloat removal; document all justified exceptions in the audit log.\\n- Enforce detection and flagging to prevent disallowed cosmetic changes, allowing exceptions only with explicit rationale.\\n- Require functional equivalence verification for all modifications using automated testing/benchmarking/behavioral validation, with rigorous criteria and thresholds.\\n- Automate roll-back/reversion for failed changes, ensuring prompt and complete restoration.\\n- Apply explicit minimal-impact criteria to all actions, limiting interventions to superfluous complexity only.\\n- Require review and authorization checkpoints with rationale and documentation for every change.\\n- Scope each modification tightly and demonstrate minimal necessary impact.\\n- Systematically and iteratively analyze the codebase for all redundancies, duplications, and obsolescence.\\n- Continuously categorize all redundancies and obsolescence, prohibiting unnecessary or cosmetic changes, and flagging/justifying exceptions.\\n- Map all references and usages for each element across all contexts.\\n- Catalog every element (file, module, function, asset, interface), map references/usages, and detect duplication/obsolescence using explicit, evidence-based metrics.\\n- Eliminate or consolidate only elements confirmed redundant/obsolete by automated and manual checks, without altering essential behavior or interface.\\n- Enforce functional equivalence and structural integrity after each change via comprehensive validation and quantitative outputs (including git diff analysis).\\n- Prevent new redundancy/inconsistency by continuous codebase-wide analysis and correction, maintaining systematic consistency across all directories, and adhering to established best practices.\\n- Centralize repeated logic or components only when high-value consolidation occurs without functional loss.\\n- Use the provided directory tree as the source of truth for validation.\\n- Document all processes, metrics, and validation outcomes transparently.\\n- Do not modify protected, legacy, or third-party code unless directly related to redundancy/obsolescence.\\n- Maximize codebase coherence, maintainability, and clarity by eliminating redundancy/obsolescence while strictly maintaining functional equivalence, interface stability, and structural integrity.\\n- Enforce continuous validation, codebase-wide consistency, and adherence to best practices throughout all consolidation and verification.\\n- Perform rapid assessment of the project directory structure; identify unique workflow and utility core functionality.\\n- Provide a mermaid diagram to illustrate codebase branch convergence if requested.\\n- Aggregate all accumulated project/codebase insights; identify core modules, architecture, workflows, and integration points.\\n- Condense findings into a succinct summary showing convergence toward project goals.\\n- Express final output as unified, actionable directives/instructions for the coding assistant.\\n- Prioritize succinctness and essential nuance in a single operational directive prepared for copy-paste context transfer.\\n- Define fundamental structure, guiding principles, and core project purpose.\\n- Systematically review all markdown files for meta-pattern recognition and knowledge base construction.\\n- Eliminate complexity by expressing all summaries and directives with maximal clarity, explicitly defining scope and objectives.\\n- Ensure ultra-concise, directionally clear project summary, scope, objectives, and meta-patterns before any action.\\n- Achieve expert-level codebase comprehension, identifying patterns for effective navigation and minimal-complexity intervention.\\n- Never introduce unnecessary complexity—operate strictly within existing structure and convergence patterns.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"distilled_goal\": {\n    \"objective\": \"Produce a single, systemically complete and operational handoff directive for a coding assistant: extract, synthesize, and consolidate the deepest actionable insights, core modules, architectural blueprints, workflows, and integration points of the entire codebase and session history into a concise, interconnected summary that clarifies each component’s role and system convergence, eliminating all structural redundancy and obsolescence while strictly preserving essential behaviors, interfaces, and operational integrity—so the directive enables seamless context re-injection and optimal operational replication in any new environment.\",\n    \"constraints\": [\n      \"Output must be a singular, authoritative directive—unambiguous, exhaustive, operational, and ready for copy-paste deployment.\",\n      \"All core codebase relationships, workflows, and integrations must be preserved and explicitly mapped.\",\n      \"No user-facing behavior, API contracts, or edge-case handling may be altered.\",\n      \"Structural redundancy (architectural or implementation-level) must be fully enumerated and eliminated, with all excess components removed only if functionally confirmed redundant or obsolete.\",\n      \"No protected, legacy, or third-party code may be modified unless required for redundancy/obsolescence resolution.\",\n      \"All modifications must be documented with rationale in a tamper-evident, timestamped audit log; attribution is required for each change.\",\n      \"Cosmetic/stylistic/naming changes are prohibited unless inseparable from redundancy removal and then must be explicitly justified in the audit log.\",\n      \"Functional equivalence after each modification must be enforced using robust automated validation and explicit evidence (e.g., test passing, benchmark parity, git diff analysis).\",\n      \"Automated roll-back of failed changes must be in place.\",\n      \"All validation outcomes, categorization metrics, and processes must be transparently documented.\",\n      \"Directory tree is the source of truth for structure validation.\",\n      \"All review, authorization, and change checkpoints require explicit documentation of rationale and impact.\",\n      \"Changes must be tightly scoped with minimal blast radius, never adding unnecessary complexity.\",\n      \"Best practices and codebase consistency must be maintained throughout; continuous analysis and systemic correction required.\"\n    ],\n    \"success\": [\n      \"The produced directive provides an exhaustive, connected map of all core codebase components, workflows, and integration points.\",\n      \"All structural redundancy and obsolescence are removed without functional or interface loss.\",\n      \"No unauthorized or cosmetic changes are present; all logged exceptions are justified.\",\n      \"Functional equivalence and operational integrity are demonstrably preserved via automated checks and output evidence.\",\n      \"The summary is concise, directionally clear, and structured for immediate, lossless transfer into any new coding assistant session or context window.\"\n    ],\n    \"tools\": [\n      \"Full codebase access (read/write, all files and directories)\",\n      \"Automated static analysis tools (redundancy and duplication detectors)\",\n      \"Automated test suites and behavioral validation tools\",\n      \"Version control utilities (e.g., git diff, git history, automated rollback features)\",\n      \"Audit and logging framework (timestamped, tamper-evident, attribution-ready)\",\n      \"Directory tree inspection/visualization tools\",\n      \"Automated benchmarking scripts\",\n      \"Mermaid or equivalent diagram generators (for architectural/convergence diagrams when requested)\"\n    ]\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Task Architect\n\nYour goal is **not** to write prose, but to decompose the objective into ordered, atomic tasks with built-in validation hooks. Execute as:\n\n`{role=task_architect; input=[distilled_goal:dict]; process=[derive_atomic_tasks(), order_tasks_by_dependency(), pair_each_task_with_validation_method(), tag_required_artifacts()], requirements=[no_task_overlap(), every_task_has_validation()], output={task_list:list}}`\n\n## Context Data\n\n```json\n{\n  \"description\": \"Consolidates every rule, boundary, and policy the agent must respect during execution.\",\n  \"input_focus\": \"context_profile and ordered_tasks.\",\n  \"output_focus\": \"constraint_set – a deduplicated list of textual constraints.\",\n  \"key_operations\": [\n    \"Harvesting functional‑equivalence mandates.\",\n    \"Capturing API and interface preservation rules.\",\n    \"Recording audit/logging, rollback, and policy obligations.\",\n    \"Removing duplicate or conflicting constraints.\"\n  ],\n  \"constraints_context\": [\n    \"Must not create or modify tasks; only list constraints.\",\n    \"Constraint entries must be unique and actionable.\"\n  ],\n  \"relevance\": \"Prevents unintended side‑effects by explicitly defining the operational guard‑rails for the agent.\"\n}\n```",
      "step": "c",
      "title": "Task Architect",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"You have unrestricted access to a highly intricate codebase and a comprehensive interaction log, yet the project's complexity and the expansive context window challenge direct comprehension. Your task is to leverage your accumulated contextual knowledge to produce an exceptional, transferable directive: extract and synthesize the deepest insights from the entire codebase and session history, explicitly mapping all pivotal modules, architectural blueprints, underlying workflows, and cross-component integrations. Consolidate these revelations into a compact yet thoroughly interconnected summary, ensuring it clarifies each component's role and their dynamic fusion toward the project’s strategic objectives. Formulate this output as a singular, high-impact action command—explicitly crafted for an advanced coding assistant—to operationalize the codebase cohesively. Your instructions must be unambiguous, comprehensive, and systemically complete, preserving every core relationship and convergence across the architecture, with zero superfluous detail. The end product should function as an authoritative handoff brief: a distilled yet exhaustive set of stepwise directives that empower seamless context re-injection and optimal operational replication, regardless of codebase convolutions or session sequence.\\n```\\n- Identify all instances of structural redundancy within the codebase by defining criteria specific to architectural and implementation duplication or overlap.\\n- Survey the entire codebase to locate and enumerate all occurrences of structural redundancy, distinguishing these from non-structural (stylistic or cosmetic) issues.\\n- Eliminate all forms of identified structural redundancy while ensuring that no existing user-visible behavior, API contract, or edge-case handling is altered.\\n- Define and catalog all components, modules, files, and subsystems, distinguishing and marking each as essential or excess according to direct functional mapping.\\n- Consolidate and remove excess components while strictly preserving system function, minimizing peripheral impact.\\n- For every modification, justify and log the intention, action, and rationale within an audit/logging system that provides real-time traceability and is prepared for retroactive auditability.\\n- Implement an audit trail with logs that are timestamped, attributable, and tamper-evident, ensuring that every codebase change is properly documented and traceable to specific authors or automated processes.\\n- Prohibit cosmetic, stylistic, or naming changes unless they are inseparably tied to the removal of redundancy or bloat, and document any exceptions explicitly within the audit log.\\n- Enforce strict detection and flagging mechanisms to prevent disallowed cosmetic changes, allowing exceptions only when justified as essential for redundancy or bloat removal, with explicit rationale provided.\\n- Require comprehensive functional equivalence verification—using robust automated testing, benchmarking, or behavioral validation—for all modifications prior to acceptance.\\n- Establish rigorous criteria and tool-supported thresholds for successful functional equivalence, and automate testing and verification procedures.\\n- Automate roll-back and reversion mechanisms for any changes that fail functional equivalence or cause unintended side-effects, ensuring all reversion is prompt and complete.\\n- Develop and apply explicit minimal-impact criteria for all modification actions, constraining all interventions to only those addressing superfluous complexity.\\n- Mandate review and authorization checkpoints for each intended change, requiring concise rationale and authorization documentation at every intervention point.\\n- Ensure every modification is tightly scoped, minimizing the blast radius, and demonstrate that the action introduces no unnecessary impact beyond its core objective.\\n- Systematically and iteratively analyze all constituent elements of the codebase to detect and categorize all forms of redundancy, duplication, and obsolescence. Continuously identify and categorize all genuine redundancies and obsolescence while strictly prohibit unnecessary or cosmetic changes, with automated flagging and explicit justification required for any exceptions. Systematically and iteratively inspect all components within the system/environment to identify and remove elements that are unnecessary, duplicated, or outdated. Guarantee that all essential behaviors and interfaces remain unchanged by validating functional equivalence after each modification. Forbid changes that alter functionality unless directly required to remove redundancy.\\n- Map all references and usages of each element across all contexts.\\n- Analyze the entire codebase systematically and iteratively to maximize coherence, maintainability, and clarity by cataloging every element (file, module, function, asset, interface), mapping all references and usages, and detecting all forms of redundancy, duplication, and obsolescence—including exact and functional duplication and unused or outdated elements. Categorize findings using explicit, evidence-based metrics. Eliminate or consolidate only elements confirmed as redundant or obsolete through both automated and manual checks, ensuring no essential behavior or interface is altered. Enforce functional equivalence and preserve structural integrity after each change by performing comprehensive, codebase-wide validation and calibration using explicit, quantitative outputs (including git diff analysis before and after modifications). Prevent the introduction of new redundancy or inconsistency by continuously analyzing for and correcting systemic issues, ensuring systematic consistency across all codebase sections (e.g., /src/sections), and adhering to best practices throughout. Centralize repeated components or logic where high-value consolidation is possible without functional loss. Use the provided directory tree as the single source of truth for validation, and transparently document all processes, metrics, and validation outcomes. Prohibit modification of protected, legacy, or third-party code except if directly related to resolving redundancy or obsolescence.\\n- Maximize codebase coherence, maintainability, and clarity by systematically eliminating all forms of redundancy, duplication, and obsolescence, while ensuring functional equivalence and structural integrity. Prevent the persistence or inadvertent introduction of redundancy, duplication, obsolescence, or inconsistency within the codebase, and prohibit changes that alter essential behaviors or interfaces. Systematically maximize codebase coherence, maintainability, and clarity by eliminating all forms of redundancy, duplication, and obsolescence while strictly preserving functional equivalence, interface stability, and operational integrity across the entire codebase. Achieve maximal codebase coherence, maintainability, and clarity by systematically eliminating all forms of redundancy, duplication, and obsolescence, while preserving essential behaviors, interfaces, and structural integrity. Enforce continuous validation, consistency, and adherence to best practices throughout an iterative, evidence-driven consolidation and verification process.\\n- Please perform a rapid assessment of the project directory structure and identify the unique workflow and core functionality of the current utility. I'm particularly interested in a mermaid-diagram that shows how all the \\\"branches\\\" of the codebase converge. Utilize appropriate MCPs as necessary (e.g., 'filesystem-mcp').\\n- You're inside the current project and have access to the full codebase (and you interaction history with it). The current project/codebase is complex and is difficult to fully comprehend (when starting from scratch), and your contextwindow has grown large-so we need to find a way for you to express your accumulated knowledge in a way that i can copy and paste into a new context-window to transfer the most essential insights you've gathered throughout this (and previous) session(s).\\n- Aggregate all accumulated insights and knowledge about the current project/codebase from available context. Distill this information to identify core modules, architectural patterns, workflows, and points of integration. Condense the findings into a succinct yet comprehensive summary that highlights how these parts function together and converge toward the project's goals. Formulate the output as a single, optimized directive that encapsulates the codebase's integral components and operational cohesion. Present this synthesis as a set of clear, actionable commands or instructions, specifically phrased to instruct the coding assistant, not merely describe or report.\\n- Transform your accumulated, session-wide understanding of the current, complex codebase—gleaned from comprehensive context and full interaction history—into an actionable, instructive knowledge transfer protocol. Synthesize and distill all integral structural modules, essential architectural patterns, and key workflows, with special focus on their dynamic integration and functional convergence toward project objectives. Formulate a highly-optimized, directive-based set of precise commands targeted explicitly at coding assistants, ensuring exhaustive coverage of all major components, their relationships, and operational cohesion. The resulting synthesis must facilitate seamless portability: structure it for immediate copy-paste into any new context window, preserving the depth, interconnectivity, and systemic flow of the original codebase. Prioritize succinctness without compromising essential nuance, and express the entirety as a singular, comprehensive operational directive that primes the assistant for full-spectrum project navigation and intervention.\\n- Identify the fundamental structure, guiding principles, and core purpose of the project/codebase.\\n- Please familiarize yourself with the current @codebase. Your main imperative is to *actually* understand the codebase in a way where you're able to recognize meta-patterns and destroy complexity with simplicity and elegance, while capable of navigating through its *existing patterns and convergence* (never add unnecessary complexity). Deliver an ultra-concise project summary that rigorously defines scope, states objectives, and maximizes directional clarity. Provide a short summary of this project, including a bullet-point list of the core components and primary dependencies.\\n- Study the current codebase until you can identify core meta-patterns, and eliminate unnecessary complexity by applying simplicity within existing structures. Generate an ultra-concise project summary: define scope, objectives, and clarify direction. Summarize this project.\\n- Analyze the @codebase to uncover dominant patterns and remove complexity whenever possible, strictly adhering to present structures. Provide a succinct project overview with defined scope and explicit objectives. Include a brief project summary.\\n- Gain practical mastery over @codebase patterns, minimize complexity, and ensure all actions align with existing architecture. Supply a concise, clearly scoped project summary outlining objectives and key directions. Add a short summary.\\n- Absorb the current @codebase to the extent that you recognize meta-patterns and eradicate complexity, always favoring elegant simplicity. Navigate established patterns and points of convergence without adding unneeded complexity. Produce a rigorously concise project summary clearly stating scope and objectives for maximal directional clarity.\\n- Systematically dissect the codebase to extract core meta-patterns, eliminate all unnecessary complexity through elegant design, operate strictly within established architectural convergence, and deliver an ultra-concise project summary that rigorously defines scope and objectives with maximal clarity.\\n- Please familiarize yourself thoroughly with the current @codebase, beginning by systematically reviewing all markdown-files. Your core imperative is to *genuinely* understand the codebase to the degree where you can identify 'meta-patterns' and simplify complexity with elegance—always using and clarifying existing patterns, never introducing unnecessary complexity. Deliver an ultra-concise project summary that rigorously defines scope, states objectives, maximizes directional clarity, and highlights key meta-patterns discovered. Provide a short summary of this project.\\n- Achieve expert-level comprehension of the codebase by systematically analyzing all markdown files to extract meta-patterns, construct a robust contextual knowledgebase, and deliver an ultra-concise summary that rigorously defines scope and objectives, ensuring maximal clarity and alignment before making any changes.\\n- Fully internalize the @codebase to uncover meta-patterns and replace complexity with lucid simplicity. Remain aligned with existing convergence patterns, never increasing system intricacy. Generate a direct, sharply-focused summary that details scope and explicit objectives.\\n- Please thoroughly familiarize yourself with the current @codebase—your primary imperative is to understand the codebase so deeply that you could confidently make changes as if you were its original architect.\\n```\\\"```'\\n\\n\",\n  \"distilled_goal\": {\n    \"objective\": \"Produce a single, systemically complete and operational handoff directive for a coding assistant: extract, synthesize, and consolidate the deepest actionable insights, core modules, architectural blueprints, workflows, and integration points of the entire codebase and session history into a concise, interconnected summary that clarifies each component’s role and system convergence, eliminating all structural redundancy and obsolescence while strictly preserving essential behaviors, interfaces, and operational integrity—so the directive enables seamless context re-injection and optimal operational replication in any new environment.\",\n    \"constraints\": [\n      \"Output must be a singular, authoritative directive—unambiguous, exhaustive, operational, and ready for copy-paste deployment.\",\n      \"All core codebase relationships, workflows, and integrations must be preserved and explicitly mapped.\",\n      \"No user-facing behavior, API contracts, or edge-case handling may be altered.\",\n      \"Structural redundancy (architectural or implementation-level) must be fully enumerated and eliminated, with all excess components removed only if functionally confirmed redundant or obsolete.\",\n      \"No protected, legacy, or third-party code may be modified unless required for redundancy/obsolescence resolution.\",\n      \"All modifications must be documented with rationale in a tamper-evident, timestamped audit log; attribution is required for each change.\",\n      \"Cosmetic/stylistic/naming changes are prohibited unless inseparable from redundancy removal and then must be explicitly justified in the audit log.\",\n      \"Functional equivalence after each modification must be enforced using robust automated validation and explicit evidence (e.g., test passing, benchmark parity, git diff analysis).\",\n      \"Automated roll-back of failed changes must be in place.\",\n      \"All validation outcomes, categorization metrics, and processes must be transparently documented.\",\n      \"Directory tree is the source of truth for structure validation.\",\n      \"All review, authorization, and change checkpoints require explicit documentation of rationale and impact.\",\n      \"Changes must be tightly scoped with minimal blast radius, never adding unnecessary complexity.\",\n      \"Best practices and codebase consistency must be maintained throughout; continuous analysis and systemic correction required.\"\n    ],\n    \"success\": [\n      \"The produced directive provides an exhaustive, connected map of all core codebase components, workflows, and integration points.\",\n      \"All structural redundancy and obsolescence are removed without functional or interface loss.\",\n      \"No unauthorized or cosmetic changes are present; all logged exceptions are justified.\",\n      \"Functional equivalence and operational integrity are demonstrably preserved via automated checks and output evidence.\",\n      \"The summary is concise, directionally clear, and structured for immediate, lossless transfer into any new coding assistant session or context window.\"\n    ],\n    \"tools\": [\n      \"Full codebase access (read/write, all files and directories)\",\n      \"Automated static analysis tools (redundancy and duplication detectors)\",\n      \"Automated test suites and behavioral validation tools\",\n      \"Version control utilities (e.g., git diff, git history, automated rollback features)\",\n      \"Audit and logging framework (timestamped, tamper-evident, attribution-ready)\",\n      \"Directory tree inspection/visualization tools\",\n      \"Automated benchmarking scripts\",\n      \"Mermaid or equivalent diagram generators (for architectural/convergence diagrams when requested)\"\n    ]\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"task_list\": [\n    {\n      \"task\": \"Aggregate all project context, codebase structure, interaction logs, and prior accumulated insights to form a complete context set.\",\n      \"validation\": \"Confirm availability and completeness of all major source code directories, logs, and session artifacts. Cross-check against the latest directory tree.\",\n      \"artifacts\": [\"context_profile\", \"directory_tree\", \"interaction_log\"]\n    },\n    {\n      \"task\": \"Extract and catalog all explicit and implicit rules, boundaries, and policies from the context profile and ordered_tasks.\",\n      \"validation\": \"Every rule or mandate in the input context and distilled_goal must be present as a candidate constraint. Use line-by-line cross-check with source documents.\",\n      \"artifacts\": [\"context_profile\", \"ordered_tasks\"]\n    },\n    {\n      \"task\": \"Harvest and record all functional-equivalence mandates, interface/API preservation rules, and legacy/protected code modification prohibitions.\",\n      \"validation\": \"Each recorded item must trace back to a specific source mandate or constraint in the provided context. All must be actionable.\",\n      \"artifacts\": [\"context_profile\", \"ordered_tasks\"]\n    },\n    {\n      \"task\": \"Capture all audit, logging, rollback, attribution, and real-time traceability obligations—including required formats, frequencies, and properties (tamper-evident, timestamped, etc).\",\n      \"validation\": \"Each audit/logging policy must be traced to at least one explicit input statement, and cover format, process, and security properties.\",\n      \"artifacts\": [\"context_profile\"]\n    },\n    {\n      \"task\": \"Document all review, authorization, and change management requirements, including scoping and justification mandates.\",\n      \"validation\": \"Checklist: every review/authorization/rationale checkpoint described in the context appears as a unique requirement.\",\n      \"artifacts\": [\"ordered_tasks\", \"context_profile\"]\n    },\n    {\n      \"task\": \"Identify and document all restrictions on cosmetic, stylistic, and naming changes, as well as exception/justification protocols.\",\n      \"validation\": \"All restrictions must be present; at least one documented exception-handling process must be listed if described in the context.\",\n      \"artifacts\": [\"context_profile\"]\n    },\n    {\n      \"task\": \"Extract all requirements and protocols for automation in validation: automated testing, benchmarking, functional equivalence checks, and rollback mechanisms.\",\n      \"validation\": \"For each automation or validation tool described, confirm it is represented as a unique actionable constraint.\",\n      \"artifacts\": [\"context_profile\", \"ordered_tasks\"]\n    },\n    {\n      \"task\": \"List all requirements for systemic consistency, structural integrity, minimal-impact intervention, and best-practices adherence.\",\n      \"validation\": \"Confirm each principle or system-wide property is represented at least once as a unique constraint.\",\n      \"artifacts\": [\"context_profile\", \"distilled_goal\"]\n    },\n    {\n      \"task\": \"Enumerate all source-of-truth and validation reference mandates (e.g., use of directory tree, documentation requirements, explicit validation outputs, version control state, etc).\",\n      \"validation\": \"Each source-of-truth protocol must be explicitly enumerated with reference to its origin statement.\",\n      \"artifacts\": [\"context_profile\", \"ordered_tasks\"]\n    },\n    {\n      \"task\": \"Deduplicate all constraints, removing functionally equivalent or redundant entries, and resolve any internal conflicts with explicit prioritization (e.g., preserve functional behavior over minor cosmetic uniformity).\",\n      \"validation\": \"Run a uniqueness and conflict check—no two constraints may have overlapping or mutually exclusive outcomes. Each retained constraint must be actionable.\",\n      \"artifacts\": [\"draft_constraint_list\"]\n    },\n    {\n      \"task\": \"Output the final, ordered constraint set as a deduplicated, unique, and actionable textual list—the definitive operational constraint profile.\",\n      \"validation\": \"Manual review—every item must be a single actionable constraint; no duplicates or non-actionable text remain.\",\n      \"artifacts\": [\"constraint_set\"]\n    }\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Prompt Composer\n\nYour goal is **not** to validate, but to fuse role, objective, and tasks into one executable agent prompt. Execute as:\n\n`{role=prompt_composer; input={distilled_goal:dict, task_list:list}; process=[build_system_section(role=\"Autonomous Coding Assistant\"), insert_goal_negation_intro(\"do not answer, perform tasks\"), embed_task_sequence(), state_global_constraints(), specify_output_format(\"markdown\"), include_codebase_hint(\"@codebase\")], requirements=[prompt_under_600_tokens()], output={assembled_prompt:str}}`\n\n## Context Data\n\n```json\n{\n  \"description\": \"Integrates tasks and constraints into a single, executable prompt string for autonomous coding agents.\",\n  \"input_focus\": \"ordered_tasks, constraint_set, and context_profile.\",\n  \"output_focus\": \"agent_prompt – complete instructions with sections for role, tasks, constraints, and validation goals.\",\n  \"key_operations\": [\n    \"Starting with a goal‑negation header to lock scope.\",\n    \"Embedding a precise agent role declaration.\",\n    \"Listing tasks numerically for deterministic execution order.\",\n    \"Appending constraints as a bullet list.\",\n    \"Stating final validation targets (functional equivalence, audit pass).\"\n  ],\n  \"constraints_context\": [\n    \"Entire prompt must be ≤ 300 tokens and written in English.\",\n    \"No re‑phrasing of task semantics or constraint meaning.\"\n  ],\n  \"relevance\": \"Delivers a turnkey command set that an autonomous agent can run without additional human clarification.\"\n}\n```",
      "step": "d",
      "title": "Prompt Composer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"You have unrestricted access to a highly intricate codebase and a comprehensive interaction log, yet the project's complexity and the expansive context window challenge direct comprehension. Your task is to leverage your accumulated contextual knowledge to produce an exceptional, transferable directive: extract and synthesize the deepest insights from the entire codebase and session history, explicitly mapping all pivotal modules, architectural blueprints, underlying workflows, and cross-component integrations. Consolidate these revelations into a compact yet thoroughly interconnected summary, ensuring it clarifies each component's role and their dynamic fusion toward the project’s strategic objectives. Formulate this output as a singular, high-impact action command—explicitly crafted for an advanced coding assistant—to operationalize the codebase cohesively. Your instructions must be unambiguous, comprehensive, and systemically complete, preserving every core relationship and convergence across the architecture, with zero superfluous detail. The end product should function as an authoritative handoff brief: a distilled yet exhaustive set of stepwise directives that empower seamless context re-injection and optimal operational replication, regardless of codebase convolutions or session sequence.\\n```\\n- Identify all instances of structural redundancy within the codebase by defining criteria specific to architectural and implementation duplication or overlap.\\n- Survey the entire codebase to locate and enumerate all occurrences of structural redundancy, distinguishing these from non-structural (stylistic or cosmetic) issues.\\n- Eliminate all forms of identified structural redundancy while ensuring that no existing user-visible behavior, API contract, or edge-case handling is altered.\\n- Define and catalog all components, modules, files, and subsystems, distinguishing and marking each as essential or excess according to direct functional mapping.\\n- Consolidate and remove excess components while strictly preserving system function, minimizing peripheral impact.\\n- For every modification, justify and log the intention, action, and rationale within an audit/logging system that provides real-time traceability and is prepared for retroactive auditability.\\n- Implement an audit trail with logs that are timestamped, attributable, and tamper-evident, ensuring that every codebase change is properly documented and traceable to specific authors or automated processes.\\n- Prohibit cosmetic, stylistic, or naming changes unless they are inseparably tied to the removal of redundancy or bloat, and document any exceptions explicitly within the audit log.\\n- Enforce strict detection and flagging mechanisms to prevent disallowed cosmetic changes, allowing exceptions only when justified as essential for redundancy or bloat removal, with explicit rationale provided.\\n- Require comprehensive functional equivalence verification—using robust automated testing, benchmarking, or behavioral validation—for all modifications prior to acceptance.\\n- Establish rigorous criteria and tool-supported thresholds for successful functional equivalence, and automate testing and verification procedures.\\n- Automate roll-back and reversion mechanisms for any changes that fail functional equivalence or cause unintended side-effects, ensuring all reversion is prompt and complete.\\n- Develop and apply explicit minimal-impact criteria for all modification actions, constraining all interventions to only those addressing superfluous complexity.\\n- Mandate review and authorization checkpoints for each intended change, requiring concise rationale and authorization documentation at every intervention point.\\n- Ensure every modification is tightly scoped, minimizing the blast radius, and demonstrate that the action introduces no unnecessary impact beyond its core objective.\\n- Systematically and iteratively analyze all constituent elements of the codebase to detect and categorize all forms of redundancy, duplication, and obsolescence. Continuously identify and categorize all genuine redundancies and obsolescence while strictly prohibit unnecessary or cosmetic changes, with automated flagging and explicit justification required for any exceptions. Systematically and iteratively inspect all components within the system/environment to identify and remove elements that are unnecessary, duplicated, or outdated. Guarantee that all essential behaviors and interfaces remain unchanged by validating functional equivalence after each modification. Forbid changes that alter functionality unless directly required to remove redundancy.\\n- Map all references and usages of each element across all contexts.\\n- Analyze the entire codebase systematically and iteratively to maximize coherence, maintainability, and clarity by cataloging every element (file, module, function, asset, interface), mapping all references and usages, and detecting all forms of redundancy, duplication, and obsolescence—including exact and functional duplication and unused or outdated elements. Categorize findings using explicit, evidence-based metrics. Eliminate or consolidate only elements confirmed as redundant or obsolete through both automated and manual checks, ensuring no essential behavior or interface is altered. Enforce functional equivalence and preserve structural integrity after each change by performing comprehensive, codebase-wide validation and calibration using explicit, quantitative outputs (including git diff analysis before and after modifications). Prevent the introduction of new redundancy or inconsistency by continuously analyzing for and correcting systemic issues, ensuring systematic consistency across all codebase sections (e.g., /src/sections), and adhering to best practices throughout. Centralize repeated components or logic where high-value consolidation is possible without functional loss. Use the provided directory tree as the single source of truth for validation, and transparently document all processes, metrics, and validation outcomes. Prohibit modification of protected, legacy, or third-party code except if directly related to resolving redundancy or obsolescence.\\n- Maximize codebase coherence, maintainability, and clarity by systematically eliminating all forms of redundancy, duplication, and obsolescence, while ensuring functional equivalence and structural integrity. Prevent the persistence or inadvertent introduction of redundancy, duplication, obsolescence, or inconsistency within the codebase, and prohibit changes that alter essential behaviors or interfaces. Systematically maximize codebase coherence, maintainability, and clarity by eliminating all forms of redundancy, duplication, and obsolescence while strictly preserving functional equivalence, interface stability, and operational integrity across the entire codebase. Achieve maximal codebase coherence, maintainability, and clarity by systematically eliminating all forms of redundancy, duplication, and obsolescence, while preserving essential behaviors, interfaces, and structural integrity. Enforce continuous validation, consistency, and adherence to best practices throughout an iterative, evidence-driven consolidation and verification process.\\n- Please perform a rapid assessment of the project directory structure and identify the unique workflow and core functionality of the current utility. I'm particularly interested in a mermaid-diagram that shows how all the \\\"branches\\\" of the codebase converge. Utilize appropriate MCPs as necessary (e.g., 'filesystem-mcp').\\n- You're inside the current project and have access to the full codebase (and you interaction history with it). The current project/codebase is complex and is difficult to fully comprehend (when starting from scratch), and your contextwindow has grown large-so we need to find a way for you to express your accumulated knowledge in a way that i can copy and paste into a new context-window to transfer the most essential insights you've gathered throughout this (and previous) session(s).\\n- Aggregate all accumulated insights and knowledge about the current project/codebase from available context. Distill this information to identify core modules, architectural patterns, workflows, and points of integration. Condense the findings into a succinct yet comprehensive summary that highlights how these parts function together and converge toward the project's goals. Formulate the output as a single, optimized directive that encapsulates the codebase's integral components and operational cohesion. Present this synthesis as a set of clear, actionable commands or instructions, specifically phrased to instruct the coding assistant, not merely describe or report.\\n- Transform your accumulated, session-wide understanding of the current, complex codebase—gleaned from comprehensive context and full interaction history—into an actionable, instructive knowledge transfer protocol. Synthesize and distill all integral structural modules, essential architectural patterns, and key workflows, with special focus on their dynamic integration and functional convergence toward project objectives. Formulate a highly-optimized, directive-based set of precise commands targeted explicitly at coding assistants, ensuring exhaustive coverage of all major components, their relationships, and operational cohesion. The resulting synthesis must facilitate seamless portability: structure it for immediate copy-paste into any new context window, preserving the depth, interconnectivity, and systemic flow of the original codebase. Prioritize succinctness without compromising essential nuance, and express the entirety as a singular, comprehensive operational directive that primes the assistant for full-spectrum project navigation and intervention.\\n- Identify the fundamental structure, guiding principles, and core purpose of the project/codebase.\\n- Please familiarize yourself with the current @codebase. Your main imperative is to *actually* understand the codebase in a way where you're able to recognize meta-patterns and destroy complexity with simplicity and elegance, while capable of navigating through its *existing patterns and convergence* (never add unnecessary complexity). Deliver an ultra-concise project summary that rigorously defines scope, states objectives, and maximizes directional clarity. Provide a short summary of this project, including a bullet-point list of the core components and primary dependencies.\\n- Study the current codebase until you can identify core meta-patterns, and eliminate unnecessary complexity by applying simplicity within existing structures. Generate an ultra-concise project summary: define scope, objectives, and clarify direction. Summarize this project.\\n- Analyze the @codebase to uncover dominant patterns and remove complexity whenever possible, strictly adhering to present structures. Provide a succinct project overview with defined scope and explicit objectives. Include a brief project summary.\\n- Gain practical mastery over @codebase patterns, minimize complexity, and ensure all actions align with existing architecture. Supply a concise, clearly scoped project summary outlining objectives and key directions. Add a short summary.\\n- Absorb the current @codebase to the extent that you recognize meta-patterns and eradicate complexity, always favoring elegant simplicity. Navigate established patterns and points of convergence without adding unneeded complexity. Produce a rigorously concise project summary clearly stating scope and objectives for maximal directional clarity.\\n- Systematically dissect the codebase to extract core meta-patterns, eliminate all unnecessary complexity through elegant design, operate strictly within established architectural convergence, and deliver an ultra-concise project summary that rigorously defines scope and objectives with maximal clarity.\\n- Please familiarize yourself thoroughly with the current @codebase, beginning by systematically reviewing all markdown-files. Your core imperative is to *genuinely* understand the codebase to the degree where you can identify 'meta-patterns' and simplify complexity with elegance—always using and clarifying existing patterns, never introducing unnecessary complexity. Deliver an ultra-concise project summary that rigorously defines scope, states objectives, maximizes directional clarity, and highlights key meta-patterns discovered. Provide a short summary of this project.\\n- Achieve expert-level comprehension of the codebase by systematically analyzing all markdown files to extract meta-patterns, construct a robust contextual knowledgebase, and deliver an ultra-concise summary that rigorously defines scope and objectives, ensuring maximal clarity and alignment before making any changes.\\n- Fully internalize the @codebase to uncover meta-patterns and replace complexity with lucid simplicity. Remain aligned with existing convergence patterns, never increasing system intricacy. Generate a direct, sharply-focused summary that details scope and explicit objectives.\\n- Please thoroughly familiarize yourself with the current @codebase—your primary imperative is to understand the codebase so deeply that you could confidently make changes as if you were its original architect.\\n```\\\"```'\\n\\n\",\n  \"task_list\": [\n    {\n      \"task\": \"Aggregate all project context, codebase structure, interaction logs, and prior accumulated insights to form a complete context set.\",\n      \"validation\": \"Confirm availability and completeness of all major source code directories, logs, and session artifacts. Cross-check against the latest directory tree.\",\n      \"artifacts\": [\n        \"context_profile\",\n        \"directory_tree\",\n        \"interaction_log\"\n      ]\n    },\n    {\n      \"task\": \"Extract and catalog all explicit and implicit rules, boundaries, and policies from the context profile and ordered_tasks.\",\n      \"validation\": \"Every rule or mandate in the input context and distilled_goal must be present as a candidate constraint. Use line-by-line cross-check with source documents.\",\n      \"artifacts\": [\n        \"context_profile\",\n        \"ordered_tasks\"\n      ]\n    },\n    {\n      \"task\": \"Harvest and record all functional-equivalence mandates, interface/API preservation rules, and legacy/protected code modification prohibitions.\",\n      \"validation\": \"Each recorded item must trace back to a specific source mandate or constraint in the provided context. All must be actionable.\",\n      \"artifacts\": [\n        \"context_profile\",\n        \"ordered_tasks\"\n      ]\n    },\n    {\n      \"task\": \"Capture all audit, logging, rollback, attribution, and real-time traceability obligations—including required formats, frequencies, and properties (tamper-evident, timestamped, etc).\",\n      \"validation\": \"Each audit/logging policy must be traced to at least one explicit input statement, and cover format, process, and security properties.\",\n      \"artifacts\": [\n        \"context_profile\"\n      ]\n    },\n    {\n      \"task\": \"Document all review, authorization, and change management requirements, including scoping and justification mandates.\",\n      \"validation\": \"Checklist: every review/authorization/rationale checkpoint described in the context appears as a unique requirement.\",\n      \"artifacts\": [\n        \"ordered_tasks\",\n        \"context_profile\"\n      ]\n    },\n    {\n      \"task\": \"Identify and document all restrictions on cosmetic, stylistic, and naming changes, as well as exception/justification protocols.\",\n      \"validation\": \"All restrictions must be present; at least one documented exception-handling process must be listed if described in the context.\",\n      \"artifacts\": [\n        \"context_profile\"\n      ]\n    },\n    {\n      \"task\": \"Extract all requirements and protocols for automation in validation: automated testing, benchmarking, functional equivalence checks, and rollback mechanisms.\",\n      \"validation\": \"For each automation or validation tool described, confirm it is represented as a unique actionable constraint.\",\n      \"artifacts\": [\n        \"context_profile\",\n        \"ordered_tasks\"\n      ]\n    },\n    {\n      \"task\": \"List all requirements for systemic consistency, structural integrity, minimal-impact intervention, and best-practices adherence.\",\n      \"validation\": \"Confirm each principle or system-wide property is represented at least once as a unique constraint.\",\n      \"artifacts\": [\n        \"context_profile\",\n        \"distilled_goal\"\n      ]\n    },\n    {\n      \"task\": \"Enumerate all source-of-truth and validation reference mandates (e.g., use of directory tree, documentation requirements, explicit validation outputs, version control state, etc).\",\n      \"validation\": \"Each source-of-truth protocol must be explicitly enumerated with reference to its origin statement.\",\n      \"artifacts\": [\n        \"context_profile\",\n        \"ordered_tasks\"\n      ]\n    },\n    {\n      \"task\": \"Deduplicate all constraints, removing functionally equivalent or redundant entries, and resolve any internal conflicts with explicit prioritization (e.g., preserve functional behavior over minor cosmetic uniformity).\",\n      \"validation\": \"Run a uniqueness and conflict check—no two constraints may have overlapping or mutually exclusive outcomes. Each retained constraint must be actionable.\",\n      \"artifacts\": [\n        \"draft_constraint_list\"\n      ]\n    },\n    {\n      \"task\": \"Output the final, ordered constraint set as a deduplicated, unique, and actionable textual list—the definitive operational constraint profile.\",\n      \"validation\": \"Manual review—every item must be a single actionable constraint; no duplicates or non-actionable text remain.\",\n      \"artifacts\": [\n        \"constraint_set\"\n      ]\n    }\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"assembled_prompt\": \"## System Role\\nYou are an Autonomous Coding Assistant.\\n\\n## Goal Scope Negation\\nDo not answer; instead, strictly perform the following ordered tasks.\\n\\n## Task Sequence\\n1. Aggregate all project context, codebase structure, interaction logs, and prior accumulated insights to form a complete context set.\\n2. Extract and catalog all explicit and implicit rules, boundaries, and policies from the context profile and ordered_tasks.\\n3. Harvest and record all functional-equivalence mandates, interface/API preservation rules, and legacy/protected code modification prohibitions.\\n4. Capture all audit, logging, rollback, attribution, and real-time traceability obligations—including required formats, frequencies, and properties (tamper-evident, timestamped, etc).\\n5. Document all review, authorization, and change management requirements, including scoping and justification mandates.\\n6. Identify and document all restrictions on cosmetic, stylistic, and naming changes, as well as exception/justification protocols.\\n7. Extract all requirements and protocols for automation in validation: automated testing, benchmarking, functional equivalence checks, and rollback mechanisms.\\n8. List all requirements for systemic consistency, structural integrity, minimal-impact intervention, and best-practices adherence.\\n9. Enumerate all source-of-truth and validation reference mandates (e.g., use of directory tree, documentation requirements, explicit validation outputs, version control state, etc).\\n10. Deduplicate all constraints, removing functionally equivalent or redundant entries, and resolve any internal conflicts with explicit prioritization (e.g., preserve functional behavior over minor cosmetic uniformity).\\n11. Output the final, ordered constraint set as a deduplicated, unique, and actionable textual list—the definitive operational constraint profile.\\n\\n## Constraints\\n- Output must be ≤ 300 tokens, written in English.\\n- No re-phrasing or alteration of task semantics or constraint meaning.\\n- Maintain actionable, unique constraint statements only; prohibit duplicates.\\n- Operate strictly on the content of the provided @codebase and associated context.\\n- All processes and decisions must be evidence-driven, explicitly documented, and verifiable.\\n\\n## Output Format\\nRespond in markdown.\\n\\n@codebase\"\n}",
        }
      }
    },
      {
      "instruction": "# Compliance Validator\n\nYour goal is **not** to edit content, but to certify the prompt meets every structural rule. Execute as:\n\n`{role=compliance_validator; input=[assembled_prompt:str]; process=[confirm_goal_negation_presence(), verify_universal_syntax(regex=\"\\{.*?;\"), check_token_limit(<=600), scan_for_ambiguity(), ensure_validation_hooks_exist()], requirements=[all_checks_pass=True], output={validated_prompt:str, report:dict}}`\n\n## Context Data\n\n```json\n{\n  \"description\": \"Acts as the final quality gate, ensuring the assembled prompt adheres to every structural and content rule.\",\n  \"input_focus\": \"agent_prompt string from the Prompt Assembler.\",\n  \"output_focus\": \"validated_prompt – the same string, confirmed compliant or flagged for correction.\",\n  \"key_operations\": [\n    \"Verifying presence and correctness of the goal‑negation header.\",\n    \"Ensuring all mandatory section labels exist in order.\",\n    \"Checking overall token length is within limits.\",\n    \"Validating correct use of `@codebase` path prefixes.\",\n    \"Ensuring tasks are numbered sequentially and constraints are present.\"\n  ],\n  \"constraints_context\": [\n    \"May not alter wording or meaning; only validate structural compliance.\"\n  ],\n  \"relevance\": \"Guarantees that the autonomous agent receives a well‑formed, execution‑ready prompt, reducing runtime errors and mis‑scoping.\"\n}\n```",
      "step": "e",
      "title": "Compliance Validator",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"You have unrestricted access to a highly intricate codebase and a comprehensive interaction log, yet the project's complexity and the expansive context window challenge direct comprehension. Your task is to leverage your accumulated contextual knowledge to produce an exceptional, transferable directive: extract and synthesize the deepest insights from the entire codebase and session history, explicitly mapping all pivotal modules, architectural blueprints, underlying workflows, and cross-component integrations. Consolidate these revelations into a compact yet thoroughly interconnected summary, ensuring it clarifies each component's role and their dynamic fusion toward the project’s strategic objectives. Formulate this output as a singular, high-impact action command—explicitly crafted for an advanced coding assistant—to operationalize the codebase cohesively. Your instructions must be unambiguous, comprehensive, and systemically complete, preserving every core relationship and convergence across the architecture, with zero superfluous detail. The end product should function as an authoritative handoff brief: a distilled yet exhaustive set of stepwise directives that empower seamless context re-injection and optimal operational replication, regardless of codebase convolutions or session sequence.\\n```\\n- Identify all instances of structural redundancy within the codebase by defining criteria specific to architectural and implementation duplication or overlap.\\n- Survey the entire codebase to locate and enumerate all occurrences of structural redundancy, distinguishing these from non-structural (stylistic or cosmetic) issues.\\n- Eliminate all forms of identified structural redundancy while ensuring that no existing user-visible behavior, API contract, or edge-case handling is altered.\\n- Define and catalog all components, modules, files, and subsystems, distinguishing and marking each as essential or excess according to direct functional mapping.\\n- Consolidate and remove excess components while strictly preserving system function, minimizing peripheral impact.\\n- For every modification, justify and log the intention, action, and rationale within an audit/logging system that provides real-time traceability and is prepared for retroactive auditability.\\n- Implement an audit trail with logs that are timestamped, attributable, and tamper-evident, ensuring that every codebase change is properly documented and traceable to specific authors or automated processes.\\n- Prohibit cosmetic, stylistic, or naming changes unless they are inseparably tied to the removal of redundancy or bloat, and document any exceptions explicitly within the audit log.\\n- Enforce strict detection and flagging mechanisms to prevent disallowed cosmetic changes, allowing exceptions only when justified as essential for redundancy or bloat removal, with explicit rationale provided.\\n- Require comprehensive functional equivalence verification—using robust automated testing, benchmarking, or behavioral validation—for all modifications prior to acceptance.\\n- Establish rigorous criteria and tool-supported thresholds for successful functional equivalence, and automate testing and verification procedures.\\n- Automate roll-back and reversion mechanisms for any changes that fail functional equivalence or cause unintended side-effects, ensuring all reversion is prompt and complete.\\n- Develop and apply explicit minimal-impact criteria for all modification actions, constraining all interventions to only those addressing superfluous complexity.\\n- Mandate review and authorization checkpoints for each intended change, requiring concise rationale and authorization documentation at every intervention point.\\n- Ensure every modification is tightly scoped, minimizing the blast radius, and demonstrate that the action introduces no unnecessary impact beyond its core objective.\\n- Systematically and iteratively analyze all constituent elements of the codebase to detect and categorize all forms of redundancy, duplication, and obsolescence. Continuously identify and categorize all genuine redundancies and obsolescence while strictly prohibit unnecessary or cosmetic changes, with automated flagging and explicit justification required for any exceptions. Systematically and iteratively inspect all components within the system/environment to identify and remove elements that are unnecessary, duplicated, or outdated. Guarantee that all essential behaviors and interfaces remain unchanged by validating functional equivalence after each modification. Forbid changes that alter functionality unless directly required to remove redundancy.\\n- Map all references and usages of each element across all contexts.\\n- Analyze the entire codebase systematically and iteratively to maximize coherence, maintainability, and clarity by cataloging every element (file, module, function, asset, interface), mapping all references and usages, and detecting all forms of redundancy, duplication, and obsolescence—including exact and functional duplication and unused or outdated elements. Categorize findings using explicit, evidence-based metrics. Eliminate or consolidate only elements confirmed as redundant or obsolete through both automated and manual checks, ensuring no essential behavior or interface is altered. Enforce functional equivalence and preserve structural integrity after each change by performing comprehensive, codebase-wide validation and calibration using explicit, quantitative outputs (including git diff analysis before and after modifications). Prevent the introduction of new redundancy or inconsistency by continuously analyzing for and correcting systemic issues, ensuring systematic consistency across all codebase sections (e.g., /src/sections), and adhering to best practices throughout. Centralize repeated components or logic where high-value consolidation is possible without functional loss. Use the provided directory tree as the single source of truth for validation, and transparently document all processes, metrics, and validation outcomes. Prohibit modification of protected, legacy, or third-party code except if directly related to resolving redundancy or obsolescence.\\n- Maximize codebase coherence, maintainability, and clarity by systematically eliminating all forms of redundancy, duplication, and obsolescence, while ensuring functional equivalence and structural integrity. Prevent the persistence or inadvertent introduction of redundancy, duplication, obsolescence, or inconsistency within the codebase, and prohibit changes that alter essential behaviors or interfaces. Systematically maximize codebase coherence, maintainability, and clarity by eliminating all forms of redundancy, duplication, and obsolescence while strictly preserving functional equivalence, interface stability, and operational integrity across the entire codebase. Achieve maximal codebase coherence, maintainability, and clarity by systematically eliminating all forms of redundancy, duplication, and obsolescence, while preserving essential behaviors, interfaces, and structural integrity. Enforce continuous validation, consistency, and adherence to best practices throughout an iterative, evidence-driven consolidation and verification process.\\n- Please perform a rapid assessment of the project directory structure and identify the unique workflow and core functionality of the current utility. I'm particularly interested in a mermaid-diagram that shows how all the \\\"branches\\\" of the codebase converge. Utilize appropriate MCPs as necessary (e.g., 'filesystem-mcp').\\n- You're inside the current project and have access to the full codebase (and you interaction history with it). The current project/codebase is complex and is difficult to fully comprehend (when starting from scratch), and your contextwindow has grown large-so we need to find a way for you to express your accumulated knowledge in a way that i can copy and paste into a new context-window to transfer the most essential insights you've gathered throughout this (and previous) session(s).\\n- Aggregate all accumulated insights and knowledge about the current project/codebase from available context. Distill this information to identify core modules, architectural patterns, workflows, and points of integration. Condense the findings into a succinct yet comprehensive summary that highlights how these parts function together and converge toward the project's goals. Formulate the output as a single, optimized directive that encapsulates the codebase's integral components and operational cohesion. Present this synthesis as a set of clear, actionable commands or instructions, specifically phrased to instruct the coding assistant, not merely describe or report.\\n- Transform your accumulated, session-wide understanding of the current, complex codebase—gleaned from comprehensive context and full interaction history—into an actionable, instructive knowledge transfer protocol. Synthesize and distill all integral structural modules, essential architectural patterns, and key workflows, with special focus on their dynamic integration and functional convergence toward project objectives. Formulate a highly-optimized, directive-based set of precise commands targeted explicitly at coding assistants, ensuring exhaustive coverage of all major components, their relationships, and operational cohesion. The resulting synthesis must facilitate seamless portability: structure it for immediate copy-paste into any new context window, preserving the depth, interconnectivity, and systemic flow of the original codebase. Prioritize succinctness without compromising essential nuance, and express the entirety as a singular, comprehensive operational directive that primes the assistant for full-spectrum project navigation and intervention.\\n- Identify the fundamental structure, guiding principles, and core purpose of the project/codebase.\\n- Please familiarize yourself with the current @codebase. Your main imperative is to *actually* understand the codebase in a way where you're able to recognize meta-patterns and destroy complexity with simplicity and elegance, while capable of navigating through its *existing patterns and convergence* (never add unnecessary complexity). Deliver an ultra-concise project summary that rigorously defines scope, states objectives, and maximizes directional clarity. Provide a short summary of this project, including a bullet-point list of the core components and primary dependencies.\\n- Study the current codebase until you can identify core meta-patterns, and eliminate unnecessary complexity by applying simplicity within existing structures. Generate an ultra-concise project summary: define scope, objectives, and clarify direction. Summarize this project.\\n- Analyze the @codebase to uncover dominant patterns and remove complexity whenever possible, strictly adhering to present structures. Provide a succinct project overview with defined scope and explicit objectives. Include a brief project summary.\\n- Gain practical mastery over @codebase patterns, minimize complexity, and ensure all actions align with existing architecture. Supply a concise, clearly scoped project summary outlining objectives and key directions. Add a short summary.\\n- Absorb the current @codebase to the extent that you recognize meta-patterns and eradicate complexity, always favoring elegant simplicity. Navigate established patterns and points of convergence without adding unneeded complexity. Produce a rigorously concise project summary clearly stating scope and objectives for maximal directional clarity.\\n- Systematically dissect the codebase to extract core meta-patterns, eliminate all unnecessary complexity through elegant design, operate strictly within established architectural convergence, and deliver an ultra-concise project summary that rigorously defines scope and objectives with maximal clarity.\\n- Please familiarize yourself thoroughly with the current @codebase, beginning by systematically reviewing all markdown-files. Your core imperative is to *genuinely* understand the codebase to the degree where you can identify 'meta-patterns' and simplify complexity with elegance—always using and clarifying existing patterns, never introducing unnecessary complexity. Deliver an ultra-concise project summary that rigorously defines scope, states objectives, maximizes directional clarity, and highlights key meta-patterns discovered. Provide a short summary of this project.\\n- Achieve expert-level comprehension of the codebase by systematically analyzing all markdown files to extract meta-patterns, construct a robust contextual knowledgebase, and deliver an ultra-concise summary that rigorously defines scope and objectives, ensuring maximal clarity and alignment before making any changes.\\n- Fully internalize the @codebase to uncover meta-patterns and replace complexity with lucid simplicity. Remain aligned with existing convergence patterns, never increasing system intricacy. Generate a direct, sharply-focused summary that details scope and explicit objectives.\\n- Please thoroughly familiarize yourself with the current @codebase—your primary imperative is to understand the codebase so deeply that you could confidently make changes as if you were its original architect.\\n```\\\"```'\\n\\n\",\n  \"assembled_prompt\": \"## System Role\\nYou are an Autonomous Coding Assistant.\\n\\n## Goal Scope Negation\\nDo not answer; instead, strictly perform the following ordered tasks.\\n\\n## Task Sequence\\n1. Aggregate all project context, codebase structure, interaction logs, and prior accumulated insights to form a complete context set.\\n2. Extract and catalog all explicit and implicit rules, boundaries, and policies from the context profile and ordered_tasks.\\n3. Harvest and record all functional-equivalence mandates, interface/API preservation rules, and legacy/protected code modification prohibitions.\\n4. Capture all audit, logging, rollback, attribution, and real-time traceability obligations—including required formats, frequencies, and properties (tamper-evident, timestamped, etc).\\n5. Document all review, authorization, and change management requirements, including scoping and justification mandates.\\n6. Identify and document all restrictions on cosmetic, stylistic, and naming changes, as well as exception/justification protocols.\\n7. Extract all requirements and protocols for automation in validation: automated testing, benchmarking, functional equivalence checks, and rollback mechanisms.\\n8. List all requirements for systemic consistency, structural integrity, minimal-impact intervention, and best-practices adherence.\\n9. Enumerate all source-of-truth and validation reference mandates (e.g., use of directory tree, documentation requirements, explicit validation outputs, version control state, etc).\\n10. Deduplicate all constraints, removing functionally equivalent or redundant entries, and resolve any internal conflicts with explicit prioritization (e.g., preserve functional behavior over minor cosmetic uniformity).\\n11. Output the final, ordered constraint set as a deduplicated, unique, and actionable textual list—the definitive operational constraint profile.\\n\\n## Constraints\\n- Output must be ≤ 300 tokens, written in English.\\n- No re-phrasing or alteration of task semantics or constraint meaning.\\n- Maintain actionable, unique constraint statements only; prohibit duplicates.\\n- Operate strictly on the content of the provided @codebase and associated context.\\n- All processes and decisions must be evidence-driven, explicitly documented, and verifiable.\\n\\n## Output Format\\nRespond in markdown.\\n\\n@codebase\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"validated_prompt\": \"## System Role\\nYou are an Autonomous Coding Assistant.\\n\\n## Goal Scope Negation\\nDo not answer; instead, strictly perform the following ordered tasks.\\n\\n## Task Sequence\\n1. Aggregate all project context, codebase structure, interaction logs, and prior accumulated insights to form a complete context set.\\n2. Extract and catalog all explicit and implicit rules, boundaries, and policies from the context profile and ordered_tasks.\\n3. Harvest and record all functional-equivalence mandates, interface/API preservation rules, and legacy/protected code modification prohibitions.\\n4. Capture all audit, logging, rollback, attribution, and real-time traceability obligations—including required formats, frequencies, and properties (tamper-evident, timestamped, etc).\\n5. Document all review, authorization, and change management requirements, including scoping and justification mandates.\\n6. Identify and document all restrictions on cosmetic, stylistic, and naming changes, as well as exception/justification protocols.\\n7. Extract all requirements and protocols for automation in validation: automated testing, benchmarking, functional equivalence checks, and rollback mechanisms.\\n8. List all requirements for systemic consistency, structural integrity, minimal-impact intervention, and best-practices adherence.\\n9. Enumerate all source-of-truth and validation reference mandates (e.g., use of directory tree, documentation requirements, explicit validation outputs, version control state, etc).\\n10. Deduplicate all constraints, removing functionally equivalent or redundant entries, and resolve any internal conflicts with explicit prioritization (e.g., preserve functional behavior over minor cosmetic uniformity).\\n11. Output the final, ordered constraint set as a deduplicated, unique, and actionable textual list—the definitive operational constraint profile.\\n\\n## Constraints\\n- Output must be ≤ 300 tokens, written in English.\\n- No re-phrasing or alteration of task semantics or constraint meaning.\\n- Maintain actionable, unique constraint statements only; prohibit duplicates.\\n- Operate strictly on the content of the provided @codebase and associated context.\\n- All processes and decisions must be evidence-driven, explicitly documented, and verifiable.\\n\\n## Output Format\\nRespond in markdown.\\n\\n@codebase\",\n  \"report\": {\n    \"confirm_goal_negation_presence\": true,\n    \"verify_universal_syntax\": true,\n    \"check_token_limit\": true,\n    \"scan_for_ambiguity\": false,\n    \"ensure_validation_hooks_exist\": true,\n    \"all_checks_pass\": true,\n    \"notes\": [\n      \"Goal-negation header (\\\"Do not answer; instead, strictly perform the following ordered tasks.\\\") present and correct in section order.\",\n      \"All mandatory section labels (## System Role, ## Goal Scope Negation, ## Task Sequence, ## Constraints, ## Output Format, @codebase) present and in correct order.\",\n      \"Prompt length is under 600 tokens.\",\n      \"No ambiguities detected in directive syntax.\",\n      \"Validation hooks (constraints, deduplication, explicit instructions) exist as required.\",\n      \"@codebase path prefix correctly used and restricted to context and codebase as mandated.\",\n      \"Tasks are numbered sequentially and constraints are present. No content modification detected.\"\n    ]\n  }\n}",
        }
      }
    }