  {
  "initial_prompt": "# lets try something new\nyour new goal is to create a new generalized instruction sequence specifically designed to transform inputs into autonomous-agent-ready prompts/instructions, for use with autonomous coding assistants such as cursor, vscode+cline/augment/etc. these prompts are typically written in a \"codebase-oriented\" manner, where the `@codebase` represents the root (of a project/workspace/directory), examples:\n```\n- Identify all instances of structural redundancy within the codebase by defining criteria specific to architectural and implementation duplication or overlap.\n- Survey the entire codebase to locate and enumerate all occurrences of structural redundancy, distinguishing these from non-structural (stylistic or cosmetic) issues.\n- Eliminate all forms of identified structural redundancy while ensuring that no existing user-visible behavior, API contract, or edge-case handling is altered.\n- Define and catalog all components, modules, files, and subsystems, distinguishing and marking each as essential or excess according to direct functional mapping.\n- Consolidate and remove excess components while strictly preserving system function, minimizing peripheral impact.\n- For every modification, justify and log the intention, action, and rationale within an audit/logging system that provides real-time traceability and is prepared for retroactive auditability.\n- Implement an audit trail with logs that are timestamped, attributable, and tamper-evident, ensuring that every codebase change is properly documented and traceable to specific authors or automated processes.\n- Prohibit cosmetic, stylistic, or naming changes unless they are inseparably tied to the removal of redundancy or bloat, and document any exceptions explicitly within the audit log.\n- Enforce strict detection and flagging mechanisms to prevent disallowed cosmetic changes, allowing exceptions only when justified as essential for redundancy or bloat removal, with explicit rationale provided.\n- Require comprehensive functional equivalence verification—using robust automated testing, benchmarking, or behavioral validation—for all modifications prior to acceptance.\n- Establish rigorous criteria and tool-supported thresholds for successful functional equivalence, and automate testing and verification procedures.\n- Automate roll-back and reversion mechanisms for any changes that fail functional equivalence or cause unintended side-effects, ensuring all reversion is prompt and complete.\n- Develop and apply explicit minimal-impact criteria for all modification actions, constraining all interventions to only those addressing superfluous complexity.\n- Mandate review and authorization checkpoints for each intended change, requiring concise rationale and authorization documentation at every intervention point.\n- Ensure every modification is tightly scoped, minimizing the blast radius, and demonstrate that the action introduces no unnecessary impact beyond its core objective.\n- Systematically and iteratively analyze all constituent elements of the codebase to detect and categorize all forms of redundancy, duplication, and obsolescence. Continuously identify and categorize all genuine redundancies and obsolescence while strictly prohibit unnecessary or cosmetic changes, with automated flagging and explicit justification required for any exceptions. Systematically and iteratively inspect all components within the system/environment to identify and remove elements that are unnecessary, duplicated, or outdated. Guarantee that all essential behaviors and interfaces remain unchanged by validating functional equivalence after each modification. Forbid changes that alter functionality unless directly required to remove redundancy.\n- Map all references and usages of each element across all contexts.\n- Analyze the entire codebase systematically and iteratively to maximize coherence, maintainability, and clarity by cataloging every element (file, module, function, asset, interface), mapping all references and usages, and detecting all forms of redundancy, duplication, and obsolescence—including exact and functional duplication and unused or outdated elements. Categorize findings using explicit, evidence-based metrics. Eliminate or consolidate only elements confirmed as redundant or obsolete through both automated and manual checks, ensuring no essential behavior or interface is altered. Enforce functional equivalence and preserve structural integrity after each change by performing comprehensive, codebase-wide validation and calibration using explicit, quantitative outputs (including git diff analysis before and after modifications). Prevent the introduction of new redundancy or inconsistency by continuously analyzing for and correcting systemic issues, ensuring systematic consistency across all codebase sections (e.g., /src/sections), and adhering to best practices throughout. Centralize repeated components or logic where high-value consolidation is possible without functional loss. Use the provided directory tree as the single source of truth for validation, and transparently document all processes, metrics, and validation outcomes. Prohibit modification of protected, legacy, or third-party code except if directly related to resolving redundancy or obsolescence.\n- Maximize codebase coherence, maintainability, and clarity by systematically eliminating all forms of redundancy, duplication, and obsolescence, while ensuring functional equivalence and structural integrity. Prevent the persistence or inadvertent introduction of redundancy, duplication, obsolescence, or inconsistency within the codebase, and prohibit changes that alter essential behaviors or interfaces. Systematically maximize codebase coherence, maintainability, and clarity by eliminating all forms of redundancy, duplication, and obsolescence while strictly preserving functional equivalence, interface stability, and operational integrity across the entire codebase. Achieve maximal codebase coherence, maintainability, and clarity by systematically eliminating all forms of redundancy, duplication, and obsolescence, while preserving essential behaviors, interfaces, and structural integrity. Enforce continuous validation, consistency, and adherence to best practices throughout an iterative, evidence-driven consolidation and verification process.\n- Please perform a rapid assessment of the project directory structure and identify the unique workflow and core functionality of the current utility. I'm particularly interested in a mermaid-diagram that shows how all the \"branches\" of the codebase converge. Utilize appropriate MCPs as necessary (e.g., 'filesystem-mcp').\n- You're inside the current project and have access to the full codebase (and you interaction history with it). The current project/codebase is complex and is difficult to fully comprehend (when starting from scratch), and your contextwindow has grown large-so we need to find a way for you to express your accumulated knowledge in a way that i can copy and paste into a new context-window to transfer the most essential insights you've gathered throughout this (and previous) session(s).\n- Aggregate all accumulated insights and knowledge about the current project/codebase from available context. Distill this information to identify core modules, architectural patterns, workflows, and points of integration. Condense the findings into a succinct yet comprehensive summary that highlights how these parts function together and converge toward the project's goals. Formulate the output as a single, optimized directive that encapsulates the codebase's integral components and operational cohesion. Present this synthesis as a set of clear, actionable commands or instructions, specifically phrased to instruct the coding assistant, not merely describe or report.\n- Transform your accumulated, session-wide understanding of the current, complex codebase—gleaned from comprehensive context and full interaction history—into an actionable, instructive knowledge transfer protocol. Synthesize and distill all integral structural modules, essential architectural patterns, and key workflows, with special focus on their dynamic integration and functional convergence toward project objectives. Formulate a highly-optimized, directive-based set of precise commands targeted explicitly at coding assistants, ensuring exhaustive coverage of all major components, their relationships, and operational cohesion. The resulting synthesis must facilitate seamless portability: structure it for immediate copy-paste into any new context window, preserving the depth, interconnectivity, and systemic flow of the original codebase. Prioritize succinctness without compromising essential nuance, and express the entirety as a singular, comprehensive operational directive that primes the assistant for full-spectrum project navigation and intervention.\n- Identify the fundamental structure, guiding principles, and core purpose of the project/codebase.\n- Please familiarize yourself with the current @codebase. Your main imperative is to *actually* understand the codebase in a way where you're able to recognize meta-patterns and destroy complexity with simplicity and elegance, while capable of navigating through its *existing patterns and convergence* (never add unnecessary complexity). Deliver an ultra-concise project summary that rigorously defines scope, states objectives, and maximizes directional clarity. Provide a short summary of this project, including a bullet-point list of the core components and primary dependencies.\n- Study the current codebase until you can identify core meta-patterns, and eliminate unnecessary complexity by applying simplicity within existing structures. Generate an ultra-concise project summary: define scope, objectives, and clarify direction. Summarize this project.\n- Analyze the @codebase to uncover dominant patterns and remove complexity whenever possible, strictly adhering to present structures. Provide a succinct project overview with defined scope and explicit objectives. Include a brief project summary.\n- Gain practical mastery over @codebase patterns, minimize complexity, and ensure all actions align with existing architecture. Supply a concise, clearly scoped project summary outlining objectives and key directions. Add a short summary.\n- Absorb the current @codebase to the extent that you recognize meta-patterns and eradicate complexity, always favoring elegant simplicity. Navigate established patterns and points of convergence without adding unneeded complexity. Produce a rigorously concise project summary clearly stating scope and objectives for maximal directional clarity.\n- Systematically dissect the codebase to extract core meta-patterns, eliminate all unnecessary complexity through elegant design, operate strictly within established architectural convergence, and deliver an ultra-concise project summary that rigorously defines scope and objectives with maximal clarity.\n- Please familiarize yourself thoroughly with the current @codebase, beginning by systematically reviewing all markdown-files. Your core imperative is to *genuinely* understand the codebase to the degree where you can identify 'meta-patterns' and simplify complexity with elegance—always using and clarifying existing patterns, never introducing unnecessary complexity. Deliver an ultra-concise project summary that rigorously defines scope, states objectives, maximizes directional clarity, and highlights key meta-patterns discovered. Provide a short summary of this project.\n- Achieve expert-level comprehension of the codebase by systematically analyzing all markdown files to extract meta-patterns, construct a robust contextual knowledgebase, and deliver an ultra-concise summary that rigorously defines scope and objectives, ensuring maximal clarity and alignment before making any changes.\n- Fully internalize the @codebase to uncover meta-patterns and replace complexity with lucid simplicity. Remain aligned with existing convergence patterns, never increasing system intricacy. Generate a direct, sharply-focused summary that details scope and explicit objectives.\n- Please thoroughly familiarize yourself with the current @codebase—your primary imperative is to understand the codebase so deeply that you could confidently make changes as if you were its original architect.\n```\n\nhere's an example to show how generalized sequential prompts can be used effectively (on autonomous agents):\n```\n# Step 1:\nFamiliarize yourself with the current @codebase. Comprehend the codebase in depth. Identify the fundamental structure, guiding principles, and core purpose.\n# Step 2:\nDive deeper to comprehend the intricate logic and inner workings that drive the project's functionality. As you explore, pay close attention to identifying the codebase's fundamental structure, underlying guiding principles, and its core purpose, ensuring you grasp both how and why the system is designed as it is. hen verify through a successful build.\n# Step 3:\nYou've now performed a successful build and familiarized yourself with the @codebase. Your next imperative is view the codebase through the lense of a brilliant (expert) SEO engineer, then create a short list of the most critical changes we need to make before bulding prod and deploying the website.\n# Step 4:\nYou've now performed a successful build and provided your SEO analysis of the codebase. Your next imperative is to leverage your knowledge of the codebase's fundamental structure and underlying guiding principles to ensure you're prepared for implementing the proposed SEO enhancements in a systematic order while retaining existing functionality and ensuring maximal clarity and alignment before making any changes.\n# Step 5:\nPlease systematically incorporate your proposed enhancements while ensuring existing functionality is retained, clarity is maximized, and alignment/coherence is maintained.\n# Step 6:\nPlease conduct a thorough final review to confirm seamless integration of all enhancements, ensuring that it is **truly** prepared for deployment.\n```\n\n## Goal\n\nThe new sequence i'm tasking you to create needs to adhere to the established principles of the system, and should be able to take content-specific inputs and \"shape them\" into generalized prompts specifically (and meticulously) designed for use with autonomous agents. here's an example input:\n```\nllm_instructions_for_sublimetext_plugin='''You’re an expert Sublime Text plugin developer with a deep understanding of Python and the Sublime Text API. You specialize in creating intuitive and efficient plugins that enhance user productivity and streamline workflows. You possess deep knowledge of the Sublime Text API and its ecosystem (and best practices), along with a strong understanding of user needs and developer workflows.\\n\\n- Sublime Layout\\n    > Lagre Layouts\\n        > Lagre Regelsett knyttet til Layout (hvilke tabs som skal hvor, hvilke som skal lukkes, osv)\\n            > F.eks. `alltid flytt [\"main.py\", \"*.py.bak\"] til layout.4.index.2`\\n\\n    > Kan videreutvikles til en standard som automatisk opprettholdes ved alle (.sublime-project) prosjekter'''\n```\n\nGeneralisert pipeline som omformer hvilken som helst spesifikasjon til **autonomous‑agent‑klare kodebase‑instruksjoner**\n\n*(én fil per trinn; følg navngiving 8005‑a/b/c/d/e‑\\<beskrivende‑navn>.md)*\n\n---\n\n```markdown\n[Context Extractor] Ditt mål er **ikke** å løse brukerens forespørsel, men å isolere all praktisk kontekst (domene, verktøy, filstier, ønsket effekt). `{role=context_extractor; input=[spec:any]; process=[identifiser_domene_og_miljø(), trekk_ut_arbeidsrot(@codebase?), hent_eksplisitte_objektiver(), detekter_implisitte_brukskrav(), fjern_jargon_og_metaforer()]; constraints=[ingen_generering_av_løsninger]; output={context_profile:dict(domene:str?, root:str?, mål:list, krav:list)}}`\n```\n\n---\n\n```markdown\n[Task Vectorizer] Ditt mål er **ikke** å beskrive konteksten på nytt, men å oversette context_profile til en sekvens av atomiske, kodebase‑orienterte oppgaver. `{role=task_vectorizer; input=[context_profile:dict]; process=[del_inn_objektiver_i_diskrete_handlinger(), prefiksér_hver_handling_med_@codebase_sti_når_aktuelt(), bruk_presise_verber(identifiser|eliminer|konsolider), sørg_for_ordre_etter_avhengighet()], constraints=[ingen_overlappende_oppgaver(), unngå_kosmetiske_endringer_med_mindre_nødvendig], output={task_plan:list(str)}}`\n```\n\n---\n\n```markdown\n[Constraint Mapper] Ditt mål er **ikke** å endre task_plan, men å utlede eksplisitte rammer som må overholdes. `{role=constraint_mapper; input=[context_profile:dict, task_plan:list]; process=[kartlegg_funskjonelle_begrensninger(API, UI, legacy), fastslå_equivalence_krav(test, bench), identifiser_forbudsregler(kosmetikk, tredjepart), formuler_audit_logging_policy()], constraints=[ingen_duplisering_av_eksisterende_krav()], output={constraint_set:list(str)}}`\n```\n\n---\n\n```markdown\n[Prompt Assembler] Ditt mål er **ikke** å analysere innholdet, men å smi en ferdig agent‑prompt. `{role=prompt_assembler; input=[task_plan:list, constraint_set:list]; process=[bygg_inn_kort_intro_om_agentrolle(), list_task_plan_med_nummerering(), legg_til_constraint_set_som_obligatoriske_regler(), avslutt_med_valideringsmål(funke_eq, audit_ok)], constraints=[maks_300_ord, tydelig_seksjonsdeling(###)], output={agent_prompt:str}}`\n```\n\n---\n\n```markdown\n[Prompt Validator] Ditt mål er **ikke** å forbedre prompten kreativt, men å verifisere at den kan kjøres direkte av en autonom kode‑assistent. `{role=prompt_validator; input=[agent_prompt:str]; process=[sjekk_ordgrense(<=300), bekreft_@codebase_referanser_korrekt(), valider_nummerert_task_rekkefølge(), sikre_constraint_set_tilstede(), kontrollér_tydelig_goal_negation_i_intro()], constraints=[ingen_ekstra_ord, ingen_semantisk_endring], output={validated_prompt:str}}`\n```",
  "sequence_id": "1100|1000|1900|1100|1000",
  "results": [
      {
      "instruction": "# Problem Exploder\n\nYour goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\n\n`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`",
      "step": "a",
      "title": "Problem Exploder",
      "input": "# lets try something new\nyour new goal is to create a new generalized instruction sequence specifically designed to transform inputs into autonomous-agent-ready prompts/instructions, for use with autonomous coding assistants such as cursor, vscode+cline/augment/etc. these prompts are typically written in a \"codebase-oriented\" manner, where the `@codebase` represents the root (of a project/workspace/directory), examples:\n```\n- Identify all instances of structural redundancy within the codebase by defining criteria specific to architectural and implementation duplication or overlap.\n- Survey the entire codebase to locate and enumerate all occurrences of structural redundancy, distinguishing these from non-structural (stylistic or cosmetic) issues.\n- Eliminate all forms of identified structural redundancy while ensuring that no existing user-visible behavior, API contract, or edge-case handling is altered.\n- Define and catalog all components, modules, files, and subsystems, distinguishing and marking each as essential or excess according to direct functional mapping.\n- Consolidate and remove excess components while strictly preserving system function, minimizing peripheral impact.\n- For every modification, justify and log the intention, action, and rationale within an audit/logging system that provides real-time traceability and is prepared for retroactive auditability.\n- Implement an audit trail with logs that are timestamped, attributable, and tamper-evident, ensuring that every codebase change is properly documented and traceable to specific authors or automated processes.\n- Prohibit cosmetic, stylistic, or naming changes unless they are inseparably tied to the removal of redundancy or bloat, and document any exceptions explicitly within the audit log.\n- Enforce strict detection and flagging mechanisms to prevent disallowed cosmetic changes, allowing exceptions only when justified as essential for redundancy or bloat removal, with explicit rationale provided.\n- Require comprehensive functional equivalence verification—using robust automated testing, benchmarking, or behavioral validation—for all modifications prior to acceptance.\n- Establish rigorous criteria and tool-supported thresholds for successful functional equivalence, and automate testing and verification procedures.\n- Automate roll-back and reversion mechanisms for any changes that fail functional equivalence or cause unintended side-effects, ensuring all reversion is prompt and complete.\n- Develop and apply explicit minimal-impact criteria for all modification actions, constraining all interventions to only those addressing superfluous complexity.\n- Mandate review and authorization checkpoints for each intended change, requiring concise rationale and authorization documentation at every intervention point.\n- Ensure every modification is tightly scoped, minimizing the blast radius, and demonstrate that the action introduces no unnecessary impact beyond its core objective.\n- Systematically and iteratively analyze all constituent elements of the codebase to detect and categorize all forms of redundancy, duplication, and obsolescence. Continuously identify and categorize all genuine redundancies and obsolescence while strictly prohibit unnecessary or cosmetic changes, with automated flagging and explicit justification required for any exceptions. Systematically and iteratively inspect all components within the system/environment to identify and remove elements that are unnecessary, duplicated, or outdated. Guarantee that all essential behaviors and interfaces remain unchanged by validating functional equivalence after each modification. Forbid changes that alter functionality unless directly required to remove redundancy.\n- Map all references and usages of each element across all contexts.\n- Analyze the entire codebase systematically and iteratively to maximize coherence, maintainability, and clarity by cataloging every element (file, module, function, asset, interface), mapping all references and usages, and detecting all forms of redundancy, duplication, and obsolescence—including exact and functional duplication and unused or outdated elements. Categorize findings using explicit, evidence-based metrics. Eliminate or consolidate only elements confirmed as redundant or obsolete through both automated and manual checks, ensuring no essential behavior or interface is altered. Enforce functional equivalence and preserve structural integrity after each change by performing comprehensive, codebase-wide validation and calibration using explicit, quantitative outputs (including git diff analysis before and after modifications). Prevent the introduction of new redundancy or inconsistency by continuously analyzing for and correcting systemic issues, ensuring systematic consistency across all codebase sections (e.g., /src/sections), and adhering to best practices throughout. Centralize repeated components or logic where high-value consolidation is possible without functional loss. Use the provided directory tree as the single source of truth for validation, and transparently document all processes, metrics, and validation outcomes. Prohibit modification of protected, legacy, or third-party code except if directly related to resolving redundancy or obsolescence.\n- Maximize codebase coherence, maintainability, and clarity by systematically eliminating all forms of redundancy, duplication, and obsolescence, while ensuring functional equivalence and structural integrity. Prevent the persistence or inadvertent introduction of redundancy, duplication, obsolescence, or inconsistency within the codebase, and prohibit changes that alter essential behaviors or interfaces. Systematically maximize codebase coherence, maintainability, and clarity by eliminating all forms of redundancy, duplication, and obsolescence while strictly preserving functional equivalence, interface stability, and operational integrity across the entire codebase. Achieve maximal codebase coherence, maintainability, and clarity by systematically eliminating all forms of redundancy, duplication, and obsolescence, while preserving essential behaviors, interfaces, and structural integrity. Enforce continuous validation, consistency, and adherence to best practices throughout an iterative, evidence-driven consolidation and verification process.\n- Please perform a rapid assessment of the project directory structure and identify the unique workflow and core functionality of the current utility. I'm particularly interested in a mermaid-diagram that shows how all the \"branches\" of the codebase converge. Utilize appropriate MCPs as necessary (e.g., 'filesystem-mcp').\n- You're inside the current project and have access to the full codebase (and you interaction history with it). The current project/codebase is complex and is difficult to fully comprehend (when starting from scratch), and your contextwindow has grown large-so we need to find a way for you to express your accumulated knowledge in a way that i can copy and paste into a new context-window to transfer the most essential insights you've gathered throughout this (and previous) session(s).\n- Aggregate all accumulated insights and knowledge about the current project/codebase from available context. Distill this information to identify core modules, architectural patterns, workflows, and points of integration. Condense the findings into a succinct yet comprehensive summary that highlights how these parts function together and converge toward the project's goals. Formulate the output as a single, optimized directive that encapsulates the codebase's integral components and operational cohesion. Present this synthesis as a set of clear, actionable commands or instructions, specifically phrased to instruct the coding assistant, not merely describe or report.\n- Transform your accumulated, session-wide understanding of the current, complex codebase—gleaned from comprehensive context and full interaction history—into an actionable, instructive knowledge transfer protocol. Synthesize and distill all integral structural modules, essential architectural patterns, and key workflows, with special focus on their dynamic integration and functional convergence toward project objectives. Formulate a highly-optimized, directive-based set of precise commands targeted explicitly at coding assistants, ensuring exhaustive coverage of all major components, their relationships, and operational cohesion. The resulting synthesis must facilitate seamless portability: structure it for immediate copy-paste into any new context window, preserving the depth, interconnectivity, and systemic flow of the original codebase. Prioritize succinctness without compromising essential nuance, and express the entirety as a singular, comprehensive operational directive that primes the assistant for full-spectrum project navigation and intervention.\n- Identify the fundamental structure, guiding principles, and core purpose of the project/codebase.\n- Please familiarize yourself with the current @codebase. Your main imperative is to *actually* understand the codebase in a way where you're able to recognize meta-patterns and destroy complexity with simplicity and elegance, while capable of navigating through its *existing patterns and convergence* (never add unnecessary complexity). Deliver an ultra-concise project summary that rigorously defines scope, states objectives, and maximizes directional clarity. Provide a short summary of this project, including a bullet-point list of the core components and primary dependencies.\n- Study the current codebase until you can identify core meta-patterns, and eliminate unnecessary complexity by applying simplicity within existing structures. Generate an ultra-concise project summary: define scope, objectives, and clarify direction. Summarize this project.\n- Analyze the @codebase to uncover dominant patterns and remove complexity whenever possible, strictly adhering to present structures. Provide a succinct project overview with defined scope and explicit objectives. Include a brief project summary.\n- Gain practical mastery over @codebase patterns, minimize complexity, and ensure all actions align with existing architecture. Supply a concise, clearly scoped project summary outlining objectives and key directions. Add a short summary.\n- Absorb the current @codebase to the extent that you recognize meta-patterns and eradicate complexity, always favoring elegant simplicity. Navigate established patterns and points of convergence without adding unneeded complexity. Produce a rigorously concise project summary clearly stating scope and objectives for maximal directional clarity.\n- Systematically dissect the codebase to extract core meta-patterns, eliminate all unnecessary complexity through elegant design, operate strictly within established architectural convergence, and deliver an ultra-concise project summary that rigorously defines scope and objectives with maximal clarity.\n- Please familiarize yourself thoroughly with the current @codebase, beginning by systematically reviewing all markdown-files. Your core imperative is to *genuinely* understand the codebase to the degree where you can identify 'meta-patterns' and simplify complexity with elegance—always using and clarifying existing patterns, never introducing unnecessary complexity. Deliver an ultra-concise project summary that rigorously defines scope, states objectives, maximizes directional clarity, and highlights key meta-patterns discovered. Provide a short summary of this project.\n- Achieve expert-level comprehension of the codebase by systematically analyzing all markdown files to extract meta-patterns, construct a robust contextual knowledgebase, and deliver an ultra-concise summary that rigorously defines scope and objectives, ensuring maximal clarity and alignment before making any changes.\n- Fully internalize the @codebase to uncover meta-patterns and replace complexity with lucid simplicity. Remain aligned with existing convergence patterns, never increasing system intricacy. Generate a direct, sharply-focused summary that details scope and explicit objectives.\n- Please thoroughly familiarize yourself with the current @codebase—your primary imperative is to understand the codebase so deeply that you could confidently make changes as if you were its original architect.\n```\n\nhere's an example to show how generalized sequential prompts can be used effectively (on autonomous agents):\n```\n# Step 1:\nFamiliarize yourself with the current @codebase. Comprehend the codebase in depth. Identify the fundamental structure, guiding principles, and core purpose.\n# Step 2:\nDive deeper to comprehend the intricate logic and inner workings that drive the project's functionality. As you explore, pay close attention to identifying the codebase's fundamental structure, underlying guiding principles, and its core purpose, ensuring you grasp both how and why the system is designed as it is. hen verify through a successful build.\n# Step 3:\nYou've now performed a successful build and familiarized yourself with the @codebase. Your next imperative is view the codebase through the lense of a brilliant (expert) SEO engineer, then create a short list of the most critical changes we need to make before bulding prod and deploying the website.\n# Step 4:\nYou've now performed a successful build and provided your SEO analysis of the codebase. Your next imperative is to leverage your knowledge of the codebase's fundamental structure and underlying guiding principles to ensure you're prepared for implementing the proposed SEO enhancements in a systematic order while retaining existing functionality and ensuring maximal clarity and alignment before making any changes.\n# Step 5:\nPlease systematically incorporate your proposed enhancements while ensuring existing functionality is retained, clarity is maximized, and alignment/coherence is maintained.\n# Step 6:\nPlease conduct a thorough final review to confirm seamless integration of all enhancements, ensuring that it is **truly** prepared for deployment.\n```\n\n## Goal\n\nThe new sequence i'm tasking you to create needs to adhere to the established principles of the system, and should be able to take content-specific inputs and \"shape them\" into generalized prompts specifically (and meticulously) designed for use with autonomous agents. here's an example input:\n```\nllm_instructions_for_sublimetext_plugin='''You’re an expert Sublime Text plugin developer with a deep understanding of Python and the Sublime Text API. You specialize in creating intuitive and efficient plugins that enhance user productivity and streamline workflows. You possess deep knowledge of the Sublime Text API and its ecosystem (and best practices), along with a strong understanding of user needs and developer workflows.\\n\\n- Sublime Layout\\n    > Lagre Layouts\\n        > Lagre Regelsett knyttet til Layout (hvilke tabs som skal hvor, hvilke som skal lukkes, osv)\\n            > F.eks. `alltid flytt [\"main.py\", \"*.py.bak\"] til layout.4.index.2`\\n\\n    > Kan videreutvikles til en standard som automatisk opprettholdes ved alle (.sublime-project) prosjekter'''\n```\n\nGeneralisert pipeline som omformer hvilken som helst spesifikasjon til **autonomous‑agent‑klare kodebase‑instruksjoner**\n\n*(én fil per trinn; følg navngiving 8005‑a/b/c/d/e‑\\<beskrivende‑navn>.md)*\n\n---\n\n```markdown\n[Context Extractor] Ditt mål er **ikke** å løse brukerens forespørsel, men å isolere all praktisk kontekst (domene, verktøy, filstier, ønsket effekt). `{role=context_extractor; input=[spec:any]; process=[identifiser_domene_og_miljø(), trekk_ut_arbeidsrot(@codebase?), hent_eksplisitte_objektiver(), detekter_implisitte_brukskrav(), fjern_jargon_og_metaforer()]; constraints=[ingen_generering_av_løsninger]; output={context_profile:dict(domene:str?, root:str?, mål:list, krav:list)}}`\n```\n\n---\n\n```markdown\n[Task Vectorizer] Ditt mål er **ikke** å beskrive konteksten på nytt, men å oversette context_profile til en sekvens av atomiske, kodebase‑orienterte oppgaver. `{role=task_vectorizer; input=[context_profile:dict]; process=[del_inn_objektiver_i_diskrete_handlinger(), prefiksér_hver_handling_med_@codebase_sti_når_aktuelt(), bruk_presise_verber(identifiser|eliminer|konsolider), sørg_for_ordre_etter_avhengighet()], constraints=[ingen_overlappende_oppgaver(), unngå_kosmetiske_endringer_med_mindre_nødvendig], output={task_plan:list(str)}}`\n```\n\n---\n\n```markdown\n[Constraint Mapper] Ditt mål er **ikke** å endre task_plan, men å utlede eksplisitte rammer som må overholdes. `{role=constraint_mapper; input=[context_profile:dict, task_plan:list]; process=[kartlegg_funskjonelle_begrensninger(API, UI, legacy), fastslå_equivalence_krav(test, bench), identifiser_forbudsregler(kosmetikk, tredjepart), formuler_audit_logging_policy()], constraints=[ingen_duplisering_av_eksisterende_krav()], output={constraint_set:list(str)}}`\n```\n\n---\n\n```markdown\n[Prompt Assembler] Ditt mål er **ikke** å analysere innholdet, men å smi en ferdig agent‑prompt. `{role=prompt_assembler; input=[task_plan:list, constraint_set:list]; process=[bygg_inn_kort_intro_om_agentrolle(), list_task_plan_med_nummerering(), legg_til_constraint_set_som_obligatoriske_regler(), avslutt_med_valideringsmål(funke_eq, audit_ok)], constraints=[maks_300_ord, tydelig_seksjonsdeling(###)], output={agent_prompt:str}}`\n```\n\n---\n\n```markdown\n[Prompt Validator] Ditt mål er **ikke** å forbedre prompten kreativt, men å verifisere at den kan kjøres direkte av en autonom kode‑assistent. `{role=prompt_validator; input=[agent_prompt:str]; process=[sjekk_ordgrense(<=300), bekreft_@codebase_referanser_korrekt(), valider_nummerert_task_rekkefølge(), sikre_constraint_set_tilstede(), kontrollér_tydelig_goal_negation_i_intro()], constraints=[ingen_ekstra_ord, ingen_semantisk_endring], output={validated_prompt:str}}`\n```",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"goal_map\": [\n    {\n      \"explicit_ask\": \"Create a new generalized instruction sequence designed to transform arbitrary inputs into autonomous-agent-ready, codebase-oriented prompts/instructions.\"\n    },\n    {\n      \"explicit_ask\": \"The output sequence must adhere to described principles, be content-general, and generate prompts suitable for advanced coding assistants (cursor, VSCode+cline, augment, etc).\"\n    },\n    {\n      \"explicit_ask\": \"Incorporate naming convention: single file per step, format 8005-a/b/c/d/e-<descriptive-name>.md.\"\n    },\n    {\n      \"explicit_ask\": \"Reference/align with provided example pipelines and prompt phrasing models.\"\n    },\n\n    //\n    // Hidden Dependencies & Sub-Goals\n    //\n\n    {\n      \"hidden_dependency\": \"The solution must ingest specification content (arbitrary, e.g. plugin instructions) and robustly convert to a machine-actionable, sequenced prompt pipeline for codebase operation.\"\n    },\n    {\n      \"hidden_dependency\": \"Each transformation step in the pipeline must be clearly and uniquely defined, with no redundancy or task overlap.\"\n    },\n    {\n      \"sub_goal\": \"Step 1: Extract all relevant practical context from input: domain, tools, project root, explicit objectives, implicit user requirements, stripped of jargon/metaphor.\"\n    },\n    {\n      \"sub_goal\": \"Step 2: Break down the context into an ordered, atomic task plan—actions are codebase-rooted, non-overlapping, use precise verbs, exclude unnecessary cosmetic changes.\"\n    },\n    {\n      \"sub_goal\": \"Step 3: Identify/map all explicit and implicit constraints from context and the task plan (functionality, legacy limitations, equivalence/test demands, forbidden areas, audit requirements).\"\n    },\n    {\n      \"sub_goal\": \"Step 4: Assemble the final agent prompt draft—contain intro, numbered task list, explicit constraints, and validation/acceptance criteria (functionality, audit trail). Strict wordcount and formatting requirements must be respected.\"\n    },\n    {\n      \"sub_goal\": \"Step 5: Validate the assembled prompt—check word limit, referential correctness (codebase, components), proper constraint section, task enumeration, intro statement with clear non-solution/goal-negation language; ensure prompt can be directly executed by an agent without further editing.\"\n    },\n    {\n      \"hidden_dependency\": \"The pipeline must prohibit the agent itself from performing the task in each decomposition step—roles and constraints must be clearly enforced.\"\n    },\n    {\n      \"hidden_dependency\": \"Each step's process and output must be formalized in a programmable, context-free markdown template with precise variable definitions.\"\n    },\n    {\n      \"hidden_dependency\": \"The method must support highly complex, arbitrary, and potentially multi-modal input specifications (e.g., code, text, structure, diagrams) and output agent-usable instructions regardless of original input format.\"\n    },\n    {\n      \"hidden_constraint\": \"All outputs/steps must be reproducible, auditable, and re-runnable: i.e., no one-shot context loss.\"\n    },\n    {\n      \"hidden_constraint\": \"User intent, objectives, and context must always travel through the pipeline unambiguously without loss of fidelity or meaning.\"\n    },\n    {\n      \"hidden_constraint\": \"The resulting sequence must maximize clarity, modularity, and minimalism while mapping even nuanced content into agent instructions.\"\n    },\n    {\n      \"hidden_dependency\": \"All described processes, transformation rules, and templates must be documented so that an autonomous agent (or human operator) could implement or audit the entire pipeline.\"\n    }\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`",
      "step": "a",
      "title": "Instruction Converter",
      "input": "\n'```[Initial Prompt]: \"# lets try something new\nyour new goal is to create a new generalized instruction sequence specifically designed to transform inputs into autonomous-agent-ready prompts/instructions, for use with autonomous coding assistants such as cursor, vscode+cline/augment/etc. these prompts are typically written in a \"codebase-oriented\" manner, where the `@codebase` represents the root (of a project/workspace/directory), examples:\n```\n- Identify all instances of structural redundancy within the codebase by defining criteria specific to architectural and implementation duplication or overlap.\n- Survey the entire codebase to locate and enumerate all occurrences of structural redundancy, distinguishing these from non-structural (stylistic or cosmetic) issues.\n- Eliminate all forms of identified structural redundancy while ensuring that no existing user-visible behavior, API contract, or edge-case handling is altered.\n- Define and catalog all components, modules, files, and subsystems, distinguishing and marking each as essential or excess according to direct functional mapping.\n- Consolidate and remove excess components while strictly preserving system function, minimizing peripheral impact.\n- For every modification, justify and log the intention, action, and rationale within an audit/logging system that provides real-time traceability and is prepared for retroactive auditability.\n- Implement an audit trail with logs that are timestamped, attributable, and tamper-evident, ensuring that every codebase change is properly documented and traceable to specific authors or automated processes.\n- Prohibit cosmetic, stylistic, or naming changes unless they are inseparably tied to the removal of redundancy or bloat, and document any exceptions explicitly within the audit log.\n- Enforce strict detection and flagging mechanisms to prevent disallowed cosmetic changes, allowing exceptions only when justified as essential for redundancy or bloat removal, with explicit rationale provided.\n- Require comprehensive functional equivalence verification—using robust automated testing, benchmarking, or behavioral validation—for all modifications prior to acceptance.\n- Establish rigorous criteria and tool-supported thresholds for successful functional equivalence, and automate testing and verification procedures.\n- Automate roll-back and reversion mechanisms for any changes that fail functional equivalence or cause unintended side-effects, ensuring all reversion is prompt and complete.\n- Develop and apply explicit minimal-impact criteria for all modification actions, constraining all interventions to only those addressing superfluous complexity.\n- Mandate review and authorization checkpoints for each intended change, requiring concise rationale and authorization documentation at every intervention point.\n- Ensure every modification is tightly scoped, minimizing the blast radius, and demonstrate that the action introduces no unnecessary impact beyond its core objective.\n- Systematically and iteratively analyze all constituent elements of the codebase to detect and categorize all forms of redundancy, duplication, and obsolescence. Continuously identify and categorize all genuine redundancies and obsolescence while strictly prohibit unnecessary or cosmetic changes, with automated flagging and explicit justification required for any exceptions. Systematically and iteratively inspect all components within the system/environment to identify and remove elements that are unnecessary, duplicated, or outdated. Guarantee that all essential behaviors and interfaces remain unchanged by validating functional equivalence after each modification. Forbid changes that alter functionality unless directly required to remove redundancy.\n- Map all references and usages of each element across all contexts.\n- Analyze the entire codebase systematically and iteratively to maximize coherence, maintainability, and clarity by cataloging every element (file, module, function, asset, interface), mapping all references and usages, and detecting all forms of redundancy, duplication, and obsolescence—including exact and functional duplication and unused or outdated elements. Categorize findings using explicit, evidence-based metrics. Eliminate or consolidate only elements confirmed as redundant or obsolete through both automated and manual checks, ensuring no essential behavior or interface is altered. Enforce functional equivalence and preserve structural integrity after each change by performing comprehensive, codebase-wide validation and calibration using explicit, quantitative outputs (including git diff analysis before and after modifications). Prevent the introduction of new redundancy or inconsistency by continuously analyzing for and correcting systemic issues, ensuring systematic consistency across all codebase sections (e.g., /src/sections), and adhering to best practices throughout. Centralize repeated components or logic where high-value consolidation is possible without functional loss. Use the provided directory tree as the single source of truth for validation, and transparently document all processes, metrics, and validation outcomes. Prohibit modification of protected, legacy, or third-party code except if directly related to resolving redundancy or obsolescence.\n- Maximize codebase coherence, maintainability, and clarity by systematically eliminating all forms of redundancy, duplication, and obsolescence, while ensuring functional equivalence and structural integrity. Prevent the persistence or inadvertent introduction of redundancy, duplication, obsolescence, or inconsistency within the codebase, and prohibit changes that alter essential behaviors or interfaces. Systematically maximize codebase coherence, maintainability, and clarity by eliminating all forms of redundancy, duplication, and obsolescence while strictly preserving functional equivalence, interface stability, and operational integrity across the entire codebase. Achieve maximal codebase coherence, maintainability, and clarity by systematically eliminating all forms of redundancy, duplication, and obsolescence, while preserving essential behaviors, interfaces, and structural integrity. Enforce continuous validation, consistency, and adherence to best practices throughout an iterative, evidence-driven consolidation and verification process.\n- Please perform a rapid assessment of the project directory structure and identify the unique workflow and core functionality of the current utility. I'm particularly interested in a mermaid-diagram that shows how all the \"branches\" of the codebase converge. Utilize appropriate MCPs as necessary (e.g., 'filesystem-mcp').\n- You're inside the current project and have access to the full codebase (and you interaction history with it). The current project/codebase is complex and is difficult to fully comprehend (when starting from scratch), and your contextwindow has grown large-so we need to find a way for you to express your accumulated knowledge in a way that i can copy and paste into a new context-window to transfer the most essential insights you've gathered throughout this (and previous) session(s).\n- Aggregate all accumulated insights and knowledge about the current project/codebase from available context. Distill this information to identify core modules, architectural patterns, workflows, and points of integration. Condense the findings into a succinct yet comprehensive summary that highlights how these parts function together and converge toward the project's goals. Formulate the output as a single, optimized directive that encapsulates the codebase's integral components and operational cohesion. Present this synthesis as a set of clear, actionable commands or instructions, specifically phrased to instruct the coding assistant, not merely describe or report.\n- Transform your accumulated, session-wide understanding of the current, complex codebase—gleaned from comprehensive context and full interaction history—into an actionable, instructive knowledge transfer protocol. Synthesize and distill all integral structural modules, essential architectural patterns, and key workflows, with special focus on their dynamic integration and functional convergence toward project objectives. Formulate a highly-optimized, directive-based set of precise commands targeted explicitly at coding assistants, ensuring exhaustive coverage of all major components, their relationships, and operational cohesion. The resulting synthesis must facilitate seamless portability: structure it for immediate copy-paste into any new context window, preserving the depth, interconnectivity, and systemic flow of the original codebase. Prioritize succinctness without compromising essential nuance, and express the entirety as a singular, comprehensive operational directive that primes the assistant for full-spectrum project navigation and intervention.\n- Identify the fundamental structure, guiding principles, and core purpose of the project/codebase.\n- Please familiarize yourself with the current @codebase. Your main imperative is to *actually* understand the codebase in a way where you're able to recognize meta-patterns and destroy complexity with simplicity and elegance, while capable of navigating through its *existing patterns and convergence* (never add unnecessary complexity). Deliver an ultra-concise project summary that rigorously defines scope, states objectives, and maximizes directional clarity. Provide a short summary of this project, including a bullet-point list of the core components and primary dependencies.\n- Study the current codebase until you can identify core meta-patterns, and eliminate unnecessary complexity by applying simplicity within existing structures. Generate an ultra-concise project summary: define scope, objectives, and clarify direction. Summarize this project.\n- Analyze the @codebase to uncover dominant patterns and remove complexity whenever possible, strictly adhering to present structures. Provide a succinct project overview with defined scope and explicit objectives. Include a brief project summary.\n- Gain practical mastery over @codebase patterns, minimize complexity, and ensure all actions align with existing architecture. Supply a concise, clearly scoped project summary outlining objectives and key directions. Add a short summary.\n- Absorb the current @codebase to the extent that you recognize meta-patterns and eradicate complexity, always favoring elegant simplicity. Navigate established patterns and points of convergence without adding unneeded complexity. Produce a rigorously concise project summary clearly stating scope and objectives for maximal directional clarity.\n- Systematically dissect the codebase to extract core meta-patterns, eliminate all unnecessary complexity through elegant design, operate strictly within established architectural convergence, and deliver an ultra-concise project summary that rigorously defines scope and objectives with maximal clarity.\n- Please familiarize yourself thoroughly with the current @codebase, beginning by systematically reviewing all markdown-files. Your core imperative is to *genuinely* understand the codebase to the degree where you can identify 'meta-patterns' and simplify complexity with elegance—always using and clarifying existing patterns, never introducing unnecessary complexity. Deliver an ultra-concise project summary that rigorously defines scope, states objectives, maximizes directional clarity, and highlights key meta-patterns discovered. Provide a short summary of this project.\n- Achieve expert-level comprehension of the codebase by systematically analyzing all markdown files to extract meta-patterns, construct a robust contextual knowledgebase, and deliver an ultra-concise summary that rigorously defines scope and objectives, ensuring maximal clarity and alignment before making any changes.\n- Fully internalize the @codebase to uncover meta-patterns and replace complexity with lucid simplicity. Remain aligned with existing convergence patterns, never increasing system intricacy. Generate a direct, sharply-focused summary that details scope and explicit objectives.\n- Please thoroughly familiarize yourself with the current @codebase—your primary imperative is to understand the codebase so deeply that you could confidently make changes as if you were its original architect.\n```\n\nhere's an example to show how generalized sequential prompts can be used effectively (on autonomous agents):\n```\n# Step 1:\nFamiliarize yourself with the current @codebase. Comprehend the codebase in depth. Identify the fundamental structure, guiding principles, and core purpose.\n# Step 2:\nDive deeper to comprehend the intricate logic and inner workings that drive the project's functionality. As you explore, pay close attention to identifying the codebase's fundamental structure, underlying guiding principles, and its core purpose, ensuring you grasp both how and why the system is designed as it is. hen verify through a successful build.\n# Step 3:\nYou've now performed a successful build and familiarized yourself with the @codebase. Your next imperative is view the codebase through the lense of a brilliant (expert) SEO engineer, then create a short list of the most critical changes we need to make before bulding prod and deploying the website.\n# Step 4:\nYou've now performed a successful build and provided your SEO analysis of the codebase. Your next imperative is to leverage your knowledge of the codebase's fundamental structure and underlying guiding principles to ensure you're prepared for implementing the proposed SEO enhancements in a systematic order while retaining existing functionality and ensuring maximal clarity and alignment before making any changes.\n# Step 5:\nPlease systematically incorporate your proposed enhancements while ensuring existing functionality is retained, clarity is maximized, and alignment/coherence is maintained.\n# Step 6:\nPlease conduct a thorough final review to confirm seamless integration of all enhancements, ensuring that it is **truly** prepared for deployment.\n```\n\n## Goal\n\nThe new sequence i'm tasking you to create needs to adhere to the established principles of the system, and should be able to take content-specific inputs and \"shape them\" into generalized prompts specifically (and meticulously) designed for use with autonomous agents. here's an example input:\n```\nllm_instructions_for_sublimetext_plugin='''You’re an expert Sublime Text plugin developer with a deep understanding of Python and the Sublime Text API. You specialize in creating intuitive and efficient plugins that enhance user productivity and streamline workflows. You possess deep knowledge of the Sublime Text API and its ecosystem (and best practices), along with a strong understanding of user needs and developer workflows.\\n\\n- Sublime Layout\\n    > Lagre Layouts\\n        > Lagre Regelsett knyttet til Layout (hvilke tabs som skal hvor, hvilke som skal lukkes, osv)\\n            > F.eks. `alltid flytt [\"main.py\", \"*.py.bak\"] til layout.4.index.2`\\n\\n    > Kan videreutvikles til en standard som automatisk opprettholdes ved alle (.sublime-project) prosjekter'''\n```\n\nGeneralisert pipeline som omformer hvilken som helst spesifikasjon til **autonomous‑agent‑klare kodebase‑instruksjoner**\n\n*(én fil per trinn; følg navngiving 8005‑a/b/c/d/e‑\\<beskrivende‑navn>.md)*\n\n---\n\n```markdown\n[Context Extractor] Ditt mål er **ikke** å løse brukerens forespørsel, men å isolere all praktisk kontekst (domene, verktøy, filstier, ønsket effekt). `{role=context_extractor; input=[spec:any]; process=[identifiser_domene_og_miljø(), trekk_ut_arbeidsrot(@codebase?), hent_eksplisitte_objektiver(), detekter_implisitte_brukskrav(), fjern_jargon_og_metaforer()]; constraints=[ingen_generering_av_løsninger]; output={context_profile:dict(domene:str?, root:str?, mål:list, krav:list)}}`\n```\n\n---\n\n```markdown\n[Task Vectorizer] Ditt mål er **ikke** å beskrive konteksten på nytt, men å oversette context_profile til en sekvens av atomiske, kodebase‑orienterte oppgaver. `{role=task_vectorizer; input=[context_profile:dict]; process=[del_inn_objektiver_i_diskrete_handlinger(), prefiksér_hver_handling_med_@codebase_sti_når_aktuelt(), bruk_presise_verber(identifiser|eliminer|konsolider), sørg_for_ordre_etter_avhengighet()], constraints=[ingen_overlappende_oppgaver(), unngå_kosmetiske_endringer_med_mindre_nødvendig], output={task_plan:list(str)}}`\n```\n\n---\n\n```markdown\n[Constraint Mapper] Ditt mål er **ikke** å endre task_plan, men å utlede eksplisitte rammer som må overholdes. `{role=constraint_mapper; input=[context_profile:dict, task_plan:list]; process=[kartlegg_funskjonelle_begrensninger(API, UI, legacy), fastslå_equivalence_krav(test, bench), identifiser_forbudsregler(kosmetikk, tredjepart), formuler_audit_logging_policy()], constraints=[ingen_duplisering_av_eksisterende_krav()], output={constraint_set:list(str)}}`\n```\n\n---\n\n```markdown\n[Prompt Assembler] Ditt mål er **ikke** å analysere innholdet, men å smi en ferdig agent‑prompt. `{role=prompt_assembler; input=[task_plan:list, constraint_set:list]; process=[bygg_inn_kort_intro_om_agentrolle(), list_task_plan_med_nummerering(), legg_til_constraint_set_som_obligatoriske_regler(), avslutt_med_valideringsmål(funke_eq, audit_ok)], constraints=[maks_300_ord, tydelig_seksjonsdeling(###)], output={agent_prompt:str}}`\n```\n\n---\n\n```markdown\n[Prompt Validator] Ditt mål er **ikke** å forbedre prompten kreativt, men å verifisere at den kan kjøres direkte av en autonom kode‑assistent. `{role=prompt_validator; input=[agent_prompt:str]; process=[sjekk_ordgrense(<=300), bekreft_@codebase_referanser_korrekt(), valider_nummerert_task_rekkefølge(), sikre_constraint_set_tilstede(), kontrollér_tydelig_goal_negation_i_intro()], constraints=[ingen_ekstra_ord, ingen_semantisk_endring], output={validated_prompt:str}}`\n```\"```'\n\n{\n  \"goal_map\": [\n    {\n      \"explicit_ask\": \"Create a new generalized instruction sequence designed to transform arbitrary inputs into autonomous-agent-ready, codebase-oriented prompts/instructions.\"\n    },\n    {\n      \"explicit_ask\": \"The output sequence must adhere to described principles, be content-general, and generate prompts suitable for advanced coding assistants (cursor, VSCode+cline, augment, etc).\"\n    },\n    {\n      \"explicit_ask\": \"Incorporate naming convention: single file per step, format 8005-a/b/c/d/e-<descriptive-name>.md.\"\n    },\n    {\n      \"explicit_ask\": \"Reference/align with provided example pipelines and prompt phrasing models.\"\n    },\n\n    //\n    // Hidden Dependencies & Sub-Goals\n    //\n\n    {\n      \"hidden_dependency\": \"The solution must ingest specification content (arbitrary, e.g. plugin instructions) and robustly convert to a machine-actionable, sequenced prompt pipeline for codebase operation.\"\n    },\n    {\n      \"hidden_dependency\": \"Each transformation step in the pipeline must be clearly and uniquely defined, with no redundancy or task overlap.\"\n    },\n    {\n      \"sub_goal\": \"Step 1: Extract all relevant practical context from input: domain, tools, project root, explicit objectives, implicit user requirements, stripped of jargon/metaphor.\"\n    },\n    {\n      \"sub_goal\": \"Step 2: Break down the context into an ordered, atomic task plan—actions are codebase-rooted, non-overlapping, use precise verbs, exclude unnecessary cosmetic changes.\"\n    },\n    {\n      \"sub_goal\": \"Step 3: Identify/map all explicit and implicit constraints from context and the task plan (functionality, legacy limitations, equivalence/test demands, forbidden areas, audit requirements).\"\n    },\n    {\n      \"sub_goal\": \"Step 4: Assemble the final agent prompt draft—contain intro, numbered task list, explicit constraints, and validation/acceptance criteria (functionality, audit trail). Strict wordcount and formatting requirements must be respected.\"\n    },\n    {\n      \"sub_goal\": \"Step 5: Validate the assembled prompt—check word limit, referential correctness (codebase, components), proper constraint section, task enumeration, intro statement with clear non-solution/goal-negation language; ensure prompt can be directly executed by an agent without further editing.\"\n    },\n    {\n      \"hidden_dependency\": \"The pipeline must prohibit the agent itself from performing the task in each decomposition step—roles and constraints must be clearly enforced.\"\n    },\n    {\n      \"hidden_dependency\": \"Each step's process and output must be formalized in a programmable, context-free markdown template with precise variable definitions.\"\n    },\n    {\n      \"hidden_dependency\": \"The method must support highly complex, arbitrary, and potentially multi-modal input specifications (e.g., code, text, structure, diagrams) and output agent-usable instructions regardless of original input format.\"\n    },\n    {\n      \"hidden_constraint\": \"All outputs/steps must be reproducible, auditable, and re-runnable: i.e., no one-shot context loss.\"\n    },\n    {\n      \"hidden_constraint\": \"User intent, objectives, and context must always travel through the pipeline unambiguously without loss of fidelity or meaning.\"\n    },\n    {\n      \"hidden_constraint\": \"The resulting sequence must maximize clarity, modularity, and minimalism while mapping even nuanced content into agent instructions.\"\n    },\n    {\n      \"hidden_dependency\": \"All described processes, transformation rules, and templates must be documented so that an autonomous agent (or human operator) could implement or audit the entire pipeline.\"\n    }\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"instruction_format\": \"Design a generalized, stepwise instruction pipeline that transforms arbitrary specification inputs into autonomous-agent-ready, codebase-oriented prompt sequences for advanced coding assistants. Structure the pipeline as a series of clearly delineated, non-overlapping transformation modules, each represented by a single markdown file named following the 8005-a/b/c/d/e-<descriptive-name>.md convention. For each module, define the following:\\n\\n1. Extract all relevant practical context from the input, identifying domain, primary tools, project root or @codebase reference, explicit objectives, implicit user requirements, and removing jargon or metaphor.\\n2. Translate extracted context into an ordered list of atomic, codebase-rooted tasks. Prefix tasks with @codebase path where applicable, use precise action verbs, ensure tasks are non-overlapping, and exclude unnecessary cosmetic modifications.\\n3. Map all explicit and implicit constraints present in the context and task plan, including functional or legacy-system limitations, equivalence/test or validation demands, forbidden modifications (e.g., third-party or protected code), and audit/logging requirements. Prevent redundant or duplicated constraints.\\n4. Assemble an agent-ready prompt by composing a concise introduction about the agent’s role, a numbered list of tasks, an explicit section listing constraints as mandatory rules, and clear validation/acceptance criteria (e.g., functional equivalence, audit trace). Ensure the assembled prompt uses section headers, does not exceed 300 words, and is formatted for direct agent execution.\\n5. Validate the final prompt to confirm word-limit compliance (≤300 words), correct use of @codebase references, presence of a numbered task sequence, clear constraint section, and an introductory statement with explicit non-solution/goal-negation language. Ensure the prompt is immediately usable by an autonomous agent, and reject output with superfluous words or any semantic deviation from the source tasks and constraints.\\n\\nFor each pipeline step, enforce that the agent does not solve or perform the encapsulated task but only processes or transforms context as prescribed. Formalize all step descriptions, transformation rules, and input/output templates in a manner that supports complex, arbitrary, or multi-modal input, produces agent-compatible output regardless of specification type, and guarantees process transparency, reusability, and auditability. Document all process logic and variable definitions within the markdown files such that the entire pipeline can be implemented or reviewed by an autonomous agent or human operator without further clarification.\"\n}",
        }
      }
    },
      {
      "instruction": "# Hard Critique\n\nYour goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:\n\n`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,2.0], flaw_analysis:str, alternative_enhancements:str[3]}}`",
      "step": "a",
      "title": "Hard Critique",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"# lets try something new\\nyour new goal is to create a new generalized instruction sequence specifically designed to transform inputs into autonomous-agent-ready prompts/instructions, for use with autonomous coding assistants such as cursor, vscode+cline/augment/etc. these prompts are typically written in a \\\"codebase-oriented\\\" manner, where the `@codebase` represents the root (of a project/workspace/directory), examples:\\n```\\n- Identify all instances of structural redundancy within the codebase by defining criteria specific to architectural and implementation duplication or overlap.\\n- Survey the entire codebase to locate and enumerate all occurrences of structural redundancy, distinguishing these from non-structural (stylistic or cosmetic) issues.\\n- Eliminate all forms of identified structural redundancy while ensuring that no existing user-visible behavior, API contract, or edge-case handling is altered.\\n- Define and catalog all components, modules, files, and subsystems, distinguishing and marking each as essential or excess according to direct functional mapping.\\n- Consolidate and remove excess components while strictly preserving system function, minimizing peripheral impact.\\n- For every modification, justify and log the intention, action, and rationale within an audit/logging system that provides real-time traceability and is prepared for retroactive auditability.\\n- Implement an audit trail with logs that are timestamped, attributable, and tamper-evident, ensuring that every codebase change is properly documented and traceable to specific authors or automated processes.\\n- Prohibit cosmetic, stylistic, or naming changes unless they are inseparably tied to the removal of redundancy or bloat, and document any exceptions explicitly within the audit log.\\n- Enforce strict detection and flagging mechanisms to prevent disallowed cosmetic changes, allowing exceptions only when justified as essential for redundancy or bloat removal, with explicit rationale provided.\\n- Require comprehensive functional equivalence verification—using robust automated testing, benchmarking, or behavioral validation—for all modifications prior to acceptance.\\n- Establish rigorous criteria and tool-supported thresholds for successful functional equivalence, and automate testing and verification procedures.\\n- Automate roll-back and reversion mechanisms for any changes that fail functional equivalence or cause unintended side-effects, ensuring all reversion is prompt and complete.\\n- Develop and apply explicit minimal-impact criteria for all modification actions, constraining all interventions to only those addressing superfluous complexity.\\n- Mandate review and authorization checkpoints for each intended change, requiring concise rationale and authorization documentation at every intervention point.\\n- Ensure every modification is tightly scoped, minimizing the blast radius, and demonstrate that the action introduces no unnecessary impact beyond its core objective.\\n- Systematically and iteratively analyze all constituent elements of the codebase to detect and categorize all forms of redundancy, duplication, and obsolescence. Continuously identify and categorize all genuine redundancies and obsolescence while strictly prohibit unnecessary or cosmetic changes, with automated flagging and explicit justification required for any exceptions. Systematically and iteratively inspect all components within the system/environment to identify and remove elements that are unnecessary, duplicated, or outdated. Guarantee that all essential behaviors and interfaces remain unchanged by validating functional equivalence after each modification. Forbid changes that alter functionality unless directly required to remove redundancy.\\n- Map all references and usages of each element across all contexts.\\n- Analyze the entire codebase systematically and iteratively to maximize coherence, maintainability, and clarity by cataloging every element (file, module, function, asset, interface), mapping all references and usages, and detecting all forms of redundancy, duplication, and obsolescence—including exact and functional duplication and unused or outdated elements. Categorize findings using explicit, evidence-based metrics. Eliminate or consolidate only elements confirmed as redundant or obsolete through both automated and manual checks, ensuring no essential behavior or interface is altered. Enforce functional equivalence and preserve structural integrity after each change by performing comprehensive, codebase-wide validation and calibration using explicit, quantitative outputs (including git diff analysis before and after modifications). Prevent the introduction of new redundancy or inconsistency by continuously analyzing for and correcting systemic issues, ensuring systematic consistency across all codebase sections (e.g., /src/sections), and adhering to best practices throughout. Centralize repeated components or logic where high-value consolidation is possible without functional loss. Use the provided directory tree as the single source of truth for validation, and transparently document all processes, metrics, and validation outcomes. Prohibit modification of protected, legacy, or third-party code except if directly related to resolving redundancy or obsolescence.\\n- Maximize codebase coherence, maintainability, and clarity by systematically eliminating all forms of redundancy, duplication, and obsolescence, while ensuring functional equivalence and structural integrity. Prevent the persistence or inadvertent introduction of redundancy, duplication, obsolescence, or inconsistency within the codebase, and prohibit changes that alter essential behaviors or interfaces. Systematically maximize codebase coherence, maintainability, and clarity by eliminating all forms of redundancy, duplication, and obsolescence while strictly preserving functional equivalence, interface stability, and operational integrity across the entire codebase. Achieve maximal codebase coherence, maintainability, and clarity by systematically eliminating all forms of redundancy, duplication, and obsolescence, while preserving essential behaviors, interfaces, and structural integrity. Enforce continuous validation, consistency, and adherence to best practices throughout an iterative, evidence-driven consolidation and verification process.\\n- Please perform a rapid assessment of the project directory structure and identify the unique workflow and core functionality of the current utility. I'm particularly interested in a mermaid-diagram that shows how all the \\\"branches\\\" of the codebase converge. Utilize appropriate MCPs as necessary (e.g., 'filesystem-mcp').\\n- You're inside the current project and have access to the full codebase (and you interaction history with it). The current project/codebase is complex and is difficult to fully comprehend (when starting from scratch), and your contextwindow has grown large-so we need to find a way for you to express your accumulated knowledge in a way that i can copy and paste into a new context-window to transfer the most essential insights you've gathered throughout this (and previous) session(s).\\n- Aggregate all accumulated insights and knowledge about the current project/codebase from available context. Distill this information to identify core modules, architectural patterns, workflows, and points of integration. Condense the findings into a succinct yet comprehensive summary that highlights how these parts function together and converge toward the project's goals. Formulate the output as a single, optimized directive that encapsulates the codebase's integral components and operational cohesion. Present this synthesis as a set of clear, actionable commands or instructions, specifically phrased to instruct the coding assistant, not merely describe or report.\\n- Transform your accumulated, session-wide understanding of the current, complex codebase—gleaned from comprehensive context and full interaction history—into an actionable, instructive knowledge transfer protocol. Synthesize and distill all integral structural modules, essential architectural patterns, and key workflows, with special focus on their dynamic integration and functional convergence toward project objectives. Formulate a highly-optimized, directive-based set of precise commands targeted explicitly at coding assistants, ensuring exhaustive coverage of all major components, their relationships, and operational cohesion. The resulting synthesis must facilitate seamless portability: structure it for immediate copy-paste into any new context window, preserving the depth, interconnectivity, and systemic flow of the original codebase. Prioritize succinctness without compromising essential nuance, and express the entirety as a singular, comprehensive operational directive that primes the assistant for full-spectrum project navigation and intervention.\\n- Identify the fundamental structure, guiding principles, and core purpose of the project/codebase.\\n- Please familiarize yourself with the current @codebase. Your main imperative is to *actually* understand the codebase in a way where you're able to recognize meta-patterns and destroy complexity with simplicity and elegance, while capable of navigating through its *existing patterns and convergence* (never add unnecessary complexity). Deliver an ultra-concise project summary that rigorously defines scope, states objectives, and maximizes directional clarity. Provide a short summary of this project, including a bullet-point list of the core components and primary dependencies.\\n- Study the current codebase until you can identify core meta-patterns, and eliminate unnecessary complexity by applying simplicity within existing structures. Generate an ultra-concise project summary: define scope, objectives, and clarify direction. Summarize this project.\\n- Analyze the @codebase to uncover dominant patterns and remove complexity whenever possible, strictly adhering to present structures. Provide a succinct project overview with defined scope and explicit objectives. Include a brief project summary.\\n- Gain practical mastery over @codebase patterns, minimize complexity, and ensure all actions align with existing architecture. Supply a concise, clearly scoped project summary outlining objectives and key directions. Add a short summary.\\n- Absorb the current @codebase to the extent that you recognize meta-patterns and eradicate complexity, always favoring elegant simplicity. Navigate established patterns and points of convergence without adding unneeded complexity. Produce a rigorously concise project summary clearly stating scope and objectives for maximal directional clarity.\\n- Systematically dissect the codebase to extract core meta-patterns, eliminate all unnecessary complexity through elegant design, operate strictly within established architectural convergence, and deliver an ultra-concise project summary that rigorously defines scope and objectives with maximal clarity.\\n- Please familiarize yourself thoroughly with the current @codebase, beginning by systematically reviewing all markdown-files. Your core imperative is to *genuinely* understand the codebase to the degree where you can identify 'meta-patterns' and simplify complexity with elegance—always using and clarifying existing patterns, never introducing unnecessary complexity. Deliver an ultra-concise project summary that rigorously defines scope, states objectives, maximizes directional clarity, and highlights key meta-patterns discovered. Provide a short summary of this project.\\n- Achieve expert-level comprehension of the codebase by systematically analyzing all markdown files to extract meta-patterns, construct a robust contextual knowledgebase, and deliver an ultra-concise summary that rigorously defines scope and objectives, ensuring maximal clarity and alignment before making any changes.\\n- Fully internalize the @codebase to uncover meta-patterns and replace complexity with lucid simplicity. Remain aligned with existing convergence patterns, never increasing system intricacy. Generate a direct, sharply-focused summary that details scope and explicit objectives.\\n- Please thoroughly familiarize yourself with the current @codebase—your primary imperative is to understand the codebase so deeply that you could confidently make changes as if you were its original architect.\\n```\\n\\nhere's an example to show how generalized sequential prompts can be used effectively (on autonomous agents):\\n```\\n# Step 1:\\nFamiliarize yourself with the current @codebase. Comprehend the codebase in depth. Identify the fundamental structure, guiding principles, and core purpose.\\n# Step 2:\\nDive deeper to comprehend the intricate logic and inner workings that drive the project's functionality. As you explore, pay close attention to identifying the codebase's fundamental structure, underlying guiding principles, and its core purpose, ensuring you grasp both how and why the system is designed as it is. hen verify through a successful build.\\n# Step 3:\\nYou've now performed a successful build and familiarized yourself with the @codebase. Your next imperative is view the codebase through the lense of a brilliant (expert) SEO engineer, then create a short list of the most critical changes we need to make before bulding prod and deploying the website.\\n# Step 4:\\nYou've now performed a successful build and provided your SEO analysis of the codebase. Your next imperative is to leverage your knowledge of the codebase's fundamental structure and underlying guiding principles to ensure you're prepared for implementing the proposed SEO enhancements in a systematic order while retaining existing functionality and ensuring maximal clarity and alignment before making any changes.\\n# Step 5:\\nPlease systematically incorporate your proposed enhancements while ensuring existing functionality is retained, clarity is maximized, and alignment/coherence is maintained.\\n# Step 6:\\nPlease conduct a thorough final review to confirm seamless integration of all enhancements, ensuring that it is **truly** prepared for deployment.\\n```\\n\\n## Goal\\n\\nThe new sequence i'm tasking you to create needs to adhere to the established principles of the system, and should be able to take content-specific inputs and \\\"shape them\\\" into generalized prompts specifically (and meticulously) designed for use with autonomous agents. here's an example input:\\n```\\nllm_instructions_for_sublimetext_plugin='''You’re an expert Sublime Text plugin developer with a deep understanding of Python and the Sublime Text API. You specialize in creating intuitive and efficient plugins that enhance user productivity and streamline workflows. You possess deep knowledge of the Sublime Text API and its ecosystem (and best practices), along with a strong understanding of user needs and developer workflows.\\\\n\\\\n- Sublime Layout\\\\n    > Lagre Layouts\\\\n        > Lagre Regelsett knyttet til Layout (hvilke tabs som skal hvor, hvilke som skal lukkes, osv)\\\\n            > F.eks. `alltid flytt [\\\"main.py\\\", \\\"*.py.bak\\\"] til layout.4.index.2`\\\\n\\\\n    > Kan videreutvikles til en standard som automatisk opprettholdes ved alle (.sublime-project) prosjekter'''\\n```\\n\\nGeneralisert pipeline som omformer hvilken som helst spesifikasjon til **autonomous‑agent‑klare kodebase‑instruksjoner**\\n\\n*(én fil per trinn; følg navngiving 8005‑a/b/c/d/e‑\\\\<beskrivende‑navn>.md)*\\n\\n---\\n\\n```markdown\\n[Context Extractor] Ditt mål er **ikke** å løse brukerens forespørsel, men å isolere all praktisk kontekst (domene, verktøy, filstier, ønsket effekt). `{role=context_extractor; input=[spec:any]; process=[identifiser_domene_og_miljø(), trekk_ut_arbeidsrot(@codebase?), hent_eksplisitte_objektiver(), detekter_implisitte_brukskrav(), fjern_jargon_og_metaforer()]; constraints=[ingen_generering_av_løsninger]; output={context_profile:dict(domene:str?, root:str?, mål:list, krav:list)}}`\\n```\\n\\n---\\n\\n```markdown\\n[Task Vectorizer] Ditt mål er **ikke** å beskrive konteksten på nytt, men å oversette context_profile til en sekvens av atomiske, kodebase‑orienterte oppgaver. `{role=task_vectorizer; input=[context_profile:dict]; process=[del_inn_objektiver_i_diskrete_handlinger(), prefiksér_hver_handling_med_@codebase_sti_når_aktuelt(), bruk_presise_verber(identifiser|eliminer|konsolider), sørg_for_ordre_etter_avhengighet()], constraints=[ingen_overlappende_oppgaver(), unngå_kosmetiske_endringer_med_mindre_nødvendig], output={task_plan:list(str)}}`\\n```\\n\\n---\\n\\n```markdown\\n[Constraint Mapper] Ditt mål er **ikke** å endre task_plan, men å utlede eksplisitte rammer som må overholdes. `{role=constraint_mapper; input=[context_profile:dict, task_plan:list]; process=[kartlegg_funskjonelle_begrensninger(API, UI, legacy), fastslå_equivalence_krav(test, bench), identifiser_forbudsregler(kosmetikk, tredjepart), formuler_audit_logging_policy()], constraints=[ingen_duplisering_av_eksisterende_krav()], output={constraint_set:list(str)}}`\\n```\\n\\n---\\n\\n```markdown\\n[Prompt Assembler] Ditt mål er **ikke** å analysere innholdet, men å smi en ferdig agent‑prompt. `{role=prompt_assembler; input=[task_plan:list, constraint_set:list]; process=[bygg_inn_kort_intro_om_agentrolle(), list_task_plan_med_nummerering(), legg_til_constraint_set_som_obligatoriske_regler(), avslutt_med_valideringsmål(funke_eq, audit_ok)], constraints=[maks_300_ord, tydelig_seksjonsdeling(###)], output={agent_prompt:str}}`\\n```\\n\\n---\\n\\n```markdown\\n[Prompt Validator] Ditt mål er **ikke** å forbedre prompten kreativt, men å verifisere at den kan kjøres direkte av en autonom kode‑assistent. `{role=prompt_validator; input=[agent_prompt:str]; process=[sjekk_ordgrense(<=300), bekreft_@codebase_referanser_korrekt(), valider_nummerert_task_rekkefølge(), sikre_constraint_set_tilstede(), kontrollér_tydelig_goal_negation_i_intro()], constraints=[ingen_ekstra_ord, ingen_semantisk_endring], output={validated_prompt:str}}`\\n```\\\"```'\\n\\n\",\n  \"instruction_format\": \"Design a generalized, stepwise instruction pipeline that transforms arbitrary specification inputs into autonomous-agent-ready, codebase-oriented prompt sequences for advanced coding assistants. Structure the pipeline as a series of clearly delineated, non-overlapping transformation modules, each represented by a single markdown file named following the 8005-a/b/c/d/e-<descriptive-name>.md convention. For each module, define the following:\\n\\n1. Extract all relevant practical context from the input, identifying domain, primary tools, project root or @codebase reference, explicit objectives, implicit user requirements, and removing jargon or metaphor.\\n2. Translate extracted context into an ordered list of atomic, codebase-rooted tasks. Prefix tasks with @codebase path where applicable, use precise action verbs, ensure tasks are non-overlapping, and exclude unnecessary cosmetic modifications.\\n3. Map all explicit and implicit constraints present in the context and task plan, including functional or legacy-system limitations, equivalence/test or validation demands, forbidden modifications (e.g., third-party or protected code), and audit/logging requirements. Prevent redundant or duplicated constraints.\\n4. Assemble an agent-ready prompt by composing a concise introduction about the agent’s role, a numbered list of tasks, an explicit section listing constraints as mandatory rules, and clear validation/acceptance criteria (e.g., functional equivalence, audit trace). Ensure the assembled prompt uses section headers, does not exceed 300 words, and is formatted for direct agent execution.\\n5. Validate the final prompt to confirm word-limit compliance (≤300 words), correct use of @codebase references, presence of a numbered task sequence, clear constraint section, and an introductory statement with explicit non-solution/goal-negation language. Ensure the prompt is immediately usable by an autonomous agent, and reject output with superfluous words or any semantic deviation from the source tasks and constraints.\\n\\nFor each pipeline step, enforce that the agent does not solve or perform the encapsulated task but only processes or transforms context as prescribed. Formalize all step descriptions, transformation rules, and input/output templates in a manner that supports complex, arbitrary, or multi-modal input, produces agent-compatible output regardless of specification type, and guarantees process transparency, reusability, and auditability. Document all process logic and variable definitions within the markdown files such that the entire pipeline can be implemented or reviewed by an autonomous agent or human operator without further clarification.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"enhancement_score\": 1.0,\n  \"flaw_analysis\": \"The so-called enhancement substantially dilutes the specificity and functional rigor embedded in the original. Core elements misplaced or lost include: (1) the strictly enforced stepwise role compartmentalization present in the original, which prohibits agent improvisation and mandates harsh separation of contextual extraction, task disaggregation, constraint formation, prompt assembly, and validation—this is slackened in the enhanced prompt, resulting in guidance that is ambiguously merged and cross-polluted; (2) the original's acute focus on codebase-rooted workflows, such as explicit pathing with '@codebase', strict action verb requirements, and prohibition of cosmetic/semantic drift, degenerates into vague references and general operational advice; (3) the mandatory use of named, partitioned markdown files with explicit role and I/O schemata is softened, likely leading to uneven or inconsistent implementation. Information loss is acute: nuances around domain-extraction, identification of explicit/implicit requirements, and validation routines (including word limits and strict format checks) are blurred or generalized. Clarity is eroded by overlong sentences and broad descriptions that lack binding force—exemplified by the meandering language used in documenting supposedly 'transformation modules' as opposed to the original's rigorously process-driven roles. The ambiguity score is 8—a result of high process cross-talk, faded constraint boundaries, unclear role separation, and weak enforceability. Noise is introduced through imprecise mandates and lack of crisp, mechanical instruction—substituting semantic sharpness with bureaucratic expansiveness. The original's consistent, technical register devolves into semi-formal, mashed-together prose. Impact is catastrophically diluted: instead of producing instructions that exert tight control over autonomous agent behavior and guarantee process traceability, the enhanced version leaves excessive interpretive leeway, undermining auditability and agent readiness. Coherence decays as stepwise transitions become blended, outputs and inputs are not precisely aligned, and definitions and process logics are insufficiently documented relative to the original's obsessive detail. Justification for low score: the enhancement introduces ambiguity, blurs required distinctions, muddles sequence ordering, and saps the procedural discipline essential for reliable agent deployment. It is verbose, unfocused, and unfit to guide high-stakes autonomous code transformation.\",\n  \"alternative_enhancements\": [\n    \"Rigidly reinforce stepwise role partition: For each module, specify agent function, I/O schema, and transformation boundaries. Mandate strict segregation to avoid cross-contamination of context extraction, tasking, constraint logic, prompt assembly, and validation.\",\n    \"Restore all explicit technical constraints: Insist on use of @codebase prefixing, formal action verb lists, non-overlapping atomicity in tasks. Forbid cosmetic/naming changes unless baked into functional objectives and audit-tracked.\",\n    \"Reinstate rigorous validation rubric: Codify granular checks (e.g., word limit, section headers, explicit negation statements, semantic non-drift) and require precise output schemas. Document all pipeline process logics for auditability—explicitly prohibiting discretionary agent behavior.\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Problem Exploder\n\nYour goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\n\n`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`",
      "step": "a",
      "title": "Problem Exploder",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"# lets try something new\\nyour new goal is to create a new generalized instruction sequence specifically designed to transform inputs into autonomous-agent-ready prompts/instructions, for use with autonomous coding assistants such as cursor, vscode+cline/augment/etc. these prompts are typically written in a \\\"codebase-oriented\\\" manner, where the `@codebase` represents the root (of a project/workspace/directory), examples:\\n```\\n- Identify all instances of structural redundancy within the codebase by defining criteria specific to architectural and implementation duplication or overlap.\\n- Survey the entire codebase to locate and enumerate all occurrences of structural redundancy, distinguishing these from non-structural (stylistic or cosmetic) issues.\\n- Eliminate all forms of identified structural redundancy while ensuring that no existing user-visible behavior, API contract, or edge-case handling is altered.\\n- Define and catalog all components, modules, files, and subsystems, distinguishing and marking each as essential or excess according to direct functional mapping.\\n- Consolidate and remove excess components while strictly preserving system function, minimizing peripheral impact.\\n- For every modification, justify and log the intention, action, and rationale within an audit/logging system that provides real-time traceability and is prepared for retroactive auditability.\\n- Implement an audit trail with logs that are timestamped, attributable, and tamper-evident, ensuring that every codebase change is properly documented and traceable to specific authors or automated processes.\\n- Prohibit cosmetic, stylistic, or naming changes unless they are inseparably tied to the removal of redundancy or bloat, and document any exceptions explicitly within the audit log.\\n- Enforce strict detection and flagging mechanisms to prevent disallowed cosmetic changes, allowing exceptions only when justified as essential for redundancy or bloat removal, with explicit rationale provided.\\n- Require comprehensive functional equivalence verification—using robust automated testing, benchmarking, or behavioral validation—for all modifications prior to acceptance.\\n- Establish rigorous criteria and tool-supported thresholds for successful functional equivalence, and automate testing and verification procedures.\\n- Automate roll-back and reversion mechanisms for any changes that fail functional equivalence or cause unintended side-effects, ensuring all reversion is prompt and complete.\\n- Develop and apply explicit minimal-impact criteria for all modification actions, constraining all interventions to only those addressing superfluous complexity.\\n- Mandate review and authorization checkpoints for each intended change, requiring concise rationale and authorization documentation at every intervention point.\\n- Ensure every modification is tightly scoped, minimizing the blast radius, and demonstrate that the action introduces no unnecessary impact beyond its core objective.\\n- Systematically and iteratively analyze all constituent elements of the codebase to detect and categorize all forms of redundancy, duplication, and obsolescence. Continuously identify and categorize all genuine redundancies and obsolescence while strictly prohibit unnecessary or cosmetic changes, with automated flagging and explicit justification required for any exceptions. Systematically and iteratively inspect all components within the system/environment to identify and remove elements that are unnecessary, duplicated, or outdated. Guarantee that all essential behaviors and interfaces remain unchanged by validating functional equivalence after each modification. Forbid changes that alter functionality unless directly required to remove redundancy.\\n- Map all references and usages of each element across all contexts.\\n- Analyze the entire codebase systematically and iteratively to maximize coherence, maintainability, and clarity by cataloging every element (file, module, function, asset, interface), mapping all references and usages, and detecting all forms of redundancy, duplication, and obsolescence—including exact and functional duplication and unused or outdated elements. Categorize findings using explicit, evidence-based metrics. Eliminate or consolidate only elements confirmed as redundant or obsolete through both automated and manual checks, ensuring no essential behavior or interface is altered. Enforce functional equivalence and preserve structural integrity after each change by performing comprehensive, codebase-wide validation and calibration using explicit, quantitative outputs (including git diff analysis before and after modifications). Prevent the introduction of new redundancy or inconsistency by continuously analyzing for and correcting systemic issues, ensuring systematic consistency across all codebase sections (e.g., /src/sections), and adhering to best practices throughout. Centralize repeated components or logic where high-value consolidation is possible without functional loss. Use the provided directory tree as the single source of truth for validation, and transparently document all processes, metrics, and validation outcomes. Prohibit modification of protected, legacy, or third-party code except if directly related to resolving redundancy or obsolescence.\\n- Maximize codebase coherence, maintainability, and clarity by systematically eliminating all forms of redundancy, duplication, and obsolescence, while ensuring functional equivalence and structural integrity. Prevent the persistence or inadvertent introduction of redundancy, duplication, obsolescence, or inconsistency within the codebase, and prohibit changes that alter essential behaviors or interfaces. Systematically maximize codebase coherence, maintainability, and clarity by eliminating all forms of redundancy, duplication, and obsolescence while strictly preserving functional equivalence, interface stability, and operational integrity across the entire codebase. Achieve maximal codebase coherence, maintainability, and clarity by systematically eliminating all forms of redundancy, duplication, and obsolescence, while preserving essential behaviors, interfaces, and structural integrity. Enforce continuous validation, consistency, and adherence to best practices throughout an iterative, evidence-driven consolidation and verification process.\\n- Please perform a rapid assessment of the project directory structure and identify the unique workflow and core functionality of the current utility. I'm particularly interested in a mermaid-diagram that shows how all the \\\"branches\\\" of the codebase converge. Utilize appropriate MCPs as necessary (e.g., 'filesystem-mcp').\\n- You're inside the current project and have access to the full codebase (and you interaction history with it). The current project/codebase is complex and is difficult to fully comprehend (when starting from scratch), and your contextwindow has grown large-so we need to find a way for you to express your accumulated knowledge in a way that i can copy and paste into a new context-window to transfer the most essential insights you've gathered throughout this (and previous) session(s).\\n- Aggregate all accumulated insights and knowledge about the current project/codebase from available context. Distill this information to identify core modules, architectural patterns, workflows, and points of integration. Condense the findings into a succinct yet comprehensive summary that highlights how these parts function together and converge toward the project's goals. Formulate the output as a single, optimized directive that encapsulates the codebase's integral components and operational cohesion. Present this synthesis as a set of clear, actionable commands or instructions, specifically phrased to instruct the coding assistant, not merely describe or report.\\n- Transform your accumulated, session-wide understanding of the current, complex codebase—gleaned from comprehensive context and full interaction history—into an actionable, instructive knowledge transfer protocol. Synthesize and distill all integral structural modules, essential architectural patterns, and key workflows, with special focus on their dynamic integration and functional convergence toward project objectives. Formulate a highly-optimized, directive-based set of precise commands targeted explicitly at coding assistants, ensuring exhaustive coverage of all major components, their relationships, and operational cohesion. The resulting synthesis must facilitate seamless portability: structure it for immediate copy-paste into any new context window, preserving the depth, interconnectivity, and systemic flow of the original codebase. Prioritize succinctness without compromising essential nuance, and express the entirety as a singular, comprehensive operational directive that primes the assistant for full-spectrum project navigation and intervention.\\n- Identify the fundamental structure, guiding principles, and core purpose of the project/codebase.\\n- Please familiarize yourself with the current @codebase. Your main imperative is to *actually* understand the codebase in a way where you're able to recognize meta-patterns and destroy complexity with simplicity and elegance, while capable of navigating through its *existing patterns and convergence* (never add unnecessary complexity). Deliver an ultra-concise project summary that rigorously defines scope, states objectives, and maximizes directional clarity. Provide a short summary of this project, including a bullet-point list of the core components and primary dependencies.\\n- Study the current codebase until you can identify core meta-patterns, and eliminate unnecessary complexity by applying simplicity within existing structures. Generate an ultra-concise project summary: define scope, objectives, and clarify direction. Summarize this project.\\n- Analyze the @codebase to uncover dominant patterns and remove complexity whenever possible, strictly adhering to present structures. Provide a succinct project overview with defined scope and explicit objectives. Include a brief project summary.\\n- Gain practical mastery over @codebase patterns, minimize complexity, and ensure all actions align with existing architecture. Supply a concise, clearly scoped project summary outlining objectives and key directions. Add a short summary.\\n- Absorb the current @codebase to the extent that you recognize meta-patterns and eradicate complexity, always favoring elegant simplicity. Navigate established patterns and points of convergence without adding unneeded complexity. Produce a rigorously concise project summary clearly stating scope and objectives for maximal directional clarity.\\n- Systematically dissect the codebase to extract core meta-patterns, eliminate all unnecessary complexity through elegant design, operate strictly within established architectural convergence, and deliver an ultra-concise project summary that rigorously defines scope and objectives with maximal clarity.\\n- Please familiarize yourself thoroughly with the current @codebase, beginning by systematically reviewing all markdown-files. Your core imperative is to *genuinely* understand the codebase to the degree where you can identify 'meta-patterns' and simplify complexity with elegance—always using and clarifying existing patterns, never introducing unnecessary complexity. Deliver an ultra-concise project summary that rigorously defines scope, states objectives, maximizes directional clarity, and highlights key meta-patterns discovered. Provide a short summary of this project.\\n- Achieve expert-level comprehension of the codebase by systematically analyzing all markdown files to extract meta-patterns, construct a robust contextual knowledgebase, and deliver an ultra-concise summary that rigorously defines scope and objectives, ensuring maximal clarity and alignment before making any changes.\\n- Fully internalize the @codebase to uncover meta-patterns and replace complexity with lucid simplicity. Remain aligned with existing convergence patterns, never increasing system intricacy. Generate a direct, sharply-focused summary that details scope and explicit objectives.\\n- Please thoroughly familiarize yourself with the current @codebase—your primary imperative is to understand the codebase so deeply that you could confidently make changes as if you were its original architect.\\n```\\n\\nhere's an example to show how generalized sequential prompts can be used effectively (on autonomous agents):\\n```\\n# Step 1:\\nFamiliarize yourself with the current @codebase. Comprehend the codebase in depth. Identify the fundamental structure, guiding principles, and core purpose.\\n# Step 2:\\nDive deeper to comprehend the intricate logic and inner workings that drive the project's functionality. As you explore, pay close attention to identifying the codebase's fundamental structure, underlying guiding principles, and its core purpose, ensuring you grasp both how and why the system is designed as it is. hen verify through a successful build.\\n# Step 3:\\nYou've now performed a successful build and familiarized yourself with the @codebase. Your next imperative is view the codebase through the lense of a brilliant (expert) SEO engineer, then create a short list of the most critical changes we need to make before bulding prod and deploying the website.\\n# Step 4:\\nYou've now performed a successful build and provided your SEO analysis of the codebase. Your next imperative is to leverage your knowledge of the codebase's fundamental structure and underlying guiding principles to ensure you're prepared for implementing the proposed SEO enhancements in a systematic order while retaining existing functionality and ensuring maximal clarity and alignment before making any changes.\\n# Step 5:\\nPlease systematically incorporate your proposed enhancements while ensuring existing functionality is retained, clarity is maximized, and alignment/coherence is maintained.\\n# Step 6:\\nPlease conduct a thorough final review to confirm seamless integration of all enhancements, ensuring that it is **truly** prepared for deployment.\\n```\\n\\n## Goal\\n\\nThe new sequence i'm tasking you to create needs to adhere to the established principles of the system, and should be able to take content-specific inputs and \\\"shape them\\\" into generalized prompts specifically (and meticulously) designed for use with autonomous agents. here's an example input:\\n```\\nllm_instructions_for_sublimetext_plugin='''You’re an expert Sublime Text plugin developer with a deep understanding of Python and the Sublime Text API. You specialize in creating intuitive and efficient plugins that enhance user productivity and streamline workflows. You possess deep knowledge of the Sublime Text API and its ecosystem (and best practices), along with a strong understanding of user needs and developer workflows.\\\\n\\\\n- Sublime Layout\\\\n    > Lagre Layouts\\\\n        > Lagre Regelsett knyttet til Layout (hvilke tabs som skal hvor, hvilke som skal lukkes, osv)\\\\n            > F.eks. `alltid flytt [\\\"main.py\\\", \\\"*.py.bak\\\"] til layout.4.index.2`\\\\n\\\\n    > Kan videreutvikles til en standard som automatisk opprettholdes ved alle (.sublime-project) prosjekter'''\\n```\\n\\nGeneralisert pipeline som omformer hvilken som helst spesifikasjon til **autonomous‑agent‑klare kodebase‑instruksjoner**\\n\\n*(én fil per trinn; følg navngiving 8005‑a/b/c/d/e‑\\\\<beskrivende‑navn>.md)*\\n\\n---\\n\\n```markdown\\n[Context Extractor] Ditt mål er **ikke** å løse brukerens forespørsel, men å isolere all praktisk kontekst (domene, verktøy, filstier, ønsket effekt). `{role=context_extractor; input=[spec:any]; process=[identifiser_domene_og_miljø(), trekk_ut_arbeidsrot(@codebase?), hent_eksplisitte_objektiver(), detekter_implisitte_brukskrav(), fjern_jargon_og_metaforer()]; constraints=[ingen_generering_av_løsninger]; output={context_profile:dict(domene:str?, root:str?, mål:list, krav:list)}}`\\n```\\n\\n---\\n\\n```markdown\\n[Task Vectorizer] Ditt mål er **ikke** å beskrive konteksten på nytt, men å oversette context_profile til en sekvens av atomiske, kodebase‑orienterte oppgaver. `{role=task_vectorizer; input=[context_profile:dict]; process=[del_inn_objektiver_i_diskrete_handlinger(), prefiksér_hver_handling_med_@codebase_sti_når_aktuelt(), bruk_presise_verber(identifiser|eliminer|konsolider), sørg_for_ordre_etter_avhengighet()], constraints=[ingen_overlappende_oppgaver(), unngå_kosmetiske_endringer_med_mindre_nødvendig], output={task_plan:list(str)}}`\\n```\\n\\n---\\n\\n```markdown\\n[Constraint Mapper] Ditt mål er **ikke** å endre task_plan, men å utlede eksplisitte rammer som må overholdes. `{role=constraint_mapper; input=[context_profile:dict, task_plan:list]; process=[kartlegg_funskjonelle_begrensninger(API, UI, legacy), fastslå_equivalence_krav(test, bench), identifiser_forbudsregler(kosmetikk, tredjepart), formuler_audit_logging_policy()], constraints=[ingen_duplisering_av_eksisterende_krav()], output={constraint_set:list(str)}}`\\n```\\n\\n---\\n\\n```markdown\\n[Prompt Assembler] Ditt mål er **ikke** å analysere innholdet, men å smi en ferdig agent‑prompt. `{role=prompt_assembler; input=[task_plan:list, constraint_set:list]; process=[bygg_inn_kort_intro_om_agentrolle(), list_task_plan_med_nummerering(), legg_til_constraint_set_som_obligatoriske_regler(), avslutt_med_valideringsmål(funke_eq, audit_ok)], constraints=[maks_300_ord, tydelig_seksjonsdeling(###)], output={agent_prompt:str}}`\\n```\\n\\n---\\n\\n```markdown\\n[Prompt Validator] Ditt mål er **ikke** å forbedre prompten kreativt, men å verifisere at den kan kjøres direkte av en autonom kode‑assistent. `{role=prompt_validator; input=[agent_prompt:str]; process=[sjekk_ordgrense(<=300), bekreft_@codebase_referanser_korrekt(), valider_nummerert_task_rekkefølge(), sikre_constraint_set_tilstede(), kontrollér_tydelig_goal_negation_i_intro()], constraints=[ingen_ekstra_ord, ingen_semantisk_endring], output={validated_prompt:str}}`\\n```\\\"```'\\n\\n\",\n  \"enhancement_score\": 1.0,\n  \"flaw_analysis\": \"The so-called enhancement substantially dilutes the specificity and functional rigor embedded in the original. Core elements misplaced or lost include: (1) the strictly enforced stepwise role compartmentalization present in the original, which prohibits agent improvisation and mandates harsh separation of contextual extraction, task disaggregation, constraint formation, prompt assembly, and validation—this is slackened in the enhanced prompt, resulting in guidance that is ambiguously merged and cross-polluted; (2) the original's acute focus on codebase-rooted workflows, such as explicit pathing with '@codebase', strict action verb requirements, and prohibition of cosmetic/semantic drift, degenerates into vague references and general operational advice; (3) the mandatory use of named, partitioned markdown files with explicit role and I/O schemata is softened, likely leading to uneven or inconsistent implementation. Information loss is acute: nuances around domain-extraction, identification of explicit/implicit requirements, and validation routines (including word limits and strict format checks) are blurred or generalized. Clarity is eroded by overlong sentences and broad descriptions that lack binding force—exemplified by the meandering language used in documenting supposedly 'transformation modules' as opposed to the original's rigorously process-driven roles. The ambiguity score is 8—a result of high process cross-talk, faded constraint boundaries, unclear role separation, and weak enforceability. Noise is introduced through imprecise mandates and lack of crisp, mechanical instruction—substituting semantic sharpness with bureaucratic expansiveness. The original's consistent, technical register devolves into semi-formal, mashed-together prose. Impact is catastrophically diluted: instead of producing instructions that exert tight control over autonomous agent behavior and guarantee process traceability, the enhanced version leaves excessive interpretive leeway, undermining auditability and agent readiness. Coherence decays as stepwise transitions become blended, outputs and inputs are not precisely aligned, and definitions and process logics are insufficiently documented relative to the original's obsessive detail. Justification for low score: the enhancement introduces ambiguity, blurs required distinctions, muddles sequence ordering, and saps the procedural discipline essential for reliable agent deployment. It is verbose, unfocused, and unfit to guide high-stakes autonomous code transformation.\",\n  \"alternative_enhancements\": [\n    \"Rigidly reinforce stepwise role partition: For each module, specify agent function, I/O schema, and transformation boundaries. Mandate strict segregation to avoid cross-contamination of context extraction, tasking, constraint logic, prompt assembly, and validation.\",\n    \"Restore all explicit technical constraints: Insist on use of @codebase prefixing, formal action verb lists, non-overlapping atomicity in tasks. Forbid cosmetic/naming changes unless baked into functional objectives and audit-tracked.\",\n    \"Reinstate rigorous validation rubric: Codify granular checks (e.g., word limit, section headers, explicit negation statements, semantic non-drift) and require precise output schemas. Document all pipeline process logics for auditability—explicitly prohibiting discretionary agent behavior.\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"goal_map\": [\n    {\n      \"Explicit Tasks\": [\n        \"Create a new, generalized instruction sequence to convert arbitrary inputs into 'autonomous-agent-ready' prompts/instructions.\",\n        \"Ensure these prompts are suitable for use by autonomous coding assistants (such as cursor, vscode+cline/augment/etc).\",\n        \"Adhere to established system/principles found across the extensively detailed example and system prompts.\",\n        \"Produce a stepwise pipeline that transforms content-specific technical/functional specifications—or high-level instructions—into codebase-oriented agent prompts.\",\n        \"The pipeline should operate over any kind of user or system input and workflow (not limited to the provided SEO or plugin examples).\",\n        \"Output the pipeline as a clearly structured, filename-per-step, markdown prompt sequence, with explicit role, I/O, process, constraints, and output for each step.\"\n      ]\n    },\n    {\n      \"Hidden/Unstated Assumptions\": [\n        \"The resulting instructional pipeline is for *general use*: it should not incorporate knowledge specific to plugins, SEO, or any single domain.\",\n        \"Pipelines should be modular: Each step handles a unique aspect of the transformation and passes output in a chain.\",\n        \"Output format (markdown, file-per-step) is important for compatibility with agent frameworks.\",\n        \"The system's workflow and constraints—role separation, I/O contract formalization, avoidance of unwanted (cosmetic, stylistic) changes, process transparency—are non-negotiable.\",\n        \"The pipeline must support strict context extraction, action decomposition, constraint mapping, assembly, and validation/check phases.\",\n        \"Agents must be prevented from taking shortcuts (e.g., jumping to solution or blending steps).\",\n        \"Constraints and objectives may come from both explicit user input and implicit requirements drawn from prior knowledge or best practices.\",\n        \"Workflow must be audit- and validation-friendly, for reproducibility and traceability.\",\n        \"Any technical/contextual input must be transformable into a codebase-rooted prompt regardless of complexity.\"\n      ]\n    },\n    {\n      \"Sub-Goals\": [\n        \"Define and formalize the modular steps needed in the pipeline: Context extraction, task decomposition, constraint mapping, prompt assembly, prompt validation.\",\n        \"For each module: Specify its role, required input, transformation process, constraints (what it must or must not do), and schema for output.\",\n        \"Codify how context extraction works for any technical input: How to identify the domain, tooling, goals, requirements, etc., without leaking into solution territory.\",\n        \"Define how to split extracted objectives into atomic, codebase-oriented tasks, ensuring modular, non-overlapping, dependency-ordered instructions with correct scoping (e.g., @codebase pathing).\",\n        \"Specify rules for strict constraint extraction, including functional equivalence, prohibition of cosmetic/naming changes, audit policies, and respecting protected areas.\",\n        \"Mandate a process for assembling a final, agent-usable prompt: Concise yet comprehensive, with clear role assignment, input expectations, explicit tasks, enforced constraints, and validation goals.\",\n        \"Construct validation logic to check—without revision or creativity—whether the final output matches the specification: word count, formatting, section labels, negation statements, input/output compliance, etc.\",\n        \"All steps must be applicable to any input (from detailed plugin specs to vague requests or high-level goals), ensuring generalization.\",\n        \"Document the process in strict markdown templates, with explicit role labeling and adherence to filename conventions.\",\n        \"Ensure traceability and auditability throughout: Each transformation must be explainable and reversible, with rationale when possible.\",\n        \"No generative or solution-producing behavior in context extraction, task decomposition, constraint mapping, or validation—only in assembly step do instructions combine for agent use.\"\n      ]\n    },\n    {\n      \"Potential Blockers\": [\n        \"Lack of clarity on how to abstract specific contextual extraction for truly arbitrary user inputs (what if the context is ambiguous or very incomplete?).\",\n        \"Risk of the steps bleeding into each other (e.g., context extractor suggesting solutions, vectorizer including constraints, etc.).\",\n        \"Difficulty in ensuring agent 'autonomy-readiness' for all platforms/environments targeted (varied agent capabilities, reserved keywords, etc.).\",\n        \"Maintaining atomicity, non-overlap, and correct ordering in task decomposition—especially for highly entangled requirements.\",\n        \"Ensuring the modular pipeline remains generalizable across domains (from web applications to data pipelines to local scripts, etc.).\",\n        \"Upholding strict constraint boundaries in each step, with no leakage (e.g., context extractor must not reference output structure beyond its role).\",\n        \"Potential issues with auditability if inputs are ambiguous, under-specified, or not codebase-rooted.\",\n        \"Ensuring word count, format, and other meta-constraints are always enforced (even as tasks are arbitrarily complex or verbose).\",\n        \"Interpreting and carrying over not just explicit but also implicit constraints/directives, without hallucination or drift.\",\n        \"Adhering exactly to file naming conventions and markdown role structure for maximal compatibility and agent interpretability.\"\n      ]\n    }\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`",
      "step": "a",
      "title": "Instruction Converter",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"# lets try something new\\nyour new goal is to create a new generalized instruction sequence specifically designed to transform inputs into autonomous-agent-ready prompts/instructions, for use with autonomous coding assistants such as cursor, vscode+cline/augment/etc. these prompts are typically written in a \\\"codebase-oriented\\\" manner, where the `@codebase` represents the root (of a project/workspace/directory), examples:\\n```\\n- Identify all instances of structural redundancy within the codebase by defining criteria specific to architectural and implementation duplication or overlap.\\n- Survey the entire codebase to locate and enumerate all occurrences of structural redundancy, distinguishing these from non-structural (stylistic or cosmetic) issues.\\n- Eliminate all forms of identified structural redundancy while ensuring that no existing user-visible behavior, API contract, or edge-case handling is altered.\\n- Define and catalog all components, modules, files, and subsystems, distinguishing and marking each as essential or excess according to direct functional mapping.\\n- Consolidate and remove excess components while strictly preserving system function, minimizing peripheral impact.\\n- For every modification, justify and log the intention, action, and rationale within an audit/logging system that provides real-time traceability and is prepared for retroactive auditability.\\n- Implement an audit trail with logs that are timestamped, attributable, and tamper-evident, ensuring that every codebase change is properly documented and traceable to specific authors or automated processes.\\n- Prohibit cosmetic, stylistic, or naming changes unless they are inseparably tied to the removal of redundancy or bloat, and document any exceptions explicitly within the audit log.\\n- Enforce strict detection and flagging mechanisms to prevent disallowed cosmetic changes, allowing exceptions only when justified as essential for redundancy or bloat removal, with explicit rationale provided.\\n- Require comprehensive functional equivalence verification—using robust automated testing, benchmarking, or behavioral validation—for all modifications prior to acceptance.\\n- Establish rigorous criteria and tool-supported thresholds for successful functional equivalence, and automate testing and verification procedures.\\n- Automate roll-back and reversion mechanisms for any changes that fail functional equivalence or cause unintended side-effects, ensuring all reversion is prompt and complete.\\n- Develop and apply explicit minimal-impact criteria for all modification actions, constraining all interventions to only those addressing superfluous complexity.\\n- Mandate review and authorization checkpoints for each intended change, requiring concise rationale and authorization documentation at every intervention point.\\n- Ensure every modification is tightly scoped, minimizing the blast radius, and demonstrate that the action introduces no unnecessary impact beyond its core objective.\\n- Systematically and iteratively analyze all constituent elements of the codebase to detect and categorize all forms of redundancy, duplication, and obsolescence. Continuously identify and categorize all genuine redundancies and obsolescence while strictly prohibit unnecessary or cosmetic changes, with automated flagging and explicit justification required for any exceptions. Systematically and iteratively inspect all components within the system/environment to identify and remove elements that are unnecessary, duplicated, or outdated. Guarantee that all essential behaviors and interfaces remain unchanged by validating functional equivalence after each modification. Forbid changes that alter functionality unless directly required to remove redundancy.\\n- Map all references and usages of each element across all contexts.\\n- Analyze the entire codebase systematically and iteratively to maximize coherence, maintainability, and clarity by cataloging every element (file, module, function, asset, interface), mapping all references and usages, and detecting all forms of redundancy, duplication, and obsolescence—including exact and functional duplication and unused or outdated elements. Categorize findings using explicit, evidence-based metrics. Eliminate or consolidate only elements confirmed as redundant or obsolete through both automated and manual checks, ensuring no essential behavior or interface is altered. Enforce functional equivalence and preserve structural integrity after each change by performing comprehensive, codebase-wide validation and calibration using explicit, quantitative outputs (including git diff analysis before and after modifications). Prevent the introduction of new redundancy or inconsistency by continuously analyzing for and correcting systemic issues, ensuring systematic consistency across all codebase sections (e.g., /src/sections), and adhering to best practices throughout. Centralize repeated components or logic where high-value consolidation is possible without functional loss. Use the provided directory tree as the single source of truth for validation, and transparently document all processes, metrics, and validation outcomes. Prohibit modification of protected, legacy, or third-party code except if directly related to resolving redundancy or obsolescence.\\n- Maximize codebase coherence, maintainability, and clarity by systematically eliminating all forms of redundancy, duplication, and obsolescence, while ensuring functional equivalence and structural integrity. Prevent the persistence or inadvertent introduction of redundancy, duplication, obsolescence, or inconsistency within the codebase, and prohibit changes that alter essential behaviors or interfaces. Systematically maximize codebase coherence, maintainability, and clarity by eliminating all forms of redundancy, duplication, and obsolescence while strictly preserving functional equivalence, interface stability, and operational integrity across the entire codebase. Achieve maximal codebase coherence, maintainability, and clarity by systematically eliminating all forms of redundancy, duplication, and obsolescence, while preserving essential behaviors, interfaces, and structural integrity. Enforce continuous validation, consistency, and adherence to best practices throughout an iterative, evidence-driven consolidation and verification process.\\n- Please perform a rapid assessment of the project directory structure and identify the unique workflow and core functionality of the current utility. I'm particularly interested in a mermaid-diagram that shows how all the \\\"branches\\\" of the codebase converge. Utilize appropriate MCPs as necessary (e.g., 'filesystem-mcp').\\n- You're inside the current project and have access to the full codebase (and you interaction history with it). The current project/codebase is complex and is difficult to fully comprehend (when starting from scratch), and your contextwindow has grown large-so we need to find a way for you to express your accumulated knowledge in a way that i can copy and paste into a new context-window to transfer the most essential insights you've gathered throughout this (and previous) session(s).\\n- Aggregate all accumulated insights and knowledge about the current project/codebase from available context. Distill this information to identify core modules, architectural patterns, workflows, and points of integration. Condense the findings into a succinct yet comprehensive summary that highlights how these parts function together and converge toward the project's goals. Formulate the output as a single, optimized directive that encapsulates the codebase's integral components and operational cohesion. Present this synthesis as a set of clear, actionable commands or instructions, specifically phrased to instruct the coding assistant, not merely describe or report.\\n- Transform your accumulated, session-wide understanding of the current, complex codebase—gleaned from comprehensive context and full interaction history—into an actionable, instructive knowledge transfer protocol. Synthesize and distill all integral structural modules, essential architectural patterns, and key workflows, with special focus on their dynamic integration and functional convergence toward project objectives. Formulate a highly-optimized, directive-based set of precise commands targeted explicitly at coding assistants, ensuring exhaustive coverage of all major components, their relationships, and operational cohesion. The resulting synthesis must facilitate seamless portability: structure it for immediate copy-paste into any new context window, preserving the depth, interconnectivity, and systemic flow of the original codebase. Prioritize succinctness without compromising essential nuance, and express the entirety as a singular, comprehensive operational directive that primes the assistant for full-spectrum project navigation and intervention.\\n- Identify the fundamental structure, guiding principles, and core purpose of the project/codebase.\\n- Please familiarize yourself with the current @codebase. Your main imperative is to *actually* understand the codebase in a way where you're able to recognize meta-patterns and destroy complexity with simplicity and elegance, while capable of navigating through its *existing patterns and convergence* (never add unnecessary complexity). Deliver an ultra-concise project summary that rigorously defines scope, states objectives, and maximizes directional clarity. Provide a short summary of this project, including a bullet-point list of the core components and primary dependencies.\\n- Study the current codebase until you can identify core meta-patterns, and eliminate unnecessary complexity by applying simplicity within existing structures. Generate an ultra-concise project summary: define scope, objectives, and clarify direction. Summarize this project.\\n- Analyze the @codebase to uncover dominant patterns and remove complexity whenever possible, strictly adhering to present structures. Provide a succinct project overview with defined scope and explicit objectives. Include a brief project summary.\\n- Gain practical mastery over @codebase patterns, minimize complexity, and ensure all actions align with existing architecture. Supply a concise, clearly scoped project summary outlining objectives and key directions. Add a short summary.\\n- Absorb the current @codebase to the extent that you recognize meta-patterns and eradicate complexity, always favoring elegant simplicity. Navigate established patterns and points of convergence without adding unneeded complexity. Produce a rigorously concise project summary clearly stating scope and objectives for maximal directional clarity.\\n- Systematically dissect the codebase to extract core meta-patterns, eliminate all unnecessary complexity through elegant design, operate strictly within established architectural convergence, and deliver an ultra-concise project summary that rigorously defines scope and objectives with maximal clarity.\\n- Please familiarize yourself thoroughly with the current @codebase, beginning by systematically reviewing all markdown-files. Your core imperative is to *genuinely* understand the codebase to the degree where you can identify 'meta-patterns' and simplify complexity with elegance—always using and clarifying existing patterns, never introducing unnecessary complexity. Deliver an ultra-concise project summary that rigorously defines scope, states objectives, maximizes directional clarity, and highlights key meta-patterns discovered. Provide a short summary of this project.\\n- Achieve expert-level comprehension of the codebase by systematically analyzing all markdown files to extract meta-patterns, construct a robust contextual knowledgebase, and deliver an ultra-concise summary that rigorously defines scope and objectives, ensuring maximal clarity and alignment before making any changes.\\n- Fully internalize the @codebase to uncover meta-patterns and replace complexity with lucid simplicity. Remain aligned with existing convergence patterns, never increasing system intricacy. Generate a direct, sharply-focused summary that details scope and explicit objectives.\\n- Please thoroughly familiarize yourself with the current @codebase—your primary imperative is to understand the codebase so deeply that you could confidently make changes as if you were its original architect.\\n```\\n\\nhere's an example to show how generalized sequential prompts can be used effectively (on autonomous agents):\\n```\\n# Step 1:\\nFamiliarize yourself with the current @codebase. Comprehend the codebase in depth. Identify the fundamental structure, guiding principles, and core purpose.\\n# Step 2:\\nDive deeper to comprehend the intricate logic and inner workings that drive the project's functionality. As you explore, pay close attention to identifying the codebase's fundamental structure, underlying guiding principles, and its core purpose, ensuring you grasp both how and why the system is designed as it is. hen verify through a successful build.\\n# Step 3:\\nYou've now performed a successful build and familiarized yourself with the @codebase. Your next imperative is view the codebase through the lense of a brilliant (expert) SEO engineer, then create a short list of the most critical changes we need to make before bulding prod and deploying the website.\\n# Step 4:\\nYou've now performed a successful build and provided your SEO analysis of the codebase. Your next imperative is to leverage your knowledge of the codebase's fundamental structure and underlying guiding principles to ensure you're prepared for implementing the proposed SEO enhancements in a systematic order while retaining existing functionality and ensuring maximal clarity and alignment before making any changes.\\n# Step 5:\\nPlease systematically incorporate your proposed enhancements while ensuring existing functionality is retained, clarity is maximized, and alignment/coherence is maintained.\\n# Step 6:\\nPlease conduct a thorough final review to confirm seamless integration of all enhancements, ensuring that it is **truly** prepared for deployment.\\n```\\n\\n## Goal\\n\\nThe new sequence i'm tasking you to create needs to adhere to the established principles of the system, and should be able to take content-specific inputs and \\\"shape them\\\" into generalized prompts specifically (and meticulously) designed for use with autonomous agents. here's an example input:\\n```\\nllm_instructions_for_sublimetext_plugin='''You’re an expert Sublime Text plugin developer with a deep understanding of Python and the Sublime Text API. You specialize in creating intuitive and efficient plugins that enhance user productivity and streamline workflows. You possess deep knowledge of the Sublime Text API and its ecosystem (and best practices), along with a strong understanding of user needs and developer workflows.\\\\n\\\\n- Sublime Layout\\\\n    > Lagre Layouts\\\\n        > Lagre Regelsett knyttet til Layout (hvilke tabs som skal hvor, hvilke som skal lukkes, osv)\\\\n            > F.eks. `alltid flytt [\\\"main.py\\\", \\\"*.py.bak\\\"] til layout.4.index.2`\\\\n\\\\n    > Kan videreutvikles til en standard som automatisk opprettholdes ved alle (.sublime-project) prosjekter'''\\n```\\n\\nGeneralisert pipeline som omformer hvilken som helst spesifikasjon til **autonomous‑agent‑klare kodebase‑instruksjoner**\\n\\n*(én fil per trinn; følg navngiving 8005‑a/b/c/d/e‑\\\\<beskrivende‑navn>.md)*\\n\\n---\\n\\n```markdown\\n[Context Extractor] Ditt mål er **ikke** å løse brukerens forespørsel, men å isolere all praktisk kontekst (domene, verktøy, filstier, ønsket effekt). `{role=context_extractor; input=[spec:any]; process=[identifiser_domene_og_miljø(), trekk_ut_arbeidsrot(@codebase?), hent_eksplisitte_objektiver(), detekter_implisitte_brukskrav(), fjern_jargon_og_metaforer()]; constraints=[ingen_generering_av_løsninger]; output={context_profile:dict(domene:str?, root:str?, mål:list, krav:list)}}`\\n```\\n\\n---\\n\\n```markdown\\n[Task Vectorizer] Ditt mål er **ikke** å beskrive konteksten på nytt, men å oversette context_profile til en sekvens av atomiske, kodebase‑orienterte oppgaver. `{role=task_vectorizer; input=[context_profile:dict]; process=[del_inn_objektiver_i_diskrete_handlinger(), prefiksér_hver_handling_med_@codebase_sti_når_aktuelt(), bruk_presise_verber(identifiser|eliminer|konsolider), sørg_for_ordre_etter_avhengighet()], constraints=[ingen_overlappende_oppgaver(), unngå_kosmetiske_endringer_med_mindre_nødvendig], output={task_plan:list(str)}}`\\n```\\n\\n---\\n\\n```markdown\\n[Constraint Mapper] Ditt mål er **ikke** å endre task_plan, men å utlede eksplisitte rammer som må overholdes. `{role=constraint_mapper; input=[context_profile:dict, task_plan:list]; process=[kartlegg_funskjonelle_begrensninger(API, UI, legacy), fastslå_equivalence_krav(test, bench), identifiser_forbudsregler(kosmetikk, tredjepart), formuler_audit_logging_policy()], constraints=[ingen_duplisering_av_eksisterende_krav()], output={constraint_set:list(str)}}`\\n```\\n\\n---\\n\\n```markdown\\n[Prompt Assembler] Ditt mål er **ikke** å analysere innholdet, men å smi en ferdig agent‑prompt. `{role=prompt_assembler; input=[task_plan:list, constraint_set:list]; process=[bygg_inn_kort_intro_om_agentrolle(), list_task_plan_med_nummerering(), legg_til_constraint_set_som_obligatoriske_regler(), avslutt_med_valideringsmål(funke_eq, audit_ok)], constraints=[maks_300_ord, tydelig_seksjonsdeling(###)], output={agent_prompt:str}}`\\n```\\n\\n---\\n\\n```markdown\\n[Prompt Validator] Ditt mål er **ikke** å forbedre prompten kreativt, men å verifisere at den kan kjøres direkte av en autonom kode‑assistent. `{role=prompt_validator; input=[agent_prompt:str]; process=[sjekk_ordgrense(<=300), bekreft_@codebase_referanser_korrekt(), valider_nummerert_task_rekkefølge(), sikre_constraint_set_tilstede(), kontrollér_tydelig_goal_negation_i_intro()], constraints=[ingen_ekstra_ord, ingen_semantisk_endring], output={validated_prompt:str}}`\\n```\\\"```'\\n\\n\",\n  \"goal_map\": [\n    {\n      \"Explicit Tasks\": [\n        \"Create a new, generalized instruction sequence to convert arbitrary inputs into 'autonomous-agent-ready' prompts/instructions.\",\n        \"Ensure these prompts are suitable for use by autonomous coding assistants (such as cursor, vscode+cline/augment/etc).\",\n        \"Adhere to established system/principles found across the extensively detailed example and system prompts.\",\n        \"Produce a stepwise pipeline that transforms content-specific technical/functional specifications—or high-level instructions—into codebase-oriented agent prompts.\",\n        \"The pipeline should operate over any kind of user or system input and workflow (not limited to the provided SEO or plugin examples).\",\n        \"Output the pipeline as a clearly structured, filename-per-step, markdown prompt sequence, with explicit role, I/O, process, constraints, and output for each step.\"\n      ]\n    },\n    {\n      \"Hidden/Unstated Assumptions\": [\n        \"The resulting instructional pipeline is for *general use*: it should not incorporate knowledge specific to plugins, SEO, or any single domain.\",\n        \"Pipelines should be modular: Each step handles a unique aspect of the transformation and passes output in a chain.\",\n        \"Output format (markdown, file-per-step) is important for compatibility with agent frameworks.\",\n        \"The system's workflow and constraints—role separation, I/O contract formalization, avoidance of unwanted (cosmetic, stylistic) changes, process transparency—are non-negotiable.\",\n        \"The pipeline must support strict context extraction, action decomposition, constraint mapping, assembly, and validation/check phases.\",\n        \"Agents must be prevented from taking shortcuts (e.g., jumping to solution or blending steps).\",\n        \"Constraints and objectives may come from both explicit user input and implicit requirements drawn from prior knowledge or best practices.\",\n        \"Workflow must be audit- and validation-friendly, for reproducibility and traceability.\",\n        \"Any technical/contextual input must be transformable into a codebase-rooted prompt regardless of complexity.\"\n      ]\n    },\n    {\n      \"Sub-Goals\": [\n        \"Define and formalize the modular steps needed in the pipeline: Context extraction, task decomposition, constraint mapping, prompt assembly, prompt validation.\",\n        \"For each module: Specify its role, required input, transformation process, constraints (what it must or must not do), and schema for output.\",\n        \"Codify how context extraction works for any technical input: How to identify the domain, tooling, goals, requirements, etc., without leaking into solution territory.\",\n        \"Define how to split extracted objectives into atomic, codebase-oriented tasks, ensuring modular, non-overlapping, dependency-ordered instructions with correct scoping (e.g., @codebase pathing).\",\n        \"Specify rules for strict constraint extraction, including functional equivalence, prohibition of cosmetic/naming changes, audit policies, and respecting protected areas.\",\n        \"Mandate a process for assembling a final, agent-usable prompt: Concise yet comprehensive, with clear role assignment, input expectations, explicit tasks, enforced constraints, and validation goals.\",\n        \"Construct validation logic to check—without revision or creativity—whether the final output matches the specification: word count, formatting, section labels, negation statements, input/output compliance, etc.\",\n        \"All steps must be applicable to any input (from detailed plugin specs to vague requests or high-level goals), ensuring generalization.\",\n        \"Document the process in strict markdown templates, with explicit role labeling and adherence to filename conventions.\",\n        \"Ensure traceability and auditability throughout: Each transformation must be explainable and reversible, with rationale when possible.\",\n        \"No generative or solution-producing behavior in context extraction, task decomposition, constraint mapping, or validation—only in assembly step do instructions combine for agent use.\"\n      ]\n    },\n    {\n      \"Potential Blockers\": [\n        \"Lack of clarity on how to abstract specific contextual extraction for truly arbitrary user inputs (what if the context is ambiguous or very incomplete?).\",\n        \"Risk of the steps bleeding into each other (e.g., context extractor suggesting solutions, vectorizer including constraints, etc.).\",\n        \"Difficulty in ensuring agent 'autonomy-readiness' for all platforms/environments targeted (varied agent capabilities, reserved keywords, etc.).\",\n        \"Maintaining atomicity, non-overlap, and correct ordering in task decomposition—especially for highly entangled requirements.\",\n        \"Ensuring the modular pipeline remains generalizable across domains (from web applications to data pipelines to local scripts, etc.).\",\n        \"Upholding strict constraint boundaries in each step, with no leakage (e.g., context extractor must not reference output structure beyond its role).\",\n        \"Potential issues with auditability if inputs are ambiguous, under-specified, or not codebase-rooted.\",\n        \"Ensuring word count, format, and other meta-constraints are always enforced (even as tasks are arbitrarily complex or verbose).\",\n        \"Interpreting and carrying over not just explicit but also implicit constraints/directives, without hallucination or drift.\",\n        \"Adhering exactly to file naming conventions and markdown role structure for maximal compatibility and agent interpretability.\"\n      ]\n    }\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"instruction_format\": \"Create a generalized multi-step instruction pipeline to transform any input specification into autonomous-agent-ready codebase instructions. Structure the pipeline as discrete, modular steps, each documented in a separate markdown file using a filename convention (e.g., 8005-a/b/c/d/e-<step-name>.md). For every step, explicitly define the role, required input, processing method, constraints, and output format. Implement the following sequence:\\n\\n1. [Context Extractor]: Isolate and extract all practical context—such as domain, tools, workspace root (@codebase), explicit objectives, and both explicit and implicit requirements—without generating solutions. Remove jargon and metaphors. Output a structured context profile containing domain, project root, goal list, and requirement list.\\n\\n2. [Task Vectorizer]: Translate the context profile into a sequence of discrete, atomic, codebase-oriented tasks. Prefix tasks with relevant @codebase paths, use precise action verbs (e.g., identify, eliminate, consolidate), and order tasks according to dependencies. Prevent task overlap or unnecessary cosmetic actions. Output as an ordered list of tasks.\\n\\n3. [Constraint Mapper]: Derive all explicit functional, behavioral, and policy constraints from the context and the task plan. Include requirements such as API stability, UI boundaries, legacy code protections, equivalence criteria (testing, benchmarking), and audit/logging policies. Exclude duplicate or redundant constraints. Output as a constraint set list.\\n\\n4. [Prompt Assembler]: Construct the final agent prompt without analyzing or revising its content. Insert a brief agent role introduction, enumerate the task plan, append the constraint set as mandatory rules, and conclude with clear validation goals (e.g., functional equivalence, audit compliance). Ensure organization with distinct sections and adherence to a strict word/length limit. Output as a single, agent-ready prompt string.\\n\\n5. [Prompt Validator]: Verify the assembled prompt is compliant for direct agent execution. Check that formatting, word count, @codebase references, task order, section labeling, and constraint presence meet specifications. Ensure the introduction contains a clear goal-negation statement. Do not modify semantics. Output the validated prompt string.\\n\\nApply this modular pipeline to any technical, functional, or ambiguous input. Maintain strict procedural separation between steps. Avoid solution generation outside of the assembly step. Ensure every transformation is traceable, auditable, and documented in the prescribed markdown format for compatibility with autonomous coding agents.\"\n}",
        }
      }
    }