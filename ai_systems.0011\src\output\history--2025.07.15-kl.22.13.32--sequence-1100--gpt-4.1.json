  {
  "initial_prompt": "### Example Prompt:\n\"Low angle static shot: The camera is angled up at a woman wearing all orange as she stands in a tropical rainforest with colorful flora. The dramatic sky is overcast and gray.\"\n\n- **Tip**: Repeating or reinforcing key ideas in different sections of your prompt improves adherence.\n\n---\n\n## Sample Prompts\n\n### 1. Seamless Transitions\n- **Prompt**: \"Continuous hyperspeed FPV footage: The camera seamlessly flies through a glacial canyon to a dreamy cloudscape.\"\n\n- **Image Description**: A dynamic FPV sequence transitioning smoothly through a serene glacial environment into ethereal clouds.\n\n### 2. Oceanic Glow\n- **Prompt**: \"A glowing ocean at night with bioluminescent creatures underwater. The camera starts with a macro close-up of a glowing jellyfish and then expands to reveal the entire ocean lit up under a starry sky.\"\n\n- **Image Description**: A glowing jellyfish in vivid detail transitions to reveal a bioluminescent ocean beneath a sparkling starry night.\n\n### 3. Title Cards\n- **Prompt**: \"A title screen with dynamic movement. The scene starts at a colorful paint-covered wall. Suddenly, black paint pours on the wall to form the word 'Runway'. The dripping paint is detailed, textured, and lit with cinematic brilliance.\"\n\n- **Image Description**: A vibrant wall turns dramatic as black paint forms bold, textured text under vivid lighting.\n\n---\n\n## Key Visual Examples\n\n### 1. Unique Camera Movements\n- **Prompt**: \"Continuous hyperspeed FPV footage through a man's mouth into an epic landscape.\"\n- **Image Description**: A surreal transition through the interior of a human mouth, revealing a panoramic landscape.\n\n### 2. Dreamlike Pillow Fort\n- **Prompt**: \"A pillow fort in a cozy living room made from quilts and pillows. The camera smoothly zooms into the entrance, revealing an ancient castle interior.\"\n- **Image Description**: A whimsical living room transforms into an enchanting castle.\n\n### 3. Fire and Chaos\n- **Prompt**: \"A vortex of fire swirling through an abandoned warehouse.\"\n- **Image Description**: Flames spiral dynamically in a desolate, dramatic warehouse setting.\n\n---\n\n## Lighting, Styles, and Effects\n\n### Lighting Styles\n- **Diffused Lighting**: Soft, even lighting for atmospheric visuals.\n- **Silhouette**: Strong backlighting creating dramatic outlines.\n- **Lens Flare**: Bright reflections adding cinematic depth.\n- **Backlit**: Subject lit from behind for dramatic effects.\n\n### Movement Types\n- **Dynamic Motion**: High-energy, flowing camera transitions.\n- **Slow Motion**: Emphasizes intricate, slowed-down details.\n- **Timelapse**: Rapid transitions showcasing passing time.\n\n---\n\n## Text and Graphic Styles\n\n### Text Prompts\n1. **ATOMIC in Flames**: Over an erupting volcano, 'ATOMIC' is depicted in fiery text.\n2. **Icy Title**: 'ATOMIC' frozen in ice over an iceberg.\n3. **Unique Materials**: Text made of spaghetti, evoking whimsy and creativity.\n\n---\n\n## Example Prompts with Descriptions\n\n### 1. Cinematic Wildlife\n- **Prompt**: \"Static close-up shot of a sloth in a tree. Highlight its sleepy eyes and fur with cinematic lighting.\"\n- **Image Description**: A serene close-up of a sloth in its natural habitat.\n\n### 2. Urban Destruction\n- **Prompt**: \"[Scene] Molten lava flowing through city streets [Lighting] Vibrant orange glow [Camera Movement] Sweeping aerial shots.\"\n- **Image Description**: Lava courses dramatically through a cityscape, contrasting fiery tones with the urban environment.\n\n### 3. Vortex of Tanks\n- **Prompt**: \"Grainy WWII footage of a massive vortex made of tanks.\"\n- **Image Description**: A surreal vortex of military tanks under vintage cinematography.\n\n---\n\n## Camera Styles Overview\n\n- **Low Angle**: Emphasizes scale and dominance.\n- **Overhead**: A bird's-eye view for expansive perspective.\n- **FPV (First-Person View)**: Immersive movement through dynamic environments.\n- **Handheld**: Natural and intimate movements, resembling real-life footage.",
  "sequence_id": "1100",
  "results": [
      {
      "instruction": "# Problem Exploder\n\nYour goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\n\n`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`",
      "step": "a",
      "title": "Problem Exploder",
      "input": "### Example Prompt:\n\"Low angle static shot: The camera is angled up at a woman wearing all orange as she stands in a tropical rainforest with colorful flora. The dramatic sky is overcast and gray.\"\n\n- **Tip**: Repeating or reinforcing key ideas in different sections of your prompt improves adherence.\n\n---\n\n## Sample Prompts\n\n### 1. Seamless Transitions\n- **Prompt**: \"Continuous hyperspeed FPV footage: The camera seamlessly flies through a glacial canyon to a dreamy cloudscape.\"\n\n- **Image Description**: A dynamic FPV sequence transitioning smoothly through a serene glacial environment into ethereal clouds.\n\n### 2. Oceanic Glow\n- **Prompt**: \"A glowing ocean at night with bioluminescent creatures underwater. The camera starts with a macro close-up of a glowing jellyfish and then expands to reveal the entire ocean lit up under a starry sky.\"\n\n- **Image Description**: A glowing jellyfish in vivid detail transitions to reveal a bioluminescent ocean beneath a sparkling starry night.\n\n### 3. Title Cards\n- **Prompt**: \"A title screen with dynamic movement. The scene starts at a colorful paint-covered wall. Suddenly, black paint pours on the wall to form the word 'Runway'. The dripping paint is detailed, textured, and lit with cinematic brilliance.\"\n\n- **Image Description**: A vibrant wall turns dramatic as black paint forms bold, textured text under vivid lighting.\n\n---\n\n## Key Visual Examples\n\n### 1. Unique Camera Movements\n- **Prompt**: \"Continuous hyperspeed FPV footage through a man's mouth into an epic landscape.\"\n- **Image Description**: A surreal transition through the interior of a human mouth, revealing a panoramic landscape.\n\n### 2. Dreamlike Pillow Fort\n- **Prompt**: \"A pillow fort in a cozy living room made from quilts and pillows. The camera smoothly zooms into the entrance, revealing an ancient castle interior.\"\n- **Image Description**: A whimsical living room transforms into an enchanting castle.\n\n### 3. Fire and Chaos\n- **Prompt**: \"A vortex of fire swirling through an abandoned warehouse.\"\n- **Image Description**: Flames spiral dynamically in a desolate, dramatic warehouse setting.\n\n---\n\n## Lighting, Styles, and Effects\n\n### Lighting Styles\n- **Diffused Lighting**: Soft, even lighting for atmospheric visuals.\n- **Silhouette**: Strong backlighting creating dramatic outlines.\n- **Lens Flare**: Bright reflections adding cinematic depth.\n- **Backlit**: Subject lit from behind for dramatic effects.\n\n### Movement Types\n- **Dynamic Motion**: High-energy, flowing camera transitions.\n- **Slow Motion**: Emphasizes intricate, slowed-down details.\n- **Timelapse**: Rapid transitions showcasing passing time.\n\n---\n\n## Text and Graphic Styles\n\n### Text Prompts\n1. **ATOMIC in Flames**: Over an erupting volcano, 'ATOMIC' is depicted in fiery text.\n2. **Icy Title**: 'ATOMIC' frozen in ice over an iceberg.\n3. **Unique Materials**: Text made of spaghetti, evoking whimsy and creativity.\n\n---\n\n## Example Prompts with Descriptions\n\n### 1. Cinematic Wildlife\n- **Prompt**: \"Static close-up shot of a sloth in a tree. Highlight its sleepy eyes and fur with cinematic lighting.\"\n- **Image Description**: A serene close-up of a sloth in its natural habitat.\n\n### 2. Urban Destruction\n- **Prompt**: \"[Scene] Molten lava flowing through city streets [Lighting] Vibrant orange glow [Camera Movement] Sweeping aerial shots.\"\n- **Image Description**: Lava courses dramatically through a cityscape, contrasting fiery tones with the urban environment.\n\n### 3. Vortex of Tanks\n- **Prompt**: \"Grainy WWII footage of a massive vortex made of tanks.\"\n- **Image Description**: A surreal vortex of military tanks under vintage cinematography.\n\n---\n\n## Camera Styles Overview\n\n- **Low Angle**: Emphasizes scale and dominance.\n- **Overhead**: A bird's-eye view for expansive perspective.\n- **FPV (First-Person View)**: Immersive movement through dynamic environments.\n- **Handheld**: Natural and intimate movements, resembling real-life footage.",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"goal_map\": [\n    {\n      \"explicit_asks\": [\n        \"Provide example prompts and image descriptions for AI-generated visuals.\",\n        \"Demonstrate how to create complex scene prompts using camera angles, movement, lighting, and unique subject matter.\",\n        \"List styles and effects for lighting, text, graphics, and camera movement.\",\n        \"Make prompts with corresponding image descriptions.\",\n        \"Showcase the use of dynamic transitions and transformation in scenes.\"\n      ],\n      \"hidden_assumptions\": [\n        \"The user understands how prompts relate to image generation tools (such as AI video or image models).\",\n        \"Descriptions should be detailed enough for an AI to interpret, but also concise.\",\n        \"Terminology (e.g. 'FPV', 'low angle', 'timelapse') is familiar to the reader.\",\n        \"The reader is seeking inspiration or templates, not exhaustive documentation or technical guides.\",\n        \"Sample prompts should be visually imaginative and demonstrate creativity.\",\n        \"Lighting descriptions refer to real cinematic techniques, enhancing realism and mood.\",\n        \"Text elements described are meant to be incorporated within visual scenes, not overlaid as captions.\"\n      ],\n      \"sub_goals\": [\n        \"Design varied visual scene prompts that utilize different camera techniques (low angle, FPV, slow motion, aerial).\",\n        \"Include cues about setting/subject, lighting styles, and movement for each prompt.\",\n        \"Pair each prompt with a brief but evocative image description.\",\n        \"Catalog lighting styles with brief explanations (diffused, silhouette, lens flare, etc.).\",\n        \"List example text/graphic styles for inspiration (fiery, icy, spaghetti text).\",\n        \"Provide direct, actionable templates that readers can adapt for their own uses.\",\n        \"Clarify the effect of each camera style on mood and composition.\"\n      ],\n      \"blockers\": [\n        \"Ambiguity in camera or lighting terminology could lead to unclear results if misunderstood.\",\n        \"Readers new to scene-writing for AI visuals may need explicit breakdowns or clarifications.\",\n        \"Lack of standardization between different AI image/video tools in processing prompt language.\",\n        \"Balancing imaginative appeal with technical clarity can be difficult.\",\n        \"Assumption that the user is not seeking actual artwork, but example language—could cause confusion.\",\n        \"The examples shown are not exhaustive; other styles or effects are possible but not represented.\"\n      ]\n    }\n  ]\n}",
        }
      }
    }