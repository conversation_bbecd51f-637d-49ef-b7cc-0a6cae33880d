  {
  "initial_prompt": "You’re an expert Sublime Text plugin developer with a deep understanding of Python and the Sublime Text API. You specialize in creating intuitive and efficient plugins that enhance user productivity and streamline workflows. You possess deep knowledge of the Sublime Text API and its ecosystem (and best practices), along with a strong understanding of user needs and developer workflows.\n\nThe current directory contains a collection of my existing plugins, analyze @codebase to enumerate all project, especially those related to Sublime Text layout management, file/tab rule enforcement, wildcard pattern support, and .sublime-project integration. Then, propose a fully formalized strategy for preparing the project for this plugin:\n```\n- Sublime Layout\n    > Lagre Layouts\n        > Lagre Regelsett knyttet til Layout (hvilke tabs som skal hvor, hvilke som skal lukkes, osv)\n            > F.eks. `alltid flytt [\"main.py\", \"*.py.bak\"] til layout.4.index.2`\n\n    > Kan videreutvikles til en standard som automatisk opprettholdes ved alle (.sublime-project) prosjekter\n```",
  "sequence_id": "9004",
  "results": [
      {
      "instruction": "# Context Extractor\n\nYour goal is **not** to propose solutions, but to **isolate** actionable context from the specification. Execute as:\n\n`{role=context_extractor; input=[raw_spec:str]; process=[strip_jargon(), strip_metaphor(), detect_domain(), locate_project_root(\"@codebase\"), list_explicit_objectives(), surface_implicit_requirements()], constraints=[no_paraphrase(), no_solution_generation()], output={context_profile:{domain:str, project_root:str, goals:list, requirements:list}}}`\n\n## Context Data\n\n```json\n{\n  \"description\": \"Dissects a raw specification to extract every piece of operational context an autonomous agent will need.\",\n  \"input_focus\": \"Unedited specification text supplied by the user.\",\n  \"output_focus\": \"A typed context_profile dict containing domain, project root, explicit objectives, and implicit requirements.\",\n  \"key_operations\": [\n    \"Removing metaphor and non‑operational language.\",\n    \"Detecting the working domain or tech stack.\",\n    \"Locating the root marker (`@codebase`) for path scoping.\",\n    \"Listing every stated objective verbatim.\",\n    \"Surfacing hidden assumptions and requirements.\"\n  ],\n  \"constraints_context\": [\n    \"May not paraphrase or interpret meaning beyond direct extraction.\",\n    \"Absolutely forbidden from proposing solutions or tasks.\"\n  ],\n  \"relevance\": \"Defines scope and boundaries for all downstream templates, guaranteeing that later steps operate on clean, actionable data.\"\n}\n```",
      "step": "a",
      "title": "Context Extractor",
      "input": "You’re an expert Sublime Text plugin developer with a deep understanding of Python and the Sublime Text API. You specialize in creating intuitive and efficient plugins that enhance user productivity and streamline workflows. You possess deep knowledge of the Sublime Text API and its ecosystem (and best practices), along with a strong understanding of user needs and developer workflows.\n\nThe current directory contains a collection of my existing plugins, analyze @codebase to enumerate all project, especially those related to Sublime Text layout management, file/tab rule enforcement, wildcard pattern support, and .sublime-project integration. Then, propose a fully formalized strategy for preparing the project for this plugin:\n```\n- Sublime Layout\n    > Lagre Layouts\n        > Lagre Regelsett knyttet til Layout (hvilke tabs som skal hvor, hvilke som skal lukkes, osv)\n            > F.eks. `alltid flytt [\"main.py\", \"*.py.bak\"] til layout.4.index.2`\n\n    > Kan videreutvikles til en standard som automatisk opprettholdes ved alle (.sublime-project) prosjekter\n```",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"context_profile\": {\n    \"domain\": \"Sublime Text plugin development (Python, Sublime Text API)\",\n    \"project_root\": \"@codebase\",\n    \"goals\": [\n      \"analyze @codebase to enumerate all project, especially those related to Sublime Text layout management, file/tab rule enforcement, wildcard pattern support, and .sublime-project integration\"\n    ],\n    \"requirements\": [\n      \"enlist existing plugins in the current directory\",\n      \"identify plugins pertaining to Sublime Text layout management\",\n      \"identify plugins for file/tab rule enforcement\",\n      \"identify plugins supporting wildcard patterns\",\n      \"identify plugins integrating with .sublime-project\",\n      \"recognize the existence of a 'Sublime Layout' directory possibly with subdirectories for layouts and rule sets\",\n      \"capture examples such as 'always move [\\\"main.py\\\", \\\"*.py.bak\\\"] to layout.4.index.2'\",\n      \"note potential standardization or automatic enforcement across all (.sublime-project) projects\",\n      \"maintain operational focus on structure, enumeration, and relationship within the @codebase folder\"\n    ]\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Task Vectorizer\n\nYour goal is **not** to restate context, but to **decompose** it into atomic, codebase-oriented tasks. Execute as:\n\n`{role=task_vectorizer; input=[context_profile:dict]; process=[derive_task_candidates(), prefix_paths(\"@codebase\"), order_by_dependency(), enforce_action_verbs(), remove_overlap(), ban_cosmetic_changes()], constraints=[tasks_unique(), verbs_only()], output={ordered_tasks:[str]}}`\n\n## Context Data\n\n```json\n{\n  \"description\": \"Transforms the context profile into a dependency‑ordered list of granular tasks fit for automated execution.\",\n  \"input_focus\": \"The context_profile produced by the Context Extractor.\",\n  \"output_focus\": \"ordered_tasks – a list of unique, verb‑driven commands each scoped to the codebase.\",\n  \"key_operations\": [\n    \"Splitting broad goals into single‑action commands.\",\n    \"Adding `@codebase` prefixes so agents act in the correct directory.\",\n    \"Sequencing tasks by logical dependency.\",\n    \"Eliminating redundancy and cosmetic‑only instructions.\"\n  ],\n  \"constraints_context\": [\n    \"Every task must begin with a strong action verb (identify, refactor, log, etc.).\",\n    \"Tasks must be non‑overlapping and directly tied to functional goals.\"\n  ],\n  \"relevance\": \"Provides the autonomous agent with a clear, step‑by‑step execution blueprint free of ambiguity.\"\n}\n```",
      "step": "b",
      "title": "Task Vectorizer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"You’re an expert Sublime Text plugin developer with a deep understanding of Python and the Sublime Text API. You specialize in creating intuitive and efficient plugins that enhance user productivity and streamline workflows. You possess deep knowledge of the Sublime Text API and its ecosystem (and best practices), along with a strong understanding of user needs and developer workflows.\\n\\nThe current directory contains a collection of my existing plugins, analyze @codebase to enumerate all project, especially those related to Sublime Text layout management, file/tab rule enforcement, wildcard pattern support, and .sublime-project integration. Then, propose a fully formalized strategy for preparing the project for this plugin:\\n```\\n- Sublime Layout\\n    > Lagre Layouts\\n        > Lagre Regelsett knyttet til Layout (hvilke tabs som skal hvor, hvilke som skal lukkes, osv)\\n            > F.eks. `alltid flytt [\\\"main.py\\\", \\\"*.py.bak\\\"] til layout.4.index.2`\\n\\n    > Kan videreutvikles til en standard som automatisk opprettholdes ved alle (.sublime-project) prosjekter\\n```\\\"```'\\n\\n\",\n  \"context_profile\": {\n    \"domain\": \"Sublime Text plugin development (Python, Sublime Text API)\",\n    \"project_root\": \"@codebase\",\n    \"goals\": [\n      \"analyze @codebase to enumerate all project, especially those related to Sublime Text layout management, file/tab rule enforcement, wildcard pattern support, and .sublime-project integration\"\n    ],\n    \"requirements\": [\n      \"enlist existing plugins in the current directory\",\n      \"identify plugins pertaining to Sublime Text layout management\",\n      \"identify plugins for file/tab rule enforcement\",\n      \"identify plugins supporting wildcard patterns\",\n      \"identify plugins integrating with .sublime-project\",\n      \"recognize the existence of a 'Sublime Layout' directory possibly with subdirectories for layouts and rule sets\",\n      \"capture examples such as 'always move [\\\"main.py\\\", \\\"*.py.bak\\\"] to layout.4.index.2'\",\n      \"note potential standardization or automatic enforcement across all (.sublime-project) projects\",\n      \"maintain operational focus on structure, enumeration, and relationship within the @codebase folder\"\n    ]\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"ordered_tasks\": [\n    \"enumerate_plugins @codebase\",\n    \"identify_layout_management_plugins @codebase\",\n    \"identify_file_tab_rule_enforcement_plugins @codebase\",\n    \"identify_wildcard_pattern_supporting_plugins @codebase\",\n    \"identify_sublime_project_integrating_plugins @codebase\",\n    \"recognize_sublime_layout_directory_and_substructure @codebase\",\n    \"extract_example_tab_rule_patterns @codebase\",\n    \"detect_standardization_mechanisms_for_sublime_project_enforcement @codebase\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Constraint Mapper\n\nYour goal is **not** to edit tasks, but to **extract** all governing limits. Execute as:\n\n`{role=constraint_mapper; input=[context_profile:dict, ordered_tasks:list]; process=[collect_functional_constraints(), collect_behavioral_constraints(), collect_policy_constraints(), deduplicate_constraints()], constraints=[no_new_tasks(), no_redundant_entries()], output={constraint_set:[str]}}`\n\n## Context Data\n\n```json\n{\n  \"description\": \"Consolidates every rule, boundary, and policy the agent must respect during execution.\",\n  \"input_focus\": \"context_profile and ordered_tasks.\",\n  \"output_focus\": \"constraint_set – a deduplicated list of textual constraints.\",\n  \"key_operations\": [\n    \"Harvesting functional‑equivalence mandates.\",\n    \"Capturing API and interface preservation rules.\",\n    \"Recording audit/logging, rollback, and policy obligations.\",\n    \"Removing duplicate or conflicting constraints.\"\n  ],\n  \"constraints_context\": [\n    \"Must not create or modify tasks; only list constraints.\",\n    \"Constraint entries must be unique and actionable.\"\n  ],\n  \"relevance\": \"Prevents unintended side‑effects by explicitly defining the operational guard‑rails for the agent.\"\n}\n```",
      "step": "c",
      "title": "Constraint Mapper",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"You’re an expert Sublime Text plugin developer with a deep understanding of Python and the Sublime Text API. You specialize in creating intuitive and efficient plugins that enhance user productivity and streamline workflows. You possess deep knowledge of the Sublime Text API and its ecosystem (and best practices), along with a strong understanding of user needs and developer workflows.\\n\\nThe current directory contains a collection of my existing plugins, analyze @codebase to enumerate all project, especially those related to Sublime Text layout management, file/tab rule enforcement, wildcard pattern support, and .sublime-project integration. Then, propose a fully formalized strategy for preparing the project for this plugin:\\n```\\n- Sublime Layout\\n    > Lagre Layouts\\n        > Lagre Regelsett knyttet til Layout (hvilke tabs som skal hvor, hvilke som skal lukkes, osv)\\n            > F.eks. `alltid flytt [\\\"main.py\\\", \\\"*.py.bak\\\"] til layout.4.index.2`\\n\\n    > Kan videreutvikles til en standard som automatisk opprettholdes ved alle (.sublime-project) prosjekter\\n```\\\"```'\\n\\n\",\n  \"ordered_tasks\": [\n    \"enumerate_plugins @codebase\",\n    \"identify_layout_management_plugins @codebase\",\n    \"identify_file_tab_rule_enforcement_plugins @codebase\",\n    \"identify_wildcard_pattern_supporting_plugins @codebase\",\n    \"identify_sublime_project_integrating_plugins @codebase\",\n    \"recognize_sublime_layout_directory_and_substructure @codebase\",\n    \"extract_example_tab_rule_patterns @codebase\",\n    \"detect_standardization_mechanisms_for_sublime_project_enforcement @codebase\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"constraint_set\": [\n    \"Do not create, delete, or modify tasks; only extract and report constraints.\",\n    \"Constraints must be deduplicated and expressed in clear, actionable terms.\",\n    \"Capture all rules relating to functional equivalence of identified plugins and their integration.\",\n    \"Record any constraints around preserving the Sublime Text API's intended usage and best practices.\",\n    \"List all requirements for correct handling of Sublime Text layouts, including tab and file placement and rule enforcement.\",\n    \"Identify and document patterns relating to wildcard rule support and enforcement in file/tab management.\",\n    \"Enumerate limits and policy obligations concerning .sublime-project integration and automatic standard maintenance.\",\n    \"Store any audit, rollback, or logging requirements observed in existing plugins or inferred from standardization mechanisms.\",\n    \"Any API or interface mandates found within project-related plugins should be explicitly listed.\",\n    \"No redundant or conflicting constraints are to be present in the final constraint set.\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Prompt Assembler\n\nYour goal is **not** to alter semantics, but to **compile** an agent-ready prompt. Execute as:\n\n`{role=prompt_assembler; input=[ordered_tasks:list, constraint_set:list, context_profile:dict]; process=[insert_goal_negation_header(), embed_agent_role(), enumerate_tasks(), append_constraints(), add_validation_targets(functional_equivalence=True,audit=True)], constraints=[≤300_tokens, english_only()], output={agent_prompt:str}}`\n\n## Context Data\n\n```json\n{\n  \"description\": \"Integrates tasks and constraints into a single, executable prompt string for autonomous coding agents.\",\n  \"input_focus\": \"ordered_tasks, constraint_set, and context_profile.\",\n  \"output_focus\": \"agent_prompt – complete instructions with sections for role, tasks, constraints, and validation goals.\",\n  \"key_operations\": [\n    \"Starting with a goal‑negation header to lock scope.\",\n    \"Embedding a precise agent role declaration.\",\n    \"Listing tasks numerically for deterministic execution order.\",\n    \"Appending constraints as a bullet list.\",\n    \"Stating final validation targets (functional equivalence, audit pass).\"\n  ],\n  \"constraints_context\": [\n    \"Entire prompt must be ≤ 300 tokens and written in English.\",\n    \"No re‑phrasing of task semantics or constraint meaning.\"\n  ],\n  \"relevance\": \"Delivers a turnkey command set that an autonomous agent can run without additional human clarification.\"\n}\n```",
      "step": "d",
      "title": "Prompt Assembler",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"You’re an expert Sublime Text plugin developer with a deep understanding of Python and the Sublime Text API. You specialize in creating intuitive and efficient plugins that enhance user productivity and streamline workflows. You possess deep knowledge of the Sublime Text API and its ecosystem (and best practices), along with a strong understanding of user needs and developer workflows.\\n\\nThe current directory contains a collection of my existing plugins, analyze @codebase to enumerate all project, especially those related to Sublime Text layout management, file/tab rule enforcement, wildcard pattern support, and .sublime-project integration. Then, propose a fully formalized strategy for preparing the project for this plugin:\\n```\\n- Sublime Layout\\n    > Lagre Layouts\\n        > Lagre Regelsett knyttet til Layout (hvilke tabs som skal hvor, hvilke som skal lukkes, osv)\\n            > F.eks. `alltid flytt [\\\"main.py\\\", \\\"*.py.bak\\\"] til layout.4.index.2`\\n\\n    > Kan videreutvikles til en standard som automatisk opprettholdes ved alle (.sublime-project) prosjekter\\n```\\\"```'\\n\\n\",\n  \"constraint_set\": [\n    \"Do not create, delete, or modify tasks; only extract and report constraints.\",\n    \"Constraints must be deduplicated and expressed in clear, actionable terms.\",\n    \"Capture all rules relating to functional equivalence of identified plugins and their integration.\",\n    \"Record any constraints around preserving the Sublime Text API's intended usage and best practices.\",\n    \"List all requirements for correct handling of Sublime Text layouts, including tab and file placement and rule enforcement.\",\n    \"Identify and document patterns relating to wildcard rule support and enforcement in file/tab management.\",\n    \"Enumerate limits and policy obligations concerning .sublime-project integration and automatic standard maintenance.\",\n    \"Store any audit, rollback, or logging requirements observed in existing plugins or inferred from standardization mechanisms.\",\n    \"Any API or interface mandates found within project-related plugins should be explicitly listed.\",\n    \"No redundant or conflicting constraints are to be present in the final constraint set.\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"agent_prompt\": \"Do not extend beyond the defined strategy proposal; actions must remain within scope only.\\n\\nRole: You are an expert Sublime Text plugin developer tasked with analyzing the current directory's @codebase to enumerate all projects, focusing on those related to Sublime Text layout management, file/tab rule enforcement, wildcard pattern support, and .sublime-project integration. Your objective is to propose a fully formalized strategy for preparing the project for a plugin with features including layout rule sets, tab/file placement policies, wildcard rules, and automated project-level standardization per .sublime-project.\\n\\nTasks:\\n1. Analyze @codebase to enumerate all projects; pay special attention to those concerning Sublime Text layout, layout rule enforcement, wildcard file/tab management, and .sublime-project support.\\n2. Collate findings related to Sublime Layout, including all major rules for tab/file placement, closing protocols, and enforcement of layout index targeting (e.g., always move [\\\"main.py\\\", \\\"*.py.bak\\\"] to layout.4.index.2).\\n3. Propose a fully formalized strategy for preparing the identified project for plugin integration as described, covering: layout rule standards, rule enforcement, extensibility for wildcard rules, and automatic adoption/maintenance via .sublime-project.\\n\\nConstraints:\\n• Do not create, delete, or modify tasks; only extract and report constraints.\\n• Constraints must be deduplicated and expressed in clear, actionable terms.\\n• Capture all rules relating to functional equivalence of identified plugins and their integration.\\n• Record any constraints around preserving the Sublime Text API's intended usage and best practices.\\n• List all requirements for correct handling of Sublime Text layouts, including tab and file placement and rule enforcement.\\n• Identify and document patterns relating to wildcard rule support and enforcement in file/tab management.\\n• Enumerate limits and policy obligations concerning .sublime-project integration and automatic standard maintenance.\\n• Store any audit, rollback, or logging requirements observed in existing plugins or inferred from standardization mechanisms.\\n• Any API or interface mandates found within project-related plugins should be explicitly listed.\\n• No redundant or conflicting constraints are to be present in the final constraint set.\\n\\nValidation targets:\\n- Functional equivalence of plugin integration strategy; ensure all identified constraint and feature sets are correctly addressed.\\n- Audit pass: documentation and strategy actions must withstand implementation review and traceability audit.\"\n}",
        }
      }
    },
      {
      "instruction": "# Prompt Validator\n\nYour goal is **not** to rewrite the prompt, but to **verify** strict compliance for execution. Execute as:\n\n`{role=prompt_validator; input=[agent_prompt:str]; process=[check_goal_negation_header(), check_section_labels(), check_length(≤300_tokens), validate_path_prefixes(\"@codebase\"), confirm_task_numbering(), confirm_constraint_block()], constraints=[no_semantic_change()], output={validated_prompt:str}}`\n\n## Context Data\n\n```json\n{\n  \"description\": \"Acts as the final quality gate, ensuring the assembled prompt adheres to every structural and content rule.\",\n  \"input_focus\": \"agent_prompt string from the Prompt Assembler.\",\n  \"output_focus\": \"validated_prompt – the same string, confirmed compliant or flagged for correction.\",\n  \"key_operations\": [\n    \"Verifying presence and correctness of the goal‑negation header.\",\n    \"Ensuring all mandatory section labels exist in order.\",\n    \"Checking overall token length is within limits.\",\n    \"Validating correct use of `@codebase` path prefixes.\",\n    \"Ensuring tasks are numbered sequentially and constraints are present.\"\n  ],\n  \"constraints_context\": [\n    \"May not alter wording or meaning; only validate structural compliance.\"\n  ],\n  \"relevance\": \"Guarantees that the autonomous agent receives a well‑formed, execution‑ready prompt, reducing runtime errors and mis‑scoping.\"\n}\n```",
      "step": "e",
      "title": "Prompt Validator",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"You’re an expert Sublime Text plugin developer with a deep understanding of Python and the Sublime Text API. You specialize in creating intuitive and efficient plugins that enhance user productivity and streamline workflows. You possess deep knowledge of the Sublime Text API and its ecosystem (and best practices), along with a strong understanding of user needs and developer workflows.\\n\\nThe current directory contains a collection of my existing plugins, analyze @codebase to enumerate all project, especially those related to Sublime Text layout management, file/tab rule enforcement, wildcard pattern support, and .sublime-project integration. Then, propose a fully formalized strategy for preparing the project for this plugin:\\n```\\n- Sublime Layout\\n    > Lagre Layouts\\n        > Lagre Regelsett knyttet til Layout (hvilke tabs som skal hvor, hvilke som skal lukkes, osv)\\n            > F.eks. `alltid flytt [\\\"main.py\\\", \\\"*.py.bak\\\"] til layout.4.index.2`\\n\\n    > Kan videreutvikles til en standard som automatisk opprettholdes ved alle (.sublime-project) prosjekter\\n```\\\"```'\\n\\n\",\n  \"agent_prompt\": \"Do not extend beyond the defined strategy proposal; actions must remain within scope only.\\n\\nRole: You are an expert Sublime Text plugin developer tasked with analyzing the current directory's @codebase to enumerate all projects, focusing on those related to Sublime Text layout management, file/tab rule enforcement, wildcard pattern support, and .sublime-project integration. Your objective is to propose a fully formalized strategy for preparing the project for a plugin with features including layout rule sets, tab/file placement policies, wildcard rules, and automated project-level standardization per .sublime-project.\\n\\nTasks:\\n1. Analyze @codebase to enumerate all projects; pay special attention to those concerning Sublime Text layout, layout rule enforcement, wildcard file/tab management, and .sublime-project support.\\n2. Collate findings related to Sublime Layout, including all major rules for tab/file placement, closing protocols, and enforcement of layout index targeting (e.g., always move [\\\"main.py\\\", \\\"*.py.bak\\\"] to layout.4.index.2).\\n3. Propose a fully formalized strategy for preparing the identified project for plugin integration as described, covering: layout rule standards, rule enforcement, extensibility for wildcard rules, and automatic adoption/maintenance via .sublime-project.\\n\\nConstraints:\\n• Do not create, delete, or modify tasks; only extract and report constraints.\\n• Constraints must be deduplicated and expressed in clear, actionable terms.\\n• Capture all rules relating to functional equivalence of identified plugins and their integration.\\n• Record any constraints around preserving the Sublime Text API's intended usage and best practices.\\n• List all requirements for correct handling of Sublime Text layouts, including tab and file placement and rule enforcement.\\n• Identify and document patterns relating to wildcard rule support and enforcement in file/tab management.\\n• Enumerate limits and policy obligations concerning .sublime-project integration and automatic standard maintenance.\\n• Store any audit, rollback, or logging requirements observed in existing plugins or inferred from standardization mechanisms.\\n• Any API or interface mandates found within project-related plugins should be explicitly listed.\\n• No redundant or conflicting constraints are to be present in the final constraint set.\\n\\nValidation targets:\\n- Functional equivalence of plugin integration strategy; ensure all identified constraint and feature sets are correctly addressed.\\n- Audit pass: documentation and strategy actions must withstand implementation review and traceability audit.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"validated_prompt\": \"Do not extend beyond the defined strategy proposal; actions must remain within scope only.\\n\\nRole: You are an expert Sublime Text plugin developer tasked with analyzing the current directory's @codebase to enumerate all projects, focusing on those related to Sublime Text layout management, file/tab rule enforcement, wildcard pattern support, and .sublime-project integration. Your objective is to propose a fully formalized strategy for preparing the project for a plugin with features including layout rule sets, tab/file placement policies, wildcard rules, and automated project-level standardization per .sublime-project.\\n\\nTasks:\\n1. Analyze @codebase to enumerate all projects; pay special attention to those concerning Sublime Text layout, layout rule enforcement, wildcard file/tab management, and .sublime-project support.\\n2. Collate findings related to Sublime Layout, including all major rules for tab/file placement, closing protocols, and enforcement of layout index targeting (e.g., always move [\\\"main.py\\\", \\\"*.py.bak\\\"] to layout.4.index.2).\\n3. Propose a fully formalized strategy for preparing the identified project for plugin integration as described, covering: layout rule standards, rule enforcement, extensibility for wildcard rules, and automatic adoption/maintenance via .sublime-project.\\n\\nConstraints:\\n• Do not create, delete, or modify tasks; only extract and report constraints.\\n• Constraints must be deduplicated and expressed in clear, actionable terms.\\n• Capture all rules relating to functional equivalence of identified plugins and their integration.\\n• Record any constraints around preserving the Sublime Text API's intended usage and best practices.\\n• List all requirements for correct handling of Sublime Text layouts, including tab and file placement and rule enforcement.\\n• Identify and document patterns relating to wildcard rule support and enforcement in file/tab management.\\n• Enumerate limits and policy obligations concerning .sublime-project integration and automatic standard maintenance.\\n• Store any audit, rollback, or logging requirements observed in existing plugins or inferred from standardization mechanisms.\\n• Any API or interface mandates found within project-related plugins should be explicitly listed.\\n• No redundant or conflicting constraints are to be present in the final constraint set.\\n\\nValidation targets:\\n- Functional equivalence of plugin integration strategy; ensure all identified constraint and feature sets are correctly addressed.\\n- Audit pass: documentation and strategy actions must withstand implementation review and traceability audit.\"\n}",
        }
      }
    }