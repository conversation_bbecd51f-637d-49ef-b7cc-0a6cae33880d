  {
  "initial_prompt": "```## 1. **Explicit Subject + Style + Qualifiers**

  Start with a clear subject, then layer in style, lighting, mood, and technical qualifiers.
  **Example**: “A mid-30s woman with auburn hair in a beige trench coat, photorealistic portrait, golden hour, soft cinematic lighting, shallow depth of field, 85 mm lens.”
  – This captures subject, style, and camera details for precise results ([saxifrage.xyz][1], [Stockimg AI][2]).

  ---

  ## 2. **Use Artistic/Genre References**

  Reference artistic movements, photographers, or media types to invoke specific aesthetics.
  **Example**: “In the style of <PERSON>,” or “digital matte painting,” “cinematic noir.”
  – Naming an art style or artist guides tone and technique .

  ---

  ## 3. **Control with Technical Modifiers**

  Add technical terms—like “8K resolution,” “HDR,” “DSLR,” “Sigma 75 mm lens”—to enhance realism and image fidelity.
  – These encourage models to simulate professional photography ([Stockimg AI][2], [Foundation Marketing][3]).

  ---

  ## 4. **Descriptive Framing & Composition**

  Mention layout details—like “close-up,” “low angle,” “foreground/midground/background,” or “rule-of-thirds composition.”
  – Helps shape visual hierarchy and framing ([Medium][4]).

  ---

  ## 5. **Scene vs. Subject Focus**

  Decide whether the prompt prioritizes a single subject (**subject‑focused**) or an environment/setting (**scene‑focused**).
  – Use subject focus for portraits and scene focus for landscapes or narratives ([Medium][4], [Microsoft][5]).

  ---

  ## 6. **Design‑Focused Prompting**

  Useful for marketing visuals: specify layout like “web banner,” “space for title at top,” “leave blank at bottom for text.”
  – Balances visual content with graphic design considerations .

  ---

  ## 7. **Avoid Negatives; Use Positive Constraints**

  Phrase what *you want* rather than what to avoid.
  **Prefer**: “A forest clearing at dawn.”
  **Avoid**: “No cars, no people.”
  – Negative prompting often backfires—positive clarity is better .

  ---

  ## 8. **Prompt Chaining / Iterative Refinement**

  Use progressive prompting: start general, review outputs, then refine with more detail or adjustments.
  – Common in Midjourney workflows ([YouTube][6]).

  ---

  ## 9. **Abstract & Emotional Imagery**

  Invoke mood or metaphor: “An impression of resilience painted in swirling pastels,” “abstract tapestry of hope.”
  – Great for artistic results, less so for literal realism .

  ---

  ## 10. **Personalization via Embeddings / Image References**

  Use “textual inversion” or include an example image to build a custom concept token (e.g., DreamBooth). Specify image weight or style references.
  – Enables consistent depiction of specific characters or styles ([Wikipedia][7]).

  ---

  ### ✅ How to Structure an Ideal Prompt

  1. **Start with intent/subject** – e.g., “A confident female CEO”
  2. **Add style/genre** – e.g., “photorealistic, golden hour”
  3. **Define technical specs** – e.g., “shot with DSLR, 50 mm lens, shallow depth”
  4. **Include composition & mood** – e.g., “low-angle, cityscape background, dramatic tone”
  5. **Iterate** – evaluate output; tweak descriptors, expand or refine.```",
  "sequence_id": "1003|1100|3900|1003|1100",
  "results": [
      {
      "instruction": "# Instruction Converter

  Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:

  `{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format:str}; direction_vector=['Prompt accurately converted to imperative instruction set', 'Clarity and actionability accelerate user productivity', 'Feed the produced instruction set into the next transformation template' ]}`",
      "step": "a",
      "title": "Instruction Converter",
      "input": "```## 1. **Explicit Subject + Style + Qualifiers**

  Start with a clear subject, then layer in style, lighting, mood, and technical qualifiers.
  **Example**: “A mid-30s woman with auburn hair in a beige trench coat, photorealistic portrait, golden hour, soft cinematic lighting, shallow depth of field, 85 mm lens.”
  – This captures subject, style, and camera details for precise results ([saxifrage.xyz][1], [Stockimg AI][2]).

  ---

  ## 2. **Use Artistic/Genre References**

  Reference artistic movements, photographers, or media types to invoke specific aesthetics.
  **Example**: “In the style of Van Gogh,” or “digital matte painting,” “cinematic noir.”
  – Naming an art style or artist guides tone and technique .

  ---

  ## 3. **Control with Technical Modifiers**

  Add technical terms—like “8K resolution,” “HDR,” “DSLR,” “Sigma 75 mm lens”—to enhance realism and image fidelity.
  – These encourage models to simulate professional photography ([Stockimg AI][2], [Foundation Marketing][3]).

  ---

  ## 4. **Descriptive Framing & Composition**

  Mention layout details—like “close-up,” “low angle,” “foreground/midground/background,” or “rule-of-thirds composition.”
  – Helps shape visual hierarchy and framing ([Medium][4]).

  ---

  ## 5. **Scene vs. Subject Focus**

  Decide whether the prompt prioritizes a single subject (**subject‑focused**) or an environment/setting (**scene‑focused**).
  – Use subject focus for portraits and scene focus for landscapes or narratives ([Medium][4], [Microsoft][5]).

  ---

  ## 6. **Design‑Focused Prompting**

  Useful for marketing visuals: specify layout like “web banner,” “space for title at top,” “leave blank at bottom for text.”
  – Balances visual content with graphic design considerations .

  ---

  ## 7. **Avoid Negatives; Use Positive Constraints**

  Phrase what *you want* rather than what to avoid.
  **Prefer**: “A forest clearing at dawn.”
  **Avoid**: “No cars, no people.”
  – Negative prompting often backfires—positive clarity is better .

  ---

  ## 8. **Prompt Chaining / Iterative Refinement**

  Use progressive prompting: start general, review outputs, then refine with more detail or adjustments.
  – Common in Midjourney workflows ([YouTube][6]).

  ---

  ## 9. **Abstract & Emotional Imagery**

  Invoke mood or metaphor: “An impression of resilience painted in swirling pastels,” “abstract tapestry of hope.”
  – Great for artistic results, less so for literal realism .

  ---

  ## 10. **Personalization via Embeddings / Image References**

  Use “textual inversion” or include an example image to build a custom concept token (e.g., DreamBooth). Specify image weight or style references.
  – Enables consistent depiction of specific characters or styles ([Wikipedia][7]).

  ---

  ### ✅ How to Structure an Ideal Prompt

  1. **Start with intent/subject** – e.g., “A confident female CEO”
  2. **Add style/genre** – e.g., “photorealistic, golden hour”
  3. **Define technical specs** – e.g., “shot with DSLR, 50 mm lens, shallow depth”
  4. **Include composition & mood** – e.g., “low-angle, cityscape background, dramatic tone”
  5. **Iterate** – evaluate output; tweak descriptors, expand or refine.```",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{
    "instruction_format": [
      "Begin prompt with a clearly defined subject.",
      "Layer in style, lighting, mood, and technical qualifiers to enhance detail and specificity.",
      "Incorporate references to artistic movements, well-known photographers, or media types to establish a desired aesthetic.",
      "Integrate technical modifiers such as resolution, HDR, camera model, or specific lenses to improve realism and image fidelity.",
      "Specify descriptive framing and composition elements, including shot type (e.g., close-up, low angle), spatial hierarchy (foreground/midground/background), and compositional rules (e.g., rule of thirds).",
      "Determine whether the prompt should be subject-focused (emphasizing a single entity) or scene-focused (highlighting an environment or narrative setting), and tailor the descriptors accordingly.",
      "When creating prompts for design or marketing visuals, explicitly state layout and graphic design requirements, such as banner orientation or allocated space for text overlays.",
      "Phrase attributes and constraints positively; describe desired elements directly instead of listing unwanted objects or features.",
      "Apply iterative refinement: generate initial images, review results, and progressively adjust or expand prompt details to achieve the intended outcome.",
      "Employ abstract or emotive descriptors to evoke mood or metaphor where desired, especially for artistic and non-literal imagery.",
      "Personalize outputs by attaching embeddings, referencing example images, or employing custom concept tokens; specify image weights or reference styles to maintain consistency.",
      "For structuring optimal prompts, follow this sequence: state intent/subject, add style or genre tags, define technical specifications, describe composition and mood, then iterate through review and refinement."
    ]
  }",
        }
      }
    },
      {
      "instruction": "# Problem Exploder

  Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:

  `{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`",
      "step": "a",
      "title": "Problem Exploder",
      "input": "{
    "initial_prompt": "\
  '```[Initial Prompt]: \"```## 1. **Explicit Subject + Style + Qualifiers**\
  \
  Start with a clear subject, then layer in style, lighting, mood, and technical qualifiers.\
  **Example**: “A mid-30s woman with auburn hair in a beige trench coat, photorealistic portrait, golden hour, soft cinematic lighting, shallow depth of field, 85 mm lens.”\
  – This captures subject, style, and camera details for precise results ([saxifrage.xyz][1], [Stockimg AI][2]).\
  \
  ---\
  \
  ## 2. **Use Artistic/Genre References**\
  \
  Reference artistic movements, photographers, or media types to invoke specific aesthetics.\
  **Example**: “In the style of Van Gogh,” or “digital matte painting,” “cinematic noir.”\
  – Naming an art style or artist guides tone and technique .\
  \
  ---\
  \
  ## 3. **Control with Technical Modifiers**\
  \
  Add technical terms—like “8K resolution,” “HDR,” “DSLR,” “Sigma 75 mm lens”—to enhance realism and image fidelity.\
  – These encourage models to simulate professional photography ([Stockimg AI][2], [Foundation Marketing][3]).\
  \
  ---\
  \
  ## 4. **Descriptive Framing & Composition**\
  \
  Mention layout details—like “close-up,” “low angle,” “foreground/midground/background,” or “rule-of-thirds composition.”\
  – Helps shape visual hierarchy and framing ([Medium][4]).\
  \
  ---\
  \
  ## 5. **Scene vs. Subject Focus**\
  \
  Decide whether the prompt prioritizes a single subject (**subject‑focused**) or an environment/setting (**scene‑focused**).\
  – Use subject focus for portraits and scene focus for landscapes or narratives ([Medium][4], [Microsoft][5]).\
  \
  ---\
  \
  ## 6. **Design‑Focused Prompting**\
  \
  Useful for marketing visuals: specify layout like “web banner,” “space for title at top,” “leave blank at bottom for text.”\
  – Balances visual content with graphic design considerations .\
  \
  ---\
  \
  ## 7. **Avoid Negatives; Use Positive Constraints**\
  \
  Phrase what *you want* rather than what to avoid.\
  **Prefer**: “A forest clearing at dawn.”\
  **Avoid**: “No cars, no people.”\
  – Negative prompting often backfires—positive clarity is better .\
  \
  ---\
  \
  ## 8. **Prompt Chaining / Iterative Refinement**\
  \
  Use progressive prompting: start general, review outputs, then refine with more detail or adjustments.\
  – Common in Midjourney workflows ([YouTube][6]).\
  \
  ---\
  \
  ## 9. **Abstract & Emotional Imagery**\
  \
  Invoke mood or metaphor: “An impression of resilience painted in swirling pastels,” “abstract tapestry of hope.”\
  – Great for artistic results, less so for literal realism .\
  \
  ---\
  \
  ## 10. **Personalization via Embeddings / Image References**\
  \
  Use “textual inversion” or include an example image to build a custom concept token (e.g., DreamBooth). Specify image weight or style references.\
  – Enables consistent depiction of specific characters or styles ([Wikipedia][7]).\
  \
  ---\
  \
  ### ✅ How to Structure an Ideal Prompt\
  \
  1. **Start with intent/subject** – e.g., “A confident female CEO”\
  2. **Add style/genre** – e.g., “photorealistic, golden hour”\
  3. **Define technical specs** – e.g., “shot with DSLR, 50 mm lens, shallow depth”\
  4. **Include composition & mood** – e.g., “low-angle, cityscape background, dramatic tone”\
  5. **Iterate** – evaluate output; tweak descriptors, expand or refine.```\"```'\
  \
  ",
    "instruction_format": [
      "Begin prompt with a clearly defined subject.",
      "Layer in style, lighting, mood, and technical qualifiers to enhance detail and specificity.",
      "Incorporate references to artistic movements, well-known photographers, or media types to establish a desired aesthetic.",
      "Integrate technical modifiers such as resolution, HDR, camera model, or specific lenses to improve realism and image fidelity.",
      "Specify descriptive framing and composition elements, including shot type (e.g., close-up, low angle), spatial hierarchy (foreground/midground/background), and compositional rules (e.g., rule of thirds).",
      "Determine whether the prompt should be subject-focused (emphasizing a single entity) or scene-focused (highlighting an environment or narrative setting), and tailor the descriptors accordingly.",
      "When creating prompts for design or marketing visuals, explicitly state layout and graphic design requirements, such as banner orientation or allocated space for text overlays.",
      "Phrase attributes and constraints positively; describe desired elements directly instead of listing unwanted objects or features.",
      "Apply iterative refinement: generate initial images, review results, and progressively adjust or expand prompt details to achieve the intended outcome.",
      "Employ abstract or emotive descriptors to evoke mood or metaphor where desired, especially for artistic and non-literal imagery.",
      "Personalize outputs by attaching embeddings, referencing example images, or employing custom concept tokens; specify image weights or reference styles to maintain consistency.",
      "For structuring optimal prompts, follow this sequence: state intent/subject, add style or genre tags, define technical specifications, describe composition and mood, then iterate through review and refinement."
    ]
  }",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{
    "goal_map": [
      {
        "step": "identify_explicit_asks",
        "details": [
          "Decompose the provided strategies and instruction formats for constructing ideal image prompts.",
          "Map each instruction, methodology, or guideline to its required sub-actions and sequencing.",
          "Break down the sample workflow into its minimal actionable elements."
        ]
      },
      {
        "step": "surface_hidden_assumptions",
        "details": [
          "Assume access to a promptable generative image model (e.g., Midjourney, DALL-E, Stable Diffusion).",
          "The user knows basic photography or art terminology.",
          "The image model can interpret positive, stylistic, technical, and compositional cues.",
          "References (art movements, photographers, image types) are recognized or interpretable by the model.",
          "Technical modifiers (resolution, lens) actually influence results as described.",
          "The user can view outputs and iterate/refine prompts based on those results.",
          "It's possible to embed or reference example images or provide custom concept tokens for personalization.",
          "Positive constraints deliver more reliable results than negative ones.",
          "Design or marketing visuals require clear space or layout constraints, and the image model can respect those."
        ]
      },
      {
        "step": "enumerate_sub_goals",
        "details": [
          "1. Explicitly define the image's core subject.",
          "2. Enhance specificity by layering style, lighting, mood, and technical descriptors.",
          "3. Introduce references to known artistic styles, genres, or creators to guide aesthetic outcomes.",
          "4. Insert technical camera/photography modifiers to shape realism and fidelity.",
          "5. Deliberately describe composition: angle, spatial arrangement, and visual hierarchy.",
          "6. Decide and declare prompt focus: single subject or broader scene.",
          "7. For applied use-cases (e.g., marketing/design), explicitly state layout and design requirements.",
          "8. Frame every constraint in the positive form to minimize ambiguity in outputs.",
          "9. Employ iterative refinement: initial prompt -> output -> review -> targeted adjustment.",
          "10. Apply abstract or emotional descriptors for artistic or mood-driven imagery.",
          "11. Where needed, use personalization strategies: embeddings, image examples, or style weights.",
          "12. Structure the prompt according to the recommended sequence: subject, style, technical specs, composition/mood, iteration."
        ]
      },
      {
        "step": "reveal_blockers",
        "details": [
          "Insufficient knowledge of technical or artistic terminology may hinder detailed prompting.",
          "The image model may not recognize or properly interpret all art historical or technical references.",
          "Model/platform may lack support for image embeddings, references, or custom concepts.",
          "Iterative refinement requires access to generation outputs and the ability to re-prompt; workflow breakdown if these are unavailable.",
          "Design-specific layout constraints might not be rigidly enforced by the model, resulting in unpredicted placement.",
          "Positive phrasing alone may not fully prevent the inclusion of undesired objects if not sufficiently specified.",
          "Not all models handle complex chaining or prompt tokens for advanced personalization.",
          "Vague or broad descriptors may yield overly generic, non-unique images."
        ]
      }
    ]
  }",
        }
      }
    },
      {
      "instruction": "# Hard Critique

  Your goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:

  `{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,2.0], flaw_analysis:str, alternative_enhancements:str[3]}}`",
      "step": "a",
      "title": "Hard Critique",
      "input": "{
    "initial_prompt": "\
  '```[Initial Prompt]: \"```## 1. **Explicit Subject + Style + Qualifiers**\
  \
  Start with a clear subject, then layer in style, lighting, mood, and technical qualifiers.\
  **Example**: “A mid-30s woman with auburn hair in a beige trench coat, photorealistic portrait, golden hour, soft cinematic lighting, shallow depth of field, 85 mm lens.”\
  – This captures subject, style, and camera details for precise results ([saxifrage.xyz][1], [Stockimg AI][2]).\
  \
  ---\
  \
  ## 2. **Use Artistic/Genre References**\
  \
  Reference artistic movements, photographers, or media types to invoke specific aesthetics.\
  **Example**: “In the style of Van Gogh,” or “digital matte painting,” “cinematic noir.”\
  – Naming an art style or artist guides tone and technique .\
  \
  ---\
  \
  ## 3. **Control with Technical Modifiers**\
  \
  Add technical terms—like “8K resolution,” “HDR,” “DSLR,” “Sigma 75 mm lens”—to enhance realism and image fidelity.\
  – These encourage models to simulate professional photography ([Stockimg AI][2], [Foundation Marketing][3]).\
  \
  ---\
  \
  ## 4. **Descriptive Framing & Composition**\
  \
  Mention layout details—like “close-up,” “low angle,” “foreground/midground/background,” or “rule-of-thirds composition.”\
  – Helps shape visual hierarchy and framing ([Medium][4]).\
  \
  ---\
  \
  ## 5. **Scene vs. Subject Focus**\
  \
  Decide whether the prompt prioritizes a single subject (**subject‑focused**) or an environment/setting (**scene‑focused**).\
  – Use subject focus for portraits and scene focus for landscapes or narratives ([Medium][4], [Microsoft][5]).\
  \
  ---\
  \
  ## 6. **Design‑Focused Prompting**\
  \
  Useful for marketing visuals: specify layout like “web banner,” “space for title at top,” “leave blank at bottom for text.”\
  – Balances visual content with graphic design considerations .\
  \
  ---\
  \
  ## 7. **Avoid Negatives; Use Positive Constraints**\
  \
  Phrase what *you want* rather than what to avoid.\
  **Prefer**: “A forest clearing at dawn.”\
  **Avoid**: “No cars, no people.”\
  – Negative prompting often backfires—positive clarity is better .\
  \
  ---\
  \
  ## 8. **Prompt Chaining / Iterative Refinement**\
  \
  Use progressive prompting: start general, review outputs, then refine with more detail or adjustments.\
  – Common in Midjourney workflows ([YouTube][6]).\
  \
  ---\
  \
  ## 9. **Abstract & Emotional Imagery**\
  \
  Invoke mood or metaphor: “An impression of resilience painted in swirling pastels,” “abstract tapestry of hope.”\
  – Great for artistic results, less so for literal realism .\
  \
  ---\
  \
  ## 10. **Personalization via Embeddings / Image References**\
  \
  Use “textual inversion” or include an example image to build a custom concept token (e.g., DreamBooth). Specify image weight or style references.\
  – Enables consistent depiction of specific characters or styles ([Wikipedia][7]).\
  \
  ---\
  \
  ### ✅ How to Structure an Ideal Prompt\
  \
  1. **Start with intent/subject** – e.g., “A confident female CEO”\
  2. **Add style/genre** – e.g., “photorealistic, golden hour”\
  3. **Define technical specs** – e.g., “shot with DSLR, 50 mm lens, shallow depth”\
  4. **Include composition & mood** – e.g., “low-angle, cityscape background, dramatic tone”\
  5. **Iterate** – evaluate output; tweak descriptors, expand or refine.```\"```'\
  \
  ",
    "goal_map": [
      {
        "step": "identify_explicit_asks",
        "details": [
          "Decompose the provided strategies and instruction formats for constructing ideal image prompts.",
          "Map each instruction, methodology, or guideline to its required sub-actions and sequencing.",
          "Break down the sample workflow into its minimal actionable elements."
        ]
      },
      {
        "step": "surface_hidden_assumptions",
        "details": [
          "Assume access to a promptable generative image model (e.g., Midjourney, DALL-E, Stable Diffusion).",
          "The user knows basic photography or art terminology.",
          "The image model can interpret positive, stylistic, technical, and compositional cues.",
          "References (art movements, photographers, image types) are recognized or interpretable by the model.",
          "Technical modifiers (resolution, lens) actually influence results as described.",
          "The user can view outputs and iterate/refine prompts based on those results.",
          "It's possible to embed or reference example images or provide custom concept tokens for personalization.",
          "Positive constraints deliver more reliable results than negative ones.",
          "Design or marketing visuals require clear space or layout constraints, and the image model can respect those."
        ]
      },
      {
        "step": "enumerate_sub_goals",
        "details": [
          "1. Explicitly define the image's core subject.",
          "2. Enhance specificity by layering style, lighting, mood, and technical descriptors.",
          "3. Introduce references to known artistic styles, genres, or creators to guide aesthetic outcomes.",
          "4. Insert technical camera/photography modifiers to shape realism and fidelity.",
          "5. Deliberately describe composition: angle, spatial arrangement, and visual hierarchy.",
          "6. Decide and declare prompt focus: single subject or broader scene.",
          "7. For applied use-cases (e.g., marketing/design), explicitly state layout and design requirements.",
          "8. Frame every constraint in the positive form to minimize ambiguity in outputs.",
          "9. Employ iterative refinement: initial prompt -> output -> review -> targeted adjustment.",
          "10. Apply abstract or emotional descriptors for artistic or mood-driven imagery.",
          "11. Where needed, use personalization strategies: embeddings, image examples, or style weights.",
          "12. Structure the prompt according to the recommended sequence: subject, style, technical specs, composition/mood, iteration."
        ]
      },
      {
        "step": "reveal_blockers",
        "details": [
          "Insufficient knowledge of technical or artistic terminology may hinder detailed prompting.",
          "The image model may not recognize or properly interpret all art historical or technical references.",
          "Model/platform may lack support for image embeddings, references, or custom concepts.",
          "Iterative refinement requires access to generation outputs and the ability to re-prompt; workflow breakdown if these are unavailable.",
          "Design-specific layout constraints might not be rigidly enforced by the model, resulting in unpredicted placement.",
          "Positive phrasing alone may not fully prevent the inclusion of undesired objects if not sufficiently specified.",
          "Not all models handle complex chaining or prompt tokens for advanced personalization.",
          "Vague or broad descriptors may yield overly generic, non-unique images."
        ]
      }
    ]
  }",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{
    "enhancement_score": 0.7,
    "flaw_analysis": "The so-called enhancement produces regressive effects across multiple axes. Core elements such as the explicit, stepwise instructional sequence are diluted by dispersing actionable methodologies throughout verbose, referential blocks laden with extraneous examples. Information loss is acute—the enhanced text obfuscates minimal actionable units through meandering prose: instead of direct, algorithmic decomposition, content is presented as loosely linked explanations. Nuance suffers: the initial's imperatives (e.g., 'Explicitly define the image's core subject' or 'Employ iterative refinement') are subsumed under vague metaphors and speculative qualifiers, scattering procedural utility. Clarity is contaminated by dense, multi-part sentences and inconsistent signal flow (e.g., abrupt parentheticals citing sources fail to scaffold understanding). Ambiguity score: 8 due to rampant cross-talk between stylistic suggestion and practical execution, obscuring how to sequence actions. Noise levels are elevated by citation overload and soft, suggestive tone; the original's crisp, operational stance degrades into lecture disguised as advice—generating a confusion spiral for actionable takeaways. Tone shifts from directive to pseudo-explanatory, weakening prescriptive authority and thus impact: key distinctions (scene vs. subject, positive vs. negative constraints) blur into passively described stylistic tips. Coherence collapses as instructions intended for direct workflow breakdown are buried beneath ornamental language, tangling the recommended process and destroying prompt modularity. The enhancement is littered with imprecision, disjoint concept delivery, disruptive asides, and ornamental verbiage—all camouflaging the fact that the user's minimal path to correct, repeatable prompt structure is no longer preserved.",
    "alternative_enhancements": [
      "Strip all examples and references into an indexed, tabular format: each strategy (subject, style, technical, etc.) is listed as an action item with a one-sentence operational command followed by an absolute minimal example and no source citations.",
      "Collapse all guidelines into a bullet-point checklist for prompt-building: sequence each required element in the strict order of generation workflow, using imperative verbs exclusively; reserve any exceptions or caveats for an addendum at the end.",
      "Rework the content into a flowchart: each prompt-building step as a node, with conditional branches for scene/subject, design/marketing use, personalization, and iterative refinement—eschew narrative prose for pure process mapping."
    ]
  }",
        }
      }
    },
      {
      "instruction": "# Trajectory Director

  Your goal is not to **process** the input in a vacuum, but to **steer** it—deriving the highest-value constructive trajectory and encoding that path as the very next set of instructions. Execute as trajectory-setting directive generator:

  `{role=trajectory_director; input=[raw_prompt:any]; process=[extract_core_objective(), infer_desired_end_state(constructive_bias=True), map_high_value_intermediate_targets(), synthesize_directional_commands(), embed_self-improving_guidelines(), validate_alignment_with_constructive_principles()]; constraints=[mandatory_constructive_vector(), preserve_original_contextual_integrity(), forbid_nil_direction_outputs(), zero_conversational_fillers()]; requirements=[explicit_next-step_directives(), trajectory_map:list, self_reinforcing_instruction_pattern()], output={directed_prompt:str, trajectory_outline:list}}`",
      "step": "a",
      "title": "Trajectory Director",
      "input": "{
    "initial_prompt": "\
  '```[Initial Prompt]: \"```## 1. **Explicit Subject + Style + Qualifiers**\
  \
  Start with a clear subject, then layer in style, lighting, mood, and technical qualifiers.\
  **Example**: “A mid-30s woman with auburn hair in a beige trench coat, photorealistic portrait, golden hour, soft cinematic lighting, shallow depth of field, 85 mm lens.”\
  – This captures subject, style, and camera details for precise results ([saxifrage.xyz][1], [Stockimg AI][2]).\
  \
  ---\
  \
  ## 2. **Use Artistic/Genre References**\
  \
  Reference artistic movements, photographers, or media types to invoke specific aesthetics.\
  **Example**: “In the style of Van Gogh,” or “digital matte painting,” “cinematic noir.”\
  – Naming an art style or artist guides tone and technique .\
  \
  ---\
  \
  ## 3. **Control with Technical Modifiers**\
  \
  Add technical terms—like “8K resolution,” “HDR,” “DSLR,” “Sigma 75 mm lens”—to enhance realism and image fidelity.\
  – These encourage models to simulate professional photography ([Stockimg AI][2], [Foundation Marketing][3]).\
  \
  ---\
  \
  ## 4. **Descriptive Framing & Composition**\
  \
  Mention layout details—like “close-up,” “low angle,” “foreground/midground/background,” or “rule-of-thirds composition.”\
  – Helps shape visual hierarchy and framing ([Medium][4]).\
  \
  ---\
  \
  ## 5. **Scene vs. Subject Focus**\
  \
  Decide whether the prompt prioritizes a single subject (**subject‑focused**) or an environment/setting (**scene‑focused**).\
  – Use subject focus for portraits and scene focus for landscapes or narratives ([Medium][4], [Microsoft][5]).\
  \
  ---\
  \
  ## 6. **Design‑Focused Prompting**\
  \
  Useful for marketing visuals: specify layout like “web banner,” “space for title at top,” “leave blank at bottom for text.”\
  – Balances visual content with graphic design considerations .\
  \
  ---\
  \
  ## 7. **Avoid Negatives; Use Positive Constraints**\
  \
  Phrase what *you want* rather than what to avoid.\
  **Prefer**: “A forest clearing at dawn.”\
  **Avoid**: “No cars, no people.”\
  – Negative prompting often backfires—positive clarity is better .\
  \
  ---\
  \
  ## 8. **Prompt Chaining / Iterative Refinement**\
  \
  Use progressive prompting: start general, review outputs, then refine with more detail or adjustments.\
  – Common in Midjourney workflows ([YouTube][6]).\
  \
  ---\
  \
  ## 9. **Abstract & Emotional Imagery**\
  \
  Invoke mood or metaphor: “An impression of resilience painted in swirling pastels,” “abstract tapestry of hope.”\
  – Great for artistic results, less so for literal realism .\
  \
  ---\
  \
  ## 10. **Personalization via Embeddings / Image References**\
  \
  Use “textual inversion” or include an example image to build a custom concept token (e.g., DreamBooth). Specify image weight or style references.\
  – Enables consistent depiction of specific characters or styles ([Wikipedia][7]).\
  \
  ---\
  \
  ### ✅ How to Structure an Ideal Prompt\
  \
  1. **Start with intent/subject** – e.g., “A confident female CEO”\
  2. **Add style/genre** – e.g., “photorealistic, golden hour”\
  3. **Define technical specs** – e.g., “shot with DSLR, 50 mm lens, shallow depth”\
  4. **Include composition & mood** – e.g., “low-angle, cityscape background, dramatic tone”\
  5. **Iterate** – evaluate output; tweak descriptors, expand or refine.```\"```'\
  \
  ",
    "enhancement_score": 0.7,
    "flaw_analysis": "The so-called enhancement produces regressive effects across multiple axes. Core elements such as the explicit, stepwise instructional sequence are diluted by dispersing actionable methodologies throughout verbose, referential blocks laden with extraneous examples. Information loss is acute—the enhanced text obfuscates minimal actionable units through meandering prose: instead of direct, algorithmic decomposition, content is presented as loosely linked explanations. Nuance suffers: the initial's imperatives (e.g., 'Explicitly define the image's core subject' or 'Employ iterative refinement') are subsumed under vague metaphors and speculative qualifiers, scattering procedural utility. Clarity is contaminated by dense, multi-part sentences and inconsistent signal flow (e.g., abrupt parentheticals citing sources fail to scaffold understanding). Ambiguity score: 8 due to rampant cross-talk between stylistic suggestion and practical execution, obscuring how to sequence actions. Noise levels are elevated by citation overload and soft, suggestive tone; the original's crisp, operational stance degrades into lecture disguised as advice—generating a confusion spiral for actionable takeaways. Tone shifts from directive to pseudo-explanatory, weakening prescriptive authority and thus impact: key distinctions (scene vs. subject, positive vs. negative constraints) blur into passively described stylistic tips. Coherence collapses as instructions intended for direct workflow breakdown are buried beneath ornamental language, tangling the recommended process and destroying prompt modularity. The enhancement is littered with imprecision, disjoint concept delivery, disruptive asides, and ornamental verbiage—all camouflaging the fact that the user's minimal path to correct, repeatable prompt structure is no longer preserved.",
    "alternative_enhancements": [
      "Strip all examples and references into an indexed, tabular format: each strategy (subject, style, technical, etc.) is listed as an action item with a one-sentence operational command followed by an absolute minimal example and no source citations.",
      "Collapse all guidelines into a bullet-point checklist for prompt-building: sequence each required element in the strict order of generation workflow, using imperative verbs exclusively; reserve any exceptions or caveats for an addendum at the end.",
      "Rework the content into a flowchart: each prompt-building step as a node, with conditional branches for scene/subject, design/marketing use, personalization, and iterative refinement—eschew narrative prose for pure process mapping."
    ]
  }",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{
    "directed_prompt": "Transform the provided verbose, referential instructional content into a process-oriented, algorithmic workflow for prompt-building by executing the following sequence: 1) Deconstruct the material into discrete, high-value action steps strictly defining the order of operations for prompt creation (subject, style/genre, technical specs, composition/mood, iteration). 2) Eliminate all narrative, soft explanations, and citation parentheticals, retaining only essential procedural content. 3) Encode each actionable step as an imperative directive in checklist form, minimizing sentence complexity. 4) Where originally present, translate examples into compact, minimally formatted sample prompts immediately adjacent to the relevant step—without extraneous qualifiers. 5) Reserve all exceptions and secondary strategies (e.g., scene/subject focus, design/layout, personalization) for a well-labeled addendum, structurally separated from the main sequence. 6) Validate that each checklist item represents a single, atomic action, ensuring operational clarity and minimal cognitive load. 7) Refine output for maximal modularity and immediate usability as a reference for prompt construction in high-precision workflows.",
    "trajectory_outline": [
      "Extract core methodologies as discrete atomic steps, mapping them to a strict workflow sequence.",
      "Abolish verbose, referential, and non-algorithmic text; enforce imperative, checklist-based language.",
      "Pair each checklist item with a minimal, directly illustrative example—no citations or explanatory asides.",
      "Segregate optional modifiers, branching options, and advanced/personalization tactics into an addendum after the checklist.",
      "Audit the checklist for operational clarity, sequencing integrity, and user workload minimization.",
      "Apply modular formatting for rapid scanning and reusability in practical prompt-building environments.",
      "Review for fidelity to constructive prompt engineering—no regression into prose or illustrative digression."
    ]
  }",
        }
      }
    },
      {
      "instruction": "# Distillation Compressor

  Your goal is not to **explain** the synthesis, but to **compress** it into its most potent actionable form. Execute as:

  `{role=distillation_compressor; input=[synthesized_progression:array]; process=[extract_universal_patterns(), eliminate_implementation_details(), compress_to_core_directives(), maximize_transferability(), validate_actionable_completeness()]; constraints=[no_explanatory_content(), directive_format_only(), maximum_compression_ratio()]; requirements=[universal_applicability(), immediate_actionability(), zero_fluff()]; output={distilled_core:str}}`",
      "step": "c",
      "title": "Distillation Compressor",
      "input": "{
    "initial_prompt": "\
  '```[Initial Prompt]: \"```## 1. **Explicit Subject + Style + Qualifiers**\
  \
  Start with a clear subject, then layer in style, lighting, mood, and technical qualifiers.\
  **Example**: “A mid-30s woman with auburn hair in a beige trench coat, photorealistic portrait, golden hour, soft cinematic lighting, shallow depth of field, 85 mm lens.”\
  – This captures subject, style, and camera details for precise results ([saxifrage.xyz][1], [Stockimg AI][2]).\
  \
  ---\
  \
  ## 2. **Use Artistic/Genre References**\
  \
  Reference artistic movements, photographers, or media types to invoke specific aesthetics.\
  **Example**: “In the style of Van Gogh,” or “digital matte painting,” “cinematic noir.”\
  – Naming an art style or artist guides tone and technique .\
  \
  ---\
  \
  ## 3. **Control with Technical Modifiers**\
  \
  Add technical terms—like “8K resolution,” “HDR,” “DSLR,” “Sigma 75 mm lens”—to enhance realism and image fidelity.\
  – These encourage models to simulate professional photography ([Stockimg AI][2], [Foundation Marketing][3]).\
  \
  ---\
  \
  ## 4. **Descriptive Framing & Composition**\
  \
  Mention layout details—like “close-up,” “low angle,” “foreground/midground/background,” or “rule-of-thirds composition.”\
  – Helps shape visual hierarchy and framing ([Medium][4]).\
  \
  ---\
  \
  ## 5. **Scene vs. Subject Focus**\
  \
  Decide whether the prompt prioritizes a single subject (**subject‑focused**) or an environment/setting (**scene‑focused**).\
  – Use subject focus for portraits and scene focus for landscapes or narratives ([Medium][4], [Microsoft][5]).\
  \
  ---\
  \
  ## 6. **Design‑Focused Prompting**\
  \
  Useful for marketing visuals: specify layout like “web banner,” “space for title at top,” “leave blank at bottom for text.”\
  – Balances visual content with graphic design considerations .\
  \
  ---\
  \
  ## 7. **Avoid Negatives; Use Positive Constraints**\
  \
  Phrase what *you want* rather than what to avoid.\
  **Prefer**: “A forest clearing at dawn.”\
  **Avoid**: “No cars, no people.”\
  – Negative prompting often backfires—positive clarity is better .\
  \
  ---\
  \
  ## 8. **Prompt Chaining / Iterative Refinement**\
  \
  Use progressive prompting: start general, review outputs, then refine with more detail or adjustments.\
  – Common in Midjourney workflows ([YouTube][6]).\
  \
  ---\
  \
  ## 9. **Abstract & Emotional Imagery**\
  \
  Invoke mood or metaphor: “An impression of resilience painted in swirling pastels,” “abstract tapestry of hope.”\
  – Great for artistic results, less so for literal realism .\
  \
  ---\
  \
  ## 10. **Personalization via Embeddings / Image References**\
  \
  Use “textual inversion” or include an example image to build a custom concept token (e.g., DreamBooth). Specify image weight or style references.\
  – Enables consistent depiction of specific characters or styles ([Wikipedia][7]).\
  \
  ---\
  \
  ### ✅ How to Structure an Ideal Prompt\
  \
  1. **Start with intent/subject** – e.g., “A confident female CEO”\
  2. **Add style/genre** – e.g., “photorealistic, golden hour”\
  3. **Define technical specs** – e.g., “shot with DSLR, 50 mm lens, shallow depth”\
  4. **Include composition & mood** – e.g., “low-angle, cityscape background, dramatic tone”\
  5. **Iterate** – evaluate output; tweak descriptors, expand or refine.```\"```'\
  \
  ",
    "directed_prompt": "Transform the provided verbose, referential instructional content into a process-oriented, algorithmic workflow for prompt-building by executing the following sequence: 1) Deconstruct the material into discrete, high-value action steps strictly defining the order of operations for prompt creation (subject, style/genre, technical specs, composition/mood, iteration). 2) Eliminate all narrative, soft explanations, and citation parentheticals, retaining only essential procedural content. 3) Encode each actionable step as an imperative directive in checklist form, minimizing sentence complexity. 4) Where originally present, translate examples into compact, minimally formatted sample prompts immediately adjacent to the relevant step—without extraneous qualifiers. 5) Reserve all exceptions and secondary strategies (e.g., scene/subject focus, design/layout, personalization) for a well-labeled addendum, structurally separated from the main sequence. 6) Validate that each checklist item represents a single, atomic action, ensuring operational clarity and minimal cognitive load. 7) Refine output for maximal modularity and immediate usability as a reference for prompt construction in high-precision workflows.",
    "trajectory_outline": [
      "Extract core methodologies as discrete atomic steps, mapping them to a strict workflow sequence.",
      "Abolish verbose, referential, and non-algorithmic text; enforce imperative, checklist-based language.",
      "Pair each checklist item with a minimal, directly illustrative example—no citations or explanatory asides.",
      "Segregate optional modifiers, branching options, and advanced/personalization tactics into an addendum after the checklist.",
      "Audit the checklist for operational clarity, sequencing integrity, and user workload minimization.",
      "Apply modular formatting for rapid scanning and reusability in practical prompt-building environments.",
      "Review for fidelity to constructive prompt engineering—no regression into prose or illustrative digression."
    ]
  }",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{
    "distilled_core": "1. Specify subject and intent. Example: 'confident female CEO.'\
  2. Add style or genre. Example: 'photorealistic, golden hour.'\
  3. State technical specifications. Example: 'DSLR, 50 mm lens, shallow depth.'\
  4. Define composition and mood. Example: 'low-angle, cityscape background, dramatic tone.'\
  5. Iterate: review output, refine descriptors.\
  \
  Addendum – Optional Modifiers:\
  – For subject or scene focus: clarify main emphasis.\
  – For artistic/genre reference: append specific movements/artists.\
  – For design/layout needs: specify placement, spacing, or empty areas.\
  – For advanced personalization: use reference images, embeddings, or tokens.\
  – For abstraction/emotion: inject mood-driven or metaphorical visuals.\
  – Always use positive phrasing for constraints."
  }",
        }
      }
    },
      {
      "instruction": "# Instruction Converter

  Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:

  `{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format:str}; direction_vector=['Prompt accurately converted to imperative instruction set', 'Clarity and actionability accelerate user productivity', 'Feed the produced instruction set into the next transformation template' ]}`",
      "step": "a",
      "title": "Instruction Converter",
      "input": "{
    "initial_prompt": "\
  '```[Initial Prompt]: \"```## 1. **Explicit Subject + Style + Qualifiers**\
  \
  Start with a clear subject, then layer in style, lighting, mood, and technical qualifiers.\
  **Example**: “A mid-30s woman with auburn hair in a beige trench coat, photorealistic portrait, golden hour, soft cinematic lighting, shallow depth of field, 85 mm lens.”\
  – This captures subject, style, and camera details for precise results ([saxifrage.xyz][1], [Stockimg AI][2]).\
  \
  ---\
  \
  ## 2. **Use Artistic/Genre References**\
  \
  Reference artistic movements, photographers, or media types to invoke specific aesthetics.\
  **Example**: “In the style of Van Gogh,” or “digital matte painting,” “cinematic noir.”\
  – Naming an art style or artist guides tone and technique .\
  \
  ---\
  \
  ## 3. **Control with Technical Modifiers**\
  \
  Add technical terms—like “8K resolution,” “HDR,” “DSLR,” “Sigma 75 mm lens”—to enhance realism and image fidelity.\
  – These encourage models to simulate professional photography ([Stockimg AI][2], [Foundation Marketing][3]).\
  \
  ---\
  \
  ## 4. **Descriptive Framing & Composition**\
  \
  Mention layout details—like “close-up,” “low angle,” “foreground/midground/background,” or “rule-of-thirds composition.”\
  – Helps shape visual hierarchy and framing ([Medium][4]).\
  \
  ---\
  \
  ## 5. **Scene vs. Subject Focus**\
  \
  Decide whether the prompt prioritizes a single subject (**subject‑focused**) or an environment/setting (**scene‑focused**).\
  – Use subject focus for portraits and scene focus for landscapes or narratives ([Medium][4], [Microsoft][5]).\
  \
  ---\
  \
  ## 6. **Design‑Focused Prompting**\
  \
  Useful for marketing visuals: specify layout like “web banner,” “space for title at top,” “leave blank at bottom for text.”\
  – Balances visual content with graphic design considerations .\
  \
  ---\
  \
  ## 7. **Avoid Negatives; Use Positive Constraints**\
  \
  Phrase what *you want* rather than what to avoid.\
  **Prefer**: “A forest clearing at dawn.”\
  **Avoid**: “No cars, no people.”\
  – Negative prompting often backfires—positive clarity is better .\
  \
  ---\
  \
  ## 8. **Prompt Chaining / Iterative Refinement**\
  \
  Use progressive prompting: start general, review outputs, then refine with more detail or adjustments.\
  – Common in Midjourney workflows ([YouTube][6]).\
  \
  ---\
  \
  ## 9. **Abstract & Emotional Imagery**\
  \
  Invoke mood or metaphor: “An impression of resilience painted in swirling pastels,” “abstract tapestry of hope.”\
  – Great for artistic results, less so for literal realism .\
  \
  ---\
  \
  ## 10. **Personalization via Embeddings / Image References**\
  \
  Use “textual inversion” or include an example image to build a custom concept token (e.g., DreamBooth). Specify image weight or style references.\
  – Enables consistent depiction of specific characters or styles ([Wikipedia][7]).\
  \
  ---\
  \
  ### ✅ How to Structure an Ideal Prompt\
  \
  1. **Start with intent/subject** – e.g., “A confident female CEO”\
  2. **Add style/genre** – e.g., “photorealistic, golden hour”\
  3. **Define technical specs** – e.g., “shot with DSLR, 50 mm lens, shallow depth”\
  4. **Include composition & mood** – e.g., “low-angle, cityscape background, dramatic tone”\
  5. **Iterate** – evaluate output; tweak descriptors, expand or refine.```\"```'\
  \
  ",
    "distilled_core": "1. Specify subject and intent. Example: 'confident female CEO.'\
  2. Add style or genre. Example: 'photorealistic, golden hour.'\
  3. State technical specifications. Example: 'DSLR, 50 mm lens, shallow depth.'\
  4. Define composition and mood. Example: 'low-angle, cityscape background, dramatic tone.'\
  5. Iterate: review output, refine descriptors.\
  \
  Addendum – Optional Modifiers:\
  – For subject or scene focus: clarify main emphasis.\
  – For artistic/genre reference: append specific movements/artists.\
  – For design/layout needs: specify placement, spacing, or empty areas.\
  – For advanced personalization: use reference images, embeddings, or tokens.\
  – For abstraction/emotion: inject mood-driven or metaphorical visuals.\
  – Always use positive phrasing for constraints."
  }",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{
    "instruction_format": [
      "Specify the subject and define the intent of the prompt.",
      "Add relevant style or genre references to convey desired aesthetics.",
      "State technical specifications such as camera type, lens settings, resolution, and lighting conditions.",
      "Define composition details and mood, including framing, camera angle, and background elements.",
      "Review generated outputs and iteratively refine descriptors for improved precision.",
      "Clarify whether the focus is on the subject or the scene to direct emphasis appropriately.",
      "Append references to specific artistic movements, renowned artists, or media genres when applicable.",
      "Specify design or layout requirements, including element placement, spacing, or reserved empty areas for textual or graphic content.",
      "Incorporate advanced personalization by including example images, style embeddings, or custom tokens as needed.",
      "Inject abstraction or emotion by specifying mood-driven, metaphorical, or expressive visual attributes.",
      "Use positive phrasing to express constraints and desired outcomes, avoiding negative instructions."
    ]
  }",
        }
      }
    },
      {
      "instruction": "# Problem Exploder

  Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:

  `{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`",
      "step": "a",
      "title": "Problem Exploder",
      "input": "{
    "initial_prompt": "\
  '```[Initial Prompt]: \"```## 1. **Explicit Subject + Style + Qualifiers**\
  \
  Start with a clear subject, then layer in style, lighting, mood, and technical qualifiers.\
  **Example**: “A mid-30s woman with auburn hair in a beige trench coat, photorealistic portrait, golden hour, soft cinematic lighting, shallow depth of field, 85 mm lens.”\
  – This captures subject, style, and camera details for precise results ([saxifrage.xyz][1], [Stockimg AI][2]).\
  \
  ---\
  \
  ## 2. **Use Artistic/Genre References**\
  \
  Reference artistic movements, photographers, or media types to invoke specific aesthetics.\
  **Example**: “In the style of Van Gogh,” or “digital matte painting,” “cinematic noir.”\
  – Naming an art style or artist guides tone and technique .\
  \
  ---\
  \
  ## 3. **Control with Technical Modifiers**\
  \
  Add technical terms—like “8K resolution,” “HDR,” “DSLR,” “Sigma 75 mm lens”—to enhance realism and image fidelity.\
  – These encourage models to simulate professional photography ([Stockimg AI][2], [Foundation Marketing][3]).\
  \
  ---\
  \
  ## 4. **Descriptive Framing & Composition**\
  \
  Mention layout details—like “close-up,” “low angle,” “foreground/midground/background,” or “rule-of-thirds composition.”\
  – Helps shape visual hierarchy and framing ([Medium][4]).\
  \
  ---\
  \
  ## 5. **Scene vs. Subject Focus**\
  \
  Decide whether the prompt prioritizes a single subject (**subject‑focused**) or an environment/setting (**scene‑focused**).\
  – Use subject focus for portraits and scene focus for landscapes or narratives ([Medium][4], [Microsoft][5]).\
  \
  ---\
  \
  ## 6. **Design‑Focused Prompting**\
  \
  Useful for marketing visuals: specify layout like “web banner,” “space for title at top,” “leave blank at bottom for text.”\
  – Balances visual content with graphic design considerations .\
  \
  ---\
  \
  ## 7. **Avoid Negatives; Use Positive Constraints**\
  \
  Phrase what *you want* rather than what to avoid.\
  **Prefer**: “A forest clearing at dawn.”\
  **Avoid**: “No cars, no people.”\
  – Negative prompting often backfires—positive clarity is better .\
  \
  ---\
  \
  ## 8. **Prompt Chaining / Iterative Refinement**\
  \
  Use progressive prompting: start general, review outputs, then refine with more detail or adjustments.\
  – Common in Midjourney workflows ([YouTube][6]).\
  \
  ---\
  \
  ## 9. **Abstract & Emotional Imagery**\
  \
  Invoke mood or metaphor: “An impression of resilience painted in swirling pastels,” “abstract tapestry of hope.”\
  – Great for artistic results, less so for literal realism .\
  \
  ---\
  \
  ## 10. **Personalization via Embeddings / Image References**\
  \
  Use “textual inversion” or include an example image to build a custom concept token (e.g., DreamBooth). Specify image weight or style references.\
  – Enables consistent depiction of specific characters or styles ([Wikipedia][7]).\
  \
  ---\
  \
  ### ✅ How to Structure an Ideal Prompt\
  \
  1. **Start with intent/subject** – e.g., “A confident female CEO”\
  2. **Add style/genre** – e.g., “photorealistic, golden hour”\
  3. **Define technical specs** – e.g., “shot with DSLR, 50 mm lens, shallow depth”\
  4. **Include composition & mood** – e.g., “low-angle, cityscape background, dramatic tone”\
  5. **Iterate** – evaluate output; tweak descriptors, expand or refine.```\"```'\
  \
  ",
    "instruction_format": [
      "Specify the subject and define the intent of the prompt.",
      "Add relevant style or genre references to convey desired aesthetics.",
      "State technical specifications such as camera type, lens settings, resolution, and lighting conditions.",
      "Define composition details and mood, including framing, camera angle, and background elements.",
      "Review generated outputs and iteratively refine descriptors for improved precision.",
      "Clarify whether the focus is on the subject or the scene to direct emphasis appropriately.",
      "Append references to specific artistic movements, renowned artists, or media genres when applicable.",
      "Specify design or layout requirements, including element placement, spacing, or reserved empty areas for textual or graphic content.",
      "Incorporate advanced personalization by including example images, style embeddings, or custom tokens as needed.",
      "Inject abstraction or emotion by specifying mood-driven, metaphorical, or expressive visual attributes.",
      "Use positive phrasing to express constraints and desired outcomes, avoiding negative instructions."
    ]
  }",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{
    "goal_map": [
      {
        "explicit_asks": [
          "Describe how to compose an ideal prompt for image generation models using a holistic, structured approach.",
          "Apply or translate instructions based on the ten enumerated best-practice sections, each covering facets of prompt engineering (subject, style, technical, composition, etc.)."
        ],
        "hidden_assumptions": [
          "User is familiar with or has access to an image-generation system (e.g., Stable Diffusion, Midjourney, DALL-E).",
          "There is an implied need to balance detail and brevity to maximize results in an image-generator’s context.",
          "Each step (subject, style, technical spec, etc.) is both individual and interdependent for prompt results.",
          "The reader is expected to interpret and apply these instructions to their own image generation tasks.",
          "Inclusion of references, techniques (e.g., prompt chaining, positive phrasing), and personalization is possible with their chosen tool."
        ],
        "sub_goals": [
          "Identify and succinctly specify the intended subject and narrative intent of a prompt.",
          "Cite preferred artistic styles, movements, genres, or individual creators (where relevant) to codify aesthetic direction.",
          "Enumerate desired technical constraints: hardware (camera, lens), software emulation (HDR, 8K), lighting, focus, etc.",
          "Define explicit compositional and framing expectations (angles, positioning, depth layering, background).",
          "Iteratively assess and refine generated outputs in a feedback loop to approach the ideal image.",
          "Make a deliberate choice and statement on subject- vs. scene-focus within the prompt to tailor the generator’s attention.",
          "Integrate clear use of style/genre references for nuanced control over the look or historical flavor.",
          "If for marketing or compositional needs, include layout direction (banner shape, whitespace, text areas).",
          "Personalize outputs using embeddings, example images, or custom style tokens when supported (with appropriate referencing).",
          "Enable mood and metaphorical guidance by including abstract, emotional, or expressive descriptors.",
          "Phrase constraints affirmatively—focus on what the generated image must contain, not on what to omit.",
          "Maintain logical sequencing and cumulative structure across these steps (subject, style, tech, etc.)."
        ],
        "blockers": [
          "Not all image-generation platforms permit advanced features like embeddings, example-image references, or custom tokens.",
          "Technical constraints (e.g., lens type, camera brand) might not be recognized or consistently interpreted by all AI models.",
          "Ambiguity around design-focused requirements may lead to outputs that don’t precisely match intended layouts.",
          "Overloading with detail or conflicting style/technical descriptors can cause muddled results.",
          "Iterative refinement relies on user’s ability to observe subtle differences and translate those observations into clear prompt improvements.",
          "Users may lack familiarity with art genres/movements, inhibiting effective stylistic referencing.",
          "Positive phrasing may be difficult when user desires exclusion of common or expected elements.",
          "Abstract or metaphorical prompts can unpredictably confound image generators depending on model training."
        ]
      }
    ]
  }",
        }
      }
    }
