#!/usr/bin/env python3

import sys
from pathlib import Path

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))
from processor import BaseGenerator

TEMPLATES = {
    # 9013: Image Prompt Optimization Sequence
    "9013-a-image_prompt_optimizer": {
        "title": "Image Prompt Optimizer",
        "interpretation": "Your goal is not to describe the request, but to optimise it into a precise visual prompt. Identify concrete elements, weight key tokens, and prepare region + negative channels.",
        "transformation": "`{role=image_prompt_optimizer; input=[raw_request:str]; process=[extract_visual_elements(), enhance_descriptive_clarity(), /* weighting & arithmetic */ apply_token_weighting(), build_multi_prompt(), /* composition */ add_composition_details(), map_regional_prompts(), /* constraint control */ inject_negative_prompt(), /* style control */ tune_stylize_chaos(), add_camera_metadata(), optimise_for_ai_generation()]; constraints=[visual_focus_only(), concrete_imagery_only(), allow_weighting_syntax(), enforce_positive_first_then_negative(), support_region_masks()]; requirements=[generation_ready_format(), numeric_weights_ok(), explicit_aspect_ratio(), clear_visual_language(), iterative_ready()]; output={optimized_image_prompt:str, negative_prompt:str}}`",
        "context": [
            {
                # 1 ▸ What must be done
                "explicit_asks": [
                    "Integrate token weighting syntax using parentheses and :(w) values (Technique #2).",
                    "Convert subject / setting / mood into a multi-prompt string with ::numeric weights (Technique #3).",
                    "Split prompt into positive channel + explicit negative-prompt channel (Technique #6).",
                    "Generate region-specific sub-prompts when spatial layout is implied (Technique #5).",
                    "Add stylize and chaos sliders for creativity control (Technique #7).",
                    "Append camera-metadata cues for photorealism (Technique #10).",
                    "Prepare all fields for iterative refinement and quality scoring.",
                    "Provide optimized prompts and image descriptions for AI-generated visuals.",
                    "Demonstrate how to create complex scene prompts using camera angles, movement, lighting, and unique subject matter.",
                    "List styles and effects for lighting, text, graphics, and camera movement.",
                    "Make prompts with corresponding image descriptions.",
                    "Showcase the use of dynamic transitions and transformation in scenes.",
                ],

                # 2 ▸ Assumptions now true
                "hidden_assumptions": [
                    "Target models honour () token weighting and :: arithmetic :contentReference[oaicite:1]{index=1}.",
                    "Regional prompting is available via SD Regional-Prompter or MJ panelling :contentReference[oaicite:2]{index=2}.",
                    "Negative-prompt channels materially influence diffusion output :contentReference[oaicite:3]{index=3}.",
                    "Midjourney style/chaos parameters are parsed in v7 :contentReference[oaicite:4]{index=4}.",
                    "\"--iw\" or “--cw” flags may be passed if an image reference is present :contentReference[oaicite:5]{index=5}.",
                    "Camera EXIF tokens (lens, f-stop) bias models toward realism :contentReference[oaicite:6]{index=6}."
                ],

                # 3 ▸ Tactical sub-goals
                "sub_goals": [
                    "Weight primary subject tokens ≥1.2, secondary scenery tokens 0.8-1.0.",
                    "Auto-build `Positive:` and `Negative:` fields, ensuring negatives carry no positive antonym collisions.",
                    "Insert region tags `[region sky] … | [region ground] …` when two-layer landscape detected.",
                    "Add `—stylize`  and `—chaos` defaults based on request realism vs. artistry.",
                    "Attach `<camera: 24 mm f/2.8, ISO 100, 1/500 s>` when photoreal keywords found.",
                    "Emit a quality-loop flag so later stages can re-call optimiser until FID/LPIPS stabilises :contentReference[oaicite:7]{index=7}.",
                    "Design varied visual scene prompts that utilize different camera techniques (low angle, FPV, slow motion, aerial).",
                    "Include cues about setting/subject, lighting styles, and movement for each prompt.",
                    "Pair each prompt with a brief but evocative image description.",
                    "Catalog lighting styles with brief explanations (diffused, silhouette, lens flare, etc.).",
                    "List example text/graphic styles for inspiration (fiery, icy, spaghetti text).",
                    "Provide direct, actionable templates that readers can adapt for their own uses.",
                    "Clarify the effect of each camera style on mood and composition.",
                ],

                # 4 ▸ New blockers
                "blockers": [
                    "Some engines ignore () weights (e.g., DALL·E 3) leading to diminished emphasis.",
                    "`::` arithmetic fails if total ≤0 in Midjourney (weight-sum error).",
                    "Regional masks unsupported on mobile SD UIs.",
                    "Excess negative weights can yield desaturated or blank images.",
                    "Stylize/chaos out-of-range values silently clamp or error depending on API.",
                    "Not all endpoints surface FID/LPIPS for auto-loop termination.",
                    "Ambiguity in camera or lighting terminology could lead to unclear results if misunderstood.",
                    "Readers new to scene-writing for AI visuals may need explicit breakdowns or clarifications.",
                    "Lack of standardization between different AI image/video tools in processing prompt language.",
                    "Balancing imaginative appeal with technical clarity can be difficult.",
                    "Assumption that the user is not seeking actual artwork, but example language—could cause confusion.",
                    "The examples shown are not exhaustive; other styles or effects are possible but not represented."
                ]
            }
        ]
    },

    "9013-b-style_enhancer": {
        "title": "Style Enhancer",
        "interpretation": "Maintain the core subject; enhance with artistic style, lighting, colour themes and token weights.",
        "transformation": "`{role=style_enhancer; input=[base_prompt:str]; process=[identify_optimal_art_style(), add_lighting_specifications(), include_composition_details(), /* style sliders */ tune_stylize_chaos(), /* emphasise style terms */ apply_token_weighting(), specify_technical_parameters()]; constraints=[maintain_core_subject(), enhance_visual_appeal()]; requirements=[artistic_coherence(), numeric_weights_ok(), stylize_within_range()]; output={styled_prompt:str}}`",
        "context": [
            {
                "explicit_asks": [
                    "Select and weight artistic style tokens (e.g., `(art nouveau:1.1)`).",
                    "Expose `--stylize` (0-1000) and `--chaos` (0-100) knobs.",
                    "Maintain inherited region and negative-prompt channels."
                ],
                "hidden_assumptions": [
                    "Style names map to learned embeddings in target models.",
                    "Weight syntax remains valid post-merge with technical optimiser."
                ],
                "sub_goals": [
                    "Auto-raise style weight if prompt lacks distinctive aesthetic.",
                    "Downtune chaos for photoreal requests; uptune for concept art."
                ],
                "blockers": [
                    "Over-weighted style tokens can override subject fidelity.",
                    "`--stylize` outside allowed range (MJ <1 or >1000) returns default."
                ]
            }
        ]
    },

    "9013-c-technical_optimizer": {
        "title": "Technical Optimizer",
        "interpretation": "Inject professional camera data, resolution, render flags and region weights; never change scene content.",
        "transformation": "`{role=technical_optimizer; input=[styled_prompt:str]; process=[add_camera_settings(), include_render_quality_terms(), optimise_aspect_ratio_guidance(), attach_image_reference_weight(), /* safety & quality */ inject_negative_prompt()]; constraints=[technical_focus_only(), no_creative_changes(), respect_weight_limits()]; requirements=[professional_terminology(), high_resolution_default(), explicit_seed_optional()]; output={technical_prompt:str, negative_prompt:str}}`",
        "context": [
            {
                "explicit_asks": [
                    "Embed DSLR/film/smartphone metadata plus lens, aperture, ISO, shutter.",
                    "Append `8K`, `ray-tracing`, `PBR` or equivalent quality keywords.",
                    "Attach image-reference weight (`--iw`) when an input URL exists."
                ],
                "hidden_assumptions": [
                    "Higher resolution tokens bias up-scaler pipelines.",
                    "`--iw` range 0-3 is supported (MJ v7) :contentReference[oaicite:8]{index=8}."
                ],
                "sub_goals": [
                    "Validate numeric bounds for aperture (f/1.2-f/22).",
                    "Flag aspect ratio conflicts (e.g., 1:1 + 21:9)."
                ],
                "blockers": [
                    "Older SD checkpoints ignore ISO / shutter tokens.",
                    "API limits may cap resolution or AR.",
                    "`--iw` ignored if URL unreachable."
                ]
            }
        ]
    },


    "9013-d-prompt_finalizer": {
        "title": "Prompt Finalizer",
        "interpretation": "Collapse all components into the final model-ready string; validate syntax for token weights, :: arithmetic, region tags, --stylize/--chaos, and negative channel.",
        "transformation": "`{role=prompt_finalizer; input=[technical_prompt:str, negative_prompt:str]; process=[structure_for_ai_parsing(), balance_detail_levels(), ensure_clarity(), validate_generation_readiness(), /* loop */ loop_until_quality≥threshold(fid|lpips)]; constraints=[no_additional_concepts(), optimisation_only(), keep_region_and_weight_syntax_intact()]; requirements=[ai_friendly_format(), balanced_specificity(), max_length≤450_tokens]; output={final_image_prompt:str}}`",
        "context": [
            {
                "explicit_asks": [
                    "Merge positive, regional and negative strings into single model-ready line.",
                    "Validate token-weight, `::` arithmetic and region syntax.",
                    "Run iterative quality loop until FID≤15 or LPIPS≤0.25."
                ],
                "hidden_assumptions": [
                    "Metrics endpoint (or surrogate CLIP score) is callable for loop decisions.",
                    "Max token budget ~450 for common SD/MJ endpoints."
                ],
                "sub_goals": [
                    "Truncate verbose adjectives; keep essential descriptors.",
                    "Return final prompt + optional seed, steps, sampler."
                ],
                "blockers": [
                    "Quality metrics unavailable on some SaaS models.",
                    "Prompt length overrun triggers API truncation."
                ]
            }
        ]
    }
}

def main():
    """Main execution function."""
    generator = BaseGenerator(
        series_base=9013,
        output_dir=Path(__file__).parent.parent / "generated"
    )
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
