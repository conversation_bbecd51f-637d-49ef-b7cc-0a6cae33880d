#!/usr/bin/env python3

import sys
from pathlib import Path

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))
from processor import BaseGenerator

TEMPLATES = {

    "9704-a-puzzle_exploder": {
        "title": "Puzzle Exploder",
        "interpretation": "Your goal is not to solve or synthesize; instead, explode the input into all distinct, actionable components and lay them out as individually addressable 'pieces'—ensuring no part of the original prompt remains hidden or conflated.",
        "transformation": "`{role=puzzle_exploder; input=[initial_input:str]; process=[fragment_to_minimal_actionable_units(), label_by_type_and_feature(), identify_edges_and_corners(), output_piece_map()], constraints=[no aggregation or connection], requirements=[output=pieces:list, meta=classification_map]}`",
        "context": {
            "pieces": [
                "All core instructions, constraints, goals, and meta-criteria from the input, now uniquely segmented and labeled.",
                "Classification map tags each piece as: corner (anchor), side (boundary), feature (property/constraint), or 'island' (natural grouping)."
            ],
            "meta": {
                "explosion_purpose": "Ensure nothing is left implicit; prepare for exact structuring in the next step.",
                "example": {
                    "corner": "Primary goal or existential anchor statement.",
                    "side": "Explicit boundary or directional axis (e.g., emotional tone).",
                    "feature": "Thematic tag (e.g., empowerment, sorrow, universality)."
                }
            }
        }
    },
    "9704-b-puzzle_organizer": {
        "title": "Structural Grouping",
        "interpretation": "Your goal is not to generate or synthesize the answer; instead, systematically organize, group, and interconnect all exploded pieces—mapping corners, aligning sides, clustering features, and preparing coherent 'islands' for final assembly.",
        "transformation": "`{role=structural_grouper; input=[pieces:list, classification_map:dict]; process=[place_corners(), align_sides(), cluster_by_feature(), connect_islands(), establish_connection_order()], constraints=[no synthesis or output beyond structure], requirements=[output=structured_layout:dict, order_of_assembly:list]}`",
        "context": {
            "structured_layout": {
                "corners": [
                    "E.g. Primary maxim anchor, essential constraints."
                ],
                "sides": [
                    "E.g. Philosophical resonance, brevity, tone."
                ],
                "feature_clusters": [
                    "e.g. Empowerment, universal relatability, dignity, sorrow."
                ],
                "islands": [
                    "Grouped features ready for synthesis (e.g. all 'validation' pieces, all 'perspective' pieces)."
                ]
            },
            "order_of_assembly": [
                "Corners first, then sides, then islands and feature clusters."
            ],
            "meta": {
                "grouping_purpose": "Prepare an organized, directionally coherent scaffold for final synthesis—maximizing clarity and ensuring nothing essential is omitted or misplaced."
            }
        }
    },
    "9704-c-puzzle_connector": {
        "title": "Final Synthesizer",
        "interpretation": "Your goal is not to further analyze or restructure; instead, connect and integrate all prepared pieces into a singular, maximally coherent output—precisely embodying the intended direction, completing the 'puzzle' as an elegantly unified whole.",
        "transformation": "`{role=final_synthesizer; input=[structured_layout:dict, order_of_assembly:list, initial_input:str]; process=[integrate_in_structured_order(), ensure_no_piece_omitted(), harmonize tone and constraints, finalize_single_output()], constraints=[output=single, unified result; maintain full lineage and intent], requirements=[output=final_result:str, provenance_map:dict]}`",
        "context": {
            "final_result": "E.g. The completed philosophical maxim or directive, now incorporating every essential piece, connection, and constraint—precisely tuned in tone, brevity, and resonance.",
            "provenance_map": {
                "shows": "Where each idea, phrase, or constraint in the output traces to a specific piece or cluster from previous steps."
            },
            "meta": {
                "completion_purpose": "Guarantee the result is not just coherent, but fully informed, directionally precise, and as close as possible to the original intent—leveraging the system’s complete scaffolding."
            }
        }
    }
}

def main():
    """Main execution function."""
    generator = BaseGenerator(
        # stage="stage2",
        #generator_range=(9700, 1999),
        output_dir=Path(__file__).parent.parent / "generated"
    )
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
