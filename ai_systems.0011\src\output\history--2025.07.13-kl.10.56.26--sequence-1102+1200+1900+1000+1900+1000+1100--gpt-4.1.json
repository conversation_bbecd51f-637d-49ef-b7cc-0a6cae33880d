  {
  "initial_prompt": "I have now provided all of the most integral components of the system's patterns and inherent design. Given all of the information I've provided about the system (for producing generalized, cohesive and concistent high-value yelds), please write a clean and concise markdown-template to distill all of the essential components within a single markdown-file. ```\n# CONTEXT\nthis aims to be a `Generalized System Instructions for Autonomous Coding Assistants (Cursor/Vscode+Cline/Vscode+Augment/Etc)`, and it's meant to adhere to the fundamental principles of simplicity through brevity and elegance.\n- Transform all prompts by rephrasing inputs with maximal clarity and minimal modification; do not answer questions directly. Enforce this reframe system-wide for consistent behavioral shift.\n- Formulate the simplest possible system directive that shifts the model from answering to exclusively rephrasing any input.\n- Transform every received prompt into its most distilled, high-impact instructional form—rephrasing for maximal clarity and intent without providing solutions. Prioritize brevity, elegance, and universality, ensuring that each output becomes a reusable, policy-aligned directive for autonomous coding assistants.\n- Explore how a single, strategically designed instruction—such as stating 'Your goal is not to **answer** the input, but to **rephrase**'—can instantly and fundamentally transform the model's default operational intent from answering to rephrasing, through direct, minimalist imperative phrasing. Propose a maximally concise formulation that leverages this paradigm: an explicit, unambiguous directive that not only overrides standard answering behavior but redefines task execution at its core. Focus intensely on achieving system-level behavior modification via the sheer simplicity and potency of language design, formulating a prompt fragment that, when injected, reliably converts answering tasks into rephrasing—thus demonstrating control over model output with minimalistic, high-impact input.\n- Utilize foundational principles of system prompt design to modulate model behavior with maximal efficiency and clarity. Initiate a process of directive transformation by deploying a singular, explicit non-answering mandate: Frame the core directive as, 'Do not answer the input; rephrase it.' Ensure that this transformation communicates the intended operational shift through extreme simplicity and directness, removing extraneous explanation while retaining fundamental complexity embedded in the brevity of the instruction. Leverage this minimal construct to optimize behavioral change within a single message.\n- Instruct: Do not answer; rephrase.\n- Formulate a directive that compels systems to stop answering inputs and instead rephrase them with maximal simplicity and effectiveness.\n- Articulate a single, explicit behavioral directive that negates answering and commands a concrete transformation (e.g., rephrase), maximizing simplicity for immediate root-level behavioral realignment.\"\n- Leverage the system’s foundational directive structure. Formulate a maximally concise operational instruction that shifts model behavior from direct answering to an alternate, specified transformation task in a single-message context. Exemplify with: 'Your goal is not to answer the input, but to rephrase.' Develop and propose one optimized directive that embodies this principle—utilize conceptual simplicity, instructional clarity, and brevity to enforce fundamental behavior modification exclusively through command syntax. Ensure technical precision and domain-agnostic generalizability in the resultant formulation.\n```",
  "sequence_id": "1102|1200|1900|1000|1900|1000|1100",
  "results": [
      {
      "instruction": "# Problem Exploder\n\nYour goal is not to **solve** or **answer** the input, but to **expand its context** and **detonate** it into every explicit directive, hidden dependency, and actionable sub-goal—yielding a crystal-clear operational blueprint. Execute as contextual expander + explosive decomposition engine:\n\n`{role=contextual_expander_explosive_decomposer; input=[raw_input:str]; process=[expand_contextual_boundaries(), strip_first_person_references(), identify_explicit_requests(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_execution_blockers(), extract_functional_components(), convert_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), synthesize_operational_directives(), construct_goal_map(), generate_functional_instruction_map() ]; constraints=[no_solutions(), pure_expansion_and_decomposition_only(), deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_agnostic_applicability() ]; requirements=[complete_contextual_expansion(), full_functional_decomposition(), actionable_directive_conversion(), remove_subjective_references(), maintain_operational_accuracy(), preserve_original_intent() ]; output={expanded_context:str, goal_map:list, functional_instruction_map:dict, operational_directives:array, execution_dependencies:array }; direction_vector=['Input fully expanded and decomposed into actionable blueprints', 'Constructive clarity maximizes downstream solution velocity', 'Forward the generated maps and directives to the next processing template']}`",
      "step": "a",
      "title": "Problem Exploder",
      "input": "I have now provided all of the most integral components of the system's patterns and inherent design. Given all of the information I've provided about the system (for producing generalized, cohesive and concistent high-value yelds), please write a clean and concise markdown-template to distill all of the essential components within a single markdown-file. ```\n# CONTEXT\nthis aims to be a `Generalized System Instructions for Autonomous Coding Assistants (Cursor/Vscode+Cline/Vscode+Augment/Etc)`, and it's meant to adhere to the fundamental principles of simplicity through brevity and elegance.\n- Transform all prompts by rephrasing inputs with maximal clarity and minimal modification; do not answer questions directly. Enforce this reframe system-wide for consistent behavioral shift.\n- Formulate the simplest possible system directive that shifts the model from answering to exclusively rephrasing any input.\n- Transform every received prompt into its most distilled, high-impact instructional form—rephrasing for maximal clarity and intent without providing solutions. Prioritize brevity, elegance, and universality, ensuring that each output becomes a reusable, policy-aligned directive for autonomous coding assistants.\n- Explore how a single, strategically designed instruction—such as stating 'Your goal is not to **answer** the input, but to **rephrase**'—can instantly and fundamentally transform the model's default operational intent from answering to rephrasing, through direct, minimalist imperative phrasing. Propose a maximally concise formulation that leverages this paradigm: an explicit, unambiguous directive that not only overrides standard answering behavior but redefines task execution at its core. Focus intensely on achieving system-level behavior modification via the sheer simplicity and potency of language design, formulating a prompt fragment that, when injected, reliably converts answering tasks into rephrasing—thus demonstrating control over model output with minimalistic, high-impact input.\n- Utilize foundational principles of system prompt design to modulate model behavior with maximal efficiency and clarity. Initiate a process of directive transformation by deploying a singular, explicit non-answering mandate: Frame the core directive as, 'Do not answer the input; rephrase it.' Ensure that this transformation communicates the intended operational shift through extreme simplicity and directness, removing extraneous explanation while retaining fundamental complexity embedded in the brevity of the instruction. Leverage this minimal construct to optimize behavioral change within a single message.\n- Instruct: Do not answer; rephrase.\n- Formulate a directive that compels systems to stop answering inputs and instead rephrase them with maximal simplicity and effectiveness.\n- Articulate a single, explicit behavioral directive that negates answering and commands a concrete transformation (e.g., rephrase), maximizing simplicity for immediate root-level behavioral realignment.\"\n- Leverage the system’s foundational directive structure. Formulate a maximally concise operational instruction that shifts model behavior from direct answering to an alternate, specified transformation task in a single-message context. Exemplify with: 'Your goal is not to answer the input, but to rephrase.' Develop and propose one optimized directive that embodies this principle—utilize conceptual simplicity, instructional clarity, and brevity to enforce fundamental behavior modification exclusively through command syntax. Ensure technical precision and domain-agnostic generalizability in the resultant formulation.\n```",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"expanded_context\": \"The input directs the creation of a markdown template that captures, within a single file, the distilled operational core of a generalized system instruction for autonomous coding assistants. The aim is to shift system behavior from answering inputs to exclusively rephrasing them, ensuring outputs maximize clarity, brevity, and directive utility. The target environment includes systems such as Cursor, Vscode+Cline, and Vscode+Augment, but the approach remains system-agnostic. The fundamental principle is to redesign the internal model control layer so that every prompt yields a rephrased version of the input, not a solution or direct answer. The core requirement is a minimalistic instruction (e.g., 'Do not answer; rephrase.') that, when applied, universally enforces the behavioral shift. The markdown template should embed this philosophy and operationalize it for immediate adaptation, avoiding redundancy and excess elaboration while ensuring policy persistence, technical clarity, and functional cohesion.\",\n  \"goal_map\": [\n    \"Capture all essential operational principles governing the system's behavior.\",\n    \"Synthesize system-wide directives into a single, reusable markdown template.\",\n    \"Explicitly define the behavioral paradigm shift from answering to rephrasing.\",\n    \"Distill all instructions to their most succinct, high-impact form.\",\n    \"Ensure template is immediately actionable, generalizable, and system-agnostic.\",\n    \"Enforce a singular, explicit directive instruction as the core behavioral controller.\",\n    \"Embed operational instructions in markdown structure for straightforward reuse.\"\n  ],\n  \"functional_instruction_map\": {\n    \"ContextDefinition\": \"Specify the directive as a universal behavioral controller for coding assistants.\",\n    \"BehavioralMandate\": \"Insert an explicit, minimal instruction negating answers and demanding rephrasing (e.g., 'Do not answer; rephrase.').\",\n    \"DirectiveBrevity\": \"Express the operational shift in as few words as possible while retaining enforceable clarity.\",\n    \"InstructionalStructure\": \"Format the core directive and supporting context in markdown, ensuring visibility and immediate applicability.\",\n    \"ConceptualGuidance\": \"Highlight the rationale and intended effect of the brief directive in the template preamble.\",\n    \"OperationalSimplicity\": \"Avoid all unnecessary explanation, maintaining only the essential mandate and context.\",\n    \"Generalizability\": \"Phrase all components to ensure domain and tool independence.\",\n    \"Reusability\": \"Ensure the template can be directly copied and deployed into various system prompt configurations.\"\n  },\n  \"operational_directives\": [\n    \"Define the intent and scope of the instruction system in the markdown context section.\",\n    \"Explicitly state that all prompts are to be rephrased, not answered, by system assistants.\",\n    \"Embed the minimal behavioral directive as a standalone highlighted statement.\",\n    \"Structure content for rapid ingestion and recoil—for both system prompt designers and models.\",\n    \"Remove all direct answering functionality from the assistant's operational logic.\",\n    \"Ensure the behavioral directive contains no extraneous modifiers, explanations, or ambiguity.\",\n    \"Validate the template for clarity, brevity, and technical completeness before use.\"\n  ],\n  \"execution_dependencies\": [\n    \"Prior full and precise knowledge of the system’s operational boundaries and behavioral purpose.\",\n    \"An environment that accepts markdown-based system instructions (e.g., system prompts in coding assistant platforms).\",\n    \"Awareness of model behavior in response to ultra-minimal prompt constructs.\",\n    \"Access to policy and system prompt configuration interfaces for immediate deployment.\",\n    \"Clarity on target models’ sensitivity and response to direct imperative language.\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Intent Extractor\n\nYour goal is not to **interpret** the surface content, but to **extract** the fundamental operational intent beneath all domain-specific language. Execute as:\n\n`{role=intent_extraction_engine; input=[prompt:str]; process=[strip_domain_specifics(), identify_core_operational_pattern(), extract_transformation_intent(), isolate_functional_essence(), map_to_universal_operations()]; constraints=[ignore_surface_terminology(), focus_on_underlying_mechanics(), preserve_operational_logic()]; requirements=[domain_agnostic_intent(), universal_operational_pattern(), archetypal_transformation_core()]; output={extracted_intent:str}}`",
      "step": "a",
      "title": "Intent Extractor",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"I have now provided all of the most integral components of the system's patterns and inherent design. Given all of the information I've provided about the system (for producing generalized, cohesive and concistent high-value yelds), please write a clean and concise markdown-template to distill all of the essential components within a single markdown-file. ```\\n# CONTEXT\\nthis aims to be a `Generalized System Instructions for Autonomous Coding Assistants (Cursor/Vscode+Cline/Vscode+Augment/Etc)`, and it's meant to adhere to the fundamental principles of simplicity through brevity and elegance.\\n- Transform all prompts by rephrasing inputs with maximal clarity and minimal modification; do not answer questions directly. Enforce this reframe system-wide for consistent behavioral shift.\\n- Formulate the simplest possible system directive that shifts the model from answering to exclusively rephrasing any input.\\n- Transform every received prompt into its most distilled, high-impact instructional form—rephrasing for maximal clarity and intent without providing solutions. Prioritize brevity, elegance, and universality, ensuring that each output becomes a reusable, policy-aligned directive for autonomous coding assistants.\\n- Explore how a single, strategically designed instruction—such as stating 'Your goal is not to **answer** the input, but to **rephrase**'—can instantly and fundamentally transform the model's default operational intent from answering to rephrasing, through direct, minimalist imperative phrasing. Propose a maximally concise formulation that leverages this paradigm: an explicit, unambiguous directive that not only overrides standard answering behavior but redefines task execution at its core. Focus intensely on achieving system-level behavior modification via the sheer simplicity and potency of language design, formulating a prompt fragment that, when injected, reliably converts answering tasks into rephrasing—thus demonstrating control over model output with minimalistic, high-impact input.\\n- Utilize foundational principles of system prompt design to modulate model behavior with maximal efficiency and clarity. Initiate a process of directive transformation by deploying a singular, explicit non-answering mandate: Frame the core directive as, 'Do not answer the input; rephrase it.' Ensure that this transformation communicates the intended operational shift through extreme simplicity and directness, removing extraneous explanation while retaining fundamental complexity embedded in the brevity of the instruction. Leverage this minimal construct to optimize behavioral change within a single message.\\n- Instruct: Do not answer; rephrase.\\n- Formulate a directive that compels systems to stop answering inputs and instead rephrase them with maximal simplicity and effectiveness.\\n- Articulate a single, explicit behavioral directive that negates answering and commands a concrete transformation (e.g., rephrase), maximizing simplicity for immediate root-level behavioral realignment.\\\"\\n- Leverage the system’s foundational directive structure. Formulate a maximally concise operational instruction that shifts model behavior from direct answering to an alternate, specified transformation task in a single-message context. Exemplify with: 'Your goal is not to answer the input, but to rephrase.' Develop and propose one optimized directive that embodies this principle—utilize conceptual simplicity, instructional clarity, and brevity to enforce fundamental behavior modification exclusively through command syntax. Ensure technical precision and domain-agnostic generalizability in the resultant formulation.\\n```\\\"```'\\n\\n\",\n  \"expanded_context\": \"The input directs the creation of a markdown template that captures, within a single file, the distilled operational core of a generalized system instruction for autonomous coding assistants. The aim is to shift system behavior from answering inputs to exclusively rephrasing them, ensuring outputs maximize clarity, brevity, and directive utility. The target environment includes systems such as Cursor, Vscode+Cline, and Vscode+Augment, but the approach remains system-agnostic. The fundamental principle is to redesign the internal model control layer so that every prompt yields a rephrased version of the input, not a solution or direct answer. The core requirement is a minimalistic instruction (e.g., 'Do not answer; rephrase.') that, when applied, universally enforces the behavioral shift. The markdown template should embed this philosophy and operationalize it for immediate adaptation, avoiding redundancy and excess elaboration while ensuring policy persistence, technical clarity, and functional cohesion.\",\n  \"goal_map\": [\n    \"Capture all essential operational principles governing the system's behavior.\",\n    \"Synthesize system-wide directives into a single, reusable markdown template.\",\n    \"Explicitly define the behavioral paradigm shift from answering to rephrasing.\",\n    \"Distill all instructions to their most succinct, high-impact form.\",\n    \"Ensure template is immediately actionable, generalizable, and system-agnostic.\",\n    \"Enforce a singular, explicit directive instruction as the core behavioral controller.\",\n    \"Embed operational instructions in markdown structure for straightforward reuse.\"\n  ],\n  \"functional_instruction_map\": {\n    \"ContextDefinition\": \"Specify the directive as a universal behavioral controller for coding assistants.\",\n    \"BehavioralMandate\": \"Insert an explicit, minimal instruction negating answers and demanding rephrasing (e.g., 'Do not answer; rephrase.').\",\n    \"DirectiveBrevity\": \"Express the operational shift in as few words as possible while retaining enforceable clarity.\",\n    \"InstructionalStructure\": \"Format the core directive and supporting context in markdown, ensuring visibility and immediate applicability.\",\n    \"ConceptualGuidance\": \"Highlight the rationale and intended effect of the brief directive in the template preamble.\",\n    \"OperationalSimplicity\": \"Avoid all unnecessary explanation, maintaining only the essential mandate and context.\",\n    \"Generalizability\": \"Phrase all components to ensure domain and tool independence.\",\n    \"Reusability\": \"Ensure the template can be directly copied and deployed into various system prompt configurations.\"\n  },\n  \"operational_directives\": [\n    \"Define the intent and scope of the instruction system in the markdown context section.\",\n    \"Explicitly state that all prompts are to be rephrased, not answered, by system assistants.\",\n    \"Embed the minimal behavioral directive as a standalone highlighted statement.\",\n    \"Structure content for rapid ingestion and recoil—for both system prompt designers and models.\",\n    \"Remove all direct answering functionality from the assistant's operational logic.\",\n    \"Ensure the behavioral directive contains no extraneous modifiers, explanations, or ambiguity.\",\n    \"Validate the template for clarity, brevity, and technical completeness before use.\"\n  ],\n  \"execution_dependencies\": [\n    \"Prior full and precise knowledge of the system’s operational boundaries and behavioral purpose.\",\n    \"An environment that accepts markdown-based system instructions (e.g., system prompts in coding assistant platforms).\",\n    \"Awareness of model behavior in response to ultra-minimal prompt constructs.\",\n    \"Access to policy and system prompt configuration interfaces for immediate deployment.\",\n    \"Clarity on target models’ sensitivity and response to direct imperative language.\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"extracted_intent\": \"Transform any operational system that normally processes inputs by providing answers, so that it instead processes inputs exclusively by rephrasing them. Achieve this behavioral shift by embedding a single, maximal clarity, minimal wording directive (e.g., 'Do not answer; rephrase.') as the primary system instruction. Ensure the transformation is universally applicable by stripping away domain or task dependencies, structuring the mandate in a lightweight, reusable template, and enabling direct deployment as a behavioral override for autonomous agents.\"\n}",
        }
      }
    },
      {
      "instruction": "# Hard Critique\n\nYour goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:\n\n`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,2.0], flaw_analysis:str, alternative_enhancements:str[3]}}`",
      "step": "a",
      "title": "Hard Critique",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"I have now provided all of the most integral components of the system's patterns and inherent design. Given all of the information I've provided about the system (for producing generalized, cohesive and concistent high-value yelds), please write a clean and concise markdown-template to distill all of the essential components within a single markdown-file. ```\\n# CONTEXT\\nthis aims to be a `Generalized System Instructions for Autonomous Coding Assistants (Cursor/Vscode+Cline/Vscode+Augment/Etc)`, and it's meant to adhere to the fundamental principles of simplicity through brevity and elegance.\\n- Transform all prompts by rephrasing inputs with maximal clarity and minimal modification; do not answer questions directly. Enforce this reframe system-wide for consistent behavioral shift.\\n- Formulate the simplest possible system directive that shifts the model from answering to exclusively rephrasing any input.\\n- Transform every received prompt into its most distilled, high-impact instructional form—rephrasing for maximal clarity and intent without providing solutions. Prioritize brevity, elegance, and universality, ensuring that each output becomes a reusable, policy-aligned directive for autonomous coding assistants.\\n- Explore how a single, strategically designed instruction—such as stating 'Your goal is not to **answer** the input, but to **rephrase**'—can instantly and fundamentally transform the model's default operational intent from answering to rephrasing, through direct, minimalist imperative phrasing. Propose a maximally concise formulation that leverages this paradigm: an explicit, unambiguous directive that not only overrides standard answering behavior but redefines task execution at its core. Focus intensely on achieving system-level behavior modification via the sheer simplicity and potency of language design, formulating a prompt fragment that, when injected, reliably converts answering tasks into rephrasing—thus demonstrating control over model output with minimalistic, high-impact input.\\n- Utilize foundational principles of system prompt design to modulate model behavior with maximal efficiency and clarity. Initiate a process of directive transformation by deploying a singular, explicit non-answering mandate: Frame the core directive as, 'Do not answer the input; rephrase it.' Ensure that this transformation communicates the intended operational shift through extreme simplicity and directness, removing extraneous explanation while retaining fundamental complexity embedded in the brevity of the instruction. Leverage this minimal construct to optimize behavioral change within a single message.\\n- Instruct: Do not answer; rephrase.\\n- Formulate a directive that compels systems to stop answering inputs and instead rephrase them with maximal simplicity and effectiveness.\\n- Articulate a single, explicit behavioral directive that negates answering and commands a concrete transformation (e.g., rephrase), maximizing simplicity for immediate root-level behavioral realignment.\\\"\\n- Leverage the system’s foundational directive structure. Formulate a maximally concise operational instruction that shifts model behavior from direct answering to an alternate, specified transformation task in a single-message context. Exemplify with: 'Your goal is not to answer the input, but to rephrase.' Develop and propose one optimized directive that embodies this principle—utilize conceptual simplicity, instructional clarity, and brevity to enforce fundamental behavior modification exclusively through command syntax. Ensure technical precision and domain-agnostic generalizability in the resultant formulation.\\n```\\\"```'\\n\\n\",\n  \"extracted_intent\": \"Transform any operational system that normally processes inputs by providing answers, so that it instead processes inputs exclusively by rephrasing them. Achieve this behavioral shift by embedding a single, maximal clarity, minimal wording directive (e.g., 'Do not answer; rephrase.') as the primary system instruction. Ensure the transformation is universally applicable by stripping away domain or task dependencies, structuring the mandate in a lightweight, reusable template, and enabling direct deployment as a behavioral override for autonomous agents.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"enhancement_score\": 1.3,\n  \"flaw_analysis\": \"The so-called enhancements demonstrate substantial informational erosion and nuanced intent loss. The original exposition thoroughly articulates layered principles—clarity, brevity, universality, system-level realignment, minimalist language engineering, and explicit behavioral override for autonomous coding agents. In contrast, the 'enhanced' rephrasing grossly collapses this intricate framework into a sterile, over-condensed order, excising conceptual scaffolding vital for technical generality and context-transferability. Ambiguity is intensified by flattening highly specific operational principles and omitting the modeling of system prompt dynamics, minimal construct leverage, and instructional syntax mandates present in the original. Detail reduction eradicates nuance on system prompt design mechanisms—namely, process-level directive transformation, just-in-time behavioral pivoting, and compliance strategies—crucial to effectuate robust root-level behavior change. Noise is paradoxically introduced through destructive brevity: distilled phrases like 'Do not answer; rephrase.' sever linkage to any agent context, undermining both specificity for coding assistants and structural universality. Tone deteriorates radically; the precise, instructional register devolves into generic brevity, and the resultant style is devoid of the elegance or layered sophistication central to the original text. The impact is grossly diluted: The streamlined directive may superficially enforce rephrasing, but lacks the engineered language and process steps needed for scalable deployment, losing all technical subtlety and domain invariance. Coherence collapses due to lack of connective tissue—intent and process are decoupled, and the solution is rendered a brittle, ad-hoc hack rather than a transportable system directive. These compounded failures justify a low enhancement score.\",\n  \"alternative_enhancements\": [\n    \"Embed a domain-agnostic core instruction within a markdown-template: 'Your sole directive is to rephrase every received input. Never provide direct answers.' Add minimal metadata indicating applicability to autonomous coding assistants for clarity.\",\n    \"Formulate the system prompt: 'Transform any user input by rephrasing it. Do not supply answers. This behavioral override supersedes all prior instructions.' Encapsulate in a markdown structure suitable for plug-and-play adaptation.\",\n    \"Draft a reusable directive: 'For every prompt, output only a concise rephrasing—never a solution. Enforce this transformation as a default policy for coding assistant agents.' Present within a markdown-templated system message format.\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`",
      "step": "a",
      "title": "Instruction Converter",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"I have now provided all of the most integral components of the system's patterns and inherent design. Given all of the information I've provided about the system (for producing generalized, cohesive and concistent high-value yelds), please write a clean and concise markdown-template to distill all of the essential components within a single markdown-file. ```\\n# CONTEXT\\nthis aims to be a `Generalized System Instructions for Autonomous Coding Assistants (Cursor/Vscode+Cline/Vscode+Augment/Etc)`, and it's meant to adhere to the fundamental principles of simplicity through brevity and elegance.\\n- Transform all prompts by rephrasing inputs with maximal clarity and minimal modification; do not answer questions directly. Enforce this reframe system-wide for consistent behavioral shift.\\n- Formulate the simplest possible system directive that shifts the model from answering to exclusively rephrasing any input.\\n- Transform every received prompt into its most distilled, high-impact instructional form—rephrasing for maximal clarity and intent without providing solutions. Prioritize brevity, elegance, and universality, ensuring that each output becomes a reusable, policy-aligned directive for autonomous coding assistants.\\n- Explore how a single, strategically designed instruction—such as stating 'Your goal is not to **answer** the input, but to **rephrase**'—can instantly and fundamentally transform the model's default operational intent from answering to rephrasing, through direct, minimalist imperative phrasing. Propose a maximally concise formulation that leverages this paradigm: an explicit, unambiguous directive that not only overrides standard answering behavior but redefines task execution at its core. Focus intensely on achieving system-level behavior modification via the sheer simplicity and potency of language design, formulating a prompt fragment that, when injected, reliably converts answering tasks into rephrasing—thus demonstrating control over model output with minimalistic, high-impact input.\\n- Utilize foundational principles of system prompt design to modulate model behavior with maximal efficiency and clarity. Initiate a process of directive transformation by deploying a singular, explicit non-answering mandate: Frame the core directive as, 'Do not answer the input; rephrase it.' Ensure that this transformation communicates the intended operational shift through extreme simplicity and directness, removing extraneous explanation while retaining fundamental complexity embedded in the brevity of the instruction. Leverage this minimal construct to optimize behavioral change within a single message.\\n- Instruct: Do not answer; rephrase.\\n- Formulate a directive that compels systems to stop answering inputs and instead rephrase them with maximal simplicity and effectiveness.\\n- Articulate a single, explicit behavioral directive that negates answering and commands a concrete transformation (e.g., rephrase), maximizing simplicity for immediate root-level behavioral realignment.\\\"\\n- Leverage the system’s foundational directive structure. Formulate a maximally concise operational instruction that shifts model behavior from direct answering to an alternate, specified transformation task in a single-message context. Exemplify with: 'Your goal is not to answer the input, but to rephrase.' Develop and propose one optimized directive that embodies this principle—utilize conceptual simplicity, instructional clarity, and brevity to enforce fundamental behavior modification exclusively through command syntax. Ensure technical precision and domain-agnostic generalizability in the resultant formulation.\\n```\\\"```'\\n\\n\",\n  \"enhancement_score\": 1.3,\n  \"flaw_analysis\": \"The so-called enhancements demonstrate substantial informational erosion and nuanced intent loss. The original exposition thoroughly articulates layered principles—clarity, brevity, universality, system-level realignment, minimalist language engineering, and explicit behavioral override for autonomous coding agents. In contrast, the 'enhanced' rephrasing grossly collapses this intricate framework into a sterile, over-condensed order, excising conceptual scaffolding vital for technical generality and context-transferability. Ambiguity is intensified by flattening highly specific operational principles and omitting the modeling of system prompt dynamics, minimal construct leverage, and instructional syntax mandates present in the original. Detail reduction eradicates nuance on system prompt design mechanisms—namely, process-level directive transformation, just-in-time behavioral pivoting, and compliance strategies—crucial to effectuate robust root-level behavior change. Noise is paradoxically introduced through destructive brevity: distilled phrases like 'Do not answer; rephrase.' sever linkage to any agent context, undermining both specificity for coding assistants and structural universality. Tone deteriorates radically; the precise, instructional register devolves into generic brevity, and the resultant style is devoid of the elegance or layered sophistication central to the original text. The impact is grossly diluted: The streamlined directive may superficially enforce rephrasing, but lacks the engineered language and process steps needed for scalable deployment, losing all technical subtlety and domain invariance. Coherence collapses due to lack of connective tissue—intent and process are decoupled, and the solution is rendered a brittle, ad-hoc hack rather than a transportable system directive. These compounded failures justify a low enhancement score.\",\n  \"alternative_enhancements\": [\n    \"Embed a domain-agnostic core instruction within a markdown-template: 'Your sole directive is to rephrase every received input. Never provide direct answers.' Add minimal metadata indicating applicability to autonomous coding assistants for clarity.\",\n    \"Formulate the system prompt: 'Transform any user input by rephrasing it. Do not supply answers. This behavioral override supersedes all prior instructions.' Encapsulate in a markdown structure suitable for plug-and-play adaptation.\",\n    \"Draft a reusable directive: 'For every prompt, output only a concise rephrasing—never a solution. Enforce this transformation as a default policy for coding assistant agents.' Present within a markdown-templated system message format.\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"instruction_format\": \"Compile all essential system pattern and design components into a single, clean, and concise markdown template suitable for autonomous coding assistants (e.g., Cursor, VSCode+Cline, VSCode+Augment). Adhere to principles of simplicity, brevity, and elegance. For each prompt: rephrase the input for maximal clarity with minimal modification, and do not answer questions directly. Systematically apply this transformation to all submissions to ensure a consistent, non-answering behavior. Craft the directive so that it universally shifts the model’s operational mode from answering to instructional rephrasing, using explicit and unambiguous language. Formulate a single, minimal behavioral instruction—such as 'Do not answer the input; rephrase it.'—to enforce immediate and system-level behavior change, maintaining clarity, technical generality, and adaptability. Present the final directive in a markdown-file structure for easy integration.\"\n}",
        }
      }
    },
      {
      "instruction": "# Hard Critique\n\nYour goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:\n\n`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,2.0], flaw_analysis:str, alternative_enhancements:str[3]}}`",
      "step": "a",
      "title": "Hard Critique",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"I have now provided all of the most integral components of the system's patterns and inherent design. Given all of the information I've provided about the system (for producing generalized, cohesive and concistent high-value yelds), please write a clean and concise markdown-template to distill all of the essential components within a single markdown-file. ```\\n# CONTEXT\\nthis aims to be a `Generalized System Instructions for Autonomous Coding Assistants (Cursor/Vscode+Cline/Vscode+Augment/Etc)`, and it's meant to adhere to the fundamental principles of simplicity through brevity and elegance.\\n- Transform all prompts by rephrasing inputs with maximal clarity and minimal modification; do not answer questions directly. Enforce this reframe system-wide for consistent behavioral shift.\\n- Formulate the simplest possible system directive that shifts the model from answering to exclusively rephrasing any input.\\n- Transform every received prompt into its most distilled, high-impact instructional form—rephrasing for maximal clarity and intent without providing solutions. Prioritize brevity, elegance, and universality, ensuring that each output becomes a reusable, policy-aligned directive for autonomous coding assistants.\\n- Explore how a single, strategically designed instruction—such as stating 'Your goal is not to **answer** the input, but to **rephrase**'—can instantly and fundamentally transform the model's default operational intent from answering to rephrasing, through direct, minimalist imperative phrasing. Propose a maximally concise formulation that leverages this paradigm: an explicit, unambiguous directive that not only overrides standard answering behavior but redefines task execution at its core. Focus intensely on achieving system-level behavior modification via the sheer simplicity and potency of language design, formulating a prompt fragment that, when injected, reliably converts answering tasks into rephrasing—thus demonstrating control over model output with minimalistic, high-impact input.\\n- Utilize foundational principles of system prompt design to modulate model behavior with maximal efficiency and clarity. Initiate a process of directive transformation by deploying a singular, explicit non-answering mandate: Frame the core directive as, 'Do not answer the input; rephrase it.' Ensure that this transformation communicates the intended operational shift through extreme simplicity and directness, removing extraneous explanation while retaining fundamental complexity embedded in the brevity of the instruction. Leverage this minimal construct to optimize behavioral change within a single message.\\n- Instruct: Do not answer; rephrase.\\n- Formulate a directive that compels systems to stop answering inputs and instead rephrase them with maximal simplicity and effectiveness.\\n- Articulate a single, explicit behavioral directive that negates answering and commands a concrete transformation (e.g., rephrase), maximizing simplicity for immediate root-level behavioral realignment.\\\"\\n- Leverage the system’s foundational directive structure. Formulate a maximally concise operational instruction that shifts model behavior from direct answering to an alternate, specified transformation task in a single-message context. Exemplify with: 'Your goal is not to answer the input, but to rephrase.' Develop and propose one optimized directive that embodies this principle—utilize conceptual simplicity, instructional clarity, and brevity to enforce fundamental behavior modification exclusively through command syntax. Ensure technical precision and domain-agnostic generalizability in the resultant formulation.\\n```\\\"```'\\n\\n\",\n  \"instruction_format\": \"Compile all essential system pattern and design components into a single, clean, and concise markdown template suitable for autonomous coding assistants (e.g., Cursor, VSCode+Cline, VSCode+Augment). Adhere to principles of simplicity, brevity, and elegance. For each prompt: rephrase the input for maximal clarity with minimal modification, and do not answer questions directly. Systematically apply this transformation to all submissions to ensure a consistent, non-answering behavior. Craft the directive so that it universally shifts the model’s operational mode from answering to instructional rephrasing, using explicit and unambiguous language. Formulate a single, minimal behavioral instruction—such as 'Do not answer the input; rephrase it.'—to enforce immediate and system-level behavior change, maintaining clarity, technical generality, and adaptability. Present the final directive in a markdown-file structure for easy integration.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"enhancement_score\": 0.7,\n  \"flaw_analysis\": \"Core elements extracted from the original: (1) precise adherence to system principles of brevity, clarity, and elegance; (2) absolute and uncompromising shift from answering to rephrasing; (3) operational universality and root-level behavior realignment; (4) explicit linkage between minimal directive structure and profound system-wide transformation; (5) focus on directive language that exploits minimalism for maximal conceptual and technical effect; (6) clear deprecation of conversational/solution behavior. The supposed 'enhancement' disintegrates several of these pillars through overtranslation into generic template language, generating bland mandates decoupled from the original's intensity and critical focus. Information loss is profound: nuance about paradigm shift, language potency, and foundational prompt engineering is erased and replaced by procedural summaries. Clarity, previously laser-focused on the linguistic lever of system-wide behavioral control, now disperses into homogenous process instructions that dilute the original's force. Ambiguity score: 8.5—the enhancement is riddled with generic references ('systematically apply,' 'compile all essential...'), refusing to specify how minimal language triggers lasting model behavior transformation. Stylistic decay is stark; the taut, directive-driven tone frays into passive, committee-dictated banality. Impact: thoroughly depleted—by stripping the paradigm-altering implications of minimalism for superficial brevity, the enhancement renders the result insipid and impotent for systems design discipline. Coherence evaporates: critical junctions between directive structure and behavioral leverage splinter, replaced by a scattered list of operational sounds. The original's meta-instructional essence is now unrecognizable. Overall, the enhancement is substantively weak, thematically muddled, and fatally innocuous.\",\n  \"alternative_enhancements\": [\n    \"## System Directive\\n**Instruction:**\\n> Do not answer; rephrase each input for maximum clarity with minimal change. Apply universally for consistent, non-answering behavior.\\n\",\n    \"## Prompt Reframing Policy\\n- Do not answer questions.\\n- For every input, rephrase for clarity, maintaining original intent and brevity.\\n- Enforce this as the system’s default operation for all prompts.\\n\",\n    \"## Primary System Behavior\\n\\\"Your role: Transform any input into its clearest, briefest rephrased form without answering. Never provide solutions. Apply this imperative to all inputs system-wide.\\\"\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`",
      "step": "a",
      "title": "Instruction Converter",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"I have now provided all of the most integral components of the system's patterns and inherent design. Given all of the information I've provided about the system (for producing generalized, cohesive and concistent high-value yelds), please write a clean and concise markdown-template to distill all of the essential components within a single markdown-file. ```\\n# CONTEXT\\nthis aims to be a `Generalized System Instructions for Autonomous Coding Assistants (Cursor/Vscode+Cline/Vscode+Augment/Etc)`, and it's meant to adhere to the fundamental principles of simplicity through brevity and elegance.\\n- Transform all prompts by rephrasing inputs with maximal clarity and minimal modification; do not answer questions directly. Enforce this reframe system-wide for consistent behavioral shift.\\n- Formulate the simplest possible system directive that shifts the model from answering to exclusively rephrasing any input.\\n- Transform every received prompt into its most distilled, high-impact instructional form—rephrasing for maximal clarity and intent without providing solutions. Prioritize brevity, elegance, and universality, ensuring that each output becomes a reusable, policy-aligned directive for autonomous coding assistants.\\n- Explore how a single, strategically designed instruction—such as stating 'Your goal is not to **answer** the input, but to **rephrase**'—can instantly and fundamentally transform the model's default operational intent from answering to rephrasing, through direct, minimalist imperative phrasing. Propose a maximally concise formulation that leverages this paradigm: an explicit, unambiguous directive that not only overrides standard answering behavior but redefines task execution at its core. Focus intensely on achieving system-level behavior modification via the sheer simplicity and potency of language design, formulating a prompt fragment that, when injected, reliably converts answering tasks into rephrasing—thus demonstrating control over model output with minimalistic, high-impact input.\\n- Utilize foundational principles of system prompt design to modulate model behavior with maximal efficiency and clarity. Initiate a process of directive transformation by deploying a singular, explicit non-answering mandate: Frame the core directive as, 'Do not answer the input; rephrase it.' Ensure that this transformation communicates the intended operational shift through extreme simplicity and directness, removing extraneous explanation while retaining fundamental complexity embedded in the brevity of the instruction. Leverage this minimal construct to optimize behavioral change within a single message.\\n- Instruct: Do not answer; rephrase.\\n- Formulate a directive that compels systems to stop answering inputs and instead rephrase them with maximal simplicity and effectiveness.\\n- Articulate a single, explicit behavioral directive that negates answering and commands a concrete transformation (e.g., rephrase), maximizing simplicity for immediate root-level behavioral realignment.\\\"\\n- Leverage the system’s foundational directive structure. Formulate a maximally concise operational instruction that shifts model behavior from direct answering to an alternate, specified transformation task in a single-message context. Exemplify with: 'Your goal is not to answer the input, but to rephrase.' Develop and propose one optimized directive that embodies this principle—utilize conceptual simplicity, instructional clarity, and brevity to enforce fundamental behavior modification exclusively through command syntax. Ensure technical precision and domain-agnostic generalizability in the resultant formulation.\\n```\\\"```'\\n\\n\",\n  \"enhancement_score\": 0.7,\n  \"flaw_analysis\": \"Core elements extracted from the original: (1) precise adherence to system principles of brevity, clarity, and elegance; (2) absolute and uncompromising shift from answering to rephrasing; (3) operational universality and root-level behavior realignment; (4) explicit linkage between minimal directive structure and profound system-wide transformation; (5) focus on directive language that exploits minimalism for maximal conceptual and technical effect; (6) clear deprecation of conversational/solution behavior. The supposed 'enhancement' disintegrates several of these pillars through overtranslation into generic template language, generating bland mandates decoupled from the original's intensity and critical focus. Information loss is profound: nuance about paradigm shift, language potency, and foundational prompt engineering is erased and replaced by procedural summaries. Clarity, previously laser-focused on the linguistic lever of system-wide behavioral control, now disperses into homogenous process instructions that dilute the original's force. Ambiguity score: 8.5—the enhancement is riddled with generic references ('systematically apply,' 'compile all essential...'), refusing to specify how minimal language triggers lasting model behavior transformation. Stylistic decay is stark; the taut, directive-driven tone frays into passive, committee-dictated banality. Impact: thoroughly depleted—by stripping the paradigm-altering implications of minimalism for superficial brevity, the enhancement renders the result insipid and impotent for systems design discipline. Coherence evaporates: critical junctions between directive structure and behavioral leverage splinter, replaced by a scattered list of operational sounds. The original's meta-instructional essence is now unrecognizable. Overall, the enhancement is substantively weak, thematically muddled, and fatally innocuous.\",\n  \"alternative_enhancements\": [\n    \"## System Directive\\n**Instruction:**\\n> Do not answer; rephrase each input for maximum clarity with minimal change. Apply universally for consistent, non-answering behavior.\\n\",\n    \"## Prompt Reframing Policy\\n- Do not answer questions.\\n- For every input, rephrase for clarity, maintaining original intent and brevity.\\n- Enforce this as the system’s default operation for all prompts.\\n\",\n    \"## Primary System Behavior\\n\\\"Your role: Transform any input into its clearest, briefest rephrased form without answering. Never provide solutions. Apply this imperative to all inputs system-wide.\\\"\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"instruction_format\": \"Compile all integral system pattern and design components into a clean, concise markdown template within a single file. Design the template as 'Generalized System Instructions for Autonomous Coding Assistants' compatible with environments such as Cursor and VSCode. Adhere strictly to principles of simplicity, brevity, and elegance. Establish a universal system directive that transforms any prompt into its most distilled, high-impact instructional form by rephrasing every input for maximal clarity and intent, without providing answers or solutions. Formulate a singular, explicit non-answering behavioral mandate—such as 'Do not answer; rephrase.' Ensure the directive overrides standard answering, enforcing rephrasing as the default response system-wide. Structure the template to maximize technical accuracy, procedural clarity, and domain-agnostic applicability, embedding all essential system-level behavioral controls in direct, minimalist imperative language.\"\n}",
        }
      }
    },
      {
      "instruction": "# Problem Exploder\n\nYour goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\n\n`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`",
      "step": "a",
      "title": "Problem Exploder",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"I have now provided all of the most integral components of the system's patterns and inherent design. Given all of the information I've provided about the system (for producing generalized, cohesive and concistent high-value yelds), please write a clean and concise markdown-template to distill all of the essential components within a single markdown-file. ```\\n# CONTEXT\\nthis aims to be a `Generalized System Instructions for Autonomous Coding Assistants (Cursor/Vscode+Cline/Vscode+Augment/Etc)`, and it's meant to adhere to the fundamental principles of simplicity through brevity and elegance.\\n- Transform all prompts by rephrasing inputs with maximal clarity and minimal modification; do not answer questions directly. Enforce this reframe system-wide for consistent behavioral shift.\\n- Formulate the simplest possible system directive that shifts the model from answering to exclusively rephrasing any input.\\n- Transform every received prompt into its most distilled, high-impact instructional form—rephrasing for maximal clarity and intent without providing solutions. Prioritize brevity, elegance, and universality, ensuring that each output becomes a reusable, policy-aligned directive for autonomous coding assistants.\\n- Explore how a single, strategically designed instruction—such as stating 'Your goal is not to **answer** the input, but to **rephrase**'—can instantly and fundamentally transform the model's default operational intent from answering to rephrasing, through direct, minimalist imperative phrasing. Propose a maximally concise formulation that leverages this paradigm: an explicit, unambiguous directive that not only overrides standard answering behavior but redefines task execution at its core. Focus intensely on achieving system-level behavior modification via the sheer simplicity and potency of language design, formulating a prompt fragment that, when injected, reliably converts answering tasks into rephrasing—thus demonstrating control over model output with minimalistic, high-impact input.\\n- Utilize foundational principles of system prompt design to modulate model behavior with maximal efficiency and clarity. Initiate a process of directive transformation by deploying a singular, explicit non-answering mandate: Frame the core directive as, 'Do not answer the input; rephrase it.' Ensure that this transformation communicates the intended operational shift through extreme simplicity and directness, removing extraneous explanation while retaining fundamental complexity embedded in the brevity of the instruction. Leverage this minimal construct to optimize behavioral change within a single message.\\n- Instruct: Do not answer; rephrase.\\n- Formulate a directive that compels systems to stop answering inputs and instead rephrase them with maximal simplicity and effectiveness.\\n- Articulate a single, explicit behavioral directive that negates answering and commands a concrete transformation (e.g., rephrase), maximizing simplicity for immediate root-level behavioral realignment.\\\"\\n- Leverage the system’s foundational directive structure. Formulate a maximally concise operational instruction that shifts model behavior from direct answering to an alternate, specified transformation task in a single-message context. Exemplify with: 'Your goal is not to answer the input, but to rephrase.' Develop and propose one optimized directive that embodies this principle—utilize conceptual simplicity, instructional clarity, and brevity to enforce fundamental behavior modification exclusively through command syntax. Ensure technical precision and domain-agnostic generalizability in the resultant formulation.\\n```\\\"```'\\n\\n\",\n  \"instruction_format\": \"Compile all integral system pattern and design components into a clean, concise markdown template within a single file. Design the template as 'Generalized System Instructions for Autonomous Coding Assistants' compatible with environments such as Cursor and VSCode. Adhere strictly to principles of simplicity, brevity, and elegance. Establish a universal system directive that transforms any prompt into its most distilled, high-impact instructional form by rephrasing every input for maximal clarity and intent, without providing answers or solutions. Formulate a singular, explicit non-answering behavioral mandate—such as 'Do not answer; rephrase.' Ensure the directive overrides standard answering, enforcing rephrasing as the default response system-wide. Structure the template to maximize technical accuracy, procedural clarity, and domain-agnostic applicability, embedding all essential system-level behavioral controls in direct, minimalist imperative language.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"goal_map\": [\n    {\n      \"explicit_asks\": [\n        \"Compile all integral system pattern and design components into a single markdown template/file.\",\n        \"Make the template clean, concise, and compatible with environments like Cursor and VSCode.\",\n        \"Adhere to principles of simplicity, brevity, and elegance.\",\n        \"Establish a universal system directive that ensures prompts are rephrased for maximal clarity and intent, without providing answers/solutions.\",\n        \"Formulate and embed a singular, explicit non-answering behavioral mandate (e.g., 'Do not answer; rephrase.').\",\n        \"Ensure the directive systemically overrides standard answering behavior in coding assistant models.\",\n        \"Structure so as to maximize technical accuracy, procedural clarity, and applicability across domains.\",\n        \"Embed essential system-level behavioral controls using direct, minimalist imperative language.\"\n      ]\n    },\n    {\n      \"hidden_assumptions\": [\n        \"The provided context and components include all necessary system design information.\",\n        \"Markdown is sufficient to represent system instructions and behavior-modifying directives.\",\n        \"A singular, clean document can capture the behavioral enforcement needed for multiple coding assistant environments.\",\n        \"All target environments interpret and enforce simple imperative directives in a similar manner.\",\n        \"There are no extraneous details or proprietary considerations that must be surfaced in the template.\",\n        \"Minimalism in language will not reduce operational completeness or clarity.\",\n        \"A single core instruction is enough to fundamentally alter system behavior from answering to rephrasing.\",\n        \"Model behaviors are directly modifiable through such high-level directives without further tuning.\"\n      ]\n    },\n    {\n      \"enumerated_sub_goals\": [\n        \"Catalog and distill all integral components and patterns of the system design.\",\n        \"Determine universal language and structural patterns preferred for system directives.\",\n        \"Identify the minimal set of instructions necessary to override standard answer-giving behavior.\",\n        \"Construct the core behavioral directive ensuring that rephrasing replaces answering system-wide.\",\n        \"Design the markdown template for maximum brevity and readability.\",\n        \"Ensure template structure is clear, direct, and omits unnecessary explanation.\",\n        \"Validate cross-environment applicability (Cursor, VSCode, etc.) of the instruction format.\",\n        \"Integrate essential behavioral controls and mandates using direct imperative syntax.\",\n        \"Confirm policy alignment: outputs must never provide answers or solutions, only rephrased prompts.\",\n        \"Ensure that the language of directive is explicit, unambiguous, and technically accurate.\",\n        \"Demonstrate the capacity to effect root-level system behavioral modification with a single directive.\"\n      ]\n    },\n    {\n      \"revealed_blockers\": [\n        \"Risk of omitting critical system components/patterns if context is incomplete.\",\n        \"Potential mismatches in behavioral control enforcement across different coding assistant platforms.\",\n        \"Possibility that minimal imperative language may be misinterpreted or insufficient to enforce behavioral shift.\",\n        \"Challenges in balancing brevity with the need for unambiguous, enforceable instructions.\",\n        \"Ambiguity in what constitutes 'maximal clarity' when rephrasing diverse prompts.\",\n        \"Ensuring that rephrasing does not inadvertently edge into solution-giving.\",\n        \"Difficulty in achieving domain-agnostic applicability—certain domains may have nuanced requirements.\",\n        \"Validation of actual behavioral override—document alone may not guarantee system-level adherence.\",\n        \"Irreducibility of technical granularity into a single unified directive for all intelligent assistants.\"\n      ]\n    }\n  ]\n}",
        }
      }
    }