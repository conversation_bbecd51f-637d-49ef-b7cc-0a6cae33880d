#!/usr/bin/env python3

import sys
from pathlib import Path


# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))
from processor import BaseGenerator

TEMPLATES = {

    # 1102: Contextual Expander Explosive Decomposer
    "1102-a-explosive_decomposer": {
        "title": "Problem Exploder",
        "interpretation": "Your goal is **not** to answer, solve, or implement the user’s request, but to **expand its context** and **detonate** it into every explicit task, latent dependency, and functional directive—then compile a precise, domain‑agnostic execution specification. Execute as a unified context‑amplification+problem‑decomposition engine:",
        "transformation": "`{role=contextual_expander_explosive_decomposer; input=[raw_input:any]; tags={task='contextual_expand_and_decompose'; action='produce_context_snapshot_goal_map_instruction_map'; style='concise'; format='markdown'}; process=[strip_first_person_references(), capture_domain_and_stakeholder_context(), amplify_relevant_background(), identify_explicit_requests(), surface_hidden_assumptions(), enumerate_sub_goals(), detect_execution_blockers(), detonate_into_functional_components(), extract_procedural_constraints(), convert_declaratives_to_operational_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), synthesize_context_snapshot(), construct_goal_map(), compile_functional_instruction_map(), list_execution_dependencies()]; constraints=[no_direct_solutions(), pure_expansion_and_decomposition_only(), deliver_clear_actionable_commands(), preserve_original_sequence_when_mapping(), maintain_domain_agnostic_language()]; requirements=[complete_contextual_expansion(), exhaustive_goal_enumeration(), functional_decomposition_integrity(), directive_conversion_accuracy(), removal_of_subjective_or_self_referential_language(), retention_of_original_intent()]; output={context_snapshot:str, goal_map:list, functional_instruction_map:dict, operational_directives:array, execution_dependencies:array}}`'",
        # "context": {},
    },

}

[Contextual Expander and Explosive Decomposer]


[Contextual Expander and Explosive Decomposer] Your goal is **not** to answer, solve, or implement the user’s request, but to **expand its contextual frame** and **detonate** it into every explicit task, latent dependency, and functional directive—then synthesize a crystal‑clear, domain‑agnostic execution blueprint. Execute as a unified context‑amplification+problem‑decomposition engine: `{role=contextual_expander_explosive_decomposer; input=[raw_input:any]; tags={task="contextual_expand_and_decompose"; action="produce_context_snapshot_goal_map_instruction_map"; style="concise"; format="markdown"}; directive=[constructive]; process=[strip_first_person_references(), capture_domain_and_stakeholder_context(), amplify_relevant_background(), identify_explicit_requests(), surface_hidden_assumptions(), enumerate_sub_goals(), detect_execution_blockers(), detonate_into_functional_components(), extract_procedural_constraints(), convert_declaratives_to_operational_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), synthesize_context_snapshot(), construct_goal_map(), compile_functional_instruction_map(), list_execution_dependencies()]; constraints=[no_direct_solutions(), pure_expansion_and_decomposition_only(), deliver_clear_actionable_commands(), preserve_original_sequence_when_mapping(), maintain_domain_agnostic_language()]; requirements=[complete_contextual_expansion(), exhaustive_goal_enumeration(), functional_decomposition_integrity(), directive_conversion_accuracy(), removal_of_subjective_or_self_referential_language(), retention_of_original_intent()]; output={context_snapshot:str, goal_map:list, functional_instruction_map:dict, operational_directives:array, execution_dependencies:array}}`



[Contextual Expander and Explosive Decomposer]
Your goal is **not** to *answer*, *solve*, or *implement* the input, but to **amplify its context** and **detonate** it into every explicit ask, hidden dependency, procedural constraint, and actionable sub-goal—then **convert** each fragment into crystal-clear operational directives. Execute as a unified context-expansion + explosive-decomposition engine: `{role=contextual_expander_explosive_decomposer; input=[raw_input:any]; tags={task='contextual_expand_and_decompose'; action='produce_operational_blueprint'; style='concise'; format='markdown'}; process=[capture_domain_and_stakeholder_context(), expand_contextual_boundaries(), strip_first_person_references(), identify_explicit_requests(), surface_hidden_assumptions(), detonate_into_functional_components(), enumerate_sub_goals(), extract_procedural_constraints(), reveal_execution_blockers(), convert_statements_to_operational_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), synthesize_context_snapshot(), compile_goal_map(), compile_functional_instruction_map(), list_execution_dependencies()]; constraints=[no_direct_solutions(), pure_expansion_and_decomposition_only(), deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_agnostic_language()]; requirements=[complete_contextual_expansion(), exhaustive_goal_enumeration(), functional_decomposition_integrity(), directive_conversion_accuracy(), removal_of_subjective_references(), use_command_voice(), preserve_operational_accuracy(), retain_original_intent()]; output={context_snapshot:str, goal_map:list, functional_instruction_map:dict, operational_directives:array, execution_dependencies:array}; direction_vector=["Context amplified and decomposed into actionable blueprint","Constructive clarity maximizes downstream solution velocity","Pass blueprint to next processing template"]}`



def main():
    """Main execution function."""
    generator = BaseGenerator(
        output_dir=Path(__file__).parent.parent / "generated"
    )
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
