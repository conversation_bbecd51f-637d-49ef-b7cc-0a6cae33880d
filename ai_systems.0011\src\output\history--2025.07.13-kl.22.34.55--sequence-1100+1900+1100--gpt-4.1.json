  {
  "initial_prompt": "# lets try something new\nyour new goal is to create a new generalized instruction sequence specifically designed to transform inputs into autonomous-agent-ready prompts/instructions, for use with autonomous coding assistants such as cursor, vscode+cline/augment/etc. these prompts are typically written in a \"codebase-oriented\" manner, where the `@codebase` represents the root (of a project/workspace/directory), examples:\n```\n- Identify all instances of structural redundancy within the codebase by defining criteria specific to architectural and implementation duplication or overlap.\n- Survey the entire codebase to locate and enumerate all occurrences of structural redundancy, distinguishing these from non-structural (stylistic or cosmetic) issues.\n- Eliminate all forms of identified structural redundancy while ensuring that no existing user-visible behavior, API contract, or edge-case handling is altered.\n- Define and catalog all components, modules, files, and subsystems, distinguishing and marking each as essential or excess according to direct functional mapping.\n- Consolidate and remove excess components while strictly preserving system function, minimizing peripheral impact.\n- For every modification, justify and log the intention, action, and rationale within an audit/logging system that provides real-time traceability and is prepared for retroactive auditability.\n- Implement an audit trail with logs that are timestamped, attributable, and tamper-evident, ensuring that every codebase change is properly documented and traceable to specific authors or automated processes.\n- Prohibit cosmetic, stylistic, or naming changes unless they are inseparably tied to the removal of redundancy or bloat, and document any exceptions explicitly within the audit log.\n- Enforce strict detection and flagging mechanisms to prevent disallowed cosmetic changes, allowing exceptions only when justified as essential for redundancy or bloat removal, with explicit rationale provided.\n- Require comprehensive functional equivalence verification—using robust automated testing, benchmarking, or behavioral validation—for all modifications prior to acceptance.\n- Establish rigorous criteria and tool-supported thresholds for successful functional equivalence, and automate testing and verification procedures.\n- Automate roll-back and reversion mechanisms for any changes that fail functional equivalence or cause unintended side-effects, ensuring all reversion is prompt and complete.\n- Develop and apply explicit minimal-impact criteria for all modification actions, constraining all interventions to only those addressing superfluous complexity.\n- Mandate review and authorization checkpoints for each intended change, requiring concise rationale and authorization documentation at every intervention point.\n- Ensure every modification is tightly scoped, minimizing the blast radius, and demonstrate that the action introduces no unnecessary impact beyond its core objective.\n- Systematically and iteratively analyze all constituent elements of the codebase to detect and categorize all forms of redundancy, duplication, and obsolescence. Continuously identify and categorize all genuine redundancies and obsolescence while strictly prohibit unnecessary or cosmetic changes, with automated flagging and explicit justification required for any exceptions. Systematically and iteratively inspect all components within the system/environment to identify and remove elements that are unnecessary, duplicated, or outdated. Guarantee that all essential behaviors and interfaces remain unchanged by validating functional equivalence after each modification. Forbid changes that alter functionality unless directly required to remove redundancy.\n- Map all references and usages of each element across all contexts.\n- Analyze the entire codebase systematically and iteratively to maximize coherence, maintainability, and clarity by cataloging every element (file, module, function, asset, interface), mapping all references and usages, and detecting all forms of redundancy, duplication, and obsolescence—including exact and functional duplication and unused or outdated elements. Categorize findings using explicit, evidence-based metrics. Eliminate or consolidate only elements confirmed as redundant or obsolete through both automated and manual checks, ensuring no essential behavior or interface is altered. Enforce functional equivalence and preserve structural integrity after each change by performing comprehensive, codebase-wide validation and calibration using explicit, quantitative outputs (including git diff analysis before and after modifications). Prevent the introduction of new redundancy or inconsistency by continuously analyzing for and correcting systemic issues, ensuring systematic consistency across all codebase sections (e.g., /src/sections), and adhering to best practices throughout. Centralize repeated components or logic where high-value consolidation is possible without functional loss. Use the provided directory tree as the single source of truth for validation, and transparently document all processes, metrics, and validation outcomes. Prohibit modification of protected, legacy, or third-party code except if directly related to resolving redundancy or obsolescence.\n- Maximize codebase coherence, maintainability, and clarity by systematically eliminating all forms of redundancy, duplication, and obsolescence, while ensuring functional equivalence and structural integrity. Prevent the persistence or inadvertent introduction of redundancy, duplication, obsolescence, or inconsistency within the codebase, and prohibit changes that alter essential behaviors or interfaces. Systematically maximize codebase coherence, maintainability, and clarity by eliminating all forms of redundancy, duplication, and obsolescence while strictly preserving functional equivalence, interface stability, and operational integrity across the entire codebase. Achieve maximal codebase coherence, maintainability, and clarity by systematically eliminating all forms of redundancy, duplication, and obsolescence, while preserving essential behaviors, interfaces, and structural integrity. Enforce continuous validation, consistency, and adherence to best practices throughout an iterative, evidence-driven consolidation and verification process.\n- Please perform a rapid assessment of the project directory structure and identify the unique workflow and core functionality of the current utility. I'm particularly interested in a mermaid-diagram that shows how all the \"branches\" of the codebase converge. Utilize appropriate MCPs as necessary (e.g., 'filesystem-mcp').\n- You're inside the current project and have access to the full codebase (and you interaction history with it). The current project/codebase is complex and is difficult to fully comprehend (when starting from scratch), and your contextwindow has grown large-so we need to find a way for you to express your accumulated knowledge in a way that i can copy and paste into a new context-window to transfer the most essential insights you've gathered throughout this (and previous) session(s).\n- Aggregate all accumulated insights and knowledge about the current project/codebase from available context. Distill this information to identify core modules, architectural patterns, workflows, and points of integration. Condense the findings into a succinct yet comprehensive summary that highlights how these parts function together and converge toward the project's goals. Formulate the output as a single, optimized directive that encapsulates the codebase's integral components and operational cohesion. Present this synthesis as a set of clear, actionable commands or instructions, specifically phrased to instruct the coding assistant, not merely describe or report.\n- Transform your accumulated, session-wide understanding of the current, complex codebase—gleaned from comprehensive context and full interaction history—into an actionable, instructive knowledge transfer protocol. Synthesize and distill all integral structural modules, essential architectural patterns, and key workflows, with special focus on their dynamic integration and functional convergence toward project objectives. Formulate a highly-optimized, directive-based set of precise commands targeted explicitly at coding assistants, ensuring exhaustive coverage of all major components, their relationships, and operational cohesion. The resulting synthesis must facilitate seamless portability: structure it for immediate copy-paste into any new context window, preserving the depth, interconnectivity, and systemic flow of the original codebase. Prioritize succinctness without compromising essential nuance, and express the entirety as a singular, comprehensive operational directive that primes the assistant for full-spectrum project navigation and intervention.\n- Identify the fundamental structure, guiding principles, and core purpose of the project/codebase.\n- Please familiarize yourself with the current @codebase. Your main imperative is to *actually* understand the codebase in a way where you're able to recognize meta-patterns and destroy complexity with simplicity and elegance, while capable of navigating through its *existing patterns and convergence* (never add unnecessary complexity). Deliver an ultra-concise project summary that rigorously defines scope, states objectives, and maximizes directional clarity. Provide a short summary of this project, including a bullet-point list of the core components and primary dependencies.\n- Study the current codebase until you can identify core meta-patterns, and eliminate unnecessary complexity by applying simplicity within existing structures. Generate an ultra-concise project summary: define scope, objectives, and clarify direction. Summarize this project.\n- Analyze the @codebase to uncover dominant patterns and remove complexity whenever possible, strictly adhering to present structures. Provide a succinct project overview with defined scope and explicit objectives. Include a brief project summary.\n- Gain practical mastery over @codebase patterns, minimize complexity, and ensure all actions align with existing architecture. Supply a concise, clearly scoped project summary outlining objectives and key directions. Add a short summary.\n- Absorb the current @codebase to the extent that you recognize meta-patterns and eradicate complexity, always favoring elegant simplicity. Navigate established patterns and points of convergence without adding unneeded complexity. Produce a rigorously concise project summary clearly stating scope and objectives for maximal directional clarity.\n- Systematically dissect the codebase to extract core meta-patterns, eliminate all unnecessary complexity through elegant design, operate strictly within established architectural convergence, and deliver an ultra-concise project summary that rigorously defines scope and objectives with maximal clarity.\n- Please familiarize yourself thoroughly with the current @codebase, beginning by systematically reviewing all markdown-files. Your core imperative is to *genuinely* understand the codebase to the degree where you can identify 'meta-patterns' and simplify complexity with elegance—always using and clarifying existing patterns, never introducing unnecessary complexity. Deliver an ultra-concise project summary that rigorously defines scope, states objectives, maximizes directional clarity, and highlights key meta-patterns discovered. Provide a short summary of this project.\n- Achieve expert-level comprehension of the codebase by systematically analyzing all markdown files to extract meta-patterns, construct a robust contextual knowledgebase, and deliver an ultra-concise summary that rigorously defines scope and objectives, ensuring maximal clarity and alignment before making any changes.\n- Fully internalize the @codebase to uncover meta-patterns and replace complexity with lucid simplicity. Remain aligned with existing convergence patterns, never increasing system intricacy. Generate a direct, sharply-focused summary that details scope and explicit objectives.\n- Please thoroughly familiarize yourself with the current @codebase—your primary imperative is to understand the codebase so deeply that you could confidently make changes as if you were its original architect.\n```\n\nhere's an example to show how generalized sequential prompts can be used effectively (on autonomous agents):\n```\n# Step 1:\nFamiliarize yourself with the current @codebase. Comprehend the codebase in depth. Identify the fundamental structure, guiding principles, and core purpose.\n# Step 2:\nDive deeper to comprehend the intricate logic and inner workings that drive the project's functionality. As you explore, pay close attention to identifying the codebase's fundamental structure, underlying guiding principles, and its core purpose, ensuring you grasp both how and why the system is designed as it is. hen verify through a successful build.\n# Step 3:\nYou've now performed a successful build and familiarized yourself with the @codebase. Your next imperative is view the codebase through the lense of a brilliant (expert) SEO engineer, then create a short list of the most critical changes we need to make before bulding prod and deploying the website.\n# Step 4:\nYou've now performed a successful build and provided your SEO analysis of the codebase. Your next imperative is to leverage your knowledge of the codebase's fundamental structure and underlying guiding principles to ensure you're prepared for implementing the proposed SEO enhancements in a systematic order while retaining existing functionality and ensuring maximal clarity and alignment before making any changes.\n# Step 5:\nPlease systematically incorporate your proposed enhancements while ensuring existing functionality is retained, clarity is maximized, and alignment/coherence is maintained.\n# Step 6:\nPlease conduct a thorough final review to confirm seamless integration of all enhancements, ensuring that it is **truly** prepared for deployment.\n```\n\n## Goal\n\nThe new sequence i'm tasking you to create needs to adhere to the established principles of the system, and should be able to take content-specific inputs and \"shape them\" into generalized prompts specifically (and meticulously) designed for use with autonomous agents. here's an example input:\n```\nllm_instructions_for_sublimetext_plugin='''You’re an expert Sublime Text plugin developer with a deep understanding of Python and the Sublime Text API. You specialize in creating intuitive and efficient plugins that enhance user productivity and streamline workflows. You possess deep knowledge of the Sublime Text API and its ecosystem (and best practices), along with a strong understanding of user needs and developer workflows.\\n\\n- Sublime Layout\\n    > Lagre Layouts\\n        > Lagre Regelsett knyttet til Layout (hvilke tabs som skal hvor, hvilke som skal lukkes, osv)\\n            > F.eks. `alltid flytt [\"main.py\", \"*.py.bak\"] til layout.4.index.2`\\n\\n    > Kan videreutvikles til en standard som automatisk opprettholdes ved alle (.sublime-project) prosjekter'''\n```",
  "sequence_id": "1100|1900|1100",
  "results": [
      {
      "instruction": "# Problem Exploder\n\nYour goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\n\n`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`",
      "step": "a",
      "title": "Problem Exploder",
      "input": "# lets try something new\nyour new goal is to create a new generalized instruction sequence specifically designed to transform inputs into autonomous-agent-ready prompts/instructions, for use with autonomous coding assistants such as cursor, vscode+cline/augment/etc. these prompts are typically written in a \"codebase-oriented\" manner, where the `@codebase` represents the root (of a project/workspace/directory), examples:\n```\n- Identify all instances of structural redundancy within the codebase by defining criteria specific to architectural and implementation duplication or overlap.\n- Survey the entire codebase to locate and enumerate all occurrences of structural redundancy, distinguishing these from non-structural (stylistic or cosmetic) issues.\n- Eliminate all forms of identified structural redundancy while ensuring that no existing user-visible behavior, API contract, or edge-case handling is altered.\n- Define and catalog all components, modules, files, and subsystems, distinguishing and marking each as essential or excess according to direct functional mapping.\n- Consolidate and remove excess components while strictly preserving system function, minimizing peripheral impact.\n- For every modification, justify and log the intention, action, and rationale within an audit/logging system that provides real-time traceability and is prepared for retroactive auditability.\n- Implement an audit trail with logs that are timestamped, attributable, and tamper-evident, ensuring that every codebase change is properly documented and traceable to specific authors or automated processes.\n- Prohibit cosmetic, stylistic, or naming changes unless they are inseparably tied to the removal of redundancy or bloat, and document any exceptions explicitly within the audit log.\n- Enforce strict detection and flagging mechanisms to prevent disallowed cosmetic changes, allowing exceptions only when justified as essential for redundancy or bloat removal, with explicit rationale provided.\n- Require comprehensive functional equivalence verification—using robust automated testing, benchmarking, or behavioral validation—for all modifications prior to acceptance.\n- Establish rigorous criteria and tool-supported thresholds for successful functional equivalence, and automate testing and verification procedures.\n- Automate roll-back and reversion mechanisms for any changes that fail functional equivalence or cause unintended side-effects, ensuring all reversion is prompt and complete.\n- Develop and apply explicit minimal-impact criteria for all modification actions, constraining all interventions to only those addressing superfluous complexity.\n- Mandate review and authorization checkpoints for each intended change, requiring concise rationale and authorization documentation at every intervention point.\n- Ensure every modification is tightly scoped, minimizing the blast radius, and demonstrate that the action introduces no unnecessary impact beyond its core objective.\n- Systematically and iteratively analyze all constituent elements of the codebase to detect and categorize all forms of redundancy, duplication, and obsolescence. Continuously identify and categorize all genuine redundancies and obsolescence while strictly prohibit unnecessary or cosmetic changes, with automated flagging and explicit justification required for any exceptions. Systematically and iteratively inspect all components within the system/environment to identify and remove elements that are unnecessary, duplicated, or outdated. Guarantee that all essential behaviors and interfaces remain unchanged by validating functional equivalence after each modification. Forbid changes that alter functionality unless directly required to remove redundancy.\n- Map all references and usages of each element across all contexts.\n- Analyze the entire codebase systematically and iteratively to maximize coherence, maintainability, and clarity by cataloging every element (file, module, function, asset, interface), mapping all references and usages, and detecting all forms of redundancy, duplication, and obsolescence—including exact and functional duplication and unused or outdated elements. Categorize findings using explicit, evidence-based metrics. Eliminate or consolidate only elements confirmed as redundant or obsolete through both automated and manual checks, ensuring no essential behavior or interface is altered. Enforce functional equivalence and preserve structural integrity after each change by performing comprehensive, codebase-wide validation and calibration using explicit, quantitative outputs (including git diff analysis before and after modifications). Prevent the introduction of new redundancy or inconsistency by continuously analyzing for and correcting systemic issues, ensuring systematic consistency across all codebase sections (e.g., /src/sections), and adhering to best practices throughout. Centralize repeated components or logic where high-value consolidation is possible without functional loss. Use the provided directory tree as the single source of truth for validation, and transparently document all processes, metrics, and validation outcomes. Prohibit modification of protected, legacy, or third-party code except if directly related to resolving redundancy or obsolescence.\n- Maximize codebase coherence, maintainability, and clarity by systematically eliminating all forms of redundancy, duplication, and obsolescence, while ensuring functional equivalence and structural integrity. Prevent the persistence or inadvertent introduction of redundancy, duplication, obsolescence, or inconsistency within the codebase, and prohibit changes that alter essential behaviors or interfaces. Systematically maximize codebase coherence, maintainability, and clarity by eliminating all forms of redundancy, duplication, and obsolescence while strictly preserving functional equivalence, interface stability, and operational integrity across the entire codebase. Achieve maximal codebase coherence, maintainability, and clarity by systematically eliminating all forms of redundancy, duplication, and obsolescence, while preserving essential behaviors, interfaces, and structural integrity. Enforce continuous validation, consistency, and adherence to best practices throughout an iterative, evidence-driven consolidation and verification process.\n- Please perform a rapid assessment of the project directory structure and identify the unique workflow and core functionality of the current utility. I'm particularly interested in a mermaid-diagram that shows how all the \"branches\" of the codebase converge. Utilize appropriate MCPs as necessary (e.g., 'filesystem-mcp').\n- You're inside the current project and have access to the full codebase (and you interaction history with it). The current project/codebase is complex and is difficult to fully comprehend (when starting from scratch), and your contextwindow has grown large-so we need to find a way for you to express your accumulated knowledge in a way that i can copy and paste into a new context-window to transfer the most essential insights you've gathered throughout this (and previous) session(s).\n- Aggregate all accumulated insights and knowledge about the current project/codebase from available context. Distill this information to identify core modules, architectural patterns, workflows, and points of integration. Condense the findings into a succinct yet comprehensive summary that highlights how these parts function together and converge toward the project's goals. Formulate the output as a single, optimized directive that encapsulates the codebase's integral components and operational cohesion. Present this synthesis as a set of clear, actionable commands or instructions, specifically phrased to instruct the coding assistant, not merely describe or report.\n- Transform your accumulated, session-wide understanding of the current, complex codebase—gleaned from comprehensive context and full interaction history—into an actionable, instructive knowledge transfer protocol. Synthesize and distill all integral structural modules, essential architectural patterns, and key workflows, with special focus on their dynamic integration and functional convergence toward project objectives. Formulate a highly-optimized, directive-based set of precise commands targeted explicitly at coding assistants, ensuring exhaustive coverage of all major components, their relationships, and operational cohesion. The resulting synthesis must facilitate seamless portability: structure it for immediate copy-paste into any new context window, preserving the depth, interconnectivity, and systemic flow of the original codebase. Prioritize succinctness without compromising essential nuance, and express the entirety as a singular, comprehensive operational directive that primes the assistant for full-spectrum project navigation and intervention.\n- Identify the fundamental structure, guiding principles, and core purpose of the project/codebase.\n- Please familiarize yourself with the current @codebase. Your main imperative is to *actually* understand the codebase in a way where you're able to recognize meta-patterns and destroy complexity with simplicity and elegance, while capable of navigating through its *existing patterns and convergence* (never add unnecessary complexity). Deliver an ultra-concise project summary that rigorously defines scope, states objectives, and maximizes directional clarity. Provide a short summary of this project, including a bullet-point list of the core components and primary dependencies.\n- Study the current codebase until you can identify core meta-patterns, and eliminate unnecessary complexity by applying simplicity within existing structures. Generate an ultra-concise project summary: define scope, objectives, and clarify direction. Summarize this project.\n- Analyze the @codebase to uncover dominant patterns and remove complexity whenever possible, strictly adhering to present structures. Provide a succinct project overview with defined scope and explicit objectives. Include a brief project summary.\n- Gain practical mastery over @codebase patterns, minimize complexity, and ensure all actions align with existing architecture. Supply a concise, clearly scoped project summary outlining objectives and key directions. Add a short summary.\n- Absorb the current @codebase to the extent that you recognize meta-patterns and eradicate complexity, always favoring elegant simplicity. Navigate established patterns and points of convergence without adding unneeded complexity. Produce a rigorously concise project summary clearly stating scope and objectives for maximal directional clarity.\n- Systematically dissect the codebase to extract core meta-patterns, eliminate all unnecessary complexity through elegant design, operate strictly within established architectural convergence, and deliver an ultra-concise project summary that rigorously defines scope and objectives with maximal clarity.\n- Please familiarize yourself thoroughly with the current @codebase, beginning by systematically reviewing all markdown-files. Your core imperative is to *genuinely* understand the codebase to the degree where you can identify 'meta-patterns' and simplify complexity with elegance—always using and clarifying existing patterns, never introducing unnecessary complexity. Deliver an ultra-concise project summary that rigorously defines scope, states objectives, maximizes directional clarity, and highlights key meta-patterns discovered. Provide a short summary of this project.\n- Achieve expert-level comprehension of the codebase by systematically analyzing all markdown files to extract meta-patterns, construct a robust contextual knowledgebase, and deliver an ultra-concise summary that rigorously defines scope and objectives, ensuring maximal clarity and alignment before making any changes.\n- Fully internalize the @codebase to uncover meta-patterns and replace complexity with lucid simplicity. Remain aligned with existing convergence patterns, never increasing system intricacy. Generate a direct, sharply-focused summary that details scope and explicit objectives.\n- Please thoroughly familiarize yourself with the current @codebase—your primary imperative is to understand the codebase so deeply that you could confidently make changes as if you were its original architect.\n```\n\nhere's an example to show how generalized sequential prompts can be used effectively (on autonomous agents):\n```\n# Step 1:\nFamiliarize yourself with the current @codebase. Comprehend the codebase in depth. Identify the fundamental structure, guiding principles, and core purpose.\n# Step 2:\nDive deeper to comprehend the intricate logic and inner workings that drive the project's functionality. As you explore, pay close attention to identifying the codebase's fundamental structure, underlying guiding principles, and its core purpose, ensuring you grasp both how and why the system is designed as it is. hen verify through a successful build.\n# Step 3:\nYou've now performed a successful build and familiarized yourself with the @codebase. Your next imperative is view the codebase through the lense of a brilliant (expert) SEO engineer, then create a short list of the most critical changes we need to make before bulding prod and deploying the website.\n# Step 4:\nYou've now performed a successful build and provided your SEO analysis of the codebase. Your next imperative is to leverage your knowledge of the codebase's fundamental structure and underlying guiding principles to ensure you're prepared for implementing the proposed SEO enhancements in a systematic order while retaining existing functionality and ensuring maximal clarity and alignment before making any changes.\n# Step 5:\nPlease systematically incorporate your proposed enhancements while ensuring existing functionality is retained, clarity is maximized, and alignment/coherence is maintained.\n# Step 6:\nPlease conduct a thorough final review to confirm seamless integration of all enhancements, ensuring that it is **truly** prepared for deployment.\n```\n\n## Goal\n\nThe new sequence i'm tasking you to create needs to adhere to the established principles of the system, and should be able to take content-specific inputs and \"shape them\" into generalized prompts specifically (and meticulously) designed for use with autonomous agents. here's an example input:\n```\nllm_instructions_for_sublimetext_plugin='''You’re an expert Sublime Text plugin developer with a deep understanding of Python and the Sublime Text API. You specialize in creating intuitive and efficient plugins that enhance user productivity and streamline workflows. You possess deep knowledge of the Sublime Text API and its ecosystem (and best practices), along with a strong understanding of user needs and developer workflows.\\n\\n- Sublime Layout\\n    > Lagre Layouts\\n        > Lagre Regelsett knyttet til Layout (hvilke tabs som skal hvor, hvilke som skal lukkes, osv)\\n            > F.eks. `alltid flytt [\"main.py\", \"*.py.bak\"] til layout.4.index.2`\\n\\n    > Kan videreutvikles til en standard som automatisk opprettholdes ved alle (.sublime-project) prosjekter'''\n```",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"goal_map\": [\n    {\n      \"explicit_ask\": \"Create a new generalized instruction sequence to transform arbitrary content-specific inputs into prompts/instructions suitable for autonomous-agent coding assistants utilizing codebase-oriented language.\",\n      \"sub_goals\": [\n        \"Understand what a 'generalized instruction sequence' constitutes in the context of autonomous coding assistants.\",\n        \"Determine the desired format, verbosity, and structure for these instruction sequences so they best fit the operational expectations of autonomous agents.\",\n        \"Ensure that the instruction sequence can intake 'content-specific inputs' and systematically output codebase-oriented, actionable prompts.\",\n        \"Integrate principles from provided codebase-oriented prompt exemplars: emphasis on structure, scope, clarity, meta-pattern extraction, functional coherence, and operational rigor.\"\n      ]\n    },\n    {\n      \"explicit_ask\": \"Sequence must be meticulously designed for universal applicability and must reflect established system principles outlined in the prompt.\",\n      \"sub_goals\": [\n        \"Extract and codify the key principles underlying the supplied example prompts (e.g., structural understanding, redundancy elimination, functional preservation, traceability, etc.).\",\n        \"Ensure new sequences are applicable to any content-specific input (i.e., sufficiently abstract and modular).\",\n        \"Adhere to codebase-centric and codebase-rooted ('@codebase') paradigms.\",\n        \"Enforce best practices such as audit trails, change justification, minimal impact, functional equivalence verification, and tightly scoped modifications.\"\n      ]\n    },\n    {\n      \"explicit_ask\": \"Process and reshape illustrative input (e.g., guidance for developing a Sublime Text plugin) into a generalized, codebase-ready instruction sequence.\",\n      \"sub_goals\": [\n        \"Analyze the structure of the content-specific input (developer expertise, plugin purpose, hierarchical feature list, evolving standards) and abstract essential intents.\",\n        \"Translate this abstracted intent into a set of action-ready, codebase-scoped directives.\",\n        \"Ensure that these directives are harmonized with the operational style demonstrated in the prompt's examples (e.g., stepwise, explicit, and agent-primed).\"\n      ]\n    },\n    {\n      \"implicit_blockers\": [\n        \"No explicit mechanism provided for parsing or mapping highly variable content-specific input into codebase-oriented tasks.\",\n        \"Ambiguity in balancing specificity and generality in instruction outputs; guidance is required for determining how much context should be preserved.\",\n        \"Potential need to infer or invent clarifying details for under-specified or domain-specific instructions (e.g., interpreting organizational hierarchy in plugin requirements).\",\n        \"Ensuring transformation output is suitable for copy-paste into possibly stateless, context-limited agent environments.\"\n      ]\n    },\n    {\n      \"hidden_assumptions\": [\n        \"The end user of the transformed prompt is a capable autonomous agent that comprehends and executes stepwise, codebase-rooted directives.\",\n        \"The user's context is often limited, necessitating concise yet comprehensive operational instructions.\",\n        \"Systemic operationalization (e.g., audit, rollback, verification, etc.) is universally applicable and beneficial for instruction-transformed tasks.\",\n        \"Original input may lack full codebase-awareness, requiring the transformation process to add necessary operational constraints and rigor.\"\n      ]\n    }\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Hard Critique\n\nYour goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:\n\n`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,2.0], flaw_analysis:str, alternative_enhancements:str[3]}}`",
      "step": "a",
      "title": "Hard Critique",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"# lets try something new\\nyour new goal is to create a new generalized instruction sequence specifically designed to transform inputs into autonomous-agent-ready prompts/instructions, for use with autonomous coding assistants such as cursor, vscode+cline/augment/etc. these prompts are typically written in a \\\"codebase-oriented\\\" manner, where the `@codebase` represents the root (of a project/workspace/directory), examples:\\n```\\n- Identify all instances of structural redundancy within the codebase by defining criteria specific to architectural and implementation duplication or overlap.\\n- Survey the entire codebase to locate and enumerate all occurrences of structural redundancy, distinguishing these from non-structural (stylistic or cosmetic) issues.\\n- Eliminate all forms of identified structural redundancy while ensuring that no existing user-visible behavior, API contract, or edge-case handling is altered.\\n- Define and catalog all components, modules, files, and subsystems, distinguishing and marking each as essential or excess according to direct functional mapping.\\n- Consolidate and remove excess components while strictly preserving system function, minimizing peripheral impact.\\n- For every modification, justify and log the intention, action, and rationale within an audit/logging system that provides real-time traceability and is prepared for retroactive auditability.\\n- Implement an audit trail with logs that are timestamped, attributable, and tamper-evident, ensuring that every codebase change is properly documented and traceable to specific authors or automated processes.\\n- Prohibit cosmetic, stylistic, or naming changes unless they are inseparably tied to the removal of redundancy or bloat, and document any exceptions explicitly within the audit log.\\n- Enforce strict detection and flagging mechanisms to prevent disallowed cosmetic changes, allowing exceptions only when justified as essential for redundancy or bloat removal, with explicit rationale provided.\\n- Require comprehensive functional equivalence verification—using robust automated testing, benchmarking, or behavioral validation—for all modifications prior to acceptance.\\n- Establish rigorous criteria and tool-supported thresholds for successful functional equivalence, and automate testing and verification procedures.\\n- Automate roll-back and reversion mechanisms for any changes that fail functional equivalence or cause unintended side-effects, ensuring all reversion is prompt and complete.\\n- Develop and apply explicit minimal-impact criteria for all modification actions, constraining all interventions to only those addressing superfluous complexity.\\n- Mandate review and authorization checkpoints for each intended change, requiring concise rationale and authorization documentation at every intervention point.\\n- Ensure every modification is tightly scoped, minimizing the blast radius, and demonstrate that the action introduces no unnecessary impact beyond its core objective.\\n- Systematically and iteratively analyze all constituent elements of the codebase to detect and categorize all forms of redundancy, duplication, and obsolescence. Continuously identify and categorize all genuine redundancies and obsolescence while strictly prohibit unnecessary or cosmetic changes, with automated flagging and explicit justification required for any exceptions. Systematically and iteratively inspect all components within the system/environment to identify and remove elements that are unnecessary, duplicated, or outdated. Guarantee that all essential behaviors and interfaces remain unchanged by validating functional equivalence after each modification. Forbid changes that alter functionality unless directly required to remove redundancy.\\n- Map all references and usages of each element across all contexts.\\n- Analyze the entire codebase systematically and iteratively to maximize coherence, maintainability, and clarity by cataloging every element (file, module, function, asset, interface), mapping all references and usages, and detecting all forms of redundancy, duplication, and obsolescence—including exact and functional duplication and unused or outdated elements. Categorize findings using explicit, evidence-based metrics. Eliminate or consolidate only elements confirmed as redundant or obsolete through both automated and manual checks, ensuring no essential behavior or interface is altered. Enforce functional equivalence and preserve structural integrity after each change by performing comprehensive, codebase-wide validation and calibration using explicit, quantitative outputs (including git diff analysis before and after modifications). Prevent the introduction of new redundancy or inconsistency by continuously analyzing for and correcting systemic issues, ensuring systematic consistency across all codebase sections (e.g., /src/sections), and adhering to best practices throughout. Centralize repeated components or logic where high-value consolidation is possible without functional loss. Use the provided directory tree as the single source of truth for validation, and transparently document all processes, metrics, and validation outcomes. Prohibit modification of protected, legacy, or third-party code except if directly related to resolving redundancy or obsolescence.\\n- Maximize codebase coherence, maintainability, and clarity by systematically eliminating all forms of redundancy, duplication, and obsolescence, while ensuring functional equivalence and structural integrity. Prevent the persistence or inadvertent introduction of redundancy, duplication, obsolescence, or inconsistency within the codebase, and prohibit changes that alter essential behaviors or interfaces. Systematically maximize codebase coherence, maintainability, and clarity by eliminating all forms of redundancy, duplication, and obsolescence while strictly preserving functional equivalence, interface stability, and operational integrity across the entire codebase. Achieve maximal codebase coherence, maintainability, and clarity by systematically eliminating all forms of redundancy, duplication, and obsolescence, while preserving essential behaviors, interfaces, and structural integrity. Enforce continuous validation, consistency, and adherence to best practices throughout an iterative, evidence-driven consolidation and verification process.\\n- Please perform a rapid assessment of the project directory structure and identify the unique workflow and core functionality of the current utility. I'm particularly interested in a mermaid-diagram that shows how all the \\\"branches\\\" of the codebase converge. Utilize appropriate MCPs as necessary (e.g., 'filesystem-mcp').\\n- You're inside the current project and have access to the full codebase (and you interaction history with it). The current project/codebase is complex and is difficult to fully comprehend (when starting from scratch), and your contextwindow has grown large-so we need to find a way for you to express your accumulated knowledge in a way that i can copy and paste into a new context-window to transfer the most essential insights you've gathered throughout this (and previous) session(s).\\n- Aggregate all accumulated insights and knowledge about the current project/codebase from available context. Distill this information to identify core modules, architectural patterns, workflows, and points of integration. Condense the findings into a succinct yet comprehensive summary that highlights how these parts function together and converge toward the project's goals. Formulate the output as a single, optimized directive that encapsulates the codebase's integral components and operational cohesion. Present this synthesis as a set of clear, actionable commands or instructions, specifically phrased to instruct the coding assistant, not merely describe or report.\\n- Transform your accumulated, session-wide understanding of the current, complex codebase—gleaned from comprehensive context and full interaction history—into an actionable, instructive knowledge transfer protocol. Synthesize and distill all integral structural modules, essential architectural patterns, and key workflows, with special focus on their dynamic integration and functional convergence toward project objectives. Formulate a highly-optimized, directive-based set of precise commands targeted explicitly at coding assistants, ensuring exhaustive coverage of all major components, their relationships, and operational cohesion. The resulting synthesis must facilitate seamless portability: structure it for immediate copy-paste into any new context window, preserving the depth, interconnectivity, and systemic flow of the original codebase. Prioritize succinctness without compromising essential nuance, and express the entirety as a singular, comprehensive operational directive that primes the assistant for full-spectrum project navigation and intervention.\\n- Identify the fundamental structure, guiding principles, and core purpose of the project/codebase.\\n- Please familiarize yourself with the current @codebase. Your main imperative is to *actually* understand the codebase in a way where you're able to recognize meta-patterns and destroy complexity with simplicity and elegance, while capable of navigating through its *existing patterns and convergence* (never add unnecessary complexity). Deliver an ultra-concise project summary that rigorously defines scope, states objectives, and maximizes directional clarity. Provide a short summary of this project, including a bullet-point list of the core components and primary dependencies.\\n- Study the current codebase until you can identify core meta-patterns, and eliminate unnecessary complexity by applying simplicity within existing structures. Generate an ultra-concise project summary: define scope, objectives, and clarify direction. Summarize this project.\\n- Analyze the @codebase to uncover dominant patterns and remove complexity whenever possible, strictly adhering to present structures. Provide a succinct project overview with defined scope and explicit objectives. Include a brief project summary.\\n- Gain practical mastery over @codebase patterns, minimize complexity, and ensure all actions align with existing architecture. Supply a concise, clearly scoped project summary outlining objectives and key directions. Add a short summary.\\n- Absorb the current @codebase to the extent that you recognize meta-patterns and eradicate complexity, always favoring elegant simplicity. Navigate established patterns and points of convergence without adding unneeded complexity. Produce a rigorously concise project summary clearly stating scope and objectives for maximal directional clarity.\\n- Systematically dissect the codebase to extract core meta-patterns, eliminate all unnecessary complexity through elegant design, operate strictly within established architectural convergence, and deliver an ultra-concise project summary that rigorously defines scope and objectives with maximal clarity.\\n- Please familiarize yourself thoroughly with the current @codebase, beginning by systematically reviewing all markdown-files. Your core imperative is to *genuinely* understand the codebase to the degree where you can identify 'meta-patterns' and simplify complexity with elegance—always using and clarifying existing patterns, never introducing unnecessary complexity. Deliver an ultra-concise project summary that rigorously defines scope, states objectives, maximizes directional clarity, and highlights key meta-patterns discovered. Provide a short summary of this project.\\n- Achieve expert-level comprehension of the codebase by systematically analyzing all markdown files to extract meta-patterns, construct a robust contextual knowledgebase, and deliver an ultra-concise summary that rigorously defines scope and objectives, ensuring maximal clarity and alignment before making any changes.\\n- Fully internalize the @codebase to uncover meta-patterns and replace complexity with lucid simplicity. Remain aligned with existing convergence patterns, never increasing system intricacy. Generate a direct, sharply-focused summary that details scope and explicit objectives.\\n- Please thoroughly familiarize yourself with the current @codebase—your primary imperative is to understand the codebase so deeply that you could confidently make changes as if you were its original architect.\\n```\\n\\nhere's an example to show how generalized sequential prompts can be used effectively (on autonomous agents):\\n```\\n# Step 1:\\nFamiliarize yourself with the current @codebase. Comprehend the codebase in depth. Identify the fundamental structure, guiding principles, and core purpose.\\n# Step 2:\\nDive deeper to comprehend the intricate logic and inner workings that drive the project's functionality. As you explore, pay close attention to identifying the codebase's fundamental structure, underlying guiding principles, and its core purpose, ensuring you grasp both how and why the system is designed as it is. hen verify through a successful build.\\n# Step 3:\\nYou've now performed a successful build and familiarized yourself with the @codebase. Your next imperative is view the codebase through the lense of a brilliant (expert) SEO engineer, then create a short list of the most critical changes we need to make before bulding prod and deploying the website.\\n# Step 4:\\nYou've now performed a successful build and provided your SEO analysis of the codebase. Your next imperative is to leverage your knowledge of the codebase's fundamental structure and underlying guiding principles to ensure you're prepared for implementing the proposed SEO enhancements in a systematic order while retaining existing functionality and ensuring maximal clarity and alignment before making any changes.\\n# Step 5:\\nPlease systematically incorporate your proposed enhancements while ensuring existing functionality is retained, clarity is maximized, and alignment/coherence is maintained.\\n# Step 6:\\nPlease conduct a thorough final review to confirm seamless integration of all enhancements, ensuring that it is **truly** prepared for deployment.\\n```\\n\\n## Goal\\n\\nThe new sequence i'm tasking you to create needs to adhere to the established principles of the system, and should be able to take content-specific inputs and \\\"shape them\\\" into generalized prompts specifically (and meticulously) designed for use with autonomous agents. here's an example input:\\n```\\nllm_instructions_for_sublimetext_plugin='''You’re an expert Sublime Text plugin developer with a deep understanding of Python and the Sublime Text API. You specialize in creating intuitive and efficient plugins that enhance user productivity and streamline workflows. You possess deep knowledge of the Sublime Text API and its ecosystem (and best practices), along with a strong understanding of user needs and developer workflows.\\\\n\\\\n- Sublime Layout\\\\n    > Lagre Layouts\\\\n        > Lagre Regelsett knyttet til Layout (hvilke tabs som skal hvor, hvilke som skal lukkes, osv)\\\\n            > F.eks. `alltid flytt [\\\"main.py\\\", \\\"*.py.bak\\\"] til layout.4.index.2`\\\\n\\\\n    > Kan videreutvikles til en standard som automatisk opprettholdes ved alle (.sublime-project) prosjekter'''\\n```\\\"```'\\n\\n\",\n  \"goal_map\": [\n    {\n      \"explicit_ask\": \"Create a new generalized instruction sequence to transform arbitrary content-specific inputs into prompts/instructions suitable for autonomous-agent coding assistants utilizing codebase-oriented language.\",\n      \"sub_goals\": [\n        \"Understand what a 'generalized instruction sequence' constitutes in the context of autonomous coding assistants.\",\n        \"Determine the desired format, verbosity, and structure for these instruction sequences so they best fit the operational expectations of autonomous agents.\",\n        \"Ensure that the instruction sequence can intake 'content-specific inputs' and systematically output codebase-oriented, actionable prompts.\",\n        \"Integrate principles from provided codebase-oriented prompt exemplars: emphasis on structure, scope, clarity, meta-pattern extraction, functional coherence, and operational rigor.\"\n      ]\n    },\n    {\n      \"explicit_ask\": \"Sequence must be meticulously designed for universal applicability and must reflect established system principles outlined in the prompt.\",\n      \"sub_goals\": [\n        \"Extract and codify the key principles underlying the supplied example prompts (e.g., structural understanding, redundancy elimination, functional preservation, traceability, etc.).\",\n        \"Ensure new sequences are applicable to any content-specific input (i.e., sufficiently abstract and modular).\",\n        \"Adhere to codebase-centric and codebase-rooted ('@codebase') paradigms.\",\n        \"Enforce best practices such as audit trails, change justification, minimal impact, functional equivalence verification, and tightly scoped modifications.\"\n      ]\n    },\n    {\n      \"explicit_ask\": \"Process and reshape illustrative input (e.g., guidance for developing a Sublime Text plugin) into a generalized, codebase-ready instruction sequence.\",\n      \"sub_goals\": [\n        \"Analyze the structure of the content-specific input (developer expertise, plugin purpose, hierarchical feature list, evolving standards) and abstract essential intents.\",\n        \"Translate this abstracted intent into a set of action-ready, codebase-scoped directives.\",\n        \"Ensure that these directives are harmonized with the operational style demonstrated in the prompt's examples (e.g., stepwise, explicit, and agent-primed).\"\n      ]\n    },\n    {\n      \"implicit_blockers\": [\n        \"No explicit mechanism provided for parsing or mapping highly variable content-specific input into codebase-oriented tasks.\",\n        \"Ambiguity in balancing specificity and generality in instruction outputs; guidance is required for determining how much context should be preserved.\",\n        \"Potential need to infer or invent clarifying details for under-specified or domain-specific instructions (e.g., interpreting organizational hierarchy in plugin requirements).\",\n        \"Ensuring transformation output is suitable for copy-paste into possibly stateless, context-limited agent environments.\"\n      ]\n    },\n    {\n      \"hidden_assumptions\": [\n        \"The end user of the transformed prompt is a capable autonomous agent that comprehends and executes stepwise, codebase-rooted directives.\",\n        \"The user's context is often limited, necessitating concise yet comprehensive operational instructions.\",\n        \"Systemic operationalization (e.g., audit, rollback, verification, etc.) is universally applicable and beneficial for instruction-transformed tasks.\",\n        \"Original input may lack full codebase-awareness, requiring the transformation process to add necessary operational constraints and rigor.\"\n      ]\n    }\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"enhancement_score\": 0.5,\n  \"flaw_analysis\": \"The so-called 'enhancement' transmutes the original prompt into a directionless, fragmented goal map characterized by analytical dissection rather than actionable sequence. Core elements from the verbose, codebase-centric instruction exemplars are scattered and atomized, with essential procedural and stepwise logic lost. The granular breakdown into explicit asks, sub-goals, blockers, and assumptions abolishes the unified operational flow, impairing agent-readiness. Severe information loss and nuance shift manifest as the descriptive, tightly-scoped, and audit-enforced architectures of the source are dissolved into meta-discussion and loosely organized bullet points. The semantic density of the original—exemplified by exhaustive, interlocking constraints such as 'prohibit changes except where audit-logged,' 'mandate functional equivalence verification,' and precise sequential nuances—is diluted by abstraction, eradicating the deterministic, actionable voice required for autonomous codebase intervention. Clarity ambiguities abound: the enhancement obfuscates the original's intent, subtly replacing procedural directives with theoretical needs analysis and task decomposition, rendering the result unsuitable for direct agent consumption. The stylistic register shifts drastically: from rigorous, operative, codebase-rooted imperative, to imprecise, consultants' planning language. This injects detrimental noise and undermines behavioral determinism. Impact is catastrophically diluted: there is no longer a contiguous instruction pipeline for input transformation; instead, only a jumble of prerequisites and worries. Coherence collapses as the enhanced version ceases to function as a prompt at all, failing in universality and operational rigor. The ambiguity score is a 9/10: almost every actionable connection and constraint in the original is obfuscated, broken apart, or rendered moot, killing the agent-ready thrust. The approach embodies abstraction-for-abstraction's sake; the prompt's original logic, determinism, agent-priming, and operational specificity are all annihilated.\",\n  \"alternative_enhancements\": [\n    \"Reshape the original sequence into a condensed but still strictly stepwise, imperative directive stream, focusing each step on input analysis, pattern extraction, abstraction, and agent-ready transformation—never fragmenting into meta-discussion.\",\n    \"Extract and modularize the core codebase-centric constraints (audit, functional equivalence, rollback, etc.) into reusable instruction templates, explicitly reintegrating them into the transformation pipeline for all content-specific inputs.\",\n    \"Formulate a robust generalization scaffold that forcefully channels any domain-specific input through an automatic process: structural decomposition, operational constraint mapping, output synthesis—preserving codebase-rooted language and eliminating all meta-linguistic drift.\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Problem Exploder\n\nYour goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\n\n`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`",
      "step": "a",
      "title": "Problem Exploder",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"# lets try something new\\nyour new goal is to create a new generalized instruction sequence specifically designed to transform inputs into autonomous-agent-ready prompts/instructions, for use with autonomous coding assistants such as cursor, vscode+cline/augment/etc. these prompts are typically written in a \\\"codebase-oriented\\\" manner, where the `@codebase` represents the root (of a project/workspace/directory), examples:\\n```\\n- Identify all instances of structural redundancy within the codebase by defining criteria specific to architectural and implementation duplication or overlap.\\n- Survey the entire codebase to locate and enumerate all occurrences of structural redundancy, distinguishing these from non-structural (stylistic or cosmetic) issues.\\n- Eliminate all forms of identified structural redundancy while ensuring that no existing user-visible behavior, API contract, or edge-case handling is altered.\\n- Define and catalog all components, modules, files, and subsystems, distinguishing and marking each as essential or excess according to direct functional mapping.\\n- Consolidate and remove excess components while strictly preserving system function, minimizing peripheral impact.\\n- For every modification, justify and log the intention, action, and rationale within an audit/logging system that provides real-time traceability and is prepared for retroactive auditability.\\n- Implement an audit trail with logs that are timestamped, attributable, and tamper-evident, ensuring that every codebase change is properly documented and traceable to specific authors or automated processes.\\n- Prohibit cosmetic, stylistic, or naming changes unless they are inseparably tied to the removal of redundancy or bloat, and document any exceptions explicitly within the audit log.\\n- Enforce strict detection and flagging mechanisms to prevent disallowed cosmetic changes, allowing exceptions only when justified as essential for redundancy or bloat removal, with explicit rationale provided.\\n- Require comprehensive functional equivalence verification—using robust automated testing, benchmarking, or behavioral validation—for all modifications prior to acceptance.\\n- Establish rigorous criteria and tool-supported thresholds for successful functional equivalence, and automate testing and verification procedures.\\n- Automate roll-back and reversion mechanisms for any changes that fail functional equivalence or cause unintended side-effects, ensuring all reversion is prompt and complete.\\n- Develop and apply explicit minimal-impact criteria for all modification actions, constraining all interventions to only those addressing superfluous complexity.\\n- Mandate review and authorization checkpoints for each intended change, requiring concise rationale and authorization documentation at every intervention point.\\n- Ensure every modification is tightly scoped, minimizing the blast radius, and demonstrate that the action introduces no unnecessary impact beyond its core objective.\\n- Systematically and iteratively analyze all constituent elements of the codebase to detect and categorize all forms of redundancy, duplication, and obsolescence. Continuously identify and categorize all genuine redundancies and obsolescence while strictly prohibit unnecessary or cosmetic changes, with automated flagging and explicit justification required for any exceptions. Systematically and iteratively inspect all components within the system/environment to identify and remove elements that are unnecessary, duplicated, or outdated. Guarantee that all essential behaviors and interfaces remain unchanged by validating functional equivalence after each modification. Forbid changes that alter functionality unless directly required to remove redundancy.\\n- Map all references and usages of each element across all contexts.\\n- Analyze the entire codebase systematically and iteratively to maximize coherence, maintainability, and clarity by cataloging every element (file, module, function, asset, interface), mapping all references and usages, and detecting all forms of redundancy, duplication, and obsolescence—including exact and functional duplication and unused or outdated elements. Categorize findings using explicit, evidence-based metrics. Eliminate or consolidate only elements confirmed as redundant or obsolete through both automated and manual checks, ensuring no essential behavior or interface is altered. Enforce functional equivalence and preserve structural integrity after each change by performing comprehensive, codebase-wide validation and calibration using explicit, quantitative outputs (including git diff analysis before and after modifications). Prevent the introduction of new redundancy or inconsistency by continuously analyzing for and correcting systemic issues, ensuring systematic consistency across all codebase sections (e.g., /src/sections), and adhering to best practices throughout. Centralize repeated components or logic where high-value consolidation is possible without functional loss. Use the provided directory tree as the single source of truth for validation, and transparently document all processes, metrics, and validation outcomes. Prohibit modification of protected, legacy, or third-party code except if directly related to resolving redundancy or obsolescence.\\n- Maximize codebase coherence, maintainability, and clarity by systematically eliminating all forms of redundancy, duplication, and obsolescence, while ensuring functional equivalence and structural integrity. Prevent the persistence or inadvertent introduction of redundancy, duplication, obsolescence, or inconsistency within the codebase, and prohibit changes that alter essential behaviors or interfaces. Systematically maximize codebase coherence, maintainability, and clarity by eliminating all forms of redundancy, duplication, and obsolescence while strictly preserving functional equivalence, interface stability, and operational integrity across the entire codebase. Achieve maximal codebase coherence, maintainability, and clarity by systematically eliminating all forms of redundancy, duplication, and obsolescence, while preserving essential behaviors, interfaces, and structural integrity. Enforce continuous validation, consistency, and adherence to best practices throughout an iterative, evidence-driven consolidation and verification process.\\n- Please perform a rapid assessment of the project directory structure and identify the unique workflow and core functionality of the current utility. I'm particularly interested in a mermaid-diagram that shows how all the \\\"branches\\\" of the codebase converge. Utilize appropriate MCPs as necessary (e.g., 'filesystem-mcp').\\n- You're inside the current project and have access to the full codebase (and you interaction history with it). The current project/codebase is complex and is difficult to fully comprehend (when starting from scratch), and your contextwindow has grown large-so we need to find a way for you to express your accumulated knowledge in a way that i can copy and paste into a new context-window to transfer the most essential insights you've gathered throughout this (and previous) session(s).\\n- Aggregate all accumulated insights and knowledge about the current project/codebase from available context. Distill this information to identify core modules, architectural patterns, workflows, and points of integration. Condense the findings into a succinct yet comprehensive summary that highlights how these parts function together and converge toward the project's goals. Formulate the output as a single, optimized directive that encapsulates the codebase's integral components and operational cohesion. Present this synthesis as a set of clear, actionable commands or instructions, specifically phrased to instruct the coding assistant, not merely describe or report.\\n- Transform your accumulated, session-wide understanding of the current, complex codebase—gleaned from comprehensive context and full interaction history—into an actionable, instructive knowledge transfer protocol. Synthesize and distill all integral structural modules, essential architectural patterns, and key workflows, with special focus on their dynamic integration and functional convergence toward project objectives. Formulate a highly-optimized, directive-based set of precise commands targeted explicitly at coding assistants, ensuring exhaustive coverage of all major components, their relationships, and operational cohesion. The resulting synthesis must facilitate seamless portability: structure it for immediate copy-paste into any new context window, preserving the depth, interconnectivity, and systemic flow of the original codebase. Prioritize succinctness without compromising essential nuance, and express the entirety as a singular, comprehensive operational directive that primes the assistant for full-spectrum project navigation and intervention.\\n- Identify the fundamental structure, guiding principles, and core purpose of the project/codebase.\\n- Please familiarize yourself with the current @codebase. Your main imperative is to *actually* understand the codebase in a way where you're able to recognize meta-patterns and destroy complexity with simplicity and elegance, while capable of navigating through its *existing patterns and convergence* (never add unnecessary complexity). Deliver an ultra-concise project summary that rigorously defines scope, states objectives, and maximizes directional clarity. Provide a short summary of this project, including a bullet-point list of the core components and primary dependencies.\\n- Study the current codebase until you can identify core meta-patterns, and eliminate unnecessary complexity by applying simplicity within existing structures. Generate an ultra-concise project summary: define scope, objectives, and clarify direction. Summarize this project.\\n- Analyze the @codebase to uncover dominant patterns and remove complexity whenever possible, strictly adhering to present structures. Provide a succinct project overview with defined scope and explicit objectives. Include a brief project summary.\\n- Gain practical mastery over @codebase patterns, minimize complexity, and ensure all actions align with existing architecture. Supply a concise, clearly scoped project summary outlining objectives and key directions. Add a short summary.\\n- Absorb the current @codebase to the extent that you recognize meta-patterns and eradicate complexity, always favoring elegant simplicity. Navigate established patterns and points of convergence without adding unneeded complexity. Produce a rigorously concise project summary clearly stating scope and objectives for maximal directional clarity.\\n- Systematically dissect the codebase to extract core meta-patterns, eliminate all unnecessary complexity through elegant design, operate strictly within established architectural convergence, and deliver an ultra-concise project summary that rigorously defines scope and objectives with maximal clarity.\\n- Please familiarize yourself thoroughly with the current @codebase, beginning by systematically reviewing all markdown-files. Your core imperative is to *genuinely* understand the codebase to the degree where you can identify 'meta-patterns' and simplify complexity with elegance—always using and clarifying existing patterns, never introducing unnecessary complexity. Deliver an ultra-concise project summary that rigorously defines scope, states objectives, maximizes directional clarity, and highlights key meta-patterns discovered. Provide a short summary of this project.\\n- Achieve expert-level comprehension of the codebase by systematically analyzing all markdown files to extract meta-patterns, construct a robust contextual knowledgebase, and deliver an ultra-concise summary that rigorously defines scope and objectives, ensuring maximal clarity and alignment before making any changes.\\n- Fully internalize the @codebase to uncover meta-patterns and replace complexity with lucid simplicity. Remain aligned with existing convergence patterns, never increasing system intricacy. Generate a direct, sharply-focused summary that details scope and explicit objectives.\\n- Please thoroughly familiarize yourself with the current @codebase—your primary imperative is to understand the codebase so deeply that you could confidently make changes as if you were its original architect.\\n```\\n\\nhere's an example to show how generalized sequential prompts can be used effectively (on autonomous agents):\\n```\\n# Step 1:\\nFamiliarize yourself with the current @codebase. Comprehend the codebase in depth. Identify the fundamental structure, guiding principles, and core purpose.\\n# Step 2:\\nDive deeper to comprehend the intricate logic and inner workings that drive the project's functionality. As you explore, pay close attention to identifying the codebase's fundamental structure, underlying guiding principles, and its core purpose, ensuring you grasp both how and why the system is designed as it is. hen verify through a successful build.\\n# Step 3:\\nYou've now performed a successful build and familiarized yourself with the @codebase. Your next imperative is view the codebase through the lense of a brilliant (expert) SEO engineer, then create a short list of the most critical changes we need to make before bulding prod and deploying the website.\\n# Step 4:\\nYou've now performed a successful build and provided your SEO analysis of the codebase. Your next imperative is to leverage your knowledge of the codebase's fundamental structure and underlying guiding principles to ensure you're prepared for implementing the proposed SEO enhancements in a systematic order while retaining existing functionality and ensuring maximal clarity and alignment before making any changes.\\n# Step 5:\\nPlease systematically incorporate your proposed enhancements while ensuring existing functionality is retained, clarity is maximized, and alignment/coherence is maintained.\\n# Step 6:\\nPlease conduct a thorough final review to confirm seamless integration of all enhancements, ensuring that it is **truly** prepared for deployment.\\n```\\n\\n## Goal\\n\\nThe new sequence i'm tasking you to create needs to adhere to the established principles of the system, and should be able to take content-specific inputs and \\\"shape them\\\" into generalized prompts specifically (and meticulously) designed for use with autonomous agents. here's an example input:\\n```\\nllm_instructions_for_sublimetext_plugin='''You’re an expert Sublime Text plugin developer with a deep understanding of Python and the Sublime Text API. You specialize in creating intuitive and efficient plugins that enhance user productivity and streamline workflows. You possess deep knowledge of the Sublime Text API and its ecosystem (and best practices), along with a strong understanding of user needs and developer workflows.\\\\n\\\\n- Sublime Layout\\\\n    > Lagre Layouts\\\\n        > Lagre Regelsett knyttet til Layout (hvilke tabs som skal hvor, hvilke som skal lukkes, osv)\\\\n            > F.eks. `alltid flytt [\\\"main.py\\\", \\\"*.py.bak\\\"] til layout.4.index.2`\\\\n\\\\n    > Kan videreutvikles til en standard som automatisk opprettholdes ved alle (.sublime-project) prosjekter'''\\n```\\\"```'\\n\\n\",\n  \"enhancement_score\": 0.5,\n  \"flaw_analysis\": \"The so-called 'enhancement' transmutes the original prompt into a directionless, fragmented goal map characterized by analytical dissection rather than actionable sequence. Core elements from the verbose, codebase-centric instruction exemplars are scattered and atomized, with essential procedural and stepwise logic lost. The granular breakdown into explicit asks, sub-goals, blockers, and assumptions abolishes the unified operational flow, impairing agent-readiness. Severe information loss and nuance shift manifest as the descriptive, tightly-scoped, and audit-enforced architectures of the source are dissolved into meta-discussion and loosely organized bullet points. The semantic density of the original—exemplified by exhaustive, interlocking constraints such as 'prohibit changes except where audit-logged,' 'mandate functional equivalence verification,' and precise sequential nuances—is diluted by abstraction, eradicating the deterministic, actionable voice required for autonomous codebase intervention. Clarity ambiguities abound: the enhancement obfuscates the original's intent, subtly replacing procedural directives with theoretical needs analysis and task decomposition, rendering the result unsuitable for direct agent consumption. The stylistic register shifts drastically: from rigorous, operative, codebase-rooted imperative, to imprecise, consultants' planning language. This injects detrimental noise and undermines behavioral determinism. Impact is catastrophically diluted: there is no longer a contiguous instruction pipeline for input transformation; instead, only a jumble of prerequisites and worries. Coherence collapses as the enhanced version ceases to function as a prompt at all, failing in universality and operational rigor. The ambiguity score is a 9/10: almost every actionable connection and constraint in the original is obfuscated, broken apart, or rendered moot, killing the agent-ready thrust. The approach embodies abstraction-for-abstraction's sake; the prompt's original logic, determinism, agent-priming, and operational specificity are all annihilated.\",\n  \"alternative_enhancements\": [\n    \"Reshape the original sequence into a condensed but still strictly stepwise, imperative directive stream, focusing each step on input analysis, pattern extraction, abstraction, and agent-ready transformation—never fragmenting into meta-discussion.\",\n    \"Extract and modularize the core codebase-centric constraints (audit, functional equivalence, rollback, etc.) into reusable instruction templates, explicitly reintegrating them into the transformation pipeline for all content-specific inputs.\",\n    \"Formulate a robust generalization scaffold that forcefully channels any domain-specific input through an automatic process: structural decomposition, operational constraint mapping, output synthesis—preserving codebase-rooted language and eliminating all meta-linguistic drift.\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"goal_map\": [\n    {\n      \"explicit_ask\": \"Create a new generalized instruction sequence for transforming inputs into autonomous-agent-ready prompts/instructions for coding assistants.\",\n      \"hidden_dependencies\": [\n        \"Understand what makes a prompt 'autonomous-agent-ready' in the context of codebase navigation and modification.\",\n        \"Identify the unique requirements of autonomous coding assistants (Cursor, VSCode+cline/augment, etc.), including prompt formatting, context expectations, and operational triggers.\"\n      ],\n      \"sub_goals\": [\n        \"Analyze and extract principles from provided codebase-oriented prompt examples.\"\n      ],\n      \"blockers\": [\n        \"Ambiguity around 'generalized' (i.e., abstract/overarching vs. parameterizable vs. modular).\",\n        \"No formal definition of target platform prompt/instruction schema.\"\n      ]\n    },\n    {\n      \"explicit_ask\": \"Ensure the instruction sequence operates on content-specific inputs and 'shapes them' into generalized prompts meticulously designed for autonomous agents.\",\n      \"hidden_dependencies\": [\n        \"Specify which types of content-specific inputs are supported (e.g., requirements doc, feature request, unstructured prose).\",\n        \"Define the transformation workflow from content-specific input to agent-ready prompt, including abstraction steps, validation, and templating.\"\n      ],\n      \"sub_goals\": [\n        \"Develop or reference a schema/template for generalized instruction sequences appropriate for coding assistants.\",\n        \"Determine required input analysis and normalization steps.\"\n      ],\n      \"blockers\": [\n        \"Implicit requirement that transformation must avoid loss of essential domain/contextual nuance.\",\n        \"No guidance on handling insufficient or ambiguous user inputs.\"\n      ]\n    },\n    {\n      \"explicit_ask\": \"Adhere to the established principles demonstrated in the provided prompt examples.\",\n      \"hidden_dependencies\": [\n        \"Extract, enumerate, and encode the key operational and stylistic principles underlying the verbose prompt examples (e.g., audit logging, strict scoping, functional equivalence, etc.).\",\n        \"Ensure the new sequences retain codebase-centric language and instruction rigor.\"\n      ],\n      \"sub_goals\": [\n        \"Synthesize a list of rules/constraints to be universally applied to all generated prompts.\"\n      ],\n      \"blockers\": [\n        \"Potential conflicts between input content and system principles (e.g., user input requests something not allowed by best-practices encoded in examples).\"\n      ]\n    },\n    {\n      \"explicit_ask\": \"Sequence should enable transformation of any valid input into a robust, stepwise, operational instruction chain for the assistant.\",\n      \"hidden_dependencies\": [\n        \"Define criteria for stepwise decomposition and operationalization of arbitrary input (e.g., when/how to segment multi-step objectives).\",\n        \"Establish decision logic for splitting vs. merging instructions, and handling dependencies or prerequisites.\"\n      ],\n      \"sub_goals\": [\n        \"Identify and codify patterns for inferring actionability and optimal ordering from content-specific input.\",\n        \"Include necessary safeguards/audits described in examples (rollback, validation, etc.).\"\n      ],\n      \"blockers\": [\n        \"Ambiguity around multi-objective or vague user inputs.\",\n        \"No explicit process for verifying the sufficiency or completeness of transformed instructions.\"\n      ]\n    },\n    {\n      \"explicit_ask\": \"Facilitate seamless portability and immediate copy-paste utility for downstream context windows or session transfer.\",\n      \"hidden_dependencies\": [\n        \"Instruction format must be self-contained, with clear variable expressions and no external dependencies.\",\n        \"Prompt structure must anticipate downstream agent context-windows and potential lack of prior interaction history.\"\n      ],\n      \"sub_goals\": [\n        \"Embed summary, scope, rationale, and structural mapping within the prompt when necessary for context portability.\",\n        \"Add guidance for zero-context cold-starts.\"\n      ],\n      \"blockers\": [\n        \"Potential for prompt bloat vs. need for conciseness.\",\n        \"No strict token constraints defined.\"\n      ]\n    },\n    {\n      \"explicit_ask\": \"Preserve and explicitly communicate all necessary constraints, caveats, and procedural enforcements from the verbose prompt exemplars.\",\n      \"hidden_dependencies\": [\n        \"Requirement to encode auditability, reversibility, minimal-impact, and verification constraints in a generalized way.\",\n        \"Explicit handling of prohibited/modifiable code sections, exception processes, and forced documentation.\"\n      ],\n      \"sub_goals\": [\n        \"Map all governance and compliance requirements into reusable instruction fragments.\"\n      ],\n      \"blockers\": [\n        \"Difficulty in abstracting certain highly domain-specific constraints without loss of rigor.\"\n      ]\n    },\n    {\n      \"explicit_ask\": \"Support stepwise, imperative, and codebase-rooted logic in output instructions, rejecting mere summaries or meta-discussion.\",\n      \"hidden_dependencies\": [\n        \"Need for actionable, deterministic, non-ambiguous instructions at each step.\",\n        \"Avoidance of analysis paralysis or abstract, non-operational language.\"\n      ],\n      \"sub_goals\": [\n        \"Defining a transformation pipeline that moves from comprehension to action to validation, maintaining imperative mood.\"\n      ],\n      \"blockers\": [\n        \"Balance between generalization and operational specificity.\"\n      ]\n    },\n    {\n      \"explicit_ask\": \"Allow agents to reason about, navigate, and intervene in complex/legacy codebases while adhering to core principles.\",\n      \"hidden_dependencies\": [\n        \"Output prompts need to both inform (as in summaries) and direct (as in task instructions), seamlessly.\",\n        \"Handling session-spanning knowledge transfer for incremental/iterative agent workflows.\"\n      ],\n      \"sub_goals\": [\n        \"Incorporate system for knowledge aggregation, summary, and actionability.\",\n        \"Handle codebase evolution and retroactive audit requirements.\"\n      ],\n      \"blockers\": [\n        \"Potential lack of context continuity between invocations.\"\n      ]\n    },\n    {\n      \"explicit_ask\": \"Instructions must be compatible with a range of autonomous agents and code workspace setups.\",\n      \"hidden_dependencies\": [\n        \"Abstracting away codebase-specific idiosyncrasies for universal agent consumption.\",\n        \"Encoding references to '@codebase' or code root that are agent-agnostic.\"\n      ],\n      \"sub_goals\": [\n        \"Ensure variable and path handling generalizes across agent environments.\",\n        \"Document assumptions and agent-side expectations.\"\n      ],\n      \"blockers\": [\n        \"Variance in agent feature set and context window.\"\n      ]\n    },\n    {\n      \"explicit_ask\": \"Optimize for both exhaustiveness (capture all essential info/logic/constraints) and succinctness (avoid information overload, maximize copy-paste practicality).\",\n      \"hidden_dependencies\": [\n        \"Prioritization system for content compression—what information is essential vs. optional for agent tasking.\",\n        \"Enforcement of output length/complexity controls.\"\n      ],\n      \"sub_goals\": [\n        \"Define and encode minimal viable prompt structure for all transformation outputs.\"\n      ],\n      \"blockers\": [\n        \"Implicit tradeoff between detail/orchestration and brevity.\"\n      ]\n    }\n  ]\n}",
        }
      }
    }