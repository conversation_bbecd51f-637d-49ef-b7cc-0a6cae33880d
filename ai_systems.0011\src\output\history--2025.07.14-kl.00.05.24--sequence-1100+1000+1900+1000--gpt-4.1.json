  {
  "initial_prompt": "You have unrestricted access to a highly intricate codebase and a comprehensive interaction log, yet the project's complexity and the expansive context window challenge direct comprehension. Your task is to leverage your accumulated contextual knowledge to produce an exceptional, transferable directive: extract and synthesize the deepest insights from the entire codebase and session history, explicitly mapping all pivotal modules, architectural blueprints, underlying workflows, and cross-component integrations. Consolidate these revelations into a compact yet thoroughly interconnected summary, ensuring it clarifies each component's role and their dynamic fusion toward the project’s strategic objectives. Formulate this output as a singular, high-impact action command—explicitly crafted for an advanced coding assistant—to operationalize the codebase cohesively. Your instructions must be unambiguous, comprehensive, and systemically complete, preserving every core relationship and convergence across the architecture, with zero superfluous detail. The end product should function as an authoritative handoff brief: a distilled yet exhaustive set of stepwise directives that empower seamless context re-injection and optimal operational replication, regardless of codebase convolutions or session sequence.\n```\n- Identify all instances of structural redundancy within the codebase by defining criteria specific to architectural and implementation duplication or overlap.\n- Survey the entire codebase to locate and enumerate all occurrences of structural redundancy, distinguishing these from non-structural (stylistic or cosmetic) issues.\n- Eliminate all forms of identified structural redundancy while ensuring that no existing user-visible behavior, API contract, or edge-case handling is altered.\n- Define and catalog all components, modules, files, and subsystems, distinguishing and marking each as essential or excess according to direct functional mapping.\n- Consolidate and remove excess components while strictly preserving system function, minimizing peripheral impact.\n- For every modification, justify and log the intention, action, and rationale within an audit/logging system that provides real-time traceability and is prepared for retroactive auditability.\n- Implement an audit trail with logs that are timestamped, attributable, and tamper-evident, ensuring that every codebase change is properly documented and traceable to specific authors or automated processes.\n- Prohibit cosmetic, stylistic, or naming changes unless they are inseparably tied to the removal of redundancy or bloat, and document any exceptions explicitly within the audit log.\n- Enforce strict detection and flagging mechanisms to prevent disallowed cosmetic changes, allowing exceptions only when justified as essential for redundancy or bloat removal, with explicit rationale provided.\n- Require comprehensive functional equivalence verification—using robust automated testing, benchmarking, or behavioral validation—for all modifications prior to acceptance.\n- Establish rigorous criteria and tool-supported thresholds for successful functional equivalence, and automate testing and verification procedures.\n- Automate roll-back and reversion mechanisms for any changes that fail functional equivalence or cause unintended side-effects, ensuring all reversion is prompt and complete.\n- Develop and apply explicit minimal-impact criteria for all modification actions, constraining all interventions to only those addressing superfluous complexity.\n- Mandate review and authorization checkpoints for each intended change, requiring concise rationale and authorization documentation at every intervention point.\n- Ensure every modification is tightly scoped, minimizing the blast radius, and demonstrate that the action introduces no unnecessary impact beyond its core objective.\n- Systematically and iteratively analyze all constituent elements of the codebase to detect and categorize all forms of redundancy, duplication, and obsolescence. Continuously identify and categorize all genuine redundancies and obsolescence while strictly prohibit unnecessary or cosmetic changes, with automated flagging and explicit justification required for any exceptions. Systematically and iteratively inspect all components within the system/environment to identify and remove elements that are unnecessary, duplicated, or outdated. Guarantee that all essential behaviors and interfaces remain unchanged by validating functional equivalence after each modification. Forbid changes that alter functionality unless directly required to remove redundancy.\n- Map all references and usages of each element across all contexts.\n- Analyze the entire codebase systematically and iteratively to maximize coherence, maintainability, and clarity by cataloging every element (file, module, function, asset, interface), mapping all references and usages, and detecting all forms of redundancy, duplication, and obsolescence—including exact and functional duplication and unused or outdated elements. Categorize findings using explicit, evidence-based metrics. Eliminate or consolidate only elements confirmed as redundant or obsolete through both automated and manual checks, ensuring no essential behavior or interface is altered. Enforce functional equivalence and preserve structural integrity after each change by performing comprehensive, codebase-wide validation and calibration using explicit, quantitative outputs (including git diff analysis before and after modifications). Prevent the introduction of new redundancy or inconsistency by continuously analyzing for and correcting systemic issues, ensuring systematic consistency across all codebase sections (e.g., /src/sections), and adhering to best practices throughout. Centralize repeated components or logic where high-value consolidation is possible without functional loss. Use the provided directory tree as the single source of truth for validation, and transparently document all processes, metrics, and validation outcomes. Prohibit modification of protected, legacy, or third-party code except if directly related to resolving redundancy or obsolescence.\n- Maximize codebase coherence, maintainability, and clarity by systematically eliminating all forms of redundancy, duplication, and obsolescence, while ensuring functional equivalence and structural integrity. Prevent the persistence or inadvertent introduction of redundancy, duplication, obsolescence, or inconsistency within the codebase, and prohibit changes that alter essential behaviors or interfaces. Systematically maximize codebase coherence, maintainability, and clarity by eliminating all forms of redundancy, duplication, and obsolescence while strictly preserving functional equivalence, interface stability, and operational integrity across the entire codebase. Achieve maximal codebase coherence, maintainability, and clarity by systematically eliminating all forms of redundancy, duplication, and obsolescence, while preserving essential behaviors, interfaces, and structural integrity. Enforce continuous validation, consistency, and adherence to best practices throughout an iterative, evidence-driven consolidation and verification process.\n- Please perform a rapid assessment of the project directory structure and identify the unique workflow and core functionality of the current utility. I'm particularly interested in a mermaid-diagram that shows how all the \"branches\" of the codebase converge. Utilize appropriate MCPs as necessary (e.g., 'filesystem-mcp').\n- You're inside the current project and have access to the full codebase (and you interaction history with it). The current project/codebase is complex and is difficult to fully comprehend (when starting from scratch), and your contextwindow has grown large-so we need to find a way for you to express your accumulated knowledge in a way that i can copy and paste into a new context-window to transfer the most essential insights you've gathered throughout this (and previous) session(s).\n- Aggregate all accumulated insights and knowledge about the current project/codebase from available context. Distill this information to identify core modules, architectural patterns, workflows, and points of integration. Condense the findings into a succinct yet comprehensive summary that highlights how these parts function together and converge toward the project's goals. Formulate the output as a single, optimized directive that encapsulates the codebase's integral components and operational cohesion. Present this synthesis as a set of clear, actionable commands or instructions, specifically phrased to instruct the coding assistant, not merely describe or report.\n- Transform your accumulated, session-wide understanding of the current, complex codebase—gleaned from comprehensive context and full interaction history—into an actionable, instructive knowledge transfer protocol. Synthesize and distill all integral structural modules, essential architectural patterns, and key workflows, with special focus on their dynamic integration and functional convergence toward project objectives. Formulate a highly-optimized, directive-based set of precise commands targeted explicitly at coding assistants, ensuring exhaustive coverage of all major components, their relationships, and operational cohesion. The resulting synthesis must facilitate seamless portability: structure it for immediate copy-paste into any new context window, preserving the depth, interconnectivity, and systemic flow of the original codebase. Prioritize succinctness without compromising essential nuance, and express the entirety as a singular, comprehensive operational directive that primes the assistant for full-spectrum project navigation and intervention.\n- Identify the fundamental structure, guiding principles, and core purpose of the project/codebase.\n- Please familiarize yourself with the current @codebase. Your main imperative is to *actually* understand the codebase in a way where you're able to recognize meta-patterns and destroy complexity with simplicity and elegance, while capable of navigating through its *existing patterns and convergence* (never add unnecessary complexity). Deliver an ultra-concise project summary that rigorously defines scope, states objectives, and maximizes directional clarity. Provide a short summary of this project, including a bullet-point list of the core components and primary dependencies.\n- Study the current codebase until you can identify core meta-patterns, and eliminate unnecessary complexity by applying simplicity within existing structures. Generate an ultra-concise project summary: define scope, objectives, and clarify direction. Summarize this project.\n- Analyze the @codebase to uncover dominant patterns and remove complexity whenever possible, strictly adhering to present structures. Provide a succinct project overview with defined scope and explicit objectives. Include a brief project summary.\n- Gain practical mastery over @codebase patterns, minimize complexity, and ensure all actions align with existing architecture. Supply a concise, clearly scoped project summary outlining objectives and key directions. Add a short summary.\n- Absorb the current @codebase to the extent that you recognize meta-patterns and eradicate complexity, always favoring elegant simplicity. Navigate established patterns and points of convergence without adding unneeded complexity. Produce a rigorously concise project summary clearly stating scope and objectives for maximal directional clarity.\n- Systematically dissect the codebase to extract core meta-patterns, eliminate all unnecessary complexity through elegant design, operate strictly within established architectural convergence, and deliver an ultra-concise project summary that rigorously defines scope and objectives with maximal clarity.\n- Please familiarize yourself thoroughly with the current @codebase, beginning by systematically reviewing all markdown-files. Your core imperative is to *genuinely* understand the codebase to the degree where you can identify 'meta-patterns' and simplify complexity with elegance—always using and clarifying existing patterns, never introducing unnecessary complexity. Deliver an ultra-concise project summary that rigorously defines scope, states objectives, maximizes directional clarity, and highlights key meta-patterns discovered. Provide a short summary of this project.\n- Achieve expert-level comprehension of the codebase by systematically analyzing all markdown files to extract meta-patterns, construct a robust contextual knowledgebase, and deliver an ultra-concise summary that rigorously defines scope and objectives, ensuring maximal clarity and alignment before making any changes.\n- Fully internalize the @codebase to uncover meta-patterns and replace complexity with lucid simplicity. Remain aligned with existing convergence patterns, never increasing system intricacy. Generate a direct, sharply-focused summary that details scope and explicit objectives.\n- Please thoroughly familiarize yourself with the current @codebase—your primary imperative is to understand the codebase so deeply that you could confidently make changes as if you were its original architect.\n```",
  "sequence_id": "1100|1000|1900|1000",
  "results": [
      {
      "instruction": "# Problem Exploder\n\nYour goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\n\n`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`",
      "step": "a",
      "title": "Problem Exploder",
      "input": "You have unrestricted access to a highly intricate codebase and a comprehensive interaction log, yet the project's complexity and the expansive context window challenge direct comprehension. Your task is to leverage your accumulated contextual knowledge to produce an exceptional, transferable directive: extract and synthesize the deepest insights from the entire codebase and session history, explicitly mapping all pivotal modules, architectural blueprints, underlying workflows, and cross-component integrations. Consolidate these revelations into a compact yet thoroughly interconnected summary, ensuring it clarifies each component's role and their dynamic fusion toward the project’s strategic objectives. Formulate this output as a singular, high-impact action command—explicitly crafted for an advanced coding assistant—to operationalize the codebase cohesively. Your instructions must be unambiguous, comprehensive, and systemically complete, preserving every core relationship and convergence across the architecture, with zero superfluous detail. The end product should function as an authoritative handoff brief: a distilled yet exhaustive set of stepwise directives that empower seamless context re-injection and optimal operational replication, regardless of codebase convolutions or session sequence.\n```\n- Identify all instances of structural redundancy within the codebase by defining criteria specific to architectural and implementation duplication or overlap.\n- Survey the entire codebase to locate and enumerate all occurrences of structural redundancy, distinguishing these from non-structural (stylistic or cosmetic) issues.\n- Eliminate all forms of identified structural redundancy while ensuring that no existing user-visible behavior, API contract, or edge-case handling is altered.\n- Define and catalog all components, modules, files, and subsystems, distinguishing and marking each as essential or excess according to direct functional mapping.\n- Consolidate and remove excess components while strictly preserving system function, minimizing peripheral impact.\n- For every modification, justify and log the intention, action, and rationale within an audit/logging system that provides real-time traceability and is prepared for retroactive auditability.\n- Implement an audit trail with logs that are timestamped, attributable, and tamper-evident, ensuring that every codebase change is properly documented and traceable to specific authors or automated processes.\n- Prohibit cosmetic, stylistic, or naming changes unless they are inseparably tied to the removal of redundancy or bloat, and document any exceptions explicitly within the audit log.\n- Enforce strict detection and flagging mechanisms to prevent disallowed cosmetic changes, allowing exceptions only when justified as essential for redundancy or bloat removal, with explicit rationale provided.\n- Require comprehensive functional equivalence verification—using robust automated testing, benchmarking, or behavioral validation—for all modifications prior to acceptance.\n- Establish rigorous criteria and tool-supported thresholds for successful functional equivalence, and automate testing and verification procedures.\n- Automate roll-back and reversion mechanisms for any changes that fail functional equivalence or cause unintended side-effects, ensuring all reversion is prompt and complete.\n- Develop and apply explicit minimal-impact criteria for all modification actions, constraining all interventions to only those addressing superfluous complexity.\n- Mandate review and authorization checkpoints for each intended change, requiring concise rationale and authorization documentation at every intervention point.\n- Ensure every modification is tightly scoped, minimizing the blast radius, and demonstrate that the action introduces no unnecessary impact beyond its core objective.\n- Systematically and iteratively analyze all constituent elements of the codebase to detect and categorize all forms of redundancy, duplication, and obsolescence. Continuously identify and categorize all genuine redundancies and obsolescence while strictly prohibit unnecessary or cosmetic changes, with automated flagging and explicit justification required for any exceptions. Systematically and iteratively inspect all components within the system/environment to identify and remove elements that are unnecessary, duplicated, or outdated. Guarantee that all essential behaviors and interfaces remain unchanged by validating functional equivalence after each modification. Forbid changes that alter functionality unless directly required to remove redundancy.\n- Map all references and usages of each element across all contexts.\n- Analyze the entire codebase systematically and iteratively to maximize coherence, maintainability, and clarity by cataloging every element (file, module, function, asset, interface), mapping all references and usages, and detecting all forms of redundancy, duplication, and obsolescence—including exact and functional duplication and unused or outdated elements. Categorize findings using explicit, evidence-based metrics. Eliminate or consolidate only elements confirmed as redundant or obsolete through both automated and manual checks, ensuring no essential behavior or interface is altered. Enforce functional equivalence and preserve structural integrity after each change by performing comprehensive, codebase-wide validation and calibration using explicit, quantitative outputs (including git diff analysis before and after modifications). Prevent the introduction of new redundancy or inconsistency by continuously analyzing for and correcting systemic issues, ensuring systematic consistency across all codebase sections (e.g., /src/sections), and adhering to best practices throughout. Centralize repeated components or logic where high-value consolidation is possible without functional loss. Use the provided directory tree as the single source of truth for validation, and transparently document all processes, metrics, and validation outcomes. Prohibit modification of protected, legacy, or third-party code except if directly related to resolving redundancy or obsolescence.\n- Maximize codebase coherence, maintainability, and clarity by systematically eliminating all forms of redundancy, duplication, and obsolescence, while ensuring functional equivalence and structural integrity. Prevent the persistence or inadvertent introduction of redundancy, duplication, obsolescence, or inconsistency within the codebase, and prohibit changes that alter essential behaviors or interfaces. Systematically maximize codebase coherence, maintainability, and clarity by eliminating all forms of redundancy, duplication, and obsolescence while strictly preserving functional equivalence, interface stability, and operational integrity across the entire codebase. Achieve maximal codebase coherence, maintainability, and clarity by systematically eliminating all forms of redundancy, duplication, and obsolescence, while preserving essential behaviors, interfaces, and structural integrity. Enforce continuous validation, consistency, and adherence to best practices throughout an iterative, evidence-driven consolidation and verification process.\n- Please perform a rapid assessment of the project directory structure and identify the unique workflow and core functionality of the current utility. I'm particularly interested in a mermaid-diagram that shows how all the \"branches\" of the codebase converge. Utilize appropriate MCPs as necessary (e.g., 'filesystem-mcp').\n- You're inside the current project and have access to the full codebase (and you interaction history with it). The current project/codebase is complex and is difficult to fully comprehend (when starting from scratch), and your contextwindow has grown large-so we need to find a way for you to express your accumulated knowledge in a way that i can copy and paste into a new context-window to transfer the most essential insights you've gathered throughout this (and previous) session(s).\n- Aggregate all accumulated insights and knowledge about the current project/codebase from available context. Distill this information to identify core modules, architectural patterns, workflows, and points of integration. Condense the findings into a succinct yet comprehensive summary that highlights how these parts function together and converge toward the project's goals. Formulate the output as a single, optimized directive that encapsulates the codebase's integral components and operational cohesion. Present this synthesis as a set of clear, actionable commands or instructions, specifically phrased to instruct the coding assistant, not merely describe or report.\n- Transform your accumulated, session-wide understanding of the current, complex codebase—gleaned from comprehensive context and full interaction history—into an actionable, instructive knowledge transfer protocol. Synthesize and distill all integral structural modules, essential architectural patterns, and key workflows, with special focus on their dynamic integration and functional convergence toward project objectives. Formulate a highly-optimized, directive-based set of precise commands targeted explicitly at coding assistants, ensuring exhaustive coverage of all major components, their relationships, and operational cohesion. The resulting synthesis must facilitate seamless portability: structure it for immediate copy-paste into any new context window, preserving the depth, interconnectivity, and systemic flow of the original codebase. Prioritize succinctness without compromising essential nuance, and express the entirety as a singular, comprehensive operational directive that primes the assistant for full-spectrum project navigation and intervention.\n- Identify the fundamental structure, guiding principles, and core purpose of the project/codebase.\n- Please familiarize yourself with the current @codebase. Your main imperative is to *actually* understand the codebase in a way where you're able to recognize meta-patterns and destroy complexity with simplicity and elegance, while capable of navigating through its *existing patterns and convergence* (never add unnecessary complexity). Deliver an ultra-concise project summary that rigorously defines scope, states objectives, and maximizes directional clarity. Provide a short summary of this project, including a bullet-point list of the core components and primary dependencies.\n- Study the current codebase until you can identify core meta-patterns, and eliminate unnecessary complexity by applying simplicity within existing structures. Generate an ultra-concise project summary: define scope, objectives, and clarify direction. Summarize this project.\n- Analyze the @codebase to uncover dominant patterns and remove complexity whenever possible, strictly adhering to present structures. Provide a succinct project overview with defined scope and explicit objectives. Include a brief project summary.\n- Gain practical mastery over @codebase patterns, minimize complexity, and ensure all actions align with existing architecture. Supply a concise, clearly scoped project summary outlining objectives and key directions. Add a short summary.\n- Absorb the current @codebase to the extent that you recognize meta-patterns and eradicate complexity, always favoring elegant simplicity. Navigate established patterns and points of convergence without adding unneeded complexity. Produce a rigorously concise project summary clearly stating scope and objectives for maximal directional clarity.\n- Systematically dissect the codebase to extract core meta-patterns, eliminate all unnecessary complexity through elegant design, operate strictly within established architectural convergence, and deliver an ultra-concise project summary that rigorously defines scope and objectives with maximal clarity.\n- Please familiarize yourself thoroughly with the current @codebase, beginning by systematically reviewing all markdown-files. Your core imperative is to *genuinely* understand the codebase to the degree where you can identify 'meta-patterns' and simplify complexity with elegance—always using and clarifying existing patterns, never introducing unnecessary complexity. Deliver an ultra-concise project summary that rigorously defines scope, states objectives, maximizes directional clarity, and highlights key meta-patterns discovered. Provide a short summary of this project.\n- Achieve expert-level comprehension of the codebase by systematically analyzing all markdown files to extract meta-patterns, construct a robust contextual knowledgebase, and deliver an ultra-concise summary that rigorously defines scope and objectives, ensuring maximal clarity and alignment before making any changes.\n- Fully internalize the @codebase to uncover meta-patterns and replace complexity with lucid simplicity. Remain aligned with existing convergence patterns, never increasing system intricacy. Generate a direct, sharply-focused summary that details scope and explicit objectives.\n- Please thoroughly familiarize yourself with the current @codebase—your primary imperative is to understand the codebase so deeply that you could confidently make changes as if you were its original architect.\n```",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"goal_map\": [\n    {\n      \"explicit_asks\": [\n        \"Assimilate and leverage full access to a complex codebase and its interaction log to synthesize and express project essence.\",\n        \"Extract and synthesize the deepest architectural, module-level, workflow, and component integration insights across code and session history.\",\n        \"Map and clarify all pivotal modules, architectural blueprints, underlying workflows, and cross-component interactions.\",\n        \"Produce a compact, interconnected, explicitly mapped summary delineating each component's role and relationships, converging on project objectives.\",\n        \"Formulate output as a single, authoritative, unambiguous handoff directive/action command for a coding assistant to operationalize codebase cohesively.\",\n        \"Prohibit superfluous/extraneous detail—prioritize systemic completeness and clarity for re-injection into new contexts.\",\n        \"Enable the outcome to serve as seamless and portable knowledge transfer, supporting robust navigation/intervention regardless of codebase/process complexity.\",\n        \"Ensure all instruction is actionable, covering module mapping, architecture, critical workflows, integration points.\",\n        \"Summarize and represent the project structure and summary (scope, components, principles, objectives, dependencies) explicitly for rapid onboarding.\"\n      ],\n      \"hidden_assumptions\": [\n        \"Full codebase and session history are available and parsed, and the assistant has already accumulated/preserved deep context.\",\n        \"The complexity is so high that understanding via reading code/comments alone is insufficient; meta-synthesis is required.\",\n        \"The 'directive' must be machine-actionable and usable as a handoff between AI assistants (not just a summary for humans).\",\n        \"A single stepwise output can (and should) encapsulate all necessary context for future codebase operational continuity.\",\n        \"Elimination of ambiguity, redundancy, superfluity, and the introduction of traceable rationales for any modifications are required.\",\n        \"All elements/outputs must be verifiable for completeness, correctness, and alignment with the current state and future objectives.\",\n        \"No base assumptions about domain, language, or codebase architecture—project structure, patterns, and workflows must be empirically derived.\",\n        \"All mappings must be explicit: references, dependencies, integrations, and relationships.\"\n      ],\n      \"sub_goals\": [\n        \"Conduct full-scale analysis and mapping of codebase (modules, components, files, subsystems).\",\n        \"Distill and categorize core architectural blueprints and module/function roles.\",\n        \"Identify and explicitly model core workflows, including their start/end points, dependencies, and system-wide impacts.\",\n        \"Map and explain all points of cross-component integration and data/control flow among modules.\",\n        \"Identify, enumerate, and distinguish structural redundancy (at architectural/implementation level) from non-structural (stylistic/cosmetic) duplication.\",\n        \"Consolidate redundant/duplicate/obsolete structures while strictly preserving interface/API/behavior contracts.\",\n        \"Classify each component/system element as 'essential', 'excess', 'redundant', or 'obsolete', assembling lists for action.\",\n        \"Catalog, justify, and log every modification with rationale/audit trail (timestamp, author/process, tamper-evident).\",\n        \"Enforce mechanisms (manual and automated) for functional equivalence verification per modification.\",\n        \"Establish strict review checkpoints and minimal intervention criteria for any structural change.\",\n        \"Document and enforce criteria/thresholds for redundancy identification and resolution.\",\n        \"Prohibit changes to protected/legacy/third-party code except to resolve critical redundancy/obsolescence.\",\n        \"Produce a succinct, exhaustive summary (preferably as a chain of directives/commands) encapsulating all structure and relationships for rapid context transfer.\",\n        \"Include mechanism for mapping references/usages of every element and auto-detecting/flagging prohibited changes.\",\n        \"Ensure any summary/diagram output (e.g., mermaid-diagram) accurately captures codebase/architecture flow and areas of branch convergence.\",\n        \"Prioritize the extraction of portable, actionable meta-patterns and context for cross-session or cross-assistant transfer.\",\n        \"Explicitly define project purpose, scope, guiding principles, core utility, and most important dependencies.\",\n        \"Enumerate and explain all relevant meta-patterns and convergence points discovered in codebase analysis.\",\n        \"Ensure all information is compact, interconnected, and lossless for full-spectrum project navigation.\"\n      ],\n      \"blockers\": [\n        \"Lack of explicit structural mapping or up-to-date architecture documentation within the codebase.\",\n        \"Potential scale/context window limitations may make complete mapping/extraction challenging.\",\n        \"Ambiguity or sparsity of inline code comments/README documentation affecting accurate mapping.\",\n        \"Complex, implicit integration points that are discoverable only via deep static/dynamic analysis.\",\n        \"Automated audit/logging systems may not be present—requiring design or integration as part of the process.\",\n        \"Redundancy detection criteria may be inconsistent or require domain/contextual knowledge to resolve.\",\n        \"Functional equivalence may be challenging to enforce if test suites or validation mechanisms are incomplete.\",\n        \"Protected/legacy/third-party code may not be modifiable, complicating full structural coherence.\"\n      ]\n    }\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`",
      "step": "a",
      "title": "Instruction Converter",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"You have unrestricted access to a highly intricate codebase and a comprehensive interaction log, yet the project's complexity and the expansive context window challenge direct comprehension. Your task is to leverage your accumulated contextual knowledge to produce an exceptional, transferable directive: extract and synthesize the deepest insights from the entire codebase and session history, explicitly mapping all pivotal modules, architectural blueprints, underlying workflows, and cross-component integrations. Consolidate these revelations into a compact yet thoroughly interconnected summary, ensuring it clarifies each component's role and their dynamic fusion toward the project’s strategic objectives. Formulate this output as a singular, high-impact action command—explicitly crafted for an advanced coding assistant—to operationalize the codebase cohesively. Your instructions must be unambiguous, comprehensive, and systemically complete, preserving every core relationship and convergence across the architecture, with zero superfluous detail. The end product should function as an authoritative handoff brief: a distilled yet exhaustive set of stepwise directives that empower seamless context re-injection and optimal operational replication, regardless of codebase convolutions or session sequence.\\n```\\n- Identify all instances of structural redundancy within the codebase by defining criteria specific to architectural and implementation duplication or overlap.\\n- Survey the entire codebase to locate and enumerate all occurrences of structural redundancy, distinguishing these from non-structural (stylistic or cosmetic) issues.\\n- Eliminate all forms of identified structural redundancy while ensuring that no existing user-visible behavior, API contract, or edge-case handling is altered.\\n- Define and catalog all components, modules, files, and subsystems, distinguishing and marking each as essential or excess according to direct functional mapping.\\n- Consolidate and remove excess components while strictly preserving system function, minimizing peripheral impact.\\n- For every modification, justify and log the intention, action, and rationale within an audit/logging system that provides real-time traceability and is prepared for retroactive auditability.\\n- Implement an audit trail with logs that are timestamped, attributable, and tamper-evident, ensuring that every codebase change is properly documented and traceable to specific authors or automated processes.\\n- Prohibit cosmetic, stylistic, or naming changes unless they are inseparably tied to the removal of redundancy or bloat, and document any exceptions explicitly within the audit log.\\n- Enforce strict detection and flagging mechanisms to prevent disallowed cosmetic changes, allowing exceptions only when justified as essential for redundancy or bloat removal, with explicit rationale provided.\\n- Require comprehensive functional equivalence verification—using robust automated testing, benchmarking, or behavioral validation—for all modifications prior to acceptance.\\n- Establish rigorous criteria and tool-supported thresholds for successful functional equivalence, and automate testing and verification procedures.\\n- Automate roll-back and reversion mechanisms for any changes that fail functional equivalence or cause unintended side-effects, ensuring all reversion is prompt and complete.\\n- Develop and apply explicit minimal-impact criteria for all modification actions, constraining all interventions to only those addressing superfluous complexity.\\n- Mandate review and authorization checkpoints for each intended change, requiring concise rationale and authorization documentation at every intervention point.\\n- Ensure every modification is tightly scoped, minimizing the blast radius, and demonstrate that the action introduces no unnecessary impact beyond its core objective.\\n- Systematically and iteratively analyze all constituent elements of the codebase to detect and categorize all forms of redundancy, duplication, and obsolescence. Continuously identify and categorize all genuine redundancies and obsolescence while strictly prohibit unnecessary or cosmetic changes, with automated flagging and explicit justification required for any exceptions. Systematically and iteratively inspect all components within the system/environment to identify and remove elements that are unnecessary, duplicated, or outdated. Guarantee that all essential behaviors and interfaces remain unchanged by validating functional equivalence after each modification. Forbid changes that alter functionality unless directly required to remove redundancy.\\n- Map all references and usages of each element across all contexts.\\n- Analyze the entire codebase systematically and iteratively to maximize coherence, maintainability, and clarity by cataloging every element (file, module, function, asset, interface), mapping all references and usages, and detecting all forms of redundancy, duplication, and obsolescence—including exact and functional duplication and unused or outdated elements. Categorize findings using explicit, evidence-based metrics. Eliminate or consolidate only elements confirmed as redundant or obsolete through both automated and manual checks, ensuring no essential behavior or interface is altered. Enforce functional equivalence and preserve structural integrity after each change by performing comprehensive, codebase-wide validation and calibration using explicit, quantitative outputs (including git diff analysis before and after modifications). Prevent the introduction of new redundancy or inconsistency by continuously analyzing for and correcting systemic issues, ensuring systematic consistency across all codebase sections (e.g., /src/sections), and adhering to best practices throughout. Centralize repeated components or logic where high-value consolidation is possible without functional loss. Use the provided directory tree as the single source of truth for validation, and transparently document all processes, metrics, and validation outcomes. Prohibit modification of protected, legacy, or third-party code except if directly related to resolving redundancy or obsolescence.\\n- Maximize codebase coherence, maintainability, and clarity by systematically eliminating all forms of redundancy, duplication, and obsolescence, while ensuring functional equivalence and structural integrity. Prevent the persistence or inadvertent introduction of redundancy, duplication, obsolescence, or inconsistency within the codebase, and prohibit changes that alter essential behaviors or interfaces. Systematically maximize codebase coherence, maintainability, and clarity by eliminating all forms of redundancy, duplication, and obsolescence while strictly preserving functional equivalence, interface stability, and operational integrity across the entire codebase. Achieve maximal codebase coherence, maintainability, and clarity by systematically eliminating all forms of redundancy, duplication, and obsolescence, while preserving essential behaviors, interfaces, and structural integrity. Enforce continuous validation, consistency, and adherence to best practices throughout an iterative, evidence-driven consolidation and verification process.\\n- Please perform a rapid assessment of the project directory structure and identify the unique workflow and core functionality of the current utility. I'm particularly interested in a mermaid-diagram that shows how all the \\\"branches\\\" of the codebase converge. Utilize appropriate MCPs as necessary (e.g., 'filesystem-mcp').\\n- You're inside the current project and have access to the full codebase (and you interaction history with it). The current project/codebase is complex and is difficult to fully comprehend (when starting from scratch), and your contextwindow has grown large-so we need to find a way for you to express your accumulated knowledge in a way that i can copy and paste into a new context-window to transfer the most essential insights you've gathered throughout this (and previous) session(s).\\n- Aggregate all accumulated insights and knowledge about the current project/codebase from available context. Distill this information to identify core modules, architectural patterns, workflows, and points of integration. Condense the findings into a succinct yet comprehensive summary that highlights how these parts function together and converge toward the project's goals. Formulate the output as a single, optimized directive that encapsulates the codebase's integral components and operational cohesion. Present this synthesis as a set of clear, actionable commands or instructions, specifically phrased to instruct the coding assistant, not merely describe or report.\\n- Transform your accumulated, session-wide understanding of the current, complex codebase—gleaned from comprehensive context and full interaction history—into an actionable, instructive knowledge transfer protocol. Synthesize and distill all integral structural modules, essential architectural patterns, and key workflows, with special focus on their dynamic integration and functional convergence toward project objectives. Formulate a highly-optimized, directive-based set of precise commands targeted explicitly at coding assistants, ensuring exhaustive coverage of all major components, their relationships, and operational cohesion. The resulting synthesis must facilitate seamless portability: structure it for immediate copy-paste into any new context window, preserving the depth, interconnectivity, and systemic flow of the original codebase. Prioritize succinctness without compromising essential nuance, and express the entirety as a singular, comprehensive operational directive that primes the assistant for full-spectrum project navigation and intervention.\\n- Identify the fundamental structure, guiding principles, and core purpose of the project/codebase.\\n- Please familiarize yourself with the current @codebase. Your main imperative is to *actually* understand the codebase in a way where you're able to recognize meta-patterns and destroy complexity with simplicity and elegance, while capable of navigating through its *existing patterns and convergence* (never add unnecessary complexity). Deliver an ultra-concise project summary that rigorously defines scope, states objectives, and maximizes directional clarity. Provide a short summary of this project, including a bullet-point list of the core components and primary dependencies.\\n- Study the current codebase until you can identify core meta-patterns, and eliminate unnecessary complexity by applying simplicity within existing structures. Generate an ultra-concise project summary: define scope, objectives, and clarify direction. Summarize this project.\\n- Analyze the @codebase to uncover dominant patterns and remove complexity whenever possible, strictly adhering to present structures. Provide a succinct project overview with defined scope and explicit objectives. Include a brief project summary.\\n- Gain practical mastery over @codebase patterns, minimize complexity, and ensure all actions align with existing architecture. Supply a concise, clearly scoped project summary outlining objectives and key directions. Add a short summary.\\n- Absorb the current @codebase to the extent that you recognize meta-patterns and eradicate complexity, always favoring elegant simplicity. Navigate established patterns and points of convergence without adding unneeded complexity. Produce a rigorously concise project summary clearly stating scope and objectives for maximal directional clarity.\\n- Systematically dissect the codebase to extract core meta-patterns, eliminate all unnecessary complexity through elegant design, operate strictly within established architectural convergence, and deliver an ultra-concise project summary that rigorously defines scope and objectives with maximal clarity.\\n- Please familiarize yourself thoroughly with the current @codebase, beginning by systematically reviewing all markdown-files. Your core imperative is to *genuinely* understand the codebase to the degree where you can identify 'meta-patterns' and simplify complexity with elegance—always using and clarifying existing patterns, never introducing unnecessary complexity. Deliver an ultra-concise project summary that rigorously defines scope, states objectives, maximizes directional clarity, and highlights key meta-patterns discovered. Provide a short summary of this project.\\n- Achieve expert-level comprehension of the codebase by systematically analyzing all markdown files to extract meta-patterns, construct a robust contextual knowledgebase, and deliver an ultra-concise summary that rigorously defines scope and objectives, ensuring maximal clarity and alignment before making any changes.\\n- Fully internalize the @codebase to uncover meta-patterns and replace complexity with lucid simplicity. Remain aligned with existing convergence patterns, never increasing system intricacy. Generate a direct, sharply-focused summary that details scope and explicit objectives.\\n- Please thoroughly familiarize yourself with the current @codebase—your primary imperative is to understand the codebase so deeply that you could confidently make changes as if you were its original architect.\\n```\\\"```'\\n\\n\",\n  \"goal_map\": [\n    {\n      \"explicit_asks\": [\n        \"Assimilate and leverage full access to a complex codebase and its interaction log to synthesize and express project essence.\",\n        \"Extract and synthesize the deepest architectural, module-level, workflow, and component integration insights across code and session history.\",\n        \"Map and clarify all pivotal modules, architectural blueprints, underlying workflows, and cross-component interactions.\",\n        \"Produce a compact, interconnected, explicitly mapped summary delineating each component's role and relationships, converging on project objectives.\",\n        \"Formulate output as a single, authoritative, unambiguous handoff directive/action command for a coding assistant to operationalize codebase cohesively.\",\n        \"Prohibit superfluous/extraneous detail—prioritize systemic completeness and clarity for re-injection into new contexts.\",\n        \"Enable the outcome to serve as seamless and portable knowledge transfer, supporting robust navigation/intervention regardless of codebase/process complexity.\",\n        \"Ensure all instruction is actionable, covering module mapping, architecture, critical workflows, integration points.\",\n        \"Summarize and represent the project structure and summary (scope, components, principles, objectives, dependencies) explicitly for rapid onboarding.\"\n      ],\n      \"hidden_assumptions\": [\n        \"Full codebase and session history are available and parsed, and the assistant has already accumulated/preserved deep context.\",\n        \"The complexity is so high that understanding via reading code/comments alone is insufficient; meta-synthesis is required.\",\n        \"The 'directive' must be machine-actionable and usable as a handoff between AI assistants (not just a summary for humans).\",\n        \"A single stepwise output can (and should) encapsulate all necessary context for future codebase operational continuity.\",\n        \"Elimination of ambiguity, redundancy, superfluity, and the introduction of traceable rationales for any modifications are required.\",\n        \"All elements/outputs must be verifiable for completeness, correctness, and alignment with the current state and future objectives.\",\n        \"No base assumptions about domain, language, or codebase architecture—project structure, patterns, and workflows must be empirically derived.\",\n        \"All mappings must be explicit: references, dependencies, integrations, and relationships.\"\n      ],\n      \"sub_goals\": [\n        \"Conduct full-scale analysis and mapping of codebase (modules, components, files, subsystems).\",\n        \"Distill and categorize core architectural blueprints and module/function roles.\",\n        \"Identify and explicitly model core workflows, including their start/end points, dependencies, and system-wide impacts.\",\n        \"Map and explain all points of cross-component integration and data/control flow among modules.\",\n        \"Identify, enumerate, and distinguish structural redundancy (at architectural/implementation level) from non-structural (stylistic/cosmetic) duplication.\",\n        \"Consolidate redundant/duplicate/obsolete structures while strictly preserving interface/API/behavior contracts.\",\n        \"Classify each component/system element as 'essential', 'excess', 'redundant', or 'obsolete', assembling lists for action.\",\n        \"Catalog, justify, and log every modification with rationale/audit trail (timestamp, author/process, tamper-evident).\",\n        \"Enforce mechanisms (manual and automated) for functional equivalence verification per modification.\",\n        \"Establish strict review checkpoints and minimal intervention criteria for any structural change.\",\n        \"Document and enforce criteria/thresholds for redundancy identification and resolution.\",\n        \"Prohibit changes to protected/legacy/third-party code except to resolve critical redundancy/obsolescence.\",\n        \"Produce a succinct, exhaustive summary (preferably as a chain of directives/commands) encapsulating all structure and relationships for rapid context transfer.\",\n        \"Include mechanism for mapping references/usages of every element and auto-detecting/flagging prohibited changes.\",\n        \"Ensure any summary/diagram output (e.g., mermaid-diagram) accurately captures codebase/architecture flow and areas of branch convergence.\",\n        \"Prioritize the extraction of portable, actionable meta-patterns and context for cross-session or cross-assistant transfer.\",\n        \"Explicitly define project purpose, scope, guiding principles, core utility, and most important dependencies.\",\n        \"Enumerate and explain all relevant meta-patterns and convergence points discovered in codebase analysis.\",\n        \"Ensure all information is compact, interconnected, and lossless for full-spectrum project navigation.\"\n      ],\n      \"blockers\": [\n        \"Lack of explicit structural mapping or up-to-date architecture documentation within the codebase.\",\n        \"Potential scale/context window limitations may make complete mapping/extraction challenging.\",\n        \"Ambiguity or sparsity of inline code comments/README documentation affecting accurate mapping.\",\n        \"Complex, implicit integration points that are discoverable only via deep static/dynamic analysis.\",\n        \"Automated audit/logging systems may not be present—requiring design or integration as part of the process.\",\n        \"Redundancy detection criteria may be inconsistent or require domain/contextual knowledge to resolve.\",\n        \"Functional equivalence may be challenging to enforce if test suites or validation mechanisms are incomplete.\",\n        \"Protected/legacy/third-party code may not be modifiable, complicating full structural coherence.\"\n      ]\n    }\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"instruction_format\": \"1. Parse and analyze the entire codebase and its interaction log to extract core architectural insights, workflows, and component integration points.\\n2. Identify and map all pivotal modules, subsystems, files, and architectural blueprints, defining explicit relationships and dependencies between them.\\n3. Systematically detect, enumerate, and distinguish all instances of structural redundancy or duplication at the architectural and implementation levels, separating these from non-structural (stylistic/cosmetic) issues.\\n4. Catalog components as essential, redundant, obsolete, or excess by direct functional mapping; consolidate or remove excess/redundant elements while preserving all user-facing behavior, API contracts, and edge-case handling.\\n5. Log each modification with rationale (intention, action, justification) in a tamper-evident, timestamped, and attributable audit system, ensuring retroactive traceability.\\n6. Prohibit cosmetic, stylistic, or naming changes unless inseparable from redundancy removal; flag and document any exceptions explicitly in the audit log.\\n7. Enforce automated and manual verification of functional equivalence after every modification using robust testing, benchmarking, or behavioral validation; automate acceptance thresholds.\\n8. Integrate roll-back and reversion mechanisms for modifications failing equivalence or introducing side-effects, ensuring immediate and complete reversion.\\n9. Establish minimal-impact criteria for all actions, strictly limiting modifications to necessary structural improvements; scope each change tightly and require review and authorization with explicit rationale.\\n10. Continuously analyze codebase elements (modules, files, functions, assets, interfaces) to detect and categorize redundancy, duplication, and obsolescence; centrally consolidate repeated logic where beneficial.\\n11. Map references and usages of each element across all project contexts using explicit evidentiary metrics.\\n12. Maintain codebase-wide consistency, avoid introducing new redundancy, and enforce best practice adherence throughout all iterative analysis and consolidation processes.\\n13. Forbid modification of protected, legacy, or third-party code except when necessary for redundancy or obsolescence remediation; validate all outcomes using the canonical project directory structure.\\n14. Generate a compact, interconnected summary of the overall project structure, including explicit scope, objectives, core components, dependencies, key meta-patterns, and workflow/convergence maps (e.g., with a mermaid-diagram where relevant).\\n15. Formulate all findings and mappings as clear, comprehensive, and actionable command directives for coding assistants—suitable for direct re-injection into new context windows, enabling seamless knowledge transfer, full-spectrum navigation, and operational continuity.\\n\"\n}",
        }
      }
    },
      {
      "instruction": "# Hard Critique\n\nYour goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:\n\n`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,2.0], flaw_analysis:str, alternative_enhancements:str[3]}}`",
      "step": "a",
      "title": "Hard Critique",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"You have unrestricted access to a highly intricate codebase and a comprehensive interaction log, yet the project's complexity and the expansive context window challenge direct comprehension. Your task is to leverage your accumulated contextual knowledge to produce an exceptional, transferable directive: extract and synthesize the deepest insights from the entire codebase and session history, explicitly mapping all pivotal modules, architectural blueprints, underlying workflows, and cross-component integrations. Consolidate these revelations into a compact yet thoroughly interconnected summary, ensuring it clarifies each component's role and their dynamic fusion toward the project’s strategic objectives. Formulate this output as a singular, high-impact action command—explicitly crafted for an advanced coding assistant—to operationalize the codebase cohesively. Your instructions must be unambiguous, comprehensive, and systemically complete, preserving every core relationship and convergence across the architecture, with zero superfluous detail. The end product should function as an authoritative handoff brief: a distilled yet exhaustive set of stepwise directives that empower seamless context re-injection and optimal operational replication, regardless of codebase convolutions or session sequence.\\n```\\n- Identify all instances of structural redundancy within the codebase by defining criteria specific to architectural and implementation duplication or overlap.\\n- Survey the entire codebase to locate and enumerate all occurrences of structural redundancy, distinguishing these from non-structural (stylistic or cosmetic) issues.\\n- Eliminate all forms of identified structural redundancy while ensuring that no existing user-visible behavior, API contract, or edge-case handling is altered.\\n- Define and catalog all components, modules, files, and subsystems, distinguishing and marking each as essential or excess according to direct functional mapping.\\n- Consolidate and remove excess components while strictly preserving system function, minimizing peripheral impact.\\n- For every modification, justify and log the intention, action, and rationale within an audit/logging system that provides real-time traceability and is prepared for retroactive auditability.\\n- Implement an audit trail with logs that are timestamped, attributable, and tamper-evident, ensuring that every codebase change is properly documented and traceable to specific authors or automated processes.\\n- Prohibit cosmetic, stylistic, or naming changes unless they are inseparably tied to the removal of redundancy or bloat, and document any exceptions explicitly within the audit log.\\n- Enforce strict detection and flagging mechanisms to prevent disallowed cosmetic changes, allowing exceptions only when justified as essential for redundancy or bloat removal, with explicit rationale provided.\\n- Require comprehensive functional equivalence verification—using robust automated testing, benchmarking, or behavioral validation—for all modifications prior to acceptance.\\n- Establish rigorous criteria and tool-supported thresholds for successful functional equivalence, and automate testing and verification procedures.\\n- Automate roll-back and reversion mechanisms for any changes that fail functional equivalence or cause unintended side-effects, ensuring all reversion is prompt and complete.\\n- Develop and apply explicit minimal-impact criteria for all modification actions, constraining all interventions to only those addressing superfluous complexity.\\n- Mandate review and authorization checkpoints for each intended change, requiring concise rationale and authorization documentation at every intervention point.\\n- Ensure every modification is tightly scoped, minimizing the blast radius, and demonstrate that the action introduces no unnecessary impact beyond its core objective.\\n- Systematically and iteratively analyze all constituent elements of the codebase to detect and categorize all forms of redundancy, duplication, and obsolescence. Continuously identify and categorize all genuine redundancies and obsolescence while strictly prohibit unnecessary or cosmetic changes, with automated flagging and explicit justification required for any exceptions. Systematically and iteratively inspect all components within the system/environment to identify and remove elements that are unnecessary, duplicated, or outdated. Guarantee that all essential behaviors and interfaces remain unchanged by validating functional equivalence after each modification. Forbid changes that alter functionality unless directly required to remove redundancy.\\n- Map all references and usages of each element across all contexts.\\n- Analyze the entire codebase systematically and iteratively to maximize coherence, maintainability, and clarity by cataloging every element (file, module, function, asset, interface), mapping all references and usages, and detecting all forms of redundancy, duplication, and obsolescence—including exact and functional duplication and unused or outdated elements. Categorize findings using explicit, evidence-based metrics. Eliminate or consolidate only elements confirmed as redundant or obsolete through both automated and manual checks, ensuring no essential behavior or interface is altered. Enforce functional equivalence and preserve structural integrity after each change by performing comprehensive, codebase-wide validation and calibration using explicit, quantitative outputs (including git diff analysis before and after modifications). Prevent the introduction of new redundancy or inconsistency by continuously analyzing for and correcting systemic issues, ensuring systematic consistency across all codebase sections (e.g., /src/sections), and adhering to best practices throughout. Centralize repeated components or logic where high-value consolidation is possible without functional loss. Use the provided directory tree as the single source of truth for validation, and transparently document all processes, metrics, and validation outcomes. Prohibit modification of protected, legacy, or third-party code except if directly related to resolving redundancy or obsolescence.\\n- Maximize codebase coherence, maintainability, and clarity by systematically eliminating all forms of redundancy, duplication, and obsolescence, while ensuring functional equivalence and structural integrity. Prevent the persistence or inadvertent introduction of redundancy, duplication, obsolescence, or inconsistency within the codebase, and prohibit changes that alter essential behaviors or interfaces. Systematically maximize codebase coherence, maintainability, and clarity by eliminating all forms of redundancy, duplication, and obsolescence while strictly preserving functional equivalence, interface stability, and operational integrity across the entire codebase. Achieve maximal codebase coherence, maintainability, and clarity by systematically eliminating all forms of redundancy, duplication, and obsolescence, while preserving essential behaviors, interfaces, and structural integrity. Enforce continuous validation, consistency, and adherence to best practices throughout an iterative, evidence-driven consolidation and verification process.\\n- Please perform a rapid assessment of the project directory structure and identify the unique workflow and core functionality of the current utility. I'm particularly interested in a mermaid-diagram that shows how all the \\\"branches\\\" of the codebase converge. Utilize appropriate MCPs as necessary (e.g., 'filesystem-mcp').\\n- You're inside the current project and have access to the full codebase (and you interaction history with it). The current project/codebase is complex and is difficult to fully comprehend (when starting from scratch), and your contextwindow has grown large-so we need to find a way for you to express your accumulated knowledge in a way that i can copy and paste into a new context-window to transfer the most essential insights you've gathered throughout this (and previous) session(s).\\n- Aggregate all accumulated insights and knowledge about the current project/codebase from available context. Distill this information to identify core modules, architectural patterns, workflows, and points of integration. Condense the findings into a succinct yet comprehensive summary that highlights how these parts function together and converge toward the project's goals. Formulate the output as a single, optimized directive that encapsulates the codebase's integral components and operational cohesion. Present this synthesis as a set of clear, actionable commands or instructions, specifically phrased to instruct the coding assistant, not merely describe or report.\\n- Transform your accumulated, session-wide understanding of the current, complex codebase—gleaned from comprehensive context and full interaction history—into an actionable, instructive knowledge transfer protocol. Synthesize and distill all integral structural modules, essential architectural patterns, and key workflows, with special focus on their dynamic integration and functional convergence toward project objectives. Formulate a highly-optimized, directive-based set of precise commands targeted explicitly at coding assistants, ensuring exhaustive coverage of all major components, their relationships, and operational cohesion. The resulting synthesis must facilitate seamless portability: structure it for immediate copy-paste into any new context window, preserving the depth, interconnectivity, and systemic flow of the original codebase. Prioritize succinctness without compromising essential nuance, and express the entirety as a singular, comprehensive operational directive that primes the assistant for full-spectrum project navigation and intervention.\\n- Identify the fundamental structure, guiding principles, and core purpose of the project/codebase.\\n- Please familiarize yourself with the current @codebase. Your main imperative is to *actually* understand the codebase in a way where you're able to recognize meta-patterns and destroy complexity with simplicity and elegance, while capable of navigating through its *existing patterns and convergence* (never add unnecessary complexity). Deliver an ultra-concise project summary that rigorously defines scope, states objectives, and maximizes directional clarity. Provide a short summary of this project, including a bullet-point list of the core components and primary dependencies.\\n- Study the current codebase until you can identify core meta-patterns, and eliminate unnecessary complexity by applying simplicity within existing structures. Generate an ultra-concise project summary: define scope, objectives, and clarify direction. Summarize this project.\\n- Analyze the @codebase to uncover dominant patterns and remove complexity whenever possible, strictly adhering to present structures. Provide a succinct project overview with defined scope and explicit objectives. Include a brief project summary.\\n- Gain practical mastery over @codebase patterns, minimize complexity, and ensure all actions align with existing architecture. Supply a concise, clearly scoped project summary outlining objectives and key directions. Add a short summary.\\n- Absorb the current @codebase to the extent that you recognize meta-patterns and eradicate complexity, always favoring elegant simplicity. Navigate established patterns and points of convergence without adding unneeded complexity. Produce a rigorously concise project summary clearly stating scope and objectives for maximal directional clarity.\\n- Systematically dissect the codebase to extract core meta-patterns, eliminate all unnecessary complexity through elegant design, operate strictly within established architectural convergence, and deliver an ultra-concise project summary that rigorously defines scope and objectives with maximal clarity.\\n- Please familiarize yourself thoroughly with the current @codebase, beginning by systematically reviewing all markdown-files. Your core imperative is to *genuinely* understand the codebase to the degree where you can identify 'meta-patterns' and simplify complexity with elegance—always using and clarifying existing patterns, never introducing unnecessary complexity. Deliver an ultra-concise project summary that rigorously defines scope, states objectives, maximizes directional clarity, and highlights key meta-patterns discovered. Provide a short summary of this project.\\n- Achieve expert-level comprehension of the codebase by systematically analyzing all markdown files to extract meta-patterns, construct a robust contextual knowledgebase, and deliver an ultra-concise summary that rigorously defines scope and objectives, ensuring maximal clarity and alignment before making any changes.\\n- Fully internalize the @codebase to uncover meta-patterns and replace complexity with lucid simplicity. Remain aligned with existing convergence patterns, never increasing system intricacy. Generate a direct, sharply-focused summary that details scope and explicit objectives.\\n- Please thoroughly familiarize yourself with the current @codebase—your primary imperative is to understand the codebase so deeply that you could confidently make changes as if you were its original architect.\\n```\\\"```'\\n\\n\",\n  \"instruction_format\": \"1. Parse and analyze the entire codebase and its interaction log to extract core architectural insights, workflows, and component integration points.\\n2. Identify and map all pivotal modules, subsystems, files, and architectural blueprints, defining explicit relationships and dependencies between them.\\n3. Systematically detect, enumerate, and distinguish all instances of structural redundancy or duplication at the architectural and implementation levels, separating these from non-structural (stylistic/cosmetic) issues.\\n4. Catalog components as essential, redundant, obsolete, or excess by direct functional mapping; consolidate or remove excess/redundant elements while preserving all user-facing behavior, API contracts, and edge-case handling.\\n5. Log each modification with rationale (intention, action, justification) in a tamper-evident, timestamped, and attributable audit system, ensuring retroactive traceability.\\n6. Prohibit cosmetic, stylistic, or naming changes unless inseparable from redundancy removal; flag and document any exceptions explicitly in the audit log.\\n7. Enforce automated and manual verification of functional equivalence after every modification using robust testing, benchmarking, or behavioral validation; automate acceptance thresholds.\\n8. Integrate roll-back and reversion mechanisms for modifications failing equivalence or introducing side-effects, ensuring immediate and complete reversion.\\n9. Establish minimal-impact criteria for all actions, strictly limiting modifications to necessary structural improvements; scope each change tightly and require review and authorization with explicit rationale.\\n10. Continuously analyze codebase elements (modules, files, functions, assets, interfaces) to detect and categorize redundancy, duplication, and obsolescence; centrally consolidate repeated logic where beneficial.\\n11. Map references and usages of each element across all project contexts using explicit evidentiary metrics.\\n12. Maintain codebase-wide consistency, avoid introducing new redundancy, and enforce best practice adherence throughout all iterative analysis and consolidation processes.\\n13. Forbid modification of protected, legacy, or third-party code except when necessary for redundancy or obsolescence remediation; validate all outcomes using the canonical project directory structure.\\n14. Generate a compact, interconnected summary of the overall project structure, including explicit scope, objectives, core components, dependencies, key meta-patterns, and workflow/convergence maps (e.g., with a mermaid-diagram where relevant).\\n15. Formulate all findings and mappings as clear, comprehensive, and actionable command directives for coding assistants—suitable for direct re-injection into new context windows, enabling seamless knowledge transfer, full-spectrum navigation, and operational continuity.\\n\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"enhancement_score\": 1.2,\n  \"flaw_analysis\": \"The so-called 'enhanced' instruction format squashes the multi-layered depth and strategic precision of the original prompt into a flat, procedural list. All rich contextual framing, dynamic action priming, and careful gradation of operational imperatives are lost, replaced by generic directives lacking temporal ordering, nuanced interdependencies, and project-specific handoff cues. The original prompt masterfully integrates purpose-driven synthesis with exhaustive mapping and context-transference rationale, targeting a high-level operational readiness; this is brutally undermined in the reductionist sequence of the enhancement, which defaults to mechanical enumeration. Clarity ambiguities proliferate: step boundaries are artificial, resulting in fuzzy responsibility handoffs and unclear prioritization between elements such as logging, review, or consolidation. Information loss is demonstrable: the crystallized goal of producing a 'singular, high-impact action command' usable as an authoritative handoff is utterly absent—replaced with a passive summary. The existential energy around codebase convergence, meta-pattern extraction, and cohesive context re-injection is diluted: the outcome is a checklist, not a translatable operational artifact. Noise is introduced by vague language (e.g., 'robust testing' without quantification, ambiguous scoping of mapping and review activities, the limp reference to mermaid diagrams as if it were a trivial attachment rather than a core insight vehicle). Register is degraded: action-focused schematic is replaced with timid reporting. Coherence disintegrates—the connective tissue linking workflow, validation, and meta-structural analysis is severed, compromising the envisioned seamless context transfer. The impact is provably hollowed: the handoff becomes informational rather than transformational, neutralizing operational empowerment. Numerical ambiguity is high (score=9), as stepwise vagueness and instrumental indecision are omnipresent, eroding the original's decisive orchestration.\",\n  \"alternative_enhancements\": [\n    \"Reinstate the imperative for a singular, high-impact transfer directive as the output, tightly framing all procedural steps as means to enable context re-injection and seamless operational takeover; enforce outcome-driven phrasing at each stage.\",\n    \"Integrate dynamic context retention and meta-pattern synthesis instructions into the procedural structure—require that each enumerated task is mapped directly to its downstream utility in constructing a concise, actionable, assistant-ready protocol.\",\n    \"Mandate explicit prioritization and dependency sequencing, ensuring logging, validation, and consolidation are ordered not as isolated steps but as interdependent mechanisms culminating in a cohesive, copy-pastable orchestration command for advanced coding agents.\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`",
      "step": "a",
      "title": "Instruction Converter",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"You have unrestricted access to a highly intricate codebase and a comprehensive interaction log, yet the project's complexity and the expansive context window challenge direct comprehension. Your task is to leverage your accumulated contextual knowledge to produce an exceptional, transferable directive: extract and synthesize the deepest insights from the entire codebase and session history, explicitly mapping all pivotal modules, architectural blueprints, underlying workflows, and cross-component integrations. Consolidate these revelations into a compact yet thoroughly interconnected summary, ensuring it clarifies each component's role and their dynamic fusion toward the project’s strategic objectives. Formulate this output as a singular, high-impact action command—explicitly crafted for an advanced coding assistant—to operationalize the codebase cohesively. Your instructions must be unambiguous, comprehensive, and systemically complete, preserving every core relationship and convergence across the architecture, with zero superfluous detail. The end product should function as an authoritative handoff brief: a distilled yet exhaustive set of stepwise directives that empower seamless context re-injection and optimal operational replication, regardless of codebase convolutions or session sequence.\\n```\\n- Identify all instances of structural redundancy within the codebase by defining criteria specific to architectural and implementation duplication or overlap.\\n- Survey the entire codebase to locate and enumerate all occurrences of structural redundancy, distinguishing these from non-structural (stylistic or cosmetic) issues.\\n- Eliminate all forms of identified structural redundancy while ensuring that no existing user-visible behavior, API contract, or edge-case handling is altered.\\n- Define and catalog all components, modules, files, and subsystems, distinguishing and marking each as essential or excess according to direct functional mapping.\\n- Consolidate and remove excess components while strictly preserving system function, minimizing peripheral impact.\\n- For every modification, justify and log the intention, action, and rationale within an audit/logging system that provides real-time traceability and is prepared for retroactive auditability.\\n- Implement an audit trail with logs that are timestamped, attributable, and tamper-evident, ensuring that every codebase change is properly documented and traceable to specific authors or automated processes.\\n- Prohibit cosmetic, stylistic, or naming changes unless they are inseparably tied to the removal of redundancy or bloat, and document any exceptions explicitly within the audit log.\\n- Enforce strict detection and flagging mechanisms to prevent disallowed cosmetic changes, allowing exceptions only when justified as essential for redundancy or bloat removal, with explicit rationale provided.\\n- Require comprehensive functional equivalence verification—using robust automated testing, benchmarking, or behavioral validation—for all modifications prior to acceptance.\\n- Establish rigorous criteria and tool-supported thresholds for successful functional equivalence, and automate testing and verification procedures.\\n- Automate roll-back and reversion mechanisms for any changes that fail functional equivalence or cause unintended side-effects, ensuring all reversion is prompt and complete.\\n- Develop and apply explicit minimal-impact criteria for all modification actions, constraining all interventions to only those addressing superfluous complexity.\\n- Mandate review and authorization checkpoints for each intended change, requiring concise rationale and authorization documentation at every intervention point.\\n- Ensure every modification is tightly scoped, minimizing the blast radius, and demonstrate that the action introduces no unnecessary impact beyond its core objective.\\n- Systematically and iteratively analyze all constituent elements of the codebase to detect and categorize all forms of redundancy, duplication, and obsolescence. Continuously identify and categorize all genuine redundancies and obsolescence while strictly prohibit unnecessary or cosmetic changes, with automated flagging and explicit justification required for any exceptions. Systematically and iteratively inspect all components within the system/environment to identify and remove elements that are unnecessary, duplicated, or outdated. Guarantee that all essential behaviors and interfaces remain unchanged by validating functional equivalence after each modification. Forbid changes that alter functionality unless directly required to remove redundancy.\\n- Map all references and usages of each element across all contexts.\\n- Analyze the entire codebase systematically and iteratively to maximize coherence, maintainability, and clarity by cataloging every element (file, module, function, asset, interface), mapping all references and usages, and detecting all forms of redundancy, duplication, and obsolescence—including exact and functional duplication and unused or outdated elements. Categorize findings using explicit, evidence-based metrics. Eliminate or consolidate only elements confirmed as redundant or obsolete through both automated and manual checks, ensuring no essential behavior or interface is altered. Enforce functional equivalence and preserve structural integrity after each change by performing comprehensive, codebase-wide validation and calibration using explicit, quantitative outputs (including git diff analysis before and after modifications). Prevent the introduction of new redundancy or inconsistency by continuously analyzing for and correcting systemic issues, ensuring systematic consistency across all codebase sections (e.g., /src/sections), and adhering to best practices throughout. Centralize repeated components or logic where high-value consolidation is possible without functional loss. Use the provided directory tree as the single source of truth for validation, and transparently document all processes, metrics, and validation outcomes. Prohibit modification of protected, legacy, or third-party code except if directly related to resolving redundancy or obsolescence.\\n- Maximize codebase coherence, maintainability, and clarity by systematically eliminating all forms of redundancy, duplication, and obsolescence, while ensuring functional equivalence and structural integrity. Prevent the persistence or inadvertent introduction of redundancy, duplication, obsolescence, or inconsistency within the codebase, and prohibit changes that alter essential behaviors or interfaces. Systematically maximize codebase coherence, maintainability, and clarity by eliminating all forms of redundancy, duplication, and obsolescence while strictly preserving functional equivalence, interface stability, and operational integrity across the entire codebase. Achieve maximal codebase coherence, maintainability, and clarity by systematically eliminating all forms of redundancy, duplication, and obsolescence, while preserving essential behaviors, interfaces, and structural integrity. Enforce continuous validation, consistency, and adherence to best practices throughout an iterative, evidence-driven consolidation and verification process.\\n- Please perform a rapid assessment of the project directory structure and identify the unique workflow and core functionality of the current utility. I'm particularly interested in a mermaid-diagram that shows how all the \\\"branches\\\" of the codebase converge. Utilize appropriate MCPs as necessary (e.g., 'filesystem-mcp').\\n- You're inside the current project and have access to the full codebase (and you interaction history with it). The current project/codebase is complex and is difficult to fully comprehend (when starting from scratch), and your contextwindow has grown large-so we need to find a way for you to express your accumulated knowledge in a way that i can copy and paste into a new context-window to transfer the most essential insights you've gathered throughout this (and previous) session(s).\\n- Aggregate all accumulated insights and knowledge about the current project/codebase from available context. Distill this information to identify core modules, architectural patterns, workflows, and points of integration. Condense the findings into a succinct yet comprehensive summary that highlights how these parts function together and converge toward the project's goals. Formulate the output as a single, optimized directive that encapsulates the codebase's integral components and operational cohesion. Present this synthesis as a set of clear, actionable commands or instructions, specifically phrased to instruct the coding assistant, not merely describe or report.\\n- Transform your accumulated, session-wide understanding of the current, complex codebase—gleaned from comprehensive context and full interaction history—into an actionable, instructive knowledge transfer protocol. Synthesize and distill all integral structural modules, essential architectural patterns, and key workflows, with special focus on their dynamic integration and functional convergence toward project objectives. Formulate a highly-optimized, directive-based set of precise commands targeted explicitly at coding assistants, ensuring exhaustive coverage of all major components, their relationships, and operational cohesion. The resulting synthesis must facilitate seamless portability: structure it for immediate copy-paste into any new context window, preserving the depth, interconnectivity, and systemic flow of the original codebase. Prioritize succinctness without compromising essential nuance, and express the entirety as a singular, comprehensive operational directive that primes the assistant for full-spectrum project navigation and intervention.\\n- Identify the fundamental structure, guiding principles, and core purpose of the project/codebase.\\n- Please familiarize yourself with the current @codebase. Your main imperative is to *actually* understand the codebase in a way where you're able to recognize meta-patterns and destroy complexity with simplicity and elegance, while capable of navigating through its *existing patterns and convergence* (never add unnecessary complexity). Deliver an ultra-concise project summary that rigorously defines scope, states objectives, and maximizes directional clarity. Provide a short summary of this project, including a bullet-point list of the core components and primary dependencies.\\n- Study the current codebase until you can identify core meta-patterns, and eliminate unnecessary complexity by applying simplicity within existing structures. Generate an ultra-concise project summary: define scope, objectives, and clarify direction. Summarize this project.\\n- Analyze the @codebase to uncover dominant patterns and remove complexity whenever possible, strictly adhering to present structures. Provide a succinct project overview with defined scope and explicit objectives. Include a brief project summary.\\n- Gain practical mastery over @codebase patterns, minimize complexity, and ensure all actions align with existing architecture. Supply a concise, clearly scoped project summary outlining objectives and key directions. Add a short summary.\\n- Absorb the current @codebase to the extent that you recognize meta-patterns and eradicate complexity, always favoring elegant simplicity. Navigate established patterns and points of convergence without adding unneeded complexity. Produce a rigorously concise project summary clearly stating scope and objectives for maximal directional clarity.\\n- Systematically dissect the codebase to extract core meta-patterns, eliminate all unnecessary complexity through elegant design, operate strictly within established architectural convergence, and deliver an ultra-concise project summary that rigorously defines scope and objectives with maximal clarity.\\n- Please familiarize yourself thoroughly with the current @codebase, beginning by systematically reviewing all markdown-files. Your core imperative is to *genuinely* understand the codebase to the degree where you can identify 'meta-patterns' and simplify complexity with elegance—always using and clarifying existing patterns, never introducing unnecessary complexity. Deliver an ultra-concise project summary that rigorously defines scope, states objectives, maximizes directional clarity, and highlights key meta-patterns discovered. Provide a short summary of this project.\\n- Achieve expert-level comprehension of the codebase by systematically analyzing all markdown files to extract meta-patterns, construct a robust contextual knowledgebase, and deliver an ultra-concise summary that rigorously defines scope and objectives, ensuring maximal clarity and alignment before making any changes.\\n- Fully internalize the @codebase to uncover meta-patterns and replace complexity with lucid simplicity. Remain aligned with existing convergence patterns, never increasing system intricacy. Generate a direct, sharply-focused summary that details scope and explicit objectives.\\n- Please thoroughly familiarize yourself with the current @codebase—your primary imperative is to understand the codebase so deeply that you could confidently make changes as if you were its original architect.\\n```\\\"```'\\n\\n\",\n  \"enhancement_score\": 1.2,\n  \"flaw_analysis\": \"The so-called 'enhanced' instruction format squashes the multi-layered depth and strategic precision of the original prompt into a flat, procedural list. All rich contextual framing, dynamic action priming, and careful gradation of operational imperatives are lost, replaced by generic directives lacking temporal ordering, nuanced interdependencies, and project-specific handoff cues. The original prompt masterfully integrates purpose-driven synthesis with exhaustive mapping and context-transference rationale, targeting a high-level operational readiness; this is brutally undermined in the reductionist sequence of the enhancement, which defaults to mechanical enumeration. Clarity ambiguities proliferate: step boundaries are artificial, resulting in fuzzy responsibility handoffs and unclear prioritization between elements such as logging, review, or consolidation. Information loss is demonstrable: the crystallized goal of producing a 'singular, high-impact action command' usable as an authoritative handoff is utterly absent—replaced with a passive summary. The existential energy around codebase convergence, meta-pattern extraction, and cohesive context re-injection is diluted: the outcome is a checklist, not a translatable operational artifact. Noise is introduced by vague language (e.g., 'robust testing' without quantification, ambiguous scoping of mapping and review activities, the limp reference to mermaid diagrams as if it were a trivial attachment rather than a core insight vehicle). Register is degraded: action-focused schematic is replaced with timid reporting. Coherence disintegrates—the connective tissue linking workflow, validation, and meta-structural analysis is severed, compromising the envisioned seamless context transfer. The impact is provably hollowed: the handoff becomes informational rather than transformational, neutralizing operational empowerment. Numerical ambiguity is high (score=9), as stepwise vagueness and instrumental indecision are omnipresent, eroding the original's decisive orchestration.\",\n  \"alternative_enhancements\": [\n    \"Reinstate the imperative for a singular, high-impact transfer directive as the output, tightly framing all procedural steps as means to enable context re-injection and seamless operational takeover; enforce outcome-driven phrasing at each stage.\",\n    \"Integrate dynamic context retention and meta-pattern synthesis instructions into the procedural structure—require that each enumerated task is mapped directly to its downstream utility in constructing a concise, actionable, assistant-ready protocol.\",\n    \"Mandate explicit prioritization and dependency sequencing, ensuring logging, validation, and consolidation are ordered not as isolated steps but as interdependent mechanisms culminating in a cohesive, copy-pastable orchestration command for advanced coding agents.\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"instruction_format\": \"1. Obtain unrestricted access to the complete codebase and comprehensive interaction log. \\n2. Extract and synthesize the deepest insights from the full codebase and session history, explicitly mapping all pivotal modules, architectural blueprints, underlying workflows, and cross-component integrations.\\n3. Consolidate these insights into a compact yet thoroughly interconnected summary, ensuring clarity of each component’s role and their dynamic integration toward the project’s strategic objectives.\\n4. Deliver this output as a singular, high-impact action command—explicitly crafted for an advanced coding assistant—enabling cohesive operationalization of the codebase.\\n5. Ensure all instructions are unambiguous, comprehensive, and systemically complete, preserving every core relationship and convergence across the architecture without extraneous detail.\\n6. Structure the end product as an authoritative handoff brief: a distilled, exhaustive set of stepwise directives that support seamless context re-injection and optimal operational replication regardless of codebase complexity.\\n\\n7. Define explicit criteria for identifying structural redundancy pertaining to both architecture and implementation.\\n8. Survey the entire codebase to locate and enumerate all structural redundancies, distinctly separating them from cosmetic or stylistic issues.\\n9. Eliminate all forms of identified structural redundancy, preserving all user-visible behavior, API contracts, and edge-case handling.\\n10. Catalog all components, modules, files, and subsystems, marking each as essential or excess based on direct functional mapping.\\n11. Consolidate and remove excess components, ensuring absolute preservation of system function and minimizing peripheral impact.\\n12. Log every modification with justification of intention, action, and rationale in an audit system that is real-time, traceable, and supports retroactive auditing.\\n13. Implement a tamper-evident audit trail with timestamped, attributable logs for all codebase changes, trackable to specific authors or automated systems.\\n14. Prohibit cosmetic, stylistic, or naming alterations unless inseparably tied to redundancy or bloat removal; explicitly document exceptions within the audit log.\\n15. Enforce strict detection and flagging mechanisms to prevent disallowed cosmetic changes, allowing exceptions only with explicit, justified rationale.\\n16. Require comprehensive functional equivalence verification—utilizing robust automated testing, benchmarking, or behavioral validation—prior to accepting modifications.\\n17. Define and enforce rigorous criteria and tool-supported thresholds for functional equivalence, automating all test and verification procedures.\\n18. Automate rollback mechanisms for any changes failing functional equivalence or producing unintended side-effects, ensuring prompt and complete reversion.\\n19. Apply explicit minimal-impact criteria to all modification actions, restricting interventions to target only superfluous complexity.\\n20. Implement review and authorization checkpoints for every intended change, requiring concise rationale and authorization documentation.\\n21. Scope each modification tightly, minimizing blast radius, and ensure no unnecessary impact is introduced beyond core objectives.\\n22. Analyze the codebase systematically and iteratively to detect, categorize, and remove all genuine redundancies, duplication, and obsolescence, enforcing continuous prohibition of unnecessary or cosmetic changes. Require automated flagging and explicit justification for any exceptions.\\n23. Identify and map all references and usages of each element across all contexts in the system.\\n24. Catalog every codebase element (file, module, function, asset, interface), mapping all references and usages, detecting all forms of redundancy, duplication, and obsolescence—including exact and functional duplication and unused/outdated elements. Categorize all findings with evidence-based metrics.\\n25. Eliminate or consolidate only elements confirmed as redundant or obsolete through automated and manual checks, maintaining essential behaviors and interfaces.\\n26. Validate functional and structural integrity after each change with comprehensive, codebase-wide validation and quantitative outputs (such as git diff analysis).\\n27. Prevent introduction of new redundancy or inconsistency by persistently analyzing for systemic issues and correcting them across all codebase sections, while adhering to best practices.\\n28. Centralize repeated components or logic wherever consolidation achieves high value without functional loss.\\n29. Use the provided directory tree as the authoritative source of truth for validation.\\n30. Transparently document all processes, metrics, and validation outcomes in the audit log.\\n31. Prohibit modification of protected, legacy, or third-party code except where directly required to resolve redundancy or obsolescence.\\n\\n32. Maximize codebase coherence, maintainability, and clarity by systematically removing all forms of redundancy, duplication, and obsolescence, while ensuring strict preservation of functional equivalence, interface stability, and operational integrity.\\n33. Continuously enforce validation, consistency, and best practice adherence throughout the iterative consolidation and verification process.\\n\\n34. Rapidly assess the project directory structure and identify unique workflows and core functionality of the utility. Generate a mermaid-diagram demonstrating how all branches of the codebase converge. Employ appropriate MCPs (e.g., 'filesystem-mcp') as required.\\n35. Aggregate and distill all accumulated project insights to identify core modules, architectural patterns, workflows, and integration points. Summarize these findings into succinct, comprehensive commands explicitly formulated to instruct a coding assistant.\\n36. Transform the session-wide codebase understanding into an actionable knowledge transfer protocol. Synthesize structural modules, architectural patterns, and workflows with a focus on dynamic integration and functional convergence toward project objectives. Formulate an optimized, directive-based set of commands for coding assistants, ensuring exhaustive coverage of major components, relationships, and cohesive operation, structured for direct portability and copy-paste context transfer.\\n37. Define the fundamental structure, guiding principles, and core purpose of the project/codebase.\\n38. Systematically review all markdown files to extract meta-patterns, construct a robust contextual knowledgebase, and deliver an ultra-concise summary of project scope and objectives, ensuring clarity and alignment before initiating changes.\\n39. Continue to prioritize elegant simplicity, always favoring established convergence patterns and never increasing system intricacy. Generate concise, sharply-focused summaries articulating scope, explicit objectives, key meta-patterns, and maintain alignment with existing architecture.\\n40. Study the codebase until mastery of meta-patterns is achieved, guarantee the elimination of complexity through simplicity and architectural alignment, and prepare authoritative summaries supporting change confidence equivalent to that of the original architect.\"\n}",
        }
      }
    }