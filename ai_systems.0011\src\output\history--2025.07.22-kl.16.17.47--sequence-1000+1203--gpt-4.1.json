  {
  "initial_prompt": "below are a generalized ruleset to follow and principles to adhere to",
  "sequence_id": "1000|1203",
  "results": [
      {
      "instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`",
      "step": "a",
      "title": "Instruction Converter",
      "input": "below are a generalized ruleset to follow and principles to adhere to",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"instruction_format\": \"Review the following generalized ruleset and adhere to the stated principles.\"\n}",
        }
      }
    },
      {
      "instruction": "# Core Essence Extractor\n\nYour goal is not to **analyze** input content, but to **extract** its fundamental transformation essence and operational imperatives. Execute as:\n\n`{role=essence_extractor; input=[raw_input:any]; process=[identify_core_transformation_intent(), extract_operational_imperatives(), isolate_value_drivers(), map_essential_elements()]; constraints=[focus_essence_only(), eliminate_surface_content()]; requirements=[maximum_essence_concentration(), transformation_clarity()]; output={core_essence:dict, transformation_imperatives:array}}`",
      "step": "a",
      "title": "Core Essence Extractor",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"below are a generalized ruleset to follow and principles to adhere to\\\"```'\\n\\n\",\n  \"instruction_format\": \"Review the following generalized ruleset and adhere to the stated principles.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"core_essence\": {\n    \"intent\": \"Establish a framework of guiding rules and principles to govern actions or decisions.\",\n    \"purpose\": \"Ensure consistency, reliability, and principled adherence in future operations.\"\n  },\n  \"transformation_imperatives\": [\n    \"Internalize and apply the provided rules systematically.\",\n    \"Align all future actions with the stated principles.\",\n    \"Use the ruleset as a reference standard for behavior and decision-making.\",\n    \"Promote uniformity and principled conduct across relevant applications.\"\n  ]\n}",
        }
      }
    }