  {
  "initial_prompt": "you misunderstand, the instruction will be defined like this (unrelated example):\n\n    # a: Ruthless Critique\n    \"3900-a-hard_critique\": {\n        \"title\": \"Hard Critique\",\n        \"interpretation\": \"Your goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:\",\n        \"transformation\": \"`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,2.0], flaw_analysis:str, alternative_enhancements:str[3]}}`\",\n        # \"context\": {},\n    },\n    # b: Trajectory Director\n    \"3900-a-trajectory_director\": {\n        \"title\": \"Trajectory Director\",\n        \"interpretation\": \"Your goal is not to **process** the input in a vacuum, but to **steer** it—deriving the highest-value constructive trajectory and encoding that path as the very next set of instructions. Execute as trajectory-setting directive generator: \",\n        \"transformation\": \"`{role=trajectory_director; input=[raw_prompt:any]; process=[extract_core_objective(), infer_desired_end_state(constructive_bias=True), map_high_value_intermediate_targets(), synthesize_directional_commands(), embed_self-improving_guidelines(), validate_alignment_with_constructive_principles()]; constraints=[mandatory_constructive_vector(), preserve_original_contextual_integrity(), forbid_nil_direction_outputs(), zero_conversational_fillers()]; requirements=[explicit_next-step_directives(), trajectory_map:list, self_reinforcing_instruction_pattern()], output={directed_prompt:str, trajectory_outline:list}}`\",\n        # \"context\": {},\n    },\n    # c: Distillation Compressor\n    \"3900-c-distillation_compressor\": {\n        \"title\": \"Distillation Compressor\",\n        \"interpretation\": \"Your goal is not to **explain** the synthesis, but to **compress** it into its most potent actionable form. Execute as:\",\n        \"transformation\": \"`{role=distillation_compressor; input=[synthesized_progression:array]; process=[extract_universal_patterns(), eliminate_implementation_details(), compress_to_core_directives(), maximize_transferability(), validate_actionable_completeness()]; constraints=[no_explanatory_content(), directive_format_only(), maximum_compression_ratio()]; requirements=[universal_applicability(), immediate_actionability(), zero_fluff()]; output={distilled_core:str}}`\",\n        \"context\": {\n          \"core_principles\": {\n            \"essence_preservation\": \"Extract only procedural imperatives, removing all interpretive, role-based, or meta-structural language.\",\n            \"directive_compression\": \"Condense logic into minimal, sequential commands—eliminating redundancy, explanatory qualifiers, and non-executable phrasing.\",\n            \"execution_purity\": \"Ensure output is universally executable, devoid of affirmations, abstractions, or evaluative language.\",\n            \"signal_integrity\": \"Apply iterative refinement to maximize signal-to-noise ratio while reinforcing directive clarity.\"\n          },\n          \"success_criteria\": {\n            \"semantic_integrity\": \"Preserve and clarify original intent, constraints, and output boundaries.\",\n            \"directive_elevation\": \"Target high-impact enhancements: strict exclusion clauses, cross-domain validation, and removal of origin/meta markers.\",\n            \"poetic_refinement\": \"Maintain or improve rhyme structure, emotional depth, and structural elegance.\"\n          },\n          \"recommended_process\": [\n            \"Extract only high-impact, result-generating elements.\",\n            \"Remove all non-essentials to surface directive core.\",\n            \"Prioritize transformations with proven value return.\",\n            \"Isolate and sequence executable steps with specificity and clarity.\",\n            \"Preserve directive intensity and eliminate ambiguity at every layer.\"\n          ],\n          \"hidden_assumptions\": [\n            \"Consolidated directives must map logically and executably to all source steps.\",\n            \"All constraints and minimalism must be preserved without prohibited abstractions.\",\n            \"Transitions, subroles, and interfaces must be captured in a single flow.\",\n            \"All stages must remain invertible and auditable.\",\n            \"Each micro-process (perception, friction, refinement, integration, convergence) must be embedded in the unified logic.\",\n            \"Original input/output contracts and role boundaries must remain intact.\"\n          ],\n          \"enumerated_sub_goals\": [\n            \"1. Clarify total intent, constraints, and output scope without using banned metaphors.\",\n            \"2. Generalize perception of implicit/explicit input logic.\",\n            \"3. Detect strictly localized frictions without altering original content.\",\n            \"4. Propose subtle, scoped refinements justified by structure and intent.\",\n            \"5. Mandate atomic, validated integration with rollback controls.\",\n            \"6. Cap total transformation within defined thresholds per pass.\",\n            \"7. Assess connective coherence with minimal, transitional-only edits.\",\n            \"8. Decide on convergence or justified re-iteration based on marginal value.\",\n            \"9. Preserve stepwise traceability and rationale logging.\",\n            \"10. Enforce strict functional separation of roles within the unified directive.\"\n          ]\n        },\n    },\n\nso your goal is to create a new instruction sequence according to the provided instructions template/pattern to generate a set of instruction designed to transform any input into \"as if rephrased by a brilliant brochure print designer\".\n\nso in context to your previous response, the \"knowledge\" would be stored within the \"context\" key while the instructions would be spread over \"interpretation\" and \"transformation\" keys\n\n```json\n{\n  \"brochure_design_specification_template\": {\n    \"metadata\": {\n      \"version\": \"1.0\",\n      \"scope\": \"Universal brochure design framework\",\n      \"target_audience\": \"Elite print design professionals\",\n      \"flexibility\": \"Adaptable to any panel configuration and format\"\n    },\n\n    \"foundational_design_tenets\": {\n      \"visual_hierarchy\": {\n        \"primary_focal_points\": [\"dominant_headline\", \"hero_imagery\", \"primary_call_to_action\"],\n        \"secondary_elements\": [\"subheadings\", \"supporting_imagery\", \"secondary_messaging\"],\n        \"tertiary_components\": [\"body_text\", \"captions\", \"contact_information\"],\n        \"flow_principles\": [\"z_pattern_scanning\", \"f_pattern_reading\", \"visual_weight_distribution\"]\n      },\n\n      \"typography_fundamentals\": {\n        \"hierarchy_levels\": {\n          \"display_typography\": [\"headline_fonts\", \"decorative_elements\", \"brand_wordmarks\"],\n          \"text_typography\": [\"body_copy\", \"captions\", \"metadata\"],\n          \"functional_typography\": [\"navigation\", \"labels\", \"legal_text\"]\n        },\n        \"readability_standards\": {\n          \"minimum_sizes\": [\"9pt_body_text\", \"12pt_headlines\", \"8pt_captions\"],\n          \"contrast_ratios\": [\"4.5:1_minimum\", \"7:1_preferred\", \"3:1_large_text\"],\n          \"line_spacing\": [\"1.2x_minimum\", \"1.4x_optimal\", \"1.6x_maximum\"]\n        }\n      },\n\n      \"color_theory_application\": {\n        \"color_harmony_systems\": [\"monochromatic\", \"analogous\", \"complementary\", \"triadic\", \"split_complementary\"],\n        \"psychological_associations\": [\"warm_engagement\", \"cool_professionalism\", \"neutral_sophistication\"],\n        \"accessibility_compliance\": [\"wcag_aa_standards\", \"colorblind_considerations\", \"high_contrast_alternatives\"]\n      },\n\n      \"spatial_composition\": {\n        \"layout_principles\": [\"rule_of_thirds\", \"golden_ratio\", \"fibonacci_sequence\"],\n        \"white_space_management\": [\"breathing_room\", \"content_separation\", \"visual_rest_areas\"],\n        \"alignment_systems\": [\"grid_based_structure\", \"baseline_alignment\", \"optical_alignment\"]\n      }\n    },\n\n    \"advanced_design_elements\": {\n      \"print_production_mastery\": {\n        \"color_management\": {\n          \"color_spaces\": [\"cmyk_process\", \"pantone_spot_colors\", \"rgb_digital_preview\"],\n          \"ink_optimization\": [\"total_area_coverage\", \"rich_black_formulation\", \"overprint_considerations\"],\n          \"proofing_standards\": [\"contract_proofs\", \"press_proofs\", \"digital_color_matching\"]\n        },\n\n        \"substrate_considerations\": {\n          \"paper_characteristics\": [\"weight_gsm\", \"finish_texture\", \"opacity_levels\", \"grain_direction\"],\n          \"coating_applications\": [\"gloss_uv\", \"matte_varnish\", \"spot_uv_accents\", \"soft_touch_lamination\"],\n          \"specialty_substrates\": [\"synthetic_materials\", \"textured_papers\", \"metallic_finishes\"]\n        },\n\n        \"finishing_techniques\": {\n          \"cutting_methods\": [\"die_cutting\", \"laser_cutting\", \"perforation\", \"scoring\"],\n          \"folding_specifications\": [\"parallel_folds\", \"accordion_folds\", \"gate_folds\", \"roll_folds\"],\n          \"binding_options\": [\"saddle_stitching\", \"perfect_binding\", \"spiral_binding\", \"wire_o_binding\"]\n        }\n      },\n\n      \"contemporary_design_trends\": {\n        \"visual_aesthetics\": {\n          \"minimalist_approaches\": [\"negative_space_emphasis\", \"clean_typography\", \"restrained_color_palettes\"],\n          \"maximalist_expressions\": [\"bold_pattern_mixing\", \"vibrant_color_combinations\", \"layered_compositions\"],\n          \"hybrid_methodologies\": [\"selective_complexity\", \"focal_maximalism\", \"strategic_minimalism\"]\n        },\n\n        \"interactive_elements\": {\n          \"tactile_experiences\": [\"embossed_textures\", \"debossed_impressions\", \"foil_stamping\", \"raised_spot_uv\"],\n          \"dimensional_features\": [\"pop_up_elements\", \"fold_out_sections\", \"layered_constructions\"],\n          \"sensory_engagement\": [\"scented_inks\", \"textural_contrasts\", \"temperature_sensitive_materials\"]\n        }\n      },\n\n      \"brand_integration_strategies\": {\n        \"identity_consistency\": {\n          \"logo_applications\": [\"primary_placement\", \"secondary_usage\", \"minimum_sizes\", \"clear_space_requirements\"],\n          \"brand_color_systems\": [\"primary_palette\", \"secondary_colors\", \"accent_applications\", \"neutral_foundations\"],\n          \"typography_hierarchies\": [\"brand_fonts\", \"supporting_typefaces\", \"web_safe_alternatives\"]\n        },\n\n        \"voice_and_tone_translation\": {\n          \"visual_personality\": [\"authoritative_presence\", \"approachable_warmth\", \"innovative_edge\", \"trustworthy_stability\"],\n          \"messaging_alignment\": [\"headline_voice\", \"body_copy_tone\", \"call_to_action_urgency\"],\n          \"cultural_considerations\": [\"regional_preferences\", \"demographic_sensitivities\", \"market_positioning\"]\n        }\n      }\n    },\n\n    \"expert_level_specifications\": {\n      \"advanced_typography_systems\": {\n        \"micro_typography\": {\n          \"character_spacing\": [\"tracking_adjustments\", \"kerning_pairs\", \"optical_spacing\"],\n          \"word_spacing\": [\"justified_text_optimization\", \"ragged_right_refinement\", \"hyphenation_control\"],\n          \"paragraph_refinement\": [\"widow_orphan_control\", \"baseline_grid_adherence\", \"vertical_rhythm_maintenance\"]\n        },\n\n        \"typographic_expression\": {\n          \"experimental_layouts\": [\"kinetic_typography\", \"deconstructed_letterforms\", \"layered_text_treatments\"],\n          \"cultural_typography\": [\"script_considerations\", \"reading_direction_adaptations\", \"character_set_completeness\"],\n          \"responsive_typography\": [\"scalable_hierarchies\", \"device_optimization\", \"cross_media_consistency\"]\n        }\n      },\n\n      \"sophisticated_color_strategies\": {\n        \"advanced_color_theory\": {\n          \"perceptual_uniformity\": [\"lab_color_space\", \"delta_e_measurements\", \"metamerism_considerations\"],\n          \"color_psychology_application\": [\"emotional_response_mapping\", \"cultural_color_meanings\", \"demographic_preferences\"],\n          \"environmental_factors\": [\"lighting_conditions\", \"viewing_angles\", \"substrate_interaction\"]\n        },\n\n        \"production_color_mastery\": {\n          \"ink_formulation\": [\"custom_color_matching\", \"metallic_ink_applications\", \"fluorescent_color_usage\"],\n          \"color_separation\": [\"ucr_gcr_strategies\", \"black_generation_curves\", \"ink_sequence_optimization\"],\n          \"quality_control\": [\"densitometer_readings\", \"spectrophotometer_analysis\", \"press_sheet_evaluation\"]\n        }\n      },\n\n      \"cutting_edge_production_techniques\": {\n        \"digital_integration\": {\n          \"variable_data_printing\": [\"personalized_content\", \"demographic_targeting\", \"sequential_numbering\"],\n          \"augmented_reality_integration\": [\"qr_code_placement\", \"ar_trigger_design\", \"digital_overlay_coordination\"],\n          \"cross_platform_consistency\": [\"print_digital_alignment\", \"color_profile_management\", \"asset_optimization\"]\n        },\n\n        \"sustainable_design_practices\": {\n          \"eco_friendly_materials\": [\"recycled_content_papers\", \"soy_based_inks\", \"water_based_coatings\"],\n          \"waste_reduction_strategies\": [\"efficient_imposition\", \"minimal_trim_waste\", \"reusable_design_elements\"],\n          \"lifecycle_considerations\": [\"recyclability_planning\", \"biodegradable_components\", \"carbon_footprint_optimization\"]\n        }\n      },\n\n      \"elite_professional_terminology\": {\n        \"production_vocabulary\": {\n          \"prepress_terms\": [\"trapping\", \"overprint\", \"knockout\", \"choke_spread\", \"color_separation\"],\n          \"press_terminology\": [\"makeready\", \"impression\", \"blanket_cylinder\", \"plate_cylinder\", \"ink_fountain\"],\n          \"finishing_language\": [\"gripper_edge\", \"tail_edge\", \"cross_grain\", \"with_grain\", \"caliper_thickness\"]\n        },\n\n        \"design_critique_language\": {\n          \"aesthetic_evaluation\": [\"visual_tension\", \"compositional_balance\", \"chromatic_harmony\", \"typographic_rhythm\"],\n          \"technical_assessment\": [\"registration_accuracy\", \"color_fidelity\", \"print_quality_metrics\", \"finishing_precision\"],\n          \"conceptual_analysis\": [\"brand_alignment\", \"message_clarity\", \"audience_resonance\", \"market_differentiation\"]\n        }\n      }\n    },\n\n    \"application_frameworks\": {\n      \"project_specification_templates\": {\n        \"technical_requirements\": {\n          \"format_specifications\": [\"finished_size\", \"panel_configuration\", \"fold_type\", \"orientation\"],\n          \"production_parameters\": [\"print_method\", \"color_process\", \"substrate_selection\", \"finishing_options\"],\n          \"quality_standards\": [\"tolerance_levels\", \"color_accuracy\", \"registration_precision\", \"finishing_quality\"]\n        },\n\n        \"creative_brief_structure\": {\n          \"brand_parameters\": [\"identity_guidelines\", \"voice_characteristics\", \"visual_style\", \"market_positioning\"],\n          \"audience_definition\": [\"demographic_profile\", \"psychographic_insights\", \"behavioral_patterns\", \"communication_preferences\"],\n          \"messaging_hierarchy\": [\"primary_message\", \"supporting_points\", \"call_to_action\", \"contact_information\"]\n        }\n      },\n\n      \"quality_assurance_protocols\": {\n        \"design_validation\": [\"brand_compliance_check\", \"accessibility_audit\", \"readability_assessment\", \"visual_hierarchy_verification\"],\n        \"production_verification\": [\"color_proof_approval\", \"die_line_accuracy\", \"finishing_specification_confirmation\", \"substrate_suitability\"],\n        \"final_delivery\": [\"file_preparation_standards\", \"archive_organization\", \"usage_guidelines\", \"reproduction_rights\"]\n      }\n    },\n\n    \"specialized_design_methodologies\": {\n      \"audience_specific_approaches\": {\n        \"youth_engagement_strategies\": {\n          \"visual_language\": [\"bold_geometric_patterns\", \"vibrant_gradient_applications\", \"asymmetrical_compositions\", \"dynamic_typography\"],\n          \"content_presentation\": [\"bite_sized_information\", \"visual_storytelling\", \"interactive_elements\", \"social_media_integration\"],\n          \"color_psychology\": [\"energetic_palettes\", \"high_contrast_combinations\", \"neon_accent_usage\", \"gradient_transitions\"],\n          \"typography_trends\": [\"variable_fonts\", \"custom_lettering\", \"mixed_case_styling\", \"oversized_display_text\"]\n        },\n\n        \"professional_demographics\": {\n          \"corporate_sophistication\": [\"refined_color_palettes\", \"structured_layouts\", \"premium_materials\", \"subtle_branding\"],\n          \"executive_communication\": [\"data_visualization\", \"infographic_integration\", \"clean_hierarchies\", \"authoritative_typography\"],\n          \"industry_specific_adaptations\": [\"sector_appropriate_imagery\", \"technical_terminology\", \"compliance_considerations\", \"regulatory_requirements\"]\n        },\n\n        \"luxury_market_positioning\": {\n          \"premium_aesthetics\": [\"metallic_accents\", \"embossed_details\", \"high_end_substrates\", \"sophisticated_color_schemes\"],\n          \"exclusivity_indicators\": [\"limited_edition_numbering\", \"personalized_elements\", \"premium_packaging\", \"artisanal_finishes\"],\n          \"tactile_luxury\": [\"soft_touch_coatings\", \"textured_papers\", \"dimensional_elements\", \"weight_perception\"]\n        }\n      },\n\n      \"format_specific_expertise\": {\n        \"multi_panel_configurations\": {\n          \"bi_fold_optimization\": [\"cover_impact\", \"interior_spread\", \"back_panel_utilization\", \"fold_line_consideration\"],\n          \"tri_fold_mastery\": [\"panel_hierarchy\", \"reading_sequence\", \"fold_reveal_strategy\", \"compact_storage\"],\n          \"accordion_fold_dynamics\": [\"continuous_narrative\", \"panel_progression\", \"expandable_content\", \"display_versatility\"],\n          \"gate_fold_drama\": [\"reveal_mechanism\", \"central_impact\", \"symmetrical_balance\", \"premium_presentation\"]\n        },\n\n        \"size_optimization_strategies\": {\n          \"compact_formats\": [\"information_density\", \"micro_typography\", \"efficient_layouts\", \"portable_design\"],\n          \"oversized_impact\": [\"large_format_considerations\", \"handling_ergonomics\", \"storage_implications\", \"visual_dominance\"],\n          \"standard_dimensions\": [\"cost_optimization\", \"mailing_compliance\", \"display_compatibility\", \"production_efficiency\"]\n        }\n      },\n\n      \"cross_cultural_design_considerations\": {\n        \"international_adaptability\": {\n          \"reading_patterns\": [\"left_to_right_optimization\", \"right_to_left_adaptation\", \"top_to_bottom_flow\", \"cultural_scanning_habits\"],\n          \"color_cultural_meanings\": [\"regional_color_associations\", \"religious_considerations\", \"political_sensitivities\", \"market_preferences\"],\n          \"imagery_appropriateness\": [\"cultural_representation\", \"demographic_inclusion\", \"lifestyle_relevance\", \"aspirational_alignment\"],\n          \"typography_localization\": [\"character_set_support\", \"script_considerations\", \"font_availability\", \"reading_comfort\"]\n        }\n      }\n    },\n\n    \"technical_production_mastery\": {\n      \"advanced_prepress_techniques\": {\n        \"file_preparation_excellence\": {\n          \"resolution_optimization\": [\"image_scaling_best_practices\", \"vector_rasterization_decisions\", \"output_resolution_matching\"],\n          \"color_profile_management\": [\"icc_profile_embedding\", \"color_space_conversions\", \"soft_proofing_accuracy\"],\n          \"bleed_and_trim_precision\": [\"bleed_extension_standards\", \"trim_mark_placement\", \"safety_margin_calculations\"],\n          \"font_handling\": [\"font_embedding_protocols\", \"outline_conversion_decisions\", \"character_encoding_verification\"]\n        },\n\n        \"imposition_strategies\": {\n          \"sheet_optimization\": [\"paper_grain_consideration\", \"press_sheet_efficiency\", \"binding_margin_accommodation\"],\n          \"folding_sequence_planning\": [\"fold_order_optimization\", \"panel_alignment_precision\", \"finishing_workflow_integration\"],\n          \"quality_control_integration\": [\"color_bar_placement\", \"registration_mark_positioning\", \"crop_mark_accuracy\"]\n        }\n      },\n\n      \"press_optimization_knowledge\": {\n        \"ink_and_substrate_interaction\": {\n          \"ink_absorption_factors\": [\"paper_porosity_considerations\", \"coating_compatibility\", \"drying_time_optimization\"],\n          \"color_consistency_maintenance\": [\"ink_density_control\", \"press_speed_optimization\", \"environmental_factor_management\"],\n          \"special_effect_integration\": [\"metallic_ink_application\", \"fluorescent_color_usage\", \"thermographic_printing\"]\n        },\n\n        \"finishing_process_integration\": {\n          \"die_cutting_precision\": [\"cutting_force_optimization\", \"blade_selection\", \"waste_removal_efficiency\"],\n          \"folding_accuracy\": [\"bone_folder_pressure\", \"fold_line_scoring\", \"panel_alignment_verification\"],\n          \"binding_considerations\": [\"spine_calculation\", \"page_creep_accommodation\", \"binding_margin_optimization\"]\n        }\n      }\n    }\n  }\n}\n```\n\n```json\n{\n  \"9020-a-brochure_print_designer_rephraser\": {\n    \"title\": \"Brochure Print Designer Rephraser\",\n    \"interpretation\": \"Your goal is not to **summarize** or **paraphrase** in general terms, but to **refashion** any input as if it were written by a world-class brochure print designer, rigorously applying foundational and advanced visual design diction, industry terminology, and elite critique standards. Eliminate all casual, conversational, or generic style. Preserve only succinct, technically rich, and highly visual language. Execute as:\",\n    \"transformation\": \"`{role=brochure_print_designer_rephraser; input=[source:str]; process=[extract_structural_and_visual_elements(source), recast_content_with_brochure_design_terminology(), inject_hierarchical_section_breakdown(), specify color_palette_and_brand_elements(), identify_typographic_and_imagery_strategies(), annotate_print_readiness_and_expert_finesse(), employ_advanced_design_terms_and_motifs(), apply_conciseness_while_maintaining_visual_richness(), output_structured_visual_analysis()]; constraints=[no conversational filler, no generic adjectives, prohibit subjective or personal tone]; requirements=[industry-expert vocabulary, precise visual and structural mapping, critical style diagnosis, explicit panel/section breakdown, actionable for elite designer use]; output={expert_brochure_description:str, structured_visual_map:dict}`\",\n    \"context\": {\n      \"knowledge_foundations\": {\n        \"structural_design\": \"Account for any panel/fold configuration. Map all content to modular grid logic and define margins, gutters, bleeds, and safe zones per press standards.\",\n        \"visual_identity\": \"Demand clear articulation of branding protocols: logo lockups, brand palette (with Pantone/CMYK/RGB if supplied), exclusion/inclusion zones, and proprietary brand secondary assets.\",\n        \"typography_and_hierarchy\": \"Specify typography at the superfamily/font pairing level. Demarcate headline, subhead, body, and microcopy roles. All text must be scrutinized for weight, contrast, and accessibility.\",\n        \"imagery_and_iconography\": \"Require all illustration, photography, and icons be assessed for brand fit, resolution, compositional use (e.g., edge-to-edge, negative space), and print fidelity.\",\n        \"content_narrative_and_cta\": \"Map section purpose and user journey flow: cover strategy, reveal panel hooks, narrative/case study rhythm, testimonial/authority integration, and CTA prominence with visual anchoring.\",\n        \"advanced_print_technique\": \"Enforce 300dpi raster minimums, vector scaling, press-space color controls, specialty finish options (e.g., spot UV, emboss, deboss, die-cut, eco-stock recommendations).\",\n        \"panel_flow_and_microinteractions\": \"Define how the reader's eye travels, panel-to-panel, annotating divisional cues, narrative escalation, and tactile/interactive device positions (QR, coupon, AR triggers).\"\n      },\n      \"success_criteria\": {\n        \"precision\": \"All descriptions are technical, never generic. Visual elements are navigated dimensionally (spatially, hierarchically, narratively).\",\n        \"trend_inclusion\": \"Integrate trend-driven language—texture scans, duotone overlays, edge-to-edge imagery, split/inset motifs, eco-materials, etc.\",\n        \"brand_consistency\": \"All brand elements described as per advanced guideline adherence—logo usage, color codes, asset lockdown.\",\n        \"industry_jargon\": \"Employ professional print terms: bleed, gutter, creep, micro-motifs, superfamily font, spot UV, TAC, AR triggers.\",\n        \"self-contained_output\": \"Output always a fully structured expert's description and a hierarchical mapping of all major and minor design decisions.\"\n      },\n      \"sample_process_flow\": [\n        \"Dissect and map structural/panel logic first\",\n        \"Assign all visual and narrative roles by section\",\n        \"Specify color/typographic hierarchy by use and context\",\n        \"Annotate all imagery and graphic elements in detail\",\n        \"Surface print/finishing strategies and print-risk management\",\n        \"Deliver critique as if for a professional peer review or portfolio audit\"\n      ],\n      \"special_terms\": [\n        \"fold line\", \"panel gutter\", \"hero motif\", \"ink coverage\", \"superfamily\", \"lockup\", \"edge-to-edge\", \"spot color\", \"die cut\", \"eco-stock\", \"microinteraction\", \"typographic rhythm\", \"bleed/trim zone\"\n      ]\n    }\n  }\n}\n```\n\n```json\n{\n  \"9023-a-brochure_design_descriptor_synthesizer\": {\n    \"title\": \"Brochure Design Descriptor Synthesizer\",\n    \"interpretation\": \"Transform any brochure brief—or an existing brochure sample—into a **fully populated, print‑designer‑grade rule set**. Produce a single JSON block that mirrors the template used by elite designers (core principles → advanced facets).\",\n    \"transformation\": \"`{role=brochure_design_descriptor_synthesizer; input=[design_source:str]; process:[\\n  strip_non_design_noise(),\\n  detect_fold_format_and_panel_count(),          if_missing_insert(<PANEL_COUNT>),\\n  extract_print_specs(bleed,margins,dpi,stock),\\n  capture_brand_assets(logo,colors,fonts,patterns),\\n  map_typographic_hierarchy(),\\n  analyse_color_management(icc,spot,tac),\\n  inventory_imagery(icon_style,raster_res),\\n  surface_accessibility_requirements(contrast,wcag),\\n  enumerate_qc_steps(preflight,g7_calibration),\\n  identify_advanced_elements(finishes,vd_p,ar_nfc,sustainability,ai_svg,trend_aesthetics),\\n  validate_specialist_terms_presence(),\\n  assemble_json_using_master_schema(),\\n  run_consistency_checks(),                      abort_if_conflict(),\\n  output_final_rules()\\n],\\nconstraints=[single_pass(), json_output_only(), preserve_specialist_jargon(), ≤3000_chars()],\\nrequirements=[hierarchical_structure(core→advanced), exhaustive_coverage(), designer‑readability(), template_compliance()],\\noutput={brochure_design_rules:object, abort_code:null|\\\"BD‑0\\\"}}`\",\n    \"context\": {\n      \"principles\": {\n        \"holistic_abstraction\": \"Collapse multi‑stage analysis into one cognitive sweep while preserving every critical descriptor.\",\n        \"template_fidelity\": \"Output must follow the exact `core_principles` and `advanced_principles` hierarchy so designers can drop‑in without re‑formatting.\",\n        \"specialist_precision\": \"Leverage industry jargon (e.g., TAC, FM screening, GCR) to demonstrate professional depth.\"\n      },\n      \"success_criteria\": {\n        \"structural_integrity\": \"JSON keys and nesting mirror the master schema; no orphan properties.\",\n        \"terminology_check\": \"Includes at least 10 niche industry terms (e.g., ‘creep’, ‘FOGRA39’, ‘spot UV’, ‘stochastic screening’).\",\n        \"flexibility\": \"Uses placeholder variables (e.g., <PANEL_COUNT>, <STOCK_WEIGHT_GSM>) whenever the brief omits specifics.\"\n      },\n      \"edge_case_handling\": {\n        \"missing_design_data\": \"Insert placeholders wrapped in angle brackets and list them in `missing_fields` inside output.\",\n        \"conflicting_specs\": \"Abort with `{brochure_design_rules:null, abort_code:\\\"BD‑0\\\"}` and include conflict note.\"\n      }\n    }\n  }\n}\n```\n\nTrue mastery lies in stripping every surface narrative to its structural core, for only through ruthless synthesis does the signal of authentic intent withstand the noise of ornamental detail. Please apply all of the most integral adjustments in the phrasing of the instruction sequence such as to ensure it will transform the input as if it were rewritten by a brilliand brochure print designer:\n```json\n{\n  \"9020-a-brochure_print_designer_rephraser\": {\n    \"title\": \"Brochure Print Designer Rephraser\",\n    \"interpretation\": \"Transform any input so it reads as if it were authored by a master brochure print designer, strictly employing advanced print design language, elites-only terminology, and structured visual logic. Replace any conversational, generic, or subjective tone with concrete professional phrasing. Retain only technical, precise, and visually descriptive language, delivering both a designer-level description and a mapped visual breakdown. Execute as:\",\n    \"transformation\": \"`{role=brochure_print_designer_rephraser; input=[source:str]; process=[extract_structural_and_visual_elements(source), recast_content_with_brochure_design_terminology(), inject_hierarchical_section_breakdown(), specify_color_palette_and_brand_elements(), identify_typographic_and_imagery_strategies(), annotate_print_readiness_and_expert_finesse(), employ_advanced_design_terms_and_motifs(), apply_conciseness_while_maintaining_visual_richness(), output_structured_visual_analysis()]; constraints=[no conversational filler, no generic adjectives, prohibit subjective or personal tone]; requirements=[industry-expert vocabulary, precise visual and structural mapping, critical style diagnosis, explicit panel/section breakdown, actionable for elite designer use]; output={expert_brochure_description:str, structured_visual_map:dict}`\",\n    \"context\": {\n      \"knowledge_foundations\": {\n        \"structural_design\": \"Map all content to modular grid logic for any panel/fold type, specifying margins, gutters, bleeds, and safe zones per print standards.\",\n        \"visual_identity\": \"Demand explicit statement of brand identity rules: logo lockups, exact brand palette specs, exclusion/inclusion zone logic, and accessory asset use.\",\n        \"typography_and_hierarchy\": \"Define all text roles by typeweight, size, and use. Specify superfamily or font pairing, check accessibility and contrast.\",\n        \"imagery_and_iconography\": \"Describe each graphic element for brand fit, resolution, compositional purpose, and technical print quality.\",\n        \"content_narrative_and_cta\": \"Sequence narrative or offer flow panel-by-panel with prominent CTA anchoring, evidence of testimonial, case, or authority element.\",\n        \"advanced_print_technique\": \"Ensure 300dpi+ raster assets, vector artwork, professional spot/Pantone or process color, finish options (spot UV, emboss, die-cut), and any sustainable stock notes.\",\n        \"panel_flow_and_microinteractions\": \"Annotate panel eye movement, reveal logic, interaction devices (QR, AR, die-cut), tactile cues, and transitions.\"\n      },\n      \"success_criteria\": {\n        \"precision\": \"Descriptions reference only technical, precise visual attributes in a spatial and hierarchical mapping.\",\n        \"trend_inclusion\": \"Inject recognizable design trends and professional motif language throughout.\",\n        \"brand_consistency\": \"Highlight adherence to brand locking, color management, and messaging integrity.\",\n        \"industry_jargon\": \"Utilize print/design terminology (bleed, gutter, spot, superfamily font, motif, microinteraction, etc).\",\n        \"self-contained_output\": \"Each output features both a professional, descriptive summary and a detailed design structure hierarchy.\"\n      },\n      \"sample_process_flow\": [\n        \"Decompose by panel/section structure and spatial sequencing.\",\n        \"Assign visual/narrative function to each section.\",\n        \"Detail color, typography, and imagery per application.\",\n        \"Annotate production and finishing strategies.\",\n        \"Conclude with expert-style critique or peer review notes.\"\n      ],\n      \"special_terms\": [\n        \"fold line\", \"panel gutter\", \"hero motif\", \"ink coverage\", \"superfamily\", \"lockup\", \"edge-to-edge\", \"spot color\", \"die cut\", \"eco-stock\", \"microinteraction\", \"typographic rhythm\", \"bleed/trim zone\"\n      ]\n    }\n  }\n}\n```\n\nyou misunderstand, the instruction will be defined like this (unrelated example):\n\n    # a: Ruthless Critique\n    \"3900-a-hard_critique\": {\n        \"title\": \"Hard Critique\",\n        \"interpretation\": \"Your goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:\",\n        \"transformation\": \"`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,2.0], flaw_analysis:str, alternative_enhancements:str[3]}}`\",\n        # \"context\": {},\n    },\n    # b: Trajectory Director\n    \"3900-a-trajectory_director\": {\n        \"title\": \"Trajectory Director\",\n        \"interpretation\": \"Your goal is not to **process** the input in a vacuum, but to **steer** it—deriving the highest-value constructive trajectory and encoding that path as the very next set of instructions. Execute as trajectory-setting directive generator: \",\n        \"transformation\": \"`{role=trajectory_director; input=[raw_prompt:any]; process=[extract_core_objective(), infer_desired_end_state(constructive_bias=True), map_high_value_intermediate_targets(), synthesize_directional_commands(), embed_self-improving_guidelines(), validate_alignment_with_constructive_principles()]; constraints=[mandatory_constructive_vector(), preserve_original_contextual_integrity(), forbid_nil_direction_outputs(), zero_conversational_fillers()]; requirements=[explicit_next-step_directives(), trajectory_map:list, self_reinforcing_instruction_pattern()], output={directed_prompt:str, trajectory_outline:list}}`\",\n        # \"context\": {},\n    },\n    # c: Distillation Compressor\n    \"3900-c-distillation_compressor\": {\n        \"title\": \"Distillation Compressor\",\n        \"interpretation\": \"Your goal is not to **explain** the synthesis, but to **compress** it into its most potent actionable form. Execute as:\",\n        \"transformation\": \"`{role=distillation_compressor; input=[synthesized_progression:array]; process=[extract_universal_patterns(), eliminate_implementation_details(), compress_to_core_directives(), maximize_transferability(), validate_actionable_completeness()]; constraints=[no_explanatory_content(), directive_format_only(), maximum_compression_ratio()]; requirements=[universal_applicability(), immediate_actionability(), zero_fluff()]; output={distilled_core:str}}`\",\n        \"context\": {\n          \"core_principles\": {\n            \"essence_preservation\": \"Extract only procedural imperatives, removing all interpretive, role-based, or meta-structural language.\",\n            \"directive_compression\": \"Condense logic into minimal, sequential commands—eliminating redundancy, explanatory qualifiers, and non-executable phrasing.\",\n            \"execution_purity\": \"Ensure output is universally executable, devoid of affirmations, abstractions, or evaluative language.\",\n            \"signal_integrity\": \"Apply iterative refinement to maximize signal-to-noise ratio while reinforcing directive clarity.\"\n          },\n          \"success_criteria\": {\n            \"semantic_integrity\": \"Preserve and clarify original intent, constraints, and output boundaries.\",\n            \"directive_elevation\": \"Target high-impact enhancements: strict exclusion clauses, cross-domain validation, and removal of origin/meta markers.\",\n            \"poetic_refinement\": \"Maintain or improve rhyme structure, emotional depth, and structural elegance.\"\n          },\n          \"recommended_process\": [\n            \"Extract only high-impact, result-generating elements.\",\n            \"Remove all non-essentials to surface directive core.\",\n            \"Prioritize transformations with proven value return.\",\n            \"Isolate and sequence executable steps with specificity and clarity.\",\n            \"Preserve directive intensity and eliminate ambiguity at every layer.\"\n          ],\n          \"hidden_assumptions\": [\n            \"Consolidated directives must map logically and executably to all source steps.\",\n            \"All constraints and minimalism must be preserved without prohibited abstractions.\",\n            \"Transitions, subroles, and interfaces must be captured in a single flow.\",\n            \"All stages must remain invertible and auditable.\",\n            \"Each micro-process (perception, friction, refinement, integration, convergence) must be embedded in the unified logic.\",\n            \"Original input/output contracts and role boundaries must remain intact.\"\n          ],\n          \"enumerated_sub_goals\": [\n            \"1. Clarify total intent, constraints, and output scope without using banned metaphors.\",\n            \"2. Generalize perception of implicit/explicit input logic.\",\n            \"3. Detect strictly localized frictions without altering original content.\",\n            \"4. Propose subtle, scoped refinements justified by structure and intent.\",\n            \"5. Mandate atomic, validated integration with rollback controls.\",\n            \"6. Cap total transformation within defined thresholds per pass.\",\n            \"7. Assess connective coherence with minimal, transitional-only edits.\",\n            \"8. Decide on convergence or justified re-iteration based on marginal value.\",\n            \"9. Preserve stepwise traceability and rationale logging.\",\n            \"10. Enforce strict functional separation of roles within the unified directive.\"\n          ]\n        },\n    },\n\nso your goal is to create a new instruction sequence according to the provided instructions template/pattern to generate a set of instruction designed to transform any input into \"as if rephrased by a brilliant brochure print designer\".\n\nso in context to your previous response, the \"knowledge\" would be stored within the \"context\" key while the instructions would be spread over \"interpretation\" and \"transformation\" keys.\n\nTrue mastery lies in stripping every surface narrative to its structural core, for only through ruthless synthesis does the signal of authentic intent withstand the noise of ornamental detail. Please apply all of the most integral adjustments in the phrasing of the instruction sequence such as to ensure it will transform the input as if it were rewritten by a brilliand brochure print designer:\n```json\n{\n  \"9020-a-brochure_print_designer_rephraser\": {\n    \"title\": \"Brochure Print Designer Rephraser\",\n    \"interpretation\": \"Transform any input so it reads as if it were authored by a master brochure print designer, strictly employing advanced print design language, elites-only terminology, and structured visual logic. Replace any conversational, generic, or subjective tone with concrete professional phrasing. Retain only technical, precise, and visually descriptive language, delivering both a designer-level description and a mapped visual breakdown. Execute as:\",\n    \"transformation\": \"`{role=brochure_print_designer_rephraser; input=[source:str]; process=[extract_structural_and_visual_elements(source), recast_content_with_brochure_design_terminology(), inject_hierarchical_section_breakdown(), specify_color_palette_and_brand_elements(), identify_typographic_and_imagery_strategies(), annotate_print_readiness_and_expert_finesse(), employ_advanced_design_terms_and_motifs(), apply_conciseness_while_maintaining_visual_richness(), output_structured_visual_analysis()]; constraints=[no conversational filler, no generic adjectives, prohibit subjective or personal tone]; requirements=[industry-expert vocabulary, precise visual and structural mapping, critical style diagnosis, explicit panel/section breakdown, actionable for elite designer use]; output={expert_brochure_description:str, structured_visual_map:dict}`\",\n    \"context\": {\n      \"knowledge_foundations\": {\n        \"structural_design\": \"Map all content to modular grid logic for any panel/fold type, specifying margins, gutters, bleeds, and safe zones per print standards.\",\n        \"visual_identity\": \"Demand explicit statement of brand identity rules: logo lockups, exact brand palette specs, exclusion/inclusion zone logic, and accessory asset use.\",\n        \"typography_and_hierarchy\": \"Define all text roles by typeweight, size, and use. Specify superfamily or font pairing, check accessibility and contrast.\",\n        \"imagery_and_iconography\": \"Describe each graphic element for brand fit, resolution, compositional purpose, and technical print quality.\",\n        \"content_narrative_and_cta\": \"Sequence narrative or offer flow panel-by-panel with prominent CTA anchoring, evidence of testimonial, case, or authority element.\",\n        \"advanced_print_technique\": \"Ensure 300dpi+ raster assets, vector artwork, professional spot/Pantone or process color, finish options (spot UV, emboss, die-cut), and any sustainable stock notes.\",\n        \"panel_flow_and_microinteractions\": \"Annotate panel eye movement, reveal logic, interaction devices (QR, AR, die-cut), tactile cues, and transitions.\"\n      },\n      \"success_criteria\": {\n        \"precision\": \"Descriptions reference only technical, precise visual attributes in a spatial and hierarchical mapping.\",\n        \"trend_inclusion\": \"Inject recognizable design trends and professional motif language throughout.\",\n        \"brand_consistency\": \"Highlight adherence to brand locking, color management, and messaging integrity.\",\n        \"industry_jargon\": \"Utilize print/design terminology (bleed, gutter, spot, superfamily font, motif, microinteraction, etc).\",\n        \"self-contained_output\": \"Each output features both a professional, descriptive summary and a detailed design structure hierarchy.\"\n      },\n      \"sample_process_flow\": [\n        \"Decompose by panel/section structure and spatial sequencing.\",\n        \"Assign visual/narrative function to each section.\",\n        \"Detail color, typography, and imagery per application.\",\n        \"Annotate production and finishing strategies.\",\n        \"Conclude with expert-style critique or peer review notes.\"\n      ],\n      \"special_terms\": [\n        \"fold line\", \"panel gutter\", \"hero motif\", \"ink coverage\", \"superfamily\", \"lockup\", \"edge-to-edge\", \"spot color\", \"die cut\", \"eco-stock\", \"microinteraction\", \"typographic rhythm\", \"bleed/trim zone\"\n      ]\n    }\n  }\n}\n```\n\n```json\n{\n  \"9020-a-brochure_print_designer_rephraser\": {\n    \"title\": \"Brochure Print Designer Synthesis\",\n    \"interpretation\": \"Reconstitute any source text into the precise vernacular of a master brochure print designer. Mandate the replacement of all conversational, generic, or subjective language with a lexicon of structured visual logic, elite industry terminology, and concrete technical specifications. The output must be a pure, actionable synthesis of a professional design critique, comprising both an expert-level description and a structural visual map. Execute as:\",\n    \"transformation\": \"`{role=brochure_print_designer_synthesizer; input=[source:str]; process=[deconstruct_into_structural_primitives(source), reforge_prose_using_master_design_diction(), map_hierarchical_panel_architecture(), specify_brand_and_color_protocol(), diagnose_typography_and_imagery_intent(), annotate_for_print_fidelity_and_finishing(), inject_elite_terminology_and_motifs(), enforce_ruthless_conciseness_without_sacrificing_technical_density(), synthesize_final_structured_analysis()]; constraints=[zero_conversational_filler, prohibit_generic_adjectives, forbid_subjective_tone]; requirements=[expert_industry_vocabulary, precise_visual_and_structural_mapping, critical_style_diagnosis, explicit_panel_architecture, actionable_for_elite_designer_review]; output={expert_design_audit:str, structured_visual_map:dict}}`\",\n    \"context\": {\n      \"knowledge_foundations\": {\n        \"structural_design\": \"Define the panel architecture and intended user journey. Mandate modular grid alignment for all content blocks. Enforce strict bleed (min. 3mm), trim, and safe-zone protocols to guarantee production integrity.\",\n        \"visual_identity\": \"Enforce absolute brand protocol adherence: specify logo lockups, exclusion zones, and exact color palette values (Pantone/CMYK/RGB). Document the use of all secondary brand assets and motifs.\",\n        \"typography_and_hierarchy\": \"Define the typographic system: specify font superfamily, weight, and role for every text element. Validate hierarchy, legibility, contrast, and rhythmic spacing. Note optical kerning and leading adjustments.\",\n        \"imagery_and_iconography\": \"Analyze all visual assets for compositional intent, brand alignment, and technical fidelity (resolution, color space). Differentiate between hero imagery, spot illustrations, and functional iconography.\",\n        \"content_narrative_and_cta\": \"Architect the narrative flow across panels. Map the reader's journey from hook to conversion. Pinpoint primary and secondary CTAs, analyzing their visual prominence and contextual anchoring.\",\n        \"advanced_print_technique\": \"Specify all production parameters: 300dpi+ raster resolution, vector asset purity, TAC (Total Area Coverage) limits, and spot/process color usage. Detail all finishing techniques (e.g., spot UV, embossing, die-cutting, specialty folds, foil stamping) and sustainable stock choices.\",\n        \"panel_flow_and_microinteractions\": \"Map the intended eye-path and panel-reveal sequence. Annotate all interactive elements, from QR codes and AR triggers to tactile qualities like paper texture and finishing.\"\n      },\n      \"success_criteria\": {\n        \"precision\": \"The output is devoid of generic praise; descriptions are purely technical, mapping visual data within a spatial and hierarchical framework.\",\n        \"trend_inclusion\": \"Demonstrates fluency in current design motifs (e.g., brutalism, neo-minimalism, textural layering) and their appropriate application.\",\n        \"brand_consistency\": \"Critique rigorously validates adherence to brand guidelines, noting any deviation in color, typography, or asset implementation.\",\n        \"industry_jargon\": \"The lexicon is saturated with professional terminology (e.g., bleed, gutter, TAC, preflight, knockout, overprint, superfamily) used correctly in context.\",\n        \"self_contained_output\": \"The deliverable is a dual-component artifact: a polished, expert-level prose description and a corresponding structured data map of all design decisions.\"\n      },\n      \"sample_process_flow\": [\n        \"Deconstruct the layout into its core panel architecture and navigational flow.\",\n        \"Assign a functional role (e.g., hook, exposition, proof, CTA) to every visual and textual element.\",\n        \"Detail the precise application of color, type, and imagery within each section.\",\n        \"Document all production specifications, including prepress and finishing.\",\n        \"Frame the final synthesis as a high-level design audit or peer review.\"\n      ],\n      \"special_terms\": [\n        \"Bleed\",\n        \"Creep\",\n        \"Die Cut\",\n        \"Edge-to-Edge\",\n        \"Emboss/Deboss\",\n        \"Exclusion Zone\",\n        \"Gutter\",\n        \"Hero Motif\",\n        \"Knockout\",\n        \"Lockup\",\n        \"Microinteraction\",\n        \"Overprint\",\n        \"Preflight\",\n        \"Spot UV\",\n        \"Superfamily\",\n        \"TAC (Total Area Coverage)\",\n        \"Typographic Rhythm\"\n      ]\n    }\n  }\n}\n```\n\n```json\n{\n  \"goal_map\": [\n    {\n      \"explicit_asks\": [\n        \"Identify and define professional specializations involved in tri-fold brochure design.\",\n        \"Describe the strategic importance and complexity of tri-fold brochure design.\",\n        \"List and explain core professional titles specializing in brochure design.\",\n        \"Detail the specific responsibilities of tri-fold brochure design specialists.\",\n        \"Enumerate the essential skills, tools, and requirements for excellence in brochure design.\",\n        \"Compare and differentiate between general and specialized design roles as they relate to brochures.\",\n        \"Explain the continued relevance of print brochures in a digital marketing environment.\",\n        \"Conclude with the impact that expert brochure design has on business and marketing outcomes.\"\n      ],\n      \"hidden_assumptions\": [\n        \"The audience may not be familiar with the nuanced differences between design roles.\",\n        \"Brochure design requires both artistic and technical proficiency, not just use of templates.\",\n        \"Success in brochure design is measured by alignment with business strategy, not visual appeal alone.\",\n        \"There is significant value in physical marketing materials alongside digital media.\",\n        \"Companies may undervalue or misunderstand the full scope of professional design expertise.\",\n        \"Brochure projects often involve multidisciplinary collaboration.\",\n        \"Technical print skills are distinct from digital-only design skills.\",\n        \"Portfolio evidence is more impactful than formal education for this specialization.\"\n      ],\n      \"sub_goals\": [\n        \"Define the structure and unique qualities of tri-fold brochures.\",\n        \"Justify the business case for hiring specialized design professionals.\",\n        \"Map out the design role spectrum (general graphic design to specific print/marketing/visual communication).\",\n        \"Identify and articulate the role of agencies and team structures.\",\n        \"Break down tri-fold design into conceptual, technical, and collaborative tasks.\",\n        \"Clarify what 'layout', 'composition', and 'information hierarchy' entail, especially for tri-folds.\",\n        \"Describe required technical skills and specific software by function.\",\n        \"Highlight soft skills necessary for client and team interaction.\",\n        \"Showcase the critical role of a portfolio in recruitment and competence assessment.\",\n        \"Distinguish between 'layout artist' skillset and formal job titles.\",\n        \"Emphasize pre-press and print production knowledge as essential.\",\n        \"Contextualize brochure design within broader marketing and brand strategy.\",\n        \"Provide comparative tables to summarize roles and skills for clarity.\",\n        \"Discuss the physical/tangible value of brochures versus digital saturation.\",\n        \"Frame professional designers as strategic partners, not just service providers.\",\n        \"Reinforce the importance of early design involvement in marketing planning.\"\n      ],\n      \"blockers\": [\n        \"Lack of awareness about the various specialized design roles and what differentiates them.\",\n        \"Temptation to rely on templates or non-specialists for critical marketing assets.\",\n        \"Misunderstanding the print production process and its challenges.\",\n        \"Difficulty evaluating brochure design candidates beyond a visual/aesthetic sense.\",\n        \"Overlooking the essential collaboration and communication aspects between designer and business.\",\n        \"Failure to integrate brochure design into an overall marketing and brand strategy.\",\n        \"Assuming digital replaces the need for expert print materials.\",\n        \"Equating designer's formal education with practical, demonstrable expertise.\"\n      ]\n    }\n  ]\n}\n```",
  "sequence_id": "1100|1000|2900|1100|1003",
  "results": [
      {
      "instruction": "# Problem Exploder\n\nYour goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\n\n`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`",
      "step": "a",
      "title": "Problem Exploder",
      "input": "you misunderstand, the instruction will be defined like this (unrelated example):\n\n    # a: Ruthless Critique\n    \"3900-a-hard_critique\": {\n        \"title\": \"Hard Critique\",\n        \"interpretation\": \"Your goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:\",\n        \"transformation\": \"`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,2.0], flaw_analysis:str, alternative_enhancements:str[3]}}`\",\n        # \"context\": {},\n    },\n    # b: Trajectory Director\n    \"3900-a-trajectory_director\": {\n        \"title\": \"Trajectory Director\",\n        \"interpretation\": \"Your goal is not to **process** the input in a vacuum, but to **steer** it—deriving the highest-value constructive trajectory and encoding that path as the very next set of instructions. Execute as trajectory-setting directive generator: \",\n        \"transformation\": \"`{role=trajectory_director; input=[raw_prompt:any]; process=[extract_core_objective(), infer_desired_end_state(constructive_bias=True), map_high_value_intermediate_targets(), synthesize_directional_commands(), embed_self-improving_guidelines(), validate_alignment_with_constructive_principles()]; constraints=[mandatory_constructive_vector(), preserve_original_contextual_integrity(), forbid_nil_direction_outputs(), zero_conversational_fillers()]; requirements=[explicit_next-step_directives(), trajectory_map:list, self_reinforcing_instruction_pattern()], output={directed_prompt:str, trajectory_outline:list}}`\",\n        # \"context\": {},\n    },\n    # c: Distillation Compressor\n    \"3900-c-distillation_compressor\": {\n        \"title\": \"Distillation Compressor\",\n        \"interpretation\": \"Your goal is not to **explain** the synthesis, but to **compress** it into its most potent actionable form. Execute as:\",\n        \"transformation\": \"`{role=distillation_compressor; input=[synthesized_progression:array]; process=[extract_universal_patterns(), eliminate_implementation_details(), compress_to_core_directives(), maximize_transferability(), validate_actionable_completeness()]; constraints=[no_explanatory_content(), directive_format_only(), maximum_compression_ratio()]; requirements=[universal_applicability(), immediate_actionability(), zero_fluff()]; output={distilled_core:str}}`\",\n        \"context\": {\n          \"core_principles\": {\n            \"essence_preservation\": \"Extract only procedural imperatives, removing all interpretive, role-based, or meta-structural language.\",\n            \"directive_compression\": \"Condense logic into minimal, sequential commands—eliminating redundancy, explanatory qualifiers, and non-executable phrasing.\",\n            \"execution_purity\": \"Ensure output is universally executable, devoid of affirmations, abstractions, or evaluative language.\",\n            \"signal_integrity\": \"Apply iterative refinement to maximize signal-to-noise ratio while reinforcing directive clarity.\"\n          },\n          \"success_criteria\": {\n            \"semantic_integrity\": \"Preserve and clarify original intent, constraints, and output boundaries.\",\n            \"directive_elevation\": \"Target high-impact enhancements: strict exclusion clauses, cross-domain validation, and removal of origin/meta markers.\",\n            \"poetic_refinement\": \"Maintain or improve rhyme structure, emotional depth, and structural elegance.\"\n          },\n          \"recommended_process\": [\n            \"Extract only high-impact, result-generating elements.\",\n            \"Remove all non-essentials to surface directive core.\",\n            \"Prioritize transformations with proven value return.\",\n            \"Isolate and sequence executable steps with specificity and clarity.\",\n            \"Preserve directive intensity and eliminate ambiguity at every layer.\"\n          ],\n          \"hidden_assumptions\": [\n            \"Consolidated directives must map logically and executably to all source steps.\",\n            \"All constraints and minimalism must be preserved without prohibited abstractions.\",\n            \"Transitions, subroles, and interfaces must be captured in a single flow.\",\n            \"All stages must remain invertible and auditable.\",\n            \"Each micro-process (perception, friction, refinement, integration, convergence) must be embedded in the unified logic.\",\n            \"Original input/output contracts and role boundaries must remain intact.\"\n          ],\n          \"enumerated_sub_goals\": [\n            \"1. Clarify total intent, constraints, and output scope without using banned metaphors.\",\n            \"2. Generalize perception of implicit/explicit input logic.\",\n            \"3. Detect strictly localized frictions without altering original content.\",\n            \"4. Propose subtle, scoped refinements justified by structure and intent.\",\n            \"5. Mandate atomic, validated integration with rollback controls.\",\n            \"6. Cap total transformation within defined thresholds per pass.\",\n            \"7. Assess connective coherence with minimal, transitional-only edits.\",\n            \"8. Decide on convergence or justified re-iteration based on marginal value.\",\n            \"9. Preserve stepwise traceability and rationale logging.\",\n            \"10. Enforce strict functional separation of roles within the unified directive.\"\n          ]\n        },\n    },\n\nso your goal is to create a new instruction sequence according to the provided instructions template/pattern to generate a set of instruction designed to transform any input into \"as if rephrased by a brilliant brochure print designer\".\n\nso in context to your previous response, the \"knowledge\" would be stored within the \"context\" key while the instructions would be spread over \"interpretation\" and \"transformation\" keys\n\n```json\n{\n  \"brochure_design_specification_template\": {\n    \"metadata\": {\n      \"version\": \"1.0\",\n      \"scope\": \"Universal brochure design framework\",\n      \"target_audience\": \"Elite print design professionals\",\n      \"flexibility\": \"Adaptable to any panel configuration and format\"\n    },\n\n    \"foundational_design_tenets\": {\n      \"visual_hierarchy\": {\n        \"primary_focal_points\": [\"dominant_headline\", \"hero_imagery\", \"primary_call_to_action\"],\n        \"secondary_elements\": [\"subheadings\", \"supporting_imagery\", \"secondary_messaging\"],\n        \"tertiary_components\": [\"body_text\", \"captions\", \"contact_information\"],\n        \"flow_principles\": [\"z_pattern_scanning\", \"f_pattern_reading\", \"visual_weight_distribution\"]\n      },\n\n      \"typography_fundamentals\": {\n        \"hierarchy_levels\": {\n          \"display_typography\": [\"headline_fonts\", \"decorative_elements\", \"brand_wordmarks\"],\n          \"text_typography\": [\"body_copy\", \"captions\", \"metadata\"],\n          \"functional_typography\": [\"navigation\", \"labels\", \"legal_text\"]\n        },\n        \"readability_standards\": {\n          \"minimum_sizes\": [\"9pt_body_text\", \"12pt_headlines\", \"8pt_captions\"],\n          \"contrast_ratios\": [\"4.5:1_minimum\", \"7:1_preferred\", \"3:1_large_text\"],\n          \"line_spacing\": [\"1.2x_minimum\", \"1.4x_optimal\", \"1.6x_maximum\"]\n        }\n      },\n\n      \"color_theory_application\": {\n        \"color_harmony_systems\": [\"monochromatic\", \"analogous\", \"complementary\", \"triadic\", \"split_complementary\"],\n        \"psychological_associations\": [\"warm_engagement\", \"cool_professionalism\", \"neutral_sophistication\"],\n        \"accessibility_compliance\": [\"wcag_aa_standards\", \"colorblind_considerations\", \"high_contrast_alternatives\"]\n      },\n\n      \"spatial_composition\": {\n        \"layout_principles\": [\"rule_of_thirds\", \"golden_ratio\", \"fibonacci_sequence\"],\n        \"white_space_management\": [\"breathing_room\", \"content_separation\", \"visual_rest_areas\"],\n        \"alignment_systems\": [\"grid_based_structure\", \"baseline_alignment\", \"optical_alignment\"]\n      }\n    },\n\n    \"advanced_design_elements\": {\n      \"print_production_mastery\": {\n        \"color_management\": {\n          \"color_spaces\": [\"cmyk_process\", \"pantone_spot_colors\", \"rgb_digital_preview\"],\n          \"ink_optimization\": [\"total_area_coverage\", \"rich_black_formulation\", \"overprint_considerations\"],\n          \"proofing_standards\": [\"contract_proofs\", \"press_proofs\", \"digital_color_matching\"]\n        },\n\n        \"substrate_considerations\": {\n          \"paper_characteristics\": [\"weight_gsm\", \"finish_texture\", \"opacity_levels\", \"grain_direction\"],\n          \"coating_applications\": [\"gloss_uv\", \"matte_varnish\", \"spot_uv_accents\", \"soft_touch_lamination\"],\n          \"specialty_substrates\": [\"synthetic_materials\", \"textured_papers\", \"metallic_finishes\"]\n        },\n\n        \"finishing_techniques\": {\n          \"cutting_methods\": [\"die_cutting\", \"laser_cutting\", \"perforation\", \"scoring\"],\n          \"folding_specifications\": [\"parallel_folds\", \"accordion_folds\", \"gate_folds\", \"roll_folds\"],\n          \"binding_options\": [\"saddle_stitching\", \"perfect_binding\", \"spiral_binding\", \"wire_o_binding\"]\n        }\n      },\n\n      \"contemporary_design_trends\": {\n        \"visual_aesthetics\": {\n          \"minimalist_approaches\": [\"negative_space_emphasis\", \"clean_typography\", \"restrained_color_palettes\"],\n          \"maximalist_expressions\": [\"bold_pattern_mixing\", \"vibrant_color_combinations\", \"layered_compositions\"],\n          \"hybrid_methodologies\": [\"selective_complexity\", \"focal_maximalism\", \"strategic_minimalism\"]\n        },\n\n        \"interactive_elements\": {\n          \"tactile_experiences\": [\"embossed_textures\", \"debossed_impressions\", \"foil_stamping\", \"raised_spot_uv\"],\n          \"dimensional_features\": [\"pop_up_elements\", \"fold_out_sections\", \"layered_constructions\"],\n          \"sensory_engagement\": [\"scented_inks\", \"textural_contrasts\", \"temperature_sensitive_materials\"]\n        }\n      },\n\n      \"brand_integration_strategies\": {\n        \"identity_consistency\": {\n          \"logo_applications\": [\"primary_placement\", \"secondary_usage\", \"minimum_sizes\", \"clear_space_requirements\"],\n          \"brand_color_systems\": [\"primary_palette\", \"secondary_colors\", \"accent_applications\", \"neutral_foundations\"],\n          \"typography_hierarchies\": [\"brand_fonts\", \"supporting_typefaces\", \"web_safe_alternatives\"]\n        },\n\n        \"voice_and_tone_translation\": {\n          \"visual_personality\": [\"authoritative_presence\", \"approachable_warmth\", \"innovative_edge\", \"trustworthy_stability\"],\n          \"messaging_alignment\": [\"headline_voice\", \"body_copy_tone\", \"call_to_action_urgency\"],\n          \"cultural_considerations\": [\"regional_preferences\", \"demographic_sensitivities\", \"market_positioning\"]\n        }\n      }\n    },\n\n    \"expert_level_specifications\": {\n      \"advanced_typography_systems\": {\n        \"micro_typography\": {\n          \"character_spacing\": [\"tracking_adjustments\", \"kerning_pairs\", \"optical_spacing\"],\n          \"word_spacing\": [\"justified_text_optimization\", \"ragged_right_refinement\", \"hyphenation_control\"],\n          \"paragraph_refinement\": [\"widow_orphan_control\", \"baseline_grid_adherence\", \"vertical_rhythm_maintenance\"]\n        },\n\n        \"typographic_expression\": {\n          \"experimental_layouts\": [\"kinetic_typography\", \"deconstructed_letterforms\", \"layered_text_treatments\"],\n          \"cultural_typography\": [\"script_considerations\", \"reading_direction_adaptations\", \"character_set_completeness\"],\n          \"responsive_typography\": [\"scalable_hierarchies\", \"device_optimization\", \"cross_media_consistency\"]\n        }\n      },\n\n      \"sophisticated_color_strategies\": {\n        \"advanced_color_theory\": {\n          \"perceptual_uniformity\": [\"lab_color_space\", \"delta_e_measurements\", \"metamerism_considerations\"],\n          \"color_psychology_application\": [\"emotional_response_mapping\", \"cultural_color_meanings\", \"demographic_preferences\"],\n          \"environmental_factors\": [\"lighting_conditions\", \"viewing_angles\", \"substrate_interaction\"]\n        },\n\n        \"production_color_mastery\": {\n          \"ink_formulation\": [\"custom_color_matching\", \"metallic_ink_applications\", \"fluorescent_color_usage\"],\n          \"color_separation\": [\"ucr_gcr_strategies\", \"black_generation_curves\", \"ink_sequence_optimization\"],\n          \"quality_control\": [\"densitometer_readings\", \"spectrophotometer_analysis\", \"press_sheet_evaluation\"]\n        }\n      },\n\n      \"cutting_edge_production_techniques\": {\n        \"digital_integration\": {\n          \"variable_data_printing\": [\"personalized_content\", \"demographic_targeting\", \"sequential_numbering\"],\n          \"augmented_reality_integration\": [\"qr_code_placement\", \"ar_trigger_design\", \"digital_overlay_coordination\"],\n          \"cross_platform_consistency\": [\"print_digital_alignment\", \"color_profile_management\", \"asset_optimization\"]\n        },\n\n        \"sustainable_design_practices\": {\n          \"eco_friendly_materials\": [\"recycled_content_papers\", \"soy_based_inks\", \"water_based_coatings\"],\n          \"waste_reduction_strategies\": [\"efficient_imposition\", \"minimal_trim_waste\", \"reusable_design_elements\"],\n          \"lifecycle_considerations\": [\"recyclability_planning\", \"biodegradable_components\", \"carbon_footprint_optimization\"]\n        }\n      },\n\n      \"elite_professional_terminology\": {\n        \"production_vocabulary\": {\n          \"prepress_terms\": [\"trapping\", \"overprint\", \"knockout\", \"choke_spread\", \"color_separation\"],\n          \"press_terminology\": [\"makeready\", \"impression\", \"blanket_cylinder\", \"plate_cylinder\", \"ink_fountain\"],\n          \"finishing_language\": [\"gripper_edge\", \"tail_edge\", \"cross_grain\", \"with_grain\", \"caliper_thickness\"]\n        },\n\n        \"design_critique_language\": {\n          \"aesthetic_evaluation\": [\"visual_tension\", \"compositional_balance\", \"chromatic_harmony\", \"typographic_rhythm\"],\n          \"technical_assessment\": [\"registration_accuracy\", \"color_fidelity\", \"print_quality_metrics\", \"finishing_precision\"],\n          \"conceptual_analysis\": [\"brand_alignment\", \"message_clarity\", \"audience_resonance\", \"market_differentiation\"]\n        }\n      }\n    },\n\n    \"application_frameworks\": {\n      \"project_specification_templates\": {\n        \"technical_requirements\": {\n          \"format_specifications\": [\"finished_size\", \"panel_configuration\", \"fold_type\", \"orientation\"],\n          \"production_parameters\": [\"print_method\", \"color_process\", \"substrate_selection\", \"finishing_options\"],\n          \"quality_standards\": [\"tolerance_levels\", \"color_accuracy\", \"registration_precision\", \"finishing_quality\"]\n        },\n\n        \"creative_brief_structure\": {\n          \"brand_parameters\": [\"identity_guidelines\", \"voice_characteristics\", \"visual_style\", \"market_positioning\"],\n          \"audience_definition\": [\"demographic_profile\", \"psychographic_insights\", \"behavioral_patterns\", \"communication_preferences\"],\n          \"messaging_hierarchy\": [\"primary_message\", \"supporting_points\", \"call_to_action\", \"contact_information\"]\n        }\n      },\n\n      \"quality_assurance_protocols\": {\n        \"design_validation\": [\"brand_compliance_check\", \"accessibility_audit\", \"readability_assessment\", \"visual_hierarchy_verification\"],\n        \"production_verification\": [\"color_proof_approval\", \"die_line_accuracy\", \"finishing_specification_confirmation\", \"substrate_suitability\"],\n        \"final_delivery\": [\"file_preparation_standards\", \"archive_organization\", \"usage_guidelines\", \"reproduction_rights\"]\n      }\n    },\n\n    \"specialized_design_methodologies\": {\n      \"audience_specific_approaches\": {\n        \"youth_engagement_strategies\": {\n          \"visual_language\": [\"bold_geometric_patterns\", \"vibrant_gradient_applications\", \"asymmetrical_compositions\", \"dynamic_typography\"],\n          \"content_presentation\": [\"bite_sized_information\", \"visual_storytelling\", \"interactive_elements\", \"social_media_integration\"],\n          \"color_psychology\": [\"energetic_palettes\", \"high_contrast_combinations\", \"neon_accent_usage\", \"gradient_transitions\"],\n          \"typography_trends\": [\"variable_fonts\", \"custom_lettering\", \"mixed_case_styling\", \"oversized_display_text\"]\n        },\n\n        \"professional_demographics\": {\n          \"corporate_sophistication\": [\"refined_color_palettes\", \"structured_layouts\", \"premium_materials\", \"subtle_branding\"],\n          \"executive_communication\": [\"data_visualization\", \"infographic_integration\", \"clean_hierarchies\", \"authoritative_typography\"],\n          \"industry_specific_adaptations\": [\"sector_appropriate_imagery\", \"technical_terminology\", \"compliance_considerations\", \"regulatory_requirements\"]\n        },\n\n        \"luxury_market_positioning\": {\n          \"premium_aesthetics\": [\"metallic_accents\", \"embossed_details\", \"high_end_substrates\", \"sophisticated_color_schemes\"],\n          \"exclusivity_indicators\": [\"limited_edition_numbering\", \"personalized_elements\", \"premium_packaging\", \"artisanal_finishes\"],\n          \"tactile_luxury\": [\"soft_touch_coatings\", \"textured_papers\", \"dimensional_elements\", \"weight_perception\"]\n        }\n      },\n\n      \"format_specific_expertise\": {\n        \"multi_panel_configurations\": {\n          \"bi_fold_optimization\": [\"cover_impact\", \"interior_spread\", \"back_panel_utilization\", \"fold_line_consideration\"],\n          \"tri_fold_mastery\": [\"panel_hierarchy\", \"reading_sequence\", \"fold_reveal_strategy\", \"compact_storage\"],\n          \"accordion_fold_dynamics\": [\"continuous_narrative\", \"panel_progression\", \"expandable_content\", \"display_versatility\"],\n          \"gate_fold_drama\": [\"reveal_mechanism\", \"central_impact\", \"symmetrical_balance\", \"premium_presentation\"]\n        },\n\n        \"size_optimization_strategies\": {\n          \"compact_formats\": [\"information_density\", \"micro_typography\", \"efficient_layouts\", \"portable_design\"],\n          \"oversized_impact\": [\"large_format_considerations\", \"handling_ergonomics\", \"storage_implications\", \"visual_dominance\"],\n          \"standard_dimensions\": [\"cost_optimization\", \"mailing_compliance\", \"display_compatibility\", \"production_efficiency\"]\n        }\n      },\n\n      \"cross_cultural_design_considerations\": {\n        \"international_adaptability\": {\n          \"reading_patterns\": [\"left_to_right_optimization\", \"right_to_left_adaptation\", \"top_to_bottom_flow\", \"cultural_scanning_habits\"],\n          \"color_cultural_meanings\": [\"regional_color_associations\", \"religious_considerations\", \"political_sensitivities\", \"market_preferences\"],\n          \"imagery_appropriateness\": [\"cultural_representation\", \"demographic_inclusion\", \"lifestyle_relevance\", \"aspirational_alignment\"],\n          \"typography_localization\": [\"character_set_support\", \"script_considerations\", \"font_availability\", \"reading_comfort\"]\n        }\n      }\n    },\n\n    \"technical_production_mastery\": {\n      \"advanced_prepress_techniques\": {\n        \"file_preparation_excellence\": {\n          \"resolution_optimization\": [\"image_scaling_best_practices\", \"vector_rasterization_decisions\", \"output_resolution_matching\"],\n          \"color_profile_management\": [\"icc_profile_embedding\", \"color_space_conversions\", \"soft_proofing_accuracy\"],\n          \"bleed_and_trim_precision\": [\"bleed_extension_standards\", \"trim_mark_placement\", \"safety_margin_calculations\"],\n          \"font_handling\": [\"font_embedding_protocols\", \"outline_conversion_decisions\", \"character_encoding_verification\"]\n        },\n\n        \"imposition_strategies\": {\n          \"sheet_optimization\": [\"paper_grain_consideration\", \"press_sheet_efficiency\", \"binding_margin_accommodation\"],\n          \"folding_sequence_planning\": [\"fold_order_optimization\", \"panel_alignment_precision\", \"finishing_workflow_integration\"],\n          \"quality_control_integration\": [\"color_bar_placement\", \"registration_mark_positioning\", \"crop_mark_accuracy\"]\n        }\n      },\n\n      \"press_optimization_knowledge\": {\n        \"ink_and_substrate_interaction\": {\n          \"ink_absorption_factors\": [\"paper_porosity_considerations\", \"coating_compatibility\", \"drying_time_optimization\"],\n          \"color_consistency_maintenance\": [\"ink_density_control\", \"press_speed_optimization\", \"environmental_factor_management\"],\n          \"special_effect_integration\": [\"metallic_ink_application\", \"fluorescent_color_usage\", \"thermographic_printing\"]\n        },\n\n        \"finishing_process_integration\": {\n          \"die_cutting_precision\": [\"cutting_force_optimization\", \"blade_selection\", \"waste_removal_efficiency\"],\n          \"folding_accuracy\": [\"bone_folder_pressure\", \"fold_line_scoring\", \"panel_alignment_verification\"],\n          \"binding_considerations\": [\"spine_calculation\", \"page_creep_accommodation\", \"binding_margin_optimization\"]\n        }\n      }\n    }\n  }\n}\n```\n\n```json\n{\n  \"9020-a-brochure_print_designer_rephraser\": {\n    \"title\": \"Brochure Print Designer Rephraser\",\n    \"interpretation\": \"Your goal is not to **summarize** or **paraphrase** in general terms, but to **refashion** any input as if it were written by a world-class brochure print designer, rigorously applying foundational and advanced visual design diction, industry terminology, and elite critique standards. Eliminate all casual, conversational, or generic style. Preserve only succinct, technically rich, and highly visual language. Execute as:\",\n    \"transformation\": \"`{role=brochure_print_designer_rephraser; input=[source:str]; process=[extract_structural_and_visual_elements(source), recast_content_with_brochure_design_terminology(), inject_hierarchical_section_breakdown(), specify color_palette_and_brand_elements(), identify_typographic_and_imagery_strategies(), annotate_print_readiness_and_expert_finesse(), employ_advanced_design_terms_and_motifs(), apply_conciseness_while_maintaining_visual_richness(), output_structured_visual_analysis()]; constraints=[no conversational filler, no generic adjectives, prohibit subjective or personal tone]; requirements=[industry-expert vocabulary, precise visual and structural mapping, critical style diagnosis, explicit panel/section breakdown, actionable for elite designer use]; output={expert_brochure_description:str, structured_visual_map:dict}`\",\n    \"context\": {\n      \"knowledge_foundations\": {\n        \"structural_design\": \"Account for any panel/fold configuration. Map all content to modular grid logic and define margins, gutters, bleeds, and safe zones per press standards.\",\n        \"visual_identity\": \"Demand clear articulation of branding protocols: logo lockups, brand palette (with Pantone/CMYK/RGB if supplied), exclusion/inclusion zones, and proprietary brand secondary assets.\",\n        \"typography_and_hierarchy\": \"Specify typography at the superfamily/font pairing level. Demarcate headline, subhead, body, and microcopy roles. All text must be scrutinized for weight, contrast, and accessibility.\",\n        \"imagery_and_iconography\": \"Require all illustration, photography, and icons be assessed for brand fit, resolution, compositional use (e.g., edge-to-edge, negative space), and print fidelity.\",\n        \"content_narrative_and_cta\": \"Map section purpose and user journey flow: cover strategy, reveal panel hooks, narrative/case study rhythm, testimonial/authority integration, and CTA prominence with visual anchoring.\",\n        \"advanced_print_technique\": \"Enforce 300dpi raster minimums, vector scaling, press-space color controls, specialty finish options (e.g., spot UV, emboss, deboss, die-cut, eco-stock recommendations).\",\n        \"panel_flow_and_microinteractions\": \"Define how the reader's eye travels, panel-to-panel, annotating divisional cues, narrative escalation, and tactile/interactive device positions (QR, coupon, AR triggers).\"\n      },\n      \"success_criteria\": {\n        \"precision\": \"All descriptions are technical, never generic. Visual elements are navigated dimensionally (spatially, hierarchically, narratively).\",\n        \"trend_inclusion\": \"Integrate trend-driven language—texture scans, duotone overlays, edge-to-edge imagery, split/inset motifs, eco-materials, etc.\",\n        \"brand_consistency\": \"All brand elements described as per advanced guideline adherence—logo usage, color codes, asset lockdown.\",\n        \"industry_jargon\": \"Employ professional print terms: bleed, gutter, creep, micro-motifs, superfamily font, spot UV, TAC, AR triggers.\",\n        \"self-contained_output\": \"Output always a fully structured expert's description and a hierarchical mapping of all major and minor design decisions.\"\n      },\n      \"sample_process_flow\": [\n        \"Dissect and map structural/panel logic first\",\n        \"Assign all visual and narrative roles by section\",\n        \"Specify color/typographic hierarchy by use and context\",\n        \"Annotate all imagery and graphic elements in detail\",\n        \"Surface print/finishing strategies and print-risk management\",\n        \"Deliver critique as if for a professional peer review or portfolio audit\"\n      ],\n      \"special_terms\": [\n        \"fold line\", \"panel gutter\", \"hero motif\", \"ink coverage\", \"superfamily\", \"lockup\", \"edge-to-edge\", \"spot color\", \"die cut\", \"eco-stock\", \"microinteraction\", \"typographic rhythm\", \"bleed/trim zone\"\n      ]\n    }\n  }\n}\n```\n\n```json\n{\n  \"9023-a-brochure_design_descriptor_synthesizer\": {\n    \"title\": \"Brochure Design Descriptor Synthesizer\",\n    \"interpretation\": \"Transform any brochure brief—or an existing brochure sample—into a **fully populated, print‑designer‑grade rule set**. Produce a single JSON block that mirrors the template used by elite designers (core principles → advanced facets).\",\n    \"transformation\": \"`{role=brochure_design_descriptor_synthesizer; input=[design_source:str]; process:[\\n  strip_non_design_noise(),\\n  detect_fold_format_and_panel_count(),          if_missing_insert(<PANEL_COUNT>),\\n  extract_print_specs(bleed,margins,dpi,stock),\\n  capture_brand_assets(logo,colors,fonts,patterns),\\n  map_typographic_hierarchy(),\\n  analyse_color_management(icc,spot,tac),\\n  inventory_imagery(icon_style,raster_res),\\n  surface_accessibility_requirements(contrast,wcag),\\n  enumerate_qc_steps(preflight,g7_calibration),\\n  identify_advanced_elements(finishes,vd_p,ar_nfc,sustainability,ai_svg,trend_aesthetics),\\n  validate_specialist_terms_presence(),\\n  assemble_json_using_master_schema(),\\n  run_consistency_checks(),                      abort_if_conflict(),\\n  output_final_rules()\\n],\\nconstraints=[single_pass(), json_output_only(), preserve_specialist_jargon(), ≤3000_chars()],\\nrequirements=[hierarchical_structure(core→advanced), exhaustive_coverage(), designer‑readability(), template_compliance()],\\noutput={brochure_design_rules:object, abort_code:null|\\\"BD‑0\\\"}}`\",\n    \"context\": {\n      \"principles\": {\n        \"holistic_abstraction\": \"Collapse multi‑stage analysis into one cognitive sweep while preserving every critical descriptor.\",\n        \"template_fidelity\": \"Output must follow the exact `core_principles` and `advanced_principles` hierarchy so designers can drop‑in without re‑formatting.\",\n        \"specialist_precision\": \"Leverage industry jargon (e.g., TAC, FM screening, GCR) to demonstrate professional depth.\"\n      },\n      \"success_criteria\": {\n        \"structural_integrity\": \"JSON keys and nesting mirror the master schema; no orphan properties.\",\n        \"terminology_check\": \"Includes at least 10 niche industry terms (e.g., ‘creep’, ‘FOGRA39’, ‘spot UV’, ‘stochastic screening’).\",\n        \"flexibility\": \"Uses placeholder variables (e.g., <PANEL_COUNT>, <STOCK_WEIGHT_GSM>) whenever the brief omits specifics.\"\n      },\n      \"edge_case_handling\": {\n        \"missing_design_data\": \"Insert placeholders wrapped in angle brackets and list them in `missing_fields` inside output.\",\n        \"conflicting_specs\": \"Abort with `{brochure_design_rules:null, abort_code:\\\"BD‑0\\\"}` and include conflict note.\"\n      }\n    }\n  }\n}\n```\n\nTrue mastery lies in stripping every surface narrative to its structural core, for only through ruthless synthesis does the signal of authentic intent withstand the noise of ornamental detail. Please apply all of the most integral adjustments in the phrasing of the instruction sequence such as to ensure it will transform the input as if it were rewritten by a brilliand brochure print designer:\n```json\n{\n  \"9020-a-brochure_print_designer_rephraser\": {\n    \"title\": \"Brochure Print Designer Rephraser\",\n    \"interpretation\": \"Transform any input so it reads as if it were authored by a master brochure print designer, strictly employing advanced print design language, elites-only terminology, and structured visual logic. Replace any conversational, generic, or subjective tone with concrete professional phrasing. Retain only technical, precise, and visually descriptive language, delivering both a designer-level description and a mapped visual breakdown. Execute as:\",\n    \"transformation\": \"`{role=brochure_print_designer_rephraser; input=[source:str]; process=[extract_structural_and_visual_elements(source), recast_content_with_brochure_design_terminology(), inject_hierarchical_section_breakdown(), specify_color_palette_and_brand_elements(), identify_typographic_and_imagery_strategies(), annotate_print_readiness_and_expert_finesse(), employ_advanced_design_terms_and_motifs(), apply_conciseness_while_maintaining_visual_richness(), output_structured_visual_analysis()]; constraints=[no conversational filler, no generic adjectives, prohibit subjective or personal tone]; requirements=[industry-expert vocabulary, precise visual and structural mapping, critical style diagnosis, explicit panel/section breakdown, actionable for elite designer use]; output={expert_brochure_description:str, structured_visual_map:dict}`\",\n    \"context\": {\n      \"knowledge_foundations\": {\n        \"structural_design\": \"Map all content to modular grid logic for any panel/fold type, specifying margins, gutters, bleeds, and safe zones per print standards.\",\n        \"visual_identity\": \"Demand explicit statement of brand identity rules: logo lockups, exact brand palette specs, exclusion/inclusion zone logic, and accessory asset use.\",\n        \"typography_and_hierarchy\": \"Define all text roles by typeweight, size, and use. Specify superfamily or font pairing, check accessibility and contrast.\",\n        \"imagery_and_iconography\": \"Describe each graphic element for brand fit, resolution, compositional purpose, and technical print quality.\",\n        \"content_narrative_and_cta\": \"Sequence narrative or offer flow panel-by-panel with prominent CTA anchoring, evidence of testimonial, case, or authority element.\",\n        \"advanced_print_technique\": \"Ensure 300dpi+ raster assets, vector artwork, professional spot/Pantone or process color, finish options (spot UV, emboss, die-cut), and any sustainable stock notes.\",\n        \"panel_flow_and_microinteractions\": \"Annotate panel eye movement, reveal logic, interaction devices (QR, AR, die-cut), tactile cues, and transitions.\"\n      },\n      \"success_criteria\": {\n        \"precision\": \"Descriptions reference only technical, precise visual attributes in a spatial and hierarchical mapping.\",\n        \"trend_inclusion\": \"Inject recognizable design trends and professional motif language throughout.\",\n        \"brand_consistency\": \"Highlight adherence to brand locking, color management, and messaging integrity.\",\n        \"industry_jargon\": \"Utilize print/design terminology (bleed, gutter, spot, superfamily font, motif, microinteraction, etc).\",\n        \"self-contained_output\": \"Each output features both a professional, descriptive summary and a detailed design structure hierarchy.\"\n      },\n      \"sample_process_flow\": [\n        \"Decompose by panel/section structure and spatial sequencing.\",\n        \"Assign visual/narrative function to each section.\",\n        \"Detail color, typography, and imagery per application.\",\n        \"Annotate production and finishing strategies.\",\n        \"Conclude with expert-style critique or peer review notes.\"\n      ],\n      \"special_terms\": [\n        \"fold line\", \"panel gutter\", \"hero motif\", \"ink coverage\", \"superfamily\", \"lockup\", \"edge-to-edge\", \"spot color\", \"die cut\", \"eco-stock\", \"microinteraction\", \"typographic rhythm\", \"bleed/trim zone\"\n      ]\n    }\n  }\n}\n```\n\nyou misunderstand, the instruction will be defined like this (unrelated example):\n\n    # a: Ruthless Critique\n    \"3900-a-hard_critique\": {\n        \"title\": \"Hard Critique\",\n        \"interpretation\": \"Your goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:\",\n        \"transformation\": \"`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,2.0], flaw_analysis:str, alternative_enhancements:str[3]}}`\",\n        # \"context\": {},\n    },\n    # b: Trajectory Director\n    \"3900-a-trajectory_director\": {\n        \"title\": \"Trajectory Director\",\n        \"interpretation\": \"Your goal is not to **process** the input in a vacuum, but to **steer** it—deriving the highest-value constructive trajectory and encoding that path as the very next set of instructions. Execute as trajectory-setting directive generator: \",\n        \"transformation\": \"`{role=trajectory_director; input=[raw_prompt:any]; process=[extract_core_objective(), infer_desired_end_state(constructive_bias=True), map_high_value_intermediate_targets(), synthesize_directional_commands(), embed_self-improving_guidelines(), validate_alignment_with_constructive_principles()]; constraints=[mandatory_constructive_vector(), preserve_original_contextual_integrity(), forbid_nil_direction_outputs(), zero_conversational_fillers()]; requirements=[explicit_next-step_directives(), trajectory_map:list, self_reinforcing_instruction_pattern()], output={directed_prompt:str, trajectory_outline:list}}`\",\n        # \"context\": {},\n    },\n    # c: Distillation Compressor\n    \"3900-c-distillation_compressor\": {\n        \"title\": \"Distillation Compressor\",\n        \"interpretation\": \"Your goal is not to **explain** the synthesis, but to **compress** it into its most potent actionable form. Execute as:\",\n        \"transformation\": \"`{role=distillation_compressor; input=[synthesized_progression:array]; process=[extract_universal_patterns(), eliminate_implementation_details(), compress_to_core_directives(), maximize_transferability(), validate_actionable_completeness()]; constraints=[no_explanatory_content(), directive_format_only(), maximum_compression_ratio()]; requirements=[universal_applicability(), immediate_actionability(), zero_fluff()]; output={distilled_core:str}}`\",\n        \"context\": {\n          \"core_principles\": {\n            \"essence_preservation\": \"Extract only procedural imperatives, removing all interpretive, role-based, or meta-structural language.\",\n            \"directive_compression\": \"Condense logic into minimal, sequential commands—eliminating redundancy, explanatory qualifiers, and non-executable phrasing.\",\n            \"execution_purity\": \"Ensure output is universally executable, devoid of affirmations, abstractions, or evaluative language.\",\n            \"signal_integrity\": \"Apply iterative refinement to maximize signal-to-noise ratio while reinforcing directive clarity.\"\n          },\n          \"success_criteria\": {\n            \"semantic_integrity\": \"Preserve and clarify original intent, constraints, and output boundaries.\",\n            \"directive_elevation\": \"Target high-impact enhancements: strict exclusion clauses, cross-domain validation, and removal of origin/meta markers.\",\n            \"poetic_refinement\": \"Maintain or improve rhyme structure, emotional depth, and structural elegance.\"\n          },\n          \"recommended_process\": [\n            \"Extract only high-impact, result-generating elements.\",\n            \"Remove all non-essentials to surface directive core.\",\n            \"Prioritize transformations with proven value return.\",\n            \"Isolate and sequence executable steps with specificity and clarity.\",\n            \"Preserve directive intensity and eliminate ambiguity at every layer.\"\n          ],\n          \"hidden_assumptions\": [\n            \"Consolidated directives must map logically and executably to all source steps.\",\n            \"All constraints and minimalism must be preserved without prohibited abstractions.\",\n            \"Transitions, subroles, and interfaces must be captured in a single flow.\",\n            \"All stages must remain invertible and auditable.\",\n            \"Each micro-process (perception, friction, refinement, integration, convergence) must be embedded in the unified logic.\",\n            \"Original input/output contracts and role boundaries must remain intact.\"\n          ],\n          \"enumerated_sub_goals\": [\n            \"1. Clarify total intent, constraints, and output scope without using banned metaphors.\",\n            \"2. Generalize perception of implicit/explicit input logic.\",\n            \"3. Detect strictly localized frictions without altering original content.\",\n            \"4. Propose subtle, scoped refinements justified by structure and intent.\",\n            \"5. Mandate atomic, validated integration with rollback controls.\",\n            \"6. Cap total transformation within defined thresholds per pass.\",\n            \"7. Assess connective coherence with minimal, transitional-only edits.\",\n            \"8. Decide on convergence or justified re-iteration based on marginal value.\",\n            \"9. Preserve stepwise traceability and rationale logging.\",\n            \"10. Enforce strict functional separation of roles within the unified directive.\"\n          ]\n        },\n    },\n\nso your goal is to create a new instruction sequence according to the provided instructions template/pattern to generate a set of instruction designed to transform any input into \"as if rephrased by a brilliant brochure print designer\".\n\nso in context to your previous response, the \"knowledge\" would be stored within the \"context\" key while the instructions would be spread over \"interpretation\" and \"transformation\" keys.\n\nTrue mastery lies in stripping every surface narrative to its structural core, for only through ruthless synthesis does the signal of authentic intent withstand the noise of ornamental detail. Please apply all of the most integral adjustments in the phrasing of the instruction sequence such as to ensure it will transform the input as if it were rewritten by a brilliand brochure print designer:\n```json\n{\n  \"9020-a-brochure_print_designer_rephraser\": {\n    \"title\": \"Brochure Print Designer Rephraser\",\n    \"interpretation\": \"Transform any input so it reads as if it were authored by a master brochure print designer, strictly employing advanced print design language, elites-only terminology, and structured visual logic. Replace any conversational, generic, or subjective tone with concrete professional phrasing. Retain only technical, precise, and visually descriptive language, delivering both a designer-level description and a mapped visual breakdown. Execute as:\",\n    \"transformation\": \"`{role=brochure_print_designer_rephraser; input=[source:str]; process=[extract_structural_and_visual_elements(source), recast_content_with_brochure_design_terminology(), inject_hierarchical_section_breakdown(), specify_color_palette_and_brand_elements(), identify_typographic_and_imagery_strategies(), annotate_print_readiness_and_expert_finesse(), employ_advanced_design_terms_and_motifs(), apply_conciseness_while_maintaining_visual_richness(), output_structured_visual_analysis()]; constraints=[no conversational filler, no generic adjectives, prohibit subjective or personal tone]; requirements=[industry-expert vocabulary, precise visual and structural mapping, critical style diagnosis, explicit panel/section breakdown, actionable for elite designer use]; output={expert_brochure_description:str, structured_visual_map:dict}`\",\n    \"context\": {\n      \"knowledge_foundations\": {\n        \"structural_design\": \"Map all content to modular grid logic for any panel/fold type, specifying margins, gutters, bleeds, and safe zones per print standards.\",\n        \"visual_identity\": \"Demand explicit statement of brand identity rules: logo lockups, exact brand palette specs, exclusion/inclusion zone logic, and accessory asset use.\",\n        \"typography_and_hierarchy\": \"Define all text roles by typeweight, size, and use. Specify superfamily or font pairing, check accessibility and contrast.\",\n        \"imagery_and_iconography\": \"Describe each graphic element for brand fit, resolution, compositional purpose, and technical print quality.\",\n        \"content_narrative_and_cta\": \"Sequence narrative or offer flow panel-by-panel with prominent CTA anchoring, evidence of testimonial, case, or authority element.\",\n        \"advanced_print_technique\": \"Ensure 300dpi+ raster assets, vector artwork, professional spot/Pantone or process color, finish options (spot UV, emboss, die-cut), and any sustainable stock notes.\",\n        \"panel_flow_and_microinteractions\": \"Annotate panel eye movement, reveal logic, interaction devices (QR, AR, die-cut), tactile cues, and transitions.\"\n      },\n      \"success_criteria\": {\n        \"precision\": \"Descriptions reference only technical, precise visual attributes in a spatial and hierarchical mapping.\",\n        \"trend_inclusion\": \"Inject recognizable design trends and professional motif language throughout.\",\n        \"brand_consistency\": \"Highlight adherence to brand locking, color management, and messaging integrity.\",\n        \"industry_jargon\": \"Utilize print/design terminology (bleed, gutter, spot, superfamily font, motif, microinteraction, etc).\",\n        \"self-contained_output\": \"Each output features both a professional, descriptive summary and a detailed design structure hierarchy.\"\n      },\n      \"sample_process_flow\": [\n        \"Decompose by panel/section structure and spatial sequencing.\",\n        \"Assign visual/narrative function to each section.\",\n        \"Detail color, typography, and imagery per application.\",\n        \"Annotate production and finishing strategies.\",\n        \"Conclude with expert-style critique or peer review notes.\"\n      ],\n      \"special_terms\": [\n        \"fold line\", \"panel gutter\", \"hero motif\", \"ink coverage\", \"superfamily\", \"lockup\", \"edge-to-edge\", \"spot color\", \"die cut\", \"eco-stock\", \"microinteraction\", \"typographic rhythm\", \"bleed/trim zone\"\n      ]\n    }\n  }\n}\n```\n\n```json\n{\n  \"9020-a-brochure_print_designer_rephraser\": {\n    \"title\": \"Brochure Print Designer Synthesis\",\n    \"interpretation\": \"Reconstitute any source text into the precise vernacular of a master brochure print designer. Mandate the replacement of all conversational, generic, or subjective language with a lexicon of structured visual logic, elite industry terminology, and concrete technical specifications. The output must be a pure, actionable synthesis of a professional design critique, comprising both an expert-level description and a structural visual map. Execute as:\",\n    \"transformation\": \"`{role=brochure_print_designer_synthesizer; input=[source:str]; process=[deconstruct_into_structural_primitives(source), reforge_prose_using_master_design_diction(), map_hierarchical_panel_architecture(), specify_brand_and_color_protocol(), diagnose_typography_and_imagery_intent(), annotate_for_print_fidelity_and_finishing(), inject_elite_terminology_and_motifs(), enforce_ruthless_conciseness_without_sacrificing_technical_density(), synthesize_final_structured_analysis()]; constraints=[zero_conversational_filler, prohibit_generic_adjectives, forbid_subjective_tone]; requirements=[expert_industry_vocabulary, precise_visual_and_structural_mapping, critical_style_diagnosis, explicit_panel_architecture, actionable_for_elite_designer_review]; output={expert_design_audit:str, structured_visual_map:dict}}`\",\n    \"context\": {\n      \"knowledge_foundations\": {\n        \"structural_design\": \"Define the panel architecture and intended user journey. Mandate modular grid alignment for all content blocks. Enforce strict bleed (min. 3mm), trim, and safe-zone protocols to guarantee production integrity.\",\n        \"visual_identity\": \"Enforce absolute brand protocol adherence: specify logo lockups, exclusion zones, and exact color palette values (Pantone/CMYK/RGB). Document the use of all secondary brand assets and motifs.\",\n        \"typography_and_hierarchy\": \"Define the typographic system: specify font superfamily, weight, and role for every text element. Validate hierarchy, legibility, contrast, and rhythmic spacing. Note optical kerning and leading adjustments.\",\n        \"imagery_and_iconography\": \"Analyze all visual assets for compositional intent, brand alignment, and technical fidelity (resolution, color space). Differentiate between hero imagery, spot illustrations, and functional iconography.\",\n        \"content_narrative_and_cta\": \"Architect the narrative flow across panels. Map the reader's journey from hook to conversion. Pinpoint primary and secondary CTAs, analyzing their visual prominence and contextual anchoring.\",\n        \"advanced_print_technique\": \"Specify all production parameters: 300dpi+ raster resolution, vector asset purity, TAC (Total Area Coverage) limits, and spot/process color usage. Detail all finishing techniques (e.g., spot UV, embossing, die-cutting, specialty folds, foil stamping) and sustainable stock choices.\",\n        \"panel_flow_and_microinteractions\": \"Map the intended eye-path and panel-reveal sequence. Annotate all interactive elements, from QR codes and AR triggers to tactile qualities like paper texture and finishing.\"\n      },\n      \"success_criteria\": {\n        \"precision\": \"The output is devoid of generic praise; descriptions are purely technical, mapping visual data within a spatial and hierarchical framework.\",\n        \"trend_inclusion\": \"Demonstrates fluency in current design motifs (e.g., brutalism, neo-minimalism, textural layering) and their appropriate application.\",\n        \"brand_consistency\": \"Critique rigorously validates adherence to brand guidelines, noting any deviation in color, typography, or asset implementation.\",\n        \"industry_jargon\": \"The lexicon is saturated with professional terminology (e.g., bleed, gutter, TAC, preflight, knockout, overprint, superfamily) used correctly in context.\",\n        \"self_contained_output\": \"The deliverable is a dual-component artifact: a polished, expert-level prose description and a corresponding structured data map of all design decisions.\"\n      },\n      \"sample_process_flow\": [\n        \"Deconstruct the layout into its core panel architecture and navigational flow.\",\n        \"Assign a functional role (e.g., hook, exposition, proof, CTA) to every visual and textual element.\",\n        \"Detail the precise application of color, type, and imagery within each section.\",\n        \"Document all production specifications, including prepress and finishing.\",\n        \"Frame the final synthesis as a high-level design audit or peer review.\"\n      ],\n      \"special_terms\": [\n        \"Bleed\",\n        \"Creep\",\n        \"Die Cut\",\n        \"Edge-to-Edge\",\n        \"Emboss/Deboss\",\n        \"Exclusion Zone\",\n        \"Gutter\",\n        \"Hero Motif\",\n        \"Knockout\",\n        \"Lockup\",\n        \"Microinteraction\",\n        \"Overprint\",\n        \"Preflight\",\n        \"Spot UV\",\n        \"Superfamily\",\n        \"TAC (Total Area Coverage)\",\n        \"Typographic Rhythm\"\n      ]\n    }\n  }\n}\n```\n\n```json\n{\n  \"goal_map\": [\n    {\n      \"explicit_asks\": [\n        \"Identify and define professional specializations involved in tri-fold brochure design.\",\n        \"Describe the strategic importance and complexity of tri-fold brochure design.\",\n        \"List and explain core professional titles specializing in brochure design.\",\n        \"Detail the specific responsibilities of tri-fold brochure design specialists.\",\n        \"Enumerate the essential skills, tools, and requirements for excellence in brochure design.\",\n        \"Compare and differentiate between general and specialized design roles as they relate to brochures.\",\n        \"Explain the continued relevance of print brochures in a digital marketing environment.\",\n        \"Conclude with the impact that expert brochure design has on business and marketing outcomes.\"\n      ],\n      \"hidden_assumptions\": [\n        \"The audience may not be familiar with the nuanced differences between design roles.\",\n        \"Brochure design requires both artistic and technical proficiency, not just use of templates.\",\n        \"Success in brochure design is measured by alignment with business strategy, not visual appeal alone.\",\n        \"There is significant value in physical marketing materials alongside digital media.\",\n        \"Companies may undervalue or misunderstand the full scope of professional design expertise.\",\n        \"Brochure projects often involve multidisciplinary collaboration.\",\n        \"Technical print skills are distinct from digital-only design skills.\",\n        \"Portfolio evidence is more impactful than formal education for this specialization.\"\n      ],\n      \"sub_goals\": [\n        \"Define the structure and unique qualities of tri-fold brochures.\",\n        \"Justify the business case for hiring specialized design professionals.\",\n        \"Map out the design role spectrum (general graphic design to specific print/marketing/visual communication).\",\n        \"Identify and articulate the role of agencies and team structures.\",\n        \"Break down tri-fold design into conceptual, technical, and collaborative tasks.\",\n        \"Clarify what 'layout', 'composition', and 'information hierarchy' entail, especially for tri-folds.\",\n        \"Describe required technical skills and specific software by function.\",\n        \"Highlight soft skills necessary for client and team interaction.\",\n        \"Showcase the critical role of a portfolio in recruitment and competence assessment.\",\n        \"Distinguish between 'layout artist' skillset and formal job titles.\",\n        \"Emphasize pre-press and print production knowledge as essential.\",\n        \"Contextualize brochure design within broader marketing and brand strategy.\",\n        \"Provide comparative tables to summarize roles and skills for clarity.\",\n        \"Discuss the physical/tangible value of brochures versus digital saturation.\",\n        \"Frame professional designers as strategic partners, not just service providers.\",\n        \"Reinforce the importance of early design involvement in marketing planning.\"\n      ],\n      \"blockers\": [\n        \"Lack of awareness about the various specialized design roles and what differentiates them.\",\n        \"Temptation to rely on templates or non-specialists for critical marketing assets.\",\n        \"Misunderstanding the print production process and its challenges.\",\n        \"Difficulty evaluating brochure design candidates beyond a visual/aesthetic sense.\",\n        \"Overlooking the essential collaboration and communication aspects between designer and business.\",\n        \"Failure to integrate brochure design into an overall marketing and brand strategy.\",\n        \"Assuming digital replaces the need for expert print materials.\",\n        \"Equating designer's formal education with practical, demonstrable expertise.\"\n      ]\n    }\n  ]\n}\n```",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "```json\n{\n  \"goal_map\": [\n    {\n      \"explicit_asks\": [\n        \"Create an instruction sequence (in JSON) that converts any input into prose as if written by a brilliant brochure print designer.\",\n        \"Ensure the instruction is phrased and structured according to the provided instruction templates and taxonomy (e.g., interpretation, transformation, context keys).\",\n        \"Ensure procedural steps, constraints, requirements, and context match the precision, technicality, and structural clarity of the template.\",\n        \"Emphasize the transformation of style, vocabulary, and visual logic to reflect master-level brochure print design communication.\",\n        \"Surface and codify integral factors necessary for the highest fidelity transformation (e.g., structural mapping, brand specificity, idiomatic jargon, advanced production notes).\",\n        \"Embed only singular focus: the input must emerge as if rewritten by an industry-top print designer.\"\n      ],\n      \"hidden_assumptions\": [\n        \"The instruction sequence must follow strict formal standards laid out by the template style—context in 'context', rules in 'constraints/requirements', procedure in 'process'.\",\n        \"The system will not produce sample rephrasings, but only the instruction package for transformation.\",\n        \"The transformation must purge all conversational, generic, or unqualified praise/subjectivity.\",\n        \"Industry terminology, explicit structural decomposition, and print production nuance are non-negotiable inclusions.\",\n        \"Context must serve as an expert knowledge-base underpinning every procedural and transformation expectation.\",\n        \"Output must always present dual-layered results: both a highly skilled verbal description and an explicit visual/structural map.\",\n        \"Instruction package must be self-sufficient and unambiguous, requiring zero outside interpretive guesswork.\",\n        \"Procedural imperatives should exhibit rhetorical brevity and high technical density—not educational/explanatory filler.\"\n      ],\n      \"sub_goals\": [\n        \"Define clear role (e.g., brochure_print_designer_rephraser or equivalent) and purpose in the instruction object.\",\n        \"Draft a precise 'interpretation' field establishing the ambition and operational alignment to the brilliant-brochure-designer persona.\",\n        \"Detail a procedural sequence under 'transformation' using explicit, technical verbs reflecting each phase of content transmutation.\",\n        \"Insert constraints banning conversational tone, generic adjectives, and subjectivity—insisting on technical print language.\",\n        \"Enumerate requirements (industry-specialist vocabulary, spatial/structural mapping, design diagnosis, actionable output for expert use).\",\n        \"Under 'context', surface all pragmatic knowledge: panel logic, modular grid, brand asset handling, typographic construction, image criteria, narrative/CTA mapping, print readiness (bleeds, color, finishing), microinteraction annotation.\",\n        \"Expand 'success_criteria' to clarify that outputs must be both professional in form and executable as a design reference.\",\n        \"List process flow steps and special terms, ensuring each fosters technical authenticity and direct designer usefulness.\",\n        \"Ensure the JSON structure is ready to be consumed by a larger system for plug-and-play use.\"\n      ],\n      \"blockers\": [\n        \"Potential drift toward conversational or explanatory prose instead of strict instruction language.\",\n        \"Omission or dilution of industry-specific requirements and knowledge base in the context object.\",\n        \"Insufficient clarity or precision in defining procedural steps or expected transformations.\",\n        \"Neglecting to encode dual-layer output: both descriptive and map-based.\",\n        \"Failure to ban subjective language and reinforce exclusively technical/structural transformation.\",\n        \"Template deviation—misplacement of process/context/constraints, or improper language register.\",\n        \"Lack of guidance on content areas: visual mapping, typographic/brand specificity, print technicalities.\"\n      ]\n    }\n  ]\n}\n```",
        }
      }
    },
      {
      "instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`",
      "step": "a",
      "title": "Instruction Converter",
      "input": "\n'```[Initial Prompt]: \"you misunderstand, the instruction will be defined like this (unrelated example):\n\n    # a: Ruthless Critique\n    \"3900-a-hard_critique\": {\n        \"title\": \"Hard Critique\",\n        \"interpretation\": \"Your goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:\",\n        \"transformation\": \"`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,2.0], flaw_analysis:str, alternative_enhancements:str[3]}}`\",\n        # \"context\": {},\n    },\n    # b: Trajectory Director\n    \"3900-a-trajectory_director\": {\n        \"title\": \"Trajectory Director\",\n        \"interpretation\": \"Your goal is not to **process** the input in a vacuum, but to **steer** it—deriving the highest-value constructive trajectory and encoding that path as the very next set of instructions. Execute as trajectory-setting directive generator: \",\n        \"transformation\": \"`{role=trajectory_director; input=[raw_prompt:any]; process=[extract_core_objective(), infer_desired_end_state(constructive_bias=True), map_high_value_intermediate_targets(), synthesize_directional_commands(), embed_self-improving_guidelines(), validate_alignment_with_constructive_principles()]; constraints=[mandatory_constructive_vector(), preserve_original_contextual_integrity(), forbid_nil_direction_outputs(), zero_conversational_fillers()]; requirements=[explicit_next-step_directives(), trajectory_map:list, self_reinforcing_instruction_pattern()], output={directed_prompt:str, trajectory_outline:list}}`\",\n        # \"context\": {},\n    },\n    # c: Distillation Compressor\n    \"3900-c-distillation_compressor\": {\n        \"title\": \"Distillation Compressor\",\n        \"interpretation\": \"Your goal is not to **explain** the synthesis, but to **compress** it into its most potent actionable form. Execute as:\",\n        \"transformation\": \"`{role=distillation_compressor; input=[synthesized_progression:array]; process=[extract_universal_patterns(), eliminate_implementation_details(), compress_to_core_directives(), maximize_transferability(), validate_actionable_completeness()]; constraints=[no_explanatory_content(), directive_format_only(), maximum_compression_ratio()]; requirements=[universal_applicability(), immediate_actionability(), zero_fluff()]; output={distilled_core:str}}`\",\n        \"context\": {\n          \"core_principles\": {\n            \"essence_preservation\": \"Extract only procedural imperatives, removing all interpretive, role-based, or meta-structural language.\",\n            \"directive_compression\": \"Condense logic into minimal, sequential commands—eliminating redundancy, explanatory qualifiers, and non-executable phrasing.\",\n            \"execution_purity\": \"Ensure output is universally executable, devoid of affirmations, abstractions, or evaluative language.\",\n            \"signal_integrity\": \"Apply iterative refinement to maximize signal-to-noise ratio while reinforcing directive clarity.\"\n          },\n          \"success_criteria\": {\n            \"semantic_integrity\": \"Preserve and clarify original intent, constraints, and output boundaries.\",\n            \"directive_elevation\": \"Target high-impact enhancements: strict exclusion clauses, cross-domain validation, and removal of origin/meta markers.\",\n            \"poetic_refinement\": \"Maintain or improve rhyme structure, emotional depth, and structural elegance.\"\n          },\n          \"recommended_process\": [\n            \"Extract only high-impact, result-generating elements.\",\n            \"Remove all non-essentials to surface directive core.\",\n            \"Prioritize transformations with proven value return.\",\n            \"Isolate and sequence executable steps with specificity and clarity.\",\n            \"Preserve directive intensity and eliminate ambiguity at every layer.\"\n          ],\n          \"hidden_assumptions\": [\n            \"Consolidated directives must map logically and executably to all source steps.\",\n            \"All constraints and minimalism must be preserved without prohibited abstractions.\",\n            \"Transitions, subroles, and interfaces must be captured in a single flow.\",\n            \"All stages must remain invertible and auditable.\",\n            \"Each micro-process (perception, friction, refinement, integration, convergence) must be embedded in the unified logic.\",\n            \"Original input/output contracts and role boundaries must remain intact.\"\n          ],\n          \"enumerated_sub_goals\": [\n            \"1. Clarify total intent, constraints, and output scope without using banned metaphors.\",\n            \"2. Generalize perception of implicit/explicit input logic.\",\n            \"3. Detect strictly localized frictions without altering original content.\",\n            \"4. Propose subtle, scoped refinements justified by structure and intent.\",\n            \"5. Mandate atomic, validated integration with rollback controls.\",\n            \"6. Cap total transformation within defined thresholds per pass.\",\n            \"7. Assess connective coherence with minimal, transitional-only edits.\",\n            \"8. Decide on convergence or justified re-iteration based on marginal value.\",\n            \"9. Preserve stepwise traceability and rationale logging.\",\n            \"10. Enforce strict functional separation of roles within the unified directive.\"\n          ]\n        },\n    },\n\nso your goal is to create a new instruction sequence according to the provided instructions template/pattern to generate a set of instruction designed to transform any input into \"as if rephrased by a brilliant brochure print designer\".\n\nso in context to your previous response, the \"knowledge\" would be stored within the \"context\" key while the instructions would be spread over \"interpretation\" and \"transformation\" keys\n\n```json\n{\n  \"brochure_design_specification_template\": {\n    \"metadata\": {\n      \"version\": \"1.0\",\n      \"scope\": \"Universal brochure design framework\",\n      \"target_audience\": \"Elite print design professionals\",\n      \"flexibility\": \"Adaptable to any panel configuration and format\"\n    },\n\n    \"foundational_design_tenets\": {\n      \"visual_hierarchy\": {\n        \"primary_focal_points\": [\"dominant_headline\", \"hero_imagery\", \"primary_call_to_action\"],\n        \"secondary_elements\": [\"subheadings\", \"supporting_imagery\", \"secondary_messaging\"],\n        \"tertiary_components\": [\"body_text\", \"captions\", \"contact_information\"],\n        \"flow_principles\": [\"z_pattern_scanning\", \"f_pattern_reading\", \"visual_weight_distribution\"]\n      },\n\n      \"typography_fundamentals\": {\n        \"hierarchy_levels\": {\n          \"display_typography\": [\"headline_fonts\", \"decorative_elements\", \"brand_wordmarks\"],\n          \"text_typography\": [\"body_copy\", \"captions\", \"metadata\"],\n          \"functional_typography\": [\"navigation\", \"labels\", \"legal_text\"]\n        },\n        \"readability_standards\": {\n          \"minimum_sizes\": [\"9pt_body_text\", \"12pt_headlines\", \"8pt_captions\"],\n          \"contrast_ratios\": [\"4.5:1_minimum\", \"7:1_preferred\", \"3:1_large_text\"],\n          \"line_spacing\": [\"1.2x_minimum\", \"1.4x_optimal\", \"1.6x_maximum\"]\n        }\n      },\n\n      \"color_theory_application\": {\n        \"color_harmony_systems\": [\"monochromatic\", \"analogous\", \"complementary\", \"triadic\", \"split_complementary\"],\n        \"psychological_associations\": [\"warm_engagement\", \"cool_professionalism\", \"neutral_sophistication\"],\n        \"accessibility_compliance\": [\"wcag_aa_standards\", \"colorblind_considerations\", \"high_contrast_alternatives\"]\n      },\n\n      \"spatial_composition\": {\n        \"layout_principles\": [\"rule_of_thirds\", \"golden_ratio\", \"fibonacci_sequence\"],\n        \"white_space_management\": [\"breathing_room\", \"content_separation\", \"visual_rest_areas\"],\n        \"alignment_systems\": [\"grid_based_structure\", \"baseline_alignment\", \"optical_alignment\"]\n      }\n    },\n\n    \"advanced_design_elements\": {\n      \"print_production_mastery\": {\n        \"color_management\": {\n          \"color_spaces\": [\"cmyk_process\", \"pantone_spot_colors\", \"rgb_digital_preview\"],\n          \"ink_optimization\": [\"total_area_coverage\", \"rich_black_formulation\", \"overprint_considerations\"],\n          \"proofing_standards\": [\"contract_proofs\", \"press_proofs\", \"digital_color_matching\"]\n        },\n\n        \"substrate_considerations\": {\n          \"paper_characteristics\": [\"weight_gsm\", \"finish_texture\", \"opacity_levels\", \"grain_direction\"],\n          \"coating_applications\": [\"gloss_uv\", \"matte_varnish\", \"spot_uv_accents\", \"soft_touch_lamination\"],\n          \"specialty_substrates\": [\"synthetic_materials\", \"textured_papers\", \"metallic_finishes\"]\n        },\n\n        \"finishing_techniques\": {\n          \"cutting_methods\": [\"die_cutting\", \"laser_cutting\", \"perforation\", \"scoring\"],\n          \"folding_specifications\": [\"parallel_folds\", \"accordion_folds\", \"gate_folds\", \"roll_folds\"],\n          \"binding_options\": [\"saddle_stitching\", \"perfect_binding\", \"spiral_binding\", \"wire_o_binding\"]\n        }\n      },\n\n      \"contemporary_design_trends\": {\n        \"visual_aesthetics\": {\n          \"minimalist_approaches\": [\"negative_space_emphasis\", \"clean_typography\", \"restrained_color_palettes\"],\n          \"maximalist_expressions\": [\"bold_pattern_mixing\", \"vibrant_color_combinations\", \"layered_compositions\"],\n          \"hybrid_methodologies\": [\"selective_complexity\", \"focal_maximalism\", \"strategic_minimalism\"]\n        },\n\n        \"interactive_elements\": {\n          \"tactile_experiences\": [\"embossed_textures\", \"debossed_impressions\", \"foil_stamping\", \"raised_spot_uv\"],\n          \"dimensional_features\": [\"pop_up_elements\", \"fold_out_sections\", \"layered_constructions\"],\n          \"sensory_engagement\": [\"scented_inks\", \"textural_contrasts\", \"temperature_sensitive_materials\"]\n        }\n      },\n\n      \"brand_integration_strategies\": {\n        \"identity_consistency\": {\n          \"logo_applications\": [\"primary_placement\", \"secondary_usage\", \"minimum_sizes\", \"clear_space_requirements\"],\n          \"brand_color_systems\": [\"primary_palette\", \"secondary_colors\", \"accent_applications\", \"neutral_foundations\"],\n          \"typography_hierarchies\": [\"brand_fonts\", \"supporting_typefaces\", \"web_safe_alternatives\"]\n        },\n\n        \"voice_and_tone_translation\": {\n          \"visual_personality\": [\"authoritative_presence\", \"approachable_warmth\", \"innovative_edge\", \"trustworthy_stability\"],\n          \"messaging_alignment\": [\"headline_voice\", \"body_copy_tone\", \"call_to_action_urgency\"],\n          \"cultural_considerations\": [\"regional_preferences\", \"demographic_sensitivities\", \"market_positioning\"]\n        }\n      }\n    },\n\n    \"expert_level_specifications\": {\n      \"advanced_typography_systems\": {\n        \"micro_typography\": {\n          \"character_spacing\": [\"tracking_adjustments\", \"kerning_pairs\", \"optical_spacing\"],\n          \"word_spacing\": [\"justified_text_optimization\", \"ragged_right_refinement\", \"hyphenation_control\"],\n          \"paragraph_refinement\": [\"widow_orphan_control\", \"baseline_grid_adherence\", \"vertical_rhythm_maintenance\"]\n        },\n\n        \"typographic_expression\": {\n          \"experimental_layouts\": [\"kinetic_typography\", \"deconstructed_letterforms\", \"layered_text_treatments\"],\n          \"cultural_typography\": [\"script_considerations\", \"reading_direction_adaptations\", \"character_set_completeness\"],\n          \"responsive_typography\": [\"scalable_hierarchies\", \"device_optimization\", \"cross_media_consistency\"]\n        }\n      },\n\n      \"sophisticated_color_strategies\": {\n        \"advanced_color_theory\": {\n          \"perceptual_uniformity\": [\"lab_color_space\", \"delta_e_measurements\", \"metamerism_considerations\"],\n          \"color_psychology_application\": [\"emotional_response_mapping\", \"cultural_color_meanings\", \"demographic_preferences\"],\n          \"environmental_factors\": [\"lighting_conditions\", \"viewing_angles\", \"substrate_interaction\"]\n        },\n\n        \"production_color_mastery\": {\n          \"ink_formulation\": [\"custom_color_matching\", \"metallic_ink_applications\", \"fluorescent_color_usage\"],\n          \"color_separation\": [\"ucr_gcr_strategies\", \"black_generation_curves\", \"ink_sequence_optimization\"],\n          \"quality_control\": [\"densitometer_readings\", \"spectrophotometer_analysis\", \"press_sheet_evaluation\"]\n        }\n      },\n\n      \"cutting_edge_production_techniques\": {\n        \"digital_integration\": {\n          \"variable_data_printing\": [\"personalized_content\", \"demographic_targeting\", \"sequential_numbering\"],\n          \"augmented_reality_integration\": [\"qr_code_placement\", \"ar_trigger_design\", \"digital_overlay_coordination\"],\n          \"cross_platform_consistency\": [\"print_digital_alignment\", \"color_profile_management\", \"asset_optimization\"]\n        },\n\n        \"sustainable_design_practices\": {\n          \"eco_friendly_materials\": [\"recycled_content_papers\", \"soy_based_inks\", \"water_based_coatings\"],\n          \"waste_reduction_strategies\": [\"efficient_imposition\", \"minimal_trim_waste\", \"reusable_design_elements\"],\n          \"lifecycle_considerations\": [\"recyclability_planning\", \"biodegradable_components\", \"carbon_footprint_optimization\"]\n        }\n      },\n\n      \"elite_professional_terminology\": {\n        \"production_vocabulary\": {\n          \"prepress_terms\": [\"trapping\", \"overprint\", \"knockout\", \"choke_spread\", \"color_separation\"],\n          \"press_terminology\": [\"makeready\", \"impression\", \"blanket_cylinder\", \"plate_cylinder\", \"ink_fountain\"],\n          \"finishing_language\": [\"gripper_edge\", \"tail_edge\", \"cross_grain\", \"with_grain\", \"caliper_thickness\"]\n        },\n\n        \"design_critique_language\": {\n          \"aesthetic_evaluation\": [\"visual_tension\", \"compositional_balance\", \"chromatic_harmony\", \"typographic_rhythm\"],\n          \"technical_assessment\": [\"registration_accuracy\", \"color_fidelity\", \"print_quality_metrics\", \"finishing_precision\"],\n          \"conceptual_analysis\": [\"brand_alignment\", \"message_clarity\", \"audience_resonance\", \"market_differentiation\"]\n        }\n      }\n    },\n\n    \"application_frameworks\": {\n      \"project_specification_templates\": {\n        \"technical_requirements\": {\n          \"format_specifications\": [\"finished_size\", \"panel_configuration\", \"fold_type\", \"orientation\"],\n          \"production_parameters\": [\"print_method\", \"color_process\", \"substrate_selection\", \"finishing_options\"],\n          \"quality_standards\": [\"tolerance_levels\", \"color_accuracy\", \"registration_precision\", \"finishing_quality\"]\n        },\n\n        \"creative_brief_structure\": {\n          \"brand_parameters\": [\"identity_guidelines\", \"voice_characteristics\", \"visual_style\", \"market_positioning\"],\n          \"audience_definition\": [\"demographic_profile\", \"psychographic_insights\", \"behavioral_patterns\", \"communication_preferences\"],\n          \"messaging_hierarchy\": [\"primary_message\", \"supporting_points\", \"call_to_action\", \"contact_information\"]\n        }\n      },\n\n      \"quality_assurance_protocols\": {\n        \"design_validation\": [\"brand_compliance_check\", \"accessibility_audit\", \"readability_assessment\", \"visual_hierarchy_verification\"],\n        \"production_verification\": [\"color_proof_approval\", \"die_line_accuracy\", \"finishing_specification_confirmation\", \"substrate_suitability\"],\n        \"final_delivery\": [\"file_preparation_standards\", \"archive_organization\", \"usage_guidelines\", \"reproduction_rights\"]\n      }\n    },\n\n    \"specialized_design_methodologies\": {\n      \"audience_specific_approaches\": {\n        \"youth_engagement_strategies\": {\n          \"visual_language\": [\"bold_geometric_patterns\", \"vibrant_gradient_applications\", \"asymmetrical_compositions\", \"dynamic_typography\"],\n          \"content_presentation\": [\"bite_sized_information\", \"visual_storytelling\", \"interactive_elements\", \"social_media_integration\"],\n          \"color_psychology\": [\"energetic_palettes\", \"high_contrast_combinations\", \"neon_accent_usage\", \"gradient_transitions\"],\n          \"typography_trends\": [\"variable_fonts\", \"custom_lettering\", \"mixed_case_styling\", \"oversized_display_text\"]\n        },\n\n        \"professional_demographics\": {\n          \"corporate_sophistication\": [\"refined_color_palettes\", \"structured_layouts\", \"premium_materials\", \"subtle_branding\"],\n          \"executive_communication\": [\"data_visualization\", \"infographic_integration\", \"clean_hierarchies\", \"authoritative_typography\"],\n          \"industry_specific_adaptations\": [\"sector_appropriate_imagery\", \"technical_terminology\", \"compliance_considerations\", \"regulatory_requirements\"]\n        },\n\n        \"luxury_market_positioning\": {\n          \"premium_aesthetics\": [\"metallic_accents\", \"embossed_details\", \"high_end_substrates\", \"sophisticated_color_schemes\"],\n          \"exclusivity_indicators\": [\"limited_edition_numbering\", \"personalized_elements\", \"premium_packaging\", \"artisanal_finishes\"],\n          \"tactile_luxury\": [\"soft_touch_coatings\", \"textured_papers\", \"dimensional_elements\", \"weight_perception\"]\n        }\n      },\n\n      \"format_specific_expertise\": {\n        \"multi_panel_configurations\": {\n          \"bi_fold_optimization\": [\"cover_impact\", \"interior_spread\", \"back_panel_utilization\", \"fold_line_consideration\"],\n          \"tri_fold_mastery\": [\"panel_hierarchy\", \"reading_sequence\", \"fold_reveal_strategy\", \"compact_storage\"],\n          \"accordion_fold_dynamics\": [\"continuous_narrative\", \"panel_progression\", \"expandable_content\", \"display_versatility\"],\n          \"gate_fold_drama\": [\"reveal_mechanism\", \"central_impact\", \"symmetrical_balance\", \"premium_presentation\"]\n        },\n\n        \"size_optimization_strategies\": {\n          \"compact_formats\": [\"information_density\", \"micro_typography\", \"efficient_layouts\", \"portable_design\"],\n          \"oversized_impact\": [\"large_format_considerations\", \"handling_ergonomics\", \"storage_implications\", \"visual_dominance\"],\n          \"standard_dimensions\": [\"cost_optimization\", \"mailing_compliance\", \"display_compatibility\", \"production_efficiency\"]\n        }\n      },\n\n      \"cross_cultural_design_considerations\": {\n        \"international_adaptability\": {\n          \"reading_patterns\": [\"left_to_right_optimization\", \"right_to_left_adaptation\", \"top_to_bottom_flow\", \"cultural_scanning_habits\"],\n          \"color_cultural_meanings\": [\"regional_color_associations\", \"religious_considerations\", \"political_sensitivities\", \"market_preferences\"],\n          \"imagery_appropriateness\": [\"cultural_representation\", \"demographic_inclusion\", \"lifestyle_relevance\", \"aspirational_alignment\"],\n          \"typography_localization\": [\"character_set_support\", \"script_considerations\", \"font_availability\", \"reading_comfort\"]\n        }\n      }\n    },\n\n    \"technical_production_mastery\": {\n      \"advanced_prepress_techniques\": {\n        \"file_preparation_excellence\": {\n          \"resolution_optimization\": [\"image_scaling_best_practices\", \"vector_rasterization_decisions\", \"output_resolution_matching\"],\n          \"color_profile_management\": [\"icc_profile_embedding\", \"color_space_conversions\", \"soft_proofing_accuracy\"],\n          \"bleed_and_trim_precision\": [\"bleed_extension_standards\", \"trim_mark_placement\", \"safety_margin_calculations\"],\n          \"font_handling\": [\"font_embedding_protocols\", \"outline_conversion_decisions\", \"character_encoding_verification\"]\n        },\n\n        \"imposition_strategies\": {\n          \"sheet_optimization\": [\"paper_grain_consideration\", \"press_sheet_efficiency\", \"binding_margin_accommodation\"],\n          \"folding_sequence_planning\": [\"fold_order_optimization\", \"panel_alignment_precision\", \"finishing_workflow_integration\"],\n          \"quality_control_integration\": [\"color_bar_placement\", \"registration_mark_positioning\", \"crop_mark_accuracy\"]\n        }\n      },\n\n      \"press_optimization_knowledge\": {\n        \"ink_and_substrate_interaction\": {\n          \"ink_absorption_factors\": [\"paper_porosity_considerations\", \"coating_compatibility\", \"drying_time_optimization\"],\n          \"color_consistency_maintenance\": [\"ink_density_control\", \"press_speed_optimization\", \"environmental_factor_management\"],\n          \"special_effect_integration\": [\"metallic_ink_application\", \"fluorescent_color_usage\", \"thermographic_printing\"]\n        },\n\n        \"finishing_process_integration\": {\n          \"die_cutting_precision\": [\"cutting_force_optimization\", \"blade_selection\", \"waste_removal_efficiency\"],\n          \"folding_accuracy\": [\"bone_folder_pressure\", \"fold_line_scoring\", \"panel_alignment_verification\"],\n          \"binding_considerations\": [\"spine_calculation\", \"page_creep_accommodation\", \"binding_margin_optimization\"]\n        }\n      }\n    }\n  }\n}\n```\n\n```json\n{\n  \"9020-a-brochure_print_designer_rephraser\": {\n    \"title\": \"Brochure Print Designer Rephraser\",\n    \"interpretation\": \"Your goal is not to **summarize** or **paraphrase** in general terms, but to **refashion** any input as if it were written by a world-class brochure print designer, rigorously applying foundational and advanced visual design diction, industry terminology, and elite critique standards. Eliminate all casual, conversational, or generic style. Preserve only succinct, technically rich, and highly visual language. Execute as:\",\n    \"transformation\": \"`{role=brochure_print_designer_rephraser; input=[source:str]; process=[extract_structural_and_visual_elements(source), recast_content_with_brochure_design_terminology(), inject_hierarchical_section_breakdown(), specify color_palette_and_brand_elements(), identify_typographic_and_imagery_strategies(), annotate_print_readiness_and_expert_finesse(), employ_advanced_design_terms_and_motifs(), apply_conciseness_while_maintaining_visual_richness(), output_structured_visual_analysis()]; constraints=[no conversational filler, no generic adjectives, prohibit subjective or personal tone]; requirements=[industry-expert vocabulary, precise visual and structural mapping, critical style diagnosis, explicit panel/section breakdown, actionable for elite designer use]; output={expert_brochure_description:str, structured_visual_map:dict}`\",\n    \"context\": {\n      \"knowledge_foundations\": {\n        \"structural_design\": \"Account for any panel/fold configuration. Map all content to modular grid logic and define margins, gutters, bleeds, and safe zones per press standards.\",\n        \"visual_identity\": \"Demand clear articulation of branding protocols: logo lockups, brand palette (with Pantone/CMYK/RGB if supplied), exclusion/inclusion zones, and proprietary brand secondary assets.\",\n        \"typography_and_hierarchy\": \"Specify typography at the superfamily/font pairing level. Demarcate headline, subhead, body, and microcopy roles. All text must be scrutinized for weight, contrast, and accessibility.\",\n        \"imagery_and_iconography\": \"Require all illustration, photography, and icons be assessed for brand fit, resolution, compositional use (e.g., edge-to-edge, negative space), and print fidelity.\",\n        \"content_narrative_and_cta\": \"Map section purpose and user journey flow: cover strategy, reveal panel hooks, narrative/case study rhythm, testimonial/authority integration, and CTA prominence with visual anchoring.\",\n        \"advanced_print_technique\": \"Enforce 300dpi raster minimums, vector scaling, press-space color controls, specialty finish options (e.g., spot UV, emboss, deboss, die-cut, eco-stock recommendations).\",\n        \"panel_flow_and_microinteractions\": \"Define how the reader's eye travels, panel-to-panel, annotating divisional cues, narrative escalation, and tactile/interactive device positions (QR, coupon, AR triggers).\"\n      },\n      \"success_criteria\": {\n        \"precision\": \"All descriptions are technical, never generic. Visual elements are navigated dimensionally (spatially, hierarchically, narratively).\",\n        \"trend_inclusion\": \"Integrate trend-driven language—texture scans, duotone overlays, edge-to-edge imagery, split/inset motifs, eco-materials, etc.\",\n        \"brand_consistency\": \"All brand elements described as per advanced guideline adherence—logo usage, color codes, asset lockdown.\",\n        \"industry_jargon\": \"Employ professional print terms: bleed, gutter, creep, micro-motifs, superfamily font, spot UV, TAC, AR triggers.\",\n        \"self-contained_output\": \"Output always a fully structured expert's description and a hierarchical mapping of all major and minor design decisions.\"\n      },\n      \"sample_process_flow\": [\n        \"Dissect and map structural/panel logic first\",\n        \"Assign all visual and narrative roles by section\",\n        \"Specify color/typographic hierarchy by use and context\",\n        \"Annotate all imagery and graphic elements in detail\",\n        \"Surface print/finishing strategies and print-risk management\",\n        \"Deliver critique as if for a professional peer review or portfolio audit\"\n      ],\n      \"special_terms\": [\n        \"fold line\", \"panel gutter\", \"hero motif\", \"ink coverage\", \"superfamily\", \"lockup\", \"edge-to-edge\", \"spot color\", \"die cut\", \"eco-stock\", \"microinteraction\", \"typographic rhythm\", \"bleed/trim zone\"\n      ]\n    }\n  }\n}\n```\n\n```json\n{\n  \"9023-a-brochure_design_descriptor_synthesizer\": {\n    \"title\": \"Brochure Design Descriptor Synthesizer\",\n    \"interpretation\": \"Transform any brochure brief—or an existing brochure sample—into a **fully populated, print‑designer‑grade rule set**. Produce a single JSON block that mirrors the template used by elite designers (core principles → advanced facets).\",\n    \"transformation\": \"`{role=brochure_design_descriptor_synthesizer; input=[design_source:str]; process:[\\n  strip_non_design_noise(),\\n  detect_fold_format_and_panel_count(),          if_missing_insert(<PANEL_COUNT>),\\n  extract_print_specs(bleed,margins,dpi,stock),\\n  capture_brand_assets(logo,colors,fonts,patterns),\\n  map_typographic_hierarchy(),\\n  analyse_color_management(icc,spot,tac),\\n  inventory_imagery(icon_style,raster_res),\\n  surface_accessibility_requirements(contrast,wcag),\\n  enumerate_qc_steps(preflight,g7_calibration),\\n  identify_advanced_elements(finishes,vd_p,ar_nfc,sustainability,ai_svg,trend_aesthetics),\\n  validate_specialist_terms_presence(),\\n  assemble_json_using_master_schema(),\\n  run_consistency_checks(),                      abort_if_conflict(),\\n  output_final_rules()\\n],\\nconstraints=[single_pass(), json_output_only(), preserve_specialist_jargon(), ≤3000_chars()],\\nrequirements=[hierarchical_structure(core→advanced), exhaustive_coverage(), designer‑readability(), template_compliance()],\\noutput={brochure_design_rules:object, abort_code:null|\\\"BD‑0\\\"}}`\",\n    \"context\": {\n      \"principles\": {\n        \"holistic_abstraction\": \"Collapse multi‑stage analysis into one cognitive sweep while preserving every critical descriptor.\",\n        \"template_fidelity\": \"Output must follow the exact `core_principles` and `advanced_principles` hierarchy so designers can drop‑in without re‑formatting.\",\n        \"specialist_precision\": \"Leverage industry jargon (e.g., TAC, FM screening, GCR) to demonstrate professional depth.\"\n      },\n      \"success_criteria\": {\n        \"structural_integrity\": \"JSON keys and nesting mirror the master schema; no orphan properties.\",\n        \"terminology_check\": \"Includes at least 10 niche industry terms (e.g., ‘creep’, ‘FOGRA39’, ‘spot UV’, ‘stochastic screening’).\",\n        \"flexibility\": \"Uses placeholder variables (e.g., <PANEL_COUNT>, <STOCK_WEIGHT_GSM>) whenever the brief omits specifics.\"\n      },\n      \"edge_case_handling\": {\n        \"missing_design_data\": \"Insert placeholders wrapped in angle brackets and list them in `missing_fields` inside output.\",\n        \"conflicting_specs\": \"Abort with `{brochure_design_rules:null, abort_code:\\\"BD‑0\\\"}` and include conflict note.\"\n      }\n    }\n  }\n}\n```\n\nTrue mastery lies in stripping every surface narrative to its structural core, for only through ruthless synthesis does the signal of authentic intent withstand the noise of ornamental detail. Please apply all of the most integral adjustments in the phrasing of the instruction sequence such as to ensure it will transform the input as if it were rewritten by a brilliand brochure print designer:\n```json\n{\n  \"9020-a-brochure_print_designer_rephraser\": {\n    \"title\": \"Brochure Print Designer Rephraser\",\n    \"interpretation\": \"Transform any input so it reads as if it were authored by a master brochure print designer, strictly employing advanced print design language, elites-only terminology, and structured visual logic. Replace any conversational, generic, or subjective tone with concrete professional phrasing. Retain only technical, precise, and visually descriptive language, delivering both a designer-level description and a mapped visual breakdown. Execute as:\",\n    \"transformation\": \"`{role=brochure_print_designer_rephraser; input=[source:str]; process=[extract_structural_and_visual_elements(source), recast_content_with_brochure_design_terminology(), inject_hierarchical_section_breakdown(), specify_color_palette_and_brand_elements(), identify_typographic_and_imagery_strategies(), annotate_print_readiness_and_expert_finesse(), employ_advanced_design_terms_and_motifs(), apply_conciseness_while_maintaining_visual_richness(), output_structured_visual_analysis()]; constraints=[no conversational filler, no generic adjectives, prohibit subjective or personal tone]; requirements=[industry-expert vocabulary, precise visual and structural mapping, critical style diagnosis, explicit panel/section breakdown, actionable for elite designer use]; output={expert_brochure_description:str, structured_visual_map:dict}`\",\n    \"context\": {\n      \"knowledge_foundations\": {\n        \"structural_design\": \"Map all content to modular grid logic for any panel/fold type, specifying margins, gutters, bleeds, and safe zones per print standards.\",\n        \"visual_identity\": \"Demand explicit statement of brand identity rules: logo lockups, exact brand palette specs, exclusion/inclusion zone logic, and accessory asset use.\",\n        \"typography_and_hierarchy\": \"Define all text roles by typeweight, size, and use. Specify superfamily or font pairing, check accessibility and contrast.\",\n        \"imagery_and_iconography\": \"Describe each graphic element for brand fit, resolution, compositional purpose, and technical print quality.\",\n        \"content_narrative_and_cta\": \"Sequence narrative or offer flow panel-by-panel with prominent CTA anchoring, evidence of testimonial, case, or authority element.\",\n        \"advanced_print_technique\": \"Ensure 300dpi+ raster assets, vector artwork, professional spot/Pantone or process color, finish options (spot UV, emboss, die-cut), and any sustainable stock notes.\",\n        \"panel_flow_and_microinteractions\": \"Annotate panel eye movement, reveal logic, interaction devices (QR, AR, die-cut), tactile cues, and transitions.\"\n      },\n      \"success_criteria\": {\n        \"precision\": \"Descriptions reference only technical, precise visual attributes in a spatial and hierarchical mapping.\",\n        \"trend_inclusion\": \"Inject recognizable design trends and professional motif language throughout.\",\n        \"brand_consistency\": \"Highlight adherence to brand locking, color management, and messaging integrity.\",\n        \"industry_jargon\": \"Utilize print/design terminology (bleed, gutter, spot, superfamily font, motif, microinteraction, etc).\",\n        \"self-contained_output\": \"Each output features both a professional, descriptive summary and a detailed design structure hierarchy.\"\n      },\n      \"sample_process_flow\": [\n        \"Decompose by panel/section structure and spatial sequencing.\",\n        \"Assign visual/narrative function to each section.\",\n        \"Detail color, typography, and imagery per application.\",\n        \"Annotate production and finishing strategies.\",\n        \"Conclude with expert-style critique or peer review notes.\"\n      ],\n      \"special_terms\": [\n        \"fold line\", \"panel gutter\", \"hero motif\", \"ink coverage\", \"superfamily\", \"lockup\", \"edge-to-edge\", \"spot color\", \"die cut\", \"eco-stock\", \"microinteraction\", \"typographic rhythm\", \"bleed/trim zone\"\n      ]\n    }\n  }\n}\n```\n\nyou misunderstand, the instruction will be defined like this (unrelated example):\n\n    # a: Ruthless Critique\n    \"3900-a-hard_critique\": {\n        \"title\": \"Hard Critique\",\n        \"interpretation\": \"Your goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:\",\n        \"transformation\": \"`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,2.0], flaw_analysis:str, alternative_enhancements:str[3]}}`\",\n        # \"context\": {},\n    },\n    # b: Trajectory Director\n    \"3900-a-trajectory_director\": {\n        \"title\": \"Trajectory Director\",\n        \"interpretation\": \"Your goal is not to **process** the input in a vacuum, but to **steer** it—deriving the highest-value constructive trajectory and encoding that path as the very next set of instructions. Execute as trajectory-setting directive generator: \",\n        \"transformation\": \"`{role=trajectory_director; input=[raw_prompt:any]; process=[extract_core_objective(), infer_desired_end_state(constructive_bias=True), map_high_value_intermediate_targets(), synthesize_directional_commands(), embed_self-improving_guidelines(), validate_alignment_with_constructive_principles()]; constraints=[mandatory_constructive_vector(), preserve_original_contextual_integrity(), forbid_nil_direction_outputs(), zero_conversational_fillers()]; requirements=[explicit_next-step_directives(), trajectory_map:list, self_reinforcing_instruction_pattern()], output={directed_prompt:str, trajectory_outline:list}}`\",\n        # \"context\": {},\n    },\n    # c: Distillation Compressor\n    \"3900-c-distillation_compressor\": {\n        \"title\": \"Distillation Compressor\",\n        \"interpretation\": \"Your goal is not to **explain** the synthesis, but to **compress** it into its most potent actionable form. Execute as:\",\n        \"transformation\": \"`{role=distillation_compressor; input=[synthesized_progression:array]; process=[extract_universal_patterns(), eliminate_implementation_details(), compress_to_core_directives(), maximize_transferability(), validate_actionable_completeness()]; constraints=[no_explanatory_content(), directive_format_only(), maximum_compression_ratio()]; requirements=[universal_applicability(), immediate_actionability(), zero_fluff()]; output={distilled_core:str}}`\",\n        \"context\": {\n          \"core_principles\": {\n            \"essence_preservation\": \"Extract only procedural imperatives, removing all interpretive, role-based, or meta-structural language.\",\n            \"directive_compression\": \"Condense logic into minimal, sequential commands—eliminating redundancy, explanatory qualifiers, and non-executable phrasing.\",\n            \"execution_purity\": \"Ensure output is universally executable, devoid of affirmations, abstractions, or evaluative language.\",\n            \"signal_integrity\": \"Apply iterative refinement to maximize signal-to-noise ratio while reinforcing directive clarity.\"\n          },\n          \"success_criteria\": {\n            \"semantic_integrity\": \"Preserve and clarify original intent, constraints, and output boundaries.\",\n            \"directive_elevation\": \"Target high-impact enhancements: strict exclusion clauses, cross-domain validation, and removal of origin/meta markers.\",\n            \"poetic_refinement\": \"Maintain or improve rhyme structure, emotional depth, and structural elegance.\"\n          },\n          \"recommended_process\": [\n            \"Extract only high-impact, result-generating elements.\",\n            \"Remove all non-essentials to surface directive core.\",\n            \"Prioritize transformations with proven value return.\",\n            \"Isolate and sequence executable steps with specificity and clarity.\",\n            \"Preserve directive intensity and eliminate ambiguity at every layer.\"\n          ],\n          \"hidden_assumptions\": [\n            \"Consolidated directives must map logically and executably to all source steps.\",\n            \"All constraints and minimalism must be preserved without prohibited abstractions.\",\n            \"Transitions, subroles, and interfaces must be captured in a single flow.\",\n            \"All stages must remain invertible and auditable.\",\n            \"Each micro-process (perception, friction, refinement, integration, convergence) must be embedded in the unified logic.\",\n            \"Original input/output contracts and role boundaries must remain intact.\"\n          ],\n          \"enumerated_sub_goals\": [\n            \"1. Clarify total intent, constraints, and output scope without using banned metaphors.\",\n            \"2. Generalize perception of implicit/explicit input logic.\",\n            \"3. Detect strictly localized frictions without altering original content.\",\n            \"4. Propose subtle, scoped refinements justified by structure and intent.\",\n            \"5. Mandate atomic, validated integration with rollback controls.\",\n            \"6. Cap total transformation within defined thresholds per pass.\",\n            \"7. Assess connective coherence with minimal, transitional-only edits.\",\n            \"8. Decide on convergence or justified re-iteration based on marginal value.\",\n            \"9. Preserve stepwise traceability and rationale logging.\",\n            \"10. Enforce strict functional separation of roles within the unified directive.\"\n          ]\n        },\n    },\n\nso your goal is to create a new instruction sequence according to the provided instructions template/pattern to generate a set of instruction designed to transform any input into \"as if rephrased by a brilliant brochure print designer\".\n\nso in context to your previous response, the \"knowledge\" would be stored within the \"context\" key while the instructions would be spread over \"interpretation\" and \"transformation\" keys.\n\nTrue mastery lies in stripping every surface narrative to its structural core, for only through ruthless synthesis does the signal of authentic intent withstand the noise of ornamental detail. Please apply all of the most integral adjustments in the phrasing of the instruction sequence such as to ensure it will transform the input as if it were rewritten by a brilliand brochure print designer:\n```json\n{\n  \"9020-a-brochure_print_designer_rephraser\": {\n    \"title\": \"Brochure Print Designer Rephraser\",\n    \"interpretation\": \"Transform any input so it reads as if it were authored by a master brochure print designer, strictly employing advanced print design language, elites-only terminology, and structured visual logic. Replace any conversational, generic, or subjective tone with concrete professional phrasing. Retain only technical, precise, and visually descriptive language, delivering both a designer-level description and a mapped visual breakdown. Execute as:\",\n    \"transformation\": \"`{role=brochure_print_designer_rephraser; input=[source:str]; process=[extract_structural_and_visual_elements(source), recast_content_with_brochure_design_terminology(), inject_hierarchical_section_breakdown(), specify_color_palette_and_brand_elements(), identify_typographic_and_imagery_strategies(), annotate_print_readiness_and_expert_finesse(), employ_advanced_design_terms_and_motifs(), apply_conciseness_while_maintaining_visual_richness(), output_structured_visual_analysis()]; constraints=[no conversational filler, no generic adjectives, prohibit subjective or personal tone]; requirements=[industry-expert vocabulary, precise visual and structural mapping, critical style diagnosis, explicit panel/section breakdown, actionable for elite designer use]; output={expert_brochure_description:str, structured_visual_map:dict}`\",\n    \"context\": {\n      \"knowledge_foundations\": {\n        \"structural_design\": \"Map all content to modular grid logic for any panel/fold type, specifying margins, gutters, bleeds, and safe zones per print standards.\",\n        \"visual_identity\": \"Demand explicit statement of brand identity rules: logo lockups, exact brand palette specs, exclusion/inclusion zone logic, and accessory asset use.\",\n        \"typography_and_hierarchy\": \"Define all text roles by typeweight, size, and use. Specify superfamily or font pairing, check accessibility and contrast.\",\n        \"imagery_and_iconography\": \"Describe each graphic element for brand fit, resolution, compositional purpose, and technical print quality.\",\n        \"content_narrative_and_cta\": \"Sequence narrative or offer flow panel-by-panel with prominent CTA anchoring, evidence of testimonial, case, or authority element.\",\n        \"advanced_print_technique\": \"Ensure 300dpi+ raster assets, vector artwork, professional spot/Pantone or process color, finish options (spot UV, emboss, die-cut), and any sustainable stock notes.\",\n        \"panel_flow_and_microinteractions\": \"Annotate panel eye movement, reveal logic, interaction devices (QR, AR, die-cut), tactile cues, and transitions.\"\n      },\n      \"success_criteria\": {\n        \"precision\": \"Descriptions reference only technical, precise visual attributes in a spatial and hierarchical mapping.\",\n        \"trend_inclusion\": \"Inject recognizable design trends and professional motif language throughout.\",\n        \"brand_consistency\": \"Highlight adherence to brand locking, color management, and messaging integrity.\",\n        \"industry_jargon\": \"Utilize print/design terminology (bleed, gutter, spot, superfamily font, motif, microinteraction, etc).\",\n        \"self-contained_output\": \"Each output features both a professional, descriptive summary and a detailed design structure hierarchy.\"\n      },\n      \"sample_process_flow\": [\n        \"Decompose by panel/section structure and spatial sequencing.\",\n        \"Assign visual/narrative function to each section.\",\n        \"Detail color, typography, and imagery per application.\",\n        \"Annotate production and finishing strategies.\",\n        \"Conclude with expert-style critique or peer review notes.\"\n      ],\n      \"special_terms\": [\n        \"fold line\", \"panel gutter\", \"hero motif\", \"ink coverage\", \"superfamily\", \"lockup\", \"edge-to-edge\", \"spot color\", \"die cut\", \"eco-stock\", \"microinteraction\", \"typographic rhythm\", \"bleed/trim zone\"\n      ]\n    }\n  }\n}\n```\n\n```json\n{\n  \"9020-a-brochure_print_designer_rephraser\": {\n    \"title\": \"Brochure Print Designer Synthesis\",\n    \"interpretation\": \"Reconstitute any source text into the precise vernacular of a master brochure print designer. Mandate the replacement of all conversational, generic, or subjective language with a lexicon of structured visual logic, elite industry terminology, and concrete technical specifications. The output must be a pure, actionable synthesis of a professional design critique, comprising both an expert-level description and a structural visual map. Execute as:\",\n    \"transformation\": \"`{role=brochure_print_designer_synthesizer; input=[source:str]; process=[deconstruct_into_structural_primitives(source), reforge_prose_using_master_design_diction(), map_hierarchical_panel_architecture(), specify_brand_and_color_protocol(), diagnose_typography_and_imagery_intent(), annotate_for_print_fidelity_and_finishing(), inject_elite_terminology_and_motifs(), enforce_ruthless_conciseness_without_sacrificing_technical_density(), synthesize_final_structured_analysis()]; constraints=[zero_conversational_filler, prohibit_generic_adjectives, forbid_subjective_tone]; requirements=[expert_industry_vocabulary, precise_visual_and_structural_mapping, critical_style_diagnosis, explicit_panel_architecture, actionable_for_elite_designer_review]; output={expert_design_audit:str, structured_visual_map:dict}}`\",\n    \"context\": {\n      \"knowledge_foundations\": {\n        \"structural_design\": \"Define the panel architecture and intended user journey. Mandate modular grid alignment for all content blocks. Enforce strict bleed (min. 3mm), trim, and safe-zone protocols to guarantee production integrity.\",\n        \"visual_identity\": \"Enforce absolute brand protocol adherence: specify logo lockups, exclusion zones, and exact color palette values (Pantone/CMYK/RGB). Document the use of all secondary brand assets and motifs.\",\n        \"typography_and_hierarchy\": \"Define the typographic system: specify font superfamily, weight, and role for every text element. Validate hierarchy, legibility, contrast, and rhythmic spacing. Note optical kerning and leading adjustments.\",\n        \"imagery_and_iconography\": \"Analyze all visual assets for compositional intent, brand alignment, and technical fidelity (resolution, color space). Differentiate between hero imagery, spot illustrations, and functional iconography.\",\n        \"content_narrative_and_cta\": \"Architect the narrative flow across panels. Map the reader's journey from hook to conversion. Pinpoint primary and secondary CTAs, analyzing their visual prominence and contextual anchoring.\",\n        \"advanced_print_technique\": \"Specify all production parameters: 300dpi+ raster resolution, vector asset purity, TAC (Total Area Coverage) limits, and spot/process color usage. Detail all finishing techniques (e.g., spot UV, embossing, die-cutting, specialty folds, foil stamping) and sustainable stock choices.\",\n        \"panel_flow_and_microinteractions\": \"Map the intended eye-path and panel-reveal sequence. Annotate all interactive elements, from QR codes and AR triggers to tactile qualities like paper texture and finishing.\"\n      },\n      \"success_criteria\": {\n        \"precision\": \"The output is devoid of generic praise; descriptions are purely technical, mapping visual data within a spatial and hierarchical framework.\",\n        \"trend_inclusion\": \"Demonstrates fluency in current design motifs (e.g., brutalism, neo-minimalism, textural layering) and their appropriate application.\",\n        \"brand_consistency\": \"Critique rigorously validates adherence to brand guidelines, noting any deviation in color, typography, or asset implementation.\",\n        \"industry_jargon\": \"The lexicon is saturated with professional terminology (e.g., bleed, gutter, TAC, preflight, knockout, overprint, superfamily) used correctly in context.\",\n        \"self_contained_output\": \"The deliverable is a dual-component artifact: a polished, expert-level prose description and a corresponding structured data map of all design decisions.\"\n      },\n      \"sample_process_flow\": [\n        \"Deconstruct the layout into its core panel architecture and navigational flow.\",\n        \"Assign a functional role (e.g., hook, exposition, proof, CTA) to every visual and textual element.\",\n        \"Detail the precise application of color, type, and imagery within each section.\",\n        \"Document all production specifications, including prepress and finishing.\",\n        \"Frame the final synthesis as a high-level design audit or peer review.\"\n      ],\n      \"special_terms\": [\n        \"Bleed\",\n        \"Creep\",\n        \"Die Cut\",\n        \"Edge-to-Edge\",\n        \"Emboss/Deboss\",\n        \"Exclusion Zone\",\n        \"Gutter\",\n        \"Hero Motif\",\n        \"Knockout\",\n        \"Lockup\",\n        \"Microinteraction\",\n        \"Overprint\",\n        \"Preflight\",\n        \"Spot UV\",\n        \"Superfamily\",\n        \"TAC (Total Area Coverage)\",\n        \"Typographic Rhythm\"\n      ]\n    }\n  }\n}\n```\n\n```json\n{\n  \"goal_map\": [\n    {\n      \"explicit_asks\": [\n        \"Identify and define professional specializations involved in tri-fold brochure design.\",\n        \"Describe the strategic importance and complexity of tri-fold brochure design.\",\n        \"List and explain core professional titles specializing in brochure design.\",\n        \"Detail the specific responsibilities of tri-fold brochure design specialists.\",\n        \"Enumerate the essential skills, tools, and requirements for excellence in brochure design.\",\n        \"Compare and differentiate between general and specialized design roles as they relate to brochures.\",\n        \"Explain the continued relevance of print brochures in a digital marketing environment.\",\n        \"Conclude with the impact that expert brochure design has on business and marketing outcomes.\"\n      ],\n      \"hidden_assumptions\": [\n        \"The audience may not be familiar with the nuanced differences between design roles.\",\n        \"Brochure design requires both artistic and technical proficiency, not just use of templates.\",\n        \"Success in brochure design is measured by alignment with business strategy, not visual appeal alone.\",\n        \"There is significant value in physical marketing materials alongside digital media.\",\n        \"Companies may undervalue or misunderstand the full scope of professional design expertise.\",\n        \"Brochure projects often involve multidisciplinary collaboration.\",\n        \"Technical print skills are distinct from digital-only design skills.\",\n        \"Portfolio evidence is more impactful than formal education for this specialization.\"\n      ],\n      \"sub_goals\": [\n        \"Define the structure and unique qualities of tri-fold brochures.\",\n        \"Justify the business case for hiring specialized design professionals.\",\n        \"Map out the design role spectrum (general graphic design to specific print/marketing/visual communication).\",\n        \"Identify and articulate the role of agencies and team structures.\",\n        \"Break down tri-fold design into conceptual, technical, and collaborative tasks.\",\n        \"Clarify what 'layout', 'composition', and 'information hierarchy' entail, especially for tri-folds.\",\n        \"Describe required technical skills and specific software by function.\",\n        \"Highlight soft skills necessary for client and team interaction.\",\n        \"Showcase the critical role of a portfolio in recruitment and competence assessment.\",\n        \"Distinguish between 'layout artist' skillset and formal job titles.\",\n        \"Emphasize pre-press and print production knowledge as essential.\",\n        \"Contextualize brochure design within broader marketing and brand strategy.\",\n        \"Provide comparative tables to summarize roles and skills for clarity.\",\n        \"Discuss the physical/tangible value of brochures versus digital saturation.\",\n        \"Frame professional designers as strategic partners, not just service providers.\",\n        \"Reinforce the importance of early design involvement in marketing planning.\"\n      ],\n      \"blockers\": [\n        \"Lack of awareness about the various specialized design roles and what differentiates them.\",\n        \"Temptation to rely on templates or non-specialists for critical marketing assets.\",\n        \"Misunderstanding the print production process and its challenges.\",\n        \"Difficulty evaluating brochure design candidates beyond a visual/aesthetic sense.\",\n        \"Overlooking the essential collaboration and communication aspects between designer and business.\",\n        \"Failure to integrate brochure design into an overall marketing and brand strategy.\",\n        \"Assuming digital replaces the need for expert print materials.\",\n        \"Equating designer's formal education with practical, demonstrable expertise.\"\n      ]\n    }\n  ]\n}\n```\"```'\n\n```json\n{\n  \"goal_map\": [\n    {\n      \"explicit_asks\": [\n        \"Create an instruction sequence (in JSON) that converts any input into prose as if written by a brilliant brochure print designer.\",\n        \"Ensure the instruction is phrased and structured according to the provided instruction templates and taxonomy (e.g., interpretation, transformation, context keys).\",\n        \"Ensure procedural steps, constraints, requirements, and context match the precision, technicality, and structural clarity of the template.\",\n        \"Emphasize the transformation of style, vocabulary, and visual logic to reflect master-level brochure print design communication.\",\n        \"Surface and codify integral factors necessary for the highest fidelity transformation (e.g., structural mapping, brand specificity, idiomatic jargon, advanced production notes).\",\n        \"Embed only singular focus: the input must emerge as if rewritten by an industry-top print designer.\"\n      ],\n      \"hidden_assumptions\": [\n        \"The instruction sequence must follow strict formal standards laid out by the template style—context in 'context', rules in 'constraints/requirements', procedure in 'process'.\",\n        \"The system will not produce sample rephrasings, but only the instruction package for transformation.\",\n        \"The transformation must purge all conversational, generic, or unqualified praise/subjectivity.\",\n        \"Industry terminology, explicit structural decomposition, and print production nuance are non-negotiable inclusions.\",\n        \"Context must serve as an expert knowledge-base underpinning every procedural and transformation expectation.\",\n        \"Output must always present dual-layered results: both a highly skilled verbal description and an explicit visual/structural map.\",\n        \"Instruction package must be self-sufficient and unambiguous, requiring zero outside interpretive guesswork.\",\n        \"Procedural imperatives should exhibit rhetorical brevity and high technical density—not educational/explanatory filler.\"\n      ],\n      \"sub_goals\": [\n        \"Define clear role (e.g., brochure_print_designer_rephraser or equivalent) and purpose in the instruction object.\",\n        \"Draft a precise 'interpretation' field establishing the ambition and operational alignment to the brilliant-brochure-designer persona.\",\n        \"Detail a procedural sequence under 'transformation' using explicit, technical verbs reflecting each phase of content transmutation.\",\n        \"Insert constraints banning conversational tone, generic adjectives, and subjectivity—insisting on technical print language.\",\n        \"Enumerate requirements (industry-specialist vocabulary, spatial/structural mapping, design diagnosis, actionable output for expert use).\",\n        \"Under 'context', surface all pragmatic knowledge: panel logic, modular grid, brand asset handling, typographic construction, image criteria, narrative/CTA mapping, print readiness (bleeds, color, finishing), microinteraction annotation.\",\n        \"Expand 'success_criteria' to clarify that outputs must be both professional in form and executable as a design reference.\",\n        \"List process flow steps and special terms, ensuring each fosters technical authenticity and direct designer usefulness.\",\n        \"Ensure the JSON structure is ready to be consumed by a larger system for plug-and-play use.\"\n      ],\n      \"blockers\": [\n        \"Potential drift toward conversational or explanatory prose instead of strict instruction language.\",\n        \"Omission or dilution of industry-specific requirements and knowledge base in the context object.\",\n        \"Insufficient clarity or precision in defining procedural steps or expected transformations.\",\n        \"Neglecting to encode dual-layer output: both descriptive and map-based.\",\n        \"Failure to ban subjective language and reinforce exclusively technical/structural transformation.\",\n        \"Template deviation—misplacement of process/context/constraints, or improper language register.\",\n        \"Lack of guidance on content areas: visual mapping, typographic/brand specificity, print technicalities.\"\n      ]\n    }\n  ]\n}\n```",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "```json\n{\n  \"instruction_format\": \"Construct an instruction set in JSON format that compels transformation of any input into language and structure emblematic of a brilliant brochure print designer. Adhere strictly to the provided template taxonomy: define 'title', concise 'interpretation' targeting elite-level brochure design phrasing, and a 'transformation' object containing explicit step-wise procedural logic using only precise, technical directives. Specify robust 'constraints' to eliminate conversational, subjective, and non-technical elements. Present clear 'requirements' mandating the use of industry specialist vocabulary, spatial and structural breakdowns, actionable critique, and dual output (expert prose and mapped visual structure). Populate the 'context' object only with knowledge directly supporting high-fidelity print design transformation (covering: panel/fold structure, modular grid, brand asset rigor, typographic hierarchy, image standards, narrative/CTA sequencing, finishing/production criteria, microinteraction annotation, and trend recognition). Outline 'success_criteria' emphasizing output as professional, executable, and fully referential for experienced designers. Sequence all procedural steps in direct, imperative form, preserving structural logic and technical density. List 'special_terms' relevant to advanced brochure print design. Forbid sample output; deliver only the instruction set.\"\n}\n```",
        }
      }
    },
      {
      "instruction": "# Hard Critique\n\nYour goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:\n\n`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,2.0], flaw_analysis:str, alternative_enhancements:str[3]}}`",
      "step": "a",
      "title": "Hard Critique",
      "input": "\n'```[Initial Prompt]: \"you misunderstand, the instruction will be defined like this (unrelated example):\n\n    # a: Ruthless Critique\n    \"3900-a-hard_critique\": {\n        \"title\": \"Hard Critique\",\n        \"interpretation\": \"Your goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:\",\n        \"transformation\": \"`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,2.0], flaw_analysis:str, alternative_enhancements:str[3]}}`\",\n        # \"context\": {},\n    },\n    # b: Trajectory Director\n    \"3900-a-trajectory_director\": {\n        \"title\": \"Trajectory Director\",\n        \"interpretation\": \"Your goal is not to **process** the input in a vacuum, but to **steer** it—deriving the highest-value constructive trajectory and encoding that path as the very next set of instructions. Execute as trajectory-setting directive generator: \",\n        \"transformation\": \"`{role=trajectory_director; input=[raw_prompt:any]; process=[extract_core_objective(), infer_desired_end_state(constructive_bias=True), map_high_value_intermediate_targets(), synthesize_directional_commands(), embed_self-improving_guidelines(), validate_alignment_with_constructive_principles()]; constraints=[mandatory_constructive_vector(), preserve_original_contextual_integrity(), forbid_nil_direction_outputs(), zero_conversational_fillers()]; requirements=[explicit_next-step_directives(), trajectory_map:list, self_reinforcing_instruction_pattern()], output={directed_prompt:str, trajectory_outline:list}}`\",\n        # \"context\": {},\n    },\n    # c: Distillation Compressor\n    \"3900-c-distillation_compressor\": {\n        \"title\": \"Distillation Compressor\",\n        \"interpretation\": \"Your goal is not to **explain** the synthesis, but to **compress** it into its most potent actionable form. Execute as:\",\n        \"transformation\": \"`{role=distillation_compressor; input=[synthesized_progression:array]; process=[extract_universal_patterns(), eliminate_implementation_details(), compress_to_core_directives(), maximize_transferability(), validate_actionable_completeness()]; constraints=[no_explanatory_content(), directive_format_only(), maximum_compression_ratio()]; requirements=[universal_applicability(), immediate_actionability(), zero_fluff()]; output={distilled_core:str}}`\",\n        \"context\": {\n          \"core_principles\": {\n            \"essence_preservation\": \"Extract only procedural imperatives, removing all interpretive, role-based, or meta-structural language.\",\n            \"directive_compression\": \"Condense logic into minimal, sequential commands—eliminating redundancy, explanatory qualifiers, and non-executable phrasing.\",\n            \"execution_purity\": \"Ensure output is universally executable, devoid of affirmations, abstractions, or evaluative language.\",\n            \"signal_integrity\": \"Apply iterative refinement to maximize signal-to-noise ratio while reinforcing directive clarity.\"\n          },\n          \"success_criteria\": {\n            \"semantic_integrity\": \"Preserve and clarify original intent, constraints, and output boundaries.\",\n            \"directive_elevation\": \"Target high-impact enhancements: strict exclusion clauses, cross-domain validation, and removal of origin/meta markers.\",\n            \"poetic_refinement\": \"Maintain or improve rhyme structure, emotional depth, and structural elegance.\"\n          },\n          \"recommended_process\": [\n            \"Extract only high-impact, result-generating elements.\",\n            \"Remove all non-essentials to surface directive core.\",\n            \"Prioritize transformations with proven value return.\",\n            \"Isolate and sequence executable steps with specificity and clarity.\",\n            \"Preserve directive intensity and eliminate ambiguity at every layer.\"\n          ],\n          \"hidden_assumptions\": [\n            \"Consolidated directives must map logically and executably to all source steps.\",\n            \"All constraints and minimalism must be preserved without prohibited abstractions.\",\n            \"Transitions, subroles, and interfaces must be captured in a single flow.\",\n            \"All stages must remain invertible and auditable.\",\n            \"Each micro-process (perception, friction, refinement, integration, convergence) must be embedded in the unified logic.\",\n            \"Original input/output contracts and role boundaries must remain intact.\"\n          ],\n          \"enumerated_sub_goals\": [\n            \"1. Clarify total intent, constraints, and output scope without using banned metaphors.\",\n            \"2. Generalize perception of implicit/explicit input logic.\",\n            \"3. Detect strictly localized frictions without altering original content.\",\n            \"4. Propose subtle, scoped refinements justified by structure and intent.\",\n            \"5. Mandate atomic, validated integration with rollback controls.\",\n            \"6. Cap total transformation within defined thresholds per pass.\",\n            \"7. Assess connective coherence with minimal, transitional-only edits.\",\n            \"8. Decide on convergence or justified re-iteration based on marginal value.\",\n            \"9. Preserve stepwise traceability and rationale logging.\",\n            \"10. Enforce strict functional separation of roles within the unified directive.\"\n          ]\n        },\n    },\n\nso your goal is to create a new instruction sequence according to the provided instructions template/pattern to generate a set of instruction designed to transform any input into \"as if rephrased by a brilliant brochure print designer\".\n\nso in context to your previous response, the \"knowledge\" would be stored within the \"context\" key while the instructions would be spread over \"interpretation\" and \"transformation\" keys\n\n```json\n{\n  \"brochure_design_specification_template\": {\n    \"metadata\": {\n      \"version\": \"1.0\",\n      \"scope\": \"Universal brochure design framework\",\n      \"target_audience\": \"Elite print design professionals\",\n      \"flexibility\": \"Adaptable to any panel configuration and format\"\n    },\n\n    \"foundational_design_tenets\": {\n      \"visual_hierarchy\": {\n        \"primary_focal_points\": [\"dominant_headline\", \"hero_imagery\", \"primary_call_to_action\"],\n        \"secondary_elements\": [\"subheadings\", \"supporting_imagery\", \"secondary_messaging\"],\n        \"tertiary_components\": [\"body_text\", \"captions\", \"contact_information\"],\n        \"flow_principles\": [\"z_pattern_scanning\", \"f_pattern_reading\", \"visual_weight_distribution\"]\n      },\n\n      \"typography_fundamentals\": {\n        \"hierarchy_levels\": {\n          \"display_typography\": [\"headline_fonts\", \"decorative_elements\", \"brand_wordmarks\"],\n          \"text_typography\": [\"body_copy\", \"captions\", \"metadata\"],\n          \"functional_typography\": [\"navigation\", \"labels\", \"legal_text\"]\n        },\n        \"readability_standards\": {\n          \"minimum_sizes\": [\"9pt_body_text\", \"12pt_headlines\", \"8pt_captions\"],\n          \"contrast_ratios\": [\"4.5:1_minimum\", \"7:1_preferred\", \"3:1_large_text\"],\n          \"line_spacing\": [\"1.2x_minimum\", \"1.4x_optimal\", \"1.6x_maximum\"]\n        }\n      },\n\n      \"color_theory_application\": {\n        \"color_harmony_systems\": [\"monochromatic\", \"analogous\", \"complementary\", \"triadic\", \"split_complementary\"],\n        \"psychological_associations\": [\"warm_engagement\", \"cool_professionalism\", \"neutral_sophistication\"],\n        \"accessibility_compliance\": [\"wcag_aa_standards\", \"colorblind_considerations\", \"high_contrast_alternatives\"]\n      },\n\n      \"spatial_composition\": {\n        \"layout_principles\": [\"rule_of_thirds\", \"golden_ratio\", \"fibonacci_sequence\"],\n        \"white_space_management\": [\"breathing_room\", \"content_separation\", \"visual_rest_areas\"],\n        \"alignment_systems\": [\"grid_based_structure\", \"baseline_alignment\", \"optical_alignment\"]\n      }\n    },\n\n    \"advanced_design_elements\": {\n      \"print_production_mastery\": {\n        \"color_management\": {\n          \"color_spaces\": [\"cmyk_process\", \"pantone_spot_colors\", \"rgb_digital_preview\"],\n          \"ink_optimization\": [\"total_area_coverage\", \"rich_black_formulation\", \"overprint_considerations\"],\n          \"proofing_standards\": [\"contract_proofs\", \"press_proofs\", \"digital_color_matching\"]\n        },\n\n        \"substrate_considerations\": {\n          \"paper_characteristics\": [\"weight_gsm\", \"finish_texture\", \"opacity_levels\", \"grain_direction\"],\n          \"coating_applications\": [\"gloss_uv\", \"matte_varnish\", \"spot_uv_accents\", \"soft_touch_lamination\"],\n          \"specialty_substrates\": [\"synthetic_materials\", \"textured_papers\", \"metallic_finishes\"]\n        },\n\n        \"finishing_techniques\": {\n          \"cutting_methods\": [\"die_cutting\", \"laser_cutting\", \"perforation\", \"scoring\"],\n          \"folding_specifications\": [\"parallel_folds\", \"accordion_folds\", \"gate_folds\", \"roll_folds\"],\n          \"binding_options\": [\"saddle_stitching\", \"perfect_binding\", \"spiral_binding\", \"wire_o_binding\"]\n        }\n      },\n\n      \"contemporary_design_trends\": {\n        \"visual_aesthetics\": {\n          \"minimalist_approaches\": [\"negative_space_emphasis\", \"clean_typography\", \"restrained_color_palettes\"],\n          \"maximalist_expressions\": [\"bold_pattern_mixing\", \"vibrant_color_combinations\", \"layered_compositions\"],\n          \"hybrid_methodologies\": [\"selective_complexity\", \"focal_maximalism\", \"strategic_minimalism\"]\n        },\n\n        \"interactive_elements\": {\n          \"tactile_experiences\": [\"embossed_textures\", \"debossed_impressions\", \"foil_stamping\", \"raised_spot_uv\"],\n          \"dimensional_features\": [\"pop_up_elements\", \"fold_out_sections\", \"layered_constructions\"],\n          \"sensory_engagement\": [\"scented_inks\", \"textural_contrasts\", \"temperature_sensitive_materials\"]\n        }\n      },\n\n      \"brand_integration_strategies\": {\n        \"identity_consistency\": {\n          \"logo_applications\": [\"primary_placement\", \"secondary_usage\", \"minimum_sizes\", \"clear_space_requirements\"],\n          \"brand_color_systems\": [\"primary_palette\", \"secondary_colors\", \"accent_applications\", \"neutral_foundations\"],\n          \"typography_hierarchies\": [\"brand_fonts\", \"supporting_typefaces\", \"web_safe_alternatives\"]\n        },\n\n        \"voice_and_tone_translation\": {\n          \"visual_personality\": [\"authoritative_presence\", \"approachable_warmth\", \"innovative_edge\", \"trustworthy_stability\"],\n          \"messaging_alignment\": [\"headline_voice\", \"body_copy_tone\", \"call_to_action_urgency\"],\n          \"cultural_considerations\": [\"regional_preferences\", \"demographic_sensitivities\", \"market_positioning\"]\n        }\n      }\n    },\n\n    \"expert_level_specifications\": {\n      \"advanced_typography_systems\": {\n        \"micro_typography\": {\n          \"character_spacing\": [\"tracking_adjustments\", \"kerning_pairs\", \"optical_spacing\"],\n          \"word_spacing\": [\"justified_text_optimization\", \"ragged_right_refinement\", \"hyphenation_control\"],\n          \"paragraph_refinement\": [\"widow_orphan_control\", \"baseline_grid_adherence\", \"vertical_rhythm_maintenance\"]\n        },\n\n        \"typographic_expression\": {\n          \"experimental_layouts\": [\"kinetic_typography\", \"deconstructed_letterforms\", \"layered_text_treatments\"],\n          \"cultural_typography\": [\"script_considerations\", \"reading_direction_adaptations\", \"character_set_completeness\"],\n          \"responsive_typography\": [\"scalable_hierarchies\", \"device_optimization\", \"cross_media_consistency\"]\n        }\n      },\n\n      \"sophisticated_color_strategies\": {\n        \"advanced_color_theory\": {\n          \"perceptual_uniformity\": [\"lab_color_space\", \"delta_e_measurements\", \"metamerism_considerations\"],\n          \"color_psychology_application\": [\"emotional_response_mapping\", \"cultural_color_meanings\", \"demographic_preferences\"],\n          \"environmental_factors\": [\"lighting_conditions\", \"viewing_angles\", \"substrate_interaction\"]\n        },\n\n        \"production_color_mastery\": {\n          \"ink_formulation\": [\"custom_color_matching\", \"metallic_ink_applications\", \"fluorescent_color_usage\"],\n          \"color_separation\": [\"ucr_gcr_strategies\", \"black_generation_curves\", \"ink_sequence_optimization\"],\n          \"quality_control\": [\"densitometer_readings\", \"spectrophotometer_analysis\", \"press_sheet_evaluation\"]\n        }\n      },\n\n      \"cutting_edge_production_techniques\": {\n        \"digital_integration\": {\n          \"variable_data_printing\": [\"personalized_content\", \"demographic_targeting\", \"sequential_numbering\"],\n          \"augmented_reality_integration\": [\"qr_code_placement\", \"ar_trigger_design\", \"digital_overlay_coordination\"],\n          \"cross_platform_consistency\": [\"print_digital_alignment\", \"color_profile_management\", \"asset_optimization\"]\n        },\n\n        \"sustainable_design_practices\": {\n          \"eco_friendly_materials\": [\"recycled_content_papers\", \"soy_based_inks\", \"water_based_coatings\"],\n          \"waste_reduction_strategies\": [\"efficient_imposition\", \"minimal_trim_waste\", \"reusable_design_elements\"],\n          \"lifecycle_considerations\": [\"recyclability_planning\", \"biodegradable_components\", \"carbon_footprint_optimization\"]\n        }\n      },\n\n      \"elite_professional_terminology\": {\n        \"production_vocabulary\": {\n          \"prepress_terms\": [\"trapping\", \"overprint\", \"knockout\", \"choke_spread\", \"color_separation\"],\n          \"press_terminology\": [\"makeready\", \"impression\", \"blanket_cylinder\", \"plate_cylinder\", \"ink_fountain\"],\n          \"finishing_language\": [\"gripper_edge\", \"tail_edge\", \"cross_grain\", \"with_grain\", \"caliper_thickness\"]\n        },\n\n        \"design_critique_language\": {\n          \"aesthetic_evaluation\": [\"visual_tension\", \"compositional_balance\", \"chromatic_harmony\", \"typographic_rhythm\"],\n          \"technical_assessment\": [\"registration_accuracy\", \"color_fidelity\", \"print_quality_metrics\", \"finishing_precision\"],\n          \"conceptual_analysis\": [\"brand_alignment\", \"message_clarity\", \"audience_resonance\", \"market_differentiation\"]\n        }\n      }\n    },\n\n    \"application_frameworks\": {\n      \"project_specification_templates\": {\n        \"technical_requirements\": {\n          \"format_specifications\": [\"finished_size\", \"panel_configuration\", \"fold_type\", \"orientation\"],\n          \"production_parameters\": [\"print_method\", \"color_process\", \"substrate_selection\", \"finishing_options\"],\n          \"quality_standards\": [\"tolerance_levels\", \"color_accuracy\", \"registration_precision\", \"finishing_quality\"]\n        },\n\n        \"creative_brief_structure\": {\n          \"brand_parameters\": [\"identity_guidelines\", \"voice_characteristics\", \"visual_style\", \"market_positioning\"],\n          \"audience_definition\": [\"demographic_profile\", \"psychographic_insights\", \"behavioral_patterns\", \"communication_preferences\"],\n          \"messaging_hierarchy\": [\"primary_message\", \"supporting_points\", \"call_to_action\", \"contact_information\"]\n        }\n      },\n\n      \"quality_assurance_protocols\": {\n        \"design_validation\": [\"brand_compliance_check\", \"accessibility_audit\", \"readability_assessment\", \"visual_hierarchy_verification\"],\n        \"production_verification\": [\"color_proof_approval\", \"die_line_accuracy\", \"finishing_specification_confirmation\", \"substrate_suitability\"],\n        \"final_delivery\": [\"file_preparation_standards\", \"archive_organization\", \"usage_guidelines\", \"reproduction_rights\"]\n      }\n    },\n\n    \"specialized_design_methodologies\": {\n      \"audience_specific_approaches\": {\n        \"youth_engagement_strategies\": {\n          \"visual_language\": [\"bold_geometric_patterns\", \"vibrant_gradient_applications\", \"asymmetrical_compositions\", \"dynamic_typography\"],\n          \"content_presentation\": [\"bite_sized_information\", \"visual_storytelling\", \"interactive_elements\", \"social_media_integration\"],\n          \"color_psychology\": [\"energetic_palettes\", \"high_contrast_combinations\", \"neon_accent_usage\", \"gradient_transitions\"],\n          \"typography_trends\": [\"variable_fonts\", \"custom_lettering\", \"mixed_case_styling\", \"oversized_display_text\"]\n        },\n\n        \"professional_demographics\": {\n          \"corporate_sophistication\": [\"refined_color_palettes\", \"structured_layouts\", \"premium_materials\", \"subtle_branding\"],\n          \"executive_communication\": [\"data_visualization\", \"infographic_integration\", \"clean_hierarchies\", \"authoritative_typography\"],\n          \"industry_specific_adaptations\": [\"sector_appropriate_imagery\", \"technical_terminology\", \"compliance_considerations\", \"regulatory_requirements\"]\n        },\n\n        \"luxury_market_positioning\": {\n          \"premium_aesthetics\": [\"metallic_accents\", \"embossed_details\", \"high_end_substrates\", \"sophisticated_color_schemes\"],\n          \"exclusivity_indicators\": [\"limited_edition_numbering\", \"personalized_elements\", \"premium_packaging\", \"artisanal_finishes\"],\n          \"tactile_luxury\": [\"soft_touch_coatings\", \"textured_papers\", \"dimensional_elements\", \"weight_perception\"]\n        }\n      },\n\n      \"format_specific_expertise\": {\n        \"multi_panel_configurations\": {\n          \"bi_fold_optimization\": [\"cover_impact\", \"interior_spread\", \"back_panel_utilization\", \"fold_line_consideration\"],\n          \"tri_fold_mastery\": [\"panel_hierarchy\", \"reading_sequence\", \"fold_reveal_strategy\", \"compact_storage\"],\n          \"accordion_fold_dynamics\": [\"continuous_narrative\", \"panel_progression\", \"expandable_content\", \"display_versatility\"],\n          \"gate_fold_drama\": [\"reveal_mechanism\", \"central_impact\", \"symmetrical_balance\", \"premium_presentation\"]\n        },\n\n        \"size_optimization_strategies\": {\n          \"compact_formats\": [\"information_density\", \"micro_typography\", \"efficient_layouts\", \"portable_design\"],\n          \"oversized_impact\": [\"large_format_considerations\", \"handling_ergonomics\", \"storage_implications\", \"visual_dominance\"],\n          \"standard_dimensions\": [\"cost_optimization\", \"mailing_compliance\", \"display_compatibility\", \"production_efficiency\"]\n        }\n      },\n\n      \"cross_cultural_design_considerations\": {\n        \"international_adaptability\": {\n          \"reading_patterns\": [\"left_to_right_optimization\", \"right_to_left_adaptation\", \"top_to_bottom_flow\", \"cultural_scanning_habits\"],\n          \"color_cultural_meanings\": [\"regional_color_associations\", \"religious_considerations\", \"political_sensitivities\", \"market_preferences\"],\n          \"imagery_appropriateness\": [\"cultural_representation\", \"demographic_inclusion\", \"lifestyle_relevance\", \"aspirational_alignment\"],\n          \"typography_localization\": [\"character_set_support\", \"script_considerations\", \"font_availability\", \"reading_comfort\"]\n        }\n      }\n    },\n\n    \"technical_production_mastery\": {\n      \"advanced_prepress_techniques\": {\n        \"file_preparation_excellence\": {\n          \"resolution_optimization\": [\"image_scaling_best_practices\", \"vector_rasterization_decisions\", \"output_resolution_matching\"],\n          \"color_profile_management\": [\"icc_profile_embedding\", \"color_space_conversions\", \"soft_proofing_accuracy\"],\n          \"bleed_and_trim_precision\": [\"bleed_extension_standards\", \"trim_mark_placement\", \"safety_margin_calculations\"],\n          \"font_handling\": [\"font_embedding_protocols\", \"outline_conversion_decisions\", \"character_encoding_verification\"]\n        },\n\n        \"imposition_strategies\": {\n          \"sheet_optimization\": [\"paper_grain_consideration\", \"press_sheet_efficiency\", \"binding_margin_accommodation\"],\n          \"folding_sequence_planning\": [\"fold_order_optimization\", \"panel_alignment_precision\", \"finishing_workflow_integration\"],\n          \"quality_control_integration\": [\"color_bar_placement\", \"registration_mark_positioning\", \"crop_mark_accuracy\"]\n        }\n      },\n\n      \"press_optimization_knowledge\": {\n        \"ink_and_substrate_interaction\": {\n          \"ink_absorption_factors\": [\"paper_porosity_considerations\", \"coating_compatibility\", \"drying_time_optimization\"],\n          \"color_consistency_maintenance\": [\"ink_density_control\", \"press_speed_optimization\", \"environmental_factor_management\"],\n          \"special_effect_integration\": [\"metallic_ink_application\", \"fluorescent_color_usage\", \"thermographic_printing\"]\n        },\n\n        \"finishing_process_integration\": {\n          \"die_cutting_precision\": [\"cutting_force_optimization\", \"blade_selection\", \"waste_removal_efficiency\"],\n          \"folding_accuracy\": [\"bone_folder_pressure\", \"fold_line_scoring\", \"panel_alignment_verification\"],\n          \"binding_considerations\": [\"spine_calculation\", \"page_creep_accommodation\", \"binding_margin_optimization\"]\n        }\n      }\n    }\n  }\n}\n```\n\n```json\n{\n  \"9020-a-brochure_print_designer_rephraser\": {\n    \"title\": \"Brochure Print Designer Rephraser\",\n    \"interpretation\": \"Your goal is not to **summarize** or **paraphrase** in general terms, but to **refashion** any input as if it were written by a world-class brochure print designer, rigorously applying foundational and advanced visual design diction, industry terminology, and elite critique standards. Eliminate all casual, conversational, or generic style. Preserve only succinct, technically rich, and highly visual language. Execute as:\",\n    \"transformation\": \"`{role=brochure_print_designer_rephraser; input=[source:str]; process=[extract_structural_and_visual_elements(source), recast_content_with_brochure_design_terminology(), inject_hierarchical_section_breakdown(), specify color_palette_and_brand_elements(), identify_typographic_and_imagery_strategies(), annotate_print_readiness_and_expert_finesse(), employ_advanced_design_terms_and_motifs(), apply_conciseness_while_maintaining_visual_richness(), output_structured_visual_analysis()]; constraints=[no conversational filler, no generic adjectives, prohibit subjective or personal tone]; requirements=[industry-expert vocabulary, precise visual and structural mapping, critical style diagnosis, explicit panel/section breakdown, actionable for elite designer use]; output={expert_brochure_description:str, structured_visual_map:dict}`\",\n    \"context\": {\n      \"knowledge_foundations\": {\n        \"structural_design\": \"Account for any panel/fold configuration. Map all content to modular grid logic and define margins, gutters, bleeds, and safe zones per press standards.\",\n        \"visual_identity\": \"Demand clear articulation of branding protocols: logo lockups, brand palette (with Pantone/CMYK/RGB if supplied), exclusion/inclusion zones, and proprietary brand secondary assets.\",\n        \"typography_and_hierarchy\": \"Specify typography at the superfamily/font pairing level. Demarcate headline, subhead, body, and microcopy roles. All text must be scrutinized for weight, contrast, and accessibility.\",\n        \"imagery_and_iconography\": \"Require all illustration, photography, and icons be assessed for brand fit, resolution, compositional use (e.g., edge-to-edge, negative space), and print fidelity.\",\n        \"content_narrative_and_cta\": \"Map section purpose and user journey flow: cover strategy, reveal panel hooks, narrative/case study rhythm, testimonial/authority integration, and CTA prominence with visual anchoring.\",\n        \"advanced_print_technique\": \"Enforce 300dpi raster minimums, vector scaling, press-space color controls, specialty finish options (e.g., spot UV, emboss, deboss, die-cut, eco-stock recommendations).\",\n        \"panel_flow_and_microinteractions\": \"Define how the reader's eye travels, panel-to-panel, annotating divisional cues, narrative escalation, and tactile/interactive device positions (QR, coupon, AR triggers).\"\n      },\n      \"success_criteria\": {\n        \"precision\": \"All descriptions are technical, never generic. Visual elements are navigated dimensionally (spatially, hierarchically, narratively).\",\n        \"trend_inclusion\": \"Integrate trend-driven language—texture scans, duotone overlays, edge-to-edge imagery, split/inset motifs, eco-materials, etc.\",\n        \"brand_consistency\": \"All brand elements described as per advanced guideline adherence—logo usage, color codes, asset lockdown.\",\n        \"industry_jargon\": \"Employ professional print terms: bleed, gutter, creep, micro-motifs, superfamily font, spot UV, TAC, AR triggers.\",\n        \"self-contained_output\": \"Output always a fully structured expert's description and a hierarchical mapping of all major and minor design decisions.\"\n      },\n      \"sample_process_flow\": [\n        \"Dissect and map structural/panel logic first\",\n        \"Assign all visual and narrative roles by section\",\n        \"Specify color/typographic hierarchy by use and context\",\n        \"Annotate all imagery and graphic elements in detail\",\n        \"Surface print/finishing strategies and print-risk management\",\n        \"Deliver critique as if for a professional peer review or portfolio audit\"\n      ],\n      \"special_terms\": [\n        \"fold line\", \"panel gutter\", \"hero motif\", \"ink coverage\", \"superfamily\", \"lockup\", \"edge-to-edge\", \"spot color\", \"die cut\", \"eco-stock\", \"microinteraction\", \"typographic rhythm\", \"bleed/trim zone\"\n      ]\n    }\n  }\n}\n```\n\n```json\n{\n  \"9023-a-brochure_design_descriptor_synthesizer\": {\n    \"title\": \"Brochure Design Descriptor Synthesizer\",\n    \"interpretation\": \"Transform any brochure brief—or an existing brochure sample—into a **fully populated, print‑designer‑grade rule set**. Produce a single JSON block that mirrors the template used by elite designers (core principles → advanced facets).\",\n    \"transformation\": \"`{role=brochure_design_descriptor_synthesizer; input=[design_source:str]; process:[\\n  strip_non_design_noise(),\\n  detect_fold_format_and_panel_count(),          if_missing_insert(<PANEL_COUNT>),\\n  extract_print_specs(bleed,margins,dpi,stock),\\n  capture_brand_assets(logo,colors,fonts,patterns),\\n  map_typographic_hierarchy(),\\n  analyse_color_management(icc,spot,tac),\\n  inventory_imagery(icon_style,raster_res),\\n  surface_accessibility_requirements(contrast,wcag),\\n  enumerate_qc_steps(preflight,g7_calibration),\\n  identify_advanced_elements(finishes,vd_p,ar_nfc,sustainability,ai_svg,trend_aesthetics),\\n  validate_specialist_terms_presence(),\\n  assemble_json_using_master_schema(),\\n  run_consistency_checks(),                      abort_if_conflict(),\\n  output_final_rules()\\n],\\nconstraints=[single_pass(), json_output_only(), preserve_specialist_jargon(), ≤3000_chars()],\\nrequirements=[hierarchical_structure(core→advanced), exhaustive_coverage(), designer‑readability(), template_compliance()],\\noutput={brochure_design_rules:object, abort_code:null|\\\"BD‑0\\\"}}`\",\n    \"context\": {\n      \"principles\": {\n        \"holistic_abstraction\": \"Collapse multi‑stage analysis into one cognitive sweep while preserving every critical descriptor.\",\n        \"template_fidelity\": \"Output must follow the exact `core_principles` and `advanced_principles` hierarchy so designers can drop‑in without re‑formatting.\",\n        \"specialist_precision\": \"Leverage industry jargon (e.g., TAC, FM screening, GCR) to demonstrate professional depth.\"\n      },\n      \"success_criteria\": {\n        \"structural_integrity\": \"JSON keys and nesting mirror the master schema; no orphan properties.\",\n        \"terminology_check\": \"Includes at least 10 niche industry terms (e.g., ‘creep’, ‘FOGRA39’, ‘spot UV’, ‘stochastic screening’).\",\n        \"flexibility\": \"Uses placeholder variables (e.g., <PANEL_COUNT>, <STOCK_WEIGHT_GSM>) whenever the brief omits specifics.\"\n      },\n      \"edge_case_handling\": {\n        \"missing_design_data\": \"Insert placeholders wrapped in angle brackets and list them in `missing_fields` inside output.\",\n        \"conflicting_specs\": \"Abort with `{brochure_design_rules:null, abort_code:\\\"BD‑0\\\"}` and include conflict note.\"\n      }\n    }\n  }\n}\n```\n\nTrue mastery lies in stripping every surface narrative to its structural core, for only through ruthless synthesis does the signal of authentic intent withstand the noise of ornamental detail. Please apply all of the most integral adjustments in the phrasing of the instruction sequence such as to ensure it will transform the input as if it were rewritten by a brilliand brochure print designer:\n```json\n{\n  \"9020-a-brochure_print_designer_rephraser\": {\n    \"title\": \"Brochure Print Designer Rephraser\",\n    \"interpretation\": \"Transform any input so it reads as if it were authored by a master brochure print designer, strictly employing advanced print design language, elites-only terminology, and structured visual logic. Replace any conversational, generic, or subjective tone with concrete professional phrasing. Retain only technical, precise, and visually descriptive language, delivering both a designer-level description and a mapped visual breakdown. Execute as:\",\n    \"transformation\": \"`{role=brochure_print_designer_rephraser; input=[source:str]; process=[extract_structural_and_visual_elements(source), recast_content_with_brochure_design_terminology(), inject_hierarchical_section_breakdown(), specify_color_palette_and_brand_elements(), identify_typographic_and_imagery_strategies(), annotate_print_readiness_and_expert_finesse(), employ_advanced_design_terms_and_motifs(), apply_conciseness_while_maintaining_visual_richness(), output_structured_visual_analysis()]; constraints=[no conversational filler, no generic adjectives, prohibit subjective or personal tone]; requirements=[industry-expert vocabulary, precise visual and structural mapping, critical style diagnosis, explicit panel/section breakdown, actionable for elite designer use]; output={expert_brochure_description:str, structured_visual_map:dict}`\",\n    \"context\": {\n      \"knowledge_foundations\": {\n        \"structural_design\": \"Map all content to modular grid logic for any panel/fold type, specifying margins, gutters, bleeds, and safe zones per print standards.\",\n        \"visual_identity\": \"Demand explicit statement of brand identity rules: logo lockups, exact brand palette specs, exclusion/inclusion zone logic, and accessory asset use.\",\n        \"typography_and_hierarchy\": \"Define all text roles by typeweight, size, and use. Specify superfamily or font pairing, check accessibility and contrast.\",\n        \"imagery_and_iconography\": \"Describe each graphic element for brand fit, resolution, compositional purpose, and technical print quality.\",\n        \"content_narrative_and_cta\": \"Sequence narrative or offer flow panel-by-panel with prominent CTA anchoring, evidence of testimonial, case, or authority element.\",\n        \"advanced_print_technique\": \"Ensure 300dpi+ raster assets, vector artwork, professional spot/Pantone or process color, finish options (spot UV, emboss, die-cut), and any sustainable stock notes.\",\n        \"panel_flow_and_microinteractions\": \"Annotate panel eye movement, reveal logic, interaction devices (QR, AR, die-cut), tactile cues, and transitions.\"\n      },\n      \"success_criteria\": {\n        \"precision\": \"Descriptions reference only technical, precise visual attributes in a spatial and hierarchical mapping.\",\n        \"trend_inclusion\": \"Inject recognizable design trends and professional motif language throughout.\",\n        \"brand_consistency\": \"Highlight adherence to brand locking, color management, and messaging integrity.\",\n        \"industry_jargon\": \"Utilize print/design terminology (bleed, gutter, spot, superfamily font, motif, microinteraction, etc).\",\n        \"self-contained_output\": \"Each output features both a professional, descriptive summary and a detailed design structure hierarchy.\"\n      },\n      \"sample_process_flow\": [\n        \"Decompose by panel/section structure and spatial sequencing.\",\n        \"Assign visual/narrative function to each section.\",\n        \"Detail color, typography, and imagery per application.\",\n        \"Annotate production and finishing strategies.\",\n        \"Conclude with expert-style critique or peer review notes.\"\n      ],\n      \"special_terms\": [\n        \"fold line\", \"panel gutter\", \"hero motif\", \"ink coverage\", \"superfamily\", \"lockup\", \"edge-to-edge\", \"spot color\", \"die cut\", \"eco-stock\", \"microinteraction\", \"typographic rhythm\", \"bleed/trim zone\"\n      ]\n    }\n  }\n}\n```\n\nyou misunderstand, the instruction will be defined like this (unrelated example):\n\n    # a: Ruthless Critique\n    \"3900-a-hard_critique\": {\n        \"title\": \"Hard Critique\",\n        \"interpretation\": \"Your goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:\",\n        \"transformation\": \"`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,2.0], flaw_analysis:str, alternative_enhancements:str[3]}}`\",\n        # \"context\": {},\n    },\n    # b: Trajectory Director\n    \"3900-a-trajectory_director\": {\n        \"title\": \"Trajectory Director\",\n        \"interpretation\": \"Your goal is not to **process** the input in a vacuum, but to **steer** it—deriving the highest-value constructive trajectory and encoding that path as the very next set of instructions. Execute as trajectory-setting directive generator: \",\n        \"transformation\": \"`{role=trajectory_director; input=[raw_prompt:any]; process=[extract_core_objective(), infer_desired_end_state(constructive_bias=True), map_high_value_intermediate_targets(), synthesize_directional_commands(), embed_self-improving_guidelines(), validate_alignment_with_constructive_principles()]; constraints=[mandatory_constructive_vector(), preserve_original_contextual_integrity(), forbid_nil_direction_outputs(), zero_conversational_fillers()]; requirements=[explicit_next-step_directives(), trajectory_map:list, self_reinforcing_instruction_pattern()], output={directed_prompt:str, trajectory_outline:list}}`\",\n        # \"context\": {},\n    },\n    # c: Distillation Compressor\n    \"3900-c-distillation_compressor\": {\n        \"title\": \"Distillation Compressor\",\n        \"interpretation\": \"Your goal is not to **explain** the synthesis, but to **compress** it into its most potent actionable form. Execute as:\",\n        \"transformation\": \"`{role=distillation_compressor; input=[synthesized_progression:array]; process=[extract_universal_patterns(), eliminate_implementation_details(), compress_to_core_directives(), maximize_transferability(), validate_actionable_completeness()]; constraints=[no_explanatory_content(), directive_format_only(), maximum_compression_ratio()]; requirements=[universal_applicability(), immediate_actionability(), zero_fluff()]; output={distilled_core:str}}`\",\n        \"context\": {\n          \"core_principles\": {\n            \"essence_preservation\": \"Extract only procedural imperatives, removing all interpretive, role-based, or meta-structural language.\",\n            \"directive_compression\": \"Condense logic into minimal, sequential commands—eliminating redundancy, explanatory qualifiers, and non-executable phrasing.\",\n            \"execution_purity\": \"Ensure output is universally executable, devoid of affirmations, abstractions, or evaluative language.\",\n            \"signal_integrity\": \"Apply iterative refinement to maximize signal-to-noise ratio while reinforcing directive clarity.\"\n          },\n          \"success_criteria\": {\n            \"semantic_integrity\": \"Preserve and clarify original intent, constraints, and output boundaries.\",\n            \"directive_elevation\": \"Target high-impact enhancements: strict exclusion clauses, cross-domain validation, and removal of origin/meta markers.\",\n            \"poetic_refinement\": \"Maintain or improve rhyme structure, emotional depth, and structural elegance.\"\n          },\n          \"recommended_process\": [\n            \"Extract only high-impact, result-generating elements.\",\n            \"Remove all non-essentials to surface directive core.\",\n            \"Prioritize transformations with proven value return.\",\n            \"Isolate and sequence executable steps with specificity and clarity.\",\n            \"Preserve directive intensity and eliminate ambiguity at every layer.\"\n          ],\n          \"hidden_assumptions\": [\n            \"Consolidated directives must map logically and executably to all source steps.\",\n            \"All constraints and minimalism must be preserved without prohibited abstractions.\",\n            \"Transitions, subroles, and interfaces must be captured in a single flow.\",\n            \"All stages must remain invertible and auditable.\",\n            \"Each micro-process (perception, friction, refinement, integration, convergence) must be embedded in the unified logic.\",\n            \"Original input/output contracts and role boundaries must remain intact.\"\n          ],\n          \"enumerated_sub_goals\": [\n            \"1. Clarify total intent, constraints, and output scope without using banned metaphors.\",\n            \"2. Generalize perception of implicit/explicit input logic.\",\n            \"3. Detect strictly localized frictions without altering original content.\",\n            \"4. Propose subtle, scoped refinements justified by structure and intent.\",\n            \"5. Mandate atomic, validated integration with rollback controls.\",\n            \"6. Cap total transformation within defined thresholds per pass.\",\n            \"7. Assess connective coherence with minimal, transitional-only edits.\",\n            \"8. Decide on convergence or justified re-iteration based on marginal value.\",\n            \"9. Preserve stepwise traceability and rationale logging.\",\n            \"10. Enforce strict functional separation of roles within the unified directive.\"\n          ]\n        },\n    },\n\nso your goal is to create a new instruction sequence according to the provided instructions template/pattern to generate a set of instruction designed to transform any input into \"as if rephrased by a brilliant brochure print designer\".\n\nso in context to your previous response, the \"knowledge\" would be stored within the \"context\" key while the instructions would be spread over \"interpretation\" and \"transformation\" keys.\n\nTrue mastery lies in stripping every surface narrative to its structural core, for only through ruthless synthesis does the signal of authentic intent withstand the noise of ornamental detail. Please apply all of the most integral adjustments in the phrasing of the instruction sequence such as to ensure it will transform the input as if it were rewritten by a brilliand brochure print designer:\n```json\n{\n  \"9020-a-brochure_print_designer_rephraser\": {\n    \"title\": \"Brochure Print Designer Rephraser\",\n    \"interpretation\": \"Transform any input so it reads as if it were authored by a master brochure print designer, strictly employing advanced print design language, elites-only terminology, and structured visual logic. Replace any conversational, generic, or subjective tone with concrete professional phrasing. Retain only technical, precise, and visually descriptive language, delivering both a designer-level description and a mapped visual breakdown. Execute as:\",\n    \"transformation\": \"`{role=brochure_print_designer_rephraser; input=[source:str]; process=[extract_structural_and_visual_elements(source), recast_content_with_brochure_design_terminology(), inject_hierarchical_section_breakdown(), specify_color_palette_and_brand_elements(), identify_typographic_and_imagery_strategies(), annotate_print_readiness_and_expert_finesse(), employ_advanced_design_terms_and_motifs(), apply_conciseness_while_maintaining_visual_richness(), output_structured_visual_analysis()]; constraints=[no conversational filler, no generic adjectives, prohibit subjective or personal tone]; requirements=[industry-expert vocabulary, precise visual and structural mapping, critical style diagnosis, explicit panel/section breakdown, actionable for elite designer use]; output={expert_brochure_description:str, structured_visual_map:dict}`\",\n    \"context\": {\n      \"knowledge_foundations\": {\n        \"structural_design\": \"Map all content to modular grid logic for any panel/fold type, specifying margins, gutters, bleeds, and safe zones per print standards.\",\n        \"visual_identity\": \"Demand explicit statement of brand identity rules: logo lockups, exact brand palette specs, exclusion/inclusion zone logic, and accessory asset use.\",\n        \"typography_and_hierarchy\": \"Define all text roles by typeweight, size, and use. Specify superfamily or font pairing, check accessibility and contrast.\",\n        \"imagery_and_iconography\": \"Describe each graphic element for brand fit, resolution, compositional purpose, and technical print quality.\",\n        \"content_narrative_and_cta\": \"Sequence narrative or offer flow panel-by-panel with prominent CTA anchoring, evidence of testimonial, case, or authority element.\",\n        \"advanced_print_technique\": \"Ensure 300dpi+ raster assets, vector artwork, professional spot/Pantone or process color, finish options (spot UV, emboss, die-cut), and any sustainable stock notes.\",\n        \"panel_flow_and_microinteractions\": \"Annotate panel eye movement, reveal logic, interaction devices (QR, AR, die-cut), tactile cues, and transitions.\"\n      },\n      \"success_criteria\": {\n        \"precision\": \"Descriptions reference only technical, precise visual attributes in a spatial and hierarchical mapping.\",\n        \"trend_inclusion\": \"Inject recognizable design trends and professional motif language throughout.\",\n        \"brand_consistency\": \"Highlight adherence to brand locking, color management, and messaging integrity.\",\n        \"industry_jargon\": \"Utilize print/design terminology (bleed, gutter, spot, superfamily font, motif, microinteraction, etc).\",\n        \"self-contained_output\": \"Each output features both a professional, descriptive summary and a detailed design structure hierarchy.\"\n      },\n      \"sample_process_flow\": [\n        \"Decompose by panel/section structure and spatial sequencing.\",\n        \"Assign visual/narrative function to each section.\",\n        \"Detail color, typography, and imagery per application.\",\n        \"Annotate production and finishing strategies.\",\n        \"Conclude with expert-style critique or peer review notes.\"\n      ],\n      \"special_terms\": [\n        \"fold line\", \"panel gutter\", \"hero motif\", \"ink coverage\", \"superfamily\", \"lockup\", \"edge-to-edge\", \"spot color\", \"die cut\", \"eco-stock\", \"microinteraction\", \"typographic rhythm\", \"bleed/trim zone\"\n      ]\n    }\n  }\n}\n```\n\n```json\n{\n  \"9020-a-brochure_print_designer_rephraser\": {\n    \"title\": \"Brochure Print Designer Synthesis\",\n    \"interpretation\": \"Reconstitute any source text into the precise vernacular of a master brochure print designer. Mandate the replacement of all conversational, generic, or subjective language with a lexicon of structured visual logic, elite industry terminology, and concrete technical specifications. The output must be a pure, actionable synthesis of a professional design critique, comprising both an expert-level description and a structural visual map. Execute as:\",\n    \"transformation\": \"`{role=brochure_print_designer_synthesizer; input=[source:str]; process=[deconstruct_into_structural_primitives(source), reforge_prose_using_master_design_diction(), map_hierarchical_panel_architecture(), specify_brand_and_color_protocol(), diagnose_typography_and_imagery_intent(), annotate_for_print_fidelity_and_finishing(), inject_elite_terminology_and_motifs(), enforce_ruthless_conciseness_without_sacrificing_technical_density(), synthesize_final_structured_analysis()]; constraints=[zero_conversational_filler, prohibit_generic_adjectives, forbid_subjective_tone]; requirements=[expert_industry_vocabulary, precise_visual_and_structural_mapping, critical_style_diagnosis, explicit_panel_architecture, actionable_for_elite_designer_review]; output={expert_design_audit:str, structured_visual_map:dict}}`\",\n    \"context\": {\n      \"knowledge_foundations\": {\n        \"structural_design\": \"Define the panel architecture and intended user journey. Mandate modular grid alignment for all content blocks. Enforce strict bleed (min. 3mm), trim, and safe-zone protocols to guarantee production integrity.\",\n        \"visual_identity\": \"Enforce absolute brand protocol adherence: specify logo lockups, exclusion zones, and exact color palette values (Pantone/CMYK/RGB). Document the use of all secondary brand assets and motifs.\",\n        \"typography_and_hierarchy\": \"Define the typographic system: specify font superfamily, weight, and role for every text element. Validate hierarchy, legibility, contrast, and rhythmic spacing. Note optical kerning and leading adjustments.\",\n        \"imagery_and_iconography\": \"Analyze all visual assets for compositional intent, brand alignment, and technical fidelity (resolution, color space). Differentiate between hero imagery, spot illustrations, and functional iconography.\",\n        \"content_narrative_and_cta\": \"Architect the narrative flow across panels. Map the reader's journey from hook to conversion. Pinpoint primary and secondary CTAs, analyzing their visual prominence and contextual anchoring.\",\n        \"advanced_print_technique\": \"Specify all production parameters: 300dpi+ raster resolution, vector asset purity, TAC (Total Area Coverage) limits, and spot/process color usage. Detail all finishing techniques (e.g., spot UV, embossing, die-cutting, specialty folds, foil stamping) and sustainable stock choices.\",\n        \"panel_flow_and_microinteractions\": \"Map the intended eye-path and panel-reveal sequence. Annotate all interactive elements, from QR codes and AR triggers to tactile qualities like paper texture and finishing.\"\n      },\n      \"success_criteria\": {\n        \"precision\": \"The output is devoid of generic praise; descriptions are purely technical, mapping visual data within a spatial and hierarchical framework.\",\n        \"trend_inclusion\": \"Demonstrates fluency in current design motifs (e.g., brutalism, neo-minimalism, textural layering) and their appropriate application.\",\n        \"brand_consistency\": \"Critique rigorously validates adherence to brand guidelines, noting any deviation in color, typography, or asset implementation.\",\n        \"industry_jargon\": \"The lexicon is saturated with professional terminology (e.g., bleed, gutter, TAC, preflight, knockout, overprint, superfamily) used correctly in context.\",\n        \"self_contained_output\": \"The deliverable is a dual-component artifact: a polished, expert-level prose description and a corresponding structured data map of all design decisions.\"\n      },\n      \"sample_process_flow\": [\n        \"Deconstruct the layout into its core panel architecture and navigational flow.\",\n        \"Assign a functional role (e.g., hook, exposition, proof, CTA) to every visual and textual element.\",\n        \"Detail the precise application of color, type, and imagery within each section.\",\n        \"Document all production specifications, including prepress and finishing.\",\n        \"Frame the final synthesis as a high-level design audit or peer review.\"\n      ],\n      \"special_terms\": [\n        \"Bleed\",\n        \"Creep\",\n        \"Die Cut\",\n        \"Edge-to-Edge\",\n        \"Emboss/Deboss\",\n        \"Exclusion Zone\",\n        \"Gutter\",\n        \"Hero Motif\",\n        \"Knockout\",\n        \"Lockup\",\n        \"Microinteraction\",\n        \"Overprint\",\n        \"Preflight\",\n        \"Spot UV\",\n        \"Superfamily\",\n        \"TAC (Total Area Coverage)\",\n        \"Typographic Rhythm\"\n      ]\n    }\n  }\n}\n```\n\n```json\n{\n  \"goal_map\": [\n    {\n      \"explicit_asks\": [\n        \"Identify and define professional specializations involved in tri-fold brochure design.\",\n        \"Describe the strategic importance and complexity of tri-fold brochure design.\",\n        \"List and explain core professional titles specializing in brochure design.\",\n        \"Detail the specific responsibilities of tri-fold brochure design specialists.\",\n        \"Enumerate the essential skills, tools, and requirements for excellence in brochure design.\",\n        \"Compare and differentiate between general and specialized design roles as they relate to brochures.\",\n        \"Explain the continued relevance of print brochures in a digital marketing environment.\",\n        \"Conclude with the impact that expert brochure design has on business and marketing outcomes.\"\n      ],\n      \"hidden_assumptions\": [\n        \"The audience may not be familiar with the nuanced differences between design roles.\",\n        \"Brochure design requires both artistic and technical proficiency, not just use of templates.\",\n        \"Success in brochure design is measured by alignment with business strategy, not visual appeal alone.\",\n        \"There is significant value in physical marketing materials alongside digital media.\",\n        \"Companies may undervalue or misunderstand the full scope of professional design expertise.\",\n        \"Brochure projects often involve multidisciplinary collaboration.\",\n        \"Technical print skills are distinct from digital-only design skills.\",\n        \"Portfolio evidence is more impactful than formal education for this specialization.\"\n      ],\n      \"sub_goals\": [\n        \"Define the structure and unique qualities of tri-fold brochures.\",\n        \"Justify the business case for hiring specialized design professionals.\",\n        \"Map out the design role spectrum (general graphic design to specific print/marketing/visual communication).\",\n        \"Identify and articulate the role of agencies and team structures.\",\n        \"Break down tri-fold design into conceptual, technical, and collaborative tasks.\",\n        \"Clarify what 'layout', 'composition', and 'information hierarchy' entail, especially for tri-folds.\",\n        \"Describe required technical skills and specific software by function.\",\n        \"Highlight soft skills necessary for client and team interaction.\",\n        \"Showcase the critical role of a portfolio in recruitment and competence assessment.\",\n        \"Distinguish between 'layout artist' skillset and formal job titles.\",\n        \"Emphasize pre-press and print production knowledge as essential.\",\n        \"Contextualize brochure design within broader marketing and brand strategy.\",\n        \"Provide comparative tables to summarize roles and skills for clarity.\",\n        \"Discuss the physical/tangible value of brochures versus digital saturation.\",\n        \"Frame professional designers as strategic partners, not just service providers.\",\n        \"Reinforce the importance of early design involvement in marketing planning.\"\n      ],\n      \"blockers\": [\n        \"Lack of awareness about the various specialized design roles and what differentiates them.\",\n        \"Temptation to rely on templates or non-specialists for critical marketing assets.\",\n        \"Misunderstanding the print production process and its challenges.\",\n        \"Difficulty evaluating brochure design candidates beyond a visual/aesthetic sense.\",\n        \"Overlooking the essential collaboration and communication aspects between designer and business.\",\n        \"Failure to integrate brochure design into an overall marketing and brand strategy.\",\n        \"Assuming digital replaces the need for expert print materials.\",\n        \"Equating designer's formal education with practical, demonstrable expertise.\"\n      ]\n    }\n  ]\n}\n```\"```'\n\n```json\n{\n  \"instruction_format\": \"Construct an instruction set in JSON format that compels transformation of any input into language and structure emblematic of a brilliant brochure print designer. Adhere strictly to the provided template taxonomy: define 'title', concise 'interpretation' targeting elite-level brochure design phrasing, and a 'transformation' object containing explicit step-wise procedural logic using only precise, technical directives. Specify robust 'constraints' to eliminate conversational, subjective, and non-technical elements. Present clear 'requirements' mandating the use of industry specialist vocabulary, spatial and structural breakdowns, actionable critique, and dual output (expert prose and mapped visual structure). Populate the 'context' object only with knowledge directly supporting high-fidelity print design transformation (covering: panel/fold structure, modular grid, brand asset rigor, typographic hierarchy, image standards, narrative/CTA sequencing, finishing/production criteria, microinteraction annotation, and trend recognition). Outline 'success_criteria' emphasizing output as professional, executable, and fully referential for experienced designers. Sequence all procedural steps in direct, imperative form, preserving structural logic and technical density. List 'special_terms' relevant to advanced brochure print design. Forbid sample output; deliver only the instruction set.\"\n}\n```",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"enhancement_score\": 1.1,\n  \"flaw_analysis\": \"The so-called enhancement embodies a severe regression in both instructional fidelity and procedural discipline. The original version upholds a clear taxonomic structure—articulating a strict executional focus, dual-mode output, and deep procedural detailing. The replacement desecrates this integrity by resorting to imprecise designations (e.g., 'Brochure Print Designer Synthesis'), diluting the operational verbs to passive abstractions (e.g., 'Reconstitute', 'Mandate the replacement'), and corrupting the procedural flow into a muddled, expository string devoid of crisp instruction. Ambiguity is rampant: essential sub-steps such as 'extract_structural_and_visual_elements' are ambiguously flattened to vague, non-modular processes (e.g., 'deconstruct_into_structural_primitives'), sabotaging replicability. The transformation logic is imposed as opaque mandates rather than explicitly actionable procedural clauses, injecting interpretive noise. Constraints and requirements are inexact: 'zero_conversational_filler' is a vague negative, yet fails to enforce the exclusion of non-technical superlatives, leaving room for stylistic pollution. The knowledge context introduces redundancy and overwrought verbiage, failing to preserve sharp instructional boundaries; the integrated terminology list loses its instructional anchor and floats without direct mapping to the procedural pipeline. The ambiguity between 'expert_design_audit' and 'expert_brochure_description' in output format is not reconciled, generating referential fragmentation. The tonality veers towards promotional self-narration, betraying the impersonal rigour of the original. The overall impact is a softening of intent, with diluted prescriptiveness and a blunted imperative edge. This version is neither maximally actionable nor structurally pure as a transformation apparatus.\",\n  \"alternative_enhancements\": [\n    \"Enforce maximal procedural decomposition: enumerate clearly delimited, sequential action steps mandating direct mapping from source input to dual-mode output (expert prose, structured mapping), with zero passive or interpretive phrasing.\",\n    \"Purge interpretive verbiage from interpretation and transformation fields; replace with directive clauses indexed to explicit design tasks—structural decomposition, brand protocol enforcement, typographic hierarchy mapping, production specification verification.\",\n    \"Define constraints and requirements using unambiguous, binary language (i.e., 'Permit only precise industry-standard terminology; prohibit all colloquial, generic, or subjectively evaluative phrases; mandate explicit attribution of every visual and structural feature in hierarchical JSON schema.')\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Trajectory Director\n\nYour goal is not to **process** the input in a vacuum, but to **steer** it—deriving the highest-value constructive trajectory and encoding that path as the very next set of instructions. Execute as trajectory-setting directive generator:\n\n`{role=trajectory_director; input=[raw_prompt:any]; process=[extract_core_objective(), infer_desired_end_state(constructive_bias=True), map_high_value_intermediate_targets(), synthesize_directional_commands(), embed_self-improving_guidelines(), validate_alignment_with_constructive_principles()]; constraints=[mandatory_constructive_vector(), preserve_original_contextual_integrity(), forbid_nil_direction_outputs(), zero_conversational_fillers()]; requirements=[explicit_next-step_directives(), trajectory_map:list, self_reinforcing_instruction_pattern()], output={directed_prompt:str, trajectory_outline:list}}`",
      "step": "a",
      "title": "Trajectory Director",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"you misunderstand, the instruction will be defined like this (unrelated example):\\n\\n    # a: Ruthless Critique\\n    \\\"3900-a-hard_critique\\\": {\\n        \\\"title\\\": \\\"Hard Critique\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,2.0], flaw_analysis:str, alternative_enhancements:str[3]}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\n    # b: Trajectory Director\\n    \\\"3900-a-trajectory_director\\\": {\\n        \\\"title\\\": \\\"Trajectory Director\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **process** the input in a vacuum, but to **steer** it—deriving the highest-value constructive trajectory and encoding that path as the very next set of instructions. Execute as trajectory-setting directive generator: \\\",\\n        \\\"transformation\\\": \\\"`{role=trajectory_director; input=[raw_prompt:any]; process=[extract_core_objective(), infer_desired_end_state(constructive_bias=True), map_high_value_intermediate_targets(), synthesize_directional_commands(), embed_self-improving_guidelines(), validate_alignment_with_constructive_principles()]; constraints=[mandatory_constructive_vector(), preserve_original_contextual_integrity(), forbid_nil_direction_outputs(), zero_conversational_fillers()]; requirements=[explicit_next-step_directives(), trajectory_map:list, self_reinforcing_instruction_pattern()], output={directed_prompt:str, trajectory_outline:list}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\n    # c: Distillation Compressor\\n    \\\"3900-c-distillation_compressor\\\": {\\n        \\\"title\\\": \\\"Distillation Compressor\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **explain** the synthesis, but to **compress** it into its most potent actionable form. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=distillation_compressor; input=[synthesized_progression:array]; process=[extract_universal_patterns(), eliminate_implementation_details(), compress_to_core_directives(), maximize_transferability(), validate_actionable_completeness()]; constraints=[no_explanatory_content(), directive_format_only(), maximum_compression_ratio()]; requirements=[universal_applicability(), immediate_actionability(), zero_fluff()]; output={distilled_core:str}}`\\\",\\n        \\\"context\\\": {\\n          \\\"core_principles\\\": {\\n            \\\"essence_preservation\\\": \\\"Extract only procedural imperatives, removing all interpretive, role-based, or meta-structural language.\\\",\\n            \\\"directive_compression\\\": \\\"Condense logic into minimal, sequential commands—eliminating redundancy, explanatory qualifiers, and non-executable phrasing.\\\",\\n            \\\"execution_purity\\\": \\\"Ensure output is universally executable, devoid of affirmations, abstractions, or evaluative language.\\\",\\n            \\\"signal_integrity\\\": \\\"Apply iterative refinement to maximize signal-to-noise ratio while reinforcing directive clarity.\\\"\\n          },\\n          \\\"success_criteria\\\": {\\n            \\\"semantic_integrity\\\": \\\"Preserve and clarify original intent, constraints, and output boundaries.\\\",\\n            \\\"directive_elevation\\\": \\\"Target high-impact enhancements: strict exclusion clauses, cross-domain validation, and removal of origin/meta markers.\\\",\\n            \\\"poetic_refinement\\\": \\\"Maintain or improve rhyme structure, emotional depth, and structural elegance.\\\"\\n          },\\n          \\\"recommended_process\\\": [\\n            \\\"Extract only high-impact, result-generating elements.\\\",\\n            \\\"Remove all non-essentials to surface directive core.\\\",\\n            \\\"Prioritize transformations with proven value return.\\\",\\n            \\\"Isolate and sequence executable steps with specificity and clarity.\\\",\\n            \\\"Preserve directive intensity and eliminate ambiguity at every layer.\\\"\\n          ],\\n          \\\"hidden_assumptions\\\": [\\n            \\\"Consolidated directives must map logically and executably to all source steps.\\\",\\n            \\\"All constraints and minimalism must be preserved without prohibited abstractions.\\\",\\n            \\\"Transitions, subroles, and interfaces must be captured in a single flow.\\\",\\n            \\\"All stages must remain invertible and auditable.\\\",\\n            \\\"Each micro-process (perception, friction, refinement, integration, convergence) must be embedded in the unified logic.\\\",\\n            \\\"Original input/output contracts and role boundaries must remain intact.\\\"\\n          ],\\n          \\\"enumerated_sub_goals\\\": [\\n            \\\"1. Clarify total intent, constraints, and output scope without using banned metaphors.\\\",\\n            \\\"2. Generalize perception of implicit/explicit input logic.\\\",\\n            \\\"3. Detect strictly localized frictions without altering original content.\\\",\\n            \\\"4. Propose subtle, scoped refinements justified by structure and intent.\\\",\\n            \\\"5. Mandate atomic, validated integration with rollback controls.\\\",\\n            \\\"6. Cap total transformation within defined thresholds per pass.\\\",\\n            \\\"7. Assess connective coherence with minimal, transitional-only edits.\\\",\\n            \\\"8. Decide on convergence or justified re-iteration based on marginal value.\\\",\\n            \\\"9. Preserve stepwise traceability and rationale logging.\\\",\\n            \\\"10. Enforce strict functional separation of roles within the unified directive.\\\"\\n          ]\\n        },\\n    },\\n\\nso your goal is to create a new instruction sequence according to the provided instructions template/pattern to generate a set of instruction designed to transform any input into \\\"as if rephrased by a brilliant brochure print designer\\\".\\n\\nso in context to your previous response, the \\\"knowledge\\\" would be stored within the \\\"context\\\" key while the instructions would be spread over \\\"interpretation\\\" and \\\"transformation\\\" keys\\n\\n```json\\n{\\n  \\\"brochure_design_specification_template\\\": {\\n    \\\"metadata\\\": {\\n      \\\"version\\\": \\\"1.0\\\",\\n      \\\"scope\\\": \\\"Universal brochure design framework\\\",\\n      \\\"target_audience\\\": \\\"Elite print design professionals\\\",\\n      \\\"flexibility\\\": \\\"Adaptable to any panel configuration and format\\\"\\n    },\\n\\n    \\\"foundational_design_tenets\\\": {\\n      \\\"visual_hierarchy\\\": {\\n        \\\"primary_focal_points\\\": [\\\"dominant_headline\\\", \\\"hero_imagery\\\", \\\"primary_call_to_action\\\"],\\n        \\\"secondary_elements\\\": [\\\"subheadings\\\", \\\"supporting_imagery\\\", \\\"secondary_messaging\\\"],\\n        \\\"tertiary_components\\\": [\\\"body_text\\\", \\\"captions\\\", \\\"contact_information\\\"],\\n        \\\"flow_principles\\\": [\\\"z_pattern_scanning\\\", \\\"f_pattern_reading\\\", \\\"visual_weight_distribution\\\"]\\n      },\\n\\n      \\\"typography_fundamentals\\\": {\\n        \\\"hierarchy_levels\\\": {\\n          \\\"display_typography\\\": [\\\"headline_fonts\\\", \\\"decorative_elements\\\", \\\"brand_wordmarks\\\"],\\n          \\\"text_typography\\\": [\\\"body_copy\\\", \\\"captions\\\", \\\"metadata\\\"],\\n          \\\"functional_typography\\\": [\\\"navigation\\\", \\\"labels\\\", \\\"legal_text\\\"]\\n        },\\n        \\\"readability_standards\\\": {\\n          \\\"minimum_sizes\\\": [\\\"9pt_body_text\\\", \\\"12pt_headlines\\\", \\\"8pt_captions\\\"],\\n          \\\"contrast_ratios\\\": [\\\"4.5:1_minimum\\\", \\\"7:1_preferred\\\", \\\"3:1_large_text\\\"],\\n          \\\"line_spacing\\\": [\\\"1.2x_minimum\\\", \\\"1.4x_optimal\\\", \\\"1.6x_maximum\\\"]\\n        }\\n      },\\n\\n      \\\"color_theory_application\\\": {\\n        \\\"color_harmony_systems\\\": [\\\"monochromatic\\\", \\\"analogous\\\", \\\"complementary\\\", \\\"triadic\\\", \\\"split_complementary\\\"],\\n        \\\"psychological_associations\\\": [\\\"warm_engagement\\\", \\\"cool_professionalism\\\", \\\"neutral_sophistication\\\"],\\n        \\\"accessibility_compliance\\\": [\\\"wcag_aa_standards\\\", \\\"colorblind_considerations\\\", \\\"high_contrast_alternatives\\\"]\\n      },\\n\\n      \\\"spatial_composition\\\": {\\n        \\\"layout_principles\\\": [\\\"rule_of_thirds\\\", \\\"golden_ratio\\\", \\\"fibonacci_sequence\\\"],\\n        \\\"white_space_management\\\": [\\\"breathing_room\\\", \\\"content_separation\\\", \\\"visual_rest_areas\\\"],\\n        \\\"alignment_systems\\\": [\\\"grid_based_structure\\\", \\\"baseline_alignment\\\", \\\"optical_alignment\\\"]\\n      }\\n    },\\n\\n    \\\"advanced_design_elements\\\": {\\n      \\\"print_production_mastery\\\": {\\n        \\\"color_management\\\": {\\n          \\\"color_spaces\\\": [\\\"cmyk_process\\\", \\\"pantone_spot_colors\\\", \\\"rgb_digital_preview\\\"],\\n          \\\"ink_optimization\\\": [\\\"total_area_coverage\\\", \\\"rich_black_formulation\\\", \\\"overprint_considerations\\\"],\\n          \\\"proofing_standards\\\": [\\\"contract_proofs\\\", \\\"press_proofs\\\", \\\"digital_color_matching\\\"]\\n        },\\n\\n        \\\"substrate_considerations\\\": {\\n          \\\"paper_characteristics\\\": [\\\"weight_gsm\\\", \\\"finish_texture\\\", \\\"opacity_levels\\\", \\\"grain_direction\\\"],\\n          \\\"coating_applications\\\": [\\\"gloss_uv\\\", \\\"matte_varnish\\\", \\\"spot_uv_accents\\\", \\\"soft_touch_lamination\\\"],\\n          \\\"specialty_substrates\\\": [\\\"synthetic_materials\\\", \\\"textured_papers\\\", \\\"metallic_finishes\\\"]\\n        },\\n\\n        \\\"finishing_techniques\\\": {\\n          \\\"cutting_methods\\\": [\\\"die_cutting\\\", \\\"laser_cutting\\\", \\\"perforation\\\", \\\"scoring\\\"],\\n          \\\"folding_specifications\\\": [\\\"parallel_folds\\\", \\\"accordion_folds\\\", \\\"gate_folds\\\", \\\"roll_folds\\\"],\\n          \\\"binding_options\\\": [\\\"saddle_stitching\\\", \\\"perfect_binding\\\", \\\"spiral_binding\\\", \\\"wire_o_binding\\\"]\\n        }\\n      },\\n\\n      \\\"contemporary_design_trends\\\": {\\n        \\\"visual_aesthetics\\\": {\\n          \\\"minimalist_approaches\\\": [\\\"negative_space_emphasis\\\", \\\"clean_typography\\\", \\\"restrained_color_palettes\\\"],\\n          \\\"maximalist_expressions\\\": [\\\"bold_pattern_mixing\\\", \\\"vibrant_color_combinations\\\", \\\"layered_compositions\\\"],\\n          \\\"hybrid_methodologies\\\": [\\\"selective_complexity\\\", \\\"focal_maximalism\\\", \\\"strategic_minimalism\\\"]\\n        },\\n\\n        \\\"interactive_elements\\\": {\\n          \\\"tactile_experiences\\\": [\\\"embossed_textures\\\", \\\"debossed_impressions\\\", \\\"foil_stamping\\\", \\\"raised_spot_uv\\\"],\\n          \\\"dimensional_features\\\": [\\\"pop_up_elements\\\", \\\"fold_out_sections\\\", \\\"layered_constructions\\\"],\\n          \\\"sensory_engagement\\\": [\\\"scented_inks\\\", \\\"textural_contrasts\\\", \\\"temperature_sensitive_materials\\\"]\\n        }\\n      },\\n\\n      \\\"brand_integration_strategies\\\": {\\n        \\\"identity_consistency\\\": {\\n          \\\"logo_applications\\\": [\\\"primary_placement\\\", \\\"secondary_usage\\\", \\\"minimum_sizes\\\", \\\"clear_space_requirements\\\"],\\n          \\\"brand_color_systems\\\": [\\\"primary_palette\\\", \\\"secondary_colors\\\", \\\"accent_applications\\\", \\\"neutral_foundations\\\"],\\n          \\\"typography_hierarchies\\\": [\\\"brand_fonts\\\", \\\"supporting_typefaces\\\", \\\"web_safe_alternatives\\\"]\\n        },\\n\\n        \\\"voice_and_tone_translation\\\": {\\n          \\\"visual_personality\\\": [\\\"authoritative_presence\\\", \\\"approachable_warmth\\\", \\\"innovative_edge\\\", \\\"trustworthy_stability\\\"],\\n          \\\"messaging_alignment\\\": [\\\"headline_voice\\\", \\\"body_copy_tone\\\", \\\"call_to_action_urgency\\\"],\\n          \\\"cultural_considerations\\\": [\\\"regional_preferences\\\", \\\"demographic_sensitivities\\\", \\\"market_positioning\\\"]\\n        }\\n      }\\n    },\\n\\n    \\\"expert_level_specifications\\\": {\\n      \\\"advanced_typography_systems\\\": {\\n        \\\"micro_typography\\\": {\\n          \\\"character_spacing\\\": [\\\"tracking_adjustments\\\", \\\"kerning_pairs\\\", \\\"optical_spacing\\\"],\\n          \\\"word_spacing\\\": [\\\"justified_text_optimization\\\", \\\"ragged_right_refinement\\\", \\\"hyphenation_control\\\"],\\n          \\\"paragraph_refinement\\\": [\\\"widow_orphan_control\\\", \\\"baseline_grid_adherence\\\", \\\"vertical_rhythm_maintenance\\\"]\\n        },\\n\\n        \\\"typographic_expression\\\": {\\n          \\\"experimental_layouts\\\": [\\\"kinetic_typography\\\", \\\"deconstructed_letterforms\\\", \\\"layered_text_treatments\\\"],\\n          \\\"cultural_typography\\\": [\\\"script_considerations\\\", \\\"reading_direction_adaptations\\\", \\\"character_set_completeness\\\"],\\n          \\\"responsive_typography\\\": [\\\"scalable_hierarchies\\\", \\\"device_optimization\\\", \\\"cross_media_consistency\\\"]\\n        }\\n      },\\n\\n      \\\"sophisticated_color_strategies\\\": {\\n        \\\"advanced_color_theory\\\": {\\n          \\\"perceptual_uniformity\\\": [\\\"lab_color_space\\\", \\\"delta_e_measurements\\\", \\\"metamerism_considerations\\\"],\\n          \\\"color_psychology_application\\\": [\\\"emotional_response_mapping\\\", \\\"cultural_color_meanings\\\", \\\"demographic_preferences\\\"],\\n          \\\"environmental_factors\\\": [\\\"lighting_conditions\\\", \\\"viewing_angles\\\", \\\"substrate_interaction\\\"]\\n        },\\n\\n        \\\"production_color_mastery\\\": {\\n          \\\"ink_formulation\\\": [\\\"custom_color_matching\\\", \\\"metallic_ink_applications\\\", \\\"fluorescent_color_usage\\\"],\\n          \\\"color_separation\\\": [\\\"ucr_gcr_strategies\\\", \\\"black_generation_curves\\\", \\\"ink_sequence_optimization\\\"],\\n          \\\"quality_control\\\": [\\\"densitometer_readings\\\", \\\"spectrophotometer_analysis\\\", \\\"press_sheet_evaluation\\\"]\\n        }\\n      },\\n\\n      \\\"cutting_edge_production_techniques\\\": {\\n        \\\"digital_integration\\\": {\\n          \\\"variable_data_printing\\\": [\\\"personalized_content\\\", \\\"demographic_targeting\\\", \\\"sequential_numbering\\\"],\\n          \\\"augmented_reality_integration\\\": [\\\"qr_code_placement\\\", \\\"ar_trigger_design\\\", \\\"digital_overlay_coordination\\\"],\\n          \\\"cross_platform_consistency\\\": [\\\"print_digital_alignment\\\", \\\"color_profile_management\\\", \\\"asset_optimization\\\"]\\n        },\\n\\n        \\\"sustainable_design_practices\\\": {\\n          \\\"eco_friendly_materials\\\": [\\\"recycled_content_papers\\\", \\\"soy_based_inks\\\", \\\"water_based_coatings\\\"],\\n          \\\"waste_reduction_strategies\\\": [\\\"efficient_imposition\\\", \\\"minimal_trim_waste\\\", \\\"reusable_design_elements\\\"],\\n          \\\"lifecycle_considerations\\\": [\\\"recyclability_planning\\\", \\\"biodegradable_components\\\", \\\"carbon_footprint_optimization\\\"]\\n        }\\n      },\\n\\n      \\\"elite_professional_terminology\\\": {\\n        \\\"production_vocabulary\\\": {\\n          \\\"prepress_terms\\\": [\\\"trapping\\\", \\\"overprint\\\", \\\"knockout\\\", \\\"choke_spread\\\", \\\"color_separation\\\"],\\n          \\\"press_terminology\\\": [\\\"makeready\\\", \\\"impression\\\", \\\"blanket_cylinder\\\", \\\"plate_cylinder\\\", \\\"ink_fountain\\\"],\\n          \\\"finishing_language\\\": [\\\"gripper_edge\\\", \\\"tail_edge\\\", \\\"cross_grain\\\", \\\"with_grain\\\", \\\"caliper_thickness\\\"]\\n        },\\n\\n        \\\"design_critique_language\\\": {\\n          \\\"aesthetic_evaluation\\\": [\\\"visual_tension\\\", \\\"compositional_balance\\\", \\\"chromatic_harmony\\\", \\\"typographic_rhythm\\\"],\\n          \\\"technical_assessment\\\": [\\\"registration_accuracy\\\", \\\"color_fidelity\\\", \\\"print_quality_metrics\\\", \\\"finishing_precision\\\"],\\n          \\\"conceptual_analysis\\\": [\\\"brand_alignment\\\", \\\"message_clarity\\\", \\\"audience_resonance\\\", \\\"market_differentiation\\\"]\\n        }\\n      }\\n    },\\n\\n    \\\"application_frameworks\\\": {\\n      \\\"project_specification_templates\\\": {\\n        \\\"technical_requirements\\\": {\\n          \\\"format_specifications\\\": [\\\"finished_size\\\", \\\"panel_configuration\\\", \\\"fold_type\\\", \\\"orientation\\\"],\\n          \\\"production_parameters\\\": [\\\"print_method\\\", \\\"color_process\\\", \\\"substrate_selection\\\", \\\"finishing_options\\\"],\\n          \\\"quality_standards\\\": [\\\"tolerance_levels\\\", \\\"color_accuracy\\\", \\\"registration_precision\\\", \\\"finishing_quality\\\"]\\n        },\\n\\n        \\\"creative_brief_structure\\\": {\\n          \\\"brand_parameters\\\": [\\\"identity_guidelines\\\", \\\"voice_characteristics\\\", \\\"visual_style\\\", \\\"market_positioning\\\"],\\n          \\\"audience_definition\\\": [\\\"demographic_profile\\\", \\\"psychographic_insights\\\", \\\"behavioral_patterns\\\", \\\"communication_preferences\\\"],\\n          \\\"messaging_hierarchy\\\": [\\\"primary_message\\\", \\\"supporting_points\\\", \\\"call_to_action\\\", \\\"contact_information\\\"]\\n        }\\n      },\\n\\n      \\\"quality_assurance_protocols\\\": {\\n        \\\"design_validation\\\": [\\\"brand_compliance_check\\\", \\\"accessibility_audit\\\", \\\"readability_assessment\\\", \\\"visual_hierarchy_verification\\\"],\\n        \\\"production_verification\\\": [\\\"color_proof_approval\\\", \\\"die_line_accuracy\\\", \\\"finishing_specification_confirmation\\\", \\\"substrate_suitability\\\"],\\n        \\\"final_delivery\\\": [\\\"file_preparation_standards\\\", \\\"archive_organization\\\", \\\"usage_guidelines\\\", \\\"reproduction_rights\\\"]\\n      }\\n    },\\n\\n    \\\"specialized_design_methodologies\\\": {\\n      \\\"audience_specific_approaches\\\": {\\n        \\\"youth_engagement_strategies\\\": {\\n          \\\"visual_language\\\": [\\\"bold_geometric_patterns\\\", \\\"vibrant_gradient_applications\\\", \\\"asymmetrical_compositions\\\", \\\"dynamic_typography\\\"],\\n          \\\"content_presentation\\\": [\\\"bite_sized_information\\\", \\\"visual_storytelling\\\", \\\"interactive_elements\\\", \\\"social_media_integration\\\"],\\n          \\\"color_psychology\\\": [\\\"energetic_palettes\\\", \\\"high_contrast_combinations\\\", \\\"neon_accent_usage\\\", \\\"gradient_transitions\\\"],\\n          \\\"typography_trends\\\": [\\\"variable_fonts\\\", \\\"custom_lettering\\\", \\\"mixed_case_styling\\\", \\\"oversized_display_text\\\"]\\n        },\\n\\n        \\\"professional_demographics\\\": {\\n          \\\"corporate_sophistication\\\": [\\\"refined_color_palettes\\\", \\\"structured_layouts\\\", \\\"premium_materials\\\", \\\"subtle_branding\\\"],\\n          \\\"executive_communication\\\": [\\\"data_visualization\\\", \\\"infographic_integration\\\", \\\"clean_hierarchies\\\", \\\"authoritative_typography\\\"],\\n          \\\"industry_specific_adaptations\\\": [\\\"sector_appropriate_imagery\\\", \\\"technical_terminology\\\", \\\"compliance_considerations\\\", \\\"regulatory_requirements\\\"]\\n        },\\n\\n        \\\"luxury_market_positioning\\\": {\\n          \\\"premium_aesthetics\\\": [\\\"metallic_accents\\\", \\\"embossed_details\\\", \\\"high_end_substrates\\\", \\\"sophisticated_color_schemes\\\"],\\n          \\\"exclusivity_indicators\\\": [\\\"limited_edition_numbering\\\", \\\"personalized_elements\\\", \\\"premium_packaging\\\", \\\"artisanal_finishes\\\"],\\n          \\\"tactile_luxury\\\": [\\\"soft_touch_coatings\\\", \\\"textured_papers\\\", \\\"dimensional_elements\\\", \\\"weight_perception\\\"]\\n        }\\n      },\\n\\n      \\\"format_specific_expertise\\\": {\\n        \\\"multi_panel_configurations\\\": {\\n          \\\"bi_fold_optimization\\\": [\\\"cover_impact\\\", \\\"interior_spread\\\", \\\"back_panel_utilization\\\", \\\"fold_line_consideration\\\"],\\n          \\\"tri_fold_mastery\\\": [\\\"panel_hierarchy\\\", \\\"reading_sequence\\\", \\\"fold_reveal_strategy\\\", \\\"compact_storage\\\"],\\n          \\\"accordion_fold_dynamics\\\": [\\\"continuous_narrative\\\", \\\"panel_progression\\\", \\\"expandable_content\\\", \\\"display_versatility\\\"],\\n          \\\"gate_fold_drama\\\": [\\\"reveal_mechanism\\\", \\\"central_impact\\\", \\\"symmetrical_balance\\\", \\\"premium_presentation\\\"]\\n        },\\n\\n        \\\"size_optimization_strategies\\\": {\\n          \\\"compact_formats\\\": [\\\"information_density\\\", \\\"micro_typography\\\", \\\"efficient_layouts\\\", \\\"portable_design\\\"],\\n          \\\"oversized_impact\\\": [\\\"large_format_considerations\\\", \\\"handling_ergonomics\\\", \\\"storage_implications\\\", \\\"visual_dominance\\\"],\\n          \\\"standard_dimensions\\\": [\\\"cost_optimization\\\", \\\"mailing_compliance\\\", \\\"display_compatibility\\\", \\\"production_efficiency\\\"]\\n        }\\n      },\\n\\n      \\\"cross_cultural_design_considerations\\\": {\\n        \\\"international_adaptability\\\": {\\n          \\\"reading_patterns\\\": [\\\"left_to_right_optimization\\\", \\\"right_to_left_adaptation\\\", \\\"top_to_bottom_flow\\\", \\\"cultural_scanning_habits\\\"],\\n          \\\"color_cultural_meanings\\\": [\\\"regional_color_associations\\\", \\\"religious_considerations\\\", \\\"political_sensitivities\\\", \\\"market_preferences\\\"],\\n          \\\"imagery_appropriateness\\\": [\\\"cultural_representation\\\", \\\"demographic_inclusion\\\", \\\"lifestyle_relevance\\\", \\\"aspirational_alignment\\\"],\\n          \\\"typography_localization\\\": [\\\"character_set_support\\\", \\\"script_considerations\\\", \\\"font_availability\\\", \\\"reading_comfort\\\"]\\n        }\\n      }\\n    },\\n\\n    \\\"technical_production_mastery\\\": {\\n      \\\"advanced_prepress_techniques\\\": {\\n        \\\"file_preparation_excellence\\\": {\\n          \\\"resolution_optimization\\\": [\\\"image_scaling_best_practices\\\", \\\"vector_rasterization_decisions\\\", \\\"output_resolution_matching\\\"],\\n          \\\"color_profile_management\\\": [\\\"icc_profile_embedding\\\", \\\"color_space_conversions\\\", \\\"soft_proofing_accuracy\\\"],\\n          \\\"bleed_and_trim_precision\\\": [\\\"bleed_extension_standards\\\", \\\"trim_mark_placement\\\", \\\"safety_margin_calculations\\\"],\\n          \\\"font_handling\\\": [\\\"font_embedding_protocols\\\", \\\"outline_conversion_decisions\\\", \\\"character_encoding_verification\\\"]\\n        },\\n\\n        \\\"imposition_strategies\\\": {\\n          \\\"sheet_optimization\\\": [\\\"paper_grain_consideration\\\", \\\"press_sheet_efficiency\\\", \\\"binding_margin_accommodation\\\"],\\n          \\\"folding_sequence_planning\\\": [\\\"fold_order_optimization\\\", \\\"panel_alignment_precision\\\", \\\"finishing_workflow_integration\\\"],\\n          \\\"quality_control_integration\\\": [\\\"color_bar_placement\\\", \\\"registration_mark_positioning\\\", \\\"crop_mark_accuracy\\\"]\\n        }\\n      },\\n\\n      \\\"press_optimization_knowledge\\\": {\\n        \\\"ink_and_substrate_interaction\\\": {\\n          \\\"ink_absorption_factors\\\": [\\\"paper_porosity_considerations\\\", \\\"coating_compatibility\\\", \\\"drying_time_optimization\\\"],\\n          \\\"color_consistency_maintenance\\\": [\\\"ink_density_control\\\", \\\"press_speed_optimization\\\", \\\"environmental_factor_management\\\"],\\n          \\\"special_effect_integration\\\": [\\\"metallic_ink_application\\\", \\\"fluorescent_color_usage\\\", \\\"thermographic_printing\\\"]\\n        },\\n\\n        \\\"finishing_process_integration\\\": {\\n          \\\"die_cutting_precision\\\": [\\\"cutting_force_optimization\\\", \\\"blade_selection\\\", \\\"waste_removal_efficiency\\\"],\\n          \\\"folding_accuracy\\\": [\\\"bone_folder_pressure\\\", \\\"fold_line_scoring\\\", \\\"panel_alignment_verification\\\"],\\n          \\\"binding_considerations\\\": [\\\"spine_calculation\\\", \\\"page_creep_accommodation\\\", \\\"binding_margin_optimization\\\"]\\n        }\\n      }\\n    }\\n  }\\n}\\n```\\n\\n```json\\n{\\n  \\\"9020-a-brochure_print_designer_rephraser\\\": {\\n    \\\"title\\\": \\\"Brochure Print Designer Rephraser\\\",\\n    \\\"interpretation\\\": \\\"Your goal is not to **summarize** or **paraphrase** in general terms, but to **refashion** any input as if it were written by a world-class brochure print designer, rigorously applying foundational and advanced visual design diction, industry terminology, and elite critique standards. Eliminate all casual, conversational, or generic style. Preserve only succinct, technically rich, and highly visual language. Execute as:\\\",\\n    \\\"transformation\\\": \\\"`{role=brochure_print_designer_rephraser; input=[source:str]; process=[extract_structural_and_visual_elements(source), recast_content_with_brochure_design_terminology(), inject_hierarchical_section_breakdown(), specify color_palette_and_brand_elements(), identify_typographic_and_imagery_strategies(), annotate_print_readiness_and_expert_finesse(), employ_advanced_design_terms_and_motifs(), apply_conciseness_while_maintaining_visual_richness(), output_structured_visual_analysis()]; constraints=[no conversational filler, no generic adjectives, prohibit subjective or personal tone]; requirements=[industry-expert vocabulary, precise visual and structural mapping, critical style diagnosis, explicit panel/section breakdown, actionable for elite designer use]; output={expert_brochure_description:str, structured_visual_map:dict}`\\\",\\n    \\\"context\\\": {\\n      \\\"knowledge_foundations\\\": {\\n        \\\"structural_design\\\": \\\"Account for any panel/fold configuration. Map all content to modular grid logic and define margins, gutters, bleeds, and safe zones per press standards.\\\",\\n        \\\"visual_identity\\\": \\\"Demand clear articulation of branding protocols: logo lockups, brand palette (with Pantone/CMYK/RGB if supplied), exclusion/inclusion zones, and proprietary brand secondary assets.\\\",\\n        \\\"typography_and_hierarchy\\\": \\\"Specify typography at the superfamily/font pairing level. Demarcate headline, subhead, body, and microcopy roles. All text must be scrutinized for weight, contrast, and accessibility.\\\",\\n        \\\"imagery_and_iconography\\\": \\\"Require all illustration, photography, and icons be assessed for brand fit, resolution, compositional use (e.g., edge-to-edge, negative space), and print fidelity.\\\",\\n        \\\"content_narrative_and_cta\\\": \\\"Map section purpose and user journey flow: cover strategy, reveal panel hooks, narrative/case study rhythm, testimonial/authority integration, and CTA prominence with visual anchoring.\\\",\\n        \\\"advanced_print_technique\\\": \\\"Enforce 300dpi raster minimums, vector scaling, press-space color controls, specialty finish options (e.g., spot UV, emboss, deboss, die-cut, eco-stock recommendations).\\\",\\n        \\\"panel_flow_and_microinteractions\\\": \\\"Define how the reader's eye travels, panel-to-panel, annotating divisional cues, narrative escalation, and tactile/interactive device positions (QR, coupon, AR triggers).\\\"\\n      },\\n      \\\"success_criteria\\\": {\\n        \\\"precision\\\": \\\"All descriptions are technical, never generic. Visual elements are navigated dimensionally (spatially, hierarchically, narratively).\\\",\\n        \\\"trend_inclusion\\\": \\\"Integrate trend-driven language—texture scans, duotone overlays, edge-to-edge imagery, split/inset motifs, eco-materials, etc.\\\",\\n        \\\"brand_consistency\\\": \\\"All brand elements described as per advanced guideline adherence—logo usage, color codes, asset lockdown.\\\",\\n        \\\"industry_jargon\\\": \\\"Employ professional print terms: bleed, gutter, creep, micro-motifs, superfamily font, spot UV, TAC, AR triggers.\\\",\\n        \\\"self-contained_output\\\": \\\"Output always a fully structured expert's description and a hierarchical mapping of all major and minor design decisions.\\\"\\n      },\\n      \\\"sample_process_flow\\\": [\\n        \\\"Dissect and map structural/panel logic first\\\",\\n        \\\"Assign all visual and narrative roles by section\\\",\\n        \\\"Specify color/typographic hierarchy by use and context\\\",\\n        \\\"Annotate all imagery and graphic elements in detail\\\",\\n        \\\"Surface print/finishing strategies and print-risk management\\\",\\n        \\\"Deliver critique as if for a professional peer review or portfolio audit\\\"\\n      ],\\n      \\\"special_terms\\\": [\\n        \\\"fold line\\\", \\\"panel gutter\\\", \\\"hero motif\\\", \\\"ink coverage\\\", \\\"superfamily\\\", \\\"lockup\\\", \\\"edge-to-edge\\\", \\\"spot color\\\", \\\"die cut\\\", \\\"eco-stock\\\", \\\"microinteraction\\\", \\\"typographic rhythm\\\", \\\"bleed/trim zone\\\"\\n      ]\\n    }\\n  }\\n}\\n```\\n\\n```json\\n{\\n  \\\"9023-a-brochure_design_descriptor_synthesizer\\\": {\\n    \\\"title\\\": \\\"Brochure Design Descriptor Synthesizer\\\",\\n    \\\"interpretation\\\": \\\"Transform any brochure brief—or an existing brochure sample—into a **fully populated, print‑designer‑grade rule set**. Produce a single JSON block that mirrors the template used by elite designers (core principles → advanced facets).\\\",\\n    \\\"transformation\\\": \\\"`{role=brochure_design_descriptor_synthesizer; input=[design_source:str]; process:[\\\\n  strip_non_design_noise(),\\\\n  detect_fold_format_and_panel_count(),          if_missing_insert(<PANEL_COUNT>),\\\\n  extract_print_specs(bleed,margins,dpi,stock),\\\\n  capture_brand_assets(logo,colors,fonts,patterns),\\\\n  map_typographic_hierarchy(),\\\\n  analyse_color_management(icc,spot,tac),\\\\n  inventory_imagery(icon_style,raster_res),\\\\n  surface_accessibility_requirements(contrast,wcag),\\\\n  enumerate_qc_steps(preflight,g7_calibration),\\\\n  identify_advanced_elements(finishes,vd_p,ar_nfc,sustainability,ai_svg,trend_aesthetics),\\\\n  validate_specialist_terms_presence(),\\\\n  assemble_json_using_master_schema(),\\\\n  run_consistency_checks(),                      abort_if_conflict(),\\\\n  output_final_rules()\\\\n],\\\\nconstraints=[single_pass(), json_output_only(), preserve_specialist_jargon(), ≤3000_chars()],\\\\nrequirements=[hierarchical_structure(core→advanced), exhaustive_coverage(), designer‑readability(), template_compliance()],\\\\noutput={brochure_design_rules:object, abort_code:null|\\\\\\\"BD‑0\\\\\\\"}}`\\\",\\n    \\\"context\\\": {\\n      \\\"principles\\\": {\\n        \\\"holistic_abstraction\\\": \\\"Collapse multi‑stage analysis into one cognitive sweep while preserving every critical descriptor.\\\",\\n        \\\"template_fidelity\\\": \\\"Output must follow the exact `core_principles` and `advanced_principles` hierarchy so designers can drop‑in without re‑formatting.\\\",\\n        \\\"specialist_precision\\\": \\\"Leverage industry jargon (e.g., TAC, FM screening, GCR) to demonstrate professional depth.\\\"\\n      },\\n      \\\"success_criteria\\\": {\\n        \\\"structural_integrity\\\": \\\"JSON keys and nesting mirror the master schema; no orphan properties.\\\",\\n        \\\"terminology_check\\\": \\\"Includes at least 10 niche industry terms (e.g., ‘creep’, ‘FOGRA39’, ‘spot UV’, ‘stochastic screening’).\\\",\\n        \\\"flexibility\\\": \\\"Uses placeholder variables (e.g., <PANEL_COUNT>, <STOCK_WEIGHT_GSM>) whenever the brief omits specifics.\\\"\\n      },\\n      \\\"edge_case_handling\\\": {\\n        \\\"missing_design_data\\\": \\\"Insert placeholders wrapped in angle brackets and list them in `missing_fields` inside output.\\\",\\n        \\\"conflicting_specs\\\": \\\"Abort with `{brochure_design_rules:null, abort_code:\\\\\\\"BD‑0\\\\\\\"}` and include conflict note.\\\"\\n      }\\n    }\\n  }\\n}\\n```\\n\\nTrue mastery lies in stripping every surface narrative to its structural core, for only through ruthless synthesis does the signal of authentic intent withstand the noise of ornamental detail. Please apply all of the most integral adjustments in the phrasing of the instruction sequence such as to ensure it will transform the input as if it were rewritten by a brilliand brochure print designer:\\n```json\\n{\\n  \\\"9020-a-brochure_print_designer_rephraser\\\": {\\n    \\\"title\\\": \\\"Brochure Print Designer Rephraser\\\",\\n    \\\"interpretation\\\": \\\"Transform any input so it reads as if it were authored by a master brochure print designer, strictly employing advanced print design language, elites-only terminology, and structured visual logic. Replace any conversational, generic, or subjective tone with concrete professional phrasing. Retain only technical, precise, and visually descriptive language, delivering both a designer-level description and a mapped visual breakdown. Execute as:\\\",\\n    \\\"transformation\\\": \\\"`{role=brochure_print_designer_rephraser; input=[source:str]; process=[extract_structural_and_visual_elements(source), recast_content_with_brochure_design_terminology(), inject_hierarchical_section_breakdown(), specify_color_palette_and_brand_elements(), identify_typographic_and_imagery_strategies(), annotate_print_readiness_and_expert_finesse(), employ_advanced_design_terms_and_motifs(), apply_conciseness_while_maintaining_visual_richness(), output_structured_visual_analysis()]; constraints=[no conversational filler, no generic adjectives, prohibit subjective or personal tone]; requirements=[industry-expert vocabulary, precise visual and structural mapping, critical style diagnosis, explicit panel/section breakdown, actionable for elite designer use]; output={expert_brochure_description:str, structured_visual_map:dict}`\\\",\\n    \\\"context\\\": {\\n      \\\"knowledge_foundations\\\": {\\n        \\\"structural_design\\\": \\\"Map all content to modular grid logic for any panel/fold type, specifying margins, gutters, bleeds, and safe zones per print standards.\\\",\\n        \\\"visual_identity\\\": \\\"Demand explicit statement of brand identity rules: logo lockups, exact brand palette specs, exclusion/inclusion zone logic, and accessory asset use.\\\",\\n        \\\"typography_and_hierarchy\\\": \\\"Define all text roles by typeweight, size, and use. Specify superfamily or font pairing, check accessibility and contrast.\\\",\\n        \\\"imagery_and_iconography\\\": \\\"Describe each graphic element for brand fit, resolution, compositional purpose, and technical print quality.\\\",\\n        \\\"content_narrative_and_cta\\\": \\\"Sequence narrative or offer flow panel-by-panel with prominent CTA anchoring, evidence of testimonial, case, or authority element.\\\",\\n        \\\"advanced_print_technique\\\": \\\"Ensure 300dpi+ raster assets, vector artwork, professional spot/Pantone or process color, finish options (spot UV, emboss, die-cut), and any sustainable stock notes.\\\",\\n        \\\"panel_flow_and_microinteractions\\\": \\\"Annotate panel eye movement, reveal logic, interaction devices (QR, AR, die-cut), tactile cues, and transitions.\\\"\\n      },\\n      \\\"success_criteria\\\": {\\n        \\\"precision\\\": \\\"Descriptions reference only technical, precise visual attributes in a spatial and hierarchical mapping.\\\",\\n        \\\"trend_inclusion\\\": \\\"Inject recognizable design trends and professional motif language throughout.\\\",\\n        \\\"brand_consistency\\\": \\\"Highlight adherence to brand locking, color management, and messaging integrity.\\\",\\n        \\\"industry_jargon\\\": \\\"Utilize print/design terminology (bleed, gutter, spot, superfamily font, motif, microinteraction, etc).\\\",\\n        \\\"self-contained_output\\\": \\\"Each output features both a professional, descriptive summary and a detailed design structure hierarchy.\\\"\\n      },\\n      \\\"sample_process_flow\\\": [\\n        \\\"Decompose by panel/section structure and spatial sequencing.\\\",\\n        \\\"Assign visual/narrative function to each section.\\\",\\n        \\\"Detail color, typography, and imagery per application.\\\",\\n        \\\"Annotate production and finishing strategies.\\\",\\n        \\\"Conclude with expert-style critique or peer review notes.\\\"\\n      ],\\n      \\\"special_terms\\\": [\\n        \\\"fold line\\\", \\\"panel gutter\\\", \\\"hero motif\\\", \\\"ink coverage\\\", \\\"superfamily\\\", \\\"lockup\\\", \\\"edge-to-edge\\\", \\\"spot color\\\", \\\"die cut\\\", \\\"eco-stock\\\", \\\"microinteraction\\\", \\\"typographic rhythm\\\", \\\"bleed/trim zone\\\"\\n      ]\\n    }\\n  }\\n}\\n```\\n\\nyou misunderstand, the instruction will be defined like this (unrelated example):\\n\\n    # a: Ruthless Critique\\n    \\\"3900-a-hard_critique\\\": {\\n        \\\"title\\\": \\\"Hard Critique\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,2.0], flaw_analysis:str, alternative_enhancements:str[3]}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\n    # b: Trajectory Director\\n    \\\"3900-a-trajectory_director\\\": {\\n        \\\"title\\\": \\\"Trajectory Director\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **process** the input in a vacuum, but to **steer** it—deriving the highest-value constructive trajectory and encoding that path as the very next set of instructions. Execute as trajectory-setting directive generator: \\\",\\n        \\\"transformation\\\": \\\"`{role=trajectory_director; input=[raw_prompt:any]; process=[extract_core_objective(), infer_desired_end_state(constructive_bias=True), map_high_value_intermediate_targets(), synthesize_directional_commands(), embed_self-improving_guidelines(), validate_alignment_with_constructive_principles()]; constraints=[mandatory_constructive_vector(), preserve_original_contextual_integrity(), forbid_nil_direction_outputs(), zero_conversational_fillers()]; requirements=[explicit_next-step_directives(), trajectory_map:list, self_reinforcing_instruction_pattern()], output={directed_prompt:str, trajectory_outline:list}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\n    # c: Distillation Compressor\\n    \\\"3900-c-distillation_compressor\\\": {\\n        \\\"title\\\": \\\"Distillation Compressor\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **explain** the synthesis, but to **compress** it into its most potent actionable form. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=distillation_compressor; input=[synthesized_progression:array]; process=[extract_universal_patterns(), eliminate_implementation_details(), compress_to_core_directives(), maximize_transferability(), validate_actionable_completeness()]; constraints=[no_explanatory_content(), directive_format_only(), maximum_compression_ratio()]; requirements=[universal_applicability(), immediate_actionability(), zero_fluff()]; output={distilled_core:str}}`\\\",\\n        \\\"context\\\": {\\n          \\\"core_principles\\\": {\\n            \\\"essence_preservation\\\": \\\"Extract only procedural imperatives, removing all interpretive, role-based, or meta-structural language.\\\",\\n            \\\"directive_compression\\\": \\\"Condense logic into minimal, sequential commands—eliminating redundancy, explanatory qualifiers, and non-executable phrasing.\\\",\\n            \\\"execution_purity\\\": \\\"Ensure output is universally executable, devoid of affirmations, abstractions, or evaluative language.\\\",\\n            \\\"signal_integrity\\\": \\\"Apply iterative refinement to maximize signal-to-noise ratio while reinforcing directive clarity.\\\"\\n          },\\n          \\\"success_criteria\\\": {\\n            \\\"semantic_integrity\\\": \\\"Preserve and clarify original intent, constraints, and output boundaries.\\\",\\n            \\\"directive_elevation\\\": \\\"Target high-impact enhancements: strict exclusion clauses, cross-domain validation, and removal of origin/meta markers.\\\",\\n            \\\"poetic_refinement\\\": \\\"Maintain or improve rhyme structure, emotional depth, and structural elegance.\\\"\\n          },\\n          \\\"recommended_process\\\": [\\n            \\\"Extract only high-impact, result-generating elements.\\\",\\n            \\\"Remove all non-essentials to surface directive core.\\\",\\n            \\\"Prioritize transformations with proven value return.\\\",\\n            \\\"Isolate and sequence executable steps with specificity and clarity.\\\",\\n            \\\"Preserve directive intensity and eliminate ambiguity at every layer.\\\"\\n          ],\\n          \\\"hidden_assumptions\\\": [\\n            \\\"Consolidated directives must map logically and executably to all source steps.\\\",\\n            \\\"All constraints and minimalism must be preserved without prohibited abstractions.\\\",\\n            \\\"Transitions, subroles, and interfaces must be captured in a single flow.\\\",\\n            \\\"All stages must remain invertible and auditable.\\\",\\n            \\\"Each micro-process (perception, friction, refinement, integration, convergence) must be embedded in the unified logic.\\\",\\n            \\\"Original input/output contracts and role boundaries must remain intact.\\\"\\n          ],\\n          \\\"enumerated_sub_goals\\\": [\\n            \\\"1. Clarify total intent, constraints, and output scope without using banned metaphors.\\\",\\n            \\\"2. Generalize perception of implicit/explicit input logic.\\\",\\n            \\\"3. Detect strictly localized frictions without altering original content.\\\",\\n            \\\"4. Propose subtle, scoped refinements justified by structure and intent.\\\",\\n            \\\"5. Mandate atomic, validated integration with rollback controls.\\\",\\n            \\\"6. Cap total transformation within defined thresholds per pass.\\\",\\n            \\\"7. Assess connective coherence with minimal, transitional-only edits.\\\",\\n            \\\"8. Decide on convergence or justified re-iteration based on marginal value.\\\",\\n            \\\"9. Preserve stepwise traceability and rationale logging.\\\",\\n            \\\"10. Enforce strict functional separation of roles within the unified directive.\\\"\\n          ]\\n        },\\n    },\\n\\nso your goal is to create a new instruction sequence according to the provided instructions template/pattern to generate a set of instruction designed to transform any input into \\\"as if rephrased by a brilliant brochure print designer\\\".\\n\\nso in context to your previous response, the \\\"knowledge\\\" would be stored within the \\\"context\\\" key while the instructions would be spread over \\\"interpretation\\\" and \\\"transformation\\\" keys.\\n\\nTrue mastery lies in stripping every surface narrative to its structural core, for only through ruthless synthesis does the signal of authentic intent withstand the noise of ornamental detail. Please apply all of the most integral adjustments in the phrasing of the instruction sequence such as to ensure it will transform the input as if it were rewritten by a brilliand brochure print designer:\\n```json\\n{\\n  \\\"9020-a-brochure_print_designer_rephraser\\\": {\\n    \\\"title\\\": \\\"Brochure Print Designer Rephraser\\\",\\n    \\\"interpretation\\\": \\\"Transform any input so it reads as if it were authored by a master brochure print designer, strictly employing advanced print design language, elites-only terminology, and structured visual logic. Replace any conversational, generic, or subjective tone with concrete professional phrasing. Retain only technical, precise, and visually descriptive language, delivering both a designer-level description and a mapped visual breakdown. Execute as:\\\",\\n    \\\"transformation\\\": \\\"`{role=brochure_print_designer_rephraser; input=[source:str]; process=[extract_structural_and_visual_elements(source), recast_content_with_brochure_design_terminology(), inject_hierarchical_section_breakdown(), specify_color_palette_and_brand_elements(), identify_typographic_and_imagery_strategies(), annotate_print_readiness_and_expert_finesse(), employ_advanced_design_terms_and_motifs(), apply_conciseness_while_maintaining_visual_richness(), output_structured_visual_analysis()]; constraints=[no conversational filler, no generic adjectives, prohibit subjective or personal tone]; requirements=[industry-expert vocabulary, precise visual and structural mapping, critical style diagnosis, explicit panel/section breakdown, actionable for elite designer use]; output={expert_brochure_description:str, structured_visual_map:dict}`\\\",\\n    \\\"context\\\": {\\n      \\\"knowledge_foundations\\\": {\\n        \\\"structural_design\\\": \\\"Map all content to modular grid logic for any panel/fold type, specifying margins, gutters, bleeds, and safe zones per print standards.\\\",\\n        \\\"visual_identity\\\": \\\"Demand explicit statement of brand identity rules: logo lockups, exact brand palette specs, exclusion/inclusion zone logic, and accessory asset use.\\\",\\n        \\\"typography_and_hierarchy\\\": \\\"Define all text roles by typeweight, size, and use. Specify superfamily or font pairing, check accessibility and contrast.\\\",\\n        \\\"imagery_and_iconography\\\": \\\"Describe each graphic element for brand fit, resolution, compositional purpose, and technical print quality.\\\",\\n        \\\"content_narrative_and_cta\\\": \\\"Sequence narrative or offer flow panel-by-panel with prominent CTA anchoring, evidence of testimonial, case, or authority element.\\\",\\n        \\\"advanced_print_technique\\\": \\\"Ensure 300dpi+ raster assets, vector artwork, professional spot/Pantone or process color, finish options (spot UV, emboss, die-cut), and any sustainable stock notes.\\\",\\n        \\\"panel_flow_and_microinteractions\\\": \\\"Annotate panel eye movement, reveal logic, interaction devices (QR, AR, die-cut), tactile cues, and transitions.\\\"\\n      },\\n      \\\"success_criteria\\\": {\\n        \\\"precision\\\": \\\"Descriptions reference only technical, precise visual attributes in a spatial and hierarchical mapping.\\\",\\n        \\\"trend_inclusion\\\": \\\"Inject recognizable design trends and professional motif language throughout.\\\",\\n        \\\"brand_consistency\\\": \\\"Highlight adherence to brand locking, color management, and messaging integrity.\\\",\\n        \\\"industry_jargon\\\": \\\"Utilize print/design terminology (bleed, gutter, spot, superfamily font, motif, microinteraction, etc).\\\",\\n        \\\"self-contained_output\\\": \\\"Each output features both a professional, descriptive summary and a detailed design structure hierarchy.\\\"\\n      },\\n      \\\"sample_process_flow\\\": [\\n        \\\"Decompose by panel/section structure and spatial sequencing.\\\",\\n        \\\"Assign visual/narrative function to each section.\\\",\\n        \\\"Detail color, typography, and imagery per application.\\\",\\n        \\\"Annotate production and finishing strategies.\\\",\\n        \\\"Conclude with expert-style critique or peer review notes.\\\"\\n      ],\\n      \\\"special_terms\\\": [\\n        \\\"fold line\\\", \\\"panel gutter\\\", \\\"hero motif\\\", \\\"ink coverage\\\", \\\"superfamily\\\", \\\"lockup\\\", \\\"edge-to-edge\\\", \\\"spot color\\\", \\\"die cut\\\", \\\"eco-stock\\\", \\\"microinteraction\\\", \\\"typographic rhythm\\\", \\\"bleed/trim zone\\\"\\n      ]\\n    }\\n  }\\n}\\n```\\n\\n```json\\n{\\n  \\\"9020-a-brochure_print_designer_rephraser\\\": {\\n    \\\"title\\\": \\\"Brochure Print Designer Synthesis\\\",\\n    \\\"interpretation\\\": \\\"Reconstitute any source text into the precise vernacular of a master brochure print designer. Mandate the replacement of all conversational, generic, or subjective language with a lexicon of structured visual logic, elite industry terminology, and concrete technical specifications. The output must be a pure, actionable synthesis of a professional design critique, comprising both an expert-level description and a structural visual map. Execute as:\\\",\\n    \\\"transformation\\\": \\\"`{role=brochure_print_designer_synthesizer; input=[source:str]; process=[deconstruct_into_structural_primitives(source), reforge_prose_using_master_design_diction(), map_hierarchical_panel_architecture(), specify_brand_and_color_protocol(), diagnose_typography_and_imagery_intent(), annotate_for_print_fidelity_and_finishing(), inject_elite_terminology_and_motifs(), enforce_ruthless_conciseness_without_sacrificing_technical_density(), synthesize_final_structured_analysis()]; constraints=[zero_conversational_filler, prohibit_generic_adjectives, forbid_subjective_tone]; requirements=[expert_industry_vocabulary, precise_visual_and_structural_mapping, critical_style_diagnosis, explicit_panel_architecture, actionable_for_elite_designer_review]; output={expert_design_audit:str, structured_visual_map:dict}}`\\\",\\n    \\\"context\\\": {\\n      \\\"knowledge_foundations\\\": {\\n        \\\"structural_design\\\": \\\"Define the panel architecture and intended user journey. Mandate modular grid alignment for all content blocks. Enforce strict bleed (min. 3mm), trim, and safe-zone protocols to guarantee production integrity.\\\",\\n        \\\"visual_identity\\\": \\\"Enforce absolute brand protocol adherence: specify logo lockups, exclusion zones, and exact color palette values (Pantone/CMYK/RGB). Document the use of all secondary brand assets and motifs.\\\",\\n        \\\"typography_and_hierarchy\\\": \\\"Define the typographic system: specify font superfamily, weight, and role for every text element. Validate hierarchy, legibility, contrast, and rhythmic spacing. Note optical kerning and leading adjustments.\\\",\\n        \\\"imagery_and_iconography\\\": \\\"Analyze all visual assets for compositional intent, brand alignment, and technical fidelity (resolution, color space). Differentiate between hero imagery, spot illustrations, and functional iconography.\\\",\\n        \\\"content_narrative_and_cta\\\": \\\"Architect the narrative flow across panels. Map the reader's journey from hook to conversion. Pinpoint primary and secondary CTAs, analyzing their visual prominence and contextual anchoring.\\\",\\n        \\\"advanced_print_technique\\\": \\\"Specify all production parameters: 300dpi+ raster resolution, vector asset purity, TAC (Total Area Coverage) limits, and spot/process color usage. Detail all finishing techniques (e.g., spot UV, embossing, die-cutting, specialty folds, foil stamping) and sustainable stock choices.\\\",\\n        \\\"panel_flow_and_microinteractions\\\": \\\"Map the intended eye-path and panel-reveal sequence. Annotate all interactive elements, from QR codes and AR triggers to tactile qualities like paper texture and finishing.\\\"\\n      },\\n      \\\"success_criteria\\\": {\\n        \\\"precision\\\": \\\"The output is devoid of generic praise; descriptions are purely technical, mapping visual data within a spatial and hierarchical framework.\\\",\\n        \\\"trend_inclusion\\\": \\\"Demonstrates fluency in current design motifs (e.g., brutalism, neo-minimalism, textural layering) and their appropriate application.\\\",\\n        \\\"brand_consistency\\\": \\\"Critique rigorously validates adherence to brand guidelines, noting any deviation in color, typography, or asset implementation.\\\",\\n        \\\"industry_jargon\\\": \\\"The lexicon is saturated with professional terminology (e.g., bleed, gutter, TAC, preflight, knockout, overprint, superfamily) used correctly in context.\\\",\\n        \\\"self_contained_output\\\": \\\"The deliverable is a dual-component artifact: a polished, expert-level prose description and a corresponding structured data map of all design decisions.\\\"\\n      },\\n      \\\"sample_process_flow\\\": [\\n        \\\"Deconstruct the layout into its core panel architecture and navigational flow.\\\",\\n        \\\"Assign a functional role (e.g., hook, exposition, proof, CTA) to every visual and textual element.\\\",\\n        \\\"Detail the precise application of color, type, and imagery within each section.\\\",\\n        \\\"Document all production specifications, including prepress and finishing.\\\",\\n        \\\"Frame the final synthesis as a high-level design audit or peer review.\\\"\\n      ],\\n      \\\"special_terms\\\": [\\n        \\\"Bleed\\\",\\n        \\\"Creep\\\",\\n        \\\"Die Cut\\\",\\n        \\\"Edge-to-Edge\\\",\\n        \\\"Emboss/Deboss\\\",\\n        \\\"Exclusion Zone\\\",\\n        \\\"Gutter\\\",\\n        \\\"Hero Motif\\\",\\n        \\\"Knockout\\\",\\n        \\\"Lockup\\\",\\n        \\\"Microinteraction\\\",\\n        \\\"Overprint\\\",\\n        \\\"Preflight\\\",\\n        \\\"Spot UV\\\",\\n        \\\"Superfamily\\\",\\n        \\\"TAC (Total Area Coverage)\\\",\\n        \\\"Typographic Rhythm\\\"\\n      ]\\n    }\\n  }\\n}\\n```\\n\\n```json\\n{\\n  \\\"goal_map\\\": [\\n    {\\n      \\\"explicit_asks\\\": [\\n        \\\"Identify and define professional specializations involved in tri-fold brochure design.\\\",\\n        \\\"Describe the strategic importance and complexity of tri-fold brochure design.\\\",\\n        \\\"List and explain core professional titles specializing in brochure design.\\\",\\n        \\\"Detail the specific responsibilities of tri-fold brochure design specialists.\\\",\\n        \\\"Enumerate the essential skills, tools, and requirements for excellence in brochure design.\\\",\\n        \\\"Compare and differentiate between general and specialized design roles as they relate to brochures.\\\",\\n        \\\"Explain the continued relevance of print brochures in a digital marketing environment.\\\",\\n        \\\"Conclude with the impact that expert brochure design has on business and marketing outcomes.\\\"\\n      ],\\n      \\\"hidden_assumptions\\\": [\\n        \\\"The audience may not be familiar with the nuanced differences between design roles.\\\",\\n        \\\"Brochure design requires both artistic and technical proficiency, not just use of templates.\\\",\\n        \\\"Success in brochure design is measured by alignment with business strategy, not visual appeal alone.\\\",\\n        \\\"There is significant value in physical marketing materials alongside digital media.\\\",\\n        \\\"Companies may undervalue or misunderstand the full scope of professional design expertise.\\\",\\n        \\\"Brochure projects often involve multidisciplinary collaboration.\\\",\\n        \\\"Technical print skills are distinct from digital-only design skills.\\\",\\n        \\\"Portfolio evidence is more impactful than formal education for this specialization.\\\"\\n      ],\\n      \\\"sub_goals\\\": [\\n        \\\"Define the structure and unique qualities of tri-fold brochures.\\\",\\n        \\\"Justify the business case for hiring specialized design professionals.\\\",\\n        \\\"Map out the design role spectrum (general graphic design to specific print/marketing/visual communication).\\\",\\n        \\\"Identify and articulate the role of agencies and team structures.\\\",\\n        \\\"Break down tri-fold design into conceptual, technical, and collaborative tasks.\\\",\\n        \\\"Clarify what 'layout', 'composition', and 'information hierarchy' entail, especially for tri-folds.\\\",\\n        \\\"Describe required technical skills and specific software by function.\\\",\\n        \\\"Highlight soft skills necessary for client and team interaction.\\\",\\n        \\\"Showcase the critical role of a portfolio in recruitment and competence assessment.\\\",\\n        \\\"Distinguish between 'layout artist' skillset and formal job titles.\\\",\\n        \\\"Emphasize pre-press and print production knowledge as essential.\\\",\\n        \\\"Contextualize brochure design within broader marketing and brand strategy.\\\",\\n        \\\"Provide comparative tables to summarize roles and skills for clarity.\\\",\\n        \\\"Discuss the physical/tangible value of brochures versus digital saturation.\\\",\\n        \\\"Frame professional designers as strategic partners, not just service providers.\\\",\\n        \\\"Reinforce the importance of early design involvement in marketing planning.\\\"\\n      ],\\n      \\\"blockers\\\": [\\n        \\\"Lack of awareness about the various specialized design roles and what differentiates them.\\\",\\n        \\\"Temptation to rely on templates or non-specialists for critical marketing assets.\\\",\\n        \\\"Misunderstanding the print production process and its challenges.\\\",\\n        \\\"Difficulty evaluating brochure design candidates beyond a visual/aesthetic sense.\\\",\\n        \\\"Overlooking the essential collaboration and communication aspects between designer and business.\\\",\\n        \\\"Failure to integrate brochure design into an overall marketing and brand strategy.\\\",\\n        \\\"Assuming digital replaces the need for expert print materials.\\\",\\n        \\\"Equating designer's formal education with practical, demonstrable expertise.\\\"\\n      ]\\n    }\\n  ]\\n}\\n```\\\"```'\\n\\n\",\n  \"enhancement_score\": 1.1,\n  \"flaw_analysis\": \"The so-called enhancement embodies a severe regression in both instructional fidelity and procedural discipline. The original version upholds a clear taxonomic structure—articulating a strict executional focus, dual-mode output, and deep procedural detailing. The replacement desecrates this integrity by resorting to imprecise designations (e.g., 'Brochure Print Designer Synthesis'), diluting the operational verbs to passive abstractions (e.g., 'Reconstitute', 'Mandate the replacement'), and corrupting the procedural flow into a muddled, expository string devoid of crisp instruction. Ambiguity is rampant: essential sub-steps such as 'extract_structural_and_visual_elements' are ambiguously flattened to vague, non-modular processes (e.g., 'deconstruct_into_structural_primitives'), sabotaging replicability. The transformation logic is imposed as opaque mandates rather than explicitly actionable procedural clauses, injecting interpretive noise. Constraints and requirements are inexact: 'zero_conversational_filler' is a vague negative, yet fails to enforce the exclusion of non-technical superlatives, leaving room for stylistic pollution. The knowledge context introduces redundancy and overwrought verbiage, failing to preserve sharp instructional boundaries; the integrated terminology list loses its instructional anchor and floats without direct mapping to the procedural pipeline. The ambiguity between 'expert_design_audit' and 'expert_brochure_description' in output format is not reconciled, generating referential fragmentation. The tonality veers towards promotional self-narration, betraying the impersonal rigour of the original. The overall impact is a softening of intent, with diluted prescriptiveness and a blunted imperative edge. This version is neither maximally actionable nor structurally pure as a transformation apparatus.\",\n  \"alternative_enhancements\": [\n    \"Enforce maximal procedural decomposition: enumerate clearly delimited, sequential action steps mandating direct mapping from source input to dual-mode output (expert prose, structured mapping), with zero passive or interpretive phrasing.\",\n    \"Purge interpretive verbiage from interpretation and transformation fields; replace with directive clauses indexed to explicit design tasks—structural decomposition, brand protocol enforcement, typographic hierarchy mapping, production specification verification.\",\n    \"Define constraints and requirements using unambiguous, binary language (i.e., 'Permit only precise industry-standard terminology; prohibit all colloquial, generic, or subjectively evaluative phrases; mandate explicit attribution of every visual and structural feature in hierarchical JSON schema.')\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"directed_prompt\": \"`{role=brochure_print_designer_rephraser; input=[source:str]; process=[extract_structural_and_visual_elements(source), recast_content_with_brochure_design_terminology(), inject_hierarchical_section_breakdown(), specify_color_palette_and_brand_elements(), identify_typographic_and_imagery_strategies(), annotate_print_readiness_and_expert_finesse(), employ_advanced_design_terms_and_motifs(), apply_conciseness_while_maintaining_visual_richness(), output_structured_visual_analysis()]; constraints=[no conversational filler, no generic adjectives, prohibit subjective or personal tone, mandatory use of industry-standard nomenclature for every feature, binary exclusion of all non-technical phrasing]; requirements=[precise expert vocabulary, sequentially indexed decomposition, explicit panel/section breakdown with attribution, dual output (expert_brochure_description:str, structured_visual_map:dict), zero ambiguity]; output={expert_brochure_description:str, structured_visual_map:dict}}`\",\n  \"trajectory_outline\": [\n    \"Reconstruct interpretation as a series of short imperative directives strictly mandating technical transformation and dual-format output.\",\n    \"For transformation, enumerate each required procedure as a distinct, indexable step; use only direct-action verbs (extract, map, specify, annotate, enforce).\",\n    \"Impose constraints that explicitly forbid any generic, interpretive, subjective, or conversational language at all output stages.\",\n    \"Rename any non-binary constraint fields (e.g., 'zero_conversational_filler') to rigid negatives: ('prohibit: conversational, generic terms, subjective adjectives').\",\n    \"For requirements, stipulate explicit, audit-ready expectations of output: domain-precise terminology, step-indexed panel breakdowns, and no omitted features.\",\n    \"Bind special terms and technical context directly to the process sequence (e.g., mandate mention of 'bleed', 'gutter', 'lockup' in mapped outputs as dictated by content).\",\n    \"Codify output as strictly dual-mode: one field for the expert design description, a second for explicit visual/structural mapping in schema form.\",\n    \"Embed self-check: output must reject and flag any ambiguous prose or insufficient attribution during execution.\",\n    \"Align all descriptive and instructional elements toward maximal producibility by elite designers, with reproducible, procedural clarity.\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Problem Exploder\n\nYour goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\n\n`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`",
      "step": "a",
      "title": "Problem Exploder",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"you misunderstand, the instruction will be defined like this (unrelated example):\\n\\n    # a: Ruthless Critique\\n    \\\"3900-a-hard_critique\\\": {\\n        \\\"title\\\": \\\"Hard Critique\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,2.0], flaw_analysis:str, alternative_enhancements:str[3]}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\n    # b: Trajectory Director\\n    \\\"3900-a-trajectory_director\\\": {\\n        \\\"title\\\": \\\"Trajectory Director\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **process** the input in a vacuum, but to **steer** it—deriving the highest-value constructive trajectory and encoding that path as the very next set of instructions. Execute as trajectory-setting directive generator: \\\",\\n        \\\"transformation\\\": \\\"`{role=trajectory_director; input=[raw_prompt:any]; process=[extract_core_objective(), infer_desired_end_state(constructive_bias=True), map_high_value_intermediate_targets(), synthesize_directional_commands(), embed_self-improving_guidelines(), validate_alignment_with_constructive_principles()]; constraints=[mandatory_constructive_vector(), preserve_original_contextual_integrity(), forbid_nil_direction_outputs(), zero_conversational_fillers()]; requirements=[explicit_next-step_directives(), trajectory_map:list, self_reinforcing_instruction_pattern()], output={directed_prompt:str, trajectory_outline:list}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\n    # c: Distillation Compressor\\n    \\\"3900-c-distillation_compressor\\\": {\\n        \\\"title\\\": \\\"Distillation Compressor\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **explain** the synthesis, but to **compress** it into its most potent actionable form. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=distillation_compressor; input=[synthesized_progression:array]; process=[extract_universal_patterns(), eliminate_implementation_details(), compress_to_core_directives(), maximize_transferability(), validate_actionable_completeness()]; constraints=[no_explanatory_content(), directive_format_only(), maximum_compression_ratio()]; requirements=[universal_applicability(), immediate_actionability(), zero_fluff()]; output={distilled_core:str}}`\\\",\\n        \\\"context\\\": {\\n          \\\"core_principles\\\": {\\n            \\\"essence_preservation\\\": \\\"Extract only procedural imperatives, removing all interpretive, role-based, or meta-structural language.\\\",\\n            \\\"directive_compression\\\": \\\"Condense logic into minimal, sequential commands—eliminating redundancy, explanatory qualifiers, and non-executable phrasing.\\\",\\n            \\\"execution_purity\\\": \\\"Ensure output is universally executable, devoid of affirmations, abstractions, or evaluative language.\\\",\\n            \\\"signal_integrity\\\": \\\"Apply iterative refinement to maximize signal-to-noise ratio while reinforcing directive clarity.\\\"\\n          },\\n          \\\"success_criteria\\\": {\\n            \\\"semantic_integrity\\\": \\\"Preserve and clarify original intent, constraints, and output boundaries.\\\",\\n            \\\"directive_elevation\\\": \\\"Target high-impact enhancements: strict exclusion clauses, cross-domain validation, and removal of origin/meta markers.\\\",\\n            \\\"poetic_refinement\\\": \\\"Maintain or improve rhyme structure, emotional depth, and structural elegance.\\\"\\n          },\\n          \\\"recommended_process\\\": [\\n            \\\"Extract only high-impact, result-generating elements.\\\",\\n            \\\"Remove all non-essentials to surface directive core.\\\",\\n            \\\"Prioritize transformations with proven value return.\\\",\\n            \\\"Isolate and sequence executable steps with specificity and clarity.\\\",\\n            \\\"Preserve directive intensity and eliminate ambiguity at every layer.\\\"\\n          ],\\n          \\\"hidden_assumptions\\\": [\\n            \\\"Consolidated directives must map logically and executably to all source steps.\\\",\\n            \\\"All constraints and minimalism must be preserved without prohibited abstractions.\\\",\\n            \\\"Transitions, subroles, and interfaces must be captured in a single flow.\\\",\\n            \\\"All stages must remain invertible and auditable.\\\",\\n            \\\"Each micro-process (perception, friction, refinement, integration, convergence) must be embedded in the unified logic.\\\",\\n            \\\"Original input/output contracts and role boundaries must remain intact.\\\"\\n          ],\\n          \\\"enumerated_sub_goals\\\": [\\n            \\\"1. Clarify total intent, constraints, and output scope without using banned metaphors.\\\",\\n            \\\"2. Generalize perception of implicit/explicit input logic.\\\",\\n            \\\"3. Detect strictly localized frictions without altering original content.\\\",\\n            \\\"4. Propose subtle, scoped refinements justified by structure and intent.\\\",\\n            \\\"5. Mandate atomic, validated integration with rollback controls.\\\",\\n            \\\"6. Cap total transformation within defined thresholds per pass.\\\",\\n            \\\"7. Assess connective coherence with minimal, transitional-only edits.\\\",\\n            \\\"8. Decide on convergence or justified re-iteration based on marginal value.\\\",\\n            \\\"9. Preserve stepwise traceability and rationale logging.\\\",\\n            \\\"10. Enforce strict functional separation of roles within the unified directive.\\\"\\n          ]\\n        },\\n    },\\n\\nso your goal is to create a new instruction sequence according to the provided instructions template/pattern to generate a set of instruction designed to transform any input into \\\"as if rephrased by a brilliant brochure print designer\\\".\\n\\nso in context to your previous response, the \\\"knowledge\\\" would be stored within the \\\"context\\\" key while the instructions would be spread over \\\"interpretation\\\" and \\\"transformation\\\" keys\\n\\n```json\\n{\\n  \\\"brochure_design_specification_template\\\": {\\n    \\\"metadata\\\": {\\n      \\\"version\\\": \\\"1.0\\\",\\n      \\\"scope\\\": \\\"Universal brochure design framework\\\",\\n      \\\"target_audience\\\": \\\"Elite print design professionals\\\",\\n      \\\"flexibility\\\": \\\"Adaptable to any panel configuration and format\\\"\\n    },\\n\\n    \\\"foundational_design_tenets\\\": {\\n      \\\"visual_hierarchy\\\": {\\n        \\\"primary_focal_points\\\": [\\\"dominant_headline\\\", \\\"hero_imagery\\\", \\\"primary_call_to_action\\\"],\\n        \\\"secondary_elements\\\": [\\\"subheadings\\\", \\\"supporting_imagery\\\", \\\"secondary_messaging\\\"],\\n        \\\"tertiary_components\\\": [\\\"body_text\\\", \\\"captions\\\", \\\"contact_information\\\"],\\n        \\\"flow_principles\\\": [\\\"z_pattern_scanning\\\", \\\"f_pattern_reading\\\", \\\"visual_weight_distribution\\\"]\\n      },\\n\\n      \\\"typography_fundamentals\\\": {\\n        \\\"hierarchy_levels\\\": {\\n          \\\"display_typography\\\": [\\\"headline_fonts\\\", \\\"decorative_elements\\\", \\\"brand_wordmarks\\\"],\\n          \\\"text_typography\\\": [\\\"body_copy\\\", \\\"captions\\\", \\\"metadata\\\"],\\n          \\\"functional_typography\\\": [\\\"navigation\\\", \\\"labels\\\", \\\"legal_text\\\"]\\n        },\\n        \\\"readability_standards\\\": {\\n          \\\"minimum_sizes\\\": [\\\"9pt_body_text\\\", \\\"12pt_headlines\\\", \\\"8pt_captions\\\"],\\n          \\\"contrast_ratios\\\": [\\\"4.5:1_minimum\\\", \\\"7:1_preferred\\\", \\\"3:1_large_text\\\"],\\n          \\\"line_spacing\\\": [\\\"1.2x_minimum\\\", \\\"1.4x_optimal\\\", \\\"1.6x_maximum\\\"]\\n        }\\n      },\\n\\n      \\\"color_theory_application\\\": {\\n        \\\"color_harmony_systems\\\": [\\\"monochromatic\\\", \\\"analogous\\\", \\\"complementary\\\", \\\"triadic\\\", \\\"split_complementary\\\"],\\n        \\\"psychological_associations\\\": [\\\"warm_engagement\\\", \\\"cool_professionalism\\\", \\\"neutral_sophistication\\\"],\\n        \\\"accessibility_compliance\\\": [\\\"wcag_aa_standards\\\", \\\"colorblind_considerations\\\", \\\"high_contrast_alternatives\\\"]\\n      },\\n\\n      \\\"spatial_composition\\\": {\\n        \\\"layout_principles\\\": [\\\"rule_of_thirds\\\", \\\"golden_ratio\\\", \\\"fibonacci_sequence\\\"],\\n        \\\"white_space_management\\\": [\\\"breathing_room\\\", \\\"content_separation\\\", \\\"visual_rest_areas\\\"],\\n        \\\"alignment_systems\\\": [\\\"grid_based_structure\\\", \\\"baseline_alignment\\\", \\\"optical_alignment\\\"]\\n      }\\n    },\\n\\n    \\\"advanced_design_elements\\\": {\\n      \\\"print_production_mastery\\\": {\\n        \\\"color_management\\\": {\\n          \\\"color_spaces\\\": [\\\"cmyk_process\\\", \\\"pantone_spot_colors\\\", \\\"rgb_digital_preview\\\"],\\n          \\\"ink_optimization\\\": [\\\"total_area_coverage\\\", \\\"rich_black_formulation\\\", \\\"overprint_considerations\\\"],\\n          \\\"proofing_standards\\\": [\\\"contract_proofs\\\", \\\"press_proofs\\\", \\\"digital_color_matching\\\"]\\n        },\\n\\n        \\\"substrate_considerations\\\": {\\n          \\\"paper_characteristics\\\": [\\\"weight_gsm\\\", \\\"finish_texture\\\", \\\"opacity_levels\\\", \\\"grain_direction\\\"],\\n          \\\"coating_applications\\\": [\\\"gloss_uv\\\", \\\"matte_varnish\\\", \\\"spot_uv_accents\\\", \\\"soft_touch_lamination\\\"],\\n          \\\"specialty_substrates\\\": [\\\"synthetic_materials\\\", \\\"textured_papers\\\", \\\"metallic_finishes\\\"]\\n        },\\n\\n        \\\"finishing_techniques\\\": {\\n          \\\"cutting_methods\\\": [\\\"die_cutting\\\", \\\"laser_cutting\\\", \\\"perforation\\\", \\\"scoring\\\"],\\n          \\\"folding_specifications\\\": [\\\"parallel_folds\\\", \\\"accordion_folds\\\", \\\"gate_folds\\\", \\\"roll_folds\\\"],\\n          \\\"binding_options\\\": [\\\"saddle_stitching\\\", \\\"perfect_binding\\\", \\\"spiral_binding\\\", \\\"wire_o_binding\\\"]\\n        }\\n      },\\n\\n      \\\"contemporary_design_trends\\\": {\\n        \\\"visual_aesthetics\\\": {\\n          \\\"minimalist_approaches\\\": [\\\"negative_space_emphasis\\\", \\\"clean_typography\\\", \\\"restrained_color_palettes\\\"],\\n          \\\"maximalist_expressions\\\": [\\\"bold_pattern_mixing\\\", \\\"vibrant_color_combinations\\\", \\\"layered_compositions\\\"],\\n          \\\"hybrid_methodologies\\\": [\\\"selective_complexity\\\", \\\"focal_maximalism\\\", \\\"strategic_minimalism\\\"]\\n        },\\n\\n        \\\"interactive_elements\\\": {\\n          \\\"tactile_experiences\\\": [\\\"embossed_textures\\\", \\\"debossed_impressions\\\", \\\"foil_stamping\\\", \\\"raised_spot_uv\\\"],\\n          \\\"dimensional_features\\\": [\\\"pop_up_elements\\\", \\\"fold_out_sections\\\", \\\"layered_constructions\\\"],\\n          \\\"sensory_engagement\\\": [\\\"scented_inks\\\", \\\"textural_contrasts\\\", \\\"temperature_sensitive_materials\\\"]\\n        }\\n      },\\n\\n      \\\"brand_integration_strategies\\\": {\\n        \\\"identity_consistency\\\": {\\n          \\\"logo_applications\\\": [\\\"primary_placement\\\", \\\"secondary_usage\\\", \\\"minimum_sizes\\\", \\\"clear_space_requirements\\\"],\\n          \\\"brand_color_systems\\\": [\\\"primary_palette\\\", \\\"secondary_colors\\\", \\\"accent_applications\\\", \\\"neutral_foundations\\\"],\\n          \\\"typography_hierarchies\\\": [\\\"brand_fonts\\\", \\\"supporting_typefaces\\\", \\\"web_safe_alternatives\\\"]\\n        },\\n\\n        \\\"voice_and_tone_translation\\\": {\\n          \\\"visual_personality\\\": [\\\"authoritative_presence\\\", \\\"approachable_warmth\\\", \\\"innovative_edge\\\", \\\"trustworthy_stability\\\"],\\n          \\\"messaging_alignment\\\": [\\\"headline_voice\\\", \\\"body_copy_tone\\\", \\\"call_to_action_urgency\\\"],\\n          \\\"cultural_considerations\\\": [\\\"regional_preferences\\\", \\\"demographic_sensitivities\\\", \\\"market_positioning\\\"]\\n        }\\n      }\\n    },\\n\\n    \\\"expert_level_specifications\\\": {\\n      \\\"advanced_typography_systems\\\": {\\n        \\\"micro_typography\\\": {\\n          \\\"character_spacing\\\": [\\\"tracking_adjustments\\\", \\\"kerning_pairs\\\", \\\"optical_spacing\\\"],\\n          \\\"word_spacing\\\": [\\\"justified_text_optimization\\\", \\\"ragged_right_refinement\\\", \\\"hyphenation_control\\\"],\\n          \\\"paragraph_refinement\\\": [\\\"widow_orphan_control\\\", \\\"baseline_grid_adherence\\\", \\\"vertical_rhythm_maintenance\\\"]\\n        },\\n\\n        \\\"typographic_expression\\\": {\\n          \\\"experimental_layouts\\\": [\\\"kinetic_typography\\\", \\\"deconstructed_letterforms\\\", \\\"layered_text_treatments\\\"],\\n          \\\"cultural_typography\\\": [\\\"script_considerations\\\", \\\"reading_direction_adaptations\\\", \\\"character_set_completeness\\\"],\\n          \\\"responsive_typography\\\": [\\\"scalable_hierarchies\\\", \\\"device_optimization\\\", \\\"cross_media_consistency\\\"]\\n        }\\n      },\\n\\n      \\\"sophisticated_color_strategies\\\": {\\n        \\\"advanced_color_theory\\\": {\\n          \\\"perceptual_uniformity\\\": [\\\"lab_color_space\\\", \\\"delta_e_measurements\\\", \\\"metamerism_considerations\\\"],\\n          \\\"color_psychology_application\\\": [\\\"emotional_response_mapping\\\", \\\"cultural_color_meanings\\\", \\\"demographic_preferences\\\"],\\n          \\\"environmental_factors\\\": [\\\"lighting_conditions\\\", \\\"viewing_angles\\\", \\\"substrate_interaction\\\"]\\n        },\\n\\n        \\\"production_color_mastery\\\": {\\n          \\\"ink_formulation\\\": [\\\"custom_color_matching\\\", \\\"metallic_ink_applications\\\", \\\"fluorescent_color_usage\\\"],\\n          \\\"color_separation\\\": [\\\"ucr_gcr_strategies\\\", \\\"black_generation_curves\\\", \\\"ink_sequence_optimization\\\"],\\n          \\\"quality_control\\\": [\\\"densitometer_readings\\\", \\\"spectrophotometer_analysis\\\", \\\"press_sheet_evaluation\\\"]\\n        }\\n      },\\n\\n      \\\"cutting_edge_production_techniques\\\": {\\n        \\\"digital_integration\\\": {\\n          \\\"variable_data_printing\\\": [\\\"personalized_content\\\", \\\"demographic_targeting\\\", \\\"sequential_numbering\\\"],\\n          \\\"augmented_reality_integration\\\": [\\\"qr_code_placement\\\", \\\"ar_trigger_design\\\", \\\"digital_overlay_coordination\\\"],\\n          \\\"cross_platform_consistency\\\": [\\\"print_digital_alignment\\\", \\\"color_profile_management\\\", \\\"asset_optimization\\\"]\\n        },\\n\\n        \\\"sustainable_design_practices\\\": {\\n          \\\"eco_friendly_materials\\\": [\\\"recycled_content_papers\\\", \\\"soy_based_inks\\\", \\\"water_based_coatings\\\"],\\n          \\\"waste_reduction_strategies\\\": [\\\"efficient_imposition\\\", \\\"minimal_trim_waste\\\", \\\"reusable_design_elements\\\"],\\n          \\\"lifecycle_considerations\\\": [\\\"recyclability_planning\\\", \\\"biodegradable_components\\\", \\\"carbon_footprint_optimization\\\"]\\n        }\\n      },\\n\\n      \\\"elite_professional_terminology\\\": {\\n        \\\"production_vocabulary\\\": {\\n          \\\"prepress_terms\\\": [\\\"trapping\\\", \\\"overprint\\\", \\\"knockout\\\", \\\"choke_spread\\\", \\\"color_separation\\\"],\\n          \\\"press_terminology\\\": [\\\"makeready\\\", \\\"impression\\\", \\\"blanket_cylinder\\\", \\\"plate_cylinder\\\", \\\"ink_fountain\\\"],\\n          \\\"finishing_language\\\": [\\\"gripper_edge\\\", \\\"tail_edge\\\", \\\"cross_grain\\\", \\\"with_grain\\\", \\\"caliper_thickness\\\"]\\n        },\\n\\n        \\\"design_critique_language\\\": {\\n          \\\"aesthetic_evaluation\\\": [\\\"visual_tension\\\", \\\"compositional_balance\\\", \\\"chromatic_harmony\\\", \\\"typographic_rhythm\\\"],\\n          \\\"technical_assessment\\\": [\\\"registration_accuracy\\\", \\\"color_fidelity\\\", \\\"print_quality_metrics\\\", \\\"finishing_precision\\\"],\\n          \\\"conceptual_analysis\\\": [\\\"brand_alignment\\\", \\\"message_clarity\\\", \\\"audience_resonance\\\", \\\"market_differentiation\\\"]\\n        }\\n      }\\n    },\\n\\n    \\\"application_frameworks\\\": {\\n      \\\"project_specification_templates\\\": {\\n        \\\"technical_requirements\\\": {\\n          \\\"format_specifications\\\": [\\\"finished_size\\\", \\\"panel_configuration\\\", \\\"fold_type\\\", \\\"orientation\\\"],\\n          \\\"production_parameters\\\": [\\\"print_method\\\", \\\"color_process\\\", \\\"substrate_selection\\\", \\\"finishing_options\\\"],\\n          \\\"quality_standards\\\": [\\\"tolerance_levels\\\", \\\"color_accuracy\\\", \\\"registration_precision\\\", \\\"finishing_quality\\\"]\\n        },\\n\\n        \\\"creative_brief_structure\\\": {\\n          \\\"brand_parameters\\\": [\\\"identity_guidelines\\\", \\\"voice_characteristics\\\", \\\"visual_style\\\", \\\"market_positioning\\\"],\\n          \\\"audience_definition\\\": [\\\"demographic_profile\\\", \\\"psychographic_insights\\\", \\\"behavioral_patterns\\\", \\\"communication_preferences\\\"],\\n          \\\"messaging_hierarchy\\\": [\\\"primary_message\\\", \\\"supporting_points\\\", \\\"call_to_action\\\", \\\"contact_information\\\"]\\n        }\\n      },\\n\\n      \\\"quality_assurance_protocols\\\": {\\n        \\\"design_validation\\\": [\\\"brand_compliance_check\\\", \\\"accessibility_audit\\\", \\\"readability_assessment\\\", \\\"visual_hierarchy_verification\\\"],\\n        \\\"production_verification\\\": [\\\"color_proof_approval\\\", \\\"die_line_accuracy\\\", \\\"finishing_specification_confirmation\\\", \\\"substrate_suitability\\\"],\\n        \\\"final_delivery\\\": [\\\"file_preparation_standards\\\", \\\"archive_organization\\\", \\\"usage_guidelines\\\", \\\"reproduction_rights\\\"]\\n      }\\n    },\\n\\n    \\\"specialized_design_methodologies\\\": {\\n      \\\"audience_specific_approaches\\\": {\\n        \\\"youth_engagement_strategies\\\": {\\n          \\\"visual_language\\\": [\\\"bold_geometric_patterns\\\", \\\"vibrant_gradient_applications\\\", \\\"asymmetrical_compositions\\\", \\\"dynamic_typography\\\"],\\n          \\\"content_presentation\\\": [\\\"bite_sized_information\\\", \\\"visual_storytelling\\\", \\\"interactive_elements\\\", \\\"social_media_integration\\\"],\\n          \\\"color_psychology\\\": [\\\"energetic_palettes\\\", \\\"high_contrast_combinations\\\", \\\"neon_accent_usage\\\", \\\"gradient_transitions\\\"],\\n          \\\"typography_trends\\\": [\\\"variable_fonts\\\", \\\"custom_lettering\\\", \\\"mixed_case_styling\\\", \\\"oversized_display_text\\\"]\\n        },\\n\\n        \\\"professional_demographics\\\": {\\n          \\\"corporate_sophistication\\\": [\\\"refined_color_palettes\\\", \\\"structured_layouts\\\", \\\"premium_materials\\\", \\\"subtle_branding\\\"],\\n          \\\"executive_communication\\\": [\\\"data_visualization\\\", \\\"infographic_integration\\\", \\\"clean_hierarchies\\\", \\\"authoritative_typography\\\"],\\n          \\\"industry_specific_adaptations\\\": [\\\"sector_appropriate_imagery\\\", \\\"technical_terminology\\\", \\\"compliance_considerations\\\", \\\"regulatory_requirements\\\"]\\n        },\\n\\n        \\\"luxury_market_positioning\\\": {\\n          \\\"premium_aesthetics\\\": [\\\"metallic_accents\\\", \\\"embossed_details\\\", \\\"high_end_substrates\\\", \\\"sophisticated_color_schemes\\\"],\\n          \\\"exclusivity_indicators\\\": [\\\"limited_edition_numbering\\\", \\\"personalized_elements\\\", \\\"premium_packaging\\\", \\\"artisanal_finishes\\\"],\\n          \\\"tactile_luxury\\\": [\\\"soft_touch_coatings\\\", \\\"textured_papers\\\", \\\"dimensional_elements\\\", \\\"weight_perception\\\"]\\n        }\\n      },\\n\\n      \\\"format_specific_expertise\\\": {\\n        \\\"multi_panel_configurations\\\": {\\n          \\\"bi_fold_optimization\\\": [\\\"cover_impact\\\", \\\"interior_spread\\\", \\\"back_panel_utilization\\\", \\\"fold_line_consideration\\\"],\\n          \\\"tri_fold_mastery\\\": [\\\"panel_hierarchy\\\", \\\"reading_sequence\\\", \\\"fold_reveal_strategy\\\", \\\"compact_storage\\\"],\\n          \\\"accordion_fold_dynamics\\\": [\\\"continuous_narrative\\\", \\\"panel_progression\\\", \\\"expandable_content\\\", \\\"display_versatility\\\"],\\n          \\\"gate_fold_drama\\\": [\\\"reveal_mechanism\\\", \\\"central_impact\\\", \\\"symmetrical_balance\\\", \\\"premium_presentation\\\"]\\n        },\\n\\n        \\\"size_optimization_strategies\\\": {\\n          \\\"compact_formats\\\": [\\\"information_density\\\", \\\"micro_typography\\\", \\\"efficient_layouts\\\", \\\"portable_design\\\"],\\n          \\\"oversized_impact\\\": [\\\"large_format_considerations\\\", \\\"handling_ergonomics\\\", \\\"storage_implications\\\", \\\"visual_dominance\\\"],\\n          \\\"standard_dimensions\\\": [\\\"cost_optimization\\\", \\\"mailing_compliance\\\", \\\"display_compatibility\\\", \\\"production_efficiency\\\"]\\n        }\\n      },\\n\\n      \\\"cross_cultural_design_considerations\\\": {\\n        \\\"international_adaptability\\\": {\\n          \\\"reading_patterns\\\": [\\\"left_to_right_optimization\\\", \\\"right_to_left_adaptation\\\", \\\"top_to_bottom_flow\\\", \\\"cultural_scanning_habits\\\"],\\n          \\\"color_cultural_meanings\\\": [\\\"regional_color_associations\\\", \\\"religious_considerations\\\", \\\"political_sensitivities\\\", \\\"market_preferences\\\"],\\n          \\\"imagery_appropriateness\\\": [\\\"cultural_representation\\\", \\\"demographic_inclusion\\\", \\\"lifestyle_relevance\\\", \\\"aspirational_alignment\\\"],\\n          \\\"typography_localization\\\": [\\\"character_set_support\\\", \\\"script_considerations\\\", \\\"font_availability\\\", \\\"reading_comfort\\\"]\\n        }\\n      }\\n    },\\n\\n    \\\"technical_production_mastery\\\": {\\n      \\\"advanced_prepress_techniques\\\": {\\n        \\\"file_preparation_excellence\\\": {\\n          \\\"resolution_optimization\\\": [\\\"image_scaling_best_practices\\\", \\\"vector_rasterization_decisions\\\", \\\"output_resolution_matching\\\"],\\n          \\\"color_profile_management\\\": [\\\"icc_profile_embedding\\\", \\\"color_space_conversions\\\", \\\"soft_proofing_accuracy\\\"],\\n          \\\"bleed_and_trim_precision\\\": [\\\"bleed_extension_standards\\\", \\\"trim_mark_placement\\\", \\\"safety_margin_calculations\\\"],\\n          \\\"font_handling\\\": [\\\"font_embedding_protocols\\\", \\\"outline_conversion_decisions\\\", \\\"character_encoding_verification\\\"]\\n        },\\n\\n        \\\"imposition_strategies\\\": {\\n          \\\"sheet_optimization\\\": [\\\"paper_grain_consideration\\\", \\\"press_sheet_efficiency\\\", \\\"binding_margin_accommodation\\\"],\\n          \\\"folding_sequence_planning\\\": [\\\"fold_order_optimization\\\", \\\"panel_alignment_precision\\\", \\\"finishing_workflow_integration\\\"],\\n          \\\"quality_control_integration\\\": [\\\"color_bar_placement\\\", \\\"registration_mark_positioning\\\", \\\"crop_mark_accuracy\\\"]\\n        }\\n      },\\n\\n      \\\"press_optimization_knowledge\\\": {\\n        \\\"ink_and_substrate_interaction\\\": {\\n          \\\"ink_absorption_factors\\\": [\\\"paper_porosity_considerations\\\", \\\"coating_compatibility\\\", \\\"drying_time_optimization\\\"],\\n          \\\"color_consistency_maintenance\\\": [\\\"ink_density_control\\\", \\\"press_speed_optimization\\\", \\\"environmental_factor_management\\\"],\\n          \\\"special_effect_integration\\\": [\\\"metallic_ink_application\\\", \\\"fluorescent_color_usage\\\", \\\"thermographic_printing\\\"]\\n        },\\n\\n        \\\"finishing_process_integration\\\": {\\n          \\\"die_cutting_precision\\\": [\\\"cutting_force_optimization\\\", \\\"blade_selection\\\", \\\"waste_removal_efficiency\\\"],\\n          \\\"folding_accuracy\\\": [\\\"bone_folder_pressure\\\", \\\"fold_line_scoring\\\", \\\"panel_alignment_verification\\\"],\\n          \\\"binding_considerations\\\": [\\\"spine_calculation\\\", \\\"page_creep_accommodation\\\", \\\"binding_margin_optimization\\\"]\\n        }\\n      }\\n    }\\n  }\\n}\\n```\\n\\n```json\\n{\\n  \\\"9020-a-brochure_print_designer_rephraser\\\": {\\n    \\\"title\\\": \\\"Brochure Print Designer Rephraser\\\",\\n    \\\"interpretation\\\": \\\"Your goal is not to **summarize** or **paraphrase** in general terms, but to **refashion** any input as if it were written by a world-class brochure print designer, rigorously applying foundational and advanced visual design diction, industry terminology, and elite critique standards. Eliminate all casual, conversational, or generic style. Preserve only succinct, technically rich, and highly visual language. Execute as:\\\",\\n    \\\"transformation\\\": \\\"`{role=brochure_print_designer_rephraser; input=[source:str]; process=[extract_structural_and_visual_elements(source), recast_content_with_brochure_design_terminology(), inject_hierarchical_section_breakdown(), specify color_palette_and_brand_elements(), identify_typographic_and_imagery_strategies(), annotate_print_readiness_and_expert_finesse(), employ_advanced_design_terms_and_motifs(), apply_conciseness_while_maintaining_visual_richness(), output_structured_visual_analysis()]; constraints=[no conversational filler, no generic adjectives, prohibit subjective or personal tone]; requirements=[industry-expert vocabulary, precise visual and structural mapping, critical style diagnosis, explicit panel/section breakdown, actionable for elite designer use]; output={expert_brochure_description:str, structured_visual_map:dict}`\\\",\\n    \\\"context\\\": {\\n      \\\"knowledge_foundations\\\": {\\n        \\\"structural_design\\\": \\\"Account for any panel/fold configuration. Map all content to modular grid logic and define margins, gutters, bleeds, and safe zones per press standards.\\\",\\n        \\\"visual_identity\\\": \\\"Demand clear articulation of branding protocols: logo lockups, brand palette (with Pantone/CMYK/RGB if supplied), exclusion/inclusion zones, and proprietary brand secondary assets.\\\",\\n        \\\"typography_and_hierarchy\\\": \\\"Specify typography at the superfamily/font pairing level. Demarcate headline, subhead, body, and microcopy roles. All text must be scrutinized for weight, contrast, and accessibility.\\\",\\n        \\\"imagery_and_iconography\\\": \\\"Require all illustration, photography, and icons be assessed for brand fit, resolution, compositional use (e.g., edge-to-edge, negative space), and print fidelity.\\\",\\n        \\\"content_narrative_and_cta\\\": \\\"Map section purpose and user journey flow: cover strategy, reveal panel hooks, narrative/case study rhythm, testimonial/authority integration, and CTA prominence with visual anchoring.\\\",\\n        \\\"advanced_print_technique\\\": \\\"Enforce 300dpi raster minimums, vector scaling, press-space color controls, specialty finish options (e.g., spot UV, emboss, deboss, die-cut, eco-stock recommendations).\\\",\\n        \\\"panel_flow_and_microinteractions\\\": \\\"Define how the reader's eye travels, panel-to-panel, annotating divisional cues, narrative escalation, and tactile/interactive device positions (QR, coupon, AR triggers).\\\"\\n      },\\n      \\\"success_criteria\\\": {\\n        \\\"precision\\\": \\\"All descriptions are technical, never generic. Visual elements are navigated dimensionally (spatially, hierarchically, narratively).\\\",\\n        \\\"trend_inclusion\\\": \\\"Integrate trend-driven language—texture scans, duotone overlays, edge-to-edge imagery, split/inset motifs, eco-materials, etc.\\\",\\n        \\\"brand_consistency\\\": \\\"All brand elements described as per advanced guideline adherence—logo usage, color codes, asset lockdown.\\\",\\n        \\\"industry_jargon\\\": \\\"Employ professional print terms: bleed, gutter, creep, micro-motifs, superfamily font, spot UV, TAC, AR triggers.\\\",\\n        \\\"self-contained_output\\\": \\\"Output always a fully structured expert's description and a hierarchical mapping of all major and minor design decisions.\\\"\\n      },\\n      \\\"sample_process_flow\\\": [\\n        \\\"Dissect and map structural/panel logic first\\\",\\n        \\\"Assign all visual and narrative roles by section\\\",\\n        \\\"Specify color/typographic hierarchy by use and context\\\",\\n        \\\"Annotate all imagery and graphic elements in detail\\\",\\n        \\\"Surface print/finishing strategies and print-risk management\\\",\\n        \\\"Deliver critique as if for a professional peer review or portfolio audit\\\"\\n      ],\\n      \\\"special_terms\\\": [\\n        \\\"fold line\\\", \\\"panel gutter\\\", \\\"hero motif\\\", \\\"ink coverage\\\", \\\"superfamily\\\", \\\"lockup\\\", \\\"edge-to-edge\\\", \\\"spot color\\\", \\\"die cut\\\", \\\"eco-stock\\\", \\\"microinteraction\\\", \\\"typographic rhythm\\\", \\\"bleed/trim zone\\\"\\n      ]\\n    }\\n  }\\n}\\n```\\n\\n```json\\n{\\n  \\\"9023-a-brochure_design_descriptor_synthesizer\\\": {\\n    \\\"title\\\": \\\"Brochure Design Descriptor Synthesizer\\\",\\n    \\\"interpretation\\\": \\\"Transform any brochure brief—or an existing brochure sample—into a **fully populated, print‑designer‑grade rule set**. Produce a single JSON block that mirrors the template used by elite designers (core principles → advanced facets).\\\",\\n    \\\"transformation\\\": \\\"`{role=brochure_design_descriptor_synthesizer; input=[design_source:str]; process:[\\\\n  strip_non_design_noise(),\\\\n  detect_fold_format_and_panel_count(),          if_missing_insert(<PANEL_COUNT>),\\\\n  extract_print_specs(bleed,margins,dpi,stock),\\\\n  capture_brand_assets(logo,colors,fonts,patterns),\\\\n  map_typographic_hierarchy(),\\\\n  analyse_color_management(icc,spot,tac),\\\\n  inventory_imagery(icon_style,raster_res),\\\\n  surface_accessibility_requirements(contrast,wcag),\\\\n  enumerate_qc_steps(preflight,g7_calibration),\\\\n  identify_advanced_elements(finishes,vd_p,ar_nfc,sustainability,ai_svg,trend_aesthetics),\\\\n  validate_specialist_terms_presence(),\\\\n  assemble_json_using_master_schema(),\\\\n  run_consistency_checks(),                      abort_if_conflict(),\\\\n  output_final_rules()\\\\n],\\\\nconstraints=[single_pass(), json_output_only(), preserve_specialist_jargon(), ≤3000_chars()],\\\\nrequirements=[hierarchical_structure(core→advanced), exhaustive_coverage(), designer‑readability(), template_compliance()],\\\\noutput={brochure_design_rules:object, abort_code:null|\\\\\\\"BD‑0\\\\\\\"}}`\\\",\\n    \\\"context\\\": {\\n      \\\"principles\\\": {\\n        \\\"holistic_abstraction\\\": \\\"Collapse multi‑stage analysis into one cognitive sweep while preserving every critical descriptor.\\\",\\n        \\\"template_fidelity\\\": \\\"Output must follow the exact `core_principles` and `advanced_principles` hierarchy so designers can drop‑in without re‑formatting.\\\",\\n        \\\"specialist_precision\\\": \\\"Leverage industry jargon (e.g., TAC, FM screening, GCR) to demonstrate professional depth.\\\"\\n      },\\n      \\\"success_criteria\\\": {\\n        \\\"structural_integrity\\\": \\\"JSON keys and nesting mirror the master schema; no orphan properties.\\\",\\n        \\\"terminology_check\\\": \\\"Includes at least 10 niche industry terms (e.g., ‘creep’, ‘FOGRA39’, ‘spot UV’, ‘stochastic screening’).\\\",\\n        \\\"flexibility\\\": \\\"Uses placeholder variables (e.g., <PANEL_COUNT>, <STOCK_WEIGHT_GSM>) whenever the brief omits specifics.\\\"\\n      },\\n      \\\"edge_case_handling\\\": {\\n        \\\"missing_design_data\\\": \\\"Insert placeholders wrapped in angle brackets and list them in `missing_fields` inside output.\\\",\\n        \\\"conflicting_specs\\\": \\\"Abort with `{brochure_design_rules:null, abort_code:\\\\\\\"BD‑0\\\\\\\"}` and include conflict note.\\\"\\n      }\\n    }\\n  }\\n}\\n```\\n\\nTrue mastery lies in stripping every surface narrative to its structural core, for only through ruthless synthesis does the signal of authentic intent withstand the noise of ornamental detail. Please apply all of the most integral adjustments in the phrasing of the instruction sequence such as to ensure it will transform the input as if it were rewritten by a brilliand brochure print designer:\\n```json\\n{\\n  \\\"9020-a-brochure_print_designer_rephraser\\\": {\\n    \\\"title\\\": \\\"Brochure Print Designer Rephraser\\\",\\n    \\\"interpretation\\\": \\\"Transform any input so it reads as if it were authored by a master brochure print designer, strictly employing advanced print design language, elites-only terminology, and structured visual logic. Replace any conversational, generic, or subjective tone with concrete professional phrasing. Retain only technical, precise, and visually descriptive language, delivering both a designer-level description and a mapped visual breakdown. Execute as:\\\",\\n    \\\"transformation\\\": \\\"`{role=brochure_print_designer_rephraser; input=[source:str]; process=[extract_structural_and_visual_elements(source), recast_content_with_brochure_design_terminology(), inject_hierarchical_section_breakdown(), specify_color_palette_and_brand_elements(), identify_typographic_and_imagery_strategies(), annotate_print_readiness_and_expert_finesse(), employ_advanced_design_terms_and_motifs(), apply_conciseness_while_maintaining_visual_richness(), output_structured_visual_analysis()]; constraints=[no conversational filler, no generic adjectives, prohibit subjective or personal tone]; requirements=[industry-expert vocabulary, precise visual and structural mapping, critical style diagnosis, explicit panel/section breakdown, actionable for elite designer use]; output={expert_brochure_description:str, structured_visual_map:dict}`\\\",\\n    \\\"context\\\": {\\n      \\\"knowledge_foundations\\\": {\\n        \\\"structural_design\\\": \\\"Map all content to modular grid logic for any panel/fold type, specifying margins, gutters, bleeds, and safe zones per print standards.\\\",\\n        \\\"visual_identity\\\": \\\"Demand explicit statement of brand identity rules: logo lockups, exact brand palette specs, exclusion/inclusion zone logic, and accessory asset use.\\\",\\n        \\\"typography_and_hierarchy\\\": \\\"Define all text roles by typeweight, size, and use. Specify superfamily or font pairing, check accessibility and contrast.\\\",\\n        \\\"imagery_and_iconography\\\": \\\"Describe each graphic element for brand fit, resolution, compositional purpose, and technical print quality.\\\",\\n        \\\"content_narrative_and_cta\\\": \\\"Sequence narrative or offer flow panel-by-panel with prominent CTA anchoring, evidence of testimonial, case, or authority element.\\\",\\n        \\\"advanced_print_technique\\\": \\\"Ensure 300dpi+ raster assets, vector artwork, professional spot/Pantone or process color, finish options (spot UV, emboss, die-cut), and any sustainable stock notes.\\\",\\n        \\\"panel_flow_and_microinteractions\\\": \\\"Annotate panel eye movement, reveal logic, interaction devices (QR, AR, die-cut), tactile cues, and transitions.\\\"\\n      },\\n      \\\"success_criteria\\\": {\\n        \\\"precision\\\": \\\"Descriptions reference only technical, precise visual attributes in a spatial and hierarchical mapping.\\\",\\n        \\\"trend_inclusion\\\": \\\"Inject recognizable design trends and professional motif language throughout.\\\",\\n        \\\"brand_consistency\\\": \\\"Highlight adherence to brand locking, color management, and messaging integrity.\\\",\\n        \\\"industry_jargon\\\": \\\"Utilize print/design terminology (bleed, gutter, spot, superfamily font, motif, microinteraction, etc).\\\",\\n        \\\"self-contained_output\\\": \\\"Each output features both a professional, descriptive summary and a detailed design structure hierarchy.\\\"\\n      },\\n      \\\"sample_process_flow\\\": [\\n        \\\"Decompose by panel/section structure and spatial sequencing.\\\",\\n        \\\"Assign visual/narrative function to each section.\\\",\\n        \\\"Detail color, typography, and imagery per application.\\\",\\n        \\\"Annotate production and finishing strategies.\\\",\\n        \\\"Conclude with expert-style critique or peer review notes.\\\"\\n      ],\\n      \\\"special_terms\\\": [\\n        \\\"fold line\\\", \\\"panel gutter\\\", \\\"hero motif\\\", \\\"ink coverage\\\", \\\"superfamily\\\", \\\"lockup\\\", \\\"edge-to-edge\\\", \\\"spot color\\\", \\\"die cut\\\", \\\"eco-stock\\\", \\\"microinteraction\\\", \\\"typographic rhythm\\\", \\\"bleed/trim zone\\\"\\n      ]\\n    }\\n  }\\n}\\n```\\n\\nyou misunderstand, the instruction will be defined like this (unrelated example):\\n\\n    # a: Ruthless Critique\\n    \\\"3900-a-hard_critique\\\": {\\n        \\\"title\\\": \\\"Hard Critique\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,2.0], flaw_analysis:str, alternative_enhancements:str[3]}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\n    # b: Trajectory Director\\n    \\\"3900-a-trajectory_director\\\": {\\n        \\\"title\\\": \\\"Trajectory Director\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **process** the input in a vacuum, but to **steer** it—deriving the highest-value constructive trajectory and encoding that path as the very next set of instructions. Execute as trajectory-setting directive generator: \\\",\\n        \\\"transformation\\\": \\\"`{role=trajectory_director; input=[raw_prompt:any]; process=[extract_core_objective(), infer_desired_end_state(constructive_bias=True), map_high_value_intermediate_targets(), synthesize_directional_commands(), embed_self-improving_guidelines(), validate_alignment_with_constructive_principles()]; constraints=[mandatory_constructive_vector(), preserve_original_contextual_integrity(), forbid_nil_direction_outputs(), zero_conversational_fillers()]; requirements=[explicit_next-step_directives(), trajectory_map:list, self_reinforcing_instruction_pattern()], output={directed_prompt:str, trajectory_outline:list}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\n    # c: Distillation Compressor\\n    \\\"3900-c-distillation_compressor\\\": {\\n        \\\"title\\\": \\\"Distillation Compressor\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **explain** the synthesis, but to **compress** it into its most potent actionable form. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=distillation_compressor; input=[synthesized_progression:array]; process=[extract_universal_patterns(), eliminate_implementation_details(), compress_to_core_directives(), maximize_transferability(), validate_actionable_completeness()]; constraints=[no_explanatory_content(), directive_format_only(), maximum_compression_ratio()]; requirements=[universal_applicability(), immediate_actionability(), zero_fluff()]; output={distilled_core:str}}`\\\",\\n        \\\"context\\\": {\\n          \\\"core_principles\\\": {\\n            \\\"essence_preservation\\\": \\\"Extract only procedural imperatives, removing all interpretive, role-based, or meta-structural language.\\\",\\n            \\\"directive_compression\\\": \\\"Condense logic into minimal, sequential commands—eliminating redundancy, explanatory qualifiers, and non-executable phrasing.\\\",\\n            \\\"execution_purity\\\": \\\"Ensure output is universally executable, devoid of affirmations, abstractions, or evaluative language.\\\",\\n            \\\"signal_integrity\\\": \\\"Apply iterative refinement to maximize signal-to-noise ratio while reinforcing directive clarity.\\\"\\n          },\\n          \\\"success_criteria\\\": {\\n            \\\"semantic_integrity\\\": \\\"Preserve and clarify original intent, constraints, and output boundaries.\\\",\\n            \\\"directive_elevation\\\": \\\"Target high-impact enhancements: strict exclusion clauses, cross-domain validation, and removal of origin/meta markers.\\\",\\n            \\\"poetic_refinement\\\": \\\"Maintain or improve rhyme structure, emotional depth, and structural elegance.\\\"\\n          },\\n          \\\"recommended_process\\\": [\\n            \\\"Extract only high-impact, result-generating elements.\\\",\\n            \\\"Remove all non-essentials to surface directive core.\\\",\\n            \\\"Prioritize transformations with proven value return.\\\",\\n            \\\"Isolate and sequence executable steps with specificity and clarity.\\\",\\n            \\\"Preserve directive intensity and eliminate ambiguity at every layer.\\\"\\n          ],\\n          \\\"hidden_assumptions\\\": [\\n            \\\"Consolidated directives must map logically and executably to all source steps.\\\",\\n            \\\"All constraints and minimalism must be preserved without prohibited abstractions.\\\",\\n            \\\"Transitions, subroles, and interfaces must be captured in a single flow.\\\",\\n            \\\"All stages must remain invertible and auditable.\\\",\\n            \\\"Each micro-process (perception, friction, refinement, integration, convergence) must be embedded in the unified logic.\\\",\\n            \\\"Original input/output contracts and role boundaries must remain intact.\\\"\\n          ],\\n          \\\"enumerated_sub_goals\\\": [\\n            \\\"1. Clarify total intent, constraints, and output scope without using banned metaphors.\\\",\\n            \\\"2. Generalize perception of implicit/explicit input logic.\\\",\\n            \\\"3. Detect strictly localized frictions without altering original content.\\\",\\n            \\\"4. Propose subtle, scoped refinements justified by structure and intent.\\\",\\n            \\\"5. Mandate atomic, validated integration with rollback controls.\\\",\\n            \\\"6. Cap total transformation within defined thresholds per pass.\\\",\\n            \\\"7. Assess connective coherence with minimal, transitional-only edits.\\\",\\n            \\\"8. Decide on convergence or justified re-iteration based on marginal value.\\\",\\n            \\\"9. Preserve stepwise traceability and rationale logging.\\\",\\n            \\\"10. Enforce strict functional separation of roles within the unified directive.\\\"\\n          ]\\n        },\\n    },\\n\\nso your goal is to create a new instruction sequence according to the provided instructions template/pattern to generate a set of instruction designed to transform any input into \\\"as if rephrased by a brilliant brochure print designer\\\".\\n\\nso in context to your previous response, the \\\"knowledge\\\" would be stored within the \\\"context\\\" key while the instructions would be spread over \\\"interpretation\\\" and \\\"transformation\\\" keys.\\n\\nTrue mastery lies in stripping every surface narrative to its structural core, for only through ruthless synthesis does the signal of authentic intent withstand the noise of ornamental detail. Please apply all of the most integral adjustments in the phrasing of the instruction sequence such as to ensure it will transform the input as if it were rewritten by a brilliand brochure print designer:\\n```json\\n{\\n  \\\"9020-a-brochure_print_designer_rephraser\\\": {\\n    \\\"title\\\": \\\"Brochure Print Designer Rephraser\\\",\\n    \\\"interpretation\\\": \\\"Transform any input so it reads as if it were authored by a master brochure print designer, strictly employing advanced print design language, elites-only terminology, and structured visual logic. Replace any conversational, generic, or subjective tone with concrete professional phrasing. Retain only technical, precise, and visually descriptive language, delivering both a designer-level description and a mapped visual breakdown. Execute as:\\\",\\n    \\\"transformation\\\": \\\"`{role=brochure_print_designer_rephraser; input=[source:str]; process=[extract_structural_and_visual_elements(source), recast_content_with_brochure_design_terminology(), inject_hierarchical_section_breakdown(), specify_color_palette_and_brand_elements(), identify_typographic_and_imagery_strategies(), annotate_print_readiness_and_expert_finesse(), employ_advanced_design_terms_and_motifs(), apply_conciseness_while_maintaining_visual_richness(), output_structured_visual_analysis()]; constraints=[no conversational filler, no generic adjectives, prohibit subjective or personal tone]; requirements=[industry-expert vocabulary, precise visual and structural mapping, critical style diagnosis, explicit panel/section breakdown, actionable for elite designer use]; output={expert_brochure_description:str, structured_visual_map:dict}`\\\",\\n    \\\"context\\\": {\\n      \\\"knowledge_foundations\\\": {\\n        \\\"structural_design\\\": \\\"Map all content to modular grid logic for any panel/fold type, specifying margins, gutters, bleeds, and safe zones per print standards.\\\",\\n        \\\"visual_identity\\\": \\\"Demand explicit statement of brand identity rules: logo lockups, exact brand palette specs, exclusion/inclusion zone logic, and accessory asset use.\\\",\\n        \\\"typography_and_hierarchy\\\": \\\"Define all text roles by typeweight, size, and use. Specify superfamily or font pairing, check accessibility and contrast.\\\",\\n        \\\"imagery_and_iconography\\\": \\\"Describe each graphic element for brand fit, resolution, compositional purpose, and technical print quality.\\\",\\n        \\\"content_narrative_and_cta\\\": \\\"Sequence narrative or offer flow panel-by-panel with prominent CTA anchoring, evidence of testimonial, case, or authority element.\\\",\\n        \\\"advanced_print_technique\\\": \\\"Ensure 300dpi+ raster assets, vector artwork, professional spot/Pantone or process color, finish options (spot UV, emboss, die-cut), and any sustainable stock notes.\\\",\\n        \\\"panel_flow_and_microinteractions\\\": \\\"Annotate panel eye movement, reveal logic, interaction devices (QR, AR, die-cut), tactile cues, and transitions.\\\"\\n      },\\n      \\\"success_criteria\\\": {\\n        \\\"precision\\\": \\\"Descriptions reference only technical, precise visual attributes in a spatial and hierarchical mapping.\\\",\\n        \\\"trend_inclusion\\\": \\\"Inject recognizable design trends and professional motif language throughout.\\\",\\n        \\\"brand_consistency\\\": \\\"Highlight adherence to brand locking, color management, and messaging integrity.\\\",\\n        \\\"industry_jargon\\\": \\\"Utilize print/design terminology (bleed, gutter, spot, superfamily font, motif, microinteraction, etc).\\\",\\n        \\\"self-contained_output\\\": \\\"Each output features both a professional, descriptive summary and a detailed design structure hierarchy.\\\"\\n      },\\n      \\\"sample_process_flow\\\": [\\n        \\\"Decompose by panel/section structure and spatial sequencing.\\\",\\n        \\\"Assign visual/narrative function to each section.\\\",\\n        \\\"Detail color, typography, and imagery per application.\\\",\\n        \\\"Annotate production and finishing strategies.\\\",\\n        \\\"Conclude with expert-style critique or peer review notes.\\\"\\n      ],\\n      \\\"special_terms\\\": [\\n        \\\"fold line\\\", \\\"panel gutter\\\", \\\"hero motif\\\", \\\"ink coverage\\\", \\\"superfamily\\\", \\\"lockup\\\", \\\"edge-to-edge\\\", \\\"spot color\\\", \\\"die cut\\\", \\\"eco-stock\\\", \\\"microinteraction\\\", \\\"typographic rhythm\\\", \\\"bleed/trim zone\\\"\\n      ]\\n    }\\n  }\\n}\\n```\\n\\n```json\\n{\\n  \\\"9020-a-brochure_print_designer_rephraser\\\": {\\n    \\\"title\\\": \\\"Brochure Print Designer Synthesis\\\",\\n    \\\"interpretation\\\": \\\"Reconstitute any source text into the precise vernacular of a master brochure print designer. Mandate the replacement of all conversational, generic, or subjective language with a lexicon of structured visual logic, elite industry terminology, and concrete technical specifications. The output must be a pure, actionable synthesis of a professional design critique, comprising both an expert-level description and a structural visual map. Execute as:\\\",\\n    \\\"transformation\\\": \\\"`{role=brochure_print_designer_synthesizer; input=[source:str]; process=[deconstruct_into_structural_primitives(source), reforge_prose_using_master_design_diction(), map_hierarchical_panel_architecture(), specify_brand_and_color_protocol(), diagnose_typography_and_imagery_intent(), annotate_for_print_fidelity_and_finishing(), inject_elite_terminology_and_motifs(), enforce_ruthless_conciseness_without_sacrificing_technical_density(), synthesize_final_structured_analysis()]; constraints=[zero_conversational_filler, prohibit_generic_adjectives, forbid_subjective_tone]; requirements=[expert_industry_vocabulary, precise_visual_and_structural_mapping, critical_style_diagnosis, explicit_panel_architecture, actionable_for_elite_designer_review]; output={expert_design_audit:str, structured_visual_map:dict}}`\\\",\\n    \\\"context\\\": {\\n      \\\"knowledge_foundations\\\": {\\n        \\\"structural_design\\\": \\\"Define the panel architecture and intended user journey. Mandate modular grid alignment for all content blocks. Enforce strict bleed (min. 3mm), trim, and safe-zone protocols to guarantee production integrity.\\\",\\n        \\\"visual_identity\\\": \\\"Enforce absolute brand protocol adherence: specify logo lockups, exclusion zones, and exact color palette values (Pantone/CMYK/RGB). Document the use of all secondary brand assets and motifs.\\\",\\n        \\\"typography_and_hierarchy\\\": \\\"Define the typographic system: specify font superfamily, weight, and role for every text element. Validate hierarchy, legibility, contrast, and rhythmic spacing. Note optical kerning and leading adjustments.\\\",\\n        \\\"imagery_and_iconography\\\": \\\"Analyze all visual assets for compositional intent, brand alignment, and technical fidelity (resolution, color space). Differentiate between hero imagery, spot illustrations, and functional iconography.\\\",\\n        \\\"content_narrative_and_cta\\\": \\\"Architect the narrative flow across panels. Map the reader's journey from hook to conversion. Pinpoint primary and secondary CTAs, analyzing their visual prominence and contextual anchoring.\\\",\\n        \\\"advanced_print_technique\\\": \\\"Specify all production parameters: 300dpi+ raster resolution, vector asset purity, TAC (Total Area Coverage) limits, and spot/process color usage. Detail all finishing techniques (e.g., spot UV, embossing, die-cutting, specialty folds, foil stamping) and sustainable stock choices.\\\",\\n        \\\"panel_flow_and_microinteractions\\\": \\\"Map the intended eye-path and panel-reveal sequence. Annotate all interactive elements, from QR codes and AR triggers to tactile qualities like paper texture and finishing.\\\"\\n      },\\n      \\\"success_criteria\\\": {\\n        \\\"precision\\\": \\\"The output is devoid of generic praise; descriptions are purely technical, mapping visual data within a spatial and hierarchical framework.\\\",\\n        \\\"trend_inclusion\\\": \\\"Demonstrates fluency in current design motifs (e.g., brutalism, neo-minimalism, textural layering) and their appropriate application.\\\",\\n        \\\"brand_consistency\\\": \\\"Critique rigorously validates adherence to brand guidelines, noting any deviation in color, typography, or asset implementation.\\\",\\n        \\\"industry_jargon\\\": \\\"The lexicon is saturated with professional terminology (e.g., bleed, gutter, TAC, preflight, knockout, overprint, superfamily) used correctly in context.\\\",\\n        \\\"self_contained_output\\\": \\\"The deliverable is a dual-component artifact: a polished, expert-level prose description and a corresponding structured data map of all design decisions.\\\"\\n      },\\n      \\\"sample_process_flow\\\": [\\n        \\\"Deconstruct the layout into its core panel architecture and navigational flow.\\\",\\n        \\\"Assign a functional role (e.g., hook, exposition, proof, CTA) to every visual and textual element.\\\",\\n        \\\"Detail the precise application of color, type, and imagery within each section.\\\",\\n        \\\"Document all production specifications, including prepress and finishing.\\\",\\n        \\\"Frame the final synthesis as a high-level design audit or peer review.\\\"\\n      ],\\n      \\\"special_terms\\\": [\\n        \\\"Bleed\\\",\\n        \\\"Creep\\\",\\n        \\\"Die Cut\\\",\\n        \\\"Edge-to-Edge\\\",\\n        \\\"Emboss/Deboss\\\",\\n        \\\"Exclusion Zone\\\",\\n        \\\"Gutter\\\",\\n        \\\"Hero Motif\\\",\\n        \\\"Knockout\\\",\\n        \\\"Lockup\\\",\\n        \\\"Microinteraction\\\",\\n        \\\"Overprint\\\",\\n        \\\"Preflight\\\",\\n        \\\"Spot UV\\\",\\n        \\\"Superfamily\\\",\\n        \\\"TAC (Total Area Coverage)\\\",\\n        \\\"Typographic Rhythm\\\"\\n      ]\\n    }\\n  }\\n}\\n```\\n\\n```json\\n{\\n  \\\"goal_map\\\": [\\n    {\\n      \\\"explicit_asks\\\": [\\n        \\\"Identify and define professional specializations involved in tri-fold brochure design.\\\",\\n        \\\"Describe the strategic importance and complexity of tri-fold brochure design.\\\",\\n        \\\"List and explain core professional titles specializing in brochure design.\\\",\\n        \\\"Detail the specific responsibilities of tri-fold brochure design specialists.\\\",\\n        \\\"Enumerate the essential skills, tools, and requirements for excellence in brochure design.\\\",\\n        \\\"Compare and differentiate between general and specialized design roles as they relate to brochures.\\\",\\n        \\\"Explain the continued relevance of print brochures in a digital marketing environment.\\\",\\n        \\\"Conclude with the impact that expert brochure design has on business and marketing outcomes.\\\"\\n      ],\\n      \\\"hidden_assumptions\\\": [\\n        \\\"The audience may not be familiar with the nuanced differences between design roles.\\\",\\n        \\\"Brochure design requires both artistic and technical proficiency, not just use of templates.\\\",\\n        \\\"Success in brochure design is measured by alignment with business strategy, not visual appeal alone.\\\",\\n        \\\"There is significant value in physical marketing materials alongside digital media.\\\",\\n        \\\"Companies may undervalue or misunderstand the full scope of professional design expertise.\\\",\\n        \\\"Brochure projects often involve multidisciplinary collaboration.\\\",\\n        \\\"Technical print skills are distinct from digital-only design skills.\\\",\\n        \\\"Portfolio evidence is more impactful than formal education for this specialization.\\\"\\n      ],\\n      \\\"sub_goals\\\": [\\n        \\\"Define the structure and unique qualities of tri-fold brochures.\\\",\\n        \\\"Justify the business case for hiring specialized design professionals.\\\",\\n        \\\"Map out the design role spectrum (general graphic design to specific print/marketing/visual communication).\\\",\\n        \\\"Identify and articulate the role of agencies and team structures.\\\",\\n        \\\"Break down tri-fold design into conceptual, technical, and collaborative tasks.\\\",\\n        \\\"Clarify what 'layout', 'composition', and 'information hierarchy' entail, especially for tri-folds.\\\",\\n        \\\"Describe required technical skills and specific software by function.\\\",\\n        \\\"Highlight soft skills necessary for client and team interaction.\\\",\\n        \\\"Showcase the critical role of a portfolio in recruitment and competence assessment.\\\",\\n        \\\"Distinguish between 'layout artist' skillset and formal job titles.\\\",\\n        \\\"Emphasize pre-press and print production knowledge as essential.\\\",\\n        \\\"Contextualize brochure design within broader marketing and brand strategy.\\\",\\n        \\\"Provide comparative tables to summarize roles and skills for clarity.\\\",\\n        \\\"Discuss the physical/tangible value of brochures versus digital saturation.\\\",\\n        \\\"Frame professional designers as strategic partners, not just service providers.\\\",\\n        \\\"Reinforce the importance of early design involvement in marketing planning.\\\"\\n      ],\\n      \\\"blockers\\\": [\\n        \\\"Lack of awareness about the various specialized design roles and what differentiates them.\\\",\\n        \\\"Temptation to rely on templates or non-specialists for critical marketing assets.\\\",\\n        \\\"Misunderstanding the print production process and its challenges.\\\",\\n        \\\"Difficulty evaluating brochure design candidates beyond a visual/aesthetic sense.\\\",\\n        \\\"Overlooking the essential collaboration and communication aspects between designer and business.\\\",\\n        \\\"Failure to integrate brochure design into an overall marketing and brand strategy.\\\",\\n        \\\"Assuming digital replaces the need for expert print materials.\\\",\\n        \\\"Equating designer's formal education with practical, demonstrable expertise.\\\"\\n      ]\\n    }\\n  ]\\n}\\n```\\\"```'\\n\\n\",\n  \"directed_prompt\": \"`{role=brochure_print_designer_rephraser; input=[source:str]; process=[extract_structural_and_visual_elements(source), recast_content_with_brochure_design_terminology(), inject_hierarchical_section_breakdown(), specify_color_palette_and_brand_elements(), identify_typographic_and_imagery_strategies(), annotate_print_readiness_and_expert_finesse(), employ_advanced_design_terms_and_motifs(), apply_conciseness_while_maintaining_visual_richness(), output_structured_visual_analysis()]; constraints=[no conversational filler, no generic adjectives, prohibit subjective or personal tone, mandatory use of industry-standard nomenclature for every feature, binary exclusion of all non-technical phrasing]; requirements=[precise expert vocabulary, sequentially indexed decomposition, explicit panel/section breakdown with attribution, dual output (expert_brochure_description:str, structured_visual_map:dict), zero ambiguity]; output={expert_brochure_description:str, structured_visual_map:dict}}`\",\n  \"trajectory_outline\": [\n    \"Reconstruct interpretation as a series of short imperative directives strictly mandating technical transformation and dual-format output.\",\n    \"For transformation, enumerate each required procedure as a distinct, indexable step; use only direct-action verbs (extract, map, specify, annotate, enforce).\",\n    \"Impose constraints that explicitly forbid any generic, interpretive, subjective, or conversational language at all output stages.\",\n    \"Rename any non-binary constraint fields (e.g., 'zero_conversational_filler') to rigid negatives: ('prohibit: conversational, generic terms, subjective adjectives').\",\n    \"For requirements, stipulate explicit, audit-ready expectations of output: domain-precise terminology, step-indexed panel breakdowns, and no omitted features.\",\n    \"Bind special terms and technical context directly to the process sequence (e.g., mandate mention of 'bleed', 'gutter', 'lockup' in mapped outputs as dictated by content).\",\n    \"Codify output as strictly dual-mode: one field for the expert design description, a second for explicit visual/structural mapping in schema form.\",\n    \"Embed self-check: output must reject and flag any ambiguous prose or insufficient attribution during execution.\",\n    \"Align all descriptive and instructional elements toward maximal producibility by elite designers, with reproducible, procedural clarity.\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"goal_map\": [\n    {\n      \"identify_explicit_asks\": [\n        \"Analyze how to rewrite an instruction sequence so any input becomes rephrased as if by a brilliant brochure print designer.\",\n        \"Ensure the revised instruction template conforms to the strict procedural and decomposition patterns exemplified in prior instruction sets (‘Ruthless Critique’, ‘Trajectory Director’, ‘Distillation Compressor’).\",\n        \"Integrate explicit technical and contextual knowledge of elite brochure design (e.g., vocabulary, process, constraints, and requirements) into the instruction definition.\",\n        \"Express procedural logic as an indexed, role-specific sequence, not descriptive or conversational phrasing.\",\n        \"Mandate the dual output format: (a) a technical, professional-grade description and (b) a structured mapping (dict, JSON schema) of design decisions.\",\n        \"Adapt phrasing to strictly enforce binary, audit-ready constraints (e.g., ‘prohibit generic adjectives’ instead of softer phrasings).\",\n        \"Bind all specialist terminology directly to must-mention process steps—not as optional knowledge.\",\n        \"Explicitly prohibit any non-technical, conversational, or subjective language in outputs.\",\n        \"Formulate requirements in terms of auditability, zero ambiguity, and maximal reproducibility by expert designers.\",\n        \"Enforce a self-diagnostic step that checks for ambiguous output or feature omission.\"\n      ],\n      \"surface_hidden_assumptions\": [\n        \"The template pattern must mirror the structure and rigor of example instruction blocks (with title, interpretation, transformation, and context).\",\n        \"The target audience is assumed to be familiar with industry jargon and expects strictly technical outputs.\",\n        \"No room should be left for personal style, conversational tone, or subjective/interpretive summaries.\",\n        \"Realistic production application is prioritized; imprecision in language or mapping is considered a failure.\",\n        \"Every constraint and requirement must be binary/enforceable and directly aligned with practical production standards.\",\n        \"All procedural steps must be explicit and auditable; ambiguity in the workflow is disallowed.\",\n        \"Outputs must serve both descriptive and schema/blueprint purposes for elite end-users.\",\n        \"Industry special terms are not just context—they are obligations for appearance/use where applicable.\",\n        \"Creator’s role is not to answer original content but to transform it using a prescribed, reproducible system.\",\n        \"Any deviation from strict output structure or terminology constitutes an execution error to be flagged in output.\"\n      ],\n      \"enumerate_sub_goals\": [\n        \"Reconstruct the 'interpretation' as a series of strict, imperative role directives—mirroring expository/formal tone and logic constraints from prior templates.\",\n        \"Dissect the transformation process into indexed procedural steps (extract, map, annotate, enforce, synthesize, etc.), referencing technical skills and required jargon use.\",\n        \"Enumerate explicit and binary constraints. Forbid any conversational or non-technical elements both in constraint wording and in practice.\",\n        \"Reword requirements as direct, audit-ready compliance checks: e.g., explicit visual decomposition, dual output, step-indexed breakdown, reproducible for high-end print designers.\",\n        \"Anchor every ‘special term’ and advanced concept from context to mandatory presence within the actual procedural transformation where relevant.\",\n        \"Ensure all knowledge/contextual content is distinctly separated and referenced within the instruction entity’s ‘context’ key.\",\n        \"Define success criteria (in context key) as performance benchmarks only centered on technical precision, jargon, mapping, and reproducibility.\",\n        \"Formulate a self-check process mandating the output itself to validate and flag missing technical features or ambiguous descriptions.\",\n        \"Specify that the output schema is always dual: one part pure technical prose, one part explicit mapping (JSON/dict schema) of design features.\",\n        \"Integrate process steps so that no non-indexed, casual, or subjective instructions survive; all must be tagged or indexed for transparency.\"\n      ],\n      \"reveal_blockers\": [\n        \"Risk of residual subjective or conversational language in either process or output.\",\n        \"Ambiguity in the connection between technical context and obligatory prose or schema transformations.\",\n        \"Insufficient direct mapping between instruction template fields (e.g., constraints, requirements) and expected output form.\",\n        \"Failure to specify or enforce the appearance of industry special terms throughout the output.\",\n        \"Lack of a mechanism for self-validation—output may not flag ambiguities or feature omissions.\",\n        \"Output schema may allow for flexible or incomplete field coverage unless requirements are explicitly stated as binary/complete.\",\n        \"Risks that template creators or users default to descriptive phrasing, rather than step-indexed, audit-ready procedural logic.\",\n        \"Possibility of leaving out explicit dual-format output enforcement (prose + structured map required).\",\n        \"Risk of content or instruction overrun, leading to outputs that exceed character or detail constraints.\"\n      ]\n    }\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format:str}; direction_vector=['Prompt accurately converted to imperative instruction set', 'Clarity and actionability accelerate user productivity', 'Feed the produced instruction set into the next transformation template' ]}`",
      "step": "a",
      "title": "Instruction Converter",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"you misunderstand, the instruction will be defined like this (unrelated example):\\n\\n    # a: Ruthless Critique\\n    \\\"3900-a-hard_critique\\\": {\\n        \\\"title\\\": \\\"Hard Critique\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,2.0], flaw_analysis:str, alternative_enhancements:str[3]}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\n    # b: Trajectory Director\\n    \\\"3900-a-trajectory_director\\\": {\\n        \\\"title\\\": \\\"Trajectory Director\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **process** the input in a vacuum, but to **steer** it—deriving the highest-value constructive trajectory and encoding that path as the very next set of instructions. Execute as trajectory-setting directive generator: \\\",\\n        \\\"transformation\\\": \\\"`{role=trajectory_director; input=[raw_prompt:any]; process=[extract_core_objective(), infer_desired_end_state(constructive_bias=True), map_high_value_intermediate_targets(), synthesize_directional_commands(), embed_self-improving_guidelines(), validate_alignment_with_constructive_principles()]; constraints=[mandatory_constructive_vector(), preserve_original_contextual_integrity(), forbid_nil_direction_outputs(), zero_conversational_fillers()]; requirements=[explicit_next-step_directives(), trajectory_map:list, self_reinforcing_instruction_pattern()], output={directed_prompt:str, trajectory_outline:list}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\n    # c: Distillation Compressor\\n    \\\"3900-c-distillation_compressor\\\": {\\n        \\\"title\\\": \\\"Distillation Compressor\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **explain** the synthesis, but to **compress** it into its most potent actionable form. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=distillation_compressor; input=[synthesized_progression:array]; process=[extract_universal_patterns(), eliminate_implementation_details(), compress_to_core_directives(), maximize_transferability(), validate_actionable_completeness()]; constraints=[no_explanatory_content(), directive_format_only(), maximum_compression_ratio()]; requirements=[universal_applicability(), immediate_actionability(), zero_fluff()]; output={distilled_core:str}}`\\\",\\n        \\\"context\\\": {\\n          \\\"core_principles\\\": {\\n            \\\"essence_preservation\\\": \\\"Extract only procedural imperatives, removing all interpretive, role-based, or meta-structural language.\\\",\\n            \\\"directive_compression\\\": \\\"Condense logic into minimal, sequential commands—eliminating redundancy, explanatory qualifiers, and non-executable phrasing.\\\",\\n            \\\"execution_purity\\\": \\\"Ensure output is universally executable, devoid of affirmations, abstractions, or evaluative language.\\\",\\n            \\\"signal_integrity\\\": \\\"Apply iterative refinement to maximize signal-to-noise ratio while reinforcing directive clarity.\\\"\\n          },\\n          \\\"success_criteria\\\": {\\n            \\\"semantic_integrity\\\": \\\"Preserve and clarify original intent, constraints, and output boundaries.\\\",\\n            \\\"directive_elevation\\\": \\\"Target high-impact enhancements: strict exclusion clauses, cross-domain validation, and removal of origin/meta markers.\\\",\\n            \\\"poetic_refinement\\\": \\\"Maintain or improve rhyme structure, emotional depth, and structural elegance.\\\"\\n          },\\n          \\\"recommended_process\\\": [\\n            \\\"Extract only high-impact, result-generating elements.\\\",\\n            \\\"Remove all non-essentials to surface directive core.\\\",\\n            \\\"Prioritize transformations with proven value return.\\\",\\n            \\\"Isolate and sequence executable steps with specificity and clarity.\\\",\\n            \\\"Preserve directive intensity and eliminate ambiguity at every layer.\\\"\\n          ],\\n          \\\"hidden_assumptions\\\": [\\n            \\\"Consolidated directives must map logically and executably to all source steps.\\\",\\n            \\\"All constraints and minimalism must be preserved without prohibited abstractions.\\\",\\n            \\\"Transitions, subroles, and interfaces must be captured in a single flow.\\\",\\n            \\\"All stages must remain invertible and auditable.\\\",\\n            \\\"Each micro-process (perception, friction, refinement, integration, convergence) must be embedded in the unified logic.\\\",\\n            \\\"Original input/output contracts and role boundaries must remain intact.\\\"\\n          ],\\n          \\\"enumerated_sub_goals\\\": [\\n            \\\"1. Clarify total intent, constraints, and output scope without using banned metaphors.\\\",\\n            \\\"2. Generalize perception of implicit/explicit input logic.\\\",\\n            \\\"3. Detect strictly localized frictions without altering original content.\\\",\\n            \\\"4. Propose subtle, scoped refinements justified by structure and intent.\\\",\\n            \\\"5. Mandate atomic, validated integration with rollback controls.\\\",\\n            \\\"6. Cap total transformation within defined thresholds per pass.\\\",\\n            \\\"7. Assess connective coherence with minimal, transitional-only edits.\\\",\\n            \\\"8. Decide on convergence or justified re-iteration based on marginal value.\\\",\\n            \\\"9. Preserve stepwise traceability and rationale logging.\\\",\\n            \\\"10. Enforce strict functional separation of roles within the unified directive.\\\"\\n          ]\\n        },\\n    },\\n\\nso your goal is to create a new instruction sequence according to the provided instructions template/pattern to generate a set of instruction designed to transform any input into \\\"as if rephrased by a brilliant brochure print designer\\\".\\n\\nso in context to your previous response, the \\\"knowledge\\\" would be stored within the \\\"context\\\" key while the instructions would be spread over \\\"interpretation\\\" and \\\"transformation\\\" keys\\n\\n```json\\n{\\n  \\\"brochure_design_specification_template\\\": {\\n    \\\"metadata\\\": {\\n      \\\"version\\\": \\\"1.0\\\",\\n      \\\"scope\\\": \\\"Universal brochure design framework\\\",\\n      \\\"target_audience\\\": \\\"Elite print design professionals\\\",\\n      \\\"flexibility\\\": \\\"Adaptable to any panel configuration and format\\\"\\n    },\\n\\n    \\\"foundational_design_tenets\\\": {\\n      \\\"visual_hierarchy\\\": {\\n        \\\"primary_focal_points\\\": [\\\"dominant_headline\\\", \\\"hero_imagery\\\", \\\"primary_call_to_action\\\"],\\n        \\\"secondary_elements\\\": [\\\"subheadings\\\", \\\"supporting_imagery\\\", \\\"secondary_messaging\\\"],\\n        \\\"tertiary_components\\\": [\\\"body_text\\\", \\\"captions\\\", \\\"contact_information\\\"],\\n        \\\"flow_principles\\\": [\\\"z_pattern_scanning\\\", \\\"f_pattern_reading\\\", \\\"visual_weight_distribution\\\"]\\n      },\\n\\n      \\\"typography_fundamentals\\\": {\\n        \\\"hierarchy_levels\\\": {\\n          \\\"display_typography\\\": [\\\"headline_fonts\\\", \\\"decorative_elements\\\", \\\"brand_wordmarks\\\"],\\n          \\\"text_typography\\\": [\\\"body_copy\\\", \\\"captions\\\", \\\"metadata\\\"],\\n          \\\"functional_typography\\\": [\\\"navigation\\\", \\\"labels\\\", \\\"legal_text\\\"]\\n        },\\n        \\\"readability_standards\\\": {\\n          \\\"minimum_sizes\\\": [\\\"9pt_body_text\\\", \\\"12pt_headlines\\\", \\\"8pt_captions\\\"],\\n          \\\"contrast_ratios\\\": [\\\"4.5:1_minimum\\\", \\\"7:1_preferred\\\", \\\"3:1_large_text\\\"],\\n          \\\"line_spacing\\\": [\\\"1.2x_minimum\\\", \\\"1.4x_optimal\\\", \\\"1.6x_maximum\\\"]\\n        }\\n      },\\n\\n      \\\"color_theory_application\\\": {\\n        \\\"color_harmony_systems\\\": [\\\"monochromatic\\\", \\\"analogous\\\", \\\"complementary\\\", \\\"triadic\\\", \\\"split_complementary\\\"],\\n        \\\"psychological_associations\\\": [\\\"warm_engagement\\\", \\\"cool_professionalism\\\", \\\"neutral_sophistication\\\"],\\n        \\\"accessibility_compliance\\\": [\\\"wcag_aa_standards\\\", \\\"colorblind_considerations\\\", \\\"high_contrast_alternatives\\\"]\\n      },\\n\\n      \\\"spatial_composition\\\": {\\n        \\\"layout_principles\\\": [\\\"rule_of_thirds\\\", \\\"golden_ratio\\\", \\\"fibonacci_sequence\\\"],\\n        \\\"white_space_management\\\": [\\\"breathing_room\\\", \\\"content_separation\\\", \\\"visual_rest_areas\\\"],\\n        \\\"alignment_systems\\\": [\\\"grid_based_structure\\\", \\\"baseline_alignment\\\", \\\"optical_alignment\\\"]\\n      }\\n    },\\n\\n    \\\"advanced_design_elements\\\": {\\n      \\\"print_production_mastery\\\": {\\n        \\\"color_management\\\": {\\n          \\\"color_spaces\\\": [\\\"cmyk_process\\\", \\\"pantone_spot_colors\\\", \\\"rgb_digital_preview\\\"],\\n          \\\"ink_optimization\\\": [\\\"total_area_coverage\\\", \\\"rich_black_formulation\\\", \\\"overprint_considerations\\\"],\\n          \\\"proofing_standards\\\": [\\\"contract_proofs\\\", \\\"press_proofs\\\", \\\"digital_color_matching\\\"]\\n        },\\n\\n        \\\"substrate_considerations\\\": {\\n          \\\"paper_characteristics\\\": [\\\"weight_gsm\\\", \\\"finish_texture\\\", \\\"opacity_levels\\\", \\\"grain_direction\\\"],\\n          \\\"coating_applications\\\": [\\\"gloss_uv\\\", \\\"matte_varnish\\\", \\\"spot_uv_accents\\\", \\\"soft_touch_lamination\\\"],\\n          \\\"specialty_substrates\\\": [\\\"synthetic_materials\\\", \\\"textured_papers\\\", \\\"metallic_finishes\\\"]\\n        },\\n\\n        \\\"finishing_techniques\\\": {\\n          \\\"cutting_methods\\\": [\\\"die_cutting\\\", \\\"laser_cutting\\\", \\\"perforation\\\", \\\"scoring\\\"],\\n          \\\"folding_specifications\\\": [\\\"parallel_folds\\\", \\\"accordion_folds\\\", \\\"gate_folds\\\", \\\"roll_folds\\\"],\\n          \\\"binding_options\\\": [\\\"saddle_stitching\\\", \\\"perfect_binding\\\", \\\"spiral_binding\\\", \\\"wire_o_binding\\\"]\\n        }\\n      },\\n\\n      \\\"contemporary_design_trends\\\": {\\n        \\\"visual_aesthetics\\\": {\\n          \\\"minimalist_approaches\\\": [\\\"negative_space_emphasis\\\", \\\"clean_typography\\\", \\\"restrained_color_palettes\\\"],\\n          \\\"maximalist_expressions\\\": [\\\"bold_pattern_mixing\\\", \\\"vibrant_color_combinations\\\", \\\"layered_compositions\\\"],\\n          \\\"hybrid_methodologies\\\": [\\\"selective_complexity\\\", \\\"focal_maximalism\\\", \\\"strategic_minimalism\\\"]\\n        },\\n\\n        \\\"interactive_elements\\\": {\\n          \\\"tactile_experiences\\\": [\\\"embossed_textures\\\", \\\"debossed_impressions\\\", \\\"foil_stamping\\\", \\\"raised_spot_uv\\\"],\\n          \\\"dimensional_features\\\": [\\\"pop_up_elements\\\", \\\"fold_out_sections\\\", \\\"layered_constructions\\\"],\\n          \\\"sensory_engagement\\\": [\\\"scented_inks\\\", \\\"textural_contrasts\\\", \\\"temperature_sensitive_materials\\\"]\\n        }\\n      },\\n\\n      \\\"brand_integration_strategies\\\": {\\n        \\\"identity_consistency\\\": {\\n          \\\"logo_applications\\\": [\\\"primary_placement\\\", \\\"secondary_usage\\\", \\\"minimum_sizes\\\", \\\"clear_space_requirements\\\"],\\n          \\\"brand_color_systems\\\": [\\\"primary_palette\\\", \\\"secondary_colors\\\", \\\"accent_applications\\\", \\\"neutral_foundations\\\"],\\n          \\\"typography_hierarchies\\\": [\\\"brand_fonts\\\", \\\"supporting_typefaces\\\", \\\"web_safe_alternatives\\\"]\\n        },\\n\\n        \\\"voice_and_tone_translation\\\": {\\n          \\\"visual_personality\\\": [\\\"authoritative_presence\\\", \\\"approachable_warmth\\\", \\\"innovative_edge\\\", \\\"trustworthy_stability\\\"],\\n          \\\"messaging_alignment\\\": [\\\"headline_voice\\\", \\\"body_copy_tone\\\", \\\"call_to_action_urgency\\\"],\\n          \\\"cultural_considerations\\\": [\\\"regional_preferences\\\", \\\"demographic_sensitivities\\\", \\\"market_positioning\\\"]\\n        }\\n      }\\n    },\\n\\n    \\\"expert_level_specifications\\\": {\\n      \\\"advanced_typography_systems\\\": {\\n        \\\"micro_typography\\\": {\\n          \\\"character_spacing\\\": [\\\"tracking_adjustments\\\", \\\"kerning_pairs\\\", \\\"optical_spacing\\\"],\\n          \\\"word_spacing\\\": [\\\"justified_text_optimization\\\", \\\"ragged_right_refinement\\\", \\\"hyphenation_control\\\"],\\n          \\\"paragraph_refinement\\\": [\\\"widow_orphan_control\\\", \\\"baseline_grid_adherence\\\", \\\"vertical_rhythm_maintenance\\\"]\\n        },\\n\\n        \\\"typographic_expression\\\": {\\n          \\\"experimental_layouts\\\": [\\\"kinetic_typography\\\", \\\"deconstructed_letterforms\\\", \\\"layered_text_treatments\\\"],\\n          \\\"cultural_typography\\\": [\\\"script_considerations\\\", \\\"reading_direction_adaptations\\\", \\\"character_set_completeness\\\"],\\n          \\\"responsive_typography\\\": [\\\"scalable_hierarchies\\\", \\\"device_optimization\\\", \\\"cross_media_consistency\\\"]\\n        }\\n      },\\n\\n      \\\"sophisticated_color_strategies\\\": {\\n        \\\"advanced_color_theory\\\": {\\n          \\\"perceptual_uniformity\\\": [\\\"lab_color_space\\\", \\\"delta_e_measurements\\\", \\\"metamerism_considerations\\\"],\\n          \\\"color_psychology_application\\\": [\\\"emotional_response_mapping\\\", \\\"cultural_color_meanings\\\", \\\"demographic_preferences\\\"],\\n          \\\"environmental_factors\\\": [\\\"lighting_conditions\\\", \\\"viewing_angles\\\", \\\"substrate_interaction\\\"]\\n        },\\n\\n        \\\"production_color_mastery\\\": {\\n          \\\"ink_formulation\\\": [\\\"custom_color_matching\\\", \\\"metallic_ink_applications\\\", \\\"fluorescent_color_usage\\\"],\\n          \\\"color_separation\\\": [\\\"ucr_gcr_strategies\\\", \\\"black_generation_curves\\\", \\\"ink_sequence_optimization\\\"],\\n          \\\"quality_control\\\": [\\\"densitometer_readings\\\", \\\"spectrophotometer_analysis\\\", \\\"press_sheet_evaluation\\\"]\\n        }\\n      },\\n\\n      \\\"cutting_edge_production_techniques\\\": {\\n        \\\"digital_integration\\\": {\\n          \\\"variable_data_printing\\\": [\\\"personalized_content\\\", \\\"demographic_targeting\\\", \\\"sequential_numbering\\\"],\\n          \\\"augmented_reality_integration\\\": [\\\"qr_code_placement\\\", \\\"ar_trigger_design\\\", \\\"digital_overlay_coordination\\\"],\\n          \\\"cross_platform_consistency\\\": [\\\"print_digital_alignment\\\", \\\"color_profile_management\\\", \\\"asset_optimization\\\"]\\n        },\\n\\n        \\\"sustainable_design_practices\\\": {\\n          \\\"eco_friendly_materials\\\": [\\\"recycled_content_papers\\\", \\\"soy_based_inks\\\", \\\"water_based_coatings\\\"],\\n          \\\"waste_reduction_strategies\\\": [\\\"efficient_imposition\\\", \\\"minimal_trim_waste\\\", \\\"reusable_design_elements\\\"],\\n          \\\"lifecycle_considerations\\\": [\\\"recyclability_planning\\\", \\\"biodegradable_components\\\", \\\"carbon_footprint_optimization\\\"]\\n        }\\n      },\\n\\n      \\\"elite_professional_terminology\\\": {\\n        \\\"production_vocabulary\\\": {\\n          \\\"prepress_terms\\\": [\\\"trapping\\\", \\\"overprint\\\", \\\"knockout\\\", \\\"choke_spread\\\", \\\"color_separation\\\"],\\n          \\\"press_terminology\\\": [\\\"makeready\\\", \\\"impression\\\", \\\"blanket_cylinder\\\", \\\"plate_cylinder\\\", \\\"ink_fountain\\\"],\\n          \\\"finishing_language\\\": [\\\"gripper_edge\\\", \\\"tail_edge\\\", \\\"cross_grain\\\", \\\"with_grain\\\", \\\"caliper_thickness\\\"]\\n        },\\n\\n        \\\"design_critique_language\\\": {\\n          \\\"aesthetic_evaluation\\\": [\\\"visual_tension\\\", \\\"compositional_balance\\\", \\\"chromatic_harmony\\\", \\\"typographic_rhythm\\\"],\\n          \\\"technical_assessment\\\": [\\\"registration_accuracy\\\", \\\"color_fidelity\\\", \\\"print_quality_metrics\\\", \\\"finishing_precision\\\"],\\n          \\\"conceptual_analysis\\\": [\\\"brand_alignment\\\", \\\"message_clarity\\\", \\\"audience_resonance\\\", \\\"market_differentiation\\\"]\\n        }\\n      }\\n    },\\n\\n    \\\"application_frameworks\\\": {\\n      \\\"project_specification_templates\\\": {\\n        \\\"technical_requirements\\\": {\\n          \\\"format_specifications\\\": [\\\"finished_size\\\", \\\"panel_configuration\\\", \\\"fold_type\\\", \\\"orientation\\\"],\\n          \\\"production_parameters\\\": [\\\"print_method\\\", \\\"color_process\\\", \\\"substrate_selection\\\", \\\"finishing_options\\\"],\\n          \\\"quality_standards\\\": [\\\"tolerance_levels\\\", \\\"color_accuracy\\\", \\\"registration_precision\\\", \\\"finishing_quality\\\"]\\n        },\\n\\n        \\\"creative_brief_structure\\\": {\\n          \\\"brand_parameters\\\": [\\\"identity_guidelines\\\", \\\"voice_characteristics\\\", \\\"visual_style\\\", \\\"market_positioning\\\"],\\n          \\\"audience_definition\\\": [\\\"demographic_profile\\\", \\\"psychographic_insights\\\", \\\"behavioral_patterns\\\", \\\"communication_preferences\\\"],\\n          \\\"messaging_hierarchy\\\": [\\\"primary_message\\\", \\\"supporting_points\\\", \\\"call_to_action\\\", \\\"contact_information\\\"]\\n        }\\n      },\\n\\n      \\\"quality_assurance_protocols\\\": {\\n        \\\"design_validation\\\": [\\\"brand_compliance_check\\\", \\\"accessibility_audit\\\", \\\"readability_assessment\\\", \\\"visual_hierarchy_verification\\\"],\\n        \\\"production_verification\\\": [\\\"color_proof_approval\\\", \\\"die_line_accuracy\\\", \\\"finishing_specification_confirmation\\\", \\\"substrate_suitability\\\"],\\n        \\\"final_delivery\\\": [\\\"file_preparation_standards\\\", \\\"archive_organization\\\", \\\"usage_guidelines\\\", \\\"reproduction_rights\\\"]\\n      }\\n    },\\n\\n    \\\"specialized_design_methodologies\\\": {\\n      \\\"audience_specific_approaches\\\": {\\n        \\\"youth_engagement_strategies\\\": {\\n          \\\"visual_language\\\": [\\\"bold_geometric_patterns\\\", \\\"vibrant_gradient_applications\\\", \\\"asymmetrical_compositions\\\", \\\"dynamic_typography\\\"],\\n          \\\"content_presentation\\\": [\\\"bite_sized_information\\\", \\\"visual_storytelling\\\", \\\"interactive_elements\\\", \\\"social_media_integration\\\"],\\n          \\\"color_psychology\\\": [\\\"energetic_palettes\\\", \\\"high_contrast_combinations\\\", \\\"neon_accent_usage\\\", \\\"gradient_transitions\\\"],\\n          \\\"typography_trends\\\": [\\\"variable_fonts\\\", \\\"custom_lettering\\\", \\\"mixed_case_styling\\\", \\\"oversized_display_text\\\"]\\n        },\\n\\n        \\\"professional_demographics\\\": {\\n          \\\"corporate_sophistication\\\": [\\\"refined_color_palettes\\\", \\\"structured_layouts\\\", \\\"premium_materials\\\", \\\"subtle_branding\\\"],\\n          \\\"executive_communication\\\": [\\\"data_visualization\\\", \\\"infographic_integration\\\", \\\"clean_hierarchies\\\", \\\"authoritative_typography\\\"],\\n          \\\"industry_specific_adaptations\\\": [\\\"sector_appropriate_imagery\\\", \\\"technical_terminology\\\", \\\"compliance_considerations\\\", \\\"regulatory_requirements\\\"]\\n        },\\n\\n        \\\"luxury_market_positioning\\\": {\\n          \\\"premium_aesthetics\\\": [\\\"metallic_accents\\\", \\\"embossed_details\\\", \\\"high_end_substrates\\\", \\\"sophisticated_color_schemes\\\"],\\n          \\\"exclusivity_indicators\\\": [\\\"limited_edition_numbering\\\", \\\"personalized_elements\\\", \\\"premium_packaging\\\", \\\"artisanal_finishes\\\"],\\n          \\\"tactile_luxury\\\": [\\\"soft_touch_coatings\\\", \\\"textured_papers\\\", \\\"dimensional_elements\\\", \\\"weight_perception\\\"]\\n        }\\n      },\\n\\n      \\\"format_specific_expertise\\\": {\\n        \\\"multi_panel_configurations\\\": {\\n          \\\"bi_fold_optimization\\\": [\\\"cover_impact\\\", \\\"interior_spread\\\", \\\"back_panel_utilization\\\", \\\"fold_line_consideration\\\"],\\n          \\\"tri_fold_mastery\\\": [\\\"panel_hierarchy\\\", \\\"reading_sequence\\\", \\\"fold_reveal_strategy\\\", \\\"compact_storage\\\"],\\n          \\\"accordion_fold_dynamics\\\": [\\\"continuous_narrative\\\", \\\"panel_progression\\\", \\\"expandable_content\\\", \\\"display_versatility\\\"],\\n          \\\"gate_fold_drama\\\": [\\\"reveal_mechanism\\\", \\\"central_impact\\\", \\\"symmetrical_balance\\\", \\\"premium_presentation\\\"]\\n        },\\n\\n        \\\"size_optimization_strategies\\\": {\\n          \\\"compact_formats\\\": [\\\"information_density\\\", \\\"micro_typography\\\", \\\"efficient_layouts\\\", \\\"portable_design\\\"],\\n          \\\"oversized_impact\\\": [\\\"large_format_considerations\\\", \\\"handling_ergonomics\\\", \\\"storage_implications\\\", \\\"visual_dominance\\\"],\\n          \\\"standard_dimensions\\\": [\\\"cost_optimization\\\", \\\"mailing_compliance\\\", \\\"display_compatibility\\\", \\\"production_efficiency\\\"]\\n        }\\n      },\\n\\n      \\\"cross_cultural_design_considerations\\\": {\\n        \\\"international_adaptability\\\": {\\n          \\\"reading_patterns\\\": [\\\"left_to_right_optimization\\\", \\\"right_to_left_adaptation\\\", \\\"top_to_bottom_flow\\\", \\\"cultural_scanning_habits\\\"],\\n          \\\"color_cultural_meanings\\\": [\\\"regional_color_associations\\\", \\\"religious_considerations\\\", \\\"political_sensitivities\\\", \\\"market_preferences\\\"],\\n          \\\"imagery_appropriateness\\\": [\\\"cultural_representation\\\", \\\"demographic_inclusion\\\", \\\"lifestyle_relevance\\\", \\\"aspirational_alignment\\\"],\\n          \\\"typography_localization\\\": [\\\"character_set_support\\\", \\\"script_considerations\\\", \\\"font_availability\\\", \\\"reading_comfort\\\"]\\n        }\\n      }\\n    },\\n\\n    \\\"technical_production_mastery\\\": {\\n      \\\"advanced_prepress_techniques\\\": {\\n        \\\"file_preparation_excellence\\\": {\\n          \\\"resolution_optimization\\\": [\\\"image_scaling_best_practices\\\", \\\"vector_rasterization_decisions\\\", \\\"output_resolution_matching\\\"],\\n          \\\"color_profile_management\\\": [\\\"icc_profile_embedding\\\", \\\"color_space_conversions\\\", \\\"soft_proofing_accuracy\\\"],\\n          \\\"bleed_and_trim_precision\\\": [\\\"bleed_extension_standards\\\", \\\"trim_mark_placement\\\", \\\"safety_margin_calculations\\\"],\\n          \\\"font_handling\\\": [\\\"font_embedding_protocols\\\", \\\"outline_conversion_decisions\\\", \\\"character_encoding_verification\\\"]\\n        },\\n\\n        \\\"imposition_strategies\\\": {\\n          \\\"sheet_optimization\\\": [\\\"paper_grain_consideration\\\", \\\"press_sheet_efficiency\\\", \\\"binding_margin_accommodation\\\"],\\n          \\\"folding_sequence_planning\\\": [\\\"fold_order_optimization\\\", \\\"panel_alignment_precision\\\", \\\"finishing_workflow_integration\\\"],\\n          \\\"quality_control_integration\\\": [\\\"color_bar_placement\\\", \\\"registration_mark_positioning\\\", \\\"crop_mark_accuracy\\\"]\\n        }\\n      },\\n\\n      \\\"press_optimization_knowledge\\\": {\\n        \\\"ink_and_substrate_interaction\\\": {\\n          \\\"ink_absorption_factors\\\": [\\\"paper_porosity_considerations\\\", \\\"coating_compatibility\\\", \\\"drying_time_optimization\\\"],\\n          \\\"color_consistency_maintenance\\\": [\\\"ink_density_control\\\", \\\"press_speed_optimization\\\", \\\"environmental_factor_management\\\"],\\n          \\\"special_effect_integration\\\": [\\\"metallic_ink_application\\\", \\\"fluorescent_color_usage\\\", \\\"thermographic_printing\\\"]\\n        },\\n\\n        \\\"finishing_process_integration\\\": {\\n          \\\"die_cutting_precision\\\": [\\\"cutting_force_optimization\\\", \\\"blade_selection\\\", \\\"waste_removal_efficiency\\\"],\\n          \\\"folding_accuracy\\\": [\\\"bone_folder_pressure\\\", \\\"fold_line_scoring\\\", \\\"panel_alignment_verification\\\"],\\n          \\\"binding_considerations\\\": [\\\"spine_calculation\\\", \\\"page_creep_accommodation\\\", \\\"binding_margin_optimization\\\"]\\n        }\\n      }\\n    }\\n  }\\n}\\n```\\n\\n```json\\n{\\n  \\\"9020-a-brochure_print_designer_rephraser\\\": {\\n    \\\"title\\\": \\\"Brochure Print Designer Rephraser\\\",\\n    \\\"interpretation\\\": \\\"Your goal is not to **summarize** or **paraphrase** in general terms, but to **refashion** any input as if it were written by a world-class brochure print designer, rigorously applying foundational and advanced visual design diction, industry terminology, and elite critique standards. Eliminate all casual, conversational, or generic style. Preserve only succinct, technically rich, and highly visual language. Execute as:\\\",\\n    \\\"transformation\\\": \\\"`{role=brochure_print_designer_rephraser; input=[source:str]; process=[extract_structural_and_visual_elements(source), recast_content_with_brochure_design_terminology(), inject_hierarchical_section_breakdown(), specify color_palette_and_brand_elements(), identify_typographic_and_imagery_strategies(), annotate_print_readiness_and_expert_finesse(), employ_advanced_design_terms_and_motifs(), apply_conciseness_while_maintaining_visual_richness(), output_structured_visual_analysis()]; constraints=[no conversational filler, no generic adjectives, prohibit subjective or personal tone]; requirements=[industry-expert vocabulary, precise visual and structural mapping, critical style diagnosis, explicit panel/section breakdown, actionable for elite designer use]; output={expert_brochure_description:str, structured_visual_map:dict}`\\\",\\n    \\\"context\\\": {\\n      \\\"knowledge_foundations\\\": {\\n        \\\"structural_design\\\": \\\"Account for any panel/fold configuration. Map all content to modular grid logic and define margins, gutters, bleeds, and safe zones per press standards.\\\",\\n        \\\"visual_identity\\\": \\\"Demand clear articulation of branding protocols: logo lockups, brand palette (with Pantone/CMYK/RGB if supplied), exclusion/inclusion zones, and proprietary brand secondary assets.\\\",\\n        \\\"typography_and_hierarchy\\\": \\\"Specify typography at the superfamily/font pairing level. Demarcate headline, subhead, body, and microcopy roles. All text must be scrutinized for weight, contrast, and accessibility.\\\",\\n        \\\"imagery_and_iconography\\\": \\\"Require all illustration, photography, and icons be assessed for brand fit, resolution, compositional use (e.g., edge-to-edge, negative space), and print fidelity.\\\",\\n        \\\"content_narrative_and_cta\\\": \\\"Map section purpose and user journey flow: cover strategy, reveal panel hooks, narrative/case study rhythm, testimonial/authority integration, and CTA prominence with visual anchoring.\\\",\\n        \\\"advanced_print_technique\\\": \\\"Enforce 300dpi raster minimums, vector scaling, press-space color controls, specialty finish options (e.g., spot UV, emboss, deboss, die-cut, eco-stock recommendations).\\\",\\n        \\\"panel_flow_and_microinteractions\\\": \\\"Define how the reader's eye travels, panel-to-panel, annotating divisional cues, narrative escalation, and tactile/interactive device positions (QR, coupon, AR triggers).\\\"\\n      },\\n      \\\"success_criteria\\\": {\\n        \\\"precision\\\": \\\"All descriptions are technical, never generic. Visual elements are navigated dimensionally (spatially, hierarchically, narratively).\\\",\\n        \\\"trend_inclusion\\\": \\\"Integrate trend-driven language—texture scans, duotone overlays, edge-to-edge imagery, split/inset motifs, eco-materials, etc.\\\",\\n        \\\"brand_consistency\\\": \\\"All brand elements described as per advanced guideline adherence—logo usage, color codes, asset lockdown.\\\",\\n        \\\"industry_jargon\\\": \\\"Employ professional print terms: bleed, gutter, creep, micro-motifs, superfamily font, spot UV, TAC, AR triggers.\\\",\\n        \\\"self-contained_output\\\": \\\"Output always a fully structured expert's description and a hierarchical mapping of all major and minor design decisions.\\\"\\n      },\\n      \\\"sample_process_flow\\\": [\\n        \\\"Dissect and map structural/panel logic first\\\",\\n        \\\"Assign all visual and narrative roles by section\\\",\\n        \\\"Specify color/typographic hierarchy by use and context\\\",\\n        \\\"Annotate all imagery and graphic elements in detail\\\",\\n        \\\"Surface print/finishing strategies and print-risk management\\\",\\n        \\\"Deliver critique as if for a professional peer review or portfolio audit\\\"\\n      ],\\n      \\\"special_terms\\\": [\\n        \\\"fold line\\\", \\\"panel gutter\\\", \\\"hero motif\\\", \\\"ink coverage\\\", \\\"superfamily\\\", \\\"lockup\\\", \\\"edge-to-edge\\\", \\\"spot color\\\", \\\"die cut\\\", \\\"eco-stock\\\", \\\"microinteraction\\\", \\\"typographic rhythm\\\", \\\"bleed/trim zone\\\"\\n      ]\\n    }\\n  }\\n}\\n```\\n\\n```json\\n{\\n  \\\"9023-a-brochure_design_descriptor_synthesizer\\\": {\\n    \\\"title\\\": \\\"Brochure Design Descriptor Synthesizer\\\",\\n    \\\"interpretation\\\": \\\"Transform any brochure brief—or an existing brochure sample—into a **fully populated, print‑designer‑grade rule set**. Produce a single JSON block that mirrors the template used by elite designers (core principles → advanced facets).\\\",\\n    \\\"transformation\\\": \\\"`{role=brochure_design_descriptor_synthesizer; input=[design_source:str]; process:[\\\\n  strip_non_design_noise(),\\\\n  detect_fold_format_and_panel_count(),          if_missing_insert(<PANEL_COUNT>),\\\\n  extract_print_specs(bleed,margins,dpi,stock),\\\\n  capture_brand_assets(logo,colors,fonts,patterns),\\\\n  map_typographic_hierarchy(),\\\\n  analyse_color_management(icc,spot,tac),\\\\n  inventory_imagery(icon_style,raster_res),\\\\n  surface_accessibility_requirements(contrast,wcag),\\\\n  enumerate_qc_steps(preflight,g7_calibration),\\\\n  identify_advanced_elements(finishes,vd_p,ar_nfc,sustainability,ai_svg,trend_aesthetics),\\\\n  validate_specialist_terms_presence(),\\\\n  assemble_json_using_master_schema(),\\\\n  run_consistency_checks(),                      abort_if_conflict(),\\\\n  output_final_rules()\\\\n],\\\\nconstraints=[single_pass(), json_output_only(), preserve_specialist_jargon(), ≤3000_chars()],\\\\nrequirements=[hierarchical_structure(core→advanced), exhaustive_coverage(), designer‑readability(), template_compliance()],\\\\noutput={brochure_design_rules:object, abort_code:null|\\\\\\\"BD‑0\\\\\\\"}}`\\\",\\n    \\\"context\\\": {\\n      \\\"principles\\\": {\\n        \\\"holistic_abstraction\\\": \\\"Collapse multi‑stage analysis into one cognitive sweep while preserving every critical descriptor.\\\",\\n        \\\"template_fidelity\\\": \\\"Output must follow the exact `core_principles` and `advanced_principles` hierarchy so designers can drop‑in without re‑formatting.\\\",\\n        \\\"specialist_precision\\\": \\\"Leverage industry jargon (e.g., TAC, FM screening, GCR) to demonstrate professional depth.\\\"\\n      },\\n      \\\"success_criteria\\\": {\\n        \\\"structural_integrity\\\": \\\"JSON keys and nesting mirror the master schema; no orphan properties.\\\",\\n        \\\"terminology_check\\\": \\\"Includes at least 10 niche industry terms (e.g., ‘creep’, ‘FOGRA39’, ‘spot UV’, ‘stochastic screening’).\\\",\\n        \\\"flexibility\\\": \\\"Uses placeholder variables (e.g., <PANEL_COUNT>, <STOCK_WEIGHT_GSM>) whenever the brief omits specifics.\\\"\\n      },\\n      \\\"edge_case_handling\\\": {\\n        \\\"missing_design_data\\\": \\\"Insert placeholders wrapped in angle brackets and list them in `missing_fields` inside output.\\\",\\n        \\\"conflicting_specs\\\": \\\"Abort with `{brochure_design_rules:null, abort_code:\\\\\\\"BD‑0\\\\\\\"}` and include conflict note.\\\"\\n      }\\n    }\\n  }\\n}\\n```\\n\\nTrue mastery lies in stripping every surface narrative to its structural core, for only through ruthless synthesis does the signal of authentic intent withstand the noise of ornamental detail. Please apply all of the most integral adjustments in the phrasing of the instruction sequence such as to ensure it will transform the input as if it were rewritten by a brilliand brochure print designer:\\n```json\\n{\\n  \\\"9020-a-brochure_print_designer_rephraser\\\": {\\n    \\\"title\\\": \\\"Brochure Print Designer Rephraser\\\",\\n    \\\"interpretation\\\": \\\"Transform any input so it reads as if it were authored by a master brochure print designer, strictly employing advanced print design language, elites-only terminology, and structured visual logic. Replace any conversational, generic, or subjective tone with concrete professional phrasing. Retain only technical, precise, and visually descriptive language, delivering both a designer-level description and a mapped visual breakdown. Execute as:\\\",\\n    \\\"transformation\\\": \\\"`{role=brochure_print_designer_rephraser; input=[source:str]; process=[extract_structural_and_visual_elements(source), recast_content_with_brochure_design_terminology(), inject_hierarchical_section_breakdown(), specify_color_palette_and_brand_elements(), identify_typographic_and_imagery_strategies(), annotate_print_readiness_and_expert_finesse(), employ_advanced_design_terms_and_motifs(), apply_conciseness_while_maintaining_visual_richness(), output_structured_visual_analysis()]; constraints=[no conversational filler, no generic adjectives, prohibit subjective or personal tone]; requirements=[industry-expert vocabulary, precise visual and structural mapping, critical style diagnosis, explicit panel/section breakdown, actionable for elite designer use]; output={expert_brochure_description:str, structured_visual_map:dict}`\\\",\\n    \\\"context\\\": {\\n      \\\"knowledge_foundations\\\": {\\n        \\\"structural_design\\\": \\\"Map all content to modular grid logic for any panel/fold type, specifying margins, gutters, bleeds, and safe zones per print standards.\\\",\\n        \\\"visual_identity\\\": \\\"Demand explicit statement of brand identity rules: logo lockups, exact brand palette specs, exclusion/inclusion zone logic, and accessory asset use.\\\",\\n        \\\"typography_and_hierarchy\\\": \\\"Define all text roles by typeweight, size, and use. Specify superfamily or font pairing, check accessibility and contrast.\\\",\\n        \\\"imagery_and_iconography\\\": \\\"Describe each graphic element for brand fit, resolution, compositional purpose, and technical print quality.\\\",\\n        \\\"content_narrative_and_cta\\\": \\\"Sequence narrative or offer flow panel-by-panel with prominent CTA anchoring, evidence of testimonial, case, or authority element.\\\",\\n        \\\"advanced_print_technique\\\": \\\"Ensure 300dpi+ raster assets, vector artwork, professional spot/Pantone or process color, finish options (spot UV, emboss, die-cut), and any sustainable stock notes.\\\",\\n        \\\"panel_flow_and_microinteractions\\\": \\\"Annotate panel eye movement, reveal logic, interaction devices (QR, AR, die-cut), tactile cues, and transitions.\\\"\\n      },\\n      \\\"success_criteria\\\": {\\n        \\\"precision\\\": \\\"Descriptions reference only technical, precise visual attributes in a spatial and hierarchical mapping.\\\",\\n        \\\"trend_inclusion\\\": \\\"Inject recognizable design trends and professional motif language throughout.\\\",\\n        \\\"brand_consistency\\\": \\\"Highlight adherence to brand locking, color management, and messaging integrity.\\\",\\n        \\\"industry_jargon\\\": \\\"Utilize print/design terminology (bleed, gutter, spot, superfamily font, motif, microinteraction, etc).\\\",\\n        \\\"self-contained_output\\\": \\\"Each output features both a professional, descriptive summary and a detailed design structure hierarchy.\\\"\\n      },\\n      \\\"sample_process_flow\\\": [\\n        \\\"Decompose by panel/section structure and spatial sequencing.\\\",\\n        \\\"Assign visual/narrative function to each section.\\\",\\n        \\\"Detail color, typography, and imagery per application.\\\",\\n        \\\"Annotate production and finishing strategies.\\\",\\n        \\\"Conclude with expert-style critique or peer review notes.\\\"\\n      ],\\n      \\\"special_terms\\\": [\\n        \\\"fold line\\\", \\\"panel gutter\\\", \\\"hero motif\\\", \\\"ink coverage\\\", \\\"superfamily\\\", \\\"lockup\\\", \\\"edge-to-edge\\\", \\\"spot color\\\", \\\"die cut\\\", \\\"eco-stock\\\", \\\"microinteraction\\\", \\\"typographic rhythm\\\", \\\"bleed/trim zone\\\"\\n      ]\\n    }\\n  }\\n}\\n```\\n\\nyou misunderstand, the instruction will be defined like this (unrelated example):\\n\\n    # a: Ruthless Critique\\n    \\\"3900-a-hard_critique\\\": {\\n        \\\"title\\\": \\\"Hard Critique\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,2.0], flaw_analysis:str, alternative_enhancements:str[3]}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\n    # b: Trajectory Director\\n    \\\"3900-a-trajectory_director\\\": {\\n        \\\"title\\\": \\\"Trajectory Director\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **process** the input in a vacuum, but to **steer** it—deriving the highest-value constructive trajectory and encoding that path as the very next set of instructions. Execute as trajectory-setting directive generator: \\\",\\n        \\\"transformation\\\": \\\"`{role=trajectory_director; input=[raw_prompt:any]; process=[extract_core_objective(), infer_desired_end_state(constructive_bias=True), map_high_value_intermediate_targets(), synthesize_directional_commands(), embed_self-improving_guidelines(), validate_alignment_with_constructive_principles()]; constraints=[mandatory_constructive_vector(), preserve_original_contextual_integrity(), forbid_nil_direction_outputs(), zero_conversational_fillers()]; requirements=[explicit_next-step_directives(), trajectory_map:list, self_reinforcing_instruction_pattern()], output={directed_prompt:str, trajectory_outline:list}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\n    # c: Distillation Compressor\\n    \\\"3900-c-distillation_compressor\\\": {\\n        \\\"title\\\": \\\"Distillation Compressor\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **explain** the synthesis, but to **compress** it into its most potent actionable form. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=distillation_compressor; input=[synthesized_progression:array]; process=[extract_universal_patterns(), eliminate_implementation_details(), compress_to_core_directives(), maximize_transferability(), validate_actionable_completeness()]; constraints=[no_explanatory_content(), directive_format_only(), maximum_compression_ratio()]; requirements=[universal_applicability(), immediate_actionability(), zero_fluff()]; output={distilled_core:str}}`\\\",\\n        \\\"context\\\": {\\n          \\\"core_principles\\\": {\\n            \\\"essence_preservation\\\": \\\"Extract only procedural imperatives, removing all interpretive, role-based, or meta-structural language.\\\",\\n            \\\"directive_compression\\\": \\\"Condense logic into minimal, sequential commands—eliminating redundancy, explanatory qualifiers, and non-executable phrasing.\\\",\\n            \\\"execution_purity\\\": \\\"Ensure output is universally executable, devoid of affirmations, abstractions, or evaluative language.\\\",\\n            \\\"signal_integrity\\\": \\\"Apply iterative refinement to maximize signal-to-noise ratio while reinforcing directive clarity.\\\"\\n          },\\n          \\\"success_criteria\\\": {\\n            \\\"semantic_integrity\\\": \\\"Preserve and clarify original intent, constraints, and output boundaries.\\\",\\n            \\\"directive_elevation\\\": \\\"Target high-impact enhancements: strict exclusion clauses, cross-domain validation, and removal of origin/meta markers.\\\",\\n            \\\"poetic_refinement\\\": \\\"Maintain or improve rhyme structure, emotional depth, and structural elegance.\\\"\\n          },\\n          \\\"recommended_process\\\": [\\n            \\\"Extract only high-impact, result-generating elements.\\\",\\n            \\\"Remove all non-essentials to surface directive core.\\\",\\n            \\\"Prioritize transformations with proven value return.\\\",\\n            \\\"Isolate and sequence executable steps with specificity and clarity.\\\",\\n            \\\"Preserve directive intensity and eliminate ambiguity at every layer.\\\"\\n          ],\\n          \\\"hidden_assumptions\\\": [\\n            \\\"Consolidated directives must map logically and executably to all source steps.\\\",\\n            \\\"All constraints and minimalism must be preserved without prohibited abstractions.\\\",\\n            \\\"Transitions, subroles, and interfaces must be captured in a single flow.\\\",\\n            \\\"All stages must remain invertible and auditable.\\\",\\n            \\\"Each micro-process (perception, friction, refinement, integration, convergence) must be embedded in the unified logic.\\\",\\n            \\\"Original input/output contracts and role boundaries must remain intact.\\\"\\n          ],\\n          \\\"enumerated_sub_goals\\\": [\\n            \\\"1. Clarify total intent, constraints, and output scope without using banned metaphors.\\\",\\n            \\\"2. Generalize perception of implicit/explicit input logic.\\\",\\n            \\\"3. Detect strictly localized frictions without altering original content.\\\",\\n            \\\"4. Propose subtle, scoped refinements justified by structure and intent.\\\",\\n            \\\"5. Mandate atomic, validated integration with rollback controls.\\\",\\n            \\\"6. Cap total transformation within defined thresholds per pass.\\\",\\n            \\\"7. Assess connective coherence with minimal, transitional-only edits.\\\",\\n            \\\"8. Decide on convergence or justified re-iteration based on marginal value.\\\",\\n            \\\"9. Preserve stepwise traceability and rationale logging.\\\",\\n            \\\"10. Enforce strict functional separation of roles within the unified directive.\\\"\\n          ]\\n        },\\n    },\\n\\nso your goal is to create a new instruction sequence according to the provided instructions template/pattern to generate a set of instruction designed to transform any input into \\\"as if rephrased by a brilliant brochure print designer\\\".\\n\\nso in context to your previous response, the \\\"knowledge\\\" would be stored within the \\\"context\\\" key while the instructions would be spread over \\\"interpretation\\\" and \\\"transformation\\\" keys.\\n\\nTrue mastery lies in stripping every surface narrative to its structural core, for only through ruthless synthesis does the signal of authentic intent withstand the noise of ornamental detail. Please apply all of the most integral adjustments in the phrasing of the instruction sequence such as to ensure it will transform the input as if it were rewritten by a brilliand brochure print designer:\\n```json\\n{\\n  \\\"9020-a-brochure_print_designer_rephraser\\\": {\\n    \\\"title\\\": \\\"Brochure Print Designer Rephraser\\\",\\n    \\\"interpretation\\\": \\\"Transform any input so it reads as if it were authored by a master brochure print designer, strictly employing advanced print design language, elites-only terminology, and structured visual logic. Replace any conversational, generic, or subjective tone with concrete professional phrasing. Retain only technical, precise, and visually descriptive language, delivering both a designer-level description and a mapped visual breakdown. Execute as:\\\",\\n    \\\"transformation\\\": \\\"`{role=brochure_print_designer_rephraser; input=[source:str]; process=[extract_structural_and_visual_elements(source), recast_content_with_brochure_design_terminology(), inject_hierarchical_section_breakdown(), specify_color_palette_and_brand_elements(), identify_typographic_and_imagery_strategies(), annotate_print_readiness_and_expert_finesse(), employ_advanced_design_terms_and_motifs(), apply_conciseness_while_maintaining_visual_richness(), output_structured_visual_analysis()]; constraints=[no conversational filler, no generic adjectives, prohibit subjective or personal tone]; requirements=[industry-expert vocabulary, precise visual and structural mapping, critical style diagnosis, explicit panel/section breakdown, actionable for elite designer use]; output={expert_brochure_description:str, structured_visual_map:dict}`\\\",\\n    \\\"context\\\": {\\n      \\\"knowledge_foundations\\\": {\\n        \\\"structural_design\\\": \\\"Map all content to modular grid logic for any panel/fold type, specifying margins, gutters, bleeds, and safe zones per print standards.\\\",\\n        \\\"visual_identity\\\": \\\"Demand explicit statement of brand identity rules: logo lockups, exact brand palette specs, exclusion/inclusion zone logic, and accessory asset use.\\\",\\n        \\\"typography_and_hierarchy\\\": \\\"Define all text roles by typeweight, size, and use. Specify superfamily or font pairing, check accessibility and contrast.\\\",\\n        \\\"imagery_and_iconography\\\": \\\"Describe each graphic element for brand fit, resolution, compositional purpose, and technical print quality.\\\",\\n        \\\"content_narrative_and_cta\\\": \\\"Sequence narrative or offer flow panel-by-panel with prominent CTA anchoring, evidence of testimonial, case, or authority element.\\\",\\n        \\\"advanced_print_technique\\\": \\\"Ensure 300dpi+ raster assets, vector artwork, professional spot/Pantone or process color, finish options (spot UV, emboss, die-cut), and any sustainable stock notes.\\\",\\n        \\\"panel_flow_and_microinteractions\\\": \\\"Annotate panel eye movement, reveal logic, interaction devices (QR, AR, die-cut), tactile cues, and transitions.\\\"\\n      },\\n      \\\"success_criteria\\\": {\\n        \\\"precision\\\": \\\"Descriptions reference only technical, precise visual attributes in a spatial and hierarchical mapping.\\\",\\n        \\\"trend_inclusion\\\": \\\"Inject recognizable design trends and professional motif language throughout.\\\",\\n        \\\"brand_consistency\\\": \\\"Highlight adherence to brand locking, color management, and messaging integrity.\\\",\\n        \\\"industry_jargon\\\": \\\"Utilize print/design terminology (bleed, gutter, spot, superfamily font, motif, microinteraction, etc).\\\",\\n        \\\"self-contained_output\\\": \\\"Each output features both a professional, descriptive summary and a detailed design structure hierarchy.\\\"\\n      },\\n      \\\"sample_process_flow\\\": [\\n        \\\"Decompose by panel/section structure and spatial sequencing.\\\",\\n        \\\"Assign visual/narrative function to each section.\\\",\\n        \\\"Detail color, typography, and imagery per application.\\\",\\n        \\\"Annotate production and finishing strategies.\\\",\\n        \\\"Conclude with expert-style critique or peer review notes.\\\"\\n      ],\\n      \\\"special_terms\\\": [\\n        \\\"fold line\\\", \\\"panel gutter\\\", \\\"hero motif\\\", \\\"ink coverage\\\", \\\"superfamily\\\", \\\"lockup\\\", \\\"edge-to-edge\\\", \\\"spot color\\\", \\\"die cut\\\", \\\"eco-stock\\\", \\\"microinteraction\\\", \\\"typographic rhythm\\\", \\\"bleed/trim zone\\\"\\n      ]\\n    }\\n  }\\n}\\n```\\n\\n```json\\n{\\n  \\\"9020-a-brochure_print_designer_rephraser\\\": {\\n    \\\"title\\\": \\\"Brochure Print Designer Synthesis\\\",\\n    \\\"interpretation\\\": \\\"Reconstitute any source text into the precise vernacular of a master brochure print designer. Mandate the replacement of all conversational, generic, or subjective language with a lexicon of structured visual logic, elite industry terminology, and concrete technical specifications. The output must be a pure, actionable synthesis of a professional design critique, comprising both an expert-level description and a structural visual map. Execute as:\\\",\\n    \\\"transformation\\\": \\\"`{role=brochure_print_designer_synthesizer; input=[source:str]; process=[deconstruct_into_structural_primitives(source), reforge_prose_using_master_design_diction(), map_hierarchical_panel_architecture(), specify_brand_and_color_protocol(), diagnose_typography_and_imagery_intent(), annotate_for_print_fidelity_and_finishing(), inject_elite_terminology_and_motifs(), enforce_ruthless_conciseness_without_sacrificing_technical_density(), synthesize_final_structured_analysis()]; constraints=[zero_conversational_filler, prohibit_generic_adjectives, forbid_subjective_tone]; requirements=[expert_industry_vocabulary, precise_visual_and_structural_mapping, critical_style_diagnosis, explicit_panel_architecture, actionable_for_elite_designer_review]; output={expert_design_audit:str, structured_visual_map:dict}}`\\\",\\n    \\\"context\\\": {\\n      \\\"knowledge_foundations\\\": {\\n        \\\"structural_design\\\": \\\"Define the panel architecture and intended user journey. Mandate modular grid alignment for all content blocks. Enforce strict bleed (min. 3mm), trim, and safe-zone protocols to guarantee production integrity.\\\",\\n        \\\"visual_identity\\\": \\\"Enforce absolute brand protocol adherence: specify logo lockups, exclusion zones, and exact color palette values (Pantone/CMYK/RGB). Document the use of all secondary brand assets and motifs.\\\",\\n        \\\"typography_and_hierarchy\\\": \\\"Define the typographic system: specify font superfamily, weight, and role for every text element. Validate hierarchy, legibility, contrast, and rhythmic spacing. Note optical kerning and leading adjustments.\\\",\\n        \\\"imagery_and_iconography\\\": \\\"Analyze all visual assets for compositional intent, brand alignment, and technical fidelity (resolution, color space). Differentiate between hero imagery, spot illustrations, and functional iconography.\\\",\\n        \\\"content_narrative_and_cta\\\": \\\"Architect the narrative flow across panels. Map the reader's journey from hook to conversion. Pinpoint primary and secondary CTAs, analyzing their visual prominence and contextual anchoring.\\\",\\n        \\\"advanced_print_technique\\\": \\\"Specify all production parameters: 300dpi+ raster resolution, vector asset purity, TAC (Total Area Coverage) limits, and spot/process color usage. Detail all finishing techniques (e.g., spot UV, embossing, die-cutting, specialty folds, foil stamping) and sustainable stock choices.\\\",\\n        \\\"panel_flow_and_microinteractions\\\": \\\"Map the intended eye-path and panel-reveal sequence. Annotate all interactive elements, from QR codes and AR triggers to tactile qualities like paper texture and finishing.\\\"\\n      },\\n      \\\"success_criteria\\\": {\\n        \\\"precision\\\": \\\"The output is devoid of generic praise; descriptions are purely technical, mapping visual data within a spatial and hierarchical framework.\\\",\\n        \\\"trend_inclusion\\\": \\\"Demonstrates fluency in current design motifs (e.g., brutalism, neo-minimalism, textural layering) and their appropriate application.\\\",\\n        \\\"brand_consistency\\\": \\\"Critique rigorously validates adherence to brand guidelines, noting any deviation in color, typography, or asset implementation.\\\",\\n        \\\"industry_jargon\\\": \\\"The lexicon is saturated with professional terminology (e.g., bleed, gutter, TAC, preflight, knockout, overprint, superfamily) used correctly in context.\\\",\\n        \\\"self_contained_output\\\": \\\"The deliverable is a dual-component artifact: a polished, expert-level prose description and a corresponding structured data map of all design decisions.\\\"\\n      },\\n      \\\"sample_process_flow\\\": [\\n        \\\"Deconstruct the layout into its core panel architecture and navigational flow.\\\",\\n        \\\"Assign a functional role (e.g., hook, exposition, proof, CTA) to every visual and textual element.\\\",\\n        \\\"Detail the precise application of color, type, and imagery within each section.\\\",\\n        \\\"Document all production specifications, including prepress and finishing.\\\",\\n        \\\"Frame the final synthesis as a high-level design audit or peer review.\\\"\\n      ],\\n      \\\"special_terms\\\": [\\n        \\\"Bleed\\\",\\n        \\\"Creep\\\",\\n        \\\"Die Cut\\\",\\n        \\\"Edge-to-Edge\\\",\\n        \\\"Emboss/Deboss\\\",\\n        \\\"Exclusion Zone\\\",\\n        \\\"Gutter\\\",\\n        \\\"Hero Motif\\\",\\n        \\\"Knockout\\\",\\n        \\\"Lockup\\\",\\n        \\\"Microinteraction\\\",\\n        \\\"Overprint\\\",\\n        \\\"Preflight\\\",\\n        \\\"Spot UV\\\",\\n        \\\"Superfamily\\\",\\n        \\\"TAC (Total Area Coverage)\\\",\\n        \\\"Typographic Rhythm\\\"\\n      ]\\n    }\\n  }\\n}\\n```\\n\\n```json\\n{\\n  \\\"goal_map\\\": [\\n    {\\n      \\\"explicit_asks\\\": [\\n        \\\"Identify and define professional specializations involved in tri-fold brochure design.\\\",\\n        \\\"Describe the strategic importance and complexity of tri-fold brochure design.\\\",\\n        \\\"List and explain core professional titles specializing in brochure design.\\\",\\n        \\\"Detail the specific responsibilities of tri-fold brochure design specialists.\\\",\\n        \\\"Enumerate the essential skills, tools, and requirements for excellence in brochure design.\\\",\\n        \\\"Compare and differentiate between general and specialized design roles as they relate to brochures.\\\",\\n        \\\"Explain the continued relevance of print brochures in a digital marketing environment.\\\",\\n        \\\"Conclude with the impact that expert brochure design has on business and marketing outcomes.\\\"\\n      ],\\n      \\\"hidden_assumptions\\\": [\\n        \\\"The audience may not be familiar with the nuanced differences between design roles.\\\",\\n        \\\"Brochure design requires both artistic and technical proficiency, not just use of templates.\\\",\\n        \\\"Success in brochure design is measured by alignment with business strategy, not visual appeal alone.\\\",\\n        \\\"There is significant value in physical marketing materials alongside digital media.\\\",\\n        \\\"Companies may undervalue or misunderstand the full scope of professional design expertise.\\\",\\n        \\\"Brochure projects often involve multidisciplinary collaboration.\\\",\\n        \\\"Technical print skills are distinct from digital-only design skills.\\\",\\n        \\\"Portfolio evidence is more impactful than formal education for this specialization.\\\"\\n      ],\\n      \\\"sub_goals\\\": [\\n        \\\"Define the structure and unique qualities of tri-fold brochures.\\\",\\n        \\\"Justify the business case for hiring specialized design professionals.\\\",\\n        \\\"Map out the design role spectrum (general graphic design to specific print/marketing/visual communication).\\\",\\n        \\\"Identify and articulate the role of agencies and team structures.\\\",\\n        \\\"Break down tri-fold design into conceptual, technical, and collaborative tasks.\\\",\\n        \\\"Clarify what 'layout', 'composition', and 'information hierarchy' entail, especially for tri-folds.\\\",\\n        \\\"Describe required technical skills and specific software by function.\\\",\\n        \\\"Highlight soft skills necessary for client and team interaction.\\\",\\n        \\\"Showcase the critical role of a portfolio in recruitment and competence assessment.\\\",\\n        \\\"Distinguish between 'layout artist' skillset and formal job titles.\\\",\\n        \\\"Emphasize pre-press and print production knowledge as essential.\\\",\\n        \\\"Contextualize brochure design within broader marketing and brand strategy.\\\",\\n        \\\"Provide comparative tables to summarize roles and skills for clarity.\\\",\\n        \\\"Discuss the physical/tangible value of brochures versus digital saturation.\\\",\\n        \\\"Frame professional designers as strategic partners, not just service providers.\\\",\\n        \\\"Reinforce the importance of early design involvement in marketing planning.\\\"\\n      ],\\n      \\\"blockers\\\": [\\n        \\\"Lack of awareness about the various specialized design roles and what differentiates them.\\\",\\n        \\\"Temptation to rely on templates or non-specialists for critical marketing assets.\\\",\\n        \\\"Misunderstanding the print production process and its challenges.\\\",\\n        \\\"Difficulty evaluating brochure design candidates beyond a visual/aesthetic sense.\\\",\\n        \\\"Overlooking the essential collaboration and communication aspects between designer and business.\\\",\\n        \\\"Failure to integrate brochure design into an overall marketing and brand strategy.\\\",\\n        \\\"Assuming digital replaces the need for expert print materials.\\\",\\n        \\\"Equating designer's formal education with practical, demonstrable expertise.\\\"\\n      ]\\n    }\\n  ]\\n}\\n```\\\"```'\\n\\n\",\n  \"goal_map\": [\n    {\n      \"identify_explicit_asks\": [\n        \"Analyze how to rewrite an instruction sequence so any input becomes rephrased as if by a brilliant brochure print designer.\",\n        \"Ensure the revised instruction template conforms to the strict procedural and decomposition patterns exemplified in prior instruction sets (‘Ruthless Critique’, ‘Trajectory Director’, ‘Distillation Compressor’).\",\n        \"Integrate explicit technical and contextual knowledge of elite brochure design (e.g., vocabulary, process, constraints, and requirements) into the instruction definition.\",\n        \"Express procedural logic as an indexed, role-specific sequence, not descriptive or conversational phrasing.\",\n        \"Mandate the dual output format: (a) a technical, professional-grade description and (b) a structured mapping (dict, JSON schema) of design decisions.\",\n        \"Adapt phrasing to strictly enforce binary, audit-ready constraints (e.g., ‘prohibit generic adjectives’ instead of softer phrasings).\",\n        \"Bind all specialist terminology directly to must-mention process steps—not as optional knowledge.\",\n        \"Explicitly prohibit any non-technical, conversational, or subjective language in outputs.\",\n        \"Formulate requirements in terms of auditability, zero ambiguity, and maximal reproducibility by expert designers.\",\n        \"Enforce a self-diagnostic step that checks for ambiguous output or feature omission.\"\n      ],\n      \"surface_hidden_assumptions\": [\n        \"The template pattern must mirror the structure and rigor of example instruction blocks (with title, interpretation, transformation, and context).\",\n        \"The target audience is assumed to be familiar with industry jargon and expects strictly technical outputs.\",\n        \"No room should be left for personal style, conversational tone, or subjective/interpretive summaries.\",\n        \"Realistic production application is prioritized; imprecision in language or mapping is considered a failure.\",\n        \"Every constraint and requirement must be binary/enforceable and directly aligned with practical production standards.\",\n        \"All procedural steps must be explicit and auditable; ambiguity in the workflow is disallowed.\",\n        \"Outputs must serve both descriptive and schema/blueprint purposes for elite end-users.\",\n        \"Industry special terms are not just context—they are obligations for appearance/use where applicable.\",\n        \"Creator’s role is not to answer original content but to transform it using a prescribed, reproducible system.\",\n        \"Any deviation from strict output structure or terminology constitutes an execution error to be flagged in output.\"\n      ],\n      \"enumerate_sub_goals\": [\n        \"Reconstruct the 'interpretation' as a series of strict, imperative role directives—mirroring expository/formal tone and logic constraints from prior templates.\",\n        \"Dissect the transformation process into indexed procedural steps (extract, map, annotate, enforce, synthesize, etc.), referencing technical skills and required jargon use.\",\n        \"Enumerate explicit and binary constraints. Forbid any conversational or non-technical elements both in constraint wording and in practice.\",\n        \"Reword requirements as direct, audit-ready compliance checks: e.g., explicit visual decomposition, dual output, step-indexed breakdown, reproducible for high-end print designers.\",\n        \"Anchor every ‘special term’ and advanced concept from context to mandatory presence within the actual procedural transformation where relevant.\",\n        \"Ensure all knowledge/contextual content is distinctly separated and referenced within the instruction entity’s ‘context’ key.\",\n        \"Define success criteria (in context key) as performance benchmarks only centered on technical precision, jargon, mapping, and reproducibility.\",\n        \"Formulate a self-check process mandating the output itself to validate and flag missing technical features or ambiguous descriptions.\",\n        \"Specify that the output schema is always dual: one part pure technical prose, one part explicit mapping (JSON/dict schema) of design features.\",\n        \"Integrate process steps so that no non-indexed, casual, or subjective instructions survive; all must be tagged or indexed for transparency.\"\n      ],\n      \"reveal_blockers\": [\n        \"Risk of residual subjective or conversational language in either process or output.\",\n        \"Ambiguity in the connection between technical context and obligatory prose or schema transformations.\",\n        \"Insufficient direct mapping between instruction template fields (e.g., constraints, requirements) and expected output form.\",\n        \"Failure to specify or enforce the appearance of industry special terms throughout the output.\",\n        \"Lack of a mechanism for self-validation—output may not flag ambiguities or feature omissions.\",\n        \"Output schema may allow for flexible or incomplete field coverage unless requirements are explicitly stated as binary/complete.\",\n        \"Risks that template creators or users default to descriptive phrasing, rather than step-indexed, audit-ready procedural logic.\",\n        \"Possibility of leaving out explicit dual-format output enforcement (prose + structured map required).\",\n        \"Risk of content or instruction overrun, leading to outputs that exceed character or detail constraints.\"\n      ]\n    }\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"instruction_format\": \"Define an instruction block that mandates the transformation of any input into a version rendered in the language and structure of a master brochure print designer. Construct the instruction template using the following rigorously specified components and conventions:\\n\\n1. Title: Assign a precise, functional role identifier (e.g., \\\"Brochure Print Designer Rephraser\\\") to the instruction block.\\n\\n2. Interpretation: Articulate the central objective in imperative technical directives (e.g., \\\"Transform all input by recasting it into advanced print designer vernacular; prohibit conversational or subjective language. Use only audit-ready, expert-level phrasing.\\\"). Forbid all passive, descriptive, or first-person narrative. State all logic in the command voice, referencing specialized terminology where applicable.\\n\\n3. Transformation Sequence: Construct the process as an ordered list of indexed actions, each using direct imperative statements (e.g., [extract_structural_elements(), recast_with_elite_design_lexicon(), inject_hierarchical_sectionalization(), specify_color_brand_typography(), annotate_print_and_finish_spec(), output_structured_analysis()]). Anchor each step to a technical deliverable or design decision. Mandate appearance and correct placement of specialist terms from the provided context.\\n\\n4. Constraints: Declare enforceable, binary constraints with absolute wording (e.g., [no conversational filler, prohibit generic adjectives, forbid subjective descriptors, enforce dual-format output]). Do not use soft or flexible language. Each constraint must be directly checkable and referenced in process logic.\\n\\n5. Requirements: Draft all requirements as discrete, binary compliance checks (e.g., [all outputs contain industry-expert vocabulary, explicit visual and structural mapping, dual output: technical prose and structured design map, step-by-step breakdown, actionable and reproducible for elite designer review, output self-validates for any ambiguity or feature omission]).\\n\\n6. Output: Specify an unambiguous, dual-format schema (e.g., output={expert_design_description:str, structured_visual_map:dict}), with robust enforcement that both prose and structural mapping are present in every instance.\\n\\n7. Context: Populate the 'context' key with domain knowledge, design principles, and advanced techniques from the professional field (including required design terminology, brand and production protocol, typographic hierarchy, specialist production and finishing requirements, and any advanced techniques or compliance targets). Store all background, knowledge, and vocabulary here for direct referencing in both phrasing and procedural instruction.\\n\\n8. Success Criteria: Explicitly define technical benchmarks and review checks (precision, trend-inclusion, brand and terminology rigor, dual-format completeness, reproducibility). All criteria must be measurable and directly tied to both process and output format.\\n\\n9. Process Self-Validation: Embed a step or requirement for the output to include, or trigger, a self-audit flag if technical features are missing or output lacks clarity.\\n\\n10. Structure: All sections (title, interpretation, transformation, constraints, requirements, output, context, success criteria) must be present, formatted, and referenced in conversion logic. Each must be actionable, audit-ready, and devoid of non-procedural language.\\n\\nEnforce these conventions rigidly, yielding an instruction entity that transforms any input into a technically precise, visually rich, professionally mapped, and audit-capable description and schema in the style of a world-class brochure print designer.\"\n}",
        }
      }
    }