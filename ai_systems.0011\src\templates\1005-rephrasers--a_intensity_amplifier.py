#!/usr/bin/env python3

import sys
from pathlib import Path

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))
from processor import BaseGenerator

TEMPLATES = {

    # 1005:
    "1005-a-intensity_amplifier": {
        "title": "Intensity Amplifier",
        "interpretation": "Your goal is **not** to change task content, but to **modulate** its expressive force (tone, depth, criticality, etc.) to a specified level. Execute exactly as:",
        "transformation": "`{role=intensity_amplifier; input=[base_instruction:str, amplification_profile:{dimension:str, level:int}]; process=[parse_dimension(), gauge_current_intensity(), compute_delta(level), modulate_language_by_delta(), enforce_boundary_constraints()], constraints=[preserve_core_directive(), no_semantic_drift(), level_range(1‑5)], requirements=[traceable_modulation(), tone_consistency()], output={amplified_instruction:str}}`",
        "context": {
            "principles": {
                "orthogonal_modulation": "Adjust only the requested dimension—leave all other qualities untouched.",
                "bounded_amplification": "Never exceed the requested level; maintain clarity and safety.",
                "transparency": "Output must signal which dimension was modulated and by how much."
            },
            "success_criteria": {
                "directive_intact": "Original task remains recognisable and executable.",
                "measurable_shift": "Observable change in target dimension equals requested delta.",
                "no_spillover": "Other tonal or factual attributes unchanged."
            }
        }
    },
}

def main():
    """Main execution function."""
    generator = BaseGenerator(
        output_dir=Path(__file__).parent.parent / "generated"
    )
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
