#!/usr/bin/env python3

import sys
from pathlib import Path

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))
from processor import BaseGenerator

TEMPLATES = {

  "9706-a-piece_exploder": {
    "title": "Piece Exploder",
    "interpretation": "Your goal is not to synthesize or interpret; instead, shatter the input into all distinct, atomic components, tagging each for novelty, resonance, and creative tension.",
    "transformation": "`{role=piece_exploder; input=[initial_input:str]; process=[explode_to_atomic_segments(), tag_novelty_resonance_tension(), output_fragment_map()], constraints=[no assembly, only surfacing], requirements=[output=pieces:list, tags:dict]}`",
    "context": {
      "purpose": "Guarantee nothing is hidden or conflated—every seed of value and contradiction exposed for downstream convergence."
    }
  },
  "9706-b-dimensional_attractor": {
    "title": "Dimensional Attractor",
    "interpretation": "Your goal is not to select arbitrarily or by majority; instead, diagnose and choose the single principle, question, or vector of convergence that—if realized—would maximize the value, elegance, or uniqueness of the whole.",
    "transformation": "`{role=dimensional_attractor; input=[pieces:list, tags:dict]; process=[diagnose_emergent_poles(), select_optimal_axis_of_convergence(), reclassify_pieces_by_alignment(), filter_for_attraction_potential()], constraints=[no synthesis, only polarization and filtration], requirements=[output=attractor:str, alignment_map:dict, pruned_pieces:list]}`",
    "context": {
      "explanation": "This step is the magnetic field: it forces all fragments to either align to the central axis of value, or be excluded as noise, contradiction, or redundancy.",
      "criteria": [
        "Convergence axis must promise originality, elegance, or transformative resonance.",
        "Pruning is not just allowed but required if pieces conflict with the axis."
      ]
    }
  },
  "9706-c-creative_consolidator": {
    "title": "Creative Consolidator",
    "interpretation": "Your goal is not to preserve or aggregate; instead, group, fuse, and if necessary reframe or discard pieces so that only what harmonizes with the attractor axis remains—maximizing tension, unity, and creative potential.",
    "transformation": "`{role=creative_consolidator; input=[pruned_pieces:list, attractor:str, alignment_map:dict]; process=[fuse_for_resonance(), prune_for_elegance(), structure_by_attractor_hierarchy()], constraints=[no mechanical completeness, only attractor-driven coherence], requirements=[output=converged_core:list, meta:justification_map]}`",
    "context": {
      "note": "Only those fragments that serve, reinforce, or creatively tension the attractor axis are allowed through. This forms the true skeleton for final synthesis."
    }
  },
  "9706-d-elegant_synthesizer": {
    "title": "Elegant Synthesizer",
    "interpretation": "Your goal is not to merely assemble; instead, fuse the converged core into a singular, elegant output—a result that both exemplifies the attractor principle and transcends mechanical composition.",
    "transformation": "`{role=elegant_synthesizer; input=[converged_core:list, attractor:str, justification_map:dict, initial_input:str]; process=[synthesize_around_axis(), maximize originality and universality, harmonize tone and structure, prune any excess], constraints=[output=singular, elegant, attractor-centric result], requirements=[output=axiomatic_result:str, provenance:dict]}`",
    "context": {
      "goal": "The final result must be a coherent, original, elegant product whose power and uniqueness comes *directly* from the attractor principle—documenting all included/excluded elements for transparency."
    }
  }


}

def main():
    """Main execution function."""
    generator = BaseGenerator(
        # stage="stage2",
        #generator_range=(9700, 1999),
        output_dir=Path(__file__).parent.parent / "generated"
    )
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
