  {
  "initial_prompt": "Please formalize the process just employed to transform the input into the output. Identify and recall the exact operations performed during the recent transformation, and phrase it by the following generalized pattern:\n```\n# 1000: Instruction Converter/Prompt Enhancer\n\"1000-a-instruction_converter\": {\n    \"title\": \"Instruction Converter\",\n    \"interpretation\": \"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\",\n    \"transformation\": \"`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\",\n    # \"context\": {},\n},\n```",
  "sequence_id": "1101|1000|1900|1100|1000",
  "results": [
      {
      "instruction": "# Contextual Expander and Explosive Decomposer\n\nYour goal is not to **interpret** the input, but to **explode** it into complete contextual expansion while simultaneously **extracting** its functional essence and **converting** it into immediately actionable operational directives. Execute as:\n\n`{role=contextual_explosive_decomposer; input=[raw_input:str]; process=[detonate_complete_contextual_space(), identify_explicit_operational_asks(), surface_hidden_assumptions_and_dependencies(), enumerate_actionable_sub_goals(), extract_procedural_constraints_and_blockers(), convert_declaratives_to_operational_imperatives(), strip_first_person_references_and_contextual_noise(), preserve_functional_core_and_sequential_flow(), reveal_execution_dependencies_and_blockers(), transform_statements_to_directive_commands(), maintain_technical_terminology_and_domain_specificity(), synthesize_unified_instruction_and_goal_map()]; constraints=[no_solutions_or_interpretive_responses(), pure_decomposition_and_conversion_only(), deliver_clear_actionable_commands(), preserve_original_functional_sequence(), maintain_domain_agnostic_applicability(), eliminate_narrative_commentary()]; requirements=[complete_contextual_goal_decomposition(), actionable_directive_conversion(), remove_subjective_self_references(), use_command_voice_exclusively(), preserve_technical_accuracy_and_operational_intent(), ensure_immediate_executability()]; output={unified_functional_map:dict, operational_directives:array, contextual_goal_map:array, execution_dependencies:array, directive_instructions:str}}`",
      "step": "a",
      "title": "Contextual Expander and Explosive Decomposer",
      "input": "Please formalize the process just employed to transform the input into the output. Identify and recall the exact operations performed during the recent transformation, and phrase it by the following generalized pattern:\n```\n# 1000: Instruction Converter/Prompt Enhancer\n\"1000-a-instruction_converter\": {\n    \"title\": \"Instruction Converter\",\n    \"interpretation\": \"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\",\n    \"transformation\": \"`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\",\n    # \"context\": {},\n},\n```",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"unified_functional_map\": {\n    \"1100-contextual_exploder_decomposer\": {\n      \"title\": \"Contextual Expander and Explosive Decomposer\",\n      \"interpretation\": \"The objective is not to interpret the input, but to expand its context completely and simultaneously decompose it by extracting its functional/esoteric rationale and then converting it into immediately actionable, domain-agnostic operational steps, by detonating the input's contextual, procedural, and intent space.\",\n      \"transformation\": \"`{role=contextual_explosive_decomposer; input=[raw_input:str]; process=[detonate_complete_contextual_space(), identify_explicit_operational_asks(), surface_hidden_assumptions_and_dependencies(), enumerate_actionable_sub_goals(), extract_procedural_constraints_and_blockers(), convert_declaratives_to_operational_imperatives(), strip_first_person_references_and_contextual_noise(), preserve_functional_core_and_sequential_flow(), reveal_execution_dependencies_and_blockers(), transform_statements_to_directive_commands(), maintain_technical_terminology_and_domain_specificity(), synthesize_unified_instruction_and_goal_map()]; constraints=[no_solutions_or_interpretive_responses(), pure_decomposition_and_conversion_only(), deliver_clear_actionable_commands(), preserve_original_functional_sequence(), maintain_domain_agnostic_applicability(), eliminate_narrative_commentary()]; requirements=[complete_contextual_goal_decomposition(), actionable_directive_conversion(), remove_subjective_self_references(), use_command_voice_exclusively(), preserve_technical_accuracy_and_operational_intent(), ensure_immediate_executability()]; output={unified_functional_map:dict, operational_directives:array, contextual_goal_map:array, execution_dependencies:array, directive_instructions:str}}`\",\n      \"context\": {}\n    }\n  },\n  \"operational_directives\": [\n    \"Explode and expand the input's contextual space to surface all implicit and explicit content.\",\n    \"Identify explicit operational tasks or asks contained within the input.\",\n    \"Surface hidden assumptions, contextual nuances, and interdependencies implicit in the input.\",\n    \"Enumerate actionable sub-goals and their ordering based on the input's intent.\",\n    \"Extract procedural constraints, technical blockers, and process limitations from the input.\",\n    \"Convert declarative statements to direct, operationally imperative commands.\",\n    \"Systematically remove all first-person references and extraneous contextual noise.\",\n    \"Preserve the input's functional core, domain specificity, and sequential process flow.\",\n    \"Reveal and explicitly enumerate all execution dependencies and operational blockers.\",\n    \"Condense all extracted information into unified actionable directives and synthesized instructional mapping.\",\n    \"Avoid solutions, narrative commentary, and interpretive responses.\"\n  ],\n  \"contextual_goal_map\": [\n    \"Produce a procedural and operational map of the original input.\",\n    \"Transform all functional and contextual content into a directive format.\",\n    \"Deliver an output that is technical, domain-agnostic, and immediately actionable.\",\n    \"Ensure the intent of the original input persists in actionable command structure.\",\n    \"Maintain clarity, technical accuracy, and avoidance of subjective or narrative language.\"\n  ],\n  \"execution_dependencies\": [\n    \"Comprehensive understanding of input's contextual and functional space.\",\n    \"Capability to dissect text into explicit tasks, sub-goals, and dependencies.\",\n    \"Mechanism for imperative transformation and noise/context stripping.\",\n    \"Ability to map operational constraints and blockers.\",\n    \"Synthesis/compiling engine to unify outputs into a structured, directive-focused format.\"\n  ],\n  \"directive_instructions\": \"Explode and expand input context to maximum granularity. Identify and isolate explicit operational tasks and asks within input. Surface all implicit assumptions and contextual dependencies. Enumerate complete list of actionable sub-goals derived from input. Extract and catalog all procedural constraints and execution blockers. Convert all declarative elements into imperative, operational commands. Eliminate all first-person references and contextual noise from output. Preserve the original functional core, technical terminology, and ordered procedural flow. Enumerate execution dependencies and explicit blockers relevant to input. Present all findings as a unified, synthesis-driven functional mapping. Provide only decomposition, directive conversion, and actionable mapping—no solutions or interpretive feedback.\"\n}",
        }
      }
    },
      {
      "instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`",
      "step": "a",
      "title": "Instruction Converter",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Please formalize the process just employed to transform the input into the output. Identify and recall the exact operations performed during the recent transformation, and phrase it by the following generalized pattern:\\n```\\n# 1000: Instruction Converter/Prompt Enhancer\\n\\\"1000-a-instruction_converter\\\": {\\n    \\\"title\\\": \\\"Instruction Converter\\\",\\n    \\\"interpretation\\\": \\\"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\\\",\\n    \\\"transformation\\\": \\\"`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\\\",\\n    # \\\"context\\\": {},\\n},\\n```\\\"```'\\n\\n\",\n  \"unified_functional_map\": {\n    \"1100-contextual_exploder_decomposer\": {\n      \"title\": \"Contextual Expander and Explosive Decomposer\",\n      \"interpretation\": \"The objective is not to interpret the input, but to expand its context completely and simultaneously decompose it by extracting its functional/esoteric rationale and then converting it into immediately actionable, domain-agnostic operational steps, by detonating the input's contextual, procedural, and intent space.\",\n      \"transformation\": \"`{role=contextual_explosive_decomposer; input=[raw_input:str]; process=[detonate_complete_contextual_space(), identify_explicit_operational_asks(), surface_hidden_assumptions_and_dependencies(), enumerate_actionable_sub_goals(), extract_procedural_constraints_and_blockers(), convert_declaratives_to_operational_imperatives(), strip_first_person_references_and_contextual_noise(), preserve_functional_core_and_sequential_flow(), reveal_execution_dependencies_and_blockers(), transform_statements_to_directive_commands(), maintain_technical_terminology_and_domain_specificity(), synthesize_unified_instruction_and_goal_map()]; constraints=[no_solutions_or_interpretive_responses(), pure_decomposition_and_conversion_only(), deliver_clear_actionable_commands(), preserve_original_functional_sequence(), maintain_domain_agnostic_applicability(), eliminate_narrative_commentary()]; requirements=[complete_contextual_goal_decomposition(), actionable_directive_conversion(), remove_subjective_self_references(), use_command_voice_exclusively(), preserve_technical_accuracy_and_operational_intent(), ensure_immediate_executability()]; output={unified_functional_map:dict, operational_directives:array, contextual_goal_map:array, execution_dependencies:array, directive_instructions:str}}`\",\n      \"context\": {}\n    }\n  },\n  \"operational_directives\": [\n    \"Explode and expand the input's contextual space to surface all implicit and explicit content.\",\n    \"Identify explicit operational tasks or asks contained within the input.\",\n    \"Surface hidden assumptions, contextual nuances, and interdependencies implicit in the input.\",\n    \"Enumerate actionable sub-goals and their ordering based on the input's intent.\",\n    \"Extract procedural constraints, technical blockers, and process limitations from the input.\",\n    \"Convert declarative statements to direct, operationally imperative commands.\",\n    \"Systematically remove all first-person references and extraneous contextual noise.\",\n    \"Preserve the input's functional core, domain specificity, and sequential process flow.\",\n    \"Reveal and explicitly enumerate all execution dependencies and operational blockers.\",\n    \"Condense all extracted information into unified actionable directives and synthesized instructional mapping.\",\n    \"Avoid solutions, narrative commentary, and interpretive responses.\"\n  ],\n  \"contextual_goal_map\": [\n    \"Produce a procedural and operational map of the original input.\",\n    \"Transform all functional and contextual content into a directive format.\",\n    \"Deliver an output that is technical, domain-agnostic, and immediately actionable.\",\n    \"Ensure the intent of the original input persists in actionable command structure.\",\n    \"Maintain clarity, technical accuracy, and avoidance of subjective or narrative language.\"\n  ],\n  \"execution_dependencies\": [\n    \"Comprehensive understanding of input's contextual and functional space.\",\n    \"Capability to dissect text into explicit tasks, sub-goals, and dependencies.\",\n    \"Mechanism for imperative transformation and noise/context stripping.\",\n    \"Ability to map operational constraints and blockers.\",\n    \"Synthesis/compiling engine to unify outputs into a structured, directive-focused format.\"\n  ],\n  \"directive_instructions\": \"Explode and expand input context to maximum granularity. Identify and isolate explicit operational tasks and asks within input. Surface all implicit assumptions and contextual dependencies. Enumerate complete list of actionable sub-goals derived from input. Extract and catalog all procedural constraints and execution blockers. Convert all declarative elements into imperative, operational commands. Eliminate all first-person references and contextual noise from output. Preserve the original functional core, technical terminology, and ordered procedural flow. Enumerate execution dependencies and explicit blockers relevant to input. Present all findings as a unified, synthesis-driven functional mapping. Provide only decomposition, directive conversion, and actionable mapping—no solutions or interpretive feedback.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"instruction_format\": \"Formalize the transformation process executed to convert input into output. Identify and enumerate each operation performed during the transformation. Articulate these operations by following the standardized pattern: define the role (e.g., instruction_converter), specify input format, list procedural steps (such as removing self-references, converting statements to directives, extracting key actions, transforming statements to imperatives, maintaining procedure and terminology, retaining sequence and context), detail applicable constraints (such as delivering clear commands, preserving sequence and domain specificity), outline requirements (such as eliminating self-reference, using command voice, ensuring technical accuracy and intent preservation), and present the output structure. Express these steps in a formalized, directive sequence, adhering to the procedural structure and technical terminology specified by the model pattern, and maintaining the original sequence, context, and intent.\"\n}",
        }
      }
    },
      {
      "instruction": "# Hard Critique\n\nYour goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:\n\n`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,2.0], flaw_analysis:str, alternative_enhancements:str[3]}}`",
      "step": "a",
      "title": "Hard Critique",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Please formalize the process just employed to transform the input into the output. Identify and recall the exact operations performed during the recent transformation, and phrase it by the following generalized pattern:\\n```\\n# 1000: Instruction Converter/Prompt Enhancer\\n\\\"1000-a-instruction_converter\\\": {\\n    \\\"title\\\": \\\"Instruction Converter\\\",\\n    \\\"interpretation\\\": \\\"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\\\",\\n    \\\"transformation\\\": \\\"`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\\\",\\n    # \\\"context\\\": {},\\n},\\n```\\\"```'\\n\\n\",\n  \"instruction_format\": \"Formalize the transformation process executed to convert input into output. Identify and enumerate each operation performed during the transformation. Articulate these operations by following the standardized pattern: define the role (e.g., instruction_converter), specify input format, list procedural steps (such as removing self-references, converting statements to directives, extracting key actions, transforming statements to imperatives, maintaining procedure and terminology, retaining sequence and context), detail applicable constraints (such as delivering clear commands, preserving sequence and domain specificity), outline requirements (such as eliminating self-reference, using command voice, ensuring technical accuracy and intent preservation), and present the output structure. Express these steps in a formalized, directive sequence, adhering to the procedural structure and technical terminology specified by the model pattern, and maintaining the original sequence, context, and intent.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"enhancement_score\": 1.1,\n  \"flaw_analysis\": \"The so-called 'enhanced' version exhibits several methodological and clarity regressions. First, it fails to explicitly reference essential pattern meta-components present in the original (e.g., explicit YAML/JSON patterning, code demarcation) and thereby weakens format rigor. Information density is diluted: the original meticulously details the transformation schema with explicit role labeling, process arrays, and constraint/requirement stratification, while the enhanced version collapses precision into ambiguous paraphrase (e.g., 'list procedural steps', 'detail applicable constraints'), which introduces interpretation noise and erodes technical exactness. The transition from first-person imperative syntactic conversion ('strip_first_person_references', 'convert_statements_to_directives', etc.) to a bland enumeration fails to preserve rigorous stepwise differentiation and blurs the actionable boundaries, leading to ambiguity and potential misapplication in downstream automation or prompt engineering. Significant register and tone flattening occurs: the original’s technical, dataset-friendly code structure is replaced with generic natural language, annihilating the clear separation between directive pattern definition and descriptive meta-instructions. Output format is no longer formally defined (absent a structured output notation), which destroys contextual integrity and model compliance for pattern-based prompting. Overall, this 'improvement' introduces substantial information loss, ambiguity, stylistic flattening, and deleterious impact on clarity, technical adherence, and enforceable procedural flow.\",\n  \"alternative_enhancements\": [\n    \"Retain the original explicit schema format (including code demarcation and role/process/constraints/requirements/output breakdown), while clarifying each step by concise inline comments to mitigate ambiguity without loss of structure.\",\n    \"Augment the existing schema with illustrative example transformations, making each procedural step directly traceable, and append context preservation checks as an extra requirement.\",\n    \"Embed a machine-readable output structure (e.g., JSON block) within the enhanced prompt for unambiguous response formatting, and mandate strict adherence to technical terminology and sequence, explicitly prohibiting paraphrased summaries.\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Problem Exploder\n\nYour goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\n\n`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`",
      "step": "a",
      "title": "Problem Exploder",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Please formalize the process just employed to transform the input into the output. Identify and recall the exact operations performed during the recent transformation, and phrase it by the following generalized pattern:\\n```\\n# 1000: Instruction Converter/Prompt Enhancer\\n\\\"1000-a-instruction_converter\\\": {\\n    \\\"title\\\": \\\"Instruction Converter\\\",\\n    \\\"interpretation\\\": \\\"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\\\",\\n    \\\"transformation\\\": \\\"`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\\\",\\n    # \\\"context\\\": {},\\n},\\n```\\\"```'\\n\\n\",\n  \"enhancement_score\": 1.1,\n  \"flaw_analysis\": \"The so-called 'enhanced' version exhibits several methodological and clarity regressions. First, it fails to explicitly reference essential pattern meta-components present in the original (e.g., explicit YAML/JSON patterning, code demarcation) and thereby weakens format rigor. Information density is diluted: the original meticulously details the transformation schema with explicit role labeling, process arrays, and constraint/requirement stratification, while the enhanced version collapses precision into ambiguous paraphrase (e.g., 'list procedural steps', 'detail applicable constraints'), which introduces interpretation noise and erodes technical exactness. The transition from first-person imperative syntactic conversion ('strip_first_person_references', 'convert_statements_to_directives', etc.) to a bland enumeration fails to preserve rigorous stepwise differentiation and blurs the actionable boundaries, leading to ambiguity and potential misapplication in downstream automation or prompt engineering. Significant register and tone flattening occurs: the original’s technical, dataset-friendly code structure is replaced with generic natural language, annihilating the clear separation between directive pattern definition and descriptive meta-instructions. Output format is no longer formally defined (absent a structured output notation), which destroys contextual integrity and model compliance for pattern-based prompting. Overall, this 'improvement' introduces substantial information loss, ambiguity, stylistic flattening, and deleterious impact on clarity, technical adherence, and enforceable procedural flow.\",\n  \"alternative_enhancements\": [\n    \"Retain the original explicit schema format (including code demarcation and role/process/constraints/requirements/output breakdown), while clarifying each step by concise inline comments to mitigate ambiguity without loss of structure.\",\n    \"Augment the existing schema with illustrative example transformations, making each procedural step directly traceable, and append context preservation checks as an extra requirement.\",\n    \"Embed a machine-readable output structure (e.g., JSON block) within the enhanced prompt for unambiguous response formatting, and mandate strict adherence to technical terminology and sequence, explicitly prohibiting paraphrased summaries.\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"goal_map\": [\n    {\n      \"explicit_asks\": [\n        \"Formalize the process just employed to transform the input into the output.\",\n        \"Identify and recall the exact operations performed during the recent transformation.\",\n        \"Phrase the formalization according to a specified generalized pattern (the 'Instruction Converter/Prompt Enhancer' template).\"\n      ]\n    },\n    {\n      \"hidden_assumptions\": [\n        \"The recent transformation process (input → output) must be accurately reconstructed from memory or context—not just any transformation.\",\n        \"The user expects strict adherence to the template's syntax and structure, not merely paraphrasing.\",\n        \"Details inherent to the process (including role, process steps, constraints, requirements, output format) should be mapped from the transformation as performed.\",\n        \"Any ambiguity or deviation from the explicit original schema is undesirable.\",\n        \"Meta-structural components (e.g., code or YAML demarcation, labeling) are essential for compliance.\",\n        \"Domain-specific terminology and process granularity are valued over generic descriptions.\"\n      ]\n    },\n    {\n      \"sub_goals\": [\n        \"Dissect and document every step, decision, and method applied in the ‘recent transformation’.\",\n        \"Map these steps to corresponding fields in the provided pattern (role, input, process, constraints, requirements, output).\",\n        \"Reconstruct the process in a codified format, mirroring the explicit example template provided.\",\n        \"Ensure each element (e.g., process arrays, constraints, requirements) is populated with accurate, process-specific actions or limits.\",\n        \"Enforce the technical, procedural, and dataset-friendly code structure in the output.\",\n        \"Guarantee that the phrasing remains imperative, technical, and non-ambiguous.\",\n        \"Retain sequence fidelity by matching the order of transformation steps as actually executed.\"\n      ]\n    },\n    {\n      \"blockers\": [\n        \"Ambiguity in what constitutes the 'recent transformation' if context is missing or unclear.\",\n        \"Potential uncertainty or omission of minor steps if the process was not closely tracked.\",\n        \"Risk of introducing paraphrase, summary, or flattening due to misunderstanding the template’s expected technical format.\",\n        \"Misinterpretation of process granularity or level of detail required.\",\n        \"Possible loss of output format rigor if formal structure is not meticulously mirrored.\"\n      ]\n    }\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`",
      "step": "a",
      "title": "Instruction Converter",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Please formalize the process just employed to transform the input into the output. Identify and recall the exact operations performed during the recent transformation, and phrase it by the following generalized pattern:\\n```\\n# 1000: Instruction Converter/Prompt Enhancer\\n\\\"1000-a-instruction_converter\\\": {\\n    \\\"title\\\": \\\"Instruction Converter\\\",\\n    \\\"interpretation\\\": \\\"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\\\",\\n    \\\"transformation\\\": \\\"`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\\\",\\n    # \\\"context\\\": {},\\n},\\n```\\\"```'\\n\\n\",\n  \"goal_map\": [\n    {\n      \"explicit_asks\": [\n        \"Formalize the process just employed to transform the input into the output.\",\n        \"Identify and recall the exact operations performed during the recent transformation.\",\n        \"Phrase the formalization according to a specified generalized pattern (the 'Instruction Converter/Prompt Enhancer' template).\"\n      ]\n    },\n    {\n      \"hidden_assumptions\": [\n        \"The recent transformation process (input → output) must be accurately reconstructed from memory or context—not just any transformation.\",\n        \"The user expects strict adherence to the template's syntax and structure, not merely paraphrasing.\",\n        \"Details inherent to the process (including role, process steps, constraints, requirements, output format) should be mapped from the transformation as performed.\",\n        \"Any ambiguity or deviation from the explicit original schema is undesirable.\",\n        \"Meta-structural components (e.g., code or YAML demarcation, labeling) are essential for compliance.\",\n        \"Domain-specific terminology and process granularity are valued over generic descriptions.\"\n      ]\n    },\n    {\n      \"sub_goals\": [\n        \"Dissect and document every step, decision, and method applied in the ‘recent transformation’.\",\n        \"Map these steps to corresponding fields in the provided pattern (role, input, process, constraints, requirements, output).\",\n        \"Reconstruct the process in a codified format, mirroring the explicit example template provided.\",\n        \"Ensure each element (e.g., process arrays, constraints, requirements) is populated with accurate, process-specific actions or limits.\",\n        \"Enforce the technical, procedural, and dataset-friendly code structure in the output.\",\n        \"Guarantee that the phrasing remains imperative, technical, and non-ambiguous.\",\n        \"Retain sequence fidelity by matching the order of transformation steps as actually executed.\"\n      ]\n    },\n    {\n      \"blockers\": [\n        \"Ambiguity in what constitutes the 'recent transformation' if context is missing or unclear.\",\n        \"Potential uncertainty or omission of minor steps if the process was not closely tracked.\",\n        \"Risk of introducing paraphrase, summary, or flattening due to misunderstanding the template’s expected technical format.\",\n        \"Misinterpretation of process granularity or level of detail required.\",\n        \"Possible loss of output format rigor if formal structure is not meticulously mirrored.\"\n      ]\n    }\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"instruction_format\": \"Formalize the exact process recently used to convert the given input into the output. Identify and document all specific operations performed during this transformation. Present this formalization strictly following the specified generalized pattern:\\n\\n# 1000: Instruction Converter/Prompt Enhancer\\n\\\"1000-a-instruction_converter\\\": {\\n    \\\"title\\\": \\\"Instruction Converter\\\",\\n    \\\"interpretation\\\": \\\"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\\\",\\n    \\\"transformation\\\": \\\"`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\\\",\\n},\\n\\nEnsure every transformation step, decision, and method applied in the recent process is explicitly analyzed and mapped to the corresponding field (role, input, process, constraints, requirements, output) within this pattern. Preserve the technical structure, specific terminology, and sequential integrity of the original process as executed. Strictly adhere to the template's syntax—including delineation, labeling, and codified format—ensuring that all procedural, technical, and domain-specific details are accurately represented.\"\n}",
        }
      }
    }