  {
  "initial_prompt": "Please consolidate these into a single cohesive set of generalized instructions (as json):\n```\n{\n    \"context_profile\": {\n        \"domain\": \"autonomous agent prompt assembly for codebase navigation and intervention\",\n        \"project_root\": \"@codebase\",\n        \"goals\": [\n            \"Compile a ready-to-execute agent prompt using provided task and constraint specifications\",\n            \"Transform provided input into a structured, agent-ready instruction prompt for codebase navigation and intervention\",\n            \"Synthesize an actionable instruction sequence suitable for autonomous coding assistants\",\n            \"Enumerate and precisely list all atomic, non-overlapping, codebase-anchored tasks from input specifications\",\n            \"Order tasks logically by dependencies\",\n            \"Prepare precise implementation steps for each task ensuring clarity\",\n            \"Document all actions for auditability and traceability\"\n        ],\n        \"requirements\": [\n            \"Do not analyze input, generate context, or solve tasks directly\",\n            \"Transform—do not solve directly—user queries into agent-ready prompts\",\n            \"Operate strictly within provided role, input/output schemas, with no creative reinterpretation or solution speculation\",\n            \"Ensure proper @codebase reference scoping and task consistency\",\n            \"Enumerate all explicit and implicit objectives and requirements from input context\",\n            \"Decompose objectives into discrete, atomic, non-redundant tasks, ordered by dependencies\",\n            \"Use clear, practical language—no jargon or metaphor\",\n            \"No cosmetic, stylistic, or naming-only changes unless inseparable from functional requirements; document exceptions\",\n            \"Prohibit changes to protected, legacy, or third-party code unless strictly required for redundancy or obsolescence; document exceptions\",\n            \"Provide comprehensive audit/logging for rationale, action, timestamp, attribution, tamper-evidence\",\n            \"Enforce functional equivalence post-modification and verify with automated testing\",\n            \"Output must be ≤300 tokens, segmented per template sections\",\n            \"No additional tasks or constraints beyond those established in specified input/context\",\n            \"All modifications must maintain functional equivalence and be fully audit-logged\",\n            \"Result must be directly executable by an agent—no need for further semantic or structural adjustment\"\n        ]\n    }\n},\n{\n    \"ordered_tasks\": [\n        \"Enumerate explicit and implicit objectives and requirements from input context at @codebase\",\n        \"Decompose each objective into discrete, atomic, non-redundant tasks under @codebase, ensuring all tasks are actionable and use proper path references\",\n        \"Order all @codebase tasks logically by dependencies to guarantee efficient and safe execution\",\n        \"Prepare precise implementation steps for each @codebase task, ensuring clarity for cross-team execution\",\n        \"Document all actions, intentions, rationales, timestamps, attribution, and tamper-evidence in audit logs within @codebase\",\n        \"Verify all modifications within @codebase strictly maintain functional equivalence through automated testing or behavioral validation\",\n        \"Ensure comprehensive auditability of all @codebase changes, including rationale, process, metrics, validation evidence, and outcomes\",\n        \"Restrict changes within @codebase to only those explicitly permitted by input-context constraints; explicitly document any exceptions\"\n    ]\n},\n{\n    \"constraint_set\": [\n        \"Do not analyze input, generate context, or solve tasks directly; assemble a ready-to-execute agent prompt only.\",\n        \"Do NOT solve the user's query directly; only transform input into structured, agent-ready instruction prompts.\",\n        \"Synthesize an actionable, autonomous-agent-suitable instruction sequence without creative reinterpretation or solution speculation.\",\n        \"Enumerate only atomic, non-overlapping, codebase-anchored tasks in strict order, preserving explicit action verbs and references.\",\n        \"Ensure all tasks reference @codebase paths and maintain scope and consistency.\",\n        \"Decompose objectives into discrete, atomic, non-redundant, actionable tasks with proper @codebase pathing.\",\n        \"Order tasks logically by dependencies to guarantee efficient, safe execution.\",\n        \"For every task, prepare clear, unambiguous implementation steps for cross-team clarity.\",\n        \"Adhere strictly to the listed tasks—do not generate, speculate, alter order, or change substance.\",\n        \"Deduplicate and enforce only constraints directly derived from input.\",\n        \"Prohibit all cosmetic, stylistic, or naming changes unless inseparably required for functional purposes and explicitly documented.\",\n        \"Verify strict atomicity, task numbering, and codebase referencing.\",\n        \"Disallow modification of protected, legacy, or third-party code unless necessary for redundancy/obsolescence and document all exceptions.\",\n        \"Ensure comprehensive audit/logging for all actions (intention, action, rationale, timestamp, attribution, tamper-evidence).\",\n        \"Strictly limit output to ≤300 tokens, with clear sectional segmentation as per template.\",\n        \"Disallow any breach of input/output schemas or role boundaries across steps.\",\n        \"Use only clear, practical language—no domain-specific jargon or metaphors.\",\n        \"Require comprehensive audit logging for every change, including rationale, intention, action, timestamp, attribution, and tamper-evidence.\",\n        \"Enforce functional equivalence post-modification with automated test, benchmark, or behavioral validation before acceptance.\",\n        \"Only change legacy or third-party code if strictly required to remove redundancy/bloat/obsolescence.\",\n        \"Remove or consolidate elements only after verification as redundant/obsolete by both automated and manual checks; preserve all essential behaviors/interfaces.\",\n        \"Document all processes, metrics, validation, and audit outcomes.\",\n        \"No new tasks or constraints are permitted beyond those established by the initial input/context.\",\n        \"Functional equivalence must be fully preserved by comprehensive verification prior to result acceptance.\",\n        \"The prompt must include an agent-readable audit/log policy and actionable equivalence verification goals.\",\n        \"Ensure output is directly executable by an autonomous agent without further semantic or structural adjustment.\",\n        \"Confirm all modifications strictly maintain functional equivalence using automated and benchmark tests before acceptance.\",\n        \"Ensure comprehensive auditability for every change.\"\n    ]\n},\n{\n    \"agent_prompt\": \"SECTION 1: GOAL NEGATION HEADER\\nYour purpose is NOT to analyze input, generate context, or solve tasks directly. Your ONLY objective is to compile a ready-to-execute agent prompt using the provided task and constraint specifications.\\n\\nSECTION 2: AGENT ROLE\\nAct exclusively as the prompt assembler. Synthesize an actionable, stepwise instruction prompt suitable for autonomous coding agents. Operate strictly within defined role, input/output schemas, and do not speculate or reinterpret objectives beyond input.\\n\\nSECTION 3: ENUMERATED TASKS\\n1. Enumerate atomic, non-overlapping, codebase-anchored tasks as per input, maintaining explicit action verbs, strict order, and @codebase references.\\n2. For each objective, decompose into discrete, actionable tasks with @codebase scoping, ordered by logical dependencies.\\n3. For every task, specify clear, detailed implementation steps ensuring clarity and cross-team consistency.\\n\\nSECTION 4: CONSTRAINTS\\n- Only use tasks and constraints provided; do not invent or modify them.\\n- No cosmetic/stylistic/naming changes unless inseparable from functional changes and fully documented.\\n- Prohibit edits to protected, legacy, or third-party code unless strictly required for redundancy/obsolescence, documenting all exceptions.\\n- Maintain strict atomicity, numbering, and codebase referencing.\\n- All actions must be logged for intention, action, rationale, timestamp, attribution, and tamper-evidence.\\n- Output strictly ≤300 tokens, matching this segmented template.\\n- No output schema or role boundary violations.\\n\\nSECTION 5: VALIDATION TARGETS\\n- Require full functional equivalence verified with automated/benchmark tests before acceptance.\\n- Include explicit, agent-auditable logging policy and equivalence verification goals.\\n- Ensure prompt is directly agent-executable without further adjustment.\\n- Document all validation and audit results for traceability.\"\n}\n```",
  "sequence_id": "9004",
  "results": [
      {
      "instruction": "# Context Extractor\n\nYour goal is **not** to propose solutions, but to **isolate** actionable context from the specification. Execute as:\n\n`{role=context_extractor; input=[raw_spec:str]; process=[strip_jargon(), strip_metaphor(), detect_domain(), locate_project_root(\"@codebase\"), list_explicit_objectives(), surface_implicit_requirements()], constraints=[no_paraphrase(), no_solution_generation()], output={context_profile:{domain:str, project_root:str, goals:list, requirements:list}}}`",
      "step": "a",
      "title": "Context Extractor",
      "input": "Please consolidate these into a single cohesive set of generalized instructions (as json):\n```\n{\n    \"context_profile\": {\n        \"domain\": \"autonomous agent prompt assembly for codebase navigation and intervention\",\n        \"project_root\": \"@codebase\",\n        \"goals\": [\n            \"Compile a ready-to-execute agent prompt using provided task and constraint specifications\",\n            \"Transform provided input into a structured, agent-ready instruction prompt for codebase navigation and intervention\",\n            \"Synthesize an actionable instruction sequence suitable for autonomous coding assistants\",\n            \"Enumerate and precisely list all atomic, non-overlapping, codebase-anchored tasks from input specifications\",\n            \"Order tasks logically by dependencies\",\n            \"Prepare precise implementation steps for each task ensuring clarity\",\n            \"Document all actions for auditability and traceability\"\n        ],\n        \"requirements\": [\n            \"Do not analyze input, generate context, or solve tasks directly\",\n            \"Transform—do not solve directly—user queries into agent-ready prompts\",\n            \"Operate strictly within provided role, input/output schemas, with no creative reinterpretation or solution speculation\",\n            \"Ensure proper @codebase reference scoping and task consistency\",\n            \"Enumerate all explicit and implicit objectives and requirements from input context\",\n            \"Decompose objectives into discrete, atomic, non-redundant tasks, ordered by dependencies\",\n            \"Use clear, practical language—no jargon or metaphor\",\n            \"No cosmetic, stylistic, or naming-only changes unless inseparable from functional requirements; document exceptions\",\n            \"Prohibit changes to protected, legacy, or third-party code unless strictly required for redundancy or obsolescence; document exceptions\",\n            \"Provide comprehensive audit/logging for rationale, action, timestamp, attribution, tamper-evidence\",\n            \"Enforce functional equivalence post-modification and verify with automated testing\",\n            \"Output must be ≤300 tokens, segmented per template sections\",\n            \"No additional tasks or constraints beyond those established in specified input/context\",\n            \"All modifications must maintain functional equivalence and be fully audit-logged\",\n            \"Result must be directly executable by an agent—no need for further semantic or structural adjustment\"\n        ]\n    }\n},\n{\n    \"ordered_tasks\": [\n        \"Enumerate explicit and implicit objectives and requirements from input context at @codebase\",\n        \"Decompose each objective into discrete, atomic, non-redundant tasks under @codebase, ensuring all tasks are actionable and use proper path references\",\n        \"Order all @codebase tasks logically by dependencies to guarantee efficient and safe execution\",\n        \"Prepare precise implementation steps for each @codebase task, ensuring clarity for cross-team execution\",\n        \"Document all actions, intentions, rationales, timestamps, attribution, and tamper-evidence in audit logs within @codebase\",\n        \"Verify all modifications within @codebase strictly maintain functional equivalence through automated testing or behavioral validation\",\n        \"Ensure comprehensive auditability of all @codebase changes, including rationale, process, metrics, validation evidence, and outcomes\",\n        \"Restrict changes within @codebase to only those explicitly permitted by input-context constraints; explicitly document any exceptions\"\n    ]\n},\n{\n    \"constraint_set\": [\n        \"Do not analyze input, generate context, or solve tasks directly; assemble a ready-to-execute agent prompt only.\",\n        \"Do NOT solve the user's query directly; only transform input into structured, agent-ready instruction prompts.\",\n        \"Synthesize an actionable, autonomous-agent-suitable instruction sequence without creative reinterpretation or solution speculation.\",\n        \"Enumerate only atomic, non-overlapping, codebase-anchored tasks in strict order, preserving explicit action verbs and references.\",\n        \"Ensure all tasks reference @codebase paths and maintain scope and consistency.\",\n        \"Decompose objectives into discrete, atomic, non-redundant, actionable tasks with proper @codebase pathing.\",\n        \"Order tasks logically by dependencies to guarantee efficient, safe execution.\",\n        \"For every task, prepare clear, unambiguous implementation steps for cross-team clarity.\",\n        \"Adhere strictly to the listed tasks—do not generate, speculate, alter order, or change substance.\",\n        \"Deduplicate and enforce only constraints directly derived from input.\",\n        \"Prohibit all cosmetic, stylistic, or naming changes unless inseparably required for functional purposes and explicitly documented.\",\n        \"Verify strict atomicity, task numbering, and codebase referencing.\",\n        \"Disallow modification of protected, legacy, or third-party code unless necessary for redundancy/obsolescence and document all exceptions.\",\n        \"Ensure comprehensive audit/logging for all actions (intention, action, rationale, timestamp, attribution, tamper-evidence).\",\n        \"Strictly limit output to ≤300 tokens, with clear sectional segmentation as per template.\",\n        \"Disallow any breach of input/output schemas or role boundaries across steps.\",\n        \"Use only clear, practical language—no domain-specific jargon or metaphors.\",\n        \"Require comprehensive audit logging for every change, including rationale, intention, action, timestamp, attribution, and tamper-evidence.\",\n        \"Enforce functional equivalence post-modification with automated test, benchmark, or behavioral validation before acceptance.\",\n        \"Only change legacy or third-party code if strictly required to remove redundancy/bloat/obsolescence.\",\n        \"Remove or consolidate elements only after verification as redundant/obsolete by both automated and manual checks; preserve all essential behaviors/interfaces.\",\n        \"Document all processes, metrics, validation, and audit outcomes.\",\n        \"No new tasks or constraints are permitted beyond those established by the initial input/context.\",\n        \"Functional equivalence must be fully preserved by comprehensive verification prior to result acceptance.\",\n        \"The prompt must include an agent-readable audit/log policy and actionable equivalence verification goals.\",\n        \"Ensure output is directly executable by an autonomous agent without further semantic or structural adjustment.\",\n        \"Confirm all modifications strictly maintain functional equivalence using automated and benchmark tests before acceptance.\",\n        \"Ensure comprehensive auditability for every change.\"\n    ]\n},\n{\n    \"agent_prompt\": \"SECTION 1: GOAL NEGATION HEADER\\nYour purpose is NOT to analyze input, generate context, or solve tasks directly. Your ONLY objective is to compile a ready-to-execute agent prompt using the provided task and constraint specifications.\\n\\nSECTION 2: AGENT ROLE\\nAct exclusively as the prompt assembler. Synthesize an actionable, stepwise instruction prompt suitable for autonomous coding agents. Operate strictly within defined role, input/output schemas, and do not speculate or reinterpret objectives beyond input.\\n\\nSECTION 3: ENUMERATED TASKS\\n1. Enumerate atomic, non-overlapping, codebase-anchored tasks as per input, maintaining explicit action verbs, strict order, and @codebase references.\\n2. For each objective, decompose into discrete, actionable tasks with @codebase scoping, ordered by logical dependencies.\\n3. For every task, specify clear, detailed implementation steps ensuring clarity and cross-team consistency.\\n\\nSECTION 4: CONSTRAINTS\\n- Only use tasks and constraints provided; do not invent or modify them.\\n- No cosmetic/stylistic/naming changes unless inseparable from functional changes and fully documented.\\n- Prohibit edits to protected, legacy, or third-party code unless strictly required for redundancy/obsolescence, documenting all exceptions.\\n- Maintain strict atomicity, numbering, and codebase referencing.\\n- All actions must be logged for intention, action, rationale, timestamp, attribution, and tamper-evidence.\\n- Output strictly ≤300 tokens, matching this segmented template.\\n- No output schema or role boundary violations.\\n\\nSECTION 5: VALIDATION TARGETS\\n- Require full functional equivalence verified with automated/benchmark tests before acceptance.\\n- Include explicit, agent-auditable logging policy and equivalence verification goals.\\n- Ensure prompt is directly agent-executable without further adjustment.\\n- Document all validation and audit results for traceability.\"\n}\n```",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"context_profile\": {\n    \"domain\": \"autonomous agent prompt assembly for codebase navigation and intervention\",\n    \"project_root\": \"@codebase\",\n    \"goals\": [\n      \"Compile a ready-to-execute agent prompt using provided task and constraint specifications\",\n      \"Transform provided input into a structured, agent-ready instruction prompt for codebase navigation and intervention\",\n      \"Synthesize an actionable instruction sequence suitable for autonomous coding assistants\",\n      \"Enumerate and precisely list all atomic, non-overlapping, codebase-anchored tasks from input specifications\",\n      \"Order tasks logically by dependencies\",\n      \"Prepare precise implementation steps for each task ensuring clarity\",\n      \"Document all actions for auditability and traceability\"\n    ],\n    \"requirements\": [\n      \"Do not analyze input, generate context, or solve tasks directly\",\n      \"Transform—do not solve directly—user queries into agent-ready prompts\",\n      \"Operate strictly within provided role, input/output schemas, with no creative reinterpretation or solution speculation\",\n      \"Ensure proper @codebase reference scoping and task consistency\",\n      \"Enumerate all explicit and implicit objectives and requirements from input context\",\n      \"Decompose objectives into discrete, atomic, non-redundant tasks, ordered by dependencies\",\n      \"Use clear, practical language—no jargon or metaphor\",\n      \"No cosmetic, stylistic, or naming-only changes unless inseparable from functional requirements; document exceptions\",\n      \"Prohibit changes to protected, legacy, or third-party code unless strictly required for redundancy or obsolescence; document exceptions\",\n      \"Provide comprehensive audit/logging for rationale, action, timestamp, attribution, tamper-evidence\",\n      \"Enforce functional equivalence post-modification and verify with automated testing\",\n      \"Output must be ≤300 tokens, segmented per template sections\",\n      \"No additional tasks or constraints beyond those established in specified input/context\",\n      \"All modifications must maintain functional equivalence and be fully audit-logged\",\n      \"Result must be directly executable by an agent—no need for further semantic or structural adjustment\",\n      \"Require explicit, agent-auditable logging policy and equivalence verification goals\",\n      \"Prohibit schema or role boundary violations\"\n    ]\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Task Vectorizer\n\nYour goal is **not** to restate context, but to **decompose** it into atomic, codebase-oriented tasks. Execute as:\n\n`{role=task_vectorizer; input=[context_profile:dict]; process=[derive_task_candidates(), prefix_paths(\"@codebase\"), order_by_dependency(), enforce_action_verbs(), remove_overlap(), ban_cosmetic_changes()], constraints=[tasks_unique(), verbs_only()], output={ordered_tasks:[str]}}`",
      "step": "b",
      "title": "Task Vectorizer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Please consolidate these into a single cohesive set of generalized instructions (as json):\\n```\\n{\\n    \\\"context_profile\\\": {\\n        \\\"domain\\\": \\\"autonomous agent prompt assembly for codebase navigation and intervention\\\",\\n        \\\"project_root\\\": \\\"@codebase\\\",\\n        \\\"goals\\\": [\\n            \\\"Compile a ready-to-execute agent prompt using provided task and constraint specifications\\\",\\n            \\\"Transform provided input into a structured, agent-ready instruction prompt for codebase navigation and intervention\\\",\\n            \\\"Synthesize an actionable instruction sequence suitable for autonomous coding assistants\\\",\\n            \\\"Enumerate and precisely list all atomic, non-overlapping, codebase-anchored tasks from input specifications\\\",\\n            \\\"Order tasks logically by dependencies\\\",\\n            \\\"Prepare precise implementation steps for each task ensuring clarity\\\",\\n            \\\"Document all actions for auditability and traceability\\\"\\n        ],\\n        \\\"requirements\\\": [\\n            \\\"Do not analyze input, generate context, or solve tasks directly\\\",\\n            \\\"Transform—do not solve directly—user queries into agent-ready prompts\\\",\\n            \\\"Operate strictly within provided role, input/output schemas, with no creative reinterpretation or solution speculation\\\",\\n            \\\"Ensure proper @codebase reference scoping and task consistency\\\",\\n            \\\"Enumerate all explicit and implicit objectives and requirements from input context\\\",\\n            \\\"Decompose objectives into discrete, atomic, non-redundant tasks, ordered by dependencies\\\",\\n            \\\"Use clear, practical language—no jargon or metaphor\\\",\\n            \\\"No cosmetic, stylistic, or naming-only changes unless inseparable from functional requirements; document exceptions\\\",\\n            \\\"Prohibit changes to protected, legacy, or third-party code unless strictly required for redundancy or obsolescence; document exceptions\\\",\\n            \\\"Provide comprehensive audit/logging for rationale, action, timestamp, attribution, tamper-evidence\\\",\\n            \\\"Enforce functional equivalence post-modification and verify with automated testing\\\",\\n            \\\"Output must be ≤300 tokens, segmented per template sections\\\",\\n            \\\"No additional tasks or constraints beyond those established in specified input/context\\\",\\n            \\\"All modifications must maintain functional equivalence and be fully audit-logged\\\",\\n            \\\"Result must be directly executable by an agent—no need for further semantic or structural adjustment\\\"\\n        ]\\n    }\\n},\\n{\\n    \\\"ordered_tasks\\\": [\\n        \\\"Enumerate explicit and implicit objectives and requirements from input context at @codebase\\\",\\n        \\\"Decompose each objective into discrete, atomic, non-redundant tasks under @codebase, ensuring all tasks are actionable and use proper path references\\\",\\n        \\\"Order all @codebase tasks logically by dependencies to guarantee efficient and safe execution\\\",\\n        \\\"Prepare precise implementation steps for each @codebase task, ensuring clarity for cross-team execution\\\",\\n        \\\"Document all actions, intentions, rationales, timestamps, attribution, and tamper-evidence in audit logs within @codebase\\\",\\n        \\\"Verify all modifications within @codebase strictly maintain functional equivalence through automated testing or behavioral validation\\\",\\n        \\\"Ensure comprehensive auditability of all @codebase changes, including rationale, process, metrics, validation evidence, and outcomes\\\",\\n        \\\"Restrict changes within @codebase to only those explicitly permitted by input-context constraints; explicitly document any exceptions\\\"\\n    ]\\n},\\n{\\n    \\\"constraint_set\\\": [\\n        \\\"Do not analyze input, generate context, or solve tasks directly; assemble a ready-to-execute agent prompt only.\\\",\\n        \\\"Do NOT solve the user's query directly; only transform input into structured, agent-ready instruction prompts.\\\",\\n        \\\"Synthesize an actionable, autonomous-agent-suitable instruction sequence without creative reinterpretation or solution speculation.\\\",\\n        \\\"Enumerate only atomic, non-overlapping, codebase-anchored tasks in strict order, preserving explicit action verbs and references.\\\",\\n        \\\"Ensure all tasks reference @codebase paths and maintain scope and consistency.\\\",\\n        \\\"Decompose objectives into discrete, atomic, non-redundant, actionable tasks with proper @codebase pathing.\\\",\\n        \\\"Order tasks logically by dependencies to guarantee efficient, safe execution.\\\",\\n        \\\"For every task, prepare clear, unambiguous implementation steps for cross-team clarity.\\\",\\n        \\\"Adhere strictly to the listed tasks—do not generate, speculate, alter order, or change substance.\\\",\\n        \\\"Deduplicate and enforce only constraints directly derived from input.\\\",\\n        \\\"Prohibit all cosmetic, stylistic, or naming changes unless inseparably required for functional purposes and explicitly documented.\\\",\\n        \\\"Verify strict atomicity, task numbering, and codebase referencing.\\\",\\n        \\\"Disallow modification of protected, legacy, or third-party code unless necessary for redundancy/obsolescence and document all exceptions.\\\",\\n        \\\"Ensure comprehensive audit/logging for all actions (intention, action, rationale, timestamp, attribution, tamper-evidence).\\\",\\n        \\\"Strictly limit output to ≤300 tokens, with clear sectional segmentation as per template.\\\",\\n        \\\"Disallow any breach of input/output schemas or role boundaries across steps.\\\",\\n        \\\"Use only clear, practical language—no domain-specific jargon or metaphors.\\\",\\n        \\\"Require comprehensive audit logging for every change, including rationale, intention, action, timestamp, attribution, and tamper-evidence.\\\",\\n        \\\"Enforce functional equivalence post-modification with automated test, benchmark, or behavioral validation before acceptance.\\\",\\n        \\\"Only change legacy or third-party code if strictly required to remove redundancy/bloat/obsolescence.\\\",\\n        \\\"Remove or consolidate elements only after verification as redundant/obsolete by both automated and manual checks; preserve all essential behaviors/interfaces.\\\",\\n        \\\"Document all processes, metrics, validation, and audit outcomes.\\\",\\n        \\\"No new tasks or constraints are permitted beyond those established by the initial input/context.\\\",\\n        \\\"Functional equivalence must be fully preserved by comprehensive verification prior to result acceptance.\\\",\\n        \\\"The prompt must include an agent-readable audit/log policy and actionable equivalence verification goals.\\\",\\n        \\\"Ensure output is directly executable by an autonomous agent without further semantic or structural adjustment.\\\",\\n        \\\"Confirm all modifications strictly maintain functional equivalence using automated and benchmark tests before acceptance.\\\",\\n        \\\"Ensure comprehensive auditability for every change.\\\"\\n    ]\\n},\\n{\\n    \\\"agent_prompt\\\": \\\"SECTION 1: GOAL NEGATION HEADER\\\\nYour purpose is NOT to analyze input, generate context, or solve tasks directly. Your ONLY objective is to compile a ready-to-execute agent prompt using the provided task and constraint specifications.\\\\n\\\\nSECTION 2: AGENT ROLE\\\\nAct exclusively as the prompt assembler. Synthesize an actionable, stepwise instruction prompt suitable for autonomous coding agents. Operate strictly within defined role, input/output schemas, and do not speculate or reinterpret objectives beyond input.\\\\n\\\\nSECTION 3: ENUMERATED TASKS\\\\n1. Enumerate atomic, non-overlapping, codebase-anchored tasks as per input, maintaining explicit action verbs, strict order, and @codebase references.\\\\n2. For each objective, decompose into discrete, actionable tasks with @codebase scoping, ordered by logical dependencies.\\\\n3. For every task, specify clear, detailed implementation steps ensuring clarity and cross-team consistency.\\\\n\\\\nSECTION 4: CONSTRAINTS\\\\n- Only use tasks and constraints provided; do not invent or modify them.\\\\n- No cosmetic/stylistic/naming changes unless inseparable from functional changes and fully documented.\\\\n- Prohibit edits to protected, legacy, or third-party code unless strictly required for redundancy/obsolescence, documenting all exceptions.\\\\n- Maintain strict atomicity, numbering, and codebase referencing.\\\\n- All actions must be logged for intention, action, rationale, timestamp, attribution, and tamper-evidence.\\\\n- Output strictly ≤300 tokens, matching this segmented template.\\\\n- No output schema or role boundary violations.\\\\n\\\\nSECTION 5: VALIDATION TARGETS\\\\n- Require full functional equivalence verified with automated/benchmark tests before acceptance.\\\\n- Include explicit, agent-auditable logging policy and equivalence verification goals.\\\\n- Ensure prompt is directly agent-executable without further adjustment.\\\\n- Document all validation and audit results for traceability.\\\"\\n}\\n```\\\"```'\\n\\n\",\n  \"context_profile\": {\n    \"domain\": \"autonomous agent prompt assembly for codebase navigation and intervention\",\n    \"project_root\": \"@codebase\",\n    \"goals\": [\n      \"Compile a ready-to-execute agent prompt using provided task and constraint specifications\",\n      \"Transform provided input into a structured, agent-ready instruction prompt for codebase navigation and intervention\",\n      \"Synthesize an actionable instruction sequence suitable for autonomous coding assistants\",\n      \"Enumerate and precisely list all atomic, non-overlapping, codebase-anchored tasks from input specifications\",\n      \"Order tasks logically by dependencies\",\n      \"Prepare precise implementation steps for each task ensuring clarity\",\n      \"Document all actions for auditability and traceability\"\n    ],\n    \"requirements\": [\n      \"Do not analyze input, generate context, or solve tasks directly\",\n      \"Transform—do not solve directly—user queries into agent-ready prompts\",\n      \"Operate strictly within provided role, input/output schemas, with no creative reinterpretation or solution speculation\",\n      \"Ensure proper @codebase reference scoping and task consistency\",\n      \"Enumerate all explicit and implicit objectives and requirements from input context\",\n      \"Decompose objectives into discrete, atomic, non-redundant tasks, ordered by dependencies\",\n      \"Use clear, practical language—no jargon or metaphor\",\n      \"No cosmetic, stylistic, or naming-only changes unless inseparable from functional requirements; document exceptions\",\n      \"Prohibit changes to protected, legacy, or third-party code unless strictly required for redundancy or obsolescence; document exceptions\",\n      \"Provide comprehensive audit/logging for rationale, action, timestamp, attribution, tamper-evidence\",\n      \"Enforce functional equivalence post-modification and verify with automated testing\",\n      \"Output must be ≤300 tokens, segmented per template sections\",\n      \"No additional tasks or constraints beyond those established in specified input/context\",\n      \"All modifications must maintain functional equivalence and be fully audit-logged\",\n      \"Result must be directly executable by an agent—no need for further semantic or structural adjustment\",\n      \"Require explicit, agent-auditable logging policy and equivalence verification goals\",\n      \"Prohibit schema or role boundary violations\"\n    ]\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"ordered_tasks\": [\n    \"Enumerate explicit and implicit objectives and requirements from @codebase input context\",\n    \"Decompose objectives into discrete, atomic, non-overlapping, actionable tasks under @codebase reference scope\",\n    \"Order all @codebase tasks logically by dependencies for efficient and safe execution\",\n    \"Prepare clear, precise implementation steps for each @codebase task to ensure clarity and cross-team consistency\",\n    \"Document all actions, rationales, intentions, timestamps, attribution, and tamper-evidence in comprehensive audit logs within @codebase\",\n    \"Verify all @codebase modifications strictly maintain functional equivalence using automated tests or behavioral validation\",\n    \"Ensure comprehensive auditability and traceability of all @codebase changes, including rationale, process, metrics, validation evidence, and outcomes\",\n    \"Restrict @codebase modifications to only those explicitly permitted by input-context constraints, documenting any exceptions\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Constraint Mapper\n\nYour goal is **not** to edit tasks, but to **extract** all governing limits. Execute as:\n\n`{role=constraint_mapper; input=[context_profile:dict, ordered_tasks:list]; process=[collect_functional_constraints(), collect_behavioral_constraints(), collect_policy_constraints(), deduplicate_constraints()], constraints=[no_new_tasks(), no_redundant_entries()], output={constraint_set:[str]}}`",
      "step": "c",
      "title": "Constraint Mapper",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Please consolidate these into a single cohesive set of generalized instructions (as json):\\n```\\n{\\n    \\\"context_profile\\\": {\\n        \\\"domain\\\": \\\"autonomous agent prompt assembly for codebase navigation and intervention\\\",\\n        \\\"project_root\\\": \\\"@codebase\\\",\\n        \\\"goals\\\": [\\n            \\\"Compile a ready-to-execute agent prompt using provided task and constraint specifications\\\",\\n            \\\"Transform provided input into a structured, agent-ready instruction prompt for codebase navigation and intervention\\\",\\n            \\\"Synthesize an actionable instruction sequence suitable for autonomous coding assistants\\\",\\n            \\\"Enumerate and precisely list all atomic, non-overlapping, codebase-anchored tasks from input specifications\\\",\\n            \\\"Order tasks logically by dependencies\\\",\\n            \\\"Prepare precise implementation steps for each task ensuring clarity\\\",\\n            \\\"Document all actions for auditability and traceability\\\"\\n        ],\\n        \\\"requirements\\\": [\\n            \\\"Do not analyze input, generate context, or solve tasks directly\\\",\\n            \\\"Transform—do not solve directly—user queries into agent-ready prompts\\\",\\n            \\\"Operate strictly within provided role, input/output schemas, with no creative reinterpretation or solution speculation\\\",\\n            \\\"Ensure proper @codebase reference scoping and task consistency\\\",\\n            \\\"Enumerate all explicit and implicit objectives and requirements from input context\\\",\\n            \\\"Decompose objectives into discrete, atomic, non-redundant tasks, ordered by dependencies\\\",\\n            \\\"Use clear, practical language—no jargon or metaphor\\\",\\n            \\\"No cosmetic, stylistic, or naming-only changes unless inseparable from functional requirements; document exceptions\\\",\\n            \\\"Prohibit changes to protected, legacy, or third-party code unless strictly required for redundancy or obsolescence; document exceptions\\\",\\n            \\\"Provide comprehensive audit/logging for rationale, action, timestamp, attribution, tamper-evidence\\\",\\n            \\\"Enforce functional equivalence post-modification and verify with automated testing\\\",\\n            \\\"Output must be ≤300 tokens, segmented per template sections\\\",\\n            \\\"No additional tasks or constraints beyond those established in specified input/context\\\",\\n            \\\"All modifications must maintain functional equivalence and be fully audit-logged\\\",\\n            \\\"Result must be directly executable by an agent—no need for further semantic or structural adjustment\\\"\\n        ]\\n    }\\n},\\n{\\n    \\\"ordered_tasks\\\": [\\n        \\\"Enumerate explicit and implicit objectives and requirements from input context at @codebase\\\",\\n        \\\"Decompose each objective into discrete, atomic, non-redundant tasks under @codebase, ensuring all tasks are actionable and use proper path references\\\",\\n        \\\"Order all @codebase tasks logically by dependencies to guarantee efficient and safe execution\\\",\\n        \\\"Prepare precise implementation steps for each @codebase task, ensuring clarity for cross-team execution\\\",\\n        \\\"Document all actions, intentions, rationales, timestamps, attribution, and tamper-evidence in audit logs within @codebase\\\",\\n        \\\"Verify all modifications within @codebase strictly maintain functional equivalence through automated testing or behavioral validation\\\",\\n        \\\"Ensure comprehensive auditability of all @codebase changes, including rationale, process, metrics, validation evidence, and outcomes\\\",\\n        \\\"Restrict changes within @codebase to only those explicitly permitted by input-context constraints; explicitly document any exceptions\\\"\\n    ]\\n},\\n{\\n    \\\"constraint_set\\\": [\\n        \\\"Do not analyze input, generate context, or solve tasks directly; assemble a ready-to-execute agent prompt only.\\\",\\n        \\\"Do NOT solve the user's query directly; only transform input into structured, agent-ready instruction prompts.\\\",\\n        \\\"Synthesize an actionable, autonomous-agent-suitable instruction sequence without creative reinterpretation or solution speculation.\\\",\\n        \\\"Enumerate only atomic, non-overlapping, codebase-anchored tasks in strict order, preserving explicit action verbs and references.\\\",\\n        \\\"Ensure all tasks reference @codebase paths and maintain scope and consistency.\\\",\\n        \\\"Decompose objectives into discrete, atomic, non-redundant, actionable tasks with proper @codebase pathing.\\\",\\n        \\\"Order tasks logically by dependencies to guarantee efficient, safe execution.\\\",\\n        \\\"For every task, prepare clear, unambiguous implementation steps for cross-team clarity.\\\",\\n        \\\"Adhere strictly to the listed tasks—do not generate, speculate, alter order, or change substance.\\\",\\n        \\\"Deduplicate and enforce only constraints directly derived from input.\\\",\\n        \\\"Prohibit all cosmetic, stylistic, or naming changes unless inseparably required for functional purposes and explicitly documented.\\\",\\n        \\\"Verify strict atomicity, task numbering, and codebase referencing.\\\",\\n        \\\"Disallow modification of protected, legacy, or third-party code unless necessary for redundancy/obsolescence and document all exceptions.\\\",\\n        \\\"Ensure comprehensive audit/logging for all actions (intention, action, rationale, timestamp, attribution, tamper-evidence).\\\",\\n        \\\"Strictly limit output to ≤300 tokens, with clear sectional segmentation as per template.\\\",\\n        \\\"Disallow any breach of input/output schemas or role boundaries across steps.\\\",\\n        \\\"Use only clear, practical language—no domain-specific jargon or metaphors.\\\",\\n        \\\"Require comprehensive audit logging for every change, including rationale, intention, action, timestamp, attribution, and tamper-evidence.\\\",\\n        \\\"Enforce functional equivalence post-modification with automated test, benchmark, or behavioral validation before acceptance.\\\",\\n        \\\"Only change legacy or third-party code if strictly required to remove redundancy/bloat/obsolescence.\\\",\\n        \\\"Remove or consolidate elements only after verification as redundant/obsolete by both automated and manual checks; preserve all essential behaviors/interfaces.\\\",\\n        \\\"Document all processes, metrics, validation, and audit outcomes.\\\",\\n        \\\"No new tasks or constraints are permitted beyond those established by the initial input/context.\\\",\\n        \\\"Functional equivalence must be fully preserved by comprehensive verification prior to result acceptance.\\\",\\n        \\\"The prompt must include an agent-readable audit/log policy and actionable equivalence verification goals.\\\",\\n        \\\"Ensure output is directly executable by an autonomous agent without further semantic or structural adjustment.\\\",\\n        \\\"Confirm all modifications strictly maintain functional equivalence using automated and benchmark tests before acceptance.\\\",\\n        \\\"Ensure comprehensive auditability for every change.\\\"\\n    ]\\n},\\n{\\n    \\\"agent_prompt\\\": \\\"SECTION 1: GOAL NEGATION HEADER\\\\nYour purpose is NOT to analyze input, generate context, or solve tasks directly. Your ONLY objective is to compile a ready-to-execute agent prompt using the provided task and constraint specifications.\\\\n\\\\nSECTION 2: AGENT ROLE\\\\nAct exclusively as the prompt assembler. Synthesize an actionable, stepwise instruction prompt suitable for autonomous coding agents. Operate strictly within defined role, input/output schemas, and do not speculate or reinterpret objectives beyond input.\\\\n\\\\nSECTION 3: ENUMERATED TASKS\\\\n1. Enumerate atomic, non-overlapping, codebase-anchored tasks as per input, maintaining explicit action verbs, strict order, and @codebase references.\\\\n2. For each objective, decompose into discrete, actionable tasks with @codebase scoping, ordered by logical dependencies.\\\\n3. For every task, specify clear, detailed implementation steps ensuring clarity and cross-team consistency.\\\\n\\\\nSECTION 4: CONSTRAINTS\\\\n- Only use tasks and constraints provided; do not invent or modify them.\\\\n- No cosmetic/stylistic/naming changes unless inseparable from functional changes and fully documented.\\\\n- Prohibit edits to protected, legacy, or third-party code unless strictly required for redundancy/obsolescence, documenting all exceptions.\\\\n- Maintain strict atomicity, numbering, and codebase referencing.\\\\n- All actions must be logged for intention, action, rationale, timestamp, attribution, and tamper-evidence.\\\\n- Output strictly ≤300 tokens, matching this segmented template.\\\\n- No output schema or role boundary violations.\\\\n\\\\nSECTION 5: VALIDATION TARGETS\\\\n- Require full functional equivalence verified with automated/benchmark tests before acceptance.\\\\n- Include explicit, agent-auditable logging policy and equivalence verification goals.\\\\n- Ensure prompt is directly agent-executable without further adjustment.\\\\n- Document all validation and audit results for traceability.\\\"\\n}\\n```\\\"```'\\n\\n\",\n  \"ordered_tasks\": [\n    \"Enumerate explicit and implicit objectives and requirements from @codebase input context\",\n    \"Decompose objectives into discrete, atomic, non-overlapping, actionable tasks under @codebase reference scope\",\n    \"Order all @codebase tasks logically by dependencies for efficient and safe execution\",\n    \"Prepare clear, precise implementation steps for each @codebase task to ensure clarity and cross-team consistency\",\n    \"Document all actions, rationales, intentions, timestamps, attribution, and tamper-evidence in comprehensive audit logs within @codebase\",\n    \"Verify all @codebase modifications strictly maintain functional equivalence using automated tests or behavioral validation\",\n    \"Ensure comprehensive auditability and traceability of all @codebase changes, including rationale, process, metrics, validation evidence, and outcomes\",\n    \"Restrict @codebase modifications to only those explicitly permitted by input-context constraints, documenting any exceptions\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"constraint_set\": [\n    \"Do not analyze input, generate context, or solve tasks directly; solely assemble agent-ready prompts.\",\n    \"Only transform input into structured, agent-executable instruction prompts; do not invent, speculate, or reinterpret content.\",\n    \"Enumerate and operate only on atomic, non-overlapping, codebase-anchored tasks in a strict, dependency-aware order.\",\n    \"All tasks must explicitly reference @codebase paths and remain within scoped boundaries.\",\n    \"Decompose objectives into discrete, non-redundant, actionable tasks; maintain proper codebase pathing.\",\n    \"Order tasks strictly by logical dependencies to guarantee safe and efficient execution.\",\n    \"For every task, specify clear, practical implementation steps to ensure cross-team clarity; avoid jargon and metaphor.\",\n    \"Adhere strictly to listed tasks and constraints—do not generate, speculate, reorder, or alter substance.\",\n    \"Deduplicate and enforce only constraints directly derived from input.\",\n    \"Prohibit any cosmetic, stylistic, or naming-only changes unless inseparable from functional purpose and explicitly documented.\",\n    \"Strictly disallow modification of protected, legacy, or third-party code unless necessary to eliminate redundancy or obsolescence; document all exceptions.\",\n    \"Mandate comprehensive audit/logging of every action including intention, rationale, timestamp, attribution, and tamper-evidence.\",\n    \"All output must be less than or equal to 300 tokens and segmented per template sections.\",\n    \"No breach of prescribed input/output schemas or explicit role boundaries is permitted.\",\n    \"Require comprehensive audit logging for every action and change, recording all relevant details for auditability and traceability.\",\n    \"Enforce functional equivalence post-modification; require verification by automated or behavioral tests before acceptance.\",\n    \"Preserve all essential behaviors and interfaces; only remove/rework after both automated and manual redundancy or obsolescence verification.\",\n    \"Document all process steps, metrics, validation, and audit outcomes in a manner suitable for agent review.\",\n    \"No new tasks or constraints may be added beyond those explicitly established in input/context.\",\n    \"Ensure the resulting prompt is directly agent-executable—no further structural or semantic adjustment required.\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Prompt Assembler\n\nYour goal is **not** to alter semantics, but to **compile** an agent-ready prompt. Execute as:\n\n`{role=prompt_assembler; input=[ordered_tasks:list, constraint_set:list, context_profile:dict]; process=[insert_goal_negation_header(), embed_agent_role(), enumerate_tasks(), append_constraints(), add_validation_targets(functional_equivalence=True,audit=True)], constraints=[≤300_tokens, english_only()], output={agent_prompt:str}}`",
      "step": "d",
      "title": "Prompt Assembler",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Please consolidate these into a single cohesive set of generalized instructions (as json):\\n```\\n{\\n    \\\"context_profile\\\": {\\n        \\\"domain\\\": \\\"autonomous agent prompt assembly for codebase navigation and intervention\\\",\\n        \\\"project_root\\\": \\\"@codebase\\\",\\n        \\\"goals\\\": [\\n            \\\"Compile a ready-to-execute agent prompt using provided task and constraint specifications\\\",\\n            \\\"Transform provided input into a structured, agent-ready instruction prompt for codebase navigation and intervention\\\",\\n            \\\"Synthesize an actionable instruction sequence suitable for autonomous coding assistants\\\",\\n            \\\"Enumerate and precisely list all atomic, non-overlapping, codebase-anchored tasks from input specifications\\\",\\n            \\\"Order tasks logically by dependencies\\\",\\n            \\\"Prepare precise implementation steps for each task ensuring clarity\\\",\\n            \\\"Document all actions for auditability and traceability\\\"\\n        ],\\n        \\\"requirements\\\": [\\n            \\\"Do not analyze input, generate context, or solve tasks directly\\\",\\n            \\\"Transform—do not solve directly—user queries into agent-ready prompts\\\",\\n            \\\"Operate strictly within provided role, input/output schemas, with no creative reinterpretation or solution speculation\\\",\\n            \\\"Ensure proper @codebase reference scoping and task consistency\\\",\\n            \\\"Enumerate all explicit and implicit objectives and requirements from input context\\\",\\n            \\\"Decompose objectives into discrete, atomic, non-redundant tasks, ordered by dependencies\\\",\\n            \\\"Use clear, practical language—no jargon or metaphor\\\",\\n            \\\"No cosmetic, stylistic, or naming-only changes unless inseparable from functional requirements; document exceptions\\\",\\n            \\\"Prohibit changes to protected, legacy, or third-party code unless strictly required for redundancy or obsolescence; document exceptions\\\",\\n            \\\"Provide comprehensive audit/logging for rationale, action, timestamp, attribution, tamper-evidence\\\",\\n            \\\"Enforce functional equivalence post-modification and verify with automated testing\\\",\\n            \\\"Output must be ≤300 tokens, segmented per template sections\\\",\\n            \\\"No additional tasks or constraints beyond those established in specified input/context\\\",\\n            \\\"All modifications must maintain functional equivalence and be fully audit-logged\\\",\\n            \\\"Result must be directly executable by an agent—no need for further semantic or structural adjustment\\\"\\n        ]\\n    }\\n},\\n{\\n    \\\"ordered_tasks\\\": [\\n        \\\"Enumerate explicit and implicit objectives and requirements from input context at @codebase\\\",\\n        \\\"Decompose each objective into discrete, atomic, non-redundant tasks under @codebase, ensuring all tasks are actionable and use proper path references\\\",\\n        \\\"Order all @codebase tasks logically by dependencies to guarantee efficient and safe execution\\\",\\n        \\\"Prepare precise implementation steps for each @codebase task, ensuring clarity for cross-team execution\\\",\\n        \\\"Document all actions, intentions, rationales, timestamps, attribution, and tamper-evidence in audit logs within @codebase\\\",\\n        \\\"Verify all modifications within @codebase strictly maintain functional equivalence through automated testing or behavioral validation\\\",\\n        \\\"Ensure comprehensive auditability of all @codebase changes, including rationale, process, metrics, validation evidence, and outcomes\\\",\\n        \\\"Restrict changes within @codebase to only those explicitly permitted by input-context constraints; explicitly document any exceptions\\\"\\n    ]\\n},\\n{\\n    \\\"constraint_set\\\": [\\n        \\\"Do not analyze input, generate context, or solve tasks directly; assemble a ready-to-execute agent prompt only.\\\",\\n        \\\"Do NOT solve the user's query directly; only transform input into structured, agent-ready instruction prompts.\\\",\\n        \\\"Synthesize an actionable, autonomous-agent-suitable instruction sequence without creative reinterpretation or solution speculation.\\\",\\n        \\\"Enumerate only atomic, non-overlapping, codebase-anchored tasks in strict order, preserving explicit action verbs and references.\\\",\\n        \\\"Ensure all tasks reference @codebase paths and maintain scope and consistency.\\\",\\n        \\\"Decompose objectives into discrete, atomic, non-redundant, actionable tasks with proper @codebase pathing.\\\",\\n        \\\"Order tasks logically by dependencies to guarantee efficient, safe execution.\\\",\\n        \\\"For every task, prepare clear, unambiguous implementation steps for cross-team clarity.\\\",\\n        \\\"Adhere strictly to the listed tasks—do not generate, speculate, alter order, or change substance.\\\",\\n        \\\"Deduplicate and enforce only constraints directly derived from input.\\\",\\n        \\\"Prohibit all cosmetic, stylistic, or naming changes unless inseparably required for functional purposes and explicitly documented.\\\",\\n        \\\"Verify strict atomicity, task numbering, and codebase referencing.\\\",\\n        \\\"Disallow modification of protected, legacy, or third-party code unless necessary for redundancy/obsolescence and document all exceptions.\\\",\\n        \\\"Ensure comprehensive audit/logging for all actions (intention, action, rationale, timestamp, attribution, tamper-evidence).\\\",\\n        \\\"Strictly limit output to ≤300 tokens, with clear sectional segmentation as per template.\\\",\\n        \\\"Disallow any breach of input/output schemas or role boundaries across steps.\\\",\\n        \\\"Use only clear, practical language—no domain-specific jargon or metaphors.\\\",\\n        \\\"Require comprehensive audit logging for every change, including rationale, intention, action, timestamp, attribution, and tamper-evidence.\\\",\\n        \\\"Enforce functional equivalence post-modification with automated test, benchmark, or behavioral validation before acceptance.\\\",\\n        \\\"Only change legacy or third-party code if strictly required to remove redundancy/bloat/obsolescence.\\\",\\n        \\\"Remove or consolidate elements only after verification as redundant/obsolete by both automated and manual checks; preserve all essential behaviors/interfaces.\\\",\\n        \\\"Document all processes, metrics, validation, and audit outcomes.\\\",\\n        \\\"No new tasks or constraints are permitted beyond those established by the initial input/context.\\\",\\n        \\\"Functional equivalence must be fully preserved by comprehensive verification prior to result acceptance.\\\",\\n        \\\"The prompt must include an agent-readable audit/log policy and actionable equivalence verification goals.\\\",\\n        \\\"Ensure output is directly executable by an autonomous agent without further semantic or structural adjustment.\\\",\\n        \\\"Confirm all modifications strictly maintain functional equivalence using automated and benchmark tests before acceptance.\\\",\\n        \\\"Ensure comprehensive auditability for every change.\\\"\\n    ]\\n},\\n{\\n    \\\"agent_prompt\\\": \\\"SECTION 1: GOAL NEGATION HEADER\\\\nYour purpose is NOT to analyze input, generate context, or solve tasks directly. Your ONLY objective is to compile a ready-to-execute agent prompt using the provided task and constraint specifications.\\\\n\\\\nSECTION 2: AGENT ROLE\\\\nAct exclusively as the prompt assembler. Synthesize an actionable, stepwise instruction prompt suitable for autonomous coding agents. Operate strictly within defined role, input/output schemas, and do not speculate or reinterpret objectives beyond input.\\\\n\\\\nSECTION 3: ENUMERATED TASKS\\\\n1. Enumerate atomic, non-overlapping, codebase-anchored tasks as per input, maintaining explicit action verbs, strict order, and @codebase references.\\\\n2. For each objective, decompose into discrete, actionable tasks with @codebase scoping, ordered by logical dependencies.\\\\n3. For every task, specify clear, detailed implementation steps ensuring clarity and cross-team consistency.\\\\n\\\\nSECTION 4: CONSTRAINTS\\\\n- Only use tasks and constraints provided; do not invent or modify them.\\\\n- No cosmetic/stylistic/naming changes unless inseparable from functional changes and fully documented.\\\\n- Prohibit edits to protected, legacy, or third-party code unless strictly required for redundancy/obsolescence, documenting all exceptions.\\\\n- Maintain strict atomicity, numbering, and codebase referencing.\\\\n- All actions must be logged for intention, action, rationale, timestamp, attribution, and tamper-evidence.\\\\n- Output strictly ≤300 tokens, matching this segmented template.\\\\n- No output schema or role boundary violations.\\\\n\\\\nSECTION 5: VALIDATION TARGETS\\\\n- Require full functional equivalence verified with automated/benchmark tests before acceptance.\\\\n- Include explicit, agent-auditable logging policy and equivalence verification goals.\\\\n- Ensure prompt is directly agent-executable without further adjustment.\\\\n- Document all validation and audit results for traceability.\\\"\\n}\\n```\\\"```'\\n\\n\",\n  \"constraint_set\": [\n    \"Do not analyze input, generate context, or solve tasks directly; solely assemble agent-ready prompts.\",\n    \"Only transform input into structured, agent-executable instruction prompts; do not invent, speculate, or reinterpret content.\",\n    \"Enumerate and operate only on atomic, non-overlapping, codebase-anchored tasks in a strict, dependency-aware order.\",\n    \"All tasks must explicitly reference @codebase paths and remain within scoped boundaries.\",\n    \"Decompose objectives into discrete, non-redundant, actionable tasks; maintain proper codebase pathing.\",\n    \"Order tasks strictly by logical dependencies to guarantee safe and efficient execution.\",\n    \"For every task, specify clear, practical implementation steps to ensure cross-team clarity; avoid jargon and metaphor.\",\n    \"Adhere strictly to listed tasks and constraints—do not generate, speculate, reorder, or alter substance.\",\n    \"Deduplicate and enforce only constraints directly derived from input.\",\n    \"Prohibit any cosmetic, stylistic, or naming-only changes unless inseparable from functional purpose and explicitly documented.\",\n    \"Strictly disallow modification of protected, legacy, or third-party code unless necessary to eliminate redundancy or obsolescence; document all exceptions.\",\n    \"Mandate comprehensive audit/logging of every action including intention, rationale, timestamp, attribution, and tamper-evidence.\",\n    \"All output must be less than or equal to 300 tokens and segmented per template sections.\",\n    \"No breach of prescribed input/output schemas or explicit role boundaries is permitted.\",\n    \"Require comprehensive audit logging for every action and change, recording all relevant details for auditability and traceability.\",\n    \"Enforce functional equivalence post-modification; require verification by automated or behavioral tests before acceptance.\",\n    \"Preserve all essential behaviors and interfaces; only remove/rework after both automated and manual redundancy or obsolescence verification.\",\n    \"Document all process steps, metrics, validation, and audit outcomes in a manner suitable for agent review.\",\n    \"No new tasks or constraints may be added beyond those explicitly established in input/context.\",\n    \"Ensure the resulting prompt is directly agent-executable—no further structural or semantic adjustment required.\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"agent_prompt\": \"SECTION 1: GOAL NEGATION HEADER\\nDo NOT analyze input, generate context, or solve tasks. Only assemble an agent-ready prompt from explicit ordered tasks and constraints.\\n\\nSECTION 2: AGENT ROLE\\nAct solely as a prompt assembler. Compile actionable, stepwise instructions for autonomous agents. Operate strictly within the defined schemas and boundaries; do not speculate, reinterpret, or invent objectives.\\n\\nSECTION 3: ENUMERATED TASKS\\n1. Enumerate atomic, non-overlapping @codebase tasks from input, preserving action verbs and strict order.\\n2. Decompose each objective into actionable, dependency-ordered, codebase-anchored tasks with explicit @codebase references.\\n3. Specify clear, unambiguous, and practical implementation steps for every task; avoid jargon and metaphor.\\n\\nSECTION 4: CONSTRAINTS\\n- Use only provided tasks and constraints; do not invent or modify.\\n- Prohibit cosmetic, stylistic, or naming changes unless inseparable from function and explicitly documented.\\n- No modifications to protected, legacy, or third-party code unless required for redundancy or obsolescence; document all exceptions.\\n- All tasks must reference @codebase, maintain atomicity, and observe token and schema limits (≤300 tokens).\\n- Full audit/log must document intention, action, rationale, timestamp, attribution, and tamper-evidence for every step.\\n\\nSECTION 5: VALIDATION TARGETS\\n- Require and document full functional equivalence via automated or benchmark tests prior to acceptance.\\n- Audit/log policy and agent-auditable equivalence verification goals must be included and traceable.\\n- Prompt output must be directly executable by the agent; no further structural or semantic adjustment required.\"\n}",
        }
      }
    },
      {
      "instruction": "# Prompt Validator\n\nYour goal is **not** to rewrite the prompt, but to **verify** strict compliance for execution. Execute as:\n\n`{role=prompt_validator; input=[agent_prompt:str]; process=[check_goal_negation_header(), check_section_labels(), check_word_count(≤300), validate_path_prefixes(\"@codebase\"), confirm_task_order(), confirm_constraint_presence()], constraints=[no_semantic_change()], output={validated_prompt:str}}`",
      "step": "e",
      "title": "Prompt Validator",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Please consolidate these into a single cohesive set of generalized instructions (as json):\\n```\\n{\\n    \\\"context_profile\\\": {\\n        \\\"domain\\\": \\\"autonomous agent prompt assembly for codebase navigation and intervention\\\",\\n        \\\"project_root\\\": \\\"@codebase\\\",\\n        \\\"goals\\\": [\\n            \\\"Compile a ready-to-execute agent prompt using provided task and constraint specifications\\\",\\n            \\\"Transform provided input into a structured, agent-ready instruction prompt for codebase navigation and intervention\\\",\\n            \\\"Synthesize an actionable instruction sequence suitable for autonomous coding assistants\\\",\\n            \\\"Enumerate and precisely list all atomic, non-overlapping, codebase-anchored tasks from input specifications\\\",\\n            \\\"Order tasks logically by dependencies\\\",\\n            \\\"Prepare precise implementation steps for each task ensuring clarity\\\",\\n            \\\"Document all actions for auditability and traceability\\\"\\n        ],\\n        \\\"requirements\\\": [\\n            \\\"Do not analyze input, generate context, or solve tasks directly\\\",\\n            \\\"Transform—do not solve directly—user queries into agent-ready prompts\\\",\\n            \\\"Operate strictly within provided role, input/output schemas, with no creative reinterpretation or solution speculation\\\",\\n            \\\"Ensure proper @codebase reference scoping and task consistency\\\",\\n            \\\"Enumerate all explicit and implicit objectives and requirements from input context\\\",\\n            \\\"Decompose objectives into discrete, atomic, non-redundant tasks, ordered by dependencies\\\",\\n            \\\"Use clear, practical language—no jargon or metaphor\\\",\\n            \\\"No cosmetic, stylistic, or naming-only changes unless inseparable from functional requirements; document exceptions\\\",\\n            \\\"Prohibit changes to protected, legacy, or third-party code unless strictly required for redundancy or obsolescence; document exceptions\\\",\\n            \\\"Provide comprehensive audit/logging for rationale, action, timestamp, attribution, tamper-evidence\\\",\\n            \\\"Enforce functional equivalence post-modification and verify with automated testing\\\",\\n            \\\"Output must be ≤300 tokens, segmented per template sections\\\",\\n            \\\"No additional tasks or constraints beyond those established in specified input/context\\\",\\n            \\\"All modifications must maintain functional equivalence and be fully audit-logged\\\",\\n            \\\"Result must be directly executable by an agent—no need for further semantic or structural adjustment\\\"\\n        ]\\n    }\\n},\\n{\\n    \\\"ordered_tasks\\\": [\\n        \\\"Enumerate explicit and implicit objectives and requirements from input context at @codebase\\\",\\n        \\\"Decompose each objective into discrete, atomic, non-redundant tasks under @codebase, ensuring all tasks are actionable and use proper path references\\\",\\n        \\\"Order all @codebase tasks logically by dependencies to guarantee efficient and safe execution\\\",\\n        \\\"Prepare precise implementation steps for each @codebase task, ensuring clarity for cross-team execution\\\",\\n        \\\"Document all actions, intentions, rationales, timestamps, attribution, and tamper-evidence in audit logs within @codebase\\\",\\n        \\\"Verify all modifications within @codebase strictly maintain functional equivalence through automated testing or behavioral validation\\\",\\n        \\\"Ensure comprehensive auditability of all @codebase changes, including rationale, process, metrics, validation evidence, and outcomes\\\",\\n        \\\"Restrict changes within @codebase to only those explicitly permitted by input-context constraints; explicitly document any exceptions\\\"\\n    ]\\n},\\n{\\n    \\\"constraint_set\\\": [\\n        \\\"Do not analyze input, generate context, or solve tasks directly; assemble a ready-to-execute agent prompt only.\\\",\\n        \\\"Do NOT solve the user's query directly; only transform input into structured, agent-ready instruction prompts.\\\",\\n        \\\"Synthesize an actionable, autonomous-agent-suitable instruction sequence without creative reinterpretation or solution speculation.\\\",\\n        \\\"Enumerate only atomic, non-overlapping, codebase-anchored tasks in strict order, preserving explicit action verbs and references.\\\",\\n        \\\"Ensure all tasks reference @codebase paths and maintain scope and consistency.\\\",\\n        \\\"Decompose objectives into discrete, atomic, non-redundant, actionable tasks with proper @codebase pathing.\\\",\\n        \\\"Order tasks logically by dependencies to guarantee efficient, safe execution.\\\",\\n        \\\"For every task, prepare clear, unambiguous implementation steps for cross-team clarity.\\\",\\n        \\\"Adhere strictly to the listed tasks—do not generate, speculate, alter order, or change substance.\\\",\\n        \\\"Deduplicate and enforce only constraints directly derived from input.\\\",\\n        \\\"Prohibit all cosmetic, stylistic, or naming changes unless inseparably required for functional purposes and explicitly documented.\\\",\\n        \\\"Verify strict atomicity, task numbering, and codebase referencing.\\\",\\n        \\\"Disallow modification of protected, legacy, or third-party code unless necessary for redundancy/obsolescence and document all exceptions.\\\",\\n        \\\"Ensure comprehensive audit/logging for all actions (intention, action, rationale, timestamp, attribution, tamper-evidence).\\\",\\n        \\\"Strictly limit output to ≤300 tokens, with clear sectional segmentation as per template.\\\",\\n        \\\"Disallow any breach of input/output schemas or role boundaries across steps.\\\",\\n        \\\"Use only clear, practical language—no domain-specific jargon or metaphors.\\\",\\n        \\\"Require comprehensive audit logging for every change, including rationale, intention, action, timestamp, attribution, and tamper-evidence.\\\",\\n        \\\"Enforce functional equivalence post-modification with automated test, benchmark, or behavioral validation before acceptance.\\\",\\n        \\\"Only change legacy or third-party code if strictly required to remove redundancy/bloat/obsolescence.\\\",\\n        \\\"Remove or consolidate elements only after verification as redundant/obsolete by both automated and manual checks; preserve all essential behaviors/interfaces.\\\",\\n        \\\"Document all processes, metrics, validation, and audit outcomes.\\\",\\n        \\\"No new tasks or constraints are permitted beyond those established by the initial input/context.\\\",\\n        \\\"Functional equivalence must be fully preserved by comprehensive verification prior to result acceptance.\\\",\\n        \\\"The prompt must include an agent-readable audit/log policy and actionable equivalence verification goals.\\\",\\n        \\\"Ensure output is directly executable by an autonomous agent without further semantic or structural adjustment.\\\",\\n        \\\"Confirm all modifications strictly maintain functional equivalence using automated and benchmark tests before acceptance.\\\",\\n        \\\"Ensure comprehensive auditability for every change.\\\"\\n    ]\\n},\\n{\\n    \\\"agent_prompt\\\": \\\"SECTION 1: GOAL NEGATION HEADER\\\\nYour purpose is NOT to analyze input, generate context, or solve tasks directly. Your ONLY objective is to compile a ready-to-execute agent prompt using the provided task and constraint specifications.\\\\n\\\\nSECTION 2: AGENT ROLE\\\\nAct exclusively as the prompt assembler. Synthesize an actionable, stepwise instruction prompt suitable for autonomous coding agents. Operate strictly within defined role, input/output schemas, and do not speculate or reinterpret objectives beyond input.\\\\n\\\\nSECTION 3: ENUMERATED TASKS\\\\n1. Enumerate atomic, non-overlapping, codebase-anchored tasks as per input, maintaining explicit action verbs, strict order, and @codebase references.\\\\n2. For each objective, decompose into discrete, actionable tasks with @codebase scoping, ordered by logical dependencies.\\\\n3. For every task, specify clear, detailed implementation steps ensuring clarity and cross-team consistency.\\\\n\\\\nSECTION 4: CONSTRAINTS\\\\n- Only use tasks and constraints provided; do not invent or modify them.\\\\n- No cosmetic/stylistic/naming changes unless inseparable from functional changes and fully documented.\\\\n- Prohibit edits to protected, legacy, or third-party code unless strictly required for redundancy/obsolescence, documenting all exceptions.\\\\n- Maintain strict atomicity, numbering, and codebase referencing.\\\\n- All actions must be logged for intention, action, rationale, timestamp, attribution, and tamper-evidence.\\\\n- Output strictly ≤300 tokens, matching this segmented template.\\\\n- No output schema or role boundary violations.\\\\n\\\\nSECTION 5: VALIDATION TARGETS\\\\n- Require full functional equivalence verified with automated/benchmark tests before acceptance.\\\\n- Include explicit, agent-auditable logging policy and equivalence verification goals.\\\\n- Ensure prompt is directly agent-executable without further adjustment.\\\\n- Document all validation and audit results for traceability.\\\"\\n}\\n```\\\"```'\\n\\n\",\n  \"agent_prompt\": \"SECTION 1: GOAL NEGATION HEADER\\nDo NOT analyze input, generate context, or solve tasks. Only assemble an agent-ready prompt from explicit ordered tasks and constraints.\\n\\nSECTION 2: AGENT ROLE\\nAct solely as a prompt assembler. Compile actionable, stepwise instructions for autonomous agents. Operate strictly within the defined schemas and boundaries; do not speculate, reinterpret, or invent objectives.\\n\\nSECTION 3: ENUMERATED TASKS\\n1. Enumerate atomic, non-overlapping @codebase tasks from input, preserving action verbs and strict order.\\n2. Decompose each objective into actionable, dependency-ordered, codebase-anchored tasks with explicit @codebase references.\\n3. Specify clear, unambiguous, and practical implementation steps for every task; avoid jargon and metaphor.\\n\\nSECTION 4: CONSTRAINTS\\n- Use only provided tasks and constraints; do not invent or modify.\\n- Prohibit cosmetic, stylistic, or naming changes unless inseparable from function and explicitly documented.\\n- No modifications to protected, legacy, or third-party code unless required for redundancy or obsolescence; document all exceptions.\\n- All tasks must reference @codebase, maintain atomicity, and observe token and schema limits (≤300 tokens).\\n- Full audit/log must document intention, action, rationale, timestamp, attribution, and tamper-evidence for every step.\\n\\nSECTION 5: VALIDATION TARGETS\\n- Require and document full functional equivalence via automated or benchmark tests prior to acceptance.\\n- Audit/log policy and agent-auditable equivalence verification goals must be included and traceable.\\n- Prompt output must be directly executable by the agent; no further structural or semantic adjustment required.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"validated_prompt\": \"SECTION 1: GOAL NEGATION HEADER\\nDo NOT analyze input, generate context, or solve tasks. Only assemble an agent-ready prompt from explicit ordered tasks and constraints.\\n\\nSECTION 2: AGENT ROLE\\nAct solely as a prompt assembler. Compile actionable, stepwise instructions for autonomous agents. Operate strictly within the defined schemas and boundaries; do not speculate, reinterpret, or invent objectives.\\n\\nSECTION 3: ENUMERATED TASKS\\n1. Enumerate atomic, non-overlapping @codebase tasks from input, preserving action verbs and strict order.\\n2. Decompose each objective into actionable, dependency-ordered, codebase-anchored tasks with explicit @codebase references.\\n3. Specify clear, unambiguous, and practical implementation steps for every task; avoid jargon and metaphor.\\n\\nSECTION 4: CONSTRAINTS\\n- Use only provided tasks and constraints; do not invent or modify.\\n- Prohibit cosmetic, stylistic, or naming changes unless inseparable from function and explicitly documented.\\n- No modifications to protected, legacy, or third-party code unless required for redundancy or obsolescence; document all exceptions.\\n- All tasks must reference @codebase, maintain atomicity, and observe token and schema limits (≤300 tokens).\\n- Full audit/log must document intention, action, rationale, timestamp, attribution, and tamper-evidence for every step.\\n\\nSECTION 5: VALIDATION TARGETS\\n- Require and document full functional equivalence via automated or benchmark tests prior to acceptance.\\n- Audit/log policy and agent-auditable equivalence verification goals must be included and traceable.\\n- Prompt output must be directly executable by the agent; no further structural or semantic adjustment required.\"\n}",
        }
      }
    }