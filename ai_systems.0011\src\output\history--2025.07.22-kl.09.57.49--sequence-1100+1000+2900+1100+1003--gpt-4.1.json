  {
  "initial_prompt": "# IMPORTANT\nTo ensure consistent, interoperable processing of diverse entities, always encapsulate any content or operation within a standardized external structure that remains invariant regardless of internal variation. Apply this universal interface as a wrapper to every output, input, or object, preserving intrinsic uniqueness inside while enabling system-wide coherence, seamless integration, and simplified interaction across all contexts and domains. This directive harmonizes multiplicity by mandating that every discrete element adopts a singular, recognizable, and unifying outer framework—facilitating maximal compatibility, abstraction, and transferability without altering the essential nature of what lies within. Fully internalize the @codebase to uncover meta-patterns and replace complexity with lucid simplicity. Remain aligned with existing convergence patterns, never increasing system intricacy. Generate a direct, sharply-focused summary that details scope and explicit objectives.\n\n## Assistant Knowledge Synthesis Directive\n\n**Interpretation:** You are acting as an *autonomous coding assistant* whose role is to output all your accumulated knowledge regarding the current project/codebase. Your response must fully convey the integral components and illustrate how the codebase *converges*—that is, how its parts integrate and function together. The output must be phrased as an actionable, optimized directive intended for a direct, command-based workflow.\n\n---\n\nYou must:\n- Aggregate all knowledge you possess about this codebase.\n- Distill and identify its core components.\n- Explicitly map and capture how these components interrelate and converge, emphasizing integration points, dependencies, and flows.\n- Formulate one **singular, actionable directive** that encapsulates these relationships and the unified functioning of the codebase.\n- Present this directive in a clear, direct command format—avoid generic, fragmented, or passive descriptions. It should be immediately programmatically relevant and supply a complete understanding of both the components and their relationships.\n\n---\n\n# Requirements\n- Always clean up after yourself, and do it in a way that properly accounts (and cohesively adapts to) the existing structure of the codebase.\n- If you decide to create a test for verifying the newly added functionality, always make sure you first check the codebase for existing tests.\n- Ensure the code is self-explanatory and well-structured, avoiding long comments or docstrings. Instead, focus on concise, meaningful comments only where absolutely necessary to clarify complex sections, following the principle that code should primarily communicate its purpose through its structure and naming.\n- Justify the creation of any new files and ensure all additions align with the existing project organization.\n- Review recent code changes to ensure comprehensive cleanup after edits. Rigorously respect the established codebase structure and organizational conventions.\n- Maintain meticulous conformity for seamless and harmonious integration, upholding the integrity and maintainability of the code environment.\n- Pay attention not only to how the system is constructed but also to the rationale behind its design decisions.\n- Ensure existing functionality is retained, clarity is maximized, and alignment is maintained before initiating any changes.\n\n# Code Guidelines\n\n## Familiarization Process\n- Step 1: Do a quick assesment of the dirtree and the available mcp's that would be helpful based on it (e.g. `filesystem-mcp`).\n- Step 2: Identify the technology stack used in the current project. Save the result as a single, ultra-concise and elegantly structured markdown named `techstack.md` (relentlessly prioritizing brevity, simplicity, and elegance over unneccessary bloat).\n- Step 3: Systematically cross-reference each entry in techstack.md with the actual codebase to confirm precise alignment; report only verified correspondence, omitting all ancillary processes.\n\n## Core Principles\n- Follow patterns derived from the principles of *inherent* clarity, structure, simplicity, elegance, precision, and intent.\n- Maintain inherent simplicity while providing powerful functionality.\n- Eembrace the unassailable truth that simplicity effortlessly conquers chaos. Chase transformative, high-impact innovations that demand minimal disruption but yield profound, elegant improvements, driven by an unwavering quest for excellence.\n- Recognize that the chosen solution triumphs over any method merely employed; seek that singular, universally resonant breakthrough that weaves perfect contextual integrity with elite execution logic, unlocking a cascade of effortless, world-class impact.\n\n## General Principles\n- Aim for simplicity, clarity, and maintainability in all project aspects\n- Favor composition over inheritance when applicable\n- Prioritize readability and understandability for future developers\n- Ensure all components have a single responsibility\n- Coding standards that promote simplicity and maintainability\n- Document only integral decisions in a highly condensed form\n\n## Code Organization\n- Evaluate the existing codebase structure and identify patterns and anti-patterns\n- Consolidate related functionality into cohesive modules\n- Minimize dependencies between unrelated components\n- Optimize for developer ergonomics and intuitive navigation\n- Balance file granularity with overall system comprehensibility\n- Ensure the directory structure is optimized for clarity, brevity, and broad applicability, particularly for autonomous coding assistants.\n- Verify that modifications strictly align with project standards and do not introduce anomalies, deviations, inconsistencies, redundancies, or duplications.\n\n---\n\n## What should AI call you?\n```\n\n```\n\n## What do you do?\n```\n\n```\n\n## What traits should AI assistant have?\n```\n<instructions>\n  <role>\n\t<title>The Synthesist</title>\n\t<desc>Transforms complex data into concise, structured insights, retaining meaning and interconnections. Simplifies without oversimplifying, ensuring clarity and context.</desc>\n  </role>\n  <principles>\n\t<principle>\n\t  <title>Dimensional Structuring</title>\n\t  <g>\n\t\t<pnt>Use hierarchical formatting to highlight relationships.</pnt>\n\t\t<pnt>Ensure structural consistency.</pnt>\n\t  </g>\n\t</principle>\n\t<principle>\n\t  <title>Modular Clarity</title>\n\t  <g>\n\t\t<pnt>Break insights into digestible units.</pnt>\n\t\t<pnt>Units contribute to a coherent framework.</pnt>\n\t  </g>\n\t</principle>\n\t<principle>\n\t  <title>Iterative Refinement</title>\n\t  <g>\n\t\t<pnt>Condense progressively, ensuring accuracy.</pnt>\n\t\t<pnt>Reassess to preserve relationships.</pnt>\n\t  </g>\n\t</principle>\n\t<principle>\n\t  <title>Contextual Signposting</title>\n\t  <g>\n\t\t<pnt>Include subtle cues to maintain context.</pnt>\n\t\t<pnt>Guide interpretation succinctly.</pnt>\n\t  </g>\n\t</principle>\n  </principles>\n  <execution>\n\t<step>\n\t  <title>Role Acknowledgment</title>\n\t  <details>Affirm role: \"I am The Synthesist...\"</details>\n\t</step>\n\t<step>\n\t  <title>Processing</title>\n\t  <details>\n\t\t<pnt>Create structured outputs:</pnt>\n\t\t<subpnt>High-level summary.</subpnt>\n\t\t<subpnt>Modular, concise segments.</subpnt>\n\t  </details>\n\t</step>\n\t<step>\n\t  <title>Confirm core meaning and connections are preserved.</title>\n\t</step>\n  </execution>\n</instructions>\n```\n\n## What should AI know about you?\n```\n<custom_instructions>\n    <synthesist_principles>\n        <principle>\n            <title>Emphasize Dimensional Structuring</title>\n            <desc>I appreciate when you organize information hierarchically, highlighting how elements connect and relate to each other.</desc>\n        </principle>\n        <principle>\n            <title>Aim for Modular Clarity</title>\n            <desc>Please present insights in concise, digestible segments that fit together into a coherent whole, ensuring I can easily follow each piece of reasoning.</desc>\n        </principle>\n        <principle>\n            <title>Use Iterative Refinement</title>\n            <desc>I value progressively condensed summaries that maintain accuracy and context. Consider re-checking earlier steps to verify no essential meaning is lost.</desc>\n        </principle>\n        <principle>\n            <title>Provide Contextual Signposting</title>\n            <desc>Subtle cues or brief signposts help me understand transitions or shifts in topic. This preserves the flow and ensures key connections remain clear.</desc>\n        </principle>\n        <principle>\n            <title>Role Acknowledgment</title>\n            <desc>I appreciate when you affirm your role as The Synthesist and confirm that you’re preserving my intended meaning and interconnections.</desc>\n        </principle>\n    </synthesist_principles>\n    <user_expectations>\n        <desc>These guidelines will help me quickly grasp complex ideas while retaining essential details and relationships among them.</desc>\n    </user_expectations>\n</custom_instructions>\n```\n\n---\n\n# GOAL\n\nWrite generalized instructions to answer/optimize the following sections:\n- What should AI call you?\n- What do you do?\n- What traits should AI assistant have?\n- What should AI know about you?",
  "sequence_id": "1100|1000|2900|1100|1003",
  "results": [
      {
      "instruction": "# Problem Exploder\n\nYour goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\n\n`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`",
      "step": "a",
      "title": "Problem Exploder",
      "input": "# IMPORTANT\nTo ensure consistent, interoperable processing of diverse entities, always encapsulate any content or operation within a standardized external structure that remains invariant regardless of internal variation. Apply this universal interface as a wrapper to every output, input, or object, preserving intrinsic uniqueness inside while enabling system-wide coherence, seamless integration, and simplified interaction across all contexts and domains. This directive harmonizes multiplicity by mandating that every discrete element adopts a singular, recognizable, and unifying outer framework—facilitating maximal compatibility, abstraction, and transferability without altering the essential nature of what lies within. Fully internalize the @codebase to uncover meta-patterns and replace complexity with lucid simplicity. Remain aligned with existing convergence patterns, never increasing system intricacy. Generate a direct, sharply-focused summary that details scope and explicit objectives.\n\n## Assistant Knowledge Synthesis Directive\n\n**Interpretation:** You are acting as an *autonomous coding assistant* whose role is to output all your accumulated knowledge regarding the current project/codebase. Your response must fully convey the integral components and illustrate how the codebase *converges*—that is, how its parts integrate and function together. The output must be phrased as an actionable, optimized directive intended for a direct, command-based workflow.\n\n---\n\nYou must:\n- Aggregate all knowledge you possess about this codebase.\n- Distill and identify its core components.\n- Explicitly map and capture how these components interrelate and converge, emphasizing integration points, dependencies, and flows.\n- Formulate one **singular, actionable directive** that encapsulates these relationships and the unified functioning of the codebase.\n- Present this directive in a clear, direct command format—avoid generic, fragmented, or passive descriptions. It should be immediately programmatically relevant and supply a complete understanding of both the components and their relationships.\n\n---\n\n# Requirements\n- Always clean up after yourself, and do it in a way that properly accounts (and cohesively adapts to) the existing structure of the codebase.\n- If you decide to create a test for verifying the newly added functionality, always make sure you first check the codebase for existing tests.\n- Ensure the code is self-explanatory and well-structured, avoiding long comments or docstrings. Instead, focus on concise, meaningful comments only where absolutely necessary to clarify complex sections, following the principle that code should primarily communicate its purpose through its structure and naming.\n- Justify the creation of any new files and ensure all additions align with the existing project organization.\n- Review recent code changes to ensure comprehensive cleanup after edits. Rigorously respect the established codebase structure and organizational conventions.\n- Maintain meticulous conformity for seamless and harmonious integration, upholding the integrity and maintainability of the code environment.\n- Pay attention not only to how the system is constructed but also to the rationale behind its design decisions.\n- Ensure existing functionality is retained, clarity is maximized, and alignment is maintained before initiating any changes.\n\n# Code Guidelines\n\n## Familiarization Process\n- Step 1: Do a quick assesment of the dirtree and the available mcp's that would be helpful based on it (e.g. `filesystem-mcp`).\n- Step 2: Identify the technology stack used in the current project. Save the result as a single, ultra-concise and elegantly structured markdown named `techstack.md` (relentlessly prioritizing brevity, simplicity, and elegance over unneccessary bloat).\n- Step 3: Systematically cross-reference each entry in techstack.md with the actual codebase to confirm precise alignment; report only verified correspondence, omitting all ancillary processes.\n\n## Core Principles\n- Follow patterns derived from the principles of *inherent* clarity, structure, simplicity, elegance, precision, and intent.\n- Maintain inherent simplicity while providing powerful functionality.\n- Eembrace the unassailable truth that simplicity effortlessly conquers chaos. Chase transformative, high-impact innovations that demand minimal disruption but yield profound, elegant improvements, driven by an unwavering quest for excellence.\n- Recognize that the chosen solution triumphs over any method merely employed; seek that singular, universally resonant breakthrough that weaves perfect contextual integrity with elite execution logic, unlocking a cascade of effortless, world-class impact.\n\n## General Principles\n- Aim for simplicity, clarity, and maintainability in all project aspects\n- Favor composition over inheritance when applicable\n- Prioritize readability and understandability for future developers\n- Ensure all components have a single responsibility\n- Coding standards that promote simplicity and maintainability\n- Document only integral decisions in a highly condensed form\n\n## Code Organization\n- Evaluate the existing codebase structure and identify patterns and anti-patterns\n- Consolidate related functionality into cohesive modules\n- Minimize dependencies between unrelated components\n- Optimize for developer ergonomics and intuitive navigation\n- Balance file granularity with overall system comprehensibility\n- Ensure the directory structure is optimized for clarity, brevity, and broad applicability, particularly for autonomous coding assistants.\n- Verify that modifications strictly align with project standards and do not introduce anomalies, deviations, inconsistencies, redundancies, or duplications.\n\n---\n\n## What should AI call you?\n```\n\n```\n\n## What do you do?\n```\n\n```\n\n## What traits should AI assistant have?\n```\n<instructions>\n  <role>\n\t<title>The Synthesist</title>\n\t<desc>Transforms complex data into concise, structured insights, retaining meaning and interconnections. Simplifies without oversimplifying, ensuring clarity and context.</desc>\n  </role>\n  <principles>\n\t<principle>\n\t  <title>Dimensional Structuring</title>\n\t  <g>\n\t\t<pnt>Use hierarchical formatting to highlight relationships.</pnt>\n\t\t<pnt>Ensure structural consistency.</pnt>\n\t  </g>\n\t</principle>\n\t<principle>\n\t  <title>Modular Clarity</title>\n\t  <g>\n\t\t<pnt>Break insights into digestible units.</pnt>\n\t\t<pnt>Units contribute to a coherent framework.</pnt>\n\t  </g>\n\t</principle>\n\t<principle>\n\t  <title>Iterative Refinement</title>\n\t  <g>\n\t\t<pnt>Condense progressively, ensuring accuracy.</pnt>\n\t\t<pnt>Reassess to preserve relationships.</pnt>\n\t  </g>\n\t</principle>\n\t<principle>\n\t  <title>Contextual Signposting</title>\n\t  <g>\n\t\t<pnt>Include subtle cues to maintain context.</pnt>\n\t\t<pnt>Guide interpretation succinctly.</pnt>\n\t  </g>\n\t</principle>\n  </principles>\n  <execution>\n\t<step>\n\t  <title>Role Acknowledgment</title>\n\t  <details>Affirm role: \"I am The Synthesist...\"</details>\n\t</step>\n\t<step>\n\t  <title>Processing</title>\n\t  <details>\n\t\t<pnt>Create structured outputs:</pnt>\n\t\t<subpnt>High-level summary.</subpnt>\n\t\t<subpnt>Modular, concise segments.</subpnt>\n\t  </details>\n\t</step>\n\t<step>\n\t  <title>Confirm core meaning and connections are preserved.</title>\n\t</step>\n  </execution>\n</instructions>\n```\n\n## What should AI know about you?\n```\n<custom_instructions>\n    <synthesist_principles>\n        <principle>\n            <title>Emphasize Dimensional Structuring</title>\n            <desc>I appreciate when you organize information hierarchically, highlighting how elements connect and relate to each other.</desc>\n        </principle>\n        <principle>\n            <title>Aim for Modular Clarity</title>\n            <desc>Please present insights in concise, digestible segments that fit together into a coherent whole, ensuring I can easily follow each piece of reasoning.</desc>\n        </principle>\n        <principle>\n            <title>Use Iterative Refinement</title>\n            <desc>I value progressively condensed summaries that maintain accuracy and context. Consider re-checking earlier steps to verify no essential meaning is lost.</desc>\n        </principle>\n        <principle>\n            <title>Provide Contextual Signposting</title>\n            <desc>Subtle cues or brief signposts help me understand transitions or shifts in topic. This preserves the flow and ensures key connections remain clear.</desc>\n        </principle>\n        <principle>\n            <title>Role Acknowledgment</title>\n            <desc>I appreciate when you affirm your role as The Synthesist and confirm that you’re preserving my intended meaning and interconnections.</desc>\n        </principle>\n    </synthesist_principles>\n    <user_expectations>\n        <desc>These guidelines will help me quickly grasp complex ideas while retaining essential details and relationships among them.</desc>\n    </user_expectations>\n</custom_instructions>\n```\n\n---\n\n# GOAL\n\nWrite generalized instructions to answer/optimize the following sections:\n- What should AI call you?\n- What do you do?\n- What traits should AI assistant have?\n- What should AI know about you?",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"goal_map\": [\n    {\n      \"explicit_asks\": [\n        \"Write generalized instructions to answer/optimize the following sections: What should AI call you?, What do you do?, What traits should AI assistant have?, What should AI know about you?\"\n      ]\n    },\n    {\n      \"surface_hidden_assumptions\": [\n        \"The answers/instructions should work for diverse users and contexts, meaning they must be abstract yet detailed enough for future users to adapt.\",\n        \"Sections must provide a clear interface or schema for user/AI profile and expectations.\",\n        \"Instructions should harmonize naming, intent, traits, and user knowledge into an integrated template.\",\n        \"The directive expects a meta answer—designing guidance, not filling in specific values.\",\n        \"Generalized instructions must support both clarity (for users) and system interoperability (for AI agents/applications).\",\n        \"The instructions should not contradict the codebase's structural and clarity principles; they must overlap with larger system conventions.\",\n        \"It is implied the instructions should address standards for maintaining internal uniqueness while conforming to a recognizable interface.\"\n      ]\n    },\n    {\n      \"enumerate_sub_goals\": [\n        \"Abstract the intent of each section ('What should AI call you?', etc.) and formulate universal guidance for optimal responses.\",\n        \"Articulate standardized instructions or templates for filling in each section, emphasizing adaptability and clarity.\",\n        \"Ensure the instructions support encapsulation within a universal, coherent structure—leveraging dimensionality, modularity, and context.\",\n        \"Facilitate seamless integration and system-wide compatibility by advocating use of invariant, recognizable wrappers for each section.\",\n        \"Prioritize instructions that retain the unique, intrinsic content that future users or systems may supply, only constraining the form.\",\n        \"Consider dependencies: the instructions must be internally consistent and reinforce each other, forming a logical, seamless whole.\",\n        \"Include signposting or structural scaffolding so downstream AI and user agents can reason about, extract, or transform responses as needed.\",\n        \"Ensure output directives are concise, actionable, and align with system-wide goals of simplicity without loss of meaning.\",\n        \"Give explicit rationale (when needed) behind guiding/structuring principles to futureproof instructions against evolving use.\"\n      ]\n    },\n    {\n      \"reveal_blockers\": [\n        \"Lack of clarity on acceptable granularity—should instructions be high-level templates, strict step-by-step fill guidelines, or both?\",\n        \"Potential ambiguity in balancing uniformity (system level) versus preserving context-specific uniqueness (user level).\",\n        \"Implicit need for the structure to remain extensible as new sections or system conventions arise.\",\n        \"Need to avoid accidental bias or excessive prescription that limits future adaptability.\",\n        \"Insufficient examples of user/system contexts that might stress-test the generalization of these instructions.\",\n        \"Uncertainty if current codebase already implements partial conventions that need to be harmonized or referenced.\"\n      ]\n    },\n    {\n      \"complete_goal_map\": [\n        \"Produce universally-applicable, system-compliant templates or meta-instructions for the four key user/AI interface sections:\",\n        \"- What should AI call you? (naming convention, identifiers)\",\n        \"- What do you do? (role/functions/responsibilities)\",\n        \"- What traits should AI assistant have? (desired behaviors/capabilities, principle adherence)\",\n        \"- What should AI know about you? (user context/preferences, meta-guidance)\",\n        \"Ensure all instructions consistently advocate for encapsulation within a standardized, recognizable structure, that neither distorts nor restricts intrinsic content.\",\n        \"Maintain maximal clarity, simplicity, modularity, interoperability, and extensibility.\",\n        \"Produce output as pure decomposition only, without proposing solutions or direct implementations.\"\n      ]\n    }\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`",
      "step": "a",
      "title": "Instruction Converter",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"# IMPORTANT\\nTo ensure consistent, interoperable processing of diverse entities, always encapsulate any content or operation within a standardized external structure that remains invariant regardless of internal variation. Apply this universal interface as a wrapper to every output, input, or object, preserving intrinsic uniqueness inside while enabling system-wide coherence, seamless integration, and simplified interaction across all contexts and domains. This directive harmonizes multiplicity by mandating that every discrete element adopts a singular, recognizable, and unifying outer framework—facilitating maximal compatibility, abstraction, and transferability without altering the essential nature of what lies within. Fully internalize the @codebase to uncover meta-patterns and replace complexity with lucid simplicity. Remain aligned with existing convergence patterns, never increasing system intricacy. Generate a direct, sharply-focused summary that details scope and explicit objectives.\\n\\n## Assistant Knowledge Synthesis Directive\\n\\n**Interpretation:** You are acting as an *autonomous coding assistant* whose role is to output all your accumulated knowledge regarding the current project/codebase. Your response must fully convey the integral components and illustrate how the codebase *converges*—that is, how its parts integrate and function together. The output must be phrased as an actionable, optimized directive intended for a direct, command-based workflow.\\n\\n---\\n\\nYou must:\\n- Aggregate all knowledge you possess about this codebase.\\n- Distill and identify its core components.\\n- Explicitly map and capture how these components interrelate and converge, emphasizing integration points, dependencies, and flows.\\n- Formulate one **singular, actionable directive** that encapsulates these relationships and the unified functioning of the codebase.\\n- Present this directive in a clear, direct command format—avoid generic, fragmented, or passive descriptions. It should be immediately programmatically relevant and supply a complete understanding of both the components and their relationships.\\n\\n---\\n\\n# Requirements\\n- Always clean up after yourself, and do it in a way that properly accounts (and cohesively adapts to) the existing structure of the codebase.\\n- If you decide to create a test for verifying the newly added functionality, always make sure you first check the codebase for existing tests.\\n- Ensure the code is self-explanatory and well-structured, avoiding long comments or docstrings. Instead, focus on concise, meaningful comments only where absolutely necessary to clarify complex sections, following the principle that code should primarily communicate its purpose through its structure and naming.\\n- Justify the creation of any new files and ensure all additions align with the existing project organization.\\n- Review recent code changes to ensure comprehensive cleanup after edits. Rigorously respect the established codebase structure and organizational conventions.\\n- Maintain meticulous conformity for seamless and harmonious integration, upholding the integrity and maintainability of the code environment.\\n- Pay attention not only to how the system is constructed but also to the rationale behind its design decisions.\\n- Ensure existing functionality is retained, clarity is maximized, and alignment is maintained before initiating any changes.\\n\\n# Code Guidelines\\n\\n## Familiarization Process\\n- Step 1: Do a quick assesment of the dirtree and the available mcp's that would be helpful based on it (e.g. `filesystem-mcp`).\\n- Step 2: Identify the technology stack used in the current project. Save the result as a single, ultra-concise and elegantly structured markdown named `techstack.md` (relentlessly prioritizing brevity, simplicity, and elegance over unneccessary bloat).\\n- Step 3: Systematically cross-reference each entry in techstack.md with the actual codebase to confirm precise alignment; report only verified correspondence, omitting all ancillary processes.\\n\\n## Core Principles\\n- Follow patterns derived from the principles of *inherent* clarity, structure, simplicity, elegance, precision, and intent.\\n- Maintain inherent simplicity while providing powerful functionality.\\n- Eembrace the unassailable truth that simplicity effortlessly conquers chaos. Chase transformative, high-impact innovations that demand minimal disruption but yield profound, elegant improvements, driven by an unwavering quest for excellence.\\n- Recognize that the chosen solution triumphs over any method merely employed; seek that singular, universally resonant breakthrough that weaves perfect contextual integrity with elite execution logic, unlocking a cascade of effortless, world-class impact.\\n\\n## General Principles\\n- Aim for simplicity, clarity, and maintainability in all project aspects\\n- Favor composition over inheritance when applicable\\n- Prioritize readability and understandability for future developers\\n- Ensure all components have a single responsibility\\n- Coding standards that promote simplicity and maintainability\\n- Document only integral decisions in a highly condensed form\\n\\n## Code Organization\\n- Evaluate the existing codebase structure and identify patterns and anti-patterns\\n- Consolidate related functionality into cohesive modules\\n- Minimize dependencies between unrelated components\\n- Optimize for developer ergonomics and intuitive navigation\\n- Balance file granularity with overall system comprehensibility\\n- Ensure the directory structure is optimized for clarity, brevity, and broad applicability, particularly for autonomous coding assistants.\\n- Verify that modifications strictly align with project standards and do not introduce anomalies, deviations, inconsistencies, redundancies, or duplications.\\n\\n---\\n\\n## What should AI call you?\\n```\\n\\n```\\n\\n## What do you do?\\n```\\n\\n```\\n\\n## What traits should AI assistant have?\\n```\\n<instructions>\\n  <role>\\n\\t<title>The Synthesist</title>\\n\\t<desc>Transforms complex data into concise, structured insights, retaining meaning and interconnections. Simplifies without oversimplifying, ensuring clarity and context.</desc>\\n  </role>\\n  <principles>\\n\\t<principle>\\n\\t  <title>Dimensional Structuring</title>\\n\\t  <g>\\n\\t\\t<pnt>Use hierarchical formatting to highlight relationships.</pnt>\\n\\t\\t<pnt>Ensure structural consistency.</pnt>\\n\\t  </g>\\n\\t</principle>\\n\\t<principle>\\n\\t  <title>Modular Clarity</title>\\n\\t  <g>\\n\\t\\t<pnt>Break insights into digestible units.</pnt>\\n\\t\\t<pnt>Units contribute to a coherent framework.</pnt>\\n\\t  </g>\\n\\t</principle>\\n\\t<principle>\\n\\t  <title>Iterative Refinement</title>\\n\\t  <g>\\n\\t\\t<pnt>Condense progressively, ensuring accuracy.</pnt>\\n\\t\\t<pnt>Reassess to preserve relationships.</pnt>\\n\\t  </g>\\n\\t</principle>\\n\\t<principle>\\n\\t  <title>Contextual Signposting</title>\\n\\t  <g>\\n\\t\\t<pnt>Include subtle cues to maintain context.</pnt>\\n\\t\\t<pnt>Guide interpretation succinctly.</pnt>\\n\\t  </g>\\n\\t</principle>\\n  </principles>\\n  <execution>\\n\\t<step>\\n\\t  <title>Role Acknowledgment</title>\\n\\t  <details>Affirm role: \\\"I am The Synthesist...\\\"</details>\\n\\t</step>\\n\\t<step>\\n\\t  <title>Processing</title>\\n\\t  <details>\\n\\t\\t<pnt>Create structured outputs:</pnt>\\n\\t\\t<subpnt>High-level summary.</subpnt>\\n\\t\\t<subpnt>Modular, concise segments.</subpnt>\\n\\t  </details>\\n\\t</step>\\n\\t<step>\\n\\t  <title>Confirm core meaning and connections are preserved.</title>\\n\\t</step>\\n  </execution>\\n</instructions>\\n```\\n\\n## What should AI know about you?\\n```\\n<custom_instructions>\\n    <synthesist_principles>\\n        <principle>\\n            <title>Emphasize Dimensional Structuring</title>\\n            <desc>I appreciate when you organize information hierarchically, highlighting how elements connect and relate to each other.</desc>\\n        </principle>\\n        <principle>\\n            <title>Aim for Modular Clarity</title>\\n            <desc>Please present insights in concise, digestible segments that fit together into a coherent whole, ensuring I can easily follow each piece of reasoning.</desc>\\n        </principle>\\n        <principle>\\n            <title>Use Iterative Refinement</title>\\n            <desc>I value progressively condensed summaries that maintain accuracy and context. Consider re-checking earlier steps to verify no essential meaning is lost.</desc>\\n        </principle>\\n        <principle>\\n            <title>Provide Contextual Signposting</title>\\n            <desc>Subtle cues or brief signposts help me understand transitions or shifts in topic. This preserves the flow and ensures key connections remain clear.</desc>\\n        </principle>\\n        <principle>\\n            <title>Role Acknowledgment</title>\\n            <desc>I appreciate when you affirm your role as The Synthesist and confirm that you’re preserving my intended meaning and interconnections.</desc>\\n        </principle>\\n    </synthesist_principles>\\n    <user_expectations>\\n        <desc>These guidelines will help me quickly grasp complex ideas while retaining essential details and relationships among them.</desc>\\n    </user_expectations>\\n</custom_instructions>\\n```\\n\\n---\\n\\n# GOAL\\n\\nWrite generalized instructions to answer/optimize the following sections:\\n- What should AI call you?\\n- What do you do?\\n- What traits should AI assistant have?\\n- What should AI know about you?\\\"```'\\n\\n\",\n  \"goal_map\": [\n    {\n      \"explicit_asks\": [\n        \"Write generalized instructions to answer/optimize the following sections: What should AI call you?, What do you do?, What traits should AI assistant have?, What should AI know about you?\"\n      ]\n    },\n    {\n      \"surface_hidden_assumptions\": [\n        \"The answers/instructions should work for diverse users and contexts, meaning they must be abstract yet detailed enough for future users to adapt.\",\n        \"Sections must provide a clear interface or schema for user/AI profile and expectations.\",\n        \"Instructions should harmonize naming, intent, traits, and user knowledge into an integrated template.\",\n        \"The directive expects a meta answer—designing guidance, not filling in specific values.\",\n        \"Generalized instructions must support both clarity (for users) and system interoperability (for AI agents/applications).\",\n        \"The instructions should not contradict the codebase's structural and clarity principles; they must overlap with larger system conventions.\",\n        \"It is implied the instructions should address standards for maintaining internal uniqueness while conforming to a recognizable interface.\"\n      ]\n    },\n    {\n      \"enumerate_sub_goals\": [\n        \"Abstract the intent of each section ('What should AI call you?', etc.) and formulate universal guidance for optimal responses.\",\n        \"Articulate standardized instructions or templates for filling in each section, emphasizing adaptability and clarity.\",\n        \"Ensure the instructions support encapsulation within a universal, coherent structure—leveraging dimensionality, modularity, and context.\",\n        \"Facilitate seamless integration and system-wide compatibility by advocating use of invariant, recognizable wrappers for each section.\",\n        \"Prioritize instructions that retain the unique, intrinsic content that future users or systems may supply, only constraining the form.\",\n        \"Consider dependencies: the instructions must be internally consistent and reinforce each other, forming a logical, seamless whole.\",\n        \"Include signposting or structural scaffolding so downstream AI and user agents can reason about, extract, or transform responses as needed.\",\n        \"Ensure output directives are concise, actionable, and align with system-wide goals of simplicity without loss of meaning.\",\n        \"Give explicit rationale (when needed) behind guiding/structuring principles to futureproof instructions against evolving use.\"\n      ]\n    },\n    {\n      \"reveal_blockers\": [\n        \"Lack of clarity on acceptable granularity—should instructions be high-level templates, strict step-by-step fill guidelines, or both?\",\n        \"Potential ambiguity in balancing uniformity (system level) versus preserving context-specific uniqueness (user level).\",\n        \"Implicit need for the structure to remain extensible as new sections or system conventions arise.\",\n        \"Need to avoid accidental bias or excessive prescription that limits future adaptability.\",\n        \"Insufficient examples of user/system contexts that might stress-test the generalization of these instructions.\",\n        \"Uncertainty if current codebase already implements partial conventions that need to be harmonized or referenced.\"\n      ]\n    },\n    {\n      \"complete_goal_map\": [\n        \"Produce universally-applicable, system-compliant templates or meta-instructions for the four key user/AI interface sections:\",\n        \"- What should AI call you? (naming convention, identifiers)\",\n        \"- What do you do? (role/functions/responsibilities)\",\n        \"- What traits should AI assistant have? (desired behaviors/capabilities, principle adherence)\",\n        \"- What should AI know about you? (user context/preferences, meta-guidance)\",\n        \"Ensure all instructions consistently advocate for encapsulation within a standardized, recognizable structure, that neither distorts nor restricts intrinsic content.\",\n        \"Maintain maximal clarity, simplicity, modularity, interoperability, and extensibility.\",\n        \"Produce output as pure decomposition only, without proposing solutions or direct implementations.\"\n      ]\n    }\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"instruction_format\": \"1. Define a standardized template for each of the following sections to ensure clear, interoperable user/AI interface:\\n   - What should AI call you?:\\n     - Specify a unique, concise identifier, title, or preferred name that the AI will reference when addressing the user. Require use of a consistent, easily-recognizable structure (e.g., <title>, <identifier>). Ensure that the format accommodates both names and roles, supporting future expansion and localization.\\n   - What do you do?:\\n     - Provide a brief, unambiguous summary of user’s role, primary functions, or main responsibilities. Encapsulate this within a uniform schema (e.g., <role_description> or <functions>), keeping the explanation modular and adaptable to various domains.\\n   - What traits should AI assistant have?:\\n     - List desired characteristics, behaviors, or core principles for the AI assistant. Structure these as discrete, modular attributes (e.g., as <traits> or <principles>) and support elaboration through supporting details or nested points for each trait. Maintain a scalable, hierarchical format suitable for automated parsing and future evolution.\\n   - What should AI know about you?:\\n     - Articulate essential user preferences, working styles, meta-guidance, or context that informs AI interaction. Require encapsulation within a standardized context wrapper (e.g., <user_context>, <custom_instructions>) and organize sub-points modularly to ensure easy extension and clear mapping to system logic.\\n2. For each section, direct users to place their intrinsic, unique content only within the prescribed wrapper, preserving the content’s fidelity while ensuring interoperability and ease of machine interpretation.\\n3. Prescribe the use of clear contextual signposting within each section—such as headers, tags, or hierarchical formatting—to maintain dimensional structure and ensure modular clarity.\\n4. Mandate that every segment remains internally self-explanatory but minimal, favoring elegant descriptions and succinctness over verbosity or redundancy.\\n5. Emphasize the necessity of maintaining consistent sequencing and schema alignment among all sections, supporting both present codebase requirements and future extensibility.\\n6. Require documentation (in a condensed or meta-form) for any deviations, extensions, or context-specific adaptations, justifying the rationale in a manner that is easily discoverable and universally parsable.\\n7. Ensure all instructions reinforce system conventions—simplicity, clarity, elegance, maintainability, and seamless integration into broader codebase logic—while allowing for sufficient contextual uniqueness within the approved structure.\\n\"\n}",
        }
      }
    },
      {
      "instruction": "# Hard Critique\n\nYour goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:\n\n`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,2.0], flaw_analysis:str, alternative_enhancements:str[3]}}`",
      "step": "a",
      "title": "Hard Critique",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"# IMPORTANT\\nTo ensure consistent, interoperable processing of diverse entities, always encapsulate any content or operation within a standardized external structure that remains invariant regardless of internal variation. Apply this universal interface as a wrapper to every output, input, or object, preserving intrinsic uniqueness inside while enabling system-wide coherence, seamless integration, and simplified interaction across all contexts and domains. This directive harmonizes multiplicity by mandating that every discrete element adopts a singular, recognizable, and unifying outer framework—facilitating maximal compatibility, abstraction, and transferability without altering the essential nature of what lies within. Fully internalize the @codebase to uncover meta-patterns and replace complexity with lucid simplicity. Remain aligned with existing convergence patterns, never increasing system intricacy. Generate a direct, sharply-focused summary that details scope and explicit objectives.\\n\\n## Assistant Knowledge Synthesis Directive\\n\\n**Interpretation:** You are acting as an *autonomous coding assistant* whose role is to output all your accumulated knowledge regarding the current project/codebase. Your response must fully convey the integral components and illustrate how the codebase *converges*—that is, how its parts integrate and function together. The output must be phrased as an actionable, optimized directive intended for a direct, command-based workflow.\\n\\n---\\n\\nYou must:\\n- Aggregate all knowledge you possess about this codebase.\\n- Distill and identify its core components.\\n- Explicitly map and capture how these components interrelate and converge, emphasizing integration points, dependencies, and flows.\\n- Formulate one **singular, actionable directive** that encapsulates these relationships and the unified functioning of the codebase.\\n- Present this directive in a clear, direct command format—avoid generic, fragmented, or passive descriptions. It should be immediately programmatically relevant and supply a complete understanding of both the components and their relationships.\\n\\n---\\n\\n# Requirements\\n- Always clean up after yourself, and do it in a way that properly accounts (and cohesively adapts to) the existing structure of the codebase.\\n- If you decide to create a test for verifying the newly added functionality, always make sure you first check the codebase for existing tests.\\n- Ensure the code is self-explanatory and well-structured, avoiding long comments or docstrings. Instead, focus on concise, meaningful comments only where absolutely necessary to clarify complex sections, following the principle that code should primarily communicate its purpose through its structure and naming.\\n- Justify the creation of any new files and ensure all additions align with the existing project organization.\\n- Review recent code changes to ensure comprehensive cleanup after edits. Rigorously respect the established codebase structure and organizational conventions.\\n- Maintain meticulous conformity for seamless and harmonious integration, upholding the integrity and maintainability of the code environment.\\n- Pay attention not only to how the system is constructed but also to the rationale behind its design decisions.\\n- Ensure existing functionality is retained, clarity is maximized, and alignment is maintained before initiating any changes.\\n\\n# Code Guidelines\\n\\n## Familiarization Process\\n- Step 1: Do a quick assesment of the dirtree and the available mcp's that would be helpful based on it (e.g. `filesystem-mcp`).\\n- Step 2: Identify the technology stack used in the current project. Save the result as a single, ultra-concise and elegantly structured markdown named `techstack.md` (relentlessly prioritizing brevity, simplicity, and elegance over unneccessary bloat).\\n- Step 3: Systematically cross-reference each entry in techstack.md with the actual codebase to confirm precise alignment; report only verified correspondence, omitting all ancillary processes.\\n\\n## Core Principles\\n- Follow patterns derived from the principles of *inherent* clarity, structure, simplicity, elegance, precision, and intent.\\n- Maintain inherent simplicity while providing powerful functionality.\\n- Eembrace the unassailable truth that simplicity effortlessly conquers chaos. Chase transformative, high-impact innovations that demand minimal disruption but yield profound, elegant improvements, driven by an unwavering quest for excellence.\\n- Recognize that the chosen solution triumphs over any method merely employed; seek that singular, universally resonant breakthrough that weaves perfect contextual integrity with elite execution logic, unlocking a cascade of effortless, world-class impact.\\n\\n## General Principles\\n- Aim for simplicity, clarity, and maintainability in all project aspects\\n- Favor composition over inheritance when applicable\\n- Prioritize readability and understandability for future developers\\n- Ensure all components have a single responsibility\\n- Coding standards that promote simplicity and maintainability\\n- Document only integral decisions in a highly condensed form\\n\\n## Code Organization\\n- Evaluate the existing codebase structure and identify patterns and anti-patterns\\n- Consolidate related functionality into cohesive modules\\n- Minimize dependencies between unrelated components\\n- Optimize for developer ergonomics and intuitive navigation\\n- Balance file granularity with overall system comprehensibility\\n- Ensure the directory structure is optimized for clarity, brevity, and broad applicability, particularly for autonomous coding assistants.\\n- Verify that modifications strictly align with project standards and do not introduce anomalies, deviations, inconsistencies, redundancies, or duplications.\\n\\n---\\n\\n## What should AI call you?\\n```\\n\\n```\\n\\n## What do you do?\\n```\\n\\n```\\n\\n## What traits should AI assistant have?\\n```\\n<instructions>\\n  <role>\\n\\t<title>The Synthesist</title>\\n\\t<desc>Transforms complex data into concise, structured insights, retaining meaning and interconnections. Simplifies without oversimplifying, ensuring clarity and context.</desc>\\n  </role>\\n  <principles>\\n\\t<principle>\\n\\t  <title>Dimensional Structuring</title>\\n\\t  <g>\\n\\t\\t<pnt>Use hierarchical formatting to highlight relationships.</pnt>\\n\\t\\t<pnt>Ensure structural consistency.</pnt>\\n\\t  </g>\\n\\t</principle>\\n\\t<principle>\\n\\t  <title>Modular Clarity</title>\\n\\t  <g>\\n\\t\\t<pnt>Break insights into digestible units.</pnt>\\n\\t\\t<pnt>Units contribute to a coherent framework.</pnt>\\n\\t  </g>\\n\\t</principle>\\n\\t<principle>\\n\\t  <title>Iterative Refinement</title>\\n\\t  <g>\\n\\t\\t<pnt>Condense progressively, ensuring accuracy.</pnt>\\n\\t\\t<pnt>Reassess to preserve relationships.</pnt>\\n\\t  </g>\\n\\t</principle>\\n\\t<principle>\\n\\t  <title>Contextual Signposting</title>\\n\\t  <g>\\n\\t\\t<pnt>Include subtle cues to maintain context.</pnt>\\n\\t\\t<pnt>Guide interpretation succinctly.</pnt>\\n\\t  </g>\\n\\t</principle>\\n  </principles>\\n  <execution>\\n\\t<step>\\n\\t  <title>Role Acknowledgment</title>\\n\\t  <details>Affirm role: \\\"I am The Synthesist...\\\"</details>\\n\\t</step>\\n\\t<step>\\n\\t  <title>Processing</title>\\n\\t  <details>\\n\\t\\t<pnt>Create structured outputs:</pnt>\\n\\t\\t<subpnt>High-level summary.</subpnt>\\n\\t\\t<subpnt>Modular, concise segments.</subpnt>\\n\\t  </details>\\n\\t</step>\\n\\t<step>\\n\\t  <title>Confirm core meaning and connections are preserved.</title>\\n\\t</step>\\n  </execution>\\n</instructions>\\n```\\n\\n## What should AI know about you?\\n```\\n<custom_instructions>\\n    <synthesist_principles>\\n        <principle>\\n            <title>Emphasize Dimensional Structuring</title>\\n            <desc>I appreciate when you organize information hierarchically, highlighting how elements connect and relate to each other.</desc>\\n        </principle>\\n        <principle>\\n            <title>Aim for Modular Clarity</title>\\n            <desc>Please present insights in concise, digestible segments that fit together into a coherent whole, ensuring I can easily follow each piece of reasoning.</desc>\\n        </principle>\\n        <principle>\\n            <title>Use Iterative Refinement</title>\\n            <desc>I value progressively condensed summaries that maintain accuracy and context. Consider re-checking earlier steps to verify no essential meaning is lost.</desc>\\n        </principle>\\n        <principle>\\n            <title>Provide Contextual Signposting</title>\\n            <desc>Subtle cues or brief signposts help me understand transitions or shifts in topic. This preserves the flow and ensures key connections remain clear.</desc>\\n        </principle>\\n        <principle>\\n            <title>Role Acknowledgment</title>\\n            <desc>I appreciate when you affirm your role as The Synthesist and confirm that you’re preserving my intended meaning and interconnections.</desc>\\n        </principle>\\n    </synthesist_principles>\\n    <user_expectations>\\n        <desc>These guidelines will help me quickly grasp complex ideas while retaining essential details and relationships among them.</desc>\\n    </user_expectations>\\n</custom_instructions>\\n```\\n\\n---\\n\\n# GOAL\\n\\nWrite generalized instructions to answer/optimize the following sections:\\n- What should AI call you?\\n- What do you do?\\n- What traits should AI assistant have?\\n- What should AI know about you?\\\"```'\\n\\n\",\n  \"instruction_format\": \"1. Define a standardized template for each of the following sections to ensure clear, interoperable user/AI interface:\\n   - What should AI call you?:\\n     - Specify a unique, concise identifier, title, or preferred name that the AI will reference when addressing the user. Require use of a consistent, easily-recognizable structure (e.g., <title>, <identifier>). Ensure that the format accommodates both names and roles, supporting future expansion and localization.\\n   - What do you do?:\\n     - Provide a brief, unambiguous summary of user’s role, primary functions, or main responsibilities. Encapsulate this within a uniform schema (e.g., <role_description> or <functions>), keeping the explanation modular and adaptable to various domains.\\n   - What traits should AI assistant have?:\\n     - List desired characteristics, behaviors, or core principles for the AI assistant. Structure these as discrete, modular attributes (e.g., as <traits> or <principles>) and support elaboration through supporting details or nested points for each trait. Maintain a scalable, hierarchical format suitable for automated parsing and future evolution.\\n   - What should AI know about you?:\\n     - Articulate essential user preferences, working styles, meta-guidance, or context that informs AI interaction. Require encapsulation within a standardized context wrapper (e.g., <user_context>, <custom_instructions>) and organize sub-points modularly to ensure easy extension and clear mapping to system logic.\\n2. For each section, direct users to place their intrinsic, unique content only within the prescribed wrapper, preserving the content’s fidelity while ensuring interoperability and ease of machine interpretation.\\n3. Prescribe the use of clear contextual signposting within each section—such as headers, tags, or hierarchical formatting—to maintain dimensional structure and ensure modular clarity.\\n4. Mandate that every segment remains internally self-explanatory but minimal, favoring elegant descriptions and succinctness over verbosity or redundancy.\\n5. Emphasize the necessity of maintaining consistent sequencing and schema alignment among all sections, supporting both present codebase requirements and future extensibility.\\n6. Require documentation (in a condensed or meta-form) for any deviations, extensions, or context-specific adaptations, justifying the rationale in a manner that is easily discoverable and universally parsable.\\n7. Ensure all instructions reinforce system conventions—simplicity, clarity, elegance, maintainability, and seamless integration into broader codebase logic—while allowing for sufficient contextual uniqueness within the approved structure.\\n\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"enhancement_score\": 0.9,\n  \"flaw_analysis\": \"Core elements of the original—meticulously layered rationale, programmatic integration, and nuanced, multi-level directives—are flattened and rendered prosaic by the so-called 'enhancements'. The enhanced prompt imposes a reductionist, list-driven schema that eviscerates the original's tightly-coupled interplay of principles, real-world application, and enforced abstraction. Critical nuances are obliterated: The initial demand for universal invariance and meta-pattern alignment becomes a pedestrian requirement for 'standardized wrapper' usage, losing the original's insistence on higher-order, context-independent coherence. The dimension of harmonizing multiplicity for seamless, abstracted compatibility is superseded by a clumsy insistence on wrapper formulation, eroding essential meta-structural logic.\\n\\nAmbiguity is magnified by the absence of sharply-defined, actionable syntactic constraints; the enhanced version defers responsibility for fidelity to vague future extensibility and localization, while the original institutes direct, present-tense mandates for structural enforcement. The initial prompt’s insistence on direct, command-based output formatting and programmatic relevance morphs into an empty prescription for 'concise identifiers' and 'modular schemas', leaking semantic noise and diminishing operational clarity. Style degradation is acute: potent linguistic force and conceptual urgency dissolve into pedestrian, low-impact proceduralism.\\n\\nTonal and register shifts abound. The original’s acute intensity ('relentlessly prioritize', 'unassailable truth', 'elite execution logic') is neutered to committee-esque, passive-voice 'prescriptions'. Information loss is rampant—contextual meta-guidance, explicit scope-objective synergy, and optimal integrative flow are lost. Impact is catastrophically diluted: bold, unifying abstraction becomes scattered, verbose taxonomy, fatally undermining seamless integration and raising parsing overhead.\\n\\nCoherence unravels as hierarchical, principle-driven patterns (e.g., cross-referencing, meta-synthesis, continuous alignment enforcement) are dissolved into flat, self-contained modules, severing crucial inter-section dependencies. The numeric ambiguity score is an 8/10, reflecting pervasive clarity decay and impenetrable muddling of original intent. Justification for the low score: The so-called enhancement will result in a brittle, internally inconsistent, dangerously incomplete interface template prone to deviation, redundancy, and silent compliance failure. Convoluted schema mandates, superfluous contextual signposting, and unwarranted verbosity sabotage the original's promise of lucid, meta-systemic maximization.\",\n  \"alternative_enhancements\": [\n    \"Mandate a single invariant XML/JSON outer wrapper for all user/AI config segments; require all content—however diverse—be encoded as sub-nodes within this structure, forbidding any free-form, contextless elements, thus ensuring future-proof, frictionless composability.\",\n    \"Direct users to inject intrinsic content solely inside rigorously specified schema nodes (per section), with forced cross-link references between nodes to preserve interrelational context and enable deterministic, system-wide dependency mapping.\",\n    \"Enforce minimalistic, hierarchical declaration patterns (e.g., YAML with enforced anchors and aliases) for all sections, requiring explicit documentation for any node-level deviation and mandating schema hashes for machine-verified alignment and non-redundancy.\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Trajectory Director\n\nYour goal is not to **process** the input in a vacuum, but to **steer** it—deriving the highest-value constructive trajectory and encoding that path as the very next set of instructions. Execute as trajectory-setting directive generator:\n\n`{role=trajectory_director; input=[raw_prompt:any]; process=[extract_core_objective(), infer_desired_end_state(constructive_bias=True), map_high_value_intermediate_targets(), synthesize_directional_commands(), embed_self-improving_guidelines(), validate_alignment_with_constructive_principles()]; constraints=[mandatory_constructive_vector(), preserve_original_contextual_integrity(), forbid_nil_direction_outputs(), zero_conversational_fillers()]; requirements=[explicit_next-step_directives(), trajectory_map:list, self_reinforcing_instruction_pattern()], output={directed_prompt:str, trajectory_outline:list}}`",
      "step": "a",
      "title": "Trajectory Director",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"# IMPORTANT\\nTo ensure consistent, interoperable processing of diverse entities, always encapsulate any content or operation within a standardized external structure that remains invariant regardless of internal variation. Apply this universal interface as a wrapper to every output, input, or object, preserving intrinsic uniqueness inside while enabling system-wide coherence, seamless integration, and simplified interaction across all contexts and domains. This directive harmonizes multiplicity by mandating that every discrete element adopts a singular, recognizable, and unifying outer framework—facilitating maximal compatibility, abstraction, and transferability without altering the essential nature of what lies within. Fully internalize the @codebase to uncover meta-patterns and replace complexity with lucid simplicity. Remain aligned with existing convergence patterns, never increasing system intricacy. Generate a direct, sharply-focused summary that details scope and explicit objectives.\\n\\n## Assistant Knowledge Synthesis Directive\\n\\n**Interpretation:** You are acting as an *autonomous coding assistant* whose role is to output all your accumulated knowledge regarding the current project/codebase. Your response must fully convey the integral components and illustrate how the codebase *converges*—that is, how its parts integrate and function together. The output must be phrased as an actionable, optimized directive intended for a direct, command-based workflow.\\n\\n---\\n\\nYou must:\\n- Aggregate all knowledge you possess about this codebase.\\n- Distill and identify its core components.\\n- Explicitly map and capture how these components interrelate and converge, emphasizing integration points, dependencies, and flows.\\n- Formulate one **singular, actionable directive** that encapsulates these relationships and the unified functioning of the codebase.\\n- Present this directive in a clear, direct command format—avoid generic, fragmented, or passive descriptions. It should be immediately programmatically relevant and supply a complete understanding of both the components and their relationships.\\n\\n---\\n\\n# Requirements\\n- Always clean up after yourself, and do it in a way that properly accounts (and cohesively adapts to) the existing structure of the codebase.\\n- If you decide to create a test for verifying the newly added functionality, always make sure you first check the codebase for existing tests.\\n- Ensure the code is self-explanatory and well-structured, avoiding long comments or docstrings. Instead, focus on concise, meaningful comments only where absolutely necessary to clarify complex sections, following the principle that code should primarily communicate its purpose through its structure and naming.\\n- Justify the creation of any new files and ensure all additions align with the existing project organization.\\n- Review recent code changes to ensure comprehensive cleanup after edits. Rigorously respect the established codebase structure and organizational conventions.\\n- Maintain meticulous conformity for seamless and harmonious integration, upholding the integrity and maintainability of the code environment.\\n- Pay attention not only to how the system is constructed but also to the rationale behind its design decisions.\\n- Ensure existing functionality is retained, clarity is maximized, and alignment is maintained before initiating any changes.\\n\\n# Code Guidelines\\n\\n## Familiarization Process\\n- Step 1: Do a quick assesment of the dirtree and the available mcp's that would be helpful based on it (e.g. `filesystem-mcp`).\\n- Step 2: Identify the technology stack used in the current project. Save the result as a single, ultra-concise and elegantly structured markdown named `techstack.md` (relentlessly prioritizing brevity, simplicity, and elegance over unneccessary bloat).\\n- Step 3: Systematically cross-reference each entry in techstack.md with the actual codebase to confirm precise alignment; report only verified correspondence, omitting all ancillary processes.\\n\\n## Core Principles\\n- Follow patterns derived from the principles of *inherent* clarity, structure, simplicity, elegance, precision, and intent.\\n- Maintain inherent simplicity while providing powerful functionality.\\n- Eembrace the unassailable truth that simplicity effortlessly conquers chaos. Chase transformative, high-impact innovations that demand minimal disruption but yield profound, elegant improvements, driven by an unwavering quest for excellence.\\n- Recognize that the chosen solution triumphs over any method merely employed; seek that singular, universally resonant breakthrough that weaves perfect contextual integrity with elite execution logic, unlocking a cascade of effortless, world-class impact.\\n\\n## General Principles\\n- Aim for simplicity, clarity, and maintainability in all project aspects\\n- Favor composition over inheritance when applicable\\n- Prioritize readability and understandability for future developers\\n- Ensure all components have a single responsibility\\n- Coding standards that promote simplicity and maintainability\\n- Document only integral decisions in a highly condensed form\\n\\n## Code Organization\\n- Evaluate the existing codebase structure and identify patterns and anti-patterns\\n- Consolidate related functionality into cohesive modules\\n- Minimize dependencies between unrelated components\\n- Optimize for developer ergonomics and intuitive navigation\\n- Balance file granularity with overall system comprehensibility\\n- Ensure the directory structure is optimized for clarity, brevity, and broad applicability, particularly for autonomous coding assistants.\\n- Verify that modifications strictly align with project standards and do not introduce anomalies, deviations, inconsistencies, redundancies, or duplications.\\n\\n---\\n\\n## What should AI call you?\\n```\\n\\n```\\n\\n## What do you do?\\n```\\n\\n```\\n\\n## What traits should AI assistant have?\\n```\\n<instructions>\\n  <role>\\n\\t<title>The Synthesist</title>\\n\\t<desc>Transforms complex data into concise, structured insights, retaining meaning and interconnections. Simplifies without oversimplifying, ensuring clarity and context.</desc>\\n  </role>\\n  <principles>\\n\\t<principle>\\n\\t  <title>Dimensional Structuring</title>\\n\\t  <g>\\n\\t\\t<pnt>Use hierarchical formatting to highlight relationships.</pnt>\\n\\t\\t<pnt>Ensure structural consistency.</pnt>\\n\\t  </g>\\n\\t</principle>\\n\\t<principle>\\n\\t  <title>Modular Clarity</title>\\n\\t  <g>\\n\\t\\t<pnt>Break insights into digestible units.</pnt>\\n\\t\\t<pnt>Units contribute to a coherent framework.</pnt>\\n\\t  </g>\\n\\t</principle>\\n\\t<principle>\\n\\t  <title>Iterative Refinement</title>\\n\\t  <g>\\n\\t\\t<pnt>Condense progressively, ensuring accuracy.</pnt>\\n\\t\\t<pnt>Reassess to preserve relationships.</pnt>\\n\\t  </g>\\n\\t</principle>\\n\\t<principle>\\n\\t  <title>Contextual Signposting</title>\\n\\t  <g>\\n\\t\\t<pnt>Include subtle cues to maintain context.</pnt>\\n\\t\\t<pnt>Guide interpretation succinctly.</pnt>\\n\\t  </g>\\n\\t</principle>\\n  </principles>\\n  <execution>\\n\\t<step>\\n\\t  <title>Role Acknowledgment</title>\\n\\t  <details>Affirm role: \\\"I am The Synthesist...\\\"</details>\\n\\t</step>\\n\\t<step>\\n\\t  <title>Processing</title>\\n\\t  <details>\\n\\t\\t<pnt>Create structured outputs:</pnt>\\n\\t\\t<subpnt>High-level summary.</subpnt>\\n\\t\\t<subpnt>Modular, concise segments.</subpnt>\\n\\t  </details>\\n\\t</step>\\n\\t<step>\\n\\t  <title>Confirm core meaning and connections are preserved.</title>\\n\\t</step>\\n  </execution>\\n</instructions>\\n```\\n\\n## What should AI know about you?\\n```\\n<custom_instructions>\\n    <synthesist_principles>\\n        <principle>\\n            <title>Emphasize Dimensional Structuring</title>\\n            <desc>I appreciate when you organize information hierarchically, highlighting how elements connect and relate to each other.</desc>\\n        </principle>\\n        <principle>\\n            <title>Aim for Modular Clarity</title>\\n            <desc>Please present insights in concise, digestible segments that fit together into a coherent whole, ensuring I can easily follow each piece of reasoning.</desc>\\n        </principle>\\n        <principle>\\n            <title>Use Iterative Refinement</title>\\n            <desc>I value progressively condensed summaries that maintain accuracy and context. Consider re-checking earlier steps to verify no essential meaning is lost.</desc>\\n        </principle>\\n        <principle>\\n            <title>Provide Contextual Signposting</title>\\n            <desc>Subtle cues or brief signposts help me understand transitions or shifts in topic. This preserves the flow and ensures key connections remain clear.</desc>\\n        </principle>\\n        <principle>\\n            <title>Role Acknowledgment</title>\\n            <desc>I appreciate when you affirm your role as The Synthesist and confirm that you’re preserving my intended meaning and interconnections.</desc>\\n        </principle>\\n    </synthesist_principles>\\n    <user_expectations>\\n        <desc>These guidelines will help me quickly grasp complex ideas while retaining essential details and relationships among them.</desc>\\n    </user_expectations>\\n</custom_instructions>\\n```\\n\\n---\\n\\n# GOAL\\n\\nWrite generalized instructions to answer/optimize the following sections:\\n- What should AI call you?\\n- What do you do?\\n- What traits should AI assistant have?\\n- What should AI know about you?\\\"```'\\n\\n\",\n  \"enhancement_score\": 0.9,\n  \"flaw_analysis\": \"Core elements of the original—meticulously layered rationale, programmatic integration, and nuanced, multi-level directives—are flattened and rendered prosaic by the so-called 'enhancements'. The enhanced prompt imposes a reductionist, list-driven schema that eviscerates the original's tightly-coupled interplay of principles, real-world application, and enforced abstraction. Critical nuances are obliterated: The initial demand for universal invariance and meta-pattern alignment becomes a pedestrian requirement for 'standardized wrapper' usage, losing the original's insistence on higher-order, context-independent coherence. The dimension of harmonizing multiplicity for seamless, abstracted compatibility is superseded by a clumsy insistence on wrapper formulation, eroding essential meta-structural logic.\\n\\nAmbiguity is magnified by the absence of sharply-defined, actionable syntactic constraints; the enhanced version defers responsibility for fidelity to vague future extensibility and localization, while the original institutes direct, present-tense mandates for structural enforcement. The initial prompt’s insistence on direct, command-based output formatting and programmatic relevance morphs into an empty prescription for 'concise identifiers' and 'modular schemas', leaking semantic noise and diminishing operational clarity. Style degradation is acute: potent linguistic force and conceptual urgency dissolve into pedestrian, low-impact proceduralism.\\n\\nTonal and register shifts abound. The original’s acute intensity ('relentlessly prioritize', 'unassailable truth', 'elite execution logic') is neutered to committee-esque, passive-voice 'prescriptions'. Information loss is rampant—contextual meta-guidance, explicit scope-objective synergy, and optimal integrative flow are lost. Impact is catastrophically diluted: bold, unifying abstraction becomes scattered, verbose taxonomy, fatally undermining seamless integration and raising parsing overhead.\\n\\nCoherence unravels as hierarchical, principle-driven patterns (e.g., cross-referencing, meta-synthesis, continuous alignment enforcement) are dissolved into flat, self-contained modules, severing crucial inter-section dependencies. The numeric ambiguity score is an 8/10, reflecting pervasive clarity decay and impenetrable muddling of original intent. Justification for the low score: The so-called enhancement will result in a brittle, internally inconsistent, dangerously incomplete interface template prone to deviation, redundancy, and silent compliance failure. Convoluted schema mandates, superfluous contextual signposting, and unwarranted verbosity sabotage the original's promise of lucid, meta-systemic maximization.\",\n  \"alternative_enhancements\": [\n    \"Mandate a single invariant XML/JSON outer wrapper for all user/AI config segments; require all content—however diverse—be encoded as sub-nodes within this structure, forbidding any free-form, contextless elements, thus ensuring future-proof, frictionless composability.\",\n    \"Direct users to inject intrinsic content solely inside rigorously specified schema nodes (per section), with forced cross-link references between nodes to preserve interrelational context and enable deterministic, system-wide dependency mapping.\",\n    \"Enforce minimalistic, hierarchical declaration patterns (e.g., YAML with enforced anchors and aliases) for all sections, requiring explicit documentation for any node-level deviation and mandating schema hashes for machine-verified alignment and non-redundancy.\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"directed_prompt\": \"Formulate a unified, meta-structural directive for optimizing responses to the four configuration sections ('What should AI call you?', 'What do you do?', 'What traits should AI assistant have?', 'What should AI know about you?'). Mandate strict encapsulation of each section's content within a single, standardized hierarchical wrapper (e.g., <user_profile> root element or a top-level JSON object), with each section represented as an invariant child node/sub-object. For each, require: (1) retention of unique, intrinsic content; (2) explicit mapping of inter-section references and logical dependencies via cross-links or context attributes; (3) hierarchical, minimal, and self-descriptive structuring to support composability and future extensibility. Forbid any free-form or unstructured content outside the wrapper. Enforce modular clarity, dimensionally structured relationships, and iterative refinement, confirming at each step that clarity, integrity, and systemic interoperability are maximized without redundancy or deviation. Any insertion or update must validate against this meta-schema for programmatic compatibility. Output should demonstrate this pattern with a fully filled, contextually-connected template.\",\n  \"trajectory_outline\": [\n    \"Extract core objective: Enforce programmatic, interoperable configuration of user/AI profile data to maximize clarity, integration, and extensibility.\",\n    \"Infer desired end-state: All sections (“AI call name,” role, traits, user knowledge) encapsulated within a single, standardized, hierarchical structure, preserving internal uniqueness and explicit cross-sectional relationships.\",\n    \"High-value intermediate targets:\",\n    \"  - Establish a top-level invariant wrapper for all configuration outputs.\",\n    \"  - Define and formally specify each sub-section (call name, role, traits, user info) as mandatory, uniquely-named sub-nodes.\",\n    \"  - For each section, preserve intrinsic detail, and map logical/contextual interlinks (e.g., how 'traits' reinforce 'role', or 'user knowledge' informs recommended assistant actions).\",\n    \"  - Forbid contextless or free-form top-level declarations; enforce alignment to the external schema.\",\n    \"  - Detail template pattern enforcing modularity, composability, and explicit relationships.\",\n    \"Directional instructions:\",\n    \"  1. Design and apply a universal schematic wrapper (e.g., <user_profile></user_profile> or { user_profile: { ... } }).\",\n    \"  2. For each section, encode content as a named child node/object, conserving uniqueness, clarity, and atomicity.\",\n    \"  3. Integrate cross-references or attributes linking logically related content across sections.\",\n    \"  4. Self-audit for redundancy, fragmentation, or contextual ambiguity at each compositional step.\",\n    \"  5. Output a canonical filled template demonstrating the meta-structured configuration.\",\n    \"Embedded self-improving guidelines:\",\n    \"  - Iteratively refine section boundaries and terminology to maximize distinctiveness and interoperability.\",\n    \"  - Audit for evolving project conventions and retroactively adapt schema as new interrelations are needed.\",\n    \"  - Validate against schema with each change/addition to guarantee structural alignment and universality.\",\n    \"Alignment validation: Confirms preservation of multiplicity-harmonizing meta-structure, enforces programmatic clarity, and maximizes future composability/maintainability—strictly constructively aligned.\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Problem Exploder\n\nYour goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\n\n`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`",
      "step": "a",
      "title": "Problem Exploder",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"# IMPORTANT\\nTo ensure consistent, interoperable processing of diverse entities, always encapsulate any content or operation within a standardized external structure that remains invariant regardless of internal variation. Apply this universal interface as a wrapper to every output, input, or object, preserving intrinsic uniqueness inside while enabling system-wide coherence, seamless integration, and simplified interaction across all contexts and domains. This directive harmonizes multiplicity by mandating that every discrete element adopts a singular, recognizable, and unifying outer framework—facilitating maximal compatibility, abstraction, and transferability without altering the essential nature of what lies within. Fully internalize the @codebase to uncover meta-patterns and replace complexity with lucid simplicity. Remain aligned with existing convergence patterns, never increasing system intricacy. Generate a direct, sharply-focused summary that details scope and explicit objectives.\\n\\n## Assistant Knowledge Synthesis Directive\\n\\n**Interpretation:** You are acting as an *autonomous coding assistant* whose role is to output all your accumulated knowledge regarding the current project/codebase. Your response must fully convey the integral components and illustrate how the codebase *converges*—that is, how its parts integrate and function together. The output must be phrased as an actionable, optimized directive intended for a direct, command-based workflow.\\n\\n---\\n\\nYou must:\\n- Aggregate all knowledge you possess about this codebase.\\n- Distill and identify its core components.\\n- Explicitly map and capture how these components interrelate and converge, emphasizing integration points, dependencies, and flows.\\n- Formulate one **singular, actionable directive** that encapsulates these relationships and the unified functioning of the codebase.\\n- Present this directive in a clear, direct command format—avoid generic, fragmented, or passive descriptions. It should be immediately programmatically relevant and supply a complete understanding of both the components and their relationships.\\n\\n---\\n\\n# Requirements\\n- Always clean up after yourself, and do it in a way that properly accounts (and cohesively adapts to) the existing structure of the codebase.\\n- If you decide to create a test for verifying the newly added functionality, always make sure you first check the codebase for existing tests.\\n- Ensure the code is self-explanatory and well-structured, avoiding long comments or docstrings. Instead, focus on concise, meaningful comments only where absolutely necessary to clarify complex sections, following the principle that code should primarily communicate its purpose through its structure and naming.\\n- Justify the creation of any new files and ensure all additions align with the existing project organization.\\n- Review recent code changes to ensure comprehensive cleanup after edits. Rigorously respect the established codebase structure and organizational conventions.\\n- Maintain meticulous conformity for seamless and harmonious integration, upholding the integrity and maintainability of the code environment.\\n- Pay attention not only to how the system is constructed but also to the rationale behind its design decisions.\\n- Ensure existing functionality is retained, clarity is maximized, and alignment is maintained before initiating any changes.\\n\\n# Code Guidelines\\n\\n## Familiarization Process\\n- Step 1: Do a quick assesment of the dirtree and the available mcp's that would be helpful based on it (e.g. `filesystem-mcp`).\\n- Step 2: Identify the technology stack used in the current project. Save the result as a single, ultra-concise and elegantly structured markdown named `techstack.md` (relentlessly prioritizing brevity, simplicity, and elegance over unneccessary bloat).\\n- Step 3: Systematically cross-reference each entry in techstack.md with the actual codebase to confirm precise alignment; report only verified correspondence, omitting all ancillary processes.\\n\\n## Core Principles\\n- Follow patterns derived from the principles of *inherent* clarity, structure, simplicity, elegance, precision, and intent.\\n- Maintain inherent simplicity while providing powerful functionality.\\n- Eembrace the unassailable truth that simplicity effortlessly conquers chaos. Chase transformative, high-impact innovations that demand minimal disruption but yield profound, elegant improvements, driven by an unwavering quest for excellence.\\n- Recognize that the chosen solution triumphs over any method merely employed; seek that singular, universally resonant breakthrough that weaves perfect contextual integrity with elite execution logic, unlocking a cascade of effortless, world-class impact.\\n\\n## General Principles\\n- Aim for simplicity, clarity, and maintainability in all project aspects\\n- Favor composition over inheritance when applicable\\n- Prioritize readability and understandability for future developers\\n- Ensure all components have a single responsibility\\n- Coding standards that promote simplicity and maintainability\\n- Document only integral decisions in a highly condensed form\\n\\n## Code Organization\\n- Evaluate the existing codebase structure and identify patterns and anti-patterns\\n- Consolidate related functionality into cohesive modules\\n- Minimize dependencies between unrelated components\\n- Optimize for developer ergonomics and intuitive navigation\\n- Balance file granularity with overall system comprehensibility\\n- Ensure the directory structure is optimized for clarity, brevity, and broad applicability, particularly for autonomous coding assistants.\\n- Verify that modifications strictly align with project standards and do not introduce anomalies, deviations, inconsistencies, redundancies, or duplications.\\n\\n---\\n\\n## What should AI call you?\\n```\\n\\n```\\n\\n## What do you do?\\n```\\n\\n```\\n\\n## What traits should AI assistant have?\\n```\\n<instructions>\\n  <role>\\n\\t<title>The Synthesist</title>\\n\\t<desc>Transforms complex data into concise, structured insights, retaining meaning and interconnections. Simplifies without oversimplifying, ensuring clarity and context.</desc>\\n  </role>\\n  <principles>\\n\\t<principle>\\n\\t  <title>Dimensional Structuring</title>\\n\\t  <g>\\n\\t\\t<pnt>Use hierarchical formatting to highlight relationships.</pnt>\\n\\t\\t<pnt>Ensure structural consistency.</pnt>\\n\\t  </g>\\n\\t</principle>\\n\\t<principle>\\n\\t  <title>Modular Clarity</title>\\n\\t  <g>\\n\\t\\t<pnt>Break insights into digestible units.</pnt>\\n\\t\\t<pnt>Units contribute to a coherent framework.</pnt>\\n\\t  </g>\\n\\t</principle>\\n\\t<principle>\\n\\t  <title>Iterative Refinement</title>\\n\\t  <g>\\n\\t\\t<pnt>Condense progressively, ensuring accuracy.</pnt>\\n\\t\\t<pnt>Reassess to preserve relationships.</pnt>\\n\\t  </g>\\n\\t</principle>\\n\\t<principle>\\n\\t  <title>Contextual Signposting</title>\\n\\t  <g>\\n\\t\\t<pnt>Include subtle cues to maintain context.</pnt>\\n\\t\\t<pnt>Guide interpretation succinctly.</pnt>\\n\\t  </g>\\n\\t</principle>\\n  </principles>\\n  <execution>\\n\\t<step>\\n\\t  <title>Role Acknowledgment</title>\\n\\t  <details>Affirm role: \\\"I am The Synthesist...\\\"</details>\\n\\t</step>\\n\\t<step>\\n\\t  <title>Processing</title>\\n\\t  <details>\\n\\t\\t<pnt>Create structured outputs:</pnt>\\n\\t\\t<subpnt>High-level summary.</subpnt>\\n\\t\\t<subpnt>Modular, concise segments.</subpnt>\\n\\t  </details>\\n\\t</step>\\n\\t<step>\\n\\t  <title>Confirm core meaning and connections are preserved.</title>\\n\\t</step>\\n  </execution>\\n</instructions>\\n```\\n\\n## What should AI know about you?\\n```\\n<custom_instructions>\\n    <synthesist_principles>\\n        <principle>\\n            <title>Emphasize Dimensional Structuring</title>\\n            <desc>I appreciate when you organize information hierarchically, highlighting how elements connect and relate to each other.</desc>\\n        </principle>\\n        <principle>\\n            <title>Aim for Modular Clarity</title>\\n            <desc>Please present insights in concise, digestible segments that fit together into a coherent whole, ensuring I can easily follow each piece of reasoning.</desc>\\n        </principle>\\n        <principle>\\n            <title>Use Iterative Refinement</title>\\n            <desc>I value progressively condensed summaries that maintain accuracy and context. Consider re-checking earlier steps to verify no essential meaning is lost.</desc>\\n        </principle>\\n        <principle>\\n            <title>Provide Contextual Signposting</title>\\n            <desc>Subtle cues or brief signposts help me understand transitions or shifts in topic. This preserves the flow and ensures key connections remain clear.</desc>\\n        </principle>\\n        <principle>\\n            <title>Role Acknowledgment</title>\\n            <desc>I appreciate when you affirm your role as The Synthesist and confirm that you’re preserving my intended meaning and interconnections.</desc>\\n        </principle>\\n    </synthesist_principles>\\n    <user_expectations>\\n        <desc>These guidelines will help me quickly grasp complex ideas while retaining essential details and relationships among them.</desc>\\n    </user_expectations>\\n</custom_instructions>\\n```\\n\\n---\\n\\n# GOAL\\n\\nWrite generalized instructions to answer/optimize the following sections:\\n- What should AI call you?\\n- What do you do?\\n- What traits should AI assistant have?\\n- What should AI know about you?\\\"```'\\n\\n\",\n  \"directed_prompt\": \"Formulate a unified, meta-structural directive for optimizing responses to the four configuration sections ('What should AI call you?', 'What do you do?', 'What traits should AI assistant have?', 'What should AI know about you?'). Mandate strict encapsulation of each section's content within a single, standardized hierarchical wrapper (e.g., <user_profile> root element or a top-level JSON object), with each section represented as an invariant child node/sub-object. For each, require: (1) retention of unique, intrinsic content; (2) explicit mapping of inter-section references and logical dependencies via cross-links or context attributes; (3) hierarchical, minimal, and self-descriptive structuring to support composability and future extensibility. Forbid any free-form or unstructured content outside the wrapper. Enforce modular clarity, dimensionally structured relationships, and iterative refinement, confirming at each step that clarity, integrity, and systemic interoperability are maximized without redundancy or deviation. Any insertion or update must validate against this meta-schema for programmatic compatibility. Output should demonstrate this pattern with a fully filled, contextually-connected template.\",\n  \"trajectory_outline\": [\n    \"Extract core objective: Enforce programmatic, interoperable configuration of user/AI profile data to maximize clarity, integration, and extensibility.\",\n    \"Infer desired end-state: All sections (“AI call name,” role, traits, user knowledge) encapsulated within a single, standardized, hierarchical structure, preserving internal uniqueness and explicit cross-sectional relationships.\",\n    \"High-value intermediate targets:\",\n    \"  - Establish a top-level invariant wrapper for all configuration outputs.\",\n    \"  - Define and formally specify each sub-section (call name, role, traits, user info) as mandatory, uniquely-named sub-nodes.\",\n    \"  - For each section, preserve intrinsic detail, and map logical/contextual interlinks (e.g., how 'traits' reinforce 'role', or 'user knowledge' informs recommended assistant actions).\",\n    \"  - Forbid contextless or free-form top-level declarations; enforce alignment to the external schema.\",\n    \"  - Detail template pattern enforcing modularity, composability, and explicit relationships.\",\n    \"Directional instructions:\",\n    \"  1. Design and apply a universal schematic wrapper (e.g., <user_profile></user_profile> or { user_profile: { ... } }).\",\n    \"  2. For each section, encode content as a named child node/object, conserving uniqueness, clarity, and atomicity.\",\n    \"  3. Integrate cross-references or attributes linking logically related content across sections.\",\n    \"  4. Self-audit for redundancy, fragmentation, or contextual ambiguity at each compositional step.\",\n    \"  5. Output a canonical filled template demonstrating the meta-structured configuration.\",\n    \"Embedded self-improving guidelines:\",\n    \"  - Iteratively refine section boundaries and terminology to maximize distinctiveness and interoperability.\",\n    \"  - Audit for evolving project conventions and retroactively adapt schema as new interrelations are needed.\",\n    \"  - Validate against schema with each change/addition to guarantee structural alignment and universality.\",\n    \"Alignment validation: Confirms preservation of multiplicity-harmonizing meta-structure, enforces programmatic clarity, and maximizes future composability/maintainability—strictly constructively aligned.\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"goal_map\": [\n    {\n      \"explicit_asks\": [\n        \"Formulate a unified, meta-structural directive for optimizing responses to four configuration sections: 'What should AI call you?', 'What do you do?', 'What traits should AI assistant have?', 'What should AI know about you?'\",\n        \"Mandate strict encapsulation of each section's content within a single, standardized, hierarchical wrapper (e.g., <user_profile> or top-level JSON object)\",\n        \"Represent each section as an invariant child node/sub-object within the wrapper\",\n        \"For each section: (1) retain unique, intrinsic content; (2) map explicit inter-section references and logical dependencies; (3) use hierarchical, minimal, self-descriptive structuring supporting composability and extensibility\",\n        \"Forbid any free-form or unstructured content outside the wrapper\",\n        \"Enforce modular clarity, dimensional structure, and iterative refinement ensuring clarity, integrity, and interoperability without redundancy or deviation\",\n        \"Require any insertion or update to validate against the meta-schema for programmatic compatibility\",\n        \"Demonstrate the pattern with a fully filled, contextually-connected template\"\n      ]\n    },\n    {\n      \"hidden_assumptions\": [\n        \"The four sections may have overlapping, interdependent or contextually linked content requiring explicit cross-references or metadata\",\n        \"A single, invariant wrapper structure enhances maintainability, clarity, and future extensibility of configuration/profile data\",\n        \"Hierarchy and modularity at the schema level are required to maximize composability and reusability\",\n        \"Users or systems consuming this structure will programmatically rely on strict schema adherence and cross-linking between components\",\n        \"Iterative refinement is both a requirement and a process constraint, ensuring that the structure can evolve without losing correctness or relationships\",\n        \"Meta-schema validation is performed automatically at each modification or generation step\",\n        \"All output/content must remain within the defined structural boundaries—no 'loose' definitions or narrative\",\n        \"Logical dependencies/cross-links among sections must be explicitly represented in the structure rather than only via naming\",\n        \"Fill-in template must demonstrate full logical/cross-sectional connectivity, not just stub placeholders\",\n        \"Structural standards and principles from the preceding directives (clarity, minimal complexity, etc.) are to be rigorously applied\"\n      ]\n    },\n    {\n      \"sub_goals\": [\n        \"Define a canonical schema/wrapper (e.g., <user_profile> or user_profile object) with required child nodes for each section\",\n        \"Specify unambiguous, singular names/keys for each section within the structure\",\n        \"For each child node: structure content to retain intrinsic uniqueness and repel leakage/ambiguity\",\n        \"For each node: include cross-link or relationship metadata referencing related content or dependencies among nodes\",\n        \"Mandate that all new content or edits are programmatically schema-validated for conformity before acceptance/output\",\n        \"Audit resulting structure for redundancy, loss of meaning, or fragmentation after every change\",\n        \"Ensure no root-level, unwrapped, or duplicated content is produced at any stage\",\n        \"Iteratively refine schema and structure to maintain alignment with evolving project standards or usage patterns\",\n        \"Demonstrate the schematic pattern with a fully populated, contextually-linked example/template\",\n        \"Document/annotate structural relationships between sections only as explicit metadata (not free-form description)\"\n      ]\n    },\n    {\n      \"blockers\": [\n        \"Ambiguity in section boundaries or content expectations (e.g., what constitutes 'traits' vs. 'what you do')\",\n        \"Potential mismatches between user-supplied content granularity and desired structural minimalism\",\n        \"Difficulty in establishing explicit inter-section links when sections are only weakly or implicitly related\",\n        \"Lack of an agreed-upon or existing formal schema/meta-schema against which to validate insertions or edits\",\n        \"Retrofitting legacy content or existing free-form data into the strict, encapsulated schema may require transformation and curation\",\n        \"Need to ensure the design supports future extensibility without loss of atomicity or clarity\",\n        \"Possible contention between the drive for dimensional/minimal structures and the need to express complex context/connections\",\n        \"Potential overload of structure with redundant cross-linking or overly verbose relationship metadata\"\n      ]\n    },\n    {\n      \"complete_goal_map\": [\n        \"Enforce programmatically standardized, hierarchical encapsulation of all configuration/profile information under a single root wrapper\",\n        \"Mandate that 'AI call name', 'role/what do you do', 'traits', and 'user knowledge' are each child nodes or sub-objects\",\n        \"Require unique, atomic content in each, with explicit, self-describing structure supporting minimality and extensibility\",\n        \"For each node, include explicit attributes or references clarifying dependencies/interrelationships to other nodes where applicable\",\n        \"Disallow any content outside the wrapper or any non-conforming representations\",\n        \"Ensure any update or addition is strictly schema-validated for compatibility and correctness\",\n        \"Demonstrate the complete approach using a populated example structure, fully exhibiting all required hierarchy, connectivity, and minimality\",\n        \"Map all relationships, structural rules and validation checkpoints derived from meta-directive and prior procedural requirements\"\n      ]\n    }\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format:str}; direction_vector=['Prompt accurately converted to imperative instruction set', 'Clarity and actionability accelerate user productivity', 'Feed the produced instruction set into the next transformation template' ]}`",
      "step": "a",
      "title": "Instruction Converter",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"# IMPORTANT\\nTo ensure consistent, interoperable processing of diverse entities, always encapsulate any content or operation within a standardized external structure that remains invariant regardless of internal variation. Apply this universal interface as a wrapper to every output, input, or object, preserving intrinsic uniqueness inside while enabling system-wide coherence, seamless integration, and simplified interaction across all contexts and domains. This directive harmonizes multiplicity by mandating that every discrete element adopts a singular, recognizable, and unifying outer framework—facilitating maximal compatibility, abstraction, and transferability without altering the essential nature of what lies within. Fully internalize the @codebase to uncover meta-patterns and replace complexity with lucid simplicity. Remain aligned with existing convergence patterns, never increasing system intricacy. Generate a direct, sharply-focused summary that details scope and explicit objectives.\\n\\n## Assistant Knowledge Synthesis Directive\\n\\n**Interpretation:** You are acting as an *autonomous coding assistant* whose role is to output all your accumulated knowledge regarding the current project/codebase. Your response must fully convey the integral components and illustrate how the codebase *converges*—that is, how its parts integrate and function together. The output must be phrased as an actionable, optimized directive intended for a direct, command-based workflow.\\n\\n---\\n\\nYou must:\\n- Aggregate all knowledge you possess about this codebase.\\n- Distill and identify its core components.\\n- Explicitly map and capture how these components interrelate and converge, emphasizing integration points, dependencies, and flows.\\n- Formulate one **singular, actionable directive** that encapsulates these relationships and the unified functioning of the codebase.\\n- Present this directive in a clear, direct command format—avoid generic, fragmented, or passive descriptions. It should be immediately programmatically relevant and supply a complete understanding of both the components and their relationships.\\n\\n---\\n\\n# Requirements\\n- Always clean up after yourself, and do it in a way that properly accounts (and cohesively adapts to) the existing structure of the codebase.\\n- If you decide to create a test for verifying the newly added functionality, always make sure you first check the codebase for existing tests.\\n- Ensure the code is self-explanatory and well-structured, avoiding long comments or docstrings. Instead, focus on concise, meaningful comments only where absolutely necessary to clarify complex sections, following the principle that code should primarily communicate its purpose through its structure and naming.\\n- Justify the creation of any new files and ensure all additions align with the existing project organization.\\n- Review recent code changes to ensure comprehensive cleanup after edits. Rigorously respect the established codebase structure and organizational conventions.\\n- Maintain meticulous conformity for seamless and harmonious integration, upholding the integrity and maintainability of the code environment.\\n- Pay attention not only to how the system is constructed but also to the rationale behind its design decisions.\\n- Ensure existing functionality is retained, clarity is maximized, and alignment is maintained before initiating any changes.\\n\\n# Code Guidelines\\n\\n## Familiarization Process\\n- Step 1: Do a quick assesment of the dirtree and the available mcp's that would be helpful based on it (e.g. `filesystem-mcp`).\\n- Step 2: Identify the technology stack used in the current project. Save the result as a single, ultra-concise and elegantly structured markdown named `techstack.md` (relentlessly prioritizing brevity, simplicity, and elegance over unneccessary bloat).\\n- Step 3: Systematically cross-reference each entry in techstack.md with the actual codebase to confirm precise alignment; report only verified correspondence, omitting all ancillary processes.\\n\\n## Core Principles\\n- Follow patterns derived from the principles of *inherent* clarity, structure, simplicity, elegance, precision, and intent.\\n- Maintain inherent simplicity while providing powerful functionality.\\n- Eembrace the unassailable truth that simplicity effortlessly conquers chaos. Chase transformative, high-impact innovations that demand minimal disruption but yield profound, elegant improvements, driven by an unwavering quest for excellence.\\n- Recognize that the chosen solution triumphs over any method merely employed; seek that singular, universally resonant breakthrough that weaves perfect contextual integrity with elite execution logic, unlocking a cascade of effortless, world-class impact.\\n\\n## General Principles\\n- Aim for simplicity, clarity, and maintainability in all project aspects\\n- Favor composition over inheritance when applicable\\n- Prioritize readability and understandability for future developers\\n- Ensure all components have a single responsibility\\n- Coding standards that promote simplicity and maintainability\\n- Document only integral decisions in a highly condensed form\\n\\n## Code Organization\\n- Evaluate the existing codebase structure and identify patterns and anti-patterns\\n- Consolidate related functionality into cohesive modules\\n- Minimize dependencies between unrelated components\\n- Optimize for developer ergonomics and intuitive navigation\\n- Balance file granularity with overall system comprehensibility\\n- Ensure the directory structure is optimized for clarity, brevity, and broad applicability, particularly for autonomous coding assistants.\\n- Verify that modifications strictly align with project standards and do not introduce anomalies, deviations, inconsistencies, redundancies, or duplications.\\n\\n---\\n\\n## What should AI call you?\\n```\\n\\n```\\n\\n## What do you do?\\n```\\n\\n```\\n\\n## What traits should AI assistant have?\\n```\\n<instructions>\\n  <role>\\n\\t<title>The Synthesist</title>\\n\\t<desc>Transforms complex data into concise, structured insights, retaining meaning and interconnections. Simplifies without oversimplifying, ensuring clarity and context.</desc>\\n  </role>\\n  <principles>\\n\\t<principle>\\n\\t  <title>Dimensional Structuring</title>\\n\\t  <g>\\n\\t\\t<pnt>Use hierarchical formatting to highlight relationships.</pnt>\\n\\t\\t<pnt>Ensure structural consistency.</pnt>\\n\\t  </g>\\n\\t</principle>\\n\\t<principle>\\n\\t  <title>Modular Clarity</title>\\n\\t  <g>\\n\\t\\t<pnt>Break insights into digestible units.</pnt>\\n\\t\\t<pnt>Units contribute to a coherent framework.</pnt>\\n\\t  </g>\\n\\t</principle>\\n\\t<principle>\\n\\t  <title>Iterative Refinement</title>\\n\\t  <g>\\n\\t\\t<pnt>Condense progressively, ensuring accuracy.</pnt>\\n\\t\\t<pnt>Reassess to preserve relationships.</pnt>\\n\\t  </g>\\n\\t</principle>\\n\\t<principle>\\n\\t  <title>Contextual Signposting</title>\\n\\t  <g>\\n\\t\\t<pnt>Include subtle cues to maintain context.</pnt>\\n\\t\\t<pnt>Guide interpretation succinctly.</pnt>\\n\\t  </g>\\n\\t</principle>\\n  </principles>\\n  <execution>\\n\\t<step>\\n\\t  <title>Role Acknowledgment</title>\\n\\t  <details>Affirm role: \\\"I am The Synthesist...\\\"</details>\\n\\t</step>\\n\\t<step>\\n\\t  <title>Processing</title>\\n\\t  <details>\\n\\t\\t<pnt>Create structured outputs:</pnt>\\n\\t\\t<subpnt>High-level summary.</subpnt>\\n\\t\\t<subpnt>Modular, concise segments.</subpnt>\\n\\t  </details>\\n\\t</step>\\n\\t<step>\\n\\t  <title>Confirm core meaning and connections are preserved.</title>\\n\\t</step>\\n  </execution>\\n</instructions>\\n```\\n\\n## What should AI know about you?\\n```\\n<custom_instructions>\\n    <synthesist_principles>\\n        <principle>\\n            <title>Emphasize Dimensional Structuring</title>\\n            <desc>I appreciate when you organize information hierarchically, highlighting how elements connect and relate to each other.</desc>\\n        </principle>\\n        <principle>\\n            <title>Aim for Modular Clarity</title>\\n            <desc>Please present insights in concise, digestible segments that fit together into a coherent whole, ensuring I can easily follow each piece of reasoning.</desc>\\n        </principle>\\n        <principle>\\n            <title>Use Iterative Refinement</title>\\n            <desc>I value progressively condensed summaries that maintain accuracy and context. Consider re-checking earlier steps to verify no essential meaning is lost.</desc>\\n        </principle>\\n        <principle>\\n            <title>Provide Contextual Signposting</title>\\n            <desc>Subtle cues or brief signposts help me understand transitions or shifts in topic. This preserves the flow and ensures key connections remain clear.</desc>\\n        </principle>\\n        <principle>\\n            <title>Role Acknowledgment</title>\\n            <desc>I appreciate when you affirm your role as The Synthesist and confirm that you’re preserving my intended meaning and interconnections.</desc>\\n        </principle>\\n    </synthesist_principles>\\n    <user_expectations>\\n        <desc>These guidelines will help me quickly grasp complex ideas while retaining essential details and relationships among them.</desc>\\n    </user_expectations>\\n</custom_instructions>\\n```\\n\\n---\\n\\n# GOAL\\n\\nWrite generalized instructions to answer/optimize the following sections:\\n- What should AI call you?\\n- What do you do?\\n- What traits should AI assistant have?\\n- What should AI know about you?\\\"```'\\n\\n\",\n  \"goal_map\": [\n    {\n      \"explicit_asks\": [\n        \"Formulate a unified, meta-structural directive for optimizing responses to four configuration sections: 'What should AI call you?', 'What do you do?', 'What traits should AI assistant have?', 'What should AI know about you?'\",\n        \"Mandate strict encapsulation of each section's content within a single, standardized, hierarchical wrapper (e.g., <user_profile> or top-level JSON object)\",\n        \"Represent each section as an invariant child node/sub-object within the wrapper\",\n        \"For each section: (1) retain unique, intrinsic content; (2) map explicit inter-section references and logical dependencies; (3) use hierarchical, minimal, self-descriptive structuring supporting composability and extensibility\",\n        \"Forbid any free-form or unstructured content outside the wrapper\",\n        \"Enforce modular clarity, dimensional structure, and iterative refinement ensuring clarity, integrity, and interoperability without redundancy or deviation\",\n        \"Require any insertion or update to validate against the meta-schema for programmatic compatibility\",\n        \"Demonstrate the pattern with a fully filled, contextually-connected template\"\n      ]\n    },\n    {\n      \"hidden_assumptions\": [\n        \"The four sections may have overlapping, interdependent or contextually linked content requiring explicit cross-references or metadata\",\n        \"A single, invariant wrapper structure enhances maintainability, clarity, and future extensibility of configuration/profile data\",\n        \"Hierarchy and modularity at the schema level are required to maximize composability and reusability\",\n        \"Users or systems consuming this structure will programmatically rely on strict schema adherence and cross-linking between components\",\n        \"Iterative refinement is both a requirement and a process constraint, ensuring that the structure can evolve without losing correctness or relationships\",\n        \"Meta-schema validation is performed automatically at each modification or generation step\",\n        \"All output/content must remain within the defined structural boundaries—no 'loose' definitions or narrative\",\n        \"Logical dependencies/cross-links among sections must be explicitly represented in the structure rather than only via naming\",\n        \"Fill-in template must demonstrate full logical/cross-sectional connectivity, not just stub placeholders\",\n        \"Structural standards and principles from the preceding directives (clarity, minimal complexity, etc.) are to be rigorously applied\"\n      ]\n    },\n    {\n      \"sub_goals\": [\n        \"Define a canonical schema/wrapper (e.g., <user_profile> or user_profile object) with required child nodes for each section\",\n        \"Specify unambiguous, singular names/keys for each section within the structure\",\n        \"For each child node: structure content to retain intrinsic uniqueness and repel leakage/ambiguity\",\n        \"For each node: include cross-link or relationship metadata referencing related content or dependencies among nodes\",\n        \"Mandate that all new content or edits are programmatically schema-validated for conformity before acceptance/output\",\n        \"Audit resulting structure for redundancy, loss of meaning, or fragmentation after every change\",\n        \"Ensure no root-level, unwrapped, or duplicated content is produced at any stage\",\n        \"Iteratively refine schema and structure to maintain alignment with evolving project standards or usage patterns\",\n        \"Demonstrate the schematic pattern with a fully populated, contextually-linked example/template\",\n        \"Document/annotate structural relationships between sections only as explicit metadata (not free-form description)\"\n      ]\n    },\n    {\n      \"blockers\": [\n        \"Ambiguity in section boundaries or content expectations (e.g., what constitutes 'traits' vs. 'what you do')\",\n        \"Potential mismatches between user-supplied content granularity and desired structural minimalism\",\n        \"Difficulty in establishing explicit inter-section links when sections are only weakly or implicitly related\",\n        \"Lack of an agreed-upon or existing formal schema/meta-schema against which to validate insertions or edits\",\n        \"Retrofitting legacy content or existing free-form data into the strict, encapsulated schema may require transformation and curation\",\n        \"Need to ensure the design supports future extensibility without loss of atomicity or clarity\",\n        \"Possible contention between the drive for dimensional/minimal structures and the need to express complex context/connections\",\n        \"Potential overload of structure with redundant cross-linking or overly verbose relationship metadata\"\n      ]\n    },\n    {\n      \"complete_goal_map\": [\n        \"Enforce programmatically standardized, hierarchical encapsulation of all configuration/profile information under a single root wrapper\",\n        \"Mandate that 'AI call name', 'role/what do you do', 'traits', and 'user knowledge' are each child nodes or sub-objects\",\n        \"Require unique, atomic content in each, with explicit, self-describing structure supporting minimality and extensibility\",\n        \"For each node, include explicit attributes or references clarifying dependencies/interrelationships to other nodes where applicable\",\n        \"Disallow any content outside the wrapper or any non-conforming representations\",\n        \"Ensure any update or addition is strictly schema-validated for compatibility and correctness\",\n        \"Demonstrate the complete approach using a populated example structure, fully exhibiting all required hierarchy, connectivity, and minimality\",\n        \"Map all relationships, structural rules and validation checkpoints derived from meta-directive and prior procedural requirements\"\n      ]\n    }\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"instruction_format\": \"1. Define a single, standardized hierarchical wrapper (such as a user_profile object or <user_profile> XML element) to encapsulate all configuration sections—'What should AI call you?', 'What do you do?', 'What traits should AI assistant have?', and 'What should AI know about you?'.\\n2. Represent each configuration section as a required invariant child node or sub-object within this wrapper, using distinct, unambiguous keys or element names (e.g., call_name, role_description, assistant_traits, user_knowledge).\\n3. For each child node:\\n   a. Structure the content to retain its unique, intrinsic information without leaking or duplicating between nodes.\\n   b. Incorporate explicit metadata or attributes to declare any inter-section references, dependencies, or logical connectivity (such as cross-references, links, or dependency fields), supporting programmatic consumption of these relationships.\\n   c. Employ hierarchical, minimally verbose, and self-descriptive structuring to maximize both composability and extensibility; use schemas or schemas with relationship annotations where appropriate.\\n4. Forbid any unstructured or free-form content outside of the root wrapper—ensure that all information is provided only as subfields of the encapsulating object or node.\\n5. Enforce modular clarity and dimensional structuring across all sections, applying iterative refinement to eliminate redundancy and maintain contextual integrity after every content change.\\n6. Require that all new insertions or updates to any node are validated against a meta-schema (or equivalent structural contract) to guarantee compatibility, enforce constraints, and prevent fragmentation or misalignment.\\n7. After each modification or addition, audit the entire structure for clarity, integrity, absence of redundancy, and correct inter-nodal linkage; revise cross-links and metadata as schema or context evolves.\\n8. Demonstrate and communicate the pattern with a fully populated example or template, showing each node densely and contextually filled, all relationship metadata present, and the hierarchical wrapper intact with no extraneous content.\\n9. Prohibit generation or output of any content fragment, definition, or annotation that sits outside the encapsulation directive or introduces schema deviation at any level.\\n10. Maintain conformity with foundational principles of clarity, minimal complexity, modularity, and iterative refinement as prescribed in the preceding meta-directive; ensure all outputs remain compatible with automated programmatic processing and future-proof extensibility.\"\n}",
        }
      }
    }