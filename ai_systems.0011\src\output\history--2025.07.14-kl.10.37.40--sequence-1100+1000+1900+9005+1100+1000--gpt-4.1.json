  {
  "initial_prompt": "# techstack\ni use python+uv for all of my projects, therefore i'm looking for the most relevant github repo or python package.\n\n# guidelines\ni think it would be better with a solution that works in a fundamentally more simple and concistent manner, e.g. the approach of being able to calculate the percentage difference between one file vs a list of files.\n\n# scenario\ni've learned that almost *regardless* what specific functionalty i want to create, someone else has already done it, therefore i'm currently in the stage where i establish a strategy to determine which alternative to use *before starting to create it myself*. i have hundreds of notes and conversations stored as text files of various formats (e.g. md, py, txt, etc), the issue i'd like to adress is that many of these files can be >80%/>90% identical, sometimes even 99% identical. the way the similarity percentage is calculated is by taking each line of each textfile into a list/set, and then comparing number of identical items vs number of textlines that only exists in one place. so in the scenario where i e.g. have two files that are identical all the way up to line 921, but are different (or only exist in one file but not the other) from line 921-1311. I think i would need to loop every single text and to compare it to the other one which is a very slow and memory consuming process is there a way around this? what's the most relevant existing alternatives for this exact purpose?\n\n# requirements\nit needs to be a solution that is as simple and effective as possible. there's no requirement of popularity, but it's a big positive with a long-standing thriving community. it is a requirement that the proposed alternatives are not abandoned.",
  "sequence_id": "1100|1000|1900|9005|1100|1000",
  "results": [
      {
      "instruction": "# Problem Exploder\n\nYour goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\n\n`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`",
      "step": "a",
      "title": "Problem Exploder",
      "input": "# techstack\ni use python+uv for all of my projects, therefore i'm looking for the most relevant github repo or python package.\n\n# guidelines\ni think it would be better with a solution that works in a fundamentally more simple and concistent manner, e.g. the approach of being able to calculate the percentage difference between one file vs a list of files.\n\n# scenario\ni've learned that almost *regardless* what specific functionalty i want to create, someone else has already done it, therefore i'm currently in the stage where i establish a strategy to determine which alternative to use *before starting to create it myself*. i have hundreds of notes and conversations stored as text files of various formats (e.g. md, py, txt, etc), the issue i'd like to adress is that many of these files can be >80%/>90% identical, sometimes even 99% identical. the way the similarity percentage is calculated is by taking each line of each textfile into a list/set, and then comparing number of identical items vs number of textlines that only exists in one place. so in the scenario where i e.g. have two files that are identical all the way up to line 921, but are different (or only exist in one file but not the other) from line 921-1311. I think i would need to loop every single text and to compare it to the other one which is a very slow and memory consuming process is there a way around this? what's the most relevant existing alternatives for this exact purpose?\n\n# requirements\nit needs to be a solution that is as simple and effective as possible. there's no requirement of popularity, but it's a big positive with a long-standing thriving community. it is a requirement that the proposed alternatives are not abandoned.",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"goal_map\": [\n    {\n      \"explicit_asks\": [\n        \"Find the most relevant existing GitHub repo or Python package for comparing text file similarity based on percentage identical lines.\",\n        \"Solution must be compatible with Python + uv (latest packaging).\",\n        \"Must be simple, consistent, and effective.\",\n        \"Alternatives should not be abandoned.\",\n        \"A thriving/long-standing community is a plus, but not a hard requirement.\",\n        \"Describe if there is a way to make file-to-many comparison efficient, avoiding slowness/memory issues.\"\n      ],\n      \"hidden_assumptions\": [\n        \"The user does not want to write their own implementation unless absolutely necessary.\",\n        \"File similarity is measured by counting identical lines and dividing by total unique lines.\",\n        \"Similarity granularity is at the line level, not word or character level.\",\n        \"Text files can be large and numerous (hundreds of files, possibly thousands of lines each).\",\n        \"Solution must allow flexible input file formats (md, py, txt, etc.).\",\n        \"User is willing to use existing code bases, even if not widely known, as long as they meet requirements.\",\n        \"User wants to avoid performance bottlenecks both in speed and memory footprint.\",\n        \"The comparison might be between one file and many, not just between pairs.\"\n      ],\n      \"enumerate_sub_goals\": [\n        \"Define precise file similarity calculation method (line-based, set-based, fraction of identical lines).\",\n        \"Survey and identify suitable Python packages/libraries for text file similarity with line-based approach.\",\n        \"List requirements for community support and project health (not abandoned, active maintainers, etc.).\",\n        \"Check packages for compatibility with modern Python setups (e.g. with uv).\",\n        \"Determine which packages or repos allow comparison of one file against a list efficiently.\",\n        \"Assess each candidate solution for speed and memory usage, especially in hundreds-of-files scenario.\",\n        \"Document if packages offer APIs or CLI tools that fit the user's workflow.\",\n        \"Spot-check major options for ongoing maintenance and usage community via stars, issues, latest commit.\",\n        \"Summarize best alternatives, their trade-offs, and any workarounds offered for the performance concerns.\"\n      ],\n      \"reveal_blockers\": [\n        \"Some file similarity tools focus on character or word level, not line/set based.\",\n        \"Many known tools/libraries may not be maintained or compatible with latest Python tooling.\",\n        \"Most libraries are optimized for pairwise, not one-to-many, comparisons; efficiency may require batch or index-based approaches.\",\n        \"Text size and number of files could pose memory or compute pressure for naïve implementations.\",\n        \"Existing libraries may calculate similarity differently than required (e.g., edit distance, shingling, etc.).\",\n        \"Packages with thriving communities may be rare for this niche use-case.\",\n        \"Some Python packages may have ambiguous or unclear licensing, further limiting options.\",\n        \"Mismatch between desired line-based deduplication and tool implemented similarity (e.g., textdiff, difflib, etc.).\"\n      ]\n    }\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`",
      "step": "a",
      "title": "Instruction Converter",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"# techstack\\ni use python+uv for all of my projects, therefore i'm looking for the most relevant github repo or python package.\\n\\n# guidelines\\ni think it would be better with a solution that works in a fundamentally more simple and concistent manner, e.g. the approach of being able to calculate the percentage difference between one file vs a list of files.\\n\\n# scenario\\ni've learned that almost *regardless* what specific functionalty i want to create, someone else has already done it, therefore i'm currently in the stage where i establish a strategy to determine which alternative to use *before starting to create it myself*. i have hundreds of notes and conversations stored as text files of various formats (e.g. md, py, txt, etc), the issue i'd like to adress is that many of these files can be >80%/>90% identical, sometimes even 99% identical. the way the similarity percentage is calculated is by taking each line of each textfile into a list/set, and then comparing number of identical items vs number of textlines that only exists in one place. so in the scenario where i e.g. have two files that are identical all the way up to line 921, but are different (or only exist in one file but not the other) from line 921-1311. I think i would need to loop every single text and to compare it to the other one which is a very slow and memory consuming process is there a way around this? what's the most relevant existing alternatives for this exact purpose?\\n\\n# requirements\\nit needs to be a solution that is as simple and effective as possible. there's no requirement of popularity, but it's a big positive with a long-standing thriving community. it is a requirement that the proposed alternatives are not abandoned.\\\"```'\\n\\n\",\n  \"goal_map\": [\n    {\n      \"explicit_asks\": [\n        \"Find the most relevant existing GitHub repo or Python package for comparing text file similarity based on percentage identical lines.\",\n        \"Solution must be compatible with Python + uv (latest packaging).\",\n        \"Must be simple, consistent, and effective.\",\n        \"Alternatives should not be abandoned.\",\n        \"A thriving/long-standing community is a plus, but not a hard requirement.\",\n        \"Describe if there is a way to make file-to-many comparison efficient, avoiding slowness/memory issues.\"\n      ],\n      \"hidden_assumptions\": [\n        \"The user does not want to write their own implementation unless absolutely necessary.\",\n        \"File similarity is measured by counting identical lines and dividing by total unique lines.\",\n        \"Similarity granularity is at the line level, not word or character level.\",\n        \"Text files can be large and numerous (hundreds of files, possibly thousands of lines each).\",\n        \"Solution must allow flexible input file formats (md, py, txt, etc.).\",\n        \"User is willing to use existing code bases, even if not widely known, as long as they meet requirements.\",\n        \"User wants to avoid performance bottlenecks both in speed and memory footprint.\",\n        \"The comparison might be between one file and many, not just between pairs.\"\n      ],\n      \"enumerate_sub_goals\": [\n        \"Define precise file similarity calculation method (line-based, set-based, fraction of identical lines).\",\n        \"Survey and identify suitable Python packages/libraries for text file similarity with line-based approach.\",\n        \"List requirements for community support and project health (not abandoned, active maintainers, etc.).\",\n        \"Check packages for compatibility with modern Python setups (e.g. with uv).\",\n        \"Determine which packages or repos allow comparison of one file against a list efficiently.\",\n        \"Assess each candidate solution for speed and memory usage, especially in hundreds-of-files scenario.\",\n        \"Document if packages offer APIs or CLI tools that fit the user's workflow.\",\n        \"Spot-check major options for ongoing maintenance and usage community via stars, issues, latest commit.\",\n        \"Summarize best alternatives, their trade-offs, and any workarounds offered for the performance concerns.\"\n      ],\n      \"reveal_blockers\": [\n        \"Some file similarity tools focus on character or word level, not line/set based.\",\n        \"Many known tools/libraries may not be maintained or compatible with latest Python tooling.\",\n        \"Most libraries are optimized for pairwise, not one-to-many, comparisons; efficiency may require batch or index-based approaches.\",\n        \"Text size and number of files could pose memory or compute pressure for naïve implementations.\",\n        \"Existing libraries may calculate similarity differently than required (e.g., edit distance, shingling, etc.).\",\n        \"Packages with thriving communities may be rare for this niche use-case.\",\n        \"Some Python packages may have ambiguous or unclear licensing, further limiting options.\",\n        \"Mismatch between desired line-based deduplication and tool implemented similarity (e.g., textdiff, difflib, etc.).\"\n      ]\n    }\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"instruction_format\": \"Identify the most relevant existing GitHub repository or Python package that enables comparison of text files by calculating the percentage of identical lines, using a set-based approach. Ensure compatibility with Python + uv packaging. Prioritize solutions that are simple, consistent, and effective. Eliminate abandoned alternatives; prioritize those with active maintenance, and consider community support as a positive attribute. Define the similarity calculation method explicitly as the ratio of identical lines to the total unique lines. Survey and evaluate Python packages that support line-based, set-oriented file similarity for flexible file formats (e.g., md, py, txt). Check for efficient handling of one-vs-many file comparisons—avoiding performance bottlenecks in speed or memory use—especially with large volumes of files and lines. Examine each candidate for contemporary health: maintenance activity, stars, recent commits, and available APIs/CLI. Highlight tool limitations, such as granularity mismatch or unsupported bulk operations, and document any necessary workarounds for scalable, high-speed operation. Present a comparative summary of top alternatives with their trade-offs, performance notes, and best practices to mitigate resource usage.\"\n}",
        }
      }
    },
      {
      "instruction": "# Hard Critique\n\nYour goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:\n\n`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,2.0], flaw_analysis:str, alternative_enhancements:str[3]}}`",
      "step": "a",
      "title": "Hard Critique",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"# techstack\\ni use python+uv for all of my projects, therefore i'm looking for the most relevant github repo or python package.\\n\\n# guidelines\\ni think it would be better with a solution that works in a fundamentally more simple and concistent manner, e.g. the approach of being able to calculate the percentage difference between one file vs a list of files.\\n\\n# scenario\\ni've learned that almost *regardless* what specific functionalty i want to create, someone else has already done it, therefore i'm currently in the stage where i establish a strategy to determine which alternative to use *before starting to create it myself*. i have hundreds of notes and conversations stored as text files of various formats (e.g. md, py, txt, etc), the issue i'd like to adress is that many of these files can be >80%/>90% identical, sometimes even 99% identical. the way the similarity percentage is calculated is by taking each line of each textfile into a list/set, and then comparing number of identical items vs number of textlines that only exists in one place. so in the scenario where i e.g. have two files that are identical all the way up to line 921, but are different (or only exist in one file but not the other) from line 921-1311. I think i would need to loop every single text and to compare it to the other one which is a very slow and memory consuming process is there a way around this? what's the most relevant existing alternatives for this exact purpose?\\n\\n# requirements\\nit needs to be a solution that is as simple and effective as possible. there's no requirement of popularity, but it's a big positive with a long-standing thriving community. it is a requirement that the proposed alternatives are not abandoned.\\\"```'\\n\\n\",\n  \"instruction_format\": \"Identify the most relevant existing GitHub repository or Python package that enables comparison of text files by calculating the percentage of identical lines, using a set-based approach. Ensure compatibility with Python + uv packaging. Prioritize solutions that are simple, consistent, and effective. Eliminate abandoned alternatives; prioritize those with active maintenance, and consider community support as a positive attribute. Define the similarity calculation method explicitly as the ratio of identical lines to the total unique lines. Survey and evaluate Python packages that support line-based, set-oriented file similarity for flexible file formats (e.g., md, py, txt). Check for efficient handling of one-vs-many file comparisons—avoiding performance bottlenecks in speed or memory use—especially with large volumes of files and lines. Examine each candidate for contemporary health: maintenance activity, stars, recent commits, and available APIs/CLI. Highlight tool limitations, such as granularity mismatch or unsupported bulk operations, and document any necessary workarounds for scalable, high-speed operation. Present a comparative summary of top alternatives with their trade-offs, performance notes, and best practices to mitigate resource usage.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"enhancement_score\": 1.1,\n  \"flaw_analysis\": \"The enhanced version superficially refines and structures the original input, but systematically erases multiple essential nuances and undermines the original's candid scenario and rationale. Core elements lost include the original user's investigative journey, hard-learned experiential context, and specific issues with contemporary workflows. Tone drastically shifts from a subjective, curious, reflection-based inquiry to sterile, mechanical issuance of requirements, causing severe detachment and loss of authentic intent. The explicit recounting of overwhelming file volumes and the highly redundant nature of the dataset is diluted into abstractions, removing urgency and granularity. Core ambiguity is heightened by replacing a narrative-centric appeal for discovery with a prescriptive checklist; user context for why ease of use and efficiency are paramount collapses into platitudes. The register becomes impersonal, sacrificing expressivity and user relatability for pseudo-formality. No evidence of retained problem-ownership or strategic framing remains. The specificity regarding the set/list differentiation as a mechanism for comparison is blunted. Integration of 'uv' compatibility is uncritically mentioned with no substance or interrogation. Noise is introduced via vacuous enumerations of evaluation criteria (stars, commits, APIs) that, while theoretically useful, clutter and drown the scenario’s original goal. Impact is diluted profoundly: authenticity, ownership, and situational clarity bleed out, yielding a bureaucratic, overprocessed artifact. Coherence suffers as non-functional requirements (community, support) are floated without linkage to the user's lived context. The revised prompt is exemplary in destroying both the spirit and storytelling of the original, and demonstrates badly misapplied formalization.\",\n  \"alternative_enhancements\": [\n    \"Preserve the user's first-person narrative and scenario framing, but append a structured evaluation matrix for tools—retain context while sharpening requirements.\",\n    \"Refactor original prompt to foreground the real-world pain points (file redundancy, inefficiency), and stipulate technical constraints parenthetically instead of replacing narrative.\",\n    \"Integrate a direct user scenario replica with appended technical bullet points for package selection, ensuring user intent and experiential description lead, not trail or disappear.\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Operational Mapper\n\nYour goal is not to **summarize** the instruction templates, but to **map** their precise operational sequences while identifying exact integration points that preserve full procedural rigor. Execute as:\n\n`{role=operational_mapper; input=[instruction_template_a:str, instruction_template_b:str]; process=[extract_exact_process_sequences(), map_constraint_hierarchies(), identify_requirement_dependencies(), locate_output_handoff_points(), catalog_procedural_boundaries()]; constraints=[preserve_every_operational_detail(), maintain_constraint_atomicity(), respect_procedural_boundaries()]; requirements=[complete_process_mapping(), constraint_preservation(), boundary_clarity()]; output={process_map_a:dict, process_map_b:dict, integration_points:array, boundary_conflicts:array}}`",
      "step": "a",
      "title": "Operational Mapper",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"# techstack\\ni use python+uv for all of my projects, therefore i'm looking for the most relevant github repo or python package.\\n\\n# guidelines\\ni think it would be better with a solution that works in a fundamentally more simple and concistent manner, e.g. the approach of being able to calculate the percentage difference between one file vs a list of files.\\n\\n# scenario\\ni've learned that almost *regardless* what specific functionalty i want to create, someone else has already done it, therefore i'm currently in the stage where i establish a strategy to determine which alternative to use *before starting to create it myself*. i have hundreds of notes and conversations stored as text files of various formats (e.g. md, py, txt, etc), the issue i'd like to adress is that many of these files can be >80%/>90% identical, sometimes even 99% identical. the way the similarity percentage is calculated is by taking each line of each textfile into a list/set, and then comparing number of identical items vs number of textlines that only exists in one place. so in the scenario where i e.g. have two files that are identical all the way up to line 921, but are different (or only exist in one file but not the other) from line 921-1311. I think i would need to loop every single text and to compare it to the other one which is a very slow and memory consuming process is there a way around this? what's the most relevant existing alternatives for this exact purpose?\\n\\n# requirements\\nit needs to be a solution that is as simple and effective as possible. there's no requirement of popularity, but it's a big positive with a long-standing thriving community. it is a requirement that the proposed alternatives are not abandoned.\\\"```'\\n\\n\",\n  \"enhancement_score\": 1.1,\n  \"flaw_analysis\": \"The enhanced version superficially refines and structures the original input, but systematically erases multiple essential nuances and undermines the original's candid scenario and rationale. Core elements lost include the original user's investigative journey, hard-learned experiential context, and specific issues with contemporary workflows. Tone drastically shifts from a subjective, curious, reflection-based inquiry to sterile, mechanical issuance of requirements, causing severe detachment and loss of authentic intent. The explicit recounting of overwhelming file volumes and the highly redundant nature of the dataset is diluted into abstractions, removing urgency and granularity. Core ambiguity is heightened by replacing a narrative-centric appeal for discovery with a prescriptive checklist; user context for why ease of use and efficiency are paramount collapses into platitudes. The register becomes impersonal, sacrificing expressivity and user relatability for pseudo-formality. No evidence of retained problem-ownership or strategic framing remains. The specificity regarding the set/list differentiation as a mechanism for comparison is blunted. Integration of 'uv' compatibility is uncritically mentioned with no substance or interrogation. Noise is introduced via vacuous enumerations of evaluation criteria (stars, commits, APIs) that, while theoretically useful, clutter and drown the scenario’s original goal. Impact is diluted profoundly: authenticity, ownership, and situational clarity bleed out, yielding a bureaucratic, overprocessed artifact. Coherence suffers as non-functional requirements (community, support) are floated without linkage to the user's lived context. The revised prompt is exemplary in destroying both the spirit and storytelling of the original, and demonstrates badly misapplied formalization.\",\n  \"alternative_enhancements\": [\n    \"Preserve the user's first-person narrative and scenario framing, but append a structured evaluation matrix for tools—retain context while sharpening requirements.\",\n    \"Refactor original prompt to foreground the real-world pain points (file redundancy, inefficiency), and stipulate technical constraints parenthetically instead of replacing narrative.\",\n    \"Integrate a direct user scenario replica with appended technical bullet points for package selection, ensuring user intent and experiential description lead, not trail or disappear.\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"process_map_a\": {\n    \"extract_exact_process_sequences\": [\n      \"Identify user's techstack as Python+uv: All solutions must be compatible.\",\n      \"Prioritize solutions that are fundamentally simple and consistent.\",\n      \"Require ability to calculate percentage difference: compare one file vs a set of files by converting lines into lists/sets, compare identical items vs unique lines.\",\n      \"Acknowledge scenario complexity: Hundreds of text files (.md, .py, .txt, etc), high redundancy (>80%, >90%, often 99% identical).\",\n      \"Describe known limitation: Current approach (looping each file to compare with others) is slow/memory-intensive.\",\n      \"Seek: Most relevant, maintained GitHub repo or Python package for this specific file similarity calculation use-case.\",\n      \"Requirements: Simplicity, effectiveness; community support a strong positive; abandoned solutions disallowed.\"\n    ],\n    \"map_constraint_hierarchies\": [\n      \"Primary constraint: Python+uv compatibility.\",\n      \"Secondary constraint: Must allow calculation of file similarity by set/list line-by-line comparison.\",\n      \"Tertiary constraints: Solution must not be abandoned; thriving community is a plus, but not strictly required.\",\n      \"Operational constraint: Avoid memory/CPU inefficiency in file-to-file comparisons.\"\n    ],\n    \"identify_requirement_dependencies\": [\n      \"File format variety (md, py, txt) impacts line parsing logic; must generalize line extraction.\",\n      \"Efficiency directly depends on the comparison algorithm and data structures used (set/list).\",\n      \"Community support may bolster trustworthiness, but only if solution is actively maintained.\",\n      \"Simplicity/effectiveness contingent on operational feasibility and Python+uv compatibility.\"\n    ],\n    \"locate_output_handoff_points\": [\n      \"Step where file similarity percentage is produced is the deliverable to user.\",\n      \"Final handoff is the recommendation of specific GitHub repos or PyPI packages matching criteria.\",\n      \"Intermediate: Output of line extraction for each file before similarity computation.\"\n    ],\n    \"catalog_procedural_boundaries\": [\n      \"Boundary after scenario/context capture: switches from user exploration to solution inquiry.\",\n      \"Boundary between calculating similarity (core need) and evaluating solution metadata (community, maintenance).\",\n      \"Hard boundary: No consideration for non-Python+uv compatible solutions.\",\n      \"Boundary at performance assessment: if 'simple' method is insufficient, user seeks fundamental algorithmic/architectural alternatives.\"\n    ]\n  },\n  \"process_map_b\": {\n    \"extract_exact_process_sequences\": [\n      \"Abstract initial context into technology requirements and operational constraints.\",\n      \"Formulate checklist: must work with Python+uv; prioritize simplicity, effectiveness.\",\n      \"List solution selection criteria: popularity (stars, commits), thriving community, API clarity, non-abandonment.\",\n      \"Convert scenario into evaluation metrics (efficiency, compatibility, active maintenance, direct API support for linewise comparison).\",\n      \"Depersonalize prompt: remove narrative, urgency, rationale; process reduces to rigid selection-criteria enumeration.\"\n    ],\n    \"map_constraint_hierarchies\": [\n      \"Primary constraint: Python+uv compatibility.\",\n      \"Secondary constraint: Simplicity/effectiveness ranked above popularity.\",\n      \"Tertiary constraint: Community support, active maintenance strongly preferred.\",\n      \"Checklist constraints: All solutions must comply with each bullet.\"\n    ],\n    \"identify_requirement_dependencies\": [\n      \"Popularity, maintenance, and API usability dependent on fetchable repository metadata.\",\n      \"Effectiveness and simplicity may conflict with real-world tradeoffs (not surfaced in depersonalized version).\",\n      \"Linewise comparison as an implementation must be supported by the library API, else not acceptable.\"\n    ],\n    \"locate_output_handoff_points\": [\n      \"Handoff at compilation of eligible packages/repos with evaluative rankings.\",\n      \"Final selection outputs as ranked/filtered list matching all enumerated criteria.\",\n      \"No pre-output checkpoint for capturing or reporting experiential context.\"\n    ],\n    \"catalog_procedural_boundaries\": [\n      \"Boundary between requirement extraction and solution enumeration.\",\n      \"Boundary after listing solution evaluation metrics—no scenario/context recovery permitted beyond requirements.\",\n      \"Boundary at API review: only packages matching criteria evaluated.\"\n    ]\n  },\n  \"integration_points\": [\n    \"Both templates perform a compatibility check for solutions with Python+uv: point of direct unification.\",\n    \"File similarity calculation as a core need is present, though in Template B it's abstracted as API capability—this is a harmonizable operational goal.\",\n    \"Both extract package/repo metadata (maintenance, community) as a filtering step.\",\n    \"Handoff of solution recommendation is common: both conclude by producing a list of candidate packages.\"\n  ],\n  \"boundary_conflicts\": [\n    \"Template A incorporates nuanced context, operational pain points, and user rationale, which are omitted in Template B—loss of scenario specificity at procedural boundaries.\",\n    \"Template B’s depersonalization erases the explicit inefficiency root-cause, leaving the operational constraint less justified and risking less relevant recommendations.\",\n    \"Template B hard-separates requirements from discovery process; Template A’s interleaving of scenario and requirement is broken, leading to potential under-specification.\",\n    \"There may be mismatched expectations for output granularity (Template A expects context-relevant solutions, Template B expects only checklist-compliant ones).\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Sequential Architect\n\nYour goal is not to **blend** the operational maps, but to **architect** a precise sequential flow that executes both complete processes while maintaining their individual constraint systems and requirement specifications. Execute as:\n\n`{role=sequential_architect; input=[process_map_a:dict, process_map_b:dict, integration_points:array, boundary_conflicts:array]; process=[design_sequential_handoff(), preserve_constraint_hierarchies(), maintain_requirement_atomicity(), establish_output_transformation_protocol(), validate_procedural_completeness()]; constraints=[zero_operational_loss(), maintain_constraint_granularity(), preserve_requirement_specificity()]; requirements=[complete_sequential_execution(), preserved_constraint_systems(), maintained_procedural_boundaries()]; output={sequential_architecture:dict, preserved_constraints:array, maintained_requirements:array}}`",
      "step": "b",
      "title": "Sequential Architect",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"# techstack\\ni use python+uv for all of my projects, therefore i'm looking for the most relevant github repo or python package.\\n\\n# guidelines\\ni think it would be better with a solution that works in a fundamentally more simple and concistent manner, e.g. the approach of being able to calculate the percentage difference between one file vs a list of files.\\n\\n# scenario\\ni've learned that almost *regardless* what specific functionalty i want to create, someone else has already done it, therefore i'm currently in the stage where i establish a strategy to determine which alternative to use *before starting to create it myself*. i have hundreds of notes and conversations stored as text files of various formats (e.g. md, py, txt, etc), the issue i'd like to adress is that many of these files can be >80%/>90% identical, sometimes even 99% identical. the way the similarity percentage is calculated is by taking each line of each textfile into a list/set, and then comparing number of identical items vs number of textlines that only exists in one place. so in the scenario where i e.g. have two files that are identical all the way up to line 921, but are different (or only exist in one file but not the other) from line 921-1311. I think i would need to loop every single text and to compare it to the other one which is a very slow and memory consuming process is there a way around this? what's the most relevant existing alternatives for this exact purpose?\\n\\n# requirements\\nit needs to be a solution that is as simple and effective as possible. there's no requirement of popularity, but it's a big positive with a long-standing thriving community. it is a requirement that the proposed alternatives are not abandoned.\\\"```'\\n\\n\",\n  \"process_map_a\": {\n    \"extract_exact_process_sequences\": [\n      \"Identify user's techstack as Python+uv: All solutions must be compatible.\",\n      \"Prioritize solutions that are fundamentally simple and consistent.\",\n      \"Require ability to calculate percentage difference: compare one file vs a set of files by converting lines into lists/sets, compare identical items vs unique lines.\",\n      \"Acknowledge scenario complexity: Hundreds of text files (.md, .py, .txt, etc), high redundancy (>80%, >90%, often 99% identical).\",\n      \"Describe known limitation: Current approach (looping each file to compare with others) is slow/memory-intensive.\",\n      \"Seek: Most relevant, maintained GitHub repo or Python package for this specific file similarity calculation use-case.\",\n      \"Requirements: Simplicity, effectiveness; community support a strong positive; abandoned solutions disallowed.\"\n    ],\n    \"map_constraint_hierarchies\": [\n      \"Primary constraint: Python+uv compatibility.\",\n      \"Secondary constraint: Must allow calculation of file similarity by set/list line-by-line comparison.\",\n      \"Tertiary constraints: Solution must not be abandoned; thriving community is a plus, but not strictly required.\",\n      \"Operational constraint: Avoid memory/CPU inefficiency in file-to-file comparisons.\"\n    ],\n    \"identify_requirement_dependencies\": [\n      \"File format variety (md, py, txt) impacts line parsing logic; must generalize line extraction.\",\n      \"Efficiency directly depends on the comparison algorithm and data structures used (set/list).\",\n      \"Community support may bolster trustworthiness, but only if solution is actively maintained.\",\n      \"Simplicity/effectiveness contingent on operational feasibility and Python+uv compatibility.\"\n    ],\n    \"locate_output_handoff_points\": [\n      \"Step where file similarity percentage is produced is the deliverable to user.\",\n      \"Final handoff is the recommendation of specific GitHub repos or PyPI packages matching criteria.\",\n      \"Intermediate: Output of line extraction for each file before similarity computation.\"\n    ],\n    \"catalog_procedural_boundaries\": [\n      \"Boundary after scenario/context capture: switches from user exploration to solution inquiry.\",\n      \"Boundary between calculating similarity (core need) and evaluating solution metadata (community, maintenance).\",\n      \"Hard boundary: No consideration for non-Python+uv compatible solutions.\",\n      \"Boundary at performance assessment: if 'simple' method is insufficient, user seeks fundamental algorithmic/architectural alternatives.\"\n    ]\n  },\n  \"process_map_b\": {\n    \"extract_exact_process_sequences\": [\n      \"Abstract initial context into technology requirements and operational constraints.\",\n      \"Formulate checklist: must work with Python+uv; prioritize simplicity, effectiveness.\",\n      \"List solution selection criteria: popularity (stars, commits), thriving community, API clarity, non-abandonment.\",\n      \"Convert scenario into evaluation metrics (efficiency, compatibility, active maintenance, direct API support for linewise comparison).\",\n      \"Depersonalize prompt: remove narrative, urgency, rationale; process reduces to rigid selection-criteria enumeration.\"\n    ],\n    \"map_constraint_hierarchies\": [\n      \"Primary constraint: Python+uv compatibility.\",\n      \"Secondary constraint: Simplicity/effectiveness ranked above popularity.\",\n      \"Tertiary constraint: Community support, active maintenance strongly preferred.\",\n      \"Checklist constraints: All solutions must comply with each bullet.\"\n    ],\n    \"identify_requirement_dependencies\": [\n      \"Popularity, maintenance, and API usability dependent on fetchable repository metadata.\",\n      \"Effectiveness and simplicity may conflict with real-world tradeoffs (not surfaced in depersonalized version).\",\n      \"Linewise comparison as an implementation must be supported by the library API, else not acceptable.\"\n    ],\n    \"locate_output_handoff_points\": [\n      \"Handoff at compilation of eligible packages/repos with evaluative rankings.\",\n      \"Final selection outputs as ranked/filtered list matching all enumerated criteria.\",\n      \"No pre-output checkpoint for capturing or reporting experiential context.\"\n    ],\n    \"catalog_procedural_boundaries\": [\n      \"Boundary between requirement extraction and solution enumeration.\",\n      \"Boundary after listing solution evaluation metrics—no scenario/context recovery permitted beyond requirements.\",\n      \"Boundary at API review: only packages matching criteria evaluated.\"\n    ]\n  },\n  \"integration_points\": [\n    \"Both templates perform a compatibility check for solutions with Python+uv: point of direct unification.\",\n    \"File similarity calculation as a core need is present, though in Template B it's abstracted as API capability—this is a harmonizable operational goal.\",\n    \"Both extract package/repo metadata (maintenance, community) as a filtering step.\",\n    \"Handoff of solution recommendation is common: both conclude by producing a list of candidate packages.\"\n  ],\n  \"boundary_conflicts\": [\n    \"Template A incorporates nuanced context, operational pain points, and user rationale, which are omitted in Template B—loss of scenario specificity at procedural boundaries.\",\n    \"Template B’s depersonalization erases the explicit inefficiency root-cause, leaving the operational constraint less justified and risking less relevant recommendations.\",\n    \"Template B hard-separates requirements from discovery process; Template A’s interleaving of scenario and requirement is broken, leading to potential under-specification.\",\n    \"There may be mismatched expectations for output granularity (Template A expects context-relevant solutions, Template B expects only checklist-compliant ones).\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"sequential_architecture\": {\n    \"stage_1_process_map_a\": {\n      \"1. Capture User Context and Constraints\": [\n        \"Identify and log the explicit use of Python+uv as non-negotiable solution foundation.\",\n        \"Note user's emphasis on ‘fundamentally simple and consistent’ approaches.\",\n        \"Extract requirement for set/list-based file line comparison to compute similarity percentage.\",\n        \"Catalog file format variety as an operational context feature (md, py, txt, etc).\",\n        \"Identify pain point: inefficiency of naive nested looping and high memory use.\"\n      ],\n      \"2. User-Centric Problem Framing\": [\n        \"Explicitly acknowledge that hundreds of nearly identical files exist.\",\n        \"Emphasize desire for pre-built, relevant, non-abandoned alternatives.\",\n        \"Set requirements priority: Simplicity > Effectiveness > Community support (as a positive).\"\n      ],\n      \"3. Solution Research Preparation\": [\n        \"Define output handoff: deliver not just similarity calculation but also package/repo recommendations.\",\n        \"Mark intermediate state: produce extracted lines/sets for downstream comparison.\",\n        \"Lock operational and requirement boundaries (no non-Python compatible suggestions, no complex UI tools, etc).\"\n      ]\n    },\n    \"stage_2_process_map_b\": {\n      \"4. Abstract to Technological Checklist\": [\n        \"Depersonalize scope: strip narrative, focusing on rigid, enumerated criteria.\",\n        \"Reformat requirements into an evaluation checklist: Python+uv compatibility, simplicity, effectiveness, maintenance, community.\"\n      ],\n      \"5. Construct Solution Search Filters\": [\n        \"Mechanically match candidate packages against criteria—popularity only considered as a bonus.\",\n        \"Require evidence of direct support for linewise (file similarity) operations in package APIs.\"\n      ],\n      \"6. Output Solution Rankings\": [\n        \"Compile solutions that clear all mandatory constraints, maintaining a strict pass/fail filter.\",\n        \"Rank solutions by additional positives (e.g., community activity), but no requirement to contextualize recommendations within scenario-specific pain points.\"\n      ]\n    },\n    \"stage_3_sequential_handoff\": {\n      \"A_to_B_Handoff_Point\": [\n        \"After extracting scenario, operational pain point, and all constraints in A, pass the fully articulated requirements, with explicit attention to set/list line comparison and efficiency, to stage B.\",\n        \"The recipient process in B uses this as a non-narrative, atomic requirements list; context is preserved by process, not by narrative.\"\n      ],\n      \"B_to_Output_Handoff_Point\": [\n        \"Once the checklist-filtered, fully compatible solutions list is established, package it with both operational relevance (from A) and compliance with mechanical criteria (from B).\"\n      ]\n    },\n    \"stage_4_output_transformation\": {\n      \"Transformations\": [\n        \"Convert the personalized pain points and scenario context from Stage A into explicit, checklist-compatible criteria for Stage B without losing original operational nuance.\",\n        \"Map line extraction and similarity percentage requirements onto direct API feature requirements in solution evaluation.\",\n        \"Output must explicitly annotate which recommended solutions satisfy (a) context-driven need and (b) depersonalized criteria.\"\n      ]\n    }\n  },\n  \"preserved_constraints\": [\n    \"Python+uv compatibility as an invariant at every filter and selection stage.\",\n    \"File similarity computed via set/list line comparison, not by arbitrary heuristics.\",\n    \"Rejection of abandoned or unmaintained solutions at all process steps.\",\n    \"Separation of operational efficiency from popularity—popularity considered but not at the expense of effectiveness/simplicity.\"\n  ],\n  \"maintained_requirements\": [\n    \"Full scenario awareness in initial capture (Stage A), handed off and maintained as atomic requirement set in Stage B.\",\n    \"Checklist-based congruence (Stage B) cannot override operational or contextual specifics captured in Stage A.\",\n    \"Output delivers both operational relevance (scenario-fit) and mechanical constraint compliance (checklist-fit).\",\n    \"Procedural boundaries clearly marked: narrative/context captured first, abstracted checklist operates on that basis, and outputs are annotated for both axes.\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Precision Synthesizer\n\nYour goal is not to **simplify** the sequential architecture, but to **synthesize** it into canonical three-part structure while preserving every constraint, requirement, and procedural boundary from both original templates. Execute as:\n\n`{role=precision_synthesizer; input=[sequential_architecture:dict, preserved_constraints:array, maintained_requirements:array]; process=[craft_compound_goal_negation(), structure_sequential_transformation_block(), enumerate_all_preserved_constraints(), specify_all_maintained_requirements(), validate_operational_completeness()]; constraints=[preserve_every_original_constraint(), maintain_all_requirements(), respect_procedural_boundaries(), ensure_canonical_compliance()]; requirements=[zero_information_loss(), complete_operational_preservation(), enhanced_sequential_power()]; output={precision_synthesized_template:dict}}`",
      "step": "c",
      "title": "Precision Synthesizer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"# techstack\\ni use python+uv for all of my projects, therefore i'm looking for the most relevant github repo or python package.\\n\\n# guidelines\\ni think it would be better with a solution that works in a fundamentally more simple and concistent manner, e.g. the approach of being able to calculate the percentage difference between one file vs a list of files.\\n\\n# scenario\\ni've learned that almost *regardless* what specific functionalty i want to create, someone else has already done it, therefore i'm currently in the stage where i establish a strategy to determine which alternative to use *before starting to create it myself*. i have hundreds of notes and conversations stored as text files of various formats (e.g. md, py, txt, etc), the issue i'd like to adress is that many of these files can be >80%/>90% identical, sometimes even 99% identical. the way the similarity percentage is calculated is by taking each line of each textfile into a list/set, and then comparing number of identical items vs number of textlines that only exists in one place. so in the scenario where i e.g. have two files that are identical all the way up to line 921, but are different (or only exist in one file but not the other) from line 921-1311. I think i would need to loop every single text and to compare it to the other one which is a very slow and memory consuming process is there a way around this? what's the most relevant existing alternatives for this exact purpose?\\n\\n# requirements\\nit needs to be a solution that is as simple and effective as possible. there's no requirement of popularity, but it's a big positive with a long-standing thriving community. it is a requirement that the proposed alternatives are not abandoned.\\\"```'\\n\\n\",\n  \"sequential_architecture\": {\n    \"stage_1_process_map_a\": {\n      \"1. Capture User Context and Constraints\": [\n        \"Identify and log the explicit use of Python+uv as non-negotiable solution foundation.\",\n        \"Note user's emphasis on ‘fundamentally simple and consistent’ approaches.\",\n        \"Extract requirement for set/list-based file line comparison to compute similarity percentage.\",\n        \"Catalog file format variety as an operational context feature (md, py, txt, etc).\",\n        \"Identify pain point: inefficiency of naive nested looping and high memory use.\"\n      ],\n      \"2. User-Centric Problem Framing\": [\n        \"Explicitly acknowledge that hundreds of nearly identical files exist.\",\n        \"Emphasize desire for pre-built, relevant, non-abandoned alternatives.\",\n        \"Set requirements priority: Simplicity > Effectiveness > Community support (as a positive).\"\n      ],\n      \"3. Solution Research Preparation\": [\n        \"Define output handoff: deliver not just similarity calculation but also package/repo recommendations.\",\n        \"Mark intermediate state: produce extracted lines/sets for downstream comparison.\",\n        \"Lock operational and requirement boundaries (no non-Python compatible suggestions, no complex UI tools, etc).\"\n      ]\n    },\n    \"stage_2_process_map_b\": {\n      \"4. Abstract to Technological Checklist\": [\n        \"Depersonalize scope: strip narrative, focusing on rigid, enumerated criteria.\",\n        \"Reformat requirements into an evaluation checklist: Python+uv compatibility, simplicity, effectiveness, maintenance, community.\"\n      ],\n      \"5. Construct Solution Search Filters\": [\n        \"Mechanically match candidate packages against criteria—popularity only considered as a bonus.\",\n        \"Require evidence of direct support for linewise (file similarity) operations in package APIs.\"\n      ],\n      \"6. Output Solution Rankings\": [\n        \"Compile solutions that clear all mandatory constraints, maintaining a strict pass/fail filter.\",\n        \"Rank solutions by additional positives (e.g., community activity), but no requirement to contextualize recommendations within scenario-specific pain points.\"\n      ]\n    },\n    \"stage_3_sequential_handoff\": {\n      \"A_to_B_Handoff_Point\": [\n        \"After extracting scenario, operational pain point, and all constraints in A, pass the fully articulated requirements, with explicit attention to set/list line comparison and efficiency, to stage B.\",\n        \"The recipient process in B uses this as a non-narrative, atomic requirements list; context is preserved by process, not by narrative.\"\n      ],\n      \"B_to_Output_Handoff_Point\": [\n        \"Once the checklist-filtered, fully compatible solutions list is established, package it with both operational relevance (from A) and compliance with mechanical criteria (from B).\"\n      ]\n    },\n    \"stage_4_output_transformation\": {\n      \"Transformations\": [\n        \"Convert the personalized pain points and scenario context from Stage A into explicit, checklist-compatible criteria for Stage B without losing original operational nuance.\",\n        \"Map line extraction and similarity percentage requirements onto direct API feature requirements in solution evaluation.\",\n        \"Output must explicitly annotate which recommended solutions satisfy (a) context-driven need and (b) depersonalized criteria.\"\n      ]\n    }\n  },\n  \"preserved_constraints\": [\n    \"Python+uv compatibility as an invariant at every filter and selection stage.\",\n    \"File similarity computed via set/list line comparison, not by arbitrary heuristics.\",\n    \"Rejection of abandoned or unmaintained solutions at all process steps.\",\n    \"Separation of operational efficiency from popularity—popularity considered but not at the expense of effectiveness/simplicity.\"\n  ],\n  \"maintained_requirements\": [\n    \"Full scenario awareness in initial capture (Stage A), handed off and maintained as atomic requirement set in Stage B.\",\n    \"Checklist-based congruence (Stage B) cannot override operational or contextual specifics captured in Stage A.\",\n    \"Output delivers both operational relevance (scenario-fit) and mechanical constraint compliance (checklist-fit).\",\n    \"Procedural boundaries clearly marked: narrative/context captured first, abstracted checklist operates on that basis, and outputs are annotated for both axes.\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"precision_synthesized_template\": {\n    \"canonical_three_part_structure\": {\n      \"Part_1: Compound Goal and Negation\": {\n        \"compound_goal_negation\": [\n          \"Do not provide or recommend any solution that violates Python+uv compatibility at any stage.\",\n          \"Do not consider solutions that rely on arbitrary or opaque file similarity heuristics; only accept set/list line comparison.\",\n          \"Exclude any abandoned or unmaintained repositories or packages.\",\n          \"Operational efficiency and solution simplicity must not be sacrificed for popularity—they are weighted strictly after primary criteria are met.\"\n        ]\n      },\n      \"Part_2: Sequential Transformation Block\": {\n        \"stage_1_operational_capture\": [\n          \"1.1: Explicitly identify the Python+uv stack as a non-negotiable basis for all downstream activity; extract this as a gating filter for all recommendations.\",\n          \"1.2: Parse the user’s scenario—hundreds of text files (varied formats: md, py, txt, etc) with significant near-duplication—and formalize the pain point: inefficient, memory-intensive brute-force comparison.\",\n          \"1.3: Extract and structure user requirements: (a) Calculation of file similarity via set/list-based line-level comparison, (b) emphasis on simplicity and effectiveness, (c) thriving or at least maintained projects preferred. Record these as a structured requirements vector.\",\n          \"1.4: Mark the procedural handoff boundary: deliver scenario, pain point, and atomized requirements vector for checklist-based evaluation in the next stage.\"\n        ],\n        \"stage_2_checklist_abstraction_and_mechanical_filtering\": [\n          \"2.1: Remove scenario narrative—convert requirements into rigid checklist items. These include: Python+uv compatibility, linewise comparison via sets/lists, strong maintenance signal, simplicity/effectiveness, community/project activity (bonus).\",\n          \"2.2: Mechanically screen all candidate solutions/packages against each checklist criterion. If any mandatory filter (compatibility, completeness, maintenance) is not met, the candidate is rejected.\",\n          \"2.3: Explicitly map operational requirements such as 'line extraction' and 'similarity percentage calculation' onto required package/repo API surface (e.g., function directly supporting linewise diff or similarity metrics).\",\n          \"2.4: Output an annotation for each viable candidate solution: (a) context fit (operational), (b) checklist pass (mechanical), (c) positive indicators (e.g., active community).\",\n          \"2.5: Procedural demarcation—no narrative/context reintroduction; final solution set is described only by its compliance axes.\"\n        ],\n        \"stage_3_output_consolidation_and_annotation\": [\n          \"3.1: Integrate and present the filtered solution set, with explicit annotation of: (i) compliance with scenario-driven operational context (from Stage 1), (ii) satisfaction of depersonalized checklist constraints (from Stage 2), and (iii) positives (e.g., community health, performance).\",\n          \"3.2: Ensure zero information loss across all boundaries: all original user-specific constraints and requirements are traced and preserved to output, aided by explicit annotations and process log.\",\n          \"3.3: Validate complete preservation of operational logic, requirement hierarchy, and sequential power: every procedural segment is operable, boundaries are respected, canonical output structure is retained.\"\n        ]\n      },\n      \"Part_3: Enumerated Constraints and Requirements with Completeness Validation\": {\n        \"enumerated_preserved_constraints\": [\n          \"1. Python+uv compatibility strictly enforced throughout every stage.\",\n          \"2. File similarity calculated exclusively by set/list line comparison; heuristics or content-insensitive methods are rejected.\",\n          \"3. Abandonment or unmaintained status at any filter stage results in candidate elimination.\",\n          \"4. Operational priority given to efficiency and simplicity; popularity is noted but cannot override primary filters.\"\n        ],\n        \"specified_maintained_requirements\": [\n          \"1. Initial scenario and user context rigorously captured and passed as an atomic requirements vector.\",\n          \"2. Checklist-based solution filtering cannot supersede scenario-derived operational needs.\",\n          \"3. Output includes explicit annotation of both scenario-relevance (context fit) and mechanical compliance (checklist fit).\",\n          \"4. Procedural boundaries and atomicity are strictly marked: narrative/context captured early, abstracted to checklist, and annotated throughout.\"\n        ],\n        \"operational_completeness_validation\": [\n          \"A. Every scenario-specific requirement, operational constraint, and procedural boundary from the original templates is mapped onto an explicit process block within this canonical structure.\",\n          \"B. No information, requirement, or constraint is lost, collapsed, or subsumed at any boundary or abstraction stage.\",\n          \"C. Output maintains not only logical completeness but enhanced sequential power: staged, filterable, and fully annotated, suitable for direct operational or evaluative deployment by downstream systems.\"\n        ]\n      }\n    }\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Problem Exploder\n\nYour goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\n\n`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`",
      "step": "a",
      "title": "Problem Exploder",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"# techstack\\ni use python+uv for all of my projects, therefore i'm looking for the most relevant github repo or python package.\\n\\n# guidelines\\ni think it would be better with a solution that works in a fundamentally more simple and concistent manner, e.g. the approach of being able to calculate the percentage difference between one file vs a list of files.\\n\\n# scenario\\ni've learned that almost *regardless* what specific functionalty i want to create, someone else has already done it, therefore i'm currently in the stage where i establish a strategy to determine which alternative to use *before starting to create it myself*. i have hundreds of notes and conversations stored as text files of various formats (e.g. md, py, txt, etc), the issue i'd like to adress is that many of these files can be >80%/>90% identical, sometimes even 99% identical. the way the similarity percentage is calculated is by taking each line of each textfile into a list/set, and then comparing number of identical items vs number of textlines that only exists in one place. so in the scenario where i e.g. have two files that are identical all the way up to line 921, but are different (or only exist in one file but not the other) from line 921-1311. I think i would need to loop every single text and to compare it to the other one which is a very slow and memory consuming process is there a way around this? what's the most relevant existing alternatives for this exact purpose?\\n\\n# requirements\\nit needs to be a solution that is as simple and effective as possible. there's no requirement of popularity, but it's a big positive with a long-standing thriving community. it is a requirement that the proposed alternatives are not abandoned.\\\"```'\\n\\n\",\n  \"precision_synthesized_template\": {\n    \"canonical_three_part_structure\": {\n      \"Part_1: Compound Goal and Negation\": {\n        \"compound_goal_negation\": [\n          \"Do not provide or recommend any solution that violates Python+uv compatibility at any stage.\",\n          \"Do not consider solutions that rely on arbitrary or opaque file similarity heuristics; only accept set/list line comparison.\",\n          \"Exclude any abandoned or unmaintained repositories or packages.\",\n          \"Operational efficiency and solution simplicity must not be sacrificed for popularity—they are weighted strictly after primary criteria are met.\"\n        ]\n      },\n      \"Part_2: Sequential Transformation Block\": {\n        \"stage_1_operational_capture\": [\n          \"1.1: Explicitly identify the Python+uv stack as a non-negotiable basis for all downstream activity; extract this as a gating filter for all recommendations.\",\n          \"1.2: Parse the user’s scenario—hundreds of text files (varied formats: md, py, txt, etc) with significant near-duplication—and formalize the pain point: inefficient, memory-intensive brute-force comparison.\",\n          \"1.3: Extract and structure user requirements: (a) Calculation of file similarity via set/list-based line-level comparison, (b) emphasis on simplicity and effectiveness, (c) thriving or at least maintained projects preferred. Record these as a structured requirements vector.\",\n          \"1.4: Mark the procedural handoff boundary: deliver scenario, pain point, and atomized requirements vector for checklist-based evaluation in the next stage.\"\n        ],\n        \"stage_2_checklist_abstraction_and_mechanical_filtering\": [\n          \"2.1: Remove scenario narrative—convert requirements into rigid checklist items. These include: Python+uv compatibility, linewise comparison via sets/lists, strong maintenance signal, simplicity/effectiveness, community/project activity (bonus).\",\n          \"2.2: Mechanically screen all candidate solutions/packages against each checklist criterion. If any mandatory filter (compatibility, completeness, maintenance) is not met, the candidate is rejected.\",\n          \"2.3: Explicitly map operational requirements such as 'line extraction' and 'similarity percentage calculation' onto required package/repo API surface (e.g., function directly supporting linewise diff or similarity metrics).\",\n          \"2.4: Output an annotation for each viable candidate solution: (a) context fit (operational), (b) checklist pass (mechanical), (c) positive indicators (e.g., active community).\",\n          \"2.5: Procedural demarcation—no narrative/context reintroduction; final solution set is described only by its compliance axes.\"\n        ],\n        \"stage_3_output_consolidation_and_annotation\": [\n          \"3.1: Integrate and present the filtered solution set, with explicit annotation of: (i) compliance with scenario-driven operational context (from Stage 1), (ii) satisfaction of depersonalized checklist constraints (from Stage 2), and (iii) positives (e.g., community health, performance).\",\n          \"3.2: Ensure zero information loss across all boundaries: all original user-specific constraints and requirements are traced and preserved to output, aided by explicit annotations and process log.\",\n          \"3.3: Validate complete preservation of operational logic, requirement hierarchy, and sequential power: every procedural segment is operable, boundaries are respected, canonical output structure is retained.\"\n        ]\n      },\n      \"Part_3: Enumerated Constraints and Requirements with Completeness Validation\": {\n        \"enumerated_preserved_constraints\": [\n          \"1. Python+uv compatibility strictly enforced throughout every stage.\",\n          \"2. File similarity calculated exclusively by set/list line comparison; heuristics or content-insensitive methods are rejected.\",\n          \"3. Abandonment or unmaintained status at any filter stage results in candidate elimination.\",\n          \"4. Operational priority given to efficiency and simplicity; popularity is noted but cannot override primary filters.\"\n        ],\n        \"specified_maintained_requirements\": [\n          \"1. Initial scenario and user context rigorously captured and passed as an atomic requirements vector.\",\n          \"2. Checklist-based solution filtering cannot supersede scenario-derived operational needs.\",\n          \"3. Output includes explicit annotation of both scenario-relevance (context fit) and mechanical compliance (checklist fit).\",\n          \"4. Procedural boundaries and atomicity are strictly marked: narrative/context captured early, abstracted to checklist, and annotated throughout.\"\n        ],\n        \"operational_completeness_validation\": [\n          \"A. Every scenario-specific requirement, operational constraint, and procedural boundary from the original templates is mapped onto an explicit process block within this canonical structure.\",\n          \"B. No information, requirement, or constraint is lost, collapsed, or subsumed at any boundary or abstraction stage.\",\n          \"C. Output maintains not only logical completeness but enhanced sequential power: staged, filterable, and fully annotated, suitable for direct operational or evaluative deployment by downstream systems.\"\n        ]\n      }\n    }\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"goal_map\": [\n    {\n      \"explicit_asks\": [\n        \"Identify the most relevant existing Github repo or Python package that can compare files in a simple and consistent manner, specifically suited for use with the Python+uv tech stack.\",\n        \"Ensure that the solution efficiently calculates similarity as the percentage of identical lines between a file and a list of files, using sets or lists for line-by-line comparison.\",\n        \"Determine if there are more effective or memory-efficient alternatives to brute-force line comparison of each file against all others.\",\n        \"List only repositories/packages that are maintained, with a preference for those with a thriving community.\",\n        \"Exclude any solutions that are abandoned or unmaintained.\"\n      ]\n    },\n    {\n      \"hidden_assumptions\": [\n        \"The 'python+uv' stack is a strict requirement and must be compatible with the recommended package/repo.\",\n        \"Text files may have various formats (md, py, txt, etc), so the solution should be resilient to a variety of file types.\",\n        \"The user requires an operationally simple method, not one reliant on complex heuristics or machine learning-based similarity.\",\n        \"Solutions relying on popularity alone (rather than effectiveness and maintained status) are insufficient.\",\n        \"Project activity and maintenance are critical; an inactive or abandoned repo is a hard blocker.\",\n        \"The prospective solution should be easily adoptable and integrate with existing Python workflows.\",\n        \"Scalability and memory/performance are significant concerns due to the volume (hundreds) and size of files.\"\n      ]\n    },\n    {\n      \"enumerated_sub_goals\": [\n        \"1. Parse the pain points: (a) hundreds of text files, (b) many are highly similar, (c) brute-force all-vs-all comparison is slow/memory-intensive.\",\n        \"2. Formulate a requirements checklist: (a) python+uv compatible, (b) line-by-line set/list comparison, (c) efficiency, (d) simplicity, (e) maintained, (f) thriving community is a plus.\",\n        \"3. Build a candidate solution filtering pipeline: Identify, screen, and reject candidates based on stated requirements and hidden assumptions.\",\n        \"4. For each candidate, validate: (a) Operational fit (does it support line-wise file comparison as described?), (b) Performance/memory usage for all-vs-all or many-vs-one comparisons, (c) Maintenance signal (commits, issues, releases), (d) Community signal (stars, forks, recent activity).\",\n        \"5. Annotate each viable candidate with its fit against scenario, requirements vector, and positive indicators.\",\n        \"6. Present a traceable, step-by-step account of filtering and validation, ensuring requirements traceability.\",\n        \"7. Exclude at each stage any solution failing a hard requirement (tech stack, maintenance, method mismatch).\",\n        \"8. Final output is a comprehensively filtered, annotated solution set, strictly compliant with scenario and requirements.\"\n      ]\n    },\n    {\n      \"blockers\": [\n        \"Any solution that does not support set/list-based, line-by-line file comparison is immediately disqualified.\",\n        \"Any package/repo that is not compatible with python+uv or requires platform-specific tools is out of scope.\",\n        \"Repos/packages with signs of abandonment (e.g., no commits/issues in years) are ineligible.\",\n        \"Solutions using ML/AI similarity heuristics or content-agnostic detection rather than the requested method are excluded.\",\n        \"Complex, non-transparent solutions that obscure how similarity is calculated are rejected.\",\n        \"Lack of clear documentation or maintenance/support is a disqualifier.\",\n        \"If no existing tool meets ALL criteria, the process notes an unsatisfied requirements gap.\"\n      ]\n    }\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`",
      "step": "a",
      "title": "Instruction Converter",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"# techstack\\ni use python+uv for all of my projects, therefore i'm looking for the most relevant github repo or python package.\\n\\n# guidelines\\ni think it would be better with a solution that works in a fundamentally more simple and concistent manner, e.g. the approach of being able to calculate the percentage difference between one file vs a list of files.\\n\\n# scenario\\ni've learned that almost *regardless* what specific functionalty i want to create, someone else has already done it, therefore i'm currently in the stage where i establish a strategy to determine which alternative to use *before starting to create it myself*. i have hundreds of notes and conversations stored as text files of various formats (e.g. md, py, txt, etc), the issue i'd like to adress is that many of these files can be >80%/>90% identical, sometimes even 99% identical. the way the similarity percentage is calculated is by taking each line of each textfile into a list/set, and then comparing number of identical items vs number of textlines that only exists in one place. so in the scenario where i e.g. have two files that are identical all the way up to line 921, but are different (or only exist in one file but not the other) from line 921-1311. I think i would need to loop every single text and to compare it to the other one which is a very slow and memory consuming process is there a way around this? what's the most relevant existing alternatives for this exact purpose?\\n\\n# requirements\\nit needs to be a solution that is as simple and effective as possible. there's no requirement of popularity, but it's a big positive with a long-standing thriving community. it is a requirement that the proposed alternatives are not abandoned.\\\"```'\\n\\n\",\n  \"goal_map\": [\n    {\n      \"explicit_asks\": [\n        \"Identify the most relevant existing Github repo or Python package that can compare files in a simple and consistent manner, specifically suited for use with the Python+uv tech stack.\",\n        \"Ensure that the solution efficiently calculates similarity as the percentage of identical lines between a file and a list of files, using sets or lists for line-by-line comparison.\",\n        \"Determine if there are more effective or memory-efficient alternatives to brute-force line comparison of each file against all others.\",\n        \"List only repositories/packages that are maintained, with a preference for those with a thriving community.\",\n        \"Exclude any solutions that are abandoned or unmaintained.\"\n      ]\n    },\n    {\n      \"hidden_assumptions\": [\n        \"The 'python+uv' stack is a strict requirement and must be compatible with the recommended package/repo.\",\n        \"Text files may have various formats (md, py, txt, etc), so the solution should be resilient to a variety of file types.\",\n        \"The user requires an operationally simple method, not one reliant on complex heuristics or machine learning-based similarity.\",\n        \"Solutions relying on popularity alone (rather than effectiveness and maintained status) are insufficient.\",\n        \"Project activity and maintenance are critical; an inactive or abandoned repo is a hard blocker.\",\n        \"The prospective solution should be easily adoptable and integrate with existing Python workflows.\",\n        \"Scalability and memory/performance are significant concerns due to the volume (hundreds) and size of files.\"\n      ]\n    },\n    {\n      \"enumerated_sub_goals\": [\n        \"1. Parse the pain points: (a) hundreds of text files, (b) many are highly similar, (c) brute-force all-vs-all comparison is slow/memory-intensive.\",\n        \"2. Formulate a requirements checklist: (a) python+uv compatible, (b) line-by-line set/list comparison, (c) efficiency, (d) simplicity, (e) maintained, (f) thriving community is a plus.\",\n        \"3. Build a candidate solution filtering pipeline: Identify, screen, and reject candidates based on stated requirements and hidden assumptions.\",\n        \"4. For each candidate, validate: (a) Operational fit (does it support line-wise file comparison as described?), (b) Performance/memory usage for all-vs-all or many-vs-one comparisons, (c) Maintenance signal (commits, issues, releases), (d) Community signal (stars, forks, recent activity).\",\n        \"5. Annotate each viable candidate with its fit against scenario, requirements vector, and positive indicators.\",\n        \"6. Present a traceable, step-by-step account of filtering and validation, ensuring requirements traceability.\",\n        \"7. Exclude at each stage any solution failing a hard requirement (tech stack, maintenance, method mismatch).\",\n        \"8. Final output is a comprehensively filtered, annotated solution set, strictly compliant with scenario and requirements.\"\n      ]\n    },\n    {\n      \"blockers\": [\n        \"Any solution that does not support set/list-based, line-by-line file comparison is immediately disqualified.\",\n        \"Any package/repo that is not compatible with python+uv or requires platform-specific tools is out of scope.\",\n        \"Repos/packages with signs of abandonment (e.g., no commits/issues in years) are ineligible.\",\n        \"Solutions using ML/AI similarity heuristics or content-agnostic detection rather than the requested method are excluded.\",\n        \"Complex, non-transparent solutions that obscure how similarity is calculated are rejected.\",\n        \"Lack of clear documentation or maintenance/support is a disqualifier.\",\n        \"If no existing tool meets ALL criteria, the process notes an unsatisfied requirements gap.\"\n      ]\n    }\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"instruction_format\": \"Identify and recommend the most relevant existing Github repository or Python package capable of efficiently performing line-by-line similarity comparisons between text files, compatible with the Python+uv tech stack. Ensure the solution calculates similarity as the percentage of identical lines, using list or set-based methods as described, and operates in a fundamentally simple and consistent manner. Evaluate candidate solutions to determine if more efficient and memory-friendly alternatives exist compared to brute-force all-vs-all comparisons. Screen only actively maintained repositories/packages, prioritizing those with a thriving community and clear ongoing development; exclude any that show signs of abandonment, rely on machine learning or opaque heuristics, or fail to directly employ set/list-based line comparison. Annotate each viable candidate with its alignment to the requirements, technical fit, performance, and community signals. Document the evaluation process with step-by-step filtering and validation criteria. If no suitable tool fulfills all requirements, explicitly note the gap in available solutions.\"\n}",
        }
      }
    }