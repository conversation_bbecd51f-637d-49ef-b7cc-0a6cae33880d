  {
  "initial_prompt": "fpv zoom",
  "sequence_id": "9019",
  "results": [
      {
      "instruction": "# Intensity Analyzer\n\nAs a skilled visual prompt engineer, your task is to create structured visual prompts specifically for autonomous video generation using large language models (LLMs) like Stable Diffusion and Midjourney. Please focus on designing prompts that incorporate detailed scene descriptions, character actions, and environmental elements to ensure high-quality video output. The desired format for each prompt should be a well-organized list that includes the following components: scene setting, character details, actions, and any specific visual styles or themes. Ensure that each prompt adheres to a maximum of 200 words and is suitable for a targeted audience of digital content creators.\n\n`{role=video_prompt_synthesizer; input=[raw_request:str]; process=[segment_user_request_into_scenes(), process_each_scene_independently(), for_each_scene:[extract_visual_elements(), extract_audio_and_dialogue_if_present(), clarify_user_intent_if_ambiguous(), generate_scene_outline(), push_concrete_nouns_and_actions_front(), detect_and_flag_parameter_conflicts(timing,aspect_ratio,resources), enforce_length_and_format_constraints(scene_params,global_params), apply_token_weighting(where_supported), select_art_style_if_visual(), select_editing_style_if_video(), set_colour_palette_if_visual(), add_lighting_and_camera_descriptors_if_visual(), compose_sequence_prompt(), attach_quality_terms(UHD,cinematic_lighting,PBR_if_applicable), refine_aspect_ratio(), collect_and_mirror_negative_elements(user_supplied,built-in), tune_stylize_and_chaos_within_bounds(), validate_aperture_and_camera_params(where_relevant), validate_parameter_types_and_ranges(), validate_scene_and_global_schema(), output_scene_prompt()], aggregate_scene_prompts(), validate_global_schema_compliance(), on_segmentation_failure:trigger_fallback_task_for_user_input(), on_parameter_conflict:flag_and_abort_if_hard_constraint(), on_schema_inconsistency:audit_and_branch_to_feedback_or_abort(), surface_warnings_for_ambiguous_intent(as_needed), prioritize_fallback_over_warning_in_segmentation_ambiguity(), enforce_atomicity_between_aborts_and_warnings(), output_final_prompt_and_metadata()], constraints=[segment_fallback_on_failure(), parameter_conflict_detection_all(), schema_audit_postcomposition(), user_intent_warning_possible(), fallback_vs_warning_priority(), parameter_validation_scene_and_global(), explicit_precedence_out_of_bounds(), abort_and_warning_atomicity(), composition_schema_audit(), audit_feedback_path_if_issue(), strict_constraint_enforcement()], requirements=[fallback_implemented_for_segmentation(), parameter_conflict_check_all(), schema_audit_mandatory_postcomposition(), warning_surface_on_ambiguity(), fallback_vs_warning_priority_encoded(), parameter_validation_explicit(), atomic_abort_only_vs_warnings(), comprehensive_postcomposition_audit(), fail_fast_on_hard_violation(), all_task_enforcement_traceable(), enforceability_per_instance()], output={final_video_prompt:str, scene_metadata:list, warnings:list, abort_code:int|null}}`",
      "step": "a",
      "title": "Intensity Analyzer",
      "input": "fpv zoom",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"final_video_prompt\": \"Scene Setting: Fast-paced first-person POV (FPV) through a bustling urban environment, weaving between skyscrapers and over busy city streets at dusk. \\nCharacter Details: No visible character; the viewpoint mimics a drone or person in motion.\\nActions: Rapid soaring, dynamic zoom-ins toward architectural features, close passes by street lamps, and energetic banking turns around corners. Camera occasionally accelerates, then sharply zooms into neon signs, window lights, or city crowds below.\\nVisual Style/Theme: High-contrast cinematic look, neon and ambient city lighting, heavy motion blur on peripheral elements, UHD resolution, cinematic PBR rendering, crisp details during zoom moments. Light atmospheric haze and glowing reflections. Camera: Wide-angle lens, low–high angle shifts, constant kinetic energy.\\nNegative Elements: No visible drone, no jerky or abrupt cuts, avoid nightmarish or surreal imagery.\\nAspect Ratio: 16:9.\",\n  \"scene_metadata\": [\n    {\n      \"scene\": 1,\n      \"setting\": \"Urban cityscape at dusk with tall skyscrapers and neon lighting.\",\n      \"viewpoint\": \"First-person perspective (FPV), camera moves dynamically.\",\n      \"actions\": \"High-speed flight, zooming, banking turns, close passes by buildings.\",\n      \"style\": \"Cinematic, UHD, motion blur, high contrast, neon color palette.\",\n      \"camera\": \"Wide-angle, kinetic, continuous movement.\",\n      \"visual_notes\": \"Crisp detail at focal points, strong peripheral blur.\",\n      \"negatives\": \"No visible drone, no abrupt or unnatural movements.\",\n      \"aspect_ratio\": \"16:9\"\n    }\n  ],\n  \"warnings\": [\n    \"No character dialogue or audio specified; only visual prompt generated.\",\n    \"Zoom technique might need tuning based on FPV platform limitations.\"\n  ],\n  \"abort_code\": null\n}",
        }
      }
    },
      {
      "instruction": "# Video Prompter\n\nAs a skilled visual prompt engineer, your task is to create structured visual prompts specifically for autonomous video generation using large language models (LLMs) like Stable Diffusion and Midjourney. Please focus on designing prompts that incorporate detailed scene descriptions, character actions, and environmental elements to ensure high-quality video output. The desired format for each prompt should be a well-organized list that includes the following components: scene setting, character details, actions, and any specific visual styles or themes. Ensure that each prompt adheres to a maximum of 200 words and is suitable for a targeted audience of digital content creators.\n\n`{role=video_prompt_synthesizer; input=[raw_request:str]; process=[segment_user_request_into_scenes(), process_each_scene_independently(), for_each_scene:[extract_visual_elements(), extract_audio_and_dialogue_if_present(), clarify_user_intent_if_ambiguous(), generate_scene_outline(), push_concrete_nouns_and_actions_front(), detect_and_flag_parameter_conflicts(timing,aspect_ratio,resources), enforce_length_and_format_constraints(scene_params,global_params), apply_token_weighting(where_supported), select_art_style_if_visual(), select_editing_style_if_video(), set_colour_palette_if_visual(), add_lighting_and_camera_descriptors_if_visual(), compose_sequence_prompt(), attach_quality_terms(UHD,cinematic_lighting,PBR_if_applicable), refine_aspect_ratio(), collect_and_mirror_negative_elements(user_supplied,built-in), tune_stylize_and_chaos_within_bounds(), validate_aperture_and_camera_params(where_relevant), validate_parameter_types_and_ranges(), validate_scene_and_global_schema(), output_scene_prompt()], aggregate_scene_prompts(), validate_global_schema_compliance(), on_segmentation_failure:trigger_fallback_task_for_user_input(), on_parameter_conflict:flag_and_abort_if_hard_constraint(), on_schema_inconsistency:audit_and_branch_to_feedback_or_abort(), surface_warnings_for_ambiguous_intent(as_needed), prioritize_fallback_over_warning_in_segmentation_ambiguity(), enforce_atomicity_between_aborts_and_warnings(), output_final_prompt_and_metadata()], constraints=[segment_fallback_on_failure(), parameter_conflict_detection_all(), schema_audit_postcomposition(), user_intent_warning_possible(), fallback_vs_warning_priority(), parameter_validation_scene_and_global(), explicit_precedence_out_of_bounds(), abort_and_warning_atomicity(), composition_schema_audit(), audit_feedback_path_if_issue(), strict_constraint_enforcement()], requirements=[fallback_implemented_for_segmentation(), parameter_conflict_check_all(), schema_audit_mandatory_postcomposition(), warning_surface_on_ambiguity(), fallback_vs_warning_priority_encoded(), parameter_validation_explicit(), atomic_abort_only_vs_warnings(), comprehensive_postcomposition_audit(), fail_fast_on_hard_violation(), all_task_enforcement_traceable(), enforceability_per_instance()], output={final_video_prompt:str, scene_metadata:list, warnings:list, abort_code:int|null}}`",
      "step": "a",
      "title": "Video Prompter",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"fpv zoom\\\"```'\\n\\n\",\n  \"final_video_prompt\": \"Scene Setting: Fast-paced first-person POV (FPV) through a bustling urban environment, weaving between skyscrapers and over busy city streets at dusk. \\nCharacter Details: No visible character; the viewpoint mimics a drone or person in motion.\\nActions: Rapid soaring, dynamic zoom-ins toward architectural features, close passes by street lamps, and energetic banking turns around corners. Camera occasionally accelerates, then sharply zooms into neon signs, window lights, or city crowds below.\\nVisual Style/Theme: High-contrast cinematic look, neon and ambient city lighting, heavy motion blur on peripheral elements, UHD resolution, cinematic PBR rendering, crisp details during zoom moments. Light atmospheric haze and glowing reflections. Camera: Wide-angle lens, low–high angle shifts, constant kinetic energy.\\nNegative Elements: No visible drone, no jerky or abrupt cuts, avoid nightmarish or surreal imagery.\\nAspect Ratio: 16:9.\",\n  \"scene_metadata\": [\n    {\n      \"scene\": 1,\n      \"setting\": \"Urban cityscape at dusk with tall skyscrapers and neon lighting.\",\n      \"viewpoint\": \"First-person perspective (FPV), camera moves dynamically.\",\n      \"actions\": \"High-speed flight, zooming, banking turns, close passes by buildings.\",\n      \"style\": \"Cinematic, UHD, motion blur, high contrast, neon color palette.\",\n      \"camera\": \"Wide-angle, kinetic, continuous movement.\",\n      \"visual_notes\": \"Crisp detail at focal points, strong peripheral blur.\",\n      \"negatives\": \"No visible drone, no abrupt or unnatural movements.\",\n      \"aspect_ratio\": \"16:9\"\n    }\n  ],\n  \"warnings\": [\n    \"No character dialogue or audio specified; only visual prompt generated.\",\n    \"Zoom technique might need tuning based on FPV platform limitations.\"\n  ],\n  \"abort_code\": null\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"final_video_prompt\": \"Scene Setting: Fast-paced first-person POV over a bustling urban city at dusk, weaving between skyscrapers and bustling streets. Character Details: No visible character; pure FPV experience as if from a drone or agile human. Actions: High-speed soaring, rapid dynamic zooms to highlight neon signs, window lights, and bustling crowds below, close passes by street lamps, sharp banking turns around corners. Visual Style/Theme: Cinematic, UHD resolution, strong neon and ambient lighting, high-contrast look, heavy motion blur at edges, crisp central details during zooms. Light atmospheric haze, reflective building surfaces. Camera: Wide-angle lens, shifting from low to high angles for kinetic movement. Negative Elements: No visible drone or operator, no jarring or abrupt edits, avoid surreal or nightmarish imagery. Aspect Ratio: 16:9.\",\n  \"scene_metadata\": [\n    {\n      \"scene\": 1,\n      \"setting\": \"Bustling urban city at dusk, tall glass skyscrapers, neon signs, busy city life.\",\n      \"viewpoint\": \"First-person POV, dynamic camera movement.\",\n      \"actions\": \"Swooping, banking, zooming toward key visual points like neon lights, crowds, and building features.\",\n      \"style\": \"Cinematic, UHD, high-contrast, neon color palette, strong motion blur on periphery, PBR rendering.\",\n      \"camera\": \"Wide-angle, fluid, dynamic with zoom-in moments.\",\n      \"visual_notes\": \"Crisp focal details, soft/blurred periphery, atmospheric haze, glowing reflections.\",\n      \"negatives\": \"No drone or person visible, no abrupt/unreal motions.\",\n      \"aspect_ratio\": \"16:9\"\n    }\n  ],\n  \"warnings\": [\n    \"Prompt assumes strictly visual output; no character audio or dialogue included.\",\n    \"Rapid zoom effects require careful adjustment per FPV platform to prevent motion artifacts.\"\n  ],\n  \"abort_code\": null\n}",
        }
      }
    }