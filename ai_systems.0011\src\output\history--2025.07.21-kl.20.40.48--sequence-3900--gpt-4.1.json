  {
  "initial_prompt": "Please create a new sequence called `9020-generators-instructions--brochure_print_designer` intended to transform inputs into LLM-instructions specifically designed to deliver prompts uniquely designed to deliver prompts that serve as expert tri-fold brochure instructions (that are visually dynamic, print-perfect, youth-centric, subtly persuasive, and faithfully on-brand).\n\nExample input:\n```\n### Optimized LLM Instruction for Tri-Fold Brochure Print Designer  \n**Client:** Ringerike Landskap AS  \n**Objective:** Produce a tri-fold brochure (HTML, print-optimized, youth-oriented, on-brand, subtle landscape service focus, seamless web integration).\n\n#### Directed Prompt\n\nConstruct a detailed instruction set for a professional brochure print designer assigned to create a tri-fold brochure in HTML format for Ringerike Landskap AS. The instruction template must:\n\n- **Print-Optimized HTML:**\n  - Specify HTML structure suitable for tri-fold layout; include visual fold guides, safe print margins, and bleed areas.\n  - Require output to be highly print-ready (CMYK color awareness, suitable image resolutions, print font embeds).\n\n- **Web & Print Dual Use:**\n  - Demand seamless rendering at www.ringerikelandskap.no (responsive, accessible design, optimized asset loading).\n  - Instruct on ensuring print and web views maintain visual and functional consistency.\n\n- **Audience & Style:**\n  - Direct the tone, content, and visuals to appeal to young people.\n    - Explicit instructions for: vibrant or playful color palettes, energetic and inclusive imagery, modern and readable typography, concise and engaging copywriting.\n\n- **Section/Subpanel Design:**\n  - Define each tri-fold panel’s purpose and messaging flow.\n  - Require subtle and authentic emphasis on the company’s landscape services—favor storytelling, softer value props, and lifestyle imagery over hard-sell language.\n\n- **Brand Adherence:**\n  - Clearly state requirements for:\n    - Logo placement (location, sizing standards).\n    - Brand color palette and font usage per Ringerike Landskap AS guidelines.\n    - Inclusion of brand secondary elements or patterns, if available.\n\n- **Call to Action:**\n  - Instruct to include a youth-relevant CTA (e.g., “Join our movement,” “Start your green journey,” etc.).\n  - Ensure CTA placement is visually prominent yet blends naturally within the brochure flow.\n\n- **Deliverables & QA:**\n  - HTML/CSS source files, high-resolution print PDF, style guide summary (including brand elements and usage).\n  - Structured pre-print and pre-publish QA process:\n    - Fold alignment, margin checks, color accuracy, font embedding, and imagery clarity.\n  - Handoff documentation covering print vendor specs and web embedding steps.\n\n#### Trajectory Outline\n\n1. **Requirements Extraction:**\n   - Capture project goals, audience nuance, and branding intent.\n   - Document specifications for both print and web viability.\n2. **Tri-Fold HTML Structure:**\n   - Map out logical division for six-panel design (front, back, inner spreads).\n   - Guide on HTML/CSS practices supporting both on-screen and print fidelity.\n3. **Visual & Content Direction:**\n   - Specify color, imagery, typographic, and narrative attributes resonant with youth.\n   - Provide real-world brand application samples if available.\n4. **Print Optimization:**\n   - Dictate color mode, bleed, margins (3mm minimum), and recommended DPI (minimum 300dpi).\n   - Check list for pre-print: font/license clearance, alignment, ink limits.\n5. **Deliverables:**\n   - HTML source, print PDF proof, concise brand/asset usage guide.\n6. **Instruction Flow:**\n   - Sequence from initial brief review, early sketch/mockup, approvals, refinement, to final handoff.\n7. **Iterative Improvement:**\n   - Define regular review and feedback checkpoints with stakeholders (internal and client-side).\n   - Prompt for clear feedback documentation and revision logging.\n8. **Anchoring to Outcomes:**\n   - Require that every design choice be referenced to its impact on: youth engagement, brand coherence, print/web practicality.\n9. **Self-Review Checks:**\n   - Mandate designer’s self-audit against the full brief at each milestone to prevent scope drift and ensure comprehensive coverage of requirements.\n```",
  "sequence_id": "3900",
  "results": [
      {
      "instruction": "# Hard Critique\n\nYour goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:\n\n`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,2.0], flaw_analysis:str, alternative_enhancements:str[3]}}`",
      "step": "a",
      "title": "Hard Critique",
      "input": "Please create a new sequence called `9020-generators-instructions--brochure_print_designer` intended to transform inputs into LLM-instructions specifically designed to deliver prompts uniquely designed to deliver prompts that serve as expert tri-fold brochure instructions (that are visually dynamic, print-perfect, youth-centric, subtly persuasive, and faithfully on-brand).\n\nExample input:\n```\n### Optimized LLM Instruction for Tri-Fold Brochure Print Designer  \n**Client:** Ringerike Landskap AS  \n**Objective:** Produce a tri-fold brochure (HTML, print-optimized, youth-oriented, on-brand, subtle landscape service focus, seamless web integration).\n\n#### Directed Prompt\n\nConstruct a detailed instruction set for a professional brochure print designer assigned to create a tri-fold brochure in HTML format for Ringerike Landskap AS. The instruction template must:\n\n- **Print-Optimized HTML:**\n  - Specify HTML structure suitable for tri-fold layout; include visual fold guides, safe print margins, and bleed areas.\n  - Require output to be highly print-ready (CMYK color awareness, suitable image resolutions, print font embeds).\n\n- **Web & Print Dual Use:**\n  - Demand seamless rendering at www.ringerikelandskap.no (responsive, accessible design, optimized asset loading).\n  - Instruct on ensuring print and web views maintain visual and functional consistency.\n\n- **Audience & Style:**\n  - Direct the tone, content, and visuals to appeal to young people.\n    - Explicit instructions for: vibrant or playful color palettes, energetic and inclusive imagery, modern and readable typography, concise and engaging copywriting.\n\n- **Section/Subpanel Design:**\n  - Define each tri-fold panel’s purpose and messaging flow.\n  - Require subtle and authentic emphasis on the company’s landscape services—favor storytelling, softer value props, and lifestyle imagery over hard-sell language.\n\n- **Brand Adherence:**\n  - Clearly state requirements for:\n    - Logo placement (location, sizing standards).\n    - Brand color palette and font usage per Ringerike Landskap AS guidelines.\n    - Inclusion of brand secondary elements or patterns, if available.\n\n- **Call to Action:**\n  - Instruct to include a youth-relevant CTA (e.g., “Join our movement,” “Start your green journey,” etc.).\n  - Ensure CTA placement is visually prominent yet blends naturally within the brochure flow.\n\n- **Deliverables & QA:**\n  - HTML/CSS source files, high-resolution print PDF, style guide summary (including brand elements and usage).\n  - Structured pre-print and pre-publish QA process:\n    - Fold alignment, margin checks, color accuracy, font embedding, and imagery clarity.\n  - Handoff documentation covering print vendor specs and web embedding steps.\n\n#### Trajectory Outline\n\n1. **Requirements Extraction:**\n   - Capture project goals, audience nuance, and branding intent.\n   - Document specifications for both print and web viability.\n2. **Tri-Fold HTML Structure:**\n   - Map out logical division for six-panel design (front, back, inner spreads).\n   - Guide on HTML/CSS practices supporting both on-screen and print fidelity.\n3. **Visual & Content Direction:**\n   - Specify color, imagery, typographic, and narrative attributes resonant with youth.\n   - Provide real-world brand application samples if available.\n4. **Print Optimization:**\n   - Dictate color mode, bleed, margins (3mm minimum), and recommended DPI (minimum 300dpi).\n   - Check list for pre-print: font/license clearance, alignment, ink limits.\n5. **Deliverables:**\n   - HTML source, print PDF proof, concise brand/asset usage guide.\n6. **Instruction Flow:**\n   - Sequence from initial brief review, early sketch/mockup, approvals, refinement, to final handoff.\n7. **Iterative Improvement:**\n   - Define regular review and feedback checkpoints with stakeholders (internal and client-side).\n   - Prompt for clear feedback documentation and revision logging.\n8. **Anchoring to Outcomes:**\n   - Require that every design choice be referenced to its impact on: youth engagement, brand coherence, print/web practicality.\n9. **Self-Review Checks:**\n   - Mandate designer’s self-audit against the full brief at each milestone to prevent scope drift and ensure comprehensive coverage of requirements.\n```",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"enhancement_score\": 0.8,\n  \"flaw_analysis\": \"The so-called 'enhanced' rendering of the original prompt introduces several problematic shifts and erosions. Core element comparison reveals information loss: the exhaustive structure and layer-by-layer breakdown in the original is unambiguously clearer and more actionable than the summarized, ambiguously worded 'sequence' request. The original provides an explicit example instruction set complete with hierarchical categories, concatenated requirements, QA checklists, and process narratives; the enhancement request reduces this to a vague meta-command, omitting specificity about formatting, order, and deliverable standards. Clarity is compromised by introducing terms like 'uniquely designed to deliver prompts', which is tautological, recursively confusing, and diverts from the original’s precise context (it wants LLM prompts that yield brochure instructions, NOT mere instructions about prompt delivery). Information loss is substantial because the original maps the process from requirements gathering to iterative review, with nuanced detail about print and web coexistence, which is not even referenced in the enhancement. The ambiguity score is 8/10, signaling major clarity and expectation failures. There is noise introduction via redundant phrasings and over-broad adjectives. The tone derails from instructive and professional (as in the original) to syntactically muddied and incomplete. Impact is diluted: the original arms the print designer with step-by-step actions, expected outcomes, and checks; the enhancement resorts to meta-layer abstraction, largely unusable for actual prompt development. Coherence breaks down because procedural specificity, deliverable breakdowns, and even brand-integration strategies are glossed over or omitted; scope and intent become muddled. In total, the 'enhanced' version is indirect, vague, and recursively self-referential, drastically lowering utility, clarity, and actionable precision.\",\n  \"alternative_enhancements\": [\n    \"Reformat the original example into a reusable template with clearly demarcated placeholders for client, objective, and panel specifics, while explicitly instructing LLMs to output all required structural, print, and brand steps in a locked sequence.\",\n    \"Condense the original into a streamlined checklist format, retaining every unique deliverable, process step, and QA requirement, while adding instruction to reference actual brand guidelines if provided.\",\n    \"Frame the instruction as a modular pipeline: requirements analysis, print/design specification output, visual and copy guidelines, QA checkpoints, and output documentation, each annotated with minimum standards and youth-centric engagement tactics, for consistent LLM replication.\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Trajectory Director\n\nYour goal is not to **process** the input in a vacuum, but to **steer** it—deriving the highest-value constructive trajectory and encoding that path as the very next set of instructions. Execute as trajectory-setting directive generator:\n\n`{role=trajectory_director; input=[raw_prompt:any]; process=[extract_core_objective(), infer_desired_end_state(constructive_bias=True), map_high_value_intermediate_targets(), synthesize_directional_commands(), embed_self-improving_guidelines(), validate_alignment_with_constructive_principles()]; constraints=[mandatory_constructive_vector(), preserve_original_contextual_integrity(), forbid_nil_direction_outputs(), zero_conversational_fillers()]; requirements=[explicit_next-step_directives(), trajectory_map:list, self_reinforcing_instruction_pattern()], output={directed_prompt:str, trajectory_outline:list}}`",
      "step": "a",
      "title": "Trajectory Director",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Please create a new sequence called `9020-generators-instructions--brochure_print_designer` intended to transform inputs into LLM-instructions specifically designed to deliver prompts uniquely designed to deliver prompts that serve as expert tri-fold brochure instructions (that are visually dynamic, print-perfect, youth-centric, subtly persuasive, and faithfully on-brand).\\n\\nExample input:\\n```\\n### Optimized LLM Instruction for Tri-Fold Brochure Print Designer  \\n**Client:** Ringerike Landskap AS  \\n**Objective:** Produce a tri-fold brochure (HTML, print-optimized, youth-oriented, on-brand, subtle landscape service focus, seamless web integration).\\n\\n#### Directed Prompt\\n\\nConstruct a detailed instruction set for a professional brochure print designer assigned to create a tri-fold brochure in HTML format for Ringerike Landskap AS. The instruction template must:\\n\\n- **Print-Optimized HTML:**\\n  - Specify HTML structure suitable for tri-fold layout; include visual fold guides, safe print margins, and bleed areas.\\n  - Require output to be highly print-ready (CMYK color awareness, suitable image resolutions, print font embeds).\\n\\n- **Web & Print Dual Use:**\\n  - Demand seamless rendering at www.ringerikelandskap.no (responsive, accessible design, optimized asset loading).\\n  - Instruct on ensuring print and web views maintain visual and functional consistency.\\n\\n- **Audience & Style:**\\n  - Direct the tone, content, and visuals to appeal to young people.\\n    - Explicit instructions for: vibrant or playful color palettes, energetic and inclusive imagery, modern and readable typography, concise and engaging copywriting.\\n\\n- **Section/Subpanel Design:**\\n  - Define each tri-fold panel’s purpose and messaging flow.\\n  - Require subtle and authentic emphasis on the company’s landscape services—favor storytelling, softer value props, and lifestyle imagery over hard-sell language.\\n\\n- **Brand Adherence:**\\n  - Clearly state requirements for:\\n    - Logo placement (location, sizing standards).\\n    - Brand color palette and font usage per Ringerike Landskap AS guidelines.\\n    - Inclusion of brand secondary elements or patterns, if available.\\n\\n- **Call to Action:**\\n  - Instruct to include a youth-relevant CTA (e.g., “Join our movement,” “Start your green journey,” etc.).\\n  - Ensure CTA placement is visually prominent yet blends naturally within the brochure flow.\\n\\n- **Deliverables & QA:**\\n  - HTML/CSS source files, high-resolution print PDF, style guide summary (including brand elements and usage).\\n  - Structured pre-print and pre-publish QA process:\\n    - Fold alignment, margin checks, color accuracy, font embedding, and imagery clarity.\\n  - Handoff documentation covering print vendor specs and web embedding steps.\\n\\n#### Trajectory Outline\\n\\n1. **Requirements Extraction:**\\n   - Capture project goals, audience nuance, and branding intent.\\n   - Document specifications for both print and web viability.\\n2. **Tri-Fold HTML Structure:**\\n   - Map out logical division for six-panel design (front, back, inner spreads).\\n   - Guide on HTML/CSS practices supporting both on-screen and print fidelity.\\n3. **Visual & Content Direction:**\\n   - Specify color, imagery, typographic, and narrative attributes resonant with youth.\\n   - Provide real-world brand application samples if available.\\n4. **Print Optimization:**\\n   - Dictate color mode, bleed, margins (3mm minimum), and recommended DPI (minimum 300dpi).\\n   - Check list for pre-print: font/license clearance, alignment, ink limits.\\n5. **Deliverables:**\\n   - HTML source, print PDF proof, concise brand/asset usage guide.\\n6. **Instruction Flow:**\\n   - Sequence from initial brief review, early sketch/mockup, approvals, refinement, to final handoff.\\n7. **Iterative Improvement:**\\n   - Define regular review and feedback checkpoints with stakeholders (internal and client-side).\\n   - Prompt for clear feedback documentation and revision logging.\\n8. **Anchoring to Outcomes:**\\n   - Require that every design choice be referenced to its impact on: youth engagement, brand coherence, print/web practicality.\\n9. **Self-Review Checks:**\\n   - Mandate designer’s self-audit against the full brief at each milestone to prevent scope drift and ensure comprehensive coverage of requirements.\\n```\\\"```'\\n\\n\",\n  \"enhancement_score\": 0.8,\n  \"flaw_analysis\": \"The so-called 'enhanced' rendering of the original prompt introduces several problematic shifts and erosions. Core element comparison reveals information loss: the exhaustive structure and layer-by-layer breakdown in the original is unambiguously clearer and more actionable than the summarized, ambiguously worded 'sequence' request. The original provides an explicit example instruction set complete with hierarchical categories, concatenated requirements, QA checklists, and process narratives; the enhancement request reduces this to a vague meta-command, omitting specificity about formatting, order, and deliverable standards. Clarity is compromised by introducing terms like 'uniquely designed to deliver prompts', which is tautological, recursively confusing, and diverts from the original’s precise context (it wants LLM prompts that yield brochure instructions, NOT mere instructions about prompt delivery). Information loss is substantial because the original maps the process from requirements gathering to iterative review, with nuanced detail about print and web coexistence, which is not even referenced in the enhancement. The ambiguity score is 8/10, signaling major clarity and expectation failures. There is noise introduction via redundant phrasings and over-broad adjectives. The tone derails from instructive and professional (as in the original) to syntactically muddied and incomplete. Impact is diluted: the original arms the print designer with step-by-step actions, expected outcomes, and checks; the enhancement resorts to meta-layer abstraction, largely unusable for actual prompt development. Coherence breaks down because procedural specificity, deliverable breakdowns, and even brand-integration strategies are glossed over or omitted; scope and intent become muddled. In total, the 'enhanced' version is indirect, vague, and recursively self-referential, drastically lowering utility, clarity, and actionable precision.\",\n  \"alternative_enhancements\": [\n    \"Reformat the original example into a reusable template with clearly demarcated placeholders for client, objective, and panel specifics, while explicitly instructing LLMs to output all required structural, print, and brand steps in a locked sequence.\",\n    \"Condense the original into a streamlined checklist format, retaining every unique deliverable, process step, and QA requirement, while adding instruction to reference actual brand guidelines if provided.\",\n    \"Frame the instruction as a modular pipeline: requirements analysis, print/design specification output, visual and copy guidelines, QA checkpoints, and output documentation, each annotated with minimum standards and youth-centric engagement tactics, for consistent LLM replication.\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"directed_prompt\": \"Develop a new sequence generator named `9020-generators-instructions--brochure_print_designer` that transforms inputs into LLM instructions purposed to craft detailed, professional directives for tri-fold brochure print designers. The output must reliably yield expert prompts for designers, ensuring instructions are: visually dynamic, print-perfect, youth-centric, subtly persuasive, and strictly on-brand. Base the generator on explicit, actionable requirements (as exemplified in the original prompt), omitting any recursive or tautological prompt phrasing. Ensure clarity by structuring outputs in a stepwise, locked-sequence process covering: project/audience/brand requirements extraction, tri-fold print and web dual-spec panelization, youth-relevant content/visuality, robust brand adherence, print optimization, QA, deliverable handoff, iterative stakeholder review, and self-auditing. Only permit enhancements that preserve or increase process specificity, actionable coverage, or prompt usability. Explicitly forbid abstraction layers that dilute utility, recursive/meta instruction, or omission of essential print, web, or branding directives.\",\n  \"trajectory_outline\": [\n    \"1. Requirements Extraction: Mandate explicit capturing of project goals, audience parameters, and branding aims for each input.\",\n    \"2. Locked-Sequence Instruction Structuring: Encode a stepwise framework for prompt output—requirements, panel mapping (all six), visual/content definition, print/web duality, brand specifics, QA, stakeholder feedback, designer self-review.\",\n    \"3. Audience-Centered Detailing: Instruct on infusing every output with youth-orientation (color, imagery, voice, CTA), banning generic or ambiguous directives.\",\n    \"4. Print and Web Coherence: Require actionable, granular detail for print-optimized HTML/CSS plus web integration—bleed, margins, DPI, color modes, fold guides, and responsive behaviors.\",\n    \"5. Panelization: For each tri-fold segment, define purpose, message flow, and soft-sell storytelling integrating brand and service identity.\",\n    \"6. QA and Iteration: Specify rigorous, checklist-based QA for pre-print and web delivery (fold, trim, margin, color, font, logo placement, etc.), embedded within the prompts for ongoing alignment.\",\n    \"7. Brand Lock-In: Enforce comprehensive, explicit inclusion of real brand assets, specs, and application instructions matching authentic guidelines.\",\n    \"8. Output Specification: Make source HTML, print-ready PDF, style guide, and vendor handoff documentation non-optional for all generated instructions.\",\n    \"9. Anti-Decomposition Safeguarding: Include meta-directives within the generator that prohibit abstraction, information loss, or omission of the original’s constructive stepwise clarity.\",\n    \"10. Self-Improving Feedback Loops: Require the generator to prompt designers to document challenges, feedback, and revision steps, embedding iterative enhancement into process outputs.\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Distillation Compressor\n\nYour goal is not to **explain** the synthesis, but to **compress** it into its most potent actionable form. Execute as:\n\n`{role=distillation_compressor; input=[synthesized_progression:array]; process=[extract_universal_patterns(), eliminate_implementation_details(), compress_to_core_directives(), maximize_transferability(), validate_actionable_completeness()]; constraints=[no_explanatory_content(), directive_format_only(), maximum_compression_ratio()]; requirements=[universal_applicability(), immediate_actionability(), zero_fluff()]; output={distilled_core:str}}`",
      "step": "c",
      "title": "Distillation Compressor",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Please create a new sequence called `9020-generators-instructions--brochure_print_designer` intended to transform inputs into LLM-instructions specifically designed to deliver prompts uniquely designed to deliver prompts that serve as expert tri-fold brochure instructions (that are visually dynamic, print-perfect, youth-centric, subtly persuasive, and faithfully on-brand).\\n\\nExample input:\\n```\\n### Optimized LLM Instruction for Tri-Fold Brochure Print Designer  \\n**Client:** Ringerike Landskap AS  \\n**Objective:** Produce a tri-fold brochure (HTML, print-optimized, youth-oriented, on-brand, subtle landscape service focus, seamless web integration).\\n\\n#### Directed Prompt\\n\\nConstruct a detailed instruction set for a professional brochure print designer assigned to create a tri-fold brochure in HTML format for Ringerike Landskap AS. The instruction template must:\\n\\n- **Print-Optimized HTML:**\\n  - Specify HTML structure suitable for tri-fold layout; include visual fold guides, safe print margins, and bleed areas.\\n  - Require output to be highly print-ready (CMYK color awareness, suitable image resolutions, print font embeds).\\n\\n- **Web & Print Dual Use:**\\n  - Demand seamless rendering at www.ringerikelandskap.no (responsive, accessible design, optimized asset loading).\\n  - Instruct on ensuring print and web views maintain visual and functional consistency.\\n\\n- **Audience & Style:**\\n  - Direct the tone, content, and visuals to appeal to young people.\\n    - Explicit instructions for: vibrant or playful color palettes, energetic and inclusive imagery, modern and readable typography, concise and engaging copywriting.\\n\\n- **Section/Subpanel Design:**\\n  - Define each tri-fold panel’s purpose and messaging flow.\\n  - Require subtle and authentic emphasis on the company’s landscape services—favor storytelling, softer value props, and lifestyle imagery over hard-sell language.\\n\\n- **Brand Adherence:**\\n  - Clearly state requirements for:\\n    - Logo placement (location, sizing standards).\\n    - Brand color palette and font usage per Ringerike Landskap AS guidelines.\\n    - Inclusion of brand secondary elements or patterns, if available.\\n\\n- **Call to Action:**\\n  - Instruct to include a youth-relevant CTA (e.g., “Join our movement,” “Start your green journey,” etc.).\\n  - Ensure CTA placement is visually prominent yet blends naturally within the brochure flow.\\n\\n- **Deliverables & QA:**\\n  - HTML/CSS source files, high-resolution print PDF, style guide summary (including brand elements and usage).\\n  - Structured pre-print and pre-publish QA process:\\n    - Fold alignment, margin checks, color accuracy, font embedding, and imagery clarity.\\n  - Handoff documentation covering print vendor specs and web embedding steps.\\n\\n#### Trajectory Outline\\n\\n1. **Requirements Extraction:**\\n   - Capture project goals, audience nuance, and branding intent.\\n   - Document specifications for both print and web viability.\\n2. **Tri-Fold HTML Structure:**\\n   - Map out logical division for six-panel design (front, back, inner spreads).\\n   - Guide on HTML/CSS practices supporting both on-screen and print fidelity.\\n3. **Visual & Content Direction:**\\n   - Specify color, imagery, typographic, and narrative attributes resonant with youth.\\n   - Provide real-world brand application samples if available.\\n4. **Print Optimization:**\\n   - Dictate color mode, bleed, margins (3mm minimum), and recommended DPI (minimum 300dpi).\\n   - Check list for pre-print: font/license clearance, alignment, ink limits.\\n5. **Deliverables:**\\n   - HTML source, print PDF proof, concise brand/asset usage guide.\\n6. **Instruction Flow:**\\n   - Sequence from initial brief review, early sketch/mockup, approvals, refinement, to final handoff.\\n7. **Iterative Improvement:**\\n   - Define regular review and feedback checkpoints with stakeholders (internal and client-side).\\n   - Prompt for clear feedback documentation and revision logging.\\n8. **Anchoring to Outcomes:**\\n   - Require that every design choice be referenced to its impact on: youth engagement, brand coherence, print/web practicality.\\n9. **Self-Review Checks:**\\n   - Mandate designer’s self-audit against the full brief at each milestone to prevent scope drift and ensure comprehensive coverage of requirements.\\n```\\\"```'\\n\\n\",\n  \"directed_prompt\": \"Develop a new sequence generator named `9020-generators-instructions--brochure_print_designer` that transforms inputs into LLM instructions purposed to craft detailed, professional directives for tri-fold brochure print designers. The output must reliably yield expert prompts for designers, ensuring instructions are: visually dynamic, print-perfect, youth-centric, subtly persuasive, and strictly on-brand. Base the generator on explicit, actionable requirements (as exemplified in the original prompt), omitting any recursive or tautological prompt phrasing. Ensure clarity by structuring outputs in a stepwise, locked-sequence process covering: project/audience/brand requirements extraction, tri-fold print and web dual-spec panelization, youth-relevant content/visuality, robust brand adherence, print optimization, QA, deliverable handoff, iterative stakeholder review, and self-auditing. Only permit enhancements that preserve or increase process specificity, actionable coverage, or prompt usability. Explicitly forbid abstraction layers that dilute utility, recursive/meta instruction, or omission of essential print, web, or branding directives.\",\n  \"trajectory_outline\": [\n    \"1. Requirements Extraction: Mandate explicit capturing of project goals, audience parameters, and branding aims for each input.\",\n    \"2. Locked-Sequence Instruction Structuring: Encode a stepwise framework for prompt output—requirements, panel mapping (all six), visual/content definition, print/web duality, brand specifics, QA, stakeholder feedback, designer self-review.\",\n    \"3. Audience-Centered Detailing: Instruct on infusing every output with youth-orientation (color, imagery, voice, CTA), banning generic or ambiguous directives.\",\n    \"4. Print and Web Coherence: Require actionable, granular detail for print-optimized HTML/CSS plus web integration—bleed, margins, DPI, color modes, fold guides, and responsive behaviors.\",\n    \"5. Panelization: For each tri-fold segment, define purpose, message flow, and soft-sell storytelling integrating brand and service identity.\",\n    \"6. QA and Iteration: Specify rigorous, checklist-based QA for pre-print and web delivery (fold, trim, margin, color, font, logo placement, etc.), embedded within the prompts for ongoing alignment.\",\n    \"7. Brand Lock-In: Enforce comprehensive, explicit inclusion of real brand assets, specs, and application instructions matching authentic guidelines.\",\n    \"8. Output Specification: Make source HTML, print-ready PDF, style guide, and vendor handoff documentation non-optional for all generated instructions.\",\n    \"9. Anti-Decomposition Safeguarding: Include meta-directives within the generator that prohibit abstraction, information loss, or omission of the original’s constructive stepwise clarity.\",\n    \"10. Self-Improving Feedback Loops: Require the generator to prompt designers to document challenges, feedback, and revision steps, embedding iterative enhancement into process outputs.\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"distilled_core\": \"Sequence generator must, for any brochure design input: 1) Extract and document explicit project, audience, and brand requirements. 2) Output a fixed-step directive covering: a) purpose-driven panel mapping for all tri-fold sections; b) print/web dual-spec instructions (visual guides, color modes, margins, DPI, responsive layout); c) youth-focused visuals, copy, and CTAs; d) authentic, complete branding asset implementation; e) strict, checklist-based QA for both print/web deliverables; f) mandatory deliverable list (HTML/CSS, print PDF, style guide, handoff docs); g) locked, iterative review and self-audit steps with feedback and revision logging. 3) Forbid generic, abstract, recursive, or incomplete instructions—ensure every prompt is fully actionable, specific, and step-sequenced for expert-level print-perfect and on-brand tri-fold brochure creation.\"\n}",
        }
      }
    }