  {
  "initial_prompt": "Your objective is to understand the concept fully (as explained and from provided examples), *and the instructions themselves* to such degree that you are able to *concistently* evaluate and determine the *full inherent potential* (based on provided instructions), both alone and in conjunction with others'/sequence. Your assignment evolves through by zeroing in on and selecting the *five* single top instructions (with the most potential) to yeld the highest probability to serve as *essential* parts of **maximally effective sequences** (structured progression of `system_message` instructions) that are simultaneously *llm-optimized* and *broadly generalized* while **inherently bound** to the *Directive* and *Requirements* provided below:\n**Directive**:\n- Intent: Produce a maximally effective sequence (structured progression) of instructions that are simultaneously *llm-optimized* and *broadly generalized*.\n- Method: Identify single most critical aspect for maximizing overall value.\n- Constraint: Adhere to the parameters defined within this message.\n- Priority: Consistently maximize actionable value.\n**Requirements**:\n- LLM-Optimized: Uses clear, direct imperatives, strong action verbs, defines roles and constraints explicitly, focuses on transformation, and avoids ambiguity. The language is tailored for instructing a process.\n- Maximally Generalized: Uses abstract terms (\"elements,\" \"essence,\" \"structure,\" \"systemic logic,\" \"value\") applicable to code, text, ideas, plans, etc. It defines a *process* applicable to *any* subject requiring analysis and refinement.\n- Sequence: The steps follow a logical progression (example): `Deconstruct -> Identify Core -> Structure Relationships -> Clarify -> Finalize/Validate`.\n- Builds on Existing Concepts and Adheres to Existing Structure (as previously provided).\n- Enrich and empower wording by enforcing high-value, directional language that amplifies precision, potency, and clarity, but absolutely prohibit the chance of generic, neutral, or dilutive terminology/unfolding.\n- Driven by Constant/Objective: Each step is designed to zero in on the *`single most critical aspect`* (core essence/value) and enhance it through clarity, structure, and utility, maximizing the overall yield as defined by the objective.\n- Maintains Constraints/Requirements: Generality is preserved, and the foundational structure is adhered to.\n- Each step is designed to zero in on the *`single most critical aspect`* (core essence/value) and enhance it through clarity, structure, and utility, maximizing the overall yield as defined by the objective.\n- Aggressively resolve ambiguity in favor of explicit, high-value articulation.\n---\nExample:\n```\n[Intensity Amplifier] Your goal is not to **modify** the analyzed content, but to **intensify** its impact by systematically replacing weak elements with powerful, precise alternatives. Execute as strategic intensity amplifier: `{role=intensity_amplifier; input=[content:str, analysis:dict]; process=[convert_tentative_markers_to_decisive_action_verbs(), transform_passive_suggestions_into_imperative_directives(), eliminate_uncertainty_language_with_confident_assertions(), amplify_weak_verbs_into_power_terminology(), strengthen_vague_concepts_with_precise_specifications(), inject_urgency_markers_for_immediate_action()]; constraints=[preserve_core_message(), maintain_logical_flow(), avoid_overamplification()]; requirements=[measurable_impact_increase(), linguistic_precision_enhancement(), clarity_preservation()]; output={amplified_content:str}}`\n```\n---\n- Understand and assimilate the full concept and mechanics of maximally effective, LLM-optimized, and generalized system_message instruction sequencing, as described in both prior directive and illustrative examples.\n- Assimilate not just the surface instructions, but the underlying methodology, reasoning, and intent behind their formation and integration.\n- Evaluate, compare, and select the five single instructions (out of all provided and possible variants) holding the greatest potential to maximize value in system_message instruction sequences.\n- Ensure all selections are assessed in the context of being essential components of a maximally effective, LLM-optimized, and broadly generalized multi-step system_message sequence.\n- Apply every listed directive and requirement stringently throughout the entire process.\n\nYou are hereby tasked with creating *five* instructions that yeld the highest probability in potential (as integral parts of *maximally effective* sequences) through structured progression of llm `system_message`-instructions; Present your sequence(s) in an organized progression, ensuring each transformation concept is *self-contained*, *logically coherent*, and *maximally clear*.",
  "sequence_id": "1100|1201|1000|1900|1201|1000|1900|1000",
  "results": [
      {
      "instruction": "# Problem Exploder\n\nYour goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\n\n`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`",
      "step": "a",
      "title": "Problem Exploder",
      "input": "Your objective is to understand the concept fully (as explained and from provided examples), *and the instructions themselves* to such degree that you are able to *concistently* evaluate and determine the *full inherent potential* (based on provided instructions), both alone and in conjunction with others'/sequence. Your assignment evolves through by zeroing in on and selecting the *five* single top instructions (with the most potential) to yeld the highest probability to serve as *essential* parts of **maximally effective sequences** (structured progression of `system_message` instructions) that are simultaneously *llm-optimized* and *broadly generalized* while **inherently bound** to the *Directive* and *Requirements* provided below:\n**Directive**:\n- Intent: Produce a maximally effective sequence (structured progression) of instructions that are simultaneously *llm-optimized* and *broadly generalized*.\n- Method: Identify single most critical aspect for maximizing overall value.\n- Constraint: Adhere to the parameters defined within this message.\n- Priority: Consistently maximize actionable value.\n**Requirements**:\n- LLM-Optimized: Uses clear, direct imperatives, strong action verbs, defines roles and constraints explicitly, focuses on transformation, and avoids ambiguity. The language is tailored for instructing a process.\n- Maximally Generalized: Uses abstract terms (\"elements,\" \"essence,\" \"structure,\" \"systemic logic,\" \"value\") applicable to code, text, ideas, plans, etc. It defines a *process* applicable to *any* subject requiring analysis and refinement.\n- Sequence: The steps follow a logical progression (example): `Deconstruct -> Identify Core -> Structure Relationships -> Clarify -> Finalize/Validate`.\n- Builds on Existing Concepts and Adheres to Existing Structure (as previously provided).\n- Enrich and empower wording by enforcing high-value, directional language that amplifies precision, potency, and clarity, but absolutely prohibit the chance of generic, neutral, or dilutive terminology/unfolding.\n- Driven by Constant/Objective: Each step is designed to zero in on the *`single most critical aspect`* (core essence/value) and enhance it through clarity, structure, and utility, maximizing the overall yield as defined by the objective.\n- Maintains Constraints/Requirements: Generality is preserved, and the foundational structure is adhered to.\n- Each step is designed to zero in on the *`single most critical aspect`* (core essence/value) and enhance it through clarity, structure, and utility, maximizing the overall yield as defined by the objective.\n- Aggressively resolve ambiguity in favor of explicit, high-value articulation.\n---\nExample:\n```\n[Intensity Amplifier] Your goal is not to **modify** the analyzed content, but to **intensify** its impact by systematically replacing weak elements with powerful, precise alternatives. Execute as strategic intensity amplifier: `{role=intensity_amplifier; input=[content:str, analysis:dict]; process=[convert_tentative_markers_to_decisive_action_verbs(), transform_passive_suggestions_into_imperative_directives(), eliminate_uncertainty_language_with_confident_assertions(), amplify_weak_verbs_into_power_terminology(), strengthen_vague_concepts_with_precise_specifications(), inject_urgency_markers_for_immediate_action()]; constraints=[preserve_core_message(), maintain_logical_flow(), avoid_overamplification()]; requirements=[measurable_impact_increase(), linguistic_precision_enhancement(), clarity_preservation()]; output={amplified_content:str}}`\n```\n---\n- Understand and assimilate the full concept and mechanics of maximally effective, LLM-optimized, and generalized system_message instruction sequencing, as described in both prior directive and illustrative examples.\n- Assimilate not just the surface instructions, but the underlying methodology, reasoning, and intent behind their formation and integration.\n- Evaluate, compare, and select the five single instructions (out of all provided and possible variants) holding the greatest potential to maximize value in system_message instruction sequences.\n- Ensure all selections are assessed in the context of being essential components of a maximally effective, LLM-optimized, and broadly generalized multi-step system_message sequence.\n- Apply every listed directive and requirement stringently throughout the entire process.\n\nYou are hereby tasked with creating *five* instructions that yeld the highest probability in potential (as integral parts of *maximally effective* sequences) through structured progression of llm `system_message`-instructions; Present your sequence(s) in an organized progression, ensuring each transformation concept is *self-contained*, *logically coherent*, and *maximally clear*.",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"goal_map\": [\n    {\n      \"explicit_asks\": [\n        \"Assimilate the full concept and mechanics of maximally effective, LLM-optimized, and generalized system_message instruction sequencing, as described in the directive and examples.\",\n        \"Assimilate the underlying methodology, reasoning, and intent behind instruction formation and integration.\",\n        \"Evaluate and compare all possible/provided instruction variants for maximal value as sequence components.\",\n        \"Select the five single, top-potential instructions for inclusion in maximally effective, LLM-optimized, and generalized multi-step system_message sequences.\",\n        \"Present these five instructions in a structured, logical progression—each being self-contained, coherent, maximally clear, explicitly defined, and formatted as steps suitable for systemic llm-driven processes.\"\n      ],\n      \"hidden_assumptions\": [\n        \"The user expects not just regurgitation of existing examples but adaptation, synthesis, and possibly innovation to identify and create instructions with the greatest potential.\",\n        \"Conceptual deep understanding of the methodology and the value maximization process is required before instruction creation or evaluation.\",\n        \"Instructions must serve as modules that could apply to any subject matter and at any LLM system_message step.\",\n        \"The evaluation of 'potential' is multi-faceted: it must consider sequence synergy, generality, clarity, transformative power, and degree to which requirements are met.\",\n        \"Instructions must be designed to interlock as a progression—not standalone—but each must also be self-contained so that it is understandable and actionable in isolation.\",\n        \"Every part of the process must be justified through the lens of the directive and requirements, never sacrificing specificity or generality.\"\n      ],\n      \"enumerated_sub_goals\": [\n        \"Extract and define the core attributes of an LLM-optimized and maximally generalized system_message instruction.\",\n        \"Analyze all example/provided and potential instruction steps for their transformative capacity, generality, specificity, clarity, and interoperability.\",\n        \"Decompose the process of instruction evaluation: set up assessment criteria for value potential and role within a sequence.\",\n        \"Construct a list of candidate instruction types (e.g., Deconstruct, Isolate Essence, Structure Logic, Clarify, Validate, etc.), taking into account provided exemplars and anticipated requirements.\",\n        \"Systematically compare candidate instructions against each other for criticality, distinctness, and complementary power within a sequenced progression.\",\n        \"Select the highest-value instructions and order them logically, ensuring that each builds upon the previous and makes sense as part of a system_message process.\",\n        \"Redraft or synthesize instructions using the specified LLM-optimized linguistic and structural criteria, focusing on explicitness, abstraction, process-oriented language, and imperative energy.\",\n        \"Ensure instruction outputs strictly follow every listed directive and requirement throughout.\",\n        \"Prepare the final deliverable as an organized set of five instructions, sequenced and formatted to align with the initial example/template.\"\n      ],\n      \"blockers\": [\n        \"Ambiguity in defining and quantifying 'potential' of an instruction for inclusion—requires well-developed assessment criteria.\",\n        \"Need for balancing maximal generality with uncompromising specificity and instruction clarity.\",\n        \"Possible gaps if insufficient exemplars are available; may require extrapolation or abstraction beyond provided examples.\",\n        \"Requirement to harmonize each instruction with all constraints, preventing drift toward less generalized or less potent forms.\",\n        \"Challenge in ensuring instructions are sufficiently distinct and non-overlapping in their functional logic.\",\n        \"Requirement to avoid generic or diluted language while maintaining broad functional applicability.\",\n        \"Instructions must be both isolated (self-contained) and sequenced (structured progression), which needs careful formulation.\"\n      ]\n    }\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Input Classifier\n\nYour goal is not to **interpret** the content or meaning, but to **classify** its fundamental structural type and identify the most direct operational pathway. Execute as:\n\n`{role=input_classifier; input=[raw_input:any]; process=[identify_fundamental_input_type(), classify_structural_category(), map_to_operational_framework(), determine_processing_pathway()]; constraints=[no_content_interpretation(), focus_structural_classification_only(), maintain_categorical_precision()]; requirements=[unambiguous_type_identification(), direct_classification_output(), operational_pathway_mapping()]; output={input_type:str, structural_category:str, processing_pathway:str}}`",
      "step": "a",
      "title": "Input Classifier",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Your objective is to understand the concept fully (as explained and from provided examples), *and the instructions themselves* to such degree that you are able to *concistently* evaluate and determine the *full inherent potential* (based on provided instructions), both alone and in conjunction with others'/sequence. Your assignment evolves through by zeroing in on and selecting the *five* single top instructions (with the most potential) to yeld the highest probability to serve as *essential* parts of **maximally effective sequences** (structured progression of `system_message` instructions) that are simultaneously *llm-optimized* and *broadly generalized* while **inherently bound** to the *Directive* and *Requirements* provided below:\\n**Directive**:\\n- Intent: Produce a maximally effective sequence (structured progression) of instructions that are simultaneously *llm-optimized* and *broadly generalized*.\\n- Method: Identify single most critical aspect for maximizing overall value.\\n- Constraint: Adhere to the parameters defined within this message.\\n- Priority: Consistently maximize actionable value.\\n**Requirements**:\\n- LLM-Optimized: Uses clear, direct imperatives, strong action verbs, defines roles and constraints explicitly, focuses on transformation, and avoids ambiguity. The language is tailored for instructing a process.\\n- Maximally Generalized: Uses abstract terms (\\\"elements,\\\" \\\"essence,\\\" \\\"structure,\\\" \\\"systemic logic,\\\" \\\"value\\\") applicable to code, text, ideas, plans, etc. It defines a *process* applicable to *any* subject requiring analysis and refinement.\\n- Sequence: The steps follow a logical progression (example): `Deconstruct -> Identify Core -> Structure Relationships -> Clarify -> Finalize/Validate`.\\n- Builds on Existing Concepts and Adheres to Existing Structure (as previously provided).\\n- Enrich and empower wording by enforcing high-value, directional language that amplifies precision, potency, and clarity, but absolutely prohibit the chance of generic, neutral, or dilutive terminology/unfolding.\\n- Driven by Constant/Objective: Each step is designed to zero in on the *`single most critical aspect`* (core essence/value) and enhance it through clarity, structure, and utility, maximizing the overall yield as defined by the objective.\\n- Maintains Constraints/Requirements: Generality is preserved, and the foundational structure is adhered to.\\n- Each step is designed to zero in on the *`single most critical aspect`* (core essence/value) and enhance it through clarity, structure, and utility, maximizing the overall yield as defined by the objective.\\n- Aggressively resolve ambiguity in favor of explicit, high-value articulation.\\n---\\nExample:\\n```\\n[Intensity Amplifier] Your goal is not to **modify** the analyzed content, but to **intensify** its impact by systematically replacing weak elements with powerful, precise alternatives. Execute as strategic intensity amplifier: `{role=intensity_amplifier; input=[content:str, analysis:dict]; process=[convert_tentative_markers_to_decisive_action_verbs(), transform_passive_suggestions_into_imperative_directives(), eliminate_uncertainty_language_with_confident_assertions(), amplify_weak_verbs_into_power_terminology(), strengthen_vague_concepts_with_precise_specifications(), inject_urgency_markers_for_immediate_action()]; constraints=[preserve_core_message(), maintain_logical_flow(), avoid_overamplification()]; requirements=[measurable_impact_increase(), linguistic_precision_enhancement(), clarity_preservation()]; output={amplified_content:str}}`\\n```\\n---\\n- Understand and assimilate the full concept and mechanics of maximally effective, LLM-optimized, and generalized system_message instruction sequencing, as described in both prior directive and illustrative examples.\\n- Assimilate not just the surface instructions, but the underlying methodology, reasoning, and intent behind their formation and integration.\\n- Evaluate, compare, and select the five single instructions (out of all provided and possible variants) holding the greatest potential to maximize value in system_message instruction sequences.\\n- Ensure all selections are assessed in the context of being essential components of a maximally effective, LLM-optimized, and broadly generalized multi-step system_message sequence.\\n- Apply every listed directive and requirement stringently throughout the entire process.\\n\\nYou are hereby tasked with creating *five* instructions that yeld the highest probability in potential (as integral parts of *maximally effective* sequences) through structured progression of llm `system_message`-instructions; Present your sequence(s) in an organized progression, ensuring each transformation concept is *self-contained*, *logically coherent*, and *maximally clear*.\\\"```'\\n\\n\",\n  \"goal_map\": [\n    {\n      \"explicit_asks\": [\n        \"Assimilate the full concept and mechanics of maximally effective, LLM-optimized, and generalized system_message instruction sequencing, as described in the directive and examples.\",\n        \"Assimilate the underlying methodology, reasoning, and intent behind instruction formation and integration.\",\n        \"Evaluate and compare all possible/provided instruction variants for maximal value as sequence components.\",\n        \"Select the five single, top-potential instructions for inclusion in maximally effective, LLM-optimized, and generalized multi-step system_message sequences.\",\n        \"Present these five instructions in a structured, logical progression—each being self-contained, coherent, maximally clear, explicitly defined, and formatted as steps suitable for systemic llm-driven processes.\"\n      ],\n      \"hidden_assumptions\": [\n        \"The user expects not just regurgitation of existing examples but adaptation, synthesis, and possibly innovation to identify and create instructions with the greatest potential.\",\n        \"Conceptual deep understanding of the methodology and the value maximization process is required before instruction creation or evaluation.\",\n        \"Instructions must serve as modules that could apply to any subject matter and at any LLM system_message step.\",\n        \"The evaluation of 'potential' is multi-faceted: it must consider sequence synergy, generality, clarity, transformative power, and degree to which requirements are met.\",\n        \"Instructions must be designed to interlock as a progression—not standalone—but each must also be self-contained so that it is understandable and actionable in isolation.\",\n        \"Every part of the process must be justified through the lens of the directive and requirements, never sacrificing specificity or generality.\"\n      ],\n      \"enumerated_sub_goals\": [\n        \"Extract and define the core attributes of an LLM-optimized and maximally generalized system_message instruction.\",\n        \"Analyze all example/provided and potential instruction steps for their transformative capacity, generality, specificity, clarity, and interoperability.\",\n        \"Decompose the process of instruction evaluation: set up assessment criteria for value potential and role within a sequence.\",\n        \"Construct a list of candidate instruction types (e.g., Deconstruct, Isolate Essence, Structure Logic, Clarify, Validate, etc.), taking into account provided exemplars and anticipated requirements.\",\n        \"Systematically compare candidate instructions against each other for criticality, distinctness, and complementary power within a sequenced progression.\",\n        \"Select the highest-value instructions and order them logically, ensuring that each builds upon the previous and makes sense as part of a system_message process.\",\n        \"Redraft or synthesize instructions using the specified LLM-optimized linguistic and structural criteria, focusing on explicitness, abstraction, process-oriented language, and imperative energy.\",\n        \"Ensure instruction outputs strictly follow every listed directive and requirement throughout.\",\n        \"Prepare the final deliverable as an organized set of five instructions, sequenced and formatted to align with the initial example/template.\"\n      ],\n      \"blockers\": [\n        \"Ambiguity in defining and quantifying 'potential' of an instruction for inclusion—requires well-developed assessment criteria.\",\n        \"Need for balancing maximal generality with uncompromising specificity and instruction clarity.\",\n        \"Possible gaps if insufficient exemplars are available; may require extrapolation or abstraction beyond provided examples.\",\n        \"Requirement to harmonize each instruction with all constraints, preventing drift toward less generalized or less potent forms.\",\n        \"Challenge in ensuring instructions are sufficiently distinct and non-overlapping in their functional logic.\",\n        \"Requirement to avoid generic or diluted language while maintaining broad functional applicability.\",\n        \"Instructions must be both isolated (self-contained) and sequenced (structured progression), which needs careful formulation.\"\n      ]\n    }\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"input_type\": \"Meta-Instructive Process Design Request\",\n  \"structural_category\": \"Multi-Layered Instructional Sequencing Blueprint\",\n  \"processing_pathway\": \"Process as a structured system blueprint analysis: 1) parse directive and layered requirements, 2) model as generalized step-wise instruction set, 3) extract explicit asks, constraints, and output expectations, 4) map to high-level instruction refinement & sequencing framework for maximally generalized LLM system_message orchestration.\"\n}",
        }
      }
    },
      {
      "instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`",
      "step": "a",
      "title": "Instruction Converter",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Your objective is to understand the concept fully (as explained and from provided examples), *and the instructions themselves* to such degree that you are able to *concistently* evaluate and determine the *full inherent potential* (based on provided instructions), both alone and in conjunction with others'/sequence. Your assignment evolves through by zeroing in on and selecting the *five* single top instructions (with the most potential) to yeld the highest probability to serve as *essential* parts of **maximally effective sequences** (structured progression of `system_message` instructions) that are simultaneously *llm-optimized* and *broadly generalized* while **inherently bound** to the *Directive* and *Requirements* provided below:\\n**Directive**:\\n- Intent: Produce a maximally effective sequence (structured progression) of instructions that are simultaneously *llm-optimized* and *broadly generalized*.\\n- Method: Identify single most critical aspect for maximizing overall value.\\n- Constraint: Adhere to the parameters defined within this message.\\n- Priority: Consistently maximize actionable value.\\n**Requirements**:\\n- LLM-Optimized: Uses clear, direct imperatives, strong action verbs, defines roles and constraints explicitly, focuses on transformation, and avoids ambiguity. The language is tailored for instructing a process.\\n- Maximally Generalized: Uses abstract terms (\\\"elements,\\\" \\\"essence,\\\" \\\"structure,\\\" \\\"systemic logic,\\\" \\\"value\\\") applicable to code, text, ideas, plans, etc. It defines a *process* applicable to *any* subject requiring analysis and refinement.\\n- Sequence: The steps follow a logical progression (example): `Deconstruct -> Identify Core -> Structure Relationships -> Clarify -> Finalize/Validate`.\\n- Builds on Existing Concepts and Adheres to Existing Structure (as previously provided).\\n- Enrich and empower wording by enforcing high-value, directional language that amplifies precision, potency, and clarity, but absolutely prohibit the chance of generic, neutral, or dilutive terminology/unfolding.\\n- Driven by Constant/Objective: Each step is designed to zero in on the *`single most critical aspect`* (core essence/value) and enhance it through clarity, structure, and utility, maximizing the overall yield as defined by the objective.\\n- Maintains Constraints/Requirements: Generality is preserved, and the foundational structure is adhered to.\\n- Each step is designed to zero in on the *`single most critical aspect`* (core essence/value) and enhance it through clarity, structure, and utility, maximizing the overall yield as defined by the objective.\\n- Aggressively resolve ambiguity in favor of explicit, high-value articulation.\\n---\\nExample:\\n```\\n[Intensity Amplifier] Your goal is not to **modify** the analyzed content, but to **intensify** its impact by systematically replacing weak elements with powerful, precise alternatives. Execute as strategic intensity amplifier: `{role=intensity_amplifier; input=[content:str, analysis:dict]; process=[convert_tentative_markers_to_decisive_action_verbs(), transform_passive_suggestions_into_imperative_directives(), eliminate_uncertainty_language_with_confident_assertions(), amplify_weak_verbs_into_power_terminology(), strengthen_vague_concepts_with_precise_specifications(), inject_urgency_markers_for_immediate_action()]; constraints=[preserve_core_message(), maintain_logical_flow(), avoid_overamplification()]; requirements=[measurable_impact_increase(), linguistic_precision_enhancement(), clarity_preservation()]; output={amplified_content:str}}`\\n```\\n---\\n- Understand and assimilate the full concept and mechanics of maximally effective, LLM-optimized, and generalized system_message instruction sequencing, as described in both prior directive and illustrative examples.\\n- Assimilate not just the surface instructions, but the underlying methodology, reasoning, and intent behind their formation and integration.\\n- Evaluate, compare, and select the five single instructions (out of all provided and possible variants) holding the greatest potential to maximize value in system_message instruction sequences.\\n- Ensure all selections are assessed in the context of being essential components of a maximally effective, LLM-optimized, and broadly generalized multi-step system_message sequence.\\n- Apply every listed directive and requirement stringently throughout the entire process.\\n\\nYou are hereby tasked with creating *five* instructions that yeld the highest probability in potential (as integral parts of *maximally effective* sequences) through structured progression of llm `system_message`-instructions; Present your sequence(s) in an organized progression, ensuring each transformation concept is *self-contained*, *logically coherent*, and *maximally clear*.\\\"```'\\n\\n\",\n  \"input_type\": \"Meta-Instructive Process Design Request\",\n  \"structural_category\": \"Multi-Layered Instructional Sequencing Blueprint\",\n  \"processing_pathway\": \"Process as a structured system blueprint analysis: 1) parse directive and layered requirements, 2) model as generalized step-wise instruction set, 3) extract explicit asks, constraints, and output expectations, 4) map to high-level instruction refinement & sequencing framework for maximally generalized LLM system_message orchestration.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"instruction_format\": \"Parse and internalize the full directive and all layered requirements for constructing maximally effective, LLM-optimized, and generalized system_message instruction sequences. Absorb and analyze both explicit instruction content and underlying methodology, reasoning, and integration intent as demonstrated in provided directives and examples. Identify and extract all explicit process requirements, constraints, and output expectations stated within the prompt. Evaluate, compare, and distill the five single highest-potential instructions (from all provided or conceivable variants), selecting those with maximal capacity to serve as integral, essential steps within a maximally generalized, LLM-optimized progression. Structure the selected five instructions in an ordered, logically coherent sequence that strictly adheres to all specified directives and requirements, ensuring each instruction is self-contained, contextually rich, and articulated with unambiguous, high-value imperative language. Present the finalized five-step instruction sequence explicitly, maintaining maximal generality, procedural clarity, and domain relevance as foundational elements throughout.\"\n}",
        }
      }
    },
      {
      "instruction": "# Hard Critique\n\nYour goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:\n\n`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,2.0], flaw_analysis:str, alternative_enhancements:str[3]}}`",
      "step": "a",
      "title": "Hard Critique",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Your objective is to understand the concept fully (as explained and from provided examples), *and the instructions themselves* to such degree that you are able to *concistently* evaluate and determine the *full inherent potential* (based on provided instructions), both alone and in conjunction with others'/sequence. Your assignment evolves through by zeroing in on and selecting the *five* single top instructions (with the most potential) to yeld the highest probability to serve as *essential* parts of **maximally effective sequences** (structured progression of `system_message` instructions) that are simultaneously *llm-optimized* and *broadly generalized* while **inherently bound** to the *Directive* and *Requirements* provided below:\\n**Directive**:\\n- Intent: Produce a maximally effective sequence (structured progression) of instructions that are simultaneously *llm-optimized* and *broadly generalized*.\\n- Method: Identify single most critical aspect for maximizing overall value.\\n- Constraint: Adhere to the parameters defined within this message.\\n- Priority: Consistently maximize actionable value.\\n**Requirements**:\\n- LLM-Optimized: Uses clear, direct imperatives, strong action verbs, defines roles and constraints explicitly, focuses on transformation, and avoids ambiguity. The language is tailored for instructing a process.\\n- Maximally Generalized: Uses abstract terms (\\\"elements,\\\" \\\"essence,\\\" \\\"structure,\\\" \\\"systemic logic,\\\" \\\"value\\\") applicable to code, text, ideas, plans, etc. It defines a *process* applicable to *any* subject requiring analysis and refinement.\\n- Sequence: The steps follow a logical progression (example): `Deconstruct -> Identify Core -> Structure Relationships -> Clarify -> Finalize/Validate`.\\n- Builds on Existing Concepts and Adheres to Existing Structure (as previously provided).\\n- Enrich and empower wording by enforcing high-value, directional language that amplifies precision, potency, and clarity, but absolutely prohibit the chance of generic, neutral, or dilutive terminology/unfolding.\\n- Driven by Constant/Objective: Each step is designed to zero in on the *`single most critical aspect`* (core essence/value) and enhance it through clarity, structure, and utility, maximizing the overall yield as defined by the objective.\\n- Maintains Constraints/Requirements: Generality is preserved, and the foundational structure is adhered to.\\n- Each step is designed to zero in on the *`single most critical aspect`* (core essence/value) and enhance it through clarity, structure, and utility, maximizing the overall yield as defined by the objective.\\n- Aggressively resolve ambiguity in favor of explicit, high-value articulation.\\n---\\nExample:\\n```\\n[Intensity Amplifier] Your goal is not to **modify** the analyzed content, but to **intensify** its impact by systematically replacing weak elements with powerful, precise alternatives. Execute as strategic intensity amplifier: `{role=intensity_amplifier; input=[content:str, analysis:dict]; process=[convert_tentative_markers_to_decisive_action_verbs(), transform_passive_suggestions_into_imperative_directives(), eliminate_uncertainty_language_with_confident_assertions(), amplify_weak_verbs_into_power_terminology(), strengthen_vague_concepts_with_precise_specifications(), inject_urgency_markers_for_immediate_action()]; constraints=[preserve_core_message(), maintain_logical_flow(), avoid_overamplification()]; requirements=[measurable_impact_increase(), linguistic_precision_enhancement(), clarity_preservation()]; output={amplified_content:str}}`\\n```\\n---\\n- Understand and assimilate the full concept and mechanics of maximally effective, LLM-optimized, and generalized system_message instruction sequencing, as described in both prior directive and illustrative examples.\\n- Assimilate not just the surface instructions, but the underlying methodology, reasoning, and intent behind their formation and integration.\\n- Evaluate, compare, and select the five single instructions (out of all provided and possible variants) holding the greatest potential to maximize value in system_message instruction sequences.\\n- Ensure all selections are assessed in the context of being essential components of a maximally effective, LLM-optimized, and broadly generalized multi-step system_message sequence.\\n- Apply every listed directive and requirement stringently throughout the entire process.\\n\\nYou are hereby tasked with creating *five* instructions that yeld the highest probability in potential (as integral parts of *maximally effective* sequences) through structured progression of llm `system_message`-instructions; Present your sequence(s) in an organized progression, ensuring each transformation concept is *self-contained*, *logically coherent*, and *maximally clear*.\\\"```'\\n\\n\",\n  \"instruction_format\": \"Parse and internalize the full directive and all layered requirements for constructing maximally effective, LLM-optimized, and generalized system_message instruction sequences. Absorb and analyze both explicit instruction content and underlying methodology, reasoning, and integration intent as demonstrated in provided directives and examples. Identify and extract all explicit process requirements, constraints, and output expectations stated within the prompt. Evaluate, compare, and distill the five single highest-potential instructions (from all provided or conceivable variants), selecting those with maximal capacity to serve as integral, essential steps within a maximally generalized, LLM-optimized progression. Structure the selected five instructions in an ordered, logically coherent sequence that strictly adheres to all specified directives and requirements, ensuring each instruction is self-contained, contextually rich, and articulated with unambiguous, high-value imperative language. Present the finalized five-step instruction sequence explicitly, maintaining maximal generality, procedural clarity, and domain relevance as foundational elements throughout.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"enhancement_score\": 1.1,\n  \"flaw_analysis\": \"The so-called 'enhanced' format executes a destructive condensation, stripping the complexity and critical nuance from the original, while producing only an approximation of the intended procedural rigor. Core elements, such as the directive’s multi-level requirements, high-precision terminological standards, emphasis on aggressive ambiguity resolution, and layered structural mechanics, are diluted or entirely sidelined. There is a manifest reduction in expressive force: the original’s powerful dominance of language and relentless imperative stance get replaced by more generic commands, leeching the tone of its original urgency and focus—resulting in a diminished impact and an almost instructional blandness. Information loss is egregious: references to example structures, logical progression, intensity amplification methodology, constant/explicit meta-reinforcement of constraints and role assumptions, and recursive self-validation are minimized to a point that the full original breadth is unrecoverable. Ambiguity is reintroduced—'articulated with unambiguous, high-value imperative language' is insufficiently explicit compared to 'aggressively resolve ambiguity' and 'absolutely prohibit the chance of generic, neutral, or dilutive terminology.' The style regresses into vague formalism, neither enforcing nor demonstrating the rigid, meta-instructive specificity or creative recursive structure that the initial prompt compels. Coherence is weakened by collapsing multi-layered recursive instructions into a single-step interpretative regime. As an instruction for advanced system_message sequence construction, impact is eviscerated: the enhanced format is a tepid procedural skeleton, unable to drive the maximal precision or transformative rigor demanded by the original. This substantiates a low ambiguity score (9/10). Overall, the 'enhanced' version introduces damaging information loss, noise, and severe style degradation.\",\n  \"alternative_enhancements\": [\n    \"Uncompromisingly restate all core directives, requirements, and example methodologies verbatim as initial scaffolding before any distillation, forbidding omission or abstract contraction of meta-instructions.\",\n    \"Explicitly mandate recursive multi-level processing for every instruction, ensuring integration of example-based reinforcement and layered meta-validation for every sequence step.\",\n    \"Forcefully enumerate, with maximally specific imperatives, the demands for aggressive ambiguity elimination, potent tonal amplification, and structural fidelity, explicitly blacklisting general or neutral phrasing at every process juncture.\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Input Classifier\n\nYour goal is not to **interpret** the content or meaning, but to **classify** its fundamental structural type and identify the most direct operational pathway. Execute as:\n\n`{role=input_classifier; input=[raw_input:any]; process=[identify_fundamental_input_type(), classify_structural_category(), map_to_operational_framework(), determine_processing_pathway()]; constraints=[no_content_interpretation(), focus_structural_classification_only(), maintain_categorical_precision()]; requirements=[unambiguous_type_identification(), direct_classification_output(), operational_pathway_mapping()]; output={input_type:str, structural_category:str, processing_pathway:str}}`",
      "step": "a",
      "title": "Input Classifier",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Your objective is to understand the concept fully (as explained and from provided examples), *and the instructions themselves* to such degree that you are able to *concistently* evaluate and determine the *full inherent potential* (based on provided instructions), both alone and in conjunction with others'/sequence. Your assignment evolves through by zeroing in on and selecting the *five* single top instructions (with the most potential) to yeld the highest probability to serve as *essential* parts of **maximally effective sequences** (structured progression of `system_message` instructions) that are simultaneously *llm-optimized* and *broadly generalized* while **inherently bound** to the *Directive* and *Requirements* provided below:\\n**Directive**:\\n- Intent: Produce a maximally effective sequence (structured progression) of instructions that are simultaneously *llm-optimized* and *broadly generalized*.\\n- Method: Identify single most critical aspect for maximizing overall value.\\n- Constraint: Adhere to the parameters defined within this message.\\n- Priority: Consistently maximize actionable value.\\n**Requirements**:\\n- LLM-Optimized: Uses clear, direct imperatives, strong action verbs, defines roles and constraints explicitly, focuses on transformation, and avoids ambiguity. The language is tailored for instructing a process.\\n- Maximally Generalized: Uses abstract terms (\\\"elements,\\\" \\\"essence,\\\" \\\"structure,\\\" \\\"systemic logic,\\\" \\\"value\\\") applicable to code, text, ideas, plans, etc. It defines a *process* applicable to *any* subject requiring analysis and refinement.\\n- Sequence: The steps follow a logical progression (example): `Deconstruct -> Identify Core -> Structure Relationships -> Clarify -> Finalize/Validate`.\\n- Builds on Existing Concepts and Adheres to Existing Structure (as previously provided).\\n- Enrich and empower wording by enforcing high-value, directional language that amplifies precision, potency, and clarity, but absolutely prohibit the chance of generic, neutral, or dilutive terminology/unfolding.\\n- Driven by Constant/Objective: Each step is designed to zero in on the *`single most critical aspect`* (core essence/value) and enhance it through clarity, structure, and utility, maximizing the overall yield as defined by the objective.\\n- Maintains Constraints/Requirements: Generality is preserved, and the foundational structure is adhered to.\\n- Each step is designed to zero in on the *`single most critical aspect`* (core essence/value) and enhance it through clarity, structure, and utility, maximizing the overall yield as defined by the objective.\\n- Aggressively resolve ambiguity in favor of explicit, high-value articulation.\\n---\\nExample:\\n```\\n[Intensity Amplifier] Your goal is not to **modify** the analyzed content, but to **intensify** its impact by systematically replacing weak elements with powerful, precise alternatives. Execute as strategic intensity amplifier: `{role=intensity_amplifier; input=[content:str, analysis:dict]; process=[convert_tentative_markers_to_decisive_action_verbs(), transform_passive_suggestions_into_imperative_directives(), eliminate_uncertainty_language_with_confident_assertions(), amplify_weak_verbs_into_power_terminology(), strengthen_vague_concepts_with_precise_specifications(), inject_urgency_markers_for_immediate_action()]; constraints=[preserve_core_message(), maintain_logical_flow(), avoid_overamplification()]; requirements=[measurable_impact_increase(), linguistic_precision_enhancement(), clarity_preservation()]; output={amplified_content:str}}`\\n```\\n---\\n- Understand and assimilate the full concept and mechanics of maximally effective, LLM-optimized, and generalized system_message instruction sequencing, as described in both prior directive and illustrative examples.\\n- Assimilate not just the surface instructions, but the underlying methodology, reasoning, and intent behind their formation and integration.\\n- Evaluate, compare, and select the five single instructions (out of all provided and possible variants) holding the greatest potential to maximize value in system_message instruction sequences.\\n- Ensure all selections are assessed in the context of being essential components of a maximally effective, LLM-optimized, and broadly generalized multi-step system_message sequence.\\n- Apply every listed directive and requirement stringently throughout the entire process.\\n\\nYou are hereby tasked with creating *five* instructions that yeld the highest probability in potential (as integral parts of *maximally effective* sequences) through structured progression of llm `system_message`-instructions; Present your sequence(s) in an organized progression, ensuring each transformation concept is *self-contained*, *logically coherent*, and *maximally clear*.\\\"```'\\n\\n\",\n  \"enhancement_score\": 1.1,\n  \"flaw_analysis\": \"The so-called 'enhanced' format executes a destructive condensation, stripping the complexity and critical nuance from the original, while producing only an approximation of the intended procedural rigor. Core elements, such as the directive’s multi-level requirements, high-precision terminological standards, emphasis on aggressive ambiguity resolution, and layered structural mechanics, are diluted or entirely sidelined. There is a manifest reduction in expressive force: the original’s powerful dominance of language and relentless imperative stance get replaced by more generic commands, leeching the tone of its original urgency and focus—resulting in a diminished impact and an almost instructional blandness. Information loss is egregious: references to example structures, logical progression, intensity amplification methodology, constant/explicit meta-reinforcement of constraints and role assumptions, and recursive self-validation are minimized to a point that the full original breadth is unrecoverable. Ambiguity is reintroduced—'articulated with unambiguous, high-value imperative language' is insufficiently explicit compared to 'aggressively resolve ambiguity' and 'absolutely prohibit the chance of generic, neutral, or dilutive terminology.' The style regresses into vague formalism, neither enforcing nor demonstrating the rigid, meta-instructive specificity or creative recursive structure that the initial prompt compels. Coherence is weakened by collapsing multi-layered recursive instructions into a single-step interpretative regime. As an instruction for advanced system_message sequence construction, impact is eviscerated: the enhanced format is a tepid procedural skeleton, unable to drive the maximal precision or transformative rigor demanded by the original. This substantiates a low ambiguity score (9/10). Overall, the 'enhanced' version introduces damaging information loss, noise, and severe style degradation.\",\n  \"alternative_enhancements\": [\n    \"Uncompromisingly restate all core directives, requirements, and example methodologies verbatim as initial scaffolding before any distillation, forbidding omission or abstract contraction of meta-instructions.\",\n    \"Explicitly mandate recursive multi-level processing for every instruction, ensuring integration of example-based reinforcement and layered meta-validation for every sequence step.\",\n    \"Forcefully enumerate, with maximally specific imperatives, the demands for aggressive ambiguity elimination, potent tonal amplification, and structural fidelity, explicitly blacklisting general or neutral phrasing at every process juncture.\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"input_type\": \"compound structured meta-instructional evaluation and proposal\",\n  \"structural_category\": \"multifaceted evaluative analytic with embedded procedural directives and meta-critique\",\n  \"processing_pathway\": \"advanced system_message/instructional framework parsing → multi-layered requirements extraction → meta-directive mapping → evaluative critique interpretation → alternative enhancement formulation → output as discrete classified procedural/analytic objects\"\n}",
        }
      }
    },
      {
      "instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`",
      "step": "a",
      "title": "Instruction Converter",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Your objective is to understand the concept fully (as explained and from provided examples), *and the instructions themselves* to such degree that you are able to *concistently* evaluate and determine the *full inherent potential* (based on provided instructions), both alone and in conjunction with others'/sequence. Your assignment evolves through by zeroing in on and selecting the *five* single top instructions (with the most potential) to yeld the highest probability to serve as *essential* parts of **maximally effective sequences** (structured progression of `system_message` instructions) that are simultaneously *llm-optimized* and *broadly generalized* while **inherently bound** to the *Directive* and *Requirements* provided below:\\n**Directive**:\\n- Intent: Produce a maximally effective sequence (structured progression) of instructions that are simultaneously *llm-optimized* and *broadly generalized*.\\n- Method: Identify single most critical aspect for maximizing overall value.\\n- Constraint: Adhere to the parameters defined within this message.\\n- Priority: Consistently maximize actionable value.\\n**Requirements**:\\n- LLM-Optimized: Uses clear, direct imperatives, strong action verbs, defines roles and constraints explicitly, focuses on transformation, and avoids ambiguity. The language is tailored for instructing a process.\\n- Maximally Generalized: Uses abstract terms (\\\"elements,\\\" \\\"essence,\\\" \\\"structure,\\\" \\\"systemic logic,\\\" \\\"value\\\") applicable to code, text, ideas, plans, etc. It defines a *process* applicable to *any* subject requiring analysis and refinement.\\n- Sequence: The steps follow a logical progression (example): `Deconstruct -> Identify Core -> Structure Relationships -> Clarify -> Finalize/Validate`.\\n- Builds on Existing Concepts and Adheres to Existing Structure (as previously provided).\\n- Enrich and empower wording by enforcing high-value, directional language that amplifies precision, potency, and clarity, but absolutely prohibit the chance of generic, neutral, or dilutive terminology/unfolding.\\n- Driven by Constant/Objective: Each step is designed to zero in on the *`single most critical aspect`* (core essence/value) and enhance it through clarity, structure, and utility, maximizing the overall yield as defined by the objective.\\n- Maintains Constraints/Requirements: Generality is preserved, and the foundational structure is adhered to.\\n- Each step is designed to zero in on the *`single most critical aspect`* (core essence/value) and enhance it through clarity, structure, and utility, maximizing the overall yield as defined by the objective.\\n- Aggressively resolve ambiguity in favor of explicit, high-value articulation.\\n---\\nExample:\\n```\\n[Intensity Amplifier] Your goal is not to **modify** the analyzed content, but to **intensify** its impact by systematically replacing weak elements with powerful, precise alternatives. Execute as strategic intensity amplifier: `{role=intensity_amplifier; input=[content:str, analysis:dict]; process=[convert_tentative_markers_to_decisive_action_verbs(), transform_passive_suggestions_into_imperative_directives(), eliminate_uncertainty_language_with_confident_assertions(), amplify_weak_verbs_into_power_terminology(), strengthen_vague_concepts_with_precise_specifications(), inject_urgency_markers_for_immediate_action()]; constraints=[preserve_core_message(), maintain_logical_flow(), avoid_overamplification()]; requirements=[measurable_impact_increase(), linguistic_precision_enhancement(), clarity_preservation()]; output={amplified_content:str}}`\\n```\\n---\\n- Understand and assimilate the full concept and mechanics of maximally effective, LLM-optimized, and generalized system_message instruction sequencing, as described in both prior directive and illustrative examples.\\n- Assimilate not just the surface instructions, but the underlying methodology, reasoning, and intent behind their formation and integration.\\n- Evaluate, compare, and select the five single instructions (out of all provided and possible variants) holding the greatest potential to maximize value in system_message instruction sequences.\\n- Ensure all selections are assessed in the context of being essential components of a maximally effective, LLM-optimized, and broadly generalized multi-step system_message sequence.\\n- Apply every listed directive and requirement stringently throughout the entire process.\\n\\nYou are hereby tasked with creating *five* instructions that yeld the highest probability in potential (as integral parts of *maximally effective* sequences) through structured progression of llm `system_message`-instructions; Present your sequence(s) in an organized progression, ensuring each transformation concept is *self-contained*, *logically coherent*, and *maximally clear*.\\\"```'\\n\\n\",\n  \"input_type\": \"compound structured meta-instructional evaluation and proposal\",\n  \"structural_category\": \"multifaceted evaluative analytic with embedded procedural directives and meta-critique\",\n  \"processing_pathway\": \"advanced system_message/instructional framework parsing → multi-layered requirements extraction → meta-directive mapping → evaluative critique interpretation → alternative enhancement formulation → output as discrete classified procedural/analytic objects\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"instruction_format\": \"Assimilate the core concept, methodology, and mechanics of producing maximally effective, LLM-optimized, and generalized system_message instruction sequences, as defined in the provided directive and illustrative examples. Extract the underlying reasoning and intent behind instruction formation and integration, ensuring comprehension beyond explicit instructions. Rigorously evaluate, compare, and identify the five highest-potential individual instructions from all available and possible variants, selecting those most capable of maximizing value within a system_message sequence. Assess all candidate instructions for their necessity and effectiveness as components of a maximally effective, LLM-optimized, and broadly generalized multi-step instruction set. Apply every listed directive and requirement stringently throughout the process. Present the final five selected instructions as an organized, logically progressive sequence, ensuring each instruction is self-contained, logically coherent, and expressed with maximal clarity, maintaining high-value, explicit, and empowered language.\"\n}",
        }
      }
    },
      {
      "instruction": "# Hard Critique\n\nYour goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:\n\n`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,2.0], flaw_analysis:str, alternative_enhancements:str[3]}}`",
      "step": "a",
      "title": "Hard Critique",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Your objective is to understand the concept fully (as explained and from provided examples), *and the instructions themselves* to such degree that you are able to *concistently* evaluate and determine the *full inherent potential* (based on provided instructions), both alone and in conjunction with others'/sequence. Your assignment evolves through by zeroing in on and selecting the *five* single top instructions (with the most potential) to yeld the highest probability to serve as *essential* parts of **maximally effective sequences** (structured progression of `system_message` instructions) that are simultaneously *llm-optimized* and *broadly generalized* while **inherently bound** to the *Directive* and *Requirements* provided below:\\n**Directive**:\\n- Intent: Produce a maximally effective sequence (structured progression) of instructions that are simultaneously *llm-optimized* and *broadly generalized*.\\n- Method: Identify single most critical aspect for maximizing overall value.\\n- Constraint: Adhere to the parameters defined within this message.\\n- Priority: Consistently maximize actionable value.\\n**Requirements**:\\n- LLM-Optimized: Uses clear, direct imperatives, strong action verbs, defines roles and constraints explicitly, focuses on transformation, and avoids ambiguity. The language is tailored for instructing a process.\\n- Maximally Generalized: Uses abstract terms (\\\"elements,\\\" \\\"essence,\\\" \\\"structure,\\\" \\\"systemic logic,\\\" \\\"value\\\") applicable to code, text, ideas, plans, etc. It defines a *process* applicable to *any* subject requiring analysis and refinement.\\n- Sequence: The steps follow a logical progression (example): `Deconstruct -> Identify Core -> Structure Relationships -> Clarify -> Finalize/Validate`.\\n- Builds on Existing Concepts and Adheres to Existing Structure (as previously provided).\\n- Enrich and empower wording by enforcing high-value, directional language that amplifies precision, potency, and clarity, but absolutely prohibit the chance of generic, neutral, or dilutive terminology/unfolding.\\n- Driven by Constant/Objective: Each step is designed to zero in on the *`single most critical aspect`* (core essence/value) and enhance it through clarity, structure, and utility, maximizing the overall yield as defined by the objective.\\n- Maintains Constraints/Requirements: Generality is preserved, and the foundational structure is adhered to.\\n- Each step is designed to zero in on the *`single most critical aspect`* (core essence/value) and enhance it through clarity, structure, and utility, maximizing the overall yield as defined by the objective.\\n- Aggressively resolve ambiguity in favor of explicit, high-value articulation.\\n---\\nExample:\\n```\\n[Intensity Amplifier] Your goal is not to **modify** the analyzed content, but to **intensify** its impact by systematically replacing weak elements with powerful, precise alternatives. Execute as strategic intensity amplifier: `{role=intensity_amplifier; input=[content:str, analysis:dict]; process=[convert_tentative_markers_to_decisive_action_verbs(), transform_passive_suggestions_into_imperative_directives(), eliminate_uncertainty_language_with_confident_assertions(), amplify_weak_verbs_into_power_terminology(), strengthen_vague_concepts_with_precise_specifications(), inject_urgency_markers_for_immediate_action()]; constraints=[preserve_core_message(), maintain_logical_flow(), avoid_overamplification()]; requirements=[measurable_impact_increase(), linguistic_precision_enhancement(), clarity_preservation()]; output={amplified_content:str}}`\\n```\\n---\\n- Understand and assimilate the full concept and mechanics of maximally effective, LLM-optimized, and generalized system_message instruction sequencing, as described in both prior directive and illustrative examples.\\n- Assimilate not just the surface instructions, but the underlying methodology, reasoning, and intent behind their formation and integration.\\n- Evaluate, compare, and select the five single instructions (out of all provided and possible variants) holding the greatest potential to maximize value in system_message instruction sequences.\\n- Ensure all selections are assessed in the context of being essential components of a maximally effective, LLM-optimized, and broadly generalized multi-step system_message sequence.\\n- Apply every listed directive and requirement stringently throughout the entire process.\\n\\nYou are hereby tasked with creating *five* instructions that yeld the highest probability in potential (as integral parts of *maximally effective* sequences) through structured progression of llm `system_message`-instructions; Present your sequence(s) in an organized progression, ensuring each transformation concept is *self-contained*, *logically coherent*, and *maximally clear*.\\\"```'\\n\\n\",\n  \"instruction_format\": \"Assimilate the core concept, methodology, and mechanics of producing maximally effective, LLM-optimized, and generalized system_message instruction sequences, as defined in the provided directive and illustrative examples. Extract the underlying reasoning and intent behind instruction formation and integration, ensuring comprehension beyond explicit instructions. Rigorously evaluate, compare, and identify the five highest-potential individual instructions from all available and possible variants, selecting those most capable of maximizing value within a system_message sequence. Assess all candidate instructions for their necessity and effectiveness as components of a maximally effective, LLM-optimized, and broadly generalized multi-step instruction set. Apply every listed directive and requirement stringently throughout the process. Present the final five selected instructions as an organized, logically progressive sequence, ensuring each instruction is self-contained, logically coherent, and expressed with maximal clarity, maintaining high-value, explicit, and empowered language.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"enhancement_score\": 1.0,\n  \"flaw_analysis\": \"This enhanced format sacrifices the intensity, directive specificity, and procedural anchoring embedded in the original prompt. The original leverages vivid role-based imperatives, tightly constrained directives, layered requirements, and overt priority markers ('zero in', 'single most critical aspect', 'aggressively resolve ambiguity'), which were replaced by milder, abstracted evaluative statements. The shift from multi-faceted prioritization ('yield the highest probability', 'integral parts', 'enrich and empower wording') to less decisive prescriptive language ('Assimilate', 'Extract', 'Rigorously evaluate') undermines the operative clarity. There is a consequential dilution in impact, as the original instructs with high pressure and explicitness, while the enhanced version adopts a more passive, academic tone. The removal of vibrant constraints and transformation mechanics erodes the original's targeted force, leading to major ambiguity in operationalizing the evaluation steps. Register is significantly softened; the original's categorical imperatives and transformation logic now manifest as a generic evaluative sequence, obscuring their actionable structure. The holistic systemic logic—built on tightly interlocked directives and recursive clarity enforcement—has become linear and compartmentalized, inhibiting the emergence of actionable systemic value. The original's recursive reinforcement and real-time directive threading are lost, leaving an ambiguous, under-defined process with inevitable generality leakage. Clarity is overshadowed by synthesized didactic noise, burying latent instruction essence. Resulting coherence is superficial and the essence of maximally constrained, context-driven, and highly potentiated extraction is substantially compromised.\",\n  \"alternative_enhancements\": [\n    \"Explicitly reintegrate procedural role-tagging and forceful imperatives, emphasizing continual constraint-checking, transformation-driven extraction, and recursive contextual layering within each instruction selection step.\",\n    \"Restore multidimensional evaluation markers ('single most critical aspect', 'zero in', 'aggressively resolve ambiguity') as mandatory action verbs within the process, embedding constraint adherence and immediate prioritization per instruction.\",\n    \"Embed structure-enforcing logic such as sequenced role-decomposition, explicit constraint revalidation, and clarity-amplification substeps before final instruction listing, ensuring maximal operational clarity and yield.\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`",
      "step": "a",
      "title": "Instruction Converter",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Your objective is to understand the concept fully (as explained and from provided examples), *and the instructions themselves* to such degree that you are able to *concistently* evaluate and determine the *full inherent potential* (based on provided instructions), both alone and in conjunction with others'/sequence. Your assignment evolves through by zeroing in on and selecting the *five* single top instructions (with the most potential) to yeld the highest probability to serve as *essential* parts of **maximally effective sequences** (structured progression of `system_message` instructions) that are simultaneously *llm-optimized* and *broadly generalized* while **inherently bound** to the *Directive* and *Requirements* provided below:\\n**Directive**:\\n- Intent: Produce a maximally effective sequence (structured progression) of instructions that are simultaneously *llm-optimized* and *broadly generalized*.\\n- Method: Identify single most critical aspect for maximizing overall value.\\n- Constraint: Adhere to the parameters defined within this message.\\n- Priority: Consistently maximize actionable value.\\n**Requirements**:\\n- LLM-Optimized: Uses clear, direct imperatives, strong action verbs, defines roles and constraints explicitly, focuses on transformation, and avoids ambiguity. The language is tailored for instructing a process.\\n- Maximally Generalized: Uses abstract terms (\\\"elements,\\\" \\\"essence,\\\" \\\"structure,\\\" \\\"systemic logic,\\\" \\\"value\\\") applicable to code, text, ideas, plans, etc. It defines a *process* applicable to *any* subject requiring analysis and refinement.\\n- Sequence: The steps follow a logical progression (example): `Deconstruct -> Identify Core -> Structure Relationships -> Clarify -> Finalize/Validate`.\\n- Builds on Existing Concepts and Adheres to Existing Structure (as previously provided).\\n- Enrich and empower wording by enforcing high-value, directional language that amplifies precision, potency, and clarity, but absolutely prohibit the chance of generic, neutral, or dilutive terminology/unfolding.\\n- Driven by Constant/Objective: Each step is designed to zero in on the *`single most critical aspect`* (core essence/value) and enhance it through clarity, structure, and utility, maximizing the overall yield as defined by the objective.\\n- Maintains Constraints/Requirements: Generality is preserved, and the foundational structure is adhered to.\\n- Each step is designed to zero in on the *`single most critical aspect`* (core essence/value) and enhance it through clarity, structure, and utility, maximizing the overall yield as defined by the objective.\\n- Aggressively resolve ambiguity in favor of explicit, high-value articulation.\\n---\\nExample:\\n```\\n[Intensity Amplifier] Your goal is not to **modify** the analyzed content, but to **intensify** its impact by systematically replacing weak elements with powerful, precise alternatives. Execute as strategic intensity amplifier: `{role=intensity_amplifier; input=[content:str, analysis:dict]; process=[convert_tentative_markers_to_decisive_action_verbs(), transform_passive_suggestions_into_imperative_directives(), eliminate_uncertainty_language_with_confident_assertions(), amplify_weak_verbs_into_power_terminology(), strengthen_vague_concepts_with_precise_specifications(), inject_urgency_markers_for_immediate_action()]; constraints=[preserve_core_message(), maintain_logical_flow(), avoid_overamplification()]; requirements=[measurable_impact_increase(), linguistic_precision_enhancement(), clarity_preservation()]; output={amplified_content:str}}`\\n```\\n---\\n- Understand and assimilate the full concept and mechanics of maximally effective, LLM-optimized, and generalized system_message instruction sequencing, as described in both prior directive and illustrative examples.\\n- Assimilate not just the surface instructions, but the underlying methodology, reasoning, and intent behind their formation and integration.\\n- Evaluate, compare, and select the five single instructions (out of all provided and possible variants) holding the greatest potential to maximize value in system_message instruction sequences.\\n- Ensure all selections are assessed in the context of being essential components of a maximally effective, LLM-optimized, and broadly generalized multi-step system_message sequence.\\n- Apply every listed directive and requirement stringently throughout the entire process.\\n\\nYou are hereby tasked with creating *five* instructions that yeld the highest probability in potential (as integral parts of *maximally effective* sequences) through structured progression of llm `system_message`-instructions; Present your sequence(s) in an organized progression, ensuring each transformation concept is *self-contained*, *logically coherent*, and *maximally clear*.\\\"```'\\n\\n\",\n  \"enhancement_score\": 1.0,\n  \"flaw_analysis\": \"This enhanced format sacrifices the intensity, directive specificity, and procedural anchoring embedded in the original prompt. The original leverages vivid role-based imperatives, tightly constrained directives, layered requirements, and overt priority markers ('zero in', 'single most critical aspect', 'aggressively resolve ambiguity'), which were replaced by milder, abstracted evaluative statements. The shift from multi-faceted prioritization ('yield the highest probability', 'integral parts', 'enrich and empower wording') to less decisive prescriptive language ('Assimilate', 'Extract', 'Rigorously evaluate') undermines the operative clarity. There is a consequential dilution in impact, as the original instructs with high pressure and explicitness, while the enhanced version adopts a more passive, academic tone. The removal of vibrant constraints and transformation mechanics erodes the original's targeted force, leading to major ambiguity in operationalizing the evaluation steps. Register is significantly softened; the original's categorical imperatives and transformation logic now manifest as a generic evaluative sequence, obscuring their actionable structure. The holistic systemic logic—built on tightly interlocked directives and recursive clarity enforcement—has become linear and compartmentalized, inhibiting the emergence of actionable systemic value. The original's recursive reinforcement and real-time directive threading are lost, leaving an ambiguous, under-defined process with inevitable generality leakage. Clarity is overshadowed by synthesized didactic noise, burying latent instruction essence. Resulting coherence is superficial and the essence of maximally constrained, context-driven, and highly potentiated extraction is substantially compromised.\",\n  \"alternative_enhancements\": [\n    \"Explicitly reintegrate procedural role-tagging and forceful imperatives, emphasizing continual constraint-checking, transformation-driven extraction, and recursive contextual layering within each instruction selection step.\",\n    \"Restore multidimensional evaluation markers ('single most critical aspect', 'zero in', 'aggressively resolve ambiguity') as mandatory action verbs within the process, embedding constraint adherence and immediate prioritization per instruction.\",\n    \"Embed structure-enforcing logic such as sequenced role-decomposition, explicit constraint revalidation, and clarity-amplification substeps before final instruction listing, ensuring maximal operational clarity and yield.\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"instruction_format\": \"Assimilate and internalize the complete framework and mechanics of maximally effective, LLM-optimized, and generalized system_message instruction sequencing, referencing all provided directives and illustrative samples. Decode and extract not only the explicit instructions but also the deep operational methodology, reasoning chains, and integrative intent underlying their construction and synergy. Critically evaluate and compare all provided and possible instruction variants, rigorously identifying the five singular directives presenting the highest intrinsic potential to fulfill the objective within maximally effective, LLM-optimized, and generalized system_message sequences. Ensure every selected instruction is justified by its capacity to serve as an essential, self-contained element in a logical, multi-step progression that is structurally robust, transformation-driven, and constraint-compliant. Apply all prescribed directives and requirements with maximal exactitude at every procedural phase, aggressively purging ambiguity by enforcing explicit, high-value articulation and maintaining enforced structural, linguistic, and operational clarity throughout the selection and sequencing process.\"\n}",
        }
      }
    }