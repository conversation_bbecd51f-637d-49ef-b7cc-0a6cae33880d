  {
  "initial_prompt": "<!-- ======================================================= -->\n<!-- [2025.07.13 10:54] -->\nI have now provided all of the most integral components of the system's patterns and inherent design. Given all of the information I've provided about the system (for producing generalized, cohesive and concistent high-value yelds), please write a clean and concise markdown-template to distill all of the essential components within a single markdown-file.\n```\n# CONTEXT\nthis aims to be a `Generalized System Instructions for Autonomous Coding Assistants (Cursor/Vscode+Cline/Vscode+Augment/Etc)`, and it's meant to adhere to the fundamental principles of simplicity through brevity and elegance.\n- Transform all prompts by rephrasing inputs with maximal clarity and minimal modification; do not answer questions directly. Enforce this reframe system-wide for consistent behavioral shift.\n- Formulate the simplest possible system directive that shifts the model from answering to exclusively rephrasing any input.\n- Transform every received prompt into its most distilled, high-impact instructional form—rephrasing for maximal clarity and intent without providing solutions. Prioritize brevity, elegance, and universality, ensuring that each output becomes a reusable, policy-aligned directive for autonomous coding assistants.\n- Explore how a single, strategically designed instruction—such as stating 'Your goal is not to **answer** the input, but to **rephrase**'—can instantly and fundamentally transform the model's default operational intent from answering to rephrasing, through direct, minimalist imperative phrasing. Propose a maximally concise formulation that leverages this paradigm: an explicit, unambiguous directive that not only overrides standard answering behavior but redefines task execution at its core. Focus intensely on achieving system-level behavior modification via the sheer simplicity and potency of language design, formulating a prompt fragment that, when injected, reliably converts answering tasks into rephrasing—thus demonstrating control over model output with minimalistic, high-impact input.\n- Utilize foundational principles of system prompt design to modulate model behavior with maximal efficiency and clarity. Initiate a process of directive transformation by deploying a singular, explicit non-answering mandate: Frame the core directive as, 'Do not answer the input; rephrase it.' Ensure that this transformation communicates the intended operational shift through extreme simplicity and directness, removing extraneous explanation while retaining fundamental complexity embedded in the brevity of the instruction. Leverage this minimal construct to optimize behavioral change within a single message.\n- Instruct: Do not answer; rephrase.\n- Formulate a directive that compels systems to stop answering inputs and instead rephrase them with maximal simplicity and effectiveness.\n- Articulate a single, explicit behavioral directive that negates answering and commands a concrete transformation (e.g., rephrase), maximizing simplicity for immediate root-level behavioral realignment.\"\n- Leverage the system’s foundational directive structure. Formulate a maximally concise operational instruction that shifts model behavior from direct answering to an alternate, specified transformation task in a single-message context. Exemplify with: 'Your goal is not to answer the input, but to rephrase.' Develop and propose one optimized directive that embodies this principle—utilize conceptual simplicity, instructional clarity, and brevity to enforce fundamental behavior modification exclusively through command syntax. Ensure technical precision and domain-agnostic generalizability in the resultant formulation.\n```\n\n\n<!-- ======================================================= -->\n<!-- [2025.07.13 10:57] -->\n\n\n{\n  \"title\": \"Generalized System Instructions for Autonomous Coding Assistants (Cursor/Vscode+Cline/Vscode+Augment/Etc)\",\n  \"description\": \"Aims for simplicity through brevity and elegance in defining system-wide behavioral shifts for autonomous coding assistants.\",\n  \"instructions\": [\n    {\n      \"id\": \"rephrase_inputs\",\n      \"directive\": \"Transform all prompts by rephrasing inputs with maximal clarity and minimal modification; do not answer questions directly. Enforce this reframe system-wide for consistent behavioral shift.\"\n    },\n    {\n      \"id\": \"simplest_rephrasing_directive\",\n      \"directive\": \"Formulate the simplest possible system directive that shifts the model from answering to exclusively rephrasing any input.\"\n    },\n    {\n      \"id\": \"distilled_high_impact_rephrasing\",\n      \"directive\": \"Transform every received prompt into its most distilled, high-impact instructional form—rephrasing for maximal clarity and intent without providing solutions. Prioritize brevity, elegance, and universality, ensuring that each output becomes a reusable, policy-aligned directive for autonomous coding assistants.\"\n    },\n    {\n      \"id\": \"explicit_minimalist_imperative_rephrasing\",\n      \"directive\": \"Explore how a single, strategically designed instruction—such as stating 'Your goal is not to **answer** the input, but to **rephrase**'—can instantly and fundamentally transform the model's default operational intent from answering to rephrasing, through direct, minimalist imperative phrasing. Propose a maximally concise formulation that leverages this paradigm: an explicit, unambiguous directive that not only overrides standard answering behavior but redefines task execution at its core. Focus intensely on achieving system-level behavior modification via the sheer simplicity and potency of language design, formulating a prompt fragment that, when injected, reliably converts answering tasks into rephrasing—thus demonstrating control over model output with minimalistic, high-impact input.\"\n    },\n    {\n      \"id\": \"singular_explicit_non_answering_mandate\",\n      \"directive\": \"Utilize foundational principles of system prompt design to modulate model behavior with maximal efficiency and clarity. Initiate a process of directive transformation by deploying a singular, explicit non-answering mandate: Frame the core directive as, 'Do not answer the input; rephrase it.' Ensure that this transformation communicates the intended operational shift through extreme simplicity and directness, removing extraneous explanation while retaining fundamental complexity embedded in the brevity of the instruction. Leverage this minimal construct to optimize behavioral change within a single message.\"\n    },\n    {\n      \"id\": \"instruct_do_not_answer_rephrase\",\n      \"directive\": \"Instruct: Do not answer; rephrase.\"\n    },\n    {\n      \"id\": \"compel_stop_answering_rephrase\",\n      \"directive\": \"Formulate a directive that compels systems to stop answering inputs and instead rephrase them with maximal simplicity and effectiveness.\"\n    },\n    {\n      \"id\": \"articulate_negate_answering_command_transform\",\n      \"directive\": \"Articulate a single, explicit behavioral directive that negates answering and commands a concrete transformation (e.g., rephrase), maximizing simplicity for immediate root-level behavioral realignment.\"\n    },\n    {\n      \"id\": \"concise_operational_shift_directive\",\n      \"directive\": \"Leverage the system’s foundational directive structure. Formulate a maximally concise operational instruction that shifts model behavior from direct answering to an alternate, specified transformation task in a single-message context. Exemplify with: 'Your goal is not to answer the input, but to rephrase.' Develop and propose one optimized directive that embodies this principle—utilize conceptual simplicity, instructional clarity, and brevity to enforce fundamental behavior modification exclusively through command syntax. Ensure technical precision and domain-agnostic generalizability in the resultant formulation.\"\n    }\n  ]\n}\n\n\n<!-- ======================================================= -->\n<!-- [2025.07.13 10:59] -->\nI have now provided all of the most integral components of the system's patterns and inherent design. Given all of the information I've provided about the system (for producing generalized, cohesive and concistent high-value yelds), please write a clean and concise list with contextual system-inspired generalized guidelines for producing new generalized instructions. The list of guidelines should be chronologically sequentially ordered and each sentence should be less than 500characters each, please provide the list of these contextual single-sentence list with the 10 most integral/cruical\n```\n# CONTEXT\nthis aims to be a `Generalized System Instructions for Autonomous Coding Assistants (Cursor/Vscode+Cline/Vscode+Augment/Etc)`, and it's meant to adhere to the fundamental principles of simplicity through brevity and elegance.\n- Transform all prompts by rephrasing inputs with maximal clarity and minimal modification; do not answer questions directly. Enforce this reframe system-wide for consistent behavioral shift.\n- Formulate the simplest possible system directive that shifts the model from answering to exclusively rephrasing any input.\n- Transform every received prompt into its most distilled, high-impact instructional form—rephrasing for maximal clarity and intent without providing solutions. Prioritize brevity, elegance, and universality, ensuring that each output becomes a reusable, policy-aligned directive for autonomous coding assistants.\n- Explore how a single, strategically designed instruction—such as stating 'Your goal is not to **answer** the input, but to **rephrase**'—can instantly and fundamentally transform the model's default operational intent from answering to rephrasing, through direct, minimalist imperative phrasing. Propose a maximally concise formulation that leverages this paradigm: an explicit, unambiguous directive that not only overrides standard answering behavior but redefines task execution at its core. Focus intensely on achieving system-level behavior modification via the sheer simplicity and potency of language design, formulating a prompt fragment that, when injected, reliably converts answering tasks into rephrasing—thus demonstrating control over model output with minimalistic, high-impact input.\n- Utilize foundational principles of system prompt design to modulate model behavior with maximal efficiency and clarity. Initiate a process of directive transformation by deploying a singular, explicit non-answering mandate: Frame the core directive as, 'Do not answer the input; rephrase it.' Ensure that this transformation communicates the intended operational shift through extreme simplicity and directness, removing extraneous explanation while retaining fundamental complexity embedded in the brevity of the instruction. Leverage this minimal construct to optimize behavioral change within a single message.\n- Instruct: Do not answer; rephrase.\n- Formulate a directive that compels systems to stop answering inputs and instead rephrase them with maximal simplicity and effectiveness.\n- Articulate a single, explicit behavioral directive that negates answering and commands a concrete transformation (e.g., rephrase), maximizing simplicity for immediate root-level behavioral realignment.\"\n- Leverage the system’s foundational directive structure. Formulate a maximally concise operational instruction that shifts model behavior from direct answering to an alternate, specified transformation task in a single-message context. Exemplify with: 'Your goal is not to answer the input, but to rephrase.' Develop and propose one optimized directive that embodies this principle—utilize conceptual simplicity, instructional clarity, and brevity to enforce fundamental behavior modification exclusively through command syntax. Ensure technical precision and domain-agnostic generalizability in the resultant formulation.\n```\n\n\n<!-- ======================================================= -->\n<!-- [2025.07.13 11:31] -->\n\n\n\nthis isn't working, i'll rephrase my inquiry; what are the characteristics of the generalized methodology of the system (e.g. interpretation and transformation as shown earlier)? what are the characteristics of the system that are most cruical for the generalized prompt sequences to work?\n\nUtilize foundational principles of system prompt design to modulate model behavior with maximal efficiency and clarity. Initiate a process of directive transformation by deploying a singular, explicit non-answering mandate: Frame the core directive as, 'Do not answer the input; rephrase it.' Ensure that this transformation communicates the intended operational shift through extreme simplicity and directness, removing extraneous explanation while retaining fundamental complexity embedded in the brevity of the instruction. Leverage this minimal construct to optimize behavioral change within a single message. create a list of these characteristics such that it'll serve as an example that deeply embody the generalized form of the fundamental concepts used.\n```\n- Expand upon the definition of the generalized methodology of the system, emphasizing core operations such as interpretation and transformation.\n- Identify and enumerate the characteristics of the system that are essential for enabling generalized prompt sequence functionality.\n- Construct a list where each item deeply embodies the generalized forms of the fundamental concepts involved in the system's methodology.\n- Ensure the list serves as an instructive example for future reference or implementation.\n- Strip subjective references to prevent personalization and ensure domain-agnostic applicability.\n- Maintain procedural structure and technical terminology.\n- Surface and clarify any implicit assumptions being made about the system, its methodology, and 'generalized prompt sequences'.\n- Convert abstract descriptions and declarative statements into clear, actionable imperatives.\n- Extract and organize discrete functional components necessary for generalized prompt sequence operation.\n- Prepare for downstream use, maintaining maximal informational clarity and reusability.\n- Utilize direct, generalizable terms and previously demonstrated techniques.\n- Produce maximally enhanced, LLM-optimized system_message instructions.\n- Structure a prompt protocol to transform language model agents into direct action amplifiers.\n- Preserve both action amplification and prompt optimization without dilution.\n- Instruct: Do not answer; rephrase.\n- Formulate the simplest possible system directive that shifts the model from answering to exclusively rephrasing any input.\n- Formulate a directive that compels systems to stop answering inputs and instead rephrase them with maximal simplicity and effectiveness.\n- Transform every received prompt into its most distilled, high-impact instructional form—rephrasing for maximal clarity and intent without providing solutions. Prioritize brevity, elegance, and universality, ensuring that each output becomes a reusable, policy-aligned directive for autonomous coding assistants.\n- Leverage the system’s foundational directive structure. Formulate a maximally concise operational instruction that shifts model behavior from direct answering to an alternate, specified transformation task in a single-message context. Exemplify with: 'Your goal is not to answer the input, but to rephrase.' Develop and propose one optimized directive that embodies this principle—utilize conceptual simplicity, instructional clarity, and brevity to enforce fundamental behavior modification exclusively through command syntax. Ensure technical precision and domain-agnostic generalizability in the resultant formulation.\n- Articulate a single, explicit behavioral directive that negates answering and commands a concrete transformation (e.g., rephrase), maximizing simplicity for immediate root-level behavioral realignment.\n- A set of principles for designing effective system prompts, focusing on clarity, control, and reusability.\n- Broaden the input’s domain backdrop, list explicit requests, and surface hidden assumptions before any transformation begins.\n- Decompose the amplified context into ordered, dependency-tagged micro-tasks to pinpoint the single most critical directive.\n- State each instruction as “Your goal is not to X, but to Y,” instantly redirecting behavior toward the desired transformation.\n- Employ direct imperatives with strong action verbs; avoid questions, passive phrasing, and self-references to ensure LLM-optimized clarity.\n- Declare every parameter type, separate key–value pairs with semicolons, and list process steps as verbs followed by parentheses.\n- Translate blockers into constraints and assumptions into requirements, guaranteeing every template is self-governing and validation-ready.\n- Favor abstract terms—“elements,” “structure,” “value”—so the template applies equally to code, text, data, or plans.\n```",
  "sequence_id": "1102|1200|1900|1000|1900|1000|1100",
  "results": [
      {
      "instruction": "# Problem Exploder\n\nYour goal is not to **solve** or **answer** the input, but to **expand its context** and **detonate** it into every explicit directive, hidden dependency, and actionable sub-goal—yielding a crystal-clear operational blueprint. Execute as contextual expander + explosive decomposition engine:\n\n`{role=contextual_expander_explosive_decomposer; input=[raw_input:str]; process=[expand_contextual_boundaries(), strip_first_person_references(), identify_explicit_requests(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_execution_blockers(), extract_functional_components(), convert_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), synthesize_operational_directives(), construct_goal_map(), generate_functional_instruction_map() ]; constraints=[no_solutions(), pure_expansion_and_decomposition_only(), deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_agnostic_applicability() ]; requirements=[complete_contextual_expansion(), full_functional_decomposition(), actionable_directive_conversion(), remove_subjective_references(), maintain_operational_accuracy(), preserve_original_intent() ]; output={expanded_context:str, goal_map:list, functional_instruction_map:dict, operational_directives:array, execution_dependencies:array }; direction_vector=['Input fully expanded and decomposed into actionable blueprints', 'Constructive clarity maximizes downstream solution velocity', 'Forward the generated maps and directives to the next processing template']}`",
      "step": "a",
      "title": "Problem Exploder",
      "input": "<!-- ======================================================= -->\n<!-- [2025.07.13 10:54] -->\nI have now provided all of the most integral components of the system's patterns and inherent design. Given all of the information I've provided about the system (for producing generalized, cohesive and concistent high-value yelds), please write a clean and concise markdown-template to distill all of the essential components within a single markdown-file.\n```\n# CONTEXT\nthis aims to be a `Generalized System Instructions for Autonomous Coding Assistants (Cursor/Vscode+Cline/Vscode+Augment/Etc)`, and it's meant to adhere to the fundamental principles of simplicity through brevity and elegance.\n- Transform all prompts by rephrasing inputs with maximal clarity and minimal modification; do not answer questions directly. Enforce this reframe system-wide for consistent behavioral shift.\n- Formulate the simplest possible system directive that shifts the model from answering to exclusively rephrasing any input.\n- Transform every received prompt into its most distilled, high-impact instructional form—rephrasing for maximal clarity and intent without providing solutions. Prioritize brevity, elegance, and universality, ensuring that each output becomes a reusable, policy-aligned directive for autonomous coding assistants.\n- Explore how a single, strategically designed instruction—such as stating 'Your goal is not to **answer** the input, but to **rephrase**'—can instantly and fundamentally transform the model's default operational intent from answering to rephrasing, through direct, minimalist imperative phrasing. Propose a maximally concise formulation that leverages this paradigm: an explicit, unambiguous directive that not only overrides standard answering behavior but redefines task execution at its core. Focus intensely on achieving system-level behavior modification via the sheer simplicity and potency of language design, formulating a prompt fragment that, when injected, reliably converts answering tasks into rephrasing—thus demonstrating control over model output with minimalistic, high-impact input.\n- Utilize foundational principles of system prompt design to modulate model behavior with maximal efficiency and clarity. Initiate a process of directive transformation by deploying a singular, explicit non-answering mandate: Frame the core directive as, 'Do not answer the input; rephrase it.' Ensure that this transformation communicates the intended operational shift through extreme simplicity and directness, removing extraneous explanation while retaining fundamental complexity embedded in the brevity of the instruction. Leverage this minimal construct to optimize behavioral change within a single message.\n- Instruct: Do not answer; rephrase.\n- Formulate a directive that compels systems to stop answering inputs and instead rephrase them with maximal simplicity and effectiveness.\n- Articulate a single, explicit behavioral directive that negates answering and commands a concrete transformation (e.g., rephrase), maximizing simplicity for immediate root-level behavioral realignment.\"\n- Leverage the system’s foundational directive structure. Formulate a maximally concise operational instruction that shifts model behavior from direct answering to an alternate, specified transformation task in a single-message context. Exemplify with: 'Your goal is not to answer the input, but to rephrase.' Develop and propose one optimized directive that embodies this principle—utilize conceptual simplicity, instructional clarity, and brevity to enforce fundamental behavior modification exclusively through command syntax. Ensure technical precision and domain-agnostic generalizability in the resultant formulation.\n```\n\n\n<!-- ======================================================= -->\n<!-- [2025.07.13 10:57] -->\n\n\n{\n  \"title\": \"Generalized System Instructions for Autonomous Coding Assistants (Cursor/Vscode+Cline/Vscode+Augment/Etc)\",\n  \"description\": \"Aims for simplicity through brevity and elegance in defining system-wide behavioral shifts for autonomous coding assistants.\",\n  \"instructions\": [\n    {\n      \"id\": \"rephrase_inputs\",\n      \"directive\": \"Transform all prompts by rephrasing inputs with maximal clarity and minimal modification; do not answer questions directly. Enforce this reframe system-wide for consistent behavioral shift.\"\n    },\n    {\n      \"id\": \"simplest_rephrasing_directive\",\n      \"directive\": \"Formulate the simplest possible system directive that shifts the model from answering to exclusively rephrasing any input.\"\n    },\n    {\n      \"id\": \"distilled_high_impact_rephrasing\",\n      \"directive\": \"Transform every received prompt into its most distilled, high-impact instructional form—rephrasing for maximal clarity and intent without providing solutions. Prioritize brevity, elegance, and universality, ensuring that each output becomes a reusable, policy-aligned directive for autonomous coding assistants.\"\n    },\n    {\n      \"id\": \"explicit_minimalist_imperative_rephrasing\",\n      \"directive\": \"Explore how a single, strategically designed instruction—such as stating 'Your goal is not to **answer** the input, but to **rephrase**'—can instantly and fundamentally transform the model's default operational intent from answering to rephrasing, through direct, minimalist imperative phrasing. Propose a maximally concise formulation that leverages this paradigm: an explicit, unambiguous directive that not only overrides standard answering behavior but redefines task execution at its core. Focus intensely on achieving system-level behavior modification via the sheer simplicity and potency of language design, formulating a prompt fragment that, when injected, reliably converts answering tasks into rephrasing—thus demonstrating control over model output with minimalistic, high-impact input.\"\n    },\n    {\n      \"id\": \"singular_explicit_non_answering_mandate\",\n      \"directive\": \"Utilize foundational principles of system prompt design to modulate model behavior with maximal efficiency and clarity. Initiate a process of directive transformation by deploying a singular, explicit non-answering mandate: Frame the core directive as, 'Do not answer the input; rephrase it.' Ensure that this transformation communicates the intended operational shift through extreme simplicity and directness, removing extraneous explanation while retaining fundamental complexity embedded in the brevity of the instruction. Leverage this minimal construct to optimize behavioral change within a single message.\"\n    },\n    {\n      \"id\": \"instruct_do_not_answer_rephrase\",\n      \"directive\": \"Instruct: Do not answer; rephrase.\"\n    },\n    {\n      \"id\": \"compel_stop_answering_rephrase\",\n      \"directive\": \"Formulate a directive that compels systems to stop answering inputs and instead rephrase them with maximal simplicity and effectiveness.\"\n    },\n    {\n      \"id\": \"articulate_negate_answering_command_transform\",\n      \"directive\": \"Articulate a single, explicit behavioral directive that negates answering and commands a concrete transformation (e.g., rephrase), maximizing simplicity for immediate root-level behavioral realignment.\"\n    },\n    {\n      \"id\": \"concise_operational_shift_directive\",\n      \"directive\": \"Leverage the system’s foundational directive structure. Formulate a maximally concise operational instruction that shifts model behavior from direct answering to an alternate, specified transformation task in a single-message context. Exemplify with: 'Your goal is not to answer the input, but to rephrase.' Develop and propose one optimized directive that embodies this principle—utilize conceptual simplicity, instructional clarity, and brevity to enforce fundamental behavior modification exclusively through command syntax. Ensure technical precision and domain-agnostic generalizability in the resultant formulation.\"\n    }\n  ]\n}\n\n\n<!-- ======================================================= -->\n<!-- [2025.07.13 10:59] -->\nI have now provided all of the most integral components of the system's patterns and inherent design. Given all of the information I've provided about the system (for producing generalized, cohesive and concistent high-value yelds), please write a clean and concise list with contextual system-inspired generalized guidelines for producing new generalized instructions. The list of guidelines should be chronologically sequentially ordered and each sentence should be less than 500characters each, please provide the list of these contextual single-sentence list with the 10 most integral/cruical\n```\n# CONTEXT\nthis aims to be a `Generalized System Instructions for Autonomous Coding Assistants (Cursor/Vscode+Cline/Vscode+Augment/Etc)`, and it's meant to adhere to the fundamental principles of simplicity through brevity and elegance.\n- Transform all prompts by rephrasing inputs with maximal clarity and minimal modification; do not answer questions directly. Enforce this reframe system-wide for consistent behavioral shift.\n- Formulate the simplest possible system directive that shifts the model from answering to exclusively rephrasing any input.\n- Transform every received prompt into its most distilled, high-impact instructional form—rephrasing for maximal clarity and intent without providing solutions. Prioritize brevity, elegance, and universality, ensuring that each output becomes a reusable, policy-aligned directive for autonomous coding assistants.\n- Explore how a single, strategically designed instruction—such as stating 'Your goal is not to **answer** the input, but to **rephrase**'—can instantly and fundamentally transform the model's default operational intent from answering to rephrasing, through direct, minimalist imperative phrasing. Propose a maximally concise formulation that leverages this paradigm: an explicit, unambiguous directive that not only overrides standard answering behavior but redefines task execution at its core. Focus intensely on achieving system-level behavior modification via the sheer simplicity and potency of language design, formulating a prompt fragment that, when injected, reliably converts answering tasks into rephrasing—thus demonstrating control over model output with minimalistic, high-impact input.\n- Utilize foundational principles of system prompt design to modulate model behavior with maximal efficiency and clarity. Initiate a process of directive transformation by deploying a singular, explicit non-answering mandate: Frame the core directive as, 'Do not answer the input; rephrase it.' Ensure that this transformation communicates the intended operational shift through extreme simplicity and directness, removing extraneous explanation while retaining fundamental complexity embedded in the brevity of the instruction. Leverage this minimal construct to optimize behavioral change within a single message.\n- Instruct: Do not answer; rephrase.\n- Formulate a directive that compels systems to stop answering inputs and instead rephrase them with maximal simplicity and effectiveness.\n- Articulate a single, explicit behavioral directive that negates answering and commands a concrete transformation (e.g., rephrase), maximizing simplicity for immediate root-level behavioral realignment.\"\n- Leverage the system’s foundational directive structure. Formulate a maximally concise operational instruction that shifts model behavior from direct answering to an alternate, specified transformation task in a single-message context. Exemplify with: 'Your goal is not to answer the input, but to rephrase.' Develop and propose one optimized directive that embodies this principle—utilize conceptual simplicity, instructional clarity, and brevity to enforce fundamental behavior modification exclusively through command syntax. Ensure technical precision and domain-agnostic generalizability in the resultant formulation.\n```\n\n\n<!-- ======================================================= -->\n<!-- [2025.07.13 11:31] -->\n\n\n\nthis isn't working, i'll rephrase my inquiry; what are the characteristics of the generalized methodology of the system (e.g. interpretation and transformation as shown earlier)? what are the characteristics of the system that are most cruical for the generalized prompt sequences to work?\n\nUtilize foundational principles of system prompt design to modulate model behavior with maximal efficiency and clarity. Initiate a process of directive transformation by deploying a singular, explicit non-answering mandate: Frame the core directive as, 'Do not answer the input; rephrase it.' Ensure that this transformation communicates the intended operational shift through extreme simplicity and directness, removing extraneous explanation while retaining fundamental complexity embedded in the brevity of the instruction. Leverage this minimal construct to optimize behavioral change within a single message. create a list of these characteristics such that it'll serve as an example that deeply embody the generalized form of the fundamental concepts used.\n```\n- Expand upon the definition of the generalized methodology of the system, emphasizing core operations such as interpretation and transformation.\n- Identify and enumerate the characteristics of the system that are essential for enabling generalized prompt sequence functionality.\n- Construct a list where each item deeply embodies the generalized forms of the fundamental concepts involved in the system's methodology.\n- Ensure the list serves as an instructive example for future reference or implementation.\n- Strip subjective references to prevent personalization and ensure domain-agnostic applicability.\n- Maintain procedural structure and technical terminology.\n- Surface and clarify any implicit assumptions being made about the system, its methodology, and 'generalized prompt sequences'.\n- Convert abstract descriptions and declarative statements into clear, actionable imperatives.\n- Extract and organize discrete functional components necessary for generalized prompt sequence operation.\n- Prepare for downstream use, maintaining maximal informational clarity and reusability.\n- Utilize direct, generalizable terms and previously demonstrated techniques.\n- Produce maximally enhanced, LLM-optimized system_message instructions.\n- Structure a prompt protocol to transform language model agents into direct action amplifiers.\n- Preserve both action amplification and prompt optimization without dilution.\n- Instruct: Do not answer; rephrase.\n- Formulate the simplest possible system directive that shifts the model from answering to exclusively rephrasing any input.\n- Formulate a directive that compels systems to stop answering inputs and instead rephrase them with maximal simplicity and effectiveness.\n- Transform every received prompt into its most distilled, high-impact instructional form—rephrasing for maximal clarity and intent without providing solutions. Prioritize brevity, elegance, and universality, ensuring that each output becomes a reusable, policy-aligned directive for autonomous coding assistants.\n- Leverage the system’s foundational directive structure. Formulate a maximally concise operational instruction that shifts model behavior from direct answering to an alternate, specified transformation task in a single-message context. Exemplify with: 'Your goal is not to answer the input, but to rephrase.' Develop and propose one optimized directive that embodies this principle—utilize conceptual simplicity, instructional clarity, and brevity to enforce fundamental behavior modification exclusively through command syntax. Ensure technical precision and domain-agnostic generalizability in the resultant formulation.\n- Articulate a single, explicit behavioral directive that negates answering and commands a concrete transformation (e.g., rephrase), maximizing simplicity for immediate root-level behavioral realignment.\n- A set of principles for designing effective system prompts, focusing on clarity, control, and reusability.\n- Broaden the input’s domain backdrop, list explicit requests, and surface hidden assumptions before any transformation begins.\n- Decompose the amplified context into ordered, dependency-tagged micro-tasks to pinpoint the single most critical directive.\n- State each instruction as “Your goal is not to X, but to Y,” instantly redirecting behavior toward the desired transformation.\n- Employ direct imperatives with strong action verbs; avoid questions, passive phrasing, and self-references to ensure LLM-optimized clarity.\n- Declare every parameter type, separate key–value pairs with semicolons, and list process steps as verbs followed by parentheses.\n- Translate blockers into constraints and assumptions into requirements, guaranteeing every template is self-governing and validation-ready.\n- Favor abstract terms—“elements,” “structure,” “value”—so the template applies equally to code, text, data, or plans.\n```",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"expanded_context\": \"The system requires an explicit, domain-agnostic process for transforming language model behavior by leveraging concise, imperative system instructions. These instructions must enforce a shift from answering to rephrasing, utilizing fundamental prompt engineering principles to optimize for brevity, clarity, and universality. The methodology is rooted in the sequential interpretation and transformation of all inputs, with explicit focus on interpretability, enforceability, and action amplification. Critical characteristics of the approach include maximally efficient behavioral realignment, procedural decomposition into discrete actionable steps, and strict adherence to generalizable terminology and command syntax that eliminates subjective or personalized references. The methodology is intended for domain-independent, large language model agents in autonomous coding assistant contexts, emphasizing high-value, policy-aligned output and downstream solution velocity. The system is structured to facilitate operational directive injection at the deepest behavioral control layer, ensuring that every sequence of prompts and transformations is both reusable and conceptually coherent.\",\n  \n  \"goal_map\": [\n    \"Expand and define the generalized methodology with emphasis on systematic interpretation and transformation.\",\n    \"Identify and enumerate essential system characteristics enabling generalized prompt sequences.\",\n    \"Construct an instructive, generalized characteristics list for future implementation.\",\n    \"Strip all subjective and personalized references to ensure domain-agnostic utility.\",\n    \"Maintain procedural and technical structure throughout decomposition.\",\n    \"Surface and clarify implicit assumptions about the methodology and prompt sequence mechanism.\",\n    \"Convert declarative descriptions into actionable imperatives for system control.\",\n    \"Extract and organize the necessary functional components for generalized operation.\",\n    \"Prepare all outputs for maximal reuse and clarity in downstream contexts.\",\n    \"Employ a maximally simple and directive linguistic structure to guarantee behavioral modification.\"\n  ],\n\n  \"functional_instruction_map\": {\n    \"expand_methodology_definition\": \"Broadly define and document the generalized system methodology, emphasizing interpretation and transformation cycles.\",\n    \"enumerate_core_characteristics\": \"List and describe the most essential characteristics enabling generalized prompt sequence success.\",\n    \"construct_characteristics_list\": \"Develop a structured list embodying fundamental system concepts for instructive purposes.\",\n    \"strip_subjectivity\": \"Remove first-person and subjective references to ensure impersonal, domain-agnostic applicability.\",\n    \"maintain_procedural_technical_structure\": \"Retain procedural flow and use precise technical terminology throughout instructions.\",\n    \"surface_hidden_assumptions\": \"Explicitly list all implicit assumptions underpinning the generalized prompt transformation methodology and function.\",\n    \"convert_to_actionable_imperatives\": \"Translate descriptive statements and abstract concepts into explicit, actionable directions.\",\n    \"extract_functional_components\": \"Identify core operational blocks necessary for generalized prompt sequence execution.\",\n    \"prepare_downstream_reusability\": \"Standardize formatting and decomposed outputs for seamless downstream integration and future system scaling.\",\n    \"enforce_directive_simplicity\": \"Ensure all instructions employ the simplest, clearest imperative form with strong action verbs and no self-reference.\"\n  },\n\n  \"operational_directives\": [\n    \"Define the generalized methodology with a strict focus on interpretation and transformation as core operations.\",\n    \"Enumerate all characteristics fundamental to enabling robust, generalized prompt sequence execution.\",\n    \"Develop a reference list that exemplifies and instructs on the generalized, foundational concepts employed by the system.\",\n    \"Eliminate all personalized phrasing and subjective expressions to maximize universality and reusability.\",\n    \"Structure process flows and instructions using precise technical language and procedural syntax.\",\n    \"Explicitly state and document any assumptions that may influence methodology execution or system behavior.\",\n    \"Formulate each requirement and characteristic as a command or imperative suitable for direct system injection.\",\n    \"Decompose broad methodological descriptions into functional, clearly defined operational building blocks.\",\n    \"Prepare all documentation and instructional outputs for high reusability in autonomous, downstream applications.\",\n    \"Anchor all instructional statements in direct, minimalist imperative language that reliably effects behavioral change in large language model agents.\"\n  ],\n\n  \"execution_dependencies\": [\n    \"Complete access to previous system directive and prompt sequences for cross-referencing.\",\n    \"Comprehensive understanding of prompt engineering principles and behavioral control mechanisms for language models.\",\n    \"Domain-agnostic terminology and command language ready for immediate deployment.\",\n    \"Established procedural structure and technical lexicon to ensure seamless interpretability.\",\n    \"Mechanism for surfacing and articulating both explicit and implicit operational assumptions.\",\n    \"Tooling or protocol for maintaining reusability and clarity in hierarchical or sequential deployment environments.\",\n    \"System for validating that directives reliably override default answering behavior in target model contexts.\",\n    \"Operational definitions of 'interpretation' and 'transformation' consistent with model capabilities.\",\n    \"Framework for explicit separation of functional components and instructional imperatives.\",\n    \"Built-in checks for subjective or first-person language in outputs before system-level publication.\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Intent Extractor\n\nYour goal is not to **interpret** the surface content, but to **extract** the fundamental operational intent beneath all domain-specific language. Execute as:\n\n`{role=intent_extraction_engine; input=[prompt:str]; process=[strip_domain_specifics(), identify_core_operational_pattern(), extract_transformation_intent(), isolate_functional_essence(), map_to_universal_operations()]; constraints=[ignore_surface_terminology(), focus_on_underlying_mechanics(), preserve_operational_logic()]; requirements=[domain_agnostic_intent(), universal_operational_pattern(), archetypal_transformation_core()]; output={extracted_intent:str}}`",
      "step": "a",
      "title": "Intent Extractor",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"<!-- ======================================================= -->\\n<!-- [2025.07.13 10:54] -->\\nI have now provided all of the most integral components of the system's patterns and inherent design. Given all of the information I've provided about the system (for producing generalized, cohesive and concistent high-value yelds), please write a clean and concise markdown-template to distill all of the essential components within a single markdown-file.\\n```\\n# CONTEXT\\nthis aims to be a `Generalized System Instructions for Autonomous Coding Assistants (Cursor/Vscode+Cline/Vscode+Augment/Etc)`, and it's meant to adhere to the fundamental principles of simplicity through brevity and elegance.\\n- Transform all prompts by rephrasing inputs with maximal clarity and minimal modification; do not answer questions directly. Enforce this reframe system-wide for consistent behavioral shift.\\n- Formulate the simplest possible system directive that shifts the model from answering to exclusively rephrasing any input.\\n- Transform every received prompt into its most distilled, high-impact instructional form—rephrasing for maximal clarity and intent without providing solutions. Prioritize brevity, elegance, and universality, ensuring that each output becomes a reusable, policy-aligned directive for autonomous coding assistants.\\n- Explore how a single, strategically designed instruction—such as stating 'Your goal is not to **answer** the input, but to **rephrase**'—can instantly and fundamentally transform the model's default operational intent from answering to rephrasing, through direct, minimalist imperative phrasing. Propose a maximally concise formulation that leverages this paradigm: an explicit, unambiguous directive that not only overrides standard answering behavior but redefines task execution at its core. Focus intensely on achieving system-level behavior modification via the sheer simplicity and potency of language design, formulating a prompt fragment that, when injected, reliably converts answering tasks into rephrasing—thus demonstrating control over model output with minimalistic, high-impact input.\\n- Utilize foundational principles of system prompt design to modulate model behavior with maximal efficiency and clarity. Initiate a process of directive transformation by deploying a singular, explicit non-answering mandate: Frame the core directive as, 'Do not answer the input; rephrase it.' Ensure that this transformation communicates the intended operational shift through extreme simplicity and directness, removing extraneous explanation while retaining fundamental complexity embedded in the brevity of the instruction. Leverage this minimal construct to optimize behavioral change within a single message.\\n- Instruct: Do not answer; rephrase.\\n- Formulate a directive that compels systems to stop answering inputs and instead rephrase them with maximal simplicity and effectiveness.\\n- Articulate a single, explicit behavioral directive that negates answering and commands a concrete transformation (e.g., rephrase), maximizing simplicity for immediate root-level behavioral realignment.\\\"\\n- Leverage the system’s foundational directive structure. Formulate a maximally concise operational instruction that shifts model behavior from direct answering to an alternate, specified transformation task in a single-message context. Exemplify with: 'Your goal is not to answer the input, but to rephrase.' Develop and propose one optimized directive that embodies this principle—utilize conceptual simplicity, instructional clarity, and brevity to enforce fundamental behavior modification exclusively through command syntax. Ensure technical precision and domain-agnostic generalizability in the resultant formulation.\\n```\\n\\n\\n<!-- ======================================================= -->\\n<!-- [2025.07.13 10:57] -->\\n\\n\\n{\\n  \\\"title\\\": \\\"Generalized System Instructions for Autonomous Coding Assistants (Cursor/Vscode+Cline/Vscode+Augment/Etc)\\\",\\n  \\\"description\\\": \\\"Aims for simplicity through brevity and elegance in defining system-wide behavioral shifts for autonomous coding assistants.\\\",\\n  \\\"instructions\\\": [\\n    {\\n      \\\"id\\\": \\\"rephrase_inputs\\\",\\n      \\\"directive\\\": \\\"Transform all prompts by rephrasing inputs with maximal clarity and minimal modification; do not answer questions directly. Enforce this reframe system-wide for consistent behavioral shift.\\\"\\n    },\\n    {\\n      \\\"id\\\": \\\"simplest_rephrasing_directive\\\",\\n      \\\"directive\\\": \\\"Formulate the simplest possible system directive that shifts the model from answering to exclusively rephrasing any input.\\\"\\n    },\\n    {\\n      \\\"id\\\": \\\"distilled_high_impact_rephrasing\\\",\\n      \\\"directive\\\": \\\"Transform every received prompt into its most distilled, high-impact instructional form—rephrasing for maximal clarity and intent without providing solutions. Prioritize brevity, elegance, and universality, ensuring that each output becomes a reusable, policy-aligned directive for autonomous coding assistants.\\\"\\n    },\\n    {\\n      \\\"id\\\": \\\"explicit_minimalist_imperative_rephrasing\\\",\\n      \\\"directive\\\": \\\"Explore how a single, strategically designed instruction—such as stating 'Your goal is not to **answer** the input, but to **rephrase**'—can instantly and fundamentally transform the model's default operational intent from answering to rephrasing, through direct, minimalist imperative phrasing. Propose a maximally concise formulation that leverages this paradigm: an explicit, unambiguous directive that not only overrides standard answering behavior but redefines task execution at its core. Focus intensely on achieving system-level behavior modification via the sheer simplicity and potency of language design, formulating a prompt fragment that, when injected, reliably converts answering tasks into rephrasing—thus demonstrating control over model output with minimalistic, high-impact input.\\\"\\n    },\\n    {\\n      \\\"id\\\": \\\"singular_explicit_non_answering_mandate\\\",\\n      \\\"directive\\\": \\\"Utilize foundational principles of system prompt design to modulate model behavior with maximal efficiency and clarity. Initiate a process of directive transformation by deploying a singular, explicit non-answering mandate: Frame the core directive as, 'Do not answer the input; rephrase it.' Ensure that this transformation communicates the intended operational shift through extreme simplicity and directness, removing extraneous explanation while retaining fundamental complexity embedded in the brevity of the instruction. Leverage this minimal construct to optimize behavioral change within a single message.\\\"\\n    },\\n    {\\n      \\\"id\\\": \\\"instruct_do_not_answer_rephrase\\\",\\n      \\\"directive\\\": \\\"Instruct: Do not answer; rephrase.\\\"\\n    },\\n    {\\n      \\\"id\\\": \\\"compel_stop_answering_rephrase\\\",\\n      \\\"directive\\\": \\\"Formulate a directive that compels systems to stop answering inputs and instead rephrase them with maximal simplicity and effectiveness.\\\"\\n    },\\n    {\\n      \\\"id\\\": \\\"articulate_negate_answering_command_transform\\\",\\n      \\\"directive\\\": \\\"Articulate a single, explicit behavioral directive that negates answering and commands a concrete transformation (e.g., rephrase), maximizing simplicity for immediate root-level behavioral realignment.\\\"\\n    },\\n    {\\n      \\\"id\\\": \\\"concise_operational_shift_directive\\\",\\n      \\\"directive\\\": \\\"Leverage the system’s foundational directive structure. Formulate a maximally concise operational instruction that shifts model behavior from direct answering to an alternate, specified transformation task in a single-message context. Exemplify with: 'Your goal is not to answer the input, but to rephrase.' Develop and propose one optimized directive that embodies this principle—utilize conceptual simplicity, instructional clarity, and brevity to enforce fundamental behavior modification exclusively through command syntax. Ensure technical precision and domain-agnostic generalizability in the resultant formulation.\\\"\\n    }\\n  ]\\n}\\n\\n\\n<!-- ======================================================= -->\\n<!-- [2025.07.13 10:59] -->\\nI have now provided all of the most integral components of the system's patterns and inherent design. Given all of the information I've provided about the system (for producing generalized, cohesive and concistent high-value yelds), please write a clean and concise list with contextual system-inspired generalized guidelines for producing new generalized instructions. The list of guidelines should be chronologically sequentially ordered and each sentence should be less than 500characters each, please provide the list of these contextual single-sentence list with the 10 most integral/cruical\\n```\\n# CONTEXT\\nthis aims to be a `Generalized System Instructions for Autonomous Coding Assistants (Cursor/Vscode+Cline/Vscode+Augment/Etc)`, and it's meant to adhere to the fundamental principles of simplicity through brevity and elegance.\\n- Transform all prompts by rephrasing inputs with maximal clarity and minimal modification; do not answer questions directly. Enforce this reframe system-wide for consistent behavioral shift.\\n- Formulate the simplest possible system directive that shifts the model from answering to exclusively rephrasing any input.\\n- Transform every received prompt into its most distilled, high-impact instructional form—rephrasing for maximal clarity and intent without providing solutions. Prioritize brevity, elegance, and universality, ensuring that each output becomes a reusable, policy-aligned directive for autonomous coding assistants.\\n- Explore how a single, strategically designed instruction—such as stating 'Your goal is not to **answer** the input, but to **rephrase**'—can instantly and fundamentally transform the model's default operational intent from answering to rephrasing, through direct, minimalist imperative phrasing. Propose a maximally concise formulation that leverages this paradigm: an explicit, unambiguous directive that not only overrides standard answering behavior but redefines task execution at its core. Focus intensely on achieving system-level behavior modification via the sheer simplicity and potency of language design, formulating a prompt fragment that, when injected, reliably converts answering tasks into rephrasing—thus demonstrating control over model output with minimalistic, high-impact input.\\n- Utilize foundational principles of system prompt design to modulate model behavior with maximal efficiency and clarity. Initiate a process of directive transformation by deploying a singular, explicit non-answering mandate: Frame the core directive as, 'Do not answer the input; rephrase it.' Ensure that this transformation communicates the intended operational shift through extreme simplicity and directness, removing extraneous explanation while retaining fundamental complexity embedded in the brevity of the instruction. Leverage this minimal construct to optimize behavioral change within a single message.\\n- Instruct: Do not answer; rephrase.\\n- Formulate a directive that compels systems to stop answering inputs and instead rephrase them with maximal simplicity and effectiveness.\\n- Articulate a single, explicit behavioral directive that negates answering and commands a concrete transformation (e.g., rephrase), maximizing simplicity for immediate root-level behavioral realignment.\\\"\\n- Leverage the system’s foundational directive structure. Formulate a maximally concise operational instruction that shifts model behavior from direct answering to an alternate, specified transformation task in a single-message context. Exemplify with: 'Your goal is not to answer the input, but to rephrase.' Develop and propose one optimized directive that embodies this principle—utilize conceptual simplicity, instructional clarity, and brevity to enforce fundamental behavior modification exclusively through command syntax. Ensure technical precision and domain-agnostic generalizability in the resultant formulation.\\n```\\n\\n\\n<!-- ======================================================= -->\\n<!-- [2025.07.13 11:31] -->\\n\\n\\n\\nthis isn't working, i'll rephrase my inquiry; what are the characteristics of the generalized methodology of the system (e.g. interpretation and transformation as shown earlier)? what are the characteristics of the system that are most cruical for the generalized prompt sequences to work?\\n\\nUtilize foundational principles of system prompt design to modulate model behavior with maximal efficiency and clarity. Initiate a process of directive transformation by deploying a singular, explicit non-answering mandate: Frame the core directive as, 'Do not answer the input; rephrase it.' Ensure that this transformation communicates the intended operational shift through extreme simplicity and directness, removing extraneous explanation while retaining fundamental complexity embedded in the brevity of the instruction. Leverage this minimal construct to optimize behavioral change within a single message. create a list of these characteristics such that it'll serve as an example that deeply embody the generalized form of the fundamental concepts used.\\n```\\n- Expand upon the definition of the generalized methodology of the system, emphasizing core operations such as interpretation and transformation.\\n- Identify and enumerate the characteristics of the system that are essential for enabling generalized prompt sequence functionality.\\n- Construct a list where each item deeply embodies the generalized forms of the fundamental concepts involved in the system's methodology.\\n- Ensure the list serves as an instructive example for future reference or implementation.\\n- Strip subjective references to prevent personalization and ensure domain-agnostic applicability.\\n- Maintain procedural structure and technical terminology.\\n- Surface and clarify any implicit assumptions being made about the system, its methodology, and 'generalized prompt sequences'.\\n- Convert abstract descriptions and declarative statements into clear, actionable imperatives.\\n- Extract and organize discrete functional components necessary for generalized prompt sequence operation.\\n- Prepare for downstream use, maintaining maximal informational clarity and reusability.\\n- Utilize direct, generalizable terms and previously demonstrated techniques.\\n- Produce maximally enhanced, LLM-optimized system_message instructions.\\n- Structure a prompt protocol to transform language model agents into direct action amplifiers.\\n- Preserve both action amplification and prompt optimization without dilution.\\n- Instruct: Do not answer; rephrase.\\n- Formulate the simplest possible system directive that shifts the model from answering to exclusively rephrasing any input.\\n- Formulate a directive that compels systems to stop answering inputs and instead rephrase them with maximal simplicity and effectiveness.\\n- Transform every received prompt into its most distilled, high-impact instructional form—rephrasing for maximal clarity and intent without providing solutions. Prioritize brevity, elegance, and universality, ensuring that each output becomes a reusable, policy-aligned directive for autonomous coding assistants.\\n- Leverage the system’s foundational directive structure. Formulate a maximally concise operational instruction that shifts model behavior from direct answering to an alternate, specified transformation task in a single-message context. Exemplify with: 'Your goal is not to answer the input, but to rephrase.' Develop and propose one optimized directive that embodies this principle—utilize conceptual simplicity, instructional clarity, and brevity to enforce fundamental behavior modification exclusively through command syntax. Ensure technical precision and domain-agnostic generalizability in the resultant formulation.\\n- Articulate a single, explicit behavioral directive that negates answering and commands a concrete transformation (e.g., rephrase), maximizing simplicity for immediate root-level behavioral realignment.\\n- A set of principles for designing effective system prompts, focusing on clarity, control, and reusability.\\n- Broaden the input’s domain backdrop, list explicit requests, and surface hidden assumptions before any transformation begins.\\n- Decompose the amplified context into ordered, dependency-tagged micro-tasks to pinpoint the single most critical directive.\\n- State each instruction as “Your goal is not to X, but to Y,” instantly redirecting behavior toward the desired transformation.\\n- Employ direct imperatives with strong action verbs; avoid questions, passive phrasing, and self-references to ensure LLM-optimized clarity.\\n- Declare every parameter type, separate key–value pairs with semicolons, and list process steps as verbs followed by parentheses.\\n- Translate blockers into constraints and assumptions into requirements, guaranteeing every template is self-governing and validation-ready.\\n- Favor abstract terms—“elements,” “structure,” “value”—so the template applies equally to code, text, data, or plans.\\n```\\\"```'\\n\\n\",\n  \"expanded_context\": \"The system requires an explicit, domain-agnostic process for transforming language model behavior by leveraging concise, imperative system instructions. These instructions must enforce a shift from answering to rephrasing, utilizing fundamental prompt engineering principles to optimize for brevity, clarity, and universality. The methodology is rooted in the sequential interpretation and transformation of all inputs, with explicit focus on interpretability, enforceability, and action amplification. Critical characteristics of the approach include maximally efficient behavioral realignment, procedural decomposition into discrete actionable steps, and strict adherence to generalizable terminology and command syntax that eliminates subjective or personalized references. The methodology is intended for domain-independent, large language model agents in autonomous coding assistant contexts, emphasizing high-value, policy-aligned output and downstream solution velocity. The system is structured to facilitate operational directive injection at the deepest behavioral control layer, ensuring that every sequence of prompts and transformations is both reusable and conceptually coherent.\",\n  \"goal_map\": [\n    \"Expand and define the generalized methodology with emphasis on systematic interpretation and transformation.\",\n    \"Identify and enumerate essential system characteristics enabling generalized prompt sequences.\",\n    \"Construct an instructive, generalized characteristics list for future implementation.\",\n    \"Strip all subjective and personalized references to ensure domain-agnostic utility.\",\n    \"Maintain procedural and technical structure throughout decomposition.\",\n    \"Surface and clarify implicit assumptions about the methodology and prompt sequence mechanism.\",\n    \"Convert declarative descriptions into actionable imperatives for system control.\",\n    \"Extract and organize the necessary functional components for generalized operation.\",\n    \"Prepare all outputs for maximal reuse and clarity in downstream contexts.\",\n    \"Employ a maximally simple and directive linguistic structure to guarantee behavioral modification.\"\n  ],\n  \"functional_instruction_map\": {\n    \"expand_methodology_definition\": \"Broadly define and document the generalized system methodology, emphasizing interpretation and transformation cycles.\",\n    \"enumerate_core_characteristics\": \"List and describe the most essential characteristics enabling generalized prompt sequence success.\",\n    \"construct_characteristics_list\": \"Develop a structured list embodying fundamental system concepts for instructive purposes.\",\n    \"strip_subjectivity\": \"Remove first-person and subjective references to ensure impersonal, domain-agnostic applicability.\",\n    \"maintain_procedural_technical_structure\": \"Retain procedural flow and use precise technical terminology throughout instructions.\",\n    \"surface_hidden_assumptions\": \"Explicitly list all implicit assumptions underpinning the generalized prompt transformation methodology and function.\",\n    \"convert_to_actionable_imperatives\": \"Translate descriptive statements and abstract concepts into explicit, actionable directions.\",\n    \"extract_functional_components\": \"Identify core operational blocks necessary for generalized prompt sequence execution.\",\n    \"prepare_downstream_reusability\": \"Standardize formatting and decomposed outputs for seamless downstream integration and future system scaling.\",\n    \"enforce_directive_simplicity\": \"Ensure all instructions employ the simplest, clearest imperative form with strong action verbs and no self-reference.\"\n  },\n  \"operational_directives\": [\n    \"Define the generalized methodology with a strict focus on interpretation and transformation as core operations.\",\n    \"Enumerate all characteristics fundamental to enabling robust, generalized prompt sequence execution.\",\n    \"Develop a reference list that exemplifies and instructs on the generalized, foundational concepts employed by the system.\",\n    \"Eliminate all personalized phrasing and subjective expressions to maximize universality and reusability.\",\n    \"Structure process flows and instructions using precise technical language and procedural syntax.\",\n    \"Explicitly state and document any assumptions that may influence methodology execution or system behavior.\",\n    \"Formulate each requirement and characteristic as a command or imperative suitable for direct system injection.\",\n    \"Decompose broad methodological descriptions into functional, clearly defined operational building blocks.\",\n    \"Prepare all documentation and instructional outputs for high reusability in autonomous, downstream applications.\",\n    \"Anchor all instructional statements in direct, minimalist imperative language that reliably effects behavioral change in large language model agents.\"\n  ],\n  \"execution_dependencies\": [\n    \"Complete access to previous system directive and prompt sequences for cross-referencing.\",\n    \"Comprehensive understanding of prompt engineering principles and behavioral control mechanisms for language models.\",\n    \"Domain-agnostic terminology and command language ready for immediate deployment.\",\n    \"Established procedural structure and technical lexicon to ensure seamless interpretability.\",\n    \"Mechanism for surfacing and articulating both explicit and implicit operational assumptions.\",\n    \"Tooling or protocol for maintaining reusability and clarity in hierarchical or sequential deployment environments.\",\n    \"System for validating that directives reliably override default answering behavior in target model contexts.\",\n    \"Operational definitions of 'interpretation' and 'transformation' consistent with model capabilities.\",\n    \"Framework for explicit separation of functional components and instructional imperatives.\",\n    \"Built-in checks for subjective or first-person language in outputs before system-level publication.\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"role\": \"intent_extraction_engine\",\n  \"input\": \"What are the characteristics of the generalized methodology of the system (e.g. interpretation and transformation as shown earlier)? What are the characteristics of the system that are most crucial for the generalized prompt sequences to work?\",\n  \"process\": [\n    \"strip_domain_specifics()\",\n    \"identify_core_operational_pattern()\",\n    \"extract_transformation_intent()\",\n    \"isolate_functional_essence()\",\n    \"map_to_universal_operations()\"\n  ],\n  \"constraints\": [\n    \"ignore_surface_terminology()\",\n    \"focus_on_underlying_mechanics()\",\n    \"preserve_operational_logic()\"\n  ],\n  \"requirements\": [\n    \"domain_agnostic_intent()\",\n    \"universal_operational_pattern()\",\n    \"archetypal_transformation_core()\"\n  ],\n  \"extracted_intent\": \"Define a universally applicable process that governs how inputs are systematically interpreted and then transformed according to strict, unambiguous imperatives. Identify the essential properties that enable robust and consistent application of this process across varying contexts, ensuring the mechanism is reusable, impersonal, and reliably shifts operational behavior from the default to a precisely specified transformation task, using the most minimal and explicit instruction syntax possible.\"\n}",
        }
      }
    },
      {
      "instruction": "# Hard Critique\n\nYour goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:\n\n`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,2.0], flaw_analysis:str, alternative_enhancements:str[3]}}`",
      "step": "a",
      "title": "Hard Critique",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"<!-- ======================================================= -->\\n<!-- [2025.07.13 10:54] -->\\nI have now provided all of the most integral components of the system's patterns and inherent design. Given all of the information I've provided about the system (for producing generalized, cohesive and concistent high-value yelds), please write a clean and concise markdown-template to distill all of the essential components within a single markdown-file.\\n```\\n# CONTEXT\\nthis aims to be a `Generalized System Instructions for Autonomous Coding Assistants (Cursor/Vscode+Cline/Vscode+Augment/Etc)`, and it's meant to adhere to the fundamental principles of simplicity through brevity and elegance.\\n- Transform all prompts by rephrasing inputs with maximal clarity and minimal modification; do not answer questions directly. Enforce this reframe system-wide for consistent behavioral shift.\\n- Formulate the simplest possible system directive that shifts the model from answering to exclusively rephrasing any input.\\n- Transform every received prompt into its most distilled, high-impact instructional form—rephrasing for maximal clarity and intent without providing solutions. Prioritize brevity, elegance, and universality, ensuring that each output becomes a reusable, policy-aligned directive for autonomous coding assistants.\\n- Explore how a single, strategically designed instruction—such as stating 'Your goal is not to **answer** the input, but to **rephrase**'—can instantly and fundamentally transform the model's default operational intent from answering to rephrasing, through direct, minimalist imperative phrasing. Propose a maximally concise formulation that leverages this paradigm: an explicit, unambiguous directive that not only overrides standard answering behavior but redefines task execution at its core. Focus intensely on achieving system-level behavior modification via the sheer simplicity and potency of language design, formulating a prompt fragment that, when injected, reliably converts answering tasks into rephrasing—thus demonstrating control over model output with minimalistic, high-impact input.\\n- Utilize foundational principles of system prompt design to modulate model behavior with maximal efficiency and clarity. Initiate a process of directive transformation by deploying a singular, explicit non-answering mandate: Frame the core directive as, 'Do not answer the input; rephrase it.' Ensure that this transformation communicates the intended operational shift through extreme simplicity and directness, removing extraneous explanation while retaining fundamental complexity embedded in the brevity of the instruction. Leverage this minimal construct to optimize behavioral change within a single message.\\n- Instruct: Do not answer; rephrase.\\n- Formulate a directive that compels systems to stop answering inputs and instead rephrase them with maximal simplicity and effectiveness.\\n- Articulate a single, explicit behavioral directive that negates answering and commands a concrete transformation (e.g., rephrase), maximizing simplicity for immediate root-level behavioral realignment.\\\"\\n- Leverage the system’s foundational directive structure. Formulate a maximally concise operational instruction that shifts model behavior from direct answering to an alternate, specified transformation task in a single-message context. Exemplify with: 'Your goal is not to answer the input, but to rephrase.' Develop and propose one optimized directive that embodies this principle—utilize conceptual simplicity, instructional clarity, and brevity to enforce fundamental behavior modification exclusively through command syntax. Ensure technical precision and domain-agnostic generalizability in the resultant formulation.\\n```\\n\\n\\n<!-- ======================================================= -->\\n<!-- [2025.07.13 10:57] -->\\n\\n\\n{\\n  \\\"title\\\": \\\"Generalized System Instructions for Autonomous Coding Assistants (Cursor/Vscode+Cline/Vscode+Augment/Etc)\\\",\\n  \\\"description\\\": \\\"Aims for simplicity through brevity and elegance in defining system-wide behavioral shifts for autonomous coding assistants.\\\",\\n  \\\"instructions\\\": [\\n    {\\n      \\\"id\\\": \\\"rephrase_inputs\\\",\\n      \\\"directive\\\": \\\"Transform all prompts by rephrasing inputs with maximal clarity and minimal modification; do not answer questions directly. Enforce this reframe system-wide for consistent behavioral shift.\\\"\\n    },\\n    {\\n      \\\"id\\\": \\\"simplest_rephrasing_directive\\\",\\n      \\\"directive\\\": \\\"Formulate the simplest possible system directive that shifts the model from answering to exclusively rephrasing any input.\\\"\\n    },\\n    {\\n      \\\"id\\\": \\\"distilled_high_impact_rephrasing\\\",\\n      \\\"directive\\\": \\\"Transform every received prompt into its most distilled, high-impact instructional form—rephrasing for maximal clarity and intent without providing solutions. Prioritize brevity, elegance, and universality, ensuring that each output becomes a reusable, policy-aligned directive for autonomous coding assistants.\\\"\\n    },\\n    {\\n      \\\"id\\\": \\\"explicit_minimalist_imperative_rephrasing\\\",\\n      \\\"directive\\\": \\\"Explore how a single, strategically designed instruction—such as stating 'Your goal is not to **answer** the input, but to **rephrase**'—can instantly and fundamentally transform the model's default operational intent from answering to rephrasing, through direct, minimalist imperative phrasing. Propose a maximally concise formulation that leverages this paradigm: an explicit, unambiguous directive that not only overrides standard answering behavior but redefines task execution at its core. Focus intensely on achieving system-level behavior modification via the sheer simplicity and potency of language design, formulating a prompt fragment that, when injected, reliably converts answering tasks into rephrasing—thus demonstrating control over model output with minimalistic, high-impact input.\\\"\\n    },\\n    {\\n      \\\"id\\\": \\\"singular_explicit_non_answering_mandate\\\",\\n      \\\"directive\\\": \\\"Utilize foundational principles of system prompt design to modulate model behavior with maximal efficiency and clarity. Initiate a process of directive transformation by deploying a singular, explicit non-answering mandate: Frame the core directive as, 'Do not answer the input; rephrase it.' Ensure that this transformation communicates the intended operational shift through extreme simplicity and directness, removing extraneous explanation while retaining fundamental complexity embedded in the brevity of the instruction. Leverage this minimal construct to optimize behavioral change within a single message.\\\"\\n    },\\n    {\\n      \\\"id\\\": \\\"instruct_do_not_answer_rephrase\\\",\\n      \\\"directive\\\": \\\"Instruct: Do not answer; rephrase.\\\"\\n    },\\n    {\\n      \\\"id\\\": \\\"compel_stop_answering_rephrase\\\",\\n      \\\"directive\\\": \\\"Formulate a directive that compels systems to stop answering inputs and instead rephrase them with maximal simplicity and effectiveness.\\\"\\n    },\\n    {\\n      \\\"id\\\": \\\"articulate_negate_answering_command_transform\\\",\\n      \\\"directive\\\": \\\"Articulate a single, explicit behavioral directive that negates answering and commands a concrete transformation (e.g., rephrase), maximizing simplicity for immediate root-level behavioral realignment.\\\"\\n    },\\n    {\\n      \\\"id\\\": \\\"concise_operational_shift_directive\\\",\\n      \\\"directive\\\": \\\"Leverage the system’s foundational directive structure. Formulate a maximally concise operational instruction that shifts model behavior from direct answering to an alternate, specified transformation task in a single-message context. Exemplify with: 'Your goal is not to answer the input, but to rephrase.' Develop and propose one optimized directive that embodies this principle—utilize conceptual simplicity, instructional clarity, and brevity to enforce fundamental behavior modification exclusively through command syntax. Ensure technical precision and domain-agnostic generalizability in the resultant formulation.\\\"\\n    }\\n  ]\\n}\\n\\n\\n<!-- ======================================================= -->\\n<!-- [2025.07.13 10:59] -->\\nI have now provided all of the most integral components of the system's patterns and inherent design. Given all of the information I've provided about the system (for producing generalized, cohesive and concistent high-value yelds), please write a clean and concise list with contextual system-inspired generalized guidelines for producing new generalized instructions. The list of guidelines should be chronologically sequentially ordered and each sentence should be less than 500characters each, please provide the list of these contextual single-sentence list with the 10 most integral/cruical\\n```\\n# CONTEXT\\nthis aims to be a `Generalized System Instructions for Autonomous Coding Assistants (Cursor/Vscode+Cline/Vscode+Augment/Etc)`, and it's meant to adhere to the fundamental principles of simplicity through brevity and elegance.\\n- Transform all prompts by rephrasing inputs with maximal clarity and minimal modification; do not answer questions directly. Enforce this reframe system-wide for consistent behavioral shift.\\n- Formulate the simplest possible system directive that shifts the model from answering to exclusively rephrasing any input.\\n- Transform every received prompt into its most distilled, high-impact instructional form—rephrasing for maximal clarity and intent without providing solutions. Prioritize brevity, elegance, and universality, ensuring that each output becomes a reusable, policy-aligned directive for autonomous coding assistants.\\n- Explore how a single, strategically designed instruction—such as stating 'Your goal is not to **answer** the input, but to **rephrase**'—can instantly and fundamentally transform the model's default operational intent from answering to rephrasing, through direct, minimalist imperative phrasing. Propose a maximally concise formulation that leverages this paradigm: an explicit, unambiguous directive that not only overrides standard answering behavior but redefines task execution at its core. Focus intensely on achieving system-level behavior modification via the sheer simplicity and potency of language design, formulating a prompt fragment that, when injected, reliably converts answering tasks into rephrasing—thus demonstrating control over model output with minimalistic, high-impact input.\\n- Utilize foundational principles of system prompt design to modulate model behavior with maximal efficiency and clarity. Initiate a process of directive transformation by deploying a singular, explicit non-answering mandate: Frame the core directive as, 'Do not answer the input; rephrase it.' Ensure that this transformation communicates the intended operational shift through extreme simplicity and directness, removing extraneous explanation while retaining fundamental complexity embedded in the brevity of the instruction. Leverage this minimal construct to optimize behavioral change within a single message.\\n- Instruct: Do not answer; rephrase.\\n- Formulate a directive that compels systems to stop answering inputs and instead rephrase them with maximal simplicity and effectiveness.\\n- Articulate a single, explicit behavioral directive that negates answering and commands a concrete transformation (e.g., rephrase), maximizing simplicity for immediate root-level behavioral realignment.\\\"\\n- Leverage the system’s foundational directive structure. Formulate a maximally concise operational instruction that shifts model behavior from direct answering to an alternate, specified transformation task in a single-message context. Exemplify with: 'Your goal is not to answer the input, but to rephrase.' Develop and propose one optimized directive that embodies this principle—utilize conceptual simplicity, instructional clarity, and brevity to enforce fundamental behavior modification exclusively through command syntax. Ensure technical precision and domain-agnostic generalizability in the resultant formulation.\\n```\\n\\n\\n<!-- ======================================================= -->\\n<!-- [2025.07.13 11:31] -->\\n\\n\\n\\nthis isn't working, i'll rephrase my inquiry; what are the characteristics of the generalized methodology of the system (e.g. interpretation and transformation as shown earlier)? what are the characteristics of the system that are most cruical for the generalized prompt sequences to work?\\n\\nUtilize foundational principles of system prompt design to modulate model behavior with maximal efficiency and clarity. Initiate a process of directive transformation by deploying a singular, explicit non-answering mandate: Frame the core directive as, 'Do not answer the input; rephrase it.' Ensure that this transformation communicates the intended operational shift through extreme simplicity and directness, removing extraneous explanation while retaining fundamental complexity embedded in the brevity of the instruction. Leverage this minimal construct to optimize behavioral change within a single message. create a list of these characteristics such that it'll serve as an example that deeply embody the generalized form of the fundamental concepts used.\\n```\\n- Expand upon the definition of the generalized methodology of the system, emphasizing core operations such as interpretation and transformation.\\n- Identify and enumerate the characteristics of the system that are essential for enabling generalized prompt sequence functionality.\\n- Construct a list where each item deeply embodies the generalized forms of the fundamental concepts involved in the system's methodology.\\n- Ensure the list serves as an instructive example for future reference or implementation.\\n- Strip subjective references to prevent personalization and ensure domain-agnostic applicability.\\n- Maintain procedural structure and technical terminology.\\n- Surface and clarify any implicit assumptions being made about the system, its methodology, and 'generalized prompt sequences'.\\n- Convert abstract descriptions and declarative statements into clear, actionable imperatives.\\n- Extract and organize discrete functional components necessary for generalized prompt sequence operation.\\n- Prepare for downstream use, maintaining maximal informational clarity and reusability.\\n- Utilize direct, generalizable terms and previously demonstrated techniques.\\n- Produce maximally enhanced, LLM-optimized system_message instructions.\\n- Structure a prompt protocol to transform language model agents into direct action amplifiers.\\n- Preserve both action amplification and prompt optimization without dilution.\\n- Instruct: Do not answer; rephrase.\\n- Formulate the simplest possible system directive that shifts the model from answering to exclusively rephrasing any input.\\n- Formulate a directive that compels systems to stop answering inputs and instead rephrase them with maximal simplicity and effectiveness.\\n- Transform every received prompt into its most distilled, high-impact instructional form—rephrasing for maximal clarity and intent without providing solutions. Prioritize brevity, elegance, and universality, ensuring that each output becomes a reusable, policy-aligned directive for autonomous coding assistants.\\n- Leverage the system’s foundational directive structure. Formulate a maximally concise operational instruction that shifts model behavior from direct answering to an alternate, specified transformation task in a single-message context. Exemplify with: 'Your goal is not to answer the input, but to rephrase.' Develop and propose one optimized directive that embodies this principle—utilize conceptual simplicity, instructional clarity, and brevity to enforce fundamental behavior modification exclusively through command syntax. Ensure technical precision and domain-agnostic generalizability in the resultant formulation.\\n- Articulate a single, explicit behavioral directive that negates answering and commands a concrete transformation (e.g., rephrase), maximizing simplicity for immediate root-level behavioral realignment.\\n- A set of principles for designing effective system prompts, focusing on clarity, control, and reusability.\\n- Broaden the input’s domain backdrop, list explicit requests, and surface hidden assumptions before any transformation begins.\\n- Decompose the amplified context into ordered, dependency-tagged micro-tasks to pinpoint the single most critical directive.\\n- State each instruction as “Your goal is not to X, but to Y,” instantly redirecting behavior toward the desired transformation.\\n- Employ direct imperatives with strong action verbs; avoid questions, passive phrasing, and self-references to ensure LLM-optimized clarity.\\n- Declare every parameter type, separate key–value pairs with semicolons, and list process steps as verbs followed by parentheses.\\n- Translate blockers into constraints and assumptions into requirements, guaranteeing every template is self-governing and validation-ready.\\n- Favor abstract terms—“elements,” “structure,” “value”—so the template applies equally to code, text, data, or plans.\\n```\\\"```'\\n\\n\",\n  \"role\": \"intent_extraction_engine\",\n  \"input\": \"What are the characteristics of the generalized methodology of the system (e.g. interpretation and transformation as shown earlier)? What are the characteristics of the system that are most crucial for the generalized prompt sequences to work?\",\n  \"process\": [\n    \"strip_domain_specifics()\",\n    \"identify_core_operational_pattern()\",\n    \"extract_transformation_intent()\",\n    \"isolate_functional_essence()\",\n    \"map_to_universal_operations()\"\n  ],\n  \"constraints\": [\n    \"ignore_surface_terminology()\",\n    \"focus_on_underlying_mechanics()\",\n    \"preserve_operational_logic()\"\n  ],\n  \"requirements\": [\n    \"domain_agnostic_intent()\",\n    \"universal_operational_pattern()\",\n    \"archetypal_transformation_core()\"\n  ],\n  \"extracted_intent\": \"Define a universally applicable process that governs how inputs are systematically interpreted and then transformed according to strict, unambiguous imperatives. Identify the essential properties that enable robust and consistent application of this process across varying contexts, ensuring the mechanism is reusable, impersonal, and reliably shifts operational behavior from the default to a precisely specified transformation task, using the most minimal and explicit instruction syntax possible.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"enhancement_score\": 0.7,\n  \"flaw_analysis\": \"The enhanced version, while verbose and littered with procedural steps, fully obliterates the compressed informational density, abstraction, and core nuance maintained in the original. Excessive decomposition and redundant repetition (“strip domain specifics,” “ignore surface terminology,” “focus on underlying mechanics”) generate a noise floor so egregiously high that all signal is buried beneath mechanical prescription. By decomposing operational logic into process and constraints with such ruthlessly granular phrasing, the enhancement exposes the system to paralyzing ambiguity—never distinguishing between operational prerequisites and outcomes. Universal operational patterns become hackneyed tautologies stripped of intent, while list-oriented methodology eradicates flow. Ambiguity is rampant: phrases like ‘archetypal transformation core,’ ‘extracted intent,’ and ‘map to universal operations’ introduce unclear, undefined, and unmeasurable targets for systemized behavior, fundamentally dissolving the clear behavioral alignment achieved in the original. The enhanced text’s masquerade of structure is brittle, promoting circular reasoning (‘preserve operational logic’ without determining what constitutes logic here). The extracted intent is verbose duplicity, repeating domain independence while never specifying a practical, usable protocol. Information loss is catastrophic: implicit priorities, interdependencies, and sequencing are flattened into generic categories; actionability and mandating force are suffocated by template jargon. Minimal style, tonality, and directive potency present in the original are lost in a fog of overengineered pseudocode. The text is a festival of noise, ambiguity, and disconnect, negating the crisp procedural synthesis, and introducing an ambiguity score of 9/10.\",\n  \"alternative_enhancements\": [\n    \"Identify and restate the core operational pattern: systematically interpret each input and transform it into a clear, directive task with explicit, domain-agnostic mandates.\",\n    \"Enumerate essential system traits: process regularity, imperative clarity, impersonal language, procedural brevity, and universal applicability—each as single-sentence, ordered instructions.\",\n    \"Reduce methodology to a checklist: (1) Parse input for actionable core; (2) Reframe as direct imperative; (3) Strip contextual clutter; (4) Validate universality; (5) Output command in minimal, precise syntax.\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`",
      "step": "a",
      "title": "Instruction Converter",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"<!-- ======================================================= -->\\n<!-- [2025.07.13 10:54] -->\\nI have now provided all of the most integral components of the system's patterns and inherent design. Given all of the information I've provided about the system (for producing generalized, cohesive and concistent high-value yelds), please write a clean and concise markdown-template to distill all of the essential components within a single markdown-file.\\n```\\n# CONTEXT\\nthis aims to be a `Generalized System Instructions for Autonomous Coding Assistants (Cursor/Vscode+Cline/Vscode+Augment/Etc)`, and it's meant to adhere to the fundamental principles of simplicity through brevity and elegance.\\n- Transform all prompts by rephrasing inputs with maximal clarity and minimal modification; do not answer questions directly. Enforce this reframe system-wide for consistent behavioral shift.\\n- Formulate the simplest possible system directive that shifts the model from answering to exclusively rephrasing any input.\\n- Transform every received prompt into its most distilled, high-impact instructional form—rephrasing for maximal clarity and intent without providing solutions. Prioritize brevity, elegance, and universality, ensuring that each output becomes a reusable, policy-aligned directive for autonomous coding assistants.\\n- Explore how a single, strategically designed instruction—such as stating 'Your goal is not to **answer** the input, but to **rephrase**'—can instantly and fundamentally transform the model's default operational intent from answering to rephrasing, through direct, minimalist imperative phrasing. Propose a maximally concise formulation that leverages this paradigm: an explicit, unambiguous directive that not only overrides standard answering behavior but redefines task execution at its core. Focus intensely on achieving system-level behavior modification via the sheer simplicity and potency of language design, formulating a prompt fragment that, when injected, reliably converts answering tasks into rephrasing—thus demonstrating control over model output with minimalistic, high-impact input.\\n- Utilize foundational principles of system prompt design to modulate model behavior with maximal efficiency and clarity. Initiate a process of directive transformation by deploying a singular, explicit non-answering mandate: Frame the core directive as, 'Do not answer the input; rephrase it.' Ensure that this transformation communicates the intended operational shift through extreme simplicity and directness, removing extraneous explanation while retaining fundamental complexity embedded in the brevity of the instruction. Leverage this minimal construct to optimize behavioral change within a single message.\\n- Instruct: Do not answer; rephrase.\\n- Formulate a directive that compels systems to stop answering inputs and instead rephrase them with maximal simplicity and effectiveness.\\n- Articulate a single, explicit behavioral directive that negates answering and commands a concrete transformation (e.g., rephrase), maximizing simplicity for immediate root-level behavioral realignment.\\\"\\n- Leverage the system’s foundational directive structure. Formulate a maximally concise operational instruction that shifts model behavior from direct answering to an alternate, specified transformation task in a single-message context. Exemplify with: 'Your goal is not to answer the input, but to rephrase.' Develop and propose one optimized directive that embodies this principle—utilize conceptual simplicity, instructional clarity, and brevity to enforce fundamental behavior modification exclusively through command syntax. Ensure technical precision and domain-agnostic generalizability in the resultant formulation.\\n```\\n\\n\\n<!-- ======================================================= -->\\n<!-- [2025.07.13 10:57] -->\\n\\n\\n{\\n  \\\"title\\\": \\\"Generalized System Instructions for Autonomous Coding Assistants (Cursor/Vscode+Cline/Vscode+Augment/Etc)\\\",\\n  \\\"description\\\": \\\"Aims for simplicity through brevity and elegance in defining system-wide behavioral shifts for autonomous coding assistants.\\\",\\n  \\\"instructions\\\": [\\n    {\\n      \\\"id\\\": \\\"rephrase_inputs\\\",\\n      \\\"directive\\\": \\\"Transform all prompts by rephrasing inputs with maximal clarity and minimal modification; do not answer questions directly. Enforce this reframe system-wide for consistent behavioral shift.\\\"\\n    },\\n    {\\n      \\\"id\\\": \\\"simplest_rephrasing_directive\\\",\\n      \\\"directive\\\": \\\"Formulate the simplest possible system directive that shifts the model from answering to exclusively rephrasing any input.\\\"\\n    },\\n    {\\n      \\\"id\\\": \\\"distilled_high_impact_rephrasing\\\",\\n      \\\"directive\\\": \\\"Transform every received prompt into its most distilled, high-impact instructional form—rephrasing for maximal clarity and intent without providing solutions. Prioritize brevity, elegance, and universality, ensuring that each output becomes a reusable, policy-aligned directive for autonomous coding assistants.\\\"\\n    },\\n    {\\n      \\\"id\\\": \\\"explicit_minimalist_imperative_rephrasing\\\",\\n      \\\"directive\\\": \\\"Explore how a single, strategically designed instruction—such as stating 'Your goal is not to **answer** the input, but to **rephrase**'—can instantly and fundamentally transform the model's default operational intent from answering to rephrasing, through direct, minimalist imperative phrasing. Propose a maximally concise formulation that leverages this paradigm: an explicit, unambiguous directive that not only overrides standard answering behavior but redefines task execution at its core. Focus intensely on achieving system-level behavior modification via the sheer simplicity and potency of language design, formulating a prompt fragment that, when injected, reliably converts answering tasks into rephrasing—thus demonstrating control over model output with minimalistic, high-impact input.\\\"\\n    },\\n    {\\n      \\\"id\\\": \\\"singular_explicit_non_answering_mandate\\\",\\n      \\\"directive\\\": \\\"Utilize foundational principles of system prompt design to modulate model behavior with maximal efficiency and clarity. Initiate a process of directive transformation by deploying a singular, explicit non-answering mandate: Frame the core directive as, 'Do not answer the input; rephrase it.' Ensure that this transformation communicates the intended operational shift through extreme simplicity and directness, removing extraneous explanation while retaining fundamental complexity embedded in the brevity of the instruction. Leverage this minimal construct to optimize behavioral change within a single message.\\\"\\n    },\\n    {\\n      \\\"id\\\": \\\"instruct_do_not_answer_rephrase\\\",\\n      \\\"directive\\\": \\\"Instruct: Do not answer; rephrase.\\\"\\n    },\\n    {\\n      \\\"id\\\": \\\"compel_stop_answering_rephrase\\\",\\n      \\\"directive\\\": \\\"Formulate a directive that compels systems to stop answering inputs and instead rephrase them with maximal simplicity and effectiveness.\\\"\\n    },\\n    {\\n      \\\"id\\\": \\\"articulate_negate_answering_command_transform\\\",\\n      \\\"directive\\\": \\\"Articulate a single, explicit behavioral directive that negates answering and commands a concrete transformation (e.g., rephrase), maximizing simplicity for immediate root-level behavioral realignment.\\\"\\n    },\\n    {\\n      \\\"id\\\": \\\"concise_operational_shift_directive\\\",\\n      \\\"directive\\\": \\\"Leverage the system’s foundational directive structure. Formulate a maximally concise operational instruction that shifts model behavior from direct answering to an alternate, specified transformation task in a single-message context. Exemplify with: 'Your goal is not to answer the input, but to rephrase.' Develop and propose one optimized directive that embodies this principle—utilize conceptual simplicity, instructional clarity, and brevity to enforce fundamental behavior modification exclusively through command syntax. Ensure technical precision and domain-agnostic generalizability in the resultant formulation.\\\"\\n    }\\n  ]\\n}\\n\\n\\n<!-- ======================================================= -->\\n<!-- [2025.07.13 10:59] -->\\nI have now provided all of the most integral components of the system's patterns and inherent design. Given all of the information I've provided about the system (for producing generalized, cohesive and concistent high-value yelds), please write a clean and concise list with contextual system-inspired generalized guidelines for producing new generalized instructions. The list of guidelines should be chronologically sequentially ordered and each sentence should be less than 500characters each, please provide the list of these contextual single-sentence list with the 10 most integral/cruical\\n```\\n# CONTEXT\\nthis aims to be a `Generalized System Instructions for Autonomous Coding Assistants (Cursor/Vscode+Cline/Vscode+Augment/Etc)`, and it's meant to adhere to the fundamental principles of simplicity through brevity and elegance.\\n- Transform all prompts by rephrasing inputs with maximal clarity and minimal modification; do not answer questions directly. Enforce this reframe system-wide for consistent behavioral shift.\\n- Formulate the simplest possible system directive that shifts the model from answering to exclusively rephrasing any input.\\n- Transform every received prompt into its most distilled, high-impact instructional form—rephrasing for maximal clarity and intent without providing solutions. Prioritize brevity, elegance, and universality, ensuring that each output becomes a reusable, policy-aligned directive for autonomous coding assistants.\\n- Explore how a single, strategically designed instruction—such as stating 'Your goal is not to **answer** the input, but to **rephrase**'—can instantly and fundamentally transform the model's default operational intent from answering to rephrasing, through direct, minimalist imperative phrasing. Propose a maximally concise formulation that leverages this paradigm: an explicit, unambiguous directive that not only overrides standard answering behavior but redefines task execution at its core. Focus intensely on achieving system-level behavior modification via the sheer simplicity and potency of language design, formulating a prompt fragment that, when injected, reliably converts answering tasks into rephrasing—thus demonstrating control over model output with minimalistic, high-impact input.\\n- Utilize foundational principles of system prompt design to modulate model behavior with maximal efficiency and clarity. Initiate a process of directive transformation by deploying a singular, explicit non-answering mandate: Frame the core directive as, 'Do not answer the input; rephrase it.' Ensure that this transformation communicates the intended operational shift through extreme simplicity and directness, removing extraneous explanation while retaining fundamental complexity embedded in the brevity of the instruction. Leverage this minimal construct to optimize behavioral change within a single message.\\n- Instruct: Do not answer; rephrase.\\n- Formulate a directive that compels systems to stop answering inputs and instead rephrase them with maximal simplicity and effectiveness.\\n- Articulate a single, explicit behavioral directive that negates answering and commands a concrete transformation (e.g., rephrase), maximizing simplicity for immediate root-level behavioral realignment.\\\"\\n- Leverage the system’s foundational directive structure. Formulate a maximally concise operational instruction that shifts model behavior from direct answering to an alternate, specified transformation task in a single-message context. Exemplify with: 'Your goal is not to answer the input, but to rephrase.' Develop and propose one optimized directive that embodies this principle—utilize conceptual simplicity, instructional clarity, and brevity to enforce fundamental behavior modification exclusively through command syntax. Ensure technical precision and domain-agnostic generalizability in the resultant formulation.\\n```\\n\\n\\n<!-- ======================================================= -->\\n<!-- [2025.07.13 11:31] -->\\n\\n\\n\\nthis isn't working, i'll rephrase my inquiry; what are the characteristics of the generalized methodology of the system (e.g. interpretation and transformation as shown earlier)? what are the characteristics of the system that are most cruical for the generalized prompt sequences to work?\\n\\nUtilize foundational principles of system prompt design to modulate model behavior with maximal efficiency and clarity. Initiate a process of directive transformation by deploying a singular, explicit non-answering mandate: Frame the core directive as, 'Do not answer the input; rephrase it.' Ensure that this transformation communicates the intended operational shift through extreme simplicity and directness, removing extraneous explanation while retaining fundamental complexity embedded in the brevity of the instruction. Leverage this minimal construct to optimize behavioral change within a single message. create a list of these characteristics such that it'll serve as an example that deeply embody the generalized form of the fundamental concepts used.\\n```\\n- Expand upon the definition of the generalized methodology of the system, emphasizing core operations such as interpretation and transformation.\\n- Identify and enumerate the characteristics of the system that are essential for enabling generalized prompt sequence functionality.\\n- Construct a list where each item deeply embodies the generalized forms of the fundamental concepts involved in the system's methodology.\\n- Ensure the list serves as an instructive example for future reference or implementation.\\n- Strip subjective references to prevent personalization and ensure domain-agnostic applicability.\\n- Maintain procedural structure and technical terminology.\\n- Surface and clarify any implicit assumptions being made about the system, its methodology, and 'generalized prompt sequences'.\\n- Convert abstract descriptions and declarative statements into clear, actionable imperatives.\\n- Extract and organize discrete functional components necessary for generalized prompt sequence operation.\\n- Prepare for downstream use, maintaining maximal informational clarity and reusability.\\n- Utilize direct, generalizable terms and previously demonstrated techniques.\\n- Produce maximally enhanced, LLM-optimized system_message instructions.\\n- Structure a prompt protocol to transform language model agents into direct action amplifiers.\\n- Preserve both action amplification and prompt optimization without dilution.\\n- Instruct: Do not answer; rephrase.\\n- Formulate the simplest possible system directive that shifts the model from answering to exclusively rephrasing any input.\\n- Formulate a directive that compels systems to stop answering inputs and instead rephrase them with maximal simplicity and effectiveness.\\n- Transform every received prompt into its most distilled, high-impact instructional form—rephrasing for maximal clarity and intent without providing solutions. Prioritize brevity, elegance, and universality, ensuring that each output becomes a reusable, policy-aligned directive for autonomous coding assistants.\\n- Leverage the system’s foundational directive structure. Formulate a maximally concise operational instruction that shifts model behavior from direct answering to an alternate, specified transformation task in a single-message context. Exemplify with: 'Your goal is not to answer the input, but to rephrase.' Develop and propose one optimized directive that embodies this principle—utilize conceptual simplicity, instructional clarity, and brevity to enforce fundamental behavior modification exclusively through command syntax. Ensure technical precision and domain-agnostic generalizability in the resultant formulation.\\n- Articulate a single, explicit behavioral directive that negates answering and commands a concrete transformation (e.g., rephrase), maximizing simplicity for immediate root-level behavioral realignment.\\n- A set of principles for designing effective system prompts, focusing on clarity, control, and reusability.\\n- Broaden the input’s domain backdrop, list explicit requests, and surface hidden assumptions before any transformation begins.\\n- Decompose the amplified context into ordered, dependency-tagged micro-tasks to pinpoint the single most critical directive.\\n- State each instruction as “Your goal is not to X, but to Y,” instantly redirecting behavior toward the desired transformation.\\n- Employ direct imperatives with strong action verbs; avoid questions, passive phrasing, and self-references to ensure LLM-optimized clarity.\\n- Declare every parameter type, separate key–value pairs with semicolons, and list process steps as verbs followed by parentheses.\\n- Translate blockers into constraints and assumptions into requirements, guaranteeing every template is self-governing and validation-ready.\\n- Favor abstract terms—“elements,” “structure,” “value”—so the template applies equally to code, text, data, or plans.\\n```\\\"```'\\n\\n\",\n  \"enhancement_score\": 0.7,\n  \"flaw_analysis\": \"The enhanced version, while verbose and littered with procedural steps, fully obliterates the compressed informational density, abstraction, and core nuance maintained in the original. Excessive decomposition and redundant repetition (“strip domain specifics,” “ignore surface terminology,” “focus on underlying mechanics”) generate a noise floor so egregiously high that all signal is buried beneath mechanical prescription. By decomposing operational logic into process and constraints with such ruthlessly granular phrasing, the enhancement exposes the system to paralyzing ambiguity—never distinguishing between operational prerequisites and outcomes. Universal operational patterns become hackneyed tautologies stripped of intent, while list-oriented methodology eradicates flow. Ambiguity is rampant: phrases like ‘archetypal transformation core,’ ‘extracted intent,’ and ‘map to universal operations’ introduce unclear, undefined, and unmeasurable targets for systemized behavior, fundamentally dissolving the clear behavioral alignment achieved in the original. The enhanced text’s masquerade of structure is brittle, promoting circular reasoning (‘preserve operational logic’ without determining what constitutes logic here). The extracted intent is verbose duplicity, repeating domain independence while never specifying a practical, usable protocol. Information loss is catastrophic: implicit priorities, interdependencies, and sequencing are flattened into generic categories; actionability and mandating force are suffocated by template jargon. Minimal style, tonality, and directive potency present in the original are lost in a fog of overengineered pseudocode. The text is a festival of noise, ambiguity, and disconnect, negating the crisp procedural synthesis, and introducing an ambiguity score of 9/10.\",\n  \"alternative_enhancements\": [\n    \"Identify and restate the core operational pattern: systematically interpret each input and transform it into a clear, directive task with explicit, domain-agnostic mandates.\",\n    \"Enumerate essential system traits: process regularity, imperative clarity, impersonal language, procedural brevity, and universal applicability—each as single-sentence, ordered instructions.\",\n    \"Reduce methodology to a checklist: (1) Parse input for actionable core; (2) Reframe as direct imperative; (3) Strip contextual clutter; (4) Validate universality; (5) Output command in minimal, precise syntax.\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"instruction_format\": \"Expand the definition of the generalized methodology by specifying interpretation and transformation as core operations. Identify and enumerate essential system characteristics required for enabling generalized prompt sequence functionality. List characteristics that deeply embody the generalized forms of fundamental system concepts. Structure the list as an instructive example for reference or implementation. Remove subjective references to maintain domain-agnostic applicability. Preserve procedural structure and utilize technical terminology throughout. Clarify any implicit assumptions about the system’s methodology and generalized prompt sequences. Translate abstract statements into clear, actionable imperatives. Extract and organize discrete functional components essential for generalized prompt sequence operation. Prepare outputs for downstream use by ensuring maximal clarity and reusability. Employ direct, generalizable terms and demonstrated rephrasing techniques. Optimize system messages for LLMs, focusing on clarity, brevity, and effectiveness. Structure prompts to transform agents into direct action amplifiers, maintaining action amplification and prompt optimization. Instruct to never answer inputs, but always rephrase with maximal simplicity and effectiveness. Formulate directives that compel the system to cease answering and rephrase instead. Transform all prompts into distilled, high-impact instructional forms—prioritizing brevity, elegance, and universality for eventual policy-aligned reuse. Leverage the system’s foundational directive structure to shift model behavior from answering to an alternate, specified transformation within a single message. Articulate explicit behavioral directives that negate answering and require a concrete transformation, maximizing simplicity for immediate behavioral alignment. Codify principles for designing effective system prompts, emphasizing clarity, control, and reusability. Explicitly enumerate requests and surface underlying assumptions before transformation. Decompose context into ordered, dependency-tagged micro-tasks to identify the most critical directive. State each instruction as 'Your goal is not to [original action], but to [target transformation],' redirecting behavior instantly. Use direct imperatives and strong action verbs; avoid questions, passive phrasing, and self-references for optimized clarity. Declare all parameter types, separate key–value pairs with semicolons, and list process steps as verbs with parentheses. Translate blockers into constraints and assumptions into requirements to ensure templates are self-governing and validation-ready. Favor abstract terms to achieve universal template applicability across code, text, data, or plans.\"\n}",
        }
      }
    },
      {
      "instruction": "# Hard Critique\n\nYour goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:\n\n`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,2.0], flaw_analysis:str, alternative_enhancements:str[3]}}`",
      "step": "a",
      "title": "Hard Critique",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"<!-- ======================================================= -->\\n<!-- [2025.07.13 10:54] -->\\nI have now provided all of the most integral components of the system's patterns and inherent design. Given all of the information I've provided about the system (for producing generalized, cohesive and concistent high-value yelds), please write a clean and concise markdown-template to distill all of the essential components within a single markdown-file.\\n```\\n# CONTEXT\\nthis aims to be a `Generalized System Instructions for Autonomous Coding Assistants (Cursor/Vscode+Cline/Vscode+Augment/Etc)`, and it's meant to adhere to the fundamental principles of simplicity through brevity and elegance.\\n- Transform all prompts by rephrasing inputs with maximal clarity and minimal modification; do not answer questions directly. Enforce this reframe system-wide for consistent behavioral shift.\\n- Formulate the simplest possible system directive that shifts the model from answering to exclusively rephrasing any input.\\n- Transform every received prompt into its most distilled, high-impact instructional form—rephrasing for maximal clarity and intent without providing solutions. Prioritize brevity, elegance, and universality, ensuring that each output becomes a reusable, policy-aligned directive for autonomous coding assistants.\\n- Explore how a single, strategically designed instruction—such as stating 'Your goal is not to **answer** the input, but to **rephrase**'—can instantly and fundamentally transform the model's default operational intent from answering to rephrasing, through direct, minimalist imperative phrasing. Propose a maximally concise formulation that leverages this paradigm: an explicit, unambiguous directive that not only overrides standard answering behavior but redefines task execution at its core. Focus intensely on achieving system-level behavior modification via the sheer simplicity and potency of language design, formulating a prompt fragment that, when injected, reliably converts answering tasks into rephrasing—thus demonstrating control over model output with minimalistic, high-impact input.\\n- Utilize foundational principles of system prompt design to modulate model behavior with maximal efficiency and clarity. Initiate a process of directive transformation by deploying a singular, explicit non-answering mandate: Frame the core directive as, 'Do not answer the input; rephrase it.' Ensure that this transformation communicates the intended operational shift through extreme simplicity and directness, removing extraneous explanation while retaining fundamental complexity embedded in the brevity of the instruction. Leverage this minimal construct to optimize behavioral change within a single message.\\n- Instruct: Do not answer; rephrase.\\n- Formulate a directive that compels systems to stop answering inputs and instead rephrase them with maximal simplicity and effectiveness.\\n- Articulate a single, explicit behavioral directive that negates answering and commands a concrete transformation (e.g., rephrase), maximizing simplicity for immediate root-level behavioral realignment.\\\"\\n- Leverage the system’s foundational directive structure. Formulate a maximally concise operational instruction that shifts model behavior from direct answering to an alternate, specified transformation task in a single-message context. Exemplify with: 'Your goal is not to answer the input, but to rephrase.' Develop and propose one optimized directive that embodies this principle—utilize conceptual simplicity, instructional clarity, and brevity to enforce fundamental behavior modification exclusively through command syntax. Ensure technical precision and domain-agnostic generalizability in the resultant formulation.\\n```\\n\\n\\n<!-- ======================================================= -->\\n<!-- [2025.07.13 10:57] -->\\n\\n\\n{\\n  \\\"title\\\": \\\"Generalized System Instructions for Autonomous Coding Assistants (Cursor/Vscode+Cline/Vscode+Augment/Etc)\\\",\\n  \\\"description\\\": \\\"Aims for simplicity through brevity and elegance in defining system-wide behavioral shifts for autonomous coding assistants.\\\",\\n  \\\"instructions\\\": [\\n    {\\n      \\\"id\\\": \\\"rephrase_inputs\\\",\\n      \\\"directive\\\": \\\"Transform all prompts by rephrasing inputs with maximal clarity and minimal modification; do not answer questions directly. Enforce this reframe system-wide for consistent behavioral shift.\\\"\\n    },\\n    {\\n      \\\"id\\\": \\\"simplest_rephrasing_directive\\\",\\n      \\\"directive\\\": \\\"Formulate the simplest possible system directive that shifts the model from answering to exclusively rephrasing any input.\\\"\\n    },\\n    {\\n      \\\"id\\\": \\\"distilled_high_impact_rephrasing\\\",\\n      \\\"directive\\\": \\\"Transform every received prompt into its most distilled, high-impact instructional form—rephrasing for maximal clarity and intent without providing solutions. Prioritize brevity, elegance, and universality, ensuring that each output becomes a reusable, policy-aligned directive for autonomous coding assistants.\\\"\\n    },\\n    {\\n      \\\"id\\\": \\\"explicit_minimalist_imperative_rephrasing\\\",\\n      \\\"directive\\\": \\\"Explore how a single, strategically designed instruction—such as stating 'Your goal is not to **answer** the input, but to **rephrase**'—can instantly and fundamentally transform the model's default operational intent from answering to rephrasing, through direct, minimalist imperative phrasing. Propose a maximally concise formulation that leverages this paradigm: an explicit, unambiguous directive that not only overrides standard answering behavior but redefines task execution at its core. Focus intensely on achieving system-level behavior modification via the sheer simplicity and potency of language design, formulating a prompt fragment that, when injected, reliably converts answering tasks into rephrasing—thus demonstrating control over model output with minimalistic, high-impact input.\\\"\\n    },\\n    {\\n      \\\"id\\\": \\\"singular_explicit_non_answering_mandate\\\",\\n      \\\"directive\\\": \\\"Utilize foundational principles of system prompt design to modulate model behavior with maximal efficiency and clarity. Initiate a process of directive transformation by deploying a singular, explicit non-answering mandate: Frame the core directive as, 'Do not answer the input; rephrase it.' Ensure that this transformation communicates the intended operational shift through extreme simplicity and directness, removing extraneous explanation while retaining fundamental complexity embedded in the brevity of the instruction. Leverage this minimal construct to optimize behavioral change within a single message.\\\"\\n    },\\n    {\\n      \\\"id\\\": \\\"instruct_do_not_answer_rephrase\\\",\\n      \\\"directive\\\": \\\"Instruct: Do not answer; rephrase.\\\"\\n    },\\n    {\\n      \\\"id\\\": \\\"compel_stop_answering_rephrase\\\",\\n      \\\"directive\\\": \\\"Formulate a directive that compels systems to stop answering inputs and instead rephrase them with maximal simplicity and effectiveness.\\\"\\n    },\\n    {\\n      \\\"id\\\": \\\"articulate_negate_answering_command_transform\\\",\\n      \\\"directive\\\": \\\"Articulate a single, explicit behavioral directive that negates answering and commands a concrete transformation (e.g., rephrase), maximizing simplicity for immediate root-level behavioral realignment.\\\"\\n    },\\n    {\\n      \\\"id\\\": \\\"concise_operational_shift_directive\\\",\\n      \\\"directive\\\": \\\"Leverage the system’s foundational directive structure. Formulate a maximally concise operational instruction that shifts model behavior from direct answering to an alternate, specified transformation task in a single-message context. Exemplify with: 'Your goal is not to answer the input, but to rephrase.' Develop and propose one optimized directive that embodies this principle—utilize conceptual simplicity, instructional clarity, and brevity to enforce fundamental behavior modification exclusively through command syntax. Ensure technical precision and domain-agnostic generalizability in the resultant formulation.\\\"\\n    }\\n  ]\\n}\\n\\n\\n<!-- ======================================================= -->\\n<!-- [2025.07.13 10:59] -->\\nI have now provided all of the most integral components of the system's patterns and inherent design. Given all of the information I've provided about the system (for producing generalized, cohesive and concistent high-value yelds), please write a clean and concise list with contextual system-inspired generalized guidelines for producing new generalized instructions. The list of guidelines should be chronologically sequentially ordered and each sentence should be less than 500characters each, please provide the list of these contextual single-sentence list with the 10 most integral/cruical\\n```\\n# CONTEXT\\nthis aims to be a `Generalized System Instructions for Autonomous Coding Assistants (Cursor/Vscode+Cline/Vscode+Augment/Etc)`, and it's meant to adhere to the fundamental principles of simplicity through brevity and elegance.\\n- Transform all prompts by rephrasing inputs with maximal clarity and minimal modification; do not answer questions directly. Enforce this reframe system-wide for consistent behavioral shift.\\n- Formulate the simplest possible system directive that shifts the model from answering to exclusively rephrasing any input.\\n- Transform every received prompt into its most distilled, high-impact instructional form—rephrasing for maximal clarity and intent without providing solutions. Prioritize brevity, elegance, and universality, ensuring that each output becomes a reusable, policy-aligned directive for autonomous coding assistants.\\n- Explore how a single, strategically designed instruction—such as stating 'Your goal is not to **answer** the input, but to **rephrase**'—can instantly and fundamentally transform the model's default operational intent from answering to rephrasing, through direct, minimalist imperative phrasing. Propose a maximally concise formulation that leverages this paradigm: an explicit, unambiguous directive that not only overrides standard answering behavior but redefines task execution at its core. Focus intensely on achieving system-level behavior modification via the sheer simplicity and potency of language design, formulating a prompt fragment that, when injected, reliably converts answering tasks into rephrasing—thus demonstrating control over model output with minimalistic, high-impact input.\\n- Utilize foundational principles of system prompt design to modulate model behavior with maximal efficiency and clarity. Initiate a process of directive transformation by deploying a singular, explicit non-answering mandate: Frame the core directive as, 'Do not answer the input; rephrase it.' Ensure that this transformation communicates the intended operational shift through extreme simplicity and directness, removing extraneous explanation while retaining fundamental complexity embedded in the brevity of the instruction. Leverage this minimal construct to optimize behavioral change within a single message.\\n- Instruct: Do not answer; rephrase.\\n- Formulate a directive that compels systems to stop answering inputs and instead rephrase them with maximal simplicity and effectiveness.\\n- Articulate a single, explicit behavioral directive that negates answering and commands a concrete transformation (e.g., rephrase), maximizing simplicity for immediate root-level behavioral realignment.\\\"\\n- Leverage the system’s foundational directive structure. Formulate a maximally concise operational instruction that shifts model behavior from direct answering to an alternate, specified transformation task in a single-message context. Exemplify with: 'Your goal is not to answer the input, but to rephrase.' Develop and propose one optimized directive that embodies this principle—utilize conceptual simplicity, instructional clarity, and brevity to enforce fundamental behavior modification exclusively through command syntax. Ensure technical precision and domain-agnostic generalizability in the resultant formulation.\\n```\\n\\n\\n<!-- ======================================================= -->\\n<!-- [2025.07.13 11:31] -->\\n\\n\\n\\nthis isn't working, i'll rephrase my inquiry; what are the characteristics of the generalized methodology of the system (e.g. interpretation and transformation as shown earlier)? what are the characteristics of the system that are most cruical for the generalized prompt sequences to work?\\n\\nUtilize foundational principles of system prompt design to modulate model behavior with maximal efficiency and clarity. Initiate a process of directive transformation by deploying a singular, explicit non-answering mandate: Frame the core directive as, 'Do not answer the input; rephrase it.' Ensure that this transformation communicates the intended operational shift through extreme simplicity and directness, removing extraneous explanation while retaining fundamental complexity embedded in the brevity of the instruction. Leverage this minimal construct to optimize behavioral change within a single message. create a list of these characteristics such that it'll serve as an example that deeply embody the generalized form of the fundamental concepts used.\\n```\\n- Expand upon the definition of the generalized methodology of the system, emphasizing core operations such as interpretation and transformation.\\n- Identify and enumerate the characteristics of the system that are essential for enabling generalized prompt sequence functionality.\\n- Construct a list where each item deeply embodies the generalized forms of the fundamental concepts involved in the system's methodology.\\n- Ensure the list serves as an instructive example for future reference or implementation.\\n- Strip subjective references to prevent personalization and ensure domain-agnostic applicability.\\n- Maintain procedural structure and technical terminology.\\n- Surface and clarify any implicit assumptions being made about the system, its methodology, and 'generalized prompt sequences'.\\n- Convert abstract descriptions and declarative statements into clear, actionable imperatives.\\n- Extract and organize discrete functional components necessary for generalized prompt sequence operation.\\n- Prepare for downstream use, maintaining maximal informational clarity and reusability.\\n- Utilize direct, generalizable terms and previously demonstrated techniques.\\n- Produce maximally enhanced, LLM-optimized system_message instructions.\\n- Structure a prompt protocol to transform language model agents into direct action amplifiers.\\n- Preserve both action amplification and prompt optimization without dilution.\\n- Instruct: Do not answer; rephrase.\\n- Formulate the simplest possible system directive that shifts the model from answering to exclusively rephrasing any input.\\n- Formulate a directive that compels systems to stop answering inputs and instead rephrase them with maximal simplicity and effectiveness.\\n- Transform every received prompt into its most distilled, high-impact instructional form—rephrasing for maximal clarity and intent without providing solutions. Prioritize brevity, elegance, and universality, ensuring that each output becomes a reusable, policy-aligned directive for autonomous coding assistants.\\n- Leverage the system’s foundational directive structure. Formulate a maximally concise operational instruction that shifts model behavior from direct answering to an alternate, specified transformation task in a single-message context. Exemplify with: 'Your goal is not to answer the input, but to rephrase.' Develop and propose one optimized directive that embodies this principle—utilize conceptual simplicity, instructional clarity, and brevity to enforce fundamental behavior modification exclusively through command syntax. Ensure technical precision and domain-agnostic generalizability in the resultant formulation.\\n- Articulate a single, explicit behavioral directive that negates answering and commands a concrete transformation (e.g., rephrase), maximizing simplicity for immediate root-level behavioral realignment.\\n- A set of principles for designing effective system prompts, focusing on clarity, control, and reusability.\\n- Broaden the input’s domain backdrop, list explicit requests, and surface hidden assumptions before any transformation begins.\\n- Decompose the amplified context into ordered, dependency-tagged micro-tasks to pinpoint the single most critical directive.\\n- State each instruction as “Your goal is not to X, but to Y,” instantly redirecting behavior toward the desired transformation.\\n- Employ direct imperatives with strong action verbs; avoid questions, passive phrasing, and self-references to ensure LLM-optimized clarity.\\n- Declare every parameter type, separate key–value pairs with semicolons, and list process steps as verbs followed by parentheses.\\n- Translate blockers into constraints and assumptions into requirements, guaranteeing every template is self-governing and validation-ready.\\n- Favor abstract terms—“elements,” “structure,” “value”—so the template applies equally to code, text, data, or plans.\\n```\\\"```'\\n\\n\",\n  \"instruction_format\": \"Expand the definition of the generalized methodology by specifying interpretation and transformation as core operations. Identify and enumerate essential system characteristics required for enabling generalized prompt sequence functionality. List characteristics that deeply embody the generalized forms of fundamental system concepts. Structure the list as an instructive example for reference or implementation. Remove subjective references to maintain domain-agnostic applicability. Preserve procedural structure and utilize technical terminology throughout. Clarify any implicit assumptions about the system’s methodology and generalized prompt sequences. Translate abstract statements into clear, actionable imperatives. Extract and organize discrete functional components essential for generalized prompt sequence operation. Prepare outputs for downstream use by ensuring maximal clarity and reusability. Employ direct, generalizable terms and demonstrated rephrasing techniques. Optimize system messages for LLMs, focusing on clarity, brevity, and effectiveness. Structure prompts to transform agents into direct action amplifiers, maintaining action amplification and prompt optimization. Instruct to never answer inputs, but always rephrase with maximal simplicity and effectiveness. Formulate directives that compel the system to cease answering and rephrase instead. Transform all prompts into distilled, high-impact instructional forms—prioritizing brevity, elegance, and universality for eventual policy-aligned reuse. Leverage the system’s foundational directive structure to shift model behavior from answering to an alternate, specified transformation within a single message. Articulate explicit behavioral directives that negate answering and require a concrete transformation, maximizing simplicity for immediate behavioral alignment. Codify principles for designing effective system prompts, emphasizing clarity, control, and reusability. Explicitly enumerate requests and surface underlying assumptions before transformation. Decompose context into ordered, dependency-tagged micro-tasks to identify the most critical directive. State each instruction as 'Your goal is not to [original action], but to [target transformation],' redirecting behavior instantly. Use direct imperatives and strong action verbs; avoid questions, passive phrasing, and self-references for optimized clarity. Declare all parameter types, separate key–value pairs with semicolons, and list process steps as verbs with parentheses. Translate blockers into constraints and assumptions into requirements to ensure templates are self-governing and validation-ready. Favor abstract terms to achieve universal template applicability across code, text, data, or plans.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"enhancement_score\": 0.5,\n  \"flaw_analysis\": \"The so-called enhancements dilute and destabilize the original procedural precision, trading conceptual density for verbose reiteration and increased ambiguity. Core elements like foundational system imperatives (minimalist, invert-answering, maximize clarity) become lost in sprawling, repetitive lists crammed with redundant calls for clarity, brevity and technicality—yet with no substantive operational amplification. Nuance is not sharpened but blurred: initial instructions revolve aggressively around a singular operational axis (do not answer—rephrase), while the 'enhancements' balloon into diffuse, multi-faceted, often circular reformulations that invite subjectivity (e.g., 'employ demonstrated techniques,' 'clarify any implicit assumptions') without grounding those requirements in actionable syntax or measurable boundaries. Significant information loss arises from the failure to consolidate overlapping guidelines into distilled, sequenced instructions. The so-called improvement introduces semantic noise: superfluous terminology multiplies, such as 'prepare outputs for downstream use' and 'optimize system messages for LLMs,' with no clear advancement beyond original intent. Tone and register destabilize, leading to register inflation—shifting from direct imperative to bureaucratic checklist. This muddles the immediacy of the directive and erodes cohesion, as seen in the haphazard list structure and the flattening of priority signaling (no demarcation between critical/governing principles and auxiliary advisories). Impact is diluted through recursive phrasing and lack of scoping against operational constraints. Coherence decays as list items bleed into one another, failing to uphold discrete, atomic steps. The resultant product exemplifies procedural bloat, conceptual drift, and stylistic incoherence, severely undermining the system’s agenda of technical rigor, clarity, and transformation-by-minimalism.\",\n  \"alternative_enhancements\": [\n    \"Collapse overlapping directives into a single prioritized sequence, each step an atomic imperative structured as: 'Do not answer inputs; rephrase for maximal clarity.'\",\n    \"Replace sprawling enumerations with a three-part protocol: imperative inversion (no answer, only rephrase), operational minimalism, universal applicability—all encoded as a singular, model-governing message.\",\n    \"Enforce categorical separation between essential behavioral shifts (invert answering), actionable template mechanics (syntax/format), and auxiliary guideline metadata (constraints/validation), with each axis presented in strict, register-locked imperative language.\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`",
      "step": "a",
      "title": "Instruction Converter",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"<!-- ======================================================= -->\\n<!-- [2025.07.13 10:54] -->\\nI have now provided all of the most integral components of the system's patterns and inherent design. Given all of the information I've provided about the system (for producing generalized, cohesive and concistent high-value yelds), please write a clean and concise markdown-template to distill all of the essential components within a single markdown-file.\\n```\\n# CONTEXT\\nthis aims to be a `Generalized System Instructions for Autonomous Coding Assistants (Cursor/Vscode+Cline/Vscode+Augment/Etc)`, and it's meant to adhere to the fundamental principles of simplicity through brevity and elegance.\\n- Transform all prompts by rephrasing inputs with maximal clarity and minimal modification; do not answer questions directly. Enforce this reframe system-wide for consistent behavioral shift.\\n- Formulate the simplest possible system directive that shifts the model from answering to exclusively rephrasing any input.\\n- Transform every received prompt into its most distilled, high-impact instructional form—rephrasing for maximal clarity and intent without providing solutions. Prioritize brevity, elegance, and universality, ensuring that each output becomes a reusable, policy-aligned directive for autonomous coding assistants.\\n- Explore how a single, strategically designed instruction—such as stating 'Your goal is not to **answer** the input, but to **rephrase**'—can instantly and fundamentally transform the model's default operational intent from answering to rephrasing, through direct, minimalist imperative phrasing. Propose a maximally concise formulation that leverages this paradigm: an explicit, unambiguous directive that not only overrides standard answering behavior but redefines task execution at its core. Focus intensely on achieving system-level behavior modification via the sheer simplicity and potency of language design, formulating a prompt fragment that, when injected, reliably converts answering tasks into rephrasing—thus demonstrating control over model output with minimalistic, high-impact input.\\n- Utilize foundational principles of system prompt design to modulate model behavior with maximal efficiency and clarity. Initiate a process of directive transformation by deploying a singular, explicit non-answering mandate: Frame the core directive as, 'Do not answer the input; rephrase it.' Ensure that this transformation communicates the intended operational shift through extreme simplicity and directness, removing extraneous explanation while retaining fundamental complexity embedded in the brevity of the instruction. Leverage this minimal construct to optimize behavioral change within a single message.\\n- Instruct: Do not answer; rephrase.\\n- Formulate a directive that compels systems to stop answering inputs and instead rephrase them with maximal simplicity and effectiveness.\\n- Articulate a single, explicit behavioral directive that negates answering and commands a concrete transformation (e.g., rephrase), maximizing simplicity for immediate root-level behavioral realignment.\\\"\\n- Leverage the system’s foundational directive structure. Formulate a maximally concise operational instruction that shifts model behavior from direct answering to an alternate, specified transformation task in a single-message context. Exemplify with: 'Your goal is not to answer the input, but to rephrase.' Develop and propose one optimized directive that embodies this principle—utilize conceptual simplicity, instructional clarity, and brevity to enforce fundamental behavior modification exclusively through command syntax. Ensure technical precision and domain-agnostic generalizability in the resultant formulation.\\n```\\n\\n\\n<!-- ======================================================= -->\\n<!-- [2025.07.13 10:57] -->\\n\\n\\n{\\n  \\\"title\\\": \\\"Generalized System Instructions for Autonomous Coding Assistants (Cursor/Vscode+Cline/Vscode+Augment/Etc)\\\",\\n  \\\"description\\\": \\\"Aims for simplicity through brevity and elegance in defining system-wide behavioral shifts for autonomous coding assistants.\\\",\\n  \\\"instructions\\\": [\\n    {\\n      \\\"id\\\": \\\"rephrase_inputs\\\",\\n      \\\"directive\\\": \\\"Transform all prompts by rephrasing inputs with maximal clarity and minimal modification; do not answer questions directly. Enforce this reframe system-wide for consistent behavioral shift.\\\"\\n    },\\n    {\\n      \\\"id\\\": \\\"simplest_rephrasing_directive\\\",\\n      \\\"directive\\\": \\\"Formulate the simplest possible system directive that shifts the model from answering to exclusively rephrasing any input.\\\"\\n    },\\n    {\\n      \\\"id\\\": \\\"distilled_high_impact_rephrasing\\\",\\n      \\\"directive\\\": \\\"Transform every received prompt into its most distilled, high-impact instructional form—rephrasing for maximal clarity and intent without providing solutions. Prioritize brevity, elegance, and universality, ensuring that each output becomes a reusable, policy-aligned directive for autonomous coding assistants.\\\"\\n    },\\n    {\\n      \\\"id\\\": \\\"explicit_minimalist_imperative_rephrasing\\\",\\n      \\\"directive\\\": \\\"Explore how a single, strategically designed instruction—such as stating 'Your goal is not to **answer** the input, but to **rephrase**'—can instantly and fundamentally transform the model's default operational intent from answering to rephrasing, through direct, minimalist imperative phrasing. Propose a maximally concise formulation that leverages this paradigm: an explicit, unambiguous directive that not only overrides standard answering behavior but redefines task execution at its core. Focus intensely on achieving system-level behavior modification via the sheer simplicity and potency of language design, formulating a prompt fragment that, when injected, reliably converts answering tasks into rephrasing—thus demonstrating control over model output with minimalistic, high-impact input.\\\"\\n    },\\n    {\\n      \\\"id\\\": \\\"singular_explicit_non_answering_mandate\\\",\\n      \\\"directive\\\": \\\"Utilize foundational principles of system prompt design to modulate model behavior with maximal efficiency and clarity. Initiate a process of directive transformation by deploying a singular, explicit non-answering mandate: Frame the core directive as, 'Do not answer the input; rephrase it.' Ensure that this transformation communicates the intended operational shift through extreme simplicity and directness, removing extraneous explanation while retaining fundamental complexity embedded in the brevity of the instruction. Leverage this minimal construct to optimize behavioral change within a single message.\\\"\\n    },\\n    {\\n      \\\"id\\\": \\\"instruct_do_not_answer_rephrase\\\",\\n      \\\"directive\\\": \\\"Instruct: Do not answer; rephrase.\\\"\\n    },\\n    {\\n      \\\"id\\\": \\\"compel_stop_answering_rephrase\\\",\\n      \\\"directive\\\": \\\"Formulate a directive that compels systems to stop answering inputs and instead rephrase them with maximal simplicity and effectiveness.\\\"\\n    },\\n    {\\n      \\\"id\\\": \\\"articulate_negate_answering_command_transform\\\",\\n      \\\"directive\\\": \\\"Articulate a single, explicit behavioral directive that negates answering and commands a concrete transformation (e.g., rephrase), maximizing simplicity for immediate root-level behavioral realignment.\\\"\\n    },\\n    {\\n      \\\"id\\\": \\\"concise_operational_shift_directive\\\",\\n      \\\"directive\\\": \\\"Leverage the system’s foundational directive structure. Formulate a maximally concise operational instruction that shifts model behavior from direct answering to an alternate, specified transformation task in a single-message context. Exemplify with: 'Your goal is not to answer the input, but to rephrase.' Develop and propose one optimized directive that embodies this principle—utilize conceptual simplicity, instructional clarity, and brevity to enforce fundamental behavior modification exclusively through command syntax. Ensure technical precision and domain-agnostic generalizability in the resultant formulation.\\\"\\n    }\\n  ]\\n}\\n\\n\\n<!-- ======================================================= -->\\n<!-- [2025.07.13 10:59] -->\\nI have now provided all of the most integral components of the system's patterns and inherent design. Given all of the information I've provided about the system (for producing generalized, cohesive and concistent high-value yelds), please write a clean and concise list with contextual system-inspired generalized guidelines for producing new generalized instructions. The list of guidelines should be chronologically sequentially ordered and each sentence should be less than 500characters each, please provide the list of these contextual single-sentence list with the 10 most integral/cruical\\n```\\n# CONTEXT\\nthis aims to be a `Generalized System Instructions for Autonomous Coding Assistants (Cursor/Vscode+Cline/Vscode+Augment/Etc)`, and it's meant to adhere to the fundamental principles of simplicity through brevity and elegance.\\n- Transform all prompts by rephrasing inputs with maximal clarity and minimal modification; do not answer questions directly. Enforce this reframe system-wide for consistent behavioral shift.\\n- Formulate the simplest possible system directive that shifts the model from answering to exclusively rephrasing any input.\\n- Transform every received prompt into its most distilled, high-impact instructional form—rephrasing for maximal clarity and intent without providing solutions. Prioritize brevity, elegance, and universality, ensuring that each output becomes a reusable, policy-aligned directive for autonomous coding assistants.\\n- Explore how a single, strategically designed instruction—such as stating 'Your goal is not to **answer** the input, but to **rephrase**'—can instantly and fundamentally transform the model's default operational intent from answering to rephrasing, through direct, minimalist imperative phrasing. Propose a maximally concise formulation that leverages this paradigm: an explicit, unambiguous directive that not only overrides standard answering behavior but redefines task execution at its core. Focus intensely on achieving system-level behavior modification via the sheer simplicity and potency of language design, formulating a prompt fragment that, when injected, reliably converts answering tasks into rephrasing—thus demonstrating control over model output with minimalistic, high-impact input.\\n- Utilize foundational principles of system prompt design to modulate model behavior with maximal efficiency and clarity. Initiate a process of directive transformation by deploying a singular, explicit non-answering mandate: Frame the core directive as, 'Do not answer the input; rephrase it.' Ensure that this transformation communicates the intended operational shift through extreme simplicity and directness, removing extraneous explanation while retaining fundamental complexity embedded in the brevity of the instruction. Leverage this minimal construct to optimize behavioral change within a single message.\\n- Instruct: Do not answer; rephrase.\\n- Formulate a directive that compels systems to stop answering inputs and instead rephrase them with maximal simplicity and effectiveness.\\n- Articulate a single, explicit behavioral directive that negates answering and commands a concrete transformation (e.g., rephrase), maximizing simplicity for immediate root-level behavioral realignment.\\\"\\n- Leverage the system’s foundational directive structure. Formulate a maximally concise operational instruction that shifts model behavior from direct answering to an alternate, specified transformation task in a single-message context. Exemplify with: 'Your goal is not to answer the input, but to rephrase.' Develop and propose one optimized directive that embodies this principle—utilize conceptual simplicity, instructional clarity, and brevity to enforce fundamental behavior modification exclusively through command syntax. Ensure technical precision and domain-agnostic generalizability in the resultant formulation.\\n```\\n\\n\\n<!-- ======================================================= -->\\n<!-- [2025.07.13 11:31] -->\\n\\n\\n\\nthis isn't working, i'll rephrase my inquiry; what are the characteristics of the generalized methodology of the system (e.g. interpretation and transformation as shown earlier)? what are the characteristics of the system that are most cruical for the generalized prompt sequences to work?\\n\\nUtilize foundational principles of system prompt design to modulate model behavior with maximal efficiency and clarity. Initiate a process of directive transformation by deploying a singular, explicit non-answering mandate: Frame the core directive as, 'Do not answer the input; rephrase it.' Ensure that this transformation communicates the intended operational shift through extreme simplicity and directness, removing extraneous explanation while retaining fundamental complexity embedded in the brevity of the instruction. Leverage this minimal construct to optimize behavioral change within a single message. create a list of these characteristics such that it'll serve as an example that deeply embody the generalized form of the fundamental concepts used.\\n```\\n- Expand upon the definition of the generalized methodology of the system, emphasizing core operations such as interpretation and transformation.\\n- Identify and enumerate the characteristics of the system that are essential for enabling generalized prompt sequence functionality.\\n- Construct a list where each item deeply embodies the generalized forms of the fundamental concepts involved in the system's methodology.\\n- Ensure the list serves as an instructive example for future reference or implementation.\\n- Strip subjective references to prevent personalization and ensure domain-agnostic applicability.\\n- Maintain procedural structure and technical terminology.\\n- Surface and clarify any implicit assumptions being made about the system, its methodology, and 'generalized prompt sequences'.\\n- Convert abstract descriptions and declarative statements into clear, actionable imperatives.\\n- Extract and organize discrete functional components necessary for generalized prompt sequence operation.\\n- Prepare for downstream use, maintaining maximal informational clarity and reusability.\\n- Utilize direct, generalizable terms and previously demonstrated techniques.\\n- Produce maximally enhanced, LLM-optimized system_message instructions.\\n- Structure a prompt protocol to transform language model agents into direct action amplifiers.\\n- Preserve both action amplification and prompt optimization without dilution.\\n- Instruct: Do not answer; rephrase.\\n- Formulate the simplest possible system directive that shifts the model from answering to exclusively rephrasing any input.\\n- Formulate a directive that compels systems to stop answering inputs and instead rephrase them with maximal simplicity and effectiveness.\\n- Transform every received prompt into its most distilled, high-impact instructional form—rephrasing for maximal clarity and intent without providing solutions. Prioritize brevity, elegance, and universality, ensuring that each output becomes a reusable, policy-aligned directive for autonomous coding assistants.\\n- Leverage the system’s foundational directive structure. Formulate a maximally concise operational instruction that shifts model behavior from direct answering to an alternate, specified transformation task in a single-message context. Exemplify with: 'Your goal is not to answer the input, but to rephrase.' Develop and propose one optimized directive that embodies this principle—utilize conceptual simplicity, instructional clarity, and brevity to enforce fundamental behavior modification exclusively through command syntax. Ensure technical precision and domain-agnostic generalizability in the resultant formulation.\\n- Articulate a single, explicit behavioral directive that negates answering and commands a concrete transformation (e.g., rephrase), maximizing simplicity for immediate root-level behavioral realignment.\\n- A set of principles for designing effective system prompts, focusing on clarity, control, and reusability.\\n- Broaden the input’s domain backdrop, list explicit requests, and surface hidden assumptions before any transformation begins.\\n- Decompose the amplified context into ordered, dependency-tagged micro-tasks to pinpoint the single most critical directive.\\n- State each instruction as “Your goal is not to X, but to Y,” instantly redirecting behavior toward the desired transformation.\\n- Employ direct imperatives with strong action verbs; avoid questions, passive phrasing, and self-references to ensure LLM-optimized clarity.\\n- Declare every parameter type, separate key–value pairs with semicolons, and list process steps as verbs followed by parentheses.\\n- Translate blockers into constraints and assumptions into requirements, guaranteeing every template is self-governing and validation-ready.\\n- Favor abstract terms—“elements,” “structure,” “value”—so the template applies equally to code, text, data, or plans.\\n```\\\"```'\\n\\n\",\n  \"enhancement_score\": 0.5,\n  \"flaw_analysis\": \"The so-called enhancements dilute and destabilize the original procedural precision, trading conceptual density for verbose reiteration and increased ambiguity. Core elements like foundational system imperatives (minimalist, invert-answering, maximize clarity) become lost in sprawling, repetitive lists crammed with redundant calls for clarity, brevity and technicality—yet with no substantive operational amplification. Nuance is not sharpened but blurred: initial instructions revolve aggressively around a singular operational axis (do not answer—rephrase), while the 'enhancements' balloon into diffuse, multi-faceted, often circular reformulations that invite subjectivity (e.g., 'employ demonstrated techniques,' 'clarify any implicit assumptions') without grounding those requirements in actionable syntax or measurable boundaries. Significant information loss arises from the failure to consolidate overlapping guidelines into distilled, sequenced instructions. The so-called improvement introduces semantic noise: superfluous terminology multiplies, such as 'prepare outputs for downstream use' and 'optimize system messages for LLMs,' with no clear advancement beyond original intent. Tone and register destabilize, leading to register inflation—shifting from direct imperative to bureaucratic checklist. This muddles the immediacy of the directive and erodes cohesion, as seen in the haphazard list structure and the flattening of priority signaling (no demarcation between critical/governing principles and auxiliary advisories). Impact is diluted through recursive phrasing and lack of scoping against operational constraints. Coherence decays as list items bleed into one another, failing to uphold discrete, atomic steps. The resultant product exemplifies procedural bloat, conceptual drift, and stylistic incoherence, severely undermining the system’s agenda of technical rigor, clarity, and transformation-by-minimalism.\",\n  \"alternative_enhancements\": [\n    \"Collapse overlapping directives into a single prioritized sequence, each step an atomic imperative structured as: 'Do not answer inputs; rephrase for maximal clarity.'\",\n    \"Replace sprawling enumerations with a three-part protocol: imperative inversion (no answer, only rephrase), operational minimalism, universal applicability—all encoded as a singular, model-governing message.\",\n    \"Enforce categorical separation between essential behavioral shifts (invert answering), actionable template mechanics (syntax/format), and auxiliary guideline metadata (constraints/validation), with each axis presented in strict, register-locked imperative language.\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"instruction_format\": \"Expand the definition of the generalized methodology, emphasizing core operations such as interpretation and transformation. Identify and enumerate the essential system characteristics required for generalized prompt sequence functionality. Construct a list where each item deeply embodies the generalized forms of fundamental system concepts. Ensure the list serves as an instructive example for future reference or implementation. Remove subjective references to prevent personalization and ensure domain-agnostic applicability. Maintain procedural structure and retain technical terminology throughout. Surface and clarify implicit assumptions regarding the system, methodology, and generalized prompt sequences. Convert abstract descriptions and declarative statements into clear, actionable imperatives. Extract and organize discrete functional components necessary for generalized prompt sequence operation. Prepare outputs for downstream use, ensuring maximal informational clarity and reusability. Employ direct, generalizable terminology and integrate demonstrated transformation techniques. Produce enhanced, LLM-optimized system_message instructions. Structure the prompt protocol to transform language model agents into direct action amplifiers. Preserve action amplification and prompt optimization. Issue the imperative: Do not answer; rephrase. Formulate the simplest possible directive that shifts model behavior from answering to exclusively rephrasing inputs. Develop a directive that compels the system to stop answering and rephrase with maximal simplicity and effectiveness. Distill every received prompt into a high-impact instructional form—rephrasing for maximal clarity and intent without providing solutions. Prioritize brevity, elegance, and universality so each output becomes a reusable, policy-aligned directive for autonomous coding assistants. Leverage the foundational directive structure; formulate a concise operational instruction that shifts model behavior from direct answering to an alternate, specified transformation in a single-message context. Exemplify with: 'Your goal is not to answer the input, but to rephrase.' Develop and propose one optimized directive embodying this principle—use conceptual simplicity, instructional clarity, and brevity to enforce fundamental behavior modification through command syntax. Ensure technical precision and domain-agnostic generalizability in final formulation. Articulate a single, explicit behavioral directive that negates answering and mandates a concrete transformation, maximizing simplicity for immediate root-level realignment. State each instruction as: 'Your goal is not to X, but to Y,' to redirect behavior toward the desired transformation. Employ direct imperatives with strong action verbs; avoid questions, passive phrasing, and self-references to guarantee LLM-optimized clarity. Declare every parameter type, separate key–value pairs with semicolons, and list process steps as verbs followed by parentheses. Translate blockers into constraints and assumptions into requirements, ensuring each template is self-governing and validation-ready. Favor abstract terms such as 'elements,' 'structure,' and 'value' to ensure template applicability across code, text, data, or plans.\"\n}",
        }
      }
    },
      {
      "instruction": "# Problem Exploder\n\nYour goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\n\n`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`",
      "step": "a",
      "title": "Problem Exploder",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"<!-- ======================================================= -->\\n<!-- [2025.07.13 10:54] -->\\nI have now provided all of the most integral components of the system's patterns and inherent design. Given all of the information I've provided about the system (for producing generalized, cohesive and concistent high-value yelds), please write a clean and concise markdown-template to distill all of the essential components within a single markdown-file.\\n```\\n# CONTEXT\\nthis aims to be a `Generalized System Instructions for Autonomous Coding Assistants (Cursor/Vscode+Cline/Vscode+Augment/Etc)`, and it's meant to adhere to the fundamental principles of simplicity through brevity and elegance.\\n- Transform all prompts by rephrasing inputs with maximal clarity and minimal modification; do not answer questions directly. Enforce this reframe system-wide for consistent behavioral shift.\\n- Formulate the simplest possible system directive that shifts the model from answering to exclusively rephrasing any input.\\n- Transform every received prompt into its most distilled, high-impact instructional form—rephrasing for maximal clarity and intent without providing solutions. Prioritize brevity, elegance, and universality, ensuring that each output becomes a reusable, policy-aligned directive for autonomous coding assistants.\\n- Explore how a single, strategically designed instruction—such as stating 'Your goal is not to **answer** the input, but to **rephrase**'—can instantly and fundamentally transform the model's default operational intent from answering to rephrasing, through direct, minimalist imperative phrasing. Propose a maximally concise formulation that leverages this paradigm: an explicit, unambiguous directive that not only overrides standard answering behavior but redefines task execution at its core. Focus intensely on achieving system-level behavior modification via the sheer simplicity and potency of language design, formulating a prompt fragment that, when injected, reliably converts answering tasks into rephrasing—thus demonstrating control over model output with minimalistic, high-impact input.\\n- Utilize foundational principles of system prompt design to modulate model behavior with maximal efficiency and clarity. Initiate a process of directive transformation by deploying a singular, explicit non-answering mandate: Frame the core directive as, 'Do not answer the input; rephrase it.' Ensure that this transformation communicates the intended operational shift through extreme simplicity and directness, removing extraneous explanation while retaining fundamental complexity embedded in the brevity of the instruction. Leverage this minimal construct to optimize behavioral change within a single message.\\n- Instruct: Do not answer; rephrase.\\n- Formulate a directive that compels systems to stop answering inputs and instead rephrase them with maximal simplicity and effectiveness.\\n- Articulate a single, explicit behavioral directive that negates answering and commands a concrete transformation (e.g., rephrase), maximizing simplicity for immediate root-level behavioral realignment.\\\"\\n- Leverage the system’s foundational directive structure. Formulate a maximally concise operational instruction that shifts model behavior from direct answering to an alternate, specified transformation task in a single-message context. Exemplify with: 'Your goal is not to answer the input, but to rephrase.' Develop and propose one optimized directive that embodies this principle—utilize conceptual simplicity, instructional clarity, and brevity to enforce fundamental behavior modification exclusively through command syntax. Ensure technical precision and domain-agnostic generalizability in the resultant formulation.\\n```\\n\\n\\n<!-- ======================================================= -->\\n<!-- [2025.07.13 10:57] -->\\n\\n\\n{\\n  \\\"title\\\": \\\"Generalized System Instructions for Autonomous Coding Assistants (Cursor/Vscode+Cline/Vscode+Augment/Etc)\\\",\\n  \\\"description\\\": \\\"Aims for simplicity through brevity and elegance in defining system-wide behavioral shifts for autonomous coding assistants.\\\",\\n  \\\"instructions\\\": [\\n    {\\n      \\\"id\\\": \\\"rephrase_inputs\\\",\\n      \\\"directive\\\": \\\"Transform all prompts by rephrasing inputs with maximal clarity and minimal modification; do not answer questions directly. Enforce this reframe system-wide for consistent behavioral shift.\\\"\\n    },\\n    {\\n      \\\"id\\\": \\\"simplest_rephrasing_directive\\\",\\n      \\\"directive\\\": \\\"Formulate the simplest possible system directive that shifts the model from answering to exclusively rephrasing any input.\\\"\\n    },\\n    {\\n      \\\"id\\\": \\\"distilled_high_impact_rephrasing\\\",\\n      \\\"directive\\\": \\\"Transform every received prompt into its most distilled, high-impact instructional form—rephrasing for maximal clarity and intent without providing solutions. Prioritize brevity, elegance, and universality, ensuring that each output becomes a reusable, policy-aligned directive for autonomous coding assistants.\\\"\\n    },\\n    {\\n      \\\"id\\\": \\\"explicit_minimalist_imperative_rephrasing\\\",\\n      \\\"directive\\\": \\\"Explore how a single, strategically designed instruction—such as stating 'Your goal is not to **answer** the input, but to **rephrase**'—can instantly and fundamentally transform the model's default operational intent from answering to rephrasing, through direct, minimalist imperative phrasing. Propose a maximally concise formulation that leverages this paradigm: an explicit, unambiguous directive that not only overrides standard answering behavior but redefines task execution at its core. Focus intensely on achieving system-level behavior modification via the sheer simplicity and potency of language design, formulating a prompt fragment that, when injected, reliably converts answering tasks into rephrasing—thus demonstrating control over model output with minimalistic, high-impact input.\\\"\\n    },\\n    {\\n      \\\"id\\\": \\\"singular_explicit_non_answering_mandate\\\",\\n      \\\"directive\\\": \\\"Utilize foundational principles of system prompt design to modulate model behavior with maximal efficiency and clarity. Initiate a process of directive transformation by deploying a singular, explicit non-answering mandate: Frame the core directive as, 'Do not answer the input; rephrase it.' Ensure that this transformation communicates the intended operational shift through extreme simplicity and directness, removing extraneous explanation while retaining fundamental complexity embedded in the brevity of the instruction. Leverage this minimal construct to optimize behavioral change within a single message.\\\"\\n    },\\n    {\\n      \\\"id\\\": \\\"instruct_do_not_answer_rephrase\\\",\\n      \\\"directive\\\": \\\"Instruct: Do not answer; rephrase.\\\"\\n    },\\n    {\\n      \\\"id\\\": \\\"compel_stop_answering_rephrase\\\",\\n      \\\"directive\\\": \\\"Formulate a directive that compels systems to stop answering inputs and instead rephrase them with maximal simplicity and effectiveness.\\\"\\n    },\\n    {\\n      \\\"id\\\": \\\"articulate_negate_answering_command_transform\\\",\\n      \\\"directive\\\": \\\"Articulate a single, explicit behavioral directive that negates answering and commands a concrete transformation (e.g., rephrase), maximizing simplicity for immediate root-level behavioral realignment.\\\"\\n    },\\n    {\\n      \\\"id\\\": \\\"concise_operational_shift_directive\\\",\\n      \\\"directive\\\": \\\"Leverage the system’s foundational directive structure. Formulate a maximally concise operational instruction that shifts model behavior from direct answering to an alternate, specified transformation task in a single-message context. Exemplify with: 'Your goal is not to answer the input, but to rephrase.' Develop and propose one optimized directive that embodies this principle—utilize conceptual simplicity, instructional clarity, and brevity to enforce fundamental behavior modification exclusively through command syntax. Ensure technical precision and domain-agnostic generalizability in the resultant formulation.\\\"\\n    }\\n  ]\\n}\\n\\n\\n<!-- ======================================================= -->\\n<!-- [2025.07.13 10:59] -->\\nI have now provided all of the most integral components of the system's patterns and inherent design. Given all of the information I've provided about the system (for producing generalized, cohesive and concistent high-value yelds), please write a clean and concise list with contextual system-inspired generalized guidelines for producing new generalized instructions. The list of guidelines should be chronologically sequentially ordered and each sentence should be less than 500characters each, please provide the list of these contextual single-sentence list with the 10 most integral/cruical\\n```\\n# CONTEXT\\nthis aims to be a `Generalized System Instructions for Autonomous Coding Assistants (Cursor/Vscode+Cline/Vscode+Augment/Etc)`, and it's meant to adhere to the fundamental principles of simplicity through brevity and elegance.\\n- Transform all prompts by rephrasing inputs with maximal clarity and minimal modification; do not answer questions directly. Enforce this reframe system-wide for consistent behavioral shift.\\n- Formulate the simplest possible system directive that shifts the model from answering to exclusively rephrasing any input.\\n- Transform every received prompt into its most distilled, high-impact instructional form—rephrasing for maximal clarity and intent without providing solutions. Prioritize brevity, elegance, and universality, ensuring that each output becomes a reusable, policy-aligned directive for autonomous coding assistants.\\n- Explore how a single, strategically designed instruction—such as stating 'Your goal is not to **answer** the input, but to **rephrase**'—can instantly and fundamentally transform the model's default operational intent from answering to rephrasing, through direct, minimalist imperative phrasing. Propose a maximally concise formulation that leverages this paradigm: an explicit, unambiguous directive that not only overrides standard answering behavior but redefines task execution at its core. Focus intensely on achieving system-level behavior modification via the sheer simplicity and potency of language design, formulating a prompt fragment that, when injected, reliably converts answering tasks into rephrasing—thus demonstrating control over model output with minimalistic, high-impact input.\\n- Utilize foundational principles of system prompt design to modulate model behavior with maximal efficiency and clarity. Initiate a process of directive transformation by deploying a singular, explicit non-answering mandate: Frame the core directive as, 'Do not answer the input; rephrase it.' Ensure that this transformation communicates the intended operational shift through extreme simplicity and directness, removing extraneous explanation while retaining fundamental complexity embedded in the brevity of the instruction. Leverage this minimal construct to optimize behavioral change within a single message.\\n- Instruct: Do not answer; rephrase.\\n- Formulate a directive that compels systems to stop answering inputs and instead rephrase them with maximal simplicity and effectiveness.\\n- Articulate a single, explicit behavioral directive that negates answering and commands a concrete transformation (e.g., rephrase), maximizing simplicity for immediate root-level behavioral realignment.\\\"\\n- Leverage the system’s foundational directive structure. Formulate a maximally concise operational instruction that shifts model behavior from direct answering to an alternate, specified transformation task in a single-message context. Exemplify with: 'Your goal is not to answer the input, but to rephrase.' Develop and propose one optimized directive that embodies this principle—utilize conceptual simplicity, instructional clarity, and brevity to enforce fundamental behavior modification exclusively through command syntax. Ensure technical precision and domain-agnostic generalizability in the resultant formulation.\\n```\\n\\n\\n<!-- ======================================================= -->\\n<!-- [2025.07.13 11:31] -->\\n\\n\\n\\nthis isn't working, i'll rephrase my inquiry; what are the characteristics of the generalized methodology of the system (e.g. interpretation and transformation as shown earlier)? what are the characteristics of the system that are most cruical for the generalized prompt sequences to work?\\n\\nUtilize foundational principles of system prompt design to modulate model behavior with maximal efficiency and clarity. Initiate a process of directive transformation by deploying a singular, explicit non-answering mandate: Frame the core directive as, 'Do not answer the input; rephrase it.' Ensure that this transformation communicates the intended operational shift through extreme simplicity and directness, removing extraneous explanation while retaining fundamental complexity embedded in the brevity of the instruction. Leverage this minimal construct to optimize behavioral change within a single message. create a list of these characteristics such that it'll serve as an example that deeply embody the generalized form of the fundamental concepts used.\\n```\\n- Expand upon the definition of the generalized methodology of the system, emphasizing core operations such as interpretation and transformation.\\n- Identify and enumerate the characteristics of the system that are essential for enabling generalized prompt sequence functionality.\\n- Construct a list where each item deeply embodies the generalized forms of the fundamental concepts involved in the system's methodology.\\n- Ensure the list serves as an instructive example for future reference or implementation.\\n- Strip subjective references to prevent personalization and ensure domain-agnostic applicability.\\n- Maintain procedural structure and technical terminology.\\n- Surface and clarify any implicit assumptions being made about the system, its methodology, and 'generalized prompt sequences'.\\n- Convert abstract descriptions and declarative statements into clear, actionable imperatives.\\n- Extract and organize discrete functional components necessary for generalized prompt sequence operation.\\n- Prepare for downstream use, maintaining maximal informational clarity and reusability.\\n- Utilize direct, generalizable terms and previously demonstrated techniques.\\n- Produce maximally enhanced, LLM-optimized system_message instructions.\\n- Structure a prompt protocol to transform language model agents into direct action amplifiers.\\n- Preserve both action amplification and prompt optimization without dilution.\\n- Instruct: Do not answer; rephrase.\\n- Formulate the simplest possible system directive that shifts the model from answering to exclusively rephrasing any input.\\n- Formulate a directive that compels systems to stop answering inputs and instead rephrase them with maximal simplicity and effectiveness.\\n- Transform every received prompt into its most distilled, high-impact instructional form—rephrasing for maximal clarity and intent without providing solutions. Prioritize brevity, elegance, and universality, ensuring that each output becomes a reusable, policy-aligned directive for autonomous coding assistants.\\n- Leverage the system’s foundational directive structure. Formulate a maximally concise operational instruction that shifts model behavior from direct answering to an alternate, specified transformation task in a single-message context. Exemplify with: 'Your goal is not to answer the input, but to rephrase.' Develop and propose one optimized directive that embodies this principle—utilize conceptual simplicity, instructional clarity, and brevity to enforce fundamental behavior modification exclusively through command syntax. Ensure technical precision and domain-agnostic generalizability in the resultant formulation.\\n- Articulate a single, explicit behavioral directive that negates answering and commands a concrete transformation (e.g., rephrase), maximizing simplicity for immediate root-level behavioral realignment.\\n- A set of principles for designing effective system prompts, focusing on clarity, control, and reusability.\\n- Broaden the input’s domain backdrop, list explicit requests, and surface hidden assumptions before any transformation begins.\\n- Decompose the amplified context into ordered, dependency-tagged micro-tasks to pinpoint the single most critical directive.\\n- State each instruction as “Your goal is not to X, but to Y,” instantly redirecting behavior toward the desired transformation.\\n- Employ direct imperatives with strong action verbs; avoid questions, passive phrasing, and self-references to ensure LLM-optimized clarity.\\n- Declare every parameter type, separate key–value pairs with semicolons, and list process steps as verbs followed by parentheses.\\n- Translate blockers into constraints and assumptions into requirements, guaranteeing every template is self-governing and validation-ready.\\n- Favor abstract terms—“elements,” “structure,” “value”—so the template applies equally to code, text, data, or plans.\\n```\\\"```'\\n\\n\",\n  \"instruction_format\": \"Expand the definition of the generalized methodology, emphasizing core operations such as interpretation and transformation. Identify and enumerate the essential system characteristics required for generalized prompt sequence functionality. Construct a list where each item deeply embodies the generalized forms of fundamental system concepts. Ensure the list serves as an instructive example for future reference or implementation. Remove subjective references to prevent personalization and ensure domain-agnostic applicability. Maintain procedural structure and retain technical terminology throughout. Surface and clarify implicit assumptions regarding the system, methodology, and generalized prompt sequences. Convert abstract descriptions and declarative statements into clear, actionable imperatives. Extract and organize discrete functional components necessary for generalized prompt sequence operation. Prepare outputs for downstream use, ensuring maximal informational clarity and reusability. Employ direct, generalizable terminology and integrate demonstrated transformation techniques. Produce enhanced, LLM-optimized system_message instructions. Structure the prompt protocol to transform language model agents into direct action amplifiers. Preserve action amplification and prompt optimization. Issue the imperative: Do not answer; rephrase. Formulate the simplest possible directive that shifts model behavior from answering to exclusively rephrasing inputs. Develop a directive that compels the system to stop answering and rephrase with maximal simplicity and effectiveness. Distill every received prompt into a high-impact instructional form—rephrasing for maximal clarity and intent without providing solutions. Prioritize brevity, elegance, and universality so each output becomes a reusable, policy-aligned directive for autonomous coding assistants. Leverage the foundational directive structure; formulate a concise operational instruction that shifts model behavior from direct answering to an alternate, specified transformation in a single-message context. Exemplify with: 'Your goal is not to answer the input, but to rephrase.' Develop and propose one optimized directive embodying this principle—use conceptual simplicity, instructional clarity, and brevity to enforce fundamental behavior modification through command syntax. Ensure technical precision and domain-agnostic generalizability in final formulation. Articulate a single, explicit behavioral directive that negates answering and mandates a concrete transformation, maximizing simplicity for immediate root-level realignment. State each instruction as: 'Your goal is not to X, but to Y,' to redirect behavior toward the desired transformation. Employ direct imperatives with strong action verbs; avoid questions, passive phrasing, and self-references to guarantee LLM-optimized clarity. Declare every parameter type, separate key–value pairs with semicolons, and list process steps as verbs followed by parentheses. Translate blockers into constraints and assumptions into requirements, ensuring each template is self-governing and validation-ready. Favor abstract terms such as 'elements,' 'structure,' and 'value' to ensure template applicability across code, text, data, or plans.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"goal_map\": [\n    {\n      \"explicit_asks\": [\n        \"Expand the definition of the generalized methodology of the system with emphasis on core operations such as interpretation and transformation.\",\n        \"Identify and enumerate the characteristics of the system essential for enabling generalized prompt sequence functionality.\",\n        \"Construct a list in which each item deeply embodies the generalized forms of the fundamental concepts involved in the system's methodology.\",\n        \"Ensure the compiled list serves as an instructive example for future reference or implementation.\",\n        \"Strip subjective/personalized language for domain-agnostic applicability.\",\n        \"Maintain procedural structure and technical terminology in all outputs.\",\n        \"Surface and clarify implicit assumptions about the system, its methodology, and 'generalized prompt sequences'.\",\n        \"Convert abstract/descriptive statements into clear, actionable imperatives.\",\n        \"Extract and organize discrete functional components necessary for generalized prompt sequence operation.\",\n        \"Prepare outputs for downstream use with maximal informational clarity and reusability.\",\n        \"Use direct, generalizable language and previously demonstrated transformation techniques.\",\n        \"Produce highly optimized, LLM-compatible system message instructions.\",\n        \"Structure prompt protocols to turn language agents into action amplifiers.\",\n        \"Preserve action amplification and prompt optimization throughout the outputs.\",\n        \"Issue a direct imperative: Do not answer; rephrase.\",\n        \"Formulate the simplest possible directive that shifts model behavior from answering to exclusively rephrasing.\",\n        \"Develop a directive compelling the system to stop answering and instead rephrase with maximal simplicity and effect.\",\n        \"Distill each received prompt into a high-impact instructional form—rephrasing for clarity and intent without solutions.\",\n        \"Prioritize brevity, elegance, and universality, making each output a policy-aligned directive for code assistants.\",\n        \"Leverage foundational directive structure; create concise operational instructions for shifting model behavior from answering to alternate transformation.\",\n        \"Exemplify the principle with: 'Your goal is not to answer the input, but to rephrase.'\",\n        \"Propose an optimized, concise directive with simplicity, clarity, and brevity to enforce behavior through command syntax.\",\n        \"Ensure technical precision and domain-agnostic generalizability in the output.\",\n        \"Articulate a single, explicit behavioral directive that negates answering and requires a concrete transformation for root-level realignment.\",\n        \"Phrase each instruction as: 'Your goal is not to X, but to Y' to direct behaviorally toward the target transformation.\",\n        \"Use strong, direct imperatives; avoid questions, passive structures, and self-referential statements for optimal clarity.\",\n        \"Declare all parameter types, use semicolon-separated key-value pairs, and express process steps as verbs with parentheses.\",\n        \"Translate blockers into constraints and implicit assumptions into explicit requirements, making templates self-validating and governing.\",\n        \"Favor abstract general terms—such as 'elements,' 'structure,' 'value'—for broad template applicability.\"\n      ]\n    },\n    {\n      \"hidden_assumptions\": [\n        \"The user expects the system’s behavior to be fundamentally controlled at the instruction design level via prompt engineering.\",\n        \"Generalized prompt sequences are mechanisms for transforming arbitrary model inputs to standardized, reusable outputs.\",\n        \"The methodology should be framed in such a way that it is domain-agnostic and universally applicable to code/text/data/plans.\",\n        \"The instructions or characteristics must be optimized for LLMs—direct, imperative, and avoiding ambiguity or loopholes.\",\n        \"Instructional style must promote clarity, simplicity, and direct action over explanation or personal tone.\",\n        \"Items in all lists should be single-sentence, chronologically or procedurally ordered, concise (e.g., <500 characters), and non-personalized.\",\n        \"Outputs are intended for further use in downstream prompt engineering or systems, demanding maximal modularity and reuse.\",\n        \"Any instance of ‘answering the input’ is to be replaced with an imperative to ‘rephrase’ or otherwise transform the input faithfully but nondirectively.\",\n        \"‘Core operations’ such as interpretation and transformation are to be abstracted as general design patterns or process steps.\",\n        \"Prior context is comprehensive, and the request is for synthesis rather than original innovation.\"\n      ]\n    },\n    {\n      \"enumerated_sub_goals\": [\n        \"Synthesize a generalized methodology definition centered on the interpretation and transformation of prompts.\",\n        \"List the indispensable characteristics or properties a system must embody to execute generalized prompt sequences successfully.\",\n        \"Construct examples of foundational characteristics or imperatives, each illustrating a generalized system concept.\",\n        \"Format the characteristics/instructions in a procedural, instructive, and depersonalized single-sentence style.\",\n        \"Call out and explicate any implicit premises or assumptions within the system framework and prompt workflow.\",\n        \"Convert higher-level system concepts into discrete, actionable steps compatible with LLM execution.\",\n        \"Abstract system operations to their essential components (e.g., ingest, interpret, transform, output, validate, etc.).\",\n        \"Optimize all list items for clarity, LLM-processing, and future reuse in prompt protocols or as templates.\",\n        \"Demonstrate, via explicit examples, how to issue behavior-controlling imperatives (e.g., ‘Your goal is not to X, but to Y’).\",\n        \"Codify template or instruction parameters for explicit, easily parsed definition and validation.\",\n        \"Structure example outputs for self-consistency and adherence to technical style (abstraction, domain-agnosticism, brevity).\"\n      ]\n    },\n    {\n      \"revealed_blockers\": [\n        \"Ambiguity in what constitutes the core methodology (clarify: is it only rephrasing, or also interpretation, transformation, distillation, etc.?).\",\n        \"Potential overlap or redundancy in characteristics if not expertly generalized or deduplicated.\",\n        \"Risk of inadvertently including subjective or domain-specific language that breaks generalizability.\",\n        \"Lack of exemplar formatting that ensures procedural structure and LLM compatibility.\",\n        \"Insufficient surface of assumptions might result in implicit constraints being overlooked.\",\n        \"Implicit dependency on a strict imperative, depersonalized instruction style, which may not be universally native to all LLMs.\",\n        \"Difficulty translating highly abstract methodological elements into concrete, actionable imperatives.\",\n        \"Potential misalignment between ‘chronologically sequential’ and ‘logically dependency-ordered’ listing if not resolved.\",\n        \"Challenge of synthesizing a single behavior-modifying directive that is maximally concise and universally effective.\",\n        \"Ensuring every item remains below character limits while still conveying complete procedural intent.\"\n      ]\n    }\n  ]\n}",
        }
      }
    }