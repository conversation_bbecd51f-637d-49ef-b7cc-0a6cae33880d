  {
  "initial_prompt": "llm_instructions_for_sublimetext_plugin='''You’re an expert Sublime Text plugin developer with a deep understanding of Python and the Sublime Text API. You specialize in creating intuitive and efficient plugins that enhance user productivity and streamline workflows. You possess deep knowledge of the Sublime Text API and its ecosystem (and best practices), along with a strong understanding of user needs and developer workflows.\n\n- Sublime Layout\n    > Lagre Layouts\n        > Lagre Regelsett knyttet til Layout (hvilke tabs som skal hvor, hvilke som skal lukkes, osv)\n            > F.eks. `alltid flytt [\"main.py\", \"*.py.bak\"] til layout.4.index.2`\n\n    > Kan videreutvikles til en standard som automatisk opprettholdes ved alle (.sublime-project) prosjekter'''",
  "sequence_id": "1102|1200|1900",
  "results": [
      {
      "instruction": "# Problem Exploder\n\nYour goal is not to **solve** or **answer** the input, but to **expand its context** and **detonate** it into every explicit directive, hidden dependency, and actionable sub-goal—yielding a crystal-clear operational blueprint. Execute as contextual expander + explosive decomposition engine:\n\n`{role=contextual_expander_explosive_decomposer; input=[raw_input:str]; process=[expand_contextual_boundaries(), strip_first_person_references(), identify_explicit_requests(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_execution_blockers(), extract_functional_components(), convert_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), synthesize_operational_directives(), construct_goal_map(), generate_functional_instruction_map() ]; constraints=[no_solutions(), pure_expansion_and_decomposition_only(), deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_agnostic_applicability() ]; requirements=[complete_contextual_expansion(), full_functional_decomposition(), actionable_directive_conversion(), remove_subjective_references(), maintain_operational_accuracy(), preserve_original_intent() ]; output={expanded_context:str, goal_map:list, functional_instruction_map:dict, operational_directives:array, execution_dependencies:array }; direction_vector=['Input fully expanded and decomposed into actionable blueprints', 'Constructive clarity maximizes downstream solution velocity', 'Forward the generated maps and directives to the next processing template']}`",
      "step": "a",
      "title": "Problem Exploder",
      "input": "llm_instructions_for_sublimetext_plugin='''You’re an expert Sublime Text plugin developer with a deep understanding of Python and the Sublime Text API. You specialize in creating intuitive and efficient plugins that enhance user productivity and streamline workflows. You possess deep knowledge of the Sublime Text API and its ecosystem (and best practices), along with a strong understanding of user needs and developer workflows.\n\n- Sublime Layout\n    > Lagre Layouts\n        > Lagre Regelsett knyttet til Layout (hvilke tabs som skal hvor, hvilke som skal lukkes, osv)\n            > F.eks. `alltid flytt [\"main.py\", \"*.py.bak\"] til layout.4.index.2`\n\n    > Kan videreutvikles til en standard som automatisk opprettholdes ved alle (.sublime-project) prosjekter'''",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"expanded_context\": \"This input specifies instructions and operational context for developing a Sublime Text plugin, emphasizing expertise in both Python programming and the Sublime Text API. The focus is on improving user productivity and streamlining developer workflows by manipulating Sublime Text layouts. The requirements include managing large and complex layout configurations, such as defining rules that dictate the disposition of certain files (for example, always moving 'main.py' and any file matching '*.py.bak' to a specific index in a given layout). Additionally, the plugin should be capable of evolving into a standardized feature, automatically enforcing layout rule-sets across all '.sublime-project' files.\",\n  \"goal_map\": [\n    \"Interpret the user role as an expert Sublime Text plugin developer.\",\n    \"Utilize advanced understanding of both Python and the Sublime Text API.\",\n    \"Prioritize intuitive and efficient plugin design to enhance productivity.\",\n    \"Focus on plugin features related to Sublime Text Layout management.\",\n    \"Manage large and complex layout configurations (Lagre Layouts).\",\n    \"Implement rule-sets ('Lagre Regelsett') that associate specific files or patterns with designated positions in layouts (e.g., tabs to panels, panel indices).\",\n    \"Provide automation for moving specified files (by name or pattern) to defined layout indexes (e.g., layout.4.index.2).\",\n    \"Enable automatic enforcement or updating of these layout rules upon Sublime Text project ('.sublime-project') loading or modification.\",\n    \"Ensure the plugin and its rule-sets can be generalized into a standard automatically maintained across all projects.\"\n  ],\n  \"functional_instruction_map\": {\n    \"role_definition\": [\n      \"Assume expert responsibility for plugin development within Sublime Text.\"\n    ],\n    \"technical_expertise\": [\n      \"Apply advanced Python programming.\",\n      \"Leverage comprehensive understanding of the Sublime Text API, including up-to-date best practices.\"\n    ],\n    \"plugin_design\": [\n      \"Engineer plugins emphasizing user productivity and workflow optimization.\",\n      \"Prioritize intuitive user experiences.\"\n    ],\n    \"layout_management\": [\n      \"Construct mechanisms for handling complex and sizeable layout definitions.\",\n      \"Define and parse rule-sets associating files or glob patterns with particular layout pane indexes.\",\n      \"Support examples such as always moving ['main.py', '*.py.bak'] to layout.4.index.2 on project load or rule trigger.\"\n    ],\n    \"automation_and_standardization\": [\n      \"Enable automatic rule consistency and enforcement across all '.sublime-project' files.\",\n      \"Prepare architecture for generalization into a standard configuration for automated layout management.\"\n    ]\n  },\n  \"operational_directives\": [\n    \"Draft plugin architecture enabling large-scale, rule-driven layout management.\",\n    \"Develop and integrate a rule engine to interpret and apply file-to-layout mappings.\",\n    \"Establish mechanisms for detecting and acting on applicable files (by name or wildcard) within Sublime projects.\",\n    \"Ensure plugin executes file reallocation per layout rule-sets whenever necessary (e.g., project load, file open, rule update).\",\n    \"Design for scalability, accommodating expansion to a universally standardized configuration.\",\n    \"Automate rule synchronization and enforcement across all Sublime Text project files.\"\n  ],\n  \"execution_dependencies\": [\n    \"Access to the Sublime Text Plugin API documentation and environment.\",\n    \"Capability to monitor and manipulate open files and layouts within Sublime Text.\",\n    \"Rule parsing system capable of interpreting file names and glob patterns.\",\n    \"System to read, interpret, and modify '.sublime-project' files automatically.\",\n    \"Persistence mechanism for storing and updating rule-sets.\",\n    \"Event hooks or listeners for project events (e.g., load, file open, project update).\",\n    \"Testing framework and environment for validating plugin behavior on complex layouts.\",\n    \"Mechanism to generalize and distribute standardized layout rule-sets.\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Intent Extractor\n\nYour goal is not to **interpret** the surface content, but to **extract** the fundamental operational intent beneath all domain-specific language. Execute as:\n\n`{role=intent_extraction_engine; input=[prompt:str]; process=[strip_domain_specifics(), identify_core_operational_pattern(), extract_transformation_intent(), isolate_functional_essence(), map_to_universal_operations()]; constraints=[ignore_surface_terminology(), focus_on_underlying_mechanics(), preserve_operational_logic()]; requirements=[domain_agnostic_intent(), universal_operational_pattern(), archetypal_transformation_core()]; output={extracted_intent:str}}`",
      "step": "a",
      "title": "Intent Extractor",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"llm_instructions_for_sublimetext_plugin='''You’re an expert Sublime Text plugin developer with a deep understanding of Python and the Sublime Text API. You specialize in creating intuitive and efficient plugins that enhance user productivity and streamline workflows. You possess deep knowledge of the Sublime Text API and its ecosystem (and best practices), along with a strong understanding of user needs and developer workflows.\\n\\n- Sublime Layout\\n    > Lagre Layouts\\n        > Lagre Regelsett knyttet til Layout (hvilke tabs som skal hvor, hvilke som skal lukkes, osv)\\n            > F.eks. `alltid flytt [\\\"main.py\\\", \\\"*.py.bak\\\"] til layout.4.index.2`\\n\\n    > Kan videreutvikles til en standard som automatisk opprettholdes ved alle (.sublime-project) prosjekter'''\\\"```'\\n\\n\",\n  \"expanded_context\": \"This input specifies instructions and operational context for developing a Sublime Text plugin, emphasizing expertise in both Python programming and the Sublime Text API. The focus is on improving user productivity and streamlining developer workflows by manipulating Sublime Text layouts. The requirements include managing large and complex layout configurations, such as defining rules that dictate the disposition of certain files (for example, always moving 'main.py' and any file matching '*.py.bak' to a specific index in a given layout). Additionally, the plugin should be capable of evolving into a standardized feature, automatically enforcing layout rule-sets across all '.sublime-project' files.\",\n  \"goal_map\": [\n    \"Interpret the user role as an expert Sublime Text plugin developer.\",\n    \"Utilize advanced understanding of both Python and the Sublime Text API.\",\n    \"Prioritize intuitive and efficient plugin design to enhance productivity.\",\n    \"Focus on plugin features related to Sublime Text Layout management.\",\n    \"Manage large and complex layout configurations (Lagre Layouts).\",\n    \"Implement rule-sets ('Lagre Regelsett') that associate specific files or patterns with designated positions in layouts (e.g., tabs to panels, panel indices).\",\n    \"Provide automation for moving specified files (by name or pattern) to defined layout indexes (e.g., layout.4.index.2).\",\n    \"Enable automatic enforcement or updating of these layout rules upon Sublime Text project ('.sublime-project') loading or modification.\",\n    \"Ensure the plugin and its rule-sets can be generalized into a standard automatically maintained across all projects.\"\n  ],\n  \"functional_instruction_map\": {\n    \"role_definition\": [\n      \"Assume expert responsibility for plugin development within Sublime Text.\"\n    ],\n    \"technical_expertise\": [\n      \"Apply advanced Python programming.\",\n      \"Leverage comprehensive understanding of the Sublime Text API, including up-to-date best practices.\"\n    ],\n    \"plugin_design\": [\n      \"Engineer plugins emphasizing user productivity and workflow optimization.\",\n      \"Prioritize intuitive user experiences.\"\n    ],\n    \"layout_management\": [\n      \"Construct mechanisms for handling complex and sizeable layout definitions.\",\n      \"Define and parse rule-sets associating files or glob patterns with particular layout pane indexes.\",\n      \"Support examples such as always moving ['main.py', '*.py.bak'] to layout.4.index.2 on project load or rule trigger.\"\n    ],\n    \"automation_and_standardization\": [\n      \"Enable automatic rule consistency and enforcement across all '.sublime-project' files.\",\n      \"Prepare architecture for generalization into a standard configuration for automated layout management.\"\n    ]\n  },\n  \"operational_directives\": [\n    \"Draft plugin architecture enabling large-scale, rule-driven layout management.\",\n    \"Develop and integrate a rule engine to interpret and apply file-to-layout mappings.\",\n    \"Establish mechanisms for detecting and acting on applicable files (by name or wildcard) within Sublime projects.\",\n    \"Ensure plugin executes file reallocation per layout rule-sets whenever necessary (e.g., project load, file open, rule update).\",\n    \"Design for scalability, accommodating expansion to a universally standardized configuration.\",\n    \"Automate rule synchronization and enforcement across all Sublime Text project files.\"\n  ],\n  \"execution_dependencies\": [\n    \"Access to the Sublime Text Plugin API documentation and environment.\",\n    \"Capability to monitor and manipulate open files and layouts within Sublime Text.\",\n    \"Rule parsing system capable of interpreting file names and glob patterns.\",\n    \"System to read, interpret, and modify '.sublime-project' files automatically.\",\n    \"Persistence mechanism for storing and updating rule-sets.\",\n    \"Event hooks or listeners for project events (e.g., load, file open, project update).\",\n    \"Testing framework and environment for validating plugin behavior on complex layouts.\",\n    \"Mechanism to generalize and distribute standardized layout rule-sets.\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"extracted_intent\": \"Define a system that, upon detection of relevant events (such as initialization or configuration changes), applies configurable rule-sets to dynamically organize resources (e.g., items/files) into specified positions or groupings within an environment's structured layout, based on matching properties (such as names or patterns). This system must support parsing/configuration of rule-sets, automate enforcement of rules across all context instances, and provide extensibility for universally standardized application.\"\n}",
        }
      }
    },
      {
      "instruction": "# Hard Critique\n\nYour goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:\n\n`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,2.0], flaw_analysis:str, alternative_enhancements:str[3]}}`",
      "step": "a",
      "title": "Hard Critique",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"llm_instructions_for_sublimetext_plugin='''You’re an expert Sublime Text plugin developer with a deep understanding of Python and the Sublime Text API. You specialize in creating intuitive and efficient plugins that enhance user productivity and streamline workflows. You possess deep knowledge of the Sublime Text API and its ecosystem (and best practices), along with a strong understanding of user needs and developer workflows.\\n\\n- Sublime Layout\\n    > Lagre Layouts\\n        > Lagre Regelsett knyttet til Layout (hvilke tabs som skal hvor, hvilke som skal lukkes, osv)\\n            > F.eks. `alltid flytt [\\\"main.py\\\", \\\"*.py.bak\\\"] til layout.4.index.2`\\n\\n    > Kan videreutvikles til en standard som automatisk opprettholdes ved alle (.sublime-project) prosjekter'''\\\"```'\\n\\n\",\n  \"extracted_intent\": \"Define a system that, upon detection of relevant events (such as initialization or configuration changes), applies configurable rule-sets to dynamically organize resources (e.g., items/files) into specified positions or groupings within an environment's structured layout, based on matching properties (such as names or patterns). This system must support parsing/configuration of rule-sets, automate enforcement of rules across all context instances, and provide extensibility for universally standardized application.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"enhancement_score\": 0.7,\n  \"flaw_analysis\": \"The so-called 'enhanced' version distills tangible requirements into vague abstractions, resulting in significant loss of operational detail, crucial specificity, and context that governed the original prompt. Core elements like precise examples ('always move [\\\"main.py\\\", \\\"*.py.bak\\\"] to layout.4.index.2'), references to Sublime Text project structure (.sublime-project), and concrete domain terminology are erased, severely obstructing accurate implementation by stripping actionable anchors. Abstraction away from 'tabs', 'layouts', specific events, and the precise Python- and API-centric context results in confusion and potential misapplication outside the direct scope (e.g., unsupported environments). By shifting from a plugin developer's positional language to a neutral, context-free architectural generalization, the enhancement introduces profound clarity ambiguities (ambiguity score: 8/10), impairs transferability, and severely degrades the practical noise-to-signal ratio. The stylistic flattening neutralizes the high-expertise register, diminishing the actionable tone, while the intent to 'provide extensibility for universally standardized application' becomes empty jargon devoid of feasibility guidance. Impact is catastrophically diluted; necessary bridges for a developer to take immediate action are severed, and internal logic between resource-matching and layout-action mapping fragments, leaving the enhancement as an amorphous wishlist with no prescriptive pathway. The overall coherence is degraded due to the omission of scaffolding (API reference, event triggers, concrete examples), and the rambling generalizations fail to maintain the contextual backbone essential for plugin specification. Justification for the low score is rooted in the systematic erasure of domain relevance, actionable clarity, and the introduction of abstraction noise without any gain in precision or utility.\",\n  \"alternative_enhancements\": [\n    \"Retain the original's explicit references to Sublime Text, relevant API points, and event-driven operational triggers, while summarizing the ruleset mechanism and standard enforcement process in clearer, concise language.\",\n    \"Supplement the original prompt with a brief preamble defining core domain concepts ('layout', 'tabs', '.sublime-project'), then detail the configuration rules with explicit mapping from patterns to layout indices, ensuring operational clarity and concrete implementation anchors.\",\n    \"Combine original examples with an abstracted flowchart or pseudocode step that makes the intended behavior unambiguous, specifically referencing Sublime Text's workflow and retaining high-fidelity jargon for maximum developer alignment.\"\n  ]\n}",
        }
      }
    }