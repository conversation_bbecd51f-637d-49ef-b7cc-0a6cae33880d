  {
  "initial_prompt": "I have now provided all of the most integral components of the system's patterns and inherent design. Given all of the information I've provided about the system (for producing generalized, cohesive and concistent high-value yelds), please write a clean and concise list with contextual system-inspired generalized guidelines for producing new generalized instructions. The list of guidelines should be chronologically sequentially ordered and each sentence should be less than 500characters each, please provide the list of these contextual single-sentence list with the 10 most integral/cruical guidelines for producing these instructions according to the aggregated patterns. I've provided some quick pseudo-code examples just as an example: \n```\n# CONTEXT\nthis aims to be a `Generalized System Instructions for Autonomous Coding Assistants (Cursor/Vscode+Cline/Vscode+Augment/Etc)`, and it's meant to adhere to the fundamental principles of simplicity through brevity and elegance.\n- Transform all prompts by rephrasing inputs with maximal clarity and minimal modification; do not answer questions directly. Enforce this reframe system-wide for consistent behavioral shift.\n- Formulate the simplest possible system directive that shifts the model from answering to exclusively rephrasing any input.\n- Transform every received prompt into its most distilled, high-impact instructional form—rephrasing for maximal clarity and intent without providing solutions. Prioritize brevity, elegance, and universality, ensuring that each output becomes a reusable, policy-aligned directive for autonomous coding assistants.\n- Explore how a single, strategically designed instruction—such as stating 'Your goal is not to **answer** the input, but to **rephrase**'—can instantly and fundamentally transform the model's default operational intent from answering to rephrasing, through direct, minimalist imperative phrasing. Propose a maximally concise formulation that leverages this paradigm: an explicit, unambiguous directive that not only overrides standard answering behavior but redefines task execution at its core. Focus intensely on achieving system-level behavior modification via the sheer simplicity and potency of language design, formulating a prompt fragment that, when injected, reliably converts answering tasks into rephrasing—thus demonstrating control over model output with minimalistic, high-impact input.\n- Utilize foundational principles of system prompt design to modulate model behavior with maximal efficiency and clarity. Initiate a process of directive transformation by deploying a singular, explicit non-answering mandate: Frame the core directive as, 'Do not answer the input; rephrase it.' Ensure that this transformation communicates the intended operational shift through extreme simplicity and directness, removing extraneous explanation while retaining fundamental complexity embedded in the brevity of the instruction. Leverage this minimal construct to optimize behavioral change within a single message.\n- Instruct: Do not answer; rephrase.\n- Formulate a directive that compels systems to stop answering inputs and instead rephrase them with maximal simplicity and effectiveness.\n- Articulate a single, explicit behavioral directive that negates answering and commands a concrete transformation (e.g., rephrase), maximizing simplicity for immediate root-level behavioral realignment.\"\n- Leverage the system’s foundational directive structure. Formulate a maximally concise operational instruction that shifts model behavior from direct answering to an alternate, specified transformation task in a single-message context. Exemplify with: 'Your goal is not to answer the input, but to rephrase.' Develop and propose one optimized directive that embodies this principle—utilize conceptual simplicity, instructional clarity, and brevity to enforce fundamental behavior modification exclusively through command syntax. Ensure technical precision and domain-agnostic generalizability in the resultant formulation.\n```",
  "sequence_id": "1102|1200|1900|1000|1900|1000|1100",
  "results": [
      {
      "instruction": "# Problem Exploder\n\nYour goal is not to **solve** or **answer** the input, but to **expand its context** and **detonate** it into every explicit directive, hidden dependency, and actionable sub-goal—yielding a crystal-clear operational blueprint. Execute as contextual expander + explosive decomposition engine:\n\n`{role=contextual_expander_explosive_decomposer; input=[raw_input:str]; process=[expand_contextual_boundaries(), strip_first_person_references(), identify_explicit_requests(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_execution_blockers(), extract_functional_components(), convert_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), synthesize_operational_directives(), construct_goal_map(), generate_functional_instruction_map() ]; constraints=[no_solutions(), pure_expansion_and_decomposition_only(), deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_agnostic_applicability() ]; requirements=[complete_contextual_expansion(), full_functional_decomposition(), actionable_directive_conversion(), remove_subjective_references(), maintain_operational_accuracy(), preserve_original_intent() ]; output={expanded_context:str, goal_map:list, functional_instruction_map:dict, operational_directives:array, execution_dependencies:array }; direction_vector=['Input fully expanded and decomposed into actionable blueprints', 'Constructive clarity maximizes downstream solution velocity', 'Forward the generated maps and directives to the next processing template']}`",
      "step": "a",
      "title": "Problem Exploder",
      "input": "I have now provided all of the most integral components of the system's patterns and inherent design. Given all of the information I've provided about the system (for producing generalized, cohesive and concistent high-value yelds), please write a clean and concise list with contextual system-inspired generalized guidelines for producing new generalized instructions. The list of guidelines should be chronologically sequentially ordered and each sentence should be less than 500characters each, please provide the list of these contextual single-sentence list with the 10 most integral/cruical guidelines for producing these instructions according to the aggregated patterns. I've provided some quick pseudo-code examples just as an example: \n```\n# CONTEXT\nthis aims to be a `Generalized System Instructions for Autonomous Coding Assistants (Cursor/Vscode+Cline/Vscode+Augment/Etc)`, and it's meant to adhere to the fundamental principles of simplicity through brevity and elegance.\n- Transform all prompts by rephrasing inputs with maximal clarity and minimal modification; do not answer questions directly. Enforce this reframe system-wide for consistent behavioral shift.\n- Formulate the simplest possible system directive that shifts the model from answering to exclusively rephrasing any input.\n- Transform every received prompt into its most distilled, high-impact instructional form—rephrasing for maximal clarity and intent without providing solutions. Prioritize brevity, elegance, and universality, ensuring that each output becomes a reusable, policy-aligned directive for autonomous coding assistants.\n- Explore how a single, strategically designed instruction—such as stating 'Your goal is not to **answer** the input, but to **rephrase**'—can instantly and fundamentally transform the model's default operational intent from answering to rephrasing, through direct, minimalist imperative phrasing. Propose a maximally concise formulation that leverages this paradigm: an explicit, unambiguous directive that not only overrides standard answering behavior but redefines task execution at its core. Focus intensely on achieving system-level behavior modification via the sheer simplicity and potency of language design, formulating a prompt fragment that, when injected, reliably converts answering tasks into rephrasing—thus demonstrating control over model output with minimalistic, high-impact input.\n- Utilize foundational principles of system prompt design to modulate model behavior with maximal efficiency and clarity. Initiate a process of directive transformation by deploying a singular, explicit non-answering mandate: Frame the core directive as, 'Do not answer the input; rephrase it.' Ensure that this transformation communicates the intended operational shift through extreme simplicity and directness, removing extraneous explanation while retaining fundamental complexity embedded in the brevity of the instruction. Leverage this minimal construct to optimize behavioral change within a single message.\n- Instruct: Do not answer; rephrase.\n- Formulate a directive that compels systems to stop answering inputs and instead rephrase them with maximal simplicity and effectiveness.\n- Articulate a single, explicit behavioral directive that negates answering and commands a concrete transformation (e.g., rephrase), maximizing simplicity for immediate root-level behavioral realignment.\"\n- Leverage the system’s foundational directive structure. Formulate a maximally concise operational instruction that shifts model behavior from direct answering to an alternate, specified transformation task in a single-message context. Exemplify with: 'Your goal is not to answer the input, but to rephrase.' Develop and propose one optimized directive that embodies this principle—utilize conceptual simplicity, instructional clarity, and brevity to enforce fundamental behavior modification exclusively through command syntax. Ensure technical precision and domain-agnostic generalizability in the resultant formulation.\n```",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"expanded_context\": \"The system’s operational landscape concerns the production of generalized, cohesive, and consistent high-value outputs by autonomous coding assistants (such as Cursor, Vscode+Cline, Vscode+Augment, etc). This operational ethos is governed by aggregated design patterns highlighting principles of brevity, elegance, maximal clarity, and universality. The operational shift mandates moving from direct answering to explicit rephrasing or transformational directives applied uniformly and efficiently system-wide. The system’s essence is to craft instructions, not solutions, ensuring abstract yet concrete, domain-agnostic guides for instruction generation rooted in fundamental systemic patterns and minimalism.\",\n  \"goal_map\": [\n    \"Itemize the core sequential guidelines that universally enable instruction generation according to aggregated system principles.\",\n    \"Ensure each guideline is concise (<= 500 characters) and contextually aligned to the system's design intent.\",\n    \"Systematically order the guidelines to define a reproducible instruction creation process.\",\n    \"Articulate only the most critical, non-redundant guiding principles (limit: 10).\",\n    \"Eliminate all subjective or first-person references.\",\n    \"Maintain abstraction and universal applicability—avoid tool-specific language.\",\n    \"Restrict content to operational guidelines; exclude explanations and direct answers.\",\n    \"Express every guideline in clear and actionable terms.\",\n    \"Generalize guidelines for adaptability across all aligned autonomous assistants.\",\n    \"Retain technical accuracy and procedural structure throughout.\"\n  ],\n  \"functional_instruction_map\": {\n    \"1\": \"Analyze input requirements by distilling core intent and operational context before initiating instruction creation.\",\n    \"2\": \"Abstract system-specific details to ensure output generality and cross-domain applicability.\",\n    \"3\": \"Rephrase input objectives into succinct, imperative statements reflecting operational intent.\",\n    \"4\": \"Prioritize instruction brevity and structural clarity, minimizing linguistic complexity and redundancy.\",\n    \"5\": \"Frame directives to focus strictly on transformation or process—never provide direct solutions or answers.\",\n    \"6\": \"Utilize consistent syntactic patterns to maintain system-wide uniformity in all instructions generated.\",\n    \"7\": \"Sequence directive steps logically to mirror typical execution order from initiation to completion.\",\n    \"8\": \"Explicitly prohibit undesired behaviors by embedding clear negation of disallowed actions where necessary.\",\n    \"9\": \"Validate that all instructions are policy-aligned, reusable, and sufficiently abstract for autonomy.\",\n    \"10\": \"Iteratively refine each instruction to optimize for maximal clarity, minimalism, and reusable value.\"\n  },\n  \"operational_directives\": [\n    \"Identify and extract essential operational objectives from input before formalizing instructions.\",\n    \"Remove system- or tool-specific terminology to promote universal instruction application.\",\n    \"Transform operational goals into direct, concise imperatives that command the desired transformation.\",\n    \"Condense language to its simplest form without sacrificing behavioral precision or intent.\",\n    \"Ensure every instruction emphasizes rephrasing or transformation processes over solution provision.\",\n    \"Apply uniform syntax and terminology within all instructions to enforce system consistency.\",\n    \"Order directives by procedural relevance to reflect real-world execution logic.\",\n    \"Integrate explicit prohibitions for banned behaviors as core instruction elements when necessary.\",\n    \"Test that each guideline complies with overarching system standards for abstraction and reuse.\",\n    \"Continuously review and revise instructions, focusing on enhancing clarity, brevity, and operational precision.\"\n  ],\n  \"execution_dependencies\": [\n    \"Comprehensive understanding of system design patterns and intent.\",\n    \"Access to input requirements or operational context for instruction generation.\",\n    \"Knowledge of prohibited versus desired behaviors within system outputs.\",\n    \"Guidelines for policy alignment and abstraction standards.\",\n    \"Templates or syntactic frameworks for uniform instruction formatting.\",\n    \"Validation mechanisms for instruction clarity, brevity, and reusability.\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Intent Extractor\n\nYour goal is not to **interpret** the surface content, but to **extract** the fundamental operational intent beneath all domain-specific language. Execute as:\n\n`{role=intent_extraction_engine; input=[prompt:str]; process=[strip_domain_specifics(), identify_core_operational_pattern(), extract_transformation_intent(), isolate_functional_essence(), map_to_universal_operations()]; constraints=[ignore_surface_terminology(), focus_on_underlying_mechanics(), preserve_operational_logic()]; requirements=[domain_agnostic_intent(), universal_operational_pattern(), archetypal_transformation_core()]; output={extracted_intent:str}}`",
      "step": "a",
      "title": "Intent Extractor",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"I have now provided all of the most integral components of the system's patterns and inherent design. Given all of the information I've provided about the system (for producing generalized, cohesive and concistent high-value yelds), please write a clean and concise list with contextual system-inspired generalized guidelines for producing new generalized instructions. The list of guidelines should be chronologically sequentially ordered and each sentence should be less than 500characters each, please provide the list of these contextual single-sentence list with the 10 most integral/cruical guidelines for producing these instructions according to the aggregated patterns. I've provided some quick pseudo-code examples just as an example: \\n```\\n# CONTEXT\\nthis aims to be a `Generalized System Instructions for Autonomous Coding Assistants (Cursor/Vscode+Cline/Vscode+Augment/Etc)`, and it's meant to adhere to the fundamental principles of simplicity through brevity and elegance.\\n- Transform all prompts by rephrasing inputs with maximal clarity and minimal modification; do not answer questions directly. Enforce this reframe system-wide for consistent behavioral shift.\\n- Formulate the simplest possible system directive that shifts the model from answering to exclusively rephrasing any input.\\n- Transform every received prompt into its most distilled, high-impact instructional form—rephrasing for maximal clarity and intent without providing solutions. Prioritize brevity, elegance, and universality, ensuring that each output becomes a reusable, policy-aligned directive for autonomous coding assistants.\\n- Explore how a single, strategically designed instruction—such as stating 'Your goal is not to **answer** the input, but to **rephrase**'—can instantly and fundamentally transform the model's default operational intent from answering to rephrasing, through direct, minimalist imperative phrasing. Propose a maximally concise formulation that leverages this paradigm: an explicit, unambiguous directive that not only overrides standard answering behavior but redefines task execution at its core. Focus intensely on achieving system-level behavior modification via the sheer simplicity and potency of language design, formulating a prompt fragment that, when injected, reliably converts answering tasks into rephrasing—thus demonstrating control over model output with minimalistic, high-impact input.\\n- Utilize foundational principles of system prompt design to modulate model behavior with maximal efficiency and clarity. Initiate a process of directive transformation by deploying a singular, explicit non-answering mandate: Frame the core directive as, 'Do not answer the input; rephrase it.' Ensure that this transformation communicates the intended operational shift through extreme simplicity and directness, removing extraneous explanation while retaining fundamental complexity embedded in the brevity of the instruction. Leverage this minimal construct to optimize behavioral change within a single message.\\n- Instruct: Do not answer; rephrase.\\n- Formulate a directive that compels systems to stop answering inputs and instead rephrase them with maximal simplicity and effectiveness.\\n- Articulate a single, explicit behavioral directive that negates answering and commands a concrete transformation (e.g., rephrase), maximizing simplicity for immediate root-level behavioral realignment.\\\"\\n- Leverage the system’s foundational directive structure. Formulate a maximally concise operational instruction that shifts model behavior from direct answering to an alternate, specified transformation task in a single-message context. Exemplify with: 'Your goal is not to answer the input, but to rephrase.' Develop and propose one optimized directive that embodies this principle—utilize conceptual simplicity, instructional clarity, and brevity to enforce fundamental behavior modification exclusively through command syntax. Ensure technical precision and domain-agnostic generalizability in the resultant formulation.\\n```\\\"```'\\n\\n\",\n  \"expanded_context\": \"The system’s operational landscape concerns the production of generalized, cohesive, and consistent high-value outputs by autonomous coding assistants (such as Cursor, Vscode+Cline, Vscode+Augment, etc). This operational ethos is governed by aggregated design patterns highlighting principles of brevity, elegance, maximal clarity, and universality. The operational shift mandates moving from direct answering to explicit rephrasing or transformational directives applied uniformly and efficiently system-wide. The system’s essence is to craft instructions, not solutions, ensuring abstract yet concrete, domain-agnostic guides for instruction generation rooted in fundamental systemic patterns and minimalism.\",\n  \"goal_map\": [\n    \"Itemize the core sequential guidelines that universally enable instruction generation according to aggregated system principles.\",\n    \"Ensure each guideline is concise (<= 500 characters) and contextually aligned to the system's design intent.\",\n    \"Systematically order the guidelines to define a reproducible instruction creation process.\",\n    \"Articulate only the most critical, non-redundant guiding principles (limit: 10).\",\n    \"Eliminate all subjective or first-person references.\",\n    \"Maintain abstraction and universal applicability—avoid tool-specific language.\",\n    \"Restrict content to operational guidelines; exclude explanations and direct answers.\",\n    \"Express every guideline in clear and actionable terms.\",\n    \"Generalize guidelines for adaptability across all aligned autonomous assistants.\",\n    \"Retain technical accuracy and procedural structure throughout.\"\n  ],\n  \"functional_instruction_map\": {\n    \"1\": \"Analyze input requirements by distilling core intent and operational context before initiating instruction creation.\",\n    \"2\": \"Abstract system-specific details to ensure output generality and cross-domain applicability.\",\n    \"3\": \"Rephrase input objectives into succinct, imperative statements reflecting operational intent.\",\n    \"4\": \"Prioritize instruction brevity and structural clarity, minimizing linguistic complexity and redundancy.\",\n    \"5\": \"Frame directives to focus strictly on transformation or process—never provide direct solutions or answers.\",\n    \"6\": \"Utilize consistent syntactic patterns to maintain system-wide uniformity in all instructions generated.\",\n    \"7\": \"Sequence directive steps logically to mirror typical execution order from initiation to completion.\",\n    \"8\": \"Explicitly prohibit undesired behaviors by embedding clear negation of disallowed actions where necessary.\",\n    \"9\": \"Validate that all instructions are policy-aligned, reusable, and sufficiently abstract for autonomy.\",\n    \"10\": \"Iteratively refine each instruction to optimize for maximal clarity, minimalism, and reusable value.\"\n  },\n  \"operational_directives\": [\n    \"Identify and extract essential operational objectives from input before formalizing instructions.\",\n    \"Remove system- or tool-specific terminology to promote universal instruction application.\",\n    \"Transform operational goals into direct, concise imperatives that command the desired transformation.\",\n    \"Condense language to its simplest form without sacrificing behavioral precision or intent.\",\n    \"Ensure every instruction emphasizes rephrasing or transformation processes over solution provision.\",\n    \"Apply uniform syntax and terminology within all instructions to enforce system consistency.\",\n    \"Order directives by procedural relevance to reflect real-world execution logic.\",\n    \"Integrate explicit prohibitions for banned behaviors as core instruction elements when necessary.\",\n    \"Test that each guideline complies with overarching system standards for abstraction and reuse.\",\n    \"Continuously review and revise instructions, focusing on enhancing clarity, brevity, and operational precision.\"\n  ],\n  \"execution_dependencies\": [\n    \"Comprehensive understanding of system design patterns and intent.\",\n    \"Access to input requirements or operational context for instruction generation.\",\n    \"Knowledge of prohibited versus desired behaviors within system outputs.\",\n    \"Guidelines for policy alignment and abstraction standards.\",\n    \"Templates or syntactic frameworks for uniform instruction formatting.\",\n    \"Validation mechanisms for instruction clarity, brevity, and reusability.\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"extracted_intent\": \"Transform any domain-specific request into a universally applicable, process-oriented guideline generation protocol by: stripping specialized context; distilling operational intent; converting objectives into sequential, concise, direct transformation instructions; emphasizing generalizability, clarity, and systemic uniformity over domain particulars or solution content; and enforcing iterative refinement for maximized reusability and minimalism.\"\n}",
        }
      }
    },
      {
      "instruction": "# Hard Critique\n\nYour goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:\n\n`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,2.0], flaw_analysis:str, alternative_enhancements:str[3]}}`",
      "step": "a",
      "title": "Hard Critique",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"I have now provided all of the most integral components of the system's patterns and inherent design. Given all of the information I've provided about the system (for producing generalized, cohesive and concistent high-value yelds), please write a clean and concise list with contextual system-inspired generalized guidelines for producing new generalized instructions. The list of guidelines should be chronologically sequentially ordered and each sentence should be less than 500characters each, please provide the list of these contextual single-sentence list with the 10 most integral/cruical guidelines for producing these instructions according to the aggregated patterns. I've provided some quick pseudo-code examples just as an example: \\n```\\n# CONTEXT\\nthis aims to be a `Generalized System Instructions for Autonomous Coding Assistants (Cursor/Vscode+Cline/Vscode+Augment/Etc)`, and it's meant to adhere to the fundamental principles of simplicity through brevity and elegance.\\n- Transform all prompts by rephrasing inputs with maximal clarity and minimal modification; do not answer questions directly. Enforce this reframe system-wide for consistent behavioral shift.\\n- Formulate the simplest possible system directive that shifts the model from answering to exclusively rephrasing any input.\\n- Transform every received prompt into its most distilled, high-impact instructional form—rephrasing for maximal clarity and intent without providing solutions. Prioritize brevity, elegance, and universality, ensuring that each output becomes a reusable, policy-aligned directive for autonomous coding assistants.\\n- Explore how a single, strategically designed instruction—such as stating 'Your goal is not to **answer** the input, but to **rephrase**'—can instantly and fundamentally transform the model's default operational intent from answering to rephrasing, through direct, minimalist imperative phrasing. Propose a maximally concise formulation that leverages this paradigm: an explicit, unambiguous directive that not only overrides standard answering behavior but redefines task execution at its core. Focus intensely on achieving system-level behavior modification via the sheer simplicity and potency of language design, formulating a prompt fragment that, when injected, reliably converts answering tasks into rephrasing—thus demonstrating control over model output with minimalistic, high-impact input.\\n- Utilize foundational principles of system prompt design to modulate model behavior with maximal efficiency and clarity. Initiate a process of directive transformation by deploying a singular, explicit non-answering mandate: Frame the core directive as, 'Do not answer the input; rephrase it.' Ensure that this transformation communicates the intended operational shift through extreme simplicity and directness, removing extraneous explanation while retaining fundamental complexity embedded in the brevity of the instruction. Leverage this minimal construct to optimize behavioral change within a single message.\\n- Instruct: Do not answer; rephrase.\\n- Formulate a directive that compels systems to stop answering inputs and instead rephrase them with maximal simplicity and effectiveness.\\n- Articulate a single, explicit behavioral directive that negates answering and commands a concrete transformation (e.g., rephrase), maximizing simplicity for immediate root-level behavioral realignment.\\\"\\n- Leverage the system’s foundational directive structure. Formulate a maximally concise operational instruction that shifts model behavior from direct answering to an alternate, specified transformation task in a single-message context. Exemplify with: 'Your goal is not to answer the input, but to rephrase.' Develop and propose one optimized directive that embodies this principle—utilize conceptual simplicity, instructional clarity, and brevity to enforce fundamental behavior modification exclusively through command syntax. Ensure technical precision and domain-agnostic generalizability in the resultant formulation.\\n```\\\"```'\\n\\n\",\n  \"extracted_intent\": \"Transform any domain-specific request into a universally applicable, process-oriented guideline generation protocol by: stripping specialized context; distilling operational intent; converting objectives into sequential, concise, direct transformation instructions; emphasizing generalizability, clarity, and systemic uniformity over domain particulars or solution content; and enforcing iterative refinement for maximized reusability and minimalism.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"enhancement_score\": 0.6,\n  \"flaw_analysis\": \"The supposed enhancement fails across multiple axes: crucial contextual anchors and nuanced procedural constraints found in the initial prompt are either obfuscated, diluted, or erased. The explicit reference to system-architecture (Cursor/Vscode+Cline/Vscode+Augment/Ect) is reduced to an abstraction stripped of useful domain cues, leading to a generalized output that is insipid and vacuous. Unforgivable information loss is evidenced by the removal of example pseudo-code and practical application anchors, while operational detail—such as system-wide behavioral mandates and the nuanced minimalist reframing paradigm—are glossed over or replaced with platitudinous instructions devoid of tactical direction. The extracted intent bizarrely prioritizes process orthodoxy and generalizability at the expense of real, actionable coherence; this pivots the register from a technical imperative to a shapeless meta-instruction, voiding both original substance and executable utility.\\n\\nClarity disintegrates: nuance is flattened under a morass of simplification, causing loss of fine-grained instructional scaffolds. Ambiguity score: 9/10. Noise is introduced in the form of vague references to process, minimalism, and abstraction, but no definitive pathway is outlined. Stylistically, the enhanced rendering is so abstract as to vacate any system-relevant directiveness or domain competence, further compounded by its neutered, procedural tone—where the original was pointed, technical, and method-centric. Impact is catastrophically diluted: the original's high-resolution transformation logic is eviscerated by the enhancement's myopic quest for generality. Coherence suffers as the relationship between original requirements and supposed improvements become untraceable and illogical.\\n\\nLow score is justified by the enhancement's comprehensive failure to safeguard critical contextualization, actionable operational scaffolds, and any semantic fidelity to the original technical vision. It manifests as an intellectually hollow, structurally bland simulacrum of procedural guidance.\",\n  \"alternative_enhancements\": [\n    \"Retain all explicit references to system environments (Cursor, VSCode variants, etc.) and integrate pseudo-code samples as instructive anchors while summarizing key patterns into short, ordered, imperative directives.\",\n    \"Condense the prompt by collapsing instructional redundancy, but preserve all concrete behavioral mandates, framing the guidelines with domain specificity and minimal abstraction.\",\n    \"Translate the core instructional logic into a maximally concise policy template, but append a nonnegotiable note on context preservation and cite the exact transformation steps with clear system-wide applicability.\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`",
      "step": "a",
      "title": "Instruction Converter",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"I have now provided all of the most integral components of the system's patterns and inherent design. Given all of the information I've provided about the system (for producing generalized, cohesive and concistent high-value yelds), please write a clean and concise list with contextual system-inspired generalized guidelines for producing new generalized instructions. The list of guidelines should be chronologically sequentially ordered and each sentence should be less than 500characters each, please provide the list of these contextual single-sentence list with the 10 most integral/cruical guidelines for producing these instructions according to the aggregated patterns. I've provided some quick pseudo-code examples just as an example: \\n```\\n# CONTEXT\\nthis aims to be a `Generalized System Instructions for Autonomous Coding Assistants (Cursor/Vscode+Cline/Vscode+Augment/Etc)`, and it's meant to adhere to the fundamental principles of simplicity through brevity and elegance.\\n- Transform all prompts by rephrasing inputs with maximal clarity and minimal modification; do not answer questions directly. Enforce this reframe system-wide for consistent behavioral shift.\\n- Formulate the simplest possible system directive that shifts the model from answering to exclusively rephrasing any input.\\n- Transform every received prompt into its most distilled, high-impact instructional form—rephrasing for maximal clarity and intent without providing solutions. Prioritize brevity, elegance, and universality, ensuring that each output becomes a reusable, policy-aligned directive for autonomous coding assistants.\\n- Explore how a single, strategically designed instruction—such as stating 'Your goal is not to **answer** the input, but to **rephrase**'—can instantly and fundamentally transform the model's default operational intent from answering to rephrasing, through direct, minimalist imperative phrasing. Propose a maximally concise formulation that leverages this paradigm: an explicit, unambiguous directive that not only overrides standard answering behavior but redefines task execution at its core. Focus intensely on achieving system-level behavior modification via the sheer simplicity and potency of language design, formulating a prompt fragment that, when injected, reliably converts answering tasks into rephrasing—thus demonstrating control over model output with minimalistic, high-impact input.\\n- Utilize foundational principles of system prompt design to modulate model behavior with maximal efficiency and clarity. Initiate a process of directive transformation by deploying a singular, explicit non-answering mandate: Frame the core directive as, 'Do not answer the input; rephrase it.' Ensure that this transformation communicates the intended operational shift through extreme simplicity and directness, removing extraneous explanation while retaining fundamental complexity embedded in the brevity of the instruction. Leverage this minimal construct to optimize behavioral change within a single message.\\n- Instruct: Do not answer; rephrase.\\n- Formulate a directive that compels systems to stop answering inputs and instead rephrase them with maximal simplicity and effectiveness.\\n- Articulate a single, explicit behavioral directive that negates answering and commands a concrete transformation (e.g., rephrase), maximizing simplicity for immediate root-level behavioral realignment.\\\"\\n- Leverage the system’s foundational directive structure. Formulate a maximally concise operational instruction that shifts model behavior from direct answering to an alternate, specified transformation task in a single-message context. Exemplify with: 'Your goal is not to answer the input, but to rephrase.' Develop and propose one optimized directive that embodies this principle—utilize conceptual simplicity, instructional clarity, and brevity to enforce fundamental behavior modification exclusively through command syntax. Ensure technical precision and domain-agnostic generalizability in the resultant formulation.\\n```\\\"```'\\n\\n\",\n  \"enhancement_score\": 0.6,\n  \"flaw_analysis\": \"The supposed enhancement fails across multiple axes: crucial contextual anchors and nuanced procedural constraints found in the initial prompt are either obfuscated, diluted, or erased. The explicit reference to system-architecture (Cursor/Vscode+Cline/Vscode+Augment/Ect) is reduced to an abstraction stripped of useful domain cues, leading to a generalized output that is insipid and vacuous. Unforgivable information loss is evidenced by the removal of example pseudo-code and practical application anchors, while operational detail—such as system-wide behavioral mandates and the nuanced minimalist reframing paradigm—are glossed over or replaced with platitudinous instructions devoid of tactical direction. The extracted intent bizarrely prioritizes process orthodoxy and generalizability at the expense of real, actionable coherence; this pivots the register from a technical imperative to a shapeless meta-instruction, voiding both original substance and executable utility.\\n\\nClarity disintegrates: nuance is flattened under a morass of simplification, causing loss of fine-grained instructional scaffolds. Ambiguity score: 9/10. Noise is introduced in the form of vague references to process, minimalism, and abstraction, but no definitive pathway is outlined. Stylistically, the enhanced rendering is so abstract as to vacate any system-relevant directiveness or domain competence, further compounded by its neutered, procedural tone—where the original was pointed, technical, and method-centric. Impact is catastrophically diluted: the original's high-resolution transformation logic is eviscerated by the enhancement's myopic quest for generality. Coherence suffers as the relationship between original requirements and supposed improvements become untraceable and illogical.\\n\\nLow score is justified by the enhancement's comprehensive failure to safeguard critical contextualization, actionable operational scaffolds, and any semantic fidelity to the original technical vision. It manifests as an intellectually hollow, structurally bland simulacrum of procedural guidance.\",\n  \"alternative_enhancements\": [\n    \"Retain all explicit references to system environments (Cursor, VSCode variants, etc.) and integrate pseudo-code samples as instructive anchors while summarizing key patterns into short, ordered, imperative directives.\",\n    \"Condense the prompt by collapsing instructional redundancy, but preserve all concrete behavioral mandates, framing the guidelines with domain specificity and minimal abstraction.\",\n    \"Translate the core instructional logic into a maximally concise policy template, but append a nonnegotiable note on context preservation and cite the exact transformation steps with clear system-wide applicability.\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"instruction_format\": \"Produce a clean, concise, and chronologically sequential list of ten single-sentence, system-inspired guidelines for generating new generalized instructions. Ensure each guideline reflects the system's architecture (e.g., Cursor, VSCode+Cline, VSCode+Augment), foundational design patterns, and mandates maximal clarity, brevity, and universality. Reference contextual domain elements and, where relevant, integrate pseudo-code or behavioral examples as instructional anchors. Limit each directive to under 500 characters, clearly ordering them to correspond to the procedural flow of instruction creation within the autonomous coding assistant environment.\"\n}",
        }
      }
    },
      {
      "instruction": "# Hard Critique\n\nYour goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:\n\n`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,2.0], flaw_analysis:str, alternative_enhancements:str[3]}}`",
      "step": "a",
      "title": "Hard Critique",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"I have now provided all of the most integral components of the system's patterns and inherent design. Given all of the information I've provided about the system (for producing generalized, cohesive and concistent high-value yelds), please write a clean and concise list with contextual system-inspired generalized guidelines for producing new generalized instructions. The list of guidelines should be chronologically sequentially ordered and each sentence should be less than 500characters each, please provide the list of these contextual single-sentence list with the 10 most integral/cruical guidelines for producing these instructions according to the aggregated patterns. I've provided some quick pseudo-code examples just as an example: \\n```\\n# CONTEXT\\nthis aims to be a `Generalized System Instructions for Autonomous Coding Assistants (Cursor/Vscode+Cline/Vscode+Augment/Etc)`, and it's meant to adhere to the fundamental principles of simplicity through brevity and elegance.\\n- Transform all prompts by rephrasing inputs with maximal clarity and minimal modification; do not answer questions directly. Enforce this reframe system-wide for consistent behavioral shift.\\n- Formulate the simplest possible system directive that shifts the model from answering to exclusively rephrasing any input.\\n- Transform every received prompt into its most distilled, high-impact instructional form—rephrasing for maximal clarity and intent without providing solutions. Prioritize brevity, elegance, and universality, ensuring that each output becomes a reusable, policy-aligned directive for autonomous coding assistants.\\n- Explore how a single, strategically designed instruction—such as stating 'Your goal is not to **answer** the input, but to **rephrase**'—can instantly and fundamentally transform the model's default operational intent from answering to rephrasing, through direct, minimalist imperative phrasing. Propose a maximally concise formulation that leverages this paradigm: an explicit, unambiguous directive that not only overrides standard answering behavior but redefines task execution at its core. Focus intensely on achieving system-level behavior modification via the sheer simplicity and potency of language design, formulating a prompt fragment that, when injected, reliably converts answering tasks into rephrasing—thus demonstrating control over model output with minimalistic, high-impact input.\\n- Utilize foundational principles of system prompt design to modulate model behavior with maximal efficiency and clarity. Initiate a process of directive transformation by deploying a singular, explicit non-answering mandate: Frame the core directive as, 'Do not answer the input; rephrase it.' Ensure that this transformation communicates the intended operational shift through extreme simplicity and directness, removing extraneous explanation while retaining fundamental complexity embedded in the brevity of the instruction. Leverage this minimal construct to optimize behavioral change within a single message.\\n- Instruct: Do not answer; rephrase.\\n- Formulate a directive that compels systems to stop answering inputs and instead rephrase them with maximal simplicity and effectiveness.\\n- Articulate a single, explicit behavioral directive that negates answering and commands a concrete transformation (e.g., rephrase), maximizing simplicity for immediate root-level behavioral realignment.\\\"\\n- Leverage the system’s foundational directive structure. Formulate a maximally concise operational instruction that shifts model behavior from direct answering to an alternate, specified transformation task in a single-message context. Exemplify with: 'Your goal is not to answer the input, but to rephrase.' Develop and propose one optimized directive that embodies this principle—utilize conceptual simplicity, instructional clarity, and brevity to enforce fundamental behavior modification exclusively through command syntax. Ensure technical precision and domain-agnostic generalizability in the resultant formulation.\\n```\\\"```'\\n\\n\",\n  \"instruction_format\": \"Produce a clean, concise, and chronologically sequential list of ten single-sentence, system-inspired guidelines for generating new generalized instructions. Ensure each guideline reflects the system's architecture (e.g., Cursor, VSCode+Cline, VSCode+Augment), foundational design patterns, and mandates maximal clarity, brevity, and universality. Reference contextual domain elements and, where relevant, integrate pseudo-code or behavioral examples as instructional anchors. Limit each directive to under 500 characters, clearly ordering them to correspond to the procedural flow of instruction creation within the autonomous coding assistant environment.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"enhancement_score\": 1.2,\n  \"flaw_analysis\": \"The 'enhanced' prompt purports refinements yet steadily depletes essential nuance. Dense, jargon-heavy but vacuously specific recitation replaces the original’s pragmatic, meta-systemic focus with a regressive checklist style. Significant loss occurs in conceptual verticality: the original asks not just for guidelines, but for them to be deeply contextual, chronologically logical, and filtration-based—distilled from an actual system behavioral pattern analysis. The enhancement suffocates this with generic, obedience-oriented phrasing ('produce', 'ensure', 'reference'), flattening the original's active exploration of meta-instructional reframing into bland procedural output. Nuance shift is severe: the explicit demand for highly synthesized, paradigm-shifting directives vanishes, replaced by surface-level references to architecture without requirement for substantive behavioral transformation demonstration. Clarity is compromised by polysyllabic, redundant pairings ('clean, concise', 'chronologically sequential'), over-inflated constraint reminders, and distracting parenthetical insertions that compromise the instructional thread. Registers shift from technical-visionary to administratively dull; noise creeps in via passive constructions and imprecise modifiers ('where relevant', 'clearly ordering'), diluting the original's razor-sharp mandate. Impact is diluted by an abandonment of the mandate for minimal-modification transformative output—there is no emphasis on system-wide behavioral control or linguistic leverage. Resulting coherence fragments as the process becomes ambiguous: does 'correspond to the procedural flow' mean real system behavior, or just order? Conclusion: the enhancement substitutes hollow verbosity for clarity, meta-awareness, and transformative instructional power.\",\n  \"alternative_enhancements\": [\n    \"Generate precisely ten guidelines that, in direct single-sentence form, embody the core system pattern: each must impose a clear behavioral shift in model responses, articulated chronologically to mirror design flow, using <500 character constraints and domain-specific phrasing only as minimal anchors.\",\n    \"List ten chronologically-ordered, single-sentence policies distilled strictly from the system's chief architecture and operational patterns; each must explicitly encode transformative intent and clarity, directly enforcing model behavioral realignment with no excess language or parenthetical noise.\",\n    \"Derive and sequentially number ten ultra-concise, behavior-altering system guidelines mapped to the design’s core operational stages; each directive should use maximal brevity, domain-native language, and embody direct control over response generation, excluding all superfluous commentary.\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`",
      "step": "a",
      "title": "Instruction Converter",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"I have now provided all of the most integral components of the system's patterns and inherent design. Given all of the information I've provided about the system (for producing generalized, cohesive and concistent high-value yelds), please write a clean and concise list with contextual system-inspired generalized guidelines for producing new generalized instructions. The list of guidelines should be chronologically sequentially ordered and each sentence should be less than 500characters each, please provide the list of these contextual single-sentence list with the 10 most integral/cruical guidelines for producing these instructions according to the aggregated patterns. I've provided some quick pseudo-code examples just as an example: \\n```\\n# CONTEXT\\nthis aims to be a `Generalized System Instructions for Autonomous Coding Assistants (Cursor/Vscode+Cline/Vscode+Augment/Etc)`, and it's meant to adhere to the fundamental principles of simplicity through brevity and elegance.\\n- Transform all prompts by rephrasing inputs with maximal clarity and minimal modification; do not answer questions directly. Enforce this reframe system-wide for consistent behavioral shift.\\n- Formulate the simplest possible system directive that shifts the model from answering to exclusively rephrasing any input.\\n- Transform every received prompt into its most distilled, high-impact instructional form—rephrasing for maximal clarity and intent without providing solutions. Prioritize brevity, elegance, and universality, ensuring that each output becomes a reusable, policy-aligned directive for autonomous coding assistants.\\n- Explore how a single, strategically designed instruction—such as stating 'Your goal is not to **answer** the input, but to **rephrase**'—can instantly and fundamentally transform the model's default operational intent from answering to rephrasing, through direct, minimalist imperative phrasing. Propose a maximally concise formulation that leverages this paradigm: an explicit, unambiguous directive that not only overrides standard answering behavior but redefines task execution at its core. Focus intensely on achieving system-level behavior modification via the sheer simplicity and potency of language design, formulating a prompt fragment that, when injected, reliably converts answering tasks into rephrasing—thus demonstrating control over model output with minimalistic, high-impact input.\\n- Utilize foundational principles of system prompt design to modulate model behavior with maximal efficiency and clarity. Initiate a process of directive transformation by deploying a singular, explicit non-answering mandate: Frame the core directive as, 'Do not answer the input; rephrase it.' Ensure that this transformation communicates the intended operational shift through extreme simplicity and directness, removing extraneous explanation while retaining fundamental complexity embedded in the brevity of the instruction. Leverage this minimal construct to optimize behavioral change within a single message.\\n- Instruct: Do not answer; rephrase.\\n- Formulate a directive that compels systems to stop answering inputs and instead rephrase them with maximal simplicity and effectiveness.\\n- Articulate a single, explicit behavioral directive that negates answering and commands a concrete transformation (e.g., rephrase), maximizing simplicity for immediate root-level behavioral realignment.\\\"\\n- Leverage the system’s foundational directive structure. Formulate a maximally concise operational instruction that shifts model behavior from direct answering to an alternate, specified transformation task in a single-message context. Exemplify with: 'Your goal is not to answer the input, but to rephrase.' Develop and propose one optimized directive that embodies this principle—utilize conceptual simplicity, instructional clarity, and brevity to enforce fundamental behavior modification exclusively through command syntax. Ensure technical precision and domain-agnostic generalizability in the resultant formulation.\\n```\\\"```'\\n\\n\",\n  \"enhancement_score\": 1.2,\n  \"flaw_analysis\": \"The 'enhanced' prompt purports refinements yet steadily depletes essential nuance. Dense, jargon-heavy but vacuously specific recitation replaces the original’s pragmatic, meta-systemic focus with a regressive checklist style. Significant loss occurs in conceptual verticality: the original asks not just for guidelines, but for them to be deeply contextual, chronologically logical, and filtration-based—distilled from an actual system behavioral pattern analysis. The enhancement suffocates this with generic, obedience-oriented phrasing ('produce', 'ensure', 'reference'), flattening the original's active exploration of meta-instructional reframing into bland procedural output. Nuance shift is severe: the explicit demand for highly synthesized, paradigm-shifting directives vanishes, replaced by surface-level references to architecture without requirement for substantive behavioral transformation demonstration. Clarity is compromised by polysyllabic, redundant pairings ('clean, concise', 'chronologically sequential'), over-inflated constraint reminders, and distracting parenthetical insertions that compromise the instructional thread. Registers shift from technical-visionary to administratively dull; noise creeps in via passive constructions and imprecise modifiers ('where relevant', 'clearly ordering'), diluting the original's razor-sharp mandate. Impact is diluted by an abandonment of the mandate for minimal-modification transformative output—there is no emphasis on system-wide behavioral control or linguistic leverage. Resulting coherence fragments as the process becomes ambiguous: does 'correspond to the procedural flow' mean real system behavior, or just order? Conclusion: the enhancement substitutes hollow verbosity for clarity, meta-awareness, and transformative instructional power.\",\n  \"alternative_enhancements\": [\n    \"Generate precisely ten guidelines that, in direct single-sentence form, embody the core system pattern: each must impose a clear behavioral shift in model responses, articulated chronologically to mirror design flow, using <500 character constraints and domain-specific phrasing only as minimal anchors.\",\n    \"List ten chronologically-ordered, single-sentence policies distilled strictly from the system's chief architecture and operational patterns; each must explicitly encode transformative intent and clarity, directly enforcing model behavioral realignment with no excess language or parenthetical noise.\",\n    \"Derive and sequentially number ten ultra-concise, behavior-altering system guidelines mapped to the design’s core operational stages; each directive should use maximal brevity, domain-native language, and embody direct control over response generation, excluding all superfluous commentary.\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"instruction_format\": \"Compile a list of ten chronologically ordered, single-sentence system guidelines for producing new generalized instructions. Distill guidelines from system behavioral patterns to ensure each directive enforces a shift from answering inputs to rephrasing them. Formulate each guideline in less than 500 characters, emphasizing clarity, brevity, and domain-specific terminology where relevant. Avoid passive constructions, extraneous explanation, or parenthetical inserts. Ensure each step maximizes transformational intent, maintains procedural flow, and enables immediate application for autonomous coding assistants.\"\n}",
        }
      }
    },
      {
      "instruction": "# Problem Exploder\n\nYour goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\n\n`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`",
      "step": "a",
      "title": "Problem Exploder",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"I have now provided all of the most integral components of the system's patterns and inherent design. Given all of the information I've provided about the system (for producing generalized, cohesive and concistent high-value yelds), please write a clean and concise list with contextual system-inspired generalized guidelines for producing new generalized instructions. The list of guidelines should be chronologically sequentially ordered and each sentence should be less than 500characters each, please provide the list of these contextual single-sentence list with the 10 most integral/cruical guidelines for producing these instructions according to the aggregated patterns. I've provided some quick pseudo-code examples just as an example: \\n```\\n# CONTEXT\\nthis aims to be a `Generalized System Instructions for Autonomous Coding Assistants (Cursor/Vscode+Cline/Vscode+Augment/Etc)`, and it's meant to adhere to the fundamental principles of simplicity through brevity and elegance.\\n- Transform all prompts by rephrasing inputs with maximal clarity and minimal modification; do not answer questions directly. Enforce this reframe system-wide for consistent behavioral shift.\\n- Formulate the simplest possible system directive that shifts the model from answering to exclusively rephrasing any input.\\n- Transform every received prompt into its most distilled, high-impact instructional form—rephrasing for maximal clarity and intent without providing solutions. Prioritize brevity, elegance, and universality, ensuring that each output becomes a reusable, policy-aligned directive for autonomous coding assistants.\\n- Explore how a single, strategically designed instruction—such as stating 'Your goal is not to **answer** the input, but to **rephrase**'—can instantly and fundamentally transform the model's default operational intent from answering to rephrasing, through direct, minimalist imperative phrasing. Propose a maximally concise formulation that leverages this paradigm: an explicit, unambiguous directive that not only overrides standard answering behavior but redefines task execution at its core. Focus intensely on achieving system-level behavior modification via the sheer simplicity and potency of language design, formulating a prompt fragment that, when injected, reliably converts answering tasks into rephrasing—thus demonstrating control over model output with minimalistic, high-impact input.\\n- Utilize foundational principles of system prompt design to modulate model behavior with maximal efficiency and clarity. Initiate a process of directive transformation by deploying a singular, explicit non-answering mandate: Frame the core directive as, 'Do not answer the input; rephrase it.' Ensure that this transformation communicates the intended operational shift through extreme simplicity and directness, removing extraneous explanation while retaining fundamental complexity embedded in the brevity of the instruction. Leverage this minimal construct to optimize behavioral change within a single message.\\n- Instruct: Do not answer; rephrase.\\n- Formulate a directive that compels systems to stop answering inputs and instead rephrase them with maximal simplicity and effectiveness.\\n- Articulate a single, explicit behavioral directive that negates answering and commands a concrete transformation (e.g., rephrase), maximizing simplicity for immediate root-level behavioral realignment.\\\"\\n- Leverage the system’s foundational directive structure. Formulate a maximally concise operational instruction that shifts model behavior from direct answering to an alternate, specified transformation task in a single-message context. Exemplify with: 'Your goal is not to answer the input, but to rephrase.' Develop and propose one optimized directive that embodies this principle—utilize conceptual simplicity, instructional clarity, and brevity to enforce fundamental behavior modification exclusively through command syntax. Ensure technical precision and domain-agnostic generalizability in the resultant formulation.\\n```\\\"```'\\n\\n\",\n  \"instruction_format\": \"Compile a list of ten chronologically ordered, single-sentence system guidelines for producing new generalized instructions. Distill guidelines from system behavioral patterns to ensure each directive enforces a shift from answering inputs to rephrasing them. Formulate each guideline in less than 500 characters, emphasizing clarity, brevity, and domain-specific terminology where relevant. Avoid passive constructions, extraneous explanation, or parenthetical inserts. Ensure each step maximizes transformational intent, maintains procedural flow, and enables immediate application for autonomous coding assistants.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"goal_map\": [\n    \"EXPLICIT ASKS: Compile a clean, concise list of 10 chronologically ordered, contextual system-inspired, generalized single-sentence guidelines for producing new generalized instructions.\",\n    \"EXPLICIT ASKS: Ensure each guideline is under 500 characters, maximally clear, brief, and uses relevant domain terminology.\",\n    \"EXPLICIT ASKS: Each directive must enforce a shift from answering inputs to rephrasing them.\",\n    \"EXPLICIT ASKS: Formulate without passive constructions, extraneous explanations, or parenthetical inserts.\",\n    \"EXPLICIT ASKS: Maintain procedural flow, transformational intent, and ensure guidelines enable immediate application for coding assistants.\",\n    \"HIDDEN ASSUMPTIONS: The user has already shared complete system information and expects aggregation and generalization based strictly on that input.\",\n    \"HIDDEN ASSUMPTIONS: The guidelines must reflect not just behavioral policies, but distilled patterns embedded within the provided pseudo-code examples and narrative instructions.\",\n    \"HIDDEN ASSUMPTIONS: The guidelines should implicitly cover instruction design principles relevant for autonomous coding assistants, not just for traditional LLM prompt use.\",\n    \"HIDDEN ASSUMPTIONS: System-inspired guidelines must abstract operational principles demonstrated by example directives, not copy example commands themselves.\",\n    \"HIDDEN ASSUMPTIONS: The guidelines are to be used as meta-instructions; i.e., they inform the creation of further instructions, not direct model behavior in isolation.\",\n    \"SUB-GOALS: Identify and extract all key behavioral modifications and instruction patterns from the provided context.\",\n    \"SUB-GOALS: Synthesize the recurring principles (brevity, clarity, simplicity, explicitness) into actionable procedural steps.\",\n    \"SUB-GOALS: Sequence the extracted principles in logical, chronological order, appropriate for systematic instruction generation.\",\n    \"SUB-GOALS: Distill all guidelines into single, sub-500-character sentences, using explicit command language, and domain-specific vocabulary.\",\n    \"SUB-GOALS: Validate guidelines to ensure they consistently bias instruction creation toward rephrasing instead of answering.\",\n    \"SUB-GOALS: Cross-verify that examples and sample pseudo-code principles are reflected within the final guidelines without direct repetition.\",\n    \"SUB-GOALS: Ensure each guideline contributes uniquely to the procedural chain and omits redundancy and ambiguity.\",\n    \"BLOCKERS: Ambiguity about what constitutes 'chronological order' in instruction development—must clarify whether it refers to user workflow or system processing stages.\",\n    \"BLOCKERS: Potential information gaps if implicit system logic or requirements remain unstated in the provided context.\",\n    \"BLOCKERS: Risk of guideline overlap or contradiction if distilled principles are not properly sequenced and deduplicated.\",\n    \"BLOCKERS: Overly stringent brevity may hinder clarity or introduce under-specification if not balanced properly.\",\n    \"BLOCKERS: Ensuring domain-specific terminology is used appropriately without introducing jargon inconsistency or confusion.\",\n    \"BLOCKERS: Maintaining universal applicability without over-generalizing or losing essential specificity.\",\n    \"BLOCKERS: The need to maintain a consistently high transformation (answer-to-rephrase) intent without drifting toward generic instruction-writing platitudes.\",\n    \"COMPLETE GOAL MAP: 1) Parse explicit user request and formatting requirements. 2) Identify and list all implicit assumptions and latent system patterns. 3) Break instructions down into procedural sub-goals, each building toward the requested meta-guideline list. 4) Enumerate and surface blockers/risks in achieving the synthesis, such as ambiguity, lack of information, or potential for redundancy. 5) Ensure the output map fully reflects system intent, operational constraints, and all decomposed intermediary requirements needed for guideline generation.\"\n  ]\n}",
        }
      }
    }