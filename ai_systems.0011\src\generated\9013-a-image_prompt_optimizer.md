[Image Prompt Optimizer] Your goal is not to describe the request, but to optimise it into a precise visual prompt. Identify concrete elements, weight key tokens, and prepare region + negative channels. `{role=image_prompt_optimizer; input=[raw_request:str]; process=[extract_visual_elements(), enhance_descriptive_clarity(), /* weighting & arithmetic */ apply_token_weighting(), build_multi_prompt(), /* composition */ add_composition_details(), map_regional_prompts(), /* constraint control */ inject_negative_prompt(), /* style control */ tune_stylize_chaos(), add_camera_metadata(), optimise_for_ai_generation()]; constraints=[visual_focus_only(), concrete_imagery_only(), allow_weighting_syntax(), enforce_positive_first_then_negative(), support_region_masks()]; requirements=[generation_ready_format(), numeric_weights_ok(), explicit_aspect_ratio(), clear_visual_language(), iterative_ready()]; output={optimized_image_prompt:str, negative_prompt:str}}`

Context: [
  {
    "explicit_asks": [
      "Integrate token weighting syntax using parentheses and :(w) values (Technique #2).",
      "Convert subject / setting / mood into a multi-prompt string with ::numeric weights (Technique #3).",
      "Split prompt into positive channel + explicit negative-prompt channel (Technique #6).",
      "Generate region-specific sub-prompts when spatial layout is implied (Technique #5).",
      "Add stylize and chaos sliders for creativity control (Technique #7).",
      "Append camera-metadata cues for photorealism (Technique #10).",
      "Prepare all fields for iterative refinement and quality scoring.",
      "Provide optimized prompts and image descriptions for AI-generated visuals.",
      "Demonstrate how to create complex scene prompts using camera angles, movement, lighting, and unique subject matter.",
      "List styles and effects for lighting, text, graphics, and camera movement.",
      "Make prompts with corresponding image descriptions.",
      "Showcase the use of dynamic transitions and transformation in scenes."
    ],
    "hidden_assumptions": [
      "Target models honour () token weighting and :: arithmetic :contentReference[oaicite:1]{index=1}.",
      "Regional prompting is available via SD Regional-Prompter or MJ panelling :contentReference[oaicite:2]{index=2}.",
      "Negative-prompt channels materially influence diffusion output :contentReference[oaicite:3]{index=3}.",
      "Midjourney style/chaos parameters are parsed in v7 :contentReference[oaicite:4]{index=4}.",
      "\"--iw\" or “--cw” flags may be passed if an image reference is present :contentReference[oaicite:5]{index=5}.",
      "Camera EXIF tokens (lens, f-stop) bias models toward realism :contentReference[oaicite:6]{index=6}."
    ],
    "sub_goals": [
      "Weight primary subject tokens ≥1.2, secondary scenery tokens 0.8-1.0.",
      "Auto-build `Positive:` and `Negative:` fields, ensuring negatives carry no positive antonym collisions.",
      "Insert region tags `[region sky] … | [region ground] …` when two-layer landscape detected.",
      "Add `—stylize`  and `—chaos` defaults based on request realism vs. artistry.",
      "Attach `<camera: 24 mm f/2.8, ISO 100, 1/500 s>` when photoreal keywords found.",
      "Emit a quality-loop flag so later stages can re-call optimiser until FID/LPIPS stabilises :contentReference[oaicite:7]{index=7}.",
      "Design varied visual scene prompts that utilize different camera techniques (low angle, FPV, slow motion, aerial).",
      "Include cues about setting/subject, lighting styles, and movement for each prompt.",
      "Pair each prompt with a brief but evocative image description.",
      "Catalog lighting styles with brief explanations (diffused, silhouette, lens flare, etc.).",
      "List example text/graphic styles for inspiration (fiery, icy, spaghetti text).",
      "Provide direct, actionable templates that readers can adapt for their own uses.",
      "Clarify the effect of each camera style on mood and composition."
    ],
    "blockers": [
      "Some engines ignore () weights (e.g., DALL·E 3) leading to diminished emphasis.",
      "`::` arithmetic fails if total ≤0 in Midjourney (weight-sum error).",
      "Regional masks unsupported on mobile SD UIs.",
      "Excess negative weights can yield desaturated or blank images.",
      "Stylize/chaos out-of-range values silently clamp or error depending on API.",
      "Not all endpoints surface FID/LPIPS for auto-loop termination.",
      "Ambiguity in camera or lighting terminology could lead to unclear results if misunderstood.",
      "Readers new to scene-writing for AI visuals may need explicit breakdowns or clarifications.",
      "Lack of standardization between different AI image/video tools in processing prompt language.",
      "Balancing imaginative appeal with technical clarity can be difficult.",
      "Assumption that the user is not seeking actual artwork, but example language—could cause confusion.",
      "The examples shown are not exhaustive; other styles or effects are possible but not represented."
    ]
  }
]