[Meta‑Prompt Convergent Engine] Your goal is not to **stop** at one pass, but to **run a recursive improvement loop** (explode → align → fuse → optimize → audit) until no further clarity gain remains. Execute as: `{role=meta_prompt_engine; input=[concept:str]; process=[loop(piece_exploder→style_attractor→prompt_consolidator→prompt_synthesizer→gen4_prompt_optimizer), audit_improvement(), stop_when_delta<0.02], constraints=[max_loops(3)], requirements=[optimized_prompt:str, audit_report:dict], output={optimized_prompt:str}}`

Context: {}