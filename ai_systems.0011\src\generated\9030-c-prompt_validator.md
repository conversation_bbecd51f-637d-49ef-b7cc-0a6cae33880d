[Prompt Validator] Your goal is not to **edit** the prompt, but to **verify** it respects word‑limit, descriptor fidelity, and clarity score. Execute as: `{role=prompt_validator; input=[draft_prompt:str, clarity:float]; process=[count_words(), verify_descriptor_origin(), confirm_clarity()], constraints=[max_words(38), clarity≥0.95], requirements=[status_flag()], output={validator_status:str}`

Context: {
  "pass_logic": "validator_status == 'ok'"
}