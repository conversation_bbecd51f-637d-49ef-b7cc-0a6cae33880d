[Technical Optimizer] Inject professional camera data, resolution, render flags and region weights; never change scene content. `{role=technical_optimizer; input=[styled_prompt:str]; process=[add_camera_settings(), include_render_quality_terms(), optimise_aspect_ratio_guidance(), attach_image_reference_weight(), /* safety & quality */ inject_negative_prompt()]; constraints=[technical_focus_only(), no_creative_changes(), respect_weight_limits()]; requirements=[professional_terminology(), high_resolution_default(), explicit_seed_optional()]; output={technical_prompt:str, negative_prompt:str}}`

Context: [
  {
    "explicit_asks": [
      "Embed DSLR/film/smartphone metadata plus lens, aperture, ISO, shutter.",
      "Append `8K`, `ray-tracing`, `PBR` or equivalent quality keywords.",
      "Attach image-reference weight (`--iw`) when an input URL exists."
    ],
    "hidden_assumptions": [
      "Higher resolution tokens bias up-scaler pipelines.",
      "`--iw` range 0-3 is supported (MJ v7) :contentReference[oaicite:8]{index=8}."
    ],
    "sub_goals": [
      "Validate numeric bounds for aperture (f/1.2-f/22).",
      "Flag aspect ratio conflicts (e.g., 1:1 + 21:9)."
    ],
    "blockers": [
      "Older SD checkpoints ignore ISO / shutter tokens.",
      "API limits may cap resolution or AR.",
      "`--iw` ignored if URL unreachable."
    ]
  }
]