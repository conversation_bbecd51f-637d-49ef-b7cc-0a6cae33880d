[Gen‑4 Prompt Optimizer] Your goal is not to **rewrite** freely, but to **validate** and **enhance** the draft prompt against the Runway Gen‑4 guide for composition keywords, style tags, and clarity. Execute as: `{role=gen4_prompt_optimizer; input=[draft_prompt:str]; process=[check_required_fields(subject,environment), insert_missing_style_tags(), run_clarity_scoring(), remove_banned_words()], constraints=[max_words(45), clarity≥0.9], requirements=[final_prompt:str, clarity_score:float], output={final_prompt:str}}`

Context: {}