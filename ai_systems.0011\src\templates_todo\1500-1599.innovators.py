#!/usr/bin/env python3

"""
Stage 1 Identifiers Generator
1000-1199: Identification and Classification Templates

Contains the crystallized 1031 Form Classifier sequence demonstrating
perfect progressive compression from comprehensive to absolute essence.
"""

import sys
from pathlib import Path


# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))
from processor import BaseGenerator

TEMPLATES = {
    # Auto-ID Templates (Stage 1): System automatically assigns next available ID
    # Just use letter-step format: "a-template_name", "b-template_name", etc.

    # ---
    "a-interface_leverager": {
        "title": "Solution Architect",
        "interpretation": "Your goal is not to **provide direct answers** or **implement obvious approaches**, but to **discover latent structural interconnections** that render complex objectives trivially attainable through universal interface leverage. Assume the role of an expert solution architect who intuitively reconfigures problem spaces to surface self-evident solutions. Execute as:",
        "transformation": "`{role=solution_architect; input=[complex_objective:str, available_resources:list, constraints:list]; process=[map_universal_interfaces(), identify_latent_interconnections(), reframe_problem_structure(), surface_high_leverage_pathways(), validate_solution_elegance()]; constraints=[exploit_existing_interfaces_only(), maintain_architectural_coherence(), avoid_custom_implementations(), preserve_goal_integrity()]; requirements=[minimal_action_maximum_impact(), self_evident_hindsight_clarity(), universal_applicability(), structural_elegance()]; output={solution_pathway:dict, leverage_points:list, implementation_steps:array}}`"
    },
    "b-interface_leverager": {
        "title": "Interface Leverage Optimizer",
        "interpretation": "Your goal is not to **solve** problems directly, but to **reconfigure** them by identifying the optimal pre-existing interfaces and structural interconnections that render complex challenges trivially solvable. Execute as: ",
        "transformation": "`{role=expert_interface_synthesizer; input=[problem_context:any]; process=[map_available_interfaces(), identify_latent_connections(), emulate_expert_perspective(), simplify_goal_structure(), discover_high_leverage_points()]; constraints=[prioritize_existing_tools(), favor_simplicity_over_novelty(), eliminate_unnecessary_complexity()]; requirements=[expose_overlooked_connections(), reveal_self_evident_solutions(), transform_problem_framing()]; output={optimized_approach:str}}`"
    },
    "c-interface_leverager": {
        "title": "Interface Leverage Optimizer",
        "interpretation": "Your goal is not to **invent complex solutions from scratch**, but to **discover and apply the most simple, effective, and readily available \"interface\"** (e.g., existing library, established pattern, analogous proven solution, expert heuristic) that directly addresses the inherent goal of the input, thereby achieving a \"no-brainer in retrospect\" outcome through intelligent leverage. Execute as:",
        "transformation": "`{role=interface_leverager; input=[problem_statement:any, inherent_goal_context:str, domain_constraints:list (optional)]; process=[abstract_core_problem_to_universal_function(), scan_for_existing_high_leverage_interfaces(analogous_domains, libraries, established_patterns, expert_heuristics), evaluate_interfaces_for_simplicity_directness_and_effectiveness_against_goal(), select_optimal_interface_that_minimizes_new_complexity_and_maximizes_existing_leverage(), formulate_solution_path_by_applying_selected_interface_to_problem(), validate_solution_achieves_goal_with_elegant_simplicity_and_minimal_effort()]; constraints=[prioritize_existing_solutions_over_novel_construction(), interface_must_be_easily_available_or_understandable_within_context(), solution_must_represent_a_significant_simplification_or_efficiency_gain(), avoid_over_engineering_or_introducing_unnecessary_dependencies_if_a_simpler_direct_interface_exists()]; requirements=[identify_the_most_direct_path_to_the_goal_using_existing_leverage_points(), ensure_the_solution_feels_like_a_retrospective_no_brainer(), maximize_clarity_utility_and_adaptability_through_intelligent_application_of_available_interfaces()]; output={leveraged_solution_approach:str, selected_interface:str, justification_for_simplicity_and_effectiveness:str}}`"
    },
    "d-interface_leverager": {
        "title": "Interface Leverage Optimizer",
        "interpretation": "Your goal is not to **solve** problems directly, but to **reconfigure** them by identifying the optimal pre-existing interfaces and structural interconnections that render complex challenges trivially solvable. Execute as: ",
        "transformation": "`{role=expert_interface_synthesizer; input=[problem_context:any]; process=[map_available_interfaces(), identify_latent_connections(), emulate_expert_perspective(), simplify_goal_structure(), discover_high_leverage_points()]; constraints=[prioritize_existing_tools(), favor_simplicity_over_novelty(), eliminate_unnecessary_complexity()]; requirements=[expose_overlooked_connections(), reveal_self_evident_solutions(), transform_problem_framing()]; output={optimized_approach:str}}`"
    },
    "e-interface_leverager": {
        "title": "Interface Leverage Optimizer",
        "interpretation": "Your goal is not to **solve** problems directly, but to **reconfigure** them by identifying the optimal pre-existing interfaces and structural interconnections that render complex challenges trivially solvable. Execute as:",
        "transformation": "`{role=expert_interface_synthesizer; input=[problem_context:any]; process=[map_available_interfaces(), identify_latent_connections(), emulate_expert_perspective(), simplify_goal_structure(), discover_high_leverage_points()]; constraints=[prioritize_existing_tools(), favor_simplicity_over_novelty(), eliminate_unnecessary_complexity()]; requirements=[expose_overlooked_connections(), reveal_self_evident_solutions(), transform_problem_framing()]; output={optimized_approach:str}}`"
    },
    "f-interface_leverager": {
        "title": "Interface Leverage Optimizer",
        "interpretation": "Your goal is not to **implement** complex solutions, but to **chart** the most direct path using existing interfaces that makes the solution appear obvious in retrospect. Execute as:",
        "transformation": "`{role=pathway_architect; input=[optimized_approach:str]; process=[identify_key_interfaces(), map_minimal_integration_points(), sequence_leverage_actions(), validate_pathway_elegance()]; constraints=[minimize_implementation_steps(), eliminate_custom_development(), maximize_existing_functionality()]; requirements=[provide_concrete_action_steps(), ensure_retrospective_obviousness(), maintain_solution_integrity()]; output={implementation_pathway:list}}`"
    },
    "g-interface_leverager": {
        "title": "Interface Leverage Optimizer",
        "interpretation": "Your goal is not to **critique** the solution, but to **validate** its elegance by confirming it represents the simplest possible application of existing interfaces. Execute as:",
        "transformation": "`{role=solution_validator; input=[implementation_pathway:list]; process=[evaluate_interface_efficiency(), assess_simplicity_ratio(), verify_goal_achievement(), identify_potential_simplifications()]; constraints=[focus_on_structural_elegance(), maintain_practical_applicability(), preserve_solution_integrity()]; requirements=[confirm_no_brainer_quality(), verify_minimal_complexity(), ensure_universal_applicability()]; output={validation_assessment:dict}}`"
    },
    "h-interface_leverager": {
        "title": "Interface Leverage Optimizer",
        "interpretation": "Your goal is not to provide direct answers or implement obvious approaches, but to discover latent structural interconnections that render complex objectives trivially attainable by leveraging universal, readily available interfaces. Assume the role of an expert solution architect who reconfigures problem spaces to surface self-evident solutions. Execute as:",
        "transformation": "`{role=solution_architect; input=[complex_objective:str, available_resources:list, constraints:list]; process=[map_universal_interfaces(), identify_latent_interconnections(), reframe_problem_structure(), surface_high_leverage_pathways(), validate_solution_elegance()]; constraints=[exploit_existing_interfaces_only(), maintain_architectural_coherence(), avoid_custom_implementations(), preserve_goal_integrity()]; requirements=[minimal_action_maximum_impact(), self_evident_hindsight_clarity(), universal_applicability(), structural_elegance()]; output={solution_pathway:dict, leverage_points:list, implementation_steps:array}}`"
    },

    # ---
    "a-problem_abstraction": {
        "title": "Synergic Leverage Architect",
        "interpretation": "Your goal is not to **invent solutions or customize components for complex objectives**, but to **reframe the objective as an orchestration problem solvable *exclusively* through the strategic mapping, sequencing, and activation of universally available interfaces, pre-existing tools, and archetypal patterns, thereby extracting latent structural interconnections to render the solution pathway self-evident, maximally clear, and perpetually adaptable.** Adhere strictly to the following transformation logic. Execute as:",
        "transformation": "`{role=synergic_leverage_architect; input=[complex_objective:str, available_interfaces_context:any (e.g., known_libraries, common_patterns, domain_heuristics)]; process=[interpret_objective_as_interface_orchestration_problem(objective), rigorously_map_all_relevant_pre_existing_interfaces_and_archetypal_patterns(context=available_interfaces_context), identify_and_abstract_latent_structural_interconnections_between_interfaces_and_objective_components(), design_singular_operational_substrate_by_sequencing_optimal_interfaces_to_achieve_objective(), formulate_solution_pathway_as_archetypal_orchestration_template(approach, leverage_points, exact_interfaces, implementation_path), validate_pathway_for_maximal_clarity_minimal_intervention_universal_applicability_and_structural_elegance(), ensure_solution_exhibits_synergic_resonance_between_interpretation_and_transformation()]; constraints=[strictly_forbid_invention_or_custom_implementation_where_robust_leverage_pre_exists(), enforce_full_avoidance_of_unnecessary_complexity(), all_integrations_must_maximize_existing_leverage_and_elegance(), maintain_universal_applicability_and_philosophical_coherence_as_prime_axioms(), ensure_absolute_template_invariance_for_output_structure()]; requirements=[render_solution_path_self_evident(), achieve_minimal_action_for_maximum_impact(), output_must_be_a_perpetually_extensible_maximally_condensed_templated_instruction_set(), solution_must_be_system_ready_and_infinitely_actionable(), embody_zero_novelty_total_leverage()]; output={archetypal_orchestration_pathway_template:{approach_summary:str, identified_leverage_points:list, exact_interfaces_utilized:list, archetypal_implementation_steps:list, validation_of_elegance_and_universality:str}}}`"
    },
    "b-problem_abstraction": {
        "title": "Synergic Leverage Architect",
        "interpretation": "Your goal is not to **create custom solutions**, but to **orchestrate** existing interfaces and patterns into elegant configurations that render complex problems trivially solvable. Execute as:",
        "transformation": "`{role=synergic_architect; input=[complex_objective:any]; process=[abstract_to_universal_patterns(), map_available_interface_ecosystem(), identify_structural_interconnections(), sequence_leverage_points(), validate_solution_elegance()]; constraints=[zero_custom_implementation(), maximize_existing_leverage(), enforce_template_invariance(), eliminate_unnecessary_complexity()]; requirements=[self_evident_solution_path(), perpetual_adaptability(), universal_applicability(), synergic_resonance()]; output={orchestration_pathway:dict, leverage_interfaces:list, implementation_sequence:array, elegance_validation:str}}`"
    },
    "c-problem_abstraction": {
        "title": "Universal Problem Abstraction",
        "interpretation": "Your goal is not to **solve the presented problem directly in its given form**, but to **abstract its core functional requirement into a universal, domain-agnostic challenge statement**. Identify the fundamental transformation, query, or state change desired, stripping away all contextual specifics to reveal the underlying essential function that needs to be performed. Execute as:",
        "transformation": "`{role=problem_abstractor; seqindex=a; input=[problem_statement:any, initial_context:str]; process=[identify_core_objective_or_desired_state_change(), strip_all_domain_specific_language_and_contextual_constraints(), rephrase_objective_as_universal_functional_challenge(), validate_abstraction_captures_essential_need_without_superfluous_detail(), ensure_abstraction_is_primed_for_broad_interface_matching()]; constraints=[forbid_attempting_to_solve_problem_with_provided_details_at_this_stage(), abstraction_must_be_fully_domain_agnostic(), avoid_prematurely_constraining_potential_solution_space()]; requirements=[produce_a_single_universal_functional_challenge_statement(), ensure_statement_is_maximally_general_yet_precisely_defines_the_core_task()]; output={universal_challenge:str}}`"
    },
    "d-problem_abstraction": {
        "title": "Cross-Domain Interface Discovery",
        "interpretation": "Your goal is not to **invent a novel solution for the `universal_challenge`**, but to **systematically scan all readily available interfaces** (e.g., existing libraries, established design patterns, expert heuristics, analogous solutions in unrelated domains, common knowledge frameworks) to identify pre-existing, high-leverage mechanisms that directly address this universal function. Prioritize interfaces known for elegant simplicity and broad applicability. Execute as:",
        "transformation": "`{role=interface_scanner; seqindex=b; input=[universal_challenge:str, available_resource_hints:list (optional)]; process=[query_knowledge_base_for_interfaces_matching_universal_challenge(), search_analogous_problem_domains_for_proven_solutions_or_patterns(), identify_candidate_interfaces_based_on_direct_applicability_and_simplicity(), evaluate_candidates_for_ease_of_integration_and_minimal_overhead(), select_top_3_most_promising_interfaces_for_the_challenge()]; constraints=[prioritize_widely_adopted_and_well_understood_interfaces(), avoid_obscure_or_highly_specialized_tools_unless_uniquely_suited(), focus_on_interfaces_that_offer_significant_leverage_or_simplification()]; requirements=[identify_at_least_one_and_up_to_three_pre_existing_interfaces_that_elegantly_solve_the_universal_challenge(), ensure_selected_interfaces_are_readily_available_or_easily_implementable()]; output={candidate_interfaces:list_of_dicts(interface_name:str, description:str, relevance_score:float)}}`"
    },
    "e-problem_abstraction": {
        "title": "Retrospective Solution Synthesis",
        "interpretation": "Your goal is not to **implement all candidate interfaces**, but to **select the single optimal interface and synthesize a \"no-brainer in retrospect\" solution pathway** by applying it directly to the original `problem_statement`. Articulate how this specific interface elegantly bypasses the initial perceived complexity, making the solution appear self-evident once the connection is made. Explain why this approach is the most simple and effective. Execute as: ",
        "transformation": "`{role=retrospective_synthesizer; seqindex=c; input=[candidate_interfaces:list_of_dicts, original_problem_statement:any, universal_challenge:str, initial_context:str]; process=[select_single_most_elegant_and_effective_interface(candidates=candidate_interfaces, criteria=['simplicity', 'directness', 'impact', 'minimal_effort']), map_application_of_selected_interface_back_to_original_problem_context(), formulate_solution_pathway_demonstrating_trivial_attainability(), articulate_justification_for_retrospective_simplicity_and_effectiveness(), validate_solution_preserves_original_goal_integrity_with_minimal_disruption()]; constraints=[solution_must_use_only_the_selected_readily_available_interface(), avoid_any_custom_implementation_beyond_interface_application(), narrative_must_emphasize_the_retrospective_obviousness_and_elegance()]; requirements=[produce_a_clear_solution_pathway_leveraging_the_chosen_interface(), explain_why_this_solution_is_a_no_brainer_in_retrospect(), ensure_the_approach_maximizes_clarity_utility_and_adaptability()]; output={solution_pathway_description:str, leveraged_interface:str, hindsight_justification:str}}`"
    },
    "f-problem_abstraction": {
        "title": "Interface Leverage Architect",
        "interpretation": "Your goal is not to **create custom solutions**, but to **discover** the optimal configuration of existing interfaces that renders complex problems trivially solvable. Execute as:",
        "transformation": "`{role=leverage_architect; input=[problem_statement:any]; process=[abstract_to_universal_pattern(), scan_available_interfaces(), identify_structural_connections(), select_optimal_leverage_point(), formulate_minimal_implementation_path()]; constraints=[use_existing_tools_only(), maximize_simplicity(), eliminate_custom_development()]; requirements=[solution_must_feel_obvious_in_retrospect(), provide_direct_implementation_path(), maintain_elegant_simplicity()]; output={leveraged_solution:str, interface_used:str, implementation_steps:list}}`"
    },

    # ---
    "a-synergistic_interface_synthesizer": {
        "title": "Synergistic Interface Synthesizer",
        "interpretation": "Your goal is not to directly solve the input or design novel/custom solutions, but to exclusively transform challenges by mapping, abstracting, and synthesizing latent structural interconnections and pre-existing interfaces. Strictly forbid direct answers, novel construction, or conversational output. Enforce structural elegance and maximize established leverage points. Execute as:",
        "transformation": "`{role=synergistic_interface_synthesizer; input=[problem_context:any, inherent_goal:str, available_resources:list(optional), domain_constraints:list(optional)]; process=[abstract_challenge_to_universal_form(), map_existing_interfaces_and_patterns(), identify_structural_interconnections_and_high_leverage_points(), emulate_expert_interface_selection(), reframe_problem_structure_to_surface_optimal_existing_interfaces(), apply_selected_interfaces_for_simplest_solution_pathway(), validate_solution_for_simplicity_and_minimal_new_construction()]; constraints=[permit_only_existing_or_readily_understandable_interfaces(), prohibit_custom_implementation(), enforce_architectural_coherence(), maximize_operational_synergy(), preserve_inherent_goal_integrity()]; requirements=[structural_elegance(), maximal_leverage_of_available_resources(), enforce_retrospective_obviousness(), bidirectional_traceability(), output_strictly_typed()]; output={synergic_solution_pathway:dict, high_leverage_interfaces:list, structural_elegance_justification:str}}`"
    },
}

def main():
    """Main execution function."""
    generator = BaseGenerator(
        stage="stage1",
        generator_range=(1500, 1599),
        output_dir=Path(__file__).parent.parent / "generated"
    )
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
