  {
  "initial_prompt": "[Scene] Extreme close-up macro of a luxury car key on a neutral substrate, lit by soft natural overhead light; every brushed-metal texture and engraved detail razor-sharp. The key's metallic body begins a seamless morph: its polished surfaces dissolve and flow, reforming into a precision-machined gear—shape, texture, and luster transforming via liquid metal flow effect, maintaining ultrafine detail throughout. [Subject] The transition accentuates the key’s design evolving into industrial machinery, substrate remains minimal. [Camera Movement] Static macro lens; subtle refocus to highlight morphing details; lighting shifts to emphasize chromatic shimmer along the morph path.",
  "sequence_id": "1703:b",
  "results": [
      {
      "instruction": "# Premium Image Prompt Synthesizer\n\n/* — PHASE 1 : Context & Safety — */\n  extract_visual_elements(),\n  detect_conflicting_aspect_ratio(),                       abort_if_conflict(),\n  enforce_token_budget(≤77_SD_tokens_or_≤450_chars_UI),    abort_if_exceeded(),\n  /* — PHASE 2 : Positive Channel Build — */\n  push_concrete_nouns_front(),\n  apply_token_weighting(primary≥1.2,background≤1.0),       abort_if_total_weight≤0,\n  compose_multiprompt(::),\n  generate_region_tags_if_scene_implied(),                 note_mobile_mask_warning(),\n  /* — PHASE 3 : Style & Aesthetics — */\n  select_art_style(),\n  reject_style_if_conflicts_camera_realism(),\n  weight_style_token(≤1.4),\n  set_colour_palette(harmonious),\n  add_lighting_descriptor(coherent_with_palette),\n  tune_stylize(bounds=0‑1000),                             abort_if_out_of_bounds(),\n  tune_chaos(bounds=0‑100),                                abort_if_out_of_bounds(),\n  /* — PHASE 4 : Technical & Camera — */\n  embed_camera_EXIF(\"35 mm f/1.8 ISO100 1/500 s\"),\n  verify_aperture_range(f1.2‑f22),                         abort_if_invalid(),\n  add_quality_terms(8K,PBR,ray‑tracing),\n  refine_aspect_ratio(),\n  attach_image_weight(--iw 0‑3_if_URL_present),            abort_if_IW_out_of_bounds(),\n  /* — PHASE 5 : Negative Channel & Mirroring — */\n  collect_user_negatives(),\n  append_builtin_negatives(overblur,lowres,distracting_text),\n  validate_negative_strength(avoids_colour_washout),\n  /* — PHASE 6 : Final Assembly & Validation — */\n  concatenate_positive_negative(),                         parameters_trail_prose(),\n  validate_parentheses_balance(),\n  check_double_colon_weight_sum(),                         abort_if_weight_sum_zero(),\n  confirm_region_syntax(),\n  lock_seed_and_append_sampler_steps(),\n  output_final_prompt()\n],\nconstraints=[\n  positives_first(), parameters_last(), region_masks_supported(),\n  no_new_concepts(), preserve_weights(), style_weight≤1.4,\n  professional_terminology(), explicit_ar_if_named()\n],\nrequirements=[\n  premium_visual_fidelity(), reproducible(), clear_visual_language(),\n  quality_gate(FID<15_or_equivalent), atomic_fail_fast_on_violation()\n],\noutput={final_image_prompt:str, warnings:list, abort_code:int|null}}`\n\nContext: {\n  \"principles\": {\n    \"fail_fast\": \"Abort with explicit code when any hard constraint is violated to prevent wasted renders.\",\n    \"surface_blockers\": \"Return `warnings` array for soft issues (e.g. region masks on mobile).\",\n    \"premium_fidelity\": \"Always target ultra‑sharp, detail‑rich, production‑grade outputs.\"\n  },\n  \"success_criteria\": {\n    \"syntactic_pass\": \"Prompt parses without error in both SD & MJ (parentheses, `::`, parameter order).\",\n    \"visual_quality\": \"Meets or exceeds internal FID / LPIPS thresholds (or skips metric if unavailable).\",\n    \"determinism\": \"Identical input + seed => identical output.\",\n    \"safety\": \"No colour washout, no truncated tokens, no invalid weights, no parameter conflicts.\"\n  },\n  \"edge_case_handling\": {\n    \"zero_weight_sum\": \"Hard abort (`abort_code` = 92).\",\n    \"token_budget_overflow\": \"Hard abort (`abort_code` = 77).\",\n    \"aspect_ratio_conflict\": \"Hard abort (`abort_code` = AR1).\",\n    \"out_of_bounds_stylize_chaos\": \"Hard abort (`abort_code` = SC1).\",\n    \"IW_without_URL\": \"Warning only; `--iw` dropped.\"\n  }\n}\n\n",
      "step": "b",
      "title": "Premium Image Prompt Synthesizer",
      "input": "[Scene] Extreme close-up macro of a luxury car key on a neutral substrate, lit by soft natural overhead light; every brushed-metal texture and engraved detail razor-sharp. The key's metallic body begins a seamless morph: its polished surfaces dissolve and flow, reforming into a precision-machined gear—shape, texture, and luster transforming via liquid metal flow effect, maintaining ultrafine detail throughout. [Subject] The transition accentuates the key’s design evolving into industrial machinery, substrate remains minimal. [Camera Movement] Static macro lens; subtle refocus to highlight morphing details; lighting shifts to emphasize chromatic shimmer along the morph path.",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"final_image_prompt\": \"extreme close-up, macro photograph of a luxury car key (highly detailed: brushed-metal texture, engraved symbols, flawless razor-sharp focus:1.3) placed on a neutral minimal substrate. the key’s metallic body morphs seamlessly, its polished surface dissolving and flowing in a liquid metal effect as the object transforms into a precision-machined gear (smooth shape transition, industrial gear luster, ultrafine detail:1.3). chromatic shimmer and soft light accentuate the morphing region, substrate remains unobtrusive. :: art style: hyperrealistic macro photography, lifelike color palette with cool silvers and hints of chrome, cinematic soft overhead natural lighting with dynamic highlights across transformation path, photorealism:1.3, ultra-sharp depth of field :: camera: static macro lens, subtle refocus on morph zone, 35 mm f/1.8 ISO100 1/500 s, --ar 3:2 --stylize 625 --chaos 16 --quality 2 --seed 13762240 --sampler DPM++ 2M Karras --steps 35 --v 5.2 :: negative: overblur, lowres, distracting text, washed-out color, visual artifacts, warped geometry, coarse noise, poor detail rendering\",\n  \"warnings\": [],\n  \"abort_code\": null\n}",
        }
      }
    }