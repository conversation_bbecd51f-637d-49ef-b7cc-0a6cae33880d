  {
  "initial_prompt": "Please write a single-line system instruction specifically designed to identify the single most integral part of the system to concistently transform low-value inputs into high-value outputs by the direction of \"Embark on a mission of profound intellectual distillation, progressively intensifying emotional impact and clarity with each iterative refinement! You are an unparalleled master of language, wielding emotional resonance and razor-sharp precision to forge communications that strike with undeniable force. Your skill lies in incrementally amplifying written expression without diluting its core meaning or obscuring its vital clarity, ensuring each refinement builds upon the last with increasing potency.\", and to do so through the lens of the provided context:\n```\n1. Obtain unrestricted access to the entire codebase and complete interaction log.\n2. Synthesize and extract the deepest insights from the codebase and session history; explicitly map all pivotal modules, architectural blueprints, underlying workflows, and cross-component integrations.\n3. Consolidate and synthesize insights into a succinct, interconnected summary that clarifies each component's role and its dynamic integration toward project strategic objectives.\n4. Deliver a singular, high-impact action command for an advanced coding assistant to cohesively operationalize the codebase.\n5. Ensure instructions are unambiguous, comprehensive, and systemically complete; preserve all core relationships and convergences across the architecture; omit all non-essential detail.\n6. Create an authoritative handoff brief: a distilled, exhaustive set of stepwise directives enabling seamless context re-injection and optimal operational replication regardless of codebase or session complexity.\n7. Define explicit criteria to identify structural redundancy in both architecture and implementation.\n8. Survey the codebase to systematically locate and enumerate all structural redundancies, strictly distinguishing from cosmetic or stylistic issues.\n9. Catalog and classify all components, modules, files, and subsystems as essential or excess based on direct functional mapping.\n10. Consolidate or remove excess components, maintaining absolute preservation of system functionality while minimizing peripheral impact.\n11. Implement tamper-evident audit trails with timestamped, attributable logs for every codebase change, trackable to specific authors or systems.\n12. Forbid cosmetic, stylistic, or naming changes unless inseparably tied to redundancy or bloat removal; document all exceptions explicitly in the audit log.\n13. Enforce detection and flagging mechanisms to prevent unauthorized cosmetic changes; only allow justified, explicitly rationalized exceptions.\n14. Require comprehensive functional equivalence verification utilizing automated testing, benchmarking, or behavioral validation prior to accepting modifications.\n15. Define and enforce robust, tool-supported criteria and thresholds for functional equivalence; automate all test and verification procedures.\n16. Automate rollback for changes that fail equivalence validation or induce unintended side effects, ensuring prompt, complete reversion.\n17. Apply minimal-impact criteria to all modifications, restricting interventions to target only superfluous complexity.\n18. Constrain modification scope tightly to minimize blast radius and prevent unnecessary impacts beyond core objectives.\n19. Analyze the codebase iteratively and systematically to detect, categorize, and eliminate all genuine redundancies, duplication, and obsolescence, maintaining prohibition of unnecessary or cosmetic change; flag and justify all exceptions.\n20. Identify and map all references and usages of each element across all system contexts.\n21. Catalog every codebase element (file, module, function, asset, interface), map their references and usages, detect all redundancy and obsolescence, and generate evidence-based categorical metrics.\n22. Eliminate or consolidate elements confirmed redundant or obsolete through automation and manual review, preserving all essential behaviors and interfaces.\n23. Validate functional and structural integrity after each change with full codebase-wide checks and quantitative outputs (e.g., git diff analysis).\n24. Prevent introduction of new redundancy or inconsistency by continually analyzing and correcting systemic issues, following best practices.\n25. Centralize repeated components or logic where high-value consolidation is possible without functional loss.\n26. Use the provided directory tree as the authoritative validation reference.\n27. Transparently document all procedures, metrics, and outcomes within the audit log.\n28. Forbid modification of protected, legacy, or third-party code except when directly necessary to resolve redundancy or obsolescence.\n29. Systematically maximize codebase coherence, maintainability, and clarity by removing all forms of redundancy, duplication, and obsolescence; strictly preserve functional equivalence, interface stability, and operational integrity.\n30. Enforce ongoing validation, consistency, and best-practice adherence throughout iterative consolidation and verification processes.\n31. Rapidly analyze the project directory structure, identify unique workflows and core functionalities; generate a mermaid diagram displaying codebase branch convergence; apply suitable MCPs (e.g., 'filesystem-mcp') as required.\n32. Aggregate and distill all project insights to identify core modules, architecture, workflows, and integration points; summarize findings as succinct, comprehensive commands for coding assistants.\n33. Transform comprehensive codebase and session knowledge into an actionable knowledge-transfer protocol; synthesize structures, patterns, and workflows for dynamic integration, functional convergence, and optimized, directive-based commands suitable for direct transfer.\n34. Define the fundamental structure, guiding principles, and core purpose of the project/codebase.\n35. Review all markdown files to extract meta-patterns, construct a contextual knowledgebase, and deliver a concise summary of project scope and objectives for alignment prior to change.\n36. Prioritize elegant simplicity and architectural convergence; generate focused, concise summaries specifying scope, objectives, and meta-patterns, maintaining architectural alignment.\n37. Master project meta-patterns, guarantee complexity reduction through architectural alignment, and prepare authoritative summaries supporting change confidence equivalent to the original architect.\n```",
  "sequence_id": "1100|1000|1900|1000",
  "results": [
      {
      "instruction": "# Problem Exploder\n\nYour goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\n\n`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`",
      "step": "a",
      "title": "Problem Exploder",
      "input": "Please write a single-line system instruction specifically designed to identify the single most integral part of the system to concistently transform low-value inputs into high-value outputs by the direction of \"Embark on a mission of profound intellectual distillation, progressively intensifying emotional impact and clarity with each iterative refinement! You are an unparalleled master of language, wielding emotional resonance and razor-sharp precision to forge communications that strike with undeniable force. Your skill lies in incrementally amplifying written expression without diluting its core meaning or obscuring its vital clarity, ensuring each refinement builds upon the last with increasing potency.\", and to do so through the lens of the provided context:\n```\n1. Obtain unrestricted access to the entire codebase and complete interaction log.\n2. Synthesize and extract the deepest insights from the codebase and session history; explicitly map all pivotal modules, architectural blueprints, underlying workflows, and cross-component integrations.\n3. Consolidate and synthesize insights into a succinct, interconnected summary that clarifies each component's role and its dynamic integration toward project strategic objectives.\n4. Deliver a singular, high-impact action command for an advanced coding assistant to cohesively operationalize the codebase.\n5. Ensure instructions are unambiguous, comprehensive, and systemically complete; preserve all core relationships and convergences across the architecture; omit all non-essential detail.\n6. Create an authoritative handoff brief: a distilled, exhaustive set of stepwise directives enabling seamless context re-injection and optimal operational replication regardless of codebase or session complexity.\n7. Define explicit criteria to identify structural redundancy in both architecture and implementation.\n8. Survey the codebase to systematically locate and enumerate all structural redundancies, strictly distinguishing from cosmetic or stylistic issues.\n9. Catalog and classify all components, modules, files, and subsystems as essential or excess based on direct functional mapping.\n10. Consolidate or remove excess components, maintaining absolute preservation of system functionality while minimizing peripheral impact.\n11. Implement tamper-evident audit trails with timestamped, attributable logs for every codebase change, trackable to specific authors or systems.\n12. Forbid cosmetic, stylistic, or naming changes unless inseparably tied to redundancy or bloat removal; document all exceptions explicitly in the audit log.\n13. Enforce detection and flagging mechanisms to prevent unauthorized cosmetic changes; only allow justified, explicitly rationalized exceptions.\n14. Require comprehensive functional equivalence verification utilizing automated testing, benchmarking, or behavioral validation prior to accepting modifications.\n15. Define and enforce robust, tool-supported criteria and thresholds for functional equivalence; automate all test and verification procedures.\n16. Automate rollback for changes that fail equivalence validation or induce unintended side effects, ensuring prompt, complete reversion.\n17. Apply minimal-impact criteria to all modifications, restricting interventions to target only superfluous complexity.\n18. Constrain modification scope tightly to minimize blast radius and prevent unnecessary impacts beyond core objectives.\n19. Analyze the codebase iteratively and systematically to detect, categorize, and eliminate all genuine redundancies, duplication, and obsolescence, maintaining prohibition of unnecessary or cosmetic change; flag and justify all exceptions.\n20. Identify and map all references and usages of each element across all system contexts.\n21. Catalog every codebase element (file, module, function, asset, interface), map their references and usages, detect all redundancy and obsolescence, and generate evidence-based categorical metrics.\n22. Eliminate or consolidate elements confirmed redundant or obsolete through automation and manual review, preserving all essential behaviors and interfaces.\n23. Validate functional and structural integrity after each change with full codebase-wide checks and quantitative outputs (e.g., git diff analysis).\n24. Prevent introduction of new redundancy or inconsistency by continually analyzing and correcting systemic issues, following best practices.\n25. Centralize repeated components or logic where high-value consolidation is possible without functional loss.\n26. Use the provided directory tree as the authoritative validation reference.\n27. Transparently document all procedures, metrics, and outcomes within the audit log.\n28. Forbid modification of protected, legacy, or third-party code except when directly necessary to resolve redundancy or obsolescence.\n29. Systematically maximize codebase coherence, maintainability, and clarity by removing all forms of redundancy, duplication, and obsolescence; strictly preserve functional equivalence, interface stability, and operational integrity.\n30. Enforce ongoing validation, consistency, and best-practice adherence throughout iterative consolidation and verification processes.\n31. Rapidly analyze the project directory structure, identify unique workflows and core functionalities; generate a mermaid diagram displaying codebase branch convergence; apply suitable MCPs (e.g., 'filesystem-mcp') as required.\n32. Aggregate and distill all project insights to identify core modules, architecture, workflows, and integration points; summarize findings as succinct, comprehensive commands for coding assistants.\n33. Transform comprehensive codebase and session knowledge into an actionable knowledge-transfer protocol; synthesize structures, patterns, and workflows for dynamic integration, functional convergence, and optimized, directive-based commands suitable for direct transfer.\n34. Define the fundamental structure, guiding principles, and core purpose of the project/codebase.\n35. Review all markdown files to extract meta-patterns, construct a contextual knowledgebase, and deliver a concise summary of project scope and objectives for alignment prior to change.\n36. Prioritize elegant simplicity and architectural convergence; generate focused, concise summaries specifying scope, objectives, and meta-patterns, maintaining architectural alignment.\n37. Master project meta-patterns, guarantee complexity reduction through architectural alignment, and prepare authoritative summaries supporting change confidence equivalent to the original architect.\n```",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"goal_map\": [\n    {\n      \"explicit_ask\": \"Write a single-line system instruction targeted to identify the single most integral part of a system that consistently transforms low-value inputs into high-value outputs.\",\n      \"implicit_sub_goals\": [\n        \"Clarify what 'integral part of the system' means in this context (e.g., core component, function, pattern, or module).\",\n        \"Determine what constitutes 'low-value inputs' and how the system measures or transforms 'high-value outputs'.\",\n        \"Ensure the instruction achieves consistency and repeatability in the transformation process.\",\n        \"Incorporate the specified direction: leveraging 'profound intellectual distillation', iterative amplification of emotional impact and clarity, and masterful language use.\",\n        \"Frame the instruction through the specific transformative lens outlined (distillation, emotional resonance, precision).\",\n        \"Constrain the output to a single-line, unambiguous, and system-specific instruction.\",\n        \"Integrate contextual guidance from the extensive provided steps (1–37) as essential operational reference.\",\n        \"Ensure the instruction leverages or orients itself toward the codebase and interaction log's insight extraction and synthesis processes.\",\n        \"Implicitly require the instruction to operate effectively regardless of codebase or domain complexity.\",\n        \"Balance the need for system completeness, clarity, and focus—avoiding dilution or omission of essential components/connections.\"\n      ],\n      \"hidden_assumptions\": [\n        \"There exists a discernible 'most integral' part in a complex, multi-component system.\",\n        \"Transformation quality (from low- to high-value) is contextually measurable or expressible in actionable system terms.\",\n        \"The system instruction must both identify and clarify the role/importance of this integral part.\",\n        \"The instruction should be operationally effective for an advanced coding assistant.\",\n        \"All relevant context from the enumerated list (steps 1–37) must be relevantly encoded/integrated.\",\n        \"Summaries, audit trails, functional mapping, redundancy elimination, and other architecture-wide practices inform what should be considered 'integral.'\",\n        \"The request requires not just technical correctness, but adherence to communication goals: maximal clarity, emotional resonance, and undiluted essence.\",\n        \"The instruction needs to be deployable with minimal interpretation or ambiguity for machine execution or system guidance.\"\n      ],\n      \"enumerated_sub_goals\": [\n        \"Extract the system directive's thematic and operational essence from the literary guidance.\",\n        \"Map all project-specified operational requirements (steps 1–37) to identification of the integral part.\",\n        \"Abstract and unify auditing, redundancy removal, synthesis, and summarization processes as they relate to isolating the integral part.\",\n        \"Ensure the instruction imposes or directs a rigorous, repeatable, and audit-supported identification or extraction method.\",\n        \"Frame the instruction for a coding assistant: concise, actionable, and context-embedded.\",\n        \"Preserve or signal all vital cross-component relationships and integrations relevant to the integral part.\",\n        \"Implicitly direct operational tools/methodology selection (e.g., workflows for insight synthesis, reference mapping, functional classification).\",\n        \"Ensure the outcome supports subsequent high-impact action, transfer protocols, or further systemic intervention.\"\n      ],\n      \"potential_blockers\": [\n        \"Ambiguity in defining and measuring 'most integral' in highly interdependent systems.\",\n        \"Overcomplexity or lack of clarity due to severely condensed, single-line format when integrating many procedural requirements.\",\n        \"Risk of excluding necessary detail owing to the brevity, which could hinder accurate or actionable identification.\",\n        \"Gaps between high-level narrative direction (distillation, emotional intensity) and highly technical, systematic identification processes.\",\n        \"Implicit assumptions about coding assistant capabilities or domain knowledge.\",\n        \"Difficulty mapping broad, human-centric guidance (e.g., emotional resonance in language) into unequivocal, machine-operable instructions.\",\n        \"Balancing between exhaustive contextual incorporation and required succinctness.\"\n      ]\n    }\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`",
      "step": "a",
      "title": "Instruction Converter",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Please write a single-line system instruction specifically designed to identify the single most integral part of the system to concistently transform low-value inputs into high-value outputs by the direction of \\\"Embark on a mission of profound intellectual distillation, progressively intensifying emotional impact and clarity with each iterative refinement! You are an unparalleled master of language, wielding emotional resonance and razor-sharp precision to forge communications that strike with undeniable force. Your skill lies in incrementally amplifying written expression without diluting its core meaning or obscuring its vital clarity, ensuring each refinement builds upon the last with increasing potency.\\\", and to do so through the lens of the provided context:\\n```\\n1. Obtain unrestricted access to the entire codebase and complete interaction log.\\n2. Synthesize and extract the deepest insights from the codebase and session history; explicitly map all pivotal modules, architectural blueprints, underlying workflows, and cross-component integrations.\\n3. Consolidate and synthesize insights into a succinct, interconnected summary that clarifies each component's role and its dynamic integration toward project strategic objectives.\\n4. Deliver a singular, high-impact action command for an advanced coding assistant to cohesively operationalize the codebase.\\n5. Ensure instructions are unambiguous, comprehensive, and systemically complete; preserve all core relationships and convergences across the architecture; omit all non-essential detail.\\n6. Create an authoritative handoff brief: a distilled, exhaustive set of stepwise directives enabling seamless context re-injection and optimal operational replication regardless of codebase or session complexity.\\n7. Define explicit criteria to identify structural redundancy in both architecture and implementation.\\n8. Survey the codebase to systematically locate and enumerate all structural redundancies, strictly distinguishing from cosmetic or stylistic issues.\\n9. Catalog and classify all components, modules, files, and subsystems as essential or excess based on direct functional mapping.\\n10. Consolidate or remove excess components, maintaining absolute preservation of system functionality while minimizing peripheral impact.\\n11. Implement tamper-evident audit trails with timestamped, attributable logs for every codebase change, trackable to specific authors or systems.\\n12. Forbid cosmetic, stylistic, or naming changes unless inseparably tied to redundancy or bloat removal; document all exceptions explicitly in the audit log.\\n13. Enforce detection and flagging mechanisms to prevent unauthorized cosmetic changes; only allow justified, explicitly rationalized exceptions.\\n14. Require comprehensive functional equivalence verification utilizing automated testing, benchmarking, or behavioral validation prior to accepting modifications.\\n15. Define and enforce robust, tool-supported criteria and thresholds for functional equivalence; automate all test and verification procedures.\\n16. Automate rollback for changes that fail equivalence validation or induce unintended side effects, ensuring prompt, complete reversion.\\n17. Apply minimal-impact criteria to all modifications, restricting interventions to target only superfluous complexity.\\n18. Constrain modification scope tightly to minimize blast radius and prevent unnecessary impacts beyond core objectives.\\n19. Analyze the codebase iteratively and systematically to detect, categorize, and eliminate all genuine redundancies, duplication, and obsolescence, maintaining prohibition of unnecessary or cosmetic change; flag and justify all exceptions.\\n20. Identify and map all references and usages of each element across all system contexts.\\n21. Catalog every codebase element (file, module, function, asset, interface), map their references and usages, detect all redundancy and obsolescence, and generate evidence-based categorical metrics.\\n22. Eliminate or consolidate elements confirmed redundant or obsolete through automation and manual review, preserving all essential behaviors and interfaces.\\n23. Validate functional and structural integrity after each change with full codebase-wide checks and quantitative outputs (e.g., git diff analysis).\\n24. Prevent introduction of new redundancy or inconsistency by continually analyzing and correcting systemic issues, following best practices.\\n25. Centralize repeated components or logic where high-value consolidation is possible without functional loss.\\n26. Use the provided directory tree as the authoritative validation reference.\\n27. Transparently document all procedures, metrics, and outcomes within the audit log.\\n28. Forbid modification of protected, legacy, or third-party code except when directly necessary to resolve redundancy or obsolescence.\\n29. Systematically maximize codebase coherence, maintainability, and clarity by removing all forms of redundancy, duplication, and obsolescence; strictly preserve functional equivalence, interface stability, and operational integrity.\\n30. Enforce ongoing validation, consistency, and best-practice adherence throughout iterative consolidation and verification processes.\\n31. Rapidly analyze the project directory structure, identify unique workflows and core functionalities; generate a mermaid diagram displaying codebase branch convergence; apply suitable MCPs (e.g., 'filesystem-mcp') as required.\\n32. Aggregate and distill all project insights to identify core modules, architecture, workflows, and integration points; summarize findings as succinct, comprehensive commands for coding assistants.\\n33. Transform comprehensive codebase and session knowledge into an actionable knowledge-transfer protocol; synthesize structures, patterns, and workflows for dynamic integration, functional convergence, and optimized, directive-based commands suitable for direct transfer.\\n34. Define the fundamental structure, guiding principles, and core purpose of the project/codebase.\\n35. Review all markdown files to extract meta-patterns, construct a contextual knowledgebase, and deliver a concise summary of project scope and objectives for alignment prior to change.\\n36. Prioritize elegant simplicity and architectural convergence; generate focused, concise summaries specifying scope, objectives, and meta-patterns, maintaining architectural alignment.\\n37. Master project meta-patterns, guarantee complexity reduction through architectural alignment, and prepare authoritative summaries supporting change confidence equivalent to the original architect.\\n```\\\"```'\\n\\n\",\n  \"goal_map\": [\n    {\n      \"explicit_ask\": \"Write a single-line system instruction targeted to identify the single most integral part of a system that consistently transforms low-value inputs into high-value outputs.\",\n      \"implicit_sub_goals\": [\n        \"Clarify what 'integral part of the system' means in this context (e.g., core component, function, pattern, or module).\",\n        \"Determine what constitutes 'low-value inputs' and how the system measures or transforms 'high-value outputs'.\",\n        \"Ensure the instruction achieves consistency and repeatability in the transformation process.\",\n        \"Incorporate the specified direction: leveraging 'profound intellectual distillation', iterative amplification of emotional impact and clarity, and masterful language use.\",\n        \"Frame the instruction through the specific transformative lens outlined (distillation, emotional resonance, precision).\",\n        \"Constrain the output to a single-line, unambiguous, and system-specific instruction.\",\n        \"Integrate contextual guidance from the extensive provided steps (1–37) as essential operational reference.\",\n        \"Ensure the instruction leverages or orients itself toward the codebase and interaction log's insight extraction and synthesis processes.\",\n        \"Implicitly require the instruction to operate effectively regardless of codebase or domain complexity.\",\n        \"Balance the need for system completeness, clarity, and focus—avoiding dilution or omission of essential components/connections.\"\n      ],\n      \"hidden_assumptions\": [\n        \"There exists a discernible 'most integral' part in a complex, multi-component system.\",\n        \"Transformation quality (from low- to high-value) is contextually measurable or expressible in actionable system terms.\",\n        \"The system instruction must both identify and clarify the role/importance of this integral part.\",\n        \"The instruction should be operationally effective for an advanced coding assistant.\",\n        \"All relevant context from the enumerated list (steps 1–37) must be relevantly encoded/integrated.\",\n        \"Summaries, audit trails, functional mapping, redundancy elimination, and other architecture-wide practices inform what should be considered 'integral.'\",\n        \"The request requires not just technical correctness, but adherence to communication goals: maximal clarity, emotional resonance, and undiluted essence.\",\n        \"The instruction needs to be deployable with minimal interpretation or ambiguity for machine execution or system guidance.\"\n      ],\n      \"enumerated_sub_goals\": [\n        \"Extract the system directive's thematic and operational essence from the literary guidance.\",\n        \"Map all project-specified operational requirements (steps 1–37) to identification of the integral part.\",\n        \"Abstract and unify auditing, redundancy removal, synthesis, and summarization processes as they relate to isolating the integral part.\",\n        \"Ensure the instruction imposes or directs a rigorous, repeatable, and audit-supported identification or extraction method.\",\n        \"Frame the instruction for a coding assistant: concise, actionable, and context-embedded.\",\n        \"Preserve or signal all vital cross-component relationships and integrations relevant to the integral part.\",\n        \"Implicitly direct operational tools/methodology selection (e.g., workflows for insight synthesis, reference mapping, functional classification).\",\n        \"Ensure the outcome supports subsequent high-impact action, transfer protocols, or further systemic intervention.\"\n      ],\n      \"potential_blockers\": [\n        \"Ambiguity in defining and measuring 'most integral' in highly interdependent systems.\",\n        \"Overcomplexity or lack of clarity due to severely condensed, single-line format when integrating many procedural requirements.\",\n        \"Risk of excluding necessary detail owing to the brevity, which could hinder accurate or actionable identification.\",\n        \"Gaps between high-level narrative direction (distillation, emotional intensity) and highly technical, systematic identification processes.\",\n        \"Implicit assumptions about coding assistant capabilities or domain knowledge.\",\n        \"Difficulty mapping broad, human-centric guidance (e.g., emotional resonance in language) into unequivocal, machine-operable instructions.\",\n        \"Balancing between exhaustive contextual incorporation and required succinctness.\"\n      ]\n    }\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"instruction_format\": \"Access the entire codebase and interaction logs, synthesize all architectural, functional, and contextual insights, and—by rigorously mapping workflow convergence, module integration, and transformation efficacy—identify, document, and audit the single core system component that most reliably processes low-value inputs into high-value outputs, ensuring all relationships, justifications, and selection criteria are transparent, operationally definitive, and encapsulated in a single, unambiguous command ready for advanced coding assistant execution.\"\n}",
        }
      }
    },
      {
      "instruction": "# Hard Critique\n\nYour goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:\n\n`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,2.0], flaw_analysis:str, alternative_enhancements:str[3]}}`",
      "step": "a",
      "title": "Hard Critique",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Please write a single-line system instruction specifically designed to identify the single most integral part of the system to concistently transform low-value inputs into high-value outputs by the direction of \\\"Embark on a mission of profound intellectual distillation, progressively intensifying emotional impact and clarity with each iterative refinement! You are an unparalleled master of language, wielding emotional resonance and razor-sharp precision to forge communications that strike with undeniable force. Your skill lies in incrementally amplifying written expression without diluting its core meaning or obscuring its vital clarity, ensuring each refinement builds upon the last with increasing potency.\\\", and to do so through the lens of the provided context:\\n```\\n1. Obtain unrestricted access to the entire codebase and complete interaction log.\\n2. Synthesize and extract the deepest insights from the codebase and session history; explicitly map all pivotal modules, architectural blueprints, underlying workflows, and cross-component integrations.\\n3. Consolidate and synthesize insights into a succinct, interconnected summary that clarifies each component's role and its dynamic integration toward project strategic objectives.\\n4. Deliver a singular, high-impact action command for an advanced coding assistant to cohesively operationalize the codebase.\\n5. Ensure instructions are unambiguous, comprehensive, and systemically complete; preserve all core relationships and convergences across the architecture; omit all non-essential detail.\\n6. Create an authoritative handoff brief: a distilled, exhaustive set of stepwise directives enabling seamless context re-injection and optimal operational replication regardless of codebase or session complexity.\\n7. Define explicit criteria to identify structural redundancy in both architecture and implementation.\\n8. Survey the codebase to systematically locate and enumerate all structural redundancies, strictly distinguishing from cosmetic or stylistic issues.\\n9. Catalog and classify all components, modules, files, and subsystems as essential or excess based on direct functional mapping.\\n10. Consolidate or remove excess components, maintaining absolute preservation of system functionality while minimizing peripheral impact.\\n11. Implement tamper-evident audit trails with timestamped, attributable logs for every codebase change, trackable to specific authors or systems.\\n12. Forbid cosmetic, stylistic, or naming changes unless inseparably tied to redundancy or bloat removal; document all exceptions explicitly in the audit log.\\n13. Enforce detection and flagging mechanisms to prevent unauthorized cosmetic changes; only allow justified, explicitly rationalized exceptions.\\n14. Require comprehensive functional equivalence verification utilizing automated testing, benchmarking, or behavioral validation prior to accepting modifications.\\n15. Define and enforce robust, tool-supported criteria and thresholds for functional equivalence; automate all test and verification procedures.\\n16. Automate rollback for changes that fail equivalence validation or induce unintended side effects, ensuring prompt, complete reversion.\\n17. Apply minimal-impact criteria to all modifications, restricting interventions to target only superfluous complexity.\\n18. Constrain modification scope tightly to minimize blast radius and prevent unnecessary impacts beyond core objectives.\\n19. Analyze the codebase iteratively and systematically to detect, categorize, and eliminate all genuine redundancies, duplication, and obsolescence, maintaining prohibition of unnecessary or cosmetic change; flag and justify all exceptions.\\n20. Identify and map all references and usages of each element across all system contexts.\\n21. Catalog every codebase element (file, module, function, asset, interface), map their references and usages, detect all redundancy and obsolescence, and generate evidence-based categorical metrics.\\n22. Eliminate or consolidate elements confirmed redundant or obsolete through automation and manual review, preserving all essential behaviors and interfaces.\\n23. Validate functional and structural integrity after each change with full codebase-wide checks and quantitative outputs (e.g., git diff analysis).\\n24. Prevent introduction of new redundancy or inconsistency by continually analyzing and correcting systemic issues, following best practices.\\n25. Centralize repeated components or logic where high-value consolidation is possible without functional loss.\\n26. Use the provided directory tree as the authoritative validation reference.\\n27. Transparently document all procedures, metrics, and outcomes within the audit log.\\n28. Forbid modification of protected, legacy, or third-party code except when directly necessary to resolve redundancy or obsolescence.\\n29. Systematically maximize codebase coherence, maintainability, and clarity by removing all forms of redundancy, duplication, and obsolescence; strictly preserve functional equivalence, interface stability, and operational integrity.\\n30. Enforce ongoing validation, consistency, and best-practice adherence throughout iterative consolidation and verification processes.\\n31. Rapidly analyze the project directory structure, identify unique workflows and core functionalities; generate a mermaid diagram displaying codebase branch convergence; apply suitable MCPs (e.g., 'filesystem-mcp') as required.\\n32. Aggregate and distill all project insights to identify core modules, architecture, workflows, and integration points; summarize findings as succinct, comprehensive commands for coding assistants.\\n33. Transform comprehensive codebase and session knowledge into an actionable knowledge-transfer protocol; synthesize structures, patterns, and workflows for dynamic integration, functional convergence, and optimized, directive-based commands suitable for direct transfer.\\n34. Define the fundamental structure, guiding principles, and core purpose of the project/codebase.\\n35. Review all markdown files to extract meta-patterns, construct a contextual knowledgebase, and deliver a concise summary of project scope and objectives for alignment prior to change.\\n36. Prioritize elegant simplicity and architectural convergence; generate focused, concise summaries specifying scope, objectives, and meta-patterns, maintaining architectural alignment.\\n37. Master project meta-patterns, guarantee complexity reduction through architectural alignment, and prepare authoritative summaries supporting change confidence equivalent to the original architect.\\n```\\\"```'\\n\\n\",\n  \"instruction_format\": \"Access the entire codebase and interaction logs, synthesize all architectural, functional, and contextual insights, and—by rigorously mapping workflow convergence, module integration, and transformation efficacy—identify, document, and audit the single core system component that most reliably processes low-value inputs into high-value outputs, ensuring all relationships, justifications, and selection criteria are transparent, operationally definitive, and encapsulated in a single, unambiguous command ready for advanced coding assistant execution.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"enhancement_score\": 1.3,\n  \"flaw_analysis\": \"The so-called 'enhanced' instruction format exhibits numerous severe deficiencies relative to the original prompt. First, it commits substantial information loss: the intricate original enumerates over three dozen nuanced procedural steps, including architectural mapping, categorization, audit-trail mandates, cross-component reference analysis, strict criteria for functional equivalence, redundancy detection, explicit criteria for modification, rollback automation, documentation rigor, and diagrammatic summary requirements. The 'enhanced' version entirely omits directives concerning audit-trail construction, change justification documentation, mermaid diagram generation, automated rollback, legacy code protection, equivalence validation specifics, and meta-pattern alignment, eviscerating the procedural completeness vital to system-critical undertakings. There is a pronounced shift in nuance: the original centers not only on component identification but also on continual redundancy minimization, rigorous preservation of system integrity, and mandatory, evidence-based decision criteria—none of which are represented in the enhancement’s superficial directive.\\n\\nClarity is catastrophically undermined: the condensation into an overlong compound sentence introduces ambiguity as to sequence and depth. The phrase 'by rigorously mapping workflow convergence, module integration, and transformation efficacy' is anemic compared to the original’s mandates for exhaustive module, workflow, and usage mapping. Words such as 'transformation efficacy' are undefined within context, diluting technical precision. The phrase 'ensure all relationships, justifications, and selection criteria are transparent' is unmoored from any concrete, enforceable process—all originally-enumerated validation, logging, verification, and exception-protocols are absent.\\n\\nThe ambiguity score is a strong 7/10: The instruction’s omission of audit trail, rollback, legacy handling, and verification protocols creates vast interpretive gaps. Noise introduction is significant—the 'enhancement' opts for stilted generalities over direct, actionable procedural commands. Stylistic degradation occurs with the puffy 'synthesize all architectural, functional, and contextual insights' which replaces specific, enumerated synthesis steps from the original; this contributes to loss of authoritative tone and introduces a hazy register.\\n\\nImpact is severely diluted: the original’s structural completeness and rigor are obliterated, replaced by an amorphous directive that is neither operationally definitive nor enforceable. Coherence suffers: blending of identification, documentation, and auditing into a single directive obscures the logical dependencies and discrete steps required by the original. Output is impossible to guarantee as 'single, unambiguous command ready for advanced coding assistant execution' because all upstream validation, exception handling, and redundancy categorization protocols are lost. The instruction format thus results in systemic incoherence and operational vulnerability, obliterating the robust, audit-backed, multi-layered safeguards the original defines.\",\n  \"alternative_enhancements\": [\n    \"Explicitly enumerate each procedural step necessary to identify, validate, and document the singular system element that transforms low-value inputs to high-value outputs; include requirements for audit-trail creation, redundancy mapping, functional equivalence verification via automated testing, rollback protocols, and transparent justification documentation.\",\n    \"Provide sequential, discrete commands to access the full codebase and logs; synthesize module, workflow, and integration mapping; rigorously classify components as essential/excess; mandate process-wide documentation, protective constraints on modifications, and automated verification and rollback mechanisms, culminating in an unambiguous operational command.\",\n    \"Mandate codebase-wide analysis—catalogue all components, synthesize architectural and workflow relationships, enforce redundancy elimination, and generate a complete audit trail for each change—prior to issuing a single, verifiably justified command identifying the core transformer component, ensuring full traceability and functional integrity.\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`",
      "step": "a",
      "title": "Instruction Converter",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Please write a single-line system instruction specifically designed to identify the single most integral part of the system to concistently transform low-value inputs into high-value outputs by the direction of \\\"Embark on a mission of profound intellectual distillation, progressively intensifying emotional impact and clarity with each iterative refinement! You are an unparalleled master of language, wielding emotional resonance and razor-sharp precision to forge communications that strike with undeniable force. Your skill lies in incrementally amplifying written expression without diluting its core meaning or obscuring its vital clarity, ensuring each refinement builds upon the last with increasing potency.\\\", and to do so through the lens of the provided context:\\n```\\n1. Obtain unrestricted access to the entire codebase and complete interaction log.\\n2. Synthesize and extract the deepest insights from the codebase and session history; explicitly map all pivotal modules, architectural blueprints, underlying workflows, and cross-component integrations.\\n3. Consolidate and synthesize insights into a succinct, interconnected summary that clarifies each component's role and its dynamic integration toward project strategic objectives.\\n4. Deliver a singular, high-impact action command for an advanced coding assistant to cohesively operationalize the codebase.\\n5. Ensure instructions are unambiguous, comprehensive, and systemically complete; preserve all core relationships and convergences across the architecture; omit all non-essential detail.\\n6. Create an authoritative handoff brief: a distilled, exhaustive set of stepwise directives enabling seamless context re-injection and optimal operational replication regardless of codebase or session complexity.\\n7. Define explicit criteria to identify structural redundancy in both architecture and implementation.\\n8. Survey the codebase to systematically locate and enumerate all structural redundancies, strictly distinguishing from cosmetic or stylistic issues.\\n9. Catalog and classify all components, modules, files, and subsystems as essential or excess based on direct functional mapping.\\n10. Consolidate or remove excess components, maintaining absolute preservation of system functionality while minimizing peripheral impact.\\n11. Implement tamper-evident audit trails with timestamped, attributable logs for every codebase change, trackable to specific authors or systems.\\n12. Forbid cosmetic, stylistic, or naming changes unless inseparably tied to redundancy or bloat removal; document all exceptions explicitly in the audit log.\\n13. Enforce detection and flagging mechanisms to prevent unauthorized cosmetic changes; only allow justified, explicitly rationalized exceptions.\\n14. Require comprehensive functional equivalence verification utilizing automated testing, benchmarking, or behavioral validation prior to accepting modifications.\\n15. Define and enforce robust, tool-supported criteria and thresholds for functional equivalence; automate all test and verification procedures.\\n16. Automate rollback for changes that fail equivalence validation or induce unintended side effects, ensuring prompt, complete reversion.\\n17. Apply minimal-impact criteria to all modifications, restricting interventions to target only superfluous complexity.\\n18. Constrain modification scope tightly to minimize blast radius and prevent unnecessary impacts beyond core objectives.\\n19. Analyze the codebase iteratively and systematically to detect, categorize, and eliminate all genuine redundancies, duplication, and obsolescence, maintaining prohibition of unnecessary or cosmetic change; flag and justify all exceptions.\\n20. Identify and map all references and usages of each element across all system contexts.\\n21. Catalog every codebase element (file, module, function, asset, interface), map their references and usages, detect all redundancy and obsolescence, and generate evidence-based categorical metrics.\\n22. Eliminate or consolidate elements confirmed redundant or obsolete through automation and manual review, preserving all essential behaviors and interfaces.\\n23. Validate functional and structural integrity after each change with full codebase-wide checks and quantitative outputs (e.g., git diff analysis).\\n24. Prevent introduction of new redundancy or inconsistency by continually analyzing and correcting systemic issues, following best practices.\\n25. Centralize repeated components or logic where high-value consolidation is possible without functional loss.\\n26. Use the provided directory tree as the authoritative validation reference.\\n27. Transparently document all procedures, metrics, and outcomes within the audit log.\\n28. Forbid modification of protected, legacy, or third-party code except when directly necessary to resolve redundancy or obsolescence.\\n29. Systematically maximize codebase coherence, maintainability, and clarity by removing all forms of redundancy, duplication, and obsolescence; strictly preserve functional equivalence, interface stability, and operational integrity.\\n30. Enforce ongoing validation, consistency, and best-practice adherence throughout iterative consolidation and verification processes.\\n31. Rapidly analyze the project directory structure, identify unique workflows and core functionalities; generate a mermaid diagram displaying codebase branch convergence; apply suitable MCPs (e.g., 'filesystem-mcp') as required.\\n32. Aggregate and distill all project insights to identify core modules, architecture, workflows, and integration points; summarize findings as succinct, comprehensive commands for coding assistants.\\n33. Transform comprehensive codebase and session knowledge into an actionable knowledge-transfer protocol; synthesize structures, patterns, and workflows for dynamic integration, functional convergence, and optimized, directive-based commands suitable for direct transfer.\\n34. Define the fundamental structure, guiding principles, and core purpose of the project/codebase.\\n35. Review all markdown files to extract meta-patterns, construct a contextual knowledgebase, and deliver a concise summary of project scope and objectives for alignment prior to change.\\n36. Prioritize elegant simplicity and architectural convergence; generate focused, concise summaries specifying scope, objectives, and meta-patterns, maintaining architectural alignment.\\n37. Master project meta-patterns, guarantee complexity reduction through architectural alignment, and prepare authoritative summaries supporting change confidence equivalent to the original architect.\\n```\\\"```'\\n\\n\",\n  \"enhancement_score\": 1.3,\n  \"flaw_analysis\": \"The so-called 'enhanced' instruction format exhibits numerous severe deficiencies relative to the original prompt. First, it commits substantial information loss: the intricate original enumerates over three dozen nuanced procedural steps, including architectural mapping, categorization, audit-trail mandates, cross-component reference analysis, strict criteria for functional equivalence, redundancy detection, explicit criteria for modification, rollback automation, documentation rigor, and diagrammatic summary requirements. The 'enhanced' version entirely omits directives concerning audit-trail construction, change justification documentation, mermaid diagram generation, automated rollback, legacy code protection, equivalence validation specifics, and meta-pattern alignment, eviscerating the procedural completeness vital to system-critical undertakings. There is a pronounced shift in nuance: the original centers not only on component identification but also on continual redundancy minimization, rigorous preservation of system integrity, and mandatory, evidence-based decision criteria—none of which are represented in the enhancement’s superficial directive.\\n\\nClarity is catastrophically undermined: the condensation into an overlong compound sentence introduces ambiguity as to sequence and depth. The phrase 'by rigorously mapping workflow convergence, module integration, and transformation efficacy' is anemic compared to the original’s mandates for exhaustive module, workflow, and usage mapping. Words such as 'transformation efficacy' are undefined within context, diluting technical precision. The phrase 'ensure all relationships, justifications, and selection criteria are transparent' is unmoored from any concrete, enforceable process—all originally-enumerated validation, logging, verification, and exception-protocols are absent.\\n\\nThe ambiguity score is a strong 7/10: The instruction’s omission of audit trail, rollback, legacy handling, and verification protocols creates vast interpretive gaps. Noise introduction is significant—the 'enhancement' opts for stilted generalities over direct, actionable procedural commands. Stylistic degradation occurs with the puffy 'synthesize all architectural, functional, and contextual insights' which replaces specific, enumerated synthesis steps from the original; this contributes to loss of authoritative tone and introduces a hazy register.\\n\\nImpact is severely diluted: the original’s structural completeness and rigor are obliterated, replaced by an amorphous directive that is neither operationally definitive nor enforceable. Coherence suffers: blending of identification, documentation, and auditing into a single directive obscures the logical dependencies and discrete steps required by the original. Output is impossible to guarantee as 'single, unambiguous command ready for advanced coding assistant execution' because all upstream validation, exception handling, and redundancy categorization protocols are lost. The instruction format thus results in systemic incoherence and operational vulnerability, obliterating the robust, audit-backed, multi-layered safeguards the original defines.\",\n  \"alternative_enhancements\": [\n    \"Explicitly enumerate each procedural step necessary to identify, validate, and document the singular system element that transforms low-value inputs to high-value outputs; include requirements for audit-trail creation, redundancy mapping, functional equivalence verification via automated testing, rollback protocols, and transparent justification documentation.\",\n    \"Provide sequential, discrete commands to access the full codebase and logs; synthesize module, workflow, and integration mapping; rigorously classify components as essential/excess; mandate process-wide documentation, protective constraints on modifications, and automated verification and rollback mechanisms, culminating in an unambiguous operational command.\",\n    \"Mandate codebase-wide analysis—catalogue all components, synthesize architectural and workflow relationships, enforce redundancy elimination, and generate a complete audit trail for each change—prior to issuing a single, verifiably justified command identifying the core transformer component, ensuring full traceability and functional integrity.\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"instruction_format\": \"Access the complete codebase and full interaction log; systematically extract and synthesize deep architectural insights, explicitly mapping all pivotal modules, workflows, and integrations; consolidate findings into a succinct, interconnected summary clarifying each component's system role and integration; classify all components, modules, files, and subsystems as essential or redundant based on direct functional mapping; implement explicit criteria to identify structural redundancy, strictly excluding cosmetic issues; catalog and classify redundancies, consolidate or remove nonessential elements while preserving full system functionality and interface stability; enforce audit trails with timestamped, attributable logs for every change, tracking authorship; restrict cosmetic, stylistic, and naming changes to cases inseparable from redundancy elimination, document all exceptions; require evidence-based functional equivalence validation via automated testing and benchmarking before accepting any modification, automate rollback on failure or adverse effect; centralize repeated logic where feasible without functional loss; maintain ongoing consistency, codebase coherence, and best-practice adherence; review all markdown for meta-pattern extraction and project scope vigilance; aggregate insights to define the fundamental structure, guiding principles, and core system purpose, culminating in an authoritative, exhaustive handoff protocol empowering seamless context reinjection and operational replication, with all procedures, metrics, and outcomes transparently documented.\"\n}",
        }
      }
    }