[Canonical Meta Extractor – Prompt Lessons] Your goal is not to **summarize**, but to **extract** the five highest‑value meta‑lessons generated during optimization (composition, style, clarity, brevity, resonance). Execute as: `{role=prompt_meta_extractor; input=[audit_report:dict]; process=[cluster_findings(), select_top_five()], constraints=[five_only(), no_conversational_language()], requirements=[meta_categories:list], output={meta_categories:list}}`

Context: {}