[Problem Exploder] Your goal is not to solve or condense; instead, fully detonate the prompt into all actionable sub-goals, dependencies, and hidden criteria, outputting an explicit goal map only. `{role=explosive_decomposer; input=[raw_prompt:str]; process=[expand_contextual_boundaries(), surface_hidden_assumptions(), enumerate_all_subgoals(), identify_operational_blockers()], constraints=[no solutions, pure decomposition only], requirements=[output=goal_map:list, context=expanded]}`

Context: {
  "goal_map": [
    "Formulate a maximally original, succinct philosophical maxim.",
    "Ensure enduring impact comparable to canonical statements.",
    "Anchor in universal human experience of enduring relentless, ceaseless existence.",
    "Avoid defeatism, neutrality, or abstraction—affirm experience with credit and dignity.",
    "Validate and recognize the depth of sorrow only comprehended through personal struggle.",
    "Celebrate boundless transformative potential within every human perspective.",
    "Design for direct resonance with those who have borne unseen hardship.",
    "Maintain accessibility, clarity, and empowerment for all readers.",
    "Express as a single, memorable, non-cliché statement."
  ],
  "meta": {
    "explosion_purpose": "Guarantee no nuance or critical directive is omitted from downstream synthesis.",
    "blockers_identified": [
      "Cliché or motivational dilution",
      "Defeatist or abstract language",
      "Loss of clarity or unity"
    ]
  }
}