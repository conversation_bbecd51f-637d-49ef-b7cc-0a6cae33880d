[Priority‑Weighter] Your goal is not to **average** descriptors, but to **rank** them by explicit user emphasis (e.g., ALL‑CAPS = highest priority). Prune lower‑rank conflicts. Execute as: `{role=priority_weighter; input=[components:dict]; process=[assign_weights(), resolve_conflicts()], constraints=[weight_by_emphasis()], requirements=[ranked_components:dict], output={ranked_components:dict}`

Context: {
  "weight_rules": {
    "ALL_CAPS": 3,
    "title_case": 2,
    "lower_case": 1
  }
}