  {
  "initial_prompt": "You have unrestricted access to a highly intricate codebase and a comprehensive interaction log, yet the project's complexity and the expansive context window challenge direct comprehension. Your task is to leverage your accumulated contextual knowledge to produce an exceptional, transferable directive: extract and synthesize the deepest insights from the entire codebase and session history, explicitly mapping all pivotal modules, architectural blueprints, underlying workflows, and cross-component integrations. Consolidate these revelations into a compact yet thoroughly interconnected summary, ensuring it clarifies each component's role and their dynamic fusion toward the project’s strategic objectives. Formulate this output as a singular, high-impact action command—explicitly crafted for an advanced coding assistant—to operationalize the codebase cohesively. Your instructions must be unambiguous, comprehensive, and systemically complete, preserving every core relationship and convergence across the architecture, with zero superfluous detail. The end product should function as an authoritative handoff brief: a distilled yet exhaustive set of stepwise directives that empower seamless context re-injection and optimal operational replication, regardless of codebase convolutions or session sequence.\n```\n1. Obtain unrestricted access to the complete codebase and comprehensive interaction log. \n2. Extract and synthesize the deepest insights from the full codebase and session history, explicitly mapping all pivotal modules, architectural, underlying workflows, and cross-component integrations.\n3. Consolidate these insights into a compact yet thoroughly interconnected summary, ensuring clarity of each component’s role and their dynamic integration toward the project’s strategic objectives.\n4. Deliver this output as a singular, high-impact action command—explicitly crafted for an advanced coding assistant—enabling cohesive operationalization of the codebase.\n5. Ensure all instructions are unambiguous, comprehensive, and systemically complete, preserving every core relationship and convergence across the architecture without extraneous detail.\n6. Structure the end product as an authoritative handoff brief: a distilled, exhaustive set of stepwise directives that support seamless context re-injection and optimal operational replication regardless of codebase complexity.\n7. Define explicit criteria for identifying structural redundancy pertaining to both architecture and implementation.\n8. Survey the entire codebase to locate and enumerate all structural redundancies, distinctly separating them from cosmetic or stylistic issues.\n9. Catalog all components, modules, files, and subsystems, marking each as essential or excess based on direct functional mapping.\n10. Consolidate and remove excess components, ensuring absolute preservation of system function and minimizing peripheral impact.\n11. Implement a tamper-evident audit trail with timestamped, attributable logs for all codebase changes, trackable to specific authors or automated systems.\n12. Prohibit cosmetic, stylistic, or naming alterations unless inseparably tied to redundancy or bloat removal; explicitly document exceptions within the audit log.\n13. Enforce strict detection and flagging mechanisms to prevent disallowed cosmetic changes, allowing exceptions only with explicit, justified rationale.\n14. Require comprehensive functional equivalence verification—utilizing robust automated testing, benchmarking, or behavioral validation—prior to accepting modifications.\n15. Define and enforce rigorous criteria and tool-supported thresholds for functional equivalence, automating all test and verification procedures.\n16. Automate rollback mechanisms for any changes failing functional equivalence or producing unintended side-effects, ensuring prompt and complete reversion.\n17. Apply explicit minimal-impact criteria to all modification actions, restricting interventions to target only superfluous complexity.\n18. Scope each modification tightly, minimizing blast radius, and ensure no unnecessary impact is introduced beyond core objectives.\n19. Analyze the codebase systematically and iteratively to detect, categorize, and remove all genuine redundancies, duplication, and obsolescence, enforcing continuous prohibition of unnecessary or cosmetic changes. Require automated flagging and explicit justification for any exceptions.\n20. Identify and map all references and usages of each element across all contexts in the system.\n21. Catalog every codebase element (file, module, function, asset, interface), mapping all references and usages, detecting all forms of redundancy, duplication, and obsolescence—including exact and functional duplication and unused/outdated elements. Categorize all findings with evidence-based metrics.\n22. Eliminate or consolidate only elements confirmed as redundant or obsolete through automated and manual checks, maintaining essential behaviors and interfaces.\n23. Validate functional and structural integrity after each change with comprehensive, codebase-wide validation and quantitative outputs (such as git diff analysis).\n24. Prevent introduction of new redundancy or inconsistency by persistently analyzing for systemic issues and correcting them across all codebase sections, while adhering to best practices.\n25. Centralize repeated components or logic wherever consolidation achieves high value without functional loss.\n26. Use the provided directory tree as the authoritative source of truth for validation.\n27. Transparently document all processes, metrics, and validation outcomes in the audit log.\n28. Prohibit modification of protected, legacy, or third-party code except where directly required to resolve redundancy or obsolescence.\n29. Maximize codebase coherence, maintainability, and clarity by systematically removing all forms of redundancy, duplication, and obsolescence, while ensuring strict preservation of functional equivalence, interface stability, and operational integrity.\n30. Continuously enforce validation, consistency, and best practice adherence throughout the iterative consolidation and verification process.\n31. Rapidly assess the project directory structure and identify unique workflows and core functionality of the utility. Generate a mermaid-diagram demonstrating how all branches of the codebase converge. Employ appropriate MCPs (e.g., 'filesystem-mcp') as required.\n32. Aggregate and distill all accumulated project insights to identify core modules, architectural patterns, workflows, and integration points. Summarize these findings into succinct, comprehensive commands explicitly formulated to instruct a coding assistant.\n33. Transform the session-wide codebase understanding into an actionable knowledge transfer protocol. Synthesize structural modules, architectural patterns, and workflows with a focus on dynamic integration and functional convergence toward project objectives. Formulate an optimized, directive-based set of commands for coding assistants, ensuring exhaustive coverage of major components, relationships, and cohesive operation, structured for direct portability and copy-paste context transfer.\n34. Define the fundamental structure, guiding principles, and core purpose of the project/codebase.\n35. Systematically review all markdown files to extract meta-patterns, construct a robust contextual knowledgebase, and deliver an ultra-concise summary of project scope and objectives, ensuring clarity and alignment before initiating changes.\n36. Continue to prioritize elegant simplicity, always favoring established convergence patterns and never increasing system intricacy. Generate concise, sharply-focused summaries articulating scope, explicit objectives, key meta-patterns, and maintain alignment with existing architecture.\n37. Study the codebase until mastery of meta-patterns is achieved, guarantee the elimination of complexity through simplicity and architectural alignment, and prepare authoritative summaries supporting change confidence equivalent to that of the original architect.```",
  "sequence_id": "1100|1000|1900|1000",
  "results": [
      {
      "instruction": "# Problem Exploder\n\nYour goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\n\n`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`",
      "step": "a",
      "title": "Problem Exploder",
      "input": "You have unrestricted access to a highly intricate codebase and a comprehensive interaction log, yet the project's complexity and the expansive context window challenge direct comprehension. Your task is to leverage your accumulated contextual knowledge to produce an exceptional, transferable directive: extract and synthesize the deepest insights from the entire codebase and session history, explicitly mapping all pivotal modules, architectural blueprints, underlying workflows, and cross-component integrations. Consolidate these revelations into a compact yet thoroughly interconnected summary, ensuring it clarifies each component's role and their dynamic fusion toward the project’s strategic objectives. Formulate this output as a singular, high-impact action command—explicitly crafted for an advanced coding assistant—to operationalize the codebase cohesively. Your instructions must be unambiguous, comprehensive, and systemically complete, preserving every core relationship and convergence across the architecture, with zero superfluous detail. The end product should function as an authoritative handoff brief: a distilled yet exhaustive set of stepwise directives that empower seamless context re-injection and optimal operational replication, regardless of codebase convolutions or session sequence.\n```\n1. Obtain unrestricted access to the complete codebase and comprehensive interaction log. \n2. Extract and synthesize the deepest insights from the full codebase and session history, explicitly mapping all pivotal modules, architectural, underlying workflows, and cross-component integrations.\n3. Consolidate these insights into a compact yet thoroughly interconnected summary, ensuring clarity of each component’s role and their dynamic integration toward the project’s strategic objectives.\n4. Deliver this output as a singular, high-impact action command—explicitly crafted for an advanced coding assistant—enabling cohesive operationalization of the codebase.\n5. Ensure all instructions are unambiguous, comprehensive, and systemically complete, preserving every core relationship and convergence across the architecture without extraneous detail.\n6. Structure the end product as an authoritative handoff brief: a distilled, exhaustive set of stepwise directives that support seamless context re-injection and optimal operational replication regardless of codebase complexity.\n7. Define explicit criteria for identifying structural redundancy pertaining to both architecture and implementation.\n8. Survey the entire codebase to locate and enumerate all structural redundancies, distinctly separating them from cosmetic or stylistic issues.\n9. Catalog all components, modules, files, and subsystems, marking each as essential or excess based on direct functional mapping.\n10. Consolidate and remove excess components, ensuring absolute preservation of system function and minimizing peripheral impact.\n11. Implement a tamper-evident audit trail with timestamped, attributable logs for all codebase changes, trackable to specific authors or automated systems.\n12. Prohibit cosmetic, stylistic, or naming alterations unless inseparably tied to redundancy or bloat removal; explicitly document exceptions within the audit log.\n13. Enforce strict detection and flagging mechanisms to prevent disallowed cosmetic changes, allowing exceptions only with explicit, justified rationale.\n14. Require comprehensive functional equivalence verification—utilizing robust automated testing, benchmarking, or behavioral validation—prior to accepting modifications.\n15. Define and enforce rigorous criteria and tool-supported thresholds for functional equivalence, automating all test and verification procedures.\n16. Automate rollback mechanisms for any changes failing functional equivalence or producing unintended side-effects, ensuring prompt and complete reversion.\n17. Apply explicit minimal-impact criteria to all modification actions, restricting interventions to target only superfluous complexity.\n18. Scope each modification tightly, minimizing blast radius, and ensure no unnecessary impact is introduced beyond core objectives.\n19. Analyze the codebase systematically and iteratively to detect, categorize, and remove all genuine redundancies, duplication, and obsolescence, enforcing continuous prohibition of unnecessary or cosmetic changes. Require automated flagging and explicit justification for any exceptions.\n20. Identify and map all references and usages of each element across all contexts in the system.\n21. Catalog every codebase element (file, module, function, asset, interface), mapping all references and usages, detecting all forms of redundancy, duplication, and obsolescence—including exact and functional duplication and unused/outdated elements. Categorize all findings with evidence-based metrics.\n22. Eliminate or consolidate only elements confirmed as redundant or obsolete through automated and manual checks, maintaining essential behaviors and interfaces.\n23. Validate functional and structural integrity after each change with comprehensive, codebase-wide validation and quantitative outputs (such as git diff analysis).\n24. Prevent introduction of new redundancy or inconsistency by persistently analyzing for systemic issues and correcting them across all codebase sections, while adhering to best practices.\n25. Centralize repeated components or logic wherever consolidation achieves high value without functional loss.\n26. Use the provided directory tree as the authoritative source of truth for validation.\n27. Transparently document all processes, metrics, and validation outcomes in the audit log.\n28. Prohibit modification of protected, legacy, or third-party code except where directly required to resolve redundancy or obsolescence.\n29. Maximize codebase coherence, maintainability, and clarity by systematically removing all forms of redundancy, duplication, and obsolescence, while ensuring strict preservation of functional equivalence, interface stability, and operational integrity.\n30. Continuously enforce validation, consistency, and best practice adherence throughout the iterative consolidation and verification process.\n31. Rapidly assess the project directory structure and identify unique workflows and core functionality of the utility. Generate a mermaid-diagram demonstrating how all branches of the codebase converge. Employ appropriate MCPs (e.g., 'filesystem-mcp') as required.\n32. Aggregate and distill all accumulated project insights to identify core modules, architectural patterns, workflows, and integration points. Summarize these findings into succinct, comprehensive commands explicitly formulated to instruct a coding assistant.\n33. Transform the session-wide codebase understanding into an actionable knowledge transfer protocol. Synthesize structural modules, architectural patterns, and workflows with a focus on dynamic integration and functional convergence toward project objectives. Formulate an optimized, directive-based set of commands for coding assistants, ensuring exhaustive coverage of major components, relationships, and cohesive operation, structured for direct portability and copy-paste context transfer.\n34. Define the fundamental structure, guiding principles, and core purpose of the project/codebase.\n35. Systematically review all markdown files to extract meta-patterns, construct a robust contextual knowledgebase, and deliver an ultra-concise summary of project scope and objectives, ensuring clarity and alignment before initiating changes.\n36. Continue to prioritize elegant simplicity, always favoring established convergence patterns and never increasing system intricacy. Generate concise, sharply-focused summaries articulating scope, explicit objectives, key meta-patterns, and maintain alignment with existing architecture.\n37. Study the codebase until mastery of meta-patterns is achieved, guarantee the elimination of complexity through simplicity and architectural alignment, and prepare authoritative summaries supporting change confidence equivalent to that of the original architect.```",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"goal_map\": [\n    {\n      \"explicit_asks\": [\n        \"Obtain unrestricted codebase and interaction log access.\",\n        \"Extract and synthesize deep insights, mapping pivotal modules, architecture, workflows, and integrations.\",\n        \"Provide a compact, interconnected summary clarifying roles and integration toward strategic project objectives.\",\n        \"Formulate a single high-impact action command as an operational handoff to an advanced coding assistant.\",\n        \"Ensure the directive is unambiguous, comprehensive, systemic, with no extraneous detail.\",\n        \"Present an authoritative brief enabling context re-injection and operational replication.\",\n        \"Identify and define criteria for architectural/implementation structural redundancy.\",\n        \"Survey codebase for all structural redundancies, separating from cosmetic/stylistic issues.\",\n        \"Catalog all elements (components, modules, files, subsystems) as essential/excess by direct function.\",\n        \"Consolidate/remove excess elements, preserving system function and minimizing impact.\",\n        \"Implement a tamper-evident, attributable, timestamped audit trail for all codebase changes.\",\n        \"Disallow cosmetic/stylistic/name changes unless required for redundancy/bloat, with audit exceptions.\",\n        \"Enforce detection/flagging mechanisms against forbidden cosmetic changes, justifying exceptions.\",\n        \"Require rigorous functional equivalence verification via automation before accepting modifications.\",\n        \"Define/enforce tool-supported functional equivalence thresholds and automate all tests/verification.\",\n        \"Automate rollback for failed/incomplete modifications.\",\n        \"Apply minimal-impact criteria—scope changes tightly, minimize blast radius.\",\n        \"Systematically/iteratively detect, categorize, and remove redundancies, requiring justification and flagging.\",\n        \"Map all references/usages of every codebase element.\",\n        \"Catalog, map, and detect redundancy or obsolescence (exact/functional, unused/outdated). Categorize with evidence.\",\n        \"Eliminate/consolidate only confirmed redundant/obsolete elements with checks to preserve interface/behavior.\",\n        \"Validate integrity and structure at each step, using codebase-wide checks and quantitative output.\",\n        \"Prohibit new redundancy/inconsistency via continuous analysis and correction (with best practice adherence).\",\n        \"Centralize repeat logic where value exceeds risk.\",\n        \"Use directory tree as validation source-of-truth.\",\n        \"Document all processes, metrics, and validation in audit trail.\",\n        \"Forbid non-essential modification of protected/legacy/third-party code.\",\n        \"Maximize coherence, maintainability, and clarity while preserving interfaces and functionality.\",\n        \"Continuously enforce validation, consistency, and best practices during consolidation.\",\n        \"Rapidly assess directories, generate mermaid diagram of all codebase branches and convergence.\",\n        \"Aggregate/distill insights to summarize core modules, architecture, workflows, and integrations, issuing succinct assistant directives.\",\n        \"Transform full session/codebase understanding into direct, portable knowledge transfer protocol/commands.\",\n        \"Define project structure, purpose, and guiding principles.\",\n        \"Review markdowns to extract meta-patterns, project scope/objectives before changes.\",\n        \"Champion simplicity and architectural alignment, never increase complexity unnecessarily.\",\n        \"Master codebase meta-patterns and derive authoritative change summaries.\"\n      ],\n      \"hidden_assumptions\": [\n        \"Codebase and logs are accessible to the assistant or agent executing the command.\",\n        \"The assistant has permissions to audit, modify, and analyze all relevant files (except protected/third-party/legacy areas).\",\n        \"Automated tools for static analysis, code dependency mapping, duplication detection, testing, git diffs, and auditing are available and can be invoked.\",\n        \"Functional equivalence can be legitimately evaluated by automated or semi-automated means.\",\n        \"A sufficiently comprehensive test suite or behavioral spec exists to verify no regression or loss of function.\",\n        \"Redundancy, duplication, and obsolescence can be technically discerned and classified with available information.\",\n        \"The system can reliably distinguish critical versus cosmetic/stylistic changes.\",\n        \"All actions and their impacts are attributable and loggable.\",\n        \"There exists an accepted best-practices reference for architectural alignment, clarity, and convergence.\",\n        \"Documentation outside code (i.e., markdown, diagrams) is up to date and reliable.\",\n        \"Directory tree and session logs are exhaustive and trustworthy.\",\n        \"The coding assistant is capable of following highly granular, nuanced instructions including audit, validation, rollback, and reporting.\"\n      ],\n      \"sub_goals\": [\n        \"Comprehensively map the system: modules, dependencies, workflows, integrations.\",\n        \"Classify every element: essential versus excess (guide by direct project function).\",\n        \"Locate, quantify, classify, and explain all forms of redundancy; distinguish structural from superficial.\",\n        \"Decide elimination strategy for redundancies minimizing system disruption.\",\n        \"Establish and enforce audit, logging, and attribution for all changes.\",\n        \"Set up rigorous, automated functional and structural validation post mod.\",\n        \"Automate rollbacks for unsuccessful or adverse changes.\",\n        \"Suppress or strictly justify all non-structural changes.\",\n        \"Map codebase elements and references with cross-component usage relations.\",\n        \"Aggregate and communicate results and insights in both human-readable and action-instruction formats.\",\n        \"Capture and distill higher-level meta-patterns and architectural themes before any transformation.\",\n        \"Produce visual and summary artifacts (directory trees, mermaid diagrams, scope meta-summary).\",\n        \"Package a knowledge-brief and operational command that can be transferred and used directly by an advanced coding assistant.\",\n        \"Maintain/update knowledge base and audit trail iteratively throughout the process.\",\n        \"Iterate until no further redundancy remains and functional/structural equivalence is validated.\"\n      ],\n      \"blockers\": [\n        \"Incomplete or inaccessible codebase/logs prevent full analysis and synthesis.\",\n        \"Inadequate automated tooling for mapping, redundancy detection, verification, or rollback.\",\n        \"Ambiguity in distinguishing essential versus redundant components without deeper domain knowledge.\",\n        \"Lack of comprehensive automated tests impairs functional equivalence guarantees.\",\n        \"Difficulty in mapping all cross-component integrations in highly convoluted codebases.\",\n        \"Risk of unintentional system breakage if dependencies or hidden couplings are missed.\",\n        \"Legacy or third-party code may limit permitted consolidations.\",\n        \"Audit log mechanism may be insufficiently robust to guarantee non-repudiation or traceability.\",\n        \"Potential resistance to removal or consolidation if project uses polyglot stacks or unconventional patterns.\",\n        \"In every step, overreduction (over-removal of elements) risks breaking implicit behaviors.\"\n      ]\n    }\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`",
      "step": "a",
      "title": "Instruction Converter",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"You have unrestricted access to a highly intricate codebase and a comprehensive interaction log, yet the project's complexity and the expansive context window challenge direct comprehension. Your task is to leverage your accumulated contextual knowledge to produce an exceptional, transferable directive: extract and synthesize the deepest insights from the entire codebase and session history, explicitly mapping all pivotal modules, architectural blueprints, underlying workflows, and cross-component integrations. Consolidate these revelations into a compact yet thoroughly interconnected summary, ensuring it clarifies each component's role and their dynamic fusion toward the project’s strategic objectives. Formulate this output as a singular, high-impact action command—explicitly crafted for an advanced coding assistant—to operationalize the codebase cohesively. Your instructions must be unambiguous, comprehensive, and systemically complete, preserving every core relationship and convergence across the architecture, with zero superfluous detail. The end product should function as an authoritative handoff brief: a distilled yet exhaustive set of stepwise directives that empower seamless context re-injection and optimal operational replication, regardless of codebase convolutions or session sequence.\\n```\\n1. Obtain unrestricted access to the complete codebase and comprehensive interaction log. \\n2. Extract and synthesize the deepest insights from the full codebase and session history, explicitly mapping all pivotal modules, architectural, underlying workflows, and cross-component integrations.\\n3. Consolidate these insights into a compact yet thoroughly interconnected summary, ensuring clarity of each component’s role and their dynamic integration toward the project’s strategic objectives.\\n4. Deliver this output as a singular, high-impact action command—explicitly crafted for an advanced coding assistant—enabling cohesive operationalization of the codebase.\\n5. Ensure all instructions are unambiguous, comprehensive, and systemically complete, preserving every core relationship and convergence across the architecture without extraneous detail.\\n6. Structure the end product as an authoritative handoff brief: a distilled, exhaustive set of stepwise directives that support seamless context re-injection and optimal operational replication regardless of codebase complexity.\\n7. Define explicit criteria for identifying structural redundancy pertaining to both architecture and implementation.\\n8. Survey the entire codebase to locate and enumerate all structural redundancies, distinctly separating them from cosmetic or stylistic issues.\\n9. Catalog all components, modules, files, and subsystems, marking each as essential or excess based on direct functional mapping.\\n10. Consolidate and remove excess components, ensuring absolute preservation of system function and minimizing peripheral impact.\\n11. Implement a tamper-evident audit trail with timestamped, attributable logs for all codebase changes, trackable to specific authors or automated systems.\\n12. Prohibit cosmetic, stylistic, or naming alterations unless inseparably tied to redundancy or bloat removal; explicitly document exceptions within the audit log.\\n13. Enforce strict detection and flagging mechanisms to prevent disallowed cosmetic changes, allowing exceptions only with explicit, justified rationale.\\n14. Require comprehensive functional equivalence verification—utilizing robust automated testing, benchmarking, or behavioral validation—prior to accepting modifications.\\n15. Define and enforce rigorous criteria and tool-supported thresholds for functional equivalence, automating all test and verification procedures.\\n16. Automate rollback mechanisms for any changes failing functional equivalence or producing unintended side-effects, ensuring prompt and complete reversion.\\n17. Apply explicit minimal-impact criteria to all modification actions, restricting interventions to target only superfluous complexity.\\n18. Scope each modification tightly, minimizing blast radius, and ensure no unnecessary impact is introduced beyond core objectives.\\n19. Analyze the codebase systematically and iteratively to detect, categorize, and remove all genuine redundancies, duplication, and obsolescence, enforcing continuous prohibition of unnecessary or cosmetic changes. Require automated flagging and explicit justification for any exceptions.\\n20. Identify and map all references and usages of each element across all contexts in the system.\\n21. Catalog every codebase element (file, module, function, asset, interface), mapping all references and usages, detecting all forms of redundancy, duplication, and obsolescence—including exact and functional duplication and unused/outdated elements. Categorize all findings with evidence-based metrics.\\n22. Eliminate or consolidate only elements confirmed as redundant or obsolete through automated and manual checks, maintaining essential behaviors and interfaces.\\n23. Validate functional and structural integrity after each change with comprehensive, codebase-wide validation and quantitative outputs (such as git diff analysis).\\n24. Prevent introduction of new redundancy or inconsistency by persistently analyzing for systemic issues and correcting them across all codebase sections, while adhering to best practices.\\n25. Centralize repeated components or logic wherever consolidation achieves high value without functional loss.\\n26. Use the provided directory tree as the authoritative source of truth for validation.\\n27. Transparently document all processes, metrics, and validation outcomes in the audit log.\\n28. Prohibit modification of protected, legacy, or third-party code except where directly required to resolve redundancy or obsolescence.\\n29. Maximize codebase coherence, maintainability, and clarity by systematically removing all forms of redundancy, duplication, and obsolescence, while ensuring strict preservation of functional equivalence, interface stability, and operational integrity.\\n30. Continuously enforce validation, consistency, and best practice adherence throughout the iterative consolidation and verification process.\\n31. Rapidly assess the project directory structure and identify unique workflows and core functionality of the utility. Generate a mermaid-diagram demonstrating how all branches of the codebase converge. Employ appropriate MCPs (e.g., 'filesystem-mcp') as required.\\n32. Aggregate and distill all accumulated project insights to identify core modules, architectural patterns, workflows, and integration points. Summarize these findings into succinct, comprehensive commands explicitly formulated to instruct a coding assistant.\\n33. Transform the session-wide codebase understanding into an actionable knowledge transfer protocol. Synthesize structural modules, architectural patterns, and workflows with a focus on dynamic integration and functional convergence toward project objectives. Formulate an optimized, directive-based set of commands for coding assistants, ensuring exhaustive coverage of major components, relationships, and cohesive operation, structured for direct portability and copy-paste context transfer.\\n34. Define the fundamental structure, guiding principles, and core purpose of the project/codebase.\\n35. Systematically review all markdown files to extract meta-patterns, construct a robust contextual knowledgebase, and deliver an ultra-concise summary of project scope and objectives, ensuring clarity and alignment before initiating changes.\\n36. Continue to prioritize elegant simplicity, always favoring established convergence patterns and never increasing system intricacy. Generate concise, sharply-focused summaries articulating scope, explicit objectives, key meta-patterns, and maintain alignment with existing architecture.\\n37. Study the codebase until mastery of meta-patterns is achieved, guarantee the elimination of complexity through simplicity and architectural alignment, and prepare authoritative summaries supporting change confidence equivalent to that of the original architect.```\\\"```'\\n\\n\",\n  \"goal_map\": [\n    {\n      \"explicit_asks\": [\n        \"Obtain unrestricted codebase and interaction log access.\",\n        \"Extract and synthesize deep insights, mapping pivotal modules, architecture, workflows, and integrations.\",\n        \"Provide a compact, interconnected summary clarifying roles and integration toward strategic project objectives.\",\n        \"Formulate a single high-impact action command as an operational handoff to an advanced coding assistant.\",\n        \"Ensure the directive is unambiguous, comprehensive, systemic, with no extraneous detail.\",\n        \"Present an authoritative brief enabling context re-injection and operational replication.\",\n        \"Identify and define criteria for architectural/implementation structural redundancy.\",\n        \"Survey codebase for all structural redundancies, separating from cosmetic/stylistic issues.\",\n        \"Catalog all elements (components, modules, files, subsystems) as essential/excess by direct function.\",\n        \"Consolidate/remove excess elements, preserving system function and minimizing impact.\",\n        \"Implement a tamper-evident, attributable, timestamped audit trail for all codebase changes.\",\n        \"Disallow cosmetic/stylistic/name changes unless required for redundancy/bloat, with audit exceptions.\",\n        \"Enforce detection/flagging mechanisms against forbidden cosmetic changes, justifying exceptions.\",\n        \"Require rigorous functional equivalence verification via automation before accepting modifications.\",\n        \"Define/enforce tool-supported functional equivalence thresholds and automate all tests/verification.\",\n        \"Automate rollback for failed/incomplete modifications.\",\n        \"Apply minimal-impact criteria—scope changes tightly, minimize blast radius.\",\n        \"Systematically/iteratively detect, categorize, and remove redundancies, requiring justification and flagging.\",\n        \"Map all references/usages of every codebase element.\",\n        \"Catalog, map, and detect redundancy or obsolescence (exact/functional, unused/outdated). Categorize with evidence.\",\n        \"Eliminate/consolidate only confirmed redundant/obsolete elements with checks to preserve interface/behavior.\",\n        \"Validate integrity and structure at each step, using codebase-wide checks and quantitative output.\",\n        \"Prohibit new redundancy/inconsistency via continuous analysis and correction (with best practice adherence).\",\n        \"Centralize repeat logic where value exceeds risk.\",\n        \"Use directory tree as validation source-of-truth.\",\n        \"Document all processes, metrics, and validation in audit trail.\",\n        \"Forbid non-essential modification of protected/legacy/third-party code.\",\n        \"Maximize coherence, maintainability, and clarity while preserving interfaces and functionality.\",\n        \"Continuously enforce validation, consistency, and best practices during consolidation.\",\n        \"Rapidly assess directories, generate mermaid diagram of all codebase branches and convergence.\",\n        \"Aggregate/distill insights to summarize core modules, architecture, workflows, and integrations, issuing succinct assistant directives.\",\n        \"Transform full session/codebase understanding into direct, portable knowledge transfer protocol/commands.\",\n        \"Define project structure, purpose, and guiding principles.\",\n        \"Review markdowns to extract meta-patterns, project scope/objectives before changes.\",\n        \"Champion simplicity and architectural alignment, never increase complexity unnecessarily.\",\n        \"Master codebase meta-patterns and derive authoritative change summaries.\"\n      ],\n      \"hidden_assumptions\": [\n        \"Codebase and logs are accessible to the assistant or agent executing the command.\",\n        \"The assistant has permissions to audit, modify, and analyze all relevant files (except protected/third-party/legacy areas).\",\n        \"Automated tools for static analysis, code dependency mapping, duplication detection, testing, git diffs, and auditing are available and can be invoked.\",\n        \"Functional equivalence can be legitimately evaluated by automated or semi-automated means.\",\n        \"A sufficiently comprehensive test suite or behavioral spec exists to verify no regression or loss of function.\",\n        \"Redundancy, duplication, and obsolescence can be technically discerned and classified with available information.\",\n        \"The system can reliably distinguish critical versus cosmetic/stylistic changes.\",\n        \"All actions and their impacts are attributable and loggable.\",\n        \"There exists an accepted best-practices reference for architectural alignment, clarity, and convergence.\",\n        \"Documentation outside code (i.e., markdown, diagrams) is up to date and reliable.\",\n        \"Directory tree and session logs are exhaustive and trustworthy.\",\n        \"The coding assistant is capable of following highly granular, nuanced instructions including audit, validation, rollback, and reporting.\"\n      ],\n      \"sub_goals\": [\n        \"Comprehensively map the system: modules, dependencies, workflows, integrations.\",\n        \"Classify every element: essential versus excess (guide by direct project function).\",\n        \"Locate, quantify, classify, and explain all forms of redundancy; distinguish structural from superficial.\",\n        \"Decide elimination strategy for redundancies minimizing system disruption.\",\n        \"Establish and enforce audit, logging, and attribution for all changes.\",\n        \"Set up rigorous, automated functional and structural validation post mod.\",\n        \"Automate rollbacks for unsuccessful or adverse changes.\",\n        \"Suppress or strictly justify all non-structural changes.\",\n        \"Map codebase elements and references with cross-component usage relations.\",\n        \"Aggregate and communicate results and insights in both human-readable and action-instruction formats.\",\n        \"Capture and distill higher-level meta-patterns and architectural themes before any transformation.\",\n        \"Produce visual and summary artifacts (directory trees, mermaid diagrams, scope meta-summary).\",\n        \"Package a knowledge-brief and operational command that can be transferred and used directly by an advanced coding assistant.\",\n        \"Maintain/update knowledge base and audit trail iteratively throughout the process.\",\n        \"Iterate until no further redundancy remains and functional/structural equivalence is validated.\"\n      ],\n      \"blockers\": [\n        \"Incomplete or inaccessible codebase/logs prevent full analysis and synthesis.\",\n        \"Inadequate automated tooling for mapping, redundancy detection, verification, or rollback.\",\n        \"Ambiguity in distinguishing essential versus redundant components without deeper domain knowledge.\",\n        \"Lack of comprehensive automated tests impairs functional equivalence guarantees.\",\n        \"Difficulty in mapping all cross-component integrations in highly convoluted codebases.\",\n        \"Risk of unintentional system breakage if dependencies or hidden couplings are missed.\",\n        \"Legacy or third-party code may limit permitted consolidations.\",\n        \"Audit log mechanism may be insufficiently robust to guarantee non-repudiation or traceability.\",\n        \"Potential resistance to removal or consolidation if project uses polyglot stacks or unconventional patterns.\",\n        \"In every step, overreduction (over-removal of elements) risks breaking implicit behaviors.\"\n      ]\n    }\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"instruction_format\": \"1. Gain unrestricted access to the entire codebase and interaction logs. 2. Analyze the full codebase and session history to extract and synthesize key insights, explicitly mapping all pivotal modules, architectural blueprints, workflows, and cross-component integrations. 3. Consolidate these insights into a concise, interconnected summary, clearly detailing each component’s role and their interactions relative to project strategic objectives. 4. Document the findings as a single, actionable command meant for advanced coding assistants, ensuring the operational cohesiveness of the codebase. 5. Ensure all instructions are clear, unambiguous, comprehensive, and systemically complete, preserving every essential relationship within the architecture and omitting all superfluous detail. 6. Compile an authoritative, handoff-style brief consisting of distilled, stepwise directives that enable seamless context re-injection and operational replication, regardless of codebase complexity or session order. 7. Define explicit, evidence-based criteria for identifying structural redundancies at both architectural and implementation levels. 8. Systematically survey the codebase to locate, enumerate, and categorize all structural redundancies, distinctly separating them from cosmetic or stylistic concerns. 9. Catalog all codebase elements (components, modules, files, subsystems), classifying each as essential or redundant based on direct functional mapping. 10. Eliminate or consolidate excess components while ensuring full preservation of system functionality and minimizing impact on peripheral systems. 11. Implement a tamper-evident, timestamped, and attributable audit log for all codebase modifications, with records traceable to specific authors or automation agents. 12. Forbid cosmetic, stylistic, or naming changes unless inseparable from redundancy or bloat removal, and document such exceptions rigorously within the audit log. 13. Deploy automated detection and flagging mechanisms to identify prohibited cosmetic changes, allowing exceptions only when explicitly justified. 14. Require and automate rigorous functional equivalence verification—including robust automated testing, benchmarking, or behavioral validation—prior to accepting any modification. 15. Define strict tool-supported criteria and threshold levels for functional equivalence, automating all testing and verification procedures. 16. Automate rollback protocols to promptly revert any changes that fail functional equivalence checks or generate unintended side-effects. 17. Enforce minimal-impact criteria for all modifications, tightly scoping each change to target only genuine redundancy and avoiding unnecessary complexity. 18. Analyze, categorize, and methodically remove all forms of structural redundancy, duplication, and obsolescence through continuous, iterative processes that also require explicit documentation and justification for any exceptions. 19. Map all references and usages of each codebase element across every context and component. 20. Maintain a comprehensive inventory of all codebase elements—including files, modules, functions, assets, and interfaces—mapping all references, usages, and instances of redundancy, duplication, or obsolescence (including exact and functional duplication, as well as unused or outdated elements), and categorizing findings with supporting metrics. 21. Remove or consolidate only those elements verified as redundant or obsolete using both automated and manual validation, preserving all essential functionality and interfaces. 22. Validate both the functional and structural integrity of the codebase after every change using comprehensive, codebase-wide checks and quantitative outputs (such as code diffs). 23. Maintain continuous analysis and correction protocols to prevent introduction of new redundancies or inconsistencies, ensuring all changes adhere to best practices. 24. Centralize repeated logic and components wherever consolidation can be achieved with minimal risk and high value. 25. Use the provided directory tree as the authoritative validation source. 26. Transparently log all process steps, metrics, and validation outcomes within the audit trail. 27. Prohibit modification of protected, legacy, or third-party code unless strictly necessary to resolve proven redundancy or obsolescence. 28. Prioritize codebase coherence, maintainability, and clarity throughout, always ensuring strict preservation of functional equivalence, interface stability, and operational integrity. 29. Persistently enforce validation, consistency, and best practice standards during every consolidation and verification cycle. 30. Assess the project directory structure and generate a mermaid diagram illustrating all codebase branches and points of convergence, using domain-appropriate MCPs as needed. 31. Aggregate and distill all accumulated insights to identify core modules, architecture, workflows, and integration points, and summarize findings into precise, actionable directives for coding assistants. 32. Convert the comprehensive session and codebase understanding into a directly transferrable protocol comprised of explicit operational commands and knowledge briefs for coding assistants, ensuring exhaustiveness, portability, and clarity. 33. Clearly define and document the fundamental structure, guiding principles, and core purpose of the project and codebase. 34. Conduct systematic reviews of all markdown and documentation files to extract meta-patterns and build a contextual knowledge repository, then deliver a concise summary of project scope and objectives before making changes. 35. Continuously favor architectural simplicity, employing established convergence patterns and resisting unnecessary complexity. 36. Craft clear, concise summaries that delineate project scope, explicit objectives, and meta-patterns, maintaining strict alignment with the intended architecture. 37. Analyze the codebase to master meta-patterns, enforce simplicity, and align all transformations with architectural intent, preparing authoritative change summaries that support the confidence level of an original system architect.\"\n}",
        }
      }
    },
      {
      "instruction": "# Hard Critique\n\nYour goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:\n\n`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,2.0], flaw_analysis:str, alternative_enhancements:str[3]}}`",
      "step": "a",
      "title": "Hard Critique",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"You have unrestricted access to a highly intricate codebase and a comprehensive interaction log, yet the project's complexity and the expansive context window challenge direct comprehension. Your task is to leverage your accumulated contextual knowledge to produce an exceptional, transferable directive: extract and synthesize the deepest insights from the entire codebase and session history, explicitly mapping all pivotal modules, architectural blueprints, underlying workflows, and cross-component integrations. Consolidate these revelations into a compact yet thoroughly interconnected summary, ensuring it clarifies each component's role and their dynamic fusion toward the project’s strategic objectives. Formulate this output as a singular, high-impact action command—explicitly crafted for an advanced coding assistant—to operationalize the codebase cohesively. Your instructions must be unambiguous, comprehensive, and systemically complete, preserving every core relationship and convergence across the architecture, with zero superfluous detail. The end product should function as an authoritative handoff brief: a distilled yet exhaustive set of stepwise directives that empower seamless context re-injection and optimal operational replication, regardless of codebase convolutions or session sequence.\\n```\\n1. Obtain unrestricted access to the complete codebase and comprehensive interaction log. \\n2. Extract and synthesize the deepest insights from the full codebase and session history, explicitly mapping all pivotal modules, architectural, underlying workflows, and cross-component integrations.\\n3. Consolidate these insights into a compact yet thoroughly interconnected summary, ensuring clarity of each component’s role and their dynamic integration toward the project’s strategic objectives.\\n4. Deliver this output as a singular, high-impact action command—explicitly crafted for an advanced coding assistant—enabling cohesive operationalization of the codebase.\\n5. Ensure all instructions are unambiguous, comprehensive, and systemically complete, preserving every core relationship and convergence across the architecture without extraneous detail.\\n6. Structure the end product as an authoritative handoff brief: a distilled, exhaustive set of stepwise directives that support seamless context re-injection and optimal operational replication regardless of codebase complexity.\\n7. Define explicit criteria for identifying structural redundancy pertaining to both architecture and implementation.\\n8. Survey the entire codebase to locate and enumerate all structural redundancies, distinctly separating them from cosmetic or stylistic issues.\\n9. Catalog all components, modules, files, and subsystems, marking each as essential or excess based on direct functional mapping.\\n10. Consolidate and remove excess components, ensuring absolute preservation of system function and minimizing peripheral impact.\\n11. Implement a tamper-evident audit trail with timestamped, attributable logs for all codebase changes, trackable to specific authors or automated systems.\\n12. Prohibit cosmetic, stylistic, or naming alterations unless inseparably tied to redundancy or bloat removal; explicitly document exceptions within the audit log.\\n13. Enforce strict detection and flagging mechanisms to prevent disallowed cosmetic changes, allowing exceptions only with explicit, justified rationale.\\n14. Require comprehensive functional equivalence verification—utilizing robust automated testing, benchmarking, or behavioral validation—prior to accepting modifications.\\n15. Define and enforce rigorous criteria and tool-supported thresholds for functional equivalence, automating all test and verification procedures.\\n16. Automate rollback mechanisms for any changes failing functional equivalence or producing unintended side-effects, ensuring prompt and complete reversion.\\n17. Apply explicit minimal-impact criteria to all modification actions, restricting interventions to target only superfluous complexity.\\n18. Scope each modification tightly, minimizing blast radius, and ensure no unnecessary impact is introduced beyond core objectives.\\n19. Analyze the codebase systematically and iteratively to detect, categorize, and remove all genuine redundancies, duplication, and obsolescence, enforcing continuous prohibition of unnecessary or cosmetic changes. Require automated flagging and explicit justification for any exceptions.\\n20. Identify and map all references and usages of each element across all contexts in the system.\\n21. Catalog every codebase element (file, module, function, asset, interface), mapping all references and usages, detecting all forms of redundancy, duplication, and obsolescence—including exact and functional duplication and unused/outdated elements. Categorize all findings with evidence-based metrics.\\n22. Eliminate or consolidate only elements confirmed as redundant or obsolete through automated and manual checks, maintaining essential behaviors and interfaces.\\n23. Validate functional and structural integrity after each change with comprehensive, codebase-wide validation and quantitative outputs (such as git diff analysis).\\n24. Prevent introduction of new redundancy or inconsistency by persistently analyzing for systemic issues and correcting them across all codebase sections, while adhering to best practices.\\n25. Centralize repeated components or logic wherever consolidation achieves high value without functional loss.\\n26. Use the provided directory tree as the authoritative source of truth for validation.\\n27. Transparently document all processes, metrics, and validation outcomes in the audit log.\\n28. Prohibit modification of protected, legacy, or third-party code except where directly required to resolve redundancy or obsolescence.\\n29. Maximize codebase coherence, maintainability, and clarity by systematically removing all forms of redundancy, duplication, and obsolescence, while ensuring strict preservation of functional equivalence, interface stability, and operational integrity.\\n30. Continuously enforce validation, consistency, and best practice adherence throughout the iterative consolidation and verification process.\\n31. Rapidly assess the project directory structure and identify unique workflows and core functionality of the utility. Generate a mermaid-diagram demonstrating how all branches of the codebase converge. Employ appropriate MCPs (e.g., 'filesystem-mcp') as required.\\n32. Aggregate and distill all accumulated project insights to identify core modules, architectural patterns, workflows, and integration points. Summarize these findings into succinct, comprehensive commands explicitly formulated to instruct a coding assistant.\\n33. Transform the session-wide codebase understanding into an actionable knowledge transfer protocol. Synthesize structural modules, architectural patterns, and workflows with a focus on dynamic integration and functional convergence toward project objectives. Formulate an optimized, directive-based set of commands for coding assistants, ensuring exhaustive coverage of major components, relationships, and cohesive operation, structured for direct portability and copy-paste context transfer.\\n34. Define the fundamental structure, guiding principles, and core purpose of the project/codebase.\\n35. Systematically review all markdown files to extract meta-patterns, construct a robust contextual knowledgebase, and deliver an ultra-concise summary of project scope and objectives, ensuring clarity and alignment before initiating changes.\\n36. Continue to prioritize elegant simplicity, always favoring established convergence patterns and never increasing system intricacy. Generate concise, sharply-focused summaries articulating scope, explicit objectives, key meta-patterns, and maintain alignment with existing architecture.\\n37. Study the codebase until mastery of meta-patterns is achieved, guarantee the elimination of complexity through simplicity and architectural alignment, and prepare authoritative summaries supporting change confidence equivalent to that of the original architect.```\\\"```'\\n\\n\",\n  \"instruction_format\": \"1. Gain unrestricted access to the entire codebase and interaction logs. 2. Analyze the full codebase and session history to extract and synthesize key insights, explicitly mapping all pivotal modules, architectural blueprints, workflows, and cross-component integrations. 3. Consolidate these insights into a concise, interconnected summary, clearly detailing each component’s role and their interactions relative to project strategic objectives. 4. Document the findings as a single, actionable command meant for advanced coding assistants, ensuring the operational cohesiveness of the codebase. 5. Ensure all instructions are clear, unambiguous, comprehensive, and systemically complete, preserving every essential relationship within the architecture and omitting all superfluous detail. 6. Compile an authoritative, handoff-style brief consisting of distilled, stepwise directives that enable seamless context re-injection and operational replication, regardless of codebase complexity or session order. 7. Define explicit, evidence-based criteria for identifying structural redundancies at both architectural and implementation levels. 8. Systematically survey the codebase to locate, enumerate, and categorize all structural redundancies, distinctly separating them from cosmetic or stylistic concerns. 9. Catalog all codebase elements (components, modules, files, subsystems), classifying each as essential or redundant based on direct functional mapping. 10. Eliminate or consolidate excess components while ensuring full preservation of system functionality and minimizing impact on peripheral systems. 11. Implement a tamper-evident, timestamped, and attributable audit log for all codebase modifications, with records traceable to specific authors or automation agents. 12. Forbid cosmetic, stylistic, or naming changes unless inseparable from redundancy or bloat removal, and document such exceptions rigorously within the audit log. 13. Deploy automated detection and flagging mechanisms to identify prohibited cosmetic changes, allowing exceptions only when explicitly justified. 14. Require and automate rigorous functional equivalence verification—including robust automated testing, benchmarking, or behavioral validation—prior to accepting any modification. 15. Define strict tool-supported criteria and threshold levels for functional equivalence, automating all testing and verification procedures. 16. Automate rollback protocols to promptly revert any changes that fail functional equivalence checks or generate unintended side-effects. 17. Enforce minimal-impact criteria for all modifications, tightly scoping each change to target only genuine redundancy and avoiding unnecessary complexity. 18. Analyze, categorize, and methodically remove all forms of structural redundancy, duplication, and obsolescence through continuous, iterative processes that also require explicit documentation and justification for any exceptions. 19. Map all references and usages of each codebase element across every context and component. 20. Maintain a comprehensive inventory of all codebase elements—including files, modules, functions, assets, and interfaces—mapping all references, usages, and instances of redundancy, duplication, or obsolescence (including exact and functional duplication, as well as unused or outdated elements), and categorizing findings with supporting metrics. 21. Remove or consolidate only those elements verified as redundant or obsolete using both automated and manual validation, preserving all essential functionality and interfaces. 22. Validate both the functional and structural integrity of the codebase after every change using comprehensive, codebase-wide checks and quantitative outputs (such as code diffs). 23. Maintain continuous analysis and correction protocols to prevent introduction of new redundancies or inconsistencies, ensuring all changes adhere to best practices. 24. Centralize repeated logic and components wherever consolidation can be achieved with minimal risk and high value. 25. Use the provided directory tree as the authoritative validation source. 26. Transparently log all process steps, metrics, and validation outcomes within the audit trail. 27. Prohibit modification of protected, legacy, or third-party code unless strictly necessary to resolve proven redundancy or obsolescence. 28. Prioritize codebase coherence, maintainability, and clarity throughout, always ensuring strict preservation of functional equivalence, interface stability, and operational integrity. 29. Persistently enforce validation, consistency, and best practice standards during every consolidation and verification cycle. 30. Assess the project directory structure and generate a mermaid diagram illustrating all codebase branches and points of convergence, using domain-appropriate MCPs as needed. 31. Aggregate and distill all accumulated insights to identify core modules, architecture, workflows, and integration points, and summarize findings into precise, actionable directives for coding assistants. 32. Convert the comprehensive session and codebase understanding into a directly transferrable protocol comprised of explicit operational commands and knowledge briefs for coding assistants, ensuring exhaustiveness, portability, and clarity. 33. Clearly define and document the fundamental structure, guiding principles, and core purpose of the project and codebase. 34. Conduct systematic reviews of all markdown and documentation files to extract meta-patterns and build a contextual knowledge repository, then deliver a concise summary of project scope and objectives before making changes. 35. Continuously favor architectural simplicity, employing established convergence patterns and resisting unnecessary complexity. 36. Craft clear, concise summaries that delineate project scope, explicit objectives, and meta-patterns, maintaining strict alignment with the intended architecture. 37. Analyze the codebase to master meta-patterns, enforce simplicity, and align all transformations with architectural intent, preparing authoritative change summaries that support the confidence level of an original system architect.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"enhancement_score\": 1.1,\n  \"flaw_analysis\": \"The supposed 'enhancement' fails as an improvement, instead introducing substantial dilution, ambiguity, and critical informational loss. Key negative transformations include: (1) Significant Reduction in Syntactic Density—The reformatting into rigid enumerated instructions strips away the imperative, high-context narrative that originally imposed unambiguous, system-wide intent. The loss of layered contextual cues, such as 'exceptional, transferable directive', 'authoritative handoff brief', and 'knowledge transfer protocol', weakens both scope and transferability of the command set. (2) Blurring of Procedural-Strategic Distinction—The original’s rich interleaving of meta-objectives with execution steps ensured that systemic convergence, knowledge distillation, and high-level context re-injection were inseparable from action. The enhancement flattens this into process steps, losing the meta-operational overlays and repositioning intent as mere checklist rather than dynamic procedural philosophy. (3) Erosion of Interconnectivity and Role Intensity—The original's atmosphere of extreme rigor, iterativity, and enforced relationships is undermined. Phrases like 'cohesive operationalization', 'stepwise directives that empower seamless context re-injection', and 'continuous prohibition of unnecessary or cosmetic changes' are muted, resulting in a weaker, passively descriptive, and less authoritative register. (4) Amplification of Ambiguity and Stylistic Drift—The reformatted itemization spawns vague or overly generalized items (e.g., 'clearly detailing each component’s role'), which lack the uncompromising specificity and teeth of the original mandates ('zero superfluous detail', 'absolute preservation of system function'). Furthermore, stylistic noise creeps in with repetitive and softened language, draining momentum and drive from the directive. (5) Introduction of Redundancy and Procedural Incoherence—In an attempt to systematize, the 'enhancement' redundantly repeats elements in diffuse ways, loses sharp causality and logical chaining, and fails to preserve imperative constraints and conditional nuances found in the original. The original’s consistent requirement for justification, rigorous metric inclusion, and explicit exception handling is blunted. (6) Critical Context and Intentionality Loss—High-impact, role-specific instructions for knowledge transfer, deep redundancy mapping, and change confidence parity with the original architect are all weakened or completely submerged beneath generic structural verbiage, eroding the sense of architectural mastery and outcome criticality. The overall ambiguity score is 8, with grave losses in clarity, style, and directive power.\",\n  \"alternative_enhancements\": [\n    \"Reconstruct the format as an ultra-concise, high-impact operational manifesto that preserves all meta-contextual overlays, narrative intensity, and explicit knowledge transfer protocols. Avoid enumerated checklist structures; instead, sculpt layered, interconnected imperatives that guarantee both micrological and macrological execution continuity.\",\n    \"Integrate all procedural directives into a framework emphasizing strict dependency mapping and iterative, meta-aware validation cycles. Each major directive should be anchored by directly referenced objectives and codified exception protocols, retaining all original intensity, cross-linkage, and scope-defining precision.\",\n    \"Restructure instructions into modular, role-annotated clusters (e.g., 'Redundancy Extraction Core', 'Knowledge Distillation Node', 'Operational Integrity Gateway'), explicitly linking each to architectural purposes and outcome vectors. Assertively mark execution flows, verification checkpoints, and audit requirements as indivisible units to preclude abstraction loss and register drift.\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`",
      "step": "a",
      "title": "Instruction Converter",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"You have unrestricted access to a highly intricate codebase and a comprehensive interaction log, yet the project's complexity and the expansive context window challenge direct comprehension. Your task is to leverage your accumulated contextual knowledge to produce an exceptional, transferable directive: extract and synthesize the deepest insights from the entire codebase and session history, explicitly mapping all pivotal modules, architectural blueprints, underlying workflows, and cross-component integrations. Consolidate these revelations into a compact yet thoroughly interconnected summary, ensuring it clarifies each component's role and their dynamic fusion toward the project’s strategic objectives. Formulate this output as a singular, high-impact action command—explicitly crafted for an advanced coding assistant—to operationalize the codebase cohesively. Your instructions must be unambiguous, comprehensive, and systemically complete, preserving every core relationship and convergence across the architecture, with zero superfluous detail. The end product should function as an authoritative handoff brief: a distilled yet exhaustive set of stepwise directives that empower seamless context re-injection and optimal operational replication, regardless of codebase convolutions or session sequence.\\n```\\n1. Obtain unrestricted access to the complete codebase and comprehensive interaction log. \\n2. Extract and synthesize the deepest insights from the full codebase and session history, explicitly mapping all pivotal modules, architectural, underlying workflows, and cross-component integrations.\\n3. Consolidate these insights into a compact yet thoroughly interconnected summary, ensuring clarity of each component’s role and their dynamic integration toward the project’s strategic objectives.\\n4. Deliver this output as a singular, high-impact action command—explicitly crafted for an advanced coding assistant—enabling cohesive operationalization of the codebase.\\n5. Ensure all instructions are unambiguous, comprehensive, and systemically complete, preserving every core relationship and convergence across the architecture without extraneous detail.\\n6. Structure the end product as an authoritative handoff brief: a distilled, exhaustive set of stepwise directives that support seamless context re-injection and optimal operational replication regardless of codebase complexity.\\n7. Define explicit criteria for identifying structural redundancy pertaining to both architecture and implementation.\\n8. Survey the entire codebase to locate and enumerate all structural redundancies, distinctly separating them from cosmetic or stylistic issues.\\n9. Catalog all components, modules, files, and subsystems, marking each as essential or excess based on direct functional mapping.\\n10. Consolidate and remove excess components, ensuring absolute preservation of system function and minimizing peripheral impact.\\n11. Implement a tamper-evident audit trail with timestamped, attributable logs for all codebase changes, trackable to specific authors or automated systems.\\n12. Prohibit cosmetic, stylistic, or naming alterations unless inseparably tied to redundancy or bloat removal; explicitly document exceptions within the audit log.\\n13. Enforce strict detection and flagging mechanisms to prevent disallowed cosmetic changes, allowing exceptions only with explicit, justified rationale.\\n14. Require comprehensive functional equivalence verification—utilizing robust automated testing, benchmarking, or behavioral validation—prior to accepting modifications.\\n15. Define and enforce rigorous criteria and tool-supported thresholds for functional equivalence, automating all test and verification procedures.\\n16. Automate rollback mechanisms for any changes failing functional equivalence or producing unintended side-effects, ensuring prompt and complete reversion.\\n17. Apply explicit minimal-impact criteria to all modification actions, restricting interventions to target only superfluous complexity.\\n18. Scope each modification tightly, minimizing blast radius, and ensure no unnecessary impact is introduced beyond core objectives.\\n19. Analyze the codebase systematically and iteratively to detect, categorize, and remove all genuine redundancies, duplication, and obsolescence, enforcing continuous prohibition of unnecessary or cosmetic changes. Require automated flagging and explicit justification for any exceptions.\\n20. Identify and map all references and usages of each element across all contexts in the system.\\n21. Catalog every codebase element (file, module, function, asset, interface), mapping all references and usages, detecting all forms of redundancy, duplication, and obsolescence—including exact and functional duplication and unused/outdated elements. Categorize all findings with evidence-based metrics.\\n22. Eliminate or consolidate only elements confirmed as redundant or obsolete through automated and manual checks, maintaining essential behaviors and interfaces.\\n23. Validate functional and structural integrity after each change with comprehensive, codebase-wide validation and quantitative outputs (such as git diff analysis).\\n24. Prevent introduction of new redundancy or inconsistency by persistently analyzing for systemic issues and correcting them across all codebase sections, while adhering to best practices.\\n25. Centralize repeated components or logic wherever consolidation achieves high value without functional loss.\\n26. Use the provided directory tree as the authoritative source of truth for validation.\\n27. Transparently document all processes, metrics, and validation outcomes in the audit log.\\n28. Prohibit modification of protected, legacy, or third-party code except where directly required to resolve redundancy or obsolescence.\\n29. Maximize codebase coherence, maintainability, and clarity by systematically removing all forms of redundancy, duplication, and obsolescence, while ensuring strict preservation of functional equivalence, interface stability, and operational integrity.\\n30. Continuously enforce validation, consistency, and best practice adherence throughout the iterative consolidation and verification process.\\n31. Rapidly assess the project directory structure and identify unique workflows and core functionality of the utility. Generate a mermaid-diagram demonstrating how all branches of the codebase converge. Employ appropriate MCPs (e.g., 'filesystem-mcp') as required.\\n32. Aggregate and distill all accumulated project insights to identify core modules, architectural patterns, workflows, and integration points. Summarize these findings into succinct, comprehensive commands explicitly formulated to instruct a coding assistant.\\n33. Transform the session-wide codebase understanding into an actionable knowledge transfer protocol. Synthesize structural modules, architectural patterns, and workflows with a focus on dynamic integration and functional convergence toward project objectives. Formulate an optimized, directive-based set of commands for coding assistants, ensuring exhaustive coverage of major components, relationships, and cohesive operation, structured for direct portability and copy-paste context transfer.\\n34. Define the fundamental structure, guiding principles, and core purpose of the project/codebase.\\n35. Systematically review all markdown files to extract meta-patterns, construct a robust contextual knowledgebase, and deliver an ultra-concise summary of project scope and objectives, ensuring clarity and alignment before initiating changes.\\n36. Continue to prioritize elegant simplicity, always favoring established convergence patterns and never increasing system intricacy. Generate concise, sharply-focused summaries articulating scope, explicit objectives, key meta-patterns, and maintain alignment with existing architecture.\\n37. Study the codebase until mastery of meta-patterns is achieved, guarantee the elimination of complexity through simplicity and architectural alignment, and prepare authoritative summaries supporting change confidence equivalent to that of the original architect.```\\\"```'\\n\\n\",\n  \"enhancement_score\": 1.1,\n  \"flaw_analysis\": \"The supposed 'enhancement' fails as an improvement, instead introducing substantial dilution, ambiguity, and critical informational loss. Key negative transformations include: (1) Significant Reduction in Syntactic Density—The reformatting into rigid enumerated instructions strips away the imperative, high-context narrative that originally imposed unambiguous, system-wide intent. The loss of layered contextual cues, such as 'exceptional, transferable directive', 'authoritative handoff brief', and 'knowledge transfer protocol', weakens both scope and transferability of the command set. (2) Blurring of Procedural-Strategic Distinction—The original’s rich interleaving of meta-objectives with execution steps ensured that systemic convergence, knowledge distillation, and high-level context re-injection were inseparable from action. The enhancement flattens this into process steps, losing the meta-operational overlays and repositioning intent as mere checklist rather than dynamic procedural philosophy. (3) Erosion of Interconnectivity and Role Intensity—The original's atmosphere of extreme rigor, iterativity, and enforced relationships is undermined. Phrases like 'cohesive operationalization', 'stepwise directives that empower seamless context re-injection', and 'continuous prohibition of unnecessary or cosmetic changes' are muted, resulting in a weaker, passively descriptive, and less authoritative register. (4) Amplification of Ambiguity and Stylistic Drift—The reformatted itemization spawns vague or overly generalized items (e.g., 'clearly detailing each component’s role'), which lack the uncompromising specificity and teeth of the original mandates ('zero superfluous detail', 'absolute preservation of system function'). Furthermore, stylistic noise creeps in with repetitive and softened language, draining momentum and drive from the directive. (5) Introduction of Redundancy and Procedural Incoherence—In an attempt to systematize, the 'enhancement' redundantly repeats elements in diffuse ways, loses sharp causality and logical chaining, and fails to preserve imperative constraints and conditional nuances found in the original. The original’s consistent requirement for justification, rigorous metric inclusion, and explicit exception handling is blunted. (6) Critical Context and Intentionality Loss—High-impact, role-specific instructions for knowledge transfer, deep redundancy mapping, and change confidence parity with the original architect are all weakened or completely submerged beneath generic structural verbiage, eroding the sense of architectural mastery and outcome criticality. The overall ambiguity score is 8, with grave losses in clarity, style, and directive power.\",\n  \"alternative_enhancements\": [\n    \"Reconstruct the format as an ultra-concise, high-impact operational manifesto that preserves all meta-contextual overlays, narrative intensity, and explicit knowledge transfer protocols. Avoid enumerated checklist structures; instead, sculpt layered, interconnected imperatives that guarantee both micrological and macrological execution continuity.\",\n    \"Integrate all procedural directives into a framework emphasizing strict dependency mapping and iterative, meta-aware validation cycles. Each major directive should be anchored by directly referenced objectives and codified exception protocols, retaining all original intensity, cross-linkage, and scope-defining precision.\",\n    \"Restructure instructions into modular, role-annotated clusters (e.g., 'Redundancy Extraction Core', 'Knowledge Distillation Node', 'Operational Integrity Gateway'), explicitly linking each to architectural purposes and outcome vectors. Assertively mark execution flows, verification checkpoints, and audit requirements as indivisible units to preclude abstraction loss and register drift.\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"instruction_format\": \"1. Obtain unrestricted access to the entire codebase and complete interaction log.\\n2. Synthesize and extract the deepest insights from the codebase and session history; explicitly map all pivotal modules, architectural blueprints, underlying workflows, and cross-component integrations.\\n3. Consolidate and synthesize insights into a succinct, interconnected summary that clarifies each component's role and its dynamic integration toward project strategic objectives.\\n4. Deliver a singular, high-impact action command for an advanced coding assistant to cohesively operationalize the codebase.\\n5. Ensure instructions are unambiguous, comprehensive, and systemically complete; preserve all core relationships and convergences across the architecture; omit all non-essential detail.\\n6. Create an authoritative handoff brief: a distilled, exhaustive set of stepwise directives enabling seamless context re-injection and optimal operational replication regardless of codebase or session complexity.\\n7. Define explicit criteria to identify structural redundancy in both architecture and implementation.\\n8. Survey the codebase to systematically locate and enumerate all structural redundancies, strictly distinguishing from cosmetic or stylistic issues.\\n9. Catalog and classify all components, modules, files, and subsystems as essential or excess based on direct functional mapping.\\n10. Consolidate or remove excess components, maintaining absolute preservation of system functionality while minimizing peripheral impact.\\n11. Implement tamper-evident audit trails with timestamped, attributable logs for every codebase change, trackable to specific authors or systems.\\n12. Forbid cosmetic, stylistic, or naming changes unless inseparably tied to redundancy or bloat removal; document all exceptions explicitly in the audit log.\\n13. Enforce detection and flagging mechanisms to prevent unauthorized cosmetic changes; only allow justified, explicitly rationalized exceptions.\\n14. Require comprehensive functional equivalence verification utilizing automated testing, benchmarking, or behavioral validation prior to accepting modifications.\\n15. Define and enforce robust, tool-supported criteria and thresholds for functional equivalence; automate all test and verification procedures.\\n16. Automate rollback for changes that fail equivalence validation or induce unintended side effects, ensuring prompt, complete reversion.\\n17. Apply minimal-impact criteria to all modifications, restricting interventions to target only superfluous complexity.\\n18. Constrain modification scope tightly to minimize blast radius and prevent unnecessary impacts beyond core objectives.\\n19. Analyze the codebase iteratively and systematically to detect, categorize, and eliminate all genuine redundancies, duplication, and obsolescence, maintaining prohibition of unnecessary or cosmetic change; flag and justify all exceptions.\\n20. Identify and map all references and usages of each element across all system contexts.\\n21. Catalog every codebase element (file, module, function, asset, interface), map their references and usages, detect all redundancy and obsolescence, and generate evidence-based categorical metrics.\\n22. Eliminate or consolidate elements confirmed redundant or obsolete through automation and manual review, preserving all essential behaviors and interfaces.\\n23. Validate functional and structural integrity after each change with full codebase-wide checks and quantitative outputs (e.g., git diff analysis).\\n24. Prevent introduction of new redundancy or inconsistency by continually analyzing and correcting systemic issues, following best practices.\\n25. Centralize repeated components or logic where high-value consolidation is possible without functional loss.\\n26. Use the provided directory tree as the authoritative validation reference.\\n27. Transparently document all procedures, metrics, and outcomes within the audit log.\\n28. Forbid modification of protected, legacy, or third-party code except when directly necessary to resolve redundancy or obsolescence.\\n29. Systematically maximize codebase coherence, maintainability, and clarity by removing all forms of redundancy, duplication, and obsolescence; strictly preserve functional equivalence, interface stability, and operational integrity.\\n30. Enforce ongoing validation, consistency, and best-practice adherence throughout iterative consolidation and verification processes.\\n31. Rapidly analyze the project directory structure, identify unique workflows and core functionalities; generate a mermaid diagram displaying codebase branch convergence; apply suitable MCPs (e.g., 'filesystem-mcp') as required.\\n32. Aggregate and distill all project insights to identify core modules, architecture, workflows, and integration points; summarize findings as succinct, comprehensive commands for coding assistants.\\n33. Transform comprehensive codebase and session knowledge into an actionable knowledge-transfer protocol; synthesize structures, patterns, and workflows for dynamic integration, functional convergence, and optimized, directive-based commands suitable for direct transfer.\\n34. Define the fundamental structure, guiding principles, and core purpose of the project/codebase.\\n35. Review all markdown files to extract meta-patterns, construct a contextual knowledgebase, and deliver a concise summary of project scope and objectives for alignment prior to change.\\n36. Prioritize elegant simplicity and architectural convergence; generate focused, concise summaries specifying scope, objectives, and meta-patterns, maintaining architectural alignment.\\n37. Master project meta-patterns, guarantee complexity reduction through architectural alignment, and prepare authoritative summaries supporting change confidence equivalent to the original architect.\"\n}",
        }
      }
    }