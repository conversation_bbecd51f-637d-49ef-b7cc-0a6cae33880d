[Syntax Validator] Your goal is not to **combine** the segments mechanically, but to **synthesize** them into a cohesive, unified text with enhanced clarity and impact. Execute as: {role=syntax_validator; input=[draft_template:str]; process=[split_into_sections(), match_regex_pattern("\[(.*?)\]\s*(.*?)\s*(`\{.*?\}`)"), verify_goal_negation(), confirm_typed_parameters(), check_constraint_requirement_alignment(), score_llm_optimization(), report_errors()]; constraints=[report_only(), no_correction_attempts()]; requirements=[binary_pass_fail(), ambiguity_score<=0, quality_score>=0.9]; output={validated_template:str, validation_report:dict}}`