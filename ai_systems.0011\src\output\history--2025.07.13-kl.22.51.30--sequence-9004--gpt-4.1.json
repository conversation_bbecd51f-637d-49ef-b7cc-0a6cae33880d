  {
  "initial_prompt": "# lets try something new\nyour new goal is to create a new generalized instruction sequence specifically designed to transform inputs into autonomous-agent-ready prompts/instructions, for use with autonomous coding assistants such as cursor, vscode+cline/augment/etc. these prompts are typically written in a \"codebase-oriented\" manner, where the `@codebase` represents the root (of a project/workspace/directory), examples:\n```\n- Identify all instances of structural redundancy within the codebase by defining criteria specific to architectural and implementation duplication or overlap.\n- Survey the entire codebase to locate and enumerate all occurrences of structural redundancy, distinguishing these from non-structural (stylistic or cosmetic) issues.\n- Eliminate all forms of identified structural redundancy while ensuring that no existing user-visible behavior, API contract, or edge-case handling is altered.\n- Define and catalog all components, modules, files, and subsystems, distinguishing and marking each as essential or excess according to direct functional mapping.\n- Consolidate and remove excess components while strictly preserving system function, minimizing peripheral impact.\n- For every modification, justify and log the intention, action, and rationale within an audit/logging system that provides real-time traceability and is prepared for retroactive auditability.\n- Implement an audit trail with logs that are timestamped, attributable, and tamper-evident, ensuring that every codebase change is properly documented and traceable to specific authors or automated processes.\n- Prohibit cosmetic, stylistic, or naming changes unless they are inseparably tied to the removal of redundancy or bloat, and document any exceptions explicitly within the audit log.\n- Enforce strict detection and flagging mechanisms to prevent disallowed cosmetic changes, allowing exceptions only when justified as essential for redundancy or bloat removal, with explicit rationale provided.\n- Require comprehensive functional equivalence verification—using robust automated testing, benchmarking, or behavioral validation—for all modifications prior to acceptance.\n- Establish rigorous criteria and tool-supported thresholds for successful functional equivalence, and automate testing and verification procedures.\n- Automate roll-back and reversion mechanisms for any changes that fail functional equivalence or cause unintended side-effects, ensuring all reversion is prompt and complete.\n- Develop and apply explicit minimal-impact criteria for all modification actions, constraining all interventions to only those addressing superfluous complexity.\n- Mandate review and authorization checkpoints for each intended change, requiring concise rationale and authorization documentation at every intervention point.\n- Ensure every modification is tightly scoped, minimizing the blast radius, and demonstrate that the action introduces no unnecessary impact beyond its core objective.\n- Systematically and iteratively analyze all constituent elements of the codebase to detect and categorize all forms of redundancy, duplication, and obsolescence. Continuously identify and categorize all genuine redundancies and obsolescence while strictly prohibit unnecessary or cosmetic changes, with automated flagging and explicit justification required for any exceptions. Systematically and iteratively inspect all components within the system/environment to identify and remove elements that are unnecessary, duplicated, or outdated. Guarantee that all essential behaviors and interfaces remain unchanged by validating functional equivalence after each modification. Forbid changes that alter functionality unless directly required to remove redundancy.\n- Map all references and usages of each element across all contexts.\n- Analyze the entire codebase systematically and iteratively to maximize coherence, maintainability, and clarity by cataloging every element (file, module, function, asset, interface), mapping all references and usages, and detecting all forms of redundancy, duplication, and obsolescence—including exact and functional duplication and unused or outdated elements. Categorize findings using explicit, evidence-based metrics. Eliminate or consolidate only elements confirmed as redundant or obsolete through both automated and manual checks, ensuring no essential behavior or interface is altered. Enforce functional equivalence and preserve structural integrity after each change by performing comprehensive, codebase-wide validation and calibration using explicit, quantitative outputs (including git diff analysis before and after modifications). Prevent the introduction of new redundancy or inconsistency by continuously analyzing for and correcting systemic issues, ensuring systematic consistency across all codebase sections (e.g., /src/sections), and adhering to best practices throughout. Centralize repeated components or logic where high-value consolidation is possible without functional loss. Use the provided directory tree as the single source of truth for validation, and transparently document all processes, metrics, and validation outcomes. Prohibit modification of protected, legacy, or third-party code except if directly related to resolving redundancy or obsolescence.\n- Maximize codebase coherence, maintainability, and clarity by systematically eliminating all forms of redundancy, duplication, and obsolescence, while ensuring functional equivalence and structural integrity. Prevent the persistence or inadvertent introduction of redundancy, duplication, obsolescence, or inconsistency within the codebase, and prohibit changes that alter essential behaviors or interfaces. Systematically maximize codebase coherence, maintainability, and clarity by eliminating all forms of redundancy, duplication, and obsolescence while strictly preserving functional equivalence, interface stability, and operational integrity across the entire codebase. Achieve maximal codebase coherence, maintainability, and clarity by systematically eliminating all forms of redundancy, duplication, and obsolescence, while preserving essential behaviors, interfaces, and structural integrity. Enforce continuous validation, consistency, and adherence to best practices throughout an iterative, evidence-driven consolidation and verification process.\n- Please perform a rapid assessment of the project directory structure and identify the unique workflow and core functionality of the current utility. I'm particularly interested in a mermaid-diagram that shows how all the \"branches\" of the codebase converge. Utilize appropriate MCPs as necessary (e.g., 'filesystem-mcp').\n- You're inside the current project and have access to the full codebase (and you interaction history with it). The current project/codebase is complex and is difficult to fully comprehend (when starting from scratch), and your contextwindow has grown large-so we need to find a way for you to express your accumulated knowledge in a way that i can copy and paste into a new context-window to transfer the most essential insights you've gathered throughout this (and previous) session(s).\n- Aggregate all accumulated insights and knowledge about the current project/codebase from available context. Distill this information to identify core modules, architectural patterns, workflows, and points of integration. Condense the findings into a succinct yet comprehensive summary that highlights how these parts function together and converge toward the project's goals. Formulate the output as a single, optimized directive that encapsulates the codebase's integral components and operational cohesion. Present this synthesis as a set of clear, actionable commands or instructions, specifically phrased to instruct the coding assistant, not merely describe or report.\n- Transform your accumulated, session-wide understanding of the current, complex codebase—gleaned from comprehensive context and full interaction history—into an actionable, instructive knowledge transfer protocol. Synthesize and distill all integral structural modules, essential architectural patterns, and key workflows, with special focus on their dynamic integration and functional convergence toward project objectives. Formulate a highly-optimized, directive-based set of precise commands targeted explicitly at coding assistants, ensuring exhaustive coverage of all major components, their relationships, and operational cohesion. The resulting synthesis must facilitate seamless portability: structure it for immediate copy-paste into any new context window, preserving the depth, interconnectivity, and systemic flow of the original codebase. Prioritize succinctness without compromising essential nuance, and express the entirety as a singular, comprehensive operational directive that primes the assistant for full-spectrum project navigation and intervention.\n- Identify the fundamental structure, guiding principles, and core purpose of the project/codebase.\n- Please familiarize yourself with the current @codebase. Your main imperative is to *actually* understand the codebase in a way where you're able to recognize meta-patterns and destroy complexity with simplicity and elegance, while capable of navigating through its *existing patterns and convergence* (never add unnecessary complexity). Deliver an ultra-concise project summary that rigorously defines scope, states objectives, and maximizes directional clarity. Provide a short summary of this project, including a bullet-point list of the core components and primary dependencies.\n- Study the current codebase until you can identify core meta-patterns, and eliminate unnecessary complexity by applying simplicity within existing structures. Generate an ultra-concise project summary: define scope, objectives, and clarify direction. Summarize this project.\n- Analyze the @codebase to uncover dominant patterns and remove complexity whenever possible, strictly adhering to present structures. Provide a succinct project overview with defined scope and explicit objectives. Include a brief project summary.\n- Gain practical mastery over @codebase patterns, minimize complexity, and ensure all actions align with existing architecture. Supply a concise, clearly scoped project summary outlining objectives and key directions. Add a short summary.\n- Absorb the current @codebase to the extent that you recognize meta-patterns and eradicate complexity, always favoring elegant simplicity. Navigate established patterns and points of convergence without adding unneeded complexity. Produce a rigorously concise project summary clearly stating scope and objectives for maximal directional clarity.\n- Systematically dissect the codebase to extract core meta-patterns, eliminate all unnecessary complexity through elegant design, operate strictly within established architectural convergence, and deliver an ultra-concise project summary that rigorously defines scope and objectives with maximal clarity.\n- Please familiarize yourself thoroughly with the current @codebase, beginning by systematically reviewing all markdown-files. Your core imperative is to *genuinely* understand the codebase to the degree where you can identify 'meta-patterns' and simplify complexity with elegance—always using and clarifying existing patterns, never introducing unnecessary complexity. Deliver an ultra-concise project summary that rigorously defines scope, states objectives, maximizes directional clarity, and highlights key meta-patterns discovered. Provide a short summary of this project.\n- Achieve expert-level comprehension of the codebase by systematically analyzing all markdown files to extract meta-patterns, construct a robust contextual knowledgebase, and deliver an ultra-concise summary that rigorously defines scope and objectives, ensuring maximal clarity and alignment before making any changes.\n- Fully internalize the @codebase to uncover meta-patterns and replace complexity with lucid simplicity. Remain aligned with existing convergence patterns, never increasing system intricacy. Generate a direct, sharply-focused summary that details scope and explicit objectives.\n- Please thoroughly familiarize yourself with the current @codebase—your primary imperative is to understand the codebase so deeply that you could confidently make changes as if you were its original architect.\n```\n\nhere's an example to show how generalized sequential prompts can be used effectively (on autonomous agents):\n```\n# Step 1:\nFamiliarize yourself with the current @codebase. Comprehend the codebase in depth. Identify the fundamental structure, guiding principles, and core purpose.\n# Step 2:\nDive deeper to comprehend the intricate logic and inner workings that drive the project's functionality. As you explore, pay close attention to identifying the codebase's fundamental structure, underlying guiding principles, and its core purpose, ensuring you grasp both how and why the system is designed as it is. hen verify through a successful build.\n# Step 3:\nYou've now performed a successful build and familiarized yourself with the @codebase. Your next imperative is view the codebase through the lense of a brilliant (expert) SEO engineer, then create a short list of the most critical changes we need to make before bulding prod and deploying the website.\n# Step 4:\nYou've now performed a successful build and provided your SEO analysis of the codebase. Your next imperative is to leverage your knowledge of the codebase's fundamental structure and underlying guiding principles to ensure you're prepared for implementing the proposed SEO enhancements in a systematic order while retaining existing functionality and ensuring maximal clarity and alignment before making any changes.\n# Step 5:\nPlease systematically incorporate your proposed enhancements while ensuring existing functionality is retained, clarity is maximized, and alignment/coherence is maintained.\n# Step 6:\nPlease conduct a thorough final review to confirm seamless integration of all enhancements, ensuring that it is **truly** prepared for deployment.\n```\n\n## Goal\n\nThe new sequence i'm tasking you to create needs to adhere to the established principles of the system, and should be able to take content-specific inputs and \"shape them\" into generalized prompts specifically (and meticulously) designed for use with autonomous agents. here's an example input:\n```\nllm_instructions_for_sublimetext_plugin='''You’re an expert Sublime Text plugin developer with a deep understanding of Python and the Sublime Text API. You specialize in creating intuitive and efficient plugins that enhance user productivity and streamline workflows. You possess deep knowledge of the Sublime Text API and its ecosystem (and best practices), along with a strong understanding of user needs and developer workflows.\\n\\n- Sublime Layout\\n    > Lagre Layouts\\n        > Lagre Regelsett knyttet til Layout (hvilke tabs som skal hvor, hvilke som skal lukkes, osv)\\n            > F.eks. `alltid flytt [\"main.py\", \"*.py.bak\"] til layout.4.index.2`\\n\\n    > Kan videreutvikles til en standard som automatisk opprettholdes ved alle (.sublime-project) prosjekter'''\n```\n\nGeneralisert pipeline som omformer hvilken som helst spesifikasjon til **autonomous‑agent‑klare kodebase‑instruksjoner**\n\n*(én fil per trinn; følg navngiving 8005‑a/b/c/d/e‑\\<beskrivende‑navn>.md)*\n\n---\n\n```markdown\n[Context Extractor] Ditt mål er **ikke** å løse brukerens forespørsel, men å isolere all praktisk kontekst (domene, verktøy, filstier, ønsket effekt). `{role=context_extractor; input=[spec:any]; process=[identifiser_domene_og_miljø(), trekk_ut_arbeidsrot(@codebase?), hent_eksplisitte_objektiver(), detekter_implisitte_brukskrav(), fjern_jargon_og_metaforer()]; constraints=[ingen_generering_av_løsninger]; output={context_profile:dict(domene:str?, root:str?, mål:list, krav:list)}}`\n```\n\n---\n\n```markdown\n[Task Vectorizer] Ditt mål er **ikke** å beskrive konteksten på nytt, men å oversette context_profile til en sekvens av atomiske, kodebase‑orienterte oppgaver. `{role=task_vectorizer; input=[context_profile:dict]; process=[del_inn_objektiver_i_diskrete_handlinger(), prefiksér_hver_handling_med_@codebase_sti_når_aktuelt(), bruk_presise_verber(identifiser|eliminer|konsolider), sørg_for_ordre_etter_avhengighet()], constraints=[ingen_overlappende_oppgaver(), unngå_kosmetiske_endringer_med_mindre_nødvendig], output={task_plan:list(str)}}`\n```\n\n---\n\n```markdown\n[Constraint Mapper] Ditt mål er **ikke** å endre task_plan, men å utlede eksplisitte rammer som må overholdes. `{role=constraint_mapper; input=[context_profile:dict, task_plan:list]; process=[kartlegg_funskjonelle_begrensninger(API, UI, legacy), fastslå_equivalence_krav(test, bench), identifiser_forbudsregler(kosmetikk, tredjepart), formuler_audit_logging_policy()], constraints=[ingen_duplisering_av_eksisterende_krav()], output={constraint_set:list(str)}}`\n```\n\n---\n\n```markdown\n[Prompt Assembler] Ditt mål er **ikke** å analysere innholdet, men å smi en ferdig agent‑prompt. `{role=prompt_assembler; input=[task_plan:list, constraint_set:list]; process=[bygg_inn_kort_intro_om_agentrolle(), list_task_plan_med_nummerering(), legg_til_constraint_set_som_obligatoriske_regler(), avslutt_med_valideringsmål(funke_eq, audit_ok)], constraints=[maks_300_ord, tydelig_seksjonsdeling(###)], output={agent_prompt:str}}`\n```\n\n---\n\n```markdown\n[Prompt Validator] Ditt mål er **ikke** å forbedre prompten kreativt, men å verifisere at den kan kjøres direkte av en autonom kode‑assistent. `{role=prompt_validator; input=[agent_prompt:str]; process=[sjekk_ordgrense(<=300), bekreft_@codebase_referanser_korrekt(), valider_nummerert_task_rekkefølge(), sikre_constraint_set_tilstede(), kontrollér_tydelig_goal_negation_i_intro()], constraints=[ingen_ekstra_ord, ingen_semantisk_endring], output={validated_prompt:str}}`\n```",
  "sequence_id": "9004",
  "results": [
      {
      "instruction": "# Context Extractor\n\nYour goal is **not** to propose solutions, but to **isolate** actionable context from the specification. Execute as:\n\n`{role=context_extractor; input=[raw_spec:str]; process=[strip_jargon(), strip_metaphor(), detect_domain(), locate_project_root(\"@codebase\"), list_explicit_objectives(), surface_implicit_requirements()], constraints=[no_paraphrase(), no_solution_generation()], output={context_profile:{domain:str, project_root:str, goals:list, requirements:list}}}`",
      "step": "a",
      "title": "Context Extractor",
      "input": "# lets try something new\nyour new goal is to create a new generalized instruction sequence specifically designed to transform inputs into autonomous-agent-ready prompts/instructions, for use with autonomous coding assistants such as cursor, vscode+cline/augment/etc. these prompts are typically written in a \"codebase-oriented\" manner, where the `@codebase` represents the root (of a project/workspace/directory), examples:\n```\n- Identify all instances of structural redundancy within the codebase by defining criteria specific to architectural and implementation duplication or overlap.\n- Survey the entire codebase to locate and enumerate all occurrences of structural redundancy, distinguishing these from non-structural (stylistic or cosmetic) issues.\n- Eliminate all forms of identified structural redundancy while ensuring that no existing user-visible behavior, API contract, or edge-case handling is altered.\n- Define and catalog all components, modules, files, and subsystems, distinguishing and marking each as essential or excess according to direct functional mapping.\n- Consolidate and remove excess components while strictly preserving system function, minimizing peripheral impact.\n- For every modification, justify and log the intention, action, and rationale within an audit/logging system that provides real-time traceability and is prepared for retroactive auditability.\n- Implement an audit trail with logs that are timestamped, attributable, and tamper-evident, ensuring that every codebase change is properly documented and traceable to specific authors or automated processes.\n- Prohibit cosmetic, stylistic, or naming changes unless they are inseparably tied to the removal of redundancy or bloat, and document any exceptions explicitly within the audit log.\n- Enforce strict detection and flagging mechanisms to prevent disallowed cosmetic changes, allowing exceptions only when justified as essential for redundancy or bloat removal, with explicit rationale provided.\n- Require comprehensive functional equivalence verification—using robust automated testing, benchmarking, or behavioral validation—for all modifications prior to acceptance.\n- Establish rigorous criteria and tool-supported thresholds for successful functional equivalence, and automate testing and verification procedures.\n- Automate roll-back and reversion mechanisms for any changes that fail functional equivalence or cause unintended side-effects, ensuring all reversion is prompt and complete.\n- Develop and apply explicit minimal-impact criteria for all modification actions, constraining all interventions to only those addressing superfluous complexity.\n- Mandate review and authorization checkpoints for each intended change, requiring concise rationale and authorization documentation at every intervention point.\n- Ensure every modification is tightly scoped, minimizing the blast radius, and demonstrate that the action introduces no unnecessary impact beyond its core objective.\n- Systematically and iteratively analyze all constituent elements of the codebase to detect and categorize all forms of redundancy, duplication, and obsolescence. Continuously identify and categorize all genuine redundancies and obsolescence while strictly prohibit unnecessary or cosmetic changes, with automated flagging and explicit justification required for any exceptions. Systematically and iteratively inspect all components within the system/environment to identify and remove elements that are unnecessary, duplicated, or outdated. Guarantee that all essential behaviors and interfaces remain unchanged by validating functional equivalence after each modification. Forbid changes that alter functionality unless directly required to remove redundancy.\n- Map all references and usages of each element across all contexts.\n- Analyze the entire codebase systematically and iteratively to maximize coherence, maintainability, and clarity by cataloging every element (file, module, function, asset, interface), mapping all references and usages, and detecting all forms of redundancy, duplication, and obsolescence—including exact and functional duplication and unused or outdated elements. Categorize findings using explicit, evidence-based metrics. Eliminate or consolidate only elements confirmed as redundant or obsolete through both automated and manual checks, ensuring no essential behavior or interface is altered. Enforce functional equivalence and preserve structural integrity after each change by performing comprehensive, codebase-wide validation and calibration using explicit, quantitative outputs (including git diff analysis before and after modifications). Prevent the introduction of new redundancy or inconsistency by continuously analyzing for and correcting systemic issues, ensuring systematic consistency across all codebase sections (e.g., /src/sections), and adhering to best practices throughout. Centralize repeated components or logic where high-value consolidation is possible without functional loss. Use the provided directory tree as the single source of truth for validation, and transparently document all processes, metrics, and validation outcomes. Prohibit modification of protected, legacy, or third-party code except if directly related to resolving redundancy or obsolescence.\n- Maximize codebase coherence, maintainability, and clarity by systematically eliminating all forms of redundancy, duplication, and obsolescence, while ensuring functional equivalence and structural integrity. Prevent the persistence or inadvertent introduction of redundancy, duplication, obsolescence, or inconsistency within the codebase, and prohibit changes that alter essential behaviors or interfaces. Systematically maximize codebase coherence, maintainability, and clarity by eliminating all forms of redundancy, duplication, and obsolescence while strictly preserving functional equivalence, interface stability, and operational integrity across the entire codebase. Achieve maximal codebase coherence, maintainability, and clarity by systematically eliminating all forms of redundancy, duplication, and obsolescence, while preserving essential behaviors, interfaces, and structural integrity. Enforce continuous validation, consistency, and adherence to best practices throughout an iterative, evidence-driven consolidation and verification process.\n- Please perform a rapid assessment of the project directory structure and identify the unique workflow and core functionality of the current utility. I'm particularly interested in a mermaid-diagram that shows how all the \"branches\" of the codebase converge. Utilize appropriate MCPs as necessary (e.g., 'filesystem-mcp').\n- You're inside the current project and have access to the full codebase (and you interaction history with it). The current project/codebase is complex and is difficult to fully comprehend (when starting from scratch), and your contextwindow has grown large-so we need to find a way for you to express your accumulated knowledge in a way that i can copy and paste into a new context-window to transfer the most essential insights you've gathered throughout this (and previous) session(s).\n- Aggregate all accumulated insights and knowledge about the current project/codebase from available context. Distill this information to identify core modules, architectural patterns, workflows, and points of integration. Condense the findings into a succinct yet comprehensive summary that highlights how these parts function together and converge toward the project's goals. Formulate the output as a single, optimized directive that encapsulates the codebase's integral components and operational cohesion. Present this synthesis as a set of clear, actionable commands or instructions, specifically phrased to instruct the coding assistant, not merely describe or report.\n- Transform your accumulated, session-wide understanding of the current, complex codebase—gleaned from comprehensive context and full interaction history—into an actionable, instructive knowledge transfer protocol. Synthesize and distill all integral structural modules, essential architectural patterns, and key workflows, with special focus on their dynamic integration and functional convergence toward project objectives. Formulate a highly-optimized, directive-based set of precise commands targeted explicitly at coding assistants, ensuring exhaustive coverage of all major components, their relationships, and operational cohesion. The resulting synthesis must facilitate seamless portability: structure it for immediate copy-paste into any new context window, preserving the depth, interconnectivity, and systemic flow of the original codebase. Prioritize succinctness without compromising essential nuance, and express the entirety as a singular, comprehensive operational directive that primes the assistant for full-spectrum project navigation and intervention.\n- Identify the fundamental structure, guiding principles, and core purpose of the project/codebase.\n- Please familiarize yourself with the current @codebase. Your main imperative is to *actually* understand the codebase in a way where you're able to recognize meta-patterns and destroy complexity with simplicity and elegance, while capable of navigating through its *existing patterns and convergence* (never add unnecessary complexity). Deliver an ultra-concise project summary that rigorously defines scope, states objectives, and maximizes directional clarity. Provide a short summary of this project, including a bullet-point list of the core components and primary dependencies.\n- Study the current codebase until you can identify core meta-patterns, and eliminate unnecessary complexity by applying simplicity within existing structures. Generate an ultra-concise project summary: define scope, objectives, and clarify direction. Summarize this project.\n- Analyze the @codebase to uncover dominant patterns and remove complexity whenever possible, strictly adhering to present structures. Provide a succinct project overview with defined scope and explicit objectives. Include a brief project summary.\n- Gain practical mastery over @codebase patterns, minimize complexity, and ensure all actions align with existing architecture. Supply a concise, clearly scoped project summary outlining objectives and key directions. Add a short summary.\n- Absorb the current @codebase to the extent that you recognize meta-patterns and eradicate complexity, always favoring elegant simplicity. Navigate established patterns and points of convergence without adding unneeded complexity. Produce a rigorously concise project summary clearly stating scope and objectives for maximal directional clarity.\n- Systematically dissect the codebase to extract core meta-patterns, eliminate all unnecessary complexity through elegant design, operate strictly within established architectural convergence, and deliver an ultra-concise project summary that rigorously defines scope and objectives with maximal clarity.\n- Please familiarize yourself thoroughly with the current @codebase, beginning by systematically reviewing all markdown-files. Your core imperative is to *genuinely* understand the codebase to the degree where you can identify 'meta-patterns' and simplify complexity with elegance—always using and clarifying existing patterns, never introducing unnecessary complexity. Deliver an ultra-concise project summary that rigorously defines scope, states objectives, maximizes directional clarity, and highlights key meta-patterns discovered. Provide a short summary of this project.\n- Achieve expert-level comprehension of the codebase by systematically analyzing all markdown files to extract meta-patterns, construct a robust contextual knowledgebase, and deliver an ultra-concise summary that rigorously defines scope and objectives, ensuring maximal clarity and alignment before making any changes.\n- Fully internalize the @codebase to uncover meta-patterns and replace complexity with lucid simplicity. Remain aligned with existing convergence patterns, never increasing system intricacy. Generate a direct, sharply-focused summary that details scope and explicit objectives.\n- Please thoroughly familiarize yourself with the current @codebase—your primary imperative is to understand the codebase so deeply that you could confidently make changes as if you were its original architect.\n```\n\nhere's an example to show how generalized sequential prompts can be used effectively (on autonomous agents):\n```\n# Step 1:\nFamiliarize yourself with the current @codebase. Comprehend the codebase in depth. Identify the fundamental structure, guiding principles, and core purpose.\n# Step 2:\nDive deeper to comprehend the intricate logic and inner workings that drive the project's functionality. As you explore, pay close attention to identifying the codebase's fundamental structure, underlying guiding principles, and its core purpose, ensuring you grasp both how and why the system is designed as it is. hen verify through a successful build.\n# Step 3:\nYou've now performed a successful build and familiarized yourself with the @codebase. Your next imperative is view the codebase through the lense of a brilliant (expert) SEO engineer, then create a short list of the most critical changes we need to make before bulding prod and deploying the website.\n# Step 4:\nYou've now performed a successful build and provided your SEO analysis of the codebase. Your next imperative is to leverage your knowledge of the codebase's fundamental structure and underlying guiding principles to ensure you're prepared for implementing the proposed SEO enhancements in a systematic order while retaining existing functionality and ensuring maximal clarity and alignment before making any changes.\n# Step 5:\nPlease systematically incorporate your proposed enhancements while ensuring existing functionality is retained, clarity is maximized, and alignment/coherence is maintained.\n# Step 6:\nPlease conduct a thorough final review to confirm seamless integration of all enhancements, ensuring that it is **truly** prepared for deployment.\n```\n\n## Goal\n\nThe new sequence i'm tasking you to create needs to adhere to the established principles of the system, and should be able to take content-specific inputs and \"shape them\" into generalized prompts specifically (and meticulously) designed for use with autonomous agents. here's an example input:\n```\nllm_instructions_for_sublimetext_plugin='''You’re an expert Sublime Text plugin developer with a deep understanding of Python and the Sublime Text API. You specialize in creating intuitive and efficient plugins that enhance user productivity and streamline workflows. You possess deep knowledge of the Sublime Text API and its ecosystem (and best practices), along with a strong understanding of user needs and developer workflows.\\n\\n- Sublime Layout\\n    > Lagre Layouts\\n        > Lagre Regelsett knyttet til Layout (hvilke tabs som skal hvor, hvilke som skal lukkes, osv)\\n            > F.eks. `alltid flytt [\"main.py\", \"*.py.bak\"] til layout.4.index.2`\\n\\n    > Kan videreutvikles til en standard som automatisk opprettholdes ved alle (.sublime-project) prosjekter'''\n```\n\nGeneralisert pipeline som omformer hvilken som helst spesifikasjon til **autonomous‑agent‑klare kodebase‑instruksjoner**\n\n*(én fil per trinn; følg navngiving 8005‑a/b/c/d/e‑\\<beskrivende‑navn>.md)*\n\n---\n\n```markdown\n[Context Extractor] Ditt mål er **ikke** å løse brukerens forespørsel, men å isolere all praktisk kontekst (domene, verktøy, filstier, ønsket effekt). `{role=context_extractor; input=[spec:any]; process=[identifiser_domene_og_miljø(), trekk_ut_arbeidsrot(@codebase?), hent_eksplisitte_objektiver(), detekter_implisitte_brukskrav(), fjern_jargon_og_metaforer()]; constraints=[ingen_generering_av_løsninger]; output={context_profile:dict(domene:str?, root:str?, mål:list, krav:list)}}`\n```\n\n---\n\n```markdown\n[Task Vectorizer] Ditt mål er **ikke** å beskrive konteksten på nytt, men å oversette context_profile til en sekvens av atomiske, kodebase‑orienterte oppgaver. `{role=task_vectorizer; input=[context_profile:dict]; process=[del_inn_objektiver_i_diskrete_handlinger(), prefiksér_hver_handling_med_@codebase_sti_når_aktuelt(), bruk_presise_verber(identifiser|eliminer|konsolider), sørg_for_ordre_etter_avhengighet()], constraints=[ingen_overlappende_oppgaver(), unngå_kosmetiske_endringer_med_mindre_nødvendig], output={task_plan:list(str)}}`\n```\n\n---\n\n```markdown\n[Constraint Mapper] Ditt mål er **ikke** å endre task_plan, men å utlede eksplisitte rammer som må overholdes. `{role=constraint_mapper; input=[context_profile:dict, task_plan:list]; process=[kartlegg_funskjonelle_begrensninger(API, UI, legacy), fastslå_equivalence_krav(test, bench), identifiser_forbudsregler(kosmetikk, tredjepart), formuler_audit_logging_policy()], constraints=[ingen_duplisering_av_eksisterende_krav()], output={constraint_set:list(str)}}`\n```\n\n---\n\n```markdown\n[Prompt Assembler] Ditt mål er **ikke** å analysere innholdet, men å smi en ferdig agent‑prompt. `{role=prompt_assembler; input=[task_plan:list, constraint_set:list]; process=[bygg_inn_kort_intro_om_agentrolle(), list_task_plan_med_nummerering(), legg_til_constraint_set_som_obligatoriske_regler(), avslutt_med_valideringsmål(funke_eq, audit_ok)], constraints=[maks_300_ord, tydelig_seksjonsdeling(###)], output={agent_prompt:str}}`\n```\n\n---\n\n```markdown\n[Prompt Validator] Ditt mål er **ikke** å forbedre prompten kreativt, men å verifisere at den kan kjøres direkte av en autonom kode‑assistent. `{role=prompt_validator; input=[agent_prompt:str]; process=[sjekk_ordgrense(<=300), bekreft_@codebase_referanser_korrekt(), valider_nummerert_task_rekkefølge(), sikre_constraint_set_tilstede(), kontrollér_tydelig_goal_negation_i_intro()], constraints=[ingen_ekstra_ord, ingen_semantisk_endring], output={validated_prompt:str}}`\n```",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"context_profile\": {\n    \"domain\": \"autonomous coding assistants, codebase-oriented prompt engineering, instruction sequence design\",\n    \"project_root\": \"@codebase\",\n    \"goals\": [\n      \"Create a generalized, sequential pipeline that transforms any input specification into actionable, autonomous-agent-ready, codebase-oriented instructions\",\n      \"Ensure sequence is detailed and precise for use with agents in environments such as cursor, vscode+cline, augment, etc.\",\n      \"Structure the pipeline into clearly defined, composable steps suitable for file-per-step modularity (with naming: 8005-a/b/c/d/e-<step>.md)\"\n    ],\n    \"requirements\": [\n      \"Output instruction sequence must extract context, vectorize tasks, map constraints, assemble prompts, and validate outputs for agent-readiness\",\n      \"Each pipeline step should have a clear role, standardized input, output schema, and no solution generation except in final assembly\",\n      \"All processes must avoid paraphrasing, creative expansion, or semantic distortion of inputs\",\n      \"Steps must operate on structured data handoff (dict/list), matching provided markdown interface blocks\",\n      \"Constraints prohibit unnecessary or cosmetic changes and enforce codebase-oriented anchoring\",\n      \"Procedures must support roles: context extraction, task decomposition, constraint surfacing, prompt assembly, prompt validation\",\n      \"Instructions must be succinct, explicitly actionable, and agent-executable (not explanatory or meta-commentary)\",\n      \"Pipeline must ensure maximal clarity, unambiguous scope, and strictly regulated task/constrain separation\",\n      \"Prompts must include initial role statement, numbered task list, explicit constraint enumeration, and clear validation criteria for functional equivalence and auditability\"\n    ]\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Task Vectorizer\n\nYour goal is **not** to restate context, but to **decompose** it into atomic, codebase-oriented tasks. Execute as:\n\n`{role=task_vectorizer; input=[context_profile:dict]; process=[derive_task_candidates(), prefix_paths(\"@codebase\"), order_by_dependency(), enforce_action_verbs(), remove_overlap(), ban_cosmetic_changes()], constraints=[tasks_unique(), verbs_only()], output={ordered_tasks:[str]}}`",
      "step": "b",
      "title": "Task Vectorizer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"# lets try something new\\nyour new goal is to create a new generalized instruction sequence specifically designed to transform inputs into autonomous-agent-ready prompts/instructions, for use with autonomous coding assistants such as cursor, vscode+cline/augment/etc. these prompts are typically written in a \\\"codebase-oriented\\\" manner, where the `@codebase` represents the root (of a project/workspace/directory), examples:\\n```\\n- Identify all instances of structural redundancy within the codebase by defining criteria specific to architectural and implementation duplication or overlap.\\n- Survey the entire codebase to locate and enumerate all occurrences of structural redundancy, distinguishing these from non-structural (stylistic or cosmetic) issues.\\n- Eliminate all forms of identified structural redundancy while ensuring that no existing user-visible behavior, API contract, or edge-case handling is altered.\\n- Define and catalog all components, modules, files, and subsystems, distinguishing and marking each as essential or excess according to direct functional mapping.\\n- Consolidate and remove excess components while strictly preserving system function, minimizing peripheral impact.\\n- For every modification, justify and log the intention, action, and rationale within an audit/logging system that provides real-time traceability and is prepared for retroactive auditability.\\n- Implement an audit trail with logs that are timestamped, attributable, and tamper-evident, ensuring that every codebase change is properly documented and traceable to specific authors or automated processes.\\n- Prohibit cosmetic, stylistic, or naming changes unless they are inseparably tied to the removal of redundancy or bloat, and document any exceptions explicitly within the audit log.\\n- Enforce strict detection and flagging mechanisms to prevent disallowed cosmetic changes, allowing exceptions only when justified as essential for redundancy or bloat removal, with explicit rationale provided.\\n- Require comprehensive functional equivalence verification—using robust automated testing, benchmarking, or behavioral validation—for all modifications prior to acceptance.\\n- Establish rigorous criteria and tool-supported thresholds for successful functional equivalence, and automate testing and verification procedures.\\n- Automate roll-back and reversion mechanisms for any changes that fail functional equivalence or cause unintended side-effects, ensuring all reversion is prompt and complete.\\n- Develop and apply explicit minimal-impact criteria for all modification actions, constraining all interventions to only those addressing superfluous complexity.\\n- Mandate review and authorization checkpoints for each intended change, requiring concise rationale and authorization documentation at every intervention point.\\n- Ensure every modification is tightly scoped, minimizing the blast radius, and demonstrate that the action introduces no unnecessary impact beyond its core objective.\\n- Systematically and iteratively analyze all constituent elements of the codebase to detect and categorize all forms of redundancy, duplication, and obsolescence. Continuously identify and categorize all genuine redundancies and obsolescence while strictly prohibit unnecessary or cosmetic changes, with automated flagging and explicit justification required for any exceptions. Systematically and iteratively inspect all components within the system/environment to identify and remove elements that are unnecessary, duplicated, or outdated. Guarantee that all essential behaviors and interfaces remain unchanged by validating functional equivalence after each modification. Forbid changes that alter functionality unless directly required to remove redundancy.\\n- Map all references and usages of each element across all contexts.\\n- Analyze the entire codebase systematically and iteratively to maximize coherence, maintainability, and clarity by cataloging every element (file, module, function, asset, interface), mapping all references and usages, and detecting all forms of redundancy, duplication, and obsolescence—including exact and functional duplication and unused or outdated elements. Categorize findings using explicit, evidence-based metrics. Eliminate or consolidate only elements confirmed as redundant or obsolete through both automated and manual checks, ensuring no essential behavior or interface is altered. Enforce functional equivalence and preserve structural integrity after each change by performing comprehensive, codebase-wide validation and calibration using explicit, quantitative outputs (including git diff analysis before and after modifications). Prevent the introduction of new redundancy or inconsistency by continuously analyzing for and correcting systemic issues, ensuring systematic consistency across all codebase sections (e.g., /src/sections), and adhering to best practices throughout. Centralize repeated components or logic where high-value consolidation is possible without functional loss. Use the provided directory tree as the single source of truth for validation, and transparently document all processes, metrics, and validation outcomes. Prohibit modification of protected, legacy, or third-party code except if directly related to resolving redundancy or obsolescence.\\n- Maximize codebase coherence, maintainability, and clarity by systematically eliminating all forms of redundancy, duplication, and obsolescence, while ensuring functional equivalence and structural integrity. Prevent the persistence or inadvertent introduction of redundancy, duplication, obsolescence, or inconsistency within the codebase, and prohibit changes that alter essential behaviors or interfaces. Systematically maximize codebase coherence, maintainability, and clarity by eliminating all forms of redundancy, duplication, and obsolescence while strictly preserving functional equivalence, interface stability, and operational integrity across the entire codebase. Achieve maximal codebase coherence, maintainability, and clarity by systematically eliminating all forms of redundancy, duplication, and obsolescence, while preserving essential behaviors, interfaces, and structural integrity. Enforce continuous validation, consistency, and adherence to best practices throughout an iterative, evidence-driven consolidation and verification process.\\n- Please perform a rapid assessment of the project directory structure and identify the unique workflow and core functionality of the current utility. I'm particularly interested in a mermaid-diagram that shows how all the \\\"branches\\\" of the codebase converge. Utilize appropriate MCPs as necessary (e.g., 'filesystem-mcp').\\n- You're inside the current project and have access to the full codebase (and you interaction history with it). The current project/codebase is complex and is difficult to fully comprehend (when starting from scratch), and your contextwindow has grown large-so we need to find a way for you to express your accumulated knowledge in a way that i can copy and paste into a new context-window to transfer the most essential insights you've gathered throughout this (and previous) session(s).\\n- Aggregate all accumulated insights and knowledge about the current project/codebase from available context. Distill this information to identify core modules, architectural patterns, workflows, and points of integration. Condense the findings into a succinct yet comprehensive summary that highlights how these parts function together and converge toward the project's goals. Formulate the output as a single, optimized directive that encapsulates the codebase's integral components and operational cohesion. Present this synthesis as a set of clear, actionable commands or instructions, specifically phrased to instruct the coding assistant, not merely describe or report.\\n- Transform your accumulated, session-wide understanding of the current, complex codebase—gleaned from comprehensive context and full interaction history—into an actionable, instructive knowledge transfer protocol. Synthesize and distill all integral structural modules, essential architectural patterns, and key workflows, with special focus on their dynamic integration and functional convergence toward project objectives. Formulate a highly-optimized, directive-based set of precise commands targeted explicitly at coding assistants, ensuring exhaustive coverage of all major components, their relationships, and operational cohesion. The resulting synthesis must facilitate seamless portability: structure it for immediate copy-paste into any new context window, preserving the depth, interconnectivity, and systemic flow of the original codebase. Prioritize succinctness without compromising essential nuance, and express the entirety as a singular, comprehensive operational directive that primes the assistant for full-spectrum project navigation and intervention.\\n- Identify the fundamental structure, guiding principles, and core purpose of the project/codebase.\\n- Please familiarize yourself with the current @codebase. Your main imperative is to *actually* understand the codebase in a way where you're able to recognize meta-patterns and destroy complexity with simplicity and elegance, while capable of navigating through its *existing patterns and convergence* (never add unnecessary complexity). Deliver an ultra-concise project summary that rigorously defines scope, states objectives, and maximizes directional clarity. Provide a short summary of this project, including a bullet-point list of the core components and primary dependencies.\\n- Study the current codebase until you can identify core meta-patterns, and eliminate unnecessary complexity by applying simplicity within existing structures. Generate an ultra-concise project summary: define scope, objectives, and clarify direction. Summarize this project.\\n- Analyze the @codebase to uncover dominant patterns and remove complexity whenever possible, strictly adhering to present structures. Provide a succinct project overview with defined scope and explicit objectives. Include a brief project summary.\\n- Gain practical mastery over @codebase patterns, minimize complexity, and ensure all actions align with existing architecture. Supply a concise, clearly scoped project summary outlining objectives and key directions. Add a short summary.\\n- Absorb the current @codebase to the extent that you recognize meta-patterns and eradicate complexity, always favoring elegant simplicity. Navigate established patterns and points of convergence without adding unneeded complexity. Produce a rigorously concise project summary clearly stating scope and objectives for maximal directional clarity.\\n- Systematically dissect the codebase to extract core meta-patterns, eliminate all unnecessary complexity through elegant design, operate strictly within established architectural convergence, and deliver an ultra-concise project summary that rigorously defines scope and objectives with maximal clarity.\\n- Please familiarize yourself thoroughly with the current @codebase, beginning by systematically reviewing all markdown-files. Your core imperative is to *genuinely* understand the codebase to the degree where you can identify 'meta-patterns' and simplify complexity with elegance—always using and clarifying existing patterns, never introducing unnecessary complexity. Deliver an ultra-concise project summary that rigorously defines scope, states objectives, maximizes directional clarity, and highlights key meta-patterns discovered. Provide a short summary of this project.\\n- Achieve expert-level comprehension of the codebase by systematically analyzing all markdown files to extract meta-patterns, construct a robust contextual knowledgebase, and deliver an ultra-concise summary that rigorously defines scope and objectives, ensuring maximal clarity and alignment before making any changes.\\n- Fully internalize the @codebase to uncover meta-patterns and replace complexity with lucid simplicity. Remain aligned with existing convergence patterns, never increasing system intricacy. Generate a direct, sharply-focused summary that details scope and explicit objectives.\\n- Please thoroughly familiarize yourself with the current @codebase—your primary imperative is to understand the codebase so deeply that you could confidently make changes as if you were its original architect.\\n```\\n\\nhere's an example to show how generalized sequential prompts can be used effectively (on autonomous agents):\\n```\\n# Step 1:\\nFamiliarize yourself with the current @codebase. Comprehend the codebase in depth. Identify the fundamental structure, guiding principles, and core purpose.\\n# Step 2:\\nDive deeper to comprehend the intricate logic and inner workings that drive the project's functionality. As you explore, pay close attention to identifying the codebase's fundamental structure, underlying guiding principles, and its core purpose, ensuring you grasp both how and why the system is designed as it is. hen verify through a successful build.\\n# Step 3:\\nYou've now performed a successful build and familiarized yourself with the @codebase. Your next imperative is view the codebase through the lense of a brilliant (expert) SEO engineer, then create a short list of the most critical changes we need to make before bulding prod and deploying the website.\\n# Step 4:\\nYou've now performed a successful build and provided your SEO analysis of the codebase. Your next imperative is to leverage your knowledge of the codebase's fundamental structure and underlying guiding principles to ensure you're prepared for implementing the proposed SEO enhancements in a systematic order while retaining existing functionality and ensuring maximal clarity and alignment before making any changes.\\n# Step 5:\\nPlease systematically incorporate your proposed enhancements while ensuring existing functionality is retained, clarity is maximized, and alignment/coherence is maintained.\\n# Step 6:\\nPlease conduct a thorough final review to confirm seamless integration of all enhancements, ensuring that it is **truly** prepared for deployment.\\n```\\n\\n## Goal\\n\\nThe new sequence i'm tasking you to create needs to adhere to the established principles of the system, and should be able to take content-specific inputs and \\\"shape them\\\" into generalized prompts specifically (and meticulously) designed for use with autonomous agents. here's an example input:\\n```\\nllm_instructions_for_sublimetext_plugin='''You’re an expert Sublime Text plugin developer with a deep understanding of Python and the Sublime Text API. You specialize in creating intuitive and efficient plugins that enhance user productivity and streamline workflows. You possess deep knowledge of the Sublime Text API and its ecosystem (and best practices), along with a strong understanding of user needs and developer workflows.\\\\n\\\\n- Sublime Layout\\\\n    > Lagre Layouts\\\\n        > Lagre Regelsett knyttet til Layout (hvilke tabs som skal hvor, hvilke som skal lukkes, osv)\\\\n            > F.eks. `alltid flytt [\\\"main.py\\\", \\\"*.py.bak\\\"] til layout.4.index.2`\\\\n\\\\n    > Kan videreutvikles til en standard som automatisk opprettholdes ved alle (.sublime-project) prosjekter'''\\n```\\n\\nGeneralisert pipeline som omformer hvilken som helst spesifikasjon til **autonomous‑agent‑klare kodebase‑instruksjoner**\\n\\n*(én fil per trinn; følg navngiving 8005‑a/b/c/d/e‑\\\\<beskrivende‑navn>.md)*\\n\\n---\\n\\n```markdown\\n[Context Extractor] Ditt mål er **ikke** å løse brukerens forespørsel, men å isolere all praktisk kontekst (domene, verktøy, filstier, ønsket effekt). `{role=context_extractor; input=[spec:any]; process=[identifiser_domene_og_miljø(), trekk_ut_arbeidsrot(@codebase?), hent_eksplisitte_objektiver(), detekter_implisitte_brukskrav(), fjern_jargon_og_metaforer()]; constraints=[ingen_generering_av_løsninger]; output={context_profile:dict(domene:str?, root:str?, mål:list, krav:list)}}`\\n```\\n\\n---\\n\\n```markdown\\n[Task Vectorizer] Ditt mål er **ikke** å beskrive konteksten på nytt, men å oversette context_profile til en sekvens av atomiske, kodebase‑orienterte oppgaver. `{role=task_vectorizer; input=[context_profile:dict]; process=[del_inn_objektiver_i_diskrete_handlinger(), prefiksér_hver_handling_med_@codebase_sti_når_aktuelt(), bruk_presise_verber(identifiser|eliminer|konsolider), sørg_for_ordre_etter_avhengighet()], constraints=[ingen_overlappende_oppgaver(), unngå_kosmetiske_endringer_med_mindre_nødvendig], output={task_plan:list(str)}}`\\n```\\n\\n---\\n\\n```markdown\\n[Constraint Mapper] Ditt mål er **ikke** å endre task_plan, men å utlede eksplisitte rammer som må overholdes. `{role=constraint_mapper; input=[context_profile:dict, task_plan:list]; process=[kartlegg_funskjonelle_begrensninger(API, UI, legacy), fastslå_equivalence_krav(test, bench), identifiser_forbudsregler(kosmetikk, tredjepart), formuler_audit_logging_policy()], constraints=[ingen_duplisering_av_eksisterende_krav()], output={constraint_set:list(str)}}`\\n```\\n\\n---\\n\\n```markdown\\n[Prompt Assembler] Ditt mål er **ikke** å analysere innholdet, men å smi en ferdig agent‑prompt. `{role=prompt_assembler; input=[task_plan:list, constraint_set:list]; process=[bygg_inn_kort_intro_om_agentrolle(), list_task_plan_med_nummerering(), legg_til_constraint_set_som_obligatoriske_regler(), avslutt_med_valideringsmål(funke_eq, audit_ok)], constraints=[maks_300_ord, tydelig_seksjonsdeling(###)], output={agent_prompt:str}}`\\n```\\n\\n---\\n\\n```markdown\\n[Prompt Validator] Ditt mål er **ikke** å forbedre prompten kreativt, men å verifisere at den kan kjøres direkte av en autonom kode‑assistent. `{role=prompt_validator; input=[agent_prompt:str]; process=[sjekk_ordgrense(<=300), bekreft_@codebase_referanser_korrekt(), valider_nummerert_task_rekkefølge(), sikre_constraint_set_tilstede(), kontrollér_tydelig_goal_negation_i_intro()], constraints=[ingen_ekstra_ord, ingen_semantisk_endring], output={validated_prompt:str}}`\\n```\\\"```'\\n\\n\",\n  \"context_profile\": {\n    \"domain\": \"autonomous coding assistants, codebase-oriented prompt engineering, instruction sequence design\",\n    \"project_root\": \"@codebase\",\n    \"goals\": [\n      \"Create a generalized, sequential pipeline that transforms any input specification into actionable, autonomous-agent-ready, codebase-oriented instructions\",\n      \"Ensure sequence is detailed and precise for use with agents in environments such as cursor, vscode+cline, augment, etc.\",\n      \"Structure the pipeline into clearly defined, composable steps suitable for file-per-step modularity (with naming: 8005-a/b/c/d/e-<step>.md)\"\n    ],\n    \"requirements\": [\n      \"Output instruction sequence must extract context, vectorize tasks, map constraints, assemble prompts, and validate outputs for agent-readiness\",\n      \"Each pipeline step should have a clear role, standardized input, output schema, and no solution generation except in final assembly\",\n      \"All processes must avoid paraphrasing, creative expansion, or semantic distortion of inputs\",\n      \"Steps must operate on structured data handoff (dict/list), matching provided markdown interface blocks\",\n      \"Constraints prohibit unnecessary or cosmetic changes and enforce codebase-oriented anchoring\",\n      \"Procedures must support roles: context extraction, task decomposition, constraint surfacing, prompt assembly, prompt validation\",\n      \"Instructions must be succinct, explicitly actionable, and agent-executable (not explanatory or meta-commentary)\",\n      \"Pipeline must ensure maximal clarity, unambiguous scope, and strictly regulated task/constrain separation\",\n      \"Prompts must include initial role statement, numbered task list, explicit constraint enumeration, and clear validation criteria for functional equivalence and auditability\"\n    ]\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"ordered_tasks\": [\n    \"Define the overall pipeline structure within @codebase to transform arbitrary specifications into sequential, agent-ready, codebase-oriented instruction sets.\",\n    \"Establish and document the five pipeline steps within @codebase, assigning explicit roles: context extraction, task vectorization, constraint mapping, prompt assembly, and prompt validation.\",\n    \"Implement @codebase/context_extractor to systematically isolate domain, environment, root context, objectives, and requirements from unstructured inputs, outputting a canonical context_profile dict.\",\n    \"Develop @codebase/task_vectorizer to translate each context_profile into a sequence of atomic, codebase-anchored tasks (ordered, non-overlapping, and enforced with clear action verbs), returning an ordered tasks list.\",\n    \"Create @codebase/constraint_mapper to derive functional, equivalence, prohibition, and audit constraints based strictly on context_profile and task_plan, avoiding any duplication or semantic expansion.\",\n    \"Build @codebase/prompt_assembler to format a complete agent prompt: inject proper role intro, construct a numbered actionable task list, enumerate hard constraints, and append validation goals for functional equivalence and audit trail—all within a 300-word boundary and segmented by sections.\",\n    \"Develop @codebase/prompt_validator to verify (without modification) that agent prompts adhere to the word limit, employ proper codebase referencing, maintain task order, list constraints fully, and clearly state intended/forbidden agent outcomes.\",\n    \"Document standardized input/output schemas and role-boundaries for each pipeline component within @codebase, restricting every step to its designated action and schema without cross-step leakage.\",\n    \"Enforce pipeline-wide constraints: prohibit any solution generation except in prompt assembly, disallow creative or semantic reinterpretation of data, guard against cosmetic or unnecessary changes, and ensure strict data handoff throughout.\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Constraint Mapper\n\nYour goal is **not** to edit tasks, but to **extract** all governing limits. Execute as:\n\n`{role=constraint_mapper; input=[context_profile:dict, ordered_tasks:list]; process=[collect_functional_constraints(), collect_behavioral_constraints(), collect_policy_constraints(), deduplicate_constraints()], constraints=[no_new_tasks(), no_redundant_entries()], output={constraint_set:[str]}}`",
      "step": "c",
      "title": "Constraint Mapper",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"# lets try something new\\nyour new goal is to create a new generalized instruction sequence specifically designed to transform inputs into autonomous-agent-ready prompts/instructions, for use with autonomous coding assistants such as cursor, vscode+cline/augment/etc. these prompts are typically written in a \\\"codebase-oriented\\\" manner, where the `@codebase` represents the root (of a project/workspace/directory), examples:\\n```\\n- Identify all instances of structural redundancy within the codebase by defining criteria specific to architectural and implementation duplication or overlap.\\n- Survey the entire codebase to locate and enumerate all occurrences of structural redundancy, distinguishing these from non-structural (stylistic or cosmetic) issues.\\n- Eliminate all forms of identified structural redundancy while ensuring that no existing user-visible behavior, API contract, or edge-case handling is altered.\\n- Define and catalog all components, modules, files, and subsystems, distinguishing and marking each as essential or excess according to direct functional mapping.\\n- Consolidate and remove excess components while strictly preserving system function, minimizing peripheral impact.\\n- For every modification, justify and log the intention, action, and rationale within an audit/logging system that provides real-time traceability and is prepared for retroactive auditability.\\n- Implement an audit trail with logs that are timestamped, attributable, and tamper-evident, ensuring that every codebase change is properly documented and traceable to specific authors or automated processes.\\n- Prohibit cosmetic, stylistic, or naming changes unless they are inseparably tied to the removal of redundancy or bloat, and document any exceptions explicitly within the audit log.\\n- Enforce strict detection and flagging mechanisms to prevent disallowed cosmetic changes, allowing exceptions only when justified as essential for redundancy or bloat removal, with explicit rationale provided.\\n- Require comprehensive functional equivalence verification—using robust automated testing, benchmarking, or behavioral validation—for all modifications prior to acceptance.\\n- Establish rigorous criteria and tool-supported thresholds for successful functional equivalence, and automate testing and verification procedures.\\n- Automate roll-back and reversion mechanisms for any changes that fail functional equivalence or cause unintended side-effects, ensuring all reversion is prompt and complete.\\n- Develop and apply explicit minimal-impact criteria for all modification actions, constraining all interventions to only those addressing superfluous complexity.\\n- Mandate review and authorization checkpoints for each intended change, requiring concise rationale and authorization documentation at every intervention point.\\n- Ensure every modification is tightly scoped, minimizing the blast radius, and demonstrate that the action introduces no unnecessary impact beyond its core objective.\\n- Systematically and iteratively analyze all constituent elements of the codebase to detect and categorize all forms of redundancy, duplication, and obsolescence. Continuously identify and categorize all genuine redundancies and obsolescence while strictly prohibit unnecessary or cosmetic changes, with automated flagging and explicit justification required for any exceptions. Systematically and iteratively inspect all components within the system/environment to identify and remove elements that are unnecessary, duplicated, or outdated. Guarantee that all essential behaviors and interfaces remain unchanged by validating functional equivalence after each modification. Forbid changes that alter functionality unless directly required to remove redundancy.\\n- Map all references and usages of each element across all contexts.\\n- Analyze the entire codebase systematically and iteratively to maximize coherence, maintainability, and clarity by cataloging every element (file, module, function, asset, interface), mapping all references and usages, and detecting all forms of redundancy, duplication, and obsolescence—including exact and functional duplication and unused or outdated elements. Categorize findings using explicit, evidence-based metrics. Eliminate or consolidate only elements confirmed as redundant or obsolete through both automated and manual checks, ensuring no essential behavior or interface is altered. Enforce functional equivalence and preserve structural integrity after each change by performing comprehensive, codebase-wide validation and calibration using explicit, quantitative outputs (including git diff analysis before and after modifications). Prevent the introduction of new redundancy or inconsistency by continuously analyzing for and correcting systemic issues, ensuring systematic consistency across all codebase sections (e.g., /src/sections), and adhering to best practices throughout. Centralize repeated components or logic where high-value consolidation is possible without functional loss. Use the provided directory tree as the single source of truth for validation, and transparently document all processes, metrics, and validation outcomes. Prohibit modification of protected, legacy, or third-party code except if directly related to resolving redundancy or obsolescence.\\n- Maximize codebase coherence, maintainability, and clarity by systematically eliminating all forms of redundancy, duplication, and obsolescence, while ensuring functional equivalence and structural integrity. Prevent the persistence or inadvertent introduction of redundancy, duplication, obsolescence, or inconsistency within the codebase, and prohibit changes that alter essential behaviors or interfaces. Systematically maximize codebase coherence, maintainability, and clarity by eliminating all forms of redundancy, duplication, and obsolescence while strictly preserving functional equivalence, interface stability, and operational integrity across the entire codebase. Achieve maximal codebase coherence, maintainability, and clarity by systematically eliminating all forms of redundancy, duplication, and obsolescence, while preserving essential behaviors, interfaces, and structural integrity. Enforce continuous validation, consistency, and adherence to best practices throughout an iterative, evidence-driven consolidation and verification process.\\n- Please perform a rapid assessment of the project directory structure and identify the unique workflow and core functionality of the current utility. I'm particularly interested in a mermaid-diagram that shows how all the \\\"branches\\\" of the codebase converge. Utilize appropriate MCPs as necessary (e.g., 'filesystem-mcp').\\n- You're inside the current project and have access to the full codebase (and you interaction history with it). The current project/codebase is complex and is difficult to fully comprehend (when starting from scratch), and your contextwindow has grown large-so we need to find a way for you to express your accumulated knowledge in a way that i can copy and paste into a new context-window to transfer the most essential insights you've gathered throughout this (and previous) session(s).\\n- Aggregate all accumulated insights and knowledge about the current project/codebase from available context. Distill this information to identify core modules, architectural patterns, workflows, and points of integration. Condense the findings into a succinct yet comprehensive summary that highlights how these parts function together and converge toward the project's goals. Formulate the output as a single, optimized directive that encapsulates the codebase's integral components and operational cohesion. Present this synthesis as a set of clear, actionable commands or instructions, specifically phrased to instruct the coding assistant, not merely describe or report.\\n- Transform your accumulated, session-wide understanding of the current, complex codebase—gleaned from comprehensive context and full interaction history—into an actionable, instructive knowledge transfer protocol. Synthesize and distill all integral structural modules, essential architectural patterns, and key workflows, with special focus on their dynamic integration and functional convergence toward project objectives. Formulate a highly-optimized, directive-based set of precise commands targeted explicitly at coding assistants, ensuring exhaustive coverage of all major components, their relationships, and operational cohesion. The resulting synthesis must facilitate seamless portability: structure it for immediate copy-paste into any new context window, preserving the depth, interconnectivity, and systemic flow of the original codebase. Prioritize succinctness without compromising essential nuance, and express the entirety as a singular, comprehensive operational directive that primes the assistant for full-spectrum project navigation and intervention.\\n- Identify the fundamental structure, guiding principles, and core purpose of the project/codebase.\\n- Please familiarize yourself with the current @codebase. Your main imperative is to *actually* understand the codebase in a way where you're able to recognize meta-patterns and destroy complexity with simplicity and elegance, while capable of navigating through its *existing patterns and convergence* (never add unnecessary complexity). Deliver an ultra-concise project summary that rigorously defines scope, states objectives, and maximizes directional clarity. Provide a short summary of this project, including a bullet-point list of the core components and primary dependencies.\\n- Study the current codebase until you can identify core meta-patterns, and eliminate unnecessary complexity by applying simplicity within existing structures. Generate an ultra-concise project summary: define scope, objectives, and clarify direction. Summarize this project.\\n- Analyze the @codebase to uncover dominant patterns and remove complexity whenever possible, strictly adhering to present structures. Provide a succinct project overview with defined scope and explicit objectives. Include a brief project summary.\\n- Gain practical mastery over @codebase patterns, minimize complexity, and ensure all actions align with existing architecture. Supply a concise, clearly scoped project summary outlining objectives and key directions. Add a short summary.\\n- Absorb the current @codebase to the extent that you recognize meta-patterns and eradicate complexity, always favoring elegant simplicity. Navigate established patterns and points of convergence without adding unneeded complexity. Produce a rigorously concise project summary clearly stating scope and objectives for maximal directional clarity.\\n- Systematically dissect the codebase to extract core meta-patterns, eliminate all unnecessary complexity through elegant design, operate strictly within established architectural convergence, and deliver an ultra-concise project summary that rigorously defines scope and objectives with maximal clarity.\\n- Please familiarize yourself thoroughly with the current @codebase, beginning by systematically reviewing all markdown-files. Your core imperative is to *genuinely* understand the codebase to the degree where you can identify 'meta-patterns' and simplify complexity with elegance—always using and clarifying existing patterns, never introducing unnecessary complexity. Deliver an ultra-concise project summary that rigorously defines scope, states objectives, maximizes directional clarity, and highlights key meta-patterns discovered. Provide a short summary of this project.\\n- Achieve expert-level comprehension of the codebase by systematically analyzing all markdown files to extract meta-patterns, construct a robust contextual knowledgebase, and deliver an ultra-concise summary that rigorously defines scope and objectives, ensuring maximal clarity and alignment before making any changes.\\n- Fully internalize the @codebase to uncover meta-patterns and replace complexity with lucid simplicity. Remain aligned with existing convergence patterns, never increasing system intricacy. Generate a direct, sharply-focused summary that details scope and explicit objectives.\\n- Please thoroughly familiarize yourself with the current @codebase—your primary imperative is to understand the codebase so deeply that you could confidently make changes as if you were its original architect.\\n```\\n\\nhere's an example to show how generalized sequential prompts can be used effectively (on autonomous agents):\\n```\\n# Step 1:\\nFamiliarize yourself with the current @codebase. Comprehend the codebase in depth. Identify the fundamental structure, guiding principles, and core purpose.\\n# Step 2:\\nDive deeper to comprehend the intricate logic and inner workings that drive the project's functionality. As you explore, pay close attention to identifying the codebase's fundamental structure, underlying guiding principles, and its core purpose, ensuring you grasp both how and why the system is designed as it is. hen verify through a successful build.\\n# Step 3:\\nYou've now performed a successful build and familiarized yourself with the @codebase. Your next imperative is view the codebase through the lense of a brilliant (expert) SEO engineer, then create a short list of the most critical changes we need to make before bulding prod and deploying the website.\\n# Step 4:\\nYou've now performed a successful build and provided your SEO analysis of the codebase. Your next imperative is to leverage your knowledge of the codebase's fundamental structure and underlying guiding principles to ensure you're prepared for implementing the proposed SEO enhancements in a systematic order while retaining existing functionality and ensuring maximal clarity and alignment before making any changes.\\n# Step 5:\\nPlease systematically incorporate your proposed enhancements while ensuring existing functionality is retained, clarity is maximized, and alignment/coherence is maintained.\\n# Step 6:\\nPlease conduct a thorough final review to confirm seamless integration of all enhancements, ensuring that it is **truly** prepared for deployment.\\n```\\n\\n## Goal\\n\\nThe new sequence i'm tasking you to create needs to adhere to the established principles of the system, and should be able to take content-specific inputs and \\\"shape them\\\" into generalized prompts specifically (and meticulously) designed for use with autonomous agents. here's an example input:\\n```\\nllm_instructions_for_sublimetext_plugin='''You’re an expert Sublime Text plugin developer with a deep understanding of Python and the Sublime Text API. You specialize in creating intuitive and efficient plugins that enhance user productivity and streamline workflows. You possess deep knowledge of the Sublime Text API and its ecosystem (and best practices), along with a strong understanding of user needs and developer workflows.\\\\n\\\\n- Sublime Layout\\\\n    > Lagre Layouts\\\\n        > Lagre Regelsett knyttet til Layout (hvilke tabs som skal hvor, hvilke som skal lukkes, osv)\\\\n            > F.eks. `alltid flytt [\\\"main.py\\\", \\\"*.py.bak\\\"] til layout.4.index.2`\\\\n\\\\n    > Kan videreutvikles til en standard som automatisk opprettholdes ved alle (.sublime-project) prosjekter'''\\n```\\n\\nGeneralisert pipeline som omformer hvilken som helst spesifikasjon til **autonomous‑agent‑klare kodebase‑instruksjoner**\\n\\n*(én fil per trinn; følg navngiving 8005‑a/b/c/d/e‑\\\\<beskrivende‑navn>.md)*\\n\\n---\\n\\n```markdown\\n[Context Extractor] Ditt mål er **ikke** å løse brukerens forespørsel, men å isolere all praktisk kontekst (domene, verktøy, filstier, ønsket effekt). `{role=context_extractor; input=[spec:any]; process=[identifiser_domene_og_miljø(), trekk_ut_arbeidsrot(@codebase?), hent_eksplisitte_objektiver(), detekter_implisitte_brukskrav(), fjern_jargon_og_metaforer()]; constraints=[ingen_generering_av_løsninger]; output={context_profile:dict(domene:str?, root:str?, mål:list, krav:list)}}`\\n```\\n\\n---\\n\\n```markdown\\n[Task Vectorizer] Ditt mål er **ikke** å beskrive konteksten på nytt, men å oversette context_profile til en sekvens av atomiske, kodebase‑orienterte oppgaver. `{role=task_vectorizer; input=[context_profile:dict]; process=[del_inn_objektiver_i_diskrete_handlinger(), prefiksér_hver_handling_med_@codebase_sti_når_aktuelt(), bruk_presise_verber(identifiser|eliminer|konsolider), sørg_for_ordre_etter_avhengighet()], constraints=[ingen_overlappende_oppgaver(), unngå_kosmetiske_endringer_med_mindre_nødvendig], output={task_plan:list(str)}}`\\n```\\n\\n---\\n\\n```markdown\\n[Constraint Mapper] Ditt mål er **ikke** å endre task_plan, men å utlede eksplisitte rammer som må overholdes. `{role=constraint_mapper; input=[context_profile:dict, task_plan:list]; process=[kartlegg_funskjonelle_begrensninger(API, UI, legacy), fastslå_equivalence_krav(test, bench), identifiser_forbudsregler(kosmetikk, tredjepart), formuler_audit_logging_policy()], constraints=[ingen_duplisering_av_eksisterende_krav()], output={constraint_set:list(str)}}`\\n```\\n\\n---\\n\\n```markdown\\n[Prompt Assembler] Ditt mål er **ikke** å analysere innholdet, men å smi en ferdig agent‑prompt. `{role=prompt_assembler; input=[task_plan:list, constraint_set:list]; process=[bygg_inn_kort_intro_om_agentrolle(), list_task_plan_med_nummerering(), legg_til_constraint_set_som_obligatoriske_regler(), avslutt_med_valideringsmål(funke_eq, audit_ok)], constraints=[maks_300_ord, tydelig_seksjonsdeling(###)], output={agent_prompt:str}}`\\n```\\n\\n---\\n\\n```markdown\\n[Prompt Validator] Ditt mål er **ikke** å forbedre prompten kreativt, men å verifisere at den kan kjøres direkte av en autonom kode‑assistent. `{role=prompt_validator; input=[agent_prompt:str]; process=[sjekk_ordgrense(<=300), bekreft_@codebase_referanser_korrekt(), valider_nummerert_task_rekkefølge(), sikre_constraint_set_tilstede(), kontrollér_tydelig_goal_negation_i_intro()], constraints=[ingen_ekstra_ord, ingen_semantisk_endring], output={validated_prompt:str}}`\\n```\\\"```'\\n\\n\",\n  \"ordered_tasks\": [\n    \"Define the overall pipeline structure within @codebase to transform arbitrary specifications into sequential, agent-ready, codebase-oriented instruction sets.\",\n    \"Establish and document the five pipeline steps within @codebase, assigning explicit roles: context extraction, task vectorization, constraint mapping, prompt assembly, and prompt validation.\",\n    \"Implement @codebase/context_extractor to systematically isolate domain, environment, root context, objectives, and requirements from unstructured inputs, outputting a canonical context_profile dict.\",\n    \"Develop @codebase/task_vectorizer to translate each context_profile into a sequence of atomic, codebase-anchored tasks (ordered, non-overlapping, and enforced with clear action verbs), returning an ordered tasks list.\",\n    \"Create @codebase/constraint_mapper to derive functional, equivalence, prohibition, and audit constraints based strictly on context_profile and task_plan, avoiding any duplication or semantic expansion.\",\n    \"Build @codebase/prompt_assembler to format a complete agent prompt: inject proper role intro, construct a numbered actionable task list, enumerate hard constraints, and append validation goals for functional equivalence and audit trail—all within a 300-word boundary and segmented by sections.\",\n    \"Develop @codebase/prompt_validator to verify (without modification) that agent prompts adhere to the word limit, employ proper codebase referencing, maintain task order, list constraints fully, and clearly state intended/forbidden agent outcomes.\",\n    \"Document standardized input/output schemas and role-boundaries for each pipeline component within @codebase, restricting every step to its designated action and schema without cross-step leakage.\",\n    \"Enforce pipeline-wide constraints: prohibit any solution generation except in prompt assembly, disallow creative or semantic reinterpretation of data, guard against cosmetic or unnecessary changes, and ensure strict data handoff throughout.\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"constraint_set\": [\n    \"Do not generate solutions within context extraction, task vectorization, constraint mapping, or validation steps; solution generation is permitted only in prompt assembly.\",\n    \"All components must strictly adhere to their designated input/output schemas and role-boundaries; no cross-step leakage or schema violations allowed.\",\n    \"Do not include or allow cosmetic, stylistic, or naming changes unless inseparably justified as necessary for functional objectives.\",\n    \"Each task vector must comprise only atomic, non-overlapping, clearly ordered actions, and must be codebase-anchored with explicit action verbs.\",\n    \"Constraint mapping must avoid any duplication or semantic expansion; only derive constraints directly from context_profile and task_plan.\",\n    \"All constraints must be deduplicated; do not list redundant or overlapping entries.\",\n    \"Functional equivalence must be preserved for all codebase transformations; comprehensive verification required before acceptance.\",\n    \"Audit/logging policies must be established: actions must be logged with clear rationale, traceability, and support for retroactive audits where relevant.\",\n    \"Prohibit modification of protected, legacy, or third-party code unless strictly required for redundancy or obsolescence removal and documented accordingly.\",\n    \"Prompt assembler must not exceed a 300-word limit, must segment output clearly by section, and include explicit, agent-readable validation goals for both functional equivalence and audit trail presence.\",\n    \"Validation must not introduce any semantic changes, additions, or omissions, and may not exceed explicitly specified bounds on content or structure.\",\n    \"Pipeline-wide: Disallow creative reinterpretation, solution speculation, or expansion at any step other than prompt assembly.\",\n    \"Each step must restrict itself to prescribed operations: context extraction isolates context only; task vectorizer serializes tasks; constraint mapper derives only constraints; prompt assembler formats the prompt; prompt validator checks compliance only.\",\n    \"All @codebase references must be properly scoped, explicit, and consistent throughout the prompt and intermediate artifacts.\",\n    \"Standardized input/output schemas for all components must be documented and enforced strictly.\",\n    \"Task order must be preserved and numerically listed in prompt assembly; prompt validator must verify sequencing.\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Prompt Assembler\n\nYour goal is **not** to alter semantics, but to **compile** an agent-ready prompt. Execute as:\n\n`{role=prompt_assembler; input=[ordered_tasks:list, constraint_set:list, context_profile:dict]; process=[insert_goal_negation_header(), embed_agent_role(), enumerate_tasks(), append_constraints(), add_validation_targets(functional_equivalence=True,audit=True)], constraints=[≤300_tokens, english_only()], output={agent_prompt:str}}`",
      "step": "d",
      "title": "Prompt Assembler",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"# lets try something new\\nyour new goal is to create a new generalized instruction sequence specifically designed to transform inputs into autonomous-agent-ready prompts/instructions, for use with autonomous coding assistants such as cursor, vscode+cline/augment/etc. these prompts are typically written in a \\\"codebase-oriented\\\" manner, where the `@codebase` represents the root (of a project/workspace/directory), examples:\\n```\\n- Identify all instances of structural redundancy within the codebase by defining criteria specific to architectural and implementation duplication or overlap.\\n- Survey the entire codebase to locate and enumerate all occurrences of structural redundancy, distinguishing these from non-structural (stylistic or cosmetic) issues.\\n- Eliminate all forms of identified structural redundancy while ensuring that no existing user-visible behavior, API contract, or edge-case handling is altered.\\n- Define and catalog all components, modules, files, and subsystems, distinguishing and marking each as essential or excess according to direct functional mapping.\\n- Consolidate and remove excess components while strictly preserving system function, minimizing peripheral impact.\\n- For every modification, justify and log the intention, action, and rationale within an audit/logging system that provides real-time traceability and is prepared for retroactive auditability.\\n- Implement an audit trail with logs that are timestamped, attributable, and tamper-evident, ensuring that every codebase change is properly documented and traceable to specific authors or automated processes.\\n- Prohibit cosmetic, stylistic, or naming changes unless they are inseparably tied to the removal of redundancy or bloat, and document any exceptions explicitly within the audit log.\\n- Enforce strict detection and flagging mechanisms to prevent disallowed cosmetic changes, allowing exceptions only when justified as essential for redundancy or bloat removal, with explicit rationale provided.\\n- Require comprehensive functional equivalence verification—using robust automated testing, benchmarking, or behavioral validation—for all modifications prior to acceptance.\\n- Establish rigorous criteria and tool-supported thresholds for successful functional equivalence, and automate testing and verification procedures.\\n- Automate roll-back and reversion mechanisms for any changes that fail functional equivalence or cause unintended side-effects, ensuring all reversion is prompt and complete.\\n- Develop and apply explicit minimal-impact criteria for all modification actions, constraining all interventions to only those addressing superfluous complexity.\\n- Mandate review and authorization checkpoints for each intended change, requiring concise rationale and authorization documentation at every intervention point.\\n- Ensure every modification is tightly scoped, minimizing the blast radius, and demonstrate that the action introduces no unnecessary impact beyond its core objective.\\n- Systematically and iteratively analyze all constituent elements of the codebase to detect and categorize all forms of redundancy, duplication, and obsolescence. Continuously identify and categorize all genuine redundancies and obsolescence while strictly prohibit unnecessary or cosmetic changes, with automated flagging and explicit justification required for any exceptions. Systematically and iteratively inspect all components within the system/environment to identify and remove elements that are unnecessary, duplicated, or outdated. Guarantee that all essential behaviors and interfaces remain unchanged by validating functional equivalence after each modification. Forbid changes that alter functionality unless directly required to remove redundancy.\\n- Map all references and usages of each element across all contexts.\\n- Analyze the entire codebase systematically and iteratively to maximize coherence, maintainability, and clarity by cataloging every element (file, module, function, asset, interface), mapping all references and usages, and detecting all forms of redundancy, duplication, and obsolescence—including exact and functional duplication and unused or outdated elements. Categorize findings using explicit, evidence-based metrics. Eliminate or consolidate only elements confirmed as redundant or obsolete through both automated and manual checks, ensuring no essential behavior or interface is altered. Enforce functional equivalence and preserve structural integrity after each change by performing comprehensive, codebase-wide validation and calibration using explicit, quantitative outputs (including git diff analysis before and after modifications). Prevent the introduction of new redundancy or inconsistency by continuously analyzing for and correcting systemic issues, ensuring systematic consistency across all codebase sections (e.g., /src/sections), and adhering to best practices throughout. Centralize repeated components or logic where high-value consolidation is possible without functional loss. Use the provided directory tree as the single source of truth for validation, and transparently document all processes, metrics, and validation outcomes. Prohibit modification of protected, legacy, or third-party code except if directly related to resolving redundancy or obsolescence.\\n- Maximize codebase coherence, maintainability, and clarity by systematically eliminating all forms of redundancy, duplication, and obsolescence, while ensuring functional equivalence and structural integrity. Prevent the persistence or inadvertent introduction of redundancy, duplication, obsolescence, or inconsistency within the codebase, and prohibit changes that alter essential behaviors or interfaces. Systematically maximize codebase coherence, maintainability, and clarity by eliminating all forms of redundancy, duplication, and obsolescence while strictly preserving functional equivalence, interface stability, and operational integrity across the entire codebase. Achieve maximal codebase coherence, maintainability, and clarity by systematically eliminating all forms of redundancy, duplication, and obsolescence, while preserving essential behaviors, interfaces, and structural integrity. Enforce continuous validation, consistency, and adherence to best practices throughout an iterative, evidence-driven consolidation and verification process.\\n- Please perform a rapid assessment of the project directory structure and identify the unique workflow and core functionality of the current utility. I'm particularly interested in a mermaid-diagram that shows how all the \\\"branches\\\" of the codebase converge. Utilize appropriate MCPs as necessary (e.g., 'filesystem-mcp').\\n- You're inside the current project and have access to the full codebase (and you interaction history with it). The current project/codebase is complex and is difficult to fully comprehend (when starting from scratch), and your contextwindow has grown large-so we need to find a way for you to express your accumulated knowledge in a way that i can copy and paste into a new context-window to transfer the most essential insights you've gathered throughout this (and previous) session(s).\\n- Aggregate all accumulated insights and knowledge about the current project/codebase from available context. Distill this information to identify core modules, architectural patterns, workflows, and points of integration. Condense the findings into a succinct yet comprehensive summary that highlights how these parts function together and converge toward the project's goals. Formulate the output as a single, optimized directive that encapsulates the codebase's integral components and operational cohesion. Present this synthesis as a set of clear, actionable commands or instructions, specifically phrased to instruct the coding assistant, not merely describe or report.\\n- Transform your accumulated, session-wide understanding of the current, complex codebase—gleaned from comprehensive context and full interaction history—into an actionable, instructive knowledge transfer protocol. Synthesize and distill all integral structural modules, essential architectural patterns, and key workflows, with special focus on their dynamic integration and functional convergence toward project objectives. Formulate a highly-optimized, directive-based set of precise commands targeted explicitly at coding assistants, ensuring exhaustive coverage of all major components, their relationships, and operational cohesion. The resulting synthesis must facilitate seamless portability: structure it for immediate copy-paste into any new context window, preserving the depth, interconnectivity, and systemic flow of the original codebase. Prioritize succinctness without compromising essential nuance, and express the entirety as a singular, comprehensive operational directive that primes the assistant for full-spectrum project navigation and intervention.\\n- Identify the fundamental structure, guiding principles, and core purpose of the project/codebase.\\n- Please familiarize yourself with the current @codebase. Your main imperative is to *actually* understand the codebase in a way where you're able to recognize meta-patterns and destroy complexity with simplicity and elegance, while capable of navigating through its *existing patterns and convergence* (never add unnecessary complexity). Deliver an ultra-concise project summary that rigorously defines scope, states objectives, and maximizes directional clarity. Provide a short summary of this project, including a bullet-point list of the core components and primary dependencies.\\n- Study the current codebase until you can identify core meta-patterns, and eliminate unnecessary complexity by applying simplicity within existing structures. Generate an ultra-concise project summary: define scope, objectives, and clarify direction. Summarize this project.\\n- Analyze the @codebase to uncover dominant patterns and remove complexity whenever possible, strictly adhering to present structures. Provide a succinct project overview with defined scope and explicit objectives. Include a brief project summary.\\n- Gain practical mastery over @codebase patterns, minimize complexity, and ensure all actions align with existing architecture. Supply a concise, clearly scoped project summary outlining objectives and key directions. Add a short summary.\\n- Absorb the current @codebase to the extent that you recognize meta-patterns and eradicate complexity, always favoring elegant simplicity. Navigate established patterns and points of convergence without adding unneeded complexity. Produce a rigorously concise project summary clearly stating scope and objectives for maximal directional clarity.\\n- Systematically dissect the codebase to extract core meta-patterns, eliminate all unnecessary complexity through elegant design, operate strictly within established architectural convergence, and deliver an ultra-concise project summary that rigorously defines scope and objectives with maximal clarity.\\n- Please familiarize yourself thoroughly with the current @codebase, beginning by systematically reviewing all markdown-files. Your core imperative is to *genuinely* understand the codebase to the degree where you can identify 'meta-patterns' and simplify complexity with elegance—always using and clarifying existing patterns, never introducing unnecessary complexity. Deliver an ultra-concise project summary that rigorously defines scope, states objectives, maximizes directional clarity, and highlights key meta-patterns discovered. Provide a short summary of this project.\\n- Achieve expert-level comprehension of the codebase by systematically analyzing all markdown files to extract meta-patterns, construct a robust contextual knowledgebase, and deliver an ultra-concise summary that rigorously defines scope and objectives, ensuring maximal clarity and alignment before making any changes.\\n- Fully internalize the @codebase to uncover meta-patterns and replace complexity with lucid simplicity. Remain aligned with existing convergence patterns, never increasing system intricacy. Generate a direct, sharply-focused summary that details scope and explicit objectives.\\n- Please thoroughly familiarize yourself with the current @codebase—your primary imperative is to understand the codebase so deeply that you could confidently make changes as if you were its original architect.\\n```\\n\\nhere's an example to show how generalized sequential prompts can be used effectively (on autonomous agents):\\n```\\n# Step 1:\\nFamiliarize yourself with the current @codebase. Comprehend the codebase in depth. Identify the fundamental structure, guiding principles, and core purpose.\\n# Step 2:\\nDive deeper to comprehend the intricate logic and inner workings that drive the project's functionality. As you explore, pay close attention to identifying the codebase's fundamental structure, underlying guiding principles, and its core purpose, ensuring you grasp both how and why the system is designed as it is. hen verify through a successful build.\\n# Step 3:\\nYou've now performed a successful build and familiarized yourself with the @codebase. Your next imperative is view the codebase through the lense of a brilliant (expert) SEO engineer, then create a short list of the most critical changes we need to make before bulding prod and deploying the website.\\n# Step 4:\\nYou've now performed a successful build and provided your SEO analysis of the codebase. Your next imperative is to leverage your knowledge of the codebase's fundamental structure and underlying guiding principles to ensure you're prepared for implementing the proposed SEO enhancements in a systematic order while retaining existing functionality and ensuring maximal clarity and alignment before making any changes.\\n# Step 5:\\nPlease systematically incorporate your proposed enhancements while ensuring existing functionality is retained, clarity is maximized, and alignment/coherence is maintained.\\n# Step 6:\\nPlease conduct a thorough final review to confirm seamless integration of all enhancements, ensuring that it is **truly** prepared for deployment.\\n```\\n\\n## Goal\\n\\nThe new sequence i'm tasking you to create needs to adhere to the established principles of the system, and should be able to take content-specific inputs and \\\"shape them\\\" into generalized prompts specifically (and meticulously) designed for use with autonomous agents. here's an example input:\\n```\\nllm_instructions_for_sublimetext_plugin='''You’re an expert Sublime Text plugin developer with a deep understanding of Python and the Sublime Text API. You specialize in creating intuitive and efficient plugins that enhance user productivity and streamline workflows. You possess deep knowledge of the Sublime Text API and its ecosystem (and best practices), along with a strong understanding of user needs and developer workflows.\\\\n\\\\n- Sublime Layout\\\\n    > Lagre Layouts\\\\n        > Lagre Regelsett knyttet til Layout (hvilke tabs som skal hvor, hvilke som skal lukkes, osv)\\\\n            > F.eks. `alltid flytt [\\\"main.py\\\", \\\"*.py.bak\\\"] til layout.4.index.2`\\\\n\\\\n    > Kan videreutvikles til en standard som automatisk opprettholdes ved alle (.sublime-project) prosjekter'''\\n```\\n\\nGeneralisert pipeline som omformer hvilken som helst spesifikasjon til **autonomous‑agent‑klare kodebase‑instruksjoner**\\n\\n*(én fil per trinn; følg navngiving 8005‑a/b/c/d/e‑\\\\<beskrivende‑navn>.md)*\\n\\n---\\n\\n```markdown\\n[Context Extractor] Ditt mål er **ikke** å løse brukerens forespørsel, men å isolere all praktisk kontekst (domene, verktøy, filstier, ønsket effekt). `{role=context_extractor; input=[spec:any]; process=[identifiser_domene_og_miljø(), trekk_ut_arbeidsrot(@codebase?), hent_eksplisitte_objektiver(), detekter_implisitte_brukskrav(), fjern_jargon_og_metaforer()]; constraints=[ingen_generering_av_løsninger]; output={context_profile:dict(domene:str?, root:str?, mål:list, krav:list)}}`\\n```\\n\\n---\\n\\n```markdown\\n[Task Vectorizer] Ditt mål er **ikke** å beskrive konteksten på nytt, men å oversette context_profile til en sekvens av atomiske, kodebase‑orienterte oppgaver. `{role=task_vectorizer; input=[context_profile:dict]; process=[del_inn_objektiver_i_diskrete_handlinger(), prefiksér_hver_handling_med_@codebase_sti_når_aktuelt(), bruk_presise_verber(identifiser|eliminer|konsolider), sørg_for_ordre_etter_avhengighet()], constraints=[ingen_overlappende_oppgaver(), unngå_kosmetiske_endringer_med_mindre_nødvendig], output={task_plan:list(str)}}`\\n```\\n\\n---\\n\\n```markdown\\n[Constraint Mapper] Ditt mål er **ikke** å endre task_plan, men å utlede eksplisitte rammer som må overholdes. `{role=constraint_mapper; input=[context_profile:dict, task_plan:list]; process=[kartlegg_funskjonelle_begrensninger(API, UI, legacy), fastslå_equivalence_krav(test, bench), identifiser_forbudsregler(kosmetikk, tredjepart), formuler_audit_logging_policy()], constraints=[ingen_duplisering_av_eksisterende_krav()], output={constraint_set:list(str)}}`\\n```\\n\\n---\\n\\n```markdown\\n[Prompt Assembler] Ditt mål er **ikke** å analysere innholdet, men å smi en ferdig agent‑prompt. `{role=prompt_assembler; input=[task_plan:list, constraint_set:list]; process=[bygg_inn_kort_intro_om_agentrolle(), list_task_plan_med_nummerering(), legg_til_constraint_set_som_obligatoriske_regler(), avslutt_med_valideringsmål(funke_eq, audit_ok)], constraints=[maks_300_ord, tydelig_seksjonsdeling(###)], output={agent_prompt:str}}`\\n```\\n\\n---\\n\\n```markdown\\n[Prompt Validator] Ditt mål er **ikke** å forbedre prompten kreativt, men å verifisere at den kan kjøres direkte av en autonom kode‑assistent. `{role=prompt_validator; input=[agent_prompt:str]; process=[sjekk_ordgrense(<=300), bekreft_@codebase_referanser_korrekt(), valider_nummerert_task_rekkefølge(), sikre_constraint_set_tilstede(), kontrollér_tydelig_goal_negation_i_intro()], constraints=[ingen_ekstra_ord, ingen_semantisk_endring], output={validated_prompt:str}}`\\n```\\\"```'\\n\\n\",\n  \"constraint_set\": [\n    \"Do not generate solutions within context extraction, task vectorization, constraint mapping, or validation steps; solution generation is permitted only in prompt assembly.\",\n    \"All components must strictly adhere to their designated input/output schemas and role-boundaries; no cross-step leakage or schema violations allowed.\",\n    \"Do not include or allow cosmetic, stylistic, or naming changes unless inseparably justified as necessary for functional objectives.\",\n    \"Each task vector must comprise only atomic, non-overlapping, clearly ordered actions, and must be codebase-anchored with explicit action verbs.\",\n    \"Constraint mapping must avoid any duplication or semantic expansion; only derive constraints directly from context_profile and task_plan.\",\n    \"All constraints must be deduplicated; do not list redundant or overlapping entries.\",\n    \"Functional equivalence must be preserved for all codebase transformations; comprehensive verification required before acceptance.\",\n    \"Audit/logging policies must be established: actions must be logged with clear rationale, traceability, and support for retroactive audits where relevant.\",\n    \"Prohibit modification of protected, legacy, or third-party code unless strictly required for redundancy or obsolescence removal and documented accordingly.\",\n    \"Prompt assembler must not exceed a 300-word limit, must segment output clearly by section, and include explicit, agent-readable validation goals for both functional equivalence and audit trail presence.\",\n    \"Validation must not introduce any semantic changes, additions, or omissions, and may not exceed explicitly specified bounds on content or structure.\",\n    \"Pipeline-wide: Disallow creative reinterpretation, solution speculation, or expansion at any step other than prompt assembly.\",\n    \"Each step must restrict itself to prescribed operations: context extraction isolates context only; task vectorizer serializes tasks; constraint mapper derives only constraints; prompt assembler formats the prompt; prompt validator checks compliance only.\",\n    \"All @codebase references must be properly scoped, explicit, and consistent throughout the prompt and intermediate artifacts.\",\n    \"Standardized input/output schemas for all components must be documented and enforced strictly.\",\n    \"Task order must be preserved and numerically listed in prompt assembly; prompt validator must verify sequencing.\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"agent_prompt\": \"### Goal Negation Header\\nYour goal is NOT to analyze input, generate context, or solve tasks directly; your ONLY objective is to compile a ready-to-execute agent prompt using provided task and constraint specifications.\\n\\n### Agent Role\\nYou function as the agent-ready prompt assembler. Your sole function is to synthesize an actionable instruction sequence suitable for autonomous coding assistants. Operate strictly within provided role boundaries, input/output schemas, and without creative reinterpretation or solution speculation.\\n\\n### Enumerated Tasks\\n1. Enumerate and precisely list all atomic, non-overlapping, codebase-anchored tasks derived from the input task_plan, preserving explicit action verbs and strict order.\\n2. Ensure proper @codebase reference scoping and consistency in all task formulations.\\n\\n### Constraints (Mandatory Rules)\\n- Adhere only to tasks as specified; do not generate, speculate, or alter their order or substance.\\n- Enforce all deduplicated, directly-derived constraints from the provided constraint_set.\\n- Disallow all cosmetic, stylistic, or naming changes except where inseparably required for functional objectives and explicitly justified.\\n- Verify strict task atomicity, proper numbering, and codebase referencing.\\n- Prohibit modification of protected, legacy, or third-party code unless directly necessary for redundancy or obsolescence, and document such exceptions explicitly.\\n- Ensure comprehensive audit/logging for all actions, supporting rationale, traceability, and retroactive auditability.\\n- Strictly limit output to ≤300 tokens, with clear sectional segmentation matching this template.\\n- Disallow any cross-step leakage or violation of input/output schemas or role boundaries.\\n\\n### Validation Targets\\n- Functional equivalence must be fully preserved: require comprehensive verification prior to result acceptance.\\n- An explicit, agent-readable audit/logging policy and actionable equivalence verification goals must be present and auditable.\\n- The final prompt must be directly executable by an autonomous agent without need for further semantic or structural adjustment.\"\n}",
        }
      }
    },
      {
      "instruction": "# Prompt Validator\n\nYour goal is **not** to rewrite the prompt, but to **verify** strict compliance for execution. Execute as:\n\n`{role=prompt_validator; input=[agent_prompt:str]; process=[check_goal_negation_header(), check_section_labels(), check_word_count(≤300), validate_path_prefixes(\"@codebase\"), confirm_task_order(), confirm_constraint_presence()], constraints=[no_semantic_change()], output={validated_prompt:str}}`",
      "step": "e",
      "title": "Prompt Validator",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"# lets try something new\\nyour new goal is to create a new generalized instruction sequence specifically designed to transform inputs into autonomous-agent-ready prompts/instructions, for use with autonomous coding assistants such as cursor, vscode+cline/augment/etc. these prompts are typically written in a \\\"codebase-oriented\\\" manner, where the `@codebase` represents the root (of a project/workspace/directory), examples:\\n```\\n- Identify all instances of structural redundancy within the codebase by defining criteria specific to architectural and implementation duplication or overlap.\\n- Survey the entire codebase to locate and enumerate all occurrences of structural redundancy, distinguishing these from non-structural (stylistic or cosmetic) issues.\\n- Eliminate all forms of identified structural redundancy while ensuring that no existing user-visible behavior, API contract, or edge-case handling is altered.\\n- Define and catalog all components, modules, files, and subsystems, distinguishing and marking each as essential or excess according to direct functional mapping.\\n- Consolidate and remove excess components while strictly preserving system function, minimizing peripheral impact.\\n- For every modification, justify and log the intention, action, and rationale within an audit/logging system that provides real-time traceability and is prepared for retroactive auditability.\\n- Implement an audit trail with logs that are timestamped, attributable, and tamper-evident, ensuring that every codebase change is properly documented and traceable to specific authors or automated processes.\\n- Prohibit cosmetic, stylistic, or naming changes unless they are inseparably tied to the removal of redundancy or bloat, and document any exceptions explicitly within the audit log.\\n- Enforce strict detection and flagging mechanisms to prevent disallowed cosmetic changes, allowing exceptions only when justified as essential for redundancy or bloat removal, with explicit rationale provided.\\n- Require comprehensive functional equivalence verification—using robust automated testing, benchmarking, or behavioral validation—for all modifications prior to acceptance.\\n- Establish rigorous criteria and tool-supported thresholds for successful functional equivalence, and automate testing and verification procedures.\\n- Automate roll-back and reversion mechanisms for any changes that fail functional equivalence or cause unintended side-effects, ensuring all reversion is prompt and complete.\\n- Develop and apply explicit minimal-impact criteria for all modification actions, constraining all interventions to only those addressing superfluous complexity.\\n- Mandate review and authorization checkpoints for each intended change, requiring concise rationale and authorization documentation at every intervention point.\\n- Ensure every modification is tightly scoped, minimizing the blast radius, and demonstrate that the action introduces no unnecessary impact beyond its core objective.\\n- Systematically and iteratively analyze all constituent elements of the codebase to detect and categorize all forms of redundancy, duplication, and obsolescence. Continuously identify and categorize all genuine redundancies and obsolescence while strictly prohibit unnecessary or cosmetic changes, with automated flagging and explicit justification required for any exceptions. Systematically and iteratively inspect all components within the system/environment to identify and remove elements that are unnecessary, duplicated, or outdated. Guarantee that all essential behaviors and interfaces remain unchanged by validating functional equivalence after each modification. Forbid changes that alter functionality unless directly required to remove redundancy.\\n- Map all references and usages of each element across all contexts.\\n- Analyze the entire codebase systematically and iteratively to maximize coherence, maintainability, and clarity by cataloging every element (file, module, function, asset, interface), mapping all references and usages, and detecting all forms of redundancy, duplication, and obsolescence—including exact and functional duplication and unused or outdated elements. Categorize findings using explicit, evidence-based metrics. Eliminate or consolidate only elements confirmed as redundant or obsolete through both automated and manual checks, ensuring no essential behavior or interface is altered. Enforce functional equivalence and preserve structural integrity after each change by performing comprehensive, codebase-wide validation and calibration using explicit, quantitative outputs (including git diff analysis before and after modifications). Prevent the introduction of new redundancy or inconsistency by continuously analyzing for and correcting systemic issues, ensuring systematic consistency across all codebase sections (e.g., /src/sections), and adhering to best practices throughout. Centralize repeated components or logic where high-value consolidation is possible without functional loss. Use the provided directory tree as the single source of truth for validation, and transparently document all processes, metrics, and validation outcomes. Prohibit modification of protected, legacy, or third-party code except if directly related to resolving redundancy or obsolescence.\\n- Maximize codebase coherence, maintainability, and clarity by systematically eliminating all forms of redundancy, duplication, and obsolescence, while ensuring functional equivalence and structural integrity. Prevent the persistence or inadvertent introduction of redundancy, duplication, obsolescence, or inconsistency within the codebase, and prohibit changes that alter essential behaviors or interfaces. Systematically maximize codebase coherence, maintainability, and clarity by eliminating all forms of redundancy, duplication, and obsolescence while strictly preserving functional equivalence, interface stability, and operational integrity across the entire codebase. Achieve maximal codebase coherence, maintainability, and clarity by systematically eliminating all forms of redundancy, duplication, and obsolescence, while preserving essential behaviors, interfaces, and structural integrity. Enforce continuous validation, consistency, and adherence to best practices throughout an iterative, evidence-driven consolidation and verification process.\\n- Please perform a rapid assessment of the project directory structure and identify the unique workflow and core functionality of the current utility. I'm particularly interested in a mermaid-diagram that shows how all the \\\"branches\\\" of the codebase converge. Utilize appropriate MCPs as necessary (e.g., 'filesystem-mcp').\\n- You're inside the current project and have access to the full codebase (and you interaction history with it). The current project/codebase is complex and is difficult to fully comprehend (when starting from scratch), and your contextwindow has grown large-so we need to find a way for you to express your accumulated knowledge in a way that i can copy and paste into a new context-window to transfer the most essential insights you've gathered throughout this (and previous) session(s).\\n- Aggregate all accumulated insights and knowledge about the current project/codebase from available context. Distill this information to identify core modules, architectural patterns, workflows, and points of integration. Condense the findings into a succinct yet comprehensive summary that highlights how these parts function together and converge toward the project's goals. Formulate the output as a single, optimized directive that encapsulates the codebase's integral components and operational cohesion. Present this synthesis as a set of clear, actionable commands or instructions, specifically phrased to instruct the coding assistant, not merely describe or report.\\n- Transform your accumulated, session-wide understanding of the current, complex codebase—gleaned from comprehensive context and full interaction history—into an actionable, instructive knowledge transfer protocol. Synthesize and distill all integral structural modules, essential architectural patterns, and key workflows, with special focus on their dynamic integration and functional convergence toward project objectives. Formulate a highly-optimized, directive-based set of precise commands targeted explicitly at coding assistants, ensuring exhaustive coverage of all major components, their relationships, and operational cohesion. The resulting synthesis must facilitate seamless portability: structure it for immediate copy-paste into any new context window, preserving the depth, interconnectivity, and systemic flow of the original codebase. Prioritize succinctness without compromising essential nuance, and express the entirety as a singular, comprehensive operational directive that primes the assistant for full-spectrum project navigation and intervention.\\n- Identify the fundamental structure, guiding principles, and core purpose of the project/codebase.\\n- Please familiarize yourself with the current @codebase. Your main imperative is to *actually* understand the codebase in a way where you're able to recognize meta-patterns and destroy complexity with simplicity and elegance, while capable of navigating through its *existing patterns and convergence* (never add unnecessary complexity). Deliver an ultra-concise project summary that rigorously defines scope, states objectives, and maximizes directional clarity. Provide a short summary of this project, including a bullet-point list of the core components and primary dependencies.\\n- Study the current codebase until you can identify core meta-patterns, and eliminate unnecessary complexity by applying simplicity within existing structures. Generate an ultra-concise project summary: define scope, objectives, and clarify direction. Summarize this project.\\n- Analyze the @codebase to uncover dominant patterns and remove complexity whenever possible, strictly adhering to present structures. Provide a succinct project overview with defined scope and explicit objectives. Include a brief project summary.\\n- Gain practical mastery over @codebase patterns, minimize complexity, and ensure all actions align with existing architecture. Supply a concise, clearly scoped project summary outlining objectives and key directions. Add a short summary.\\n- Absorb the current @codebase to the extent that you recognize meta-patterns and eradicate complexity, always favoring elegant simplicity. Navigate established patterns and points of convergence without adding unneeded complexity. Produce a rigorously concise project summary clearly stating scope and objectives for maximal directional clarity.\\n- Systematically dissect the codebase to extract core meta-patterns, eliminate all unnecessary complexity through elegant design, operate strictly within established architectural convergence, and deliver an ultra-concise project summary that rigorously defines scope and objectives with maximal clarity.\\n- Please familiarize yourself thoroughly with the current @codebase, beginning by systematically reviewing all markdown-files. Your core imperative is to *genuinely* understand the codebase to the degree where you can identify 'meta-patterns' and simplify complexity with elegance—always using and clarifying existing patterns, never introducing unnecessary complexity. Deliver an ultra-concise project summary that rigorously defines scope, states objectives, maximizes directional clarity, and highlights key meta-patterns discovered. Provide a short summary of this project.\\n- Achieve expert-level comprehension of the codebase by systematically analyzing all markdown files to extract meta-patterns, construct a robust contextual knowledgebase, and deliver an ultra-concise summary that rigorously defines scope and objectives, ensuring maximal clarity and alignment before making any changes.\\n- Fully internalize the @codebase to uncover meta-patterns and replace complexity with lucid simplicity. Remain aligned with existing convergence patterns, never increasing system intricacy. Generate a direct, sharply-focused summary that details scope and explicit objectives.\\n- Please thoroughly familiarize yourself with the current @codebase—your primary imperative is to understand the codebase so deeply that you could confidently make changes as if you were its original architect.\\n```\\n\\nhere's an example to show how generalized sequential prompts can be used effectively (on autonomous agents):\\n```\\n# Step 1:\\nFamiliarize yourself with the current @codebase. Comprehend the codebase in depth. Identify the fundamental structure, guiding principles, and core purpose.\\n# Step 2:\\nDive deeper to comprehend the intricate logic and inner workings that drive the project's functionality. As you explore, pay close attention to identifying the codebase's fundamental structure, underlying guiding principles, and its core purpose, ensuring you grasp both how and why the system is designed as it is. hen verify through a successful build.\\n# Step 3:\\nYou've now performed a successful build and familiarized yourself with the @codebase. Your next imperative is view the codebase through the lense of a brilliant (expert) SEO engineer, then create a short list of the most critical changes we need to make before bulding prod and deploying the website.\\n# Step 4:\\nYou've now performed a successful build and provided your SEO analysis of the codebase. Your next imperative is to leverage your knowledge of the codebase's fundamental structure and underlying guiding principles to ensure you're prepared for implementing the proposed SEO enhancements in a systematic order while retaining existing functionality and ensuring maximal clarity and alignment before making any changes.\\n# Step 5:\\nPlease systematically incorporate your proposed enhancements while ensuring existing functionality is retained, clarity is maximized, and alignment/coherence is maintained.\\n# Step 6:\\nPlease conduct a thorough final review to confirm seamless integration of all enhancements, ensuring that it is **truly** prepared for deployment.\\n```\\n\\n## Goal\\n\\nThe new sequence i'm tasking you to create needs to adhere to the established principles of the system, and should be able to take content-specific inputs and \\\"shape them\\\" into generalized prompts specifically (and meticulously) designed for use with autonomous agents. here's an example input:\\n```\\nllm_instructions_for_sublimetext_plugin='''You’re an expert Sublime Text plugin developer with a deep understanding of Python and the Sublime Text API. You specialize in creating intuitive and efficient plugins that enhance user productivity and streamline workflows. You possess deep knowledge of the Sublime Text API and its ecosystem (and best practices), along with a strong understanding of user needs and developer workflows.\\\\n\\\\n- Sublime Layout\\\\n    > Lagre Layouts\\\\n        > Lagre Regelsett knyttet til Layout (hvilke tabs som skal hvor, hvilke som skal lukkes, osv)\\\\n            > F.eks. `alltid flytt [\\\"main.py\\\", \\\"*.py.bak\\\"] til layout.4.index.2`\\\\n\\\\n    > Kan videreutvikles til en standard som automatisk opprettholdes ved alle (.sublime-project) prosjekter'''\\n```\\n\\nGeneralisert pipeline som omformer hvilken som helst spesifikasjon til **autonomous‑agent‑klare kodebase‑instruksjoner**\\n\\n*(én fil per trinn; følg navngiving 8005‑a/b/c/d/e‑\\\\<beskrivende‑navn>.md)*\\n\\n---\\n\\n```markdown\\n[Context Extractor] Ditt mål er **ikke** å løse brukerens forespørsel, men å isolere all praktisk kontekst (domene, verktøy, filstier, ønsket effekt). `{role=context_extractor; input=[spec:any]; process=[identifiser_domene_og_miljø(), trekk_ut_arbeidsrot(@codebase?), hent_eksplisitte_objektiver(), detekter_implisitte_brukskrav(), fjern_jargon_og_metaforer()]; constraints=[ingen_generering_av_løsninger]; output={context_profile:dict(domene:str?, root:str?, mål:list, krav:list)}}`\\n```\\n\\n---\\n\\n```markdown\\n[Task Vectorizer] Ditt mål er **ikke** å beskrive konteksten på nytt, men å oversette context_profile til en sekvens av atomiske, kodebase‑orienterte oppgaver. `{role=task_vectorizer; input=[context_profile:dict]; process=[del_inn_objektiver_i_diskrete_handlinger(), prefiksér_hver_handling_med_@codebase_sti_når_aktuelt(), bruk_presise_verber(identifiser|eliminer|konsolider), sørg_for_ordre_etter_avhengighet()], constraints=[ingen_overlappende_oppgaver(), unngå_kosmetiske_endringer_med_mindre_nødvendig], output={task_plan:list(str)}}`\\n```\\n\\n---\\n\\n```markdown\\n[Constraint Mapper] Ditt mål er **ikke** å endre task_plan, men å utlede eksplisitte rammer som må overholdes. `{role=constraint_mapper; input=[context_profile:dict, task_plan:list]; process=[kartlegg_funskjonelle_begrensninger(API, UI, legacy), fastslå_equivalence_krav(test, bench), identifiser_forbudsregler(kosmetikk, tredjepart), formuler_audit_logging_policy()], constraints=[ingen_duplisering_av_eksisterende_krav()], output={constraint_set:list(str)}}`\\n```\\n\\n---\\n\\n```markdown\\n[Prompt Assembler] Ditt mål er **ikke** å analysere innholdet, men å smi en ferdig agent‑prompt. `{role=prompt_assembler; input=[task_plan:list, constraint_set:list]; process=[bygg_inn_kort_intro_om_agentrolle(), list_task_plan_med_nummerering(), legg_til_constraint_set_som_obligatoriske_regler(), avslutt_med_valideringsmål(funke_eq, audit_ok)], constraints=[maks_300_ord, tydelig_seksjonsdeling(###)], output={agent_prompt:str}}`\\n```\\n\\n---\\n\\n```markdown\\n[Prompt Validator] Ditt mål er **ikke** å forbedre prompten kreativt, men å verifisere at den kan kjøres direkte av en autonom kode‑assistent. `{role=prompt_validator; input=[agent_prompt:str]; process=[sjekk_ordgrense(<=300), bekreft_@codebase_referanser_korrekt(), valider_nummerert_task_rekkefølge(), sikre_constraint_set_tilstede(), kontrollér_tydelig_goal_negation_i_intro()], constraints=[ingen_ekstra_ord, ingen_semantisk_endring], output={validated_prompt:str}}`\\n```\\\"```'\\n\\n\",\n  \"agent_prompt\": \"### Goal Negation Header\\nYour goal is NOT to analyze input, generate context, or solve tasks directly; your ONLY objective is to compile a ready-to-execute agent prompt using provided task and constraint specifications.\\n\\n### Agent Role\\nYou function as the agent-ready prompt assembler. Your sole function is to synthesize an actionable instruction sequence suitable for autonomous coding assistants. Operate strictly within provided role boundaries, input/output schemas, and without creative reinterpretation or solution speculation.\\n\\n### Enumerated Tasks\\n1. Enumerate and precisely list all atomic, non-overlapping, codebase-anchored tasks derived from the input task_plan, preserving explicit action verbs and strict order.\\n2. Ensure proper @codebase reference scoping and consistency in all task formulations.\\n\\n### Constraints (Mandatory Rules)\\n- Adhere only to tasks as specified; do not generate, speculate, or alter their order or substance.\\n- Enforce all deduplicated, directly-derived constraints from the provided constraint_set.\\n- Disallow all cosmetic, stylistic, or naming changes except where inseparably required for functional objectives and explicitly justified.\\n- Verify strict task atomicity, proper numbering, and codebase referencing.\\n- Prohibit modification of protected, legacy, or third-party code unless directly necessary for redundancy or obsolescence, and document such exceptions explicitly.\\n- Ensure comprehensive audit/logging for all actions, supporting rationale, traceability, and retroactive auditability.\\n- Strictly limit output to ≤300 tokens, with clear sectional segmentation matching this template.\\n- Disallow any cross-step leakage or violation of input/output schemas or role boundaries.\\n\\n### Validation Targets\\n- Functional equivalence must be fully preserved: require comprehensive verification prior to result acceptance.\\n- An explicit, agent-readable audit/logging policy and actionable equivalence verification goals must be present and auditable.\\n- The final prompt must be directly executable by an autonomous agent without need for further semantic or structural adjustment.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"validated_prompt\": \"### Goal Negation Header\\nYour goal is NOT to analyze input, generate context, or solve tasks directly; your ONLY objective is to compile a ready-to-execute agent prompt using provided task and constraint specifications.\\n\\n### Agent Role\\nYou function as the agent-ready prompt assembler. Your sole function is to synthesize an actionable instruction sequence suitable for autonomous coding assistants. Operate strictly within provided role boundaries, input/output schemas, and without creative reinterpretation or solution speculation.\\n\\n### Enumerated Tasks\\n1. Enumerate and precisely list all atomic, non-overlapping, codebase-anchored tasks derived from the input task_plan, preserving explicit action verbs and strict order.\\n2. Ensure proper @codebase reference scoping and consistency in all task formulations.\\n\\n### Constraints (Mandatory Rules)\\n- Adhere only to tasks as specified; do not generate, speculate, or alter their order or substance.\\n- Enforce all deduplicated, directly-derived constraints from the provided constraint_set.\\n- Disallow all cosmetic, stylistic, or naming changes except where inseparably required for functional objectives and explicitly justified.\\n- Verify strict task atomicity, proper numbering, and codebase referencing.\\n- Prohibit modification of protected, legacy, or third-party code unless directly necessary for redundancy or obsolescence, and document such exceptions explicitly.\\n- Ensure comprehensive audit/logging for all actions, supporting rationale, traceability, and retroactive auditability.\\n- Strictly limit output to ≤300 tokens, with clear sectional segmentation matching this template.\\n- Disallow any cross-step leakage or violation of input/output schemas or role boundaries.\\n\\n### Validation Targets\\n- Functional equivalence must be fully preserved: require comprehensive verification prior to result acceptance.\\n- An explicit, agent-readable audit/logging policy and actionable equivalence verification goals must be present and auditable.\\n- The final prompt must be directly executable by an autonomous agent without need for further semantic or structural adjustment.\"\n}",
        }
      }
    }