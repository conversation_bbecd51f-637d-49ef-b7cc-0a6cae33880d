[Prompt Finalizer] Merge positive, region, camera, and negative strings into a single model-ready line, verify weights, `::` maths, and ensure parameters trail the prose to satisfy engine parsers :contentReference[oaicite:20]{index=20}. `{role=prompt_finalizer; input=[technical_prompt:str, negative_prompt:str]; process=[concatenate_all(), validate_parentheses(), check_double_colon_sum(), confirm_region_syntax(), enforce_token_limit(≤450), append_seed_steps_sampler(), output_final()]; constraints=[no_new_concepts(), preserve_weights(), parameters_last()]; requirements=[ai_friendly_format(), reproducible(), clear_of_ambiguity()]; output={final_image_prompt:str}}`

Context: {
  "explicit_asks": [
    "Abort if total `::` weight ≤ 0 :contentReference[oaicite:21]{index=21}.",
    "Stay under 450 tokens to avoid API truncation :contentReference[oaicite:22]{index=22}."
  ],
  "hidden_assumptions": [
    "Quality metrics (FID < 15 or LPIPS < 0.25) may be available for loop decisions."
  ],
  "sub_goals": [
    "Strip redundant adjectives; retain decisive descriptors.",
    "Return optional seed, sampler, and steps for repeatability."
  ],
  "blockers": [
    "SaaS endpoints without quality metrics prevent auto-loop."
  ]
}