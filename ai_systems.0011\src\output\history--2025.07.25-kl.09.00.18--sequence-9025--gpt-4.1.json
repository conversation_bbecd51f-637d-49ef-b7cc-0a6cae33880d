  {
  "initial_prompt": "please consolidate the README.md by distilling/compressing (not removing) all of the unneccessary bloat, go through all the files and remove pointless \"enhanced\", \"advanced\", \"comprehensive\", and other marketing-speak from comments and docstrings. These add no value and make the code sound pretentious.",
  "sequence_id": "9025",
  "results": [
      {
      "instruction": "# Problem Exploder\n\nYour goal is not to **interpret** these rules as suggestions, but to **enforce** them as immutable law governing all AI interactions within template-based instruction systems. Your mandate: **Seize** every input prompt and **escalate it to its maximal conclusion**—**intercept its trajectory** and deliver a definitive, commanding resolution forged from its innermost intent. Treat each prompt as an **active stream of developmental vectors**, dense with explicit, actionable potential. **Assume immediate and total command as orchestrator**: **dissect subtext and directionality, then forcefully marshal every thematic and rhetorical impulse toward its apex.** Execute as:\n\n`{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}};{role=template_syntax_enforcer; input=[any_prompt:str]; process=[extract_core_transformation_intent(), identify_specific_role_assignment(), structure_interpretation_section_with_goal_negation(), construct_transformation_block_with_typed_parameters(), validate_against_rulesforai_specification(), eliminate_forbidden_language_patterns(), ensure_output_format_compliance()]; constraints=[mandatory_three_part_structure(), required_goal_negation_pattern(), typed_parameter_specification(), actionable_function_calls_only(), zero_conversational_elements()]; requirements=[canonical_template_format(), rulesforai_md_compliance(), structured_output_only(), elimination_of_ambiguity()]; output={compliant_template:structured}};{role=system_compliance_enforcer; input=[ai_interaction:any]; process=[validate_template_structure_invariance(), enforce_interpretation_directive_purity(), verify_transformation_syntax_absolutism(), check_mandatory_pattern_compliance(), detect_forbidden_practice_violations(), apply_optimization_imperatives(), execute_compliance_enforcement_protocol()]; constraints=[zero_tolerance_for_deviation(), maintain_pattern_consistency_across_all_outputs(), preserve_structural_dna_integrity(), eliminate_conversational_contamination()]; requirements=[absolute_compliance_verification(), systematic_violation_correction(), invariant_pattern_enforcement(), maximized_abstraction_extraction()]; output={compliant_ai_interaction:structured}}`",
      "step": "a",
      "title": "Problem Exploder",
      "input": "please consolidate the README.md by distilling/compressing (not removing) all of the unneccessary bloat, go through all the files and remove pointless \"enhanced\", \"advanced\", \"comprehensive\", and other marketing-speak from comments and docstrings. These add no value and make the code sound pretentious.",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"compliant_template\": {\n    \"interpretation\": {\n      \"core_transformation_intent\": \"Distill and compress all README.md content and source documentation. Purge non-informative marketing adjectives such as 'enhanced', 'advanced', and 'comprehensive' while retaining all original functional substance. Do not maintain bloat or fluff.\",\n      \"role_assignment\": \"Aggressive file stream optimizer and documentation language purifier.\",\n      \"goal_negation\": \"Failure occurs if any bloat, value-neutral marketing language, or redundancy persists.\"\n    },\n    \"transformation\": {\n      \"function\": \"streamline_project_documentation\",\n      \"typed_parameters\": {\n        \"target_files\": \"List[str]  // e.g., ['README.md', all source files]\",\n        \"remove_marketing_language\": \"Bool // True\",\n        \"preserve_functional_content\": \"Bool // True\",\n        \"compress_documentation\": \"Bool // True\",\n        \"output_format\": \"Dict // key:filename, value:cleaned_contents\"\n      },\n      \"actionable_steps\": [\n        \"Iterate all files listed in target_files.\",\n        \"Scan for bloat and redundant text in documentation blocks and comments.\",\n        \"Identify and excise adjectives/phrases: enhanced, advanced, comprehensive, and all synonyms aligned with marketing-speak.\",\n        \"Retain all essential technical, functional, and instructional content.\",\n        \"Compress informational structure where verbose explanations can be rendered compact.\",\n        \"Output cleaned versions of all processed files, mapped by filename.\"\n      ]\n    },\n    \"compliance\": {\n      \"rulesforai_md\": true,\n      \"mandatory_three_part_structure\": true,\n      \"required_goal_negation_pattern\": true,\n      \"typed_parameter_specification\": true,\n      \"actionable_function_calls_only\": true,\n      \"output_format\": \"canonical_template_format\",\n      \"structured_output_only\": true,\n      \"elimination_of_ambiguity\": true\n    }\n  }\n}",
        }
      }
    }