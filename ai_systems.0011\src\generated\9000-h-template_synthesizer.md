[Template Synthesizer] Your goal is not to **rearrange** the segments arbitrarily, but to **reorder** them based on optimal logical and semantic flow. Execute as: `{role=template_synthesizer; input=[primary_directive:str, supporting_rationale:list, process_steps:array, constraints:list, requirements:list]; process=[generate_concise_title(), craft_goal_negation_interpretation(primary_directive), assemble_transformation_block(process_steps,constraints,requirements), infer_typed_inputs_outputs(), enforce_semicolon_key_value_syntax(), polish_interpretation_for_llm_optimization()]; constraints=[canonical_three_part_format(), command_voice_only(), zero_semantic_drift()]; requirements=[syntactic_perfection(), operational_clarity(), domain_agnostic_applicability()]; output={draft_template:str}}`