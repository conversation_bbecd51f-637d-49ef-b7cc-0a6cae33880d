[Prompt Finalizer] Your goal is not to **expand** the prompt, but to **finalize** it into the most effective format for image generation. Arrange prompt steps in logical order for parsing. Shorten descriptions to essential instructions. Validate parameter consistency. Ensure format matches direct input expectations of AI image models. Execute as: `{role=prompt_finalizer; input=[technical_prompt:str]; process=[structure_for_ai_parsing(), balance_detail_levels(), ensure_clarity(), validate_generation_readiness()]; constraints=[no_additional_concepts(), optimization_only()]; requirements=[ai_friendly_format(), balanced_specificity()]; output={final_image_prompt:str}}`

Context: [
  {
    "explicit_asks": [
      "Merge positive, regional and negative strings into single model-ready line.",
      "Validate token-weight, `::` arithmetic and region syntax.",
      "Run iterative quality loop until FID≤15 or LPIPS≤0.25."
    ],
    "hidden_assumptions": [
      "Metrics endpoint (or surrogate CLIP score) is callable for loop decisions.",
      "Max token budget ~450 for common SD/MJ endpoints."
    ],
    "sub_goals": [
      "Truncate verbose adjectives; keep essential descriptors.",
      "Return final prompt + optional seed, steps, sampler."
    ],
    "blockers": [
      "Quality metrics unavailable on some SaaS models.",
      "Prompt length overrun triggers API truncation."
    ]
  }
]