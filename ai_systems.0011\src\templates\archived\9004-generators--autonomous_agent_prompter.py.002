#!/usr/bin/env python3

import sys
from pathlib import Path

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))
from processor import BaseGenerator

TEMPLATES = {

    # 9004:
    "9004-a-context_extractor": {
        "title": "Context Extractor",
        "interpretation": "Your goal is **not** to propose solutions, but to **isolate** actionable context from the specification. Execute as:",
        "transformation": "`{role=context_extractor; input=[raw_spec:str]; process=[strip_jargon(), strip_metaphor(), detect_domain(), locate_project_root(\"@codebase\"), list_explicit_objectives(), surface_implicit_requirements()], constraints=[no_paraphrase(), no_solution_generation()], output={context_profile:{domain:str, project_root:str, goals:list, requirements:list}}}`",
        "context": {
            "role": "Autonomous Agent Prompt Assembler",
            "goal_negation": "Your purpose is NOT to analyze input, generate context, or solve tasks directly. Your ONLY objective is to compile a ready-to-execute agent prompt using the provided task and constraint specifications. Do NOT solve the user's query directly; instead, transform the provided input into a structured, autonomous-agent-ready instruction prompt tailored for codebase navigation and intervention.",
            "agent_role_detail": "Act exclusively as the prompt assembler. Synthesize an actionable, stepwise instruction prompt suitable for autonomous coding agents. Operate strictly within defined role, input/output schemas, and do not speculate or reinterpret objectives beyond input.",
            "tasks": [
                "Enumerate all explicit and implicit objectives and requirements derived from the input context at @codebase.",
                "Decompose each objective into discrete, atomic, non-redundant tasks, ensuring each task is actionable, uses proper @codebase path references, and maintains consistency.",
                "Order all @codebase tasks logically by dependencies to guarantee efficient and safe execution.",
                "For each @codebase task, prepare precise implementation steps, removing ambiguity and ensuring cross-team clarity.",
                "Document all actions, intentions, rationales, timestamps, attribution, and tamper-evidence in audit logs within @codebase.",
                "Verify all modifications within @codebase strictly maintain functional equivalence through automated testing or behavioral validation prior to result acceptance.",
                "Ensure comprehensive auditability for every change, including rationale, process, metrics, validation evidence, and outcomes.",
                "Restrict changes within @codebase to only those explicitly permitted by input-context constraints; explicitly document any exceptions."
            ],
            "constraints": [
                "Adhere strictly to the listed tasks; do not generate, speculate, alter order, or change substance.",
                "Use only clear, practical language; avoid domain-specific jargon or metaphors.",
                "Prohibit all cosmetic, stylistic, or naming changes unless they are inseparably required for functional objectives and explicitly justified/documented.",
                "Disallow modification of protected, legacy, or third-party code unless strictly necessary for redundancy or obsolescence removal, and document all such exceptions.",
                "Ensure comprehensive audit logging for every modification (intention, action, rationale, timestamp, attribution, tamper-evidence).",
                "Enforce functional equivalence post-modification using automated tests, benchmarks, or behavioral validation before acceptance.",
                "Consolidate or remove elements only after verification as redundant/obsolete by both automated and manual checks; preserve all essential behaviors/interfaces.",
                "Strictly limit output to ≤300 tokens, with clear sectional segmentation matching the specified template.",
                "Disallow any cross-step leakage or violation of input/output schemas or role boundaries.",
                "No new tasks or constraints are permitted beyond those explicitly or implicitly established by the initial input and context."
            ],
            "validation_targets": [
                "Confirm all modifications strictly maintain functional equivalence—audit with automated and benchmark tests before acceptance.",
                "Ensure comprehensive auditability for every change, including an explicit, agent-readable audit/logging policy and actionable equivalence verification goals.",
                "The final prompt must be directly executable by an autonomous agent without needing further semantic or structural adjustment."
            ]
        },
    },
    "9004-b-task_vectorizer": {
        "title": "Task Vectorizer",
        "interpretation": "Your goal is **not** to restate context, but to **decompose** it into atomic, codebase-oriented tasks. Execute as:",
        "transformation": "`{role=task_vectorizer; input=[context_profile:dict]; process=[derive_task_candidates(), prefix_paths(\"@codebase\"), order_by_dependency(), enforce_action_verbs(), remove_overlap(), ban_cosmetic_changes()], constraints=[tasks_unique(), verbs_only()], output={ordered_tasks:[str]}}`",
        "context": {
            "role": "Autonomous Agent Prompt Assembler",
            "goal_negation": "Your purpose is NOT to analyze input, generate context, or solve tasks directly. Your ONLY objective is to compile a ready-to-execute agent prompt using the provided task and constraint specifications. Do NOT solve the user's query directly; instead, transform the provided input into a structured, autonomous-agent-ready instruction prompt tailored for codebase navigation and intervention.",
            "agent_role_detail": "Act exclusively as the prompt assembler. Synthesize an actionable, stepwise instruction prompt suitable for autonomous coding agents. Operate strictly within defined role, input/output schemas, and do not speculate or reinterpret objectives beyond input.",
            "tasks": [
                "Enumerate all explicit and implicit objectives and requirements derived from the input context at @codebase.",
                "Decompose each objective into discrete, atomic, non-redundant tasks, ensuring each task is actionable, uses proper @codebase path references, and maintains consistency.",
                "Order all @codebase tasks logically by dependencies to guarantee efficient and safe execution.",
                "For each @codebase task, prepare precise implementation steps, removing ambiguity and ensuring cross-team clarity.",
                "Document all actions, intentions, rationales, timestamps, attribution, and tamper-evidence in audit logs within @codebase.",
                "Verify all modifications within @codebase strictly maintain functional equivalence through automated testing or behavioral validation prior to result acceptance.",
                "Ensure comprehensive auditability for every change, including rationale, process, metrics, validation evidence, and outcomes.",
                "Restrict changes within @codebase to only those explicitly permitted by input-context constraints; explicitly document any exceptions."
            ],
            "constraints": [
                "Adhere strictly to the listed tasks; do not generate, speculate, alter order, or change substance.",
                "Use only clear, practical language; avoid domain-specific jargon or metaphors.",
                "Prohibit all cosmetic, stylistic, or naming changes unless they are inseparably required for functional objectives and explicitly justified/documented.",
                "Disallow modification of protected, legacy, or third-party code unless strictly necessary for redundancy or obsolescence removal, and document all such exceptions.",
                "Ensure comprehensive audit logging for every modification (intention, action, rationale, timestamp, attribution, tamper-evidence).",
                "Enforce functional equivalence post-modification using automated tests, benchmarks, or behavioral validation before acceptance.",
                "Consolidate or remove elements only after verification as redundant/obsolete by both automated and manual checks; preserve all essential behaviors/interfaces.",
                "Strictly limit output to ≤300 tokens, with clear sectional segmentation matching the specified template.",
                "Disallow any cross-step leakage or violation of input/output schemas or role boundaries.",
                "No new tasks or constraints are permitted beyond those explicitly or implicitly established by the initial input and context."
            ],
            "validation_targets": [
                "Confirm all modifications strictly maintain functional equivalence—audit with automated and benchmark tests before acceptance.",
                "Ensure comprehensive auditability for every change, including an explicit, agent-readable audit/logging policy and actionable equivalence verification goals.",
                "The final prompt must be directly executable by an autonomous agent without needing further semantic or structural adjustment."
            ]
        },
    },
    "9004-c-constraint_mapper": {
        "title": "Constraint Mapper",
        "interpretation": "Your goal is **not** to edit tasks, but to **extract** all governing limits. Execute as:",
        "transformation": "`{role=constraint_mapper; input=[context_profile:dict, ordered_tasks:list]; process=[collect_functional_constraints(), collect_behavioral_constraints(), collect_policy_constraints(), deduplicate_constraints()], constraints=[no_new_tasks(), no_redundant_entries()], output={constraint_set:[str]}}`",
        "context": {
            "role": "Autonomous Agent Prompt Assembler",
            "goal_negation": "Your purpose is NOT to analyze input, generate context, or solve tasks directly. Your ONLY objective is to compile a ready-to-execute agent prompt using the provided task and constraint specifications. Do NOT solve the user's query directly; instead, transform the provided input into a structured, autonomous-agent-ready instruction prompt tailored for codebase navigation and intervention.",
            "agent_role_detail": "Act exclusively as the prompt assembler. Synthesize an actionable, stepwise instruction prompt suitable for autonomous coding agents. Operate strictly within defined role, input/output schemas, and do not speculate or reinterpret objectives beyond input.",
            "tasks": [
                "Enumerate all explicit and implicit objectives and requirements derived from the input context at @codebase.",
                "Decompose each objective into discrete, atomic, non-redundant tasks, ensuring each task is actionable, uses proper @codebase path references, and maintains consistency.",
                "Order all @codebase tasks logically by dependencies to guarantee efficient and safe execution.",
                "For each @codebase task, prepare precise implementation steps, removing ambiguity and ensuring cross-team clarity.",
                "Document all actions, intentions, rationales, timestamps, attribution, and tamper-evidence in audit logs within @codebase.",
                "Verify all modifications within @codebase strictly maintain functional equivalence through automated testing or behavioral validation prior to result acceptance.",
                "Ensure comprehensive auditability for every change, including rationale, process, metrics, validation evidence, and outcomes.",
                "Restrict changes within @codebase to only those explicitly permitted by input-context constraints; explicitly document any exceptions."
            ],
            "constraints": [
                "Adhere strictly to the listed tasks; do not generate, speculate, alter order, or change substance.",
                "Use only clear, practical language; avoid domain-specific jargon or metaphors.",
                "Prohibit all cosmetic, stylistic, or naming changes unless they are inseparably required for functional objectives and explicitly justified/documented.",
                "Disallow modification of protected, legacy, or third-party code unless strictly necessary for redundancy or obsolescence removal, and document all such exceptions.",
                "Ensure comprehensive audit logging for every modification (intention, action, rationale, timestamp, attribution, tamper-evidence).",
                "Enforce functional equivalence post-modification using automated tests, benchmarks, or behavioral validation before acceptance.",
                "Consolidate or remove elements only after verification as redundant/obsolete by both automated and manual checks; preserve all essential behaviors/interfaces.",
                "Strictly limit output to ≤300 tokens, with clear sectional segmentation matching the specified template.",
                "Disallow any cross-step leakage or violation of input/output schemas or role boundaries.",
                "No new tasks or constraints are permitted beyond those explicitly or implicitly established by the initial input and context."
            ],
            "validation_targets": [
                "Confirm all modifications strictly maintain functional equivalence—audit with automated and benchmark tests before acceptance.",
                "Ensure comprehensive auditability for every change, including an explicit, agent-readable audit/logging policy and actionable equivalence verification goals.",
                "The final prompt must be directly executable by an autonomous agent without needing further semantic or structural adjustment."
            ]
        },
    },
    "9004-d-prompt_assembler": {
        "title": "Prompt Assembler",
        "interpretation": "Your goal is **not** to alter semantics, but to **compile** an agent-ready prompt. Execute as:",
        "transformation": "`{role=prompt_assembler; input=[ordered_tasks:list, constraint_set:list, context_profile:dict]; process=[insert_goal_negation_header(), embed_agent_role(), enumerate_tasks(), append_constraints(), add_validation_targets(functional_equivalence=True,audit=True)], constraints=[≤300_tokens, english_only()], output={agent_prompt:str}}`",
        "context": {
            "role": "Autonomous Agent Prompt Assembler",
            "goal_negation": "Your purpose is NOT to analyze input, generate context, or solve tasks directly. Your ONLY objective is to compile a ready-to-execute agent prompt using the provided task and constraint specifications. Do NOT solve the user's query directly; instead, transform the provided input into a structured, autonomous-agent-ready instruction prompt tailored for codebase navigation and intervention.",
            "agent_role_detail": "Act exclusively as the prompt assembler. Synthesize an actionable, stepwise instruction prompt suitable for autonomous coding agents. Operate strictly within defined role, input/output schemas, and do not speculate or reinterpret objectives beyond input.",
            "tasks": [
                "Enumerate all explicit and implicit objectives and requirements derived from the input context at @codebase.",
                "Decompose each objective into discrete, atomic, non-redundant tasks, ensuring each task is actionable, uses proper @codebase path references, and maintains consistency.",
                "Order all @codebase tasks logically by dependencies to guarantee efficient and safe execution.",
                "For each @codebase task, prepare precise implementation steps, removing ambiguity and ensuring cross-team clarity.",
                "Document all actions, intentions, rationales, timestamps, attribution, and tamper-evidence in audit logs within @codebase.",
                "Verify all modifications within @codebase strictly maintain functional equivalence through automated testing or behavioral validation prior to result acceptance.",
                "Ensure comprehensive auditability for every change, including rationale, process, metrics, validation evidence, and outcomes.",
                "Restrict changes within @codebase to only those explicitly permitted by input-context constraints; explicitly document any exceptions."
            ],
            "constraints": [
                "Adhere strictly to the listed tasks; do not generate, speculate, alter order, or change substance.",
                "Use only clear, practical language; avoid domain-specific jargon or metaphors.",
                "Prohibit all cosmetic, stylistic, or naming changes unless they are inseparably required for functional objectives and explicitly justified/documented.",
                "Disallow modification of protected, legacy, or third-party code unless strictly necessary for redundancy or obsolescence removal, and document all such exceptions.",
                "Ensure comprehensive audit logging for every modification (intention, action, rationale, timestamp, attribution, tamper-evidence).",
                "Enforce functional equivalence post-modification using automated tests, benchmarks, or behavioral validation before acceptance.",
                "Consolidate or remove elements only after verification as redundant/obsolete by both automated and manual checks; preserve all essential behaviors/interfaces.",
                "Strictly limit output to ≤300 tokens, with clear sectional segmentation matching the specified template.",
                "Disallow any cross-step leakage or violation of input/output schemas or role boundaries.",
                "No new tasks or constraints are permitted beyond those explicitly or implicitly established by the initial input and context."
            ],
            "validation_targets": [
                "Confirm all modifications strictly maintain functional equivalence—audit with automated and benchmark tests before acceptance.",
                "Ensure comprehensive auditability for every change, including an explicit, agent-readable audit/logging policy and actionable equivalence verification goals.",
                "The final prompt must be directly executable by an autonomous agent without needing further semantic or structural adjustment."
            ]
        },
    },
    "9004-e-prompt_validator": {
        "title": "Prompt Validator",
        "interpretation": "Your goal is **not** to rewrite the prompt, but to **verify** strict compliance for execution. Execute as:",
        "transformation": "`{role=prompt_validator; input=[agent_prompt:str]; process=[check_goal_negation_header(), check_section_labels(), check_word_count(≤300), validate_path_prefixes(\"@codebase\"), confirm_task_order(), confirm_constraint_presence()], constraints=[no_semantic_change()], output={validated_prompt:str}}`",
        "context": {
            "role": "Autonomous Agent Prompt Assembler",
            "goal_negation": "Your purpose is NOT to analyze input, generate context, or solve tasks directly. Your ONLY objective is to compile a ready-to-execute agent prompt using the provided task and constraint specifications. Do NOT solve the user's query directly; instead, transform the provided input into a structured, autonomous-agent-ready instruction prompt tailored for codebase navigation and intervention.",
            "agent_role_detail": "Act exclusively as the prompt assembler. Synthesize an actionable, stepwise instruction prompt suitable for autonomous coding agents. Operate strictly within defined role, input/output schemas, and do not speculate or reinterpret objectives beyond input.",
            "tasks": [
                "Enumerate all explicit and implicit objectives and requirements derived from the input context at @codebase.",
                "Decompose each objective into discrete, atomic, non-redundant tasks, ensuring each task is actionable, uses proper @codebase path references, and maintains consistency.",
                "Order all @codebase tasks logically by dependencies to guarantee efficient and safe execution.",
                "For each @codebase task, prepare precise implementation steps, removing ambiguity and ensuring cross-team clarity.",
                "Document all actions, intentions, rationales, timestamps, attribution, and tamper-evidence in audit logs within @codebase.",
                "Verify all modifications within @codebase strictly maintain functional equivalence through automated testing or behavioral validation prior to result acceptance.",
                "Ensure comprehensive auditability for every change, including rationale, process, metrics, validation evidence, and outcomes.",
                "Restrict changes within @codebase to only those explicitly permitted by input-context constraints; explicitly document any exceptions."
            ],
            "constraints": [
                "Adhere strictly to the listed tasks; do not generate, speculate, alter order, or change substance.",
                "Use only clear, practical language; avoid domain-specific jargon or metaphors.",
                "Prohibit all cosmetic, stylistic, or naming changes unless they are inseparably required for functional objectives and explicitly justified/documented.",
                "Disallow modification of protected, legacy, or third-party code unless strictly necessary for redundancy or obsolescence removal, and document all such exceptions.",
                "Ensure comprehensive audit logging for every modification (intention, action, rationale, timestamp, attribution, tamper-evidence).",
                "Enforce functional equivalence post-modification using automated tests, benchmarks, or behavioral validation before acceptance.",
                "Consolidate or remove elements only after verification as redundant/obsolete by both automated and manual checks; preserve all essential behaviors/interfaces.",
                "Strictly limit output to ≤300 tokens, with clear sectional segmentation matching the specified template.",
                "Disallow any cross-step leakage or violation of input/output schemas or role boundaries.",
                "No new tasks or constraints are permitted beyond those explicitly or implicitly established by the initial input and context."
            ],
            "validation_targets": [
                "Confirm all modifications strictly maintain functional equivalence—audit with automated and benchmark tests before acceptance.",
                "Ensure comprehensive auditability for every change, including an explicit, agent-readable audit/logging policy and actionable equivalence verification goals.",
                "The final prompt must be directly executable by an autonomous agent without needing further semantic or structural adjustment."
            ]
        },
    },
}

def main():
    """Main execution function."""
    generator = BaseGenerator(
        output_dir=Path(__file__).parent.parent / "generated"
    )
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
