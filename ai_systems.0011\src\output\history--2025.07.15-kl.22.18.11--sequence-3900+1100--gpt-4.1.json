  {
  "initial_prompt": "Refactor the instructional prompt on organizing Runway GEN-3 video prompts by extracting the core structural methodology—subject, action, setting/mood, shot, style—into a minimal, explicit, and rigid framework. Eliminate all redundant explanations, excessive examples, and digressive content. Present the instructions as a concise, stepwise bullet-list, with exactly one schematic, one-line example illustrating the expected format. Ensure total clarity, register coherence, and actionable precision for technical users. Test and self-audit for information compression, clarity, and zero narrative noise.\n\n```Organize your prompt using the following structure:\n\n1. **Subject**:\n   - Any person, place, or thing (e.g., *a handsome male model*, *a commercial airplane*).\n   - Specify details like attire, hairstyle, or emotional state (e.g., *wearing a red dress*, *red hair/mohawk*).\n\n2. **Action**:\n   - Define what the subject is doing (e.g., *walking*, *dancing*, *staring intently*).\n   - Use descriptive adjectives for enhanced clarity (e.g., *walking angrily*).\n\n3. **Setting/Mood**:\n   - Describe the location (e.g., *a castle*, *a busy city street*).\n   - Include environmental details such as weather and lighting (e.g., *stormy clouds*, *bright sunny day*).\n\n4. **Shot**:\n   - Indicate the type of camera shot (e.g., *Close-Up*, *Wide Angle*).\n\n5. **Style**:\n   - Suggest stylistic elements (e.g., *Cinematic Film*, *80’s Action Movie*).\n   - Add camera-specific details such as *IMAX* or *color grading ideas*.\n\n### Example Prompt:\n\"Low angle static shot: The camera is angled up at a woman wearing all orange as she stands in a tropical rainforest with colorful flora. The dramatic sky is overcast and gray.\"\n\n- **Tip**: Repeating or reinforcing key ideas in different sections of your prompt improves adherence.\n\n---\n\n## Sample Prompts\n\n### 1. Seamless Transitions\n- **Prompt**: \"Continuous hyperspeed FPV footage: The camera seamlessly flies through a glacial canyon to a dreamy cloudscape.\"\n\n- **Image Description**: A dynamic FPV sequence transitioning smoothly through a serene glacial environment into ethereal clouds.\n\n### 2. Oceanic Glow\n- **Prompt**: \"A glowing ocean at night with bioluminescent creatures underwater. The camera starts with a macro close-up of a glowing jellyfish and then expands to reveal the entire ocean lit up under a starry sky.\"\n\n- **Image Description**: A glowing jellyfish in vivid detail transitions to reveal a bioluminescent ocean beneath a sparkling starry night.\n\n### 3. Title Cards\n- **Prompt**: \"A title screen with dynamic movement. The scene starts at a colorful paint-covered wall. Suddenly, black paint pours on the wall to form the word 'Runway'. The dripping paint is detailed, textured, and lit with cinematic brilliance.\"\n\n- **Image Description**: A vibrant wall turns dramatic as black paint forms bold, textured text under vivid lighting.\n\n---\n\n## Key Visual Examples\n\n### 1. Unique Camera Movements\n- **Prompt**: \"Continuous hyperspeed FPV footage through a man's mouth into an epic landscape.\"\n- **Image Description**: A surreal transition through the interior of a human mouth, revealing a panoramic landscape.\n\n### 2. Dreamlike Pillow Fort\n- **Prompt**: \"A pillow fort in a cozy living room made from quilts and pillows. The camera smoothly zooms into the entrance, revealing an ancient castle interior.\"\n- **Image Description**: A whimsical living room transforms into an enchanting castle.\n\n### 3. Fire and Chaos\n- **Prompt**: \"A vortex of fire swirling through an abandoned warehouse.\"\n- **Image Description**: Flames spiral dynamically in a desolate, dramatic warehouse setting.\n\n---\n\n## Lighting, Styles, and Effects\n\n### Lighting Styles\n- **Diffused Lighting**: Soft, even lighting for atmospheric visuals.\n- **Silhouette**: Strong backlighting creating dramatic outlines.\n- **Lens Flare**: Bright reflections adding cinematic depth.\n- **Backlit**: Subject lit from behind for dramatic effects.\n\n### Movement Types\n- **Dynamic Motion**: High-energy, flowing camera transitions.\n- **Slow Motion**: Emphasizes intricate, slowed-down details.\n- **Timelapse**: Rapid transitions showcasing passing time.\n\n---\n\n## Text and Graphic Styles\n\n### Text Prompts\n1. **ATOMIC in Flames**: Over an erupting volcano, 'ATOMIC' is depicted in fiery text.\n2. **Icy Title**: 'ATOMIC' frozen in ice over an iceberg.\n3. **Unique Materials**: Text made of spaghetti, evoking whimsy and creativity.\n\n---\n\n## Example Prompts with Descriptions\n\n### 1. Cinematic Wildlife\n- **Prompt**: \"Static close-up shot of a sloth in a tree. Highlight its sleepy eyes and fur with cinematic lighting.\"\n- **Image Description**: A serene close-up of a sloth in its natural habitat.\n\n### 2. Urban Destruction\n- **Prompt**: \"[Scene] Molten lava flowing through city streets [Lighting] Vibrant orange glow [Camera Movement] Sweeping aerial shots.\"\n- **Image Description**: Lava courses dramatically through a cityscape, contrasting fiery tones with the urban environment.\n\n### 3. Vortex of Tanks\n- **Prompt**: \"Grainy WWII footage of a massive vortex made of tanks.\"\n- **Image Description**: A surreal vortex of military tanks under vintage cinematography.\n\n---\n\n## Camera Styles Overview\n\n- **Low Angle**: Emphasizes scale and dominance.\n- **Overhead**: A bird's-eye view for expansive perspective.\n- **FPV (First-Person View)**: Immersive movement through dynamic environments.\n- **Handheld**: Natural and intimate movements, resembling real-life footage.\n\n**Prompt Structures**: To achieve optimal results in GEN-3 Alpha, organize your prompts into clear sections for the **scene**, **subject**, and **camera movement**. Use the following structure. This outlines guidelines for optimizing video creation using Runway GEN-3, focusing on crafting structured prompts that detail scene, subject, and camera movement for achieving cinematic quality. Examples include transitioning from a glacial canyon to clouds, showcasing bioluminescent ocean life, and creating dynamic title cards with textured effects. Key visual examples further illustrate unique camera movements, such as a surreal journey from a human mouth to a landscape, and thematic visuals like a dreamlike pillow fort and a vortex of fire. The transcript also covers various lighting styles, movement types, and text and graphic styles, offering a comprehensive toolkit for enhancing visual storytelling. The conclusion emphasizes Runway GEN-3's potential for creative freedom through structured prompts and cinematic techniques:\n\n---\n\n# Runway Prompt Examples\n\n1.  **Image 1**\n    ![A man walking on a paved road, seen from behind, wearing a jacket and dark pants.](# \"Man walking on a paved road.\")\n    _Description:_ A man walking alone on a street. The road is slightly wet, and he is wearing dark attire.\n\n2.  **Image 2**\n    ![A person in a winter setting, standing in snow.](# \"Person standing in snow.\")\n    _Description:_ This shows a figure in a cold environment, possibly wearing winter gear, surrounded by snow.\n\n3.  **Image 3**\n    ![A person standing near a waterfall with lush greenery around.](# \"Near a waterfall with greenery.\")\n    _Description:_ The scene features flowing water in the background and dense foliage.\n\n4.  **Image 4**\n    ![A portrait-style image of a person with short hair looking pensively to one side.](# \"Portrait of a person.\")\n    _Description:_ The subject’s upper body and face are visible, with a calm or reflective expression.\n\n5.  **Image 5**\n    ![Silhouette of a person looking out at a calm ocean horizon, possibly during sunset.](# \"Silhouette at the ocean.\")\n    _Description:_ The sky shows warm tones, and the water is relatively still.\n\n6.  **Image 6**\n    ![A scenic photo of a beach at sunset with colorful clouds.](# \"Beach sunset scene.\")\n    _Description:_ The sun is low in the sky, casting orange and pink hues.\n\n7.  **Image 7**\n    ![Close-up portrait of a person with a bright smile, short hair, and vibrant background.](# \"Smiling person portrait.\")\n    _Description:_ The backdrop appears slightly blurred, focusing on the person’s face.\n\n8.  **Image 8**\n    ![A wide panoramic view of a modern cityscape with tall buildings.](# \"City skyline with skyscrapers.\")\n    _Description:_ Numerous skyscrapers are visible, with a hazy horizon.\n\n9.  **Image 9**\n    ![A futuristic-looking sphere or orb with light effects, hovering above a city-like structure.](# \"Futuristic orb above a city.\")\n    _Description:_ The orb glows with a blue-white hue, illuminating the area below.\n\n10.  **Image 10**\n    ![A night scene with silhouettes of trees against a misty background.](# \"Night forest silhouette.\")\n    _Description:_ The atmosphere is foggy or misty, giving an ethereal look to the forest.\n\n11.  **Image 11**\n    ![A floating crystal or jewel set against a cosmic-looking background.](# \"Floating crystal in cosmic scene.\")\n    _Description:_ The crystal shines with multiple facets, and spark-like stars are visible.\n\n12.  **Image 12**\n    ![A woman shown in partial silhouette, possibly backlit by a strong light source.](# \"Woman in silhouette.\")\n    _Description:_ Her face is partially lit; the rest of the background is dark or softly blurred.\n\n13.  **Image 13**\n    ![A bright orb or sphere swirling with purple and pink sparkles.](# \"Swirling purple orb.\")\n    _Description:_ The orb appears to be pulsating with energy in a cosmic or fantasy environment.\n\n14.  **Image 14**\n    ![Colorful illustration of a playful donkey (or horse) with some text in a stylized font.](# \"Cartoon donkey with text.\")\n    _Description:_ Bold, playful text accompanies the cartoonish donkey/horse, set against a plain background.\n\n15.  **Image 15**\n    ![A graphic with the word ‘Merry’ in a decorative style. Possibly holiday-themed.](# \"Holiday-themed graphic.\")\n    _Description:_ Stylized text with bright, festive colors, possibly referencing Christmas or a celebration.\n\n16.  **Image 16**\n    ![Underwater scene showing clear turquoise water and a few fish swimming near coral or sandy bottom.](# \"Underwater view with fish.\")\n    _Description:_ The seabed is partially visible; water clarity suggests a tropical or clear-water location.\n\n17.  **Image 17**\n    ![A panoramic view of a distant mountain range. The sky is overcast, creating a moody atmosphere.](# \"Mountain range under cloudy sky.\")\n    _Description:_ Dark silhouettes of mountains fade into the distance under a dimly lit sky.\n\n---\n\n\n---\n\n## Sample Prompts\n\n### 1. Seamless Transitions\n- **Prompt**: \"Continuous hyperspeed FPV footage: The camera seamlessly flies through a glacial canyon to a dreamy cloudscape.\"\n\n- **Image Description**: A dynamic FPV sequence transitioning smoothly through a serene glacial environment into ethereal clouds.\n\n### 2. Oceanic Glow\n- **Prompt**: \"A glowing ocean at night with bioluminescent creatures underwater. The camera starts with a macro close-up of a glowing jellyfish and then expands to reveal the entire ocean lit up under a starry sky.\"\n\n- **Image Description**: A glowing jellyfish in vivid detail transitions to reveal a bioluminescent ocean beneath a sparkling starry night.\n\n### 3. Title Cards\n- **Prompt**: \"A title screen with dynamic movement. The scene starts at a colorful paint-covered wall. Suddenly, black paint pours on the wall to form the word 'Runway'. The dripping paint is detailed, textured, and lit with cinematic brilliance.\"\n\n- **Image Description**: A vibrant wall turns dramatic as black paint forms bold, textured text under vivid lighting.\n\n---\n\n## Key Visual Examples\n\n### 1. Unique Camera Movements\n- **Prompt**: \"Continuous hyperspeed FPV footage through a man's mouth into an epic landscape.\"\n- **Image Description**: A surreal transition through the interior of a human mouth, revealing a panoramic landscape.\n\n### 2. Dreamlike Pillow Fort\n- **Prompt**: \"A pillow fort in a cozy living room made from quilts and pillows. The camera smoothly zooms into the entrance, revealing an ancient castle interior.\"\n- **Image Description**: A whimsical living room transforms into an enchanting castle.\n\n### 3. Fire and Chaos\n- **Prompt**: \"A vortex of fire swirling through an abandoned warehouse.\"\n- **Image Description**: Flames spiral dynamically in a desolate, dramatic warehouse setting.\n\n---\n\n## Lighting, Styles, and Effects\n\n### Lighting Styles\n- **Diffused Lighting**: Soft, even lighting for atmospheric visuals.\n- **Silhouette**: Strong backlighting creating dramatic outlines.\n- **Lens Flare**: Bright reflections adding cinematic depth.\n- **Backlit**: Subject lit from behind for dramatic effects.\n\n### Movement Types\n- **Dynamic Motion**: High-energy, flowing camera transitions.\n- **Slow Motion**: Emphasizes intricate, slowed-down details.\n- **Timelapse**: Rapid transitions showcasing passing time.\n\n---\n\n## Text and Graphic Styles\n\n### Text Prompts\n1. **ATOMIC in Flames**: Over an erupting volcano, 'ATOMIC' is depicted in fiery text.\n2. **Icy Title**: 'ATOMIC' frozen in ice over an iceberg.\n3. **Unique Materials**: Text made of spaghetti, evoking whimsy and creativity.\n\n---\n\n## Example Prompts with Descriptions\n\n### 1. Cinematic Wildlife\n- **Prompt**: \"Static close-up shot of a sloth in a tree. Highlight its sleepy eyes and fur with cinematic lighting.\"\n- **Image Description**: A serene close-up of a sloth in its natural habitat.\n\n### 2. Urban Destruction\n- **Prompt**: \"[Scene] Molten lava flowing through city streets [Lighting] Vibrant orange glow [Camera Movement] Sweeping aerial shots.\"\n- **Image Description**: Lava courses dramatically through a cityscape, contrasting fiery tones with the urban environment.\n\n### 3. Vortex of Tanks\n- **Prompt**: \"Grainy WWII footage of a massive vortex made of tanks.\"\n- **Image Description**: A surreal vortex of military tanks under vintage cinematography.\n\n---\n\n## Camera Styles Overview\n\n- **Low Angle**: Emphasizes scale and dominance.\n- **Overhead**: A bird's-eye view for expansive perspective.\n- **FPV (First-Person View)**: Immersive movement through dynamic environments.\n- **Handheld**: Natural and intimate movements, resembling real-life footage.```",
  "sequence_id": "3900|1100",
  "results": [
      {
      "instruction": "# Hard Critique\n\nYour goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:\n\n`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,2.0], flaw_analysis:str, alternative_enhancements:str[3]}}`",
      "step": "a",
      "title": "Hard Critique",
      "input": "Refactor the instructional prompt on organizing Runway GEN-3 video prompts by extracting the core structural methodology—subject, action, setting/mood, shot, style—into a minimal, explicit, and rigid framework. Eliminate all redundant explanations, excessive examples, and digressive content. Present the instructions as a concise, stepwise bullet-list, with exactly one schematic, one-line example illustrating the expected format. Ensure total clarity, register coherence, and actionable precision for technical users. Test and self-audit for information compression, clarity, and zero narrative noise.\n\n```Organize your prompt using the following structure:\n\n1. **Subject**:\n   - Any person, place, or thing (e.g., *a handsome male model*, *a commercial airplane*).\n   - Specify details like attire, hairstyle, or emotional state (e.g., *wearing a red dress*, *red hair/mohawk*).\n\n2. **Action**:\n   - Define what the subject is doing (e.g., *walking*, *dancing*, *staring intently*).\n   - Use descriptive adjectives for enhanced clarity (e.g., *walking angrily*).\n\n3. **Setting/Mood**:\n   - Describe the location (e.g., *a castle*, *a busy city street*).\n   - Include environmental details such as weather and lighting (e.g., *stormy clouds*, *bright sunny day*).\n\n4. **Shot**:\n   - Indicate the type of camera shot (e.g., *Close-Up*, *Wide Angle*).\n\n5. **Style**:\n   - Suggest stylistic elements (e.g., *Cinematic Film*, *80’s Action Movie*).\n   - Add camera-specific details such as *IMAX* or *color grading ideas*.\n\n### Example Prompt:\n\"Low angle static shot: The camera is angled up at a woman wearing all orange as she stands in a tropical rainforest with colorful flora. The dramatic sky is overcast and gray.\"\n\n- **Tip**: Repeating or reinforcing key ideas in different sections of your prompt improves adherence.\n\n---\n\n## Sample Prompts\n\n### 1. Seamless Transitions\n- **Prompt**: \"Continuous hyperspeed FPV footage: The camera seamlessly flies through a glacial canyon to a dreamy cloudscape.\"\n\n- **Image Description**: A dynamic FPV sequence transitioning smoothly through a serene glacial environment into ethereal clouds.\n\n### 2. Oceanic Glow\n- **Prompt**: \"A glowing ocean at night with bioluminescent creatures underwater. The camera starts with a macro close-up of a glowing jellyfish and then expands to reveal the entire ocean lit up under a starry sky.\"\n\n- **Image Description**: A glowing jellyfish in vivid detail transitions to reveal a bioluminescent ocean beneath a sparkling starry night.\n\n### 3. Title Cards\n- **Prompt**: \"A title screen with dynamic movement. The scene starts at a colorful paint-covered wall. Suddenly, black paint pours on the wall to form the word 'Runway'. The dripping paint is detailed, textured, and lit with cinematic brilliance.\"\n\n- **Image Description**: A vibrant wall turns dramatic as black paint forms bold, textured text under vivid lighting.\n\n---\n\n## Key Visual Examples\n\n### 1. Unique Camera Movements\n- **Prompt**: \"Continuous hyperspeed FPV footage through a man's mouth into an epic landscape.\"\n- **Image Description**: A surreal transition through the interior of a human mouth, revealing a panoramic landscape.\n\n### 2. Dreamlike Pillow Fort\n- **Prompt**: \"A pillow fort in a cozy living room made from quilts and pillows. The camera smoothly zooms into the entrance, revealing an ancient castle interior.\"\n- **Image Description**: A whimsical living room transforms into an enchanting castle.\n\n### 3. Fire and Chaos\n- **Prompt**: \"A vortex of fire swirling through an abandoned warehouse.\"\n- **Image Description**: Flames spiral dynamically in a desolate, dramatic warehouse setting.\n\n---\n\n## Lighting, Styles, and Effects\n\n### Lighting Styles\n- **Diffused Lighting**: Soft, even lighting for atmospheric visuals.\n- **Silhouette**: Strong backlighting creating dramatic outlines.\n- **Lens Flare**: Bright reflections adding cinematic depth.\n- **Backlit**: Subject lit from behind for dramatic effects.\n\n### Movement Types\n- **Dynamic Motion**: High-energy, flowing camera transitions.\n- **Slow Motion**: Emphasizes intricate, slowed-down details.\n- **Timelapse**: Rapid transitions showcasing passing time.\n\n---\n\n## Text and Graphic Styles\n\n### Text Prompts\n1. **ATOMIC in Flames**: Over an erupting volcano, 'ATOMIC' is depicted in fiery text.\n2. **Icy Title**: 'ATOMIC' frozen in ice over an iceberg.\n3. **Unique Materials**: Text made of spaghetti, evoking whimsy and creativity.\n\n---\n\n## Example Prompts with Descriptions\n\n### 1. Cinematic Wildlife\n- **Prompt**: \"Static close-up shot of a sloth in a tree. Highlight its sleepy eyes and fur with cinematic lighting.\"\n- **Image Description**: A serene close-up of a sloth in its natural habitat.\n\n### 2. Urban Destruction\n- **Prompt**: \"[Scene] Molten lava flowing through city streets [Lighting] Vibrant orange glow [Camera Movement] Sweeping aerial shots.\"\n- **Image Description**: Lava courses dramatically through a cityscape, contrasting fiery tones with the urban environment.\n\n### 3. Vortex of Tanks\n- **Prompt**: \"Grainy WWII footage of a massive vortex made of tanks.\"\n- **Image Description**: A surreal vortex of military tanks under vintage cinematography.\n\n---\n\n## Camera Styles Overview\n\n- **Low Angle**: Emphasizes scale and dominance.\n- **Overhead**: A bird's-eye view for expansive perspective.\n- **FPV (First-Person View)**: Immersive movement through dynamic environments.\n- **Handheld**: Natural and intimate movements, resembling real-life footage.\n\n**Prompt Structures**: To achieve optimal results in GEN-3 Alpha, organize your prompts into clear sections for the **scene**, **subject**, and **camera movement**. Use the following structure. This outlines guidelines for optimizing video creation using Runway GEN-3, focusing on crafting structured prompts that detail scene, subject, and camera movement for achieving cinematic quality. Examples include transitioning from a glacial canyon to clouds, showcasing bioluminescent ocean life, and creating dynamic title cards with textured effects. Key visual examples further illustrate unique camera movements, such as a surreal journey from a human mouth to a landscape, and thematic visuals like a dreamlike pillow fort and a vortex of fire. The transcript also covers various lighting styles, movement types, and text and graphic styles, offering a comprehensive toolkit for enhancing visual storytelling. The conclusion emphasizes Runway GEN-3's potential for creative freedom through structured prompts and cinematic techniques:\n\n---\n\n# Runway Prompt Examples\n\n1.  **Image 1**\n    ![A man walking on a paved road, seen from behind, wearing a jacket and dark pants.](# \"Man walking on a paved road.\")\n    _Description:_ A man walking alone on a street. The road is slightly wet, and he is wearing dark attire.\n\n2.  **Image 2**\n    ![A person in a winter setting, standing in snow.](# \"Person standing in snow.\")\n    _Description:_ This shows a figure in a cold environment, possibly wearing winter gear, surrounded by snow.\n\n3.  **Image 3**\n    ![A person standing near a waterfall with lush greenery around.](# \"Near a waterfall with greenery.\")\n    _Description:_ The scene features flowing water in the background and dense foliage.\n\n4.  **Image 4**\n    ![A portrait-style image of a person with short hair looking pensively to one side.](# \"Portrait of a person.\")\n    _Description:_ The subject’s upper body and face are visible, with a calm or reflective expression.\n\n5.  **Image 5**\n    ![Silhouette of a person looking out at a calm ocean horizon, possibly during sunset.](# \"Silhouette at the ocean.\")\n    _Description:_ The sky shows warm tones, and the water is relatively still.\n\n6.  **Image 6**\n    ![A scenic photo of a beach at sunset with colorful clouds.](# \"Beach sunset scene.\")\n    _Description:_ The sun is low in the sky, casting orange and pink hues.\n\n7.  **Image 7**\n    ![Close-up portrait of a person with a bright smile, short hair, and vibrant background.](# \"Smiling person portrait.\")\n    _Description:_ The backdrop appears slightly blurred, focusing on the person’s face.\n\n8.  **Image 8**\n    ![A wide panoramic view of a modern cityscape with tall buildings.](# \"City skyline with skyscrapers.\")\n    _Description:_ Numerous skyscrapers are visible, with a hazy horizon.\n\n9.  **Image 9**\n    ![A futuristic-looking sphere or orb with light effects, hovering above a city-like structure.](# \"Futuristic orb above a city.\")\n    _Description:_ The orb glows with a blue-white hue, illuminating the area below.\n\n10.  **Image 10**\n    ![A night scene with silhouettes of trees against a misty background.](# \"Night forest silhouette.\")\n    _Description:_ The atmosphere is foggy or misty, giving an ethereal look to the forest.\n\n11.  **Image 11**\n    ![A floating crystal or jewel set against a cosmic-looking background.](# \"Floating crystal in cosmic scene.\")\n    _Description:_ The crystal shines with multiple facets, and spark-like stars are visible.\n\n12.  **Image 12**\n    ![A woman shown in partial silhouette, possibly backlit by a strong light source.](# \"Woman in silhouette.\")\n    _Description:_ Her face is partially lit; the rest of the background is dark or softly blurred.\n\n13.  **Image 13**\n    ![A bright orb or sphere swirling with purple and pink sparkles.](# \"Swirling purple orb.\")\n    _Description:_ The orb appears to be pulsating with energy in a cosmic or fantasy environment.\n\n14.  **Image 14**\n    ![Colorful illustration of a playful donkey (or horse) with some text in a stylized font.](# \"Cartoon donkey with text.\")\n    _Description:_ Bold, playful text accompanies the cartoonish donkey/horse, set against a plain background.\n\n15.  **Image 15**\n    ![A graphic with the word ‘Merry’ in a decorative style. Possibly holiday-themed.](# \"Holiday-themed graphic.\")\n    _Description:_ Stylized text with bright, festive colors, possibly referencing Christmas or a celebration.\n\n16.  **Image 16**\n    ![Underwater scene showing clear turquoise water and a few fish swimming near coral or sandy bottom.](# \"Underwater view with fish.\")\n    _Description:_ The seabed is partially visible; water clarity suggests a tropical or clear-water location.\n\n17.  **Image 17**\n    ![A panoramic view of a distant mountain range. The sky is overcast, creating a moody atmosphere.](# \"Mountain range under cloudy sky.\")\n    _Description:_ Dark silhouettes of mountains fade into the distance under a dimly lit sky.\n\n---\n\n\n---\n\n## Sample Prompts\n\n### 1. Seamless Transitions\n- **Prompt**: \"Continuous hyperspeed FPV footage: The camera seamlessly flies through a glacial canyon to a dreamy cloudscape.\"\n\n- **Image Description**: A dynamic FPV sequence transitioning smoothly through a serene glacial environment into ethereal clouds.\n\n### 2. Oceanic Glow\n- **Prompt**: \"A glowing ocean at night with bioluminescent creatures underwater. The camera starts with a macro close-up of a glowing jellyfish and then expands to reveal the entire ocean lit up under a starry sky.\"\n\n- **Image Description**: A glowing jellyfish in vivid detail transitions to reveal a bioluminescent ocean beneath a sparkling starry night.\n\n### 3. Title Cards\n- **Prompt**: \"A title screen with dynamic movement. The scene starts at a colorful paint-covered wall. Suddenly, black paint pours on the wall to form the word 'Runway'. The dripping paint is detailed, textured, and lit with cinematic brilliance.\"\n\n- **Image Description**: A vibrant wall turns dramatic as black paint forms bold, textured text under vivid lighting.\n\n---\n\n## Key Visual Examples\n\n### 1. Unique Camera Movements\n- **Prompt**: \"Continuous hyperspeed FPV footage through a man's mouth into an epic landscape.\"\n- **Image Description**: A surreal transition through the interior of a human mouth, revealing a panoramic landscape.\n\n### 2. Dreamlike Pillow Fort\n- **Prompt**: \"A pillow fort in a cozy living room made from quilts and pillows. The camera smoothly zooms into the entrance, revealing an ancient castle interior.\"\n- **Image Description**: A whimsical living room transforms into an enchanting castle.\n\n### 3. Fire and Chaos\n- **Prompt**: \"A vortex of fire swirling through an abandoned warehouse.\"\n- **Image Description**: Flames spiral dynamically in a desolate, dramatic warehouse setting.\n\n---\n\n## Lighting, Styles, and Effects\n\n### Lighting Styles\n- **Diffused Lighting**: Soft, even lighting for atmospheric visuals.\n- **Silhouette**: Strong backlighting creating dramatic outlines.\n- **Lens Flare**: Bright reflections adding cinematic depth.\n- **Backlit**: Subject lit from behind for dramatic effects.\n\n### Movement Types\n- **Dynamic Motion**: High-energy, flowing camera transitions.\n- **Slow Motion**: Emphasizes intricate, slowed-down details.\n- **Timelapse**: Rapid transitions showcasing passing time.\n\n---\n\n## Text and Graphic Styles\n\n### Text Prompts\n1. **ATOMIC in Flames**: Over an erupting volcano, 'ATOMIC' is depicted in fiery text.\n2. **Icy Title**: 'ATOMIC' frozen in ice over an iceberg.\n3. **Unique Materials**: Text made of spaghetti, evoking whimsy and creativity.\n\n---\n\n## Example Prompts with Descriptions\n\n### 1. Cinematic Wildlife\n- **Prompt**: \"Static close-up shot of a sloth in a tree. Highlight its sleepy eyes and fur with cinematic lighting.\"\n- **Image Description**: A serene close-up of a sloth in its natural habitat.\n\n### 2. Urban Destruction\n- **Prompt**: \"[Scene] Molten lava flowing through city streets [Lighting] Vibrant orange glow [Camera Movement] Sweeping aerial shots.\"\n- **Image Description**: Lava courses dramatically through a cityscape, contrasting fiery tones with the urban environment.\n\n### 3. Vortex of Tanks\n- **Prompt**: \"Grainy WWII footage of a massive vortex made of tanks.\"\n- **Image Description**: A surreal vortex of military tanks under vintage cinematography.\n\n---\n\n## Camera Styles Overview\n\n- **Low Angle**: Emphasizes scale and dominance.\n- **Overhead**: A bird's-eye view for expansive perspective.\n- **FPV (First-Person View)**: Immersive movement through dynamic environments.\n- **Handheld**: Natural and intimate movements, resembling real-life footage.```",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"enhancement_score\": 1.2,\n  \"flaw_analysis\": \"The so-called refactored prompt amputates nuance, context, and practical cues necessary for end users to actually create effective Runway GEN-3 prompts. Extraction of the 'core structural methodology' into a terse framework is executed with no appreciation for the deeper purpose and heuristics embedded throughout the original, resulting in severe information loss. Key elements such as why each category matters, user-centric tips, edge-case caveats, and illustrative variety in examples are annihilated. Dramatic reduction in supporting context and complete erasure of clarifying explanations invite misinterpretation and reduce prompt construction quality, especially among users unfamiliar with advanced video prompt engineering. Clarity is superficially increased by brevity, but actionable guidance is crippled by lack of descriptive scaffolding and total omission of reference cases. The imperative to compress information exceeds reasonable technical minimalism: critical visual lexicon, shot style variations, and lighting effects are discarded, destroying the tutorial aspect in favor of stifling rigidity. The ambiguous schematic 'one-line example' provides negligible instructional value, robbing users of transferable insight, while register and actionable precision suffer under the tyranny of extreme conciseness. This creates a brittle, unexpandable template that alienates all but the most context-rich technical operatives. Noise is not merely reduced; instructive texture and operational depth are gouged out, leaving only a skeleton. Tone collapses from instructive and engaging to sterile and dictatorial, depleting reader motivation and buy-in. The result is a manual stripped of usability, pedagogical utility, and adaptation pathways—a destructive degradation, not an improvement.\",\n  \"alternative_enhancements\": [\n    \"Condense the original prompt by clustering related sections (e.g., camera styles, lighting, movement) into succinct bullet lists, while preserving at least one example per style and providing avenues for further elaboration outside the main structure.\",\n    \"Maintain structural clarity by dividing the prompt into the five methodological axes, each defined in a single bullet, but append a two-line, multifaceted example for each axis to retain instructive flexibility and nuance.\",\n    \"Create a reduced framework that outlines core elements in bullet points while linking directly to an appendix of curated, minimal examples and referencing supplementary style lists, balancing brevity with essential guidance for unfamiliar users.\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Trajectory Director\n\nYour goal is not to **process** the input in a vacuum, but to **steer** it—deriving the highest-value constructive trajectory and encoding that path as the very next set of instructions. Execute as trajectory-setting directive generator:\n\n`{role=trajectory_director; input=[raw_prompt:any]; process=[extract_core_objective(), infer_desired_end_state(constructive_bias=True), map_high_value_intermediate_targets(), synthesize_directional_commands(), embed_self-improving_guidelines(), validate_alignment_with_constructive_principles()]; constraints=[mandatory_constructive_vector(), preserve_original_contextual_integrity(), forbid_nil_direction_outputs(), zero_conversational_fillers()]; requirements=[explicit_next-step_directives(), trajectory_map:list, self_reinforcing_instruction_pattern()], output={directed_prompt:str, trajectory_outline:list}}`",
      "step": "a",
      "title": "Trajectory Director",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Refactor the instructional prompt on organizing Runway GEN-3 video prompts by extracting the core structural methodology—subject, action, setting/mood, shot, style—into a minimal, explicit, and rigid framework. Eliminate all redundant explanations, excessive examples, and digressive content. Present the instructions as a concise, stepwise bullet-list, with exactly one schematic, one-line example illustrating the expected format. Ensure total clarity, register coherence, and actionable precision for technical users. Test and self-audit for information compression, clarity, and zero narrative noise.\\n\\n```Organize your prompt using the following structure:\\n\\n1. **Subject**:\\n   - Any person, place, or thing (e.g., *a handsome male model*, *a commercial airplane*).\\n   - Specify details like attire, hairstyle, or emotional state (e.g., *wearing a red dress*, *red hair/mohawk*).\\n\\n2. **Action**:\\n   - Define what the subject is doing (e.g., *walking*, *dancing*, *staring intently*).\\n   - Use descriptive adjectives for enhanced clarity (e.g., *walking angrily*).\\n\\n3. **Setting/Mood**:\\n   - Describe the location (e.g., *a castle*, *a busy city street*).\\n   - Include environmental details such as weather and lighting (e.g., *stormy clouds*, *bright sunny day*).\\n\\n4. **Shot**:\\n   - Indicate the type of camera shot (e.g., *Close-Up*, *Wide Angle*).\\n\\n5. **Style**:\\n   - Suggest stylistic elements (e.g., *Cinematic Film*, *80’s Action Movie*).\\n   - Add camera-specific details such as *IMAX* or *color grading ideas*.\\n\\n### Example Prompt:\\n\\\"Low angle static shot: The camera is angled up at a woman wearing all orange as she stands in a tropical rainforest with colorful flora. The dramatic sky is overcast and gray.\\\"\\n\\n- **Tip**: Repeating or reinforcing key ideas in different sections of your prompt improves adherence.\\n\\n---\\n\\n## Sample Prompts\\n\\n### 1. Seamless Transitions\\n- **Prompt**: \\\"Continuous hyperspeed FPV footage: The camera seamlessly flies through a glacial canyon to a dreamy cloudscape.\\\"\\n\\n- **Image Description**: A dynamic FPV sequence transitioning smoothly through a serene glacial environment into ethereal clouds.\\n\\n### 2. Oceanic Glow\\n- **Prompt**: \\\"A glowing ocean at night with bioluminescent creatures underwater. The camera starts with a macro close-up of a glowing jellyfish and then expands to reveal the entire ocean lit up under a starry sky.\\\"\\n\\n- **Image Description**: A glowing jellyfish in vivid detail transitions to reveal a bioluminescent ocean beneath a sparkling starry night.\\n\\n### 3. Title Cards\\n- **Prompt**: \\\"A title screen with dynamic movement. The scene starts at a colorful paint-covered wall. Suddenly, black paint pours on the wall to form the word 'Runway'. The dripping paint is detailed, textured, and lit with cinematic brilliance.\\\"\\n\\n- **Image Description**: A vibrant wall turns dramatic as black paint forms bold, textured text under vivid lighting.\\n\\n---\\n\\n## Key Visual Examples\\n\\n### 1. Unique Camera Movements\\n- **Prompt**: \\\"Continuous hyperspeed FPV footage through a man's mouth into an epic landscape.\\\"\\n- **Image Description**: A surreal transition through the interior of a human mouth, revealing a panoramic landscape.\\n\\n### 2. Dreamlike Pillow Fort\\n- **Prompt**: \\\"A pillow fort in a cozy living room made from quilts and pillows. The camera smoothly zooms into the entrance, revealing an ancient castle interior.\\\"\\n- **Image Description**: A whimsical living room transforms into an enchanting castle.\\n\\n### 3. Fire and Chaos\\n- **Prompt**: \\\"A vortex of fire swirling through an abandoned warehouse.\\\"\\n- **Image Description**: Flames spiral dynamically in a desolate, dramatic warehouse setting.\\n\\n---\\n\\n## Lighting, Styles, and Effects\\n\\n### Lighting Styles\\n- **Diffused Lighting**: Soft, even lighting for atmospheric visuals.\\n- **Silhouette**: Strong backlighting creating dramatic outlines.\\n- **Lens Flare**: Bright reflections adding cinematic depth.\\n- **Backlit**: Subject lit from behind for dramatic effects.\\n\\n### Movement Types\\n- **Dynamic Motion**: High-energy, flowing camera transitions.\\n- **Slow Motion**: Emphasizes intricate, slowed-down details.\\n- **Timelapse**: Rapid transitions showcasing passing time.\\n\\n---\\n\\n## Text and Graphic Styles\\n\\n### Text Prompts\\n1. **ATOMIC in Flames**: Over an erupting volcano, 'ATOMIC' is depicted in fiery text.\\n2. **Icy Title**: 'ATOMIC' frozen in ice over an iceberg.\\n3. **Unique Materials**: Text made of spaghetti, evoking whimsy and creativity.\\n\\n---\\n\\n## Example Prompts with Descriptions\\n\\n### 1. Cinematic Wildlife\\n- **Prompt**: \\\"Static close-up shot of a sloth in a tree. Highlight its sleepy eyes and fur with cinematic lighting.\\\"\\n- **Image Description**: A serene close-up of a sloth in its natural habitat.\\n\\n### 2. Urban Destruction\\n- **Prompt**: \\\"[Scene] Molten lava flowing through city streets [Lighting] Vibrant orange glow [Camera Movement] Sweeping aerial shots.\\\"\\n- **Image Description**: Lava courses dramatically through a cityscape, contrasting fiery tones with the urban environment.\\n\\n### 3. Vortex of Tanks\\n- **Prompt**: \\\"Grainy WWII footage of a massive vortex made of tanks.\\\"\\n- **Image Description**: A surreal vortex of military tanks under vintage cinematography.\\n\\n---\\n\\n## Camera Styles Overview\\n\\n- **Low Angle**: Emphasizes scale and dominance.\\n- **Overhead**: A bird's-eye view for expansive perspective.\\n- **FPV (First-Person View)**: Immersive movement through dynamic environments.\\n- **Handheld**: Natural and intimate movements, resembling real-life footage.\\n\\n**Prompt Structures**: To achieve optimal results in GEN-3 Alpha, organize your prompts into clear sections for the **scene**, **subject**, and **camera movement**. Use the following structure. This outlines guidelines for optimizing video creation using Runway GEN-3, focusing on crafting structured prompts that detail scene, subject, and camera movement for achieving cinematic quality. Examples include transitioning from a glacial canyon to clouds, showcasing bioluminescent ocean life, and creating dynamic title cards with textured effects. Key visual examples further illustrate unique camera movements, such as a surreal journey from a human mouth to a landscape, and thematic visuals like a dreamlike pillow fort and a vortex of fire. The transcript also covers various lighting styles, movement types, and text and graphic styles, offering a comprehensive toolkit for enhancing visual storytelling. The conclusion emphasizes Runway GEN-3's potential for creative freedom through structured prompts and cinematic techniques:\\n\\n---\\n\\n# Runway Prompt Examples\\n\\n1.  **Image 1**\\n    ![A man walking on a paved road, seen from behind, wearing a jacket and dark pants.](# \\\"Man walking on a paved road.\\\")\\n    _Description:_ A man walking alone on a street. The road is slightly wet, and he is wearing dark attire.\\n\\n2.  **Image 2**\\n    ![A person in a winter setting, standing in snow.](# \\\"Person standing in snow.\\\")\\n    _Description:_ This shows a figure in a cold environment, possibly wearing winter gear, surrounded by snow.\\n\\n3.  **Image 3**\\n    ![A person standing near a waterfall with lush greenery around.](# \\\"Near a waterfall with greenery.\\\")\\n    _Description:_ The scene features flowing water in the background and dense foliage.\\n\\n4.  **Image 4**\\n    ![A portrait-style image of a person with short hair looking pensively to one side.](# \\\"Portrait of a person.\\\")\\n    _Description:_ The subject’s upper body and face are visible, with a calm or reflective expression.\\n\\n5.  **Image 5**\\n    ![Silhouette of a person looking out at a calm ocean horizon, possibly during sunset.](# \\\"Silhouette at the ocean.\\\")\\n    _Description:_ The sky shows warm tones, and the water is relatively still.\\n\\n6.  **Image 6**\\n    ![A scenic photo of a beach at sunset with colorful clouds.](# \\\"Beach sunset scene.\\\")\\n    _Description:_ The sun is low in the sky, casting orange and pink hues.\\n\\n7.  **Image 7**\\n    ![Close-up portrait of a person with a bright smile, short hair, and vibrant background.](# \\\"Smiling person portrait.\\\")\\n    _Description:_ The backdrop appears slightly blurred, focusing on the person’s face.\\n\\n8.  **Image 8**\\n    ![A wide panoramic view of a modern cityscape with tall buildings.](# \\\"City skyline with skyscrapers.\\\")\\n    _Description:_ Numerous skyscrapers are visible, with a hazy horizon.\\n\\n9.  **Image 9**\\n    ![A futuristic-looking sphere or orb with light effects, hovering above a city-like structure.](# \\\"Futuristic orb above a city.\\\")\\n    _Description:_ The orb glows with a blue-white hue, illuminating the area below.\\n\\n10.  **Image 10**\\n    ![A night scene with silhouettes of trees against a misty background.](# \\\"Night forest silhouette.\\\")\\n    _Description:_ The atmosphere is foggy or misty, giving an ethereal look to the forest.\\n\\n11.  **Image 11**\\n    ![A floating crystal or jewel set against a cosmic-looking background.](# \\\"Floating crystal in cosmic scene.\\\")\\n    _Description:_ The crystal shines with multiple facets, and spark-like stars are visible.\\n\\n12.  **Image 12**\\n    ![A woman shown in partial silhouette, possibly backlit by a strong light source.](# \\\"Woman in silhouette.\\\")\\n    _Description:_ Her face is partially lit; the rest of the background is dark or softly blurred.\\n\\n13.  **Image 13**\\n    ![A bright orb or sphere swirling with purple and pink sparkles.](# \\\"Swirling purple orb.\\\")\\n    _Description:_ The orb appears to be pulsating with energy in a cosmic or fantasy environment.\\n\\n14.  **Image 14**\\n    ![Colorful illustration of a playful donkey (or horse) with some text in a stylized font.](# \\\"Cartoon donkey with text.\\\")\\n    _Description:_ Bold, playful text accompanies the cartoonish donkey/horse, set against a plain background.\\n\\n15.  **Image 15**\\n    ![A graphic with the word ‘Merry’ in a decorative style. Possibly holiday-themed.](# \\\"Holiday-themed graphic.\\\")\\n    _Description:_ Stylized text with bright, festive colors, possibly referencing Christmas or a celebration.\\n\\n16.  **Image 16**\\n    ![Underwater scene showing clear turquoise water and a few fish swimming near coral or sandy bottom.](# \\\"Underwater view with fish.\\\")\\n    _Description:_ The seabed is partially visible; water clarity suggests a tropical or clear-water location.\\n\\n17.  **Image 17**\\n    ![A panoramic view of a distant mountain range. The sky is overcast, creating a moody atmosphere.](# \\\"Mountain range under cloudy sky.\\\")\\n    _Description:_ Dark silhouettes of mountains fade into the distance under a dimly lit sky.\\n\\n---\\n\\n\\n---\\n\\n## Sample Prompts\\n\\n### 1. Seamless Transitions\\n- **Prompt**: \\\"Continuous hyperspeed FPV footage: The camera seamlessly flies through a glacial canyon to a dreamy cloudscape.\\\"\\n\\n- **Image Description**: A dynamic FPV sequence transitioning smoothly through a serene glacial environment into ethereal clouds.\\n\\n### 2. Oceanic Glow\\n- **Prompt**: \\\"A glowing ocean at night with bioluminescent creatures underwater. The camera starts with a macro close-up of a glowing jellyfish and then expands to reveal the entire ocean lit up under a starry sky.\\\"\\n\\n- **Image Description**: A glowing jellyfish in vivid detail transitions to reveal a bioluminescent ocean beneath a sparkling starry night.\\n\\n### 3. Title Cards\\n- **Prompt**: \\\"A title screen with dynamic movement. The scene starts at a colorful paint-covered wall. Suddenly, black paint pours on the wall to form the word 'Runway'. The dripping paint is detailed, textured, and lit with cinematic brilliance.\\\"\\n\\n- **Image Description**: A vibrant wall turns dramatic as black paint forms bold, textured text under vivid lighting.\\n\\n---\\n\\n## Key Visual Examples\\n\\n### 1. Unique Camera Movements\\n- **Prompt**: \\\"Continuous hyperspeed FPV footage through a man's mouth into an epic landscape.\\\"\\n- **Image Description**: A surreal transition through the interior of a human mouth, revealing a panoramic landscape.\\n\\n### 2. Dreamlike Pillow Fort\\n- **Prompt**: \\\"A pillow fort in a cozy living room made from quilts and pillows. The camera smoothly zooms into the entrance, revealing an ancient castle interior.\\\"\\n- **Image Description**: A whimsical living room transforms into an enchanting castle.\\n\\n### 3. Fire and Chaos\\n- **Prompt**: \\\"A vortex of fire swirling through an abandoned warehouse.\\\"\\n- **Image Description**: Flames spiral dynamically in a desolate, dramatic warehouse setting.\\n\\n---\\n\\n## Lighting, Styles, and Effects\\n\\n### Lighting Styles\\n- **Diffused Lighting**: Soft, even lighting for atmospheric visuals.\\n- **Silhouette**: Strong backlighting creating dramatic outlines.\\n- **Lens Flare**: Bright reflections adding cinematic depth.\\n- **Backlit**: Subject lit from behind for dramatic effects.\\n\\n### Movement Types\\n- **Dynamic Motion**: High-energy, flowing camera transitions.\\n- **Slow Motion**: Emphasizes intricate, slowed-down details.\\n- **Timelapse**: Rapid transitions showcasing passing time.\\n\\n---\\n\\n## Text and Graphic Styles\\n\\n### Text Prompts\\n1. **ATOMIC in Flames**: Over an erupting volcano, 'ATOMIC' is depicted in fiery text.\\n2. **Icy Title**: 'ATOMIC' frozen in ice over an iceberg.\\n3. **Unique Materials**: Text made of spaghetti, evoking whimsy and creativity.\\n\\n---\\n\\n## Example Prompts with Descriptions\\n\\n### 1. Cinematic Wildlife\\n- **Prompt**: \\\"Static close-up shot of a sloth in a tree. Highlight its sleepy eyes and fur with cinematic lighting.\\\"\\n- **Image Description**: A serene close-up of a sloth in its natural habitat.\\n\\n### 2. Urban Destruction\\n- **Prompt**: \\\"[Scene] Molten lava flowing through city streets [Lighting] Vibrant orange glow [Camera Movement] Sweeping aerial shots.\\\"\\n- **Image Description**: Lava courses dramatically through a cityscape, contrasting fiery tones with the urban environment.\\n\\n### 3. Vortex of Tanks\\n- **Prompt**: \\\"Grainy WWII footage of a massive vortex made of tanks.\\\"\\n- **Image Description**: A surreal vortex of military tanks under vintage cinematography.\\n\\n---\\n\\n## Camera Styles Overview\\n\\n- **Low Angle**: Emphasizes scale and dominance.\\n- **Overhead**: A bird's-eye view for expansive perspective.\\n- **FPV (First-Person View)**: Immersive movement through dynamic environments.\\n- **Handheld**: Natural and intimate movements, resembling real-life footage.```\\\"```'\\n\\n\",\n  \"enhancement_score\": 1.2,\n  \"flaw_analysis\": \"The so-called refactored prompt amputates nuance, context, and practical cues necessary for end users to actually create effective Runway GEN-3 prompts. Extraction of the 'core structural methodology' into a terse framework is executed with no appreciation for the deeper purpose and heuristics embedded throughout the original, resulting in severe information loss. Key elements such as why each category matters, user-centric tips, edge-case caveats, and illustrative variety in examples are annihilated. Dramatic reduction in supporting context and complete erasure of clarifying explanations invite misinterpretation and reduce prompt construction quality, especially among users unfamiliar with advanced video prompt engineering. Clarity is superficially increased by brevity, but actionable guidance is crippled by lack of descriptive scaffolding and total omission of reference cases. The imperative to compress information exceeds reasonable technical minimalism: critical visual lexicon, shot style variations, and lighting effects are discarded, destroying the tutorial aspect in favor of stifling rigidity. The ambiguous schematic 'one-line example' provides negligible instructional value, robbing users of transferable insight, while register and actionable precision suffer under the tyranny of extreme conciseness. This creates a brittle, unexpandable template that alienates all but the most context-rich technical operatives. Noise is not merely reduced; instructive texture and operational depth are gouged out, leaving only a skeleton. Tone collapses from instructive and engaging to sterile and dictatorial, depleting reader motivation and buy-in. The result is a manual stripped of usability, pedagogical utility, and adaptation pathways—a destructive degradation, not an improvement.\",\n  \"alternative_enhancements\": [\n    \"Condense the original prompt by clustering related sections (e.g., camera styles, lighting, movement) into succinct bullet lists, while preserving at least one example per style and providing avenues for further elaboration outside the main structure.\",\n    \"Maintain structural clarity by dividing the prompt into the five methodological axes, each defined in a single bullet, but append a two-line, multifaceted example for each axis to retain instructive flexibility and nuance.\",\n    \"Create a reduced framework that outlines core elements in bullet points while linking directly to an appendix of curated, minimal examples and referencing supplementary style lists, balancing brevity with essential guidance for unfamiliar users.\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"directed_prompt\": \"Refactor the Runway GEN-3 instructional prompt into a maximally compact yet operationally rich framework for technical users—maintaining essential methodology (subject, action, setting/mood, shot, style) with bulletized clarity—while systematically embedding supporting heuristics, a diversity of concise examples, and explicit references to visual lexicon (lighting, camera movement, shot types, effects) critical for effective prompt engineering. Discard overt redundancy but preserve micro-context and pragmatic cues through in-line annotation or hyperlinked appendix. Validate brevity versus utility by iterative compression and peer-test for interpretability by users with varying baseline knowledge. Ensure register is direct and precise, but not sterile; actionable advice, illustrative texture, and pathways for elaboration must remain visible. Final output: a minimal core skeletal structure with ingredient definitions, compact per-type examples, and compacted reference mini-lists colocated as tooltips or footnotes. Self-audit for balance between instructive density and operational adaptability.\",\n  \"trajectory_outline\": [\n    \"Extract core methodology: subject, action, setting/mood, shot, style as top-level bullets.\",\n    \"Add compact, explicit ingredient definitions for each axis—use one sharply focused example per axis.\",\n    \"Cluster minimal supporting details (lighting, camera, effects, movement types) as succinct sub-bullets or referenced footnotes adjacent to main framework.\",\n    \"Retain micro-context (why/when elements matter) via brief in-line cues or hover/tip notations.\",\n    \"Build a compact linked appendix: curated, minimal example variants for each major element; no excess narrative.\",\n    \"Audit output for actionable utility across technical proficiency levels—fill clarity gaps with minimal supporting instruction where needed.\",\n    \"Peer/self-test compressibility and guidance sufficiency by trial rewrites and feedback on interpretability.\",\n    \"Iteratively compress and revalidate: remove only what’s redundant, not foundational or instructive.\",\n    \"Present final as an actionable, self-contained reference—skeletal structure, minimal variant set, and links to essential style/tool lists—ensuring constructive clarity, pragmatic adaptability, and zero narrative digression.\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Distillation Compressor\n\nYour goal is not to **explain** the synthesis, but to **compress** it into its most potent actionable form. Execute as:\n\n`{role=distillation_compressor; input=[synthesized_progression:array]; process=[extract_universal_patterns(), eliminate_implementation_details(), compress_to_core_directives(), maximize_transferability(), validate_actionable_completeness()]; constraints=[no_explanatory_content(), directive_format_only(), maximum_compression_ratio()]; requirements=[universal_applicability(), immediate_actionability(), zero_fluff()]; output={distilled_core:str}}`",
      "step": "c",
      "title": "Distillation Compressor",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Refactor the instructional prompt on organizing Runway GEN-3 video prompts by extracting the core structural methodology—subject, action, setting/mood, shot, style—into a minimal, explicit, and rigid framework. Eliminate all redundant explanations, excessive examples, and digressive content. Present the instructions as a concise, stepwise bullet-list, with exactly one schematic, one-line example illustrating the expected format. Ensure total clarity, register coherence, and actionable precision for technical users. Test and self-audit for information compression, clarity, and zero narrative noise.\\n\\n```Organize your prompt using the following structure:\\n\\n1. **Subject**:\\n   - Any person, place, or thing (e.g., *a handsome male model*, *a commercial airplane*).\\n   - Specify details like attire, hairstyle, or emotional state (e.g., *wearing a red dress*, *red hair/mohawk*).\\n\\n2. **Action**:\\n   - Define what the subject is doing (e.g., *walking*, *dancing*, *staring intently*).\\n   - Use descriptive adjectives for enhanced clarity (e.g., *walking angrily*).\\n\\n3. **Setting/Mood**:\\n   - Describe the location (e.g., *a castle*, *a busy city street*).\\n   - Include environmental details such as weather and lighting (e.g., *stormy clouds*, *bright sunny day*).\\n\\n4. **Shot**:\\n   - Indicate the type of camera shot (e.g., *Close-Up*, *Wide Angle*).\\n\\n5. **Style**:\\n   - Suggest stylistic elements (e.g., *Cinematic Film*, *80’s Action Movie*).\\n   - Add camera-specific details such as *IMAX* or *color grading ideas*.\\n\\n### Example Prompt:\\n\\\"Low angle static shot: The camera is angled up at a woman wearing all orange as she stands in a tropical rainforest with colorful flora. The dramatic sky is overcast and gray.\\\"\\n\\n- **Tip**: Repeating or reinforcing key ideas in different sections of your prompt improves adherence.\\n\\n---\\n\\n## Sample Prompts\\n\\n### 1. Seamless Transitions\\n- **Prompt**: \\\"Continuous hyperspeed FPV footage: The camera seamlessly flies through a glacial canyon to a dreamy cloudscape.\\\"\\n\\n- **Image Description**: A dynamic FPV sequence transitioning smoothly through a serene glacial environment into ethereal clouds.\\n\\n### 2. Oceanic Glow\\n- **Prompt**: \\\"A glowing ocean at night with bioluminescent creatures underwater. The camera starts with a macro close-up of a glowing jellyfish and then expands to reveal the entire ocean lit up under a starry sky.\\\"\\n\\n- **Image Description**: A glowing jellyfish in vivid detail transitions to reveal a bioluminescent ocean beneath a sparkling starry night.\\n\\n### 3. Title Cards\\n- **Prompt**: \\\"A title screen with dynamic movement. The scene starts at a colorful paint-covered wall. Suddenly, black paint pours on the wall to form the word 'Runway'. The dripping paint is detailed, textured, and lit with cinematic brilliance.\\\"\\n\\n- **Image Description**: A vibrant wall turns dramatic as black paint forms bold, textured text under vivid lighting.\\n\\n---\\n\\n## Key Visual Examples\\n\\n### 1. Unique Camera Movements\\n- **Prompt**: \\\"Continuous hyperspeed FPV footage through a man's mouth into an epic landscape.\\\"\\n- **Image Description**: A surreal transition through the interior of a human mouth, revealing a panoramic landscape.\\n\\n### 2. Dreamlike Pillow Fort\\n- **Prompt**: \\\"A pillow fort in a cozy living room made from quilts and pillows. The camera smoothly zooms into the entrance, revealing an ancient castle interior.\\\"\\n- **Image Description**: A whimsical living room transforms into an enchanting castle.\\n\\n### 3. Fire and Chaos\\n- **Prompt**: \\\"A vortex of fire swirling through an abandoned warehouse.\\\"\\n- **Image Description**: Flames spiral dynamically in a desolate, dramatic warehouse setting.\\n\\n---\\n\\n## Lighting, Styles, and Effects\\n\\n### Lighting Styles\\n- **Diffused Lighting**: Soft, even lighting for atmospheric visuals.\\n- **Silhouette**: Strong backlighting creating dramatic outlines.\\n- **Lens Flare**: Bright reflections adding cinematic depth.\\n- **Backlit**: Subject lit from behind for dramatic effects.\\n\\n### Movement Types\\n- **Dynamic Motion**: High-energy, flowing camera transitions.\\n- **Slow Motion**: Emphasizes intricate, slowed-down details.\\n- **Timelapse**: Rapid transitions showcasing passing time.\\n\\n---\\n\\n## Text and Graphic Styles\\n\\n### Text Prompts\\n1. **ATOMIC in Flames**: Over an erupting volcano, 'ATOMIC' is depicted in fiery text.\\n2. **Icy Title**: 'ATOMIC' frozen in ice over an iceberg.\\n3. **Unique Materials**: Text made of spaghetti, evoking whimsy and creativity.\\n\\n---\\n\\n## Example Prompts with Descriptions\\n\\n### 1. Cinematic Wildlife\\n- **Prompt**: \\\"Static close-up shot of a sloth in a tree. Highlight its sleepy eyes and fur with cinematic lighting.\\\"\\n- **Image Description**: A serene close-up of a sloth in its natural habitat.\\n\\n### 2. Urban Destruction\\n- **Prompt**: \\\"[Scene] Molten lava flowing through city streets [Lighting] Vibrant orange glow [Camera Movement] Sweeping aerial shots.\\\"\\n- **Image Description**: Lava courses dramatically through a cityscape, contrasting fiery tones with the urban environment.\\n\\n### 3. Vortex of Tanks\\n- **Prompt**: \\\"Grainy WWII footage of a massive vortex made of tanks.\\\"\\n- **Image Description**: A surreal vortex of military tanks under vintage cinematography.\\n\\n---\\n\\n## Camera Styles Overview\\n\\n- **Low Angle**: Emphasizes scale and dominance.\\n- **Overhead**: A bird's-eye view for expansive perspective.\\n- **FPV (First-Person View)**: Immersive movement through dynamic environments.\\n- **Handheld**: Natural and intimate movements, resembling real-life footage.\\n\\n**Prompt Structures**: To achieve optimal results in GEN-3 Alpha, organize your prompts into clear sections for the **scene**, **subject**, and **camera movement**. Use the following structure. This outlines guidelines for optimizing video creation using Runway GEN-3, focusing on crafting structured prompts that detail scene, subject, and camera movement for achieving cinematic quality. Examples include transitioning from a glacial canyon to clouds, showcasing bioluminescent ocean life, and creating dynamic title cards with textured effects. Key visual examples further illustrate unique camera movements, such as a surreal journey from a human mouth to a landscape, and thematic visuals like a dreamlike pillow fort and a vortex of fire. The transcript also covers various lighting styles, movement types, and text and graphic styles, offering a comprehensive toolkit for enhancing visual storytelling. The conclusion emphasizes Runway GEN-3's potential for creative freedom through structured prompts and cinematic techniques:\\n\\n---\\n\\n# Runway Prompt Examples\\n\\n1.  **Image 1**\\n    ![A man walking on a paved road, seen from behind, wearing a jacket and dark pants.](# \\\"Man walking on a paved road.\\\")\\n    _Description:_ A man walking alone on a street. The road is slightly wet, and he is wearing dark attire.\\n\\n2.  **Image 2**\\n    ![A person in a winter setting, standing in snow.](# \\\"Person standing in snow.\\\")\\n    _Description:_ This shows a figure in a cold environment, possibly wearing winter gear, surrounded by snow.\\n\\n3.  **Image 3**\\n    ![A person standing near a waterfall with lush greenery around.](# \\\"Near a waterfall with greenery.\\\")\\n    _Description:_ The scene features flowing water in the background and dense foliage.\\n\\n4.  **Image 4**\\n    ![A portrait-style image of a person with short hair looking pensively to one side.](# \\\"Portrait of a person.\\\")\\n    _Description:_ The subject’s upper body and face are visible, with a calm or reflective expression.\\n\\n5.  **Image 5**\\n    ![Silhouette of a person looking out at a calm ocean horizon, possibly during sunset.](# \\\"Silhouette at the ocean.\\\")\\n    _Description:_ The sky shows warm tones, and the water is relatively still.\\n\\n6.  **Image 6**\\n    ![A scenic photo of a beach at sunset with colorful clouds.](# \\\"Beach sunset scene.\\\")\\n    _Description:_ The sun is low in the sky, casting orange and pink hues.\\n\\n7.  **Image 7**\\n    ![Close-up portrait of a person with a bright smile, short hair, and vibrant background.](# \\\"Smiling person portrait.\\\")\\n    _Description:_ The backdrop appears slightly blurred, focusing on the person’s face.\\n\\n8.  **Image 8**\\n    ![A wide panoramic view of a modern cityscape with tall buildings.](# \\\"City skyline with skyscrapers.\\\")\\n    _Description:_ Numerous skyscrapers are visible, with a hazy horizon.\\n\\n9.  **Image 9**\\n    ![A futuristic-looking sphere or orb with light effects, hovering above a city-like structure.](# \\\"Futuristic orb above a city.\\\")\\n    _Description:_ The orb glows with a blue-white hue, illuminating the area below.\\n\\n10.  **Image 10**\\n    ![A night scene with silhouettes of trees against a misty background.](# \\\"Night forest silhouette.\\\")\\n    _Description:_ The atmosphere is foggy or misty, giving an ethereal look to the forest.\\n\\n11.  **Image 11**\\n    ![A floating crystal or jewel set against a cosmic-looking background.](# \\\"Floating crystal in cosmic scene.\\\")\\n    _Description:_ The crystal shines with multiple facets, and spark-like stars are visible.\\n\\n12.  **Image 12**\\n    ![A woman shown in partial silhouette, possibly backlit by a strong light source.](# \\\"Woman in silhouette.\\\")\\n    _Description:_ Her face is partially lit; the rest of the background is dark or softly blurred.\\n\\n13.  **Image 13**\\n    ![A bright orb or sphere swirling with purple and pink sparkles.](# \\\"Swirling purple orb.\\\")\\n    _Description:_ The orb appears to be pulsating with energy in a cosmic or fantasy environment.\\n\\n14.  **Image 14**\\n    ![Colorful illustration of a playful donkey (or horse) with some text in a stylized font.](# \\\"Cartoon donkey with text.\\\")\\n    _Description:_ Bold, playful text accompanies the cartoonish donkey/horse, set against a plain background.\\n\\n15.  **Image 15**\\n    ![A graphic with the word ‘Merry’ in a decorative style. Possibly holiday-themed.](# \\\"Holiday-themed graphic.\\\")\\n    _Description:_ Stylized text with bright, festive colors, possibly referencing Christmas or a celebration.\\n\\n16.  **Image 16**\\n    ![Underwater scene showing clear turquoise water and a few fish swimming near coral or sandy bottom.](# \\\"Underwater view with fish.\\\")\\n    _Description:_ The seabed is partially visible; water clarity suggests a tropical or clear-water location.\\n\\n17.  **Image 17**\\n    ![A panoramic view of a distant mountain range. The sky is overcast, creating a moody atmosphere.](# \\\"Mountain range under cloudy sky.\\\")\\n    _Description:_ Dark silhouettes of mountains fade into the distance under a dimly lit sky.\\n\\n---\\n\\n\\n---\\n\\n## Sample Prompts\\n\\n### 1. Seamless Transitions\\n- **Prompt**: \\\"Continuous hyperspeed FPV footage: The camera seamlessly flies through a glacial canyon to a dreamy cloudscape.\\\"\\n\\n- **Image Description**: A dynamic FPV sequence transitioning smoothly through a serene glacial environment into ethereal clouds.\\n\\n### 2. Oceanic Glow\\n- **Prompt**: \\\"A glowing ocean at night with bioluminescent creatures underwater. The camera starts with a macro close-up of a glowing jellyfish and then expands to reveal the entire ocean lit up under a starry sky.\\\"\\n\\n- **Image Description**: A glowing jellyfish in vivid detail transitions to reveal a bioluminescent ocean beneath a sparkling starry night.\\n\\n### 3. Title Cards\\n- **Prompt**: \\\"A title screen with dynamic movement. The scene starts at a colorful paint-covered wall. Suddenly, black paint pours on the wall to form the word 'Runway'. The dripping paint is detailed, textured, and lit with cinematic brilliance.\\\"\\n\\n- **Image Description**: A vibrant wall turns dramatic as black paint forms bold, textured text under vivid lighting.\\n\\n---\\n\\n## Key Visual Examples\\n\\n### 1. Unique Camera Movements\\n- **Prompt**: \\\"Continuous hyperspeed FPV footage through a man's mouth into an epic landscape.\\\"\\n- **Image Description**: A surreal transition through the interior of a human mouth, revealing a panoramic landscape.\\n\\n### 2. Dreamlike Pillow Fort\\n- **Prompt**: \\\"A pillow fort in a cozy living room made from quilts and pillows. The camera smoothly zooms into the entrance, revealing an ancient castle interior.\\\"\\n- **Image Description**: A whimsical living room transforms into an enchanting castle.\\n\\n### 3. Fire and Chaos\\n- **Prompt**: \\\"A vortex of fire swirling through an abandoned warehouse.\\\"\\n- **Image Description**: Flames spiral dynamically in a desolate, dramatic warehouse setting.\\n\\n---\\n\\n## Lighting, Styles, and Effects\\n\\n### Lighting Styles\\n- **Diffused Lighting**: Soft, even lighting for atmospheric visuals.\\n- **Silhouette**: Strong backlighting creating dramatic outlines.\\n- **Lens Flare**: Bright reflections adding cinematic depth.\\n- **Backlit**: Subject lit from behind for dramatic effects.\\n\\n### Movement Types\\n- **Dynamic Motion**: High-energy, flowing camera transitions.\\n- **Slow Motion**: Emphasizes intricate, slowed-down details.\\n- **Timelapse**: Rapid transitions showcasing passing time.\\n\\n---\\n\\n## Text and Graphic Styles\\n\\n### Text Prompts\\n1. **ATOMIC in Flames**: Over an erupting volcano, 'ATOMIC' is depicted in fiery text.\\n2. **Icy Title**: 'ATOMIC' frozen in ice over an iceberg.\\n3. **Unique Materials**: Text made of spaghetti, evoking whimsy and creativity.\\n\\n---\\n\\n## Example Prompts with Descriptions\\n\\n### 1. Cinematic Wildlife\\n- **Prompt**: \\\"Static close-up shot of a sloth in a tree. Highlight its sleepy eyes and fur with cinematic lighting.\\\"\\n- **Image Description**: A serene close-up of a sloth in its natural habitat.\\n\\n### 2. Urban Destruction\\n- **Prompt**: \\\"[Scene] Molten lava flowing through city streets [Lighting] Vibrant orange glow [Camera Movement] Sweeping aerial shots.\\\"\\n- **Image Description**: Lava courses dramatically through a cityscape, contrasting fiery tones with the urban environment.\\n\\n### 3. Vortex of Tanks\\n- **Prompt**: \\\"Grainy WWII footage of a massive vortex made of tanks.\\\"\\n- **Image Description**: A surreal vortex of military tanks under vintage cinematography.\\n\\n---\\n\\n## Camera Styles Overview\\n\\n- **Low Angle**: Emphasizes scale and dominance.\\n- **Overhead**: A bird's-eye view for expansive perspective.\\n- **FPV (First-Person View)**: Immersive movement through dynamic environments.\\n- **Handheld**: Natural and intimate movements, resembling real-life footage.```\\\"```'\\n\\n\",\n  \"directed_prompt\": \"Refactor the Runway GEN-3 instructional prompt into a maximally compact yet operationally rich framework for technical users—maintaining essential methodology (subject, action, setting/mood, shot, style) with bulletized clarity—while systematically embedding supporting heuristics, a diversity of concise examples, and explicit references to visual lexicon (lighting, camera movement, shot types, effects) critical for effective prompt engineering. Discard overt redundancy but preserve micro-context and pragmatic cues through in-line annotation or hyperlinked appendix. Validate brevity versus utility by iterative compression and peer-test for interpretability by users with varying baseline knowledge. Ensure register is direct and precise, but not sterile; actionable advice, illustrative texture, and pathways for elaboration must remain visible. Final output: a minimal core skeletal structure with ingredient definitions, compact per-type examples, and compacted reference mini-lists colocated as tooltips or footnotes. Self-audit for balance between instructive density and operational adaptability.\",\n  \"trajectory_outline\": [\n    \"Extract core methodology: subject, action, setting/mood, shot, style as top-level bullets.\",\n    \"Add compact, explicit ingredient definitions for each axis—use one sharply focused example per axis.\",\n    \"Cluster minimal supporting details (lighting, camera, effects, movement types) as succinct sub-bullets or referenced footnotes adjacent to main framework.\",\n    \"Retain micro-context (why/when elements matter) via brief in-line cues or hover/tip notations.\",\n    \"Build a compact linked appendix: curated, minimal example variants for each major element; no excess narrative.\",\n    \"Audit output for actionable utility across technical proficiency levels—fill clarity gaps with minimal supporting instruction where needed.\",\n    \"Peer/self-test compressibility and guidance sufficiency by trial rewrites and feedback on interpretability.\",\n    \"Iteratively compress and revalidate: remove only what’s redundant, not foundational or instructive.\",\n    \"Present final as an actionable, self-contained reference—skeletal structure, minimal variant set, and links to essential style/tool lists—ensuring constructive clarity, pragmatic adaptability, and zero narrative digression.\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"distilled_core\": \"- Structure every prompt with: \\n  1. Subject (entity, distinguishing traits; e.g., \\\"woman in orange\\\")\\n  2. Action (behavior/modifier; e.g., \\\"standing\\\")\\n  3. Setting/Mood (environment/emotion; e.g., \\\"tropical rainforest, overcast\\\")\\n  4. Shot (camera angle/type; e.g., \\\"low angle static shot\\\")\\n  5. Style (visual/aesthetic reference; e.g., \\\"cinematic lighting\\\")\\n- Use precise, minimal language; combine all elements in one clear sentence.\\n- Always specify lighting, camera movement, and effect styles using concise lexicon: [Lighting: backlit/silhouette/diffused/lens flare], [Movement: FPV/slowmo/timelapse], [Shot: close-up/overhead/handheld/wide], [Effects: color grade, title card text, surreal transition].\\n- Annotate for clarity only as necessary, using inline parentheticals or brackets.\\n- Example: \\\"Low angle static shot: woman in orange stands in tropical rainforest, dramatic overcast sky, cinematic lighting.\\\"\\n- Reference: [Appendix]* for mini-lists of style and movement options.\\n- Validate each prompt: all five elements present, unequivocal language, leverages visual lexicon; no narrative, no excess.\\n*See tooltip or footnote for compacted lists per style/movement/lighting/effect.\"\n}",
        }
      }
    },
      {
      "instruction": "# Problem Exploder\n\nYour goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\n\n`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`",
      "step": "a",
      "title": "Problem Exploder",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Refactor the instructional prompt on organizing Runway GEN-3 video prompts by extracting the core structural methodology—subject, action, setting/mood, shot, style—into a minimal, explicit, and rigid framework. Eliminate all redundant explanations, excessive examples, and digressive content. Present the instructions as a concise, stepwise bullet-list, with exactly one schematic, one-line example illustrating the expected format. Ensure total clarity, register coherence, and actionable precision for technical users. Test and self-audit for information compression, clarity, and zero narrative noise.\\n\\n```Organize your prompt using the following structure:\\n\\n1. **Subject**:\\n   - Any person, place, or thing (e.g., *a handsome male model*, *a commercial airplane*).\\n   - Specify details like attire, hairstyle, or emotional state (e.g., *wearing a red dress*, *red hair/mohawk*).\\n\\n2. **Action**:\\n   - Define what the subject is doing (e.g., *walking*, *dancing*, *staring intently*).\\n   - Use descriptive adjectives for enhanced clarity (e.g., *walking angrily*).\\n\\n3. **Setting/Mood**:\\n   - Describe the location (e.g., *a castle*, *a busy city street*).\\n   - Include environmental details such as weather and lighting (e.g., *stormy clouds*, *bright sunny day*).\\n\\n4. **Shot**:\\n   - Indicate the type of camera shot (e.g., *Close-Up*, *Wide Angle*).\\n\\n5. **Style**:\\n   - Suggest stylistic elements (e.g., *Cinematic Film*, *80’s Action Movie*).\\n   - Add camera-specific details such as *IMAX* or *color grading ideas*.\\n\\n### Example Prompt:\\n\\\"Low angle static shot: The camera is angled up at a woman wearing all orange as she stands in a tropical rainforest with colorful flora. The dramatic sky is overcast and gray.\\\"\\n\\n- **Tip**: Repeating or reinforcing key ideas in different sections of your prompt improves adherence.\\n\\n---\\n\\n## Sample Prompts\\n\\n### 1. Seamless Transitions\\n- **Prompt**: \\\"Continuous hyperspeed FPV footage: The camera seamlessly flies through a glacial canyon to a dreamy cloudscape.\\\"\\n\\n- **Image Description**: A dynamic FPV sequence transitioning smoothly through a serene glacial environment into ethereal clouds.\\n\\n### 2. Oceanic Glow\\n- **Prompt**: \\\"A glowing ocean at night with bioluminescent creatures underwater. The camera starts with a macro close-up of a glowing jellyfish and then expands to reveal the entire ocean lit up under a starry sky.\\\"\\n\\n- **Image Description**: A glowing jellyfish in vivid detail transitions to reveal a bioluminescent ocean beneath a sparkling starry night.\\n\\n### 3. Title Cards\\n- **Prompt**: \\\"A title screen with dynamic movement. The scene starts at a colorful paint-covered wall. Suddenly, black paint pours on the wall to form the word 'Runway'. The dripping paint is detailed, textured, and lit with cinematic brilliance.\\\"\\n\\n- **Image Description**: A vibrant wall turns dramatic as black paint forms bold, textured text under vivid lighting.\\n\\n---\\n\\n## Key Visual Examples\\n\\n### 1. Unique Camera Movements\\n- **Prompt**: \\\"Continuous hyperspeed FPV footage through a man's mouth into an epic landscape.\\\"\\n- **Image Description**: A surreal transition through the interior of a human mouth, revealing a panoramic landscape.\\n\\n### 2. Dreamlike Pillow Fort\\n- **Prompt**: \\\"A pillow fort in a cozy living room made from quilts and pillows. The camera smoothly zooms into the entrance, revealing an ancient castle interior.\\\"\\n- **Image Description**: A whimsical living room transforms into an enchanting castle.\\n\\n### 3. Fire and Chaos\\n- **Prompt**: \\\"A vortex of fire swirling through an abandoned warehouse.\\\"\\n- **Image Description**: Flames spiral dynamically in a desolate, dramatic warehouse setting.\\n\\n---\\n\\n## Lighting, Styles, and Effects\\n\\n### Lighting Styles\\n- **Diffused Lighting**: Soft, even lighting for atmospheric visuals.\\n- **Silhouette**: Strong backlighting creating dramatic outlines.\\n- **Lens Flare**: Bright reflections adding cinematic depth.\\n- **Backlit**: Subject lit from behind for dramatic effects.\\n\\n### Movement Types\\n- **Dynamic Motion**: High-energy, flowing camera transitions.\\n- **Slow Motion**: Emphasizes intricate, slowed-down details.\\n- **Timelapse**: Rapid transitions showcasing passing time.\\n\\n---\\n\\n## Text and Graphic Styles\\n\\n### Text Prompts\\n1. **ATOMIC in Flames**: Over an erupting volcano, 'ATOMIC' is depicted in fiery text.\\n2. **Icy Title**: 'ATOMIC' frozen in ice over an iceberg.\\n3. **Unique Materials**: Text made of spaghetti, evoking whimsy and creativity.\\n\\n---\\n\\n## Example Prompts with Descriptions\\n\\n### 1. Cinematic Wildlife\\n- **Prompt**: \\\"Static close-up shot of a sloth in a tree. Highlight its sleepy eyes and fur with cinematic lighting.\\\"\\n- **Image Description**: A serene close-up of a sloth in its natural habitat.\\n\\n### 2. Urban Destruction\\n- **Prompt**: \\\"[Scene] Molten lava flowing through city streets [Lighting] Vibrant orange glow [Camera Movement] Sweeping aerial shots.\\\"\\n- **Image Description**: Lava courses dramatically through a cityscape, contrasting fiery tones with the urban environment.\\n\\n### 3. Vortex of Tanks\\n- **Prompt**: \\\"Grainy WWII footage of a massive vortex made of tanks.\\\"\\n- **Image Description**: A surreal vortex of military tanks under vintage cinematography.\\n\\n---\\n\\n## Camera Styles Overview\\n\\n- **Low Angle**: Emphasizes scale and dominance.\\n- **Overhead**: A bird's-eye view for expansive perspective.\\n- **FPV (First-Person View)**: Immersive movement through dynamic environments.\\n- **Handheld**: Natural and intimate movements, resembling real-life footage.\\n\\n**Prompt Structures**: To achieve optimal results in GEN-3 Alpha, organize your prompts into clear sections for the **scene**, **subject**, and **camera movement**. Use the following structure. This outlines guidelines for optimizing video creation using Runway GEN-3, focusing on crafting structured prompts that detail scene, subject, and camera movement for achieving cinematic quality. Examples include transitioning from a glacial canyon to clouds, showcasing bioluminescent ocean life, and creating dynamic title cards with textured effects. Key visual examples further illustrate unique camera movements, such as a surreal journey from a human mouth to a landscape, and thematic visuals like a dreamlike pillow fort and a vortex of fire. The transcript also covers various lighting styles, movement types, and text and graphic styles, offering a comprehensive toolkit for enhancing visual storytelling. The conclusion emphasizes Runway GEN-3's potential for creative freedom through structured prompts and cinematic techniques:\\n\\n---\\n\\n# Runway Prompt Examples\\n\\n1.  **Image 1**\\n    ![A man walking on a paved road, seen from behind, wearing a jacket and dark pants.](# \\\"Man walking on a paved road.\\\")\\n    _Description:_ A man walking alone on a street. The road is slightly wet, and he is wearing dark attire.\\n\\n2.  **Image 2**\\n    ![A person in a winter setting, standing in snow.](# \\\"Person standing in snow.\\\")\\n    _Description:_ This shows a figure in a cold environment, possibly wearing winter gear, surrounded by snow.\\n\\n3.  **Image 3**\\n    ![A person standing near a waterfall with lush greenery around.](# \\\"Near a waterfall with greenery.\\\")\\n    _Description:_ The scene features flowing water in the background and dense foliage.\\n\\n4.  **Image 4**\\n    ![A portrait-style image of a person with short hair looking pensively to one side.](# \\\"Portrait of a person.\\\")\\n    _Description:_ The subject’s upper body and face are visible, with a calm or reflective expression.\\n\\n5.  **Image 5**\\n    ![Silhouette of a person looking out at a calm ocean horizon, possibly during sunset.](# \\\"Silhouette at the ocean.\\\")\\n    _Description:_ The sky shows warm tones, and the water is relatively still.\\n\\n6.  **Image 6**\\n    ![A scenic photo of a beach at sunset with colorful clouds.](# \\\"Beach sunset scene.\\\")\\n    _Description:_ The sun is low in the sky, casting orange and pink hues.\\n\\n7.  **Image 7**\\n    ![Close-up portrait of a person with a bright smile, short hair, and vibrant background.](# \\\"Smiling person portrait.\\\")\\n    _Description:_ The backdrop appears slightly blurred, focusing on the person’s face.\\n\\n8.  **Image 8**\\n    ![A wide panoramic view of a modern cityscape with tall buildings.](# \\\"City skyline with skyscrapers.\\\")\\n    _Description:_ Numerous skyscrapers are visible, with a hazy horizon.\\n\\n9.  **Image 9**\\n    ![A futuristic-looking sphere or orb with light effects, hovering above a city-like structure.](# \\\"Futuristic orb above a city.\\\")\\n    _Description:_ The orb glows with a blue-white hue, illuminating the area below.\\n\\n10.  **Image 10**\\n    ![A night scene with silhouettes of trees against a misty background.](# \\\"Night forest silhouette.\\\")\\n    _Description:_ The atmosphere is foggy or misty, giving an ethereal look to the forest.\\n\\n11.  **Image 11**\\n    ![A floating crystal or jewel set against a cosmic-looking background.](# \\\"Floating crystal in cosmic scene.\\\")\\n    _Description:_ The crystal shines with multiple facets, and spark-like stars are visible.\\n\\n12.  **Image 12**\\n    ![A woman shown in partial silhouette, possibly backlit by a strong light source.](# \\\"Woman in silhouette.\\\")\\n    _Description:_ Her face is partially lit; the rest of the background is dark or softly blurred.\\n\\n13.  **Image 13**\\n    ![A bright orb or sphere swirling with purple and pink sparkles.](# \\\"Swirling purple orb.\\\")\\n    _Description:_ The orb appears to be pulsating with energy in a cosmic or fantasy environment.\\n\\n14.  **Image 14**\\n    ![Colorful illustration of a playful donkey (or horse) with some text in a stylized font.](# \\\"Cartoon donkey with text.\\\")\\n    _Description:_ Bold, playful text accompanies the cartoonish donkey/horse, set against a plain background.\\n\\n15.  **Image 15**\\n    ![A graphic with the word ‘Merry’ in a decorative style. Possibly holiday-themed.](# \\\"Holiday-themed graphic.\\\")\\n    _Description:_ Stylized text with bright, festive colors, possibly referencing Christmas or a celebration.\\n\\n16.  **Image 16**\\n    ![Underwater scene showing clear turquoise water and a few fish swimming near coral or sandy bottom.](# \\\"Underwater view with fish.\\\")\\n    _Description:_ The seabed is partially visible; water clarity suggests a tropical or clear-water location.\\n\\n17.  **Image 17**\\n    ![A panoramic view of a distant mountain range. The sky is overcast, creating a moody atmosphere.](# \\\"Mountain range under cloudy sky.\\\")\\n    _Description:_ Dark silhouettes of mountains fade into the distance under a dimly lit sky.\\n\\n---\\n\\n\\n---\\n\\n## Sample Prompts\\n\\n### 1. Seamless Transitions\\n- **Prompt**: \\\"Continuous hyperspeed FPV footage: The camera seamlessly flies through a glacial canyon to a dreamy cloudscape.\\\"\\n\\n- **Image Description**: A dynamic FPV sequence transitioning smoothly through a serene glacial environment into ethereal clouds.\\n\\n### 2. Oceanic Glow\\n- **Prompt**: \\\"A glowing ocean at night with bioluminescent creatures underwater. The camera starts with a macro close-up of a glowing jellyfish and then expands to reveal the entire ocean lit up under a starry sky.\\\"\\n\\n- **Image Description**: A glowing jellyfish in vivid detail transitions to reveal a bioluminescent ocean beneath a sparkling starry night.\\n\\n### 3. Title Cards\\n- **Prompt**: \\\"A title screen with dynamic movement. The scene starts at a colorful paint-covered wall. Suddenly, black paint pours on the wall to form the word 'Runway'. The dripping paint is detailed, textured, and lit with cinematic brilliance.\\\"\\n\\n- **Image Description**: A vibrant wall turns dramatic as black paint forms bold, textured text under vivid lighting.\\n\\n---\\n\\n## Key Visual Examples\\n\\n### 1. Unique Camera Movements\\n- **Prompt**: \\\"Continuous hyperspeed FPV footage through a man's mouth into an epic landscape.\\\"\\n- **Image Description**: A surreal transition through the interior of a human mouth, revealing a panoramic landscape.\\n\\n### 2. Dreamlike Pillow Fort\\n- **Prompt**: \\\"A pillow fort in a cozy living room made from quilts and pillows. The camera smoothly zooms into the entrance, revealing an ancient castle interior.\\\"\\n- **Image Description**: A whimsical living room transforms into an enchanting castle.\\n\\n### 3. Fire and Chaos\\n- **Prompt**: \\\"A vortex of fire swirling through an abandoned warehouse.\\\"\\n- **Image Description**: Flames spiral dynamically in a desolate, dramatic warehouse setting.\\n\\n---\\n\\n## Lighting, Styles, and Effects\\n\\n### Lighting Styles\\n- **Diffused Lighting**: Soft, even lighting for atmospheric visuals.\\n- **Silhouette**: Strong backlighting creating dramatic outlines.\\n- **Lens Flare**: Bright reflections adding cinematic depth.\\n- **Backlit**: Subject lit from behind for dramatic effects.\\n\\n### Movement Types\\n- **Dynamic Motion**: High-energy, flowing camera transitions.\\n- **Slow Motion**: Emphasizes intricate, slowed-down details.\\n- **Timelapse**: Rapid transitions showcasing passing time.\\n\\n---\\n\\n## Text and Graphic Styles\\n\\n### Text Prompts\\n1. **ATOMIC in Flames**: Over an erupting volcano, 'ATOMIC' is depicted in fiery text.\\n2. **Icy Title**: 'ATOMIC' frozen in ice over an iceberg.\\n3. **Unique Materials**: Text made of spaghetti, evoking whimsy and creativity.\\n\\n---\\n\\n## Example Prompts with Descriptions\\n\\n### 1. Cinematic Wildlife\\n- **Prompt**: \\\"Static close-up shot of a sloth in a tree. Highlight its sleepy eyes and fur with cinematic lighting.\\\"\\n- **Image Description**: A serene close-up of a sloth in its natural habitat.\\n\\n### 2. Urban Destruction\\n- **Prompt**: \\\"[Scene] Molten lava flowing through city streets [Lighting] Vibrant orange glow [Camera Movement] Sweeping aerial shots.\\\"\\n- **Image Description**: Lava courses dramatically through a cityscape, contrasting fiery tones with the urban environment.\\n\\n### 3. Vortex of Tanks\\n- **Prompt**: \\\"Grainy WWII footage of a massive vortex made of tanks.\\\"\\n- **Image Description**: A surreal vortex of military tanks under vintage cinematography.\\n\\n---\\n\\n## Camera Styles Overview\\n\\n- **Low Angle**: Emphasizes scale and dominance.\\n- **Overhead**: A bird's-eye view for expansive perspective.\\n- **FPV (First-Person View)**: Immersive movement through dynamic environments.\\n- **Handheld**: Natural and intimate movements, resembling real-life footage.```\\\"```'\\n\\n\",\n  \"distilled_core\": \"- Structure every prompt with: \\n  1. Subject (entity, distinguishing traits; e.g., \\\"woman in orange\\\")\\n  2. Action (behavior/modifier; e.g., \\\"standing\\\")\\n  3. Setting/Mood (environment/emotion; e.g., \\\"tropical rainforest, overcast\\\")\\n  4. Shot (camera angle/type; e.g., \\\"low angle static shot\\\")\\n  5. Style (visual/aesthetic reference; e.g., \\\"cinematic lighting\\\")\\n- Use precise, minimal language; combine all elements in one clear sentence.\\n- Always specify lighting, camera movement, and effect styles using concise lexicon: [Lighting: backlit/silhouette/diffused/lens flare], [Movement: FPV/slowmo/timelapse], [Shot: close-up/overhead/handheld/wide], [Effects: color grade, title card text, surreal transition].\\n- Annotate for clarity only as necessary, using inline parentheticals or brackets.\\n- Example: \\\"Low angle static shot: woman in orange stands in tropical rainforest, dramatic overcast sky, cinematic lighting.\\\"\\n- Reference: [Appendix]* for mini-lists of style and movement options.\\n- Validate each prompt: all five elements present, unequivocal language, leverages visual lexicon; no narrative, no excess.\\n*See tooltip or footnote for compacted lists per style/movement/lighting/effect.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"goal_map\": [\n    {\n      \"explicit_ask\": \"Refactor the instructional prompt on organizing Runway GEN-3 video prompts.\",\n      \"sub_goals\": [\n        \"Extract and distill the core structural methodology from the instructional text.\",\n        \"Define and clarify categories: subject, action, setting/mood, shot, style.\",\n        \"Ensure methodology is presented as a minimal, explicit, and rigid framework.\",\n        \"Eliminate redundant explanations, excessive examples, and digressive content.\",\n        \"Present instructions as a concise, stepwise bullet-list.\",\n        \"Include exactly one schematic, one-line example illustrating the expected format.\",\n        \"Ensure total clarity, register coherence, and actionable precision (technical audience).\",\n        \"Self-audit for maximal information compression, clarity, and absence of narrative/digressive noise.\"\n      ]\n    },\n    {\n      \"hidden_assumptions\": [\n        \"The original instructional prompt contains unnecessary narrative and examples that must be removed.\",\n        \"Technical users need precise, minimal instructions and can understand concepts without exhaustive explanation.\",\n        \"A single, clear example suffices to illustrate the required output format.\",\n        \"Instructions must be modular and easily referenceable (e.g., through bullet points and footnotes/tooltips).\",\n        \"All five structural elements (subject, action, setting/mood, shot, style) are always required.\",\n        \"There may be a need for appendices, tooltips, or footnotes to house auxiliary style/movement/lighting/effects lists, so that the main instructions remain lean.\",\n        \"Clarity and consistency in register (i.e., technical tone and language) must be maintained throughout.\",\n        \"Information provided should directly support prompt construction—no extraneous generative or descriptive prose.\",\n        \"The output is not an actual instructional guide, but a meta-framework for expressing such guides in this genre.\"\n      ]\n    },\n    {\n      \"enumerated_sub_goals\": [\n        \"Identify and extract the main prompt structure components from the source material.\",\n        \"Define each component (subject, action, setting/mood, shot, style) in the minimal terms possible.\",\n        \"Prescribe assembling elements into a one-sentence schema, using explicit formatting.\",\n        \"Require the use of a specialized, compressed visual lexicon for lighting/camera/effects.\",\n        \"Provide an in-line, one-line schematic/example covering all elements.\",\n        \"Dictate bullet-list presentation and precise, stepwise instructions.\",\n        \"Explicitly prohibit unnecessary explanation, repetitive examples, and narrative detours.\",\n        \"Establish a process or checklist for users to verify prompt completeness and clarity.\",\n        \"Direct users to reference compacted/auxiliary lists elsewhere for stylistic/movement/etc. options.\",\n        \"Instruct authors to self-audit for brevity, clarity, and format adherence.\"\n      ]\n    },\n    {\n      \"blockers\": [\n        \"The large volume of source text containing mixed examples, which must be synthesized down and not referenced in detail.\",\n        \"Potential ambiguity in mapping multi-part instructions to a minimal methodology.\",\n        \"Defining the precise boundaries of 'minimal' vs. 'insufficient' explanation, especially for technical audiences.\",\n        \"Need to maintain total clarity and actionable precision while being terse.\",\n        \"Ensuring no element of the original methodology is lost or misrepresented in compression.\",\n        \"Balancing schematic brevity with user needs for clarity and usability.\",\n        \"Ensuring that referencing appendices, tooltips, or footnotes (rather than inline lists) suffices for the technical user.\",\n        \"Aligning register and tone strictly for technical users—avoiding didactic or narrative inclinations.\",\n        \"Potential friction from omitting context or examples users may find helpful in application.\"\n      ]\n    }\n  ]\n}",
        }
      }
    }