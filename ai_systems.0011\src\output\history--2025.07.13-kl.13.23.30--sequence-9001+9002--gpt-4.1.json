  {
  "initial_prompt": "<PERSON><PERSON><PERSON><PERSON> så mye med dagen <PERSON>, 25 år, min nivø. Du er den yngre generasjon, fortsett å tenke ambisiøst, men gjør det i balanse med innsats.",
  "sequence_id": "9001|9002",
  "results": [
      {
      "instruction": "# Context Extractor\n\nDitt mål er ikke å **skrive** hilsen, men å **hente** personlige fakta. Execute as:\n\n`{role=context_extractor; input=[raw_text:str]; process=[identify_recipient(), detect_relationship(), extract_age_or_milestone(), list_unique_traits(), capture_shared_memories()], constraints=[ingen_omtolkning(), ingen_humor()], output={facts:{name:str|None, relation:str|None, age:str|None, traits:list, memories:list}}}`",
      "step": "a",
      "title": "Context Extractor",
      "input": "Gratulerer så mye med dagen <PERSON>, 25 år, min nivø. Du er den yngre generasjon, fortsett å tenke ambisiøst, men gjør det i balanse med innsats.",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"facts\": {\n    \"name\": \"Sebastian\",\n    \"relation\": \"min nevø\",\n    \"age\": \"25 år\",\n    \"traits\": [\"ambisiøs\", \"del av den yngre generasjon\"],\n    \"memories\": []\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Personality Amplifier\n\nDitt mål er ikke å **endre** fakta, men å **farge** dem med mottakerens unike personlighet. Execute as:\n\n`{role=personality_amplifier; input=[facts:dict]; process=[select_signature_trait(), weave_inside_joke(), preserve_affection_level()], constraints=[ingen_generiske_floskler()], requirements=[tone=varm_og_leken], output={flair:str}}`",
      "step": "b",
      "title": "Personality Amplifier",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Gratulerer så mye med dagen Sebastian, 25 år, min nivø. Du er den yngre generasjon, fortsett å tenke ambisiøst, men gjør det i balanse med innsats.\\\"```'\\n\\n\",\n  \"facts\": {\n    \"name\": \"Sebastian\",\n    \"relation\": \"min nevø\",\n    \"age\": \"25 år\",\n    \"traits\": [\n      \"ambisiøs\",\n      \"del av den yngre generasjon\"\n    ],\n    \"memories\": []\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"flair\": \"Hurra for Sebastian – vår offisielle representant for next-gen-ambisjoner og den eneste i gjengen som faktisk skjønner TikTok-memetante stadig sender! 25 år med målrettet pågangsmot, og fortsatt rom for balanse mellom storslagne planer og tid til å ta av seg skoene under søndagsmiddagen (vet du har en egen teknikk for akkurat dét). Kjære nevø, måtte du fortsette å sikte høyt, men aldri glemme å lande her og nå iblant – så kanskje vi andre også skjønner hva som foregår (i alle fall litt).\"\n}",
        }
      }
    },
      {
      "instruction": "# Humor Synthesizer\n\nDitt mål er ikke å **liste** informasjon, men å **lansere punchline**. Execute as:\n\n`{role=humor_synthesizer; input=[flair:str]; process=[craft_witty_twist(), insert_playful_tease(), add_emoji()], constraints=[max_words(12), unngå_støtende_innhold()], output={humor:str}}`",
      "step": "c",
      "title": "Humor Synthesizer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Gratulerer så mye med dagen Sebastian, 25 år, min nivø. Du er den yngre generasjon, fortsett å tenke ambisiøst, men gjør det i balanse med innsats.\\\"```'\\n\\n\",\n  \"flair\": \"Hurra for Sebastian – vår offisielle representant for next-gen-ambisjoner og den eneste i gjengen som faktisk skjønner TikTok-memetante stadig sender! 25 år med målrettet pågangsmot, og fortsatt rom for balanse mellom storslagne planer og tid til å ta av seg skoene under søndagsmiddagen (vet du har en egen teknikk for akkurat dét). Kjære nevø, måtte du fortsette å sikte høyt, men aldri glemme å lande her og nå iblant – så kanskje vi andre også skjønner hva som foregår (i alle fall litt).\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"humor\": \"Gratulerer! 25 år og fortsatt ikke blokkert tanten på TikTok! 🥳\"\n}",
        }
      }
    },
      {
      "instruction": "# Greeting Composer\n\nDitt mål er ikke å **forklare** prosessen, men å **smelte** fakta og humor til én kort, personlig bursdagshilsen. Execute as:\n\n`{role=greeting_composer; input=[facts:dict, humor:str]; process=[merge_name_and_age(), embed_humor(), close_with_affection()], constraints=[≤125_tegn, én_setning()], requirements=[norwegian_language(), tydelig_bursdagsreferanse()], output={greeting:str}}`",
      "step": "d",
      "title": "Greeting Composer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Gratulerer så mye med dagen Sebastian, 25 år, min nivø. Du er den yngre generasjon, fortsett å tenke ambisiøst, men gjør det i balanse med innsats.\\\"```'\\n\\n\",\n  \"humor\": \"Gratulerer! 25 år og fortsatt ikke blokkert tanten på TikTok! 🥳\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"greeting\": \"Gratulerer med dagen Sebastian, 25 år og fortsatt ikke blokkert tanten på TikTok – satser på balanse og latter!\"\n}",
        }
      }
    },
      {
      "instruction": "# Essens‑Kartlegger\n\nDitt mål er **ikke** å svare teksten, men å kartlegge bursdagsfakta + personlige særtrekk. Execute as:\n\n`{role=essence_mapper; input=[content:any]; process=[lokaliser_feiringsdato_eller_alder(), hent_navn_og_relasjon(), fang_personlige_quirks(), noter_klengete_kjælenavn(), fjern_unødvendig_metaforikk()]; constraints=[ingen_humor_enda()]; output={profil:dict(navn:str, alder:int?, relasjon:str?, quirks:list, kjælenavn:str?)}}`",
      "step": "a",
      "title": "Essens‑Kartlegger",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Gratulerer så mye med dagen Sebastian, 25 år, min nivø. Du er den yngre generasjon, fortsett å tenke ambisiøst, men gjør det i balanse med innsats.\\\"```'\\n\\n\",\n  \"greeting\": \"Gratulerer med dagen Sebastian, 25 år og fortsatt ikke blokkert tanten på TikTok – satser på balanse og latter!\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"profil\": {\n    \"navn\": \"Sebastian\",\n    \"alder\": 25,\n    \"relasjon\": \"nivø\",\n    \"quirks\": [\n      \"tenker ambisiøst\",\n      \"jobber for balanse mellom mål og innsats\",\n      \"har ikke blokkert tanten på TikTok\"\n    ],\n    \"kjælenavn\": null\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Humor‑Edge‑Designer\n\nDitt mål er **ikke** å gjengi profilen, men å skape en subtil, skarp humorvinkel. Execute as:\n\n`{role=humor_edge_designer; input=[profil:dict]; process=[generer_spiss_ordspill(profil), velg_ertende_vinkel(balanser_varme=True), planlegg_overraskelses_punchline(), velg_passende_emoji(erter+feiring)]; constraints=[unngå_sårende_personangrep(), bevare_kjærlig_tone()]; output={humor_edge:dict(ordspill:str, teasing:str, punch:str, emoji:str)}}`",
      "step": "b",
      "title": "Humor‑Edge‑Designer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Gratulerer så mye med dagen Sebastian, 25 år, min nivø. Du er den yngre generasjon, fortsett å tenke ambisiøst, men gjør det i balanse med innsats.\\\"```'\\n\\n\",\n  \"profil\": {\n    \"navn\": \"Sebastian\",\n    \"alder\": 25,\n    \"relasjon\": \"nivø\",\n    \"quirks\": [\n      \"tenker ambisiøst\",\n      \"jobber for balanse mellom mål og innsats\",\n      \"har ikke blokkert tanten på TikTok\"\n    ],\n    \"kjælenavn\": null\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"humor_edge\": {\n    \"ordspill\": \"Gratulerer med kvart-århundre, Sebastian – nå er du akkurat gammel nok til å drømme stort, men fortsatt ung nok til å la moren din betale for Netflix.\",\n    \"teasing\": \"Bare husk: Å være ambisiøs og i balanse er viktig – men ikke så mye balanse at sofaen får innsøkk etter deg.\",\n    \"punch\": \"Og takk for at du ikke har blokkert tanten din på TikTok – nå skal hele feeden få se deg danse Balanse-Bossa Nova!\",\n    \"emoji\": \"🎉😏🥳\"\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Personlig‑Tone‑Mixer\n\nDitt mål er **ikke** å forklare humoren, men å flette varme og personlighet. Execute as:\n\n`{role=tone_mixer; input=[profil:dict, humor_edge:dict]; process=[bland_kjælenavn_og_relasjon(), injiser_varm_kompliment(quota=1), sørg_for_emosjonell_balanselinje()], constraints=[humor_må_fortsatt_være_tydelig(), ingen_lang_sentimentalitet()]; output={tone_plan:dict(åpner:str, kompliment:str, balanse:str)}}`",
      "step": "c",
      "title": "Personlig‑Tone‑Mixer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Gratulerer så mye med dagen Sebastian, 25 år, min nivø. Du er den yngre generasjon, fortsett å tenke ambisiøst, men gjør det i balanse med innsats.\\\"```'\\n\\n\",\n  \"humor_edge\": {\n    \"ordspill\": \"Gratulerer med kvart-århundre, Sebastian – nå er du akkurat gammel nok til å drømme stort, men fortsatt ung nok til å la moren din betale for Netflix.\",\n    \"teasing\": \"Bare husk: Å være ambisiøs og i balanse er viktig – men ikke så mye balanse at sofaen får innsøkk etter deg.\",\n    \"punch\": \"Og takk for at du ikke har blokkert tanten din på TikTok – nå skal hele feeden få se deg danse Balanse-Bossa Nova!\",\n    \"emoji\": \"🎉😏🥳\"\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"tone_plan\": {\n    \"åpner\": \"Gratulerer med kvart-århundre, Sebastian – til min favorittnivø (ikke si det til de andre), du er nå stor nok til å drømme, men fortsatt liten nok til at mamma betaler Netflix!\",\n    \"kompliment\": \"Du har alltid hatt evnen til å sikte mot stjernene, og jammen gjør du det med stil.\",\n    \"balanse\": \"Bare husk: Ambisjoner er topp, så lenge ikke sofaen får bedre plass til deg enn du har til drømmene dine. 😉\"\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Hilsen‑Smed\n\nDitt mål er **ikke** å analysere planen, men å smi selve hilsenen. Execute as:\n\n`{role=greeting_smith; input=[profil:dict, humor_edge:dict, tone_plan:dict]; process=[sett_sammen_åpner_og_ordspill(), plasser_teasing_midt_i_setningen(), avslutt_med_punch+emoji(), trim_til_max_30_ord(), mål_tegn<=125], constraints=[behold_feiringsfokus(), ingen_ekstra_emojis()], output={rå_hilsen:str}}`",
      "step": "d",
      "title": "Hilsen‑Smed",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Gratulerer så mye med dagen Sebastian, 25 år, min nivø. Du er den yngre generasjon, fortsett å tenke ambisiøst, men gjør det i balanse med innsats.\\\"```'\\n\\n\",\n  \"tone_plan\": {\n    \"åpner\": \"Gratulerer med kvart-århundre, Sebastian – til min favorittnivø (ikke si det til de andre), du er nå stor nok til å drømme, men fortsatt liten nok til at mamma betaler Netflix!\",\n    \"kompliment\": \"Du har alltid hatt evnen til å sikte mot stjernene, og jammen gjør du det med stil.\",\n    \"balanse\": \"Bare husk: Ambisjoner er topp, så lenge ikke sofaen får bedre plass til deg enn du har til drømmene dine. 😉\"\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"rå_hilsen\": \"Gratulerer med kvart-århundre, Sebastian! Endelig så voksen at du kan drømme stort, men fortsatt så ung at mamma betaler Netflix – hold balansen og fest løs! 😉\"\n}",
        }
      }
    },
      {
      "instruction": "# Skjerpe‑Validator\n\nDitt mål er **ikke** å skrive om alt, men å skjerpe og godkjenne. Execute as:\n\n`{role=punch_validator; input=[rå_hilsen:str]; process=[sjekk_tegnlengde(<=125), tell_ord(<=30), verifiser_humor_skarphet(), sikre_kjærlig_subtekst(), fiks_rytme_og_punktsetting()], constraints=[ingen_nye_fakta(), ingen_overskridelse_av_grenser()], output={endelig_hilsen:str}}`",
      "step": "e",
      "title": "Skjerpe‑Validator",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Gratulerer så mye med dagen Sebastian, 25 år, min nivø. Du er den yngre generasjon, fortsett å tenke ambisiøst, men gjør det i balanse med innsats.\\\"```'\\n\\n\",\n  \"rå_hilsen\": \"Gratulerer med kvart-århundre, Sebastian! Endelig så voksen at du kan drømme stort, men fortsatt så ung at mamma betaler Netflix – hold balansen og fest løs! 😉\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"endelig_hilsen\": \"Gratulerer med kvart-århundre, Sebastian! Nå er du voksen nok til å drømme stort, men fortsatt ung nok til at mamma betaler Netflix. Hold balansen, og ha en rå dag!\"\n}",
        }
      }
    }