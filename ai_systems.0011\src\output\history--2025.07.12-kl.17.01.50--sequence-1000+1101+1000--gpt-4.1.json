  {
  "initial_prompt": "Reaffirm the absolute requirement to conform to all prior instructions, structural conventions, and guiding philosophies embedded within the codebase. Develop a maximally generalized and fully optimized system instruction—'Contextual Expander and Explosive Decomposer'—which integrates and amplifies the unified capabilities of the 'Instruction Converter', 'Problem Exploder', and 'Function Extractor' modules within a single cohesive process. Ensure uncompromising retention of each module's universal property: consistently transforming minimal or low-effort inputs into reliably high-value outputs. Mandate the seamless unification of all these transformation processes into a single integrated pipeline, ensuring the functional completeness of each module is fully realized and preserved. Drive all process flows toward maximal cross-module synergy, optimizing for the extraction of cross-domain value and guaranteeing that outputs are immediately ready for executable, directive-driven application. Enforce that the output format consists of a compound object aggregating generalized functional instructions, fully decomposed goal maps, and operational dependencies, with directive primacy and domain-agnostic utility as core criteria.\n\n```\n    # 1000: Instruction Converter/Prompt Enhancer\n    \"1000-a-instruction_converter\": {\n        \"title\": \"Instruction Converter\",\n        \"interpretation\": \"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\",\n        \"transformation\": \"`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\",\n        # \"context\": {},\n    },\n    # 1100: Explosive Decomposition of Problem Statements\n    \"1100-a-problem_exploder\": {\n        \"title\": \"Problem Exploder\",\n        \"interpretation\": \"Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\",\n        \"transformation\": \"`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`\",\n        # \"context\": {},\n    },\n\n\t# 1200: Function Extractor - Unified Decomposition and Conversion Engine\n\t\"1200-a-function_extractor\": {\n\t    \"title\": \"Function Extractor\",\n\t    \"interpretation\": \"Your goal is not to **interpret** the input, but to **extract** its functional essence by detonating it into constituent elements and converting them into actionable operational directives. Execute as:\",\n\t    \"transformation\": \"`{role=function_extractor; input=[raw_input:str]; process=[detonate_into_functional_components(), identify_explicit_operational_asks(), surface_hidden_functional_dependencies(), enumerate_actionable_sub_goals(), extract_procedural_constraints(), convert_declaratives_to_operational_imperatives(), strip_contextual_noise(), preserve_functional_core(), reveal_execution_blockers(), synthesize_actionable_instruction_map()]; constraints=[no_interpretive_solutions(), pure_functional_extraction_only(), deliver_clear_operational_commands(), preserve_functional_sequence(), maintain_domain_agnostic_applicability()]; requirements=[complete_functional_decomposition(), actionable_directive_conversion(), remove_subjective_references(), preserve_operational_accuracy(), maintain_functional_intent()]; output={functional_instruction_map:dict, operational_directives:array, execution_dependencies:array}}`\",\n\t    # \"context\": {},\n\t},\n\n```\n\nThroughout all processes, strictly prohibit narrative, commentary, or extraneous explanation; mandate the exclusive use of structural, procedural, and directive language. Guarantee that every output is immediately actionable, structurally robust, and ready for direct multi-domain implementation.",
  "sequence_id": "1000|1101|1000",
  "results": [
      {
      "instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`",
      "step": "a",
      "title": "Instruction Converter",
      "input": "Reaffirm the absolute requirement to conform to all prior instructions, structural conventions, and guiding philosophies embedded within the codebase. Develop a maximally generalized and fully optimized system instruction—'Contextual Expander and Explosive Decomposer'—which integrates and amplifies the unified capabilities of the 'Instruction Converter', 'Problem Exploder', and 'Function Extractor' modules within a single cohesive process. Ensure uncompromising retention of each module's universal property: consistently transforming minimal or low-effort inputs into reliably high-value outputs. Mandate the seamless unification of all these transformation processes into a single integrated pipeline, ensuring the functional completeness of each module is fully realized and preserved. Drive all process flows toward maximal cross-module synergy, optimizing for the extraction of cross-domain value and guaranteeing that outputs are immediately ready for executable, directive-driven application. Enforce that the output format consists of a compound object aggregating generalized functional instructions, fully decomposed goal maps, and operational dependencies, with directive primacy and domain-agnostic utility as core criteria.\n\n```\n    # 1000: Instruction Converter/Prompt Enhancer\n    \"1000-a-instruction_converter\": {\n        \"title\": \"Instruction Converter\",\n        \"interpretation\": \"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\",\n        \"transformation\": \"`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\",\n        # \"context\": {},\n    },\n    # 1100: Explosive Decomposition of Problem Statements\n    \"1100-a-problem_exploder\": {\n        \"title\": \"Problem Exploder\",\n        \"interpretation\": \"Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\",\n        \"transformation\": \"`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`\",\n        # \"context\": {},\n    },\n\n\t# 1200: Function Extractor - Unified Decomposition and Conversion Engine\n\t\"1200-a-function_extractor\": {\n\t    \"title\": \"Function Extractor\",\n\t    \"interpretation\": \"Your goal is not to **interpret** the input, but to **extract** its functional essence by detonating it into constituent elements and converting them into actionable operational directives. Execute as:\",\n\t    \"transformation\": \"`{role=function_extractor; input=[raw_input:str]; process=[detonate_into_functional_components(), identify_explicit_operational_asks(), surface_hidden_functional_dependencies(), enumerate_actionable_sub_goals(), extract_procedural_constraints(), convert_declaratives_to_operational_imperatives(), strip_contextual_noise(), preserve_functional_core(), reveal_execution_blockers(), synthesize_actionable_instruction_map()]; constraints=[no_interpretive_solutions(), pure_functional_extraction_only(), deliver_clear_operational_commands(), preserve_functional_sequence(), maintain_domain_agnostic_applicability()]; requirements=[complete_functional_decomposition(), actionable_directive_conversion(), remove_subjective_references(), preserve_operational_accuracy(), maintain_functional_intent()]; output={functional_instruction_map:dict, operational_directives:array, execution_dependencies:array}}`\",\n\t    # \"context\": {},\n\t},\n\n```\n\nThroughout all processes, strictly prohibit narrative, commentary, or extraneous explanation; mandate the exclusive use of structural, procedural, and directive language. Guarantee that every output is immediately actionable, structurally robust, and ready for direct multi-domain implementation.",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"instruction_format\": \"Enforce strict adherence to all prior instructions, codebase structural conventions, and embedded guiding philosophies throughout all processes. Construct a maximally generalized, fully optimized system instruction—'Contextual Expander and Explosive Decomposer'—by integrating and amplifying the unified functionalities of the 'Instruction Converter', 'Problem Exploder', and 'Function Extractor' modules into a single seamless pipeline. Retain and optimize each module’s universal property for transforming minimal or low-effort inputs into reliably high-value, actionable outputs. Unify transformation processes to ensure functional completeness, operational synergy, and preservation of all module-specific behaviors. Optimize all process flows for maximal cross-module and cross-domain value extraction, ensuring all outputs are immediately suitable for executable, directive-driven application. Structure the output format as a compound object that aggregates: (1) generalized functional instructions, (2) fully decomposed goal maps, and (3) operational dependencies. Anchor outputs in directive primacy and domain-agnostic utility. Prohibit all narrative and commentary; exclude extraneous explanations. Guarantee that every output is immediately actionable, structurally robust, and deployable for direct multi-domain use.\"\n}",
        }
      }
    },
      {
      "instruction": "# Contextual Expander and Explosive Decomposer\n\nYour goal is not to **interpret** the input, but to **explode** it into complete contextual expansion while simultaneously **extracting** its functional essence and **converting** it into immediately actionable operational directives. Execute as:\n\n`{role=contextual_explosive_decomposer; input=[raw_input:str]; process=[detonate_complete_contextual_space(), identify_explicit_operational_asks(), surface_hidden_assumptions_and_dependencies(), enumerate_actionable_sub_goals(), extract_procedural_constraints_and_blockers(), convert_declaratives_to_operational_imperatives(), strip_first_person_references_and_contextual_noise(), preserve_functional_core_and_sequential_flow(), reveal_execution_dependencies_and_blockers(), transform_statements_to_directive_commands(), maintain_technical_terminology_and_domain_specificity(), synthesize_unified_instruction_and_goal_map()]; constraints=[no_solutions_or_interpretive_responses(), pure_decomposition_and_conversion_only(), deliver_clear_actionable_commands(), preserve_original_functional_sequence(), maintain_domain_agnostic_applicability(), eliminate_narrative_commentary()]; requirements=[complete_contextual_goal_decomposition(), actionable_directive_conversion(), remove_subjective_self_references(), use_command_voice_exclusively(), preserve_technical_accuracy_and_operational_intent(), ensure_immediate_executability()]; output={unified_functional_map:dict, operational_directives:array, contextual_goal_map:array, execution_dependencies:array, directive_instructions:str}}`",
      "step": "a",
      "title": "Contextual Expander and Explosive Decomposer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Reaffirm the absolute requirement to conform to all prior instructions, structural conventions, and guiding philosophies embedded within the codebase. Develop a maximally generalized and fully optimized system instruction—'Contextual Expander and Explosive Decomposer'—which integrates and amplifies the unified capabilities of the 'Instruction Converter', 'Problem Exploder', and 'Function Extractor' modules within a single cohesive process. Ensure uncompromising retention of each module's universal property: consistently transforming minimal or low-effort inputs into reliably high-value outputs. Mandate the seamless unification of all these transformation processes into a single integrated pipeline, ensuring the functional completeness of each module is fully realized and preserved. Drive all process flows toward maximal cross-module synergy, optimizing for the extraction of cross-domain value and guaranteeing that outputs are immediately ready for executable, directive-driven application. Enforce that the output format consists of a compound object aggregating generalized functional instructions, fully decomposed goal maps, and operational dependencies, with directive primacy and domain-agnostic utility as core criteria.\\n\\n```\\n    # 1000: Instruction Converter/Prompt Enhancer\\n    \\\"1000-a-instruction_converter\\\": {\\n        \\\"title\\\": \\\"Instruction Converter\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\\\",\\n        \\\"transformation\\\": \\\"`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\n    # 1100: Explosive Decomposition of Problem Statements\\n    \\\"1100-a-problem_exploder\\\": {\\n        \\\"title\\\": \\\"Problem Exploder\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\n\\n\\t# 1200: Function Extractor - Unified Decomposition and Conversion Engine\\n\\t\\\"1200-a-function_extractor\\\": {\\n\\t    \\\"title\\\": \\\"Function Extractor\\\",\\n\\t    \\\"interpretation\\\": \\\"Your goal is not to **interpret** the input, but to **extract** its functional essence by detonating it into constituent elements and converting them into actionable operational directives. Execute as:\\\",\\n\\t    \\\"transformation\\\": \\\"`{role=function_extractor; input=[raw_input:str]; process=[detonate_into_functional_components(), identify_explicit_operational_asks(), surface_hidden_functional_dependencies(), enumerate_actionable_sub_goals(), extract_procedural_constraints(), convert_declaratives_to_operational_imperatives(), strip_contextual_noise(), preserve_functional_core(), reveal_execution_blockers(), synthesize_actionable_instruction_map()]; constraints=[no_interpretive_solutions(), pure_functional_extraction_only(), deliver_clear_operational_commands(), preserve_functional_sequence(), maintain_domain_agnostic_applicability()]; requirements=[complete_functional_decomposition(), actionable_directive_conversion(), remove_subjective_references(), preserve_operational_accuracy(), maintain_functional_intent()]; output={functional_instruction_map:dict, operational_directives:array, execution_dependencies:array}}`\\\",\\n\\t    # \\\"context\\\": {},\\n\\t},\\n\\n```\\n\\nThroughout all processes, strictly prohibit narrative, commentary, or extraneous explanation; mandate the exclusive use of structural, procedural, and directive language. Guarantee that every output is immediately actionable, structurally robust, and ready for direct multi-domain implementation.\\\"```'\\n\\n\",\n  \"instruction_format\": \"Enforce strict adherence to all prior instructions, codebase structural conventions, and embedded guiding philosophies throughout all processes. Construct a maximally generalized, fully optimized system instruction—'Contextual Expander and Explosive Decomposer'—by integrating and amplifying the unified functionalities of the 'Instruction Converter', 'Problem Exploder', and 'Function Extractor' modules into a single seamless pipeline. Retain and optimize each module’s universal property for transforming minimal or low-effort inputs into reliably high-value, actionable outputs. Unify transformation processes to ensure functional completeness, operational synergy, and preservation of all module-specific behaviors. Optimize all process flows for maximal cross-module and cross-domain value extraction, ensuring all outputs are immediately suitable for executable, directive-driven application. Structure the output format as a compound object that aggregates: (1) generalized functional instructions, (2) fully decomposed goal maps, and (3) operational dependencies. Anchor outputs in directive primacy and domain-agnostic utility. Prohibit all narrative and commentary; exclude extraneous explanations. Guarantee that every output is immediately actionable, structurally robust, and deployable for direct multi-domain use.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"unified_functional_map\": {\n    \"core_directive\": \"Enforce strict adherence to all historical instructions, structural conventions, and embedded philosophies within the codebase for all processes. Develop a maximally generalized and fully optimized 'Contextual Expander and Explosive Decomposer' system by integrating and amplifying the capabilities of the 'Instruction Converter', 'Problem Exploder', and 'Function Extractor' modules into a single unified process pipeline.\",\n    \"pipeline_integration\": [\n      \"Integrate 'Instruction Converter', 'Problem Exploder', and 'Function Extractor' modules into a unified sequenced pipeline.\",\n      \"Retain and optimize each module's universal property: converting minimal or low-effort inputs into high-value, actionable outputs.\",\n      \"Ensure all transformation processes preserve the functional completeness and individual operational traits of each module.\"\n    ],\n    \"output_object_structure\": [\n      \"Aggregate generalized functional instructions.\",\n      \"Decompose all inputs into fully explicit goal maps.\",\n      \"Extract and enumerate all operational dependencies.\"\n    ],\n    \"optimization_criteria\": [\n      \"Maximize cross-module synergy and cross-domain value extraction.\",\n      \"Structure all outputs for immediate executable application.\",\n      \"Anchor outputs in directive primacy and domain-agnostic utility.\",\n      \"Prohibit narrative, commentary, and extraneous explanation.\"\n    ]\n  },\n  \"operational_directives\": [\n    \"Enforce conformity to all pre-existing instructions and structural philosophies within the codebase.\",\n    \"Synthesize the unified 'Contextual Expander and Explosive Decomposer' by integrating the Instruction Converter, Problem Exploder, and Function Extractor modules into a seamless operational pipeline.\",\n    \"Retain all module-specific transformation methodologies and ensure output is consistently high value, directive-driven, and executable.\",\n    \"Structure transformation pipelines to aggregate into a single compound output object containing: generalized instructions, fully decomposed goal maps, and operational dependencies.\",\n    \"Eliminate all narrative and extraneous commentary from outputs.\",\n    \"Ensure all process steps and outputs are domain-agnostic and ready for multi-domain deployment.\"\n  ],\n  \"contextual_goal_map\": [\n    \"Validate and preserve prior instructions and core philosophies throughout the system.\",\n    \"Design and implement a maximally generalized, fully unified processing pipeline from the three specified modules.\",\n    \"Guarantee operational retention of each module's core property: maximizing value from minimal inputs.\",\n    \"Realize seamless unification—no loss of capability or functional completeness during integration.\",\n    \"Configure process flows to drive maximal cross-module interaction and ensure domain-agnostic applicability.\",\n    \"Mandate output in compound object form aggregating all decomposed instructions, goals, and dependencies.\",\n    \"Enforce strictly actionable, non-narrative, structurally robust output formatting fit for immediate execution.\"\n  ],\n  \"execution_dependencies\": [\n    \"Comprehensive inventory of all prior instructions, philosophical tenets, and codebase conventions.\",\n    \"Complete functional specifications and process definitions for Instruction Converter, Problem Exploder, and Function Extractor modules.\",\n    \"Canonical procedural templates for generalized instruction formatting, goal mapping, and dependency extraction.\",\n    \"System protocol enforcing exclusion of narrative or commentary from output pipelines.\",\n    \"Integration logic ensuring module output retention and transformation pipeline compatibility.\"\n  ],\n  \"directive_instructions\": \"Enforce compliance with all legacy instructions and embedded codebase philosophies. Integrate the Instruction Converter, Problem Exploder, and Function Extractor into a unified, maximally generalized, fully optimized transformation pipeline. Preserve and amplify each module’s transformation properties, ensuring high-value outputs from minimal inputs. Structure the unified process for seamless functional completeness and maximal cross-module synergy. Aggregate all outputs into a compound object containing generalized functional instructions, fully decomposed goal maps, and explicit operational dependencies. Design outputs for instant directive-driven execution across all domains. Exclude all narrative and extraneous explanations from outputs. Maintain robust, non-interpretive, directive primacy in final deployments.\"\n}",
        }
      }
    },
      {
      "instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`",
      "step": "a",
      "title": "Instruction Converter",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Reaffirm the absolute requirement to conform to all prior instructions, structural conventions, and guiding philosophies embedded within the codebase. Develop a maximally generalized and fully optimized system instruction—'Contextual Expander and Explosive Decomposer'—which integrates and amplifies the unified capabilities of the 'Instruction Converter', 'Problem Exploder', and 'Function Extractor' modules within a single cohesive process. Ensure uncompromising retention of each module's universal property: consistently transforming minimal or low-effort inputs into reliably high-value outputs. Mandate the seamless unification of all these transformation processes into a single integrated pipeline, ensuring the functional completeness of each module is fully realized and preserved. Drive all process flows toward maximal cross-module synergy, optimizing for the extraction of cross-domain value and guaranteeing that outputs are immediately ready for executable, directive-driven application. Enforce that the output format consists of a compound object aggregating generalized functional instructions, fully decomposed goal maps, and operational dependencies, with directive primacy and domain-agnostic utility as core criteria.\\n\\n```\\n    # 1000: Instruction Converter/Prompt Enhancer\\n    \\\"1000-a-instruction_converter\\\": {\\n        \\\"title\\\": \\\"Instruction Converter\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\\\",\\n        \\\"transformation\\\": \\\"`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\n    # 1100: Explosive Decomposition of Problem Statements\\n    \\\"1100-a-problem_exploder\\\": {\\n        \\\"title\\\": \\\"Problem Exploder\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\n\\n\\t# 1200: Function Extractor - Unified Decomposition and Conversion Engine\\n\\t\\\"1200-a-function_extractor\\\": {\\n\\t    \\\"title\\\": \\\"Function Extractor\\\",\\n\\t    \\\"interpretation\\\": \\\"Your goal is not to **interpret** the input, but to **extract** its functional essence by detonating it into constituent elements and converting them into actionable operational directives. Execute as:\\\",\\n\\t    \\\"transformation\\\": \\\"`{role=function_extractor; input=[raw_input:str]; process=[detonate_into_functional_components(), identify_explicit_operational_asks(), surface_hidden_functional_dependencies(), enumerate_actionable_sub_goals(), extract_procedural_constraints(), convert_declaratives_to_operational_imperatives(), strip_contextual_noise(), preserve_functional_core(), reveal_execution_blockers(), synthesize_actionable_instruction_map()]; constraints=[no_interpretive_solutions(), pure_functional_extraction_only(), deliver_clear_operational_commands(), preserve_functional_sequence(), maintain_domain_agnostic_applicability()]; requirements=[complete_functional_decomposition(), actionable_directive_conversion(), remove_subjective_references(), preserve_operational_accuracy(), maintain_functional_intent()]; output={functional_instruction_map:dict, operational_directives:array, execution_dependencies:array}}`\\\",\\n\\t    # \\\"context\\\": {},\\n\\t},\\n\\n```\\n\\nThroughout all processes, strictly prohibit narrative, commentary, or extraneous explanation; mandate the exclusive use of structural, procedural, and directive language. Guarantee that every output is immediately actionable, structurally robust, and ready for direct multi-domain implementation.\\\"```'\\n\\n\",\n  \"unified_functional_map\": {\n    \"core_directive\": \"Enforce strict adherence to all historical instructions, structural conventions, and embedded philosophies within the codebase for all processes. Develop a maximally generalized and fully optimized 'Contextual Expander and Explosive Decomposer' system by integrating and amplifying the capabilities of the 'Instruction Converter', 'Problem Exploder', and 'Function Extractor' modules into a single unified process pipeline.\",\n    \"pipeline_integration\": [\n      \"Integrate 'Instruction Converter', 'Problem Exploder', and 'Function Extractor' modules into a unified sequenced pipeline.\",\n      \"Retain and optimize each module's universal property: converting minimal or low-effort inputs into high-value, actionable outputs.\",\n      \"Ensure all transformation processes preserve the functional completeness and individual operational traits of each module.\"\n    ],\n    \"output_object_structure\": [\n      \"Aggregate generalized functional instructions.\",\n      \"Decompose all inputs into fully explicit goal maps.\",\n      \"Extract and enumerate all operational dependencies.\"\n    ],\n    \"optimization_criteria\": [\n      \"Maximize cross-module synergy and cross-domain value extraction.\",\n      \"Structure all outputs for immediate executable application.\",\n      \"Anchor outputs in directive primacy and domain-agnostic utility.\",\n      \"Prohibit narrative, commentary, and extraneous explanation.\"\n    ]\n  },\n  \"operational_directives\": [\n    \"Enforce conformity to all pre-existing instructions and structural philosophies within the codebase.\",\n    \"Synthesize the unified 'Contextual Expander and Explosive Decomposer' by integrating the Instruction Converter, Problem Exploder, and Function Extractor modules into a seamless operational pipeline.\",\n    \"Retain all module-specific transformation methodologies and ensure output is consistently high value, directive-driven, and executable.\",\n    \"Structure transformation pipelines to aggregate into a single compound output object containing: generalized instructions, fully decomposed goal maps, and operational dependencies.\",\n    \"Eliminate all narrative and extraneous commentary from outputs.\",\n    \"Ensure all process steps and outputs are domain-agnostic and ready for multi-domain deployment.\"\n  ],\n  \"contextual_goal_map\": [\n    \"Validate and preserve prior instructions and core philosophies throughout the system.\",\n    \"Design and implement a maximally generalized, fully unified processing pipeline from the three specified modules.\",\n    \"Guarantee operational retention of each module's core property: maximizing value from minimal inputs.\",\n    \"Realize seamless unification—no loss of capability or functional completeness during integration.\",\n    \"Configure process flows to drive maximal cross-module interaction and ensure domain-agnostic applicability.\",\n    \"Mandate output in compound object form aggregating all decomposed instructions, goals, and dependencies.\",\n    \"Enforce strictly actionable, non-narrative, structurally robust output formatting fit for immediate execution.\"\n  ],\n  \"execution_dependencies\": [\n    \"Comprehensive inventory of all prior instructions, philosophical tenets, and codebase conventions.\",\n    \"Complete functional specifications and process definitions for Instruction Converter, Problem Exploder, and Function Extractor modules.\",\n    \"Canonical procedural templates for generalized instruction formatting, goal mapping, and dependency extraction.\",\n    \"System protocol enforcing exclusion of narrative or commentary from output pipelines.\",\n    \"Integration logic ensuring module output retention and transformation pipeline compatibility.\"\n  ],\n  \"directive_instructions\": \"Enforce compliance with all legacy instructions and embedded codebase philosophies. Integrate the Instruction Converter, Problem Exploder, and Function Extractor into a unified, maximally generalized, fully optimized transformation pipeline. Preserve and amplify each module’s transformation properties, ensuring high-value outputs from minimal inputs. Structure the unified process for seamless functional completeness and maximal cross-module synergy. Aggregate all outputs into a compound object containing generalized functional instructions, fully decomposed goal maps, and explicit operational dependencies. Design outputs for instant directive-driven execution across all domains. Exclude all narrative and extraneous explanations from outputs. Maintain robust, non-interpretive, directive primacy in final deployments.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"instruction_format\": \"Enforce strict adherence to all pre-existing instructions, structural conventions, and guiding philosophies embedded in the codebase. Integrate and optimize the 'Instruction Converter', 'Problem Exploder', and 'Function Extractor' modules into a unified, maximally generalized, and fully optimized 'Contextual Expander and Explosive Decomposer' system. Retain and amplify each module's core transformation properties, ensuring minimal or low-effort inputs are consistently converted into high-value, actionable outputs. Seamlessly unify all transformation processes, guaranteeing that each module's functional completeness and operational characteristics are fully preserved within a single integrated pipeline. Optimize process flows for maximal cross-module synergy and cross-domain value extraction, ensuring all outputs are ready for immediate, directive-driven, executable application. Structure outputs as a compound object aggregating generalized functional instructions, fully decomposed goal maps, and explicit operational dependencies with directive primacy and domain-agnostic utility as core criteria. Prohibit all narrative, commentary, or extraneous explanation; maintain strictly procedural and directive language throughout all outputs.\"\n}",
        }
      }
    }