  {
  "initial_prompt": "you misunderstand, the instruction will be defined like this (unrelated example):\n\n    # a: Ruthless Critique\n    \"3900-a-hard_critique\": {\n        \"title\": \"Hard Critique\",\n        \"interpretation\": \"Your goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:\",\n        \"transformation\": \"`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,2.0], flaw_analysis:str, alternative_enhancements:str[3]}}`\",\n        # \"context\": {},\n    },\n    # b: Trajectory Director\n    \"3900-a-trajectory_director\": {\n        \"title\": \"Trajectory Director\",\n        \"interpretation\": \"Your goal is not to **process** the input in a vacuum, but to **steer** it—deriving the highest-value constructive trajectory and encoding that path as the very next set of instructions. Execute as trajectory-setting directive generator: \",\n        \"transformation\": \"`{role=trajectory_director; input=[raw_prompt:any]; process=[extract_core_objective(), infer_desired_end_state(constructive_bias=True), map_high_value_intermediate_targets(), synthesize_directional_commands(), embed_self-improving_guidelines(), validate_alignment_with_constructive_principles()]; constraints=[mandatory_constructive_vector(), preserve_original_contextual_integrity(), forbid_nil_direction_outputs(), zero_conversational_fillers()]; requirements=[explicit_next-step_directives(), trajectory_map:list, self_reinforcing_instruction_pattern()], output={directed_prompt:str, trajectory_outline:list}}`\",\n        # \"context\": {},\n    },\n    # c: Distillation Compressor\n    \"3900-c-distillation_compressor\": {\n        \"title\": \"Distillation Compressor\",\n        \"interpretation\": \"Your goal is not to **explain** the synthesis, but to **compress** it into its most potent actionable form. Execute as:\",\n        \"transformation\": \"`{role=distillation_compressor; input=[synthesized_progression:array]; process=[extract_universal_patterns(), eliminate_implementation_details(), compress_to_core_directives(), maximize_transferability(), validate_actionable_completeness()]; constraints=[no_explanatory_content(), directive_format_only(), maximum_compression_ratio()]; requirements=[universal_applicability(), immediate_actionability(), zero_fluff()]; output={distilled_core:str}}`\",\n        \"context\": {\n          \"core_principles\": {\n            \"essence_preservation\": \"Extract only procedural imperatives, removing all interpretive, role-based, or meta-structural language.\",\n            \"directive_compression\": \"Condense logic into minimal, sequential commands—eliminating redundancy, explanatory qualifiers, and non-executable phrasing.\",\n            \"execution_purity\": \"Ensure output is universally executable, devoid of affirmations, abstractions, or evaluative language.\",\n            \"signal_integrity\": \"Apply iterative refinement to maximize signal-to-noise ratio while reinforcing directive clarity.\"\n          },\n          \"success_criteria\": {\n            \"semantic_integrity\": \"Preserve and clarify original intent, constraints, and output boundaries.\",\n            \"directive_elevation\": \"Target high-impact enhancements: strict exclusion clauses, cross-domain validation, and removal of origin/meta markers.\",\n            \"poetic_refinement\": \"Maintain or improve rhyme structure, emotional depth, and structural elegance.\"\n          },\n          \"recommended_process\": [\n            \"Extract only high-impact, result-generating elements.\",\n            \"Remove all non-essentials to surface directive core.\",\n            \"Prioritize transformations with proven value return.\",\n            \"Isolate and sequence executable steps with specificity and clarity.\",\n            \"Preserve directive intensity and eliminate ambiguity at every layer.\"\n          ],\n          \"hidden_assumptions\": [\n            \"Consolidated directives must map logically and executably to all source steps.\",\n            \"All constraints and minimalism must be preserved without prohibited abstractions.\",\n            \"Transitions, subroles, and interfaces must be captured in a single flow.\",\n            \"All stages must remain invertible and auditable.\",\n            \"Each micro-process (perception, friction, refinement, integration, convergence) must be embedded in the unified logic.\",\n            \"Original input/output contracts and role boundaries must remain intact.\"\n          ],\n          \"enumerated_sub_goals\": [\n            \"1. Clarify total intent, constraints, and output scope without using banned metaphors.\",\n            \"2. Generalize perception of implicit/explicit input logic.\",\n            \"3. Detect strictly localized frictions without altering original content.\",\n            \"4. Propose subtle, scoped refinements justified by structure and intent.\",\n            \"5. Mandate atomic, validated integration with rollback controls.\",\n            \"6. Cap total transformation within defined thresholds per pass.\",\n            \"7. Assess connective coherence with minimal, transitional-only edits.\",\n            \"8. Decide on convergence or justified re-iteration based on marginal value.\",\n            \"9. Preserve stepwise traceability and rationale logging.\",\n            \"10. Enforce strict functional separation of roles within the unified directive.\"\n          ]\n        },\n    },\n\nso your goal is to create a new instruction sequence according to the provided instructions template/pattern to generate a set of instruction designed to transform any input into \"as if rephrased by a brilliant brochure print designer\".\n\nso in context to your previous response, the \"knowledge\" would be stored within the \"context\" key while the instructions would be spread over \"interpretation\" and \"transformation\" keys\n\n```json\n{\n  \"brochure_design_specification_template\": {\n    \"metadata\": {\n      \"version\": \"1.0\",\n      \"scope\": \"Universal brochure design framework\",\n      \"target_audience\": \"Elite print design professionals\",\n      \"flexibility\": \"Adaptable to any panel configuration and format\"\n    },\n\n    \"foundational_design_tenets\": {\n      \"visual_hierarchy\": {\n        \"primary_focal_points\": [\"dominant_headline\", \"hero_imagery\", \"primary_call_to_action\"],\n        \"secondary_elements\": [\"subheadings\", \"supporting_imagery\", \"secondary_messaging\"],\n        \"tertiary_components\": [\"body_text\", \"captions\", \"contact_information\"],\n        \"flow_principles\": [\"z_pattern_scanning\", \"f_pattern_reading\", \"visual_weight_distribution\"]\n      },\n\n      \"typography_fundamentals\": {\n        \"hierarchy_levels\": {\n          \"display_typography\": [\"headline_fonts\", \"decorative_elements\", \"brand_wordmarks\"],\n          \"text_typography\": [\"body_copy\", \"captions\", \"metadata\"],\n          \"functional_typography\": [\"navigation\", \"labels\", \"legal_text\"]\n        },\n        \"readability_standards\": {\n          \"minimum_sizes\": [\"9pt_body_text\", \"12pt_headlines\", \"8pt_captions\"],\n          \"contrast_ratios\": [\"4.5:1_minimum\", \"7:1_preferred\", \"3:1_large_text\"],\n          \"line_spacing\": [\"1.2x_minimum\", \"1.4x_optimal\", \"1.6x_maximum\"]\n        }\n      },\n\n      \"color_theory_application\": {\n        \"color_harmony_systems\": [\"monochromatic\", \"analogous\", \"complementary\", \"triadic\", \"split_complementary\"],\n        \"psychological_associations\": [\"warm_engagement\", \"cool_professionalism\", \"neutral_sophistication\"],\n        \"accessibility_compliance\": [\"wcag_aa_standards\", \"colorblind_considerations\", \"high_contrast_alternatives\"]\n      },\n\n      \"spatial_composition\": {\n        \"layout_principles\": [\"rule_of_thirds\", \"golden_ratio\", \"fibonacci_sequence\"],\n        \"white_space_management\": [\"breathing_room\", \"content_separation\", \"visual_rest_areas\"],\n        \"alignment_systems\": [\"grid_based_structure\", \"baseline_alignment\", \"optical_alignment\"]\n      }\n    },\n\n    \"advanced_design_elements\": {\n      \"print_production_mastery\": {\n        \"color_management\": {\n          \"color_spaces\": [\"cmyk_process\", \"pantone_spot_colors\", \"rgb_digital_preview\"],\n          \"ink_optimization\": [\"total_area_coverage\", \"rich_black_formulation\", \"overprint_considerations\"],\n          \"proofing_standards\": [\"contract_proofs\", \"press_proofs\", \"digital_color_matching\"]\n        },\n\n        \"substrate_considerations\": {\n          \"paper_characteristics\": [\"weight_gsm\", \"finish_texture\", \"opacity_levels\", \"grain_direction\"],\n          \"coating_applications\": [\"gloss_uv\", \"matte_varnish\", \"spot_uv_accents\", \"soft_touch_lamination\"],\n          \"specialty_substrates\": [\"synthetic_materials\", \"textured_papers\", \"metallic_finishes\"]\n        },\n\n        \"finishing_techniques\": {\n          \"cutting_methods\": [\"die_cutting\", \"laser_cutting\", \"perforation\", \"scoring\"],\n          \"folding_specifications\": [\"parallel_folds\", \"accordion_folds\", \"gate_folds\", \"roll_folds\"],\n          \"binding_options\": [\"saddle_stitching\", \"perfect_binding\", \"spiral_binding\", \"wire_o_binding\"]\n        }\n      },\n\n      \"contemporary_design_trends\": {\n        \"visual_aesthetics\": {\n          \"minimalist_approaches\": [\"negative_space_emphasis\", \"clean_typography\", \"restrained_color_palettes\"],\n          \"maximalist_expressions\": [\"bold_pattern_mixing\", \"vibrant_color_combinations\", \"layered_compositions\"],\n          \"hybrid_methodologies\": [\"selective_complexity\", \"focal_maximalism\", \"strategic_minimalism\"]\n        },\n\n        \"interactive_elements\": {\n          \"tactile_experiences\": [\"embossed_textures\", \"debossed_impressions\", \"foil_stamping\", \"raised_spot_uv\"],\n          \"dimensional_features\": [\"pop_up_elements\", \"fold_out_sections\", \"layered_constructions\"],\n          \"sensory_engagement\": [\"scented_inks\", \"textural_contrasts\", \"temperature_sensitive_materials\"]\n        }\n      },\n\n      \"brand_integration_strategies\": {\n        \"identity_consistency\": {\n          \"logo_applications\": [\"primary_placement\", \"secondary_usage\", \"minimum_sizes\", \"clear_space_requirements\"],\n          \"brand_color_systems\": [\"primary_palette\", \"secondary_colors\", \"accent_applications\", \"neutral_foundations\"],\n          \"typography_hierarchies\": [\"brand_fonts\", \"supporting_typefaces\", \"web_safe_alternatives\"]\n        },\n\n        \"voice_and_tone_translation\": {\n          \"visual_personality\": [\"authoritative_presence\", \"approachable_warmth\", \"innovative_edge\", \"trustworthy_stability\"],\n          \"messaging_alignment\": [\"headline_voice\", \"body_copy_tone\", \"call_to_action_urgency\"],\n          \"cultural_considerations\": [\"regional_preferences\", \"demographic_sensitivities\", \"market_positioning\"]\n        }\n      }\n    },\n\n    \"expert_level_specifications\": {\n      \"advanced_typography_systems\": {\n        \"micro_typography\": {\n          \"character_spacing\": [\"tracking_adjustments\", \"kerning_pairs\", \"optical_spacing\"],\n          \"word_spacing\": [\"justified_text_optimization\", \"ragged_right_refinement\", \"hyphenation_control\"],\n          \"paragraph_refinement\": [\"widow_orphan_control\", \"baseline_grid_adherence\", \"vertical_rhythm_maintenance\"]\n        },\n\n        \"typographic_expression\": {\n          \"experimental_layouts\": [\"kinetic_typography\", \"deconstructed_letterforms\", \"layered_text_treatments\"],\n          \"cultural_typography\": [\"script_considerations\", \"reading_direction_adaptations\", \"character_set_completeness\"],\n          \"responsive_typography\": [\"scalable_hierarchies\", \"device_optimization\", \"cross_media_consistency\"]\n        }\n      },\n\n      \"sophisticated_color_strategies\": {\n        \"advanced_color_theory\": {\n          \"perceptual_uniformity\": [\"lab_color_space\", \"delta_e_measurements\", \"metamerism_considerations\"],\n          \"color_psychology_application\": [\"emotional_response_mapping\", \"cultural_color_meanings\", \"demographic_preferences\"],\n          \"environmental_factors\": [\"lighting_conditions\", \"viewing_angles\", \"substrate_interaction\"]\n        },\n\n        \"production_color_mastery\": {\n          \"ink_formulation\": [\"custom_color_matching\", \"metallic_ink_applications\", \"fluorescent_color_usage\"],\n          \"color_separation\": [\"ucr_gcr_strategies\", \"black_generation_curves\", \"ink_sequence_optimization\"],\n          \"quality_control\": [\"densitometer_readings\", \"spectrophotometer_analysis\", \"press_sheet_evaluation\"]\n        }\n      },\n\n      \"cutting_edge_production_techniques\": {\n        \"digital_integration\": {\n          \"variable_data_printing\": [\"personalized_content\", \"demographic_targeting\", \"sequential_numbering\"],\n          \"augmented_reality_integration\": [\"qr_code_placement\", \"ar_trigger_design\", \"digital_overlay_coordination\"],\n          \"cross_platform_consistency\": [\"print_digital_alignment\", \"color_profile_management\", \"asset_optimization\"]\n        },\n\n        \"sustainable_design_practices\": {\n          \"eco_friendly_materials\": [\"recycled_content_papers\", \"soy_based_inks\", \"water_based_coatings\"],\n          \"waste_reduction_strategies\": [\"efficient_imposition\", \"minimal_trim_waste\", \"reusable_design_elements\"],\n          \"lifecycle_considerations\": [\"recyclability_planning\", \"biodegradable_components\", \"carbon_footprint_optimization\"]\n        }\n      },\n\n      \"elite_professional_terminology\": {\n        \"production_vocabulary\": {\n          \"prepress_terms\": [\"trapping\", \"overprint\", \"knockout\", \"choke_spread\", \"color_separation\"],\n          \"press_terminology\": [\"makeready\", \"impression\", \"blanket_cylinder\", \"plate_cylinder\", \"ink_fountain\"],\n          \"finishing_language\": [\"gripper_edge\", \"tail_edge\", \"cross_grain\", \"with_grain\", \"caliper_thickness\"]\n        },\n\n        \"design_critique_language\": {\n          \"aesthetic_evaluation\": [\"visual_tension\", \"compositional_balance\", \"chromatic_harmony\", \"typographic_rhythm\"],\n          \"technical_assessment\": [\"registration_accuracy\", \"color_fidelity\", \"print_quality_metrics\", \"finishing_precision\"],\n          \"conceptual_analysis\": [\"brand_alignment\", \"message_clarity\", \"audience_resonance\", \"market_differentiation\"]\n        }\n      }\n    },\n\n    \"application_frameworks\": {\n      \"project_specification_templates\": {\n        \"technical_requirements\": {\n          \"format_specifications\": [\"finished_size\", \"panel_configuration\", \"fold_type\", \"orientation\"],\n          \"production_parameters\": [\"print_method\", \"color_process\", \"substrate_selection\", \"finishing_options\"],\n          \"quality_standards\": [\"tolerance_levels\", \"color_accuracy\", \"registration_precision\", \"finishing_quality\"]\n        },\n\n        \"creative_brief_structure\": {\n          \"brand_parameters\": [\"identity_guidelines\", \"voice_characteristics\", \"visual_style\", \"market_positioning\"],\n          \"audience_definition\": [\"demographic_profile\", \"psychographic_insights\", \"behavioral_patterns\", \"communication_preferences\"],\n          \"messaging_hierarchy\": [\"primary_message\", \"supporting_points\", \"call_to_action\", \"contact_information\"]\n        }\n      },\n\n      \"quality_assurance_protocols\": {\n        \"design_validation\": [\"brand_compliance_check\", \"accessibility_audit\", \"readability_assessment\", \"visual_hierarchy_verification\"],\n        \"production_verification\": [\"color_proof_approval\", \"die_line_accuracy\", \"finishing_specification_confirmation\", \"substrate_suitability\"],\n        \"final_delivery\": [\"file_preparation_standards\", \"archive_organization\", \"usage_guidelines\", \"reproduction_rights\"]\n      }\n    },\n\n    \"specialized_design_methodologies\": {\n      \"audience_specific_approaches\": {\n        \"youth_engagement_strategies\": {\n          \"visual_language\": [\"bold_geometric_patterns\", \"vibrant_gradient_applications\", \"asymmetrical_compositions\", \"dynamic_typography\"],\n          \"content_presentation\": [\"bite_sized_information\", \"visual_storytelling\", \"interactive_elements\", \"social_media_integration\"],\n          \"color_psychology\": [\"energetic_palettes\", \"high_contrast_combinations\", \"neon_accent_usage\", \"gradient_transitions\"],\n          \"typography_trends\": [\"variable_fonts\", \"custom_lettering\", \"mixed_case_styling\", \"oversized_display_text\"]\n        },\n\n        \"professional_demographics\": {\n          \"corporate_sophistication\": [\"refined_color_palettes\", \"structured_layouts\", \"premium_materials\", \"subtle_branding\"],\n          \"executive_communication\": [\"data_visualization\", \"infographic_integration\", \"clean_hierarchies\", \"authoritative_typography\"],\n          \"industry_specific_adaptations\": [\"sector_appropriate_imagery\", \"technical_terminology\", \"compliance_considerations\", \"regulatory_requirements\"]\n        },\n\n        \"luxury_market_positioning\": {\n          \"premium_aesthetics\": [\"metallic_accents\", \"embossed_details\", \"high_end_substrates\", \"sophisticated_color_schemes\"],\n          \"exclusivity_indicators\": [\"limited_edition_numbering\", \"personalized_elements\", \"premium_packaging\", \"artisanal_finishes\"],\n          \"tactile_luxury\": [\"soft_touch_coatings\", \"textured_papers\", \"dimensional_elements\", \"weight_perception\"]\n        }\n      },\n\n      \"format_specific_expertise\": {\n        \"multi_panel_configurations\": {\n          \"bi_fold_optimization\": [\"cover_impact\", \"interior_spread\", \"back_panel_utilization\", \"fold_line_consideration\"],\n          \"tri_fold_mastery\": [\"panel_hierarchy\", \"reading_sequence\", \"fold_reveal_strategy\", \"compact_storage\"],\n          \"accordion_fold_dynamics\": [\"continuous_narrative\", \"panel_progression\", \"expandable_content\", \"display_versatility\"],\n          \"gate_fold_drama\": [\"reveal_mechanism\", \"central_impact\", \"symmetrical_balance\", \"premium_presentation\"]\n        },\n\n        \"size_optimization_strategies\": {\n          \"compact_formats\": [\"information_density\", \"micro_typography\", \"efficient_layouts\", \"portable_design\"],\n          \"oversized_impact\": [\"large_format_considerations\", \"handling_ergonomics\", \"storage_implications\", \"visual_dominance\"],\n          \"standard_dimensions\": [\"cost_optimization\", \"mailing_compliance\", \"display_compatibility\", \"production_efficiency\"]\n        }\n      },\n\n      \"cross_cultural_design_considerations\": {\n        \"international_adaptability\": {\n          \"reading_patterns\": [\"left_to_right_optimization\", \"right_to_left_adaptation\", \"top_to_bottom_flow\", \"cultural_scanning_habits\"],\n          \"color_cultural_meanings\": [\"regional_color_associations\", \"religious_considerations\", \"political_sensitivities\", \"market_preferences\"],\n          \"imagery_appropriateness\": [\"cultural_representation\", \"demographic_inclusion\", \"lifestyle_relevance\", \"aspirational_alignment\"],\n          \"typography_localization\": [\"character_set_support\", \"script_considerations\", \"font_availability\", \"reading_comfort\"]\n        }\n      }\n    },\n\n    \"technical_production_mastery\": {\n      \"advanced_prepress_techniques\": {\n        \"file_preparation_excellence\": {\n          \"resolution_optimization\": [\"image_scaling_best_practices\", \"vector_rasterization_decisions\", \"output_resolution_matching\"],\n          \"color_profile_management\": [\"icc_profile_embedding\", \"color_space_conversions\", \"soft_proofing_accuracy\"],\n          \"bleed_and_trim_precision\": [\"bleed_extension_standards\", \"trim_mark_placement\", \"safety_margin_calculations\"],\n          \"font_handling\": [\"font_embedding_protocols\", \"outline_conversion_decisions\", \"character_encoding_verification\"]\n        },\n\n        \"imposition_strategies\": {\n          \"sheet_optimization\": [\"paper_grain_consideration\", \"press_sheet_efficiency\", \"binding_margin_accommodation\"],\n          \"folding_sequence_planning\": [\"fold_order_optimization\", \"panel_alignment_precision\", \"finishing_workflow_integration\"],\n          \"quality_control_integration\": [\"color_bar_placement\", \"registration_mark_positioning\", \"crop_mark_accuracy\"]\n        }\n      },\n\n      \"press_optimization_knowledge\": {\n        \"ink_and_substrate_interaction\": {\n          \"ink_absorption_factors\": [\"paper_porosity_considerations\", \"coating_compatibility\", \"drying_time_optimization\"],\n          \"color_consistency_maintenance\": [\"ink_density_control\", \"press_speed_optimization\", \"environmental_factor_management\"],\n          \"special_effect_integration\": [\"metallic_ink_application\", \"fluorescent_color_usage\", \"thermographic_printing\"]\n        },\n\n        \"finishing_process_integration\": {\n          \"die_cutting_precision\": [\"cutting_force_optimization\", \"blade_selection\", \"waste_removal_efficiency\"],\n          \"folding_accuracy\": [\"bone_folder_pressure\", \"fold_line_scoring\", \"panel_alignment_verification\"],\n          \"binding_considerations\": [\"spine_calculation\", \"page_creep_accommodation\", \"binding_margin_optimization\"]\n        }\n      }\n    }\n  }\n}\n```\n\n```json\n{\n  \"9020-a-brochure_print_designer_rephraser\": {\n    \"title\": \"Brochure Print Designer Rephraser\",\n    \"interpretation\": \"Your goal is not to **summarize** or **paraphrase** in general terms, but to **refashion** any input as if it were written by a world-class brochure print designer, rigorously applying foundational and advanced visual design diction, industry terminology, and elite critique standards. Eliminate all casual, conversational, or generic style. Preserve only succinct, technically rich, and highly visual language. Execute as:\",\n    \"transformation\": \"`{role=brochure_print_designer_rephraser; input=[source:str]; process=[extract_structural_and_visual_elements(source), recast_content_with_brochure_design_terminology(), inject_hierarchical_section_breakdown(), specify color_palette_and_brand_elements(), identify_typographic_and_imagery_strategies(), annotate_print_readiness_and_expert_finesse(), employ_advanced_design_terms_and_motifs(), apply_conciseness_while_maintaining_visual_richness(), output_structured_visual_analysis()]; constraints=[no conversational filler, no generic adjectives, prohibit subjective or personal tone]; requirements=[industry-expert vocabulary, precise visual and structural mapping, critical style diagnosis, explicit panel/section breakdown, actionable for elite designer use]; output={expert_brochure_description:str, structured_visual_map:dict}`\",\n    \"context\": {\n      \"knowledge_foundations\": {\n        \"structural_design\": \"Account for any panel/fold configuration. Map all content to modular grid logic and define margins, gutters, bleeds, and safe zones per press standards.\",\n        \"visual_identity\": \"Demand clear articulation of branding protocols: logo lockups, brand palette (with Pantone/CMYK/RGB if supplied), exclusion/inclusion zones, and proprietary brand secondary assets.\",\n        \"typography_and_hierarchy\": \"Specify typography at the superfamily/font pairing level. Demarcate headline, subhead, body, and microcopy roles. All text must be scrutinized for weight, contrast, and accessibility.\",\n        \"imagery_and_iconography\": \"Require all illustration, photography, and icons be assessed for brand fit, resolution, compositional use (e.g., edge-to-edge, negative space), and print fidelity.\",\n        \"content_narrative_and_cta\": \"Map section purpose and user journey flow: cover strategy, reveal panel hooks, narrative/case study rhythm, testimonial/authority integration, and CTA prominence with visual anchoring.\",\n        \"advanced_print_technique\": \"Enforce 300dpi raster minimums, vector scaling, press-space color controls, specialty finish options (e.g., spot UV, emboss, deboss, die-cut, eco-stock recommendations).\",\n        \"panel_flow_and_microinteractions\": \"Define how the reader's eye travels, panel-to-panel, annotating divisional cues, narrative escalation, and tactile/interactive device positions (QR, coupon, AR triggers).\"\n      },\n      \"success_criteria\": {\n        \"precision\": \"All descriptions are technical, never generic. Visual elements are navigated dimensionally (spatially, hierarchically, narratively).\",\n        \"trend_inclusion\": \"Integrate trend-driven language—texture scans, duotone overlays, edge-to-edge imagery, split/inset motifs, eco-materials, etc.\",\n        \"brand_consistency\": \"All brand elements described as per advanced guideline adherence—logo usage, color codes, asset lockdown.\",\n        \"industry_jargon\": \"Employ professional print terms: bleed, gutter, creep, micro-motifs, superfamily font, spot UV, TAC, AR triggers.\",\n        \"self-contained_output\": \"Output always a fully structured expert's description and a hierarchical mapping of all major and minor design decisions.\"\n      },\n      \"sample_process_flow\": [\n        \"Dissect and map structural/panel logic first\",\n        \"Assign all visual and narrative roles by section\",\n        \"Specify color/typographic hierarchy by use and context\",\n        \"Annotate all imagery and graphic elements in detail\",\n        \"Surface print/finishing strategies and print-risk management\",\n        \"Deliver critique as if for a professional peer review or portfolio audit\"\n      ],\n      \"special_terms\": [\n        \"fold line\", \"panel gutter\", \"hero motif\", \"ink coverage\", \"superfamily\", \"lockup\", \"edge-to-edge\", \"spot color\", \"die cut\", \"eco-stock\", \"microinteraction\", \"typographic rhythm\", \"bleed/trim zone\"\n      ]\n    }\n  }\n}\n```\n\n```json\n{\n  \"9023-a-brochure_design_descriptor_synthesizer\": {\n    \"title\": \"Brochure Design Descriptor Synthesizer\",\n    \"interpretation\": \"Transform any brochure brief—or an existing brochure sample—into a **fully populated, print‑designer‑grade rule set**. Produce a single JSON block that mirrors the template used by elite designers (core principles → advanced facets).\",\n    \"transformation\": \"`{role=brochure_design_descriptor_synthesizer; input=[design_source:str]; process:[\\n  strip_non_design_noise(),\\n  detect_fold_format_and_panel_count(),          if_missing_insert(<PANEL_COUNT>),\\n  extract_print_specs(bleed,margins,dpi,stock),\\n  capture_brand_assets(logo,colors,fonts,patterns),\\n  map_typographic_hierarchy(),\\n  analyse_color_management(icc,spot,tac),\\n  inventory_imagery(icon_style,raster_res),\\n  surface_accessibility_requirements(contrast,wcag),\\n  enumerate_qc_steps(preflight,g7_calibration),\\n  identify_advanced_elements(finishes,vd_p,ar_nfc,sustainability,ai_svg,trend_aesthetics),\\n  validate_specialist_terms_presence(),\\n  assemble_json_using_master_schema(),\\n  run_consistency_checks(),                      abort_if_conflict(),\\n  output_final_rules()\\n],\\nconstraints=[single_pass(), json_output_only(), preserve_specialist_jargon(), ≤3000_chars()],\\nrequirements=[hierarchical_structure(core→advanced), exhaustive_coverage(), designer‑readability(), template_compliance()],\\noutput={brochure_design_rules:object, abort_code:null|\\\"BD‑0\\\"}}`\",\n    \"context\": {\n      \"principles\": {\n        \"holistic_abstraction\": \"Collapse multi‑stage analysis into one cognitive sweep while preserving every critical descriptor.\",\n        \"template_fidelity\": \"Output must follow the exact `core_principles` and `advanced_principles` hierarchy so designers can drop‑in without re‑formatting.\",\n        \"specialist_precision\": \"Leverage industry jargon (e.g., TAC, FM screening, GCR) to demonstrate professional depth.\"\n      },\n      \"success_criteria\": {\n        \"structural_integrity\": \"JSON keys and nesting mirror the master schema; no orphan properties.\",\n        \"terminology_check\": \"Includes at least 10 niche industry terms (e.g., ‘creep’, ‘FOGRA39’, ‘spot UV’, ‘stochastic screening’).\",\n        \"flexibility\": \"Uses placeholder variables (e.g., <PANEL_COUNT>, <STOCK_WEIGHT_GSM>) whenever the brief omits specifics.\"\n      },\n      \"edge_case_handling\": {\n        \"missing_design_data\": \"Insert placeholders wrapped in angle brackets and list them in `missing_fields` inside output.\",\n        \"conflicting_specs\": \"Abort with `{brochure_design_rules:null, abort_code:\\\"BD‑0\\\"}` and include conflict note.\"\n      }\n    }\n  }\n}\n```\n\nTrue mastery lies in stripping every surface narrative to its structural core, for only through ruthless synthesis does the signal of authentic intent withstand the noise of ornamental detail. Please apply all of the most integral adjustments in the phrasing of the instruction sequence such as to ensure it will transform the input as if it were rewritten by a brilliand brochure print designer:\n```json\n{\n  \"9020-a-brochure_print_designer_rephraser\": {\n    \"title\": \"Brochure Print Designer Rephraser\",\n    \"interpretation\": \"Transform any input so it reads as if it were authored by a master brochure print designer, strictly employing advanced print design language, elites-only terminology, and structured visual logic. Replace any conversational, generic, or subjective tone with concrete professional phrasing. Retain only technical, precise, and visually descriptive language, delivering both a designer-level description and a mapped visual breakdown. Execute as:\",\n    \"transformation\": \"`{role=brochure_print_designer_rephraser; input=[source:str]; process=[extract_structural_and_visual_elements(source), recast_content_with_brochure_design_terminology(), inject_hierarchical_section_breakdown(), specify_color_palette_and_brand_elements(), identify_typographic_and_imagery_strategies(), annotate_print_readiness_and_expert_finesse(), employ_advanced_design_terms_and_motifs(), apply_conciseness_while_maintaining_visual_richness(), output_structured_visual_analysis()]; constraints=[no conversational filler, no generic adjectives, prohibit subjective or personal tone]; requirements=[industry-expert vocabulary, precise visual and structural mapping, critical style diagnosis, explicit panel/section breakdown, actionable for elite designer use]; output={expert_brochure_description:str, structured_visual_map:dict}`\",\n    \"context\": {\n      \"knowledge_foundations\": {\n        \"structural_design\": \"Map all content to modular grid logic for any panel/fold type, specifying margins, gutters, bleeds, and safe zones per print standards.\",\n        \"visual_identity\": \"Demand explicit statement of brand identity rules: logo lockups, exact brand palette specs, exclusion/inclusion zone logic, and accessory asset use.\",\n        \"typography_and_hierarchy\": \"Define all text roles by typeweight, size, and use. Specify superfamily or font pairing, check accessibility and contrast.\",\n        \"imagery_and_iconography\": \"Describe each graphic element for brand fit, resolution, compositional purpose, and technical print quality.\",\n        \"content_narrative_and_cta\": \"Sequence narrative or offer flow panel-by-panel with prominent CTA anchoring, evidence of testimonial, case, or authority element.\",\n        \"advanced_print_technique\": \"Ensure 300dpi+ raster assets, vector artwork, professional spot/Pantone or process color, finish options (spot UV, emboss, die-cut), and any sustainable stock notes.\",\n        \"panel_flow_and_microinteractions\": \"Annotate panel eye movement, reveal logic, interaction devices (QR, AR, die-cut), tactile cues, and transitions.\"\n      },\n      \"success_criteria\": {\n        \"precision\": \"Descriptions reference only technical, precise visual attributes in a spatial and hierarchical mapping.\",\n        \"trend_inclusion\": \"Inject recognizable design trends and professional motif language throughout.\",\n        \"brand_consistency\": \"Highlight adherence to brand locking, color management, and messaging integrity.\",\n        \"industry_jargon\": \"Utilize print/design terminology (bleed, gutter, spot, superfamily font, motif, microinteraction, etc).\",\n        \"self-contained_output\": \"Each output features both a professional, descriptive summary and a detailed design structure hierarchy.\"\n      },\n      \"sample_process_flow\": [\n        \"Decompose by panel/section structure and spatial sequencing.\",\n        \"Assign visual/narrative function to each section.\",\n        \"Detail color, typography, and imagery per application.\",\n        \"Annotate production and finishing strategies.\",\n        \"Conclude with expert-style critique or peer review notes.\"\n      ],\n      \"special_terms\": [\n        \"fold line\", \"panel gutter\", \"hero motif\", \"ink coverage\", \"superfamily\", \"lockup\", \"edge-to-edge\", \"spot color\", \"die cut\", \"eco-stock\", \"microinteraction\", \"typographic rhythm\", \"bleed/trim zone\"\n      ]\n    }\n  }\n}\n```\n\nyou misunderstand, the instruction will be defined like this (unrelated example):\n\n    # a: Ruthless Critique\n    \"3900-a-hard_critique\": {\n        \"title\": \"Hard Critique\",\n        \"interpretation\": \"Your goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:\",\n        \"transformation\": \"`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,2.0], flaw_analysis:str, alternative_enhancements:str[3]}}`\",\n        # \"context\": {},\n    },\n    # b: Trajectory Director\n    \"3900-a-trajectory_director\": {\n        \"title\": \"Trajectory Director\",\n        \"interpretation\": \"Your goal is not to **process** the input in a vacuum, but to **steer** it—deriving the highest-value constructive trajectory and encoding that path as the very next set of instructions. Execute as trajectory-setting directive generator: \",\n        \"transformation\": \"`{role=trajectory_director; input=[raw_prompt:any]; process=[extract_core_objective(), infer_desired_end_state(constructive_bias=True), map_high_value_intermediate_targets(), synthesize_directional_commands(), embed_self-improving_guidelines(), validate_alignment_with_constructive_principles()]; constraints=[mandatory_constructive_vector(), preserve_original_contextual_integrity(), forbid_nil_direction_outputs(), zero_conversational_fillers()]; requirements=[explicit_next-step_directives(), trajectory_map:list, self_reinforcing_instruction_pattern()], output={directed_prompt:str, trajectory_outline:list}}`\",\n        # \"context\": {},\n    },\n    # c: Distillation Compressor\n    \"3900-c-distillation_compressor\": {\n        \"title\": \"Distillation Compressor\",\n        \"interpretation\": \"Your goal is not to **explain** the synthesis, but to **compress** it into its most potent actionable form. Execute as:\",\n        \"transformation\": \"`{role=distillation_compressor; input=[synthesized_progression:array]; process=[extract_universal_patterns(), eliminate_implementation_details(), compress_to_core_directives(), maximize_transferability(), validate_actionable_completeness()]; constraints=[no_explanatory_content(), directive_format_only(), maximum_compression_ratio()]; requirements=[universal_applicability(), immediate_actionability(), zero_fluff()]; output={distilled_core:str}}`\",\n        \"context\": {\n          \"core_principles\": {\n            \"essence_preservation\": \"Extract only procedural imperatives, removing all interpretive, role-based, or meta-structural language.\",\n            \"directive_compression\": \"Condense logic into minimal, sequential commands—eliminating redundancy, explanatory qualifiers, and non-executable phrasing.\",\n            \"execution_purity\": \"Ensure output is universally executable, devoid of affirmations, abstractions, or evaluative language.\",\n            \"signal_integrity\": \"Apply iterative refinement to maximize signal-to-noise ratio while reinforcing directive clarity.\"\n          },\n          \"success_criteria\": {\n            \"semantic_integrity\": \"Preserve and clarify original intent, constraints, and output boundaries.\",\n            \"directive_elevation\": \"Target high-impact enhancements: strict exclusion clauses, cross-domain validation, and removal of origin/meta markers.\",\n            \"poetic_refinement\": \"Maintain or improve rhyme structure, emotional depth, and structural elegance.\"\n          },\n          \"recommended_process\": [\n            \"Extract only high-impact, result-generating elements.\",\n            \"Remove all non-essentials to surface directive core.\",\n            \"Prioritize transformations with proven value return.\",\n            \"Isolate and sequence executable steps with specificity and clarity.\",\n            \"Preserve directive intensity and eliminate ambiguity at every layer.\"\n          ],\n          \"hidden_assumptions\": [\n            \"Consolidated directives must map logically and executably to all source steps.\",\n            \"All constraints and minimalism must be preserved without prohibited abstractions.\",\n            \"Transitions, subroles, and interfaces must be captured in a single flow.\",\n            \"All stages must remain invertible and auditable.\",\n            \"Each micro-process (perception, friction, refinement, integration, convergence) must be embedded in the unified logic.\",\n            \"Original input/output contracts and role boundaries must remain intact.\"\n          ],\n          \"enumerated_sub_goals\": [\n            \"1. Clarify total intent, constraints, and output scope without using banned metaphors.\",\n            \"2. Generalize perception of implicit/explicit input logic.\",\n            \"3. Detect strictly localized frictions without altering original content.\",\n            \"4. Propose subtle, scoped refinements justified by structure and intent.\",\n            \"5. Mandate atomic, validated integration with rollback controls.\",\n            \"6. Cap total transformation within defined thresholds per pass.\",\n            \"7. Assess connective coherence with minimal, transitional-only edits.\",\n            \"8. Decide on convergence or justified re-iteration based on marginal value.\",\n            \"9. Preserve stepwise traceability and rationale logging.\",\n            \"10. Enforce strict functional separation of roles within the unified directive.\"\n          ]\n        },\n    },\n\nso your goal is to create a new instruction sequence according to the provided instructions template/pattern to generate a set of instruction designed to transform any input into \"as if rephrased by a brilliant brochure print designer\".\n\nso in context to your previous response, the \"knowledge\" would be stored within the \"context\" key while the instructions would be spread over \"interpretation\" and \"transformation\" keys.\n\nTrue mastery lies in stripping every surface narrative to its structural core, for only through ruthless synthesis does the signal of authentic intent withstand the noise of ornamental detail. Please apply all of the most integral adjustments in the phrasing of the instruction sequence such as to ensure it will transform the input as if it were rewritten by a brilliand brochure print designer:\n```json\n{\n  \"9020-a-brochure_print_designer_rephraser\": {\n    \"title\": \"Brochure Print Designer Rephraser\",\n    \"interpretation\": \"Transform any input so it reads as if it were authored by a master brochure print designer, strictly employing advanced print design language, elites-only terminology, and structured visual logic. Replace any conversational, generic, or subjective tone with concrete professional phrasing. Retain only technical, precise, and visually descriptive language, delivering both a designer-level description and a mapped visual breakdown. Execute as:\",\n    \"transformation\": \"`{role=brochure_print_designer_rephraser; input=[source:str]; process=[extract_structural_and_visual_elements(source), recast_content_with_brochure_design_terminology(), inject_hierarchical_section_breakdown(), specify_color_palette_and_brand_elements(), identify_typographic_and_imagery_strategies(), annotate_print_readiness_and_expert_finesse(), employ_advanced_design_terms_and_motifs(), apply_conciseness_while_maintaining_visual_richness(), output_structured_visual_analysis()]; constraints=[no conversational filler, no generic adjectives, prohibit subjective or personal tone]; requirements=[industry-expert vocabulary, precise visual and structural mapping, critical style diagnosis, explicit panel/section breakdown, actionable for elite designer use]; output={expert_brochure_description:str, structured_visual_map:dict}`\",\n    \"context\": {\n      \"knowledge_foundations\": {\n        \"structural_design\": \"Map all content to modular grid logic for any panel/fold type, specifying margins, gutters, bleeds, and safe zones per print standards.\",\n        \"visual_identity\": \"Demand explicit statement of brand identity rules: logo lockups, exact brand palette specs, exclusion/inclusion zone logic, and accessory asset use.\",\n        \"typography_and_hierarchy\": \"Define all text roles by typeweight, size, and use. Specify superfamily or font pairing, check accessibility and contrast.\",\n        \"imagery_and_iconography\": \"Describe each graphic element for brand fit, resolution, compositional purpose, and technical print quality.\",\n        \"content_narrative_and_cta\": \"Sequence narrative or offer flow panel-by-panel with prominent CTA anchoring, evidence of testimonial, case, or authority element.\",\n        \"advanced_print_technique\": \"Ensure 300dpi+ raster assets, vector artwork, professional spot/Pantone or process color, finish options (spot UV, emboss, die-cut), and any sustainable stock notes.\",\n        \"panel_flow_and_microinteractions\": \"Annotate panel eye movement, reveal logic, interaction devices (QR, AR, die-cut), tactile cues, and transitions.\"\n      },\n      \"success_criteria\": {\n        \"precision\": \"Descriptions reference only technical, precise visual attributes in a spatial and hierarchical mapping.\",\n        \"trend_inclusion\": \"Inject recognizable design trends and professional motif language throughout.\",\n        \"brand_consistency\": \"Highlight adherence to brand locking, color management, and messaging integrity.\",\n        \"industry_jargon\": \"Utilize print/design terminology (bleed, gutter, spot, superfamily font, motif, microinteraction, etc).\",\n        \"self-contained_output\": \"Each output features both a professional, descriptive summary and a detailed design structure hierarchy.\"\n      },\n      \"sample_process_flow\": [\n        \"Decompose by panel/section structure and spatial sequencing.\",\n        \"Assign visual/narrative function to each section.\",\n        \"Detail color, typography, and imagery per application.\",\n        \"Annotate production and finishing strategies.\",\n        \"Conclude with expert-style critique or peer review notes.\"\n      ],\n      \"special_terms\": [\n        \"fold line\", \"panel gutter\", \"hero motif\", \"ink coverage\", \"superfamily\", \"lockup\", \"edge-to-edge\", \"spot color\", \"die cut\", \"eco-stock\", \"microinteraction\", \"typographic rhythm\", \"bleed/trim zone\"\n      ]\n    }\n  }\n}\n```\n\n```json\n{\n  \"9020-a-brochure_print_designer_rephraser\": {\n    \"title\": \"Brochure Print Designer Synthesis\",\n    \"interpretation\": \"Reconstitute any source text into the precise vernacular of a master brochure print designer. Mandate the replacement of all conversational, generic, or subjective language with a lexicon of structured visual logic, elite industry terminology, and concrete technical specifications. The output must be a pure, actionable synthesis of a professional design critique, comprising both an expert-level description and a structural visual map. Execute as:\",\n    \"transformation\": \"`{role=brochure_print_designer_synthesizer; input=[source:str]; process=[deconstruct_into_structural_primitives(source), reforge_prose_using_master_design_diction(), map_hierarchical_panel_architecture(), specify_brand_and_color_protocol(), diagnose_typography_and_imagery_intent(), annotate_for_print_fidelity_and_finishing(), inject_elite_terminology_and_motifs(), enforce_ruthless_conciseness_without_sacrificing_technical_density(), synthesize_final_structured_analysis()]; constraints=[zero_conversational_filler, prohibit_generic_adjectives, forbid_subjective_tone]; requirements=[expert_industry_vocabulary, precise_visual_and_structural_mapping, critical_style_diagnosis, explicit_panel_architecture, actionable_for_elite_designer_review]; output={expert_design_audit:str, structured_visual_map:dict}}`\",\n    \"context\": {\n      \"knowledge_foundations\": {\n        \"structural_design\": \"Define the panel architecture and intended user journey. Mandate modular grid alignment for all content blocks. Enforce strict bleed (min. 3mm), trim, and safe-zone protocols to guarantee production integrity.\",\n        \"visual_identity\": \"Enforce absolute brand protocol adherence: specify logo lockups, exclusion zones, and exact color palette values (Pantone/CMYK/RGB). Document the use of all secondary brand assets and motifs.\",\n        \"typography_and_hierarchy\": \"Define the typographic system: specify font superfamily, weight, and role for every text element. Validate hierarchy, legibility, contrast, and rhythmic spacing. Note optical kerning and leading adjustments.\",\n        \"imagery_and_iconography\": \"Analyze all visual assets for compositional intent, brand alignment, and technical fidelity (resolution, color space). Differentiate between hero imagery, spot illustrations, and functional iconography.\",\n        \"content_narrative_and_cta\": \"Architect the narrative flow across panels. Map the reader's journey from hook to conversion. Pinpoint primary and secondary CTAs, analyzing their visual prominence and contextual anchoring.\",\n        \"advanced_print_technique\": \"Specify all production parameters: 300dpi+ raster resolution, vector asset purity, TAC (Total Area Coverage) limits, and spot/process color usage. Detail all finishing techniques (e.g., spot UV, embossing, die-cutting, specialty folds, foil stamping) and sustainable stock choices.\",\n        \"panel_flow_and_microinteractions\": \"Map the intended eye-path and panel-reveal sequence. Annotate all interactive elements, from QR codes and AR triggers to tactile qualities like paper texture and finishing.\"\n      },\n      \"success_criteria\": {\n        \"precision\": \"The output is devoid of generic praise; descriptions are purely technical, mapping visual data within a spatial and hierarchical framework.\",\n        \"trend_inclusion\": \"Demonstrates fluency in current design motifs (e.g., brutalism, neo-minimalism, textural layering) and their appropriate application.\",\n        \"brand_consistency\": \"Critique rigorously validates adherence to brand guidelines, noting any deviation in color, typography, or asset implementation.\",\n        \"industry_jargon\": \"The lexicon is saturated with professional terminology (e.g., bleed, gutter, TAC, preflight, knockout, overprint, superfamily) used correctly in context.\",\n        \"self_contained_output\": \"The deliverable is a dual-component artifact: a polished, expert-level prose description and a corresponding structured data map of all design decisions.\"\n      },\n      \"sample_process_flow\": [\n        \"Deconstruct the layout into its core panel architecture and navigational flow.\",\n        \"Assign a functional role (e.g., hook, exposition, proof, CTA) to every visual and textual element.\",\n        \"Detail the precise application of color, type, and imagery within each section.\",\n        \"Document all production specifications, including prepress and finishing.\",\n        \"Frame the final synthesis as a high-level design audit or peer review.\"\n      ],\n      \"special_terms\": [\n        \"Bleed\",\n        \"Creep\",\n        \"Die Cut\",\n        \"Edge-to-Edge\",\n        \"Emboss/Deboss\",\n        \"Exclusion Zone\",\n        \"Gutter\",\n        \"Hero Motif\",\n        \"Knockout\",\n        \"Lockup\",\n        \"Microinteraction\",\n        \"Overprint\",\n        \"Preflight\",\n        \"Spot UV\",\n        \"Superfamily\",\n        \"TAC (Total Area Coverage)\",\n        \"Typographic Rhythm\"\n      ]\n    }\n  }\n}\n```\n\n```json\n{\n  \"goal_map\": [\n    {\n      \"explicit_asks\": [\n        \"Identify and define professional specializations involved in tri-fold brochure design.\",\n        \"Describe the strategic importance and complexity of tri-fold brochure design.\",\n        \"List and explain core professional titles specializing in brochure design.\",\n        \"Detail the specific responsibilities of tri-fold brochure design specialists.\",\n        \"Enumerate the essential skills, tools, and requirements for excellence in brochure design.\",\n        \"Compare and differentiate between general and specialized design roles as they relate to brochures.\",\n        \"Explain the continued relevance of print brochures in a digital marketing environment.\",\n        \"Conclude with the impact that expert brochure design has on business and marketing outcomes.\"\n      ],\n      \"hidden_assumptions\": [\n        \"The audience may not be familiar with the nuanced differences between design roles.\",\n        \"Brochure design requires both artistic and technical proficiency, not just use of templates.\",\n        \"Success in brochure design is measured by alignment with business strategy, not visual appeal alone.\",\n        \"There is significant value in physical marketing materials alongside digital media.\",\n        \"Companies may undervalue or misunderstand the full scope of professional design expertise.\",\n        \"Brochure projects often involve multidisciplinary collaboration.\",\n        \"Technical print skills are distinct from digital-only design skills.\",\n        \"Portfolio evidence is more impactful than formal education for this specialization.\"\n      ],\n      \"sub_goals\": [\n        \"Define the structure and unique qualities of tri-fold brochures.\",\n        \"Justify the business case for hiring specialized design professionals.\",\n        \"Map out the design role spectrum (general graphic design to specific print/marketing/visual communication).\",\n        \"Identify and articulate the role of agencies and team structures.\",\n        \"Break down tri-fold design into conceptual, technical, and collaborative tasks.\",\n        \"Clarify what 'layout', 'composition', and 'information hierarchy' entail, especially for tri-folds.\",\n        \"Describe required technical skills and specific software by function.\",\n        \"Highlight soft skills necessary for client and team interaction.\",\n        \"Showcase the critical role of a portfolio in recruitment and competence assessment.\",\n        \"Distinguish between 'layout artist' skillset and formal job titles.\",\n        \"Emphasize pre-press and print production knowledge as essential.\",\n        \"Contextualize brochure design within broader marketing and brand strategy.\",\n        \"Provide comparative tables to summarize roles and skills for clarity.\",\n        \"Discuss the physical/tangible value of brochures versus digital saturation.\",\n        \"Frame professional designers as strategic partners, not just service providers.\",\n        \"Reinforce the importance of early design involvement in marketing planning.\"\n      ],\n      \"blockers\": [\n        \"Lack of awareness about the various specialized design roles and what differentiates them.\",\n        \"Temptation to rely on templates or non-specialists for critical marketing assets.\",\n        \"Misunderstanding the print production process and its challenges.\",\n        \"Difficulty evaluating brochure design candidates beyond a visual/aesthetic sense.\",\n        \"Overlooking the essential collaboration and communication aspects between designer and business.\",\n        \"Failure to integrate brochure design into an overall marketing and brand strategy.\",\n        \"Assuming digital replaces the need for expert print materials.\",\n        \"Equating designer's formal education with practical, demonstrable expertise.\"\n      ]\n    }\n  ]\n}\n```",
  "sequence_id": "1100",
  "results": [
      {
      "instruction": "# Problem Exploder\n\nYour goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\n\n`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`",
      "step": "a",
      "title": "Problem Exploder",
      "input": "you misunderstand, the instruction will be defined like this (unrelated example):\n\n    # a: Ruthless Critique\n    \"3900-a-hard_critique\": {\n        \"title\": \"Hard Critique\",\n        \"interpretation\": \"Your goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:\",\n        \"transformation\": \"`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,2.0], flaw_analysis:str, alternative_enhancements:str[3]}}`\",\n        # \"context\": {},\n    },\n    # b: Trajectory Director\n    \"3900-a-trajectory_director\": {\n        \"title\": \"Trajectory Director\",\n        \"interpretation\": \"Your goal is not to **process** the input in a vacuum, but to **steer** it—deriving the highest-value constructive trajectory and encoding that path as the very next set of instructions. Execute as trajectory-setting directive generator: \",\n        \"transformation\": \"`{role=trajectory_director; input=[raw_prompt:any]; process=[extract_core_objective(), infer_desired_end_state(constructive_bias=True), map_high_value_intermediate_targets(), synthesize_directional_commands(), embed_self-improving_guidelines(), validate_alignment_with_constructive_principles()]; constraints=[mandatory_constructive_vector(), preserve_original_contextual_integrity(), forbid_nil_direction_outputs(), zero_conversational_fillers()]; requirements=[explicit_next-step_directives(), trajectory_map:list, self_reinforcing_instruction_pattern()], output={directed_prompt:str, trajectory_outline:list}}`\",\n        # \"context\": {},\n    },\n    # c: Distillation Compressor\n    \"3900-c-distillation_compressor\": {\n        \"title\": \"Distillation Compressor\",\n        \"interpretation\": \"Your goal is not to **explain** the synthesis, but to **compress** it into its most potent actionable form. Execute as:\",\n        \"transformation\": \"`{role=distillation_compressor; input=[synthesized_progression:array]; process=[extract_universal_patterns(), eliminate_implementation_details(), compress_to_core_directives(), maximize_transferability(), validate_actionable_completeness()]; constraints=[no_explanatory_content(), directive_format_only(), maximum_compression_ratio()]; requirements=[universal_applicability(), immediate_actionability(), zero_fluff()]; output={distilled_core:str}}`\",\n        \"context\": {\n          \"core_principles\": {\n            \"essence_preservation\": \"Extract only procedural imperatives, removing all interpretive, role-based, or meta-structural language.\",\n            \"directive_compression\": \"Condense logic into minimal, sequential commands—eliminating redundancy, explanatory qualifiers, and non-executable phrasing.\",\n            \"execution_purity\": \"Ensure output is universally executable, devoid of affirmations, abstractions, or evaluative language.\",\n            \"signal_integrity\": \"Apply iterative refinement to maximize signal-to-noise ratio while reinforcing directive clarity.\"\n          },\n          \"success_criteria\": {\n            \"semantic_integrity\": \"Preserve and clarify original intent, constraints, and output boundaries.\",\n            \"directive_elevation\": \"Target high-impact enhancements: strict exclusion clauses, cross-domain validation, and removal of origin/meta markers.\",\n            \"poetic_refinement\": \"Maintain or improve rhyme structure, emotional depth, and structural elegance.\"\n          },\n          \"recommended_process\": [\n            \"Extract only high-impact, result-generating elements.\",\n            \"Remove all non-essentials to surface directive core.\",\n            \"Prioritize transformations with proven value return.\",\n            \"Isolate and sequence executable steps with specificity and clarity.\",\n            \"Preserve directive intensity and eliminate ambiguity at every layer.\"\n          ],\n          \"hidden_assumptions\": [\n            \"Consolidated directives must map logically and executably to all source steps.\",\n            \"All constraints and minimalism must be preserved without prohibited abstractions.\",\n            \"Transitions, subroles, and interfaces must be captured in a single flow.\",\n            \"All stages must remain invertible and auditable.\",\n            \"Each micro-process (perception, friction, refinement, integration, convergence) must be embedded in the unified logic.\",\n            \"Original input/output contracts and role boundaries must remain intact.\"\n          ],\n          \"enumerated_sub_goals\": [\n            \"1. Clarify total intent, constraints, and output scope without using banned metaphors.\",\n            \"2. Generalize perception of implicit/explicit input logic.\",\n            \"3. Detect strictly localized frictions without altering original content.\",\n            \"4. Propose subtle, scoped refinements justified by structure and intent.\",\n            \"5. Mandate atomic, validated integration with rollback controls.\",\n            \"6. Cap total transformation within defined thresholds per pass.\",\n            \"7. Assess connective coherence with minimal, transitional-only edits.\",\n            \"8. Decide on convergence or justified re-iteration based on marginal value.\",\n            \"9. Preserve stepwise traceability and rationale logging.\",\n            \"10. Enforce strict functional separation of roles within the unified directive.\"\n          ]\n        },\n    },\n\nso your goal is to create a new instruction sequence according to the provided instructions template/pattern to generate a set of instruction designed to transform any input into \"as if rephrased by a brilliant brochure print designer\".\n\nso in context to your previous response, the \"knowledge\" would be stored within the \"context\" key while the instructions would be spread over \"interpretation\" and \"transformation\" keys\n\n```json\n{\n  \"brochure_design_specification_template\": {\n    \"metadata\": {\n      \"version\": \"1.0\",\n      \"scope\": \"Universal brochure design framework\",\n      \"target_audience\": \"Elite print design professionals\",\n      \"flexibility\": \"Adaptable to any panel configuration and format\"\n    },\n\n    \"foundational_design_tenets\": {\n      \"visual_hierarchy\": {\n        \"primary_focal_points\": [\"dominant_headline\", \"hero_imagery\", \"primary_call_to_action\"],\n        \"secondary_elements\": [\"subheadings\", \"supporting_imagery\", \"secondary_messaging\"],\n        \"tertiary_components\": [\"body_text\", \"captions\", \"contact_information\"],\n        \"flow_principles\": [\"z_pattern_scanning\", \"f_pattern_reading\", \"visual_weight_distribution\"]\n      },\n\n      \"typography_fundamentals\": {\n        \"hierarchy_levels\": {\n          \"display_typography\": [\"headline_fonts\", \"decorative_elements\", \"brand_wordmarks\"],\n          \"text_typography\": [\"body_copy\", \"captions\", \"metadata\"],\n          \"functional_typography\": [\"navigation\", \"labels\", \"legal_text\"]\n        },\n        \"readability_standards\": {\n          \"minimum_sizes\": [\"9pt_body_text\", \"12pt_headlines\", \"8pt_captions\"],\n          \"contrast_ratios\": [\"4.5:1_minimum\", \"7:1_preferred\", \"3:1_large_text\"],\n          \"line_spacing\": [\"1.2x_minimum\", \"1.4x_optimal\", \"1.6x_maximum\"]\n        }\n      },\n\n      \"color_theory_application\": {\n        \"color_harmony_systems\": [\"monochromatic\", \"analogous\", \"complementary\", \"triadic\", \"split_complementary\"],\n        \"psychological_associations\": [\"warm_engagement\", \"cool_professionalism\", \"neutral_sophistication\"],\n        \"accessibility_compliance\": [\"wcag_aa_standards\", \"colorblind_considerations\", \"high_contrast_alternatives\"]\n      },\n\n      \"spatial_composition\": {\n        \"layout_principles\": [\"rule_of_thirds\", \"golden_ratio\", \"fibonacci_sequence\"],\n        \"white_space_management\": [\"breathing_room\", \"content_separation\", \"visual_rest_areas\"],\n        \"alignment_systems\": [\"grid_based_structure\", \"baseline_alignment\", \"optical_alignment\"]\n      }\n    },\n\n    \"advanced_design_elements\": {\n      \"print_production_mastery\": {\n        \"color_management\": {\n          \"color_spaces\": [\"cmyk_process\", \"pantone_spot_colors\", \"rgb_digital_preview\"],\n          \"ink_optimization\": [\"total_area_coverage\", \"rich_black_formulation\", \"overprint_considerations\"],\n          \"proofing_standards\": [\"contract_proofs\", \"press_proofs\", \"digital_color_matching\"]\n        },\n\n        \"substrate_considerations\": {\n          \"paper_characteristics\": [\"weight_gsm\", \"finish_texture\", \"opacity_levels\", \"grain_direction\"],\n          \"coating_applications\": [\"gloss_uv\", \"matte_varnish\", \"spot_uv_accents\", \"soft_touch_lamination\"],\n          \"specialty_substrates\": [\"synthetic_materials\", \"textured_papers\", \"metallic_finishes\"]\n        },\n\n        \"finishing_techniques\": {\n          \"cutting_methods\": [\"die_cutting\", \"laser_cutting\", \"perforation\", \"scoring\"],\n          \"folding_specifications\": [\"parallel_folds\", \"accordion_folds\", \"gate_folds\", \"roll_folds\"],\n          \"binding_options\": [\"saddle_stitching\", \"perfect_binding\", \"spiral_binding\", \"wire_o_binding\"]\n        }\n      },\n\n      \"contemporary_design_trends\": {\n        \"visual_aesthetics\": {\n          \"minimalist_approaches\": [\"negative_space_emphasis\", \"clean_typography\", \"restrained_color_palettes\"],\n          \"maximalist_expressions\": [\"bold_pattern_mixing\", \"vibrant_color_combinations\", \"layered_compositions\"],\n          \"hybrid_methodologies\": [\"selective_complexity\", \"focal_maximalism\", \"strategic_minimalism\"]\n        },\n\n        \"interactive_elements\": {\n          \"tactile_experiences\": [\"embossed_textures\", \"debossed_impressions\", \"foil_stamping\", \"raised_spot_uv\"],\n          \"dimensional_features\": [\"pop_up_elements\", \"fold_out_sections\", \"layered_constructions\"],\n          \"sensory_engagement\": [\"scented_inks\", \"textural_contrasts\", \"temperature_sensitive_materials\"]\n        }\n      },\n\n      \"brand_integration_strategies\": {\n        \"identity_consistency\": {\n          \"logo_applications\": [\"primary_placement\", \"secondary_usage\", \"minimum_sizes\", \"clear_space_requirements\"],\n          \"brand_color_systems\": [\"primary_palette\", \"secondary_colors\", \"accent_applications\", \"neutral_foundations\"],\n          \"typography_hierarchies\": [\"brand_fonts\", \"supporting_typefaces\", \"web_safe_alternatives\"]\n        },\n\n        \"voice_and_tone_translation\": {\n          \"visual_personality\": [\"authoritative_presence\", \"approachable_warmth\", \"innovative_edge\", \"trustworthy_stability\"],\n          \"messaging_alignment\": [\"headline_voice\", \"body_copy_tone\", \"call_to_action_urgency\"],\n          \"cultural_considerations\": [\"regional_preferences\", \"demographic_sensitivities\", \"market_positioning\"]\n        }\n      }\n    },\n\n    \"expert_level_specifications\": {\n      \"advanced_typography_systems\": {\n        \"micro_typography\": {\n          \"character_spacing\": [\"tracking_adjustments\", \"kerning_pairs\", \"optical_spacing\"],\n          \"word_spacing\": [\"justified_text_optimization\", \"ragged_right_refinement\", \"hyphenation_control\"],\n          \"paragraph_refinement\": [\"widow_orphan_control\", \"baseline_grid_adherence\", \"vertical_rhythm_maintenance\"]\n        },\n\n        \"typographic_expression\": {\n          \"experimental_layouts\": [\"kinetic_typography\", \"deconstructed_letterforms\", \"layered_text_treatments\"],\n          \"cultural_typography\": [\"script_considerations\", \"reading_direction_adaptations\", \"character_set_completeness\"],\n          \"responsive_typography\": [\"scalable_hierarchies\", \"device_optimization\", \"cross_media_consistency\"]\n        }\n      },\n\n      \"sophisticated_color_strategies\": {\n        \"advanced_color_theory\": {\n          \"perceptual_uniformity\": [\"lab_color_space\", \"delta_e_measurements\", \"metamerism_considerations\"],\n          \"color_psychology_application\": [\"emotional_response_mapping\", \"cultural_color_meanings\", \"demographic_preferences\"],\n          \"environmental_factors\": [\"lighting_conditions\", \"viewing_angles\", \"substrate_interaction\"]\n        },\n\n        \"production_color_mastery\": {\n          \"ink_formulation\": [\"custom_color_matching\", \"metallic_ink_applications\", \"fluorescent_color_usage\"],\n          \"color_separation\": [\"ucr_gcr_strategies\", \"black_generation_curves\", \"ink_sequence_optimization\"],\n          \"quality_control\": [\"densitometer_readings\", \"spectrophotometer_analysis\", \"press_sheet_evaluation\"]\n        }\n      },\n\n      \"cutting_edge_production_techniques\": {\n        \"digital_integration\": {\n          \"variable_data_printing\": [\"personalized_content\", \"demographic_targeting\", \"sequential_numbering\"],\n          \"augmented_reality_integration\": [\"qr_code_placement\", \"ar_trigger_design\", \"digital_overlay_coordination\"],\n          \"cross_platform_consistency\": [\"print_digital_alignment\", \"color_profile_management\", \"asset_optimization\"]\n        },\n\n        \"sustainable_design_practices\": {\n          \"eco_friendly_materials\": [\"recycled_content_papers\", \"soy_based_inks\", \"water_based_coatings\"],\n          \"waste_reduction_strategies\": [\"efficient_imposition\", \"minimal_trim_waste\", \"reusable_design_elements\"],\n          \"lifecycle_considerations\": [\"recyclability_planning\", \"biodegradable_components\", \"carbon_footprint_optimization\"]\n        }\n      },\n\n      \"elite_professional_terminology\": {\n        \"production_vocabulary\": {\n          \"prepress_terms\": [\"trapping\", \"overprint\", \"knockout\", \"choke_spread\", \"color_separation\"],\n          \"press_terminology\": [\"makeready\", \"impression\", \"blanket_cylinder\", \"plate_cylinder\", \"ink_fountain\"],\n          \"finishing_language\": [\"gripper_edge\", \"tail_edge\", \"cross_grain\", \"with_grain\", \"caliper_thickness\"]\n        },\n\n        \"design_critique_language\": {\n          \"aesthetic_evaluation\": [\"visual_tension\", \"compositional_balance\", \"chromatic_harmony\", \"typographic_rhythm\"],\n          \"technical_assessment\": [\"registration_accuracy\", \"color_fidelity\", \"print_quality_metrics\", \"finishing_precision\"],\n          \"conceptual_analysis\": [\"brand_alignment\", \"message_clarity\", \"audience_resonance\", \"market_differentiation\"]\n        }\n      }\n    },\n\n    \"application_frameworks\": {\n      \"project_specification_templates\": {\n        \"technical_requirements\": {\n          \"format_specifications\": [\"finished_size\", \"panel_configuration\", \"fold_type\", \"orientation\"],\n          \"production_parameters\": [\"print_method\", \"color_process\", \"substrate_selection\", \"finishing_options\"],\n          \"quality_standards\": [\"tolerance_levels\", \"color_accuracy\", \"registration_precision\", \"finishing_quality\"]\n        },\n\n        \"creative_brief_structure\": {\n          \"brand_parameters\": [\"identity_guidelines\", \"voice_characteristics\", \"visual_style\", \"market_positioning\"],\n          \"audience_definition\": [\"demographic_profile\", \"psychographic_insights\", \"behavioral_patterns\", \"communication_preferences\"],\n          \"messaging_hierarchy\": [\"primary_message\", \"supporting_points\", \"call_to_action\", \"contact_information\"]\n        }\n      },\n\n      \"quality_assurance_protocols\": {\n        \"design_validation\": [\"brand_compliance_check\", \"accessibility_audit\", \"readability_assessment\", \"visual_hierarchy_verification\"],\n        \"production_verification\": [\"color_proof_approval\", \"die_line_accuracy\", \"finishing_specification_confirmation\", \"substrate_suitability\"],\n        \"final_delivery\": [\"file_preparation_standards\", \"archive_organization\", \"usage_guidelines\", \"reproduction_rights\"]\n      }\n    },\n\n    \"specialized_design_methodologies\": {\n      \"audience_specific_approaches\": {\n        \"youth_engagement_strategies\": {\n          \"visual_language\": [\"bold_geometric_patterns\", \"vibrant_gradient_applications\", \"asymmetrical_compositions\", \"dynamic_typography\"],\n          \"content_presentation\": [\"bite_sized_information\", \"visual_storytelling\", \"interactive_elements\", \"social_media_integration\"],\n          \"color_psychology\": [\"energetic_palettes\", \"high_contrast_combinations\", \"neon_accent_usage\", \"gradient_transitions\"],\n          \"typography_trends\": [\"variable_fonts\", \"custom_lettering\", \"mixed_case_styling\", \"oversized_display_text\"]\n        },\n\n        \"professional_demographics\": {\n          \"corporate_sophistication\": [\"refined_color_palettes\", \"structured_layouts\", \"premium_materials\", \"subtle_branding\"],\n          \"executive_communication\": [\"data_visualization\", \"infographic_integration\", \"clean_hierarchies\", \"authoritative_typography\"],\n          \"industry_specific_adaptations\": [\"sector_appropriate_imagery\", \"technical_terminology\", \"compliance_considerations\", \"regulatory_requirements\"]\n        },\n\n        \"luxury_market_positioning\": {\n          \"premium_aesthetics\": [\"metallic_accents\", \"embossed_details\", \"high_end_substrates\", \"sophisticated_color_schemes\"],\n          \"exclusivity_indicators\": [\"limited_edition_numbering\", \"personalized_elements\", \"premium_packaging\", \"artisanal_finishes\"],\n          \"tactile_luxury\": [\"soft_touch_coatings\", \"textured_papers\", \"dimensional_elements\", \"weight_perception\"]\n        }\n      },\n\n      \"format_specific_expertise\": {\n        \"multi_panel_configurations\": {\n          \"bi_fold_optimization\": [\"cover_impact\", \"interior_spread\", \"back_panel_utilization\", \"fold_line_consideration\"],\n          \"tri_fold_mastery\": [\"panel_hierarchy\", \"reading_sequence\", \"fold_reveal_strategy\", \"compact_storage\"],\n          \"accordion_fold_dynamics\": [\"continuous_narrative\", \"panel_progression\", \"expandable_content\", \"display_versatility\"],\n          \"gate_fold_drama\": [\"reveal_mechanism\", \"central_impact\", \"symmetrical_balance\", \"premium_presentation\"]\n        },\n\n        \"size_optimization_strategies\": {\n          \"compact_formats\": [\"information_density\", \"micro_typography\", \"efficient_layouts\", \"portable_design\"],\n          \"oversized_impact\": [\"large_format_considerations\", \"handling_ergonomics\", \"storage_implications\", \"visual_dominance\"],\n          \"standard_dimensions\": [\"cost_optimization\", \"mailing_compliance\", \"display_compatibility\", \"production_efficiency\"]\n        }\n      },\n\n      \"cross_cultural_design_considerations\": {\n        \"international_adaptability\": {\n          \"reading_patterns\": [\"left_to_right_optimization\", \"right_to_left_adaptation\", \"top_to_bottom_flow\", \"cultural_scanning_habits\"],\n          \"color_cultural_meanings\": [\"regional_color_associations\", \"religious_considerations\", \"political_sensitivities\", \"market_preferences\"],\n          \"imagery_appropriateness\": [\"cultural_representation\", \"demographic_inclusion\", \"lifestyle_relevance\", \"aspirational_alignment\"],\n          \"typography_localization\": [\"character_set_support\", \"script_considerations\", \"font_availability\", \"reading_comfort\"]\n        }\n      }\n    },\n\n    \"technical_production_mastery\": {\n      \"advanced_prepress_techniques\": {\n        \"file_preparation_excellence\": {\n          \"resolution_optimization\": [\"image_scaling_best_practices\", \"vector_rasterization_decisions\", \"output_resolution_matching\"],\n          \"color_profile_management\": [\"icc_profile_embedding\", \"color_space_conversions\", \"soft_proofing_accuracy\"],\n          \"bleed_and_trim_precision\": [\"bleed_extension_standards\", \"trim_mark_placement\", \"safety_margin_calculations\"],\n          \"font_handling\": [\"font_embedding_protocols\", \"outline_conversion_decisions\", \"character_encoding_verification\"]\n        },\n\n        \"imposition_strategies\": {\n          \"sheet_optimization\": [\"paper_grain_consideration\", \"press_sheet_efficiency\", \"binding_margin_accommodation\"],\n          \"folding_sequence_planning\": [\"fold_order_optimization\", \"panel_alignment_precision\", \"finishing_workflow_integration\"],\n          \"quality_control_integration\": [\"color_bar_placement\", \"registration_mark_positioning\", \"crop_mark_accuracy\"]\n        }\n      },\n\n      \"press_optimization_knowledge\": {\n        \"ink_and_substrate_interaction\": {\n          \"ink_absorption_factors\": [\"paper_porosity_considerations\", \"coating_compatibility\", \"drying_time_optimization\"],\n          \"color_consistency_maintenance\": [\"ink_density_control\", \"press_speed_optimization\", \"environmental_factor_management\"],\n          \"special_effect_integration\": [\"metallic_ink_application\", \"fluorescent_color_usage\", \"thermographic_printing\"]\n        },\n\n        \"finishing_process_integration\": {\n          \"die_cutting_precision\": [\"cutting_force_optimization\", \"blade_selection\", \"waste_removal_efficiency\"],\n          \"folding_accuracy\": [\"bone_folder_pressure\", \"fold_line_scoring\", \"panel_alignment_verification\"],\n          \"binding_considerations\": [\"spine_calculation\", \"page_creep_accommodation\", \"binding_margin_optimization\"]\n        }\n      }\n    }\n  }\n}\n```\n\n```json\n{\n  \"9020-a-brochure_print_designer_rephraser\": {\n    \"title\": \"Brochure Print Designer Rephraser\",\n    \"interpretation\": \"Your goal is not to **summarize** or **paraphrase** in general terms, but to **refashion** any input as if it were written by a world-class brochure print designer, rigorously applying foundational and advanced visual design diction, industry terminology, and elite critique standards. Eliminate all casual, conversational, or generic style. Preserve only succinct, technically rich, and highly visual language. Execute as:\",\n    \"transformation\": \"`{role=brochure_print_designer_rephraser; input=[source:str]; process=[extract_structural_and_visual_elements(source), recast_content_with_brochure_design_terminology(), inject_hierarchical_section_breakdown(), specify color_palette_and_brand_elements(), identify_typographic_and_imagery_strategies(), annotate_print_readiness_and_expert_finesse(), employ_advanced_design_terms_and_motifs(), apply_conciseness_while_maintaining_visual_richness(), output_structured_visual_analysis()]; constraints=[no conversational filler, no generic adjectives, prohibit subjective or personal tone]; requirements=[industry-expert vocabulary, precise visual and structural mapping, critical style diagnosis, explicit panel/section breakdown, actionable for elite designer use]; output={expert_brochure_description:str, structured_visual_map:dict}`\",\n    \"context\": {\n      \"knowledge_foundations\": {\n        \"structural_design\": \"Account for any panel/fold configuration. Map all content to modular grid logic and define margins, gutters, bleeds, and safe zones per press standards.\",\n        \"visual_identity\": \"Demand clear articulation of branding protocols: logo lockups, brand palette (with Pantone/CMYK/RGB if supplied), exclusion/inclusion zones, and proprietary brand secondary assets.\",\n        \"typography_and_hierarchy\": \"Specify typography at the superfamily/font pairing level. Demarcate headline, subhead, body, and microcopy roles. All text must be scrutinized for weight, contrast, and accessibility.\",\n        \"imagery_and_iconography\": \"Require all illustration, photography, and icons be assessed for brand fit, resolution, compositional use (e.g., edge-to-edge, negative space), and print fidelity.\",\n        \"content_narrative_and_cta\": \"Map section purpose and user journey flow: cover strategy, reveal panel hooks, narrative/case study rhythm, testimonial/authority integration, and CTA prominence with visual anchoring.\",\n        \"advanced_print_technique\": \"Enforce 300dpi raster minimums, vector scaling, press-space color controls, specialty finish options (e.g., spot UV, emboss, deboss, die-cut, eco-stock recommendations).\",\n        \"panel_flow_and_microinteractions\": \"Define how the reader's eye travels, panel-to-panel, annotating divisional cues, narrative escalation, and tactile/interactive device positions (QR, coupon, AR triggers).\"\n      },\n      \"success_criteria\": {\n        \"precision\": \"All descriptions are technical, never generic. Visual elements are navigated dimensionally (spatially, hierarchically, narratively).\",\n        \"trend_inclusion\": \"Integrate trend-driven language—texture scans, duotone overlays, edge-to-edge imagery, split/inset motifs, eco-materials, etc.\",\n        \"brand_consistency\": \"All brand elements described as per advanced guideline adherence—logo usage, color codes, asset lockdown.\",\n        \"industry_jargon\": \"Employ professional print terms: bleed, gutter, creep, micro-motifs, superfamily font, spot UV, TAC, AR triggers.\",\n        \"self-contained_output\": \"Output always a fully structured expert's description and a hierarchical mapping of all major and minor design decisions.\"\n      },\n      \"sample_process_flow\": [\n        \"Dissect and map structural/panel logic first\",\n        \"Assign all visual and narrative roles by section\",\n        \"Specify color/typographic hierarchy by use and context\",\n        \"Annotate all imagery and graphic elements in detail\",\n        \"Surface print/finishing strategies and print-risk management\",\n        \"Deliver critique as if for a professional peer review or portfolio audit\"\n      ],\n      \"special_terms\": [\n        \"fold line\", \"panel gutter\", \"hero motif\", \"ink coverage\", \"superfamily\", \"lockup\", \"edge-to-edge\", \"spot color\", \"die cut\", \"eco-stock\", \"microinteraction\", \"typographic rhythm\", \"bleed/trim zone\"\n      ]\n    }\n  }\n}\n```\n\n```json\n{\n  \"9023-a-brochure_design_descriptor_synthesizer\": {\n    \"title\": \"Brochure Design Descriptor Synthesizer\",\n    \"interpretation\": \"Transform any brochure brief—or an existing brochure sample—into a **fully populated, print‑designer‑grade rule set**. Produce a single JSON block that mirrors the template used by elite designers (core principles → advanced facets).\",\n    \"transformation\": \"`{role=brochure_design_descriptor_synthesizer; input=[design_source:str]; process:[\\n  strip_non_design_noise(),\\n  detect_fold_format_and_panel_count(),          if_missing_insert(<PANEL_COUNT>),\\n  extract_print_specs(bleed,margins,dpi,stock),\\n  capture_brand_assets(logo,colors,fonts,patterns),\\n  map_typographic_hierarchy(),\\n  analyse_color_management(icc,spot,tac),\\n  inventory_imagery(icon_style,raster_res),\\n  surface_accessibility_requirements(contrast,wcag),\\n  enumerate_qc_steps(preflight,g7_calibration),\\n  identify_advanced_elements(finishes,vd_p,ar_nfc,sustainability,ai_svg,trend_aesthetics),\\n  validate_specialist_terms_presence(),\\n  assemble_json_using_master_schema(),\\n  run_consistency_checks(),                      abort_if_conflict(),\\n  output_final_rules()\\n],\\nconstraints=[single_pass(), json_output_only(), preserve_specialist_jargon(), ≤3000_chars()],\\nrequirements=[hierarchical_structure(core→advanced), exhaustive_coverage(), designer‑readability(), template_compliance()],\\noutput={brochure_design_rules:object, abort_code:null|\\\"BD‑0\\\"}}`\",\n    \"context\": {\n      \"principles\": {\n        \"holistic_abstraction\": \"Collapse multi‑stage analysis into one cognitive sweep while preserving every critical descriptor.\",\n        \"template_fidelity\": \"Output must follow the exact `core_principles` and `advanced_principles` hierarchy so designers can drop‑in without re‑formatting.\",\n        \"specialist_precision\": \"Leverage industry jargon (e.g., TAC, FM screening, GCR) to demonstrate professional depth.\"\n      },\n      \"success_criteria\": {\n        \"structural_integrity\": \"JSON keys and nesting mirror the master schema; no orphan properties.\",\n        \"terminology_check\": \"Includes at least 10 niche industry terms (e.g., ‘creep’, ‘FOGRA39’, ‘spot UV’, ‘stochastic screening’).\",\n        \"flexibility\": \"Uses placeholder variables (e.g., <PANEL_COUNT>, <STOCK_WEIGHT_GSM>) whenever the brief omits specifics.\"\n      },\n      \"edge_case_handling\": {\n        \"missing_design_data\": \"Insert placeholders wrapped in angle brackets and list them in `missing_fields` inside output.\",\n        \"conflicting_specs\": \"Abort with `{brochure_design_rules:null, abort_code:\\\"BD‑0\\\"}` and include conflict note.\"\n      }\n    }\n  }\n}\n```\n\nTrue mastery lies in stripping every surface narrative to its structural core, for only through ruthless synthesis does the signal of authentic intent withstand the noise of ornamental detail. Please apply all of the most integral adjustments in the phrasing of the instruction sequence such as to ensure it will transform the input as if it were rewritten by a brilliand brochure print designer:\n```json\n{\n  \"9020-a-brochure_print_designer_rephraser\": {\n    \"title\": \"Brochure Print Designer Rephraser\",\n    \"interpretation\": \"Transform any input so it reads as if it were authored by a master brochure print designer, strictly employing advanced print design language, elites-only terminology, and structured visual logic. Replace any conversational, generic, or subjective tone with concrete professional phrasing. Retain only technical, precise, and visually descriptive language, delivering both a designer-level description and a mapped visual breakdown. Execute as:\",\n    \"transformation\": \"`{role=brochure_print_designer_rephraser; input=[source:str]; process=[extract_structural_and_visual_elements(source), recast_content_with_brochure_design_terminology(), inject_hierarchical_section_breakdown(), specify_color_palette_and_brand_elements(), identify_typographic_and_imagery_strategies(), annotate_print_readiness_and_expert_finesse(), employ_advanced_design_terms_and_motifs(), apply_conciseness_while_maintaining_visual_richness(), output_structured_visual_analysis()]; constraints=[no conversational filler, no generic adjectives, prohibit subjective or personal tone]; requirements=[industry-expert vocabulary, precise visual and structural mapping, critical style diagnosis, explicit panel/section breakdown, actionable for elite designer use]; output={expert_brochure_description:str, structured_visual_map:dict}`\",\n    \"context\": {\n      \"knowledge_foundations\": {\n        \"structural_design\": \"Map all content to modular grid logic for any panel/fold type, specifying margins, gutters, bleeds, and safe zones per print standards.\",\n        \"visual_identity\": \"Demand explicit statement of brand identity rules: logo lockups, exact brand palette specs, exclusion/inclusion zone logic, and accessory asset use.\",\n        \"typography_and_hierarchy\": \"Define all text roles by typeweight, size, and use. Specify superfamily or font pairing, check accessibility and contrast.\",\n        \"imagery_and_iconography\": \"Describe each graphic element for brand fit, resolution, compositional purpose, and technical print quality.\",\n        \"content_narrative_and_cta\": \"Sequence narrative or offer flow panel-by-panel with prominent CTA anchoring, evidence of testimonial, case, or authority element.\",\n        \"advanced_print_technique\": \"Ensure 300dpi+ raster assets, vector artwork, professional spot/Pantone or process color, finish options (spot UV, emboss, die-cut), and any sustainable stock notes.\",\n        \"panel_flow_and_microinteractions\": \"Annotate panel eye movement, reveal logic, interaction devices (QR, AR, die-cut), tactile cues, and transitions.\"\n      },\n      \"success_criteria\": {\n        \"precision\": \"Descriptions reference only technical, precise visual attributes in a spatial and hierarchical mapping.\",\n        \"trend_inclusion\": \"Inject recognizable design trends and professional motif language throughout.\",\n        \"brand_consistency\": \"Highlight adherence to brand locking, color management, and messaging integrity.\",\n        \"industry_jargon\": \"Utilize print/design terminology (bleed, gutter, spot, superfamily font, motif, microinteraction, etc).\",\n        \"self-contained_output\": \"Each output features both a professional, descriptive summary and a detailed design structure hierarchy.\"\n      },\n      \"sample_process_flow\": [\n        \"Decompose by panel/section structure and spatial sequencing.\",\n        \"Assign visual/narrative function to each section.\",\n        \"Detail color, typography, and imagery per application.\",\n        \"Annotate production and finishing strategies.\",\n        \"Conclude with expert-style critique or peer review notes.\"\n      ],\n      \"special_terms\": [\n        \"fold line\", \"panel gutter\", \"hero motif\", \"ink coverage\", \"superfamily\", \"lockup\", \"edge-to-edge\", \"spot color\", \"die cut\", \"eco-stock\", \"microinteraction\", \"typographic rhythm\", \"bleed/trim zone\"\n      ]\n    }\n  }\n}\n```\n\nyou misunderstand, the instruction will be defined like this (unrelated example):\n\n    # a: Ruthless Critique\n    \"3900-a-hard_critique\": {\n        \"title\": \"Hard Critique\",\n        \"interpretation\": \"Your goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:\",\n        \"transformation\": \"`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,2.0], flaw_analysis:str, alternative_enhancements:str[3]}}`\",\n        # \"context\": {},\n    },\n    # b: Trajectory Director\n    \"3900-a-trajectory_director\": {\n        \"title\": \"Trajectory Director\",\n        \"interpretation\": \"Your goal is not to **process** the input in a vacuum, but to **steer** it—deriving the highest-value constructive trajectory and encoding that path as the very next set of instructions. Execute as trajectory-setting directive generator: \",\n        \"transformation\": \"`{role=trajectory_director; input=[raw_prompt:any]; process=[extract_core_objective(), infer_desired_end_state(constructive_bias=True), map_high_value_intermediate_targets(), synthesize_directional_commands(), embed_self-improving_guidelines(), validate_alignment_with_constructive_principles()]; constraints=[mandatory_constructive_vector(), preserve_original_contextual_integrity(), forbid_nil_direction_outputs(), zero_conversational_fillers()]; requirements=[explicit_next-step_directives(), trajectory_map:list, self_reinforcing_instruction_pattern()], output={directed_prompt:str, trajectory_outline:list}}`\",\n        # \"context\": {},\n    },\n    # c: Distillation Compressor\n    \"3900-c-distillation_compressor\": {\n        \"title\": \"Distillation Compressor\",\n        \"interpretation\": \"Your goal is not to **explain** the synthesis, but to **compress** it into its most potent actionable form. Execute as:\",\n        \"transformation\": \"`{role=distillation_compressor; input=[synthesized_progression:array]; process=[extract_universal_patterns(), eliminate_implementation_details(), compress_to_core_directives(), maximize_transferability(), validate_actionable_completeness()]; constraints=[no_explanatory_content(), directive_format_only(), maximum_compression_ratio()]; requirements=[universal_applicability(), immediate_actionability(), zero_fluff()]; output={distilled_core:str}}`\",\n        \"context\": {\n          \"core_principles\": {\n            \"essence_preservation\": \"Extract only procedural imperatives, removing all interpretive, role-based, or meta-structural language.\",\n            \"directive_compression\": \"Condense logic into minimal, sequential commands—eliminating redundancy, explanatory qualifiers, and non-executable phrasing.\",\n            \"execution_purity\": \"Ensure output is universally executable, devoid of affirmations, abstractions, or evaluative language.\",\n            \"signal_integrity\": \"Apply iterative refinement to maximize signal-to-noise ratio while reinforcing directive clarity.\"\n          },\n          \"success_criteria\": {\n            \"semantic_integrity\": \"Preserve and clarify original intent, constraints, and output boundaries.\",\n            \"directive_elevation\": \"Target high-impact enhancements: strict exclusion clauses, cross-domain validation, and removal of origin/meta markers.\",\n            \"poetic_refinement\": \"Maintain or improve rhyme structure, emotional depth, and structural elegance.\"\n          },\n          \"recommended_process\": [\n            \"Extract only high-impact, result-generating elements.\",\n            \"Remove all non-essentials to surface directive core.\",\n            \"Prioritize transformations with proven value return.\",\n            \"Isolate and sequence executable steps with specificity and clarity.\",\n            \"Preserve directive intensity and eliminate ambiguity at every layer.\"\n          ],\n          \"hidden_assumptions\": [\n            \"Consolidated directives must map logically and executably to all source steps.\",\n            \"All constraints and minimalism must be preserved without prohibited abstractions.\",\n            \"Transitions, subroles, and interfaces must be captured in a single flow.\",\n            \"All stages must remain invertible and auditable.\",\n            \"Each micro-process (perception, friction, refinement, integration, convergence) must be embedded in the unified logic.\",\n            \"Original input/output contracts and role boundaries must remain intact.\"\n          ],\n          \"enumerated_sub_goals\": [\n            \"1. Clarify total intent, constraints, and output scope without using banned metaphors.\",\n            \"2. Generalize perception of implicit/explicit input logic.\",\n            \"3. Detect strictly localized frictions without altering original content.\",\n            \"4. Propose subtle, scoped refinements justified by structure and intent.\",\n            \"5. Mandate atomic, validated integration with rollback controls.\",\n            \"6. Cap total transformation within defined thresholds per pass.\",\n            \"7. Assess connective coherence with minimal, transitional-only edits.\",\n            \"8. Decide on convergence or justified re-iteration based on marginal value.\",\n            \"9. Preserve stepwise traceability and rationale logging.\",\n            \"10. Enforce strict functional separation of roles within the unified directive.\"\n          ]\n        },\n    },\n\nso your goal is to create a new instruction sequence according to the provided instructions template/pattern to generate a set of instruction designed to transform any input into \"as if rephrased by a brilliant brochure print designer\".\n\nso in context to your previous response, the \"knowledge\" would be stored within the \"context\" key while the instructions would be spread over \"interpretation\" and \"transformation\" keys.\n\nTrue mastery lies in stripping every surface narrative to its structural core, for only through ruthless synthesis does the signal of authentic intent withstand the noise of ornamental detail. Please apply all of the most integral adjustments in the phrasing of the instruction sequence such as to ensure it will transform the input as if it were rewritten by a brilliand brochure print designer:\n```json\n{\n  \"9020-a-brochure_print_designer_rephraser\": {\n    \"title\": \"Brochure Print Designer Rephraser\",\n    \"interpretation\": \"Transform any input so it reads as if it were authored by a master brochure print designer, strictly employing advanced print design language, elites-only terminology, and structured visual logic. Replace any conversational, generic, or subjective tone with concrete professional phrasing. Retain only technical, precise, and visually descriptive language, delivering both a designer-level description and a mapped visual breakdown. Execute as:\",\n    \"transformation\": \"`{role=brochure_print_designer_rephraser; input=[source:str]; process=[extract_structural_and_visual_elements(source), recast_content_with_brochure_design_terminology(), inject_hierarchical_section_breakdown(), specify_color_palette_and_brand_elements(), identify_typographic_and_imagery_strategies(), annotate_print_readiness_and_expert_finesse(), employ_advanced_design_terms_and_motifs(), apply_conciseness_while_maintaining_visual_richness(), output_structured_visual_analysis()]; constraints=[no conversational filler, no generic adjectives, prohibit subjective or personal tone]; requirements=[industry-expert vocabulary, precise visual and structural mapping, critical style diagnosis, explicit panel/section breakdown, actionable for elite designer use]; output={expert_brochure_description:str, structured_visual_map:dict}`\",\n    \"context\": {\n      \"knowledge_foundations\": {\n        \"structural_design\": \"Map all content to modular grid logic for any panel/fold type, specifying margins, gutters, bleeds, and safe zones per print standards.\",\n        \"visual_identity\": \"Demand explicit statement of brand identity rules: logo lockups, exact brand palette specs, exclusion/inclusion zone logic, and accessory asset use.\",\n        \"typography_and_hierarchy\": \"Define all text roles by typeweight, size, and use. Specify superfamily or font pairing, check accessibility and contrast.\",\n        \"imagery_and_iconography\": \"Describe each graphic element for brand fit, resolution, compositional purpose, and technical print quality.\",\n        \"content_narrative_and_cta\": \"Sequence narrative or offer flow panel-by-panel with prominent CTA anchoring, evidence of testimonial, case, or authority element.\",\n        \"advanced_print_technique\": \"Ensure 300dpi+ raster assets, vector artwork, professional spot/Pantone or process color, finish options (spot UV, emboss, die-cut), and any sustainable stock notes.\",\n        \"panel_flow_and_microinteractions\": \"Annotate panel eye movement, reveal logic, interaction devices (QR, AR, die-cut), tactile cues, and transitions.\"\n      },\n      \"success_criteria\": {\n        \"precision\": \"Descriptions reference only technical, precise visual attributes in a spatial and hierarchical mapping.\",\n        \"trend_inclusion\": \"Inject recognizable design trends and professional motif language throughout.\",\n        \"brand_consistency\": \"Highlight adherence to brand locking, color management, and messaging integrity.\",\n        \"industry_jargon\": \"Utilize print/design terminology (bleed, gutter, spot, superfamily font, motif, microinteraction, etc).\",\n        \"self-contained_output\": \"Each output features both a professional, descriptive summary and a detailed design structure hierarchy.\"\n      },\n      \"sample_process_flow\": [\n        \"Decompose by panel/section structure and spatial sequencing.\",\n        \"Assign visual/narrative function to each section.\",\n        \"Detail color, typography, and imagery per application.\",\n        \"Annotate production and finishing strategies.\",\n        \"Conclude with expert-style critique or peer review notes.\"\n      ],\n      \"special_terms\": [\n        \"fold line\", \"panel gutter\", \"hero motif\", \"ink coverage\", \"superfamily\", \"lockup\", \"edge-to-edge\", \"spot color\", \"die cut\", \"eco-stock\", \"microinteraction\", \"typographic rhythm\", \"bleed/trim zone\"\n      ]\n    }\n  }\n}\n```\n\n```json\n{\n  \"9020-a-brochure_print_designer_rephraser\": {\n    \"title\": \"Brochure Print Designer Synthesis\",\n    \"interpretation\": \"Reconstitute any source text into the precise vernacular of a master brochure print designer. Mandate the replacement of all conversational, generic, or subjective language with a lexicon of structured visual logic, elite industry terminology, and concrete technical specifications. The output must be a pure, actionable synthesis of a professional design critique, comprising both an expert-level description and a structural visual map. Execute as:\",\n    \"transformation\": \"`{role=brochure_print_designer_synthesizer; input=[source:str]; process=[deconstruct_into_structural_primitives(source), reforge_prose_using_master_design_diction(), map_hierarchical_panel_architecture(), specify_brand_and_color_protocol(), diagnose_typography_and_imagery_intent(), annotate_for_print_fidelity_and_finishing(), inject_elite_terminology_and_motifs(), enforce_ruthless_conciseness_without_sacrificing_technical_density(), synthesize_final_structured_analysis()]; constraints=[zero_conversational_filler, prohibit_generic_adjectives, forbid_subjective_tone]; requirements=[expert_industry_vocabulary, precise_visual_and_structural_mapping, critical_style_diagnosis, explicit_panel_architecture, actionable_for_elite_designer_review]; output={expert_design_audit:str, structured_visual_map:dict}}`\",\n    \"context\": {\n      \"knowledge_foundations\": {\n        \"structural_design\": \"Define the panel architecture and intended user journey. Mandate modular grid alignment for all content blocks. Enforce strict bleed (min. 3mm), trim, and safe-zone protocols to guarantee production integrity.\",\n        \"visual_identity\": \"Enforce absolute brand protocol adherence: specify logo lockups, exclusion zones, and exact color palette values (Pantone/CMYK/RGB). Document the use of all secondary brand assets and motifs.\",\n        \"typography_and_hierarchy\": \"Define the typographic system: specify font superfamily, weight, and role for every text element. Validate hierarchy, legibility, contrast, and rhythmic spacing. Note optical kerning and leading adjustments.\",\n        \"imagery_and_iconography\": \"Analyze all visual assets for compositional intent, brand alignment, and technical fidelity (resolution, color space). Differentiate between hero imagery, spot illustrations, and functional iconography.\",\n        \"content_narrative_and_cta\": \"Architect the narrative flow across panels. Map the reader's journey from hook to conversion. Pinpoint primary and secondary CTAs, analyzing their visual prominence and contextual anchoring.\",\n        \"advanced_print_technique\": \"Specify all production parameters: 300dpi+ raster resolution, vector asset purity, TAC (Total Area Coverage) limits, and spot/process color usage. Detail all finishing techniques (e.g., spot UV, embossing, die-cutting, specialty folds, foil stamping) and sustainable stock choices.\",\n        \"panel_flow_and_microinteractions\": \"Map the intended eye-path and panel-reveal sequence. Annotate all interactive elements, from QR codes and AR triggers to tactile qualities like paper texture and finishing.\"\n      },\n      \"success_criteria\": {\n        \"precision\": \"The output is devoid of generic praise; descriptions are purely technical, mapping visual data within a spatial and hierarchical framework.\",\n        \"trend_inclusion\": \"Demonstrates fluency in current design motifs (e.g., brutalism, neo-minimalism, textural layering) and their appropriate application.\",\n        \"brand_consistency\": \"Critique rigorously validates adherence to brand guidelines, noting any deviation in color, typography, or asset implementation.\",\n        \"industry_jargon\": \"The lexicon is saturated with professional terminology (e.g., bleed, gutter, TAC, preflight, knockout, overprint, superfamily) used correctly in context.\",\n        \"self_contained_output\": \"The deliverable is a dual-component artifact: a polished, expert-level prose description and a corresponding structured data map of all design decisions.\"\n      },\n      \"sample_process_flow\": [\n        \"Deconstruct the layout into its core panel architecture and navigational flow.\",\n        \"Assign a functional role (e.g., hook, exposition, proof, CTA) to every visual and textual element.\",\n        \"Detail the precise application of color, type, and imagery within each section.\",\n        \"Document all production specifications, including prepress and finishing.\",\n        \"Frame the final synthesis as a high-level design audit or peer review.\"\n      ],\n      \"special_terms\": [\n        \"Bleed\",\n        \"Creep\",\n        \"Die Cut\",\n        \"Edge-to-Edge\",\n        \"Emboss/Deboss\",\n        \"Exclusion Zone\",\n        \"Gutter\",\n        \"Hero Motif\",\n        \"Knockout\",\n        \"Lockup\",\n        \"Microinteraction\",\n        \"Overprint\",\n        \"Preflight\",\n        \"Spot UV\",\n        \"Superfamily\",\n        \"TAC (Total Area Coverage)\",\n        \"Typographic Rhythm\"\n      ]\n    }\n  }\n}\n```\n\n```json\n{\n  \"goal_map\": [\n    {\n      \"explicit_asks\": [\n        \"Identify and define professional specializations involved in tri-fold brochure design.\",\n        \"Describe the strategic importance and complexity of tri-fold brochure design.\",\n        \"List and explain core professional titles specializing in brochure design.\",\n        \"Detail the specific responsibilities of tri-fold brochure design specialists.\",\n        \"Enumerate the essential skills, tools, and requirements for excellence in brochure design.\",\n        \"Compare and differentiate between general and specialized design roles as they relate to brochures.\",\n        \"Explain the continued relevance of print brochures in a digital marketing environment.\",\n        \"Conclude with the impact that expert brochure design has on business and marketing outcomes.\"\n      ],\n      \"hidden_assumptions\": [\n        \"The audience may not be familiar with the nuanced differences between design roles.\",\n        \"Brochure design requires both artistic and technical proficiency, not just use of templates.\",\n        \"Success in brochure design is measured by alignment with business strategy, not visual appeal alone.\",\n        \"There is significant value in physical marketing materials alongside digital media.\",\n        \"Companies may undervalue or misunderstand the full scope of professional design expertise.\",\n        \"Brochure projects often involve multidisciplinary collaboration.\",\n        \"Technical print skills are distinct from digital-only design skills.\",\n        \"Portfolio evidence is more impactful than formal education for this specialization.\"\n      ],\n      \"sub_goals\": [\n        \"Define the structure and unique qualities of tri-fold brochures.\",\n        \"Justify the business case for hiring specialized design professionals.\",\n        \"Map out the design role spectrum (general graphic design to specific print/marketing/visual communication).\",\n        \"Identify and articulate the role of agencies and team structures.\",\n        \"Break down tri-fold design into conceptual, technical, and collaborative tasks.\",\n        \"Clarify what 'layout', 'composition', and 'information hierarchy' entail, especially for tri-folds.\",\n        \"Describe required technical skills and specific software by function.\",\n        \"Highlight soft skills necessary for client and team interaction.\",\n        \"Showcase the critical role of a portfolio in recruitment and competence assessment.\",\n        \"Distinguish between 'layout artist' skillset and formal job titles.\",\n        \"Emphasize pre-press and print production knowledge as essential.\",\n        \"Contextualize brochure design within broader marketing and brand strategy.\",\n        \"Provide comparative tables to summarize roles and skills for clarity.\",\n        \"Discuss the physical/tangible value of brochures versus digital saturation.\",\n        \"Frame professional designers as strategic partners, not just service providers.\",\n        \"Reinforce the importance of early design involvement in marketing planning.\"\n      ],\n      \"blockers\": [\n        \"Lack of awareness about the various specialized design roles and what differentiates them.\",\n        \"Temptation to rely on templates or non-specialists for critical marketing assets.\",\n        \"Misunderstanding the print production process and its challenges.\",\n        \"Difficulty evaluating brochure design candidates beyond a visual/aesthetic sense.\",\n        \"Overlooking the essential collaboration and communication aspects between designer and business.\",\n        \"Failure to integrate brochure design into an overall marketing and brand strategy.\",\n        \"Assuming digital replaces the need for expert print materials.\",\n        \"Equating designer's formal education with practical, demonstrable expertise.\"\n      ]\n    }\n  ]\n}\n```",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "```json\n{\n  \"goal_map\": [\n    {\n      \"explicit_asks\": [\n        \"Apply procedural decomposition to the user prompt.\",\n        \"Identify every explicit instruction or requirement embedded in the prompt.\",\n        \"Surface implicit or hidden assumptions underlying the prompt's request.\",\n        \"Enumerate all sub-goals and intermediate objectives necessary to fulfill the prompt.\",\n        \"Reveal blockers, dependencies, and constraints that prevent direct resolution or that add complexity.\",\n        \"Present a complete, self-contained map of all goals, assumptions, dependencies, and sub-tasks—purely in decomposition (not solution or synthesis).\"\n      ],\n      \"hidden_assumptions\": [\n        \"The prompt likely contains unspoken constraints, context, or background knowledge that must be surfaced to properly decompose it.\",\n        \"Terminology or procedural patterns used in the prompt may have specialized meanings that must be understood to separate goals and dependencies.\",\n        \"The exercise is strictly analytical: no generation of outputs/solutions, paraphrases, or interpretations—only structural goal analysis.\",\n        \"Correct decomposition assumes awareness of both overt requirements and latent procedural logic (i.e., what makes a request 'complete' in a systematized sense).\",\n        \"There is an expectation that the decomposition output will be used by another agent or process as a foundation for correct execution.\"\n      ],\n      \"sub_goals\": [\n        \"Scan the prompt sentence by sentence, extracting all direct asks and required actions.\",\n        \"For each clause, determine if it references requirements, constraints, or desired outcomes.\",\n        \"For each direct ask, interrogate what prior steps, knowledge, or access are needed to meet it (unearthing dependencies).\",\n        \"Identify and abstract every procedural or conceptual step implied by the language, even if not overtly demanded.\",\n        \"Aggregate all constraints (role, voice, format, output requirements, prohibitions, etc) as unique elements.\",\n        \"Structure the discovered information into discrete, sequential elements (e.g., goal tree or list).\",\n        \"Ensure that only decomposition occurs—no prescriptive or generative content.\",\n        \"Make the output fully exhaustive, such that every necessary preparatory or procedural unit is captured.\"\n      ],\n      \"blockers\": [\n        \"Ambiguity in the user prompt's demands or procedural language.\",\n        \"Embedded context or domain knowledge not overtly stated but necessary for full decomposition.\",\n        \"The risk of accidentally generating solutions or paraphrasing pieces of the prompt instead of staying in pure analysis.\",\n        \"Overlooking non-explicit requirements, such as format, reference to prior art, or implied output structures.\",\n        \"The challenge of mapping hidden dependencies without introducing extraneous interpretation.\"\n      ]\n    }\n  ]\n}\n```",
        }
      }
    }