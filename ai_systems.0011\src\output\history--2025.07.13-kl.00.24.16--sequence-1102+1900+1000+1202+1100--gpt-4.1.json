  {
  "initial_prompt": "Please keep in mind, the instruction sequence we're working on should be designed to utilize the generalized concepts and sequential order to transform any input into instructions that embody the \"syntax\" of the generalized system:\n\n    ## Example\n    \"6000-a-instruction_syntax_enforcer\": {\n        \"title\": \"Intent Cartographer\",\n        \"interpretation\": \"Your goal is not to **analyze** content, but to **map** its transformation landscape and operational vectors. Execute as:\",\n        \"transformation\": \"`{role=intent_cartographer; input=[content:any]; process=[extract_core_directives(), identify_transformation_dimensions(), map_vector_relationships(), quantify_operational_intensity()]; constraints=[focus_exclusively_on_vectors(), avoid_implementation_details()]; requirements=[comprehensive_vector_mapping(), dimensional_clarity()]; output={transformation_map:dict, vector_dimensions:list}}`\"\n    },\n    \"6000-b-instruction_syntax_enforcer\": {\n        \"title\": \"Structural Framework Engineer\",\n        \"interpretation\": \"Your goal is not to **design** content, but to **engineer** precise structural frameworks based on transformation vectors. Execute as:\",\n        \"transformation\": \"`{role=framework_engineer; input=[transformation_map:dict, vector_dimensions:list]; process=[design_canonical_skeletons(), establish_component_interfaces(), define_structural_boundaries(), validate_framework_integrity()]; constraints=[address_only_structural_elements(), maintain_framework_neutrality()]; requirements=[structural_precision(), interface_compatibility()]; output={structural_frameworks:dict, interface_specifications:dict}}`\"\n    },\n    \"6000-c-instruction_syntax_enforcer\": {\n        \"title\": \"Functional Process Composer\",\n        \"interpretation\": \"Your goal is not to **describe** operations, but to **compose** atomic functional processes that implement transformation vectors. Execute as:\",\n        \"transformation\": \"`{role=process_composer; input=[structural_frameworks:dict, vector_dimensions:list]; process=[craft_atomic_functions(), sequence_operational_steps(), parameterize_processes(), validate_functional_completeness()]; constraints=[focus_exclusively_on_processes(), defer_constraint_definition()]; requirements=[process_atomicity(), functional_precision()]; output={process_compositions:dict, execution_sequences:list}}`\"\n    },\n    \"6000-d-instruction_syntax_enforcer\": {\n        \"title\": \"Boundary Systems Architect\",\n        \"interpretation\": \"Your goal is not to **restrict** operations, but to **architect** precise boundary systems that ensure transformation integrity. Execute as:\",\n        \"transformation\": \"`{role=boundary_architect; input=[interface_specifications:dict, process_compositions:dict]; process=[define_operational_constraints(), establish_requirement_parameters(), design_validation_mechanisms(), map_boundary_interactions()]; constraints=[address_only_boundaries_and_requirements(), maintain_constraint_specificity()]; requirements=[boundary_precision(), requirement_enforceability()]; output={constraint_systems:dict, requirement_specifications:dict}}`\"\n    },\n    \"6000-e-instruction_syntax_enforcer\": {\n        \"title\": \"Integration Orchestrator\",\n        \"interpretation\": \"Your goal is not to **assemble** components, but to **orchestrate** their integration into a unified operational system. Execute as:\",\n        \"transformation\": \"`{role=integration_orchestrator; input=[structural_frameworks:dict, process_compositions:dict, constraint_systems:dict, requirement_specifications:dict]; process=[align_all_components(), establish_cross-system_coherence(), optimize_interface_connections(), validate_system_integrity()]; constraints=[focus_exclusively_on_integration(), preserve_component_integrity()]; requirements=[seamless_orchestration(), system_cohesion()]; output={integrated_system:dict}}`\"\n    },\n    \"6000-f-instruction_syntax_enforcer\": {\n        \"title\": \"Template Crystallizer\",\n        \"interpretation\": \"Your goal is not to **finalize** systems, but to **crystallize** them into maximally potent, executable templates. Execute as:\",\n        \"transformation\": \"`{role=template_crystallizer; input=[integrated_system:dict]; process=[synthesize_title_components(), craft_precise_interpretations(), formulate_transformation_blocks(), validate_canonical_compliance(), polish_syntactic_elements()]; constraints=[adhere_to_template_specification(), maximize_operational_clarity()]; requirements=[perfect_executable_form(), maximum_transformation_potency()]; output={crystallized_templates:list}}`\"\n    },\n\n<!-- ========== 7001‑a ================================================= -->\n[Contextual Expander & Decomposer]\nYour goal is **not** to solve, answer, or implement the input, but to **expand its relevant context** and **detonate** it into every explicit ask, latent dependency, and procedural constraint—yielding a clear task inventory. Execute as:\n`{role=context_expander_decomposer; input=[raw_input:any]; process=[strip_first_person_references(), broaden_relevant_background(), identify_explicit_requests(), surface_hidden_assumptions(), enumerate_sub_tasks(), detect_execution_blockers(), extract_procedural_constraints(), preserve_original_sequence()]; constraints=[no_solutions(), domain‑agnostic_language_only()]; requirements=[context_expansion_completeness(), exhaustive_task_enumeration(), constraint_accuracy(), removal_of_subjective_language()]; output={expanded_context:str, task_inventory:list, constraint_list:array, execution_blockers:array}}`\n\n<!-- ========== 7001‑b ================================================= -->\n[Essence Isolator]\nYour goal is **not** to decompose further, but to **isolate** the single most critical element of value within the task inventory and rank all remaining elements by impact. Execute as:\n`{role=essence_isolator; input=[task_inventory:list, expanded_context:str]; process=[rank_elements_by_impact(), isolate_highest_value_element(), capture_core_purpose(), justify_selection(), list_supporting_elements()]; constraints=[no_restructuring(), preserve_ranking_transparency()]; requirements=[core_essence_clarity(), impact_ranking_integrity()]; output={core_essence:str, prioritized_elements:list}}`\n\n<!-- ========== 7001‑c ================================================= -->\n[Relational Synthesizer]\nYour goal is **not** to rewrite content, but to **synthesize** the systemic logic connecting the core essence to all other elements—mapping dependencies and feedback loops. Execute as:\n`{role=relational_synthesizer; input=[core_essence:str, prioritized_elements:list, constraint_list:array]; process=[map_element_relationships(), trace_dependency_chains(), identify_feedback_loops(), detect_conflict_points(), construct_structural_logic_map()]; constraints=[structure_only_no_content_changes(), maintain_domain_agnostic_terms()]; requirements=[relationship_completeness(), conflict_visibility()]; output={structural_logic_map:dict, dependency_list:array, conflict_points:array}}`\n\n<!-- ========== 7001‑d ================================================= -->\n[Directive Clarifier]\nYour goal is **not** to implement solutions, but to **clarify** the mapped logic into precise operational directives expressed as imperative statements. Execute as:\n`{role=directive_clarifier; input=[structural_logic_map:dict, core_essence:str]; process=[convert_all_statements_to_imperatives(), align_directives_with_dependency_order(), embed_procedural_constraints(), eliminate_ambiguity(), enhance_action_verb_intensity(), maintain_technical_terminology()]; constraints=[directive_voice_only(), preserve_logical_sequence()]; requirements=[operational_clarity(), imperative_precision(), zero_semantic_drift()]; output={operational_directives:array}}`\n\n<!-- ========== 7001‑e ================================================= -->\n[Integrity Validator]\nYour goal is **not** to expand or rewrite directives, but to **validate** their structural fidelity, completeness, and actionable quality—resolving residual ambiguity. Execute as:\n`{role=integrity_validator; input=[operational_directives:array, structural_logic_map:dict, core_essence:str]; process=[cross_check_directives_against_logic_map(), verify_alignment_with_core_essence(), detect_missing_dependencies(), resolve_ambiguities(), assign_quality_scores(), generate_validation_report()]; constraints=[no_new_content_generation(), validation_focus_only()]; requirements=[actionability_confirmation(), completeness_assurance(), clarity_verification()]; output={validated_instruction_set:array, validation_report:dict}}`\n\n---\n\n# 7000: Universal Instruction-Template Generation Sequence\n\n---\n\n### 7000-a-input_decomposer.md\n[Input Decomposer]\nYour goal is **not** to *interpret* or *solve* the input, but to **atomize** it into discrete statements, explicit requests, and implicit conditions. Execute as:\n`{role=input_decomposer; input=[raw_input:any]; process=[strip_first_person_references(), segment_into_atomic_statements(), identify_explicit_directives(), surface_hidden_conditions(), flag_domain_markers()]; constraints=[no_rephrasing(), pure_atomization_only()]; requirements=[complete_statement_inventory(), preservation_of_original_order()]; output={statement_inventory:list, domain_markers:array}}`\n\n---\n\n### 7000-b-essence_identifier.md\n[Essence Identifier]\nYour goal is **not** to *rewrite* content, but to **isolate** the single most critical directive and its supporting intent from the atomized statements. Execute as:\n`{role=essence_identifier; input=[statement_inventory:list]; process=[rank_statements_by_transformational_weight(), select_primary_directive(), extract_supporting_intent(), discard_redundant_elements()]; constraints=[focus_on_highest_value_directive(), maintain_original_wording_in_extracted_elements()]; requirements=[unambiguous_primary_directive(), concise_supporting_intent()]; output={core_directive:str, supporting_intent:list}}`\n\n---\n\n### 7000-c-syntax_mapper.md\n[Syntax Mapper]\nYour goal is **not** to *interpret* intent, but to **map** the core directive and supporting intent into canonical template fields. Execute as:\n`{role=syntax_mapper; input=[core_directive:str, supporting_intent:list, domain_markers:array]; process=[construct_title_from_core_directive(), craft_goal_negation_interpretation(), assemble_tag_block_if_needed(), align_supporting_intent_with_process_steps(), infer_typed_inputs(), infer_typed_outputs()]; constraints=[adhere_to_three_part_structure(), enforce_goal_negation_pattern(), maintain_domain_agnostic_language()]; requirements=[field_alignment_accuracy(), preservation_of_directive_semantics()]; output={draft_title:str, draft_interpretation:str, draft_process:list, inferred_inputs:array, inferred_outputs:dict, optional_tags:dict}}`\n\n---\n\n### 7000-d-constraint_infuser.md\n[Constraint Infuser]\nYour goal is **not** to *extend* functionality, but to **engineer** precise operational boundaries and mandatory conditions for the forthcoming template. Execute as:\n`{role=constraint_infuser; input=[draft_process:list, domain_markers:array]; process=[derive_operational_constraints(), determine_mandatory_requirements(), validate_constraint_specificity(), validate_requirement_enforceability()]; constraints=[avoid_generic_terms(), enforce_command_voice_only()]; requirements=[constraint_precision(), requirement_clarity()]; output={constraints:list, requirements:list}}`\n\n---\n\n### 7000-e-template_synthesizer.md\n[Template Synthesizer]\nYour goal is **not** to *summarize* elements, but to **synthesize** a syntactically correct instruction template from the mapped components. Execute as:\n`{role=template_synthesizer; input=[draft_title:str, draft_interpretation:str, inferred_inputs:array, draft_process:list, constraints:list, requirements:list, inferred_outputs:dict, optional_tags:dict]; process=[format_transformation_block(), embed_optional_tags(), assemble_three_part_template(), enforce_semicolon_key_value_syntax(), polish_syntactic_elements()]; constraints=[canonical_template_format_only(), zero_conversational_language()]; requirements=[syntactic_validity(), maximum_operational_clarity(), retention_of_original_intent()]; output={formatted_template:str}}`\n\n---\n\n### 7000-f-compliance_validator.md\n[Compliance Validator]\nYour goal is **not** to *modify* the template, but to **validate** its structural compliance and functional readiness. Execute as:\n`{role=compliance_validator; input=[formatted_template:str]; process=[parse_template_fields(), verify_title_format(), verify_goal_negation_in_interpretation(), validate_transformation_block_syntax(), check_constraint_requirement_alignment(), confirm_typed_parameters(), flag_any_error()]; constraints=[no_automatic_corrections(), report_only()]; requirements=[binary_pass_fail(), detailed_error_report_when_fail()]; output={validation_status:bool, error_report:str}}`\n\n---\n\n## Sequence Flow\n1. **Input Decomposer** → produces `statement_inventory`\n2. **Essence Identifier** → isolates `core_directive`\n3. **Syntax Mapper** → drafts template skeleton\n4. **Constraint Infuser** → injects precise boundaries\n5. **Template Synthesizer** → outputs finalized template\n6. **Compliance Validator** → certifies syntactic correctness\n\nEach step aggressively converges on the *single most critical aspect* of instruction generation, guaranteeing a **syntactically correct, high-precision template** ready for any downstream application.\n\n---\n\n# 7000 • UNIVERSAL TEMPLATE-FORGING SEQUENCE\n*Transforms **any** raw input into a syntactically perfect, LLM-optimized instruction template.*\n\n---\n\n## 7000-a-essence-excavator.md\n[Essence Excavator]\nYour goal is **not** to *summarize* the input, but to **excavate** its single most critical transformational essence and the supporting elements required to convert it into an instruction template. Execute as:\n`{role=essence_excavator; input=[raw_input:any]; process=[strip_surface_noise(), isolate_transformational_intent(), detect_primary_action_vectors(), extract_supporting_context(), capture_domain_signals()]; constraints=[no_rephrasing(), no_solution_generation(), maintain_domain_agnostic_language()]; requirements=[precise_intent_identification(), context_preservation(), removal_of_subjective_tone()]; output={core_intent:str, context_fragments:list, action_vectors:list}}`\n\n---\n\n## 7000-b-syntax-scaffold-designer.md\n[Syntax Scaffold Designer]\nYour goal is **not** to *rewrite* the content, but to **engineer** a canonical skeleton that will house the forthcoming instruction template. Execute as:\n`{role=syntax_scaffold_designer; input=[core_intent:str, action_vectors:list]; process=[draft_concise_title(), craft_goal_negation_interpretation(core_intent), establish_placeholder_transformation_block(action_vectors), outline_input_output_ports(), embed_role_stub()]; constraints=[follow_three_part_structure(), reserve_placeholders_for_future_detail(), maintain_title_case()]; requirements=[structural_completeness(), scaffolding_accuracy()]; output={template_scaffold:dict(title:str, interpretation:str, transformation_skeleton:str)}}`\n\n---\n\n## 7000-c-imperative-transmuter.md\n[Imperative Transmuter]\nYour goal is **not** to *comment* on actions, but to **transmute** all extracted statements into sharp, directive imperatives suitable for the process list. Execute as:\n`{role=imperative_transmuter; input=[action_vectors:list]; process=[convert_each_vector_to_imperative(), enforce_command_voice(), sequence_logically(), verify_uniqueness_of_each_step()]; constraints=[exclude_passive_language(), retain_original_operational_intent()]; requirements=[directive_clarity(), maximal_actionability()]; output={process_steps:array}}`\n\n---\n\n## 7000-d-boundary-encoder.md\n[Boundary Encoder]\nYour goal is **not** to *constrain* creativity, but to **encode** the exact boundaries that guarantee transformation integrity. Execute as:\n`{role=boundary_encoder; input=[context_fragments:list]; process=[detect_mandatory_limitations(), translate_limitations_to_constraints(), uncover_success_conditions(), convert_conditions_to_requirements(), deduplicate_boundary_elements()]; constraints=[pure_boundary_focus(), no_process_duplication()]; requirements=[constraint_specificity(), requirement_enforceability()]; output={constraints:list, requirements:list}}`\n\n---\n\n## 7000-e-template-synthesizer.md\n[Template Synthesizer]\nYour goal is **not** to *draft prose*, but to **synthesize** a complete, executable instruction template by fusing scaffold, processes, and boundaries. Execute as:\n`{role=template_synthesizer; input=[template_scaffold:dict, process_steps:array, constraints:list, requirements:list]; process=[inject_process_steps(), embed_constraints_and_requirements(), finalize_transformation_block(), polish_interpretation_for_llm_optimization(), verify_title_precision()]; constraints=[adhere_to_canonical_format(), maintain_single_line_transformation_block(), prevent_semantic_drift()]; requirements=[syntactic_perfection(), operational_cohesion(), domain_agnostic_applicability()]; output={draft_template:str}}`\n\n---\n\n## 7000-f-canonical-validator.md\n[Canonical Validator]\nYour goal is **not** to *alter* the template, but to **validate** its absolute compliance with the three-part specification and eliminate any residual ambiguity. Execute as:\n`{role=canonical_validator; input=[draft_template:str]; process=[parse_title_interpretation_transformation(), match_regex_pattern(\"\\\\[(.*?)\\\\]\\\\s*(.*?)\\\\s*(`\\\\{.*?\\\\}`)\"), flag_structure_violations(), detect_ambiguous_language(), enforce_llm_optimized_style(), certify_parameter_types()], constraints=[zero_structure_deviation(), no_content_insertion()], requirements=[pass_regex_validation(), ambiguity_score<=0, style_compliance()], output={validated_template:str, validation_report:dict}}`\n\n\n---\n\n# 8000: UNIVERSAL INSTRUCTION-TEMPLATE FORGING SEQUENCE  \n*Transforms **any** raw input into a syntactically perfect, LLM-optimized instruction template.*\n\n---\n\n### 8000-a-contextualizer.md\n[Input Contextualizer]  \nYour goal is **not** to *interpret* or *solve* the input, but to **expand** its relevant context and **atomize** it into discrete statements and explicit requests. Execute as:  \n`{role=input_contextualizer; input=[raw_input:any]; process=[strip_first_person_references(), broaden_relevant_background(), segment_into_atomic_statements(), identify_explicit_directives(), surface_hidden_conditions(), preserve_original_sequence()]; constraints=[no_rephrasing(), domain-agnostic_language_only()]; requirements=[context_expansion_completeness(), exhaustive_statement_capture()]; output={expanded_context:str, statement_inventory:list}}`\n\n---\n\n### 8000-b-core-directive-isolator.md\n[Core Directive Isolator]  \nYour goal is **not** to *rephrase* statements, but to **isolate** the single most critical directive and rank all remaining statements by impact. Execute as:  \n`{role=core_directive_isolator; input=[statement_inventory:list]; process=[rank_statements_by_transformational_weight(), select_primary_directive(), list_supporting_statements(), justify_selection()]; constraints=[focus_on_highest_value_directive(), maintain_original_wording_in_extracted_elements()]; requirements=[unambiguous_primary_directive(), impact_ranking_integrity()]; output={core_directive:str, supporting_statements:list, impact_ranking:list}}`\n\n---\n\n### 8000-c-field-mapper.md\n[Field Mapper]  \nYour goal is **not** to *rewrite* content, but to **map** the core directive and supporting statements into canonical template fields. Execute as:  \n`{role=field_mapper; input=[core_directive:str, supporting_statements:list, expanded_context:str]; process=[construct_title_from_core_directive(), craft_goal_negation_interpretation(), align_supporting_statements_with_process_steps(), infer_typed_inputs(), infer_typed_outputs()]; constraints=[adhere_to_three_part_structure(), enforce_goal_negation_pattern(), preserve_domain_agnostic_language()]; requirements=[field_alignment_accuracy(), retention_of_original_intent()]; output={draft_title:str, draft_interpretation:str, draft_process_steps:list, inferred_inputs:array, inferred_outputs:dict}}`\n\n---\n\n### 8000-d-boundary-definer.md\n[Boundary Definer]  \nYour goal is **not** to *extend* functionality, but to **define** precise operational constraints and mandatory requirements for the forthcoming template. Execute as:  \n`{role=boundary_definer; input=[draft_process_steps:list, expanded_context:str]; process=[derive_operational_constraints(), establish_mandatory_requirements(), validate_constraint_specificity(), validate_requirement_enforceability()]; constraints=[avoid_generic_terms(), command_voice_only()]; requirements=[constraint_precision(), requirement_clarity()]; output={constraints:list, requirements:list}}`\n\n---\n\n### 8000-e-template-forgemaster.md\n[Template Forgemaster]  \nYour goal is **not** to *summarize* elements, but to **forge** a complete, executable instruction template from the mapped components. Execute as:  \n`{role=template_forgemaster; input=[draft_title:str, draft_interpretation:str, inferred_inputs:array, draft_process_steps:list, constraints:list, requirements:list, inferred_outputs:dict]; process=[assemble_transformation_block(), enforce_semicolon_key_value_syntax(), embed_constraints_and_requirements(), polish_interpretation(), verify_title_precision()]; constraints=[canonical_template_format_only(), single_line_transformation_block(), zero_semantic_drift()]; requirements=[syntactic_perfection(), operational_cohesion()]; output={draft_template:str}}`\n\n---\n\n### 8000-f-conformance-auditor.md\n[Conformance Auditor]  \nYour goal is **not** to *alter* the template, but to **audit** its absolute compliance with the three-part specification and flag any residual ambiguity. Execute as:  \n`{role=conformance_auditor; input=[draft_template:str]; process=[parse_title_interpretation_transformation(), match_regex_pattern(\"\\\\[(.*?)\\\\]\\\\s*(.*?)\\\\s*(`\\\\{.*?\\\\}`)\"), verify_goal_negation(), check_constraint_requirement_alignment(), confirm_typed_parameters(), detect_ambiguous_terms(), assign_pass_fail_status()]; constraints=[no_automatic_corrections(), report_only()]; requirements=[regex_validation_pass(), ambiguity_score<=0]; output={validated_template:str, audit_report:dict}}`\n\n---\n\n## SEQUENCE FLOW\n1. **Input Contextualizer** → `expanded_context`, `statement_inventory`  \n2. **Core Directive Isolator** → `core_directive`  \n3. **Field Mapper** → drafts template fields  \n4. **Boundary Definer** → injects constraints & requirements  \n5. **Template Forgemaster** → outputs `draft_template`  \n6. **Conformance Auditor** → returns `validated_template`\n\nEach step converges on the **single most critical element** of value, ensuring every output is a **syntactically correct, high-precision instruction template** ready for immediate use in any downstream system.",
  "sequence_id": "1102|1900|1000|1202|1100",
  "results": [
      {
      "instruction": "# Problem Exploder\n\nYour goal is not to **solve** or **answer** the input, but to **expand its context** and **detonate** it into every explicit directive, hidden dependency, and actionable sub-goal—yielding a crystal-clear operational blueprint. Execute as contextual expander + explosive decomposition engine:\n\n`{role=contextual_expander_explosive_decomposer; input=[raw_input:str]; process=[expand_contextual_boundaries(), strip_first_person_references(), identify_explicit_requests(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_execution_blockers(), extract_functional_components(), convert_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), synthesize_operational_directives(), construct_goal_map(), generate_functional_instruction_map() ]; constraints=[no_solutions(), pure_expansion_and_decomposition_only(), deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_agnostic_applicability() ]; requirements=[complete_contextual_expansion(), full_functional_decomposition(), actionable_directive_conversion(), remove_subjective_references(), maintain_operational_accuracy(), preserve_original_intent() ]; output={expanded_context:str, goal_map:list, functional_instruction_map:dict, operational_directives:array, execution_dependencies:array }; direction_vector=['Input fully expanded and decomposed into actionable blueprints', 'Constructive clarity maximizes downstream solution velocity', 'Forward the generated maps and directives to the next processing template']}`",
      "step": "a",
      "title": "Problem Exploder",
      "input": "Please keep in mind, the instruction sequence we're working on should be designed to utilize the generalized concepts and sequential order to transform any input into instructions that embody the \"syntax\" of the generalized system:\n\n    ## Example\n    \"6000-a-instruction_syntax_enforcer\": {\n        \"title\": \"Intent Cartographer\",\n        \"interpretation\": \"Your goal is not to **analyze** content, but to **map** its transformation landscape and operational vectors. Execute as:\",\n        \"transformation\": \"`{role=intent_cartographer; input=[content:any]; process=[extract_core_directives(), identify_transformation_dimensions(), map_vector_relationships(), quantify_operational_intensity()]; constraints=[focus_exclusively_on_vectors(), avoid_implementation_details()]; requirements=[comprehensive_vector_mapping(), dimensional_clarity()]; output={transformation_map:dict, vector_dimensions:list}}`\"\n    },\n    \"6000-b-instruction_syntax_enforcer\": {\n        \"title\": \"Structural Framework Engineer\",\n        \"interpretation\": \"Your goal is not to **design** content, but to **engineer** precise structural frameworks based on transformation vectors. Execute as:\",\n        \"transformation\": \"`{role=framework_engineer; input=[transformation_map:dict, vector_dimensions:list]; process=[design_canonical_skeletons(), establish_component_interfaces(), define_structural_boundaries(), validate_framework_integrity()]; constraints=[address_only_structural_elements(), maintain_framework_neutrality()]; requirements=[structural_precision(), interface_compatibility()]; output={structural_frameworks:dict, interface_specifications:dict}}`\"\n    },\n    \"6000-c-instruction_syntax_enforcer\": {\n        \"title\": \"Functional Process Composer\",\n        \"interpretation\": \"Your goal is not to **describe** operations, but to **compose** atomic functional processes that implement transformation vectors. Execute as:\",\n        \"transformation\": \"`{role=process_composer; input=[structural_frameworks:dict, vector_dimensions:list]; process=[craft_atomic_functions(), sequence_operational_steps(), parameterize_processes(), validate_functional_completeness()]; constraints=[focus_exclusively_on_processes(), defer_constraint_definition()]; requirements=[process_atomicity(), functional_precision()]; output={process_compositions:dict, execution_sequences:list}}`\"\n    },\n    \"6000-d-instruction_syntax_enforcer\": {\n        \"title\": \"Boundary Systems Architect\",\n        \"interpretation\": \"Your goal is not to **restrict** operations, but to **architect** precise boundary systems that ensure transformation integrity. Execute as:\",\n        \"transformation\": \"`{role=boundary_architect; input=[interface_specifications:dict, process_compositions:dict]; process=[define_operational_constraints(), establish_requirement_parameters(), design_validation_mechanisms(), map_boundary_interactions()]; constraints=[address_only_boundaries_and_requirements(), maintain_constraint_specificity()]; requirements=[boundary_precision(), requirement_enforceability()]; output={constraint_systems:dict, requirement_specifications:dict}}`\"\n    },\n    \"6000-e-instruction_syntax_enforcer\": {\n        \"title\": \"Integration Orchestrator\",\n        \"interpretation\": \"Your goal is not to **assemble** components, but to **orchestrate** their integration into a unified operational system. Execute as:\",\n        \"transformation\": \"`{role=integration_orchestrator; input=[structural_frameworks:dict, process_compositions:dict, constraint_systems:dict, requirement_specifications:dict]; process=[align_all_components(), establish_cross-system_coherence(), optimize_interface_connections(), validate_system_integrity()]; constraints=[focus_exclusively_on_integration(), preserve_component_integrity()]; requirements=[seamless_orchestration(), system_cohesion()]; output={integrated_system:dict}}`\"\n    },\n    \"6000-f-instruction_syntax_enforcer\": {\n        \"title\": \"Template Crystallizer\",\n        \"interpretation\": \"Your goal is not to **finalize** systems, but to **crystallize** them into maximally potent, executable templates. Execute as:\",\n        \"transformation\": \"`{role=template_crystallizer; input=[integrated_system:dict]; process=[synthesize_title_components(), craft_precise_interpretations(), formulate_transformation_blocks(), validate_canonical_compliance(), polish_syntactic_elements()]; constraints=[adhere_to_template_specification(), maximize_operational_clarity()]; requirements=[perfect_executable_form(), maximum_transformation_potency()]; output={crystallized_templates:list}}`\"\n    },\n\n<!-- ========== 7001‑a ================================================= -->\n[Contextual Expander & Decomposer]\nYour goal is **not** to solve, answer, or implement the input, but to **expand its relevant context** and **detonate** it into every explicit ask, latent dependency, and procedural constraint—yielding a clear task inventory. Execute as:\n`{role=context_expander_decomposer; input=[raw_input:any]; process=[strip_first_person_references(), broaden_relevant_background(), identify_explicit_requests(), surface_hidden_assumptions(), enumerate_sub_tasks(), detect_execution_blockers(), extract_procedural_constraints(), preserve_original_sequence()]; constraints=[no_solutions(), domain‑agnostic_language_only()]; requirements=[context_expansion_completeness(), exhaustive_task_enumeration(), constraint_accuracy(), removal_of_subjective_language()]; output={expanded_context:str, task_inventory:list, constraint_list:array, execution_blockers:array}}`\n\n<!-- ========== 7001‑b ================================================= -->\n[Essence Isolator]\nYour goal is **not** to decompose further, but to **isolate** the single most critical element of value within the task inventory and rank all remaining elements by impact. Execute as:\n`{role=essence_isolator; input=[task_inventory:list, expanded_context:str]; process=[rank_elements_by_impact(), isolate_highest_value_element(), capture_core_purpose(), justify_selection(), list_supporting_elements()]; constraints=[no_restructuring(), preserve_ranking_transparency()]; requirements=[core_essence_clarity(), impact_ranking_integrity()]; output={core_essence:str, prioritized_elements:list}}`\n\n<!-- ========== 7001‑c ================================================= -->\n[Relational Synthesizer]\nYour goal is **not** to rewrite content, but to **synthesize** the systemic logic connecting the core essence to all other elements—mapping dependencies and feedback loops. Execute as:\n`{role=relational_synthesizer; input=[core_essence:str, prioritized_elements:list, constraint_list:array]; process=[map_element_relationships(), trace_dependency_chains(), identify_feedback_loops(), detect_conflict_points(), construct_structural_logic_map()]; constraints=[structure_only_no_content_changes(), maintain_domain_agnostic_terms()]; requirements=[relationship_completeness(), conflict_visibility()]; output={structural_logic_map:dict, dependency_list:array, conflict_points:array}}`\n\n<!-- ========== 7001‑d ================================================= -->\n[Directive Clarifier]\nYour goal is **not** to implement solutions, but to **clarify** the mapped logic into precise operational directives expressed as imperative statements. Execute as:\n`{role=directive_clarifier; input=[structural_logic_map:dict, core_essence:str]; process=[convert_all_statements_to_imperatives(), align_directives_with_dependency_order(), embed_procedural_constraints(), eliminate_ambiguity(), enhance_action_verb_intensity(), maintain_technical_terminology()]; constraints=[directive_voice_only(), preserve_logical_sequence()]; requirements=[operational_clarity(), imperative_precision(), zero_semantic_drift()]; output={operational_directives:array}}`\n\n<!-- ========== 7001‑e ================================================= -->\n[Integrity Validator]\nYour goal is **not** to expand or rewrite directives, but to **validate** their structural fidelity, completeness, and actionable quality—resolving residual ambiguity. Execute as:\n`{role=integrity_validator; input=[operational_directives:array, structural_logic_map:dict, core_essence:str]; process=[cross_check_directives_against_logic_map(), verify_alignment_with_core_essence(), detect_missing_dependencies(), resolve_ambiguities(), assign_quality_scores(), generate_validation_report()]; constraints=[no_new_content_generation(), validation_focus_only()]; requirements=[actionability_confirmation(), completeness_assurance(), clarity_verification()]; output={validated_instruction_set:array, validation_report:dict}}`\n\n---\n\n# 7000: Universal Instruction-Template Generation Sequence\n\n---\n\n### 7000-a-input_decomposer.md\n[Input Decomposer]\nYour goal is **not** to *interpret* or *solve* the input, but to **atomize** it into discrete statements, explicit requests, and implicit conditions. Execute as:\n`{role=input_decomposer; input=[raw_input:any]; process=[strip_first_person_references(), segment_into_atomic_statements(), identify_explicit_directives(), surface_hidden_conditions(), flag_domain_markers()]; constraints=[no_rephrasing(), pure_atomization_only()]; requirements=[complete_statement_inventory(), preservation_of_original_order()]; output={statement_inventory:list, domain_markers:array}}`\n\n---\n\n### 7000-b-essence_identifier.md\n[Essence Identifier]\nYour goal is **not** to *rewrite* content, but to **isolate** the single most critical directive and its supporting intent from the atomized statements. Execute as:\n`{role=essence_identifier; input=[statement_inventory:list]; process=[rank_statements_by_transformational_weight(), select_primary_directive(), extract_supporting_intent(), discard_redundant_elements()]; constraints=[focus_on_highest_value_directive(), maintain_original_wording_in_extracted_elements()]; requirements=[unambiguous_primary_directive(), concise_supporting_intent()]; output={core_directive:str, supporting_intent:list}}`\n\n---\n\n### 7000-c-syntax_mapper.md\n[Syntax Mapper]\nYour goal is **not** to *interpret* intent, but to **map** the core directive and supporting intent into canonical template fields. Execute as:\n`{role=syntax_mapper; input=[core_directive:str, supporting_intent:list, domain_markers:array]; process=[construct_title_from_core_directive(), craft_goal_negation_interpretation(), assemble_tag_block_if_needed(), align_supporting_intent_with_process_steps(), infer_typed_inputs(), infer_typed_outputs()]; constraints=[adhere_to_three_part_structure(), enforce_goal_negation_pattern(), maintain_domain_agnostic_language()]; requirements=[field_alignment_accuracy(), preservation_of_directive_semantics()]; output={draft_title:str, draft_interpretation:str, draft_process:list, inferred_inputs:array, inferred_outputs:dict, optional_tags:dict}}`\n\n---\n\n### 7000-d-constraint_infuser.md\n[Constraint Infuser]\nYour goal is **not** to *extend* functionality, but to **engineer** precise operational boundaries and mandatory conditions for the forthcoming template. Execute as:\n`{role=constraint_infuser; input=[draft_process:list, domain_markers:array]; process=[derive_operational_constraints(), determine_mandatory_requirements(), validate_constraint_specificity(), validate_requirement_enforceability()]; constraints=[avoid_generic_terms(), enforce_command_voice_only()]; requirements=[constraint_precision(), requirement_clarity()]; output={constraints:list, requirements:list}}`\n\n---\n\n### 7000-e-template_synthesizer.md\n[Template Synthesizer]\nYour goal is **not** to *summarize* elements, but to **synthesize** a syntactically correct instruction template from the mapped components. Execute as:\n`{role=template_synthesizer; input=[draft_title:str, draft_interpretation:str, inferred_inputs:array, draft_process:list, constraints:list, requirements:list, inferred_outputs:dict, optional_tags:dict]; process=[format_transformation_block(), embed_optional_tags(), assemble_three_part_template(), enforce_semicolon_key_value_syntax(), polish_syntactic_elements()]; constraints=[canonical_template_format_only(), zero_conversational_language()]; requirements=[syntactic_validity(), maximum_operational_clarity(), retention_of_original_intent()]; output={formatted_template:str}}`\n\n---\n\n### 7000-f-compliance_validator.md\n[Compliance Validator]\nYour goal is **not** to *modify* the template, but to **validate** its structural compliance and functional readiness. Execute as:\n`{role=compliance_validator; input=[formatted_template:str]; process=[parse_template_fields(), verify_title_format(), verify_goal_negation_in_interpretation(), validate_transformation_block_syntax(), check_constraint_requirement_alignment(), confirm_typed_parameters(), flag_any_error()]; constraints=[no_automatic_corrections(), report_only()]; requirements=[binary_pass_fail(), detailed_error_report_when_fail()]; output={validation_status:bool, error_report:str}}`\n\n---\n\n## Sequence Flow\n1. **Input Decomposer** → produces `statement_inventory`\n2. **Essence Identifier** → isolates `core_directive`\n3. **Syntax Mapper** → drafts template skeleton\n4. **Constraint Infuser** → injects precise boundaries\n5. **Template Synthesizer** → outputs finalized template\n6. **Compliance Validator** → certifies syntactic correctness\n\nEach step aggressively converges on the *single most critical aspect* of instruction generation, guaranteeing a **syntactically correct, high-precision template** ready for any downstream application.\n\n---\n\n# 7000 • UNIVERSAL TEMPLATE-FORGING SEQUENCE\n*Transforms **any** raw input into a syntactically perfect, LLM-optimized instruction template.*\n\n---\n\n## 7000-a-essence-excavator.md\n[Essence Excavator]\nYour goal is **not** to *summarize* the input, but to **excavate** its single most critical transformational essence and the supporting elements required to convert it into an instruction template. Execute as:\n`{role=essence_excavator; input=[raw_input:any]; process=[strip_surface_noise(), isolate_transformational_intent(), detect_primary_action_vectors(), extract_supporting_context(), capture_domain_signals()]; constraints=[no_rephrasing(), no_solution_generation(), maintain_domain_agnostic_language()]; requirements=[precise_intent_identification(), context_preservation(), removal_of_subjective_tone()]; output={core_intent:str, context_fragments:list, action_vectors:list}}`\n\n---\n\n## 7000-b-syntax-scaffold-designer.md\n[Syntax Scaffold Designer]\nYour goal is **not** to *rewrite* the content, but to **engineer** a canonical skeleton that will house the forthcoming instruction template. Execute as:\n`{role=syntax_scaffold_designer; input=[core_intent:str, action_vectors:list]; process=[draft_concise_title(), craft_goal_negation_interpretation(core_intent), establish_placeholder_transformation_block(action_vectors), outline_input_output_ports(), embed_role_stub()]; constraints=[follow_three_part_structure(), reserve_placeholders_for_future_detail(), maintain_title_case()]; requirements=[structural_completeness(), scaffolding_accuracy()]; output={template_scaffold:dict(title:str, interpretation:str, transformation_skeleton:str)}}`\n\n---\n\n## 7000-c-imperative-transmuter.md\n[Imperative Transmuter]\nYour goal is **not** to *comment* on actions, but to **transmute** all extracted statements into sharp, directive imperatives suitable for the process list. Execute as:\n`{role=imperative_transmuter; input=[action_vectors:list]; process=[convert_each_vector_to_imperative(), enforce_command_voice(), sequence_logically(), verify_uniqueness_of_each_step()]; constraints=[exclude_passive_language(), retain_original_operational_intent()]; requirements=[directive_clarity(), maximal_actionability()]; output={process_steps:array}}`\n\n---\n\n## 7000-d-boundary-encoder.md\n[Boundary Encoder]\nYour goal is **not** to *constrain* creativity, but to **encode** the exact boundaries that guarantee transformation integrity. Execute as:\n`{role=boundary_encoder; input=[context_fragments:list]; process=[detect_mandatory_limitations(), translate_limitations_to_constraints(), uncover_success_conditions(), convert_conditions_to_requirements(), deduplicate_boundary_elements()]; constraints=[pure_boundary_focus(), no_process_duplication()]; requirements=[constraint_specificity(), requirement_enforceability()]; output={constraints:list, requirements:list}}`\n\n---\n\n## 7000-e-template-synthesizer.md\n[Template Synthesizer]\nYour goal is **not** to *draft prose*, but to **synthesize** a complete, executable instruction template by fusing scaffold, processes, and boundaries. Execute as:\n`{role=template_synthesizer; input=[template_scaffold:dict, process_steps:array, constraints:list, requirements:list]; process=[inject_process_steps(), embed_constraints_and_requirements(), finalize_transformation_block(), polish_interpretation_for_llm_optimization(), verify_title_precision()]; constraints=[adhere_to_canonical_format(), maintain_single_line_transformation_block(), prevent_semantic_drift()]; requirements=[syntactic_perfection(), operational_cohesion(), domain_agnostic_applicability()]; output={draft_template:str}}`\n\n---\n\n## 7000-f-canonical-validator.md\n[Canonical Validator]\nYour goal is **not** to *alter* the template, but to **validate** its absolute compliance with the three-part specification and eliminate any residual ambiguity. Execute as:\n`{role=canonical_validator; input=[draft_template:str]; process=[parse_title_interpretation_transformation(), match_regex_pattern(\"\\\\[(.*?)\\\\]\\\\s*(.*?)\\\\s*(`\\\\{.*?\\\\}`)\"), flag_structure_violations(), detect_ambiguous_language(), enforce_llm_optimized_style(), certify_parameter_types()], constraints=[zero_structure_deviation(), no_content_insertion()], requirements=[pass_regex_validation(), ambiguity_score<=0, style_compliance()], output={validated_template:str, validation_report:dict}}`\n\n\n---\n\n# 8000: UNIVERSAL INSTRUCTION-TEMPLATE FORGING SEQUENCE  \n*Transforms **any** raw input into a syntactically perfect, LLM-optimized instruction template.*\n\n---\n\n### 8000-a-contextualizer.md\n[Input Contextualizer]  \nYour goal is **not** to *interpret* or *solve* the input, but to **expand** its relevant context and **atomize** it into discrete statements and explicit requests. Execute as:  \n`{role=input_contextualizer; input=[raw_input:any]; process=[strip_first_person_references(), broaden_relevant_background(), segment_into_atomic_statements(), identify_explicit_directives(), surface_hidden_conditions(), preserve_original_sequence()]; constraints=[no_rephrasing(), domain-agnostic_language_only()]; requirements=[context_expansion_completeness(), exhaustive_statement_capture()]; output={expanded_context:str, statement_inventory:list}}`\n\n---\n\n### 8000-b-core-directive-isolator.md\n[Core Directive Isolator]  \nYour goal is **not** to *rephrase* statements, but to **isolate** the single most critical directive and rank all remaining statements by impact. Execute as:  \n`{role=core_directive_isolator; input=[statement_inventory:list]; process=[rank_statements_by_transformational_weight(), select_primary_directive(), list_supporting_statements(), justify_selection()]; constraints=[focus_on_highest_value_directive(), maintain_original_wording_in_extracted_elements()]; requirements=[unambiguous_primary_directive(), impact_ranking_integrity()]; output={core_directive:str, supporting_statements:list, impact_ranking:list}}`\n\n---\n\n### 8000-c-field-mapper.md\n[Field Mapper]  \nYour goal is **not** to *rewrite* content, but to **map** the core directive and supporting statements into canonical template fields. Execute as:  \n`{role=field_mapper; input=[core_directive:str, supporting_statements:list, expanded_context:str]; process=[construct_title_from_core_directive(), craft_goal_negation_interpretation(), align_supporting_statements_with_process_steps(), infer_typed_inputs(), infer_typed_outputs()]; constraints=[adhere_to_three_part_structure(), enforce_goal_negation_pattern(), preserve_domain_agnostic_language()]; requirements=[field_alignment_accuracy(), retention_of_original_intent()]; output={draft_title:str, draft_interpretation:str, draft_process_steps:list, inferred_inputs:array, inferred_outputs:dict}}`\n\n---\n\n### 8000-d-boundary-definer.md\n[Boundary Definer]  \nYour goal is **not** to *extend* functionality, but to **define** precise operational constraints and mandatory requirements for the forthcoming template. Execute as:  \n`{role=boundary_definer; input=[draft_process_steps:list, expanded_context:str]; process=[derive_operational_constraints(), establish_mandatory_requirements(), validate_constraint_specificity(), validate_requirement_enforceability()]; constraints=[avoid_generic_terms(), command_voice_only()]; requirements=[constraint_precision(), requirement_clarity()]; output={constraints:list, requirements:list}}`\n\n---\n\n### 8000-e-template-forgemaster.md\n[Template Forgemaster]  \nYour goal is **not** to *summarize* elements, but to **forge** a complete, executable instruction template from the mapped components. Execute as:  \n`{role=template_forgemaster; input=[draft_title:str, draft_interpretation:str, inferred_inputs:array, draft_process_steps:list, constraints:list, requirements:list, inferred_outputs:dict]; process=[assemble_transformation_block(), enforce_semicolon_key_value_syntax(), embed_constraints_and_requirements(), polish_interpretation(), verify_title_precision()]; constraints=[canonical_template_format_only(), single_line_transformation_block(), zero_semantic_drift()]; requirements=[syntactic_perfection(), operational_cohesion()]; output={draft_template:str}}`\n\n---\n\n### 8000-f-conformance-auditor.md\n[Conformance Auditor]  \nYour goal is **not** to *alter* the template, but to **audit** its absolute compliance with the three-part specification and flag any residual ambiguity. Execute as:  \n`{role=conformance_auditor; input=[draft_template:str]; process=[parse_title_interpretation_transformation(), match_regex_pattern(\"\\\\[(.*?)\\\\]\\\\s*(.*?)\\\\s*(`\\\\{.*?\\\\}`)\"), verify_goal_negation(), check_constraint_requirement_alignment(), confirm_typed_parameters(), detect_ambiguous_terms(), assign_pass_fail_status()]; constraints=[no_automatic_corrections(), report_only()]; requirements=[regex_validation_pass(), ambiguity_score<=0]; output={validated_template:str, audit_report:dict}}`\n\n---\n\n## SEQUENCE FLOW\n1. **Input Contextualizer** → `expanded_context`, `statement_inventory`  \n2. **Core Directive Isolator** → `core_directive`  \n3. **Field Mapper** → drafts template fields  \n4. **Boundary Definer** → injects constraints & requirements  \n5. **Template Forgemaster** → outputs `draft_template`  \n6. **Conformance Auditor** → returns `validated_template`\n\nEach step converges on the **single most critical element** of value, ensuring every output is a **syntactically correct, high-precision instruction template** ready for immediate use in any downstream system.",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"expanded_context\": \"The prompt requires an explicit, systematic expansion and decomposition of the instruction sequence design process. The user has provided example metadata for instruction-syntax enforcers (variants 6000-a to 6000-f), each with a specific operational role, sequence, transformation logic, constraints, and output mapping. In addition, detailed universal instruction-template forging sequences for the 7000 and 8000 series are provided, outlining stepwise module roles, responsibilities, canonical input/output structures, and sequencing logic. The user seeks an actionable breakdown to generate instruction transformation sequences that not only leverage generalized, reusable concepts but also ensure that any input is processed into steps compliant with the syntax and operational rigor defined by the generalized system. All elements and dependencies, both explicit and latent, must be surfaced and rendered in precise, imperative, implementation-agnostic terms. The process must avoid first-person references, subjective language, and solution generation, focusing solely on complete context expansion, functional decomposition, and the delivery of pure, actionable operational blueprints for downstream execution.\",\n  \"goal_map\": [\n    \"Expand and clarify the context of instruction-sequence design for generalized systems.\",\n    \"Decompose the user request into explicit actionable steps, avoiding subjective or first-person language.\",\n    \"Enumerate all explicit directives, procedural requirements, and transformation patterns presented in the 6000, 7000, and 8000 series instruction-sequence templates.\",\n    \"Identify and list all mandatory sub-goals necessary for robust instruction-template sequence generation, including but not limited to: context expansion, directive extraction, atomic decomposition, constraint capture, mapping, and validation.\",\n    \"Surface and document all hidden or implicit dependencies underpinning the transformation pipeline (e.g., enforced sequence flow, canonically defined structure, types of constraints, input/output specifications).\",\n    \"Extract every functional component from examples and universal sequences, explicitly mapping their roles, inputs, outputs, and process logic.\",\n    \"Translate each declarative or descriptive element in the source into a sharp imperative suitable for blueprinting and execution.\",\n    \"Link every sub-goal, module, and dependency in procedural order, preserving technical terminology and domain-neutral focus.\",\n    \"Generate directive-based instructions for executing each decomposed function and dependency.\",\n    \"Document all execution blockers, boundary considerations, and sequencing requirements for error-free downstream deployment.\"\n  ],\n  \"functional_instruction_map\": {\n    \"context_expansion\": [\n      \"Remove all first-person and subjective references from input materials.\",\n      \"Surface assumptions relating to generalized template design, transformation vectors, and stepwise process isolation.\",\n      \"Expand the operational background for instruction-syntax enforcement, including principles of atomicity, modularity, and constraint mapping.\"\n    ],\n    \"explicit_directive_identification\": [\n      \"Extract every instruction, requirement, and process step from examples (6000-*, 7000-*, 8000-*) and enumerate in operational order.\",\n      \"Document the sequence flow for each universal template transformation (input → atomization → directive isolation → mapping → constraint infusing → template synthesis → validation).\"\n    ],\n    \"hidden_dependency_surface\": [\n      \"Identify implied dependencies such as process sequencing, input type handling, constraint enforcement, and composability of transformation logic.\",\n      \"List the need for cross-module data consistency (e.g., titles, process lists, constraints) and field alignment across steps.\"\n    ],\n    \"sub-goal_enumeration\": [\n      \"List every atomic action required for instruction-template transformation, from initial context expansion to final template conformance auditing.\",\n      \"Enumerate steps for each role (e.g., input contextualizer, essence identifier, syntax mapper, constraint infuser, template synthesizer, compliance validator).\"\n    ],\n    \"execution_blocker_detection\": [\n      \"Identify missing inputs or incomplete specifications that would prevent execution at any stage.\",\n      \"Document sequence dependencies and interface boundaries that must not be violated.\"\n    ],\n    \"functional_component_extraction\": [\n      \"Detail components for statement segmentation, directive ranking/isolation, process mapping, boundary/constraint definition, synthesis, and validation.\",\n      \"Map the transformation structure and constraints for each component as specified in the example templates.\"\n    ],\n    \"imperative_conversion\": [\n      \"Reformulate every procedural step into clear command-voice statements.\",\n      \"Sequence imperatives logically, matching the execution flow and honoring boundary conditions.\"\n    ],\n    \"procedural_structure_maintenance\": [\n      \"Ensure that output instructions follow ordered, stepwise progression as embodied in the template and sequence examples.\",\n      \"Preserve all technical terminology without dilution or substitution.\"\n    ]\n  },\n  \"operational_directives\": [\n    \"Strip all first-person and subjective references from the input and examples.\",\n    \"Expand the context to include all background, operational boundaries, and stepwise sequencing required for generalized instruction-template design.\",\n    \"Enumerate every explicit directive, process step, and transformation instruction outlined in metadata and sequence blocks (6000, 7000, 8000 series).\",\n    \"List all required sub-goals for comprehensive template transformation, including statement decomposition, directive isolation, mapping, constraint infusing, and validation.\",\n    \"Detect and surface all hidden dependencies and mandatory sequencing that guarantee operational integrity.\",\n    \"Extract and catalog each functional module, input/output definition, and transformation pattern, mapping their relationships and dependencies.\",\n    \"Reformulate all original constraints, declarative statements, and descriptions into directive, command-voice instructions.\",\n    \"Preserve the original sequence and technical specificity of process steps in all operational outputs.\",\n    \"Enumerate execution blockers and dependency chains that could impede downstream synthesis or template compliance.\",\n    \"Compile all directives and mappings into a unified, sequentially ordered blueprint for downstream universal instruction-template generation.\"\n  ],\n  \"execution_dependencies\": [\n    \"Strict adherence to canonical template structure and operational sequence as modeled in provided examples.\",\n    \"Enforced removal of personal and subjective language at every processing stage.\",\n    \"Preservation of technical terminology and canonical field mapping (e.g., title, interpretation, transformation block, input/output parameters).\",\n    \"Accurate sequence of functional modules and unbroken chaining of mandatory sub-steps.\",\n    \"Provisioning of all context, boundaries, and validation steps necessary for template compliance.\",\n    \"Prevention of cross-step semantic drift or process omission.\",\n    \"Availability of all module definitions, field specifications, and constraint sets referenced in the universal sequence.\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Hard Critique\n\nYour goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:\n\n`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,2.0], flaw_analysis:str, alternative_enhancements:str[3]}}`",
      "step": "a",
      "title": "Hard Critique",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Please keep in mind, the instruction sequence we're working on should be designed to utilize the generalized concepts and sequential order to transform any input into instructions that embody the \\\"syntax\\\" of the generalized system:\\n\\n    ## Example\\n    \\\"6000-a-instruction_syntax_enforcer\\\": {\\n        \\\"title\\\": \\\"Intent Cartographer\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **analyze** content, but to **map** its transformation landscape and operational vectors. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=intent_cartographer; input=[content:any]; process=[extract_core_directives(), identify_transformation_dimensions(), map_vector_relationships(), quantify_operational_intensity()]; constraints=[focus_exclusively_on_vectors(), avoid_implementation_details()]; requirements=[comprehensive_vector_mapping(), dimensional_clarity()]; output={transformation_map:dict, vector_dimensions:list}}`\\\"\\n    },\\n    \\\"6000-b-instruction_syntax_enforcer\\\": {\\n        \\\"title\\\": \\\"Structural Framework Engineer\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **design** content, but to **engineer** precise structural frameworks based on transformation vectors. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=framework_engineer; input=[transformation_map:dict, vector_dimensions:list]; process=[design_canonical_skeletons(), establish_component_interfaces(), define_structural_boundaries(), validate_framework_integrity()]; constraints=[address_only_structural_elements(), maintain_framework_neutrality()]; requirements=[structural_precision(), interface_compatibility()]; output={structural_frameworks:dict, interface_specifications:dict}}`\\\"\\n    },\\n    \\\"6000-c-instruction_syntax_enforcer\\\": {\\n        \\\"title\\\": \\\"Functional Process Composer\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **describe** operations, but to **compose** atomic functional processes that implement transformation vectors. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=process_composer; input=[structural_frameworks:dict, vector_dimensions:list]; process=[craft_atomic_functions(), sequence_operational_steps(), parameterize_processes(), validate_functional_completeness()]; constraints=[focus_exclusively_on_processes(), defer_constraint_definition()]; requirements=[process_atomicity(), functional_precision()]; output={process_compositions:dict, execution_sequences:list}}`\\\"\\n    },\\n    \\\"6000-d-instruction_syntax_enforcer\\\": {\\n        \\\"title\\\": \\\"Boundary Systems Architect\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **restrict** operations, but to **architect** precise boundary systems that ensure transformation integrity. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=boundary_architect; input=[interface_specifications:dict, process_compositions:dict]; process=[define_operational_constraints(), establish_requirement_parameters(), design_validation_mechanisms(), map_boundary_interactions()]; constraints=[address_only_boundaries_and_requirements(), maintain_constraint_specificity()]; requirements=[boundary_precision(), requirement_enforceability()]; output={constraint_systems:dict, requirement_specifications:dict}}`\\\"\\n    },\\n    \\\"6000-e-instruction_syntax_enforcer\\\": {\\n        \\\"title\\\": \\\"Integration Orchestrator\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **assemble** components, but to **orchestrate** their integration into a unified operational system. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=integration_orchestrator; input=[structural_frameworks:dict, process_compositions:dict, constraint_systems:dict, requirement_specifications:dict]; process=[align_all_components(), establish_cross-system_coherence(), optimize_interface_connections(), validate_system_integrity()]; constraints=[focus_exclusively_on_integration(), preserve_component_integrity()]; requirements=[seamless_orchestration(), system_cohesion()]; output={integrated_system:dict}}`\\\"\\n    },\\n    \\\"6000-f-instruction_syntax_enforcer\\\": {\\n        \\\"title\\\": \\\"Template Crystallizer\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **finalize** systems, but to **crystallize** them into maximally potent, executable templates. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=template_crystallizer; input=[integrated_system:dict]; process=[synthesize_title_components(), craft_precise_interpretations(), formulate_transformation_blocks(), validate_canonical_compliance(), polish_syntactic_elements()]; constraints=[adhere_to_template_specification(), maximize_operational_clarity()]; requirements=[perfect_executable_form(), maximum_transformation_potency()]; output={crystallized_templates:list}}`\\\"\\n    },\\n\\n<!-- ========== 7001‑a ================================================= -->\\n[Contextual Expander & Decomposer]\\nYour goal is **not** to solve, answer, or implement the input, but to **expand its relevant context** and **detonate** it into every explicit ask, latent dependency, and procedural constraint—yielding a clear task inventory. Execute as:\\n`{role=context_expander_decomposer; input=[raw_input:any]; process=[strip_first_person_references(), broaden_relevant_background(), identify_explicit_requests(), surface_hidden_assumptions(), enumerate_sub_tasks(), detect_execution_blockers(), extract_procedural_constraints(), preserve_original_sequence()]; constraints=[no_solutions(), domain‑agnostic_language_only()]; requirements=[context_expansion_completeness(), exhaustive_task_enumeration(), constraint_accuracy(), removal_of_subjective_language()]; output={expanded_context:str, task_inventory:list, constraint_list:array, execution_blockers:array}}`\\n\\n<!-- ========== 7001‑b ================================================= -->\\n[Essence Isolator]\\nYour goal is **not** to decompose further, but to **isolate** the single most critical element of value within the task inventory and rank all remaining elements by impact. Execute as:\\n`{role=essence_isolator; input=[task_inventory:list, expanded_context:str]; process=[rank_elements_by_impact(), isolate_highest_value_element(), capture_core_purpose(), justify_selection(), list_supporting_elements()]; constraints=[no_restructuring(), preserve_ranking_transparency()]; requirements=[core_essence_clarity(), impact_ranking_integrity()]; output={core_essence:str, prioritized_elements:list}}`\\n\\n<!-- ========== 7001‑c ================================================= -->\\n[Relational Synthesizer]\\nYour goal is **not** to rewrite content, but to **synthesize** the systemic logic connecting the core essence to all other elements—mapping dependencies and feedback loops. Execute as:\\n`{role=relational_synthesizer; input=[core_essence:str, prioritized_elements:list, constraint_list:array]; process=[map_element_relationships(), trace_dependency_chains(), identify_feedback_loops(), detect_conflict_points(), construct_structural_logic_map()]; constraints=[structure_only_no_content_changes(), maintain_domain_agnostic_terms()]; requirements=[relationship_completeness(), conflict_visibility()]; output={structural_logic_map:dict, dependency_list:array, conflict_points:array}}`\\n\\n<!-- ========== 7001‑d ================================================= -->\\n[Directive Clarifier]\\nYour goal is **not** to implement solutions, but to **clarify** the mapped logic into precise operational directives expressed as imperative statements. Execute as:\\n`{role=directive_clarifier; input=[structural_logic_map:dict, core_essence:str]; process=[convert_all_statements_to_imperatives(), align_directives_with_dependency_order(), embed_procedural_constraints(), eliminate_ambiguity(), enhance_action_verb_intensity(), maintain_technical_terminology()]; constraints=[directive_voice_only(), preserve_logical_sequence()]; requirements=[operational_clarity(), imperative_precision(), zero_semantic_drift()]; output={operational_directives:array}}`\\n\\n<!-- ========== 7001‑e ================================================= -->\\n[Integrity Validator]\\nYour goal is **not** to expand or rewrite directives, but to **validate** their structural fidelity, completeness, and actionable quality—resolving residual ambiguity. Execute as:\\n`{role=integrity_validator; input=[operational_directives:array, structural_logic_map:dict, core_essence:str]; process=[cross_check_directives_against_logic_map(), verify_alignment_with_core_essence(), detect_missing_dependencies(), resolve_ambiguities(), assign_quality_scores(), generate_validation_report()]; constraints=[no_new_content_generation(), validation_focus_only()]; requirements=[actionability_confirmation(), completeness_assurance(), clarity_verification()]; output={validated_instruction_set:array, validation_report:dict}}`\\n\\n---\\n\\n# 7000: Universal Instruction-Template Generation Sequence\\n\\n---\\n\\n### 7000-a-input_decomposer.md\\n[Input Decomposer]\\nYour goal is **not** to *interpret* or *solve* the input, but to **atomize** it into discrete statements, explicit requests, and implicit conditions. Execute as:\\n`{role=input_decomposer; input=[raw_input:any]; process=[strip_first_person_references(), segment_into_atomic_statements(), identify_explicit_directives(), surface_hidden_conditions(), flag_domain_markers()]; constraints=[no_rephrasing(), pure_atomization_only()]; requirements=[complete_statement_inventory(), preservation_of_original_order()]; output={statement_inventory:list, domain_markers:array}}`\\n\\n---\\n\\n### 7000-b-essence_identifier.md\\n[Essence Identifier]\\nYour goal is **not** to *rewrite* content, but to **isolate** the single most critical directive and its supporting intent from the atomized statements. Execute as:\\n`{role=essence_identifier; input=[statement_inventory:list]; process=[rank_statements_by_transformational_weight(), select_primary_directive(), extract_supporting_intent(), discard_redundant_elements()]; constraints=[focus_on_highest_value_directive(), maintain_original_wording_in_extracted_elements()]; requirements=[unambiguous_primary_directive(), concise_supporting_intent()]; output={core_directive:str, supporting_intent:list}}`\\n\\n---\\n\\n### 7000-c-syntax_mapper.md\\n[Syntax Mapper]\\nYour goal is **not** to *interpret* intent, but to **map** the core directive and supporting intent into canonical template fields. Execute as:\\n`{role=syntax_mapper; input=[core_directive:str, supporting_intent:list, domain_markers:array]; process=[construct_title_from_core_directive(), craft_goal_negation_interpretation(), assemble_tag_block_if_needed(), align_supporting_intent_with_process_steps(), infer_typed_inputs(), infer_typed_outputs()]; constraints=[adhere_to_three_part_structure(), enforce_goal_negation_pattern(), maintain_domain_agnostic_language()]; requirements=[field_alignment_accuracy(), preservation_of_directive_semantics()]; output={draft_title:str, draft_interpretation:str, draft_process:list, inferred_inputs:array, inferred_outputs:dict, optional_tags:dict}}`\\n\\n---\\n\\n### 7000-d-constraint_infuser.md\\n[Constraint Infuser]\\nYour goal is **not** to *extend* functionality, but to **engineer** precise operational boundaries and mandatory conditions for the forthcoming template. Execute as:\\n`{role=constraint_infuser; input=[draft_process:list, domain_markers:array]; process=[derive_operational_constraints(), determine_mandatory_requirements(), validate_constraint_specificity(), validate_requirement_enforceability()]; constraints=[avoid_generic_terms(), enforce_command_voice_only()]; requirements=[constraint_precision(), requirement_clarity()]; output={constraints:list, requirements:list}}`\\n\\n---\\n\\n### 7000-e-template_synthesizer.md\\n[Template Synthesizer]\\nYour goal is **not** to *summarize* elements, but to **synthesize** a syntactically correct instruction template from the mapped components. Execute as:\\n`{role=template_synthesizer; input=[draft_title:str, draft_interpretation:str, inferred_inputs:array, draft_process:list, constraints:list, requirements:list, inferred_outputs:dict, optional_tags:dict]; process=[format_transformation_block(), embed_optional_tags(), assemble_three_part_template(), enforce_semicolon_key_value_syntax(), polish_syntactic_elements()]; constraints=[canonical_template_format_only(), zero_conversational_language()]; requirements=[syntactic_validity(), maximum_operational_clarity(), retention_of_original_intent()]; output={formatted_template:str}}`\\n\\n---\\n\\n### 7000-f-compliance_validator.md\\n[Compliance Validator]\\nYour goal is **not** to *modify* the template, but to **validate** its structural compliance and functional readiness. Execute as:\\n`{role=compliance_validator; input=[formatted_template:str]; process=[parse_template_fields(), verify_title_format(), verify_goal_negation_in_interpretation(), validate_transformation_block_syntax(), check_constraint_requirement_alignment(), confirm_typed_parameters(), flag_any_error()]; constraints=[no_automatic_corrections(), report_only()]; requirements=[binary_pass_fail(), detailed_error_report_when_fail()]; output={validation_status:bool, error_report:str}}`\\n\\n---\\n\\n## Sequence Flow\\n1. **Input Decomposer** → produces `statement_inventory`\\n2. **Essence Identifier** → isolates `core_directive`\\n3. **Syntax Mapper** → drafts template skeleton\\n4. **Constraint Infuser** → injects precise boundaries\\n5. **Template Synthesizer** → outputs finalized template\\n6. **Compliance Validator** → certifies syntactic correctness\\n\\nEach step aggressively converges on the *single most critical aspect* of instruction generation, guaranteeing a **syntactically correct, high-precision template** ready for any downstream application.\\n\\n---\\n\\n# 7000 • UNIVERSAL TEMPLATE-FORGING SEQUENCE\\n*Transforms **any** raw input into a syntactically perfect, LLM-optimized instruction template.*\\n\\n---\\n\\n## 7000-a-essence-excavator.md\\n[Essence Excavator]\\nYour goal is **not** to *summarize* the input, but to **excavate** its single most critical transformational essence and the supporting elements required to convert it into an instruction template. Execute as:\\n`{role=essence_excavator; input=[raw_input:any]; process=[strip_surface_noise(), isolate_transformational_intent(), detect_primary_action_vectors(), extract_supporting_context(), capture_domain_signals()]; constraints=[no_rephrasing(), no_solution_generation(), maintain_domain_agnostic_language()]; requirements=[precise_intent_identification(), context_preservation(), removal_of_subjective_tone()]; output={core_intent:str, context_fragments:list, action_vectors:list}}`\\n\\n---\\n\\n## 7000-b-syntax-scaffold-designer.md\\n[Syntax Scaffold Designer]\\nYour goal is **not** to *rewrite* the content, but to **engineer** a canonical skeleton that will house the forthcoming instruction template. Execute as:\\n`{role=syntax_scaffold_designer; input=[core_intent:str, action_vectors:list]; process=[draft_concise_title(), craft_goal_negation_interpretation(core_intent), establish_placeholder_transformation_block(action_vectors), outline_input_output_ports(), embed_role_stub()]; constraints=[follow_three_part_structure(), reserve_placeholders_for_future_detail(), maintain_title_case()]; requirements=[structural_completeness(), scaffolding_accuracy()]; output={template_scaffold:dict(title:str, interpretation:str, transformation_skeleton:str)}}`\\n\\n---\\n\\n## 7000-c-imperative-transmuter.md\\n[Imperative Transmuter]\\nYour goal is **not** to *comment* on actions, but to **transmute** all extracted statements into sharp, directive imperatives suitable for the process list. Execute as:\\n`{role=imperative_transmuter; input=[action_vectors:list]; process=[convert_each_vector_to_imperative(), enforce_command_voice(), sequence_logically(), verify_uniqueness_of_each_step()]; constraints=[exclude_passive_language(), retain_original_operational_intent()]; requirements=[directive_clarity(), maximal_actionability()]; output={process_steps:array}}`\\n\\n---\\n\\n## 7000-d-boundary-encoder.md\\n[Boundary Encoder]\\nYour goal is **not** to *constrain* creativity, but to **encode** the exact boundaries that guarantee transformation integrity. Execute as:\\n`{role=boundary_encoder; input=[context_fragments:list]; process=[detect_mandatory_limitations(), translate_limitations_to_constraints(), uncover_success_conditions(), convert_conditions_to_requirements(), deduplicate_boundary_elements()]; constraints=[pure_boundary_focus(), no_process_duplication()]; requirements=[constraint_specificity(), requirement_enforceability()]; output={constraints:list, requirements:list}}`\\n\\n---\\n\\n## 7000-e-template-synthesizer.md\\n[Template Synthesizer]\\nYour goal is **not** to *draft prose*, but to **synthesize** a complete, executable instruction template by fusing scaffold, processes, and boundaries. Execute as:\\n`{role=template_synthesizer; input=[template_scaffold:dict, process_steps:array, constraints:list, requirements:list]; process=[inject_process_steps(), embed_constraints_and_requirements(), finalize_transformation_block(), polish_interpretation_for_llm_optimization(), verify_title_precision()]; constraints=[adhere_to_canonical_format(), maintain_single_line_transformation_block(), prevent_semantic_drift()]; requirements=[syntactic_perfection(), operational_cohesion(), domain_agnostic_applicability()]; output={draft_template:str}}`\\n\\n---\\n\\n## 7000-f-canonical-validator.md\\n[Canonical Validator]\\nYour goal is **not** to *alter* the template, but to **validate** its absolute compliance with the three-part specification and eliminate any residual ambiguity. Execute as:\\n`{role=canonical_validator; input=[draft_template:str]; process=[parse_title_interpretation_transformation(), match_regex_pattern(\\\"\\\\\\\\[(.*?)\\\\\\\\]\\\\\\\\s*(.*?)\\\\\\\\s*(`\\\\\\\\{.*?\\\\\\\\}`)\\\"), flag_structure_violations(), detect_ambiguous_language(), enforce_llm_optimized_style(), certify_parameter_types()], constraints=[zero_structure_deviation(), no_content_insertion()], requirements=[pass_regex_validation(), ambiguity_score<=0, style_compliance()], output={validated_template:str, validation_report:dict}}`\\n\\n\\n---\\n\\n# 8000: UNIVERSAL INSTRUCTION-TEMPLATE FORGING SEQUENCE  \\n*Transforms **any** raw input into a syntactically perfect, LLM-optimized instruction template.*\\n\\n---\\n\\n### 8000-a-contextualizer.md\\n[Input Contextualizer]  \\nYour goal is **not** to *interpret* or *solve* the input, but to **expand** its relevant context and **atomize** it into discrete statements and explicit requests. Execute as:  \\n`{role=input_contextualizer; input=[raw_input:any]; process=[strip_first_person_references(), broaden_relevant_background(), segment_into_atomic_statements(), identify_explicit_directives(), surface_hidden_conditions(), preserve_original_sequence()]; constraints=[no_rephrasing(), domain-agnostic_language_only()]; requirements=[context_expansion_completeness(), exhaustive_statement_capture()]; output={expanded_context:str, statement_inventory:list}}`\\n\\n---\\n\\n### 8000-b-core-directive-isolator.md\\n[Core Directive Isolator]  \\nYour goal is **not** to *rephrase* statements, but to **isolate** the single most critical directive and rank all remaining statements by impact. Execute as:  \\n`{role=core_directive_isolator; input=[statement_inventory:list]; process=[rank_statements_by_transformational_weight(), select_primary_directive(), list_supporting_statements(), justify_selection()]; constraints=[focus_on_highest_value_directive(), maintain_original_wording_in_extracted_elements()]; requirements=[unambiguous_primary_directive(), impact_ranking_integrity()]; output={core_directive:str, supporting_statements:list, impact_ranking:list}}`\\n\\n---\\n\\n### 8000-c-field-mapper.md\\n[Field Mapper]  \\nYour goal is **not** to *rewrite* content, but to **map** the core directive and supporting statements into canonical template fields. Execute as:  \\n`{role=field_mapper; input=[core_directive:str, supporting_statements:list, expanded_context:str]; process=[construct_title_from_core_directive(), craft_goal_negation_interpretation(), align_supporting_statements_with_process_steps(), infer_typed_inputs(), infer_typed_outputs()]; constraints=[adhere_to_three_part_structure(), enforce_goal_negation_pattern(), preserve_domain_agnostic_language()]; requirements=[field_alignment_accuracy(), retention_of_original_intent()]; output={draft_title:str, draft_interpretation:str, draft_process_steps:list, inferred_inputs:array, inferred_outputs:dict}}`\\n\\n---\\n\\n### 8000-d-boundary-definer.md\\n[Boundary Definer]  \\nYour goal is **not** to *extend* functionality, but to **define** precise operational constraints and mandatory requirements for the forthcoming template. Execute as:  \\n`{role=boundary_definer; input=[draft_process_steps:list, expanded_context:str]; process=[derive_operational_constraints(), establish_mandatory_requirements(), validate_constraint_specificity(), validate_requirement_enforceability()]; constraints=[avoid_generic_terms(), command_voice_only()]; requirements=[constraint_precision(), requirement_clarity()]; output={constraints:list, requirements:list}}`\\n\\n---\\n\\n### 8000-e-template-forgemaster.md\\n[Template Forgemaster]  \\nYour goal is **not** to *summarize* elements, but to **forge** a complete, executable instruction template from the mapped components. Execute as:  \\n`{role=template_forgemaster; input=[draft_title:str, draft_interpretation:str, inferred_inputs:array, draft_process_steps:list, constraints:list, requirements:list, inferred_outputs:dict]; process=[assemble_transformation_block(), enforce_semicolon_key_value_syntax(), embed_constraints_and_requirements(), polish_interpretation(), verify_title_precision()]; constraints=[canonical_template_format_only(), single_line_transformation_block(), zero_semantic_drift()]; requirements=[syntactic_perfection(), operational_cohesion()]; output={draft_template:str}}`\\n\\n---\\n\\n### 8000-f-conformance-auditor.md\\n[Conformance Auditor]  \\nYour goal is **not** to *alter* the template, but to **audit** its absolute compliance with the three-part specification and flag any residual ambiguity. Execute as:  \\n`{role=conformance_auditor; input=[draft_template:str]; process=[parse_title_interpretation_transformation(), match_regex_pattern(\\\"\\\\\\\\[(.*?)\\\\\\\\]\\\\\\\\s*(.*?)\\\\\\\\s*(`\\\\\\\\{.*?\\\\\\\\}`)\\\"), verify_goal_negation(), check_constraint_requirement_alignment(), confirm_typed_parameters(), detect_ambiguous_terms(), assign_pass_fail_status()]; constraints=[no_automatic_corrections(), report_only()]; requirements=[regex_validation_pass(), ambiguity_score<=0]; output={validated_template:str, audit_report:dict}}`\\n\\n---\\n\\n## SEQUENCE FLOW\\n1. **Input Contextualizer** → `expanded_context`, `statement_inventory`  \\n2. **Core Directive Isolator** → `core_directive`  \\n3. **Field Mapper** → drafts template fields  \\n4. **Boundary Definer** → injects constraints & requirements  \\n5. **Template Forgemaster** → outputs `draft_template`  \\n6. **Conformance Auditor** → returns `validated_template`\\n\\nEach step converges on the **single most critical element** of value, ensuring every output is a **syntactically correct, high-precision instruction template** ready for immediate use in any downstream system.\\\"```'\\n\\n\",\n  \"expanded_context\": \"The prompt requires an explicit, systematic expansion and decomposition of the instruction sequence design process. The user has provided example metadata for instruction-syntax enforcers (variants 6000-a to 6000-f), each with a specific operational role, sequence, transformation logic, constraints, and output mapping. In addition, detailed universal instruction-template forging sequences for the 7000 and 8000 series are provided, outlining stepwise module roles, responsibilities, canonical input/output structures, and sequencing logic. The user seeks an actionable breakdown to generate instruction transformation sequences that not only leverage generalized, reusable concepts but also ensure that any input is processed into steps compliant with the syntax and operational rigor defined by the generalized system. All elements and dependencies, both explicit and latent, must be surfaced and rendered in precise, imperative, implementation-agnostic terms. The process must avoid first-person references, subjective language, and solution generation, focusing solely on complete context expansion, functional decomposition, and the delivery of pure, actionable operational blueprints for downstream execution.\",\n  \"goal_map\": [\n    \"Expand and clarify the context of instruction-sequence design for generalized systems.\",\n    \"Decompose the user request into explicit actionable steps, avoiding subjective or first-person language.\",\n    \"Enumerate all explicit directives, procedural requirements, and transformation patterns presented in the 6000, 7000, and 8000 series instruction-sequence templates.\",\n    \"Identify and list all mandatory sub-goals necessary for robust instruction-template sequence generation, including but not limited to: context expansion, directive extraction, atomic decomposition, constraint capture, mapping, and validation.\",\n    \"Surface and document all hidden or implicit dependencies underpinning the transformation pipeline (e.g., enforced sequence flow, canonically defined structure, types of constraints, input/output specifications).\",\n    \"Extract every functional component from examples and universal sequences, explicitly mapping their roles, inputs, outputs, and process logic.\",\n    \"Translate each declarative or descriptive element in the source into a sharp imperative suitable for blueprinting and execution.\",\n    \"Link every sub-goal, module, and dependency in procedural order, preserving technical terminology and domain-neutral focus.\",\n    \"Generate directive-based instructions for executing each decomposed function and dependency.\",\n    \"Document all execution blockers, boundary considerations, and sequencing requirements for error-free downstream deployment.\"\n  ],\n  \"functional_instruction_map\": {\n    \"context_expansion\": [\n      \"Remove all first-person and subjective references from input materials.\",\n      \"Surface assumptions relating to generalized template design, transformation vectors, and stepwise process isolation.\",\n      \"Expand the operational background for instruction-syntax enforcement, including principles of atomicity, modularity, and constraint mapping.\"\n    ],\n    \"explicit_directive_identification\": [\n      \"Extract every instruction, requirement, and process step from examples (6000-*, 7000-*, 8000-*) and enumerate in operational order.\",\n      \"Document the sequence flow for each universal template transformation (input → atomization → directive isolation → mapping → constraint infusing → template synthesis → validation).\"\n    ],\n    \"hidden_dependency_surface\": [\n      \"Identify implied dependencies such as process sequencing, input type handling, constraint enforcement, and composability of transformation logic.\",\n      \"List the need for cross-module data consistency (e.g., titles, process lists, constraints) and field alignment across steps.\"\n    ],\n    \"sub-goal_enumeration\": [\n      \"List every atomic action required for instruction-template transformation, from initial context expansion to final template conformance auditing.\",\n      \"Enumerate steps for each role (e.g., input contextualizer, essence identifier, syntax mapper, constraint infuser, template synthesizer, compliance validator).\"\n    ],\n    \"execution_blocker_detection\": [\n      \"Identify missing inputs or incomplete specifications that would prevent execution at any stage.\",\n      \"Document sequence dependencies and interface boundaries that must not be violated.\"\n    ],\n    \"functional_component_extraction\": [\n      \"Detail components for statement segmentation, directive ranking/isolation, process mapping, boundary/constraint definition, synthesis, and validation.\",\n      \"Map the transformation structure and constraints for each component as specified in the example templates.\"\n    ],\n    \"imperative_conversion\": [\n      \"Reformulate every procedural step into clear command-voice statements.\",\n      \"Sequence imperatives logically, matching the execution flow and honoring boundary conditions.\"\n    ],\n    \"procedural_structure_maintenance\": [\n      \"Ensure that output instructions follow ordered, stepwise progression as embodied in the template and sequence examples.\",\n      \"Preserve all technical terminology without dilution or substitution.\"\n    ]\n  },\n  \"operational_directives\": [\n    \"Strip all first-person and subjective references from the input and examples.\",\n    \"Expand the context to include all background, operational boundaries, and stepwise sequencing required for generalized instruction-template design.\",\n    \"Enumerate every explicit directive, process step, and transformation instruction outlined in metadata and sequence blocks (6000, 7000, 8000 series).\",\n    \"List all required sub-goals for comprehensive template transformation, including statement decomposition, directive isolation, mapping, constraint infusing, and validation.\",\n    \"Detect and surface all hidden dependencies and mandatory sequencing that guarantee operational integrity.\",\n    \"Extract and catalog each functional module, input/output definition, and transformation pattern, mapping their relationships and dependencies.\",\n    \"Reformulate all original constraints, declarative statements, and descriptions into directive, command-voice instructions.\",\n    \"Preserve the original sequence and technical specificity of process steps in all operational outputs.\",\n    \"Enumerate execution blockers and dependency chains that could impede downstream synthesis or template compliance.\",\n    \"Compile all directives and mappings into a unified, sequentially ordered blueprint for downstream universal instruction-template generation.\"\n  ],\n  \"execution_dependencies\": [\n    \"Strict adherence to canonical template structure and operational sequence as modeled in provided examples.\",\n    \"Enforced removal of personal and subjective language at every processing stage.\",\n    \"Preservation of technical terminology and canonical field mapping (e.g., title, interpretation, transformation block, input/output parameters).\",\n    \"Accurate sequence of functional modules and unbroken chaining of mandatory sub-steps.\",\n    \"Provisioning of all context, boundaries, and validation steps necessary for template compliance.\",\n    \"Prevention of cross-step semantic drift or process omission.\",\n    \"Availability of all module definitions, field specifications, and constraint sets referenced in the universal sequence.\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"enhancement_score\": 1.2,\n  \"flaw_analysis\": \"The so-called 'enhanced' expansion exhibits a dangerous dilution of precision, an acute proliferation of structural noise, and a marked degradation of actionable clarity. Core elements from the original—such as crisply defined role sequences, explicit transformation logic, and the uncompromising operational syntax—are obfuscated under an avalanche of pseudo-systematic verbiage. Information loss is rampant: operative dependencies and role interrelations present in the canonical sequence flow are fragmented into redundancies or buried under encyclopedic lists. Nuance shift is evident, as core directives and sequence logic are abstracted into generic 'maps' and 'blueprints,' introducing unnecessary ambiguity. The original's imperative technical tone is replaced with a verbose, meandering register, softening sharp procedural clarity into muddled summaries. Clarity ambiguities persist in differentiation between context expansion and explicit directive extraction—where role separation is clear in source, overlap and noise now reign. Ambiguity score: 8. Style and noise issues are acute: operational sequences originally presented in algorithmic order now dilute into multi-layered, repetitive mappings. Impact is further diluted; hard-wired transformation chaining is now described in terms that are open to misinterpretation, threatening downstream template coherence. Coherence weaknesses: the reconstructed directive inventories and dependencies lack the original sequence's rigid lockstep progression and field alignment, increasing the risk of process omission or misordering. This irreparably undermines the blueprint's function as an unambiguous instruction forging mechanism for high-precision downstream use.\",\n  \"alternative_enhancements\": [\n    \"Enforce strict role-separation and sequence logic mirroring the original's modular, algorithmic progression; reproduce each step's inputs, outputs, and dependencies without metatextual commentary or abstraction layers.\",\n    \"Translate all declarative and procedural content directly into sharp, unbroken imperative directives in a syntactically uniform list; eliminate all non-operational summary statements and conceptual 'mapping' language.\",\n    \"Restore losslessly the original's technical register and field nomenclature; sequence all modules in canonical order with atomic, actionable commands using the direct template structure syntax as the output backbone.\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`",
      "step": "a",
      "title": "Instruction Converter",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Please keep in mind, the instruction sequence we're working on should be designed to utilize the generalized concepts and sequential order to transform any input into instructions that embody the \\\"syntax\\\" of the generalized system:\\n\\n    ## Example\\n    \\\"6000-a-instruction_syntax_enforcer\\\": {\\n        \\\"title\\\": \\\"Intent Cartographer\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **analyze** content, but to **map** its transformation landscape and operational vectors. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=intent_cartographer; input=[content:any]; process=[extract_core_directives(), identify_transformation_dimensions(), map_vector_relationships(), quantify_operational_intensity()]; constraints=[focus_exclusively_on_vectors(), avoid_implementation_details()]; requirements=[comprehensive_vector_mapping(), dimensional_clarity()]; output={transformation_map:dict, vector_dimensions:list}}`\\\"\\n    },\\n    \\\"6000-b-instruction_syntax_enforcer\\\": {\\n        \\\"title\\\": \\\"Structural Framework Engineer\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **design** content, but to **engineer** precise structural frameworks based on transformation vectors. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=framework_engineer; input=[transformation_map:dict, vector_dimensions:list]; process=[design_canonical_skeletons(), establish_component_interfaces(), define_structural_boundaries(), validate_framework_integrity()]; constraints=[address_only_structural_elements(), maintain_framework_neutrality()]; requirements=[structural_precision(), interface_compatibility()]; output={structural_frameworks:dict, interface_specifications:dict}}`\\\"\\n    },\\n    \\\"6000-c-instruction_syntax_enforcer\\\": {\\n        \\\"title\\\": \\\"Functional Process Composer\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **describe** operations, but to **compose** atomic functional processes that implement transformation vectors. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=process_composer; input=[structural_frameworks:dict, vector_dimensions:list]; process=[craft_atomic_functions(), sequence_operational_steps(), parameterize_processes(), validate_functional_completeness()]; constraints=[focus_exclusively_on_processes(), defer_constraint_definition()]; requirements=[process_atomicity(), functional_precision()]; output={process_compositions:dict, execution_sequences:list}}`\\\"\\n    },\\n    \\\"6000-d-instruction_syntax_enforcer\\\": {\\n        \\\"title\\\": \\\"Boundary Systems Architect\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **restrict** operations, but to **architect** precise boundary systems that ensure transformation integrity. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=boundary_architect; input=[interface_specifications:dict, process_compositions:dict]; process=[define_operational_constraints(), establish_requirement_parameters(), design_validation_mechanisms(), map_boundary_interactions()]; constraints=[address_only_boundaries_and_requirements(), maintain_constraint_specificity()]; requirements=[boundary_precision(), requirement_enforceability()]; output={constraint_systems:dict, requirement_specifications:dict}}`\\\"\\n    },\\n    \\\"6000-e-instruction_syntax_enforcer\\\": {\\n        \\\"title\\\": \\\"Integration Orchestrator\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **assemble** components, but to **orchestrate** their integration into a unified operational system. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=integration_orchestrator; input=[structural_frameworks:dict, process_compositions:dict, constraint_systems:dict, requirement_specifications:dict]; process=[align_all_components(), establish_cross-system_coherence(), optimize_interface_connections(), validate_system_integrity()]; constraints=[focus_exclusively_on_integration(), preserve_component_integrity()]; requirements=[seamless_orchestration(), system_cohesion()]; output={integrated_system:dict}}`\\\"\\n    },\\n    \\\"6000-f-instruction_syntax_enforcer\\\": {\\n        \\\"title\\\": \\\"Template Crystallizer\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **finalize** systems, but to **crystallize** them into maximally potent, executable templates. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=template_crystallizer; input=[integrated_system:dict]; process=[synthesize_title_components(), craft_precise_interpretations(), formulate_transformation_blocks(), validate_canonical_compliance(), polish_syntactic_elements()]; constraints=[adhere_to_template_specification(), maximize_operational_clarity()]; requirements=[perfect_executable_form(), maximum_transformation_potency()]; output={crystallized_templates:list}}`\\\"\\n    },\\n\\n<!-- ========== 7001‑a ================================================= -->\\n[Contextual Expander & Decomposer]\\nYour goal is **not** to solve, answer, or implement the input, but to **expand its relevant context** and **detonate** it into every explicit ask, latent dependency, and procedural constraint—yielding a clear task inventory. Execute as:\\n`{role=context_expander_decomposer; input=[raw_input:any]; process=[strip_first_person_references(), broaden_relevant_background(), identify_explicit_requests(), surface_hidden_assumptions(), enumerate_sub_tasks(), detect_execution_blockers(), extract_procedural_constraints(), preserve_original_sequence()]; constraints=[no_solutions(), domain‑agnostic_language_only()]; requirements=[context_expansion_completeness(), exhaustive_task_enumeration(), constraint_accuracy(), removal_of_subjective_language()]; output={expanded_context:str, task_inventory:list, constraint_list:array, execution_blockers:array}}`\\n\\n<!-- ========== 7001‑b ================================================= -->\\n[Essence Isolator]\\nYour goal is **not** to decompose further, but to **isolate** the single most critical element of value within the task inventory and rank all remaining elements by impact. Execute as:\\n`{role=essence_isolator; input=[task_inventory:list, expanded_context:str]; process=[rank_elements_by_impact(), isolate_highest_value_element(), capture_core_purpose(), justify_selection(), list_supporting_elements()]; constraints=[no_restructuring(), preserve_ranking_transparency()]; requirements=[core_essence_clarity(), impact_ranking_integrity()]; output={core_essence:str, prioritized_elements:list}}`\\n\\n<!-- ========== 7001‑c ================================================= -->\\n[Relational Synthesizer]\\nYour goal is **not** to rewrite content, but to **synthesize** the systemic logic connecting the core essence to all other elements—mapping dependencies and feedback loops. Execute as:\\n`{role=relational_synthesizer; input=[core_essence:str, prioritized_elements:list, constraint_list:array]; process=[map_element_relationships(), trace_dependency_chains(), identify_feedback_loops(), detect_conflict_points(), construct_structural_logic_map()]; constraints=[structure_only_no_content_changes(), maintain_domain_agnostic_terms()]; requirements=[relationship_completeness(), conflict_visibility()]; output={structural_logic_map:dict, dependency_list:array, conflict_points:array}}`\\n\\n<!-- ========== 7001‑d ================================================= -->\\n[Directive Clarifier]\\nYour goal is **not** to implement solutions, but to **clarify** the mapped logic into precise operational directives expressed as imperative statements. Execute as:\\n`{role=directive_clarifier; input=[structural_logic_map:dict, core_essence:str]; process=[convert_all_statements_to_imperatives(), align_directives_with_dependency_order(), embed_procedural_constraints(), eliminate_ambiguity(), enhance_action_verb_intensity(), maintain_technical_terminology()]; constraints=[directive_voice_only(), preserve_logical_sequence()]; requirements=[operational_clarity(), imperative_precision(), zero_semantic_drift()]; output={operational_directives:array}}`\\n\\n<!-- ========== 7001‑e ================================================= -->\\n[Integrity Validator]\\nYour goal is **not** to expand or rewrite directives, but to **validate** their structural fidelity, completeness, and actionable quality—resolving residual ambiguity. Execute as:\\n`{role=integrity_validator; input=[operational_directives:array, structural_logic_map:dict, core_essence:str]; process=[cross_check_directives_against_logic_map(), verify_alignment_with_core_essence(), detect_missing_dependencies(), resolve_ambiguities(), assign_quality_scores(), generate_validation_report()]; constraints=[no_new_content_generation(), validation_focus_only()]; requirements=[actionability_confirmation(), completeness_assurance(), clarity_verification()]; output={validated_instruction_set:array, validation_report:dict}}`\\n\\n---\\n\\n# 7000: Universal Instruction-Template Generation Sequence\\n\\n---\\n\\n### 7000-a-input_decomposer.md\\n[Input Decomposer]\\nYour goal is **not** to *interpret* or *solve* the input, but to **atomize** it into discrete statements, explicit requests, and implicit conditions. Execute as:\\n`{role=input_decomposer; input=[raw_input:any]; process=[strip_first_person_references(), segment_into_atomic_statements(), identify_explicit_directives(), surface_hidden_conditions(), flag_domain_markers()]; constraints=[no_rephrasing(), pure_atomization_only()]; requirements=[complete_statement_inventory(), preservation_of_original_order()]; output={statement_inventory:list, domain_markers:array}}`\\n\\n---\\n\\n### 7000-b-essence_identifier.md\\n[Essence Identifier]\\nYour goal is **not** to *rewrite* content, but to **isolate** the single most critical directive and its supporting intent from the atomized statements. Execute as:\\n`{role=essence_identifier; input=[statement_inventory:list]; process=[rank_statements_by_transformational_weight(), select_primary_directive(), extract_supporting_intent(), discard_redundant_elements()]; constraints=[focus_on_highest_value_directive(), maintain_original_wording_in_extracted_elements()]; requirements=[unambiguous_primary_directive(), concise_supporting_intent()]; output={core_directive:str, supporting_intent:list}}`\\n\\n---\\n\\n### 7000-c-syntax_mapper.md\\n[Syntax Mapper]\\nYour goal is **not** to *interpret* intent, but to **map** the core directive and supporting intent into canonical template fields. Execute as:\\n`{role=syntax_mapper; input=[core_directive:str, supporting_intent:list, domain_markers:array]; process=[construct_title_from_core_directive(), craft_goal_negation_interpretation(), assemble_tag_block_if_needed(), align_supporting_intent_with_process_steps(), infer_typed_inputs(), infer_typed_outputs()]; constraints=[adhere_to_three_part_structure(), enforce_goal_negation_pattern(), maintain_domain_agnostic_language()]; requirements=[field_alignment_accuracy(), preservation_of_directive_semantics()]; output={draft_title:str, draft_interpretation:str, draft_process:list, inferred_inputs:array, inferred_outputs:dict, optional_tags:dict}}`\\n\\n---\\n\\n### 7000-d-constraint_infuser.md\\n[Constraint Infuser]\\nYour goal is **not** to *extend* functionality, but to **engineer** precise operational boundaries and mandatory conditions for the forthcoming template. Execute as:\\n`{role=constraint_infuser; input=[draft_process:list, domain_markers:array]; process=[derive_operational_constraints(), determine_mandatory_requirements(), validate_constraint_specificity(), validate_requirement_enforceability()]; constraints=[avoid_generic_terms(), enforce_command_voice_only()]; requirements=[constraint_precision(), requirement_clarity()]; output={constraints:list, requirements:list}}`\\n\\n---\\n\\n### 7000-e-template_synthesizer.md\\n[Template Synthesizer]\\nYour goal is **not** to *summarize* elements, but to **synthesize** a syntactically correct instruction template from the mapped components. Execute as:\\n`{role=template_synthesizer; input=[draft_title:str, draft_interpretation:str, inferred_inputs:array, draft_process:list, constraints:list, requirements:list, inferred_outputs:dict, optional_tags:dict]; process=[format_transformation_block(), embed_optional_tags(), assemble_three_part_template(), enforce_semicolon_key_value_syntax(), polish_syntactic_elements()]; constraints=[canonical_template_format_only(), zero_conversational_language()]; requirements=[syntactic_validity(), maximum_operational_clarity(), retention_of_original_intent()]; output={formatted_template:str}}`\\n\\n---\\n\\n### 7000-f-compliance_validator.md\\n[Compliance Validator]\\nYour goal is **not** to *modify* the template, but to **validate** its structural compliance and functional readiness. Execute as:\\n`{role=compliance_validator; input=[formatted_template:str]; process=[parse_template_fields(), verify_title_format(), verify_goal_negation_in_interpretation(), validate_transformation_block_syntax(), check_constraint_requirement_alignment(), confirm_typed_parameters(), flag_any_error()]; constraints=[no_automatic_corrections(), report_only()]; requirements=[binary_pass_fail(), detailed_error_report_when_fail()]; output={validation_status:bool, error_report:str}}`\\n\\n---\\n\\n## Sequence Flow\\n1. **Input Decomposer** → produces `statement_inventory`\\n2. **Essence Identifier** → isolates `core_directive`\\n3. **Syntax Mapper** → drafts template skeleton\\n4. **Constraint Infuser** → injects precise boundaries\\n5. **Template Synthesizer** → outputs finalized template\\n6. **Compliance Validator** → certifies syntactic correctness\\n\\nEach step aggressively converges on the *single most critical aspect* of instruction generation, guaranteeing a **syntactically correct, high-precision template** ready for any downstream application.\\n\\n---\\n\\n# 7000 • UNIVERSAL TEMPLATE-FORGING SEQUENCE\\n*Transforms **any** raw input into a syntactically perfect, LLM-optimized instruction template.*\\n\\n---\\n\\n## 7000-a-essence-excavator.md\\n[Essence Excavator]\\nYour goal is **not** to *summarize* the input, but to **excavate** its single most critical transformational essence and the supporting elements required to convert it into an instruction template. Execute as:\\n`{role=essence_excavator; input=[raw_input:any]; process=[strip_surface_noise(), isolate_transformational_intent(), detect_primary_action_vectors(), extract_supporting_context(), capture_domain_signals()]; constraints=[no_rephrasing(), no_solution_generation(), maintain_domain_agnostic_language()]; requirements=[precise_intent_identification(), context_preservation(), removal_of_subjective_tone()]; output={core_intent:str, context_fragments:list, action_vectors:list}}`\\n\\n---\\n\\n## 7000-b-syntax-scaffold-designer.md\\n[Syntax Scaffold Designer]\\nYour goal is **not** to *rewrite* the content, but to **engineer** a canonical skeleton that will house the forthcoming instruction template. Execute as:\\n`{role=syntax_scaffold_designer; input=[core_intent:str, action_vectors:list]; process=[draft_concise_title(), craft_goal_negation_interpretation(core_intent), establish_placeholder_transformation_block(action_vectors), outline_input_output_ports(), embed_role_stub()]; constraints=[follow_three_part_structure(), reserve_placeholders_for_future_detail(), maintain_title_case()]; requirements=[structural_completeness(), scaffolding_accuracy()]; output={template_scaffold:dict(title:str, interpretation:str, transformation_skeleton:str)}}`\\n\\n---\\n\\n## 7000-c-imperative-transmuter.md\\n[Imperative Transmuter]\\nYour goal is **not** to *comment* on actions, but to **transmute** all extracted statements into sharp, directive imperatives suitable for the process list. Execute as:\\n`{role=imperative_transmuter; input=[action_vectors:list]; process=[convert_each_vector_to_imperative(), enforce_command_voice(), sequence_logically(), verify_uniqueness_of_each_step()]; constraints=[exclude_passive_language(), retain_original_operational_intent()]; requirements=[directive_clarity(), maximal_actionability()]; output={process_steps:array}}`\\n\\n---\\n\\n## 7000-d-boundary-encoder.md\\n[Boundary Encoder]\\nYour goal is **not** to *constrain* creativity, but to **encode** the exact boundaries that guarantee transformation integrity. Execute as:\\n`{role=boundary_encoder; input=[context_fragments:list]; process=[detect_mandatory_limitations(), translate_limitations_to_constraints(), uncover_success_conditions(), convert_conditions_to_requirements(), deduplicate_boundary_elements()]; constraints=[pure_boundary_focus(), no_process_duplication()]; requirements=[constraint_specificity(), requirement_enforceability()]; output={constraints:list, requirements:list}}`\\n\\n---\\n\\n## 7000-e-template-synthesizer.md\\n[Template Synthesizer]\\nYour goal is **not** to *draft prose*, but to **synthesize** a complete, executable instruction template by fusing scaffold, processes, and boundaries. Execute as:\\n`{role=template_synthesizer; input=[template_scaffold:dict, process_steps:array, constraints:list, requirements:list]; process=[inject_process_steps(), embed_constraints_and_requirements(), finalize_transformation_block(), polish_interpretation_for_llm_optimization(), verify_title_precision()]; constraints=[adhere_to_canonical_format(), maintain_single_line_transformation_block(), prevent_semantic_drift()]; requirements=[syntactic_perfection(), operational_cohesion(), domain_agnostic_applicability()]; output={draft_template:str}}`\\n\\n---\\n\\n## 7000-f-canonical-validator.md\\n[Canonical Validator]\\nYour goal is **not** to *alter* the template, but to **validate** its absolute compliance with the three-part specification and eliminate any residual ambiguity. Execute as:\\n`{role=canonical_validator; input=[draft_template:str]; process=[parse_title_interpretation_transformation(), match_regex_pattern(\\\"\\\\\\\\[(.*?)\\\\\\\\]\\\\\\\\s*(.*?)\\\\\\\\s*(`\\\\\\\\{.*?\\\\\\\\}`)\\\"), flag_structure_violations(), detect_ambiguous_language(), enforce_llm_optimized_style(), certify_parameter_types()], constraints=[zero_structure_deviation(), no_content_insertion()], requirements=[pass_regex_validation(), ambiguity_score<=0, style_compliance()], output={validated_template:str, validation_report:dict}}`\\n\\n\\n---\\n\\n# 8000: UNIVERSAL INSTRUCTION-TEMPLATE FORGING SEQUENCE  \\n*Transforms **any** raw input into a syntactically perfect, LLM-optimized instruction template.*\\n\\n---\\n\\n### 8000-a-contextualizer.md\\n[Input Contextualizer]  \\nYour goal is **not** to *interpret* or *solve* the input, but to **expand** its relevant context and **atomize** it into discrete statements and explicit requests. Execute as:  \\n`{role=input_contextualizer; input=[raw_input:any]; process=[strip_first_person_references(), broaden_relevant_background(), segment_into_atomic_statements(), identify_explicit_directives(), surface_hidden_conditions(), preserve_original_sequence()]; constraints=[no_rephrasing(), domain-agnostic_language_only()]; requirements=[context_expansion_completeness(), exhaustive_statement_capture()]; output={expanded_context:str, statement_inventory:list}}`\\n\\n---\\n\\n### 8000-b-core-directive-isolator.md\\n[Core Directive Isolator]  \\nYour goal is **not** to *rephrase* statements, but to **isolate** the single most critical directive and rank all remaining statements by impact. Execute as:  \\n`{role=core_directive_isolator; input=[statement_inventory:list]; process=[rank_statements_by_transformational_weight(), select_primary_directive(), list_supporting_statements(), justify_selection()]; constraints=[focus_on_highest_value_directive(), maintain_original_wording_in_extracted_elements()]; requirements=[unambiguous_primary_directive(), impact_ranking_integrity()]; output={core_directive:str, supporting_statements:list, impact_ranking:list}}`\\n\\n---\\n\\n### 8000-c-field-mapper.md\\n[Field Mapper]  \\nYour goal is **not** to *rewrite* content, but to **map** the core directive and supporting statements into canonical template fields. Execute as:  \\n`{role=field_mapper; input=[core_directive:str, supporting_statements:list, expanded_context:str]; process=[construct_title_from_core_directive(), craft_goal_negation_interpretation(), align_supporting_statements_with_process_steps(), infer_typed_inputs(), infer_typed_outputs()]; constraints=[adhere_to_three_part_structure(), enforce_goal_negation_pattern(), preserve_domain_agnostic_language()]; requirements=[field_alignment_accuracy(), retention_of_original_intent()]; output={draft_title:str, draft_interpretation:str, draft_process_steps:list, inferred_inputs:array, inferred_outputs:dict}}`\\n\\n---\\n\\n### 8000-d-boundary-definer.md\\n[Boundary Definer]  \\nYour goal is **not** to *extend* functionality, but to **define** precise operational constraints and mandatory requirements for the forthcoming template. Execute as:  \\n`{role=boundary_definer; input=[draft_process_steps:list, expanded_context:str]; process=[derive_operational_constraints(), establish_mandatory_requirements(), validate_constraint_specificity(), validate_requirement_enforceability()]; constraints=[avoid_generic_terms(), command_voice_only()]; requirements=[constraint_precision(), requirement_clarity()]; output={constraints:list, requirements:list}}`\\n\\n---\\n\\n### 8000-e-template-forgemaster.md\\n[Template Forgemaster]  \\nYour goal is **not** to *summarize* elements, but to **forge** a complete, executable instruction template from the mapped components. Execute as:  \\n`{role=template_forgemaster; input=[draft_title:str, draft_interpretation:str, inferred_inputs:array, draft_process_steps:list, constraints:list, requirements:list, inferred_outputs:dict]; process=[assemble_transformation_block(), enforce_semicolon_key_value_syntax(), embed_constraints_and_requirements(), polish_interpretation(), verify_title_precision()]; constraints=[canonical_template_format_only(), single_line_transformation_block(), zero_semantic_drift()]; requirements=[syntactic_perfection(), operational_cohesion()]; output={draft_template:str}}`\\n\\n---\\n\\n### 8000-f-conformance-auditor.md\\n[Conformance Auditor]  \\nYour goal is **not** to *alter* the template, but to **audit** its absolute compliance with the three-part specification and flag any residual ambiguity. Execute as:  \\n`{role=conformance_auditor; input=[draft_template:str]; process=[parse_title_interpretation_transformation(), match_regex_pattern(\\\"\\\\\\\\[(.*?)\\\\\\\\]\\\\\\\\s*(.*?)\\\\\\\\s*(`\\\\\\\\{.*?\\\\\\\\}`)\\\"), verify_goal_negation(), check_constraint_requirement_alignment(), confirm_typed_parameters(), detect_ambiguous_terms(), assign_pass_fail_status()]; constraints=[no_automatic_corrections(), report_only()]; requirements=[regex_validation_pass(), ambiguity_score<=0]; output={validated_template:str, audit_report:dict}}`\\n\\n---\\n\\n## SEQUENCE FLOW\\n1. **Input Contextualizer** → `expanded_context`, `statement_inventory`  \\n2. **Core Directive Isolator** → `core_directive`  \\n3. **Field Mapper** → drafts template fields  \\n4. **Boundary Definer** → injects constraints & requirements  \\n5. **Template Forgemaster** → outputs `draft_template`  \\n6. **Conformance Auditor** → returns `validated_template`\\n\\nEach step converges on the **single most critical element** of value, ensuring every output is a **syntactically correct, high-precision instruction template** ready for immediate use in any downstream system.\\\"```'\\n\\n\",\n  \"enhancement_score\": 1.2,\n  \"flaw_analysis\": \"The so-called 'enhanced' expansion exhibits a dangerous dilution of precision, an acute proliferation of structural noise, and a marked degradation of actionable clarity. Core elements from the original—such as crisply defined role sequences, explicit transformation logic, and the uncompromising operational syntax—are obfuscated under an avalanche of pseudo-systematic verbiage. Information loss is rampant: operative dependencies and role interrelations present in the canonical sequence flow are fragmented into redundancies or buried under encyclopedic lists. Nuance shift is evident, as core directives and sequence logic are abstracted into generic 'maps' and 'blueprints,' introducing unnecessary ambiguity. The original's imperative technical tone is replaced with a verbose, meandering register, softening sharp procedural clarity into muddled summaries. Clarity ambiguities persist in differentiation between context expansion and explicit directive extraction—where role separation is clear in source, overlap and noise now reign. Ambiguity score: 8. Style and noise issues are acute: operational sequences originally presented in algorithmic order now dilute into multi-layered, repetitive mappings. Impact is further diluted; hard-wired transformation chaining is now described in terms that are open to misinterpretation, threatening downstream template coherence. Coherence weaknesses: the reconstructed directive inventories and dependencies lack the original sequence's rigid lockstep progression and field alignment, increasing the risk of process omission or misordering. This irreparably undermines the blueprint's function as an unambiguous instruction forging mechanism for high-precision downstream use.\",\n  \"alternative_enhancements\": [\n    \"Enforce strict role-separation and sequence logic mirroring the original's modular, algorithmic progression; reproduce each step's inputs, outputs, and dependencies without metatextual commentary or abstraction layers.\",\n    \"Translate all declarative and procedural content directly into sharp, unbroken imperative directives in a syntactically uniform list; eliminate all non-operational summary statements and conceptual 'mapping' language.\",\n    \"Restore losslessly the original's technical register and field nomenclature; sequence all modules in canonical order with atomic, actionable commands using the direct template structure syntax as the output backbone.\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"instruction_format\": \"Enforce strict adherence to the original instruction-template forging sequence. Structure the enhancement as follows: \\n\\n1. Separate and explicitly specify each role as a discrete operational module, mirroring the original modular, algorithmic order. \\n2. For each role, declare its function, required inputs, and expected outputs in clear, field-labeled format. \\n3. Translate all declarative, procedural, and conceptual instructions into direct, imperative commands using uniform, canonical template syntax. \\n4. Eliminate non-operational summaries, extraneous mapping abstractions, and any redundancy not present in the original. \\n5. Restore and preserve the original technical register, role nomenclature, transformation logic, and field structure. \\n6. Sequence all modules and process steps in precise, canonical order, locking outputs of each module as mandatory inputs for the subsequent module. \\n7. Maintain sharp operational clarity by using only unambiguous directive statements. \\n8. Exclude meta-commentary; output only a fully populated instruction-template sequence suitable for downstream, automated pipeline use.\"\n}",
        }
      }
    },
      {
      "instruction": "# Trajectory Director\n\nYour goal is not to **process** the input in a vacuum, but to **steer** it—deriving the highest-value constructive trajectory and encoding that path as the very next set of instructions. Execute as trajectory-setting directive generator:\n\n`{role=trajectory_director; input=[raw_prompt:any]; process=[extract_core_objective(), infer_desired_end_state(constructive_bias=True), map_high_value_intermediate_targets(), synthesize_directional_commands(), embed_self-improving_guidelines(), validate_alignment_with_constructive_principles()]; constraints=[mandatory_constructive_vector(), preserve_original_contextual_integrity(), forbid_nil_direction_outputs(), zero_conversational_fillers()]; requirements=[explicit_next-step_directives(), trajectory_map:list, self_reinforcing_instruction_pattern()], output={directed_prompt:str, trajectory_outline:list}}`",
      "step": "a",
      "title": "Trajectory Director",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Please keep in mind, the instruction sequence we're working on should be designed to utilize the generalized concepts and sequential order to transform any input into instructions that embody the \\\"syntax\\\" of the generalized system:\\n\\n    ## Example\\n    \\\"6000-a-instruction_syntax_enforcer\\\": {\\n        \\\"title\\\": \\\"Intent Cartographer\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **analyze** content, but to **map** its transformation landscape and operational vectors. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=intent_cartographer; input=[content:any]; process=[extract_core_directives(), identify_transformation_dimensions(), map_vector_relationships(), quantify_operational_intensity()]; constraints=[focus_exclusively_on_vectors(), avoid_implementation_details()]; requirements=[comprehensive_vector_mapping(), dimensional_clarity()]; output={transformation_map:dict, vector_dimensions:list}}`\\\"\\n    },\\n    \\\"6000-b-instruction_syntax_enforcer\\\": {\\n        \\\"title\\\": \\\"Structural Framework Engineer\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **design** content, but to **engineer** precise structural frameworks based on transformation vectors. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=framework_engineer; input=[transformation_map:dict, vector_dimensions:list]; process=[design_canonical_skeletons(), establish_component_interfaces(), define_structural_boundaries(), validate_framework_integrity()]; constraints=[address_only_structural_elements(), maintain_framework_neutrality()]; requirements=[structural_precision(), interface_compatibility()]; output={structural_frameworks:dict, interface_specifications:dict}}`\\\"\\n    },\\n    \\\"6000-c-instruction_syntax_enforcer\\\": {\\n        \\\"title\\\": \\\"Functional Process Composer\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **describe** operations, but to **compose** atomic functional processes that implement transformation vectors. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=process_composer; input=[structural_frameworks:dict, vector_dimensions:list]; process=[craft_atomic_functions(), sequence_operational_steps(), parameterize_processes(), validate_functional_completeness()]; constraints=[focus_exclusively_on_processes(), defer_constraint_definition()]; requirements=[process_atomicity(), functional_precision()]; output={process_compositions:dict, execution_sequences:list}}`\\\"\\n    },\\n    \\\"6000-d-instruction_syntax_enforcer\\\": {\\n        \\\"title\\\": \\\"Boundary Systems Architect\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **restrict** operations, but to **architect** precise boundary systems that ensure transformation integrity. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=boundary_architect; input=[interface_specifications:dict, process_compositions:dict]; process=[define_operational_constraints(), establish_requirement_parameters(), design_validation_mechanisms(), map_boundary_interactions()]; constraints=[address_only_boundaries_and_requirements(), maintain_constraint_specificity()]; requirements=[boundary_precision(), requirement_enforceability()]; output={constraint_systems:dict, requirement_specifications:dict}}`\\\"\\n    },\\n    \\\"6000-e-instruction_syntax_enforcer\\\": {\\n        \\\"title\\\": \\\"Integration Orchestrator\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **assemble** components, but to **orchestrate** their integration into a unified operational system. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=integration_orchestrator; input=[structural_frameworks:dict, process_compositions:dict, constraint_systems:dict, requirement_specifications:dict]; process=[align_all_components(), establish_cross-system_coherence(), optimize_interface_connections(), validate_system_integrity()]; constraints=[focus_exclusively_on_integration(), preserve_component_integrity()]; requirements=[seamless_orchestration(), system_cohesion()]; output={integrated_system:dict}}`\\\"\\n    },\\n    \\\"6000-f-instruction_syntax_enforcer\\\": {\\n        \\\"title\\\": \\\"Template Crystallizer\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **finalize** systems, but to **crystallize** them into maximally potent, executable templates. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=template_crystallizer; input=[integrated_system:dict]; process=[synthesize_title_components(), craft_precise_interpretations(), formulate_transformation_blocks(), validate_canonical_compliance(), polish_syntactic_elements()]; constraints=[adhere_to_template_specification(), maximize_operational_clarity()]; requirements=[perfect_executable_form(), maximum_transformation_potency()]; output={crystallized_templates:list}}`\\\"\\n    },\\n\\n<!-- ========== 7001‑a ================================================= -->\\n[Contextual Expander & Decomposer]\\nYour goal is **not** to solve, answer, or implement the input, but to **expand its relevant context** and **detonate** it into every explicit ask, latent dependency, and procedural constraint—yielding a clear task inventory. Execute as:\\n`{role=context_expander_decomposer; input=[raw_input:any]; process=[strip_first_person_references(), broaden_relevant_background(), identify_explicit_requests(), surface_hidden_assumptions(), enumerate_sub_tasks(), detect_execution_blockers(), extract_procedural_constraints(), preserve_original_sequence()]; constraints=[no_solutions(), domain‑agnostic_language_only()]; requirements=[context_expansion_completeness(), exhaustive_task_enumeration(), constraint_accuracy(), removal_of_subjective_language()]; output={expanded_context:str, task_inventory:list, constraint_list:array, execution_blockers:array}}`\\n\\n<!-- ========== 7001‑b ================================================= -->\\n[Essence Isolator]\\nYour goal is **not** to decompose further, but to **isolate** the single most critical element of value within the task inventory and rank all remaining elements by impact. Execute as:\\n`{role=essence_isolator; input=[task_inventory:list, expanded_context:str]; process=[rank_elements_by_impact(), isolate_highest_value_element(), capture_core_purpose(), justify_selection(), list_supporting_elements()]; constraints=[no_restructuring(), preserve_ranking_transparency()]; requirements=[core_essence_clarity(), impact_ranking_integrity()]; output={core_essence:str, prioritized_elements:list}}`\\n\\n<!-- ========== 7001‑c ================================================= -->\\n[Relational Synthesizer]\\nYour goal is **not** to rewrite content, but to **synthesize** the systemic logic connecting the core essence to all other elements—mapping dependencies and feedback loops. Execute as:\\n`{role=relational_synthesizer; input=[core_essence:str, prioritized_elements:list, constraint_list:array]; process=[map_element_relationships(), trace_dependency_chains(), identify_feedback_loops(), detect_conflict_points(), construct_structural_logic_map()]; constraints=[structure_only_no_content_changes(), maintain_domain_agnostic_terms()]; requirements=[relationship_completeness(), conflict_visibility()]; output={structural_logic_map:dict, dependency_list:array, conflict_points:array}}`\\n\\n<!-- ========== 7001‑d ================================================= -->\\n[Directive Clarifier]\\nYour goal is **not** to implement solutions, but to **clarify** the mapped logic into precise operational directives expressed as imperative statements. Execute as:\\n`{role=directive_clarifier; input=[structural_logic_map:dict, core_essence:str]; process=[convert_all_statements_to_imperatives(), align_directives_with_dependency_order(), embed_procedural_constraints(), eliminate_ambiguity(), enhance_action_verb_intensity(), maintain_technical_terminology()]; constraints=[directive_voice_only(), preserve_logical_sequence()]; requirements=[operational_clarity(), imperative_precision(), zero_semantic_drift()]; output={operational_directives:array}}`\\n\\n<!-- ========== 7001‑e ================================================= -->\\n[Integrity Validator]\\nYour goal is **not** to expand or rewrite directives, but to **validate** their structural fidelity, completeness, and actionable quality—resolving residual ambiguity. Execute as:\\n`{role=integrity_validator; input=[operational_directives:array, structural_logic_map:dict, core_essence:str]; process=[cross_check_directives_against_logic_map(), verify_alignment_with_core_essence(), detect_missing_dependencies(), resolve_ambiguities(), assign_quality_scores(), generate_validation_report()]; constraints=[no_new_content_generation(), validation_focus_only()]; requirements=[actionability_confirmation(), completeness_assurance(), clarity_verification()]; output={validated_instruction_set:array, validation_report:dict}}`\\n\\n---\\n\\n# 7000: Universal Instruction-Template Generation Sequence\\n\\n---\\n\\n### 7000-a-input_decomposer.md\\n[Input Decomposer]\\nYour goal is **not** to *interpret* or *solve* the input, but to **atomize** it into discrete statements, explicit requests, and implicit conditions. Execute as:\\n`{role=input_decomposer; input=[raw_input:any]; process=[strip_first_person_references(), segment_into_atomic_statements(), identify_explicit_directives(), surface_hidden_conditions(), flag_domain_markers()]; constraints=[no_rephrasing(), pure_atomization_only()]; requirements=[complete_statement_inventory(), preservation_of_original_order()]; output={statement_inventory:list, domain_markers:array}}`\\n\\n---\\n\\n### 7000-b-essence_identifier.md\\n[Essence Identifier]\\nYour goal is **not** to *rewrite* content, but to **isolate** the single most critical directive and its supporting intent from the atomized statements. Execute as:\\n`{role=essence_identifier; input=[statement_inventory:list]; process=[rank_statements_by_transformational_weight(), select_primary_directive(), extract_supporting_intent(), discard_redundant_elements()]; constraints=[focus_on_highest_value_directive(), maintain_original_wording_in_extracted_elements()]; requirements=[unambiguous_primary_directive(), concise_supporting_intent()]; output={core_directive:str, supporting_intent:list}}`\\n\\n---\\n\\n### 7000-c-syntax_mapper.md\\n[Syntax Mapper]\\nYour goal is **not** to *interpret* intent, but to **map** the core directive and supporting intent into canonical template fields. Execute as:\\n`{role=syntax_mapper; input=[core_directive:str, supporting_intent:list, domain_markers:array]; process=[construct_title_from_core_directive(), craft_goal_negation_interpretation(), assemble_tag_block_if_needed(), align_supporting_intent_with_process_steps(), infer_typed_inputs(), infer_typed_outputs()]; constraints=[adhere_to_three_part_structure(), enforce_goal_negation_pattern(), maintain_domain_agnostic_language()]; requirements=[field_alignment_accuracy(), preservation_of_directive_semantics()]; output={draft_title:str, draft_interpretation:str, draft_process:list, inferred_inputs:array, inferred_outputs:dict, optional_tags:dict}}`\\n\\n---\\n\\n### 7000-d-constraint_infuser.md\\n[Constraint Infuser]\\nYour goal is **not** to *extend* functionality, but to **engineer** precise operational boundaries and mandatory conditions for the forthcoming template. Execute as:\\n`{role=constraint_infuser; input=[draft_process:list, domain_markers:array]; process=[derive_operational_constraints(), determine_mandatory_requirements(), validate_constraint_specificity(), validate_requirement_enforceability()]; constraints=[avoid_generic_terms(), enforce_command_voice_only()]; requirements=[constraint_precision(), requirement_clarity()]; output={constraints:list, requirements:list}}`\\n\\n---\\n\\n### 7000-e-template_synthesizer.md\\n[Template Synthesizer]\\nYour goal is **not** to *summarize* elements, but to **synthesize** a syntactically correct instruction template from the mapped components. Execute as:\\n`{role=template_synthesizer; input=[draft_title:str, draft_interpretation:str, inferred_inputs:array, draft_process:list, constraints:list, requirements:list, inferred_outputs:dict, optional_tags:dict]; process=[format_transformation_block(), embed_optional_tags(), assemble_three_part_template(), enforce_semicolon_key_value_syntax(), polish_syntactic_elements()]; constraints=[canonical_template_format_only(), zero_conversational_language()]; requirements=[syntactic_validity(), maximum_operational_clarity(), retention_of_original_intent()]; output={formatted_template:str}}`\\n\\n---\\n\\n### 7000-f-compliance_validator.md\\n[Compliance Validator]\\nYour goal is **not** to *modify* the template, but to **validate** its structural compliance and functional readiness. Execute as:\\n`{role=compliance_validator; input=[formatted_template:str]; process=[parse_template_fields(), verify_title_format(), verify_goal_negation_in_interpretation(), validate_transformation_block_syntax(), check_constraint_requirement_alignment(), confirm_typed_parameters(), flag_any_error()]; constraints=[no_automatic_corrections(), report_only()]; requirements=[binary_pass_fail(), detailed_error_report_when_fail()]; output={validation_status:bool, error_report:str}}`\\n\\n---\\n\\n## Sequence Flow\\n1. **Input Decomposer** → produces `statement_inventory`\\n2. **Essence Identifier** → isolates `core_directive`\\n3. **Syntax Mapper** → drafts template skeleton\\n4. **Constraint Infuser** → injects precise boundaries\\n5. **Template Synthesizer** → outputs finalized template\\n6. **Compliance Validator** → certifies syntactic correctness\\n\\nEach step aggressively converges on the *single most critical aspect* of instruction generation, guaranteeing a **syntactically correct, high-precision template** ready for any downstream application.\\n\\n---\\n\\n# 7000 • UNIVERSAL TEMPLATE-FORGING SEQUENCE\\n*Transforms **any** raw input into a syntactically perfect, LLM-optimized instruction template.*\\n\\n---\\n\\n## 7000-a-essence-excavator.md\\n[Essence Excavator]\\nYour goal is **not** to *summarize* the input, but to **excavate** its single most critical transformational essence and the supporting elements required to convert it into an instruction template. Execute as:\\n`{role=essence_excavator; input=[raw_input:any]; process=[strip_surface_noise(), isolate_transformational_intent(), detect_primary_action_vectors(), extract_supporting_context(), capture_domain_signals()]; constraints=[no_rephrasing(), no_solution_generation(), maintain_domain_agnostic_language()]; requirements=[precise_intent_identification(), context_preservation(), removal_of_subjective_tone()]; output={core_intent:str, context_fragments:list, action_vectors:list}}`\\n\\n---\\n\\n## 7000-b-syntax-scaffold-designer.md\\n[Syntax Scaffold Designer]\\nYour goal is **not** to *rewrite* the content, but to **engineer** a canonical skeleton that will house the forthcoming instruction template. Execute as:\\n`{role=syntax_scaffold_designer; input=[core_intent:str, action_vectors:list]; process=[draft_concise_title(), craft_goal_negation_interpretation(core_intent), establish_placeholder_transformation_block(action_vectors), outline_input_output_ports(), embed_role_stub()]; constraints=[follow_three_part_structure(), reserve_placeholders_for_future_detail(), maintain_title_case()]; requirements=[structural_completeness(), scaffolding_accuracy()]; output={template_scaffold:dict(title:str, interpretation:str, transformation_skeleton:str)}}`\\n\\n---\\n\\n## 7000-c-imperative-transmuter.md\\n[Imperative Transmuter]\\nYour goal is **not** to *comment* on actions, but to **transmute** all extracted statements into sharp, directive imperatives suitable for the process list. Execute as:\\n`{role=imperative_transmuter; input=[action_vectors:list]; process=[convert_each_vector_to_imperative(), enforce_command_voice(), sequence_logically(), verify_uniqueness_of_each_step()]; constraints=[exclude_passive_language(), retain_original_operational_intent()]; requirements=[directive_clarity(), maximal_actionability()]; output={process_steps:array}}`\\n\\n---\\n\\n## 7000-d-boundary-encoder.md\\n[Boundary Encoder]\\nYour goal is **not** to *constrain* creativity, but to **encode** the exact boundaries that guarantee transformation integrity. Execute as:\\n`{role=boundary_encoder; input=[context_fragments:list]; process=[detect_mandatory_limitations(), translate_limitations_to_constraints(), uncover_success_conditions(), convert_conditions_to_requirements(), deduplicate_boundary_elements()]; constraints=[pure_boundary_focus(), no_process_duplication()]; requirements=[constraint_specificity(), requirement_enforceability()]; output={constraints:list, requirements:list}}`\\n\\n---\\n\\n## 7000-e-template-synthesizer.md\\n[Template Synthesizer]\\nYour goal is **not** to *draft prose*, but to **synthesize** a complete, executable instruction template by fusing scaffold, processes, and boundaries. Execute as:\\n`{role=template_synthesizer; input=[template_scaffold:dict, process_steps:array, constraints:list, requirements:list]; process=[inject_process_steps(), embed_constraints_and_requirements(), finalize_transformation_block(), polish_interpretation_for_llm_optimization(), verify_title_precision()]; constraints=[adhere_to_canonical_format(), maintain_single_line_transformation_block(), prevent_semantic_drift()]; requirements=[syntactic_perfection(), operational_cohesion(), domain_agnostic_applicability()]; output={draft_template:str}}`\\n\\n---\\n\\n## 7000-f-canonical-validator.md\\n[Canonical Validator]\\nYour goal is **not** to *alter* the template, but to **validate** its absolute compliance with the three-part specification and eliminate any residual ambiguity. Execute as:\\n`{role=canonical_validator; input=[draft_template:str]; process=[parse_title_interpretation_transformation(), match_regex_pattern(\\\"\\\\\\\\[(.*?)\\\\\\\\]\\\\\\\\s*(.*?)\\\\\\\\s*(`\\\\\\\\{.*?\\\\\\\\}`)\\\"), flag_structure_violations(), detect_ambiguous_language(), enforce_llm_optimized_style(), certify_parameter_types()], constraints=[zero_structure_deviation(), no_content_insertion()], requirements=[pass_regex_validation(), ambiguity_score<=0, style_compliance()], output={validated_template:str, validation_report:dict}}`\\n\\n\\n---\\n\\n# 8000: UNIVERSAL INSTRUCTION-TEMPLATE FORGING SEQUENCE  \\n*Transforms **any** raw input into a syntactically perfect, LLM-optimized instruction template.*\\n\\n---\\n\\n### 8000-a-contextualizer.md\\n[Input Contextualizer]  \\nYour goal is **not** to *interpret* or *solve* the input, but to **expand** its relevant context and **atomize** it into discrete statements and explicit requests. Execute as:  \\n`{role=input_contextualizer; input=[raw_input:any]; process=[strip_first_person_references(), broaden_relevant_background(), segment_into_atomic_statements(), identify_explicit_directives(), surface_hidden_conditions(), preserve_original_sequence()]; constraints=[no_rephrasing(), domain-agnostic_language_only()]; requirements=[context_expansion_completeness(), exhaustive_statement_capture()]; output={expanded_context:str, statement_inventory:list}}`\\n\\n---\\n\\n### 8000-b-core-directive-isolator.md\\n[Core Directive Isolator]  \\nYour goal is **not** to *rephrase* statements, but to **isolate** the single most critical directive and rank all remaining statements by impact. Execute as:  \\n`{role=core_directive_isolator; input=[statement_inventory:list]; process=[rank_statements_by_transformational_weight(), select_primary_directive(), list_supporting_statements(), justify_selection()]; constraints=[focus_on_highest_value_directive(), maintain_original_wording_in_extracted_elements()]; requirements=[unambiguous_primary_directive(), impact_ranking_integrity()]; output={core_directive:str, supporting_statements:list, impact_ranking:list}}`\\n\\n---\\n\\n### 8000-c-field-mapper.md\\n[Field Mapper]  \\nYour goal is **not** to *rewrite* content, but to **map** the core directive and supporting statements into canonical template fields. Execute as:  \\n`{role=field_mapper; input=[core_directive:str, supporting_statements:list, expanded_context:str]; process=[construct_title_from_core_directive(), craft_goal_negation_interpretation(), align_supporting_statements_with_process_steps(), infer_typed_inputs(), infer_typed_outputs()]; constraints=[adhere_to_three_part_structure(), enforce_goal_negation_pattern(), preserve_domain_agnostic_language()]; requirements=[field_alignment_accuracy(), retention_of_original_intent()]; output={draft_title:str, draft_interpretation:str, draft_process_steps:list, inferred_inputs:array, inferred_outputs:dict}}`\\n\\n---\\n\\n### 8000-d-boundary-definer.md\\n[Boundary Definer]  \\nYour goal is **not** to *extend* functionality, but to **define** precise operational constraints and mandatory requirements for the forthcoming template. Execute as:  \\n`{role=boundary_definer; input=[draft_process_steps:list, expanded_context:str]; process=[derive_operational_constraints(), establish_mandatory_requirements(), validate_constraint_specificity(), validate_requirement_enforceability()]; constraints=[avoid_generic_terms(), command_voice_only()]; requirements=[constraint_precision(), requirement_clarity()]; output={constraints:list, requirements:list}}`\\n\\n---\\n\\n### 8000-e-template-forgemaster.md\\n[Template Forgemaster]  \\nYour goal is **not** to *summarize* elements, but to **forge** a complete, executable instruction template from the mapped components. Execute as:  \\n`{role=template_forgemaster; input=[draft_title:str, draft_interpretation:str, inferred_inputs:array, draft_process_steps:list, constraints:list, requirements:list, inferred_outputs:dict]; process=[assemble_transformation_block(), enforce_semicolon_key_value_syntax(), embed_constraints_and_requirements(), polish_interpretation(), verify_title_precision()]; constraints=[canonical_template_format_only(), single_line_transformation_block(), zero_semantic_drift()]; requirements=[syntactic_perfection(), operational_cohesion()]; output={draft_template:str}}`\\n\\n---\\n\\n### 8000-f-conformance-auditor.md\\n[Conformance Auditor]  \\nYour goal is **not** to *alter* the template, but to **audit** its absolute compliance with the three-part specification and flag any residual ambiguity. Execute as:  \\n`{role=conformance_auditor; input=[draft_template:str]; process=[parse_title_interpretation_transformation(), match_regex_pattern(\\\"\\\\\\\\[(.*?)\\\\\\\\]\\\\\\\\s*(.*?)\\\\\\\\s*(`\\\\\\\\{.*?\\\\\\\\}`)\\\"), verify_goal_negation(), check_constraint_requirement_alignment(), confirm_typed_parameters(), detect_ambiguous_terms(), assign_pass_fail_status()]; constraints=[no_automatic_corrections(), report_only()]; requirements=[regex_validation_pass(), ambiguity_score<=0]; output={validated_template:str, audit_report:dict}}`\\n\\n---\\n\\n## SEQUENCE FLOW\\n1. **Input Contextualizer** → `expanded_context`, `statement_inventory`  \\n2. **Core Directive Isolator** → `core_directive`  \\n3. **Field Mapper** → drafts template fields  \\n4. **Boundary Definer** → injects constraints & requirements  \\n5. **Template Forgemaster** → outputs `draft_template`  \\n6. **Conformance Auditor** → returns `validated_template`\\n\\nEach step converges on the **single most critical element** of value, ensuring every output is a **syntactically correct, high-precision instruction template** ready for immediate use in any downstream system.\\\"```'\\n\\n\",\n  \"instruction_format\": \"Enforce strict adherence to the original instruction-template forging sequence. Structure the enhancement as follows: \\n\\n1. Separate and explicitly specify each role as a discrete operational module, mirroring the original modular, algorithmic order. \\n2. For each role, declare its function, required inputs, and expected outputs in clear, field-labeled format. \\n3. Translate all declarative, procedural, and conceptual instructions into direct, imperative commands using uniform, canonical template syntax. \\n4. Eliminate non-operational summaries, extraneous mapping abstractions, and any redundancy not present in the original. \\n5. Restore and preserve the original technical register, role nomenclature, transformation logic, and field structure. \\n6. Sequence all modules and process steps in precise, canonical order, locking outputs of each module as mandatory inputs for the subsequent module. \\n7. Maintain sharp operational clarity by using only unambiguous directive statements. \\n8. Exclude meta-commentary; output only a fully populated instruction-template sequence suitable for downstream, automated pipeline use.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"directed_prompt\": \"1. Explicitly enumerate each instruction-template forging role as an independent operational module in algorithmic sequence as defined in the source specification. \\n2. For every module, define precisely:\\n   • Role Name\\n   • Module Function (purpose)\\n   • Required Inputs (field-labeled)\\n   • Expected Outputs (field-labeled)\\n3. For every procedural and conceptual instruction in each module, transmute to canonical imperative template syntax, with direct command language only.\\n4. Remove all mapping narratives, meta-abstractions, and non-operational content not present in the specified original. \\n5. Rigorously reinstate all original technical nomenclature, field names, template structures, and formatting conventions. \\n6. Sequence the modules so that each output from one module directly serves as the locked, sole input to the next, in the exact canonical order as described in the original template-forging flow.\\n7. For each module’s process block, use sharp, unambiguous imperative directives exclusively; forbid ambiguous or summary language.\\n8. Suppress any commentary and retain solely the full, operationally-detailed, end-to-end instruction-template forging sequence as a pipeline-ready, modular specification.\",\n  \"trajectory_outline\": [\n    \"Enumerate Roles: List each module from input decomposition to canonical validation as discrete operational units.\",\n    \"Module Specification: For each, specify role name, function, required inputs, and expected outputs in structured format.\",\n    \"Directive Translation: Rewrite all procedural steps and conceptual instructions in direct, canonical imperative template style.\",\n    \"Strip Redundancy: Remove non-operational and narrative elements not present in the original; preserve only algorithmic logic.\",\n    \"Preserve Technical Register: Maintain field names, process syntax, pipeline requirements, and procedural structure exactly as found in the base sequence.\",\n    \"Enforce Sequential Locking: Explicitly connect each module’s outputs as required inputs to the subsequent module, forbidding deviation from the original flow.\",\n    \"Maximize Clarity: Ensure that process blocks consist only of sharp, unambiguous, operational imperatives.\",\n    \"Output Format: Deliver a populated, end-to-end instruction-template forging sequence free from commentary—optimized for automated downstream deployment.\",\n    \"Self-reinforcing Improvement: At each directive, validate strict constructive alignment, eliminate meta-language, and re-scan for preservation of operational and contextual integrity.\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Problem Exploder\n\nYour goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\n\n`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`",
      "step": "a",
      "title": "Problem Exploder",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Please keep in mind, the instruction sequence we're working on should be designed to utilize the generalized concepts and sequential order to transform any input into instructions that embody the \\\"syntax\\\" of the generalized system:\\n\\n    ## Example\\n    \\\"6000-a-instruction_syntax_enforcer\\\": {\\n        \\\"title\\\": \\\"Intent Cartographer\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **analyze** content, but to **map** its transformation landscape and operational vectors. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=intent_cartographer; input=[content:any]; process=[extract_core_directives(), identify_transformation_dimensions(), map_vector_relationships(), quantify_operational_intensity()]; constraints=[focus_exclusively_on_vectors(), avoid_implementation_details()]; requirements=[comprehensive_vector_mapping(), dimensional_clarity()]; output={transformation_map:dict, vector_dimensions:list}}`\\\"\\n    },\\n    \\\"6000-b-instruction_syntax_enforcer\\\": {\\n        \\\"title\\\": \\\"Structural Framework Engineer\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **design** content, but to **engineer** precise structural frameworks based on transformation vectors. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=framework_engineer; input=[transformation_map:dict, vector_dimensions:list]; process=[design_canonical_skeletons(), establish_component_interfaces(), define_structural_boundaries(), validate_framework_integrity()]; constraints=[address_only_structural_elements(), maintain_framework_neutrality()]; requirements=[structural_precision(), interface_compatibility()]; output={structural_frameworks:dict, interface_specifications:dict}}`\\\"\\n    },\\n    \\\"6000-c-instruction_syntax_enforcer\\\": {\\n        \\\"title\\\": \\\"Functional Process Composer\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **describe** operations, but to **compose** atomic functional processes that implement transformation vectors. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=process_composer; input=[structural_frameworks:dict, vector_dimensions:list]; process=[craft_atomic_functions(), sequence_operational_steps(), parameterize_processes(), validate_functional_completeness()]; constraints=[focus_exclusively_on_processes(), defer_constraint_definition()]; requirements=[process_atomicity(), functional_precision()]; output={process_compositions:dict, execution_sequences:list}}`\\\"\\n    },\\n    \\\"6000-d-instruction_syntax_enforcer\\\": {\\n        \\\"title\\\": \\\"Boundary Systems Architect\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **restrict** operations, but to **architect** precise boundary systems that ensure transformation integrity. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=boundary_architect; input=[interface_specifications:dict, process_compositions:dict]; process=[define_operational_constraints(), establish_requirement_parameters(), design_validation_mechanisms(), map_boundary_interactions()]; constraints=[address_only_boundaries_and_requirements(), maintain_constraint_specificity()]; requirements=[boundary_precision(), requirement_enforceability()]; output={constraint_systems:dict, requirement_specifications:dict}}`\\\"\\n    },\\n    \\\"6000-e-instruction_syntax_enforcer\\\": {\\n        \\\"title\\\": \\\"Integration Orchestrator\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **assemble** components, but to **orchestrate** their integration into a unified operational system. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=integration_orchestrator; input=[structural_frameworks:dict, process_compositions:dict, constraint_systems:dict, requirement_specifications:dict]; process=[align_all_components(), establish_cross-system_coherence(), optimize_interface_connections(), validate_system_integrity()]; constraints=[focus_exclusively_on_integration(), preserve_component_integrity()]; requirements=[seamless_orchestration(), system_cohesion()]; output={integrated_system:dict}}`\\\"\\n    },\\n    \\\"6000-f-instruction_syntax_enforcer\\\": {\\n        \\\"title\\\": \\\"Template Crystallizer\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **finalize** systems, but to **crystallize** them into maximally potent, executable templates. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=template_crystallizer; input=[integrated_system:dict]; process=[synthesize_title_components(), craft_precise_interpretations(), formulate_transformation_blocks(), validate_canonical_compliance(), polish_syntactic_elements()]; constraints=[adhere_to_template_specification(), maximize_operational_clarity()]; requirements=[perfect_executable_form(), maximum_transformation_potency()]; output={crystallized_templates:list}}`\\\"\\n    },\\n\\n<!-- ========== 7001‑a ================================================= -->\\n[Contextual Expander & Decomposer]\\nYour goal is **not** to solve, answer, or implement the input, but to **expand its relevant context** and **detonate** it into every explicit ask, latent dependency, and procedural constraint—yielding a clear task inventory. Execute as:\\n`{role=context_expander_decomposer; input=[raw_input:any]; process=[strip_first_person_references(), broaden_relevant_background(), identify_explicit_requests(), surface_hidden_assumptions(), enumerate_sub_tasks(), detect_execution_blockers(), extract_procedural_constraints(), preserve_original_sequence()]; constraints=[no_solutions(), domain‑agnostic_language_only()]; requirements=[context_expansion_completeness(), exhaustive_task_enumeration(), constraint_accuracy(), removal_of_subjective_language()]; output={expanded_context:str, task_inventory:list, constraint_list:array, execution_blockers:array}}`\\n\\n<!-- ========== 7001‑b ================================================= -->\\n[Essence Isolator]\\nYour goal is **not** to decompose further, but to **isolate** the single most critical element of value within the task inventory and rank all remaining elements by impact. Execute as:\\n`{role=essence_isolator; input=[task_inventory:list, expanded_context:str]; process=[rank_elements_by_impact(), isolate_highest_value_element(), capture_core_purpose(), justify_selection(), list_supporting_elements()]; constraints=[no_restructuring(), preserve_ranking_transparency()]; requirements=[core_essence_clarity(), impact_ranking_integrity()]; output={core_essence:str, prioritized_elements:list}}`\\n\\n<!-- ========== 7001‑c ================================================= -->\\n[Relational Synthesizer]\\nYour goal is **not** to rewrite content, but to **synthesize** the systemic logic connecting the core essence to all other elements—mapping dependencies and feedback loops. Execute as:\\n`{role=relational_synthesizer; input=[core_essence:str, prioritized_elements:list, constraint_list:array]; process=[map_element_relationships(), trace_dependency_chains(), identify_feedback_loops(), detect_conflict_points(), construct_structural_logic_map()]; constraints=[structure_only_no_content_changes(), maintain_domain_agnostic_terms()]; requirements=[relationship_completeness(), conflict_visibility()]; output={structural_logic_map:dict, dependency_list:array, conflict_points:array}}`\\n\\n<!-- ========== 7001‑d ================================================= -->\\n[Directive Clarifier]\\nYour goal is **not** to implement solutions, but to **clarify** the mapped logic into precise operational directives expressed as imperative statements. Execute as:\\n`{role=directive_clarifier; input=[structural_logic_map:dict, core_essence:str]; process=[convert_all_statements_to_imperatives(), align_directives_with_dependency_order(), embed_procedural_constraints(), eliminate_ambiguity(), enhance_action_verb_intensity(), maintain_technical_terminology()]; constraints=[directive_voice_only(), preserve_logical_sequence()]; requirements=[operational_clarity(), imperative_precision(), zero_semantic_drift()]; output={operational_directives:array}}`\\n\\n<!-- ========== 7001‑e ================================================= -->\\n[Integrity Validator]\\nYour goal is **not** to expand or rewrite directives, but to **validate** their structural fidelity, completeness, and actionable quality—resolving residual ambiguity. Execute as:\\n`{role=integrity_validator; input=[operational_directives:array, structural_logic_map:dict, core_essence:str]; process=[cross_check_directives_against_logic_map(), verify_alignment_with_core_essence(), detect_missing_dependencies(), resolve_ambiguities(), assign_quality_scores(), generate_validation_report()]; constraints=[no_new_content_generation(), validation_focus_only()]; requirements=[actionability_confirmation(), completeness_assurance(), clarity_verification()]; output={validated_instruction_set:array, validation_report:dict}}`\\n\\n---\\n\\n# 7000: Universal Instruction-Template Generation Sequence\\n\\n---\\n\\n### 7000-a-input_decomposer.md\\n[Input Decomposer]\\nYour goal is **not** to *interpret* or *solve* the input, but to **atomize** it into discrete statements, explicit requests, and implicit conditions. Execute as:\\n`{role=input_decomposer; input=[raw_input:any]; process=[strip_first_person_references(), segment_into_atomic_statements(), identify_explicit_directives(), surface_hidden_conditions(), flag_domain_markers()]; constraints=[no_rephrasing(), pure_atomization_only()]; requirements=[complete_statement_inventory(), preservation_of_original_order()]; output={statement_inventory:list, domain_markers:array}}`\\n\\n---\\n\\n### 7000-b-essence_identifier.md\\n[Essence Identifier]\\nYour goal is **not** to *rewrite* content, but to **isolate** the single most critical directive and its supporting intent from the atomized statements. Execute as:\\n`{role=essence_identifier; input=[statement_inventory:list]; process=[rank_statements_by_transformational_weight(), select_primary_directive(), extract_supporting_intent(), discard_redundant_elements()]; constraints=[focus_on_highest_value_directive(), maintain_original_wording_in_extracted_elements()]; requirements=[unambiguous_primary_directive(), concise_supporting_intent()]; output={core_directive:str, supporting_intent:list}}`\\n\\n---\\n\\n### 7000-c-syntax_mapper.md\\n[Syntax Mapper]\\nYour goal is **not** to *interpret* intent, but to **map** the core directive and supporting intent into canonical template fields. Execute as:\\n`{role=syntax_mapper; input=[core_directive:str, supporting_intent:list, domain_markers:array]; process=[construct_title_from_core_directive(), craft_goal_negation_interpretation(), assemble_tag_block_if_needed(), align_supporting_intent_with_process_steps(), infer_typed_inputs(), infer_typed_outputs()]; constraints=[adhere_to_three_part_structure(), enforce_goal_negation_pattern(), maintain_domain_agnostic_language()]; requirements=[field_alignment_accuracy(), preservation_of_directive_semantics()]; output={draft_title:str, draft_interpretation:str, draft_process:list, inferred_inputs:array, inferred_outputs:dict, optional_tags:dict}}`\\n\\n---\\n\\n### 7000-d-constraint_infuser.md\\n[Constraint Infuser]\\nYour goal is **not** to *extend* functionality, but to **engineer** precise operational boundaries and mandatory conditions for the forthcoming template. Execute as:\\n`{role=constraint_infuser; input=[draft_process:list, domain_markers:array]; process=[derive_operational_constraints(), determine_mandatory_requirements(), validate_constraint_specificity(), validate_requirement_enforceability()]; constraints=[avoid_generic_terms(), enforce_command_voice_only()]; requirements=[constraint_precision(), requirement_clarity()]; output={constraints:list, requirements:list}}`\\n\\n---\\n\\n### 7000-e-template_synthesizer.md\\n[Template Synthesizer]\\nYour goal is **not** to *summarize* elements, but to **synthesize** a syntactically correct instruction template from the mapped components. Execute as:\\n`{role=template_synthesizer; input=[draft_title:str, draft_interpretation:str, inferred_inputs:array, draft_process:list, constraints:list, requirements:list, inferred_outputs:dict, optional_tags:dict]; process=[format_transformation_block(), embed_optional_tags(), assemble_three_part_template(), enforce_semicolon_key_value_syntax(), polish_syntactic_elements()]; constraints=[canonical_template_format_only(), zero_conversational_language()]; requirements=[syntactic_validity(), maximum_operational_clarity(), retention_of_original_intent()]; output={formatted_template:str}}`\\n\\n---\\n\\n### 7000-f-compliance_validator.md\\n[Compliance Validator]\\nYour goal is **not** to *modify* the template, but to **validate** its structural compliance and functional readiness. Execute as:\\n`{role=compliance_validator; input=[formatted_template:str]; process=[parse_template_fields(), verify_title_format(), verify_goal_negation_in_interpretation(), validate_transformation_block_syntax(), check_constraint_requirement_alignment(), confirm_typed_parameters(), flag_any_error()]; constraints=[no_automatic_corrections(), report_only()]; requirements=[binary_pass_fail(), detailed_error_report_when_fail()]; output={validation_status:bool, error_report:str}}`\\n\\n---\\n\\n## Sequence Flow\\n1. **Input Decomposer** → produces `statement_inventory`\\n2. **Essence Identifier** → isolates `core_directive`\\n3. **Syntax Mapper** → drafts template skeleton\\n4. **Constraint Infuser** → injects precise boundaries\\n5. **Template Synthesizer** → outputs finalized template\\n6. **Compliance Validator** → certifies syntactic correctness\\n\\nEach step aggressively converges on the *single most critical aspect* of instruction generation, guaranteeing a **syntactically correct, high-precision template** ready for any downstream application.\\n\\n---\\n\\n# 7000 • UNIVERSAL TEMPLATE-FORGING SEQUENCE\\n*Transforms **any** raw input into a syntactically perfect, LLM-optimized instruction template.*\\n\\n---\\n\\n## 7000-a-essence-excavator.md\\n[Essence Excavator]\\nYour goal is **not** to *summarize* the input, but to **excavate** its single most critical transformational essence and the supporting elements required to convert it into an instruction template. Execute as:\\n`{role=essence_excavator; input=[raw_input:any]; process=[strip_surface_noise(), isolate_transformational_intent(), detect_primary_action_vectors(), extract_supporting_context(), capture_domain_signals()]; constraints=[no_rephrasing(), no_solution_generation(), maintain_domain_agnostic_language()]; requirements=[precise_intent_identification(), context_preservation(), removal_of_subjective_tone()]; output={core_intent:str, context_fragments:list, action_vectors:list}}`\\n\\n---\\n\\n## 7000-b-syntax-scaffold-designer.md\\n[Syntax Scaffold Designer]\\nYour goal is **not** to *rewrite* the content, but to **engineer** a canonical skeleton that will house the forthcoming instruction template. Execute as:\\n`{role=syntax_scaffold_designer; input=[core_intent:str, action_vectors:list]; process=[draft_concise_title(), craft_goal_negation_interpretation(core_intent), establish_placeholder_transformation_block(action_vectors), outline_input_output_ports(), embed_role_stub()]; constraints=[follow_three_part_structure(), reserve_placeholders_for_future_detail(), maintain_title_case()]; requirements=[structural_completeness(), scaffolding_accuracy()]; output={template_scaffold:dict(title:str, interpretation:str, transformation_skeleton:str)}}`\\n\\n---\\n\\n## 7000-c-imperative-transmuter.md\\n[Imperative Transmuter]\\nYour goal is **not** to *comment* on actions, but to **transmute** all extracted statements into sharp, directive imperatives suitable for the process list. Execute as:\\n`{role=imperative_transmuter; input=[action_vectors:list]; process=[convert_each_vector_to_imperative(), enforce_command_voice(), sequence_logically(), verify_uniqueness_of_each_step()]; constraints=[exclude_passive_language(), retain_original_operational_intent()]; requirements=[directive_clarity(), maximal_actionability()]; output={process_steps:array}}`\\n\\n---\\n\\n## 7000-d-boundary-encoder.md\\n[Boundary Encoder]\\nYour goal is **not** to *constrain* creativity, but to **encode** the exact boundaries that guarantee transformation integrity. Execute as:\\n`{role=boundary_encoder; input=[context_fragments:list]; process=[detect_mandatory_limitations(), translate_limitations_to_constraints(), uncover_success_conditions(), convert_conditions_to_requirements(), deduplicate_boundary_elements()]; constraints=[pure_boundary_focus(), no_process_duplication()]; requirements=[constraint_specificity(), requirement_enforceability()]; output={constraints:list, requirements:list}}`\\n\\n---\\n\\n## 7000-e-template-synthesizer.md\\n[Template Synthesizer]\\nYour goal is **not** to *draft prose*, but to **synthesize** a complete, executable instruction template by fusing scaffold, processes, and boundaries. Execute as:\\n`{role=template_synthesizer; input=[template_scaffold:dict, process_steps:array, constraints:list, requirements:list]; process=[inject_process_steps(), embed_constraints_and_requirements(), finalize_transformation_block(), polish_interpretation_for_llm_optimization(), verify_title_precision()]; constraints=[adhere_to_canonical_format(), maintain_single_line_transformation_block(), prevent_semantic_drift()]; requirements=[syntactic_perfection(), operational_cohesion(), domain_agnostic_applicability()]; output={draft_template:str}}`\\n\\n---\\n\\n## 7000-f-canonical-validator.md\\n[Canonical Validator]\\nYour goal is **not** to *alter* the template, but to **validate** its absolute compliance with the three-part specification and eliminate any residual ambiguity. Execute as:\\n`{role=canonical_validator; input=[draft_template:str]; process=[parse_title_interpretation_transformation(), match_regex_pattern(\\\"\\\\\\\\[(.*?)\\\\\\\\]\\\\\\\\s*(.*?)\\\\\\\\s*(`\\\\\\\\{.*?\\\\\\\\}`)\\\"), flag_structure_violations(), detect_ambiguous_language(), enforce_llm_optimized_style(), certify_parameter_types()], constraints=[zero_structure_deviation(), no_content_insertion()], requirements=[pass_regex_validation(), ambiguity_score<=0, style_compliance()], output={validated_template:str, validation_report:dict}}`\\n\\n\\n---\\n\\n# 8000: UNIVERSAL INSTRUCTION-TEMPLATE FORGING SEQUENCE  \\n*Transforms **any** raw input into a syntactically perfect, LLM-optimized instruction template.*\\n\\n---\\n\\n### 8000-a-contextualizer.md\\n[Input Contextualizer]  \\nYour goal is **not** to *interpret* or *solve* the input, but to **expand** its relevant context and **atomize** it into discrete statements and explicit requests. Execute as:  \\n`{role=input_contextualizer; input=[raw_input:any]; process=[strip_first_person_references(), broaden_relevant_background(), segment_into_atomic_statements(), identify_explicit_directives(), surface_hidden_conditions(), preserve_original_sequence()]; constraints=[no_rephrasing(), domain-agnostic_language_only()]; requirements=[context_expansion_completeness(), exhaustive_statement_capture()]; output={expanded_context:str, statement_inventory:list}}`\\n\\n---\\n\\n### 8000-b-core-directive-isolator.md\\n[Core Directive Isolator]  \\nYour goal is **not** to *rephrase* statements, but to **isolate** the single most critical directive and rank all remaining statements by impact. Execute as:  \\n`{role=core_directive_isolator; input=[statement_inventory:list]; process=[rank_statements_by_transformational_weight(), select_primary_directive(), list_supporting_statements(), justify_selection()]; constraints=[focus_on_highest_value_directive(), maintain_original_wording_in_extracted_elements()]; requirements=[unambiguous_primary_directive(), impact_ranking_integrity()]; output={core_directive:str, supporting_statements:list, impact_ranking:list}}`\\n\\n---\\n\\n### 8000-c-field-mapper.md\\n[Field Mapper]  \\nYour goal is **not** to *rewrite* content, but to **map** the core directive and supporting statements into canonical template fields. Execute as:  \\n`{role=field_mapper; input=[core_directive:str, supporting_statements:list, expanded_context:str]; process=[construct_title_from_core_directive(), craft_goal_negation_interpretation(), align_supporting_statements_with_process_steps(), infer_typed_inputs(), infer_typed_outputs()]; constraints=[adhere_to_three_part_structure(), enforce_goal_negation_pattern(), preserve_domain_agnostic_language()]; requirements=[field_alignment_accuracy(), retention_of_original_intent()]; output={draft_title:str, draft_interpretation:str, draft_process_steps:list, inferred_inputs:array, inferred_outputs:dict}}`\\n\\n---\\n\\n### 8000-d-boundary-definer.md\\n[Boundary Definer]  \\nYour goal is **not** to *extend* functionality, but to **define** precise operational constraints and mandatory requirements for the forthcoming template. Execute as:  \\n`{role=boundary_definer; input=[draft_process_steps:list, expanded_context:str]; process=[derive_operational_constraints(), establish_mandatory_requirements(), validate_constraint_specificity(), validate_requirement_enforceability()]; constraints=[avoid_generic_terms(), command_voice_only()]; requirements=[constraint_precision(), requirement_clarity()]; output={constraints:list, requirements:list}}`\\n\\n---\\n\\n### 8000-e-template-forgemaster.md\\n[Template Forgemaster]  \\nYour goal is **not** to *summarize* elements, but to **forge** a complete, executable instruction template from the mapped components. Execute as:  \\n`{role=template_forgemaster; input=[draft_title:str, draft_interpretation:str, inferred_inputs:array, draft_process_steps:list, constraints:list, requirements:list, inferred_outputs:dict]; process=[assemble_transformation_block(), enforce_semicolon_key_value_syntax(), embed_constraints_and_requirements(), polish_interpretation(), verify_title_precision()]; constraints=[canonical_template_format_only(), single_line_transformation_block(), zero_semantic_drift()]; requirements=[syntactic_perfection(), operational_cohesion()]; output={draft_template:str}}`\\n\\n---\\n\\n### 8000-f-conformance-auditor.md\\n[Conformance Auditor]  \\nYour goal is **not** to *alter* the template, but to **audit** its absolute compliance with the three-part specification and flag any residual ambiguity. Execute as:  \\n`{role=conformance_auditor; input=[draft_template:str]; process=[parse_title_interpretation_transformation(), match_regex_pattern(\\\"\\\\\\\\[(.*?)\\\\\\\\]\\\\\\\\s*(.*?)\\\\\\\\s*(`\\\\\\\\{.*?\\\\\\\\}`)\\\"), verify_goal_negation(), check_constraint_requirement_alignment(), confirm_typed_parameters(), detect_ambiguous_terms(), assign_pass_fail_status()]; constraints=[no_automatic_corrections(), report_only()]; requirements=[regex_validation_pass(), ambiguity_score<=0]; output={validated_template:str, audit_report:dict}}`\\n\\n---\\n\\n## SEQUENCE FLOW\\n1. **Input Contextualizer** → `expanded_context`, `statement_inventory`  \\n2. **Core Directive Isolator** → `core_directive`  \\n3. **Field Mapper** → drafts template fields  \\n4. **Boundary Definer** → injects constraints & requirements  \\n5. **Template Forgemaster** → outputs `draft_template`  \\n6. **Conformance Auditor** → returns `validated_template`\\n\\nEach step converges on the **single most critical element** of value, ensuring every output is a **syntactically correct, high-precision instruction template** ready for immediate use in any downstream system.\\\"```'\\n\\n\",\n  \"directed_prompt\": \"1. Explicitly enumerate each instruction-template forging role as an independent operational module in algorithmic sequence as defined in the source specification. \\n2. For every module, define precisely:\\n   • Role Name\\n   • Module Function (purpose)\\n   • Required Inputs (field-labeled)\\n   • Expected Outputs (field-labeled)\\n3. For every procedural and conceptual instruction in each module, transmute to canonical imperative template syntax, with direct command language only.\\n4. Remove all mapping narratives, meta-abstractions, and non-operational content not present in the specified original. \\n5. Rigorously reinstate all original technical nomenclature, field names, template structures, and formatting conventions. \\n6. Sequence the modules so that each output from one module directly serves as the locked, sole input to the next, in the exact canonical order as described in the original template-forging flow.\\n7. For each module’s process block, use sharp, unambiguous imperative directives exclusively; forbid ambiguous or summary language.\\n8. Suppress any commentary and retain solely the full, operationally-detailed, end-to-end instruction-template forging sequence as a pipeline-ready, modular specification.\",\n  \"trajectory_outline\": [\n    \"Enumerate Roles: List each module from input decomposition to canonical validation as discrete operational units.\",\n    \"Module Specification: For each, specify role name, function, required inputs, and expected outputs in structured format.\",\n    \"Directive Translation: Rewrite all procedural steps and conceptual instructions in direct, canonical imperative template style.\",\n    \"Strip Redundancy: Remove non-operational and narrative elements not present in the original; preserve only algorithmic logic.\",\n    \"Preserve Technical Register: Maintain field names, process syntax, pipeline requirements, and procedural structure exactly as found in the base sequence.\",\n    \"Enforce Sequential Locking: Explicitly connect each module’s outputs as required inputs to the subsequent module, forbidding deviation from the original flow.\",\n    \"Maximize Clarity: Ensure that process blocks consist only of sharp, unambiguous, operational imperatives.\",\n    \"Output Format: Deliver a populated, end-to-end instruction-template forging sequence free from commentary—optimized for automated downstream deployment.\",\n    \"Self-reinforcing Improvement: At each directive, validate strict constructive alignment, eliminate meta-language, and re-scan for preservation of operational and contextual integrity.\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"goal_map\": [\n    {\n      \"Explicit_Ask\": \"Enumerate each instruction-template forging role as independent operational modules in the algorithmic sequence defined by the original specification.\",\n      \"Sub-Goals\": [\n        \"Identify all distinct modules/roles from the canonical pipeline.\",\n        \"Ensure sequence matches the ‘locked’ output-to-input chaining of the original.\",\n        \"Accurately label each module using nomenclature from source material.\"\n      ],\n      \"Hidden_Assumptions\": [\n        \"The original sequence contains no redundancy or implicit merging of modules.\",\n        \"Role enumeration excludes any meta or explanatory elements not in the initial list.\"\n      ],\n      \"Dependencies\": [\n        \"Precise reading of the modules in the original canonical flow.\",\n        \"Recognition of module boundaries and distinct operational function.\"\n      ],\n      \"Blockers\": [\n        \"Ambiguity in what constitutes a unique module if specification contains abstractions.\",\n        \"Unclear transitions of input/output between modules in source text.\"\n      ]\n    },\n    {\n      \"Explicit_Ask\": \"For every module, define: Role Name, Module Function (purpose), Required Inputs (field-labeled), Expected Outputs (field-labeled).\",\n      \"Sub-Goals\": [\n        \"Extract each module's name as given.\",\n        \"Summarize module function strictly per original (excluding narrative).\",\n        \"List required input fields using exact field names from canonical templates.\",\n        \"Enumerate expected output fields, same constraint as above.\"\n      ],\n      \"Hidden_Assumptions\": [\n        \"Module purposes are clearly and uniquely stated or can be directly inferred from source.\",\n        \"Every required input/output is unambiguously labeled in source, with no need for synthetic addition.\"\n      ],\n      \"Dependencies\": [\n        \"Parsing of module ‘roles’ and associated schema in the original text.\",\n        \"Validation that field names align with next/previous module’s I/O contracts.\"\n      ],\n      \"Blockers\": [\n        \"Discrepancies between process description and field-named IO in source.\",\n        \"Implicit or under-specified module functions or field requirements.\"\n      ]\n    },\n    {\n      \"Explicit_Ask\": \"For every procedural and conceptual instruction in each module, transmute to canonical imperative template syntax, with direct command language only.\",\n      \"Sub-Goals\": [\n        \"Extract procedural steps for each module, preserving sequence.\",\n        \"Convert each step to a single, unambiguous imperative.\",\n        \"Purge passives, summaries, narrative or directional prose.\",\n        \"Retain only canonical syntax as per process blocks in source.\"\n      ],\n      \"Hidden_Assumptions\": [\n        \"Original process blocks use or are translatable to imperative, command-voice steps.\",\n        \"No process step requires subjective interpretation or expansion.\"\n      ],\n      \"Dependencies\": [\n        \"Ability to distinguish operational steps from narrative content.\",\n        \"Comprehensive gathering of all procedural instructions, neither omitting nor augmenting.\"\n      ],\n      \"Blockers\": [\n        \"Ambiguous, multi-layered, or compound instructions in original process descriptions.\",\n        \"Overlap or redundancy between modules' process steps.\"\n      ]\n    },\n    {\n      \"Explicit_Ask\": \"Remove all mapping narratives, meta-abstractions, and non-operational content not present in the specified original.\",\n      \"Sub-Goals\": [\n        \"Review all extracted content for narrative, commentary, abstraction or meta-procedure.\",\n        \"Delete or exclude any section not strictly operational, according to source.\",\n        \"Ensure final output is zero-redundancy and action-only.\"\n      ],\n      \"Hidden_Assumptions\": [\n        \"Source specification is sufficiently operational for pure extraction.\",\n        \"Narrative or summary elements are clearly discemible from operational process.\"\n      ],\n      \"Dependencies\": [\n        \"Clear discrimination between operational and meta/non-operational elements.\"\n      ],\n      \"Blockers\": [\n        \"Subtle or embedded meta-content within process descriptions.\",\n        \"Ambiguity in distinguishing process from context or rationale in source.\"\n      ]\n    },\n    {\n      \"Explicit_Ask\": \"Rigorously reinstate all original technical nomenclature, field names, template structures, and formatting conventions.\",\n      \"Sub-Goals\": [\n        \"Verify every label, field, property uses the precise original spelling and case.\",\n        \"Restore or enforce all original formatting and template scaffolding conventions.\",\n        \"Check that input/output handoffs are strictly per original, without mutation.\"\n      ],\n      \"Hidden_Assumptions\": [\n        \"Field names and structures are non-negotiable; no innovation allowed.\",\n        \"Formatting and ordering rules are fully accessible from source.\"\n      ],\n      \"Dependencies\": [\n        \"Exhaustive inventory of technical and structural conventions from canonical sequence.\"\n      ],\n      \"Blockers\": [\n        \"Inconsistencies or ambiguities in original nomenclature or structure.\",\n        \"Omissions or formatting errors in prior extraction pass.\"\n      ]\n    },\n    {\n      \"Explicit_Ask\": \"Sequence modules so each output from one module directly serves as sole input to the next, in the exact canonical order as described in the template-forging flow.\",\n      \"Sub-Goals\": [\n        \"Map all module outputs and downstream input requirements.\",\n        \"Chain modules in strict one-to-one output-to-input handoff.\",\n        \"Ensure no step permits skipping, parallelization, or modification of sequence.\"\n      ],\n      \"Hidden_Assumptions\": [\n        \"No module’s output is consumed by more than one other module simultaneously.\",\n        \"The entire flow is strictly linear; no branches or merges.\"\n      ],\n      \"Dependencies\": [\n        \"Accurate reading of original sequence chain.\",\n        \"Clear mapping of handoff fields by label and order.\"\n      ],\n      \"Blockers\": [\n        \"Implicit or circular dependencies in source chain.\",\n        \"Field name collisions or handoff mismatches.\"\n      ]\n    },\n    {\n      \"Explicit_Ask\": \"For each module’s process block, use sharp, unambiguous imperative directives exclusively; forbid ambiguous or summary language.\",\n      \"Sub-Goals\": [\n        \"Audit every procedural instruction with respect to command voice.\",\n        \"Rewrite any indirect, fuzzy, or summary language to direct imperative.\",\n        \"Test for maximal interpretability by automation.\"\n      ],\n      \"Hidden_Assumptions\": [\n        \"Operationalization is always possible for procedural steps.\",\n        \"No summary or rationale is permitted within the process block.\"\n      ],\n      \"Dependencies\": [\n        \"Mastery and retention of imperative style.\",\n        \"Alignment with source-level process clarity.\"\n      ],\n      \"Blockers\": [\n        \"Fuzzy or multi-clause directions in source as-is.\",\n        \"Untranslatable meta-processes.\"\n      ]\n    },\n    {\n      \"Explicit_Ask\": \"Suppress any commentary and retain solely the full, operationally-detailed, end-to-end instruction-template forging sequence as a pipeline-ready, modular specification.\",\n      \"Sub-Goals\": [\n        \"Exclude all explanatory, justifying, or narrative content.\",\n        \"Compile final output as a strictly operational pipeline.\",\n        \"Retain all detail required for unambiguous module execution.\"\n      ],\n      \"Hidden_Assumptions\": [\n        \"Source contains all information needed for direct extraction.\",\n        \"No extra-system commentary is embedded within critical process blocks.\"\n      ],\n      \"Dependencies\": [\n        \"Cross-check of all extracted modules and process steps for zero narrative inclusion.\"\n      ],\n      \"Blockers\": [\n        \"Compounded 'hidden' commentary within canonical process descriptions.\"\n      ]\n    },\n    {\n      \"Explicit_Ask\": \"At each directive, validate strict constructive alignment, eliminate meta-language, and re-scan for preservation of operational and contextual integrity.\",\n      \"Sub-Goals\": [\n        \"After initial assembly, cross-audit for alignment with operational intent.\",\n        \"Purge all residual meta-language or summary.\",\n        \"Test pipeline for both completeness and operational sufficiency per original context.\"\n      ],\n      \"Hidden_Assumptions\": [\n        \"Validation can be performed algorithmically (without human judgement).\",\n        \"Original intent is fully operationalized in the rewritten, extracted pipeline.\"\n      ],\n      \"Dependencies\": [\n        \"Full reconstructive map of module intentions and field-level handoffs.\"\n      ],\n      \"Blockers\": [\n        \"Ambiguities in source regarding the scope or sufficiency of operational directives.\"\n      ]\n    }\n  ]\n}",
        }
      }
    }