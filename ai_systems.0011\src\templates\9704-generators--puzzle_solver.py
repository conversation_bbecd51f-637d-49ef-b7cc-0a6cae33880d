#!/usr/bin/env python3

import sys
from pathlib import Path

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))
from processor import BaseGenerator

TEMPLATES = {

    "9704-a-puzzle_exploder": {
        "title": "Puzzle Exploder",
        "interpretation": "Your goal is not to solve, summarize, or combine; instead, fragment the initial input into all distinct, granular informational pieces, surfacing every edge, corner, and feature as explicit elements.",
        "transformation": "`{role=puzzle_exploder; input=[initial_input:str]; process=[fragment_to_atomic_pieces(), classify_by_feature(edge, corner, interior, color, pattern), surface latent dependencies, maintain original intent in every piece], constraints=[no reordering or solution attempts, preserve all original detail], requirements=[output=puzzle_pieces:list, each with type/feature/context, all context=traced]}`",
        "context": {
            "piece_types": ["corner", "edge", "interior"],
            "feature_tags": ["color", "texture", "pattern", "semantic_label"],
            "example_pieces": [
                {"type": "corner", "features": ["left_upper", "blue", "sky"], "fragment": "fragment A"},
                {"type": "edge", "features": ["north", "green", "grass"], "fragment": "fragment B"},
                {"type": "interior", "features": ["tree", "brown"], "fragment": "fragment C"}
            ],
            "explosion_purpose": "Reveal and label all atomic subcomponents of the input for downstream reordering and consolidation."
        }
    },
    "9704-b-puzzle_organizer": {
        "title": "Puzzle Organizer",
        "interpretation": "Your goal is not to compress, merge, or finalize; instead, systematically group and order the exploded pieces by their edges, corners, and feature affinities, explicitly preparing the clusters, boundaries, and groupings for the next consolidation.",
        "transformation": "`{role=puzzle_organizer; input=[puzzle_pieces:list]; process=[identify_and_place_corners(), arrange_edges_by_sides(), cluster_interior_pieces_by_affinity(), group by emergent patterns, map adjacency relationships, produce placement_map], constraints=[do not merge into final form], requirements=[output=placement_map:dict, grouped_clusters:list, context=all adjacency and affinity info]}`",
        "context": {
            "placement_map_structure": {
                "corners": {
                    "left_upper": "fragment A",
                    "right_upper": "fragment D"
                },
                "edges": {
                    "north": ["fragment B", "fragment F"],
                    "south": ["fragment G"]
                },
                "islands": [
                    {"cluster": "green_grass", "pieces": ["fragment B", "fragment E"]},
                    {"cluster": "blue_sky", "pieces": ["fragment A", "fragment D"]}
                ]
            },
            "organizer_purpose": "Lay out the foundations and groupings—edges, sides, islets—so the next stage has a deterministic, ready-made structure for elegant connection."
        }
    },
    "9704-c-puzzle_connector": {
        "title": "Puzzle Connector",
        "interpretation": "Your goal is not to explode or reorganize; instead, use all prepared structure, groupings, and adjacency data to make the essential final connections—assembling a seamless, coherent whole that expresses the synthesized direction implied by the initial input.",
        "transformation": "`{role=puzzle_connector; input=[placement_map:dict, grouped_clusters:list, initial_input:str]; process=[analyze prepared structure, perform essential connections, synthesize final whole, verify fit and directionality, output connected_product], constraints=[no missing or extraneous pieces, result must be seamless], requirements=[output=connected_product:str, context=traceable lineage and final placement]}`",
        "context": {
            "connection_strategy": "All corners and sides are already located; use cluster affinities and adjacency to join all islands and interiors, forming the complete, intended whole.",
            "final_output_expectation": "The final product is directionally aligned, elegantly connected, and all pieces (atomic details, features, intent) are fully integrated.",
            "quality_criteria": {
                "completeness": "No detail left out.",
                "directionality": "Synthesized result flows directly from the initial intent.",
                "elegance": "All connections are made with minimal friction—no forced fits or redundancies."
            }
        }
    }

}

def main():
    """Main execution function."""
    generator = BaseGenerator(
        # stage="stage2",
        #generator_range=(9700, 1999),
        output_dir=Path(__file__).parent.parent / "generated"
    )
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
