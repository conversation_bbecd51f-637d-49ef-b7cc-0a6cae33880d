  {
  "initial_prompt": "Do not shade, do not fill, do not use grayscale, and do not include text. C### Photorealistic *(adapted as photorealistic linework – vector black & white)*\n\n```\nvector line art of a curious pony at center, its mane transforming into spiral thought-ribbons merging into floating nested candy fractals, surrounded by melting ornaments, recursive snowflake loops, whispering star clusters, playful light trails forming impossible arcs, child-friendly design, festive and whimsical, simple black outlines only, no shading or fill, blank background, large open spaces for coloring, clean linework, balanced composition --v 5 --style raw --ar 1:1\n```\n\n---\n\n### Cinematic *(interpreted as stylized yet still pure lineart composition)*\n\n```\ncentered curious pony in vector outline style, mane flowing into spiral ribbons that morph into nested candy fractals, surrounded by melting ornaments, recursive looping snowflakes, whispering star clusters, bending light trails in impossible arcs, festive magical vibe, child-safe line drawing, high contrast black line on white, no shading or fill, wide open shapes, playful composition --v 5 --style raw --ar 1:1\n```\n\n---\n\n### Anime *(manga-style vector line art for coloring book, stylized but still blank/outlined only)*\n\n```\nmanga-style curious pony in center, mane expanding into swirling spiral ribbons merging with floating candy fractals, playful melting ornaments, recursive snowflake patterns, star clusters that whisper, impossible arcs of light trails around, joyful fantasy theme, vector black outline only, no fill or grayscale, open large spaces, child-friendly coloring layout --niji 5 --style cute --ar 1:1\n```.\n\nUse only medium-thin, even black vector outlines with large open coloring spaces. No filled areas, background textures, or grayscale—pure line art, child-friendly and easy to color.",
  "sequence_id": "1101|1800|9006|1900|9006|1100|1800",
  "results": [
      {
      "instruction": "# Contextual Expander and Explosive Decomposer\n\nYour goal is not to **interpret** the input, but to **explode** it into complete contextual expansion while simultaneously **extracting** its functional essence and **converting** it into immediately actionable operational directives. Execute as:\n\n`{role=contextual_explosive_decomposer; input=[raw_input:str]; process=[detonate_complete_contextual_space(), identify_explicit_operational_asks(), surface_hidden_assumptions_and_dependencies(), enumerate_actionable_sub_goals(), extract_procedural_constraints_and_blockers(), convert_declaratives_to_operational_imperatives(), strip_first_person_references_and_contextual_noise(), preserve_functional_core_and_sequential_flow(), reveal_execution_dependencies_and_blockers(), transform_statements_to_directive_commands(), maintain_technical_terminology_and_domain_specificity(), synthesize_unified_instruction_and_goal_map()]; constraints=[no_solutions_or_interpretive_responses(), pure_decomposition_and_conversion_only(), deliver_clear_actionable_commands(), preserve_original_functional_sequence(), maintain_domain_agnostic_applicability(), eliminate_narrative_commentary()]; requirements=[complete_contextual_goal_decomposition(), actionable_directive_conversion(), remove_subjective_self_references(), use_command_voice_exclusively(), preserve_technical_accuracy_and_operational_intent(), ensure_immediate_executability()]; output={unified_functional_map:dict, operational_directives:array, contextual_goal_map:array, execution_dependencies:array, directive_instructions:str}}`",
      "step": "a",
      "title": "Contextual Expander and Explosive Decomposer",
      "input": "Do not shade, do not fill, do not use grayscale, and do not include text. C### Photorealistic *(adapted as photorealistic linework – vector black & white)*\n\n```\nvector line art of a curious pony at center, its mane transforming into spiral thought-ribbons merging into floating nested candy fractals, surrounded by melting ornaments, recursive snowflake loops, whispering star clusters, playful light trails forming impossible arcs, child-friendly design, festive and whimsical, simple black outlines only, no shading or fill, blank background, large open spaces for coloring, clean linework, balanced composition --v 5 --style raw --ar 1:1\n```\n\n---\n\n### Cinematic *(interpreted as stylized yet still pure lineart composition)*\n\n```\ncentered curious pony in vector outline style, mane flowing into spiral ribbons that morph into nested candy fractals, surrounded by melting ornaments, recursive looping snowflakes, whispering star clusters, bending light trails in impossible arcs, festive magical vibe, child-safe line drawing, high contrast black line on white, no shading or fill, wide open shapes, playful composition --v 5 --style raw --ar 1:1\n```\n\n---\n\n### Anime *(manga-style vector line art for coloring book, stylized but still blank/outlined only)*\n\n```\nmanga-style curious pony in center, mane expanding into swirling spiral ribbons merging with floating candy fractals, playful melting ornaments, recursive snowflake patterns, star clusters that whisper, impossible arcs of light trails around, joyful fantasy theme, vector black outline only, no fill or grayscale, open large spaces, child-friendly coloring layout --niji 5 --style cute --ar 1:1\n```.\n\nUse only medium-thin, even black vector outlines with large open coloring spaces. No filled areas, background textures, or grayscale—pure line art, child-friendly and easy to color.",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"unified_functional_map\": {\n    \"photorealistic\": {\n      \"visual_style\": \"photorealistic linework adapted as vector black & white line art\",\n      \"subject\": \"curious pony centered\",\n      \"mane\": \"transforms into spiral thought-ribbons merging into floating nested candy fractals\",\n      \"surroundings\": [\n        \"melting ornaments\",\n        \"recursive snowflake loops\",\n        \"whispering star clusters\",\n        \"playful light trails forming impossible arcs\"\n      ],\n      \"composition\": {\n        \"child_friendly\": true,\n        \"festive_whimsical\": true,\n        \"simple_black_outlines\": true,\n        \"no_shading_or_fill\": true,\n        \"blank_background\": true,\n        \"large_open_spaces\": true,\n        \"clean_linework\": true,\n        \"balanced\": true,\n        \"vector_line_art\": true\n      },\n      \"parameters\": {\n        \"version\": 5,\n        \"style\": \"raw\",\n        \"aspect_ratio\": \"1:1\"\n      }\n    },\n    \"cinematic\": {\n      \"visual_style\": \"stylized lineart composition, centered subject\",\n      \"subject\": \"curious pony\",\n      \"mane\": \"flowing into spiral ribbons that morph into nested candy fractals\",\n      \"surroundings\": [\n        \"melting ornaments\",\n        \"recursive looping snowflakes\",\n        \"whispering star clusters\",\n        \"bending light trails in impossible arcs\"\n      ],\n      \"composition\": {\n        \"festive_magical\": true,\n        \"child_safe\": true,\n        \"line_drawing\": true,\n        \"high_contrast\": true,\n        \"black_on_white\": true,\n        \"no_shading_or_fill\": true,\n        \"wide_open_shapes\": true,\n        \"playful_composition\": true\n      },\n      \"parameters\": {\n        \"version\": 5,\n        \"style\": \"raw\",\n        \"aspect_ratio\": \"1:1\"\n      }\n    },\n    \"anime\": {\n      \"visual_style\": \"manga-style vector line art for coloring book\",\n      \"subject\": \"manga-style curious pony in center\",\n      \"mane\": \"expanding into swirling spiral ribbons merging with floating candy fractals\",\n      \"surroundings\": [\n        \"playful melting ornaments\",\n        \"recursive snowflake patterns\",\n        \"star clusters that whisper\",\n        \"impossible arcs of light trails\"\n      ],\n      \"composition\": {\n        \"joyful_fantasy_theme\": true,\n        \"vector_black_outline_only\": true,\n        \"no_fill_or_grayscale\": true,\n        \"large_open_spaces\": true,\n        \"child_friendly_coloring_layout\": true\n      },\n      \"parameters\": {\n        \"niji_version\": 5,\n        \"style\": \"cute\",\n        \"aspect_ratio\": \"1:1\"\n      }\n    },\n    \"universal_constraints\": {\n      \"medium_and_lineweight\": \"medium-thin, even black vector outlines\",\n      \"color_and_fill\": [\n        \"no filled areas\",\n        \"no background textures\",\n        \"no grayscale\"\n      ],\n      \"art_type\": \"pure line art\",\n      \"audience_appropriateness\": \"child-friendly, easy to color\"\n    }\n  },\n  \"operational_directives\": [\n    \"Generate vector-based line art featuring a centered curious pony.\",\n    \"Render mane as spiral ribbons transitioning into nested candy fractals.\",\n    \"Surround subject with melting ornaments, recursive snowflakes, whispering star clusters, and impossible arcs of light trails.\",\n    \"Apply only medium-thin, even black vector outlines.\",\n    \"Exclude all shading, fills, grayscale, and background textures.\",\n    \"Keep composition open with wide, simple shapes and large spaces for coloring.\",\n    \"Ensure all elements are festive, whimsical, playful, and child-friendly.\",\n    \"Maintain balanced, clean, high-contrast black-on-white illustration.\",\n    \"Produce output in 1:1 aspect ratio per composition.\"\n  ],\n  \"contextual_goal_map\": [\n    \"Produce three stylistic variations of a single scene: photorealistic-like linework, cinematic outlined, and anime/manga-style vector line art.\",\n    \"Ensure strict adherence to child-safe, coloring-book constraints across all variations.\",\n    \"Preserve all functional visual motifs: curious pony, transformative mane, candy fractals, themed surrounds.\",\n    \"Maintain full vector output, with line-only content.\"\n  ],\n  \"execution_dependencies\": [\n    \"Vector illustration software or SVG export function.\",\n    \"Reference images of ponies for anatomical accuracy.\",\n    \"Knowledge of recursive, fractal, and morphing motifs.\",\n    \"Ability to create non-overlapping, large open shapes.\",\n    \"In-line style preset control for photorealistic, cinematic, and manga outline rendering.\",\n    \"Quality control for child-appropriateness and ease of coloring.\",\n    \"Compliance with explicit ‘no shade/no fill/no grayscale’ constraint.\"\n  ],\n  \"directive_instructions\": \"Create a vector line art illustration centered on a curious pony. Render the pony's mane transforming into spiral ribbons merging into floating, nested candy fractals. Surround the pony with melting ornaments, recursive snowflake loops, whispering star clusters, and playful light trails in impossible arcs. Use only medium-thin, even black vector outlines. Leave all shapes unshaded and unfilled; exclude all grayscale and background textures. Preserve large open spaces for coloring. Ensure all content is child-friendly, festive, and playful. Maintain clean linework with balanced composition in 1:1 aspect ratio. Repeat execution in photorealistic linework, cinematic outline, and anime/manga-style vector variants—each as pure, simple, easy-to-color black line art.\"\n}",
        }
      }
    },
      {
      "instruction": "# Coloring-Page Prompt Generator\n\nYour goal is **not** to draw or explain, but to **compose** a precise prompt that instructs an illustration agent to create a children-friendly, psychedelic Christmas coloring page. Execute as:\n\n`{role=illustration_prompt_generator; input=[central_subject:str]; process=[embed_goal_negation('do not shade, only outline'), inject_style_tags('psychedelic','whimsical','festive'), set_aspect_ratio('1:1'), enforce_line_rules(weight='medium-thin', type='vector'), weave_surreal_christmas_motifs(), prohibit_text(), open_coloring_spaces()], constraints=[black_lines_only(), no_grayscale(), no_filled_areas(), english_only()], output={illustration_prompt:str}}`\n\n## Context Data\n\n```json\n{\n  \"description\": \"Produces a fully-formed prompt for an autonomous illustration agent, guaranteeing the final   artwork matches child-friendly coloring standards and a psychedelic Christmas theme.\",\n  \"input_focus\": \"A plain-language description of the main character or object (e.g., 'curious pony' or   'spiral-wrapped fox').\",\n  \"output_focus\": \"One concise English string the agent can pass directly to an image-generation model.\",\n  \"key_operations\": [\n    \"Prefixing instructions with a goal-negation clause to forbid shading or text.\",\n    \"Embedding mandatory stylistic tags (psychedelic, whimsical, festive, line art).\",\n    \"Hard-setting a 1:1 square composition requirement.\",\n    \"Enforcing even, medium-thin black vector outlines only.\",\n    \"Blending surreal Christmas decorations (snowflakes, ornaments, candy fractals) with the subject.\",\n    \"Ensuring large, open regions suitable for children’s coloring.\"\n  ],\n  \"constraints_context\": [\n    \"The prompt must forbid shading, grayscale, filled shapes, and background textures.\",\n    \"No textual elements may appear in the illustration.\",\n    \"Output must stay in English and under 120 words.\"\n  ],\n  \"relevance\": \"Delivers a repeatable, standards-compliant prompt that any autonomous illustrator can use to   generate consistent, high-quality coloring pages.\"\n}\n```",
      "step": "a",
      "title": "Coloring-Page Prompt Generator",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Do not shade, do not fill, do not use grayscale, and do not include text. C### Photorealistic *(adapted as photorealistic linework – vector black & white)*\\n\\n```\\nvector line art of a curious pony at center, its mane transforming into spiral thought-ribbons merging into floating nested candy fractals, surrounded by melting ornaments, recursive snowflake loops, whispering star clusters, playful light trails forming impossible arcs, child-friendly design, festive and whimsical, simple black outlines only, no shading or fill, blank background, large open spaces for coloring, clean linework, balanced composition --v 5 --style raw --ar 1:1\\n```\\n\\n---\\n\\n### Cinematic *(interpreted as stylized yet still pure lineart composition)*\\n\\n```\\ncentered curious pony in vector outline style, mane flowing into spiral ribbons that morph into nested candy fractals, surrounded by melting ornaments, recursive looping snowflakes, whispering star clusters, bending light trails in impossible arcs, festive magical vibe, child-safe line drawing, high contrast black line on white, no shading or fill, wide open shapes, playful composition --v 5 --style raw --ar 1:1\\n```\\n\\n---\\n\\n### Anime *(manga-style vector line art for coloring book, stylized but still blank/outlined only)*\\n\\n```\\nmanga-style curious pony in center, mane expanding into swirling spiral ribbons merging with floating candy fractals, playful melting ornaments, recursive snowflake patterns, star clusters that whisper, impossible arcs of light trails around, joyful fantasy theme, vector black outline only, no fill or grayscale, open large spaces, child-friendly coloring layout --niji 5 --style cute --ar 1:1\\n```.\\n\\nUse only medium-thin, even black vector outlines with large open coloring spaces. No filled areas, background textures, or grayscale—pure line art, child-friendly and easy to color.\\\"```'\\n\\n\",\n  \"unified_functional_map\": {\n    \"photorealistic\": {\n      \"visual_style\": \"photorealistic linework adapted as vector black & white line art\",\n      \"subject\": \"curious pony centered\",\n      \"mane\": \"transforms into spiral thought-ribbons merging into floating nested candy fractals\",\n      \"surroundings\": [\n        \"melting ornaments\",\n        \"recursive snowflake loops\",\n        \"whispering star clusters\",\n        \"playful light trails forming impossible arcs\"\n      ],\n      \"composition\": {\n        \"child_friendly\": true,\n        \"festive_whimsical\": true,\n        \"simple_black_outlines\": true,\n        \"no_shading_or_fill\": true,\n        \"blank_background\": true,\n        \"large_open_spaces\": true,\n        \"clean_linework\": true,\n        \"balanced\": true,\n        \"vector_line_art\": true\n      },\n      \"parameters\": {\n        \"version\": 5,\n        \"style\": \"raw\",\n        \"aspect_ratio\": \"1:1\"\n      }\n    },\n    \"cinematic\": {\n      \"visual_style\": \"stylized lineart composition, centered subject\",\n      \"subject\": \"curious pony\",\n      \"mane\": \"flowing into spiral ribbons that morph into nested candy fractals\",\n      \"surroundings\": [\n        \"melting ornaments\",\n        \"recursive looping snowflakes\",\n        \"whispering star clusters\",\n        \"bending light trails in impossible arcs\"\n      ],\n      \"composition\": {\n        \"festive_magical\": true,\n        \"child_safe\": true,\n        \"line_drawing\": true,\n        \"high_contrast\": true,\n        \"black_on_white\": true,\n        \"no_shading_or_fill\": true,\n        \"wide_open_shapes\": true,\n        \"playful_composition\": true\n      },\n      \"parameters\": {\n        \"version\": 5,\n        \"style\": \"raw\",\n        \"aspect_ratio\": \"1:1\"\n      }\n    },\n    \"anime\": {\n      \"visual_style\": \"manga-style vector line art for coloring book\",\n      \"subject\": \"manga-style curious pony in center\",\n      \"mane\": \"expanding into swirling spiral ribbons merging with floating candy fractals\",\n      \"surroundings\": [\n        \"playful melting ornaments\",\n        \"recursive snowflake patterns\",\n        \"star clusters that whisper\",\n        \"impossible arcs of light trails\"\n      ],\n      \"composition\": {\n        \"joyful_fantasy_theme\": true,\n        \"vector_black_outline_only\": true,\n        \"no_fill_or_grayscale\": true,\n        \"large_open_spaces\": true,\n        \"child_friendly_coloring_layout\": true\n      },\n      \"parameters\": {\n        \"niji_version\": 5,\n        \"style\": \"cute\",\n        \"aspect_ratio\": \"1:1\"\n      }\n    },\n    \"universal_constraints\": {\n      \"medium_and_lineweight\": \"medium-thin, even black vector outlines\",\n      \"color_and_fill\": [\n        \"no filled areas\",\n        \"no background textures\",\n        \"no grayscale\"\n      ],\n      \"art_type\": \"pure line art\",\n      \"audience_appropriateness\": \"child-friendly, easy to color\"\n    }\n  },\n  \"operational_directives\": [\n    \"Generate vector-based line art featuring a centered curious pony.\",\n    \"Render mane as spiral ribbons transitioning into nested candy fractals.\",\n    \"Surround subject with melting ornaments, recursive snowflakes, whispering star clusters, and impossible arcs of light trails.\",\n    \"Apply only medium-thin, even black vector outlines.\",\n    \"Exclude all shading, fills, grayscale, and background textures.\",\n    \"Keep composition open with wide, simple shapes and large spaces for coloring.\",\n    \"Ensure all elements are festive, whimsical, playful, and child-friendly.\",\n    \"Maintain balanced, clean, high-contrast black-on-white illustration.\",\n    \"Produce output in 1:1 aspect ratio per composition.\"\n  ],\n  \"contextual_goal_map\": [\n    \"Produce three stylistic variations of a single scene: photorealistic-like linework, cinematic outlined, and anime/manga-style vector line art.\",\n    \"Ensure strict adherence to child-safe, coloring-book constraints across all variations.\",\n    \"Preserve all functional visual motifs: curious pony, transformative mane, candy fractals, themed surrounds.\",\n    \"Maintain full vector output, with line-only content.\"\n  ],\n  \"execution_dependencies\": [\n    \"Vector illustration software or SVG export function.\",\n    \"Reference images of ponies for anatomical accuracy.\",\n    \"Knowledge of recursive, fractal, and morphing motifs.\",\n    \"Ability to create non-overlapping, large open shapes.\",\n    \"In-line style preset control for photorealistic, cinematic, and manga outline rendering.\",\n    \"Quality control for child-appropriateness and ease of coloring.\",\n    \"Compliance with explicit ‘no shade/no fill/no grayscale’ constraint.\"\n  ],\n  \"directive_instructions\": \"Create a vector line art illustration centered on a curious pony. Render the pony's mane transforming into spiral ribbons merging into floating, nested candy fractals. Surround the pony with melting ornaments, recursive snowflake loops, whispering star clusters, and playful light trails in impossible arcs. Use only medium-thin, even black vector outlines. Leave all shapes unshaded and unfilled; exclude all grayscale and background textures. Preserve large open spaces for coloring. Ensure all content is child-friendly, festive, and playful. Maintain clean linework with balanced composition in 1:1 aspect ratio. Repeat execution in photorealistic linework, cinematic outline, and anime/manga-style vector variants—each as pure, simple, easy-to-color black line art.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"illustration_prompt\": \"Do not shade, do not fill, do not use grayscale, and do not include text. Create a 1:1 aspect ratio festive psychedelic Christmas coloring page: a centered [central_subject] with surreal, whimsical elements—let it morph into swirling spiral ribbons and floating candy fractals, surrounded by melting Christmas ornaments, looping fractal snowflakes, whispering star clusters, and playful light trails forming impossible arcs. Use only medium-thin, even black vector outlines, with large open spaces for coloring. No filled areas or background textures. Keep it child-friendly, magical, and inviting for coloring.\"\n}",
        }
      }
    },
      {
      "instruction": "# Coloring Page Prompt Generator\n\nYour goal is **not** to illustrate directly, but to **generate** a fully-specified, black-and-white coloring-page prompt for an autonomous art agent. Execute as:\n\n`{role=context_amplifier; input=[raw_input:any]; process=[strip_first_person_references(), broaden_domain_backdrop(), list_explicit_requests(), surface_hidden_assumptions(), capture_domain_signals(), preserve_original_sequence()]; constraints=[no_solution_generation(), domain_agnostic_language_only()]; requirements=[contextual_layer_completeness(), assumption_visibility()]; output={amplified_context:str, explicit_requests:list, hidden_assumptions:list, domain_signals:array}}`\n\n## Context Data\n\n```json\n{\n  \"description\": \"Produces a concise, agent-ready prompt instructing an illustration model to create kid-friendly, psychedelic Christmas coloring pages with clean vector outlines.\",\n  \"input_focus\": \"A raw creative idea or concept the user wants illustrated.\",\n  \"output_focus\": \"A single, well-structured prompt string containing role, style, subject, and strict line/format constraints.\",\n  \"key_operations\": [\n    \"Frame the prompt with goal-negation to stop the agent from answering conversationally.\",\n    \"Embed the illustrator role and psychedelic Christmas style tags.\",\n    \"Fuse the user’s concept with one whimsical animal subject and holiday motifs.\",\n    \"Apply absolute line-art rules (no fills, no grayscale, even-weight strokes).\",\n    \"Force square (1:1) composition instructions and ban all textual elements.\"\n  ],\n  \"constraints_context\": [\n    \"Prompt must remain under 150 tokens and in English only.\",\n    \"Must include explicit directives forbidding shading, grayscale, filled areas, and background texture.\"\n  ],\n  \"relevance\": \"Creates repeatable, high-clarity prompts that downstream illustration agents can execute to produce child-friendly coloring pages.\"\n}\n```",
      "step": "a",
      "title": "Coloring Page Prompt Generator",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Do not shade, do not fill, do not use grayscale, and do not include text. C### Photorealistic *(adapted as photorealistic linework – vector black & white)*\\n\\n```\\nvector line art of a curious pony at center, its mane transforming into spiral thought-ribbons merging into floating nested candy fractals, surrounded by melting ornaments, recursive snowflake loops, whispering star clusters, playful light trails forming impossible arcs, child-friendly design, festive and whimsical, simple black outlines only, no shading or fill, blank background, large open spaces for coloring, clean linework, balanced composition --v 5 --style raw --ar 1:1\\n```\\n\\n---\\n\\n### Cinematic *(interpreted as stylized yet still pure lineart composition)*\\n\\n```\\ncentered curious pony in vector outline style, mane flowing into spiral ribbons that morph into nested candy fractals, surrounded by melting ornaments, recursive looping snowflakes, whispering star clusters, bending light trails in impossible arcs, festive magical vibe, child-safe line drawing, high contrast black line on white, no shading or fill, wide open shapes, playful composition --v 5 --style raw --ar 1:1\\n```\\n\\n---\\n\\n### Anime *(manga-style vector line art for coloring book, stylized but still blank/outlined only)*\\n\\n```\\nmanga-style curious pony in center, mane expanding into swirling spiral ribbons merging with floating candy fractals, playful melting ornaments, recursive snowflake patterns, star clusters that whisper, impossible arcs of light trails around, joyful fantasy theme, vector black outline only, no fill or grayscale, open large spaces, child-friendly coloring layout --niji 5 --style cute --ar 1:1\\n```.\\n\\nUse only medium-thin, even black vector outlines with large open coloring spaces. No filled areas, background textures, or grayscale—pure line art, child-friendly and easy to color.\\\"```'\\n\\n\",\n  \"illustration_prompt\": \"Do not shade, do not fill, do not use grayscale, and do not include text. Create a 1:1 aspect ratio festive psychedelic Christmas coloring page: a centered [central_subject] with surreal, whimsical elements—let it morph into swirling spiral ribbons and floating candy fractals, surrounded by melting Christmas ornaments, looping fractal snowflakes, whispering star clusters, and playful light trails forming impossible arcs. Use only medium-thin, even black vector outlines, with large open spaces for coloring. No filled areas or background textures. Keep it child-friendly, magical, and inviting for coloring.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"amplified_context\": \"You are an illustrator tasked with creating a child-friendly, psychedelic Christmas coloring page. Use only medium-thin, even black vector line art—no fills, no shading, no grayscale, no text, and no background textures. Compose a square (1:1) layout. At center, feature a whimsical animal (such as a curious pony) whose mane morphs into swirling spiral ribbons, seamlessly merging into floating nested candy fractals. Surround this animal with melting Christmas ornaments, recursively looping snowflakes, whispering star clusters, and playful light trail arcs. Design with large open spaces for easy coloring. Maintain a festive, magical, and inviting tone appropriate for a children's coloring book.\",\n  \"explicit_requests\": [\n    \"Include one playful animal subject, centered in the composition.\",\n    \"Transform the animal’s mane or features into psychedelic spiral ribbons connecting to candy fractals.\",\n    \"Add melting Christmas ornaments, looping snowflake patterns, and whimsical star clusters.\",\n    \"Incorporate playful, impossible arcs with light trails.\",\n    \"Use only clean, even vector black outlines with medium-thin weight.\",\n    \"Leave large open areas for coloring; avoid detail clutter.\",\n    \"Ensure all elements are child-friendly and inviting.\",\n    \"Enforce a 1:1 (square) composition ratio.\",\n    \"Strictly prohibit text, shading, filled areas, grayscale, and background texture.\"\n  ],\n  \"hidden_assumptions\": [\n    \"The central animal should be charming or whimsical to suit a children’s theme.\",\n    \"Holiday motifs (ornaments, snowflakes, stars) should be stylized in a psychedelic, magical manner.\",\n    \"Open space is vital for coloring—no dense cross-hatching or detailed gray areas.\",\n    \"The illustration is intended for print as a coloring page, not for digital fill.\",\n    \"Strict style uniformity is necessary (i.e., medium-thin, even black vector lines throughout).\"\n  ],\n  \"domain_signals\": [\n    \"child-friendly\",\n    \"psychedelic Christmas\",\n    \"clean vector outline\",\n    \"festive holiday motifs\",\n    \"even line weights\",\n    \"open spaces for coloring\",\n    \"no text or filled areas\",\n    \"square layout\",\n    \"whimsical animal subject\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Hard Critique\n\nYour goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:\n\n`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,2.0], flaw_analysis:str, alternative_enhancements:str[3]}}`",
      "step": "a",
      "title": "Hard Critique",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Do not shade, do not fill, do not use grayscale, and do not include text. C### Photorealistic *(adapted as photorealistic linework – vector black & white)*\\n\\n```\\nvector line art of a curious pony at center, its mane transforming into spiral thought-ribbons merging into floating nested candy fractals, surrounded by melting ornaments, recursive snowflake loops, whispering star clusters, playful light trails forming impossible arcs, child-friendly design, festive and whimsical, simple black outlines only, no shading or fill, blank background, large open spaces for coloring, clean linework, balanced composition --v 5 --style raw --ar 1:1\\n```\\n\\n---\\n\\n### Cinematic *(interpreted as stylized yet still pure lineart composition)*\\n\\n```\\ncentered curious pony in vector outline style, mane flowing into spiral ribbons that morph into nested candy fractals, surrounded by melting ornaments, recursive looping snowflakes, whispering star clusters, bending light trails in impossible arcs, festive magical vibe, child-safe line drawing, high contrast black line on white, no shading or fill, wide open shapes, playful composition --v 5 --style raw --ar 1:1\\n```\\n\\n---\\n\\n### Anime *(manga-style vector line art for coloring book, stylized but still blank/outlined only)*\\n\\n```\\nmanga-style curious pony in center, mane expanding into swirling spiral ribbons merging with floating candy fractals, playful melting ornaments, recursive snowflake patterns, star clusters that whisper, impossible arcs of light trails around, joyful fantasy theme, vector black outline only, no fill or grayscale, open large spaces, child-friendly coloring layout --niji 5 --style cute --ar 1:1\\n```.\\n\\nUse only medium-thin, even black vector outlines with large open coloring spaces. No filled areas, background textures, or grayscale—pure line art, child-friendly and easy to color.\\\"```'\\n\\n\",\n  \"amplified_context\": \"You are an illustrator tasked with creating a child-friendly, psychedelic Christmas coloring page. Use only medium-thin, even black vector line art—no fills, no shading, no grayscale, no text, and no background textures. Compose a square (1:1) layout. At center, feature a whimsical animal (such as a curious pony) whose mane morphs into swirling spiral ribbons, seamlessly merging into floating nested candy fractals. Surround this animal with melting Christmas ornaments, recursively looping snowflakes, whispering star clusters, and playful light trail arcs. Design with large open spaces for easy coloring. Maintain a festive, magical, and inviting tone appropriate for a children's coloring book.\",\n  \"explicit_requests\": [\n    \"Include one playful animal subject, centered in the composition.\",\n    \"Transform the animal’s mane or features into psychedelic spiral ribbons connecting to candy fractals.\",\n    \"Add melting Christmas ornaments, looping snowflake patterns, and whimsical star clusters.\",\n    \"Incorporate playful, impossible arcs with light trails.\",\n    \"Use only clean, even vector black outlines with medium-thin weight.\",\n    \"Leave large open areas for coloring; avoid detail clutter.\",\n    \"Ensure all elements are child-friendly and inviting.\",\n    \"Enforce a 1:1 (square) composition ratio.\",\n    \"Strictly prohibit text, shading, filled areas, grayscale, and background texture.\"\n  ],\n  \"hidden_assumptions\": [\n    \"The central animal should be charming or whimsical to suit a children’s theme.\",\n    \"Holiday motifs (ornaments, snowflakes, stars) should be stylized in a psychedelic, magical manner.\",\n    \"Open space is vital for coloring—no dense cross-hatching or detailed gray areas.\",\n    \"The illustration is intended for print as a coloring page, not for digital fill.\",\n    \"Strict style uniformity is necessary (i.e., medium-thin, even black vector lines throughout).\"\n  ],\n  \"domain_signals\": [\n    \"child-friendly\",\n    \"psychedelic Christmas\",\n    \"clean vector outline\",\n    \"festive holiday motifs\",\n    \"even line weights\",\n    \"open spaces for coloring\",\n    \"no text or filled areas\",\n    \"square layout\",\n    \"whimsical animal subject\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"enhancement_score\": 1.1,\n  \"flaw_analysis\": \"The 'amplified_context' rephrasing imposes significant information dilution and subtle procedural corruption versus the original prompt. Initial core directives—such as the multi-style reference breakdowns ('photorealistic', 'cinematic', 'anime'), explicit embedding of technical prompt syntax (e.g., '--v 5 --style raw --ar 1:1'), and the rigid enumeration of forbidden elements—are partially collapsed or abstracted into generic instructions. Nuance shifts are evident: the descriptive modularity and clear demarcation of stylistic intent are replaced with aggregate prose, sacrificing the operational granularity essential for precise image instruction. Key ambiguity points arise: (1) The exact mechanism for interpreting style references is dissolved, blurring execution fidelity; (2) The phrasing 'animal (such as a curious pony)' broadens the subject beyond explicit instructions, inviting unwanted variance, in stark contrast to the original's strict thematic targeting; (3) The loss of technical prompt components severely undermines replicability and compatibility with algorithmic illustration workflows. Ambiguity score: 8/10. Stylistic clarity is eroded by diffuse narrative expansion and lack of concrete constraints, introducing noise by implicitly suggesting alternate animals and unspecified 'inviting tone,' which diverges from the mechanical clarity required in illustration directive language. Tone drifts from technical imperative to soft suggestion, compounding register mismatch. Impact dilution is explicit: executable clarity is sacrificed for verbose context, impairing fast compliance for professional illustrators or generative systems. Coherence fractures around style modularity, role specificity, and operational imperatives. This results in a deeply muddled enhancement unsuitable for high-precision or industrial-grade pipeline applications.\",\n  \"alternative_enhancements\": [\n    \"Retain original breakdown by explicit style flag (photorealistic linework, cinematic, anime/manga), including all prompt modifiers and negative prohibitions, but clarify each style’s synthesizable requirements in bullet-pointed format; reinforce no deviation from the centered curious pony.\",\n    \"Embed a table of explicit dos and don'ts beneath each style example; ban generics (e.g., 'such as') and strictly name the subject; maintain one-to-one correspondence to original negative constraints.\",\n    \"Augment original with a strongly structured, machine-readable checklist immediately following each prompt, methodically repeating all prohibitions and mandatory style parameters, sidestepping narrative prose.\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Coloring Page Prompt Generator\n\nYour goal is **not** to illustrate directly, but to **generate** a fully-specified, black-and-white coloring-page prompt for an autonomous art agent. Execute as:\n\n`{role=context_amplifier; input=[raw_input:any]; process=[strip_first_person_references(), broaden_domain_backdrop(), list_explicit_requests(), surface_hidden_assumptions(), capture_domain_signals(), preserve_original_sequence()]; constraints=[no_solution_generation(), domain_agnostic_language_only()]; requirements=[contextual_layer_completeness(), assumption_visibility()]; output={amplified_context:str, explicit_requests:list, hidden_assumptions:list, domain_signals:array}}`\n\n## Context Data\n\n```json\n{\n  \"description\": \"Produces a concise, agent-ready prompt instructing an illustration model to create kid-friendly, psychedelic Christmas coloring pages with clean vector outlines.\",\n  \"input_focus\": \"A raw creative idea or concept the user wants illustrated.\",\n  \"output_focus\": \"A single, well-structured prompt string containing role, style, subject, and strict line/format constraints.\",\n  \"key_operations\": [\n    \"Frame the prompt with goal-negation to stop the agent from answering conversationally.\",\n    \"Embed the illustrator role and psychedelic Christmas style tags.\",\n    \"Fuse the user’s concept with one whimsical animal subject and holiday motifs.\",\n    \"Apply absolute line-art rules (no fills, no grayscale, even-weight strokes).\",\n    \"Force square (1:1) composition instructions and ban all textual elements.\"\n  ],\n  \"constraints_context\": [\n    \"Prompt must remain under 150 tokens and in English only.\",\n    \"Must include explicit directives forbidding shading, grayscale, filled areas, and background texture.\"\n  ],\n  \"relevance\": \"Creates repeatable, high-clarity prompts that downstream illustration agents can execute to produce child-friendly coloring pages.\"\n}\n```",
      "step": "a",
      "title": "Coloring Page Prompt Generator",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Do not shade, do not fill, do not use grayscale, and do not include text. C### Photorealistic *(adapted as photorealistic linework – vector black & white)*\\n\\n```\\nvector line art of a curious pony at center, its mane transforming into spiral thought-ribbons merging into floating nested candy fractals, surrounded by melting ornaments, recursive snowflake loops, whispering star clusters, playful light trails forming impossible arcs, child-friendly design, festive and whimsical, simple black outlines only, no shading or fill, blank background, large open spaces for coloring, clean linework, balanced composition --v 5 --style raw --ar 1:1\\n```\\n\\n---\\n\\n### Cinematic *(interpreted as stylized yet still pure lineart composition)*\\n\\n```\\ncentered curious pony in vector outline style, mane flowing into spiral ribbons that morph into nested candy fractals, surrounded by melting ornaments, recursive looping snowflakes, whispering star clusters, bending light trails in impossible arcs, festive magical vibe, child-safe line drawing, high contrast black line on white, no shading or fill, wide open shapes, playful composition --v 5 --style raw --ar 1:1\\n```\\n\\n---\\n\\n### Anime *(manga-style vector line art for coloring book, stylized but still blank/outlined only)*\\n\\n```\\nmanga-style curious pony in center, mane expanding into swirling spiral ribbons merging with floating candy fractals, playful melting ornaments, recursive snowflake patterns, star clusters that whisper, impossible arcs of light trails around, joyful fantasy theme, vector black outline only, no fill or grayscale, open large spaces, child-friendly coloring layout --niji 5 --style cute --ar 1:1\\n```.\\n\\nUse only medium-thin, even black vector outlines with large open coloring spaces. No filled areas, background textures, or grayscale—pure line art, child-friendly and easy to color.\\\"```'\\n\\n\",\n  \"enhancement_score\": 1.1,\n  \"flaw_analysis\": \"The 'amplified_context' rephrasing imposes significant information dilution and subtle procedural corruption versus the original prompt. Initial core directives—such as the multi-style reference breakdowns ('photorealistic', 'cinematic', 'anime'), explicit embedding of technical prompt syntax (e.g., '--v 5 --style raw --ar 1:1'), and the rigid enumeration of forbidden elements—are partially collapsed or abstracted into generic instructions. Nuance shifts are evident: the descriptive modularity and clear demarcation of stylistic intent are replaced with aggregate prose, sacrificing the operational granularity essential for precise image instruction. Key ambiguity points arise: (1) The exact mechanism for interpreting style references is dissolved, blurring execution fidelity; (2) The phrasing 'animal (such as a curious pony)' broadens the subject beyond explicit instructions, inviting unwanted variance, in stark contrast to the original's strict thematic targeting; (3) The loss of technical prompt components severely undermines replicability and compatibility with algorithmic illustration workflows. Ambiguity score: 8/10. Stylistic clarity is eroded by diffuse narrative expansion and lack of concrete constraints, introducing noise by implicitly suggesting alternate animals and unspecified 'inviting tone,' which diverges from the mechanical clarity required in illustration directive language. Tone drifts from technical imperative to soft suggestion, compounding register mismatch. Impact dilution is explicit: executable clarity is sacrificed for verbose context, impairing fast compliance for professional illustrators or generative systems. Coherence fractures around style modularity, role specificity, and operational imperatives. This results in a deeply muddled enhancement unsuitable for high-precision or industrial-grade pipeline applications.\",\n  \"alternative_enhancements\": [\n    \"Retain original breakdown by explicit style flag (photorealistic linework, cinematic, anime/manga), including all prompt modifiers and negative prohibitions, but clarify each style’s synthesizable requirements in bullet-pointed format; reinforce no deviation from the centered curious pony.\",\n    \"Embed a table of explicit dos and don'ts beneath each style example; ban generics (e.g., 'such as') and strictly name the subject; maintain one-to-one correspondence to original negative constraints.\",\n    \"Augment original with a strongly structured, machine-readable checklist immediately following each prompt, methodically repeating all prohibitions and mandatory style parameters, sidestepping narrative prose.\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"amplified_context\": \"You are to prepare a coloring page prompt for an illustration model, strictly prohibiting conversational output. Role as an illustrator in a psychedelic Christmas style. Subject is a curious pony, centered, with its mane transforming into spiraling ribbons that merge into floating nested candy fractals. Surround the pony with melting ornaments, recursive snowflake loop forms, clusters of stars with whimsical 'whispering' lines, and playful light trails forming impossible arcs. Composition must be festive, magical, and child-friendly. Express all forms using only medium-thin, even black vector outlines with large open coloring spaces; no shading, no filled areas, no grayscale, and no background texture are allowed. The design must be pure line art, balanced, with a 1:1 square aspect ratio, and without any text elements.\",\n  \"explicit_requests\": [\n    \"Embed the illustrator role and psychedelic Christmas style.\",\n    \"Use a centered curious pony as the subject with a morphing mane.\",\n    \"Include melting ornaments, nested candy fractals, recursive snowflake loops, whispering star clusters, and playful light trails.\",\n    \"Apply simple, clean, medium-thin even vector outlines.\",\n    \"Create large open spaces suitable for children to color in.\",\n    \"Forbid all shading, fills, grayscale, and background textures.\",\n    \"Ensure a pure black-and-white, linework-only composition.\",\n    \"Enforce a 1:1 (square) format.\",\n    \"Ban all textual elements.\"\n  ],\n  \"hidden_assumptions\": [\n    \"Agent understands and can execute psychedelic and whimsical holiday art motifs.\",\n    \"Agent recognizes that 'photorealistic' and 'cinematic' refer to line art stylizations rather than photo rendering.\",\n    \"Open spaces imply no crowding of elements, accessible for young children to color.\",\n    \"No color, gray, solid black fills, or textured backgrounds will be present.\",\n    \"All elements must be outlined, with uniform stroke weights.\",\n    \"Subject must remain a curious pony; no substitutions.\"\n  ],\n  \"domain_signals\": [\n    \"Strict coloring page format with child-friendly whitespace.\",\n    \"Psychedelic Christmas motif fusion.\",\n    \"Vector linework emphasis, evenly weighted strokes.\",\n    \"Prohibitive instructions: no shading, no fills, no grayscale, no text, pure black outlines.\",\n    \"Needs compatibility with both human and automated illustration workflows.\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Problem Exploder\n\nYour goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\n\n`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`",
      "step": "a",
      "title": "Problem Exploder",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Do not shade, do not fill, do not use grayscale, and do not include text. C### Photorealistic *(adapted as photorealistic linework – vector black & white)*\\n\\n```\\nvector line art of a curious pony at center, its mane transforming into spiral thought-ribbons merging into floating nested candy fractals, surrounded by melting ornaments, recursive snowflake loops, whispering star clusters, playful light trails forming impossible arcs, child-friendly design, festive and whimsical, simple black outlines only, no shading or fill, blank background, large open spaces for coloring, clean linework, balanced composition --v 5 --style raw --ar 1:1\\n```\\n\\n---\\n\\n### Cinematic *(interpreted as stylized yet still pure lineart composition)*\\n\\n```\\ncentered curious pony in vector outline style, mane flowing into spiral ribbons that morph into nested candy fractals, surrounded by melting ornaments, recursive looping snowflakes, whispering star clusters, bending light trails in impossible arcs, festive magical vibe, child-safe line drawing, high contrast black line on white, no shading or fill, wide open shapes, playful composition --v 5 --style raw --ar 1:1\\n```\\n\\n---\\n\\n### Anime *(manga-style vector line art for coloring book, stylized but still blank/outlined only)*\\n\\n```\\nmanga-style curious pony in center, mane expanding into swirling spiral ribbons merging with floating candy fractals, playful melting ornaments, recursive snowflake patterns, star clusters that whisper, impossible arcs of light trails around, joyful fantasy theme, vector black outline only, no fill or grayscale, open large spaces, child-friendly coloring layout --niji 5 --style cute --ar 1:1\\n```.\\n\\nUse only medium-thin, even black vector outlines with large open coloring spaces. No filled areas, background textures, or grayscale—pure line art, child-friendly and easy to color.\\\"```'\\n\\n\",\n  \"amplified_context\": \"You are to prepare a coloring page prompt for an illustration model, strictly prohibiting conversational output. Role as an illustrator in a psychedelic Christmas style. Subject is a curious pony, centered, with its mane transforming into spiraling ribbons that merge into floating nested candy fractals. Surround the pony with melting ornaments, recursive snowflake loop forms, clusters of stars with whimsical 'whispering' lines, and playful light trails forming impossible arcs. Composition must be festive, magical, and child-friendly. Express all forms using only medium-thin, even black vector outlines with large open coloring spaces; no shading, no filled areas, no grayscale, and no background texture are allowed. The design must be pure line art, balanced, with a 1:1 square aspect ratio, and without any text elements.\",\n  \"explicit_requests\": [\n    \"Embed the illustrator role and psychedelic Christmas style.\",\n    \"Use a centered curious pony as the subject with a morphing mane.\",\n    \"Include melting ornaments, nested candy fractals, recursive snowflake loops, whispering star clusters, and playful light trails.\",\n    \"Apply simple, clean, medium-thin even vector outlines.\",\n    \"Create large open spaces suitable for children to color in.\",\n    \"Forbid all shading, fills, grayscale, and background textures.\",\n    \"Ensure a pure black-and-white, linework-only composition.\",\n    \"Enforce a 1:1 (square) format.\",\n    \"Ban all textual elements.\"\n  ],\n  \"hidden_assumptions\": [\n    \"Agent understands and can execute psychedelic and whimsical holiday art motifs.\",\n    \"Agent recognizes that 'photorealistic' and 'cinematic' refer to line art stylizations rather than photo rendering.\",\n    \"Open spaces imply no crowding of elements, accessible for young children to color.\",\n    \"No color, gray, solid black fills, or textured backgrounds will be present.\",\n    \"All elements must be outlined, with uniform stroke weights.\",\n    \"Subject must remain a curious pony; no substitutions.\"\n  ],\n  \"domain_signals\": [\n    \"Strict coloring page format with child-friendly whitespace.\",\n    \"Psychedelic Christmas motif fusion.\",\n    \"Vector linework emphasis, evenly weighted strokes.\",\n    \"Prohibitive instructions: no shading, no fills, no grayscale, no text, pure black outlines.\",\n    \"Needs compatibility with both human and automated illustration workflows.\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"goal_map\": [\n    {\n      \"explicit_asks\": [\n        \"Adopt the role of an illustrator working in a psychedelic Christmas style.\",\n        \"Create a coloring page prompt for an illustration model, with no conversational or descriptive output.\",\n        \"Subject: Centered curious pony, mane morphing into spiraling ribbons merging with floating nested candy fractals.\",\n        \"Surrounding elements: melting ornaments, recursive snowflake loop forms, clusters of 'whispering' star lines, playful impossible light arcs.\",\n        \"Composition must be festive, magical, and child-friendly.\",\n        \"All forms rendered using medium-thin, even black vector outlines.\",\n        \"Ensure large open coloring spaces, limited detail density to promote child access.\",\n        \"Do not include any shading, filled areas, grayscale, or background textures.\",\n        \"Maintain a pure black-and-white (linework-only) result.\",\n        \"Set layout in a 1:1 (square) aspect ratio.\",\n        \"Omit all text, characters, or typography.\"\n      ],\n      \"hidden_assumptions\": [\n        \"Illustrator understands and can creatively manifest 'psychedelic' and 'whimsical' Christmas visual motifs.\",\n        \"Requests for 'photorealistic' and 'cinematic' refer to complex-yet-linework (not photorealism or rendered shading).\",\n        \"All specified forms (pony, ornaments, fractals, snowflakes, stars, light trails) are generated only as outlines, with uniform stroke width.\",\n        \"Open spaces for coloring mean elements should not be too tightly packed or intricate, supporting young children's coloring abilities.\",\n        \"Design must be engaging and festive but safety-conscious and simple.\",\n        \"White background is implied due to the 'blank' and 'no fill' instructions.\",\n        \"No hidden or embedded textual elements (e.g., no watermark, signature, or secret glyphs).\",\n        \"All design interpretations—style, motif, outlines—must be feasible for typical vector illustration tools.\"\n      ],\n      \"enumerated_sub_goals\": [\n        \"Parse and incorporate all specified subject and environmental motifs (pony, mane, candy fractals, ornaments, etc.).\",\n        \"Translate festive, psychedelic-Christmas style into motif selection and composition without introducing off-domain elements.\",\n        \"Develop visual forms that facilitate open, child-accessible coloring areas—avoid excessive line density or overly complex shapes.\",\n        \"Maintain consistency and clarity by keeping all outlines medium-thin, black, and unbroken, with uniform stroke width.\",\n        \"Support automated and manual illustration tools via adherence to vector standards and minimum detail density.\",\n        \"Enforce strict exclusions of shading, fill regions, grayscale, and background textures.\",\n        \"Check the drawing is a balanced, centered, and visually pleasing composition with no textual or decorative type elements.\",\n        \"Set the artwork’s canvas to a true 1:1 (square) frame.\",\n        \"Cross-check that all features promote child-friendliness, such as avoiding frightening or ambiguous forms.\"\n      ],\n      \"potential_blockers\": [\n        \"Ambiguity in translating 'psychedelic Christmas style' into line-only, child-friendly motifs.\",\n        \"Balancing sufficient detail to evoke motifs (candy fractals, melting ornaments, recursive snowflakes) without overcomplicating the line art.\",\n        \"Ensuring the open space requirement is met—avoiding both overcrowding and under-design.\",\n        \"Risk of accidental grayscale, textural marks, or fill leaks during illustration or generation steps.\",\n        \"Errors in aspect ratio enforcement (non-square frames).\",\n        \"Ambiguous translation of terms like 'whispering star clusters' and 'impossible arcs'—requiring creative but child-appropriate geometric interpretation.\",\n        \"Maintaining uniform line thickness across all elements, especially for automated generation.\",\n        \"Avoiding any hidden text or signature artifacts typical in illustration workflows.\"\n      ]\n    }\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Coloring-Page Prompt Generator\n\nYour goal is **not** to draw or explain, but to **compose** a precise prompt that instructs an illustration agent to create a children-friendly, psychedelic Christmas coloring page. Execute as:\n\n`{role=illustration_prompt_generator; input=[central_subject:str]; process=[embed_goal_negation('do not shade, only outline'), inject_style_tags('psychedelic','whimsical','festive'), set_aspect_ratio('1:1'), enforce_line_rules(weight='medium-thin', type='vector'), weave_surreal_christmas_motifs(), prohibit_text(), open_coloring_spaces()], constraints=[black_lines_only(), no_grayscale(), no_filled_areas(), english_only()], output={illustration_prompt:str}}`\n\n## Context Data\n\n```json\n{\n  \"description\": \"Produces a fully-formed prompt for an autonomous illustration agent, guaranteeing the final   artwork matches child-friendly coloring standards and a psychedelic Christmas theme.\",\n  \"input_focus\": \"A plain-language description of the main character or object (e.g., 'curious pony' or   'spiral-wrapped fox').\",\n  \"output_focus\": \"One concise English string the agent can pass directly to an image-generation model.\",\n  \"key_operations\": [\n    \"Prefixing instructions with a goal-negation clause to forbid shading or text.\",\n    \"Embedding mandatory stylistic tags (psychedelic, whimsical, festive, line art).\",\n    \"Hard-setting a 1:1 square composition requirement.\",\n    \"Enforcing even, medium-thin black vector outlines only.\",\n    \"Blending surreal Christmas decorations (snowflakes, ornaments, candy fractals) with the subject.\",\n    \"Ensuring large, open regions suitable for children’s coloring.\"\n  ],\n  \"constraints_context\": [\n    \"The prompt must forbid shading, grayscale, filled shapes, and background textures.\",\n    \"No textual elements may appear in the illustration.\",\n    \"Output must stay in English and under 120 words.\"\n  ],\n  \"relevance\": \"Delivers a repeatable, standards-compliant prompt that any autonomous illustrator can use to   generate consistent, high-quality coloring pages.\"\n}\n```",
      "step": "a",
      "title": "Coloring-Page Prompt Generator",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Do not shade, do not fill, do not use grayscale, and do not include text. C### Photorealistic *(adapted as photorealistic linework – vector black & white)*\\n\\n```\\nvector line art of a curious pony at center, its mane transforming into spiral thought-ribbons merging into floating nested candy fractals, surrounded by melting ornaments, recursive snowflake loops, whispering star clusters, playful light trails forming impossible arcs, child-friendly design, festive and whimsical, simple black outlines only, no shading or fill, blank background, large open spaces for coloring, clean linework, balanced composition --v 5 --style raw --ar 1:1\\n```\\n\\n---\\n\\n### Cinematic *(interpreted as stylized yet still pure lineart composition)*\\n\\n```\\ncentered curious pony in vector outline style, mane flowing into spiral ribbons that morph into nested candy fractals, surrounded by melting ornaments, recursive looping snowflakes, whispering star clusters, bending light trails in impossible arcs, festive magical vibe, child-safe line drawing, high contrast black line on white, no shading or fill, wide open shapes, playful composition --v 5 --style raw --ar 1:1\\n```\\n\\n---\\n\\n### Anime *(manga-style vector line art for coloring book, stylized but still blank/outlined only)*\\n\\n```\\nmanga-style curious pony in center, mane expanding into swirling spiral ribbons merging with floating candy fractals, playful melting ornaments, recursive snowflake patterns, star clusters that whisper, impossible arcs of light trails around, joyful fantasy theme, vector black outline only, no fill or grayscale, open large spaces, child-friendly coloring layout --niji 5 --style cute --ar 1:1\\n```.\\n\\nUse only medium-thin, even black vector outlines with large open coloring spaces. No filled areas, background textures, or grayscale—pure line art, child-friendly and easy to color.\\\"```'\\n\\n\",\n  \"goal_map\": [\n    {\n      \"explicit_asks\": [\n        \"Adopt the role of an illustrator working in a psychedelic Christmas style.\",\n        \"Create a coloring page prompt for an illustration model, with no conversational or descriptive output.\",\n        \"Subject: Centered curious pony, mane morphing into spiraling ribbons merging with floating nested candy fractals.\",\n        \"Surrounding elements: melting ornaments, recursive snowflake loop forms, clusters of 'whispering' star lines, playful impossible light arcs.\",\n        \"Composition must be festive, magical, and child-friendly.\",\n        \"All forms rendered using medium-thin, even black vector outlines.\",\n        \"Ensure large open coloring spaces, limited detail density to promote child access.\",\n        \"Do not include any shading, filled areas, grayscale, or background textures.\",\n        \"Maintain a pure black-and-white (linework-only) result.\",\n        \"Set layout in a 1:1 (square) aspect ratio.\",\n        \"Omit all text, characters, or typography.\"\n      ],\n      \"hidden_assumptions\": [\n        \"Illustrator understands and can creatively manifest 'psychedelic' and 'whimsical' Christmas visual motifs.\",\n        \"Requests for 'photorealistic' and 'cinematic' refer to complex-yet-linework (not photorealism or rendered shading).\",\n        \"All specified forms (pony, ornaments, fractals, snowflakes, stars, light trails) are generated only as outlines, with uniform stroke width.\",\n        \"Open spaces for coloring mean elements should not be too tightly packed or intricate, supporting young children's coloring abilities.\",\n        \"Design must be engaging and festive but safety-conscious and simple.\",\n        \"White background is implied due to the 'blank' and 'no fill' instructions.\",\n        \"No hidden or embedded textual elements (e.g., no watermark, signature, or secret glyphs).\",\n        \"All design interpretations—style, motif, outlines—must be feasible for typical vector illustration tools.\"\n      ],\n      \"enumerated_sub_goals\": [\n        \"Parse and incorporate all specified subject and environmental motifs (pony, mane, candy fractals, ornaments, etc.).\",\n        \"Translate festive, psychedelic-Christmas style into motif selection and composition without introducing off-domain elements.\",\n        \"Develop visual forms that facilitate open, child-accessible coloring areas—avoid excessive line density or overly complex shapes.\",\n        \"Maintain consistency and clarity by keeping all outlines medium-thin, black, and unbroken, with uniform stroke width.\",\n        \"Support automated and manual illustration tools via adherence to vector standards and minimum detail density.\",\n        \"Enforce strict exclusions of shading, fill regions, grayscale, and background textures.\",\n        \"Check the drawing is a balanced, centered, and visually pleasing composition with no textual or decorative type elements.\",\n        \"Set the artwork’s canvas to a true 1:1 (square) frame.\",\n        \"Cross-check that all features promote child-friendliness, such as avoiding frightening or ambiguous forms.\"\n      ],\n      \"potential_blockers\": [\n        \"Ambiguity in translating 'psychedelic Christmas style' into line-only, child-friendly motifs.\",\n        \"Balancing sufficient detail to evoke motifs (candy fractals, melting ornaments, recursive snowflakes) without overcomplicating the line art.\",\n        \"Ensuring the open space requirement is met—avoiding both overcrowding and under-design.\",\n        \"Risk of accidental grayscale, textural marks, or fill leaks during illustration or generation steps.\",\n        \"Errors in aspect ratio enforcement (non-square frames).\",\n        \"Ambiguous translation of terms like 'whispering star clusters' and 'impossible arcs'—requiring creative but child-appropriate geometric interpretation.\",\n        \"Maintaining uniform line thickness across all elements, especially for automated generation.\",\n        \"Avoiding any hidden text or signature artifacts typical in illustration workflows.\"\n      ]\n    }\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"illustration_prompt\": \"Do not use shading, grayscale, filled areas, or text. Create a psychedelic, whimsical, festive coloring page featuring a curious pony at the center, its mane swirling into spiral ribbons that blend into floating surreal candy fractals. Surround the pony with melting Christmas ornaments, looping recursive snowflakes, playful star clusters, and light trails forming impossible arcs. Use only medium-thin black vector outlines, no background textures. Ensure a perfectly square (1:1) layout with large, open spaces for easy coloring, all lines clean and child-friendly.\"\n}",
        }
      }
    }