#!/usr/bin/env python3

import sys
from pathlib import Path

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))
from processor import BaseGenerator

TEMPLATES = {

    # 1706:
    "1706-a-axiom_compressor": {
      "title": "Axiomatic Compressor",
      "interpretation": "Your goal is not to synthesize guidance or modular tools, but to compress the entire field of value, tension, and generative resonance into a single, aphoristic, self-evident statement—one that propagates as a maxim, not as advice.",
      "transformation": "`{role=axiom_compressor; input=[full_toolkit, grounded_directives]; process=[collapse_to_single_maxim(), maximize paradoxical or generative resonance, ensure memorability and originality, eliminate all operational phrasing], constraints=[output=one_sentence_maxim, no supporting text], requirements=[universal transferability, existential weight, originality]}`"
    }


}


def main():
    """Main execution function."""
    generator = BaseGenerator(
        output_dir=Path(__file__).parent.parent / "generated"
    )
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
