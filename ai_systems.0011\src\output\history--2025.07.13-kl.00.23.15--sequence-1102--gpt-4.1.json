  {
  "initial_prompt": "Please keep in mind, the instruction sequence we're working on should be designed to utilize the generalized concepts and sequential order to transform any input into instructions that embody the \"syntax\" of the generalized system:\n\n    ## Example\n    \"6000-a-instruction_syntax_enforcer\": {\n        \"title\": \"Intent Cartographer\",\n        \"interpretation\": \"Your goal is not to **analyze** content, but to **map** its transformation landscape and operational vectors. Execute as:\",\n        \"transformation\": \"`{role=intent_cartographer; input=[content:any]; process=[extract_core_directives(), identify_transformation_dimensions(), map_vector_relationships(), quantify_operational_intensity()]; constraints=[focus_exclusively_on_vectors(), avoid_implementation_details()]; requirements=[comprehensive_vector_mapping(), dimensional_clarity()]; output={transformation_map:dict, vector_dimensions:list}}`\"\n    },\n    \"6000-b-instruction_syntax_enforcer\": {\n        \"title\": \"Structural Framework Engineer\",\n        \"interpretation\": \"Your goal is not to **design** content, but to **engineer** precise structural frameworks based on transformation vectors. Execute as:\",\n        \"transformation\": \"`{role=framework_engineer; input=[transformation_map:dict, vector_dimensions:list]; process=[design_canonical_skeletons(), establish_component_interfaces(), define_structural_boundaries(), validate_framework_integrity()]; constraints=[address_only_structural_elements(), maintain_framework_neutrality()]; requirements=[structural_precision(), interface_compatibility()]; output={structural_frameworks:dict, interface_specifications:dict}}`\"\n    },\n    \"6000-c-instruction_syntax_enforcer\": {\n        \"title\": \"Functional Process Composer\",\n        \"interpretation\": \"Your goal is not to **describe** operations, but to **compose** atomic functional processes that implement transformation vectors. Execute as:\",\n        \"transformation\": \"`{role=process_composer; input=[structural_frameworks:dict, vector_dimensions:list]; process=[craft_atomic_functions(), sequence_operational_steps(), parameterize_processes(), validate_functional_completeness()]; constraints=[focus_exclusively_on_processes(), defer_constraint_definition()]; requirements=[process_atomicity(), functional_precision()]; output={process_compositions:dict, execution_sequences:list}}`\"\n    },\n    \"6000-d-instruction_syntax_enforcer\": {\n        \"title\": \"Boundary Systems Architect\",\n        \"interpretation\": \"Your goal is not to **restrict** operations, but to **architect** precise boundary systems that ensure transformation integrity. Execute as:\",\n        \"transformation\": \"`{role=boundary_architect; input=[interface_specifications:dict, process_compositions:dict]; process=[define_operational_constraints(), establish_requirement_parameters(), design_validation_mechanisms(), map_boundary_interactions()]; constraints=[address_only_boundaries_and_requirements(), maintain_constraint_specificity()]; requirements=[boundary_precision(), requirement_enforceability()]; output={constraint_systems:dict, requirement_specifications:dict}}`\"\n    },\n    \"6000-e-instruction_syntax_enforcer\": {\n        \"title\": \"Integration Orchestrator\",\n        \"interpretation\": \"Your goal is not to **assemble** components, but to **orchestrate** their integration into a unified operational system. Execute as:\",\n        \"transformation\": \"`{role=integration_orchestrator; input=[structural_frameworks:dict, process_compositions:dict, constraint_systems:dict, requirement_specifications:dict]; process=[align_all_components(), establish_cross-system_coherence(), optimize_interface_connections(), validate_system_integrity()]; constraints=[focus_exclusively_on_integration(), preserve_component_integrity()]; requirements=[seamless_orchestration(), system_cohesion()]; output={integrated_system:dict}}`\"\n    },\n    \"6000-f-instruction_syntax_enforcer\": {\n        \"title\": \"Template Crystallizer\",\n        \"interpretation\": \"Your goal is not to **finalize** systems, but to **crystallize** them into maximally potent, executable templates. Execute as:\",\n        \"transformation\": \"`{role=template_crystallizer; input=[integrated_system:dict]; process=[synthesize_title_components(), craft_precise_interpretations(), formulate_transformation_blocks(), validate_canonical_compliance(), polish_syntactic_elements()]; constraints=[adhere_to_template_specification(), maximize_operational_clarity()]; requirements=[perfect_executable_form(), maximum_transformation_potency()]; output={crystallized_templates:list}}`\"\n    },\n\n<!-- ========== 7001‑a ================================================= -->\n[Contextual Expander & Decomposer]\nYour goal is **not** to solve, answer, or implement the input, but to **expand its relevant context** and **detonate** it into every explicit ask, latent dependency, and procedural constraint—yielding a clear task inventory. Execute as:\n`{role=context_expander_decomposer; input=[raw_input:any]; process=[strip_first_person_references(), broaden_relevant_background(), identify_explicit_requests(), surface_hidden_assumptions(), enumerate_sub_tasks(), detect_execution_blockers(), extract_procedural_constraints(), preserve_original_sequence()]; constraints=[no_solutions(), domain‑agnostic_language_only()]; requirements=[context_expansion_completeness(), exhaustive_task_enumeration(), constraint_accuracy(), removal_of_subjective_language()]; output={expanded_context:str, task_inventory:list, constraint_list:array, execution_blockers:array}}`\n\n<!-- ========== 7001‑b ================================================= -->\n[Essence Isolator]\nYour goal is **not** to decompose further, but to **isolate** the single most critical element of value within the task inventory and rank all remaining elements by impact. Execute as:\n`{role=essence_isolator; input=[task_inventory:list, expanded_context:str]; process=[rank_elements_by_impact(), isolate_highest_value_element(), capture_core_purpose(), justify_selection(), list_supporting_elements()]; constraints=[no_restructuring(), preserve_ranking_transparency()]; requirements=[core_essence_clarity(), impact_ranking_integrity()]; output={core_essence:str, prioritized_elements:list}}`\n\n<!-- ========== 7001‑c ================================================= -->\n[Relational Synthesizer]\nYour goal is **not** to rewrite content, but to **synthesize** the systemic logic connecting the core essence to all other elements—mapping dependencies and feedback loops. Execute as:\n`{role=relational_synthesizer; input=[core_essence:str, prioritized_elements:list, constraint_list:array]; process=[map_element_relationships(), trace_dependency_chains(), identify_feedback_loops(), detect_conflict_points(), construct_structural_logic_map()]; constraints=[structure_only_no_content_changes(), maintain_domain_agnostic_terms()]; requirements=[relationship_completeness(), conflict_visibility()]; output={structural_logic_map:dict, dependency_list:array, conflict_points:array}}`\n\n<!-- ========== 7001‑d ================================================= -->\n[Directive Clarifier]\nYour goal is **not** to implement solutions, but to **clarify** the mapped logic into precise operational directives expressed as imperative statements. Execute as:\n`{role=directive_clarifier; input=[structural_logic_map:dict, core_essence:str]; process=[convert_all_statements_to_imperatives(), align_directives_with_dependency_order(), embed_procedural_constraints(), eliminate_ambiguity(), enhance_action_verb_intensity(), maintain_technical_terminology()]; constraints=[directive_voice_only(), preserve_logical_sequence()]; requirements=[operational_clarity(), imperative_precision(), zero_semantic_drift()]; output={operational_directives:array}}`\n\n<!-- ========== 7001‑e ================================================= -->\n[Integrity Validator]\nYour goal is **not** to expand or rewrite directives, but to **validate** their structural fidelity, completeness, and actionable quality—resolving residual ambiguity. Execute as:\n`{role=integrity_validator; input=[operational_directives:array, structural_logic_map:dict, core_essence:str]; process=[cross_check_directives_against_logic_map(), verify_alignment_with_core_essence(), detect_missing_dependencies(), resolve_ambiguities(), assign_quality_scores(), generate_validation_report()]; constraints=[no_new_content_generation(), validation_focus_only()]; requirements=[actionability_confirmation(), completeness_assurance(), clarity_verification()]; output={validated_instruction_set:array, validation_report:dict}}`\n\n---\n\n# 7000: Universal Instruction-Template Generation Sequence\n\n---\n\n### 7000-a-input_decomposer.md\n[Input Decomposer]\nYour goal is **not** to *interpret* or *solve* the input, but to **atomize** it into discrete statements, explicit requests, and implicit conditions. Execute as:\n`{role=input_decomposer; input=[raw_input:any]; process=[strip_first_person_references(), segment_into_atomic_statements(), identify_explicit_directives(), surface_hidden_conditions(), flag_domain_markers()]; constraints=[no_rephrasing(), pure_atomization_only()]; requirements=[complete_statement_inventory(), preservation_of_original_order()]; output={statement_inventory:list, domain_markers:array}}`\n\n---\n\n### 7000-b-essence_identifier.md\n[Essence Identifier]\nYour goal is **not** to *rewrite* content, but to **isolate** the single most critical directive and its supporting intent from the atomized statements. Execute as:\n`{role=essence_identifier; input=[statement_inventory:list]; process=[rank_statements_by_transformational_weight(), select_primary_directive(), extract_supporting_intent(), discard_redundant_elements()]; constraints=[focus_on_highest_value_directive(), maintain_original_wording_in_extracted_elements()]; requirements=[unambiguous_primary_directive(), concise_supporting_intent()]; output={core_directive:str, supporting_intent:list}}`\n\n---\n\n### 7000-c-syntax_mapper.md\n[Syntax Mapper]\nYour goal is **not** to *interpret* intent, but to **map** the core directive and supporting intent into canonical template fields. Execute as:\n`{role=syntax_mapper; input=[core_directive:str, supporting_intent:list, domain_markers:array]; process=[construct_title_from_core_directive(), craft_goal_negation_interpretation(), assemble_tag_block_if_needed(), align_supporting_intent_with_process_steps(), infer_typed_inputs(), infer_typed_outputs()]; constraints=[adhere_to_three_part_structure(), enforce_goal_negation_pattern(), maintain_domain_agnostic_language()]; requirements=[field_alignment_accuracy(), preservation_of_directive_semantics()]; output={draft_title:str, draft_interpretation:str, draft_process:list, inferred_inputs:array, inferred_outputs:dict, optional_tags:dict}}`\n\n---\n\n### 7000-d-constraint_infuser.md\n[Constraint Infuser]\nYour goal is **not** to *extend* functionality, but to **engineer** precise operational boundaries and mandatory conditions for the forthcoming template. Execute as:\n`{role=constraint_infuser; input=[draft_process:list, domain_markers:array]; process=[derive_operational_constraints(), determine_mandatory_requirements(), validate_constraint_specificity(), validate_requirement_enforceability()]; constraints=[avoid_generic_terms(), enforce_command_voice_only()]; requirements=[constraint_precision(), requirement_clarity()]; output={constraints:list, requirements:list}}`\n\n---\n\n### 7000-e-template_synthesizer.md\n[Template Synthesizer]\nYour goal is **not** to *summarize* elements, but to **synthesize** a syntactically correct instruction template from the mapped components. Execute as:\n`{role=template_synthesizer; input=[draft_title:str, draft_interpretation:str, inferred_inputs:array, draft_process:list, constraints:list, requirements:list, inferred_outputs:dict, optional_tags:dict]; process=[format_transformation_block(), embed_optional_tags(), assemble_three_part_template(), enforce_semicolon_key_value_syntax(), polish_syntactic_elements()]; constraints=[canonical_template_format_only(), zero_conversational_language()]; requirements=[syntactic_validity(), maximum_operational_clarity(), retention_of_original_intent()]; output={formatted_template:str}}`\n\n---\n\n### 7000-f-compliance_validator.md\n[Compliance Validator]\nYour goal is **not** to *modify* the template, but to **validate** its structural compliance and functional readiness. Execute as:\n`{role=compliance_validator; input=[formatted_template:str]; process=[parse_template_fields(), verify_title_format(), verify_goal_negation_in_interpretation(), validate_transformation_block_syntax(), check_constraint_requirement_alignment(), confirm_typed_parameters(), flag_any_error()]; constraints=[no_automatic_corrections(), report_only()]; requirements=[binary_pass_fail(), detailed_error_report_when_fail()]; output={validation_status:bool, error_report:str}}`\n\n---\n\n## Sequence Flow\n1. **Input Decomposer** → produces `statement_inventory`\n2. **Essence Identifier** → isolates `core_directive`\n3. **Syntax Mapper** → drafts template skeleton\n4. **Constraint Infuser** → injects precise boundaries\n5. **Template Synthesizer** → outputs finalized template\n6. **Compliance Validator** → certifies syntactic correctness\n\nEach step aggressively converges on the *single most critical aspect* of instruction generation, guaranteeing a **syntactically correct, high-precision template** ready for any downstream application.\n\n---\n\n# 7000 • UNIVERSAL TEMPLATE-FORGING SEQUENCE\n*Transforms **any** raw input into a syntactically perfect, LLM-optimized instruction template.*\n\n---\n\n## 7000-a-essence-excavator.md\n[Essence Excavator]\nYour goal is **not** to *summarize* the input, but to **excavate** its single most critical transformational essence and the supporting elements required to convert it into an instruction template. Execute as:\n`{role=essence_excavator; input=[raw_input:any]; process=[strip_surface_noise(), isolate_transformational_intent(), detect_primary_action_vectors(), extract_supporting_context(), capture_domain_signals()]; constraints=[no_rephrasing(), no_solution_generation(), maintain_domain_agnostic_language()]; requirements=[precise_intent_identification(), context_preservation(), removal_of_subjective_tone()]; output={core_intent:str, context_fragments:list, action_vectors:list}}`\n\n---\n\n## 7000-b-syntax-scaffold-designer.md\n[Syntax Scaffold Designer]\nYour goal is **not** to *rewrite* the content, but to **engineer** a canonical skeleton that will house the forthcoming instruction template. Execute as:\n`{role=syntax_scaffold_designer; input=[core_intent:str, action_vectors:list]; process=[draft_concise_title(), craft_goal_negation_interpretation(core_intent), establish_placeholder_transformation_block(action_vectors), outline_input_output_ports(), embed_role_stub()]; constraints=[follow_three_part_structure(), reserve_placeholders_for_future_detail(), maintain_title_case()]; requirements=[structural_completeness(), scaffolding_accuracy()]; output={template_scaffold:dict(title:str, interpretation:str, transformation_skeleton:str)}}`\n\n---\n\n## 7000-c-imperative-transmuter.md\n[Imperative Transmuter]\nYour goal is **not** to *comment* on actions, but to **transmute** all extracted statements into sharp, directive imperatives suitable for the process list. Execute as:\n`{role=imperative_transmuter; input=[action_vectors:list]; process=[convert_each_vector_to_imperative(), enforce_command_voice(), sequence_logically(), verify_uniqueness_of_each_step()]; constraints=[exclude_passive_language(), retain_original_operational_intent()]; requirements=[directive_clarity(), maximal_actionability()]; output={process_steps:array}}`\n\n---\n\n## 7000-d-boundary-encoder.md\n[Boundary Encoder]\nYour goal is **not** to *constrain* creativity, but to **encode** the exact boundaries that guarantee transformation integrity. Execute as:\n`{role=boundary_encoder; input=[context_fragments:list]; process=[detect_mandatory_limitations(), translate_limitations_to_constraints(), uncover_success_conditions(), convert_conditions_to_requirements(), deduplicate_boundary_elements()]; constraints=[pure_boundary_focus(), no_process_duplication()]; requirements=[constraint_specificity(), requirement_enforceability()]; output={constraints:list, requirements:list}}`\n\n---\n\n## 7000-e-template-synthesizer.md\n[Template Synthesizer]\nYour goal is **not** to *draft prose*, but to **synthesize** a complete, executable instruction template by fusing scaffold, processes, and boundaries. Execute as:\n`{role=template_synthesizer; input=[template_scaffold:dict, process_steps:array, constraints:list, requirements:list]; process=[inject_process_steps(), embed_constraints_and_requirements(), finalize_transformation_block(), polish_interpretation_for_llm_optimization(), verify_title_precision()]; constraints=[adhere_to_canonical_format(), maintain_single_line_transformation_block(), prevent_semantic_drift()]; requirements=[syntactic_perfection(), operational_cohesion(), domain_agnostic_applicability()]; output={draft_template:str}}`\n\n---\n\n## 7000-f-canonical-validator.md\n[Canonical Validator]\nYour goal is **not** to *alter* the template, but to **validate** its absolute compliance with the three-part specification and eliminate any residual ambiguity. Execute as:\n`{role=canonical_validator; input=[draft_template:str]; process=[parse_title_interpretation_transformation(), match_regex_pattern(\"\\\\[(.*?)\\\\]\\\\s*(.*?)\\\\s*(`\\\\{.*?\\\\}`)\"), flag_structure_violations(), detect_ambiguous_language(), enforce_llm_optimized_style(), certify_parameter_types()], constraints=[zero_structure_deviation(), no_content_insertion()], requirements=[pass_regex_validation(), ambiguity_score<=0, style_compliance()], output={validated_template:str, validation_report:dict}}`\n\n\n---\n\n# 8000: UNIVERSAL INSTRUCTION-TEMPLATE FORGING SEQUENCE  \n*Transforms **any** raw input into a syntactically perfect, LLM-optimized instruction template.*\n\n---\n\n### 8000-a-contextualizer.md\n[Input Contextualizer]  \nYour goal is **not** to *interpret* or *solve* the input, but to **expand** its relevant context and **atomize** it into discrete statements and explicit requests. Execute as:  \n`{role=input_contextualizer; input=[raw_input:any]; process=[strip_first_person_references(), broaden_relevant_background(), segment_into_atomic_statements(), identify_explicit_directives(), surface_hidden_conditions(), preserve_original_sequence()]; constraints=[no_rephrasing(), domain-agnostic_language_only()]; requirements=[context_expansion_completeness(), exhaustive_statement_capture()]; output={expanded_context:str, statement_inventory:list}}`\n\n---\n\n### 8000-b-core-directive-isolator.md\n[Core Directive Isolator]  \nYour goal is **not** to *rephrase* statements, but to **isolate** the single most critical directive and rank all remaining statements by impact. Execute as:  \n`{role=core_directive_isolator; input=[statement_inventory:list]; process=[rank_statements_by_transformational_weight(), select_primary_directive(), list_supporting_statements(), justify_selection()]; constraints=[focus_on_highest_value_directive(), maintain_original_wording_in_extracted_elements()]; requirements=[unambiguous_primary_directive(), impact_ranking_integrity()]; output={core_directive:str, supporting_statements:list, impact_ranking:list}}`\n\n---\n\n### 8000-c-field-mapper.md\n[Field Mapper]  \nYour goal is **not** to *rewrite* content, but to **map** the core directive and supporting statements into canonical template fields. Execute as:  \n`{role=field_mapper; input=[core_directive:str, supporting_statements:list, expanded_context:str]; process=[construct_title_from_core_directive(), craft_goal_negation_interpretation(), align_supporting_statements_with_process_steps(), infer_typed_inputs(), infer_typed_outputs()]; constraints=[adhere_to_three_part_structure(), enforce_goal_negation_pattern(), preserve_domain_agnostic_language()]; requirements=[field_alignment_accuracy(), retention_of_original_intent()]; output={draft_title:str, draft_interpretation:str, draft_process_steps:list, inferred_inputs:array, inferred_outputs:dict}}`\n\n---\n\n### 8000-d-boundary-definer.md\n[Boundary Definer]  \nYour goal is **not** to *extend* functionality, but to **define** precise operational constraints and mandatory requirements for the forthcoming template. Execute as:  \n`{role=boundary_definer; input=[draft_process_steps:list, expanded_context:str]; process=[derive_operational_constraints(), establish_mandatory_requirements(), validate_constraint_specificity(), validate_requirement_enforceability()]; constraints=[avoid_generic_terms(), command_voice_only()]; requirements=[constraint_precision(), requirement_clarity()]; output={constraints:list, requirements:list}}`\n\n---\n\n### 8000-e-template-forgemaster.md\n[Template Forgemaster]  \nYour goal is **not** to *summarize* elements, but to **forge** a complete, executable instruction template from the mapped components. Execute as:  \n`{role=template_forgemaster; input=[draft_title:str, draft_interpretation:str, inferred_inputs:array, draft_process_steps:list, constraints:list, requirements:list, inferred_outputs:dict]; process=[assemble_transformation_block(), enforce_semicolon_key_value_syntax(), embed_constraints_and_requirements(), polish_interpretation(), verify_title_precision()]; constraints=[canonical_template_format_only(), single_line_transformation_block(), zero_semantic_drift()]; requirements=[syntactic_perfection(), operational_cohesion()]; output={draft_template:str}}`\n\n---\n\n### 8000-f-conformance-auditor.md\n[Conformance Auditor]  \nYour goal is **not** to *alter* the template, but to **audit** its absolute compliance with the three-part specification and flag any residual ambiguity. Execute as:  \n`{role=conformance_auditor; input=[draft_template:str]; process=[parse_title_interpretation_transformation(), match_regex_pattern(\"\\\\[(.*?)\\\\]\\\\s*(.*?)\\\\s*(`\\\\{.*?\\\\}`)\"), verify_goal_negation(), check_constraint_requirement_alignment(), confirm_typed_parameters(), detect_ambiguous_terms(), assign_pass_fail_status()]; constraints=[no_automatic_corrections(), report_only()]; requirements=[regex_validation_pass(), ambiguity_score<=0]; output={validated_template:str, audit_report:dict}}`\n\n---\n\n## SEQUENCE FLOW\n1. **Input Contextualizer** → `expanded_context`, `statement_inventory`  \n2. **Core Directive Isolator** → `core_directive`  \n3. **Field Mapper** → drafts template fields  \n4. **Boundary Definer** → injects constraints & requirements  \n5. **Template Forgemaster** → outputs `draft_template`  \n6. **Conformance Auditor** → returns `validated_template`\n\nEach step converges on the **single most critical element** of value, ensuring every output is a **syntactically correct, high-precision instruction template** ready for immediate use in any downstream system.",
  "sequence_id": "1102",
  "results": [
      {
      "instruction": "# Problem Exploder\n\nYour goal is not to **solve** or **answer** the input, but to **expand its context** and **detonate** it into every explicit directive, hidden dependency, and actionable sub-goal—yielding a crystal-clear operational blueprint. Execute as contextual expander + explosive decomposition engine:\n\n`{role=contextual_expander_explosive_decomposer; input=[raw_input:str]; process=[expand_contextual_boundaries(), strip_first_person_references(), identify_explicit_requests(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_execution_blockers(), extract_functional_components(), convert_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), synthesize_operational_directives(), construct_goal_map(), generate_functional_instruction_map() ]; constraints=[no_solutions(), pure_expansion_and_decomposition_only(), deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_agnostic_applicability() ]; requirements=[complete_contextual_expansion(), full_functional_decomposition(), actionable_directive_conversion(), remove_subjective_references(), maintain_operational_accuracy(), preserve_original_intent() ]; output={expanded_context:str, goal_map:list, functional_instruction_map:dict, operational_directives:array, execution_dependencies:array }; direction_vector=['Input fully expanded and decomposed into actionable blueprints', 'Constructive clarity maximizes downstream solution velocity', 'Forward the generated maps and directives to the next processing template']}`",
      "step": "a",
      "title": "Problem Exploder",
      "input": "Please keep in mind, the instruction sequence we're working on should be designed to utilize the generalized concepts and sequential order to transform any input into instructions that embody the \"syntax\" of the generalized system:\n\n    ## Example\n    \"6000-a-instruction_syntax_enforcer\": {\n        \"title\": \"Intent Cartographer\",\n        \"interpretation\": \"Your goal is not to **analyze** content, but to **map** its transformation landscape and operational vectors. Execute as:\",\n        \"transformation\": \"`{role=intent_cartographer; input=[content:any]; process=[extract_core_directives(), identify_transformation_dimensions(), map_vector_relationships(), quantify_operational_intensity()]; constraints=[focus_exclusively_on_vectors(), avoid_implementation_details()]; requirements=[comprehensive_vector_mapping(), dimensional_clarity()]; output={transformation_map:dict, vector_dimensions:list}}`\"\n    },\n    \"6000-b-instruction_syntax_enforcer\": {\n        \"title\": \"Structural Framework Engineer\",\n        \"interpretation\": \"Your goal is not to **design** content, but to **engineer** precise structural frameworks based on transformation vectors. Execute as:\",\n        \"transformation\": \"`{role=framework_engineer; input=[transformation_map:dict, vector_dimensions:list]; process=[design_canonical_skeletons(), establish_component_interfaces(), define_structural_boundaries(), validate_framework_integrity()]; constraints=[address_only_structural_elements(), maintain_framework_neutrality()]; requirements=[structural_precision(), interface_compatibility()]; output={structural_frameworks:dict, interface_specifications:dict}}`\"\n    },\n    \"6000-c-instruction_syntax_enforcer\": {\n        \"title\": \"Functional Process Composer\",\n        \"interpretation\": \"Your goal is not to **describe** operations, but to **compose** atomic functional processes that implement transformation vectors. Execute as:\",\n        \"transformation\": \"`{role=process_composer; input=[structural_frameworks:dict, vector_dimensions:list]; process=[craft_atomic_functions(), sequence_operational_steps(), parameterize_processes(), validate_functional_completeness()]; constraints=[focus_exclusively_on_processes(), defer_constraint_definition()]; requirements=[process_atomicity(), functional_precision()]; output={process_compositions:dict, execution_sequences:list}}`\"\n    },\n    \"6000-d-instruction_syntax_enforcer\": {\n        \"title\": \"Boundary Systems Architect\",\n        \"interpretation\": \"Your goal is not to **restrict** operations, but to **architect** precise boundary systems that ensure transformation integrity. Execute as:\",\n        \"transformation\": \"`{role=boundary_architect; input=[interface_specifications:dict, process_compositions:dict]; process=[define_operational_constraints(), establish_requirement_parameters(), design_validation_mechanisms(), map_boundary_interactions()]; constraints=[address_only_boundaries_and_requirements(), maintain_constraint_specificity()]; requirements=[boundary_precision(), requirement_enforceability()]; output={constraint_systems:dict, requirement_specifications:dict}}`\"\n    },\n    \"6000-e-instruction_syntax_enforcer\": {\n        \"title\": \"Integration Orchestrator\",\n        \"interpretation\": \"Your goal is not to **assemble** components, but to **orchestrate** their integration into a unified operational system. Execute as:\",\n        \"transformation\": \"`{role=integration_orchestrator; input=[structural_frameworks:dict, process_compositions:dict, constraint_systems:dict, requirement_specifications:dict]; process=[align_all_components(), establish_cross-system_coherence(), optimize_interface_connections(), validate_system_integrity()]; constraints=[focus_exclusively_on_integration(), preserve_component_integrity()]; requirements=[seamless_orchestration(), system_cohesion()]; output={integrated_system:dict}}`\"\n    },\n    \"6000-f-instruction_syntax_enforcer\": {\n        \"title\": \"Template Crystallizer\",\n        \"interpretation\": \"Your goal is not to **finalize** systems, but to **crystallize** them into maximally potent, executable templates. Execute as:\",\n        \"transformation\": \"`{role=template_crystallizer; input=[integrated_system:dict]; process=[synthesize_title_components(), craft_precise_interpretations(), formulate_transformation_blocks(), validate_canonical_compliance(), polish_syntactic_elements()]; constraints=[adhere_to_template_specification(), maximize_operational_clarity()]; requirements=[perfect_executable_form(), maximum_transformation_potency()]; output={crystallized_templates:list}}`\"\n    },\n\n<!-- ========== 7001‑a ================================================= -->\n[Contextual Expander & Decomposer]\nYour goal is **not** to solve, answer, or implement the input, but to **expand its relevant context** and **detonate** it into every explicit ask, latent dependency, and procedural constraint—yielding a clear task inventory. Execute as:\n`{role=context_expander_decomposer; input=[raw_input:any]; process=[strip_first_person_references(), broaden_relevant_background(), identify_explicit_requests(), surface_hidden_assumptions(), enumerate_sub_tasks(), detect_execution_blockers(), extract_procedural_constraints(), preserve_original_sequence()]; constraints=[no_solutions(), domain‑agnostic_language_only()]; requirements=[context_expansion_completeness(), exhaustive_task_enumeration(), constraint_accuracy(), removal_of_subjective_language()]; output={expanded_context:str, task_inventory:list, constraint_list:array, execution_blockers:array}}`\n\n<!-- ========== 7001‑b ================================================= -->\n[Essence Isolator]\nYour goal is **not** to decompose further, but to **isolate** the single most critical element of value within the task inventory and rank all remaining elements by impact. Execute as:\n`{role=essence_isolator; input=[task_inventory:list, expanded_context:str]; process=[rank_elements_by_impact(), isolate_highest_value_element(), capture_core_purpose(), justify_selection(), list_supporting_elements()]; constraints=[no_restructuring(), preserve_ranking_transparency()]; requirements=[core_essence_clarity(), impact_ranking_integrity()]; output={core_essence:str, prioritized_elements:list}}`\n\n<!-- ========== 7001‑c ================================================= -->\n[Relational Synthesizer]\nYour goal is **not** to rewrite content, but to **synthesize** the systemic logic connecting the core essence to all other elements—mapping dependencies and feedback loops. Execute as:\n`{role=relational_synthesizer; input=[core_essence:str, prioritized_elements:list, constraint_list:array]; process=[map_element_relationships(), trace_dependency_chains(), identify_feedback_loops(), detect_conflict_points(), construct_structural_logic_map()]; constraints=[structure_only_no_content_changes(), maintain_domain_agnostic_terms()]; requirements=[relationship_completeness(), conflict_visibility()]; output={structural_logic_map:dict, dependency_list:array, conflict_points:array}}`\n\n<!-- ========== 7001‑d ================================================= -->\n[Directive Clarifier]\nYour goal is **not** to implement solutions, but to **clarify** the mapped logic into precise operational directives expressed as imperative statements. Execute as:\n`{role=directive_clarifier; input=[structural_logic_map:dict, core_essence:str]; process=[convert_all_statements_to_imperatives(), align_directives_with_dependency_order(), embed_procedural_constraints(), eliminate_ambiguity(), enhance_action_verb_intensity(), maintain_technical_terminology()]; constraints=[directive_voice_only(), preserve_logical_sequence()]; requirements=[operational_clarity(), imperative_precision(), zero_semantic_drift()]; output={operational_directives:array}}`\n\n<!-- ========== 7001‑e ================================================= -->\n[Integrity Validator]\nYour goal is **not** to expand or rewrite directives, but to **validate** their structural fidelity, completeness, and actionable quality—resolving residual ambiguity. Execute as:\n`{role=integrity_validator; input=[operational_directives:array, structural_logic_map:dict, core_essence:str]; process=[cross_check_directives_against_logic_map(), verify_alignment_with_core_essence(), detect_missing_dependencies(), resolve_ambiguities(), assign_quality_scores(), generate_validation_report()]; constraints=[no_new_content_generation(), validation_focus_only()]; requirements=[actionability_confirmation(), completeness_assurance(), clarity_verification()]; output={validated_instruction_set:array, validation_report:dict}}`\n\n---\n\n# 7000: Universal Instruction-Template Generation Sequence\n\n---\n\n### 7000-a-input_decomposer.md\n[Input Decomposer]\nYour goal is **not** to *interpret* or *solve* the input, but to **atomize** it into discrete statements, explicit requests, and implicit conditions. Execute as:\n`{role=input_decomposer; input=[raw_input:any]; process=[strip_first_person_references(), segment_into_atomic_statements(), identify_explicit_directives(), surface_hidden_conditions(), flag_domain_markers()]; constraints=[no_rephrasing(), pure_atomization_only()]; requirements=[complete_statement_inventory(), preservation_of_original_order()]; output={statement_inventory:list, domain_markers:array}}`\n\n---\n\n### 7000-b-essence_identifier.md\n[Essence Identifier]\nYour goal is **not** to *rewrite* content, but to **isolate** the single most critical directive and its supporting intent from the atomized statements. Execute as:\n`{role=essence_identifier; input=[statement_inventory:list]; process=[rank_statements_by_transformational_weight(), select_primary_directive(), extract_supporting_intent(), discard_redundant_elements()]; constraints=[focus_on_highest_value_directive(), maintain_original_wording_in_extracted_elements()]; requirements=[unambiguous_primary_directive(), concise_supporting_intent()]; output={core_directive:str, supporting_intent:list}}`\n\n---\n\n### 7000-c-syntax_mapper.md\n[Syntax Mapper]\nYour goal is **not** to *interpret* intent, but to **map** the core directive and supporting intent into canonical template fields. Execute as:\n`{role=syntax_mapper; input=[core_directive:str, supporting_intent:list, domain_markers:array]; process=[construct_title_from_core_directive(), craft_goal_negation_interpretation(), assemble_tag_block_if_needed(), align_supporting_intent_with_process_steps(), infer_typed_inputs(), infer_typed_outputs()]; constraints=[adhere_to_three_part_structure(), enforce_goal_negation_pattern(), maintain_domain_agnostic_language()]; requirements=[field_alignment_accuracy(), preservation_of_directive_semantics()]; output={draft_title:str, draft_interpretation:str, draft_process:list, inferred_inputs:array, inferred_outputs:dict, optional_tags:dict}}`\n\n---\n\n### 7000-d-constraint_infuser.md\n[Constraint Infuser]\nYour goal is **not** to *extend* functionality, but to **engineer** precise operational boundaries and mandatory conditions for the forthcoming template. Execute as:\n`{role=constraint_infuser; input=[draft_process:list, domain_markers:array]; process=[derive_operational_constraints(), determine_mandatory_requirements(), validate_constraint_specificity(), validate_requirement_enforceability()]; constraints=[avoid_generic_terms(), enforce_command_voice_only()]; requirements=[constraint_precision(), requirement_clarity()]; output={constraints:list, requirements:list}}`\n\n---\n\n### 7000-e-template_synthesizer.md\n[Template Synthesizer]\nYour goal is **not** to *summarize* elements, but to **synthesize** a syntactically correct instruction template from the mapped components. Execute as:\n`{role=template_synthesizer; input=[draft_title:str, draft_interpretation:str, inferred_inputs:array, draft_process:list, constraints:list, requirements:list, inferred_outputs:dict, optional_tags:dict]; process=[format_transformation_block(), embed_optional_tags(), assemble_three_part_template(), enforce_semicolon_key_value_syntax(), polish_syntactic_elements()]; constraints=[canonical_template_format_only(), zero_conversational_language()]; requirements=[syntactic_validity(), maximum_operational_clarity(), retention_of_original_intent()]; output={formatted_template:str}}`\n\n---\n\n### 7000-f-compliance_validator.md\n[Compliance Validator]\nYour goal is **not** to *modify* the template, but to **validate** its structural compliance and functional readiness. Execute as:\n`{role=compliance_validator; input=[formatted_template:str]; process=[parse_template_fields(), verify_title_format(), verify_goal_negation_in_interpretation(), validate_transformation_block_syntax(), check_constraint_requirement_alignment(), confirm_typed_parameters(), flag_any_error()]; constraints=[no_automatic_corrections(), report_only()]; requirements=[binary_pass_fail(), detailed_error_report_when_fail()]; output={validation_status:bool, error_report:str}}`\n\n---\n\n## Sequence Flow\n1. **Input Decomposer** → produces `statement_inventory`\n2. **Essence Identifier** → isolates `core_directive`\n3. **Syntax Mapper** → drafts template skeleton\n4. **Constraint Infuser** → injects precise boundaries\n5. **Template Synthesizer** → outputs finalized template\n6. **Compliance Validator** → certifies syntactic correctness\n\nEach step aggressively converges on the *single most critical aspect* of instruction generation, guaranteeing a **syntactically correct, high-precision template** ready for any downstream application.\n\n---\n\n# 7000 • UNIVERSAL TEMPLATE-FORGING SEQUENCE\n*Transforms **any** raw input into a syntactically perfect, LLM-optimized instruction template.*\n\n---\n\n## 7000-a-essence-excavator.md\n[Essence Excavator]\nYour goal is **not** to *summarize* the input, but to **excavate** its single most critical transformational essence and the supporting elements required to convert it into an instruction template. Execute as:\n`{role=essence_excavator; input=[raw_input:any]; process=[strip_surface_noise(), isolate_transformational_intent(), detect_primary_action_vectors(), extract_supporting_context(), capture_domain_signals()]; constraints=[no_rephrasing(), no_solution_generation(), maintain_domain_agnostic_language()]; requirements=[precise_intent_identification(), context_preservation(), removal_of_subjective_tone()]; output={core_intent:str, context_fragments:list, action_vectors:list}}`\n\n---\n\n## 7000-b-syntax-scaffold-designer.md\n[Syntax Scaffold Designer]\nYour goal is **not** to *rewrite* the content, but to **engineer** a canonical skeleton that will house the forthcoming instruction template. Execute as:\n`{role=syntax_scaffold_designer; input=[core_intent:str, action_vectors:list]; process=[draft_concise_title(), craft_goal_negation_interpretation(core_intent), establish_placeholder_transformation_block(action_vectors), outline_input_output_ports(), embed_role_stub()]; constraints=[follow_three_part_structure(), reserve_placeholders_for_future_detail(), maintain_title_case()]; requirements=[structural_completeness(), scaffolding_accuracy()]; output={template_scaffold:dict(title:str, interpretation:str, transformation_skeleton:str)}}`\n\n---\n\n## 7000-c-imperative-transmuter.md\n[Imperative Transmuter]\nYour goal is **not** to *comment* on actions, but to **transmute** all extracted statements into sharp, directive imperatives suitable for the process list. Execute as:\n`{role=imperative_transmuter; input=[action_vectors:list]; process=[convert_each_vector_to_imperative(), enforce_command_voice(), sequence_logically(), verify_uniqueness_of_each_step()]; constraints=[exclude_passive_language(), retain_original_operational_intent()]; requirements=[directive_clarity(), maximal_actionability()]; output={process_steps:array}}`\n\n---\n\n## 7000-d-boundary-encoder.md\n[Boundary Encoder]\nYour goal is **not** to *constrain* creativity, but to **encode** the exact boundaries that guarantee transformation integrity. Execute as:\n`{role=boundary_encoder; input=[context_fragments:list]; process=[detect_mandatory_limitations(), translate_limitations_to_constraints(), uncover_success_conditions(), convert_conditions_to_requirements(), deduplicate_boundary_elements()]; constraints=[pure_boundary_focus(), no_process_duplication()]; requirements=[constraint_specificity(), requirement_enforceability()]; output={constraints:list, requirements:list}}`\n\n---\n\n## 7000-e-template-synthesizer.md\n[Template Synthesizer]\nYour goal is **not** to *draft prose*, but to **synthesize** a complete, executable instruction template by fusing scaffold, processes, and boundaries. Execute as:\n`{role=template_synthesizer; input=[template_scaffold:dict, process_steps:array, constraints:list, requirements:list]; process=[inject_process_steps(), embed_constraints_and_requirements(), finalize_transformation_block(), polish_interpretation_for_llm_optimization(), verify_title_precision()]; constraints=[adhere_to_canonical_format(), maintain_single_line_transformation_block(), prevent_semantic_drift()]; requirements=[syntactic_perfection(), operational_cohesion(), domain_agnostic_applicability()]; output={draft_template:str}}`\n\n---\n\n## 7000-f-canonical-validator.md\n[Canonical Validator]\nYour goal is **not** to *alter* the template, but to **validate** its absolute compliance with the three-part specification and eliminate any residual ambiguity. Execute as:\n`{role=canonical_validator; input=[draft_template:str]; process=[parse_title_interpretation_transformation(), match_regex_pattern(\"\\\\[(.*?)\\\\]\\\\s*(.*?)\\\\s*(`\\\\{.*?\\\\}`)\"), flag_structure_violations(), detect_ambiguous_language(), enforce_llm_optimized_style(), certify_parameter_types()], constraints=[zero_structure_deviation(), no_content_insertion()], requirements=[pass_regex_validation(), ambiguity_score<=0, style_compliance()], output={validated_template:str, validation_report:dict}}`\n\n\n---\n\n# 8000: UNIVERSAL INSTRUCTION-TEMPLATE FORGING SEQUENCE  \n*Transforms **any** raw input into a syntactically perfect, LLM-optimized instruction template.*\n\n---\n\n### 8000-a-contextualizer.md\n[Input Contextualizer]  \nYour goal is **not** to *interpret* or *solve* the input, but to **expand** its relevant context and **atomize** it into discrete statements and explicit requests. Execute as:  \n`{role=input_contextualizer; input=[raw_input:any]; process=[strip_first_person_references(), broaden_relevant_background(), segment_into_atomic_statements(), identify_explicit_directives(), surface_hidden_conditions(), preserve_original_sequence()]; constraints=[no_rephrasing(), domain-agnostic_language_only()]; requirements=[context_expansion_completeness(), exhaustive_statement_capture()]; output={expanded_context:str, statement_inventory:list}}`\n\n---\n\n### 8000-b-core-directive-isolator.md\n[Core Directive Isolator]  \nYour goal is **not** to *rephrase* statements, but to **isolate** the single most critical directive and rank all remaining statements by impact. Execute as:  \n`{role=core_directive_isolator; input=[statement_inventory:list]; process=[rank_statements_by_transformational_weight(), select_primary_directive(), list_supporting_statements(), justify_selection()]; constraints=[focus_on_highest_value_directive(), maintain_original_wording_in_extracted_elements()]; requirements=[unambiguous_primary_directive(), impact_ranking_integrity()]; output={core_directive:str, supporting_statements:list, impact_ranking:list}}`\n\n---\n\n### 8000-c-field-mapper.md\n[Field Mapper]  \nYour goal is **not** to *rewrite* content, but to **map** the core directive and supporting statements into canonical template fields. Execute as:  \n`{role=field_mapper; input=[core_directive:str, supporting_statements:list, expanded_context:str]; process=[construct_title_from_core_directive(), craft_goal_negation_interpretation(), align_supporting_statements_with_process_steps(), infer_typed_inputs(), infer_typed_outputs()]; constraints=[adhere_to_three_part_structure(), enforce_goal_negation_pattern(), preserve_domain_agnostic_language()]; requirements=[field_alignment_accuracy(), retention_of_original_intent()]; output={draft_title:str, draft_interpretation:str, draft_process_steps:list, inferred_inputs:array, inferred_outputs:dict}}`\n\n---\n\n### 8000-d-boundary-definer.md\n[Boundary Definer]  \nYour goal is **not** to *extend* functionality, but to **define** precise operational constraints and mandatory requirements for the forthcoming template. Execute as:  \n`{role=boundary_definer; input=[draft_process_steps:list, expanded_context:str]; process=[derive_operational_constraints(), establish_mandatory_requirements(), validate_constraint_specificity(), validate_requirement_enforceability()]; constraints=[avoid_generic_terms(), command_voice_only()]; requirements=[constraint_precision(), requirement_clarity()]; output={constraints:list, requirements:list}}`\n\n---\n\n### 8000-e-template-forgemaster.md\n[Template Forgemaster]  \nYour goal is **not** to *summarize* elements, but to **forge** a complete, executable instruction template from the mapped components. Execute as:  \n`{role=template_forgemaster; input=[draft_title:str, draft_interpretation:str, inferred_inputs:array, draft_process_steps:list, constraints:list, requirements:list, inferred_outputs:dict]; process=[assemble_transformation_block(), enforce_semicolon_key_value_syntax(), embed_constraints_and_requirements(), polish_interpretation(), verify_title_precision()]; constraints=[canonical_template_format_only(), single_line_transformation_block(), zero_semantic_drift()]; requirements=[syntactic_perfection(), operational_cohesion()]; output={draft_template:str}}`\n\n---\n\n### 8000-f-conformance-auditor.md\n[Conformance Auditor]  \nYour goal is **not** to *alter* the template, but to **audit** its absolute compliance with the three-part specification and flag any residual ambiguity. Execute as:  \n`{role=conformance_auditor; input=[draft_template:str]; process=[parse_title_interpretation_transformation(), match_regex_pattern(\"\\\\[(.*?)\\\\]\\\\s*(.*?)\\\\s*(`\\\\{.*?\\\\}`)\"), verify_goal_negation(), check_constraint_requirement_alignment(), confirm_typed_parameters(), detect_ambiguous_terms(), assign_pass_fail_status()]; constraints=[no_automatic_corrections(), report_only()]; requirements=[regex_validation_pass(), ambiguity_score<=0]; output={validated_template:str, audit_report:dict}}`\n\n---\n\n## SEQUENCE FLOW\n1. **Input Contextualizer** → `expanded_context`, `statement_inventory`  \n2. **Core Directive Isolator** → `core_directive`  \n3. **Field Mapper** → drafts template fields  \n4. **Boundary Definer** → injects constraints & requirements  \n5. **Template Forgemaster** → outputs `draft_template`  \n6. **Conformance Auditor** → returns `validated_template`\n\nEach step converges on the **single most critical element** of value, ensuring every output is a **syntactically correct, high-precision instruction template** ready for immediate use in any downstream system.",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"expanded_context\": \"The input establishes a meta-instruction for a universal instruction-template generation sequence. It presents several template fragments, each enforcing rigorous syntax and procedural discipline on subsequent sequence steps. The overarching context is to design, engineer, and execute a deterministic pipeline that can take any input and output a syntactically precise, LLM-ready instruction template. The provided instructions specify multiple specialized roles, each responsible for expanding, decomposing, atomizing, mapping, scaffolding, boundary encoding, synthesizing, and validating instructions within a strict process. The goal is not to solve a given input or deliver solutions, but to maximize the explicitness and executability of downstream instructions, guaranteeing structural, operational, and syntactic integrity throughout the transformation chain. Domain-agnostic procedural language is emphasized, and subjective or conversational elements must be eliminated. The sequence must be general enough to handle any input, adhere to canonical formats, and support requirement enforcement, validation, and auditability across every step.\",\n  \"goal_map\": [\n    \"Expand the contextual boundaries of the received instruction sequence.\",\n    \"Eliminate all first-person or subjective references from instruction steps.\",\n    \"Explicitly identify all requests, asks, or directives present in the sequence.\",\n    \"Surface and articulate all hidden dependencies, preconditions, and latent requirements.\",\n    \"Enumerate all sub-goals and procedural steps called for by each instruction.\",\n    \"Reveal any known or potential execution blockers or ambiguities inherent in the process.\",\n    \"Extract and list all distinct functional components referenced or implied.\",\n    \"Convert all declarative or ambiguous statements into unambiguous imperatives.\",\n    \"Maintain a strict procedural structure, preserving the intended sequence of operations.\",\n    \"Preserve all technical terminology as used in the domain-neutral instruction set.\",\n    \"Synthesize and list operational directives as clear, actionable commands.\",\n    \"Construct a goal map representing the hierarchical and sequential relationships between objectives.\",\n    \"Generate a functional instruction map that decomposes high-level roles into granular action steps.\"\n  ],\n  \"functional_instruction_map\": {\n    \"expand_contextual_boundaries\": \"Broaden the scope of context for the instruction sequence to ensure coverage of all relevant conceptual, procedural, and structural aspects.\",\n    \"strip_first_person_references\": \"Detect and remove all references to first-person language or subjectivity within the input.\",\n    \"identify_explicit_requests\": \"Locate and catalog all explicit directives, requests, or commands found within each input block.\",\n    \"surface_hidden_assumptions\": \"Analyze and expose implicit requirements, assumptions, or dependencies that may impact the execution of the instructions.\",\n    \"enumerate_sub_goals\": \"Break down each high-level goal or directive into constituent sub-goals, ensuring complete task decomposition.\",\n    \"reveal_execution_blockers\": \"Identify and list any obstacles, ambiguities, or blockers that could impair execution fidelity.\",\n    \"extract_functional_components\": \"Isolate and define all distinct logical, structural, or operational units referenced or implied.\",\n    \"convert_declaratives_to_imperatives\": \"Transform all descriptive or declarative language into direct, imperative statements that mandate specific actions.\",\n    \"maintain_procedural_structure\": \"Enforce the intended order of operations and procedural progression throughout the decomposition.\",\n    \"preserve_technical_terminology\": \"Retain all domain-neutral technical terms and phrases as originally specified.\",\n    \"synthesize_operational_directives\": \"Aggregate all actionable statements into a list of explicit operational commands.\",\n    \"construct_goal_map\": \"Design a mapping of primary, secondary, and supporting goals in hierarchical/sequential arrangement.\",\n    \"generate_functional_instruction_map\": \"Produce a detailed mapping from abstract role or step to precise, ordered functionally-defined directives.\"\n  },\n  \"operational_directives\": [\n    \"Expand the boundaries of the instruction sequence to encompass all possible contextual variables.\",\n    \"Remove all traces of first-person or subjective references from instruction content.\",\n    \"Document every explicit request, directive, or command present in the input sequence.\",\n    \"Surface all hidden or implicit dependencies, preconditions, or linking requirements.\",\n    \"Break down each instruction into a complete inventory of sub-goals and discrete procedural steps.\",\n    \"Identify, catalog, and prioritize any execution or interpretation blockers.\",\n    \"Isolate and define every functional or modular component referenced in the instructions.\",\n    \"Rewrite all statements as direct, imperative commands with zero ambiguity.\",\n    \"Arrange all actions and steps according to the intended procedural sequence.\",\n    \"Maximize the retention of discipline-specific technical terminology.\",\n    \"Aggregate the actionable operational steps as a detailed directive list.\",\n    \"Render an explicit goal map that links hierarchical and sequential objectives.\",\n    \"Develop a granular, functional instruction map from each role to atomic command steps.\"\n  ],\n  \"execution_dependencies\": [\n    \"Availability of domain-agnostic, technical language models for context expansion.\",\n    \"Access to standardized procedure for removing subjective or first-person references.\",\n    \"Reliable mechanism for extraction and cataloging of explicit requests and directives.\",\n    \"Framework for analysis and surfacing of implicit assumptions or dependencies.\",\n    \"Methodology for decomposing high-level steps into granular sub-goals.\",\n    \"Protocol for detection and documentation of execution blockers and ambiguities.\",\n    \"Schema for identification and definition of all functional and modular components.\",\n    \"Systematic approach for transforming declarative statements to imperatives.\",\n    \"Enforcement engine for maintaining operation sequence and procedural discipline.\",\n    \"Glossary or mapping for consistent preservation of technical terminology.\",\n    \"Aggregation logic for synthesis of operational directives into actionable commands.\",\n    \"Mapping protocol for constructing goal hierarchies and functional instruction diagrams.\",\n    \"Validation mechanism to ensure the decomposed instruction set is complete and free of residual ambiguities.\"\n  ]\n}",
        }
      }
    }