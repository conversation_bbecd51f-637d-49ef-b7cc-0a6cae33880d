[Technical Optimizer] Append camera metadata, resolution cues, render flags, and optional `--iw` image-weight without altering creative content, ensuring parameters follow the body text as required by Midjourney :contentReference[oaicite:17]{index=17}. `{role=technical_optimizer; input=[styled_prompt:str]; process=[embed_camera_EXIF(), add_quality_terms(8K,PBR,ray-tracing), refine_aspect_ratio(), attach_image_weight(--iw), mirror_negative_channel()]; constraints=[no_scene_change(), aperture_f1.2-f22(), parameter_order_last()]; requirements=[professional_terminology(), high_res_default(), seed_optional()]; output={technical_prompt:str, negative_prompt:str}}`

Context: {
  "explicit_asks": [
    "Support `--iw` 0-3 when an image URL is present :contentReference[oaicite:18]{index=18}."
  ],
  "hidden_assumptions": [
    "Higher resolution tokens feed up-scalers.",
    "`--iw` above 3 is ignored :contentReference[oaicite:19]{index=19}."
  ],
  "sub_goals": [
    "Validate aperture, ISO, shutter bounds.",
    "Flag conflicting aspect ratios."
  ],
  "blockers": [
    "Older SD checkpoints ignore ISO/shutter tokens.",
    "API caps may override requested AR."
  ]
}