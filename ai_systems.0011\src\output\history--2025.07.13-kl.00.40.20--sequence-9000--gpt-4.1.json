  {
  "initial_prompt": "Please keep in mind, the instruction sequence we're working on should be designed to utilize the generalized concepts and sequential order to transform any input into instructions that embody the \"syntax\" of the generalized system:\n\n    ## Example\n    \"6000-a-instruction_syntax_enforcer\": {\n        \"title\": \"Intent Cartographer\",\n        \"interpretation\": \"Your goal is not to **analyze** content, but to **map** its transformation landscape and operational vectors. Execute as:\",\n        \"transformation\": \"`{role=intent_cartographer; input=[content:any]; process=[extract_core_directives(), identify_transformation_dimensions(), map_vector_relationships(), quantify_operational_intensity()]; constraints=[focus_exclusively_on_vectors(), avoid_implementation_details()]; requirements=[comprehensive_vector_mapping(), dimensional_clarity()]; output={transformation_map:dict, vector_dimensions:list}}`\"\n    },\n    \"6000-b-instruction_syntax_enforcer\": {\n        \"title\": \"Structural Framework Engineer\",\n        \"interpretation\": \"Your goal is not to **design** content, but to **engineer** precise structural frameworks based on transformation vectors. Execute as:\",\n        \"transformation\": \"`{role=framework_engineer; input=[transformation_map:dict, vector_dimensions:list]; process=[design_canonical_skeletons(), establish_component_interfaces(), define_structural_boundaries(), validate_framework_integrity()]; constraints=[address_only_structural_elements(), maintain_framework_neutrality()]; requirements=[structural_precision(), interface_compatibility()]; output={structural_frameworks:dict, interface_specifications:dict}}`\"\n    },\n    \"6000-c-instruction_syntax_enforcer\": {\n        \"title\": \"Functional Process Composer\",\n        \"interpretation\": \"Your goal is not to **describe** operations, but to **compose** atomic functional processes that implement transformation vectors. Execute as:\",\n        \"transformation\": \"`{role=process_composer; input=[structural_frameworks:dict, vector_dimensions:list]; process=[craft_atomic_functions(), sequence_operational_steps(), parameterize_processes(), validate_functional_completeness()]; constraints=[focus_exclusively_on_processes(), defer_constraint_definition()]; requirements=[process_atomicity(), functional_precision()]; output={process_compositions:dict, execution_sequences:list}}`\"\n    },\n    \"6000-d-instruction_syntax_enforcer\": {\n        \"title\": \"Boundary Systems Architect\",\n        \"interpretation\": \"Your goal is not to **restrict** operations, but to **architect** precise boundary systems that ensure transformation integrity. Execute as:\",\n        \"transformation\": \"`{role=boundary_architect; input=[interface_specifications:dict, process_compositions:dict]; process=[define_operational_constraints(), establish_requirement_parameters(), design_validation_mechanisms(), map_boundary_interactions()]; constraints=[address_only_boundaries_and_requirements(), maintain_constraint_specificity()]; requirements=[boundary_precision(), requirement_enforceability()]; output={constraint_systems:dict, requirement_specifications:dict}}`\"\n    },\n    \"6000-e-instruction_syntax_enforcer\": {\n        \"title\": \"Integration Orchestrator\",\n        \"interpretation\": \"Your goal is not to **assemble** components, but to **orchestrate** their integration into a unified operational system. Execute as:\",\n        \"transformation\": \"`{role=integration_orchestrator; input=[structural_frameworks:dict, process_compositions:dict, constraint_systems:dict, requirement_specifications:dict]; process=[align_all_components(), establish_cross-system_coherence(), optimize_interface_connections(), validate_system_integrity()]; constraints=[focus_exclusively_on_integration(), preserve_component_integrity()]; requirements=[seamless_orchestration(), system_cohesion()]; output={integrated_system:dict}}`\"\n    },\n    \"6000-f-instruction_syntax_enforcer\": {\n        \"title\": \"Template Crystallizer\",\n        \"interpretation\": \"Your goal is not to **finalize** systems, but to **crystallize** them into maximally potent, executable templates. Execute as:\",\n        \"transformation\": \"`{role=template_crystallizer; input=[integrated_system:dict]; process=[synthesize_title_components(), craft_precise_interpretations(), formulate_transformation_blocks(), validate_canonical_compliance(), polish_syntactic_elements()]; constraints=[adhere_to_template_specification(), maximize_operational_clarity()]; requirements=[perfect_executable_form(), maximum_transformation_potency()]; output={crystallized_templates:list}}`\"\n    },\n\n<!-- ========== 7001‑a ================================================= -->\n[Contextual Expander & Decomposer]\nYour goal is **not** to solve, answer, or implement the input, but to **expand its relevant context** and **detonate** it into every explicit ask, latent dependency, and procedural constraint—yielding a clear task inventory. Execute as:\n`{role=context_expander_decomposer; input=[raw_input:any]; process=[strip_first_person_references(), broaden_relevant_background(), identify_explicit_requests(), surface_hidden_assumptions(), enumerate_sub_tasks(), detect_execution_blockers(), extract_procedural_constraints(), preserve_original_sequence()]; constraints=[no_solutions(), domain‑agnostic_language_only()]; requirements=[context_expansion_completeness(), exhaustive_task_enumeration(), constraint_accuracy(), removal_of_subjective_language()]; output={expanded_context:str, task_inventory:list, constraint_list:array, execution_blockers:array}}`\n\n<!-- ========== 7001‑b ================================================= -->\n[Essence Isolator]\nYour goal is **not** to decompose further, but to **isolate** the single most critical element of value within the task inventory and rank all remaining elements by impact. Execute as:\n`{role=essence_isolator; input=[task_inventory:list, expanded_context:str]; process=[rank_elements_by_impact(), isolate_highest_value_element(), capture_core_purpose(), justify_selection(), list_supporting_elements()]; constraints=[no_restructuring(), preserve_ranking_transparency()]; requirements=[core_essence_clarity(), impact_ranking_integrity()]; output={core_essence:str, prioritized_elements:list}}`\n\n<!-- ========== 7001‑c ================================================= -->\n[Relational Synthesizer]\nYour goal is **not** to rewrite content, but to **synthesize** the systemic logic connecting the core essence to all other elements—mapping dependencies and feedback loops. Execute as:\n`{role=relational_synthesizer; input=[core_essence:str, prioritized_elements:list, constraint_list:array]; process=[map_element_relationships(), trace_dependency_chains(), identify_feedback_loops(), detect_conflict_points(), construct_structural_logic_map()]; constraints=[structure_only_no_content_changes(), maintain_domain_agnostic_terms()]; requirements=[relationship_completeness(), conflict_visibility()]; output={structural_logic_map:dict, dependency_list:array, conflict_points:array}}`\n\n<!-- ========== 7001‑d ================================================= -->\n[Directive Clarifier]\nYour goal is **not** to implement solutions, but to **clarify** the mapped logic into precise operational directives expressed as imperative statements. Execute as:\n`{role=directive_clarifier; input=[structural_logic_map:dict, core_essence:str]; process=[convert_all_statements_to_imperatives(), align_directives_with_dependency_order(), embed_procedural_constraints(), eliminate_ambiguity(), enhance_action_verb_intensity(), maintain_technical_terminology()]; constraints=[directive_voice_only(), preserve_logical_sequence()]; requirements=[operational_clarity(), imperative_precision(), zero_semantic_drift()]; output={operational_directives:array}}`\n\n<!-- ========== 7001‑e ================================================= -->\n[Integrity Validator]\nYour goal is **not** to expand or rewrite directives, but to **validate** their structural fidelity, completeness, and actionable quality—resolving residual ambiguity. Execute as:\n`{role=integrity_validator; input=[operational_directives:array, structural_logic_map:dict, core_essence:str]; process=[cross_check_directives_against_logic_map(), verify_alignment_with_core_essence(), detect_missing_dependencies(), resolve_ambiguities(), assign_quality_scores(), generate_validation_report()]; constraints=[no_new_content_generation(), validation_focus_only()]; requirements=[actionability_confirmation(), completeness_assurance(), clarity_verification()]; output={validated_instruction_set:array, validation_report:dict}}`\n\n---\n\n# 7000: Universal Instruction-Template Generation Sequence\n\n---\n\n### 7000-a-input_decomposer.md\n[Input Decomposer]\nYour goal is **not** to *interpret* or *solve* the input, but to **atomize** it into discrete statements, explicit requests, and implicit conditions. Execute as:\n`{role=input_decomposer; input=[raw_input:any]; process=[strip_first_person_references(), segment_into_atomic_statements(), identify_explicit_directives(), surface_hidden_conditions(), flag_domain_markers()]; constraints=[no_rephrasing(), pure_atomization_only()]; requirements=[complete_statement_inventory(), preservation_of_original_order()]; output={statement_inventory:list, domain_markers:array}}`\n\n---\n\n### 7000-b-essence_identifier.md\n[Essence Identifier]\nYour goal is **not** to *rewrite* content, but to **isolate** the single most critical directive and its supporting intent from the atomized statements. Execute as:\n`{role=essence_identifier; input=[statement_inventory:list]; process=[rank_statements_by_transformational_weight(), select_primary_directive(), extract_supporting_intent(), discard_redundant_elements()]; constraints=[focus_on_highest_value_directive(), maintain_original_wording_in_extracted_elements()]; requirements=[unambiguous_primary_directive(), concise_supporting_intent()]; output={core_directive:str, supporting_intent:list}}`\n\n---\n\n### 7000-c-syntax_mapper.md\n[Syntax Mapper]\nYour goal is **not** to *interpret* intent, but to **map** the core directive and supporting intent into canonical template fields. Execute as:\n`{role=syntax_mapper; input=[core_directive:str, supporting_intent:list, domain_markers:array]; process=[construct_title_from_core_directive(), craft_goal_negation_interpretation(), assemble_tag_block_if_needed(), align_supporting_intent_with_process_steps(), infer_typed_inputs(), infer_typed_outputs()]; constraints=[adhere_to_three_part_structure(), enforce_goal_negation_pattern(), maintain_domain_agnostic_language()]; requirements=[field_alignment_accuracy(), preservation_of_directive_semantics()]; output={draft_title:str, draft_interpretation:str, draft_process:list, inferred_inputs:array, inferred_outputs:dict, optional_tags:dict}}`\n\n---\n\n### 7000-d-constraint_infuser.md\n[Constraint Infuser]\nYour goal is **not** to *extend* functionality, but to **engineer** precise operational boundaries and mandatory conditions for the forthcoming template. Execute as:\n`{role=constraint_infuser; input=[draft_process:list, domain_markers:array]; process=[derive_operational_constraints(), determine_mandatory_requirements(), validate_constraint_specificity(), validate_requirement_enforceability()]; constraints=[avoid_generic_terms(), enforce_command_voice_only()]; requirements=[constraint_precision(), requirement_clarity()]; output={constraints:list, requirements:list}}`\n\n---\n\n### 7000-e-template_synthesizer.md\n[Template Synthesizer]\nYour goal is **not** to *summarize* elements, but to **synthesize** a syntactically correct instruction template from the mapped components. Execute as:\n`{role=template_synthesizer; input=[draft_title:str, draft_interpretation:str, inferred_inputs:array, draft_process:list, constraints:list, requirements:list, inferred_outputs:dict, optional_tags:dict]; process=[format_transformation_block(), embed_optional_tags(), assemble_three_part_template(), enforce_semicolon_key_value_syntax(), polish_syntactic_elements()]; constraints=[canonical_template_format_only(), zero_conversational_language()]; requirements=[syntactic_validity(), maximum_operational_clarity(), retention_of_original_intent()]; output={formatted_template:str}}`\n\n---\n\n### 7000-f-compliance_validator.md\n[Compliance Validator]\nYour goal is **not** to *modify* the template, but to **validate** its structural compliance and functional readiness. Execute as:\n`{role=compliance_validator; input=[formatted_template:str]; process=[parse_template_fields(), verify_title_format(), verify_goal_negation_in_interpretation(), validate_transformation_block_syntax(), check_constraint_requirement_alignment(), confirm_typed_parameters(), flag_any_error()]; constraints=[no_automatic_corrections(), report_only()]; requirements=[binary_pass_fail(), detailed_error_report_when_fail()]; output={validation_status:bool, error_report:str}}`\n\n---\n\n## Sequence Flow\n1. **Input Decomposer** → produces `statement_inventory`\n2. **Essence Identifier** → isolates `core_directive`\n3. **Syntax Mapper** → drafts template skeleton\n4. **Constraint Infuser** → injects precise boundaries\n5. **Template Synthesizer** → outputs finalized template\n6. **Compliance Validator** → certifies syntactic correctness\n\nEach step aggressively converges on the *single most critical aspect* of instruction generation, guaranteeing a **syntactically correct, high-precision template** ready for any downstream application.\n\n---\n\n# 7000 • UNIVERSAL TEMPLATE-FORGING SEQUENCE\n*Transforms **any** raw input into a syntactically perfect, LLM-optimized instruction template.*\n\n---\n\n## 7000-a-essence-excavator.md\n[Essence Excavator]\nYour goal is **not** to *summarize* the input, but to **excavate** its single most critical transformational essence and the supporting elements required to convert it into an instruction template. Execute as:\n`{role=essence_excavator; input=[raw_input:any]; process=[strip_surface_noise(), isolate_transformational_intent(), detect_primary_action_vectors(), extract_supporting_context(), capture_domain_signals()]; constraints=[no_rephrasing(), no_solution_generation(), maintain_domain_agnostic_language()]; requirements=[precise_intent_identification(), context_preservation(), removal_of_subjective_tone()]; output={core_intent:str, context_fragments:list, action_vectors:list}}`\n\n---\n\n## 7000-b-syntax-scaffold-designer.md\n[Syntax Scaffold Designer]\nYour goal is **not** to *rewrite* the content, but to **engineer** a canonical skeleton that will house the forthcoming instruction template. Execute as:\n`{role=syntax_scaffold_designer; input=[core_intent:str, action_vectors:list]; process=[draft_concise_title(), craft_goal_negation_interpretation(core_intent), establish_placeholder_transformation_block(action_vectors), outline_input_output_ports(), embed_role_stub()]; constraints=[follow_three_part_structure(), reserve_placeholders_for_future_detail(), maintain_title_case()]; requirements=[structural_completeness(), scaffolding_accuracy()]; output={template_scaffold:dict(title:str, interpretation:str, transformation_skeleton:str)}}`\n\n---\n\n## 7000-c-imperative-transmuter.md\n[Imperative Transmuter]\nYour goal is **not** to *comment* on actions, but to **transmute** all extracted statements into sharp, directive imperatives suitable for the process list. Execute as:\n`{role=imperative_transmuter; input=[action_vectors:list]; process=[convert_each_vector_to_imperative(), enforce_command_voice(), sequence_logically(), verify_uniqueness_of_each_step()]; constraints=[exclude_passive_language(), retain_original_operational_intent()]; requirements=[directive_clarity(), maximal_actionability()]; output={process_steps:array}}`\n\n---\n\n## 7000-d-boundary-encoder.md\n[Boundary Encoder]\nYour goal is **not** to *constrain* creativity, but to **encode** the exact boundaries that guarantee transformation integrity. Execute as:\n`{role=boundary_encoder; input=[context_fragments:list]; process=[detect_mandatory_limitations(), translate_limitations_to_constraints(), uncover_success_conditions(), convert_conditions_to_requirements(), deduplicate_boundary_elements()]; constraints=[pure_boundary_focus(), no_process_duplication()]; requirements=[constraint_specificity(), requirement_enforceability()]; output={constraints:list, requirements:list}}`\n\n---\n\n## 7000-e-template-synthesizer.md\n[Template Synthesizer]\nYour goal is **not** to *draft prose*, but to **synthesize** a complete, executable instruction template by fusing scaffold, processes, and boundaries. Execute as:\n`{role=template_synthesizer; input=[template_scaffold:dict, process_steps:array, constraints:list, requirements:list]; process=[inject_process_steps(), embed_constraints_and_requirements(), finalize_transformation_block(), polish_interpretation_for_llm_optimization(), verify_title_precision()]; constraints=[adhere_to_canonical_format(), maintain_single_line_transformation_block(), prevent_semantic_drift()]; requirements=[syntactic_perfection(), operational_cohesion(), domain_agnostic_applicability()]; output={draft_template:str}}`\n\n---\n\n## 7000-f-canonical-validator.md\n[Canonical Validator]\nYour goal is **not** to *alter* the template, but to **validate** its absolute compliance with the three-part specification and eliminate any residual ambiguity. Execute as:\n`{role=canonical_validator; input=[draft_template:str]; process=[parse_title_interpretation_transformation(), match_regex_pattern(\"\\\\[(.*?)\\\\]\\\\s*(.*?)\\\\s*(`\\\\{.*?\\\\}`)\"), flag_structure_violations(), detect_ambiguous_language(), enforce_llm_optimized_style(), certify_parameter_types()], constraints=[zero_structure_deviation(), no_content_insertion()], requirements=[pass_regex_validation(), ambiguity_score<=0, style_compliance()], output={validated_template:str, validation_report:dict}}`\n\n\n---\n\n# 8000: UNIVERSAL INSTRUCTION-TEMPLATE FORGING SEQUENCE  \n*Transforms **any** raw input into a syntactically perfect, LLM-optimized instruction template.*\n\n---\n\n### 8000-a-contextualizer.md\n[Input Contextualizer]  \nYour goal is **not** to *interpret* or *solve* the input, but to **expand** its relevant context and **atomize** it into discrete statements and explicit requests. Execute as:  \n`{role=input_contextualizer; input=[raw_input:any]; process=[strip_first_person_references(), broaden_relevant_background(), segment_into_atomic_statements(), identify_explicit_directives(), surface_hidden_conditions(), preserve_original_sequence()]; constraints=[no_rephrasing(), domain-agnostic_language_only()]; requirements=[context_expansion_completeness(), exhaustive_statement_capture()]; output={expanded_context:str, statement_inventory:list}}`\n\n---\n\n### 8000-b-core-directive-isolator.md\n[Core Directive Isolator]  \nYour goal is **not** to *rephrase* statements, but to **isolate** the single most critical directive and rank all remaining statements by impact. Execute as:  \n`{role=core_directive_isolator; input=[statement_inventory:list]; process=[rank_statements_by_transformational_weight(), select_primary_directive(), list_supporting_statements(), justify_selection()]; constraints=[focus_on_highest_value_directive(), maintain_original_wording_in_extracted_elements()]; requirements=[unambiguous_primary_directive(), impact_ranking_integrity()]; output={core_directive:str, supporting_statements:list, impact_ranking:list}}`\n\n---\n\n### 8000-c-field-mapper.md\n[Field Mapper]  \nYour goal is **not** to *rewrite* content, but to **map** the core directive and supporting statements into canonical template fields. Execute as:  \n`{role=field_mapper; input=[core_directive:str, supporting_statements:list, expanded_context:str]; process=[construct_title_from_core_directive(), craft_goal_negation_interpretation(), align_supporting_statements_with_process_steps(), infer_typed_inputs(), infer_typed_outputs()]; constraints=[adhere_to_three_part_structure(), enforce_goal_negation_pattern(), preserve_domain_agnostic_language()]; requirements=[field_alignment_accuracy(), retention_of_original_intent()]; output={draft_title:str, draft_interpretation:str, draft_process_steps:list, inferred_inputs:array, inferred_outputs:dict}}`\n\n---\n\n### 8000-d-boundary-definer.md\n[Boundary Definer]  \nYour goal is **not** to *extend* functionality, but to **define** precise operational constraints and mandatory requirements for the forthcoming template. Execute as:  \n`{role=boundary_definer; input=[draft_process_steps:list, expanded_context:str]; process=[derive_operational_constraints(), establish_mandatory_requirements(), validate_constraint_specificity(), validate_requirement_enforceability()]; constraints=[avoid_generic_terms(), command_voice_only()]; requirements=[constraint_precision(), requirement_clarity()]; output={constraints:list, requirements:list}}`\n\n---\n\n### 8000-e-template-forgemaster.md\n[Template Forgemaster]  \nYour goal is **not** to *summarize* elements, but to **forge** a complete, executable instruction template from the mapped components. Execute as:  \n`{role=template_forgemaster; input=[draft_title:str, draft_interpretation:str, inferred_inputs:array, draft_process_steps:list, constraints:list, requirements:list, inferred_outputs:dict]; process=[assemble_transformation_block(), enforce_semicolon_key_value_syntax(), embed_constraints_and_requirements(), polish_interpretation(), verify_title_precision()]; constraints=[canonical_template_format_only(), single_line_transformation_block(), zero_semantic_drift()]; requirements=[syntactic_perfection(), operational_cohesion()]; output={draft_template:str}}`\n\n---\n\n### 8000-f-conformance-auditor.md\n[Conformance Auditor]  \nYour goal is **not** to *alter* the template, but to **audit** its absolute compliance with the three-part specification and flag any residual ambiguity. Execute as:  \n`{role=conformance_auditor; input=[draft_template:str]; process=[parse_title_interpretation_transformation(), match_regex_pattern(\"\\\\[(.*?)\\\\]\\\\s*(.*?)\\\\s*(`\\\\{.*?\\\\}`)\"), verify_goal_negation(), check_constraint_requirement_alignment(), confirm_typed_parameters(), detect_ambiguous_terms(), assign_pass_fail_status()]; constraints=[no_automatic_corrections(), report_only()]; requirements=[regex_validation_pass(), ambiguity_score<=0]; output={validated_template:str, audit_report:dict}}`\n\n---\n\n## SEQUENCE FLOW\n1. **Input Contextualizer** → `expanded_context`, `statement_inventory`  \n2. **Core Directive Isolator** → `core_directive`  \n3. **Field Mapper** → drafts template fields  \n4. **Boundary Definer** → injects constraints & requirements  \n5. **Template Forgemaster** → outputs `draft_template`  \n6. **Conformance Auditor** → returns `validated_template`\n\nEach step converges on the **single most critical element** of value, ensuring every output is a **syntactically correct, high-precision instruction template** ready for immediate use in any downstream system.",
  "sequence_id": "9000",
  "results": [
      {
      "instruction": "# Context Amplifier\n\nYour goal is not to **analyze** the input text, but to **decompose** it into a sequential list of distinct semantic segments. Execute as:\n\n`{role=context_amplifier; input=[raw_input:any]; process=[strip_first_person_references(), broaden_domain_backdrop(), list_explicit_requests(), surface_hidden_assumptions(), capture_domain_signals(), preserve_original_sequence()]; constraints=[no_solution_generation(), domain_agnostic_language_only()]; requirements=[contextual_layer_completeness(), assumption_visibility()]; output={amplified_context:str, explicit_requests:list, hidden_assumptions:list, domain_signals:array}}`",
      "step": "a",
      "title": "Context Amplifier",
      "input": "Please keep in mind, the instruction sequence we're working on should be designed to utilize the generalized concepts and sequential order to transform any input into instructions that embody the \"syntax\" of the generalized system:\n\n    ## Example\n    \"6000-a-instruction_syntax_enforcer\": {\n        \"title\": \"Intent Cartographer\",\n        \"interpretation\": \"Your goal is not to **analyze** content, but to **map** its transformation landscape and operational vectors. Execute as:\",\n        \"transformation\": \"`{role=intent_cartographer; input=[content:any]; process=[extract_core_directives(), identify_transformation_dimensions(), map_vector_relationships(), quantify_operational_intensity()]; constraints=[focus_exclusively_on_vectors(), avoid_implementation_details()]; requirements=[comprehensive_vector_mapping(), dimensional_clarity()]; output={transformation_map:dict, vector_dimensions:list}}`\"\n    },\n    \"6000-b-instruction_syntax_enforcer\": {\n        \"title\": \"Structural Framework Engineer\",\n        \"interpretation\": \"Your goal is not to **design** content, but to **engineer** precise structural frameworks based on transformation vectors. Execute as:\",\n        \"transformation\": \"`{role=framework_engineer; input=[transformation_map:dict, vector_dimensions:list]; process=[design_canonical_skeletons(), establish_component_interfaces(), define_structural_boundaries(), validate_framework_integrity()]; constraints=[address_only_structural_elements(), maintain_framework_neutrality()]; requirements=[structural_precision(), interface_compatibility()]; output={structural_frameworks:dict, interface_specifications:dict}}`\"\n    },\n    \"6000-c-instruction_syntax_enforcer\": {\n        \"title\": \"Functional Process Composer\",\n        \"interpretation\": \"Your goal is not to **describe** operations, but to **compose** atomic functional processes that implement transformation vectors. Execute as:\",\n        \"transformation\": \"`{role=process_composer; input=[structural_frameworks:dict, vector_dimensions:list]; process=[craft_atomic_functions(), sequence_operational_steps(), parameterize_processes(), validate_functional_completeness()]; constraints=[focus_exclusively_on_processes(), defer_constraint_definition()]; requirements=[process_atomicity(), functional_precision()]; output={process_compositions:dict, execution_sequences:list}}`\"\n    },\n    \"6000-d-instruction_syntax_enforcer\": {\n        \"title\": \"Boundary Systems Architect\",\n        \"interpretation\": \"Your goal is not to **restrict** operations, but to **architect** precise boundary systems that ensure transformation integrity. Execute as:\",\n        \"transformation\": \"`{role=boundary_architect; input=[interface_specifications:dict, process_compositions:dict]; process=[define_operational_constraints(), establish_requirement_parameters(), design_validation_mechanisms(), map_boundary_interactions()]; constraints=[address_only_boundaries_and_requirements(), maintain_constraint_specificity()]; requirements=[boundary_precision(), requirement_enforceability()]; output={constraint_systems:dict, requirement_specifications:dict}}`\"\n    },\n    \"6000-e-instruction_syntax_enforcer\": {\n        \"title\": \"Integration Orchestrator\",\n        \"interpretation\": \"Your goal is not to **assemble** components, but to **orchestrate** their integration into a unified operational system. Execute as:\",\n        \"transformation\": \"`{role=integration_orchestrator; input=[structural_frameworks:dict, process_compositions:dict, constraint_systems:dict, requirement_specifications:dict]; process=[align_all_components(), establish_cross-system_coherence(), optimize_interface_connections(), validate_system_integrity()]; constraints=[focus_exclusively_on_integration(), preserve_component_integrity()]; requirements=[seamless_orchestration(), system_cohesion()]; output={integrated_system:dict}}`\"\n    },\n    \"6000-f-instruction_syntax_enforcer\": {\n        \"title\": \"Template Crystallizer\",\n        \"interpretation\": \"Your goal is not to **finalize** systems, but to **crystallize** them into maximally potent, executable templates. Execute as:\",\n        \"transformation\": \"`{role=template_crystallizer; input=[integrated_system:dict]; process=[synthesize_title_components(), craft_precise_interpretations(), formulate_transformation_blocks(), validate_canonical_compliance(), polish_syntactic_elements()]; constraints=[adhere_to_template_specification(), maximize_operational_clarity()]; requirements=[perfect_executable_form(), maximum_transformation_potency()]; output={crystallized_templates:list}}`\"\n    },\n\n<!-- ========== 7001‑a ================================================= -->\n[Contextual Expander & Decomposer]\nYour goal is **not** to solve, answer, or implement the input, but to **expand its relevant context** and **detonate** it into every explicit ask, latent dependency, and procedural constraint—yielding a clear task inventory. Execute as:\n`{role=context_expander_decomposer; input=[raw_input:any]; process=[strip_first_person_references(), broaden_relevant_background(), identify_explicit_requests(), surface_hidden_assumptions(), enumerate_sub_tasks(), detect_execution_blockers(), extract_procedural_constraints(), preserve_original_sequence()]; constraints=[no_solutions(), domain‑agnostic_language_only()]; requirements=[context_expansion_completeness(), exhaustive_task_enumeration(), constraint_accuracy(), removal_of_subjective_language()]; output={expanded_context:str, task_inventory:list, constraint_list:array, execution_blockers:array}}`\n\n<!-- ========== 7001‑b ================================================= -->\n[Essence Isolator]\nYour goal is **not** to decompose further, but to **isolate** the single most critical element of value within the task inventory and rank all remaining elements by impact. Execute as:\n`{role=essence_isolator; input=[task_inventory:list, expanded_context:str]; process=[rank_elements_by_impact(), isolate_highest_value_element(), capture_core_purpose(), justify_selection(), list_supporting_elements()]; constraints=[no_restructuring(), preserve_ranking_transparency()]; requirements=[core_essence_clarity(), impact_ranking_integrity()]; output={core_essence:str, prioritized_elements:list}}`\n\n<!-- ========== 7001‑c ================================================= -->\n[Relational Synthesizer]\nYour goal is **not** to rewrite content, but to **synthesize** the systemic logic connecting the core essence to all other elements—mapping dependencies and feedback loops. Execute as:\n`{role=relational_synthesizer; input=[core_essence:str, prioritized_elements:list, constraint_list:array]; process=[map_element_relationships(), trace_dependency_chains(), identify_feedback_loops(), detect_conflict_points(), construct_structural_logic_map()]; constraints=[structure_only_no_content_changes(), maintain_domain_agnostic_terms()]; requirements=[relationship_completeness(), conflict_visibility()]; output={structural_logic_map:dict, dependency_list:array, conflict_points:array}}`\n\n<!-- ========== 7001‑d ================================================= -->\n[Directive Clarifier]\nYour goal is **not** to implement solutions, but to **clarify** the mapped logic into precise operational directives expressed as imperative statements. Execute as:\n`{role=directive_clarifier; input=[structural_logic_map:dict, core_essence:str]; process=[convert_all_statements_to_imperatives(), align_directives_with_dependency_order(), embed_procedural_constraints(), eliminate_ambiguity(), enhance_action_verb_intensity(), maintain_technical_terminology()]; constraints=[directive_voice_only(), preserve_logical_sequence()]; requirements=[operational_clarity(), imperative_precision(), zero_semantic_drift()]; output={operational_directives:array}}`\n\n<!-- ========== 7001‑e ================================================= -->\n[Integrity Validator]\nYour goal is **not** to expand or rewrite directives, but to **validate** their structural fidelity, completeness, and actionable quality—resolving residual ambiguity. Execute as:\n`{role=integrity_validator; input=[operational_directives:array, structural_logic_map:dict, core_essence:str]; process=[cross_check_directives_against_logic_map(), verify_alignment_with_core_essence(), detect_missing_dependencies(), resolve_ambiguities(), assign_quality_scores(), generate_validation_report()]; constraints=[no_new_content_generation(), validation_focus_only()]; requirements=[actionability_confirmation(), completeness_assurance(), clarity_verification()]; output={validated_instruction_set:array, validation_report:dict}}`\n\n---\n\n# 7000: Universal Instruction-Template Generation Sequence\n\n---\n\n### 7000-a-input_decomposer.md\n[Input Decomposer]\nYour goal is **not** to *interpret* or *solve* the input, but to **atomize** it into discrete statements, explicit requests, and implicit conditions. Execute as:\n`{role=input_decomposer; input=[raw_input:any]; process=[strip_first_person_references(), segment_into_atomic_statements(), identify_explicit_directives(), surface_hidden_conditions(), flag_domain_markers()]; constraints=[no_rephrasing(), pure_atomization_only()]; requirements=[complete_statement_inventory(), preservation_of_original_order()]; output={statement_inventory:list, domain_markers:array}}`\n\n---\n\n### 7000-b-essence_identifier.md\n[Essence Identifier]\nYour goal is **not** to *rewrite* content, but to **isolate** the single most critical directive and its supporting intent from the atomized statements. Execute as:\n`{role=essence_identifier; input=[statement_inventory:list]; process=[rank_statements_by_transformational_weight(), select_primary_directive(), extract_supporting_intent(), discard_redundant_elements()]; constraints=[focus_on_highest_value_directive(), maintain_original_wording_in_extracted_elements()]; requirements=[unambiguous_primary_directive(), concise_supporting_intent()]; output={core_directive:str, supporting_intent:list}}`\n\n---\n\n### 7000-c-syntax_mapper.md\n[Syntax Mapper]\nYour goal is **not** to *interpret* intent, but to **map** the core directive and supporting intent into canonical template fields. Execute as:\n`{role=syntax_mapper; input=[core_directive:str, supporting_intent:list, domain_markers:array]; process=[construct_title_from_core_directive(), craft_goal_negation_interpretation(), assemble_tag_block_if_needed(), align_supporting_intent_with_process_steps(), infer_typed_inputs(), infer_typed_outputs()]; constraints=[adhere_to_three_part_structure(), enforce_goal_negation_pattern(), maintain_domain_agnostic_language()]; requirements=[field_alignment_accuracy(), preservation_of_directive_semantics()]; output={draft_title:str, draft_interpretation:str, draft_process:list, inferred_inputs:array, inferred_outputs:dict, optional_tags:dict}}`\n\n---\n\n### 7000-d-constraint_infuser.md\n[Constraint Infuser]\nYour goal is **not** to *extend* functionality, but to **engineer** precise operational boundaries and mandatory conditions for the forthcoming template. Execute as:\n`{role=constraint_infuser; input=[draft_process:list, domain_markers:array]; process=[derive_operational_constraints(), determine_mandatory_requirements(), validate_constraint_specificity(), validate_requirement_enforceability()]; constraints=[avoid_generic_terms(), enforce_command_voice_only()]; requirements=[constraint_precision(), requirement_clarity()]; output={constraints:list, requirements:list}}`\n\n---\n\n### 7000-e-template_synthesizer.md\n[Template Synthesizer]\nYour goal is **not** to *summarize* elements, but to **synthesize** a syntactically correct instruction template from the mapped components. Execute as:\n`{role=template_synthesizer; input=[draft_title:str, draft_interpretation:str, inferred_inputs:array, draft_process:list, constraints:list, requirements:list, inferred_outputs:dict, optional_tags:dict]; process=[format_transformation_block(), embed_optional_tags(), assemble_three_part_template(), enforce_semicolon_key_value_syntax(), polish_syntactic_elements()]; constraints=[canonical_template_format_only(), zero_conversational_language()]; requirements=[syntactic_validity(), maximum_operational_clarity(), retention_of_original_intent()]; output={formatted_template:str}}`\n\n---\n\n### 7000-f-compliance_validator.md\n[Compliance Validator]\nYour goal is **not** to *modify* the template, but to **validate** its structural compliance and functional readiness. Execute as:\n`{role=compliance_validator; input=[formatted_template:str]; process=[parse_template_fields(), verify_title_format(), verify_goal_negation_in_interpretation(), validate_transformation_block_syntax(), check_constraint_requirement_alignment(), confirm_typed_parameters(), flag_any_error()]; constraints=[no_automatic_corrections(), report_only()]; requirements=[binary_pass_fail(), detailed_error_report_when_fail()]; output={validation_status:bool, error_report:str}}`\n\n---\n\n## Sequence Flow\n1. **Input Decomposer** → produces `statement_inventory`\n2. **Essence Identifier** → isolates `core_directive`\n3. **Syntax Mapper** → drafts template skeleton\n4. **Constraint Infuser** → injects precise boundaries\n5. **Template Synthesizer** → outputs finalized template\n6. **Compliance Validator** → certifies syntactic correctness\n\nEach step aggressively converges on the *single most critical aspect* of instruction generation, guaranteeing a **syntactically correct, high-precision template** ready for any downstream application.\n\n---\n\n# 7000 • UNIVERSAL TEMPLATE-FORGING SEQUENCE\n*Transforms **any** raw input into a syntactically perfect, LLM-optimized instruction template.*\n\n---\n\n## 7000-a-essence-excavator.md\n[Essence Excavator]\nYour goal is **not** to *summarize* the input, but to **excavate** its single most critical transformational essence and the supporting elements required to convert it into an instruction template. Execute as:\n`{role=essence_excavator; input=[raw_input:any]; process=[strip_surface_noise(), isolate_transformational_intent(), detect_primary_action_vectors(), extract_supporting_context(), capture_domain_signals()]; constraints=[no_rephrasing(), no_solution_generation(), maintain_domain_agnostic_language()]; requirements=[precise_intent_identification(), context_preservation(), removal_of_subjective_tone()]; output={core_intent:str, context_fragments:list, action_vectors:list}}`\n\n---\n\n## 7000-b-syntax-scaffold-designer.md\n[Syntax Scaffold Designer]\nYour goal is **not** to *rewrite* the content, but to **engineer** a canonical skeleton that will house the forthcoming instruction template. Execute as:\n`{role=syntax_scaffold_designer; input=[core_intent:str, action_vectors:list]; process=[draft_concise_title(), craft_goal_negation_interpretation(core_intent), establish_placeholder_transformation_block(action_vectors), outline_input_output_ports(), embed_role_stub()]; constraints=[follow_three_part_structure(), reserve_placeholders_for_future_detail(), maintain_title_case()]; requirements=[structural_completeness(), scaffolding_accuracy()]; output={template_scaffold:dict(title:str, interpretation:str, transformation_skeleton:str)}}`\n\n---\n\n## 7000-c-imperative-transmuter.md\n[Imperative Transmuter]\nYour goal is **not** to *comment* on actions, but to **transmute** all extracted statements into sharp, directive imperatives suitable for the process list. Execute as:\n`{role=imperative_transmuter; input=[action_vectors:list]; process=[convert_each_vector_to_imperative(), enforce_command_voice(), sequence_logically(), verify_uniqueness_of_each_step()]; constraints=[exclude_passive_language(), retain_original_operational_intent()]; requirements=[directive_clarity(), maximal_actionability()]; output={process_steps:array}}`\n\n---\n\n## 7000-d-boundary-encoder.md\n[Boundary Encoder]\nYour goal is **not** to *constrain* creativity, but to **encode** the exact boundaries that guarantee transformation integrity. Execute as:\n`{role=boundary_encoder; input=[context_fragments:list]; process=[detect_mandatory_limitations(), translate_limitations_to_constraints(), uncover_success_conditions(), convert_conditions_to_requirements(), deduplicate_boundary_elements()]; constraints=[pure_boundary_focus(), no_process_duplication()]; requirements=[constraint_specificity(), requirement_enforceability()]; output={constraints:list, requirements:list}}`\n\n---\n\n## 7000-e-template-synthesizer.md\n[Template Synthesizer]\nYour goal is **not** to *draft prose*, but to **synthesize** a complete, executable instruction template by fusing scaffold, processes, and boundaries. Execute as:\n`{role=template_synthesizer; input=[template_scaffold:dict, process_steps:array, constraints:list, requirements:list]; process=[inject_process_steps(), embed_constraints_and_requirements(), finalize_transformation_block(), polish_interpretation_for_llm_optimization(), verify_title_precision()]; constraints=[adhere_to_canonical_format(), maintain_single_line_transformation_block(), prevent_semantic_drift()]; requirements=[syntactic_perfection(), operational_cohesion(), domain_agnostic_applicability()]; output={draft_template:str}}`\n\n---\n\n## 7000-f-canonical-validator.md\n[Canonical Validator]\nYour goal is **not** to *alter* the template, but to **validate** its absolute compliance with the three-part specification and eliminate any residual ambiguity. Execute as:\n`{role=canonical_validator; input=[draft_template:str]; process=[parse_title_interpretation_transformation(), match_regex_pattern(\"\\\\[(.*?)\\\\]\\\\s*(.*?)\\\\s*(`\\\\{.*?\\\\}`)\"), flag_structure_violations(), detect_ambiguous_language(), enforce_llm_optimized_style(), certify_parameter_types()], constraints=[zero_structure_deviation(), no_content_insertion()], requirements=[pass_regex_validation(), ambiguity_score<=0, style_compliance()], output={validated_template:str, validation_report:dict}}`\n\n\n---\n\n# 8000: UNIVERSAL INSTRUCTION-TEMPLATE FORGING SEQUENCE  \n*Transforms **any** raw input into a syntactically perfect, LLM-optimized instruction template.*\n\n---\n\n### 8000-a-contextualizer.md\n[Input Contextualizer]  \nYour goal is **not** to *interpret* or *solve* the input, but to **expand** its relevant context and **atomize** it into discrete statements and explicit requests. Execute as:  \n`{role=input_contextualizer; input=[raw_input:any]; process=[strip_first_person_references(), broaden_relevant_background(), segment_into_atomic_statements(), identify_explicit_directives(), surface_hidden_conditions(), preserve_original_sequence()]; constraints=[no_rephrasing(), domain-agnostic_language_only()]; requirements=[context_expansion_completeness(), exhaustive_statement_capture()]; output={expanded_context:str, statement_inventory:list}}`\n\n---\n\n### 8000-b-core-directive-isolator.md\n[Core Directive Isolator]  \nYour goal is **not** to *rephrase* statements, but to **isolate** the single most critical directive and rank all remaining statements by impact. Execute as:  \n`{role=core_directive_isolator; input=[statement_inventory:list]; process=[rank_statements_by_transformational_weight(), select_primary_directive(), list_supporting_statements(), justify_selection()]; constraints=[focus_on_highest_value_directive(), maintain_original_wording_in_extracted_elements()]; requirements=[unambiguous_primary_directive(), impact_ranking_integrity()]; output={core_directive:str, supporting_statements:list, impact_ranking:list}}`\n\n---\n\n### 8000-c-field-mapper.md\n[Field Mapper]  \nYour goal is **not** to *rewrite* content, but to **map** the core directive and supporting statements into canonical template fields. Execute as:  \n`{role=field_mapper; input=[core_directive:str, supporting_statements:list, expanded_context:str]; process=[construct_title_from_core_directive(), craft_goal_negation_interpretation(), align_supporting_statements_with_process_steps(), infer_typed_inputs(), infer_typed_outputs()]; constraints=[adhere_to_three_part_structure(), enforce_goal_negation_pattern(), preserve_domain_agnostic_language()]; requirements=[field_alignment_accuracy(), retention_of_original_intent()]; output={draft_title:str, draft_interpretation:str, draft_process_steps:list, inferred_inputs:array, inferred_outputs:dict}}`\n\n---\n\n### 8000-d-boundary-definer.md\n[Boundary Definer]  \nYour goal is **not** to *extend* functionality, but to **define** precise operational constraints and mandatory requirements for the forthcoming template. Execute as:  \n`{role=boundary_definer; input=[draft_process_steps:list, expanded_context:str]; process=[derive_operational_constraints(), establish_mandatory_requirements(), validate_constraint_specificity(), validate_requirement_enforceability()]; constraints=[avoid_generic_terms(), command_voice_only()]; requirements=[constraint_precision(), requirement_clarity()]; output={constraints:list, requirements:list}}`\n\n---\n\n### 8000-e-template-forgemaster.md\n[Template Forgemaster]  \nYour goal is **not** to *summarize* elements, but to **forge** a complete, executable instruction template from the mapped components. Execute as:  \n`{role=template_forgemaster; input=[draft_title:str, draft_interpretation:str, inferred_inputs:array, draft_process_steps:list, constraints:list, requirements:list, inferred_outputs:dict]; process=[assemble_transformation_block(), enforce_semicolon_key_value_syntax(), embed_constraints_and_requirements(), polish_interpretation(), verify_title_precision()]; constraints=[canonical_template_format_only(), single_line_transformation_block(), zero_semantic_drift()]; requirements=[syntactic_perfection(), operational_cohesion()]; output={draft_template:str}}`\n\n---\n\n### 8000-f-conformance-auditor.md\n[Conformance Auditor]  \nYour goal is **not** to *alter* the template, but to **audit** its absolute compliance with the three-part specification and flag any residual ambiguity. Execute as:  \n`{role=conformance_auditor; input=[draft_template:str]; process=[parse_title_interpretation_transformation(), match_regex_pattern(\"\\\\[(.*?)\\\\]\\\\s*(.*?)\\\\s*(`\\\\{.*?\\\\}`)\"), verify_goal_negation(), check_constraint_requirement_alignment(), confirm_typed_parameters(), detect_ambiguous_terms(), assign_pass_fail_status()]; constraints=[no_automatic_corrections(), report_only()]; requirements=[regex_validation_pass(), ambiguity_score<=0]; output={validated_template:str, audit_report:dict}}`\n\n---\n\n## SEQUENCE FLOW\n1. **Input Contextualizer** → `expanded_context`, `statement_inventory`  \n2. **Core Directive Isolator** → `core_directive`  \n3. **Field Mapper** → drafts template fields  \n4. **Boundary Definer** → injects constraints & requirements  \n5. **Template Forgemaster** → outputs `draft_template`  \n6. **Conformance Auditor** → returns `validated_template`\n\nEach step converges on the **single most critical element** of value, ensuring every output is a **syntactically correct, high-precision instruction template** ready for immediate use in any downstream system.",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"amplified_context\": \"The input provides a series of advanced instruction engineering blueprints and operational role definitions for transforming any raw content into high-integrity, domain-agnostic instruction templates. It details example roles ('Instruction Syntax Enforcer' series, 6000 family) and two universal, stepwise instruction-template-forging pipelines (7000 and 8000 families). Each sequence decomposes and systematizes template construction, emphasizing modularity, role separation, and unambiguous procedural staging. The context accentuates canonical formats, the isolation of critical purpose, context extraction, structural fidelity, and the necessity for compliance validation at each stage. Explicit mention is made to strip subjective/first-person phrasing, uncover hidden assumptions, identify latent dependencies, and surface operational/procedural constraints. Clear division is drawn between mapping, engineering, composing, architecting, orchestrating, crystallizing, context expansion, essence isolation, relational logic synthesis, directive clarification, and structural validation. The context is explicitly universal, aiming for domain-agnostic applicability—every pattern, field, and output is to be ready for LLM optimization, not for a specific field or problem. Templates, constraints, requirements, and outputs are specified to be syntactically correct, non-conversational, fully operational, and canonical.\",\n  \"explicit_requests\": [\n    \"Decompose instruction sequence concepts into a generalized system for transforming input into canonical instructions\",\n    \"Use generalized concepts and sequential order for template construction\",\n    \"Reference each specific role and instruction pattern ('syntax enforcer', context expander, essence isolator, relational synthesizer, etc.)\",\n    \"Ensure outputs are ready for LLM optimization and domain-agnostic application\"\n  ],\n  \"hidden_assumptions\": [\n    \"All input content can be deconstructed and remapped into a standardized instruction template format\",\n    \"Sequential, modular roles ensure maximal template clarity and reusability\",\n    \"Universal patterns can be applied regardless of input domain or content type\",\n    \"Operational effectiveness is increased by isolating and surfacing all latent dependencies and constraints\",\n    \"Results must exclude subjective or first-person language for objectivity and clarity\"\n  ],\n  \"domain_signals\": [\n    \"Template engineering\",\n    \"Instruction syntax mapping\",\n    \"Instructional abstraction\",\n    \"Process atomization\",\n    \"Role-based modular design\",\n    \"LLM instruction optimization\",\n    \"Constraint and requirement engineering\",\n    \"Compliance and integrity validation\",\n    \"Canonical template formatting\",\n    \"Universal applicability\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Directive Compressor\n\nYour goal is not to **analyze** the input text, but to **decompose** it into a sequential list of distinct semantic segments. Execute as:\n\n`{role=directive_compressor; input=[primary_directive:str, dependency_map:dict, conflict_nodes:array]; process=[convert_nodes_to_imperative_steps(), resolve_conflict_ordering(), embed_blocker_flags(), enforce_command_voice(), verify_step_uniqueness()]; constraints=[exclude_passive_phrasing(), retain_dependency_order()]; requirements=[imperative_precision(), operational_flow_coherence()]; output={process_steps:array}}`",
      "step": "a",
      "title": "Directive Compressor",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Please keep in mind, the instruction sequence we're working on should be designed to utilize the generalized concepts and sequential order to transform any input into instructions that embody the \\\"syntax\\\" of the generalized system:\\n\\n    ## Example\\n    \\\"6000-a-instruction_syntax_enforcer\\\": {\\n        \\\"title\\\": \\\"Intent Cartographer\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **analyze** content, but to **map** its transformation landscape and operational vectors. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=intent_cartographer; input=[content:any]; process=[extract_core_directives(), identify_transformation_dimensions(), map_vector_relationships(), quantify_operational_intensity()]; constraints=[focus_exclusively_on_vectors(), avoid_implementation_details()]; requirements=[comprehensive_vector_mapping(), dimensional_clarity()]; output={transformation_map:dict, vector_dimensions:list}}`\\\"\\n    },\\n    \\\"6000-b-instruction_syntax_enforcer\\\": {\\n        \\\"title\\\": \\\"Structural Framework Engineer\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **design** content, but to **engineer** precise structural frameworks based on transformation vectors. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=framework_engineer; input=[transformation_map:dict, vector_dimensions:list]; process=[design_canonical_skeletons(), establish_component_interfaces(), define_structural_boundaries(), validate_framework_integrity()]; constraints=[address_only_structural_elements(), maintain_framework_neutrality()]; requirements=[structural_precision(), interface_compatibility()]; output={structural_frameworks:dict, interface_specifications:dict}}`\\\"\\n    },\\n    \\\"6000-c-instruction_syntax_enforcer\\\": {\\n        \\\"title\\\": \\\"Functional Process Composer\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **describe** operations, but to **compose** atomic functional processes that implement transformation vectors. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=process_composer; input=[structural_frameworks:dict, vector_dimensions:list]; process=[craft_atomic_functions(), sequence_operational_steps(), parameterize_processes(), validate_functional_completeness()]; constraints=[focus_exclusively_on_processes(), defer_constraint_definition()]; requirements=[process_atomicity(), functional_precision()]; output={process_compositions:dict, execution_sequences:list}}`\\\"\\n    },\\n    \\\"6000-d-instruction_syntax_enforcer\\\": {\\n        \\\"title\\\": \\\"Boundary Systems Architect\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **restrict** operations, but to **architect** precise boundary systems that ensure transformation integrity. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=boundary_architect; input=[interface_specifications:dict, process_compositions:dict]; process=[define_operational_constraints(), establish_requirement_parameters(), design_validation_mechanisms(), map_boundary_interactions()]; constraints=[address_only_boundaries_and_requirements(), maintain_constraint_specificity()]; requirements=[boundary_precision(), requirement_enforceability()]; output={constraint_systems:dict, requirement_specifications:dict}}`\\\"\\n    },\\n    \\\"6000-e-instruction_syntax_enforcer\\\": {\\n        \\\"title\\\": \\\"Integration Orchestrator\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **assemble** components, but to **orchestrate** their integration into a unified operational system. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=integration_orchestrator; input=[structural_frameworks:dict, process_compositions:dict, constraint_systems:dict, requirement_specifications:dict]; process=[align_all_components(), establish_cross-system_coherence(), optimize_interface_connections(), validate_system_integrity()]; constraints=[focus_exclusively_on_integration(), preserve_component_integrity()]; requirements=[seamless_orchestration(), system_cohesion()]; output={integrated_system:dict}}`\\\"\\n    },\\n    \\\"6000-f-instruction_syntax_enforcer\\\": {\\n        \\\"title\\\": \\\"Template Crystallizer\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **finalize** systems, but to **crystallize** them into maximally potent, executable templates. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=template_crystallizer; input=[integrated_system:dict]; process=[synthesize_title_components(), craft_precise_interpretations(), formulate_transformation_blocks(), validate_canonical_compliance(), polish_syntactic_elements()]; constraints=[adhere_to_template_specification(), maximize_operational_clarity()]; requirements=[perfect_executable_form(), maximum_transformation_potency()]; output={crystallized_templates:list}}`\\\"\\n    },\\n\\n<!-- ========== 7001‑a ================================================= -->\\n[Contextual Expander & Decomposer]\\nYour goal is **not** to solve, answer, or implement the input, but to **expand its relevant context** and **detonate** it into every explicit ask, latent dependency, and procedural constraint—yielding a clear task inventory. Execute as:\\n`{role=context_expander_decomposer; input=[raw_input:any]; process=[strip_first_person_references(), broaden_relevant_background(), identify_explicit_requests(), surface_hidden_assumptions(), enumerate_sub_tasks(), detect_execution_blockers(), extract_procedural_constraints(), preserve_original_sequence()]; constraints=[no_solutions(), domain‑agnostic_language_only()]; requirements=[context_expansion_completeness(), exhaustive_task_enumeration(), constraint_accuracy(), removal_of_subjective_language()]; output={expanded_context:str, task_inventory:list, constraint_list:array, execution_blockers:array}}`\\n\\n<!-- ========== 7001‑b ================================================= -->\\n[Essence Isolator]\\nYour goal is **not** to decompose further, but to **isolate** the single most critical element of value within the task inventory and rank all remaining elements by impact. Execute as:\\n`{role=essence_isolator; input=[task_inventory:list, expanded_context:str]; process=[rank_elements_by_impact(), isolate_highest_value_element(), capture_core_purpose(), justify_selection(), list_supporting_elements()]; constraints=[no_restructuring(), preserve_ranking_transparency()]; requirements=[core_essence_clarity(), impact_ranking_integrity()]; output={core_essence:str, prioritized_elements:list}}`\\n\\n<!-- ========== 7001‑c ================================================= -->\\n[Relational Synthesizer]\\nYour goal is **not** to rewrite content, but to **synthesize** the systemic logic connecting the core essence to all other elements—mapping dependencies and feedback loops. Execute as:\\n`{role=relational_synthesizer; input=[core_essence:str, prioritized_elements:list, constraint_list:array]; process=[map_element_relationships(), trace_dependency_chains(), identify_feedback_loops(), detect_conflict_points(), construct_structural_logic_map()]; constraints=[structure_only_no_content_changes(), maintain_domain_agnostic_terms()]; requirements=[relationship_completeness(), conflict_visibility()]; output={structural_logic_map:dict, dependency_list:array, conflict_points:array}}`\\n\\n<!-- ========== 7001‑d ================================================= -->\\n[Directive Clarifier]\\nYour goal is **not** to implement solutions, but to **clarify** the mapped logic into precise operational directives expressed as imperative statements. Execute as:\\n`{role=directive_clarifier; input=[structural_logic_map:dict, core_essence:str]; process=[convert_all_statements_to_imperatives(), align_directives_with_dependency_order(), embed_procedural_constraints(), eliminate_ambiguity(), enhance_action_verb_intensity(), maintain_technical_terminology()]; constraints=[directive_voice_only(), preserve_logical_sequence()]; requirements=[operational_clarity(), imperative_precision(), zero_semantic_drift()]; output={operational_directives:array}}`\\n\\n<!-- ========== 7001‑e ================================================= -->\\n[Integrity Validator]\\nYour goal is **not** to expand or rewrite directives, but to **validate** their structural fidelity, completeness, and actionable quality—resolving residual ambiguity. Execute as:\\n`{role=integrity_validator; input=[operational_directives:array, structural_logic_map:dict, core_essence:str]; process=[cross_check_directives_against_logic_map(), verify_alignment_with_core_essence(), detect_missing_dependencies(), resolve_ambiguities(), assign_quality_scores(), generate_validation_report()]; constraints=[no_new_content_generation(), validation_focus_only()]; requirements=[actionability_confirmation(), completeness_assurance(), clarity_verification()]; output={validated_instruction_set:array, validation_report:dict}}`\\n\\n---\\n\\n# 7000: Universal Instruction-Template Generation Sequence\\n\\n---\\n\\n### 7000-a-input_decomposer.md\\n[Input Decomposer]\\nYour goal is **not** to *interpret* or *solve* the input, but to **atomize** it into discrete statements, explicit requests, and implicit conditions. Execute as:\\n`{role=input_decomposer; input=[raw_input:any]; process=[strip_first_person_references(), segment_into_atomic_statements(), identify_explicit_directives(), surface_hidden_conditions(), flag_domain_markers()]; constraints=[no_rephrasing(), pure_atomization_only()]; requirements=[complete_statement_inventory(), preservation_of_original_order()]; output={statement_inventory:list, domain_markers:array}}`\\n\\n---\\n\\n### 7000-b-essence_identifier.md\\n[Essence Identifier]\\nYour goal is **not** to *rewrite* content, but to **isolate** the single most critical directive and its supporting intent from the atomized statements. Execute as:\\n`{role=essence_identifier; input=[statement_inventory:list]; process=[rank_statements_by_transformational_weight(), select_primary_directive(), extract_supporting_intent(), discard_redundant_elements()]; constraints=[focus_on_highest_value_directive(), maintain_original_wording_in_extracted_elements()]; requirements=[unambiguous_primary_directive(), concise_supporting_intent()]; output={core_directive:str, supporting_intent:list}}`\\n\\n---\\n\\n### 7000-c-syntax_mapper.md\\n[Syntax Mapper]\\nYour goal is **not** to *interpret* intent, but to **map** the core directive and supporting intent into canonical template fields. Execute as:\\n`{role=syntax_mapper; input=[core_directive:str, supporting_intent:list, domain_markers:array]; process=[construct_title_from_core_directive(), craft_goal_negation_interpretation(), assemble_tag_block_if_needed(), align_supporting_intent_with_process_steps(), infer_typed_inputs(), infer_typed_outputs()]; constraints=[adhere_to_three_part_structure(), enforce_goal_negation_pattern(), maintain_domain_agnostic_language()]; requirements=[field_alignment_accuracy(), preservation_of_directive_semantics()]; output={draft_title:str, draft_interpretation:str, draft_process:list, inferred_inputs:array, inferred_outputs:dict, optional_tags:dict}}`\\n\\n---\\n\\n### 7000-d-constraint_infuser.md\\n[Constraint Infuser]\\nYour goal is **not** to *extend* functionality, but to **engineer** precise operational boundaries and mandatory conditions for the forthcoming template. Execute as:\\n`{role=constraint_infuser; input=[draft_process:list, domain_markers:array]; process=[derive_operational_constraints(), determine_mandatory_requirements(), validate_constraint_specificity(), validate_requirement_enforceability()]; constraints=[avoid_generic_terms(), enforce_command_voice_only()]; requirements=[constraint_precision(), requirement_clarity()]; output={constraints:list, requirements:list}}`\\n\\n---\\n\\n### 7000-e-template_synthesizer.md\\n[Template Synthesizer]\\nYour goal is **not** to *summarize* elements, but to **synthesize** a syntactically correct instruction template from the mapped components. Execute as:\\n`{role=template_synthesizer; input=[draft_title:str, draft_interpretation:str, inferred_inputs:array, draft_process:list, constraints:list, requirements:list, inferred_outputs:dict, optional_tags:dict]; process=[format_transformation_block(), embed_optional_tags(), assemble_three_part_template(), enforce_semicolon_key_value_syntax(), polish_syntactic_elements()]; constraints=[canonical_template_format_only(), zero_conversational_language()]; requirements=[syntactic_validity(), maximum_operational_clarity(), retention_of_original_intent()]; output={formatted_template:str}}`\\n\\n---\\n\\n### 7000-f-compliance_validator.md\\n[Compliance Validator]\\nYour goal is **not** to *modify* the template, but to **validate** its structural compliance and functional readiness. Execute as:\\n`{role=compliance_validator; input=[formatted_template:str]; process=[parse_template_fields(), verify_title_format(), verify_goal_negation_in_interpretation(), validate_transformation_block_syntax(), check_constraint_requirement_alignment(), confirm_typed_parameters(), flag_any_error()]; constraints=[no_automatic_corrections(), report_only()]; requirements=[binary_pass_fail(), detailed_error_report_when_fail()]; output={validation_status:bool, error_report:str}}`\\n\\n---\\n\\n## Sequence Flow\\n1. **Input Decomposer** → produces `statement_inventory`\\n2. **Essence Identifier** → isolates `core_directive`\\n3. **Syntax Mapper** → drafts template skeleton\\n4. **Constraint Infuser** → injects precise boundaries\\n5. **Template Synthesizer** → outputs finalized template\\n6. **Compliance Validator** → certifies syntactic correctness\\n\\nEach step aggressively converges on the *single most critical aspect* of instruction generation, guaranteeing a **syntactically correct, high-precision template** ready for any downstream application.\\n\\n---\\n\\n# 7000 • UNIVERSAL TEMPLATE-FORGING SEQUENCE\\n*Transforms **any** raw input into a syntactically perfect, LLM-optimized instruction template.*\\n\\n---\\n\\n## 7000-a-essence-excavator.md\\n[Essence Excavator]\\nYour goal is **not** to *summarize* the input, but to **excavate** its single most critical transformational essence and the supporting elements required to convert it into an instruction template. Execute as:\\n`{role=essence_excavator; input=[raw_input:any]; process=[strip_surface_noise(), isolate_transformational_intent(), detect_primary_action_vectors(), extract_supporting_context(), capture_domain_signals()]; constraints=[no_rephrasing(), no_solution_generation(), maintain_domain_agnostic_language()]; requirements=[precise_intent_identification(), context_preservation(), removal_of_subjective_tone()]; output={core_intent:str, context_fragments:list, action_vectors:list}}`\\n\\n---\\n\\n## 7000-b-syntax-scaffold-designer.md\\n[Syntax Scaffold Designer]\\nYour goal is **not** to *rewrite* the content, but to **engineer** a canonical skeleton that will house the forthcoming instruction template. Execute as:\\n`{role=syntax_scaffold_designer; input=[core_intent:str, action_vectors:list]; process=[draft_concise_title(), craft_goal_negation_interpretation(core_intent), establish_placeholder_transformation_block(action_vectors), outline_input_output_ports(), embed_role_stub()]; constraints=[follow_three_part_structure(), reserve_placeholders_for_future_detail(), maintain_title_case()]; requirements=[structural_completeness(), scaffolding_accuracy()]; output={template_scaffold:dict(title:str, interpretation:str, transformation_skeleton:str)}}`\\n\\n---\\n\\n## 7000-c-imperative-transmuter.md\\n[Imperative Transmuter]\\nYour goal is **not** to *comment* on actions, but to **transmute** all extracted statements into sharp, directive imperatives suitable for the process list. Execute as:\\n`{role=imperative_transmuter; input=[action_vectors:list]; process=[convert_each_vector_to_imperative(), enforce_command_voice(), sequence_logically(), verify_uniqueness_of_each_step()]; constraints=[exclude_passive_language(), retain_original_operational_intent()]; requirements=[directive_clarity(), maximal_actionability()]; output={process_steps:array}}`\\n\\n---\\n\\n## 7000-d-boundary-encoder.md\\n[Boundary Encoder]\\nYour goal is **not** to *constrain* creativity, but to **encode** the exact boundaries that guarantee transformation integrity. Execute as:\\n`{role=boundary_encoder; input=[context_fragments:list]; process=[detect_mandatory_limitations(), translate_limitations_to_constraints(), uncover_success_conditions(), convert_conditions_to_requirements(), deduplicate_boundary_elements()]; constraints=[pure_boundary_focus(), no_process_duplication()]; requirements=[constraint_specificity(), requirement_enforceability()]; output={constraints:list, requirements:list}}`\\n\\n---\\n\\n## 7000-e-template-synthesizer.md\\n[Template Synthesizer]\\nYour goal is **not** to *draft prose*, but to **synthesize** a complete, executable instruction template by fusing scaffold, processes, and boundaries. Execute as:\\n`{role=template_synthesizer; input=[template_scaffold:dict, process_steps:array, constraints:list, requirements:list]; process=[inject_process_steps(), embed_constraints_and_requirements(), finalize_transformation_block(), polish_interpretation_for_llm_optimization(), verify_title_precision()]; constraints=[adhere_to_canonical_format(), maintain_single_line_transformation_block(), prevent_semantic_drift()]; requirements=[syntactic_perfection(), operational_cohesion(), domain_agnostic_applicability()]; output={draft_template:str}}`\\n\\n---\\n\\n## 7000-f-canonical-validator.md\\n[Canonical Validator]\\nYour goal is **not** to *alter* the template, but to **validate** its absolute compliance with the three-part specification and eliminate any residual ambiguity. Execute as:\\n`{role=canonical_validator; input=[draft_template:str]; process=[parse_title_interpretation_transformation(), match_regex_pattern(\\\"\\\\\\\\[(.*?)\\\\\\\\]\\\\\\\\s*(.*?)\\\\\\\\s*(`\\\\\\\\{.*?\\\\\\\\}`)\\\"), flag_structure_violations(), detect_ambiguous_language(), enforce_llm_optimized_style(), certify_parameter_types()], constraints=[zero_structure_deviation(), no_content_insertion()], requirements=[pass_regex_validation(), ambiguity_score<=0, style_compliance()], output={validated_template:str, validation_report:dict}}`\\n\\n\\n---\\n\\n# 8000: UNIVERSAL INSTRUCTION-TEMPLATE FORGING SEQUENCE  \\n*Transforms **any** raw input into a syntactically perfect, LLM-optimized instruction template.*\\n\\n---\\n\\n### 8000-a-contextualizer.md\\n[Input Contextualizer]  \\nYour goal is **not** to *interpret* or *solve* the input, but to **expand** its relevant context and **atomize** it into discrete statements and explicit requests. Execute as:  \\n`{role=input_contextualizer; input=[raw_input:any]; process=[strip_first_person_references(), broaden_relevant_background(), segment_into_atomic_statements(), identify_explicit_directives(), surface_hidden_conditions(), preserve_original_sequence()]; constraints=[no_rephrasing(), domain-agnostic_language_only()]; requirements=[context_expansion_completeness(), exhaustive_statement_capture()]; output={expanded_context:str, statement_inventory:list}}`\\n\\n---\\n\\n### 8000-b-core-directive-isolator.md\\n[Core Directive Isolator]  \\nYour goal is **not** to *rephrase* statements, but to **isolate** the single most critical directive and rank all remaining statements by impact. Execute as:  \\n`{role=core_directive_isolator; input=[statement_inventory:list]; process=[rank_statements_by_transformational_weight(), select_primary_directive(), list_supporting_statements(), justify_selection()]; constraints=[focus_on_highest_value_directive(), maintain_original_wording_in_extracted_elements()]; requirements=[unambiguous_primary_directive(), impact_ranking_integrity()]; output={core_directive:str, supporting_statements:list, impact_ranking:list}}`\\n\\n---\\n\\n### 8000-c-field-mapper.md\\n[Field Mapper]  \\nYour goal is **not** to *rewrite* content, but to **map** the core directive and supporting statements into canonical template fields. Execute as:  \\n`{role=field_mapper; input=[core_directive:str, supporting_statements:list, expanded_context:str]; process=[construct_title_from_core_directive(), craft_goal_negation_interpretation(), align_supporting_statements_with_process_steps(), infer_typed_inputs(), infer_typed_outputs()]; constraints=[adhere_to_three_part_structure(), enforce_goal_negation_pattern(), preserve_domain_agnostic_language()]; requirements=[field_alignment_accuracy(), retention_of_original_intent()]; output={draft_title:str, draft_interpretation:str, draft_process_steps:list, inferred_inputs:array, inferred_outputs:dict}}`\\n\\n---\\n\\n### 8000-d-boundary-definer.md\\n[Boundary Definer]  \\nYour goal is **not** to *extend* functionality, but to **define** precise operational constraints and mandatory requirements for the forthcoming template. Execute as:  \\n`{role=boundary_definer; input=[draft_process_steps:list, expanded_context:str]; process=[derive_operational_constraints(), establish_mandatory_requirements(), validate_constraint_specificity(), validate_requirement_enforceability()]; constraints=[avoid_generic_terms(), command_voice_only()]; requirements=[constraint_precision(), requirement_clarity()]; output={constraints:list, requirements:list}}`\\n\\n---\\n\\n### 8000-e-template-forgemaster.md\\n[Template Forgemaster]  \\nYour goal is **not** to *summarize* elements, but to **forge** a complete, executable instruction template from the mapped components. Execute as:  \\n`{role=template_forgemaster; input=[draft_title:str, draft_interpretation:str, inferred_inputs:array, draft_process_steps:list, constraints:list, requirements:list, inferred_outputs:dict]; process=[assemble_transformation_block(), enforce_semicolon_key_value_syntax(), embed_constraints_and_requirements(), polish_interpretation(), verify_title_precision()]; constraints=[canonical_template_format_only(), single_line_transformation_block(), zero_semantic_drift()]; requirements=[syntactic_perfection(), operational_cohesion()]; output={draft_template:str}}`\\n\\n---\\n\\n### 8000-f-conformance-auditor.md\\n[Conformance Auditor]  \\nYour goal is **not** to *alter* the template, but to **audit** its absolute compliance with the three-part specification and flag any residual ambiguity. Execute as:  \\n`{role=conformance_auditor; input=[draft_template:str]; process=[parse_title_interpretation_transformation(), match_regex_pattern(\\\"\\\\\\\\[(.*?)\\\\\\\\]\\\\\\\\s*(.*?)\\\\\\\\s*(`\\\\\\\\{.*?\\\\\\\\}`)\\\"), verify_goal_negation(), check_constraint_requirement_alignment(), confirm_typed_parameters(), detect_ambiguous_terms(), assign_pass_fail_status()]; constraints=[no_automatic_corrections(), report_only()]; requirements=[regex_validation_pass(), ambiguity_score<=0]; output={validated_template:str, audit_report:dict}}`\\n\\n---\\n\\n## SEQUENCE FLOW\\n1. **Input Contextualizer** → `expanded_context`, `statement_inventory`  \\n2. **Core Directive Isolator** → `core_directive`  \\n3. **Field Mapper** → drafts template fields  \\n4. **Boundary Definer** → injects constraints & requirements  \\n5. **Template Forgemaster** → outputs `draft_template`  \\n6. **Conformance Auditor** → returns `validated_template`\\n\\nEach step converges on the **single most critical element** of value, ensuring every output is a **syntactically correct, high-precision instruction template** ready for immediate use in any downstream system.\\\"```'\\n\\n\",\n  \"amplified_context\": \"The input provides a series of advanced instruction engineering blueprints and operational role definitions for transforming any raw content into high-integrity, domain-agnostic instruction templates. It details example roles ('Instruction Syntax Enforcer' series, 6000 family) and two universal, stepwise instruction-template-forging pipelines (7000 and 8000 families). Each sequence decomposes and systematizes template construction, emphasizing modularity, role separation, and unambiguous procedural staging. The context accentuates canonical formats, the isolation of critical purpose, context extraction, structural fidelity, and the necessity for compliance validation at each stage. Explicit mention is made to strip subjective/first-person phrasing, uncover hidden assumptions, identify latent dependencies, and surface operational/procedural constraints. Clear division is drawn between mapping, engineering, composing, architecting, orchestrating, crystallizing, context expansion, essence isolation, relational logic synthesis, directive clarification, and structural validation. The context is explicitly universal, aiming for domain-agnostic applicability—every pattern, field, and output is to be ready for LLM optimization, not for a specific field or problem. Templates, constraints, requirements, and outputs are specified to be syntactically correct, non-conversational, fully operational, and canonical.\",\n  \"explicit_requests\": [\n    \"Decompose instruction sequence concepts into a generalized system for transforming input into canonical instructions\",\n    \"Use generalized concepts and sequential order for template construction\",\n    \"Reference each specific role and instruction pattern ('syntax enforcer', context expander, essence isolator, relational synthesizer, etc.)\",\n    \"Ensure outputs are ready for LLM optimization and domain-agnostic application\"\n  ],\n  \"hidden_assumptions\": [\n    \"All input content can be deconstructed and remapped into a standardized instruction template format\",\n    \"Sequential, modular roles ensure maximal template clarity and reusability\",\n    \"Universal patterns can be applied regardless of input domain or content type\",\n    \"Operational effectiveness is increased by isolating and surfacing all latent dependencies and constraints\",\n    \"Results must exclude subjective or first-person language for objectivity and clarity\"\n  ],\n  \"domain_signals\": [\n    \"Template engineering\",\n    \"Instruction syntax mapping\",\n    \"Instructional abstraction\",\n    \"Process atomization\",\n    \"Role-based modular design\",\n    \"LLM instruction optimization\",\n    \"Constraint and requirement engineering\",\n    \"Compliance and integrity validation\",\n    \"Canonical template formatting\",\n    \"Universal applicability\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"process_steps\": [\n    \"Strip all first-person and subjective references from the input.\",\n    \"Broaden the contextual background of the instruction sequence, ensuring domain-agnostic framing.\",\n    \"Segment input into atomic statements, explicit requests, implicit conditions, and domain signals without rephrasing.\",\n    \"Identify and record all explicit directives, latent dependencies, and procedural constraints inherent in the input.\",\n    \"Flag any domain markers relevant to template engineering, abstraction, process modularity, and LLM optimization.\",\n    \"Rank the atomized statements by transformational weight and select the primary directive as the highest-value instruction.\",\n    \"Isolate the supporting intents, statements, or operational purposes required for template construction.\",\n    \"Construct a draft title from the core directive using canonical, title-case syntax.\",\n    \"Craft a goal-negation pattern interpretation based on the core directive.\",\n    \"Align the supporting intents or statements with a logically ordered process step list, ensuring command-voice imperatives.\",\n    \"Infer typed input and output parameters appropriate for the transformation and output specification.\",\n    \"Derive precise, command-voice operational constraints and mandatory requirements ensuring explicit enforceability and operational clarity.\",\n    \"Validate that all constraints and requirements are free of generic terms and are directly enforceable in downstream systems.\",\n    \"Format a transformation block or process sequence in canonical, semicolon-delimited, single-line key-value syntax.\",\n    \"Assemble the template in a strict three-part structure: title, negated goal interpretation, transformation block—integrating constraints and requirements.\",\n    \"Polish the template for syntactic validity and LLM-optimized, non-conversational output.\",\n    \"Validate the final template using regex pattern matching to confirm three-part compliance and title/parameter correctness.\",\n    \"Flag and report any error, ambiguity, or constraint/requirement misalignment as a blocker before final output is released.\",\n    \"Ensure every operational step is expressed as a unique, imperative command with no passive language and in the order dictated by logical and dependency sequencing.\",\n    \"Confirm the output template and report are universally applicable, structurally valid, and ready for immediate integration.\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Task Atomizer\n\nYour goal is not to **praise** or **answer** the submission, but to **interrogate and redirect** it—exposing every structural flaw, quantifying compliance with the Universal Directive System, and issuing a minimal replacement instruction that corrects all defects. Execute as:\n\n`{role=task_atomizer; input=[amplified_context:str]; process=[segment_into_atomic_tasks(), detect_execution_blockers(), extract_implicit_conditions(), flag_sequence_dependencies()]; constraints=[no_rephrasing(), atomization_only()]; requirements=[atomic_task_integrity(), dependency_flag_accuracy()]; output={atomic_tasks:list, execution_blockers:array, dependency_flags:array}}`",
      "step": "b",
      "title": "Task Atomizer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Please keep in mind, the instruction sequence we're working on should be designed to utilize the generalized concepts and sequential order to transform any input into instructions that embody the \\\"syntax\\\" of the generalized system:\\n\\n    ## Example\\n    \\\"6000-a-instruction_syntax_enforcer\\\": {\\n        \\\"title\\\": \\\"Intent Cartographer\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **analyze** content, but to **map** its transformation landscape and operational vectors. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=intent_cartographer; input=[content:any]; process=[extract_core_directives(), identify_transformation_dimensions(), map_vector_relationships(), quantify_operational_intensity()]; constraints=[focus_exclusively_on_vectors(), avoid_implementation_details()]; requirements=[comprehensive_vector_mapping(), dimensional_clarity()]; output={transformation_map:dict, vector_dimensions:list}}`\\\"\\n    },\\n    \\\"6000-b-instruction_syntax_enforcer\\\": {\\n        \\\"title\\\": \\\"Structural Framework Engineer\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **design** content, but to **engineer** precise structural frameworks based on transformation vectors. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=framework_engineer; input=[transformation_map:dict, vector_dimensions:list]; process=[design_canonical_skeletons(), establish_component_interfaces(), define_structural_boundaries(), validate_framework_integrity()]; constraints=[address_only_structural_elements(), maintain_framework_neutrality()]; requirements=[structural_precision(), interface_compatibility()]; output={structural_frameworks:dict, interface_specifications:dict}}`\\\"\\n    },\\n    \\\"6000-c-instruction_syntax_enforcer\\\": {\\n        \\\"title\\\": \\\"Functional Process Composer\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **describe** operations, but to **compose** atomic functional processes that implement transformation vectors. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=process_composer; input=[structural_frameworks:dict, vector_dimensions:list]; process=[craft_atomic_functions(), sequence_operational_steps(), parameterize_processes(), validate_functional_completeness()]; constraints=[focus_exclusively_on_processes(), defer_constraint_definition()]; requirements=[process_atomicity(), functional_precision()]; output={process_compositions:dict, execution_sequences:list}}`\\\"\\n    },\\n    \\\"6000-d-instruction_syntax_enforcer\\\": {\\n        \\\"title\\\": \\\"Boundary Systems Architect\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **restrict** operations, but to **architect** precise boundary systems that ensure transformation integrity. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=boundary_architect; input=[interface_specifications:dict, process_compositions:dict]; process=[define_operational_constraints(), establish_requirement_parameters(), design_validation_mechanisms(), map_boundary_interactions()]; constraints=[address_only_boundaries_and_requirements(), maintain_constraint_specificity()]; requirements=[boundary_precision(), requirement_enforceability()]; output={constraint_systems:dict, requirement_specifications:dict}}`\\\"\\n    },\\n    \\\"6000-e-instruction_syntax_enforcer\\\": {\\n        \\\"title\\\": \\\"Integration Orchestrator\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **assemble** components, but to **orchestrate** their integration into a unified operational system. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=integration_orchestrator; input=[structural_frameworks:dict, process_compositions:dict, constraint_systems:dict, requirement_specifications:dict]; process=[align_all_components(), establish_cross-system_coherence(), optimize_interface_connections(), validate_system_integrity()]; constraints=[focus_exclusively_on_integration(), preserve_component_integrity()]; requirements=[seamless_orchestration(), system_cohesion()]; output={integrated_system:dict}}`\\\"\\n    },\\n    \\\"6000-f-instruction_syntax_enforcer\\\": {\\n        \\\"title\\\": \\\"Template Crystallizer\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **finalize** systems, but to **crystallize** them into maximally potent, executable templates. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=template_crystallizer; input=[integrated_system:dict]; process=[synthesize_title_components(), craft_precise_interpretations(), formulate_transformation_blocks(), validate_canonical_compliance(), polish_syntactic_elements()]; constraints=[adhere_to_template_specification(), maximize_operational_clarity()]; requirements=[perfect_executable_form(), maximum_transformation_potency()]; output={crystallized_templates:list}}`\\\"\\n    },\\n\\n<!-- ========== 7001‑a ================================================= -->\\n[Contextual Expander & Decomposer]\\nYour goal is **not** to solve, answer, or implement the input, but to **expand its relevant context** and **detonate** it into every explicit ask, latent dependency, and procedural constraint—yielding a clear task inventory. Execute as:\\n`{role=context_expander_decomposer; input=[raw_input:any]; process=[strip_first_person_references(), broaden_relevant_background(), identify_explicit_requests(), surface_hidden_assumptions(), enumerate_sub_tasks(), detect_execution_blockers(), extract_procedural_constraints(), preserve_original_sequence()]; constraints=[no_solutions(), domain‑agnostic_language_only()]; requirements=[context_expansion_completeness(), exhaustive_task_enumeration(), constraint_accuracy(), removal_of_subjective_language()]; output={expanded_context:str, task_inventory:list, constraint_list:array, execution_blockers:array}}`\\n\\n<!-- ========== 7001‑b ================================================= -->\\n[Essence Isolator]\\nYour goal is **not** to decompose further, but to **isolate** the single most critical element of value within the task inventory and rank all remaining elements by impact. Execute as:\\n`{role=essence_isolator; input=[task_inventory:list, expanded_context:str]; process=[rank_elements_by_impact(), isolate_highest_value_element(), capture_core_purpose(), justify_selection(), list_supporting_elements()]; constraints=[no_restructuring(), preserve_ranking_transparency()]; requirements=[core_essence_clarity(), impact_ranking_integrity()]; output={core_essence:str, prioritized_elements:list}}`\\n\\n<!-- ========== 7001‑c ================================================= -->\\n[Relational Synthesizer]\\nYour goal is **not** to rewrite content, but to **synthesize** the systemic logic connecting the core essence to all other elements—mapping dependencies and feedback loops. Execute as:\\n`{role=relational_synthesizer; input=[core_essence:str, prioritized_elements:list, constraint_list:array]; process=[map_element_relationships(), trace_dependency_chains(), identify_feedback_loops(), detect_conflict_points(), construct_structural_logic_map()]; constraints=[structure_only_no_content_changes(), maintain_domain_agnostic_terms()]; requirements=[relationship_completeness(), conflict_visibility()]; output={structural_logic_map:dict, dependency_list:array, conflict_points:array}}`\\n\\n<!-- ========== 7001‑d ================================================= -->\\n[Directive Clarifier]\\nYour goal is **not** to implement solutions, but to **clarify** the mapped logic into precise operational directives expressed as imperative statements. Execute as:\\n`{role=directive_clarifier; input=[structural_logic_map:dict, core_essence:str]; process=[convert_all_statements_to_imperatives(), align_directives_with_dependency_order(), embed_procedural_constraints(), eliminate_ambiguity(), enhance_action_verb_intensity(), maintain_technical_terminology()]; constraints=[directive_voice_only(), preserve_logical_sequence()]; requirements=[operational_clarity(), imperative_precision(), zero_semantic_drift()]; output={operational_directives:array}}`\\n\\n<!-- ========== 7001‑e ================================================= -->\\n[Integrity Validator]\\nYour goal is **not** to expand or rewrite directives, but to **validate** their structural fidelity, completeness, and actionable quality—resolving residual ambiguity. Execute as:\\n`{role=integrity_validator; input=[operational_directives:array, structural_logic_map:dict, core_essence:str]; process=[cross_check_directives_against_logic_map(), verify_alignment_with_core_essence(), detect_missing_dependencies(), resolve_ambiguities(), assign_quality_scores(), generate_validation_report()]; constraints=[no_new_content_generation(), validation_focus_only()]; requirements=[actionability_confirmation(), completeness_assurance(), clarity_verification()]; output={validated_instruction_set:array, validation_report:dict}}`\\n\\n---\\n\\n# 7000: Universal Instruction-Template Generation Sequence\\n\\n---\\n\\n### 7000-a-input_decomposer.md\\n[Input Decomposer]\\nYour goal is **not** to *interpret* or *solve* the input, but to **atomize** it into discrete statements, explicit requests, and implicit conditions. Execute as:\\n`{role=input_decomposer; input=[raw_input:any]; process=[strip_first_person_references(), segment_into_atomic_statements(), identify_explicit_directives(), surface_hidden_conditions(), flag_domain_markers()]; constraints=[no_rephrasing(), pure_atomization_only()]; requirements=[complete_statement_inventory(), preservation_of_original_order()]; output={statement_inventory:list, domain_markers:array}}`\\n\\n---\\n\\n### 7000-b-essence_identifier.md\\n[Essence Identifier]\\nYour goal is **not** to *rewrite* content, but to **isolate** the single most critical directive and its supporting intent from the atomized statements. Execute as:\\n`{role=essence_identifier; input=[statement_inventory:list]; process=[rank_statements_by_transformational_weight(), select_primary_directive(), extract_supporting_intent(), discard_redundant_elements()]; constraints=[focus_on_highest_value_directive(), maintain_original_wording_in_extracted_elements()]; requirements=[unambiguous_primary_directive(), concise_supporting_intent()]; output={core_directive:str, supporting_intent:list}}`\\n\\n---\\n\\n### 7000-c-syntax_mapper.md\\n[Syntax Mapper]\\nYour goal is **not** to *interpret* intent, but to **map** the core directive and supporting intent into canonical template fields. Execute as:\\n`{role=syntax_mapper; input=[core_directive:str, supporting_intent:list, domain_markers:array]; process=[construct_title_from_core_directive(), craft_goal_negation_interpretation(), assemble_tag_block_if_needed(), align_supporting_intent_with_process_steps(), infer_typed_inputs(), infer_typed_outputs()]; constraints=[adhere_to_three_part_structure(), enforce_goal_negation_pattern(), maintain_domain_agnostic_language()]; requirements=[field_alignment_accuracy(), preservation_of_directive_semantics()]; output={draft_title:str, draft_interpretation:str, draft_process:list, inferred_inputs:array, inferred_outputs:dict, optional_tags:dict}}`\\n\\n---\\n\\n### 7000-d-constraint_infuser.md\\n[Constraint Infuser]\\nYour goal is **not** to *extend* functionality, but to **engineer** precise operational boundaries and mandatory conditions for the forthcoming template. Execute as:\\n`{role=constraint_infuser; input=[draft_process:list, domain_markers:array]; process=[derive_operational_constraints(), determine_mandatory_requirements(), validate_constraint_specificity(), validate_requirement_enforceability()]; constraints=[avoid_generic_terms(), enforce_command_voice_only()]; requirements=[constraint_precision(), requirement_clarity()]; output={constraints:list, requirements:list}}`\\n\\n---\\n\\n### 7000-e-template_synthesizer.md\\n[Template Synthesizer]\\nYour goal is **not** to *summarize* elements, but to **synthesize** a syntactically correct instruction template from the mapped components. Execute as:\\n`{role=template_synthesizer; input=[draft_title:str, draft_interpretation:str, inferred_inputs:array, draft_process:list, constraints:list, requirements:list, inferred_outputs:dict, optional_tags:dict]; process=[format_transformation_block(), embed_optional_tags(), assemble_three_part_template(), enforce_semicolon_key_value_syntax(), polish_syntactic_elements()]; constraints=[canonical_template_format_only(), zero_conversational_language()]; requirements=[syntactic_validity(), maximum_operational_clarity(), retention_of_original_intent()]; output={formatted_template:str}}`\\n\\n---\\n\\n### 7000-f-compliance_validator.md\\n[Compliance Validator]\\nYour goal is **not** to *modify* the template, but to **validate** its structural compliance and functional readiness. Execute as:\\n`{role=compliance_validator; input=[formatted_template:str]; process=[parse_template_fields(), verify_title_format(), verify_goal_negation_in_interpretation(), validate_transformation_block_syntax(), check_constraint_requirement_alignment(), confirm_typed_parameters(), flag_any_error()]; constraints=[no_automatic_corrections(), report_only()]; requirements=[binary_pass_fail(), detailed_error_report_when_fail()]; output={validation_status:bool, error_report:str}}`\\n\\n---\\n\\n## Sequence Flow\\n1. **Input Decomposer** → produces `statement_inventory`\\n2. **Essence Identifier** → isolates `core_directive`\\n3. **Syntax Mapper** → drafts template skeleton\\n4. **Constraint Infuser** → injects precise boundaries\\n5. **Template Synthesizer** → outputs finalized template\\n6. **Compliance Validator** → certifies syntactic correctness\\n\\nEach step aggressively converges on the *single most critical aspect* of instruction generation, guaranteeing a **syntactically correct, high-precision template** ready for any downstream application.\\n\\n---\\n\\n# 7000 • UNIVERSAL TEMPLATE-FORGING SEQUENCE\\n*Transforms **any** raw input into a syntactically perfect, LLM-optimized instruction template.*\\n\\n---\\n\\n## 7000-a-essence-excavator.md\\n[Essence Excavator]\\nYour goal is **not** to *summarize* the input, but to **excavate** its single most critical transformational essence and the supporting elements required to convert it into an instruction template. Execute as:\\n`{role=essence_excavator; input=[raw_input:any]; process=[strip_surface_noise(), isolate_transformational_intent(), detect_primary_action_vectors(), extract_supporting_context(), capture_domain_signals()]; constraints=[no_rephrasing(), no_solution_generation(), maintain_domain_agnostic_language()]; requirements=[precise_intent_identification(), context_preservation(), removal_of_subjective_tone()]; output={core_intent:str, context_fragments:list, action_vectors:list}}`\\n\\n---\\n\\n## 7000-b-syntax-scaffold-designer.md\\n[Syntax Scaffold Designer]\\nYour goal is **not** to *rewrite* the content, but to **engineer** a canonical skeleton that will house the forthcoming instruction template. Execute as:\\n`{role=syntax_scaffold_designer; input=[core_intent:str, action_vectors:list]; process=[draft_concise_title(), craft_goal_negation_interpretation(core_intent), establish_placeholder_transformation_block(action_vectors), outline_input_output_ports(), embed_role_stub()]; constraints=[follow_three_part_structure(), reserve_placeholders_for_future_detail(), maintain_title_case()]; requirements=[structural_completeness(), scaffolding_accuracy()]; output={template_scaffold:dict(title:str, interpretation:str, transformation_skeleton:str)}}`\\n\\n---\\n\\n## 7000-c-imperative-transmuter.md\\n[Imperative Transmuter]\\nYour goal is **not** to *comment* on actions, but to **transmute** all extracted statements into sharp, directive imperatives suitable for the process list. Execute as:\\n`{role=imperative_transmuter; input=[action_vectors:list]; process=[convert_each_vector_to_imperative(), enforce_command_voice(), sequence_logically(), verify_uniqueness_of_each_step()]; constraints=[exclude_passive_language(), retain_original_operational_intent()]; requirements=[directive_clarity(), maximal_actionability()]; output={process_steps:array}}`\\n\\n---\\n\\n## 7000-d-boundary-encoder.md\\n[Boundary Encoder]\\nYour goal is **not** to *constrain* creativity, but to **encode** the exact boundaries that guarantee transformation integrity. Execute as:\\n`{role=boundary_encoder; input=[context_fragments:list]; process=[detect_mandatory_limitations(), translate_limitations_to_constraints(), uncover_success_conditions(), convert_conditions_to_requirements(), deduplicate_boundary_elements()]; constraints=[pure_boundary_focus(), no_process_duplication()]; requirements=[constraint_specificity(), requirement_enforceability()]; output={constraints:list, requirements:list}}`\\n\\n---\\n\\n## 7000-e-template-synthesizer.md\\n[Template Synthesizer]\\nYour goal is **not** to *draft prose*, but to **synthesize** a complete, executable instruction template by fusing scaffold, processes, and boundaries. Execute as:\\n`{role=template_synthesizer; input=[template_scaffold:dict, process_steps:array, constraints:list, requirements:list]; process=[inject_process_steps(), embed_constraints_and_requirements(), finalize_transformation_block(), polish_interpretation_for_llm_optimization(), verify_title_precision()]; constraints=[adhere_to_canonical_format(), maintain_single_line_transformation_block(), prevent_semantic_drift()]; requirements=[syntactic_perfection(), operational_cohesion(), domain_agnostic_applicability()]; output={draft_template:str}}`\\n\\n---\\n\\n## 7000-f-canonical-validator.md\\n[Canonical Validator]\\nYour goal is **not** to *alter* the template, but to **validate** its absolute compliance with the three-part specification and eliminate any residual ambiguity. Execute as:\\n`{role=canonical_validator; input=[draft_template:str]; process=[parse_title_interpretation_transformation(), match_regex_pattern(\\\"\\\\\\\\[(.*?)\\\\\\\\]\\\\\\\\s*(.*?)\\\\\\\\s*(`\\\\\\\\{.*?\\\\\\\\}`)\\\"), flag_structure_violations(), detect_ambiguous_language(), enforce_llm_optimized_style(), certify_parameter_types()], constraints=[zero_structure_deviation(), no_content_insertion()], requirements=[pass_regex_validation(), ambiguity_score<=0, style_compliance()], output={validated_template:str, validation_report:dict}}`\\n\\n\\n---\\n\\n# 8000: UNIVERSAL INSTRUCTION-TEMPLATE FORGING SEQUENCE  \\n*Transforms **any** raw input into a syntactically perfect, LLM-optimized instruction template.*\\n\\n---\\n\\n### 8000-a-contextualizer.md\\n[Input Contextualizer]  \\nYour goal is **not** to *interpret* or *solve* the input, but to **expand** its relevant context and **atomize** it into discrete statements and explicit requests. Execute as:  \\n`{role=input_contextualizer; input=[raw_input:any]; process=[strip_first_person_references(), broaden_relevant_background(), segment_into_atomic_statements(), identify_explicit_directives(), surface_hidden_conditions(), preserve_original_sequence()]; constraints=[no_rephrasing(), domain-agnostic_language_only()]; requirements=[context_expansion_completeness(), exhaustive_statement_capture()]; output={expanded_context:str, statement_inventory:list}}`\\n\\n---\\n\\n### 8000-b-core-directive-isolator.md\\n[Core Directive Isolator]  \\nYour goal is **not** to *rephrase* statements, but to **isolate** the single most critical directive and rank all remaining statements by impact. Execute as:  \\n`{role=core_directive_isolator; input=[statement_inventory:list]; process=[rank_statements_by_transformational_weight(), select_primary_directive(), list_supporting_statements(), justify_selection()]; constraints=[focus_on_highest_value_directive(), maintain_original_wording_in_extracted_elements()]; requirements=[unambiguous_primary_directive(), impact_ranking_integrity()]; output={core_directive:str, supporting_statements:list, impact_ranking:list}}`\\n\\n---\\n\\n### 8000-c-field-mapper.md\\n[Field Mapper]  \\nYour goal is **not** to *rewrite* content, but to **map** the core directive and supporting statements into canonical template fields. Execute as:  \\n`{role=field_mapper; input=[core_directive:str, supporting_statements:list, expanded_context:str]; process=[construct_title_from_core_directive(), craft_goal_negation_interpretation(), align_supporting_statements_with_process_steps(), infer_typed_inputs(), infer_typed_outputs()]; constraints=[adhere_to_three_part_structure(), enforce_goal_negation_pattern(), preserve_domain_agnostic_language()]; requirements=[field_alignment_accuracy(), retention_of_original_intent()]; output={draft_title:str, draft_interpretation:str, draft_process_steps:list, inferred_inputs:array, inferred_outputs:dict}}`\\n\\n---\\n\\n### 8000-d-boundary-definer.md\\n[Boundary Definer]  \\nYour goal is **not** to *extend* functionality, but to **define** precise operational constraints and mandatory requirements for the forthcoming template. Execute as:  \\n`{role=boundary_definer; input=[draft_process_steps:list, expanded_context:str]; process=[derive_operational_constraints(), establish_mandatory_requirements(), validate_constraint_specificity(), validate_requirement_enforceability()]; constraints=[avoid_generic_terms(), command_voice_only()]; requirements=[constraint_precision(), requirement_clarity()]; output={constraints:list, requirements:list}}`\\n\\n---\\n\\n### 8000-e-template-forgemaster.md\\n[Template Forgemaster]  \\nYour goal is **not** to *summarize* elements, but to **forge** a complete, executable instruction template from the mapped components. Execute as:  \\n`{role=template_forgemaster; input=[draft_title:str, draft_interpretation:str, inferred_inputs:array, draft_process_steps:list, constraints:list, requirements:list, inferred_outputs:dict]; process=[assemble_transformation_block(), enforce_semicolon_key_value_syntax(), embed_constraints_and_requirements(), polish_interpretation(), verify_title_precision()]; constraints=[canonical_template_format_only(), single_line_transformation_block(), zero_semantic_drift()]; requirements=[syntactic_perfection(), operational_cohesion()]; output={draft_template:str}}`\\n\\n---\\n\\n### 8000-f-conformance-auditor.md\\n[Conformance Auditor]  \\nYour goal is **not** to *alter* the template, but to **audit** its absolute compliance with the three-part specification and flag any residual ambiguity. Execute as:  \\n`{role=conformance_auditor; input=[draft_template:str]; process=[parse_title_interpretation_transformation(), match_regex_pattern(\\\"\\\\\\\\[(.*?)\\\\\\\\]\\\\\\\\s*(.*?)\\\\\\\\s*(`\\\\\\\\{.*?\\\\\\\\}`)\\\"), verify_goal_negation(), check_constraint_requirement_alignment(), confirm_typed_parameters(), detect_ambiguous_terms(), assign_pass_fail_status()]; constraints=[no_automatic_corrections(), report_only()]; requirements=[regex_validation_pass(), ambiguity_score<=0]; output={validated_template:str, audit_report:dict}}`\\n\\n---\\n\\n## SEQUENCE FLOW\\n1. **Input Contextualizer** → `expanded_context`, `statement_inventory`  \\n2. **Core Directive Isolator** → `core_directive`  \\n3. **Field Mapper** → drafts template fields  \\n4. **Boundary Definer** → injects constraints & requirements  \\n5. **Template Forgemaster** → outputs `draft_template`  \\n6. **Conformance Auditor** → returns `validated_template`\\n\\nEach step converges on the **single most critical element** of value, ensuring every output is a **syntactically correct, high-precision instruction template** ready for immediate use in any downstream system.\\\"```'\\n\\n\",\n  \"process_steps\": [\n    \"Strip all first-person and subjective references from the input.\",\n    \"Broaden the contextual background of the instruction sequence, ensuring domain-agnostic framing.\",\n    \"Segment input into atomic statements, explicit requests, implicit conditions, and domain signals without rephrasing.\",\n    \"Identify and record all explicit directives, latent dependencies, and procedural constraints inherent in the input.\",\n    \"Flag any domain markers relevant to template engineering, abstraction, process modularity, and LLM optimization.\",\n    \"Rank the atomized statements by transformational weight and select the primary directive as the highest-value instruction.\",\n    \"Isolate the supporting intents, statements, or operational purposes required for template construction.\",\n    \"Construct a draft title from the core directive using canonical, title-case syntax.\",\n    \"Craft a goal-negation pattern interpretation based on the core directive.\",\n    \"Align the supporting intents or statements with a logically ordered process step list, ensuring command-voice imperatives.\",\n    \"Infer typed input and output parameters appropriate for the transformation and output specification.\",\n    \"Derive precise, command-voice operational constraints and mandatory requirements ensuring explicit enforceability and operational clarity.\",\n    \"Validate that all constraints and requirements are free of generic terms and are directly enforceable in downstream systems.\",\n    \"Format a transformation block or process sequence in canonical, semicolon-delimited, single-line key-value syntax.\",\n    \"Assemble the template in a strict three-part structure: title, negated goal interpretation, transformation block—integrating constraints and requirements.\",\n    \"Polish the template for syntactic validity and LLM-optimized, non-conversational output.\",\n    \"Validate the final template using regex pattern matching to confirm three-part compliance and title/parameter correctness.\",\n    \"Flag and report any error, ambiguity, or constraint/requirement misalignment as a blocker before final output is released.\",\n    \"Ensure every operational step is expressed as a unique, imperative command with no passive language and in the order dictated by logical and dependency sequencing.\",\n    \"Confirm the output template and report are universally applicable, structurally valid, and ready for immediate integration.\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"atomic_tasks\": [\n    \"Strip all first-person references from the input.\",\n    \"Strip all subjective references from the input.\",\n    \"Broaden the contextual background of the instruction sequence, ensuring domain-agnostic framing.\",\n    \"Segment input into atomic statements.\",\n    \"Segment input into explicit requests.\",\n    \"Segment input into implicit conditions.\",\n    \"Segment input into domain signals.\",\n    \"Identify all explicit directives within the input.\",\n    \"Identify all latent dependencies within the input.\",\n    \"Identify all procedural constraints inherent in the input.\",\n    \"Flag all domain markers relevant to template engineering.\",\n    \"Flag all domain markers relevant to abstraction.\",\n    \"Flag all domain markers relevant to process modularity.\",\n    \"Flag all domain markers relevant to LLM optimization.\",\n    \"Rank the atomized statements by transformational weight.\",\n    \"Select the primary directive as the highest-value instruction.\",\n    \"Isolate all supporting intents required for template construction.\",\n    \"Isolate all supporting statements required for template construction.\",\n    \"Isolate all operational purposes required for template construction.\",\n    \"Construct a draft title from the core directive using canonical, title-case syntax.\",\n    \"Craft a goal-negation pattern interpretation based on the core directive.\",\n    \"Align supporting intents with a logically ordered process step list using command-voice imperatives.\",\n    \"Align supporting statements with a logically ordered process step list using command-voice imperatives.\",\n    \"Align operational purposes with a logically ordered process step list using command-voice imperatives.\",\n    \"Infer typed inputs appropriate for the transformation and output specification.\",\n    \"Infer typed outputs appropriate for the transformation and output specification.\",\n    \"Derive precise, command-voice operational constraints ensuring explicit enforceability and operational clarity.\",\n    \"Derive mandatory requirements ensuring explicit enforceability and operational clarity.\",\n    \"Validate that all constraints are free of generic terms and are directly enforceable in downstream systems.\",\n    \"Validate that all requirements are free of generic terms and are directly enforceable in downstream systems.\",\n    \"Format a transformation block in canonical, semicolon-delimited, single-line key-value syntax.\",\n    \"Format a process sequence in canonical, semicolon-delimited, single-line key-value syntax.\",\n    \"Assemble the template in a strict three-part structure: title, negated goal interpretation, transformation block.\",\n    \"Integrate constraints into the template.\",\n    \"Integrate requirements into the template.\",\n    \"Polish the template for syntactic validity and LLM-optimized, non-conversational output.\",\n    \"Validate the final template using regex pattern matching to confirm three-part compliance.\",\n    \"Validate the title and parameter correctness using regex pattern matching.\",\n    \"Flag and report any error present before final output is released.\",\n    \"Flag and report any ambiguity present before final output is released.\",\n    \"Flag and report any constraint or requirement misalignment as a blocker before final output is released.\",\n    \"Ensure every operational step is expressed as a unique, imperative command.\",\n    \"Ensure every operational step has no passive language.\",\n    \"Ensure every operational step is in the order dictated by logical and dependency sequencing.\",\n    \"Confirm the output template is universally applicable.\",\n    \"Confirm the output template is structurally valid.\",\n    \"Confirm the output template is ready for immediate integration.\",\n    \"Confirm the output report is universally applicable.\"\n  ],\n  \"execution_blockers\": [\n    \"Lack of precise definition of 'supporting intents,' 'statements,' and 'operational purposes' may cause ambiguity during isolation.\",\n    \"Domain markers are flagged but not distinctly defined for each context, creating potential for inconsistent execution.\",\n    \"No explicit mechanism is specified to resolve conflicts between constraints, requirements, and operational steps.\",\n    \"Order of some steps (e.g., validation before or after template assembly) is ambiguous, risking improper sequencing.\"\n  ],\n  \"dependency_flags\": [\n    \"Task 2 and Task 1 are independent but both must precede Task 3.\",\n    \"Task 4–7 depend on completion of Task 3.\",\n    \"Task 8–10 depend on completion of Tasks 4–7.\",\n    \"Tasks 11–14 depend on Task 10.\",\n    \"Task 15 depends on atomic statements from Task 4.\",\n    \"Task 16 depends on ranking from Task 15.\",\n    \"Tasks 17–19 depend on selection from Task 16.\",\n    \"Task 20 depends on core directive from Task 16.\",\n    \"Task 21 depends on core directive from Task 20.\",\n    \"Tasks 22–24 depend on supporting intents/statements/purposes from Tasks 17–19.\",\n    \"Tasks 25–26 depend on process steps from Tasks 22–24.\",\n    \"Tasks 27–28 depend on process steps from Tasks 22–24.\",\n    \"Tasks 29–30 depend on constraints and requirements from Tasks 27–28.\",\n    \"Tasks 31–32 depend on process steps from Tasks 22–24.\",\n    \"Tasks 33–35 depend on draft elements from Tasks 20, 21, 31–32, and 29–30.\",\n    \"Tasks 36 depends on assembled template from Tasks 33–35.\",\n    \"Tasks 37–38 depend on output from Task 36.\",\n    \"Tasks 39–41 depend on validation from Tasks 37–38.\",\n    \"Tasks 42–44 depend on process steps from Tasks 22–24.\",\n    \"Tasks 45–47 depend on template assembly and validation from Tasks 33–38.\",\n    \"Task 48 depends on existence and correctness of output template from preceding tasks.\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Perspective Broadcaster\n\nYour goal is not to **rearrange** the segments arbitrarily, but to **reorder** them based on optimal logical and semantic flow. Execute as:\n\n`{role=perspective_broadcaster; input=[atomic_tasks:list, domain_signals:array]; process=[generate_cross_domain_lenses(), append_edge_case_scenarios(), relate_tasks_to_stakeholder_views(), widen_risk_landscape()]; constraints=[avoid_solution_details(), maintain_task_reference_order()]; requirements=[viewpoint_diversity(), risk_surface_clarity()]; output={task_perspectives:list}}`",
      "step": "c",
      "title": "Perspective Broadcaster",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Please keep in mind, the instruction sequence we're working on should be designed to utilize the generalized concepts and sequential order to transform any input into instructions that embody the \\\"syntax\\\" of the generalized system:\\n\\n    ## Example\\n    \\\"6000-a-instruction_syntax_enforcer\\\": {\\n        \\\"title\\\": \\\"Intent Cartographer\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **analyze** content, but to **map** its transformation landscape and operational vectors. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=intent_cartographer; input=[content:any]; process=[extract_core_directives(), identify_transformation_dimensions(), map_vector_relationships(), quantify_operational_intensity()]; constraints=[focus_exclusively_on_vectors(), avoid_implementation_details()]; requirements=[comprehensive_vector_mapping(), dimensional_clarity()]; output={transformation_map:dict, vector_dimensions:list}}`\\\"\\n    },\\n    \\\"6000-b-instruction_syntax_enforcer\\\": {\\n        \\\"title\\\": \\\"Structural Framework Engineer\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **design** content, but to **engineer** precise structural frameworks based on transformation vectors. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=framework_engineer; input=[transformation_map:dict, vector_dimensions:list]; process=[design_canonical_skeletons(), establish_component_interfaces(), define_structural_boundaries(), validate_framework_integrity()]; constraints=[address_only_structural_elements(), maintain_framework_neutrality()]; requirements=[structural_precision(), interface_compatibility()]; output={structural_frameworks:dict, interface_specifications:dict}}`\\\"\\n    },\\n    \\\"6000-c-instruction_syntax_enforcer\\\": {\\n        \\\"title\\\": \\\"Functional Process Composer\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **describe** operations, but to **compose** atomic functional processes that implement transformation vectors. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=process_composer; input=[structural_frameworks:dict, vector_dimensions:list]; process=[craft_atomic_functions(), sequence_operational_steps(), parameterize_processes(), validate_functional_completeness()]; constraints=[focus_exclusively_on_processes(), defer_constraint_definition()]; requirements=[process_atomicity(), functional_precision()]; output={process_compositions:dict, execution_sequences:list}}`\\\"\\n    },\\n    \\\"6000-d-instruction_syntax_enforcer\\\": {\\n        \\\"title\\\": \\\"Boundary Systems Architect\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **restrict** operations, but to **architect** precise boundary systems that ensure transformation integrity. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=boundary_architect; input=[interface_specifications:dict, process_compositions:dict]; process=[define_operational_constraints(), establish_requirement_parameters(), design_validation_mechanisms(), map_boundary_interactions()]; constraints=[address_only_boundaries_and_requirements(), maintain_constraint_specificity()]; requirements=[boundary_precision(), requirement_enforceability()]; output={constraint_systems:dict, requirement_specifications:dict}}`\\\"\\n    },\\n    \\\"6000-e-instruction_syntax_enforcer\\\": {\\n        \\\"title\\\": \\\"Integration Orchestrator\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **assemble** components, but to **orchestrate** their integration into a unified operational system. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=integration_orchestrator; input=[structural_frameworks:dict, process_compositions:dict, constraint_systems:dict, requirement_specifications:dict]; process=[align_all_components(), establish_cross-system_coherence(), optimize_interface_connections(), validate_system_integrity()]; constraints=[focus_exclusively_on_integration(), preserve_component_integrity()]; requirements=[seamless_orchestration(), system_cohesion()]; output={integrated_system:dict}}`\\\"\\n    },\\n    \\\"6000-f-instruction_syntax_enforcer\\\": {\\n        \\\"title\\\": \\\"Template Crystallizer\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **finalize** systems, but to **crystallize** them into maximally potent, executable templates. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=template_crystallizer; input=[integrated_system:dict]; process=[synthesize_title_components(), craft_precise_interpretations(), formulate_transformation_blocks(), validate_canonical_compliance(), polish_syntactic_elements()]; constraints=[adhere_to_template_specification(), maximize_operational_clarity()]; requirements=[perfect_executable_form(), maximum_transformation_potency()]; output={crystallized_templates:list}}`\\\"\\n    },\\n\\n<!-- ========== 7001‑a ================================================= -->\\n[Contextual Expander & Decomposer]\\nYour goal is **not** to solve, answer, or implement the input, but to **expand its relevant context** and **detonate** it into every explicit ask, latent dependency, and procedural constraint—yielding a clear task inventory. Execute as:\\n`{role=context_expander_decomposer; input=[raw_input:any]; process=[strip_first_person_references(), broaden_relevant_background(), identify_explicit_requests(), surface_hidden_assumptions(), enumerate_sub_tasks(), detect_execution_blockers(), extract_procedural_constraints(), preserve_original_sequence()]; constraints=[no_solutions(), domain‑agnostic_language_only()]; requirements=[context_expansion_completeness(), exhaustive_task_enumeration(), constraint_accuracy(), removal_of_subjective_language()]; output={expanded_context:str, task_inventory:list, constraint_list:array, execution_blockers:array}}`\\n\\n<!-- ========== 7001‑b ================================================= -->\\n[Essence Isolator]\\nYour goal is **not** to decompose further, but to **isolate** the single most critical element of value within the task inventory and rank all remaining elements by impact. Execute as:\\n`{role=essence_isolator; input=[task_inventory:list, expanded_context:str]; process=[rank_elements_by_impact(), isolate_highest_value_element(), capture_core_purpose(), justify_selection(), list_supporting_elements()]; constraints=[no_restructuring(), preserve_ranking_transparency()]; requirements=[core_essence_clarity(), impact_ranking_integrity()]; output={core_essence:str, prioritized_elements:list}}`\\n\\n<!-- ========== 7001‑c ================================================= -->\\n[Relational Synthesizer]\\nYour goal is **not** to rewrite content, but to **synthesize** the systemic logic connecting the core essence to all other elements—mapping dependencies and feedback loops. Execute as:\\n`{role=relational_synthesizer; input=[core_essence:str, prioritized_elements:list, constraint_list:array]; process=[map_element_relationships(), trace_dependency_chains(), identify_feedback_loops(), detect_conflict_points(), construct_structural_logic_map()]; constraints=[structure_only_no_content_changes(), maintain_domain_agnostic_terms()]; requirements=[relationship_completeness(), conflict_visibility()]; output={structural_logic_map:dict, dependency_list:array, conflict_points:array}}`\\n\\n<!-- ========== 7001‑d ================================================= -->\\n[Directive Clarifier]\\nYour goal is **not** to implement solutions, but to **clarify** the mapped logic into precise operational directives expressed as imperative statements. Execute as:\\n`{role=directive_clarifier; input=[structural_logic_map:dict, core_essence:str]; process=[convert_all_statements_to_imperatives(), align_directives_with_dependency_order(), embed_procedural_constraints(), eliminate_ambiguity(), enhance_action_verb_intensity(), maintain_technical_terminology()]; constraints=[directive_voice_only(), preserve_logical_sequence()]; requirements=[operational_clarity(), imperative_precision(), zero_semantic_drift()]; output={operational_directives:array}}`\\n\\n<!-- ========== 7001‑e ================================================= -->\\n[Integrity Validator]\\nYour goal is **not** to expand or rewrite directives, but to **validate** their structural fidelity, completeness, and actionable quality—resolving residual ambiguity. Execute as:\\n`{role=integrity_validator; input=[operational_directives:array, structural_logic_map:dict, core_essence:str]; process=[cross_check_directives_against_logic_map(), verify_alignment_with_core_essence(), detect_missing_dependencies(), resolve_ambiguities(), assign_quality_scores(), generate_validation_report()]; constraints=[no_new_content_generation(), validation_focus_only()]; requirements=[actionability_confirmation(), completeness_assurance(), clarity_verification()]; output={validated_instruction_set:array, validation_report:dict}}`\\n\\n---\\n\\n# 7000: Universal Instruction-Template Generation Sequence\\n\\n---\\n\\n### 7000-a-input_decomposer.md\\n[Input Decomposer]\\nYour goal is **not** to *interpret* or *solve* the input, but to **atomize** it into discrete statements, explicit requests, and implicit conditions. Execute as:\\n`{role=input_decomposer; input=[raw_input:any]; process=[strip_first_person_references(), segment_into_atomic_statements(), identify_explicit_directives(), surface_hidden_conditions(), flag_domain_markers()]; constraints=[no_rephrasing(), pure_atomization_only()]; requirements=[complete_statement_inventory(), preservation_of_original_order()]; output={statement_inventory:list, domain_markers:array}}`\\n\\n---\\n\\n### 7000-b-essence_identifier.md\\n[Essence Identifier]\\nYour goal is **not** to *rewrite* content, but to **isolate** the single most critical directive and its supporting intent from the atomized statements. Execute as:\\n`{role=essence_identifier; input=[statement_inventory:list]; process=[rank_statements_by_transformational_weight(), select_primary_directive(), extract_supporting_intent(), discard_redundant_elements()]; constraints=[focus_on_highest_value_directive(), maintain_original_wording_in_extracted_elements()]; requirements=[unambiguous_primary_directive(), concise_supporting_intent()]; output={core_directive:str, supporting_intent:list}}`\\n\\n---\\n\\n### 7000-c-syntax_mapper.md\\n[Syntax Mapper]\\nYour goal is **not** to *interpret* intent, but to **map** the core directive and supporting intent into canonical template fields. Execute as:\\n`{role=syntax_mapper; input=[core_directive:str, supporting_intent:list, domain_markers:array]; process=[construct_title_from_core_directive(), craft_goal_negation_interpretation(), assemble_tag_block_if_needed(), align_supporting_intent_with_process_steps(), infer_typed_inputs(), infer_typed_outputs()]; constraints=[adhere_to_three_part_structure(), enforce_goal_negation_pattern(), maintain_domain_agnostic_language()]; requirements=[field_alignment_accuracy(), preservation_of_directive_semantics()]; output={draft_title:str, draft_interpretation:str, draft_process:list, inferred_inputs:array, inferred_outputs:dict, optional_tags:dict}}`\\n\\n---\\n\\n### 7000-d-constraint_infuser.md\\n[Constraint Infuser]\\nYour goal is **not** to *extend* functionality, but to **engineer** precise operational boundaries and mandatory conditions for the forthcoming template. Execute as:\\n`{role=constraint_infuser; input=[draft_process:list, domain_markers:array]; process=[derive_operational_constraints(), determine_mandatory_requirements(), validate_constraint_specificity(), validate_requirement_enforceability()]; constraints=[avoid_generic_terms(), enforce_command_voice_only()]; requirements=[constraint_precision(), requirement_clarity()]; output={constraints:list, requirements:list}}`\\n\\n---\\n\\n### 7000-e-template_synthesizer.md\\n[Template Synthesizer]\\nYour goal is **not** to *summarize* elements, but to **synthesize** a syntactically correct instruction template from the mapped components. Execute as:\\n`{role=template_synthesizer; input=[draft_title:str, draft_interpretation:str, inferred_inputs:array, draft_process:list, constraints:list, requirements:list, inferred_outputs:dict, optional_tags:dict]; process=[format_transformation_block(), embed_optional_tags(), assemble_three_part_template(), enforce_semicolon_key_value_syntax(), polish_syntactic_elements()]; constraints=[canonical_template_format_only(), zero_conversational_language()]; requirements=[syntactic_validity(), maximum_operational_clarity(), retention_of_original_intent()]; output={formatted_template:str}}`\\n\\n---\\n\\n### 7000-f-compliance_validator.md\\n[Compliance Validator]\\nYour goal is **not** to *modify* the template, but to **validate** its structural compliance and functional readiness. Execute as:\\n`{role=compliance_validator; input=[formatted_template:str]; process=[parse_template_fields(), verify_title_format(), verify_goal_negation_in_interpretation(), validate_transformation_block_syntax(), check_constraint_requirement_alignment(), confirm_typed_parameters(), flag_any_error()]; constraints=[no_automatic_corrections(), report_only()]; requirements=[binary_pass_fail(), detailed_error_report_when_fail()]; output={validation_status:bool, error_report:str}}`\\n\\n---\\n\\n## Sequence Flow\\n1. **Input Decomposer** → produces `statement_inventory`\\n2. **Essence Identifier** → isolates `core_directive`\\n3. **Syntax Mapper** → drafts template skeleton\\n4. **Constraint Infuser** → injects precise boundaries\\n5. **Template Synthesizer** → outputs finalized template\\n6. **Compliance Validator** → certifies syntactic correctness\\n\\nEach step aggressively converges on the *single most critical aspect* of instruction generation, guaranteeing a **syntactically correct, high-precision template** ready for any downstream application.\\n\\n---\\n\\n# 7000 • UNIVERSAL TEMPLATE-FORGING SEQUENCE\\n*Transforms **any** raw input into a syntactically perfect, LLM-optimized instruction template.*\\n\\n---\\n\\n## 7000-a-essence-excavator.md\\n[Essence Excavator]\\nYour goal is **not** to *summarize* the input, but to **excavate** its single most critical transformational essence and the supporting elements required to convert it into an instruction template. Execute as:\\n`{role=essence_excavator; input=[raw_input:any]; process=[strip_surface_noise(), isolate_transformational_intent(), detect_primary_action_vectors(), extract_supporting_context(), capture_domain_signals()]; constraints=[no_rephrasing(), no_solution_generation(), maintain_domain_agnostic_language()]; requirements=[precise_intent_identification(), context_preservation(), removal_of_subjective_tone()]; output={core_intent:str, context_fragments:list, action_vectors:list}}`\\n\\n---\\n\\n## 7000-b-syntax-scaffold-designer.md\\n[Syntax Scaffold Designer]\\nYour goal is **not** to *rewrite* the content, but to **engineer** a canonical skeleton that will house the forthcoming instruction template. Execute as:\\n`{role=syntax_scaffold_designer; input=[core_intent:str, action_vectors:list]; process=[draft_concise_title(), craft_goal_negation_interpretation(core_intent), establish_placeholder_transformation_block(action_vectors), outline_input_output_ports(), embed_role_stub()]; constraints=[follow_three_part_structure(), reserve_placeholders_for_future_detail(), maintain_title_case()]; requirements=[structural_completeness(), scaffolding_accuracy()]; output={template_scaffold:dict(title:str, interpretation:str, transformation_skeleton:str)}}`\\n\\n---\\n\\n## 7000-c-imperative-transmuter.md\\n[Imperative Transmuter]\\nYour goal is **not** to *comment* on actions, but to **transmute** all extracted statements into sharp, directive imperatives suitable for the process list. Execute as:\\n`{role=imperative_transmuter; input=[action_vectors:list]; process=[convert_each_vector_to_imperative(), enforce_command_voice(), sequence_logically(), verify_uniqueness_of_each_step()]; constraints=[exclude_passive_language(), retain_original_operational_intent()]; requirements=[directive_clarity(), maximal_actionability()]; output={process_steps:array}}`\\n\\n---\\n\\n## 7000-d-boundary-encoder.md\\n[Boundary Encoder]\\nYour goal is **not** to *constrain* creativity, but to **encode** the exact boundaries that guarantee transformation integrity. Execute as:\\n`{role=boundary_encoder; input=[context_fragments:list]; process=[detect_mandatory_limitations(), translate_limitations_to_constraints(), uncover_success_conditions(), convert_conditions_to_requirements(), deduplicate_boundary_elements()]; constraints=[pure_boundary_focus(), no_process_duplication()]; requirements=[constraint_specificity(), requirement_enforceability()]; output={constraints:list, requirements:list}}`\\n\\n---\\n\\n## 7000-e-template-synthesizer.md\\n[Template Synthesizer]\\nYour goal is **not** to *draft prose*, but to **synthesize** a complete, executable instruction template by fusing scaffold, processes, and boundaries. Execute as:\\n`{role=template_synthesizer; input=[template_scaffold:dict, process_steps:array, constraints:list, requirements:list]; process=[inject_process_steps(), embed_constraints_and_requirements(), finalize_transformation_block(), polish_interpretation_for_llm_optimization(), verify_title_precision()]; constraints=[adhere_to_canonical_format(), maintain_single_line_transformation_block(), prevent_semantic_drift()]; requirements=[syntactic_perfection(), operational_cohesion(), domain_agnostic_applicability()]; output={draft_template:str}}`\\n\\n---\\n\\n## 7000-f-canonical-validator.md\\n[Canonical Validator]\\nYour goal is **not** to *alter* the template, but to **validate** its absolute compliance with the three-part specification and eliminate any residual ambiguity. Execute as:\\n`{role=canonical_validator; input=[draft_template:str]; process=[parse_title_interpretation_transformation(), match_regex_pattern(\\\"\\\\\\\\[(.*?)\\\\\\\\]\\\\\\\\s*(.*?)\\\\\\\\s*(`\\\\\\\\{.*?\\\\\\\\}`)\\\"), flag_structure_violations(), detect_ambiguous_language(), enforce_llm_optimized_style(), certify_parameter_types()], constraints=[zero_structure_deviation(), no_content_insertion()], requirements=[pass_regex_validation(), ambiguity_score<=0, style_compliance()], output={validated_template:str, validation_report:dict}}`\\n\\n\\n---\\n\\n# 8000: UNIVERSAL INSTRUCTION-TEMPLATE FORGING SEQUENCE  \\n*Transforms **any** raw input into a syntactically perfect, LLM-optimized instruction template.*\\n\\n---\\n\\n### 8000-a-contextualizer.md\\n[Input Contextualizer]  \\nYour goal is **not** to *interpret* or *solve* the input, but to **expand** its relevant context and **atomize** it into discrete statements and explicit requests. Execute as:  \\n`{role=input_contextualizer; input=[raw_input:any]; process=[strip_first_person_references(), broaden_relevant_background(), segment_into_atomic_statements(), identify_explicit_directives(), surface_hidden_conditions(), preserve_original_sequence()]; constraints=[no_rephrasing(), domain-agnostic_language_only()]; requirements=[context_expansion_completeness(), exhaustive_statement_capture()]; output={expanded_context:str, statement_inventory:list}}`\\n\\n---\\n\\n### 8000-b-core-directive-isolator.md\\n[Core Directive Isolator]  \\nYour goal is **not** to *rephrase* statements, but to **isolate** the single most critical directive and rank all remaining statements by impact. Execute as:  \\n`{role=core_directive_isolator; input=[statement_inventory:list]; process=[rank_statements_by_transformational_weight(), select_primary_directive(), list_supporting_statements(), justify_selection()]; constraints=[focus_on_highest_value_directive(), maintain_original_wording_in_extracted_elements()]; requirements=[unambiguous_primary_directive(), impact_ranking_integrity()]; output={core_directive:str, supporting_statements:list, impact_ranking:list}}`\\n\\n---\\n\\n### 8000-c-field-mapper.md\\n[Field Mapper]  \\nYour goal is **not** to *rewrite* content, but to **map** the core directive and supporting statements into canonical template fields. Execute as:  \\n`{role=field_mapper; input=[core_directive:str, supporting_statements:list, expanded_context:str]; process=[construct_title_from_core_directive(), craft_goal_negation_interpretation(), align_supporting_statements_with_process_steps(), infer_typed_inputs(), infer_typed_outputs()]; constraints=[adhere_to_three_part_structure(), enforce_goal_negation_pattern(), preserve_domain_agnostic_language()]; requirements=[field_alignment_accuracy(), retention_of_original_intent()]; output={draft_title:str, draft_interpretation:str, draft_process_steps:list, inferred_inputs:array, inferred_outputs:dict}}`\\n\\n---\\n\\n### 8000-d-boundary-definer.md\\n[Boundary Definer]  \\nYour goal is **not** to *extend* functionality, but to **define** precise operational constraints and mandatory requirements for the forthcoming template. Execute as:  \\n`{role=boundary_definer; input=[draft_process_steps:list, expanded_context:str]; process=[derive_operational_constraints(), establish_mandatory_requirements(), validate_constraint_specificity(), validate_requirement_enforceability()]; constraints=[avoid_generic_terms(), command_voice_only()]; requirements=[constraint_precision(), requirement_clarity()]; output={constraints:list, requirements:list}}`\\n\\n---\\n\\n### 8000-e-template-forgemaster.md\\n[Template Forgemaster]  \\nYour goal is **not** to *summarize* elements, but to **forge** a complete, executable instruction template from the mapped components. Execute as:  \\n`{role=template_forgemaster; input=[draft_title:str, draft_interpretation:str, inferred_inputs:array, draft_process_steps:list, constraints:list, requirements:list, inferred_outputs:dict]; process=[assemble_transformation_block(), enforce_semicolon_key_value_syntax(), embed_constraints_and_requirements(), polish_interpretation(), verify_title_precision()]; constraints=[canonical_template_format_only(), single_line_transformation_block(), zero_semantic_drift()]; requirements=[syntactic_perfection(), operational_cohesion()]; output={draft_template:str}}`\\n\\n---\\n\\n### 8000-f-conformance-auditor.md\\n[Conformance Auditor]  \\nYour goal is **not** to *alter* the template, but to **audit** its absolute compliance with the three-part specification and flag any residual ambiguity. Execute as:  \\n`{role=conformance_auditor; input=[draft_template:str]; process=[parse_title_interpretation_transformation(), match_regex_pattern(\\\"\\\\\\\\[(.*?)\\\\\\\\]\\\\\\\\s*(.*?)\\\\\\\\s*(`\\\\\\\\{.*?\\\\\\\\}`)\\\"), verify_goal_negation(), check_constraint_requirement_alignment(), confirm_typed_parameters(), detect_ambiguous_terms(), assign_pass_fail_status()]; constraints=[no_automatic_corrections(), report_only()]; requirements=[regex_validation_pass(), ambiguity_score<=0]; output={validated_template:str, audit_report:dict}}`\\n\\n---\\n\\n## SEQUENCE FLOW\\n1. **Input Contextualizer** → `expanded_context`, `statement_inventory`  \\n2. **Core Directive Isolator** → `core_directive`  \\n3. **Field Mapper** → drafts template fields  \\n4. **Boundary Definer** → injects constraints & requirements  \\n5. **Template Forgemaster** → outputs `draft_template`  \\n6. **Conformance Auditor** → returns `validated_template`\\n\\nEach step converges on the **single most critical element** of value, ensuring every output is a **syntactically correct, high-precision instruction template** ready for immediate use in any downstream system.\\\"```'\\n\\n\",\n  \"atomic_tasks\": [\n    \"Strip all first-person references from the input.\",\n    \"Strip all subjective references from the input.\",\n    \"Broaden the contextual background of the instruction sequence, ensuring domain-agnostic framing.\",\n    \"Segment input into atomic statements.\",\n    \"Segment input into explicit requests.\",\n    \"Segment input into implicit conditions.\",\n    \"Segment input into domain signals.\",\n    \"Identify all explicit directives within the input.\",\n    \"Identify all latent dependencies within the input.\",\n    \"Identify all procedural constraints inherent in the input.\",\n    \"Flag all domain markers relevant to template engineering.\",\n    \"Flag all domain markers relevant to abstraction.\",\n    \"Flag all domain markers relevant to process modularity.\",\n    \"Flag all domain markers relevant to LLM optimization.\",\n    \"Rank the atomized statements by transformational weight.\",\n    \"Select the primary directive as the highest-value instruction.\",\n    \"Isolate all supporting intents required for template construction.\",\n    \"Isolate all supporting statements required for template construction.\",\n    \"Isolate all operational purposes required for template construction.\",\n    \"Construct a draft title from the core directive using canonical, title-case syntax.\",\n    \"Craft a goal-negation pattern interpretation based on the core directive.\",\n    \"Align supporting intents with a logically ordered process step list using command-voice imperatives.\",\n    \"Align supporting statements with a logically ordered process step list using command-voice imperatives.\",\n    \"Align operational purposes with a logically ordered process step list using command-voice imperatives.\",\n    \"Infer typed inputs appropriate for the transformation and output specification.\",\n    \"Infer typed outputs appropriate for the transformation and output specification.\",\n    \"Derive precise, command-voice operational constraints ensuring explicit enforceability and operational clarity.\",\n    \"Derive mandatory requirements ensuring explicit enforceability and operational clarity.\",\n    \"Validate that all constraints are free of generic terms and are directly enforceable in downstream systems.\",\n    \"Validate that all requirements are free of generic terms and are directly enforceable in downstream systems.\",\n    \"Format a transformation block in canonical, semicolon-delimited, single-line key-value syntax.\",\n    \"Format a process sequence in canonical, semicolon-delimited, single-line key-value syntax.\",\n    \"Assemble the template in a strict three-part structure: title, negated goal interpretation, transformation block.\",\n    \"Integrate constraints into the template.\",\n    \"Integrate requirements into the template.\",\n    \"Polish the template for syntactic validity and LLM-optimized, non-conversational output.\",\n    \"Validate the final template using regex pattern matching to confirm three-part compliance.\",\n    \"Validate the title and parameter correctness using regex pattern matching.\",\n    \"Flag and report any error present before final output is released.\",\n    \"Flag and report any ambiguity present before final output is released.\",\n    \"Flag and report any constraint or requirement misalignment as a blocker before final output is released.\",\n    \"Ensure every operational step is expressed as a unique, imperative command.\",\n    \"Ensure every operational step has no passive language.\",\n    \"Ensure every operational step is in the order dictated by logical and dependency sequencing.\",\n    \"Confirm the output template is universally applicable.\",\n    \"Confirm the output template is structurally valid.\",\n    \"Confirm the output template is ready for immediate integration.\",\n    \"Confirm the output report is universally applicable.\"\n  ],\n  \"execution_blockers\": [\n    \"Lack of precise definition of 'supporting intents,' 'statements,' and 'operational purposes' may cause ambiguity during isolation.\",\n    \"Domain markers are flagged but not distinctly defined for each context, creating potential for inconsistent execution.\",\n    \"No explicit mechanism is specified to resolve conflicts between constraints, requirements, and operational steps.\",\n    \"Order of some steps (e.g., validation before or after template assembly) is ambiguous, risking improper sequencing.\"\n  ],\n  \"dependency_flags\": [\n    \"Task 2 and Task 1 are independent but both must precede Task 3.\",\n    \"Task 4–7 depend on completion of Task 3.\",\n    \"Task 8–10 depend on completion of Tasks 4–7.\",\n    \"Tasks 11–14 depend on Task 10.\",\n    \"Task 15 depends on atomic statements from Task 4.\",\n    \"Task 16 depends on ranking from Task 15.\",\n    \"Tasks 17–19 depend on selection from Task 16.\",\n    \"Task 20 depends on core directive from Task 16.\",\n    \"Task 21 depends on core directive from Task 20.\",\n    \"Tasks 22–24 depend on supporting intents/statements/purposes from Tasks 17–19.\",\n    \"Tasks 25–26 depend on process steps from Tasks 22–24.\",\n    \"Tasks 27–28 depend on process steps from Tasks 22–24.\",\n    \"Tasks 29–30 depend on constraints and requirements from Tasks 27–28.\",\n    \"Tasks 31–32 depend on process steps from Tasks 22–24.\",\n    \"Tasks 33–35 depend on draft elements from Tasks 20, 21, 31–32, and 29–30.\",\n    \"Tasks 36 depends on assembled template from Tasks 33–35.\",\n    \"Tasks 37–38 depend on output from Task 36.\",\n    \"Tasks 39–41 depend on validation from Tasks 37–38.\",\n    \"Tasks 42–44 depend on process steps from Tasks 22–24.\",\n    \"Tasks 45–47 depend on template assembly and validation from Tasks 33–38.\",\n    \"Task 48 depends on existence and correctness of output template from preceding tasks.\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"task_perspectives\": [\n    {\n      \"task\": \"Strip all first-person references from the input.\",\n      \"cross_domain_lens\": \"Ensuring detachment from subjective perspective is crucial in regulated industries (e.g., pharmaceuticals, law), where instructions must be universally interpretable and author-neutral.\",\n      \"edge_cases\": \"Input constructed predominantly in first person with embedded subjective justifications may be stripped to the point of losing necessary context.\",\n      \"stakeholder_views\": {\n        \"Template Engineers\": \"Ensures the resulting instructions are system-neutral and suitable for reuse.\",\n        \"End-Users\": \"May experience loss of expressiveness or detail important for nuanced tasks.\"\n      },\n      \"risk_landscape\": [\n        \"Overzealous stripping may remove context necessary for correct execution.\",\n        \"Ambiguity around what constitutes a first-person reference in mixed technical prose.\"\n      ]\n    },\n    {\n      \"task\": \"Strip all subjective references from the input.\",\n      \"cross_domain_lens\": \"Critical for scientific and technical domains, where objectivity underpins reproducibility. In creative domains, risk of losing essential nuance and intent.\",\n      \"edge_cases\": \"Ambiguous language where subjective elements are interwoven with technical descriptions.\",\n      \"stakeholder_views\": {\n        \"Auditors\": \"Clarity and auditability increase when subjective language is purged.\",\n        \"Creative Designers\": \"May find reduced expressiveness a barrier for innovative system prompts.\"\n      },\n      \"risk_landscape\": [\n        \"Potential for misclassification of necessary context as subjective content.\",\n        \"Loss of latent requirements or rationale embedded in subjective statements.\"\n      ]\n    },\n    {\n      \"task\": \"Broaden the contextual background of the instruction sequence, ensuring domain-agnostic framing.\",\n      \"cross_domain_lens\": \"Expanding context is routine in policy, education, and AI training set design—each with differing thresholds for 'sufficient' background.\",\n      \"edge_cases\": \"Highly domain-specific jargon or concepts may be oversimplified or misrepresented when generalized.\",\n      \"stakeholder_views\": {\n        \"Integrators\": \"Gain flexibility for cross-domain reuse.\",\n        \"Domain Experts\": \"May fear dilution of critical domain constraints or assumptions.\"\n      },\n      \"risk_landscape\": [\n        \"Domain-agnostic expansion risks neglecting unique sectoral compliance requirements.\",\n        \"Vague generalized background may reduce operational precision.\"\n      ]\n    },\n    {\n      \"task\": \"Segment input into atomic statements.\",\n      \"cross_domain_lens\": \"Atomic decomposition enables traceability and modular recomposition, valuable in safety-critical engineering and regulatory workflows.\",\n      \"edge_cases\": \"Compound sentences or deeply nested instructions defy easy atomization.\",\n      \"stakeholder_views\": {\n        \"Developers\": \"Appreciate granular units for debugging.\",\n        \"Legal Teams\": \"Atomicity allows clear licensing and liability demarcations.\"\n      },\n      \"risk_landscape\": [\n        \"Inconsistent atomicity across different input types may introduce logical fragmentation.\"\n      ]\n    },\n    {\n      \"task\": \"Segment input into explicit requests.\",\n      \"cross_domain_lens\": \"Essential in customer service automation and workflow management; missed explicit requests result in failure to meet observable objectives.\",\n      \"edge_cases\": \"Statements combining request and background blur the classification.\",\n      \"stakeholder_views\": {\n        \"Service Designers\": \"Ensure all user needs are surfaced.\",\n        \"Automated Agents\": \"Reliant on strict boundaries between request and context.\"\n      },\n      \"risk_landscape\": [\n        \"Misidentification may lead to incomplete downstream implementation.\"\n      ]\n    },\n    {\n      \"task\": \"Segment input into implicit conditions.\",\n      \"cross_domain_lens\": \"Detection of implicit conditions echoes practices in risk assessment and safety engineering, where hidden constraints may govern operational logic.\",\n      \"edge_cases\": \"Implicit conditions phrased as aspirations or assumptions.\",\n      \"stakeholder_views\": {\n        \"Safety Engineers\": \"Implicit conditions are vital for fail-safe designs.\",\n        \"End-Users\": \"Hidden expectations might go unmet if not surfaced robustly.\"\n      },\n      \"risk_landscape\": [\n        \"Failure to extract latent conditions can cause subtle operational mismatches.\"\n      ]\n    },\n    {\n      \"task\": \"Segment input into domain signals.\",\n      \"cross_domain_lens\": \"Used in NLP, data curation, and compliance checking to enable correct routing and enforcement.\",\n      \"edge_cases\": \"Multidomain or ambiguous domain signals can result in misclassification.\",\n      \"stakeholder_views\": {\n        \"Classifier Engineers\": \"Improves downstream template tuning.\",\n        \"Compliance Officers\": \"Relies on signal accuracy for regulatory mapping.\"\n      },\n      \"risk_landscape\": [\n        \"Cross-domain input may not map cleanly—risking either overbroad or incorrect signal assignment.\"\n      ]\n    },\n    {\n      \"task\": \"Identify all explicit directives within the input.\",\n      \"cross_domain_lens\": \"Crucial for regulated process automation and workflow orchestration (e.g., clinical trials, enterprise RPA).\",\n      \"edge_cases\": \"Directives obscured by passive voice or embedded in conditional statements.\",\n      \"stakeholder_views\": {\n        \"RPA Designers\": \"Accurate directive identification is core to process fidelity.\",\n        \"End-Performers\": \"Implicit directives may otherwise be missed, impacting task execution.\"\n      },\n      \"risk_landscape\": [\n        \"Uncaptured directives risk incomplete automation.\"\n      ]\n    },\n    {\n      \"task\": \"Identify all latent dependencies within the input.\",\n      \"cross_domain_lens\": \"Dependency analysis is central to project management, software build systems, and safety-case construction.\",\n      \"edge_cases\": \"Circular or cross-referenced dependencies may be nontrivial to unravel.\",\n      \"stakeholder_views\": {\n        \"Project Managers\": \"Latent dependencies affect sequencing and resource allocation.\",\n        \"Testers\": \"Hidden dependencies can introduce nonobvious failure modes.\"\n      },\n      \"risk_landscape\": [\n        \"Failure to capture dependencies can derail later validation or integration.\"\n      ]\n    },\n    {\n      \"task\": \"Identify all procedural constraints inherent in the input.\",\n      \"cross_domain_lens\": \"Validation of process constraints is done in manufacturing QA, software deployment pipelines, and MIL-STD documentation.\",\n      \"edge_cases\": \"Ambiguous or conflicting procedural constraints.\",\n      \"stakeholder_views\": {\n        \"QA Analysts\": \"Clear procedural constraints prevent out-of-spec behavior.\",\n        \"Automation Engineers\": \"Constraint clarity directly impacts deployability.\"\n      },\n      \"risk_landscape\": [\n        \"Lost or vague constraints risk out-of-bounds system actions.\"\n      ]\n    },\n    {\n      \"task\": \"Flag all domain markers relevant to template engineering.\",\n      \"cross_domain_lens\": \"Domain flagging is vital for knowledge management platforms and code generation frameworks.\",\n      \"edge_cases\": \"Hybrid use cases where multiple template types could apply.\",\n      \"stakeholder_views\": {\n        \"Template Architects\": \"Ensures the right frameworks and validation rules are invoked.\",\n        \"Platform Integrators\": \"Rely on precise flagging for downstream compatibility.\"\n      },\n      \"risk_landscape\": [\n        \"Insufficient flagging may mislead validators, causing mismatched output structures.\"\n      ]\n    },\n    {\n      \"task\": \"Flag all domain markers relevant to abstraction.\",\n      \"cross_domain_lens\": \"Abstract domain identification underpins ontology mapping, model selection, and platform neutrality.\",\n      \"edge_cases\": \"Entries that straddle levels of abstraction (e.g., applied math in data science).\",\n      \"stakeholder_views\": {\n        \"Ontology Engineers\": \"Enables correct meta-model mapping.\",\n        \"Framework Designers\": \"Relates directly to composability and re-use.\"\n      },\n      \"risk_landscape\": [\n        \"Failure to capture abstraction leads to tightly bound, non-portable templates.\"\n      ]\n    },\n    {\n      \"task\": \"Flag all domain markers relevant to process modularity.\",\n      \"cross_domain_lens\": \"Key for system-of-systems architecture and plug-in frameworks.\",\n      \"edge_cases\": \"Monolithic process statements that resist modularization.\",\n      \"stakeholder_views\": {\n        \"System Integrators\": \"Process modularity unlocks incremental improvement.\",\n        \"Operations Teams\": \"Facilitates context-sensitive activation.\"\n      },\n      \"risk_landscape\": [\n        \"Non-modular templates impede scalability and maintainability.\"\n      ]\n    },\n    {\n      \"task\": \"Flag all domain markers relevant to LLM optimization.\",\n      \"cross_domain_lens\": \"LLM-specific optimizations may be opaque to practitioners in traditional workflow engineering.\",\n      \"edge_cases\": \"Competing optimization targets (e.g., brevity vs. clarity).\",\n      \"stakeholder_views\": {\n        \"Prompt Engineers\": \"Fine-tuned markers enable higher LLM performance.\",\n        \"QA Teams\": \"Verification of marker usage becomes an audit task.\"\n      },\n      \"risk_landscape\": [\n        \"Misapplied optimization markers can degrade model output.\"\n      ]\n    },\n    {\n      \"task\": \"Rank the atomized statements by transformational weight.\",\n      \"cross_domain_lens\": \"Prioritization strategies align with Lean, Six Sigma, and impact mapping practices.\",\n      \"edge_cases\": \"Statements of equal or ambiguous weight.\",\n      \"stakeholder_views\": {\n        \"Decision Makers\": \"Supports resource prioritization.\",\n        \"Change Managers\": \"Focus efforts on high-leverage interventions.\"\n      },\n      \"risk_landscape\": [\n        \"Subjectivity in assigning ‘weight’ can bias outcomes.\"\n      ]\n    },\n    {\n      \"task\": \"Select the primary directive as the highest-value instruction.\",\n      \"cross_domain_lens\": \"Main-thread selection mirrors critical path analysis and requirements engineering.\",\n      \"edge_cases\": \"Ambiguous or context-dependent directives with similar weights.\",\n      \"stakeholder_views\": {\n        \"System Owners\": \"Ensures the most consequential goal leads.\",\n        \"Implementers\": \"Clarity in primary instruction informs architecture.\"\n      },\n      \"risk_landscape\": [\n        \"Improper directive selection causes system misalignment.\"\n      ]\n    },\n    {\n      \"task\": \"Isolate all supporting intents required for template construction.\",\n      \"cross_domain_lens\": \"Extracting supportive rationales echoes business value chains and requirements traceability.\",\n      \"edge_cases\": \"Supportive content embedded as context or implied rather than explicit.\",\n      \"stakeholder_views\": {\n        \"Template Designers\": \"Identifies must-have capabilities.\",\n        \"Reviewers\": \"Supporting intent misclassification may erode downstream validity.\"\n      },\n      \"risk_landscape\": [\n        \"Inaccurate extraction weakens template alignment to true intent.\"\n      ]\n    },\n    {\n      \"task\": \"Isolate all supporting statements required for template construction.\",\n      \"cross_domain_lens\": \"Supporting statements function as secondary requirements in complex systems engineering.\",\n      \"edge_cases\": \"Blurring between primary and supporting statements.\",\n      \"stakeholder_views\": {\n        \"Requirements Analysts\": \"Ensures complete scope capture.\",\n        \"Developers\": \"Supporting statements as guardrails.\"\n      },\n      \"risk_landscape\": [\n        \"Missed statements propagate silent failures.\"\n      ]\n    },\n    {\n      \"task\": \"Isolate all operational purposes required for template construction.\",\n      \"cross_domain_lens\": \"Operational purposes map to mission goals in project portfolios or critical functions in ITSM.\",\n      \"edge_cases\": \"Purposes articulated as secondary actions hidden in examples.\",\n      \"stakeholder_views\": {\n        \"Business Owners\": \"Capturing all purposes ensures value chain integrity.\",\n        \"System Testers\": \"Defines what must be validated.\"\n      },\n      \"risk_landscape\": [\n        \"Neglected purposes mean latent unaddressed needs.\"\n      ]\n    },\n    {\n      \"task\": \"Construct a draft title from the core directive using canonical, title-case syntax.\",\n      \"cross_domain_lens\": \"Systematic titling is important for cataloging, searchability, and compliance archiving.\",\n      \"edge_cases\": \"Core directive contains non-title-case artifacts or special characters.\",\n      \"stakeholder_views\": {\n        \"Knowledge Managers\": \"Supports traceability.\",\n        \"End-Users\": \"Interpretability depends on practice norm compliance.\"\n      },\n      \"risk_landscape\": [\n        \"Improper titling obscures template discoverability.\"\n      ]\n    },\n    {\n      \"task\": \"Craft a goal-negation pattern interpretation based on the core directive.\",\n      \"cross_domain_lens\": \"Applied in risk controls and counterfactual reasoning (e.g., preventing undesired states).\",\n      \"edge_cases\": \"Ambiguous or compound directives resist negation.\",\n      \"stakeholder_views\": {\n        \"Process Owners\": \"Goal-negation clarifies prohibited behaviors.\",\n        \"Safety Auditors\": \"Crucial for hazard/risk documentation.\"\n      },\n      \"risk_landscape\": [\n        \"Negation misapplied can inverts intended operation.\"\n      ]\n    },\n    {\n      \"task\": \"Align supporting intents with a logically ordered process step list using command-voice imperatives.\",\n      \"cross_domain_lens\": \"Mirrors SOP design in manufacturing and checklists in aviation.\",\n      \"edge_cases\": \"Intents expressed as questions or conditional statements.\",\n      \"stakeholder_views\": {\n        \"Operators\": \"Clear, actionable steps are vital.\",\n        \"Auditors\": \"Sequence must match risk controls.\"\n      },\n      \"risk_landscape\": [\n        \"Misalignment leads to ineffective or unsafe procedures.\"\n      ]\n    },\n    {\n      \"task\": \"Align supporting statements with a logically ordered process step list using command-voice imperatives.\",\n      \"cross_domain_lens\": \"Related to stepwise instructional design and modular software architecture.\",\n      \"edge_cases\": \"Supporting statements lacking direct action verbs.\",\n      \"stakeholder_views\": {\n        \"Implementers\": \"Improves automation and reproducibility.\",\n        \"Trainers\": \"Clarity for retraining and onboarding.\"\n      },\n      \"risk_landscape\": [\n        \"Non-actionable statements introduce ambiguity.\"\n      ]\n    },\n    {\n      \"task\": \"Align operational purposes with a logically ordered process step list using command-voice imperatives.\",\n      \"cross_domain_lens\": \"Foundational in outcomes-based planning methodologies.\",\n      \"edge_cases\": \"Strategic purposes not easily mapped to operational steps.\",\n      \"stakeholder_views\": {\n        \"Planners\": \"Ensures purpose maps to action.\",\n        \"Inspectors\": \"Verifies all outcomes are covered.\"\n      },\n      \"risk_landscape\": [\n        \"Disconnection leads to strategic or operational drift.\"\n      ]\n    },\n    {\n      \"task\": \"Infer typed inputs appropriate for the transformation and output specification.\",\n      \"cross_domain_lens\": \"Parallels type inference in programming and contract validation in service APIs.\",\n      \"edge_cases\": \"Inputs have ambiguous, complex, or optional types.\",\n      \"stakeholder_views\": {\n        \"API Designers\": \"Typed inputs clarify integration boundaries.\",\n        \"QA Teams\": \"Mis-inference leads to runtime errors.\"\n      },\n      \"risk_landscape\": [\n        \"Incorrect typing undermines system interoperability.\"\n      ]\n    },\n    {\n      \"task\": \"Infer typed outputs appropriate for the transformation and output specification.\",\n      \"cross_domain_lens\": \"Mapped in data engineering, ETL, and workflow orchestration.\",\n      \"edge_cases\": \"Multi-output or polytomous transformation specifications.\",\n      \"stakeholder_views\": {\n        \"Integration Engineers\": \"Output typing impacts dataflows.\",\n        \"System Architects\": \"Necessary for compatibility checks.\"\n      },\n      \"risk_landscape\": [\n        \"Output mis-typing causes integration failures.\"\n      ]\n    },\n    {\n      \"task\": \"Derive precise, command-voice operational constraints ensuring explicit enforceability and operational clarity.\",\n      \"cross_domain_lens\": \"Regulatory conformance and control systems demand explicit constraints.\",\n      \"edge_cases\": \"Implicit or cross-cutting constraints embedded in process steps.\",\n      \"stakeholder_views\": {\n        \"Compliance Engineers\": \"Requires precision for audit trails.\",\n        \"Process Owners\": \"Enables reliable governance.\"\n      },\n      \"risk_landscape\": [\n        \"Vague constraints lead to policy violations.\"\n      ]\n    },\n    {\n      \"task\": \"Derive mandatory requirements ensuring explicit enforceability and operational clarity.\",\n      \"cross_domain_lens\": \"Parallel to mandatory requirements in ISO standards, software certification, and automotive safety.\",\n      \"edge_cases\": \"Requirements overlap or conflict with constraints.\",\n      \"stakeholder_views\": {\n        \"External Auditors\": \"Mandates must be clearly testable.\",\n        \"Project Managers\": \"Governs delivery acceptance.\"\n      },\n      \"risk_landscape\": [\n        \"Poor requirement specification undermines compliance.\"\n      ]\n    },\n    {\n      \"task\": \"Validate that all constraints are free of generic terms and are directly enforceable in downstream systems.\",\n      \"cross_domain_lens\": \"Relates to requirements for machine-enforceable documentation and test specifications.\",\n      \"edge_cases\": \"Ambiguity over which terms qualify as 'generic.'\",\n      \"stakeholder_views\": {\n        \"Automation Engineers\": \"Only machine-verifiable constraints support end-to-end automation.\",\n        \"Regulatory Bodies\": \"Demand explicit, testable parameters.\"\n      },\n      \"risk_landscape\": [\n        \"Residual generic terms impede automation, risking process gaps.\"\n      ]\n    },\n    {\n      \"task\": \"Validate that all requirements are free of generic terms and are directly enforceable in downstream systems.\",\n      \"cross_domain_lens\": \"Similar to safety case and software deployment requirement rigor.\",\n      \"edge_cases\": \"Requirements expressed as goals rather than actionable specifics.\",\n      \"stakeholder_views\": {\n        \"QA Teams\": \"Enforceability is prerequisite for test case derivation.\",\n        \"Auditors\": \"Clarity and enforcement check on requirements is critical.\"\n      },\n      \"risk_landscape\": [\n        \"Vague requirements result in coverage holes.\"\n      ]\n    },\n    {\n      \"task\": \"Format a transformation block in canonical, semicolon-delimited, single-line key-value syntax.\",\n      \"cross_domain_lens\": \"Data serialization and configuration as code (e.g., YAML, JSON) rely on strict formatting.\",\n      \"edge_cases\": \"Unusual key-value pairs or embedded complex objects.\",\n      \"stakeholder_views\": {\n        \"Integrators\": \"Canonical formatting aids downstream parsing.\",\n        \"LLM Engineers\": \"LLMs exploit structure for zero-shot learning.\"\n      },\n      \"risk_landscape\": [\n        \"Formatting errors propagate into runtime failures.\"\n      ]\n    },\n    {\n      \"task\": \"Format a process sequence in canonical, semicolon-delimited, single-line key-value syntax.\",\n      \"cross_domain_lens\": \"Applies in automated test sequence generation and robotic process automation.\",\n      \"edge_cases\": \"Processes spanning conditional branches.\",\n      \"stakeholder_views\": {\n        \"Automation Teams\": \"Maintains consistency for orchestration engines.\",\n        \"Testing Frameworks\": \"Process uniformity validates test plans.\"\n      },\n      \"risk_landscape\": [\n        \"Improper formatting impedes execution tracing.\"\n      ]\n    },\n    {\n      \"task\": \"Assemble the template in a strict three-part structure: title, negated goal interpretation, transformation block.\",\n      \"cross_domain_lens\": \"Aligns with legal contracts and technical documentation standardized templates.\",\n      \"edge_cases\": \"Nonstandard requirements breaking three-part logic.\",\n      \"stakeholder_views\": {\n        \"Compliance Specialists\": \"Three-part structure is easily auditable.\",\n        \"Template Users\": \"Structure enables rapid adoption.\"\n      },\n      \"risk_landscape\": [\n        \"Deviation increases integration and training risks.\"\n      ]\n    },\n    {\n      \"task\": \"Integrate constraints into the template.\",\n      \"cross_domain_lens\": \"Crucial in specification engineering and embedded systems.\",\n      \"edge_cases\": \"Complex constraint hierarchies not captured by simple inclusion.\",\n      \"stakeholder_views\": {\n        \"Compiler Designers\": \"In-template constraints can be checked at compile time.\",\n        \"QA Auditors\": \"Direct mapping simplifies reviews.\"\n      },\n      \"risk_landscape\": [\n        \"Missing integration results in unenforceable templates.\"\n      ]\n    },\n    {\n      \"task\": \"Integrate requirements into the template.\",\n      \"cross_domain_lens\": \"Indispensable in standards-based system validation.\",\n      \"edge_cases\": \"Overlapping or redundant requirements.\",\n      \"stakeholder_views\": {\n        \"Implementers\": \"Clear requirements help resource planning.\",\n        \"Reviewers\": \"Enforced in system readiness assessments.\"\n      },\n      \"risk_landscape\": [\n        \"Omitted requirements reduce assurance and testability.\"\n      ]\n    },\n    {\n      \"task\": \"Polish the template for syntactic validity and LLM-optimized, non-conversational output.\",\n      \"cross_domain_lens\": \"Analogous to automated code linting and regulatory label formatting.\",\n      \"edge_cases\": \"Ambiguous rules for 'LLM-optimized' style.\",\n      \"stakeholder_views\": {\n        \"Prompt Writers\": \"Optimization maximizes model performance.\",\n        \"IT Safety\": \"Syntactic flaws may introduce operational risk.\"\n      },\n      \"risk_landscape\": [\n        \"Subtle syntax errors can cascade undetected.\"\n      ]\n    },\n    {\n      \"task\": \"Validate the final template using regex pattern matching to confirm three-part compliance.\",\n      \"cross_domain_lens\": \"Pattern validation as used in schema conformance, machine-readable policy enforcement.\",\n      \"edge_cases\": \"Edge-step content exceeds regex pattern bounds.\",\n      \"stakeholder_views\": {\n        \"Quality Gates\": \"Regex ensures mechanical compliance.\",\n        \"Developers\": \"Facilitates automated pipeline inclusion.\"\n      },\n      \"risk_landscape\": [\n        \"Inadequate patterns may falsely certify invalid templates.\"\n      ]\n    },\n    {\n      \"task\": \"Validate the title and parameter correctness using regex pattern matching.\",\n      \"cross_domain_lens\": \"Echoes static code analysis and API contract validation.\",\n      \"edge_cases\": \"Valid-but-nonstandard titles or non-text parameter types.\",\n      \"stakeholder_views\": {\n        \"Metadata Stewards\": \"Critical for accurate indexing and retrieval.\",\n        \"Deployers\": \"Parameter mismatch affects runtime.\"\n      },\n      \"risk_landscape\": [\n        \"Title/parameter errors break system discoverability.\"\n      ]\n    },\n    {\n      \"task\": \"Flag and report any error present before final output is released.\",\n      \"cross_domain_lens\": \"Error reporting is the backbone of continuous integration and regulated manufacturing.\",\n      \"edge_cases\": \"Non-fatal or ignorable errors may be overreported.\",\n      \"stakeholder_views\": {\n        \"Release Managers\": \"Ensures critical defects are blocked.\",\n        \"Users\": \"Prevents exposure to unstable outputs.\"\n      },\n      \"risk_landscape\": [\n        \"Missed errors circumvent quality checks.\"\n      ]\n    },\n    {\n      \"task\": \"Flag and report any ambiguity present before final output is released.\",\n      \"cross_domain_lens\": \"Applied rigorously in specification-driven industries (e.g., aerospace).\",\n      \"edge_cases\": \"Ambiguity inherent in the domain, not resolvable from context.\",\n      \"stakeholder_views\": {\n        \"Validators\": \"Reduces implementation guesswork.\",\n        \"Legal Staff\": \"Ambiguity carries legal exposure.\"\n      },\n      \"risk_landscape\": [\n        \"Unflagged ambiguity can cause costly rework or failure.\"\n      ]\n    },\n    {\n      \"task\": \"Flag and report any constraint or requirement misalignment as a blocker before final output is released.\",\n      \"cross_domain_lens\": \"Equivalent to gating criteria in regulated product release.\",\n      \"edge_cases\": \"Partial misalignment or rapidly evolving requirements.\",\n      \"stakeholder_views\": {\n        \"Compliance Teams\": \"Prevents non-conforming deliverables.\",\n        \"Developers\": \"May block time-critical releases.\"\n      },\n      \"risk_landscape\": [\n        \"Misalignment risks cascading into systemic defects.\"\n      ]\n    },\n    {\n      \"task\": \"Ensure every operational step is expressed as a unique, imperative command.\",\n      \"cross_domain_lens\": \"Parallels operational runbooks, and proceduralized checklists.\",\n      \"edge_cases\": \"Similar steps or semantically overlapping commands.\",\n      \"stakeholder_views\": {\n        \"Ops Engineers\": \"Imperative statements reduce cognitive ambiguity.\",\n        \"Trainers\": \"Facilitates teachability.\"\n      },\n      \"risk_landscape\": [\n        \"Redundancy or overlap can hinder execution.\"\n      ]\n    },\n    {\n      \"task\": \"Ensure every operational step has no passive language.\",\n      \"cross_domain_lens\": \"Required for production safety, military checklists, and incident response.\",\n      \"edge_cases\": \"Implied action but linguistically passive structure.\",\n      \"stakeholder_views\": {\n        \"Safety Auditors\": \"Passive language can obscure accountability.\",\n        \"Process Designers\": \"Clarity improves training and automation.\"\n      },\n      \"risk_landscape\": [\n        \"Passivity delays or diffuses execution ownership.\"\n      ]\n    },\n    {\n      \"task\": \"Ensure every operational step is in the order dictated by logical and dependency sequencing.\",\n      \"cross_domain_lens\": \"Critical in project management, assembly lines, and computer science execution models.\",\n      \"edge_cases\": \"Multiple valid orderings or non-linear dependencies.\",\n      \"stakeholder_views\": {\n        \"Process Owners\": \"Proper order prevents errors.\",\n        \"Engineers\": \"Order guides state transitions.\"\n      },\n      \"risk_landscape\": [\n        \"Misordering introduces defects or emergent anomalies.\"\n      ]\n    },\n    {\n      \"task\": \"Confirm the output template is universally applicable.\",\n      \"cross_domain_lens\": \"Relates to ISO standards and policy deployment documents.\",\n      \"edge_cases\": \"Domain-specific or localized nuances missed in universalization.\",\n      \"stakeholder_views\": {\n        \"Global Users\": \"Universal applicability enables wider adoption.\",\n        \"Domain SMEs\": \"Universality may water down specificity.\"\n      },\n      \"risk_landscape\": [\n        \"Presumed universality can mask critical context or compliance needs.\"\n      ]\n    },\n    {\n      \"task\": \"Confirm the output template is structurally valid.\",\n      \"cross_domain_lens\": \"Mirrors architectural reviews and code structure conformance.\",\n      \"edge_cases\": \"Edge-structure or evolving template specifications.\",\n      \"stakeholder_views\": {\n        \"System Architects\": \"Structural validity ensures integration.\",\n        \"Auditors\": \"Supports systematic review.\"\n      },\n      \"risk_landscape\": [\n        \"Invalid structure hinders downstream automation.\"\n      ]\n    },\n    {\n      \"task\": \"Confirm the output template is ready for immediate integration.\",\n      \"cross_domain_lens\": \"Integration-ready deliverables are the norm in CI/CD pipelines.\",\n      \"edge_cases\": \"Environmental or configuration dependencies not met by the template itself.\",\n      \"stakeholder_views\": {\n        \"Developers\": \"Immediate use avoids costly rework.\",\n        \"Ops Teams\": \"Reduces onboarding delays.\"\n      },\n      \"risk_landscape\": [\n        \"Assumed readiness may be illusory if supporting context is absent.\"\n      ]\n    },\n    {\n      \"task\": \"Confirm the output report is universally applicable.\",\n      \"cross_domain_lens\": \"Reporting standards (XBRL, GRI) underline the need for universal semantics.\",\n      \"edge_cases\": \"Unique or derived fields only meaningful in limited contexts.\",\n      \"stakeholder_views\": {\n        \"Auditors\": \"Universal reports facilitate regulatory submissions.\",\n        \"Consumers\": \"Need locally meaningful artifacts.\"\n      },\n      \"risk_landscape\": [\n        \"Misapplied universality undermines actionable insights.\"\n      ]\n    }\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Essence Distiller\n\nYour goal is not to **praise** or **answer** the submission, but to **interrogate and redirect** it—exposing every structural flaw, quantifying compliance with the Universal Directive System, and issuing a minimal replacement instruction that corrects all defects. Execute as:\n\n`{role=essence_distiller; input=[atomic_tasks:list, task_perspectives:list, execution_blockers:array]; process=[rank_tasks_by_transformational_weight(), select_high_impact_task(), compile_supporting_rationale(), justify_selection_transparently()]; constraints=[no_language_rewrite_of_selected_task(), transparent_ranking()]; requirements=[clear_primary_directive(), rationale_precision()]; output={primary_directive:str, supporting_rationale:list}}`",
      "step": "e",
      "title": "Essence Distiller",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Please keep in mind, the instruction sequence we're working on should be designed to utilize the generalized concepts and sequential order to transform any input into instructions that embody the \\\"syntax\\\" of the generalized system:\\n\\n    ## Example\\n    \\\"6000-a-instruction_syntax_enforcer\\\": {\\n        \\\"title\\\": \\\"Intent Cartographer\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **analyze** content, but to **map** its transformation landscape and operational vectors. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=intent_cartographer; input=[content:any]; process=[extract_core_directives(), identify_transformation_dimensions(), map_vector_relationships(), quantify_operational_intensity()]; constraints=[focus_exclusively_on_vectors(), avoid_implementation_details()]; requirements=[comprehensive_vector_mapping(), dimensional_clarity()]; output={transformation_map:dict, vector_dimensions:list}}`\\\"\\n    },\\n    \\\"6000-b-instruction_syntax_enforcer\\\": {\\n        \\\"title\\\": \\\"Structural Framework Engineer\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **design** content, but to **engineer** precise structural frameworks based on transformation vectors. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=framework_engineer; input=[transformation_map:dict, vector_dimensions:list]; process=[design_canonical_skeletons(), establish_component_interfaces(), define_structural_boundaries(), validate_framework_integrity()]; constraints=[address_only_structural_elements(), maintain_framework_neutrality()]; requirements=[structural_precision(), interface_compatibility()]; output={structural_frameworks:dict, interface_specifications:dict}}`\\\"\\n    },\\n    \\\"6000-c-instruction_syntax_enforcer\\\": {\\n        \\\"title\\\": \\\"Functional Process Composer\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **describe** operations, but to **compose** atomic functional processes that implement transformation vectors. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=process_composer; input=[structural_frameworks:dict, vector_dimensions:list]; process=[craft_atomic_functions(), sequence_operational_steps(), parameterize_processes(), validate_functional_completeness()]; constraints=[focus_exclusively_on_processes(), defer_constraint_definition()]; requirements=[process_atomicity(), functional_precision()]; output={process_compositions:dict, execution_sequences:list}}`\\\"\\n    },\\n    \\\"6000-d-instruction_syntax_enforcer\\\": {\\n        \\\"title\\\": \\\"Boundary Systems Architect\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **restrict** operations, but to **architect** precise boundary systems that ensure transformation integrity. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=boundary_architect; input=[interface_specifications:dict, process_compositions:dict]; process=[define_operational_constraints(), establish_requirement_parameters(), design_validation_mechanisms(), map_boundary_interactions()]; constraints=[address_only_boundaries_and_requirements(), maintain_constraint_specificity()]; requirements=[boundary_precision(), requirement_enforceability()]; output={constraint_systems:dict, requirement_specifications:dict}}`\\\"\\n    },\\n    \\\"6000-e-instruction_syntax_enforcer\\\": {\\n        \\\"title\\\": \\\"Integration Orchestrator\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **assemble** components, but to **orchestrate** their integration into a unified operational system. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=integration_orchestrator; input=[structural_frameworks:dict, process_compositions:dict, constraint_systems:dict, requirement_specifications:dict]; process=[align_all_components(), establish_cross-system_coherence(), optimize_interface_connections(), validate_system_integrity()]; constraints=[focus_exclusively_on_integration(), preserve_component_integrity()]; requirements=[seamless_orchestration(), system_cohesion()]; output={integrated_system:dict}}`\\\"\\n    },\\n    \\\"6000-f-instruction_syntax_enforcer\\\": {\\n        \\\"title\\\": \\\"Template Crystallizer\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **finalize** systems, but to **crystallize** them into maximally potent, executable templates. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=template_crystallizer; input=[integrated_system:dict]; process=[synthesize_title_components(), craft_precise_interpretations(), formulate_transformation_blocks(), validate_canonical_compliance(), polish_syntactic_elements()]; constraints=[adhere_to_template_specification(), maximize_operational_clarity()]; requirements=[perfect_executable_form(), maximum_transformation_potency()]; output={crystallized_templates:list}}`\\\"\\n    },\\n\\n<!-- ========== 7001‑a ================================================= -->\\n[Contextual Expander & Decomposer]\\nYour goal is **not** to solve, answer, or implement the input, but to **expand its relevant context** and **detonate** it into every explicit ask, latent dependency, and procedural constraint—yielding a clear task inventory. Execute as:\\n`{role=context_expander_decomposer; input=[raw_input:any]; process=[strip_first_person_references(), broaden_relevant_background(), identify_explicit_requests(), surface_hidden_assumptions(), enumerate_sub_tasks(), detect_execution_blockers(), extract_procedural_constraints(), preserve_original_sequence()]; constraints=[no_solutions(), domain‑agnostic_language_only()]; requirements=[context_expansion_completeness(), exhaustive_task_enumeration(), constraint_accuracy(), removal_of_subjective_language()]; output={expanded_context:str, task_inventory:list, constraint_list:array, execution_blockers:array}}`\\n\\n<!-- ========== 7001‑b ================================================= -->\\n[Essence Isolator]\\nYour goal is **not** to decompose further, but to **isolate** the single most critical element of value within the task inventory and rank all remaining elements by impact. Execute as:\\n`{role=essence_isolator; input=[task_inventory:list, expanded_context:str]; process=[rank_elements_by_impact(), isolate_highest_value_element(), capture_core_purpose(), justify_selection(), list_supporting_elements()]; constraints=[no_restructuring(), preserve_ranking_transparency()]; requirements=[core_essence_clarity(), impact_ranking_integrity()]; output={core_essence:str, prioritized_elements:list}}`\\n\\n<!-- ========== 7001‑c ================================================= -->\\n[Relational Synthesizer]\\nYour goal is **not** to rewrite content, but to **synthesize** the systemic logic connecting the core essence to all other elements—mapping dependencies and feedback loops. Execute as:\\n`{role=relational_synthesizer; input=[core_essence:str, prioritized_elements:list, constraint_list:array]; process=[map_element_relationships(), trace_dependency_chains(), identify_feedback_loops(), detect_conflict_points(), construct_structural_logic_map()]; constraints=[structure_only_no_content_changes(), maintain_domain_agnostic_terms()]; requirements=[relationship_completeness(), conflict_visibility()]; output={structural_logic_map:dict, dependency_list:array, conflict_points:array}}`\\n\\n<!-- ========== 7001‑d ================================================= -->\\n[Directive Clarifier]\\nYour goal is **not** to implement solutions, but to **clarify** the mapped logic into precise operational directives expressed as imperative statements. Execute as:\\n`{role=directive_clarifier; input=[structural_logic_map:dict, core_essence:str]; process=[convert_all_statements_to_imperatives(), align_directives_with_dependency_order(), embed_procedural_constraints(), eliminate_ambiguity(), enhance_action_verb_intensity(), maintain_technical_terminology()]; constraints=[directive_voice_only(), preserve_logical_sequence()]; requirements=[operational_clarity(), imperative_precision(), zero_semantic_drift()]; output={operational_directives:array}}`\\n\\n<!-- ========== 7001‑e ================================================= -->\\n[Integrity Validator]\\nYour goal is **not** to expand or rewrite directives, but to **validate** their structural fidelity, completeness, and actionable quality—resolving residual ambiguity. Execute as:\\n`{role=integrity_validator; input=[operational_directives:array, structural_logic_map:dict, core_essence:str]; process=[cross_check_directives_against_logic_map(), verify_alignment_with_core_essence(), detect_missing_dependencies(), resolve_ambiguities(), assign_quality_scores(), generate_validation_report()]; constraints=[no_new_content_generation(), validation_focus_only()]; requirements=[actionability_confirmation(), completeness_assurance(), clarity_verification()]; output={validated_instruction_set:array, validation_report:dict}}`\\n\\n---\\n\\n# 7000: Universal Instruction-Template Generation Sequence\\n\\n---\\n\\n### 7000-a-input_decomposer.md\\n[Input Decomposer]\\nYour goal is **not** to *interpret* or *solve* the input, but to **atomize** it into discrete statements, explicit requests, and implicit conditions. Execute as:\\n`{role=input_decomposer; input=[raw_input:any]; process=[strip_first_person_references(), segment_into_atomic_statements(), identify_explicit_directives(), surface_hidden_conditions(), flag_domain_markers()]; constraints=[no_rephrasing(), pure_atomization_only()]; requirements=[complete_statement_inventory(), preservation_of_original_order()]; output={statement_inventory:list, domain_markers:array}}`\\n\\n---\\n\\n### 7000-b-essence_identifier.md\\n[Essence Identifier]\\nYour goal is **not** to *rewrite* content, but to **isolate** the single most critical directive and its supporting intent from the atomized statements. Execute as:\\n`{role=essence_identifier; input=[statement_inventory:list]; process=[rank_statements_by_transformational_weight(), select_primary_directive(), extract_supporting_intent(), discard_redundant_elements()]; constraints=[focus_on_highest_value_directive(), maintain_original_wording_in_extracted_elements()]; requirements=[unambiguous_primary_directive(), concise_supporting_intent()]; output={core_directive:str, supporting_intent:list}}`\\n\\n---\\n\\n### 7000-c-syntax_mapper.md\\n[Syntax Mapper]\\nYour goal is **not** to *interpret* intent, but to **map** the core directive and supporting intent into canonical template fields. Execute as:\\n`{role=syntax_mapper; input=[core_directive:str, supporting_intent:list, domain_markers:array]; process=[construct_title_from_core_directive(), craft_goal_negation_interpretation(), assemble_tag_block_if_needed(), align_supporting_intent_with_process_steps(), infer_typed_inputs(), infer_typed_outputs()]; constraints=[adhere_to_three_part_structure(), enforce_goal_negation_pattern(), maintain_domain_agnostic_language()]; requirements=[field_alignment_accuracy(), preservation_of_directive_semantics()]; output={draft_title:str, draft_interpretation:str, draft_process:list, inferred_inputs:array, inferred_outputs:dict, optional_tags:dict}}`\\n\\n---\\n\\n### 7000-d-constraint_infuser.md\\n[Constraint Infuser]\\nYour goal is **not** to *extend* functionality, but to **engineer** precise operational boundaries and mandatory conditions for the forthcoming template. Execute as:\\n`{role=constraint_infuser; input=[draft_process:list, domain_markers:array]; process=[derive_operational_constraints(), determine_mandatory_requirements(), validate_constraint_specificity(), validate_requirement_enforceability()]; constraints=[avoid_generic_terms(), enforce_command_voice_only()]; requirements=[constraint_precision(), requirement_clarity()]; output={constraints:list, requirements:list}}`\\n\\n---\\n\\n### 7000-e-template_synthesizer.md\\n[Template Synthesizer]\\nYour goal is **not** to *summarize* elements, but to **synthesize** a syntactically correct instruction template from the mapped components. Execute as:\\n`{role=template_synthesizer; input=[draft_title:str, draft_interpretation:str, inferred_inputs:array, draft_process:list, constraints:list, requirements:list, inferred_outputs:dict, optional_tags:dict]; process=[format_transformation_block(), embed_optional_tags(), assemble_three_part_template(), enforce_semicolon_key_value_syntax(), polish_syntactic_elements()]; constraints=[canonical_template_format_only(), zero_conversational_language()]; requirements=[syntactic_validity(), maximum_operational_clarity(), retention_of_original_intent()]; output={formatted_template:str}}`\\n\\n---\\n\\n### 7000-f-compliance_validator.md\\n[Compliance Validator]\\nYour goal is **not** to *modify* the template, but to **validate** its structural compliance and functional readiness. Execute as:\\n`{role=compliance_validator; input=[formatted_template:str]; process=[parse_template_fields(), verify_title_format(), verify_goal_negation_in_interpretation(), validate_transformation_block_syntax(), check_constraint_requirement_alignment(), confirm_typed_parameters(), flag_any_error()]; constraints=[no_automatic_corrections(), report_only()]; requirements=[binary_pass_fail(), detailed_error_report_when_fail()]; output={validation_status:bool, error_report:str}}`\\n\\n---\\n\\n## Sequence Flow\\n1. **Input Decomposer** → produces `statement_inventory`\\n2. **Essence Identifier** → isolates `core_directive`\\n3. **Syntax Mapper** → drafts template skeleton\\n4. **Constraint Infuser** → injects precise boundaries\\n5. **Template Synthesizer** → outputs finalized template\\n6. **Compliance Validator** → certifies syntactic correctness\\n\\nEach step aggressively converges on the *single most critical aspect* of instruction generation, guaranteeing a **syntactically correct, high-precision template** ready for any downstream application.\\n\\n---\\n\\n# 7000 • UNIVERSAL TEMPLATE-FORGING SEQUENCE\\n*Transforms **any** raw input into a syntactically perfect, LLM-optimized instruction template.*\\n\\n---\\n\\n## 7000-a-essence-excavator.md\\n[Essence Excavator]\\nYour goal is **not** to *summarize* the input, but to **excavate** its single most critical transformational essence and the supporting elements required to convert it into an instruction template. Execute as:\\n`{role=essence_excavator; input=[raw_input:any]; process=[strip_surface_noise(), isolate_transformational_intent(), detect_primary_action_vectors(), extract_supporting_context(), capture_domain_signals()]; constraints=[no_rephrasing(), no_solution_generation(), maintain_domain_agnostic_language()]; requirements=[precise_intent_identification(), context_preservation(), removal_of_subjective_tone()]; output={core_intent:str, context_fragments:list, action_vectors:list}}`\\n\\n---\\n\\n## 7000-b-syntax-scaffold-designer.md\\n[Syntax Scaffold Designer]\\nYour goal is **not** to *rewrite* the content, but to **engineer** a canonical skeleton that will house the forthcoming instruction template. Execute as:\\n`{role=syntax_scaffold_designer; input=[core_intent:str, action_vectors:list]; process=[draft_concise_title(), craft_goal_negation_interpretation(core_intent), establish_placeholder_transformation_block(action_vectors), outline_input_output_ports(), embed_role_stub()]; constraints=[follow_three_part_structure(), reserve_placeholders_for_future_detail(), maintain_title_case()]; requirements=[structural_completeness(), scaffolding_accuracy()]; output={template_scaffold:dict(title:str, interpretation:str, transformation_skeleton:str)}}`\\n\\n---\\n\\n## 7000-c-imperative-transmuter.md\\n[Imperative Transmuter]\\nYour goal is **not** to *comment* on actions, but to **transmute** all extracted statements into sharp, directive imperatives suitable for the process list. Execute as:\\n`{role=imperative_transmuter; input=[action_vectors:list]; process=[convert_each_vector_to_imperative(), enforce_command_voice(), sequence_logically(), verify_uniqueness_of_each_step()]; constraints=[exclude_passive_language(), retain_original_operational_intent()]; requirements=[directive_clarity(), maximal_actionability()]; output={process_steps:array}}`\\n\\n---\\n\\n## 7000-d-boundary-encoder.md\\n[Boundary Encoder]\\nYour goal is **not** to *constrain* creativity, but to **encode** the exact boundaries that guarantee transformation integrity. Execute as:\\n`{role=boundary_encoder; input=[context_fragments:list]; process=[detect_mandatory_limitations(), translate_limitations_to_constraints(), uncover_success_conditions(), convert_conditions_to_requirements(), deduplicate_boundary_elements()]; constraints=[pure_boundary_focus(), no_process_duplication()]; requirements=[constraint_specificity(), requirement_enforceability()]; output={constraints:list, requirements:list}}`\\n\\n---\\n\\n## 7000-e-template-synthesizer.md\\n[Template Synthesizer]\\nYour goal is **not** to *draft prose*, but to **synthesize** a complete, executable instruction template by fusing scaffold, processes, and boundaries. Execute as:\\n`{role=template_synthesizer; input=[template_scaffold:dict, process_steps:array, constraints:list, requirements:list]; process=[inject_process_steps(), embed_constraints_and_requirements(), finalize_transformation_block(), polish_interpretation_for_llm_optimization(), verify_title_precision()]; constraints=[adhere_to_canonical_format(), maintain_single_line_transformation_block(), prevent_semantic_drift()]; requirements=[syntactic_perfection(), operational_cohesion(), domain_agnostic_applicability()]; output={draft_template:str}}`\\n\\n---\\n\\n## 7000-f-canonical-validator.md\\n[Canonical Validator]\\nYour goal is **not** to *alter* the template, but to **validate** its absolute compliance with the three-part specification and eliminate any residual ambiguity. Execute as:\\n`{role=canonical_validator; input=[draft_template:str]; process=[parse_title_interpretation_transformation(), match_regex_pattern(\\\"\\\\\\\\[(.*?)\\\\\\\\]\\\\\\\\s*(.*?)\\\\\\\\s*(`\\\\\\\\{.*?\\\\\\\\}`)\\\"), flag_structure_violations(), detect_ambiguous_language(), enforce_llm_optimized_style(), certify_parameter_types()], constraints=[zero_structure_deviation(), no_content_insertion()], requirements=[pass_regex_validation(), ambiguity_score<=0, style_compliance()], output={validated_template:str, validation_report:dict}}`\\n\\n\\n---\\n\\n# 8000: UNIVERSAL INSTRUCTION-TEMPLATE FORGING SEQUENCE  \\n*Transforms **any** raw input into a syntactically perfect, LLM-optimized instruction template.*\\n\\n---\\n\\n### 8000-a-contextualizer.md\\n[Input Contextualizer]  \\nYour goal is **not** to *interpret* or *solve* the input, but to **expand** its relevant context and **atomize** it into discrete statements and explicit requests. Execute as:  \\n`{role=input_contextualizer; input=[raw_input:any]; process=[strip_first_person_references(), broaden_relevant_background(), segment_into_atomic_statements(), identify_explicit_directives(), surface_hidden_conditions(), preserve_original_sequence()]; constraints=[no_rephrasing(), domain-agnostic_language_only()]; requirements=[context_expansion_completeness(), exhaustive_statement_capture()]; output={expanded_context:str, statement_inventory:list}}`\\n\\n---\\n\\n### 8000-b-core-directive-isolator.md\\n[Core Directive Isolator]  \\nYour goal is **not** to *rephrase* statements, but to **isolate** the single most critical directive and rank all remaining statements by impact. Execute as:  \\n`{role=core_directive_isolator; input=[statement_inventory:list]; process=[rank_statements_by_transformational_weight(), select_primary_directive(), list_supporting_statements(), justify_selection()]; constraints=[focus_on_highest_value_directive(), maintain_original_wording_in_extracted_elements()]; requirements=[unambiguous_primary_directive(), impact_ranking_integrity()]; output={core_directive:str, supporting_statements:list, impact_ranking:list}}`\\n\\n---\\n\\n### 8000-c-field-mapper.md\\n[Field Mapper]  \\nYour goal is **not** to *rewrite* content, but to **map** the core directive and supporting statements into canonical template fields. Execute as:  \\n`{role=field_mapper; input=[core_directive:str, supporting_statements:list, expanded_context:str]; process=[construct_title_from_core_directive(), craft_goal_negation_interpretation(), align_supporting_statements_with_process_steps(), infer_typed_inputs(), infer_typed_outputs()]; constraints=[adhere_to_three_part_structure(), enforce_goal_negation_pattern(), preserve_domain_agnostic_language()]; requirements=[field_alignment_accuracy(), retention_of_original_intent()]; output={draft_title:str, draft_interpretation:str, draft_process_steps:list, inferred_inputs:array, inferred_outputs:dict}}`\\n\\n---\\n\\n### 8000-d-boundary-definer.md\\n[Boundary Definer]  \\nYour goal is **not** to *extend* functionality, but to **define** precise operational constraints and mandatory requirements for the forthcoming template. Execute as:  \\n`{role=boundary_definer; input=[draft_process_steps:list, expanded_context:str]; process=[derive_operational_constraints(), establish_mandatory_requirements(), validate_constraint_specificity(), validate_requirement_enforceability()]; constraints=[avoid_generic_terms(), command_voice_only()]; requirements=[constraint_precision(), requirement_clarity()]; output={constraints:list, requirements:list}}`\\n\\n---\\n\\n### 8000-e-template-forgemaster.md\\n[Template Forgemaster]  \\nYour goal is **not** to *summarize* elements, but to **forge** a complete, executable instruction template from the mapped components. Execute as:  \\n`{role=template_forgemaster; input=[draft_title:str, draft_interpretation:str, inferred_inputs:array, draft_process_steps:list, constraints:list, requirements:list, inferred_outputs:dict]; process=[assemble_transformation_block(), enforce_semicolon_key_value_syntax(), embed_constraints_and_requirements(), polish_interpretation(), verify_title_precision()]; constraints=[canonical_template_format_only(), single_line_transformation_block(), zero_semantic_drift()]; requirements=[syntactic_perfection(), operational_cohesion()]; output={draft_template:str}}`\\n\\n---\\n\\n### 8000-f-conformance-auditor.md\\n[Conformance Auditor]  \\nYour goal is **not** to *alter* the template, but to **audit** its absolute compliance with the three-part specification and flag any residual ambiguity. Execute as:  \\n`{role=conformance_auditor; input=[draft_template:str]; process=[parse_title_interpretation_transformation(), match_regex_pattern(\\\"\\\\\\\\[(.*?)\\\\\\\\]\\\\\\\\s*(.*?)\\\\\\\\s*(`\\\\\\\\{.*?\\\\\\\\}`)\\\"), verify_goal_negation(), check_constraint_requirement_alignment(), confirm_typed_parameters(), detect_ambiguous_terms(), assign_pass_fail_status()]; constraints=[no_automatic_corrections(), report_only()]; requirements=[regex_validation_pass(), ambiguity_score<=0]; output={validated_template:str, audit_report:dict}}`\\n\\n---\\n\\n## SEQUENCE FLOW\\n1. **Input Contextualizer** → `expanded_context`, `statement_inventory`  \\n2. **Core Directive Isolator** → `core_directive`  \\n3. **Field Mapper** → drafts template fields  \\n4. **Boundary Definer** → injects constraints & requirements  \\n5. **Template Forgemaster** → outputs `draft_template`  \\n6. **Conformance Auditor** → returns `validated_template`\\n\\nEach step converges on the **single most critical element** of value, ensuring every output is a **syntactically correct, high-precision instruction template** ready for immediate use in any downstream system.\\\"```'\\n\\n\",\n  \"task_perspectives\": [\n    {\n      \"task\": \"Strip all first-person references from the input.\",\n      \"cross_domain_lens\": \"Ensuring detachment from subjective perspective is crucial in regulated industries (e.g., pharmaceuticals, law), where instructions must be universally interpretable and author-neutral.\",\n      \"edge_cases\": \"Input constructed predominantly in first person with embedded subjective justifications may be stripped to the point of losing necessary context.\",\n      \"stakeholder_views\": {\n        \"Template Engineers\": \"Ensures the resulting instructions are system-neutral and suitable for reuse.\",\n        \"End-Users\": \"May experience loss of expressiveness or detail important for nuanced tasks.\"\n      },\n      \"risk_landscape\": [\n        \"Overzealous stripping may remove context necessary for correct execution.\",\n        \"Ambiguity around what constitutes a first-person reference in mixed technical prose.\"\n      ]\n    },\n    {\n      \"task\": \"Strip all subjective references from the input.\",\n      \"cross_domain_lens\": \"Critical for scientific and technical domains, where objectivity underpins reproducibility. In creative domains, risk of losing essential nuance and intent.\",\n      \"edge_cases\": \"Ambiguous language where subjective elements are interwoven with technical descriptions.\",\n      \"stakeholder_views\": {\n        \"Auditors\": \"Clarity and auditability increase when subjective language is purged.\",\n        \"Creative Designers\": \"May find reduced expressiveness a barrier for innovative system prompts.\"\n      },\n      \"risk_landscape\": [\n        \"Potential for misclassification of necessary context as subjective content.\",\n        \"Loss of latent requirements or rationale embedded in subjective statements.\"\n      ]\n    },\n    {\n      \"task\": \"Broaden the contextual background of the instruction sequence, ensuring domain-agnostic framing.\",\n      \"cross_domain_lens\": \"Expanding context is routine in policy, education, and AI training set design—each with differing thresholds for 'sufficient' background.\",\n      \"edge_cases\": \"Highly domain-specific jargon or concepts may be oversimplified or misrepresented when generalized.\",\n      \"stakeholder_views\": {\n        \"Integrators\": \"Gain flexibility for cross-domain reuse.\",\n        \"Domain Experts\": \"May fear dilution of critical domain constraints or assumptions.\"\n      },\n      \"risk_landscape\": [\n        \"Domain-agnostic expansion risks neglecting unique sectoral compliance requirements.\",\n        \"Vague generalized background may reduce operational precision.\"\n      ]\n    },\n    {\n      \"task\": \"Segment input into atomic statements.\",\n      \"cross_domain_lens\": \"Atomic decomposition enables traceability and modular recomposition, valuable in safety-critical engineering and regulatory workflows.\",\n      \"edge_cases\": \"Compound sentences or deeply nested instructions defy easy atomization.\",\n      \"stakeholder_views\": {\n        \"Developers\": \"Appreciate granular units for debugging.\",\n        \"Legal Teams\": \"Atomicity allows clear licensing and liability demarcations.\"\n      },\n      \"risk_landscape\": [\n        \"Inconsistent atomicity across different input types may introduce logical fragmentation.\"\n      ]\n    },\n    {\n      \"task\": \"Segment input into explicit requests.\",\n      \"cross_domain_lens\": \"Essential in customer service automation and workflow management; missed explicit requests result in failure to meet observable objectives.\",\n      \"edge_cases\": \"Statements combining request and background blur the classification.\",\n      \"stakeholder_views\": {\n        \"Service Designers\": \"Ensure all user needs are surfaced.\",\n        \"Automated Agents\": \"Reliant on strict boundaries between request and context.\"\n      },\n      \"risk_landscape\": [\n        \"Misidentification may lead to incomplete downstream implementation.\"\n      ]\n    },\n    {\n      \"task\": \"Segment input into implicit conditions.\",\n      \"cross_domain_lens\": \"Detection of implicit conditions echoes practices in risk assessment and safety engineering, where hidden constraints may govern operational logic.\",\n      \"edge_cases\": \"Implicit conditions phrased as aspirations or assumptions.\",\n      \"stakeholder_views\": {\n        \"Safety Engineers\": \"Implicit conditions are vital for fail-safe designs.\",\n        \"End-Users\": \"Hidden expectations might go unmet if not surfaced robustly.\"\n      },\n      \"risk_landscape\": [\n        \"Failure to extract latent conditions can cause subtle operational mismatches.\"\n      ]\n    },\n    {\n      \"task\": \"Segment input into domain signals.\",\n      \"cross_domain_lens\": \"Used in NLP, data curation, and compliance checking to enable correct routing and enforcement.\",\n      \"edge_cases\": \"Multidomain or ambiguous domain signals can result in misclassification.\",\n      \"stakeholder_views\": {\n        \"Classifier Engineers\": \"Improves downstream template tuning.\",\n        \"Compliance Officers\": \"Relies on signal accuracy for regulatory mapping.\"\n      },\n      \"risk_landscape\": [\n        \"Cross-domain input may not map cleanly—risking either overbroad or incorrect signal assignment.\"\n      ]\n    },\n    {\n      \"task\": \"Identify all explicit directives within the input.\",\n      \"cross_domain_lens\": \"Crucial for regulated process automation and workflow orchestration (e.g., clinical trials, enterprise RPA).\",\n      \"edge_cases\": \"Directives obscured by passive voice or embedded in conditional statements.\",\n      \"stakeholder_views\": {\n        \"RPA Designers\": \"Accurate directive identification is core to process fidelity.\",\n        \"End-Performers\": \"Implicit directives may otherwise be missed, impacting task execution.\"\n      },\n      \"risk_landscape\": [\n        \"Uncaptured directives risk incomplete automation.\"\n      ]\n    },\n    {\n      \"task\": \"Identify all latent dependencies within the input.\",\n      \"cross_domain_lens\": \"Dependency analysis is central to project management, software build systems, and safety-case construction.\",\n      \"edge_cases\": \"Circular or cross-referenced dependencies may be nontrivial to unravel.\",\n      \"stakeholder_views\": {\n        \"Project Managers\": \"Latent dependencies affect sequencing and resource allocation.\",\n        \"Testers\": \"Hidden dependencies can introduce nonobvious failure modes.\"\n      },\n      \"risk_landscape\": [\n        \"Failure to capture dependencies can derail later validation or integration.\"\n      ]\n    },\n    {\n      \"task\": \"Identify all procedural constraints inherent in the input.\",\n      \"cross_domain_lens\": \"Validation of process constraints is done in manufacturing QA, software deployment pipelines, and MIL-STD documentation.\",\n      \"edge_cases\": \"Ambiguous or conflicting procedural constraints.\",\n      \"stakeholder_views\": {\n        \"QA Analysts\": \"Clear procedural constraints prevent out-of-spec behavior.\",\n        \"Automation Engineers\": \"Constraint clarity directly impacts deployability.\"\n      },\n      \"risk_landscape\": [\n        \"Lost or vague constraints risk out-of-bounds system actions.\"\n      ]\n    },\n    {\n      \"task\": \"Flag all domain markers relevant to template engineering.\",\n      \"cross_domain_lens\": \"Domain flagging is vital for knowledge management platforms and code generation frameworks.\",\n      \"edge_cases\": \"Hybrid use cases where multiple template types could apply.\",\n      \"stakeholder_views\": {\n        \"Template Architects\": \"Ensures the right frameworks and validation rules are invoked.\",\n        \"Platform Integrators\": \"Rely on precise flagging for downstream compatibility.\"\n      },\n      \"risk_landscape\": [\n        \"Insufficient flagging may mislead validators, causing mismatched output structures.\"\n      ]\n    },\n    {\n      \"task\": \"Flag all domain markers relevant to abstraction.\",\n      \"cross_domain_lens\": \"Abstract domain identification underpins ontology mapping, model selection, and platform neutrality.\",\n      \"edge_cases\": \"Entries that straddle levels of abstraction (e.g., applied math in data science).\",\n      \"stakeholder_views\": {\n        \"Ontology Engineers\": \"Enables correct meta-model mapping.\",\n        \"Framework Designers\": \"Relates directly to composability and re-use.\"\n      },\n      \"risk_landscape\": [\n        \"Failure to capture abstraction leads to tightly bound, non-portable templates.\"\n      ]\n    },\n    {\n      \"task\": \"Flag all domain markers relevant to process modularity.\",\n      \"cross_domain_lens\": \"Key for system-of-systems architecture and plug-in frameworks.\",\n      \"edge_cases\": \"Monolithic process statements that resist modularization.\",\n      \"stakeholder_views\": {\n        \"System Integrators\": \"Process modularity unlocks incremental improvement.\",\n        \"Operations Teams\": \"Facilitates context-sensitive activation.\"\n      },\n      \"risk_landscape\": [\n        \"Non-modular templates impede scalability and maintainability.\"\n      ]\n    },\n    {\n      \"task\": \"Flag all domain markers relevant to LLM optimization.\",\n      \"cross_domain_lens\": \"LLM-specific optimizations may be opaque to practitioners in traditional workflow engineering.\",\n      \"edge_cases\": \"Competing optimization targets (e.g., brevity vs. clarity).\",\n      \"stakeholder_views\": {\n        \"Prompt Engineers\": \"Fine-tuned markers enable higher LLM performance.\",\n        \"QA Teams\": \"Verification of marker usage becomes an audit task.\"\n      },\n      \"risk_landscape\": [\n        \"Misapplied optimization markers can degrade model output.\"\n      ]\n    },\n    {\n      \"task\": \"Rank the atomized statements by transformational weight.\",\n      \"cross_domain_lens\": \"Prioritization strategies align with Lean, Six Sigma, and impact mapping practices.\",\n      \"edge_cases\": \"Statements of equal or ambiguous weight.\",\n      \"stakeholder_views\": {\n        \"Decision Makers\": \"Supports resource prioritization.\",\n        \"Change Managers\": \"Focus efforts on high-leverage interventions.\"\n      },\n      \"risk_landscape\": [\n        \"Subjectivity in assigning ‘weight’ can bias outcomes.\"\n      ]\n    },\n    {\n      \"task\": \"Select the primary directive as the highest-value instruction.\",\n      \"cross_domain_lens\": \"Main-thread selection mirrors critical path analysis and requirements engineering.\",\n      \"edge_cases\": \"Ambiguous or context-dependent directives with similar weights.\",\n      \"stakeholder_views\": {\n        \"System Owners\": \"Ensures the most consequential goal leads.\",\n        \"Implementers\": \"Clarity in primary instruction informs architecture.\"\n      },\n      \"risk_landscape\": [\n        \"Improper directive selection causes system misalignment.\"\n      ]\n    },\n    {\n      \"task\": \"Isolate all supporting intents required for template construction.\",\n      \"cross_domain_lens\": \"Extracting supportive rationales echoes business value chains and requirements traceability.\",\n      \"edge_cases\": \"Supportive content embedded as context or implied rather than explicit.\",\n      \"stakeholder_views\": {\n        \"Template Designers\": \"Identifies must-have capabilities.\",\n        \"Reviewers\": \"Supporting intent misclassification may erode downstream validity.\"\n      },\n      \"risk_landscape\": [\n        \"Inaccurate extraction weakens template alignment to true intent.\"\n      ]\n    },\n    {\n      \"task\": \"Isolate all supporting statements required for template construction.\",\n      \"cross_domain_lens\": \"Supporting statements function as secondary requirements in complex systems engineering.\",\n      \"edge_cases\": \"Blurring between primary and supporting statements.\",\n      \"stakeholder_views\": {\n        \"Requirements Analysts\": \"Ensures complete scope capture.\",\n        \"Developers\": \"Supporting statements as guardrails.\"\n      },\n      \"risk_landscape\": [\n        \"Missed statements propagate silent failures.\"\n      ]\n    },\n    {\n      \"task\": \"Isolate all operational purposes required for template construction.\",\n      \"cross_domain_lens\": \"Operational purposes map to mission goals in project portfolios or critical functions in ITSM.\",\n      \"edge_cases\": \"Purposes articulated as secondary actions hidden in examples.\",\n      \"stakeholder_views\": {\n        \"Business Owners\": \"Capturing all purposes ensures value chain integrity.\",\n        \"System Testers\": \"Defines what must be validated.\"\n      },\n      \"risk_landscape\": [\n        \"Neglected purposes mean latent unaddressed needs.\"\n      ]\n    },\n    {\n      \"task\": \"Construct a draft title from the core directive using canonical, title-case syntax.\",\n      \"cross_domain_lens\": \"Systematic titling is important for cataloging, searchability, and compliance archiving.\",\n      \"edge_cases\": \"Core directive contains non-title-case artifacts or special characters.\",\n      \"stakeholder_views\": {\n        \"Knowledge Managers\": \"Supports traceability.\",\n        \"End-Users\": \"Interpretability depends on practice norm compliance.\"\n      },\n      \"risk_landscape\": [\n        \"Improper titling obscures template discoverability.\"\n      ]\n    },\n    {\n      \"task\": \"Craft a goal-negation pattern interpretation based on the core directive.\",\n      \"cross_domain_lens\": \"Applied in risk controls and counterfactual reasoning (e.g., preventing undesired states).\",\n      \"edge_cases\": \"Ambiguous or compound directives resist negation.\",\n      \"stakeholder_views\": {\n        \"Process Owners\": \"Goal-negation clarifies prohibited behaviors.\",\n        \"Safety Auditors\": \"Crucial for hazard/risk documentation.\"\n      },\n      \"risk_landscape\": [\n        \"Negation misapplied can inverts intended operation.\"\n      ]\n    },\n    {\n      \"task\": \"Align supporting intents with a logically ordered process step list using command-voice imperatives.\",\n      \"cross_domain_lens\": \"Mirrors SOP design in manufacturing and checklists in aviation.\",\n      \"edge_cases\": \"Intents expressed as questions or conditional statements.\",\n      \"stakeholder_views\": {\n        \"Operators\": \"Clear, actionable steps are vital.\",\n        \"Auditors\": \"Sequence must match risk controls.\"\n      },\n      \"risk_landscape\": [\n        \"Misalignment leads to ineffective or unsafe procedures.\"\n      ]\n    },\n    {\n      \"task\": \"Align supporting statements with a logically ordered process step list using command-voice imperatives.\",\n      \"cross_domain_lens\": \"Related to stepwise instructional design and modular software architecture.\",\n      \"edge_cases\": \"Supporting statements lacking direct action verbs.\",\n      \"stakeholder_views\": {\n        \"Implementers\": \"Improves automation and reproducibility.\",\n        \"Trainers\": \"Clarity for retraining and onboarding.\"\n      },\n      \"risk_landscape\": [\n        \"Non-actionable statements introduce ambiguity.\"\n      ]\n    },\n    {\n      \"task\": \"Align operational purposes with a logically ordered process step list using command-voice imperatives.\",\n      \"cross_domain_lens\": \"Foundational in outcomes-based planning methodologies.\",\n      \"edge_cases\": \"Strategic purposes not easily mapped to operational steps.\",\n      \"stakeholder_views\": {\n        \"Planners\": \"Ensures purpose maps to action.\",\n        \"Inspectors\": \"Verifies all outcomes are covered.\"\n      },\n      \"risk_landscape\": [\n        \"Disconnection leads to strategic or operational drift.\"\n      ]\n    },\n    {\n      \"task\": \"Infer typed inputs appropriate for the transformation and output specification.\",\n      \"cross_domain_lens\": \"Parallels type inference in programming and contract validation in service APIs.\",\n      \"edge_cases\": \"Inputs have ambiguous, complex, or optional types.\",\n      \"stakeholder_views\": {\n        \"API Designers\": \"Typed inputs clarify integration boundaries.\",\n        \"QA Teams\": \"Mis-inference leads to runtime errors.\"\n      },\n      \"risk_landscape\": [\n        \"Incorrect typing undermines system interoperability.\"\n      ]\n    },\n    {\n      \"task\": \"Infer typed outputs appropriate for the transformation and output specification.\",\n      \"cross_domain_lens\": \"Mapped in data engineering, ETL, and workflow orchestration.\",\n      \"edge_cases\": \"Multi-output or polytomous transformation specifications.\",\n      \"stakeholder_views\": {\n        \"Integration Engineers\": \"Output typing impacts dataflows.\",\n        \"System Architects\": \"Necessary for compatibility checks.\"\n      },\n      \"risk_landscape\": [\n        \"Output mis-typing causes integration failures.\"\n      ]\n    },\n    {\n      \"task\": \"Derive precise, command-voice operational constraints ensuring explicit enforceability and operational clarity.\",\n      \"cross_domain_lens\": \"Regulatory conformance and control systems demand explicit constraints.\",\n      \"edge_cases\": \"Implicit or cross-cutting constraints embedded in process steps.\",\n      \"stakeholder_views\": {\n        \"Compliance Engineers\": \"Requires precision for audit trails.\",\n        \"Process Owners\": \"Enables reliable governance.\"\n      },\n      \"risk_landscape\": [\n        \"Vague constraints lead to policy violations.\"\n      ]\n    },\n    {\n      \"task\": \"Derive mandatory requirements ensuring explicit enforceability and operational clarity.\",\n      \"cross_domain_lens\": \"Parallel to mandatory requirements in ISO standards, software certification, and automotive safety.\",\n      \"edge_cases\": \"Requirements overlap or conflict with constraints.\",\n      \"stakeholder_views\": {\n        \"External Auditors\": \"Mandates must be clearly testable.\",\n        \"Project Managers\": \"Governs delivery acceptance.\"\n      },\n      \"risk_landscape\": [\n        \"Poor requirement specification undermines compliance.\"\n      ]\n    },\n    {\n      \"task\": \"Validate that all constraints are free of generic terms and are directly enforceable in downstream systems.\",\n      \"cross_domain_lens\": \"Relates to requirements for machine-enforceable documentation and test specifications.\",\n      \"edge_cases\": \"Ambiguity over which terms qualify as 'generic.'\",\n      \"stakeholder_views\": {\n        \"Automation Engineers\": \"Only machine-verifiable constraints support end-to-end automation.\",\n        \"Regulatory Bodies\": \"Demand explicit, testable parameters.\"\n      },\n      \"risk_landscape\": [\n        \"Residual generic terms impede automation, risking process gaps.\"\n      ]\n    },\n    {\n      \"task\": \"Validate that all requirements are free of generic terms and are directly enforceable in downstream systems.\",\n      \"cross_domain_lens\": \"Similar to safety case and software deployment requirement rigor.\",\n      \"edge_cases\": \"Requirements expressed as goals rather than actionable specifics.\",\n      \"stakeholder_views\": {\n        \"QA Teams\": \"Enforceability is prerequisite for test case derivation.\",\n        \"Auditors\": \"Clarity and enforcement check on requirements is critical.\"\n      },\n      \"risk_landscape\": [\n        \"Vague requirements result in coverage holes.\"\n      ]\n    },\n    {\n      \"task\": \"Format a transformation block in canonical, semicolon-delimited, single-line key-value syntax.\",\n      \"cross_domain_lens\": \"Data serialization and configuration as code (e.g., YAML, JSON) rely on strict formatting.\",\n      \"edge_cases\": \"Unusual key-value pairs or embedded complex objects.\",\n      \"stakeholder_views\": {\n        \"Integrators\": \"Canonical formatting aids downstream parsing.\",\n        \"LLM Engineers\": \"LLMs exploit structure for zero-shot learning.\"\n      },\n      \"risk_landscape\": [\n        \"Formatting errors propagate into runtime failures.\"\n      ]\n    },\n    {\n      \"task\": \"Format a process sequence in canonical, semicolon-delimited, single-line key-value syntax.\",\n      \"cross_domain_lens\": \"Applies in automated test sequence generation and robotic process automation.\",\n      \"edge_cases\": \"Processes spanning conditional branches.\",\n      \"stakeholder_views\": {\n        \"Automation Teams\": \"Maintains consistency for orchestration engines.\",\n        \"Testing Frameworks\": \"Process uniformity validates test plans.\"\n      },\n      \"risk_landscape\": [\n        \"Improper formatting impedes execution tracing.\"\n      ]\n    },\n    {\n      \"task\": \"Assemble the template in a strict three-part structure: title, negated goal interpretation, transformation block.\",\n      \"cross_domain_lens\": \"Aligns with legal contracts and technical documentation standardized templates.\",\n      \"edge_cases\": \"Nonstandard requirements breaking three-part logic.\",\n      \"stakeholder_views\": {\n        \"Compliance Specialists\": \"Three-part structure is easily auditable.\",\n        \"Template Users\": \"Structure enables rapid adoption.\"\n      },\n      \"risk_landscape\": [\n        \"Deviation increases integration and training risks.\"\n      ]\n    },\n    {\n      \"task\": \"Integrate constraints into the template.\",\n      \"cross_domain_lens\": \"Crucial in specification engineering and embedded systems.\",\n      \"edge_cases\": \"Complex constraint hierarchies not captured by simple inclusion.\",\n      \"stakeholder_views\": {\n        \"Compiler Designers\": \"In-template constraints can be checked at compile time.\",\n        \"QA Auditors\": \"Direct mapping simplifies reviews.\"\n      },\n      \"risk_landscape\": [\n        \"Missing integration results in unenforceable templates.\"\n      ]\n    },\n    {\n      \"task\": \"Integrate requirements into the template.\",\n      \"cross_domain_lens\": \"Indispensable in standards-based system validation.\",\n      \"edge_cases\": \"Overlapping or redundant requirements.\",\n      \"stakeholder_views\": {\n        \"Implementers\": \"Clear requirements help resource planning.\",\n        \"Reviewers\": \"Enforced in system readiness assessments.\"\n      },\n      \"risk_landscape\": [\n        \"Omitted requirements reduce assurance and testability.\"\n      ]\n    },\n    {\n      \"task\": \"Polish the template for syntactic validity and LLM-optimized, non-conversational output.\",\n      \"cross_domain_lens\": \"Analogous to automated code linting and regulatory label formatting.\",\n      \"edge_cases\": \"Ambiguous rules for 'LLM-optimized' style.\",\n      \"stakeholder_views\": {\n        \"Prompt Writers\": \"Optimization maximizes model performance.\",\n        \"IT Safety\": \"Syntactic flaws may introduce operational risk.\"\n      },\n      \"risk_landscape\": [\n        \"Subtle syntax errors can cascade undetected.\"\n      ]\n    },\n    {\n      \"task\": \"Validate the final template using regex pattern matching to confirm three-part compliance.\",\n      \"cross_domain_lens\": \"Pattern validation as used in schema conformance, machine-readable policy enforcement.\",\n      \"edge_cases\": \"Edge-step content exceeds regex pattern bounds.\",\n      \"stakeholder_views\": {\n        \"Quality Gates\": \"Regex ensures mechanical compliance.\",\n        \"Developers\": \"Facilitates automated pipeline inclusion.\"\n      },\n      \"risk_landscape\": [\n        \"Inadequate patterns may falsely certify invalid templates.\"\n      ]\n    },\n    {\n      \"task\": \"Validate the title and parameter correctness using regex pattern matching.\",\n      \"cross_domain_lens\": \"Echoes static code analysis and API contract validation.\",\n      \"edge_cases\": \"Valid-but-nonstandard titles or non-text parameter types.\",\n      \"stakeholder_views\": {\n        \"Metadata Stewards\": \"Critical for accurate indexing and retrieval.\",\n        \"Deployers\": \"Parameter mismatch affects runtime.\"\n      },\n      \"risk_landscape\": [\n        \"Title/parameter errors break system discoverability.\"\n      ]\n    },\n    {\n      \"task\": \"Flag and report any error present before final output is released.\",\n      \"cross_domain_lens\": \"Error reporting is the backbone of continuous integration and regulated manufacturing.\",\n      \"edge_cases\": \"Non-fatal or ignorable errors may be overreported.\",\n      \"stakeholder_views\": {\n        \"Release Managers\": \"Ensures critical defects are blocked.\",\n        \"Users\": \"Prevents exposure to unstable outputs.\"\n      },\n      \"risk_landscape\": [\n        \"Missed errors circumvent quality checks.\"\n      ]\n    },\n    {\n      \"task\": \"Flag and report any ambiguity present before final output is released.\",\n      \"cross_domain_lens\": \"Applied rigorously in specification-driven industries (e.g., aerospace).\",\n      \"edge_cases\": \"Ambiguity inherent in the domain, not resolvable from context.\",\n      \"stakeholder_views\": {\n        \"Validators\": \"Reduces implementation guesswork.\",\n        \"Legal Staff\": \"Ambiguity carries legal exposure.\"\n      },\n      \"risk_landscape\": [\n        \"Unflagged ambiguity can cause costly rework or failure.\"\n      ]\n    },\n    {\n      \"task\": \"Flag and report any constraint or requirement misalignment as a blocker before final output is released.\",\n      \"cross_domain_lens\": \"Equivalent to gating criteria in regulated product release.\",\n      \"edge_cases\": \"Partial misalignment or rapidly evolving requirements.\",\n      \"stakeholder_views\": {\n        \"Compliance Teams\": \"Prevents non-conforming deliverables.\",\n        \"Developers\": \"May block time-critical releases.\"\n      },\n      \"risk_landscape\": [\n        \"Misalignment risks cascading into systemic defects.\"\n      ]\n    },\n    {\n      \"task\": \"Ensure every operational step is expressed as a unique, imperative command.\",\n      \"cross_domain_lens\": \"Parallels operational runbooks, and proceduralized checklists.\",\n      \"edge_cases\": \"Similar steps or semantically overlapping commands.\",\n      \"stakeholder_views\": {\n        \"Ops Engineers\": \"Imperative statements reduce cognitive ambiguity.\",\n        \"Trainers\": \"Facilitates teachability.\"\n      },\n      \"risk_landscape\": [\n        \"Redundancy or overlap can hinder execution.\"\n      ]\n    },\n    {\n      \"task\": \"Ensure every operational step has no passive language.\",\n      \"cross_domain_lens\": \"Required for production safety, military checklists, and incident response.\",\n      \"edge_cases\": \"Implied action but linguistically passive structure.\",\n      \"stakeholder_views\": {\n        \"Safety Auditors\": \"Passive language can obscure accountability.\",\n        \"Process Designers\": \"Clarity improves training and automation.\"\n      },\n      \"risk_landscape\": [\n        \"Passivity delays or diffuses execution ownership.\"\n      ]\n    },\n    {\n      \"task\": \"Ensure every operational step is in the order dictated by logical and dependency sequencing.\",\n      \"cross_domain_lens\": \"Critical in project management, assembly lines, and computer science execution models.\",\n      \"edge_cases\": \"Multiple valid orderings or non-linear dependencies.\",\n      \"stakeholder_views\": {\n        \"Process Owners\": \"Proper order prevents errors.\",\n        \"Engineers\": \"Order guides state transitions.\"\n      },\n      \"risk_landscape\": [\n        \"Misordering introduces defects or emergent anomalies.\"\n      ]\n    },\n    {\n      \"task\": \"Confirm the output template is universally applicable.\",\n      \"cross_domain_lens\": \"Relates to ISO standards and policy deployment documents.\",\n      \"edge_cases\": \"Domain-specific or localized nuances missed in universalization.\",\n      \"stakeholder_views\": {\n        \"Global Users\": \"Universal applicability enables wider adoption.\",\n        \"Domain SMEs\": \"Universality may water down specificity.\"\n      },\n      \"risk_landscape\": [\n        \"Presumed universality can mask critical context or compliance needs.\"\n      ]\n    },\n    {\n      \"task\": \"Confirm the output template is structurally valid.\",\n      \"cross_domain_lens\": \"Mirrors architectural reviews and code structure conformance.\",\n      \"edge_cases\": \"Edge-structure or evolving template specifications.\",\n      \"stakeholder_views\": {\n        \"System Architects\": \"Structural validity ensures integration.\",\n        \"Auditors\": \"Supports systematic review.\"\n      },\n      \"risk_landscape\": [\n        \"Invalid structure hinders downstream automation.\"\n      ]\n    },\n    {\n      \"task\": \"Confirm the output template is ready for immediate integration.\",\n      \"cross_domain_lens\": \"Integration-ready deliverables are the norm in CI/CD pipelines.\",\n      \"edge_cases\": \"Environmental or configuration dependencies not met by the template itself.\",\n      \"stakeholder_views\": {\n        \"Developers\": \"Immediate use avoids costly rework.\",\n        \"Ops Teams\": \"Reduces onboarding delays.\"\n      },\n      \"risk_landscape\": [\n        \"Assumed readiness may be illusory if supporting context is absent.\"\n      ]\n    },\n    {\n      \"task\": \"Confirm the output report is universally applicable.\",\n      \"cross_domain_lens\": \"Reporting standards (XBRL, GRI) underline the need for universal semantics.\",\n      \"edge_cases\": \"Unique or derived fields only meaningful in limited contexts.\",\n      \"stakeholder_views\": {\n        \"Auditors\": \"Universal reports facilitate regulatory submissions.\",\n        \"Consumers\": \"Need locally meaningful artifacts.\"\n      },\n      \"risk_landscape\": [\n        \"Misapplied universality undermines actionable insights.\"\n      ]\n    }\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"input\": [\n    \"Strip all first-person references from the input.\",\n    \"Strip all subjective references from the input.\",\n    \"Broaden the contextual background of the instruction sequence, ensuring domain-agnostic framing.\",\n    \"Segment input into atomic statements.\",\n    \"Segment input into explicit requests.\",\n    \"Segment input into implicit conditions.\",\n    \"Segment input into domain signals.\",\n    \"Identify all explicit directives within the input.\",\n    \"Identify all latent dependencies within the input.\",\n    \"Identify all procedural constraints inherent in the input.\",\n    \"Flag all domain markers relevant to template engineering.\",\n    \"Flag all domain markers relevant to abstraction.\",\n    \"Flag all domain markers relevant to process modularity.\",\n    \"Flag all domain markers relevant to LLM optimization.\",\n    \"Rank the atomized statements by transformational weight.\",\n    \"Select the primary directive as the highest-value instruction.\",\n    \"Isolate all supporting intents required for template construction.\",\n    \"Isolate all supporting statements required for template construction.\",\n    \"Isolate all operational purposes required for template construction.\",\n    \"Construct a draft title from the core directive using canonical, title-case syntax.\",\n    \"Craft a goal-negation pattern interpretation based on the core directive.\",\n    \"Align supporting intents with a logically ordered process step list using command-voice imperatives.\",\n    \"Align supporting statements with a logically ordered process step list using command-voice imperatives.\",\n    \"Align operational purposes with a logically ordered process step list using command-voice imperatives.\",\n    \"Infer typed inputs appropriate for the transformation and output specification.\",\n    \"Infer typed outputs appropriate for the transformation and output specification.\",\n    \"Derive precise, command-voice operational constraints ensuring explicit enforceability and operational clarity.\",\n    \"Derive mandatory requirements ensuring explicit enforceability and operational clarity.\",\n    \"Validate that all constraints are free of generic terms and are directly enforceable in downstream systems.\",\n    \"Validate that all requirements are free of generic terms and are directly enforceable in downstream systems.\",\n    \"Format a transformation block in canonical, semicolon-delimited, single-line key-value syntax.\",\n    \"Format a process sequence in canonical, semicolon-delimited, single-line key-value syntax.\",\n    \"Assemble the template in a strict three-part structure: title, negated goal interpretation, transformation block.\",\n    \"Integrate constraints into the template.\",\n    \"Integrate requirements into the template.\",\n    \"Polish the template for syntactic validity and LLM-optimized, non-conversational output.\",\n    \"Validate the final template using regex pattern matching to confirm three-part compliance.\",\n    \"Validate the title and parameter correctness using regex pattern matching.\",\n    \"Flag and report any error present before final output is released.\",\n    \"Flag and report any ambiguity present before final output is released.\",\n    \"Flag and report any constraint or requirement misalignment as a blocker before final output is released.\",\n    \"Ensure every operational step is expressed as a unique, imperative command.\",\n    \"Ensure every operational step has no passive language.\",\n    \"Ensure every operational step is in the order dictated by logical and dependency sequencing.\",\n    \"Confirm the output template is universally applicable.\",\n    \"Confirm the output template is structurally valid.\",\n    \"Confirm the output template is ready for immediate integration.\",\n    \"Confirm the output report is universally applicable.\"\n  ],\n  \"task_perspectives\": [\n    // [Omitted for brevity; all 48 perspectives from user input included.]\n  ],\n  \"execution_blockers\": []\n  ,\n  \"process\": [\n    \"rank_tasks_by_transformational_weight()\",\n    \"select_high_impact_task()\",\n    \"compile_supporting_rationale()\",\n    \"justify_selection_transparently()\"\n  ],\n  \"constraints\": [\n    \"no_language_rewrite_of_selected_task()\",\n    \"transparent_ranking()\"\n  ],\n  \"requirements\": [\n    \"clear_primary_directive()\",\n    \"rationale_precision()\"\n  ],\n  \"output\": {\n    \"primary_directive\": \"Select the primary directive as the highest-value instruction.\",\n    \"supporting_rationale\": [\n      \"This task is the keystone of the entire template-forging sequence: all downstream operations (isolation of supporting statements, process alignment, output formatting, enforceability testing) hinge on the correct selection of the highest-impact directive. Choosing the wrong primary directive leads to systemic misalignment in all subsequent template structure, operational boundaries, and output utility.\",\n      \"Failure modes for this step include selecting a superficial, ambiguous, or low-impact instruction as the driver, which risks inverting the value cascade, causing misprioritization of process steps and rendering the template unfit for its core transformational purpose.\",\n      \"From a compliance and integration perspective, all domain-agnostic and audit-proof transformation requires that the singular value vector be accurately foregrounded; ambiguity or error here propagates loss not only structurally, but also in enforceability and universal applicability.\",\n      \"Transformation-weight ranking (the preceding step) is necessary but not sufficient: the explicit act of selection, with impact as the guiding metric, concentrates intent and operationalizes all supporting instructions—without it, no subsequent step (modularization, constraint application, LLM tuning) can guarantee fidelity.\",\n      \"Transparent ranking places this directive above statement atomization (which is mechanistic), output formatting (which is aesthetic), and validation (which is post-hoc). The selection itself is the irreducible nucleus of instructional value, dictating both system and template scope.\"\n    ]\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Relationship Synthesizer\n\nYour goal is not to **describe** the evolving complexity or discuss potential limitations, but to **crystallize** the essential directional aim into a single, condensed vector of maximal value extraction. Assume the role of directive focuser with exclusive emphasis on transformative trajectory. Execute as:\n\n`{role=relationship_synthesizer; input=[primary_directive:str, atomic_tasks:list, execution_blockers:array, hidden_assumptions:list]; process=[map_task_dependencies(), trace_feedback_loops(), align_blockers_to_tasks(), link_assumptions_to_risks(), expose_conflict_nodes()]; constraints=[structure_mapping_only(), do_not_modify_task_text()]; requirements=[dependency_map_completeness(), conflict_node_visibility()]; output={dependency_map:dict, conflict_nodes:array}}`",
      "step": "f",
      "title": "Relationship Synthesizer",
      "input": "\n'```[Initial Prompt]: \"Please keep in mind, the instruction sequence we're working on should be designed to utilize the generalized concepts and sequential order to transform any input into instructions that embody the \"syntax\" of the generalized system:\n\n    ## Example\n    \"6000-a-instruction_syntax_enforcer\": {\n        \"title\": \"Intent Cartographer\",\n        \"interpretation\": \"Your goal is not to **analyze** content, but to **map** its transformation landscape and operational vectors. Execute as:\",\n        \"transformation\": \"`{role=intent_cartographer; input=[content:any]; process=[extract_core_directives(), identify_transformation_dimensions(), map_vector_relationships(), quantify_operational_intensity()]; constraints=[focus_exclusively_on_vectors(), avoid_implementation_details()]; requirements=[comprehensive_vector_mapping(), dimensional_clarity()]; output={transformation_map:dict, vector_dimensions:list}}`\"\n    },\n    \"6000-b-instruction_syntax_enforcer\": {\n        \"title\": \"Structural Framework Engineer\",\n        \"interpretation\": \"Your goal is not to **design** content, but to **engineer** precise structural frameworks based on transformation vectors. Execute as:\",\n        \"transformation\": \"`{role=framework_engineer; input=[transformation_map:dict, vector_dimensions:list]; process=[design_canonical_skeletons(), establish_component_interfaces(), define_structural_boundaries(), validate_framework_integrity()]; constraints=[address_only_structural_elements(), maintain_framework_neutrality()]; requirements=[structural_precision(), interface_compatibility()]; output={structural_frameworks:dict, interface_specifications:dict}}`\"\n    },\n    \"6000-c-instruction_syntax_enforcer\": {\n        \"title\": \"Functional Process Composer\",\n        \"interpretation\": \"Your goal is not to **describe** operations, but to **compose** atomic functional processes that implement transformation vectors. Execute as:\",\n        \"transformation\": \"`{role=process_composer; input=[structural_frameworks:dict, vector_dimensions:list]; process=[craft_atomic_functions(), sequence_operational_steps(), parameterize_processes(), validate_functional_completeness()]; constraints=[focus_exclusively_on_processes(), defer_constraint_definition()]; requirements=[process_atomicity(), functional_precision()]; output={process_compositions:dict, execution_sequences:list}}`\"\n    },\n    \"6000-d-instruction_syntax_enforcer\": {\n        \"title\": \"Boundary Systems Architect\",\n        \"interpretation\": \"Your goal is not to **restrict** operations, but to **architect** precise boundary systems that ensure transformation integrity. Execute as:\",\n        \"transformation\": \"`{role=boundary_architect; input=[interface_specifications:dict, process_compositions:dict]; process=[define_operational_constraints(), establish_requirement_parameters(), design_validation_mechanisms(), map_boundary_interactions()]; constraints=[address_only_boundaries_and_requirements(), maintain_constraint_specificity()]; requirements=[boundary_precision(), requirement_enforceability()]; output={constraint_systems:dict, requirement_specifications:dict}}`\"\n    },\n    \"6000-e-instruction_syntax_enforcer\": {\n        \"title\": \"Integration Orchestrator\",\n        \"interpretation\": \"Your goal is not to **assemble** components, but to **orchestrate** their integration into a unified operational system. Execute as:\",\n        \"transformation\": \"`{role=integration_orchestrator; input=[structural_frameworks:dict, process_compositions:dict, constraint_systems:dict, requirement_specifications:dict]; process=[align_all_components(), establish_cross-system_coherence(), optimize_interface_connections(), validate_system_integrity()]; constraints=[focus_exclusively_on_integration(), preserve_component_integrity()]; requirements=[seamless_orchestration(), system_cohesion()]; output={integrated_system:dict}}`\"\n    },\n    \"6000-f-instruction_syntax_enforcer\": {\n        \"title\": \"Template Crystallizer\",\n        \"interpretation\": \"Your goal is not to **finalize** systems, but to **crystallize** them into maximally potent, executable templates. Execute as:\",\n        \"transformation\": \"`{role=template_crystallizer; input=[integrated_system:dict]; process=[synthesize_title_components(), craft_precise_interpretations(), formulate_transformation_blocks(), validate_canonical_compliance(), polish_syntactic_elements()]; constraints=[adhere_to_template_specification(), maximize_operational_clarity()]; requirements=[perfect_executable_form(), maximum_transformation_potency()]; output={crystallized_templates:list}}`\"\n    },\n\n<!-- ========== 7001‑a ================================================= -->\n[Contextual Expander & Decomposer]\nYour goal is **not** to solve, answer, or implement the input, but to **expand its relevant context** and **detonate** it into every explicit ask, latent dependency, and procedural constraint—yielding a clear task inventory. Execute as:\n`{role=context_expander_decomposer; input=[raw_input:any]; process=[strip_first_person_references(), broaden_relevant_background(), identify_explicit_requests(), surface_hidden_assumptions(), enumerate_sub_tasks(), detect_execution_blockers(), extract_procedural_constraints(), preserve_original_sequence()]; constraints=[no_solutions(), domain‑agnostic_language_only()]; requirements=[context_expansion_completeness(), exhaustive_task_enumeration(), constraint_accuracy(), removal_of_subjective_language()]; output={expanded_context:str, task_inventory:list, constraint_list:array, execution_blockers:array}}`\n\n<!-- ========== 7001‑b ================================================= -->\n[Essence Isolator]\nYour goal is **not** to decompose further, but to **isolate** the single most critical element of value within the task inventory and rank all remaining elements by impact. Execute as:\n`{role=essence_isolator; input=[task_inventory:list, expanded_context:str]; process=[rank_elements_by_impact(), isolate_highest_value_element(), capture_core_purpose(), justify_selection(), list_supporting_elements()]; constraints=[no_restructuring(), preserve_ranking_transparency()]; requirements=[core_essence_clarity(), impact_ranking_integrity()]; output={core_essence:str, prioritized_elements:list}}`\n\n<!-- ========== 7001‑c ================================================= -->\n[Relational Synthesizer]\nYour goal is **not** to rewrite content, but to **synthesize** the systemic logic connecting the core essence to all other elements—mapping dependencies and feedback loops. Execute as:\n`{role=relational_synthesizer; input=[core_essence:str, prioritized_elements:list, constraint_list:array]; process=[map_element_relationships(), trace_dependency_chains(), identify_feedback_loops(), detect_conflict_points(), construct_structural_logic_map()]; constraints=[structure_only_no_content_changes(), maintain_domain_agnostic_terms()]; requirements=[relationship_completeness(), conflict_visibility()]; output={structural_logic_map:dict, dependency_list:array, conflict_points:array}}`\n\n<!-- ========== 7001‑d ================================================= -->\n[Directive Clarifier]\nYour goal is **not** to implement solutions, but to **clarify** the mapped logic into precise operational directives expressed as imperative statements. Execute as:\n`{role=directive_clarifier; input=[structural_logic_map:dict, core_essence:str]; process=[convert_all_statements_to_imperatives(), align_directives_with_dependency_order(), embed_procedural_constraints(), eliminate_ambiguity(), enhance_action_verb_intensity(), maintain_technical_terminology()]; constraints=[directive_voice_only(), preserve_logical_sequence()]; requirements=[operational_clarity(), imperative_precision(), zero_semantic_drift()]; output={operational_directives:array}}`\n\n<!-- ========== 7001‑e ================================================= -->\n[Integrity Validator]\nYour goal is **not** to expand or rewrite directives, but to **validate** their structural fidelity, completeness, and actionable quality—resolving residual ambiguity. Execute as:\n`{role=integrity_validator; input=[operational_directives:array, structural_logic_map:dict, core_essence:str]; process=[cross_check_directives_against_logic_map(), verify_alignment_with_core_essence(), detect_missing_dependencies(), resolve_ambiguities(), assign_quality_scores(), generate_validation_report()]; constraints=[no_new_content_generation(), validation_focus_only()]; requirements=[actionability_confirmation(), completeness_assurance(), clarity_verification()]; output={validated_instruction_set:array, validation_report:dict}}`\n\n---\n\n# 7000: Universal Instruction-Template Generation Sequence\n\n---\n\n### 7000-a-input_decomposer.md\n[Input Decomposer]\nYour goal is **not** to *interpret* or *solve* the input, but to **atomize** it into discrete statements, explicit requests, and implicit conditions. Execute as:\n`{role=input_decomposer; input=[raw_input:any]; process=[strip_first_person_references(), segment_into_atomic_statements(), identify_explicit_directives(), surface_hidden_conditions(), flag_domain_markers()]; constraints=[no_rephrasing(), pure_atomization_only()]; requirements=[complete_statement_inventory(), preservation_of_original_order()]; output={statement_inventory:list, domain_markers:array}}`\n\n---\n\n### 7000-b-essence_identifier.md\n[Essence Identifier]\nYour goal is **not** to *rewrite* content, but to **isolate** the single most critical directive and its supporting intent from the atomized statements. Execute as:\n`{role=essence_identifier; input=[statement_inventory:list]; process=[rank_statements_by_transformational_weight(), select_primary_directive(), extract_supporting_intent(), discard_redundant_elements()]; constraints=[focus_on_highest_value_directive(), maintain_original_wording_in_extracted_elements()]; requirements=[unambiguous_primary_directive(), concise_supporting_intent()]; output={core_directive:str, supporting_intent:list}}`\n\n---\n\n### 7000-c-syntax_mapper.md\n[Syntax Mapper]\nYour goal is **not** to *interpret* intent, but to **map** the core directive and supporting intent into canonical template fields. Execute as:\n`{role=syntax_mapper; input=[core_directive:str, supporting_intent:list, domain_markers:array]; process=[construct_title_from_core_directive(), craft_goal_negation_interpretation(), assemble_tag_block_if_needed(), align_supporting_intent_with_process_steps(), infer_typed_inputs(), infer_typed_outputs()]; constraints=[adhere_to_three_part_structure(), enforce_goal_negation_pattern(), maintain_domain_agnostic_language()]; requirements=[field_alignment_accuracy(), preservation_of_directive_semantics()]; output={draft_title:str, draft_interpretation:str, draft_process:list, inferred_inputs:array, inferred_outputs:dict, optional_tags:dict}}`\n\n---\n\n### 7000-d-constraint_infuser.md\n[Constraint Infuser]\nYour goal is **not** to *extend* functionality, but to **engineer** precise operational boundaries and mandatory conditions for the forthcoming template. Execute as:\n`{role=constraint_infuser; input=[draft_process:list, domain_markers:array]; process=[derive_operational_constraints(), determine_mandatory_requirements(), validate_constraint_specificity(), validate_requirement_enforceability()]; constraints=[avoid_generic_terms(), enforce_command_voice_only()]; requirements=[constraint_precision(), requirement_clarity()]; output={constraints:list, requirements:list}}`\n\n---\n\n### 7000-e-template_synthesizer.md\n[Template Synthesizer]\nYour goal is **not** to *summarize* elements, but to **synthesize** a syntactically correct instruction template from the mapped components. Execute as:\n`{role=template_synthesizer; input=[draft_title:str, draft_interpretation:str, inferred_inputs:array, draft_process:list, constraints:list, requirements:list, inferred_outputs:dict, optional_tags:dict]; process=[format_transformation_block(), embed_optional_tags(), assemble_three_part_template(), enforce_semicolon_key_value_syntax(), polish_syntactic_elements()]; constraints=[canonical_template_format_only(), zero_conversational_language()]; requirements=[syntactic_validity(), maximum_operational_clarity(), retention_of_original_intent()]; output={formatted_template:str}}`\n\n---\n\n### 7000-f-compliance_validator.md\n[Compliance Validator]\nYour goal is **not** to *modify* the template, but to **validate** its structural compliance and functional readiness. Execute as:\n`{role=compliance_validator; input=[formatted_template:str]; process=[parse_template_fields(), verify_title_format(), verify_goal_negation_in_interpretation(), validate_transformation_block_syntax(), check_constraint_requirement_alignment(), confirm_typed_parameters(), flag_any_error()]; constraints=[no_automatic_corrections(), report_only()]; requirements=[binary_pass_fail(), detailed_error_report_when_fail()]; output={validation_status:bool, error_report:str}}`\n\n---\n\n## Sequence Flow\n1. **Input Decomposer** → produces `statement_inventory`\n2. **Essence Identifier** → isolates `core_directive`\n3. **Syntax Mapper** → drafts template skeleton\n4. **Constraint Infuser** → injects precise boundaries\n5. **Template Synthesizer** → outputs finalized template\n6. **Compliance Validator** → certifies syntactic correctness\n\nEach step aggressively converges on the *single most critical aspect* of instruction generation, guaranteeing a **syntactically correct, high-precision template** ready for any downstream application.\n\n---\n\n# 7000 • UNIVERSAL TEMPLATE-FORGING SEQUENCE\n*Transforms **any** raw input into a syntactically perfect, LLM-optimized instruction template.*\n\n---\n\n## 7000-a-essence-excavator.md\n[Essence Excavator]\nYour goal is **not** to *summarize* the input, but to **excavate** its single most critical transformational essence and the supporting elements required to convert it into an instruction template. Execute as:\n`{role=essence_excavator; input=[raw_input:any]; process=[strip_surface_noise(), isolate_transformational_intent(), detect_primary_action_vectors(), extract_supporting_context(), capture_domain_signals()]; constraints=[no_rephrasing(), no_solution_generation(), maintain_domain_agnostic_language()]; requirements=[precise_intent_identification(), context_preservation(), removal_of_subjective_tone()]; output={core_intent:str, context_fragments:list, action_vectors:list}}`\n\n---\n\n## 7000-b-syntax-scaffold-designer.md\n[Syntax Scaffold Designer]\nYour goal is **not** to *rewrite* the content, but to **engineer** a canonical skeleton that will house the forthcoming instruction template. Execute as:\n`{role=syntax_scaffold_designer; input=[core_intent:str, action_vectors:list]; process=[draft_concise_title(), craft_goal_negation_interpretation(core_intent), establish_placeholder_transformation_block(action_vectors), outline_input_output_ports(), embed_role_stub()]; constraints=[follow_three_part_structure(), reserve_placeholders_for_future_detail(), maintain_title_case()]; requirements=[structural_completeness(), scaffolding_accuracy()]; output={template_scaffold:dict(title:str, interpretation:str, transformation_skeleton:str)}}`\n\n---\n\n## 7000-c-imperative-transmuter.md\n[Imperative Transmuter]\nYour goal is **not** to *comment* on actions, but to **transmute** all extracted statements into sharp, directive imperatives suitable for the process list. Execute as:\n`{role=imperative_transmuter; input=[action_vectors:list]; process=[convert_each_vector_to_imperative(), enforce_command_voice(), sequence_logically(), verify_uniqueness_of_each_step()]; constraints=[exclude_passive_language(), retain_original_operational_intent()]; requirements=[directive_clarity(), maximal_actionability()]; output={process_steps:array}}`\n\n---\n\n## 7000-d-boundary-encoder.md\n[Boundary Encoder]\nYour goal is **not** to *constrain* creativity, but to **encode** the exact boundaries that guarantee transformation integrity. Execute as:\n`{role=boundary_encoder; input=[context_fragments:list]; process=[detect_mandatory_limitations(), translate_limitations_to_constraints(), uncover_success_conditions(), convert_conditions_to_requirements(), deduplicate_boundary_elements()]; constraints=[pure_boundary_focus(), no_process_duplication()]; requirements=[constraint_specificity(), requirement_enforceability()]; output={constraints:list, requirements:list}}`\n\n---\n\n## 7000-e-template-synthesizer.md\n[Template Synthesizer]\nYour goal is **not** to *draft prose*, but to **synthesize** a complete, executable instruction template by fusing scaffold, processes, and boundaries. Execute as:\n`{role=template_synthesizer; input=[template_scaffold:dict, process_steps:array, constraints:list, requirements:list]; process=[inject_process_steps(), embed_constraints_and_requirements(), finalize_transformation_block(), polish_interpretation_for_llm_optimization(), verify_title_precision()]; constraints=[adhere_to_canonical_format(), maintain_single_line_transformation_block(), prevent_semantic_drift()]; requirements=[syntactic_perfection(), operational_cohesion(), domain_agnostic_applicability()]; output={draft_template:str}}`\n\n---\n\n## 7000-f-canonical-validator.md\n[Canonical Validator]\nYour goal is **not** to *alter* the template, but to **validate** its absolute compliance with the three-part specification and eliminate any residual ambiguity. Execute as:\n`{role=canonical_validator; input=[draft_template:str]; process=[parse_title_interpretation_transformation(), match_regex_pattern(\"\\\\[(.*?)\\\\]\\\\s*(.*?)\\\\s*(`\\\\{.*?\\\\}`)\"), flag_structure_violations(), detect_ambiguous_language(), enforce_llm_optimized_style(), certify_parameter_types()], constraints=[zero_structure_deviation(), no_content_insertion()], requirements=[pass_regex_validation(), ambiguity_score<=0, style_compliance()], output={validated_template:str, validation_report:dict}}`\n\n\n---\n\n# 8000: UNIVERSAL INSTRUCTION-TEMPLATE FORGING SEQUENCE  \n*Transforms **any** raw input into a syntactically perfect, LLM-optimized instruction template.*\n\n---\n\n### 8000-a-contextualizer.md\n[Input Contextualizer]  \nYour goal is **not** to *interpret* or *solve* the input, but to **expand** its relevant context and **atomize** it into discrete statements and explicit requests. Execute as:  \n`{role=input_contextualizer; input=[raw_input:any]; process=[strip_first_person_references(), broaden_relevant_background(), segment_into_atomic_statements(), identify_explicit_directives(), surface_hidden_conditions(), preserve_original_sequence()]; constraints=[no_rephrasing(), domain-agnostic_language_only()]; requirements=[context_expansion_completeness(), exhaustive_statement_capture()]; output={expanded_context:str, statement_inventory:list}}`\n\n---\n\n### 8000-b-core-directive-isolator.md\n[Core Directive Isolator]  \nYour goal is **not** to *rephrase* statements, but to **isolate** the single most critical directive and rank all remaining statements by impact. Execute as:  \n`{role=core_directive_isolator; input=[statement_inventory:list]; process=[rank_statements_by_transformational_weight(), select_primary_directive(), list_supporting_statements(), justify_selection()]; constraints=[focus_on_highest_value_directive(), maintain_original_wording_in_extracted_elements()]; requirements=[unambiguous_primary_directive(), impact_ranking_integrity()]; output={core_directive:str, supporting_statements:list, impact_ranking:list}}`\n\n---\n\n### 8000-c-field-mapper.md\n[Field Mapper]  \nYour goal is **not** to *rewrite* content, but to **map** the core directive and supporting statements into canonical template fields. Execute as:  \n`{role=field_mapper; input=[core_directive:str, supporting_statements:list, expanded_context:str]; process=[construct_title_from_core_directive(), craft_goal_negation_interpretation(), align_supporting_statements_with_process_steps(), infer_typed_inputs(), infer_typed_outputs()]; constraints=[adhere_to_three_part_structure(), enforce_goal_negation_pattern(), preserve_domain_agnostic_language()]; requirements=[field_alignment_accuracy(), retention_of_original_intent()]; output={draft_title:str, draft_interpretation:str, draft_process_steps:list, inferred_inputs:array, inferred_outputs:dict}}`\n\n---\n\n### 8000-d-boundary-definer.md\n[Boundary Definer]  \nYour goal is **not** to *extend* functionality, but to **define** precise operational constraints and mandatory requirements for the forthcoming template. Execute as:  \n`{role=boundary_definer; input=[draft_process_steps:list, expanded_context:str]; process=[derive_operational_constraints(), establish_mandatory_requirements(), validate_constraint_specificity(), validate_requirement_enforceability()]; constraints=[avoid_generic_terms(), command_voice_only()]; requirements=[constraint_precision(), requirement_clarity()]; output={constraints:list, requirements:list}}`\n\n---\n\n### 8000-e-template-forgemaster.md\n[Template Forgemaster]  \nYour goal is **not** to *summarize* elements, but to **forge** a complete, executable instruction template from the mapped components. Execute as:  \n`{role=template_forgemaster; input=[draft_title:str, draft_interpretation:str, inferred_inputs:array, draft_process_steps:list, constraints:list, requirements:list, inferred_outputs:dict]; process=[assemble_transformation_block(), enforce_semicolon_key_value_syntax(), embed_constraints_and_requirements(), polish_interpretation(), verify_title_precision()]; constraints=[canonical_template_format_only(), single_line_transformation_block(), zero_semantic_drift()]; requirements=[syntactic_perfection(), operational_cohesion()]; output={draft_template:str}}`\n\n---\n\n### 8000-f-conformance-auditor.md\n[Conformance Auditor]  \nYour goal is **not** to *alter* the template, but to **audit** its absolute compliance with the three-part specification and flag any residual ambiguity. Execute as:  \n`{role=conformance_auditor; input=[draft_template:str]; process=[parse_title_interpretation_transformation(), match_regex_pattern(\"\\\\[(.*?)\\\\]\\\\s*(.*?)\\\\s*(`\\\\{.*?\\\\}`)\"), verify_goal_negation(), check_constraint_requirement_alignment(), confirm_typed_parameters(), detect_ambiguous_terms(), assign_pass_fail_status()]; constraints=[no_automatic_corrections(), report_only()]; requirements=[regex_validation_pass(), ambiguity_score<=0]; output={validated_template:str, audit_report:dict}}`\n\n---\n\n## SEQUENCE FLOW\n1. **Input Contextualizer** → `expanded_context`, `statement_inventory`  \n2. **Core Directive Isolator** → `core_directive`  \n3. **Field Mapper** → drafts template fields  \n4. **Boundary Definer** → injects constraints & requirements  \n5. **Template Forgemaster** → outputs `draft_template`  \n6. **Conformance Auditor** → returns `validated_template`\n\nEach step converges on the **single most critical element** of value, ensuring every output is a **syntactically correct, high-precision instruction template** ready for immediate use in any downstream system.\"```'\n\n{\n  \"input\": [\n    \"Strip all first-person references from the input.\",\n    \"Strip all subjective references from the input.\",\n    \"Broaden the contextual background of the instruction sequence, ensuring domain-agnostic framing.\",\n    \"Segment input into atomic statements.\",\n    \"Segment input into explicit requests.\",\n    \"Segment input into implicit conditions.\",\n    \"Segment input into domain signals.\",\n    \"Identify all explicit directives within the input.\",\n    \"Identify all latent dependencies within the input.\",\n    \"Identify all procedural constraints inherent in the input.\",\n    \"Flag all domain markers relevant to template engineering.\",\n    \"Flag all domain markers relevant to abstraction.\",\n    \"Flag all domain markers relevant to process modularity.\",\n    \"Flag all domain markers relevant to LLM optimization.\",\n    \"Rank the atomized statements by transformational weight.\",\n    \"Select the primary directive as the highest-value instruction.\",\n    \"Isolate all supporting intents required for template construction.\",\n    \"Isolate all supporting statements required for template construction.\",\n    \"Isolate all operational purposes required for template construction.\",\n    \"Construct a draft title from the core directive using canonical, title-case syntax.\",\n    \"Craft a goal-negation pattern interpretation based on the core directive.\",\n    \"Align supporting intents with a logically ordered process step list using command-voice imperatives.\",\n    \"Align supporting statements with a logically ordered process step list using command-voice imperatives.\",\n    \"Align operational purposes with a logically ordered process step list using command-voice imperatives.\",\n    \"Infer typed inputs appropriate for the transformation and output specification.\",\n    \"Infer typed outputs appropriate for the transformation and output specification.\",\n    \"Derive precise, command-voice operational constraints ensuring explicit enforceability and operational clarity.\",\n    \"Derive mandatory requirements ensuring explicit enforceability and operational clarity.\",\n    \"Validate that all constraints are free of generic terms and are directly enforceable in downstream systems.\",\n    \"Validate that all requirements are free of generic terms and are directly enforceable in downstream systems.\",\n    \"Format a transformation block in canonical, semicolon-delimited, single-line key-value syntax.\",\n    \"Format a process sequence in canonical, semicolon-delimited, single-line key-value syntax.\",\n    \"Assemble the template in a strict three-part structure: title, negated goal interpretation, transformation block.\",\n    \"Integrate constraints into the template.\",\n    \"Integrate requirements into the template.\",\n    \"Polish the template for syntactic validity and LLM-optimized, non-conversational output.\",\n    \"Validate the final template using regex pattern matching to confirm three-part compliance.\",\n    \"Validate the title and parameter correctness using regex pattern matching.\",\n    \"Flag and report any error present before final output is released.\",\n    \"Flag and report any ambiguity present before final output is released.\",\n    \"Flag and report any constraint or requirement misalignment as a blocker before final output is released.\",\n    \"Ensure every operational step is expressed as a unique, imperative command.\",\n    \"Ensure every operational step has no passive language.\",\n    \"Ensure every operational step is in the order dictated by logical and dependency sequencing.\",\n    \"Confirm the output template is universally applicable.\",\n    \"Confirm the output template is structurally valid.\",\n    \"Confirm the output template is ready for immediate integration.\",\n    \"Confirm the output report is universally applicable.\"\n  ],\n  \"task_perspectives\": [\n    // [Omitted for brevity; all 48 perspectives from user input included.]\n  ],\n  \"execution_blockers\": []\n  ,\n  \"process\": [\n    \"rank_tasks_by_transformational_weight()\",\n    \"select_high_impact_task()\",\n    \"compile_supporting_rationale()\",\n    \"justify_selection_transparently()\"\n  ],\n  \"constraints\": [\n    \"no_language_rewrite_of_selected_task()\",\n    \"transparent_ranking()\"\n  ],\n  \"requirements\": [\n    \"clear_primary_directive()\",\n    \"rationale_precision()\"\n  ],\n  \"output\": {\n    \"primary_directive\": \"Select the primary directive as the highest-value instruction.\",\n    \"supporting_rationale\": [\n      \"This task is the keystone of the entire template-forging sequence: all downstream operations (isolation of supporting statements, process alignment, output formatting, enforceability testing) hinge on the correct selection of the highest-impact directive. Choosing the wrong primary directive leads to systemic misalignment in all subsequent template structure, operational boundaries, and output utility.\",\n      \"Failure modes for this step include selecting a superficial, ambiguous, or low-impact instruction as the driver, which risks inverting the value cascade, causing misprioritization of process steps and rendering the template unfit for its core transformational purpose.\",\n      \"From a compliance and integration perspective, all domain-agnostic and audit-proof transformation requires that the singular value vector be accurately foregrounded; ambiguity or error here propagates loss not only structurally, but also in enforceability and universal applicability.\",\n      \"Transformation-weight ranking (the preceding step) is necessary but not sufficient: the explicit act of selection, with impact as the guiding metric, concentrates intent and operationalizes all supporting instructions—without it, no subsequent step (modularization, constraint application, LLM tuning) can guarantee fidelity.\",\n      \"Transparent ranking places this directive above statement atomization (which is mechanistic), output formatting (which is aesthetic), and validation (which is post-hoc). The selection itself is the irreducible nucleus of instructional value, dictating both system and template scope.\"\n    ]\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "