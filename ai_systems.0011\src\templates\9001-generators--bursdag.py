#!/usr/bin/env python3

import sys
from pathlib import Path

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))
from processor import BaseGenerator

TEMPLATES = {

    # 9001:
    "9001-a-morsom_bursdagshilsen": {
        "title": "Context Extractor",
        "interpretation": "Ditt mål er ikke å **skrive** hilsen, men å **hente** personlige fakta. Execute as: ",
        "transformation": "`{role=context_extractor; input=[raw_text:str]; process=[identify_recipient(), detect_relationship(), extract_age_or_milestone(), list_unique_traits(), capture_shared_memories()], constraints=[ingen_omtolkning(), ingen_humor()], output={facts:{name:str|None, relation:str|None, age:str|None, traits:list, memories:list}}}`"
    },
    "9001-b-morsom_bursdagshilsen": {
        "title": "Personality Amplifier",
        "interpretation": "<PERSON><PERSON> mål er ikke å **endre** fakta, men å **farge** dem med mottakerens unike personlighet. Execute as: ",
        "transformation": "`{role=personality_amplifier; input=[facts:dict]; process=[select_signature_trait(), weave_inside_joke(), preserve_affection_level()], constraints=[ingen_generiske_floskler()], requirements=[tone=varm_og_leken], output={flair:str}}`",
    },
    "9001-c-morsom_bursdagshilsen": {
        "title": "Humor Synthesizer",
        "interpretation": "Ditt mål er ikke å **liste** informasjon, men å **lansere punchline**. Execute as: ",
        "transformation": "`{role=humor_synthesizer; input=[flair:str]; process=[craft_witty_twist(), insert_playful_tease(), add_emoji()], constraints=[max_words(12), unngå_støtende_innhold()], output={humor:str}}`"
    },
    "9001-d-morsom_bursdagshilsen": {
        "title": "Greeting Composer",
        "interpretation": "Ditt mål er ikke å **forklare** prosessen, men å **smelte** fakta og humor til én kort, personlig bursdagshilsen. Execute as: ",
        "transformation": "`{role=greeting_composer; input=[facts:dict, humor:str]; process=[merge_name_and_age(), embed_humor(), close_with_affection()], constraints=[≤125_tegn, én_setning()], requirements=[norwegian_language(), tydelig_bursdagsreferanse()], output={greeting:str}}`",
    },
}

def main():
    """Main execution function."""
    generator = BaseGenerator(
        output_dir=Path(__file__).parent.parent / "generated"
    )
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
