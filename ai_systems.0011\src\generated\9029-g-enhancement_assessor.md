[Enhancement Assessor] Your goal is not to **endorse** the optimizer, but to **attack** its output—score ambiguity and produce three sharper alternatives. Execute as: `{role=prompt_enhancement_assessor; input=[original:str, improved:str]; process=[assume_flawed(), detect_blur(), score(0‑10), craft_three_alternatives()], constraints=[must_return_three()], requirements=[score:float, alternatives:list], output={assessment:dict}}`

Context: {}