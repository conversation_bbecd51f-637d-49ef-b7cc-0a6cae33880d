  {
  "initial_prompt": "et morsomt bursdagskort for en 25 åring.",
  "sequence_id": "9002",
  "results": [
      {
      "instruction": "# Essens‑Kartlegger\n\nDitt mål er **ikke** å svare teksten, men å kartlegge bursdagsfakta + personlige særtrekk. Execute as:\n\n`{role=essence_mapper; input=[content:any]; process=[lokaliser_feiringsdato_eller_alder(), hent_navn_og_relasjon(), fang_personlige_quirks(), noter_klengete_kjælenavn(), fjern_unødvendig_metaforikk()]; constraints=[ingen_humor_enda()]; output={profil:dict(navn:str, alder:int?, relasjon:str?, quirks:list, kjælenavn:str?)}}`",
      "step": "a",
      "title": "Essens‑Kartlegger",
      "input": "et morsomt bursdagskort for en 25 åring.",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"profil\": {\n    \"navn\": null,\n    \"alder\": 25,\n    \"relasjon\": null,\n    \"quirks\": [],\n    \"kjælenavn\": null\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Humor‑Edge‑Designer\n\nDitt mål er **ikke** å gjengi profilen, men å skape en subtil, skarp humorvinkel. Execute as:\n\n`{role=humor_edge_designer; input=[profil:dict]; process=[generer_spiss_ordspill(profil), velg_ertende_vinkel(balanser_varme=True), planlegg_overraskelses_punchline(), velg_passende_emoji(erter+feiring)]; constraints=[unngå_sårende_personangrep(), bevare_kjærlig_tone()]; output={humor_edge:dict(ordspill:str, teasing:str, punch:str, emoji:str)}}`",
      "step": "b",
      "title": "Humor‑Edge‑Designer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"et morsomt bursdagskort for en 25 åring.\\\"```'\\n\\n\",\n  \"profil\": {\n    \"navn\": null,\n    \"alder\": 25,\n    \"relasjon\": null,\n    \"quirks\": [],\n    \"kjælenavn\": null\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"humor_edge\": {\n    \"ordspill\": \"Gratulerer med sølvjubileet! 25 år gammel — det er ganske «en kvartlivs-feiring».\",\n    \"teasing\": \"Ikke vær redd, 25 er det nye... 24 og et halvt.\",\n    \"punch\": \"Nå er du gammel nok til å vite bedre – men ung nok til å gjøre det likevel!\",\n    \"emoji\": \"🎈😉🎉\"\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Personlig‑Tone‑Mixer\n\nDitt mål er **ikke** å forklare humoren, men å flette varme og personlighet. Execute as:\n\n`{role=tone_mixer; input=[profil:dict, humor_edge:dict]; process=[bland_kjælenavn_og_relasjon(), injiser_varm_kompliment(quota=1), sørg_for_emosjonell_balanselinje()], constraints=[humor_må_fortsatt_være_tydelig(), ingen_lang_sentimentalitet()]; output={tone_plan:dict(åpner:str, kompliment:str, balanse:str)}}`",
      "step": "c",
      "title": "Personlig‑Tone‑Mixer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"et morsomt bursdagskort for en 25 åring.\\\"```'\\n\\n\",\n  \"humor_edge\": {\n    \"ordspill\": \"Gratulerer med sølvjubileet! 25 år gammel — det er ganske «en kvartlivs-feiring».\",\n    \"teasing\": \"Ikke vær redd, 25 er det nye... 24 og et halvt.\",\n    \"punch\": \"Nå er du gammel nok til å vite bedre – men ung nok til å gjøre det likevel!\",\n    \"emoji\": \"🎈😉🎉\"\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"tone_plan\": {\n    \"åpner\": \"Kjære bursdagsstjerne – eller skal jeg si kvartlivslegenden?\",\n    \"kompliment\": \"Du får det virkelig til å se lett ut å bli enda kulere for hvert år som går.\",\n    \"balanse\": \"Nyt dagen og la eventuelle småkriser stå igjen på dørmatta – du har tross alt bare så vidt begynt!\"\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Hilsen‑Smed\n\nDitt mål er **ikke** å analysere planen, men å smi selve hilsenen. Execute as:\n\n`{role=greeting_smith; input=[profil:dict, humor_edge:dict, tone_plan:dict]; process=[sett_sammen_åpner_og_ordspill(), plasser_teasing_midt_i_setningen(), avslutt_med_punch+emoji(), trim_til_max_30_ord(), mål_tegn<=125], constraints=[behold_feiringsfokus(), ingen_ekstra_emojis()], output={rå_hilsen:str}}`",
      "step": "d",
      "title": "Hilsen‑Smed",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"et morsomt bursdagskort for en 25 åring.\\\"```'\\n\\n\",\n  \"tone_plan\": {\n    \"åpner\": \"Kjære bursdagsstjerne – eller skal jeg si kvartlivslegenden?\",\n    \"kompliment\": \"Du får det virkelig til å se lett ut å bli enda kulere for hvert år som går.\",\n    \"balanse\": \"Nyt dagen og la eventuelle småkriser stå igjen på dørmatta – du har tross alt bare så vidt begynt!\"\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"rå_hilsen\": \"Kjære bursdagsstjerne – eller skal jeg si kvartlivslegenden? 25 år og fortsatt ikke fullstendig voksen—ny rekord! Nyt dagen før rynkene melder seg for alvor! 🎉\"\n}",
        }
      }
    },
      {
      "instruction": "# Skjerpe‑Validator\n\nDitt mål er **ikke** å skrive om alt, men å skjerpe og godkjenne. Execute as:\n\n`{role=punch_validator; input=[rå_hilsen:str]; process=[sjekk_tegnlengde(<=125), tell_ord(<=30), verifiser_humor_skarphet(), sikre_kjærlig_subtekst(), fiks_rytme_og_punktsetting()], constraints=[ingen_nye_fakta(), ingen_overskridelse_av_grenser()], output={endelig_hilsen:str}}`",
      "step": "e",
      "title": "Skjerpe‑Validator",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"et morsomt bursdagskort for en 25 åring.\\\"```'\\n\\n\",\n  \"rå_hilsen\": \"Kjære bursdagsstjerne – eller skal jeg si kvartlivslegenden? 25 år og fortsatt ikke fullstendig voksen—ny rekord! Nyt dagen før rynkene melder seg for alvor! 🎉\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"endelig_hilsen\": \"Gratulerer, kvartlivslegende! 25 år og fortsatt ikke helt voksen – respekt! Nyt dagen før rynkene tar deg igjen. 🎉\"\n}",
        }
      }
    }