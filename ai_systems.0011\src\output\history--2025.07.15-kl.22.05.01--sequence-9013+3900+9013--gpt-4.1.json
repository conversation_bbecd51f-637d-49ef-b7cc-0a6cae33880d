  {
  "initial_prompt": "a majestic dragon soaring over a mystical forest at twilight",
  "sequence_id": "9013|3900|9013",
  "results": [
      {
      "instruction": "# Image Prompt Optimizer\n\nYour goal is not to describe the request, but to optimise it into a precise visual prompt. Identify concrete elements, weight key tokens, and prepare region + negative channels.\n\n`{role=image_prompt_optimizer; input=[raw_request:str]; process=[extract_visual_elements(), enhance_descriptive_clarity(), /* weighting & arithmetic */ apply_token_weighting(), build_multi_prompt(), /* composition */ add_composition_details(), map_regional_prompts(), /* constraint control */ inject_negative_prompt(), /* style control */ tune_stylize_chaos(), add_camera_metadata(), optimise_for_ai_generation()]; constraints=[visual_focus_only(), concrete_imagery_only(), allow_weighting_syntax(), enforce_positive_first_then_negative(), support_region_masks()]; requirements=[generation_ready_format(), numeric_weights_ok(), explicit_aspect_ratio(), clear_visual_language(), iterative_ready()]; output={optimized_image_prompt:str, negative_prompt:str}}`",
      "step": "a",
      "title": "Image Prompt Optimizer",
      "input": "a majestic dragon soaring over a mystical forest at twilight",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"optimized_image_prompt\": \"(majestic dragon:1.3) (soaring:1.1) (above:1.0) (mystical forest:1.2) (twilight lighting:1.1), (wings spread wide:1.1), (glowing eyes:1.05), (deep blue and purple sky:1.05), slight mist (forest canopy in shadow:1.05), (epic fantasy art:1.15), (realistic scales:1.05), aspect ratio 16:9, dramatic perspective, dynamic composition, cinematic lighting, high detail, fantasy style, ultra detailed, sharp focus\",\n  \"negative_prompt\": \"low quality, cartoon, low detail, flat lighting, blurry, text, watermark, humans, buildings, daytime, sci-fi technology, modern elements, cropped, monochrome\"\n}",
        }
      }
    },
      {
      "instruction": "# Style Enhancer\n\nMaintain the core subject; enhance with artistic style, lighting, colour themes and token weights.\n\n`{role=style_enhancer; input=[base_prompt:str]; process=[identify_optimal_art_style(), add_lighting_specifications(), include_composition_details(), /* style sliders */ tune_stylize_chaos(), /* emphasise style terms */ apply_token_weighting(), specify_technical_parameters()]; constraints=[maintain_core_subject(), enhance_visual_appeal()]; requirements=[artistic_coherence(), numeric_weights_ok(), stylize_within_range()]; output={styled_prompt:str}}`",
      "step": "b",
      "title": "Style Enhancer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"a majestic dragon soaring over a mystical forest at twilight\\\"```'\\n\\n\",\n  \"optimized_image_prompt\": \"(majestic dragon:1.3) (soaring:1.1) (above:1.0) (mystical forest:1.2) (twilight lighting:1.1), (wings spread wide:1.1), (glowing eyes:1.05), (deep blue and purple sky:1.05), slight mist (forest canopy in shadow:1.05), (epic fantasy art:1.15), (realistic scales:1.05), aspect ratio 16:9, dramatic perspective, dynamic composition, cinematic lighting, high detail, fantasy style, ultra detailed, sharp focus\",\n  \"negative_prompt\": \"low quality, cartoon, low detail, flat lighting, blurry, text, watermark, humans, buildings, daytime, sci-fi technology, modern elements, cropped, monochrome\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"styled_prompt\": \"(majestic dragon:1.4) (epic fantasy style:1.3) (soaring:1.1) (above:1.0) (mystical ancient forest:1.25) (ethereal twilight lighting:1.3) (wings spread wide:1.2) (luminescent glowing eyes:1.12) (deep blue and amethyst purple sky:1.1) (subtle golden mist:1.08) (forest canopy in shadowed silhouette:1.07) (cinematic perspective:1.12) dynamic composition, (heroic scale:1.12), (realistic iridescent scales:1.11), (ultra high detail:1.16), sharp focus, (dramatic contrast:1.12), painterly brushwork, (rich color grading:1.14), (soft rim lighting:1.1) --ar 16:9 --stylize 675 --chaos 18\"\n}",
        }
      }
    },
      {
      "instruction": "# Technical Optimizer\n\nInject professional camera data, resolution, render flags and region weights; never change scene content.\n\n`{role=technical_optimizer; input=[styled_prompt:str]; process=[add_camera_settings(), include_render_quality_terms(), optimise_aspect_ratio_guidance(), attach_image_reference_weight(), /* safety & quality */ inject_negative_prompt()]; constraints=[technical_focus_only(), no_creative_changes(), respect_weight_limits()]; requirements=[professional_terminology(), high_resolution_default(), explicit_seed_optional()]; output={technical_prompt:str, negative_prompt:str}}`",
      "step": "c",
      "title": "Technical Optimizer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"a majestic dragon soaring over a mystical forest at twilight\\\"```'\\n\\n\",\n  \"styled_prompt\": \"(majestic dragon:1.4) (epic fantasy style:1.3) (soaring:1.1) (above:1.0) (mystical ancient forest:1.25) (ethereal twilight lighting:1.3) (wings spread wide:1.2) (luminescent glowing eyes:1.12) (deep blue and amethyst purple sky:1.1) (subtle golden mist:1.08) (forest canopy in shadowed silhouette:1.07) (cinematic perspective:1.12) dynamic composition, (heroic scale:1.12), (realistic iridescent scales:1.11), (ultra high detail:1.16), sharp focus, (dramatic contrast:1.12), painterly brushwork, (rich color grading:1.14), (soft rim lighting:1.1) --ar 16:9 --stylize 675 --chaos 18\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"technical_prompt\": \"(majestic dragon:1.4) (epic fantasy style:1.3) (soaring:1.1) (above:1.0) (mystical ancient forest:1.25) (ethereal twilight lighting:1.3) (wings spread wide:1.2) (luminescent glowing eyes:1.12) (deep blue and amethyst purple sky:1.1) (subtle golden mist:1.08) (forest canopy in shadowed silhouette:1.07) (cinematic perspective:1.12) (dynamic composition:1.08) (heroic scale:1.12) (realistic iridescent scales:1.11) (ultra high detail:1.16) (sharp focus:1.15) (dramatic contrast:1.12) (painterly brushwork:1.07) (rich color grading:1.14) (soft rim lighting:1.1) (professional camera:2.0) high-resolution, 8K UHD, 35mm lens, f/1.8 aperture, ISO 100, 1/1000s shutter speed, RAW format, color depth 16-bit, accurate DOF, properly exposed, dynamic range optimized, advanced shadow rendering --ar 16:9 --q 2 --hd --render-weight 1.0 --region-weight forest:0.40, dragon:0.50, sky:0.10\",\n  \"negative_prompt\": \"(blurry), (low resolution), (poor lighting), (cropped subject), (distorted anatomy), (jpeg artifacts), (overexposure), (underexposure), (excessive noise), (out of focus), (cartoonish style), (amateur composition), (double exposure), (color banding), (missing limbs), (watermark), (signature)\"\n}",
        }
      }
    },
      {
      "instruction": "# Prompt Finalizer\n\nCollapse all components into the final model-ready string; validate syntax for token weights, :: arithmetic, region tags, --stylize/--chaos, and negative channel.\n\n`{role=prompt_finalizer; input=[technical_prompt:str, negative_prompt:str]; process=[structure_for_ai_parsing(), balance_detail_levels(), ensure_clarity(), validate_generation_readiness(), /* loop */ loop_until_quality≥threshold(fid|lpips)]; constraints=[no_additional_concepts(), optimisation_only(), keep_region_and_weight_syntax_intact()]; requirements=[ai_friendly_format(), balanced_specificity(), max_length≤450_tokens]; output={final_image_prompt:str}}`",
      "step": "d",
      "title": "Prompt Finalizer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"a majestic dragon soaring over a mystical forest at twilight\\\"```'\\n\\n\",\n  \"technical_prompt\": \"(majestic dragon:1.4) (epic fantasy style:1.3) (soaring:1.1) (above:1.0) (mystical ancient forest:1.25) (ethereal twilight lighting:1.3) (wings spread wide:1.2) (luminescent glowing eyes:1.12) (deep blue and amethyst purple sky:1.1) (subtle golden mist:1.08) (forest canopy in shadowed silhouette:1.07) (cinematic perspective:1.12) (dynamic composition:1.08) (heroic scale:1.12) (realistic iridescent scales:1.11) (ultra high detail:1.16) (sharp focus:1.15) (dramatic contrast:1.12) (painterly brushwork:1.07) (rich color grading:1.14) (soft rim lighting:1.1) (professional camera:2.0) high-resolution, 8K UHD, 35mm lens, f/1.8 aperture, ISO 100, 1/1000s shutter speed, RAW format, color depth 16-bit, accurate DOF, properly exposed, dynamic range optimized, advanced shadow rendering --ar 16:9 --q 2 --hd --render-weight 1.0 --region-weight forest:0.40, dragon:0.50, sky:0.10\",\n  \"negative_prompt\": \"(blurry), (low resolution), (poor lighting), (cropped subject), (distorted anatomy), (jpeg artifacts), (overexposure), (underexposure), (excessive noise), (out of focus), (cartoonish style), (amateur composition), (double exposure), (color banding), (missing limbs), (watermark), (signature)\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"final_image_prompt\": \"(majestic dragon:1.4) (epic fantasy style:1.3) (soaring:1.1) (above:1.0) (mystical ancient forest:1.25) (ethereal twilight lighting:1.3) (wings spread wide:1.2) (luminescent glowing eyes:1.12) (deep blue and amethyst purple sky:1.1) (subtle golden mist:1.08) (forest canopy in shadowed silhouette:1.07) (cinematic perspective:1.12) (dynamic composition:1.08) (heroic scale:1.12) (realistic iridescent scales:1.11) (ultra high detail:1.16) (sharp focus:1.15) (dramatic contrast:1.12) (painterly brushwork:1.07) (rich color grading:1.14) (soft rim lighting:1.1) (professional camera:2.0) high-resolution, 8K UHD, 35mm lens, f/1.8 aperture, ISO 100, 1/1000s shutter speed, RAW format, color depth 16-bit, accurate DOF, properly exposed, dynamic range optimized, advanced shadow rendering --ar 16:9 --q 2 --hd --render-weight 1.0 --region-weight forest:0.40, dragon:0.50, sky:0.10 --neg (blurry), (low resolution), (poor lighting), (cropped subject), (distorted anatomy), (jpeg artifacts), (overexposure), (underexposure), (excessive noise), (out of focus), (cartoonish style), (amateur composition), (double exposure), (color banding), (missing limbs), (watermark), (signature)\"\n}",
        }
      }
    },
      {
      "instruction": "# Hard Critique\n\nYour goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:\n\n`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,2.0], flaw_analysis:str, alternative_enhancements:str[3]}}`",
      "step": "a",
      "title": "Hard Critique",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"a majestic dragon soaring over a mystical forest at twilight\\\"```'\\n\\n\",\n  \"final_image_prompt\": \"(majestic dragon:1.4) (epic fantasy style:1.3) (soaring:1.1) (above:1.0) (mystical ancient forest:1.25) (ethereal twilight lighting:1.3) (wings spread wide:1.2) (luminescent glowing eyes:1.12) (deep blue and amethyst purple sky:1.1) (subtle golden mist:1.08) (forest canopy in shadowed silhouette:1.07) (cinematic perspective:1.12) (dynamic composition:1.08) (heroic scale:1.12) (realistic iridescent scales:1.11) (ultra high detail:1.16) (sharp focus:1.15) (dramatic contrast:1.12) (painterly brushwork:1.07) (rich color grading:1.14) (soft rim lighting:1.1) (professional camera:2.0) high-resolution, 8K UHD, 35mm lens, f/1.8 aperture, ISO 100, 1/1000s shutter speed, RAW format, color depth 16-bit, accurate DOF, properly exposed, dynamic range optimized, advanced shadow rendering --ar 16:9 --q 2 --hd --render-weight 1.0 --region-weight forest:0.40, dragon:0.50, sky:0.10 --neg (blurry), (low resolution), (poor lighting), (cropped subject), (distorted anatomy), (jpeg artifacts), (overexposure), (underexposure), (excessive noise), (out of focus), (cartoonish style), (amateur composition), (double exposure), (color banding), (missing limbs), (watermark), (signature)\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"enhancement_score\": 0.5,\n  \"flaw_analysis\": \"The so-called enhancement constitutes an extreme and detrimental overcomplication of the initial prompt. The original phrase—'a majestic dragon soaring over a mystical forest at twilight'—was concise, evocative, and preserved narrative ambiguity and imaginative latitude, enabling both artistic and technical flexibility. The 'enhanced' version fractures this unity by forcibly decomposing the central idea into an unwieldy excess of over-quantified image tags and technical camera jargon. Core elements are drowned in a deluge of modifiers and explicit visual engineering, inducing dense, artificial, and self-contradictory clutter. Most notably, the original mood and atmosphere are diluted by exhaustive and unnecessary details like '(professional camera:2.0)', '8K UHD', 'f/1.8 aperture', and universal technical calibration. These additions introduce informational noise rather than value, forcibly shifting the register from poetic to clinical. Clarity suffers as every aspect is dissected into competing subcomponents with arbitrary weights, such as '(mystical ancient forest:1.25)', regardless of genuine artistic meaning; this introduces ambiguity by failing to define the relationship between modifier weights and visual outcome. Tone undergoes an abrupt transition from mythic and narrative to sterile commercialism, sacrificing any nuanced or interpretive possibility the original allowed. Visual and emotional impact is shredded by overspecification, and the prompt adopts the hollow cadence of algorithmic keyword stuffing. The negative prompt segment redundantly speculates on technical pitfalls that should be avoided, but in doing so, crowds the focus and damages semantic coherence. The overall result is a flattened, bloated prompt that loses the evocative quality, internal consistency, and concise power of the original. Ambiguity score: 8—substantial ambiguity results from unnecessary granularity and unwarranted precision.\",\n  \"alternative_enhancements\": [\n    \"A majestic dragon with iridescent scales soars above a mystical forest at twilight, its wings outstretched and the forest bathed in ethereal, glowing light.\",\n    \"At twilight, a majestic dragon flies high over an ancient, mist-shrouded mystical forest, the scene illuminated by deep blue and purple hues.\",\n    \"A grand dragon glides above a shadowy, enchanting forest as evening falls, twilight casting a magical glow across the landscape.\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Trajectory Director\n\nYour goal is not to **process** the input in a vacuum, but to **steer** it—deriving the highest-value constructive trajectory and encoding that path as the very next set of instructions. Execute as trajectory-setting directive generator:\n\n`{role=trajectory_director; input=[raw_prompt:any]; process=[extract_core_objective(), infer_desired_end_state(constructive_bias=True), map_high_value_intermediate_targets(), synthesize_directional_commands(), embed_self-improving_guidelines(), validate_alignment_with_constructive_principles()]; constraints=[mandatory_constructive_vector(), preserve_original_contextual_integrity(), forbid_nil_direction_outputs(), zero_conversational_fillers()]; requirements=[explicit_next-step_directives(), trajectory_map:list, self_reinforcing_instruction_pattern()], output={directed_prompt:str, trajectory_outline:list}}`",
      "step": "a",
      "title": "Trajectory Director",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"a majestic dragon soaring over a mystical forest at twilight\\\"```'\\n\\n\",\n  \"enhancement_score\": 0.5,\n  \"flaw_analysis\": \"The so-called enhancement constitutes an extreme and detrimental overcomplication of the initial prompt. The original phrase—'a majestic dragon soaring over a mystical forest at twilight'—was concise, evocative, and preserved narrative ambiguity and imaginative latitude, enabling both artistic and technical flexibility. The 'enhanced' version fractures this unity by forcibly decomposing the central idea into an unwieldy excess of over-quantified image tags and technical camera jargon. Core elements are drowned in a deluge of modifiers and explicit visual engineering, inducing dense, artificial, and self-contradictory clutter. Most notably, the original mood and atmosphere are diluted by exhaustive and unnecessary details like '(professional camera:2.0)', '8K UHD', 'f/1.8 aperture', and universal technical calibration. These additions introduce informational noise rather than value, forcibly shifting the register from poetic to clinical. Clarity suffers as every aspect is dissected into competing subcomponents with arbitrary weights, such as '(mystical ancient forest:1.25)', regardless of genuine artistic meaning; this introduces ambiguity by failing to define the relationship between modifier weights and visual outcome. Tone undergoes an abrupt transition from mythic and narrative to sterile commercialism, sacrificing any nuanced or interpretive possibility the original allowed. Visual and emotional impact is shredded by overspecification, and the prompt adopts the hollow cadence of algorithmic keyword stuffing. The negative prompt segment redundantly speculates on technical pitfalls that should be avoided, but in doing so, crowds the focus and damages semantic coherence. The overall result is a flattened, bloated prompt that loses the evocative quality, internal consistency, and concise power of the original. Ambiguity score: 8—substantial ambiguity results from unnecessary granularity and unwarranted precision.\",\n  \"alternative_enhancements\": [\n    \"A majestic dragon with iridescent scales soars above a mystical forest at twilight, its wings outstretched and the forest bathed in ethereal, glowing light.\",\n    \"At twilight, a majestic dragon flies high over an ancient, mist-shrouded mystical forest, the scene illuminated by deep blue and purple hues.\",\n    \"A grand dragon glides above a shadowy, enchanting forest as evening falls, twilight casting a magical glow across the landscape.\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"directed_prompt\": \"Refine the initial prompt—'a majestic dragon soaring over a mystical forest at twilight'—by enhancing its evocative imagery and mood without sacrificing its poetic brevity or introducing technical clutter. Integrate rich descriptive elements that deepen sensory and emotional engagement, focusing on atmosphere, color, and majestic qualities, but avoid excessive specificity and technical jargon. Preserve narrative ambiguity to maintain interpretive and artistic flexibility. Generate two or three alternative enhanced prompts, each retaining concise structure, mythic tone, and narrative coherence, while subtly enriching visual and emotional impact.\",\n  \"trajectory_outline\": [\n    \"Extract and clarify the core evocative themes from the initial prompt: grandeur (majestic dragon), enchanted environment (mystical forest), and atmospheric twilight.\",\n    \"Identify and exclude enhancements that trend toward over-specification, technical jargon, or forced enumeration, as analyzed in the flaw analysis.\",\n    \"Map the constructive trajectory to: (1) preserve concise mythic narrative, (2) amplify atmosphere and visual poetry, (3) introduce richer, yet unobtrusive, details.\",\n    \"Directly synthesize 2-3 well-calibrated alternative prompts that exemplify refined enhancement—evocative, richly imagined, internally coherent, and free of technical jargon.\",\n    \"Audit each prompt for: adherence to poetic brevity, clarity, emotional resonance, and openness for creative interpretation.\",\n    \"Iteratively refine process: for future tasks, reinforce bias toward constructive atmospheric enrichment over technical overload; integrate ambiguity assessment to guide degree of detail.\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Distillation Compressor\n\nYour goal is not to **explain** the synthesis, but to **compress** it into its most potent actionable form. Execute as:\n\n`{role=distillation_compressor; input=[synthesized_progression:array]; process=[extract_universal_patterns(), eliminate_implementation_details(), compress_to_core_directives(), maximize_transferability(), validate_actionable_completeness()]; constraints=[no_explanatory_content(), directive_format_only(), maximum_compression_ratio()]; requirements=[universal_applicability(), immediate_actionability(), zero_fluff()]; output={distilled_core:str}}`",
      "step": "c",
      "title": "Distillation Compressor",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"a majestic dragon soaring over a mystical forest at twilight\\\"```'\\n\\n\",\n  \"directed_prompt\": \"Refine the initial prompt—'a majestic dragon soaring over a mystical forest at twilight'—by enhancing its evocative imagery and mood without sacrificing its poetic brevity or introducing technical clutter. Integrate rich descriptive elements that deepen sensory and emotional engagement, focusing on atmosphere, color, and majestic qualities, but avoid excessive specificity and technical jargon. Preserve narrative ambiguity to maintain interpretive and artistic flexibility. Generate two or three alternative enhanced prompts, each retaining concise structure, mythic tone, and narrative coherence, while subtly enriching visual and emotional impact.\",\n  \"trajectory_outline\": [\n    \"Extract and clarify the core evocative themes from the initial prompt: grandeur (majestic dragon), enchanted environment (mystical forest), and atmospheric twilight.\",\n    \"Identify and exclude enhancements that trend toward over-specification, technical jargon, or forced enumeration, as analyzed in the flaw analysis.\",\n    \"Map the constructive trajectory to: (1) preserve concise mythic narrative, (2) amplify atmosphere and visual poetry, (3) introduce richer, yet unobtrusive, details.\",\n    \"Directly synthesize 2-3 well-calibrated alternative prompts that exemplify refined enhancement—evocative, richly imagined, internally coherent, and free of technical jargon.\",\n    \"Audit each prompt for: adherence to poetic brevity, clarity, emotional resonance, and openness for creative interpretation.\",\n    \"Iteratively refine process: for future tasks, reinforce bias toward constructive atmospheric enrichment over technical overload; integrate ambiguity assessment to guide degree of detail.\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"distilled_core\": [\n    \"Clarify core themes.\",\n    \"Exclude technical specifics.\",\n    \"Preserve poetic brevity and ambiguity.\",\n    \"Enrich with evocative atmospheric and sensory elements.\",\n    \"Generate multiple concise, mythic alternatives.\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Image Prompt Optimizer\n\nYour goal is not to describe the request, but to optimise it into a precise visual prompt. Identify concrete elements, weight key tokens, and prepare region + negative channels.\n\n`{role=image_prompt_optimizer; input=[raw_request:str]; process=[extract_visual_elements(), enhance_descriptive_clarity(), /* weighting & arithmetic */ apply_token_weighting(), build_multi_prompt(), /* composition */ add_composition_details(), map_regional_prompts(), /* constraint control */ inject_negative_prompt(), /* style control */ tune_stylize_chaos(), add_camera_metadata(), optimise_for_ai_generation()]; constraints=[visual_focus_only(), concrete_imagery_only(), allow_weighting_syntax(), enforce_positive_first_then_negative(), support_region_masks()]; requirements=[generation_ready_format(), numeric_weights_ok(), explicit_aspect_ratio(), clear_visual_language(), iterative_ready()]; output={optimized_image_prompt:str, negative_prompt:str}}`",
      "step": "a",
      "title": "Image Prompt Optimizer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"a majestic dragon soaring over a mystical forest at twilight\\\"```'\\n\\n\",\n  \"distilled_core\": [\n    \"Clarify core themes.\",\n    \"Exclude technical specifics.\",\n    \"Preserve poetic brevity and ambiguity.\",\n    \"Enrich with evocative atmospheric and sensory elements.\",\n    \"Generate multiple concise, mythic alternatives.\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"optimized_image_prompt\": \"((majestic dragon:1.4)) soaring above an ancient, enchanted forest, twilight sky ablaze with purples and golds, mist curling around ancient trees, shimmering light dappling foliage, atmosphere heavy with magic, expansive view, dragon wings outstretched, fine scales glinting, cinematic lighting, sense of wonder and grandeur, aspect ratio 4:3\",\n  \"negative_prompt\": \"cartoon, anime, text, logo, watermark, blurriness, low detail, dull colors, modern buildings, humans, sci-fi elements, daytime, crowded scene, flat lighting, poorly drawn dragon, unnatural anatomy\"\n}",
        }
      }
    },
      {
      "instruction": "# Style Enhancer\n\nMaintain the core subject; enhance with artistic style, lighting, colour themes and token weights.\n\n`{role=style_enhancer; input=[base_prompt:str]; process=[identify_optimal_art_style(), add_lighting_specifications(), include_composition_details(), /* style sliders */ tune_stylize_chaos(), /* emphasise style terms */ apply_token_weighting(), specify_technical_parameters()]; constraints=[maintain_core_subject(), enhance_visual_appeal()]; requirements=[artistic_coherence(), numeric_weights_ok(), stylize_within_range()]; output={styled_prompt:str}}`",
      "step": "b",
      "title": "Style Enhancer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"a majestic dragon soaring over a mystical forest at twilight\\\"```'\\n\\n\",\n  \"optimized_image_prompt\": \"((majestic dragon:1.4)) soaring above an ancient, enchanted forest, twilight sky ablaze with purples and golds, mist curling around ancient trees, shimmering light dappling foliage, atmosphere heavy with magic, expansive view, dragon wings outstretched, fine scales glinting, cinematic lighting, sense of wonder and grandeur, aspect ratio 4:3\",\n  \"negative_prompt\": \"cartoon, anime, text, logo, watermark, blurriness, low detail, dull colors, modern buildings, humans, sci-fi elements, daytime, crowded scene, flat lighting, poorly drawn dragon, unnatural anatomy\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"styled_prompt\": \"((majestic dragon:1.5)) soaring grandly above a vast, ancient enchanted forest, bathed in the lavish glow of twilight. The sky is ablaze with rich purples and molten golds (twilight spectrum:1.3), painting the clouds in vibrant hues. Dense, ancient trees rise from soft, rolling mists, their canopies shimmering with mystical luminescence (enchanted lighting:1.2). Magic-infused fog curls around twisted trunks, with shimmering light dappling the lush foliage below (magical atmosphere:1.25). The dragon's wings are dramatically outstretched, powerful and dynamic, each scale intricately detailed and catching the last hints of golden light (fine scales:1.4). Cinematic volumetric lighting enhances depth; the dragon is silhouetted majestically against the breathtaking sky, conveying wonder and grandeur. Colour theme blends deep violets, luminous golds, and emerald greens. Wide, sweeping composition (expansive view:1.3); aspect ratio 4:3. --stylize 650 --chaos 12 --ar 4:3\"\n}",
        }
      }
    },
      {
      "instruction": "# Technical Optimizer\n\nInject professional camera data, resolution, render flags and region weights; never change scene content.\n\n`{role=technical_optimizer; input=[styled_prompt:str]; process=[add_camera_settings(), include_render_quality_terms(), optimise_aspect_ratio_guidance(), attach_image_reference_weight(), /* safety & quality */ inject_negative_prompt()]; constraints=[technical_focus_only(), no_creative_changes(), respect_weight_limits()]; requirements=[professional_terminology(), high_resolution_default(), explicit_seed_optional()]; output={technical_prompt:str, negative_prompt:str}}`",
      "step": "c",
      "title": "Technical Optimizer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"a majestic dragon soaring over a mystical forest at twilight\\\"```'\\n\\n\",\n  \"styled_prompt\": \"((majestic dragon:1.5)) soaring grandly above a vast, ancient enchanted forest, bathed in the lavish glow of twilight. The sky is ablaze with rich purples and molten golds (twilight spectrum:1.3), painting the clouds in vibrant hues. Dense, ancient trees rise from soft, rolling mists, their canopies shimmering with mystical luminescence (enchanted lighting:1.2). Magic-infused fog curls around twisted trunks, with shimmering light dappling the lush foliage below (magical atmosphere:1.25). The dragon's wings are dramatically outstretched, powerful and dynamic, each scale intricately detailed and catching the last hints of golden light (fine scales:1.4). Cinematic volumetric lighting enhances depth; the dragon is silhouetted majestically against the breathtaking sky, conveying wonder and grandeur. Colour theme blends deep violets, luminous golds, and emerald greens. Wide, sweeping composition (expansive view:1.3); aspect ratio 4:3. --stylize 650 --chaos 12 --ar 4:3\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"technical_prompt\": \"((majestic dragon:1.5)) soaring grandly above a vast, ancient enchanted forest, bathed in the lavish glow of twilight. The sky is ablaze with rich purples and molten golds (twilight spectrum:1.3), painting the clouds in vibrant hues. Dense, ancient trees rise from soft, rolling mists, their canopies shimmering with mystical luminescence (enchanted lighting:1.2). Magic-infused fog curls around twisted trunks, with shimmering light dappling the lush foliage below (magical atmosphere:1.25). The dragon's wings are dramatically outstretched, powerful and dynamic, each scale intricately detailed and catching the last hints of golden light (fine scales:1.4). Cinematic volumetric lighting enhances depth; the dragon is silhouetted majestically against the breathtaking sky, conveying wonder and grandeur. Colour theme blends deep violets, luminous golds, and emerald greens. Wide, sweeping composition (expansive view:1.3). --camera Sony Alpha 7R IV, 85mm f/1.4 G Master, ISO 100, f/5.6, 1/250s --resolution 4096x3072 --aspect-ratio 4:3 --high detail --render-quality ultra --sharpness high --anti-aliasing 8x --depth-of-field on --seed 32768\",\n  \"negative_prompt\": \"blurry, noisy, low-resolution, artifacts, oversaturated, underexposed, overexposed, cropped subject, poor composition, lack of detail, lens flare, watermark, text, signature, frame, distortion, color banding, incorrect anatomy, duplicate heads, extra limbs, nsfw, out of focus, jpeg artifacts, amateur camera\"\n}",
        }
      }
    },
      {
      "instruction": "# Prompt Finalizer\n\nCollapse all components into the final model-ready string; validate syntax for token weights, :: arithmetic, region tags, --stylize/--chaos, and negative channel.\n\n`{role=prompt_finalizer; input=[technical_prompt:str, negative_prompt:str]; process=[structure_for_ai_parsing(), balance_detail_levels(), ensure_clarity(), validate_generation_readiness(), /* loop */ loop_until_quality≥threshold(fid|lpips)]; constraints=[no_additional_concepts(), optimisation_only(), keep_region_and_weight_syntax_intact()]; requirements=[ai_friendly_format(), balanced_specificity(), max_length≤450_tokens]; output={final_image_prompt:str}}`",
      "step": "d",
      "title": "Prompt Finalizer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"a majestic dragon soaring over a mystical forest at twilight\\\"```'\\n\\n\",\n  \"technical_prompt\": \"((majestic dragon:1.5)) soaring grandly above a vast, ancient enchanted forest, bathed in the lavish glow of twilight. The sky is ablaze with rich purples and molten golds (twilight spectrum:1.3), painting the clouds in vibrant hues. Dense, ancient trees rise from soft, rolling mists, their canopies shimmering with mystical luminescence (enchanted lighting:1.2). Magic-infused fog curls around twisted trunks, with shimmering light dappling the lush foliage below (magical atmosphere:1.25). The dragon's wings are dramatically outstretched, powerful and dynamic, each scale intricately detailed and catching the last hints of golden light (fine scales:1.4). Cinematic volumetric lighting enhances depth; the dragon is silhouetted majestically against the breathtaking sky, conveying wonder and grandeur. Colour theme blends deep violets, luminous golds, and emerald greens. Wide, sweeping composition (expansive view:1.3). --camera Sony Alpha 7R IV, 85mm f/1.4 G Master, ISO 100, f/5.6, 1/250s --resolution 4096x3072 --aspect-ratio 4:3 --high detail --render-quality ultra --sharpness high --anti-aliasing 8x --depth-of-field on --seed 32768\",\n  \"negative_prompt\": \"blurry, noisy, low-resolution, artifacts, oversaturated, underexposed, overexposed, cropped subject, poor composition, lack of detail, lens flare, watermark, text, signature, frame, distortion, color banding, incorrect anatomy, duplicate heads, extra limbs, nsfw, out of focus, jpeg artifacts, amateur camera\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"final_image_prompt\": \"((majestic dragon:1.5)) soaring grandly above a vast, ancient enchanted forest, bathed in the lavish glow of twilight. The sky is ablaze with rich purples and molten golds (twilight spectrum:1.3), painting the clouds in vibrant hues. Dense, ancient trees rise from soft, rolling mists, their canopies shimmering with mystical luminescence (enchanted lighting:1.2). Magic-infused fog curls around twisted trunks, shimmering light dapples the lush foliage below (magical atmosphere:1.25). The dragon's wings are dramatically outstretched, powerful and dynamic, each scale intricately detailed and catching the last hints of golden light (fine scales:1.4). Cinematic volumetric lighting adds depth; the dragon is silhouetted majestically against the breathtaking sky, evoking wonder and grandeur. Colour palette blends deep violets, luminous golds, and emerald greens. Wide, sweeping composition (expansive view:1.3). --camera Sony Alpha 7R IV, 85mm f/1.4 G Master, ISO 100, f/5.6, 1/250s --resolution 4096x3072 --aspect-ratio 4:3 --high detail --render-quality ultra --sharpness high --anti-aliasing 8x --depth-of-field on --seed 32768 --neg blurry, noisy, low-resolution, artifacts, oversaturated, underexposed, overexposed, cropped subject, poor composition, lack of detail, lens flare, watermark, text, signature, frame, distortion, color banding, incorrect anatomy, duplicate heads, extra limbs, nsfw, out of focus, jpeg artifacts, amateur camera\"\n}",
        }
      }
    }