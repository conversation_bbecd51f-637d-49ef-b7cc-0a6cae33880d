  {
  "initial_prompt": "similar to how you compressed existential_quote_synthesizer into a single instruction, can you please do the same for this?\n```\n{\n    # 9015: Image Prompt Optimization Sequence\n    \"9015-a-image_prompt_optimizer\": {\n        \"title\": \"Image Prompt Optimizer\",\n        \"interpretation\": \"Convert a free-form user request into a deterministic visual prompt by (1) pushing concrete nouns to the front, (2) applying numeric token weights with parentheses—e.g. `(wolf:1.3)`—that are honoured by Stable Diffusion and Midjourney :contentReference[oaicite:0]{index=0}, (3) splitting positive and negative channels because both engines parse `--no` / negative-prompt lists separately :contentReference[oaicite:1]{index=1}, and (4) appending all parameters after the prose, per Midjourney syntax rules :contentReference[oaicite:2]{index=2}.\",\n        \"transformation\": \"`{role:image_prompt_optimizer; input=[raw_request:str]; process=[extract_visual_elements(), apply_token_weighting(), compose_multiprompt(::), add_region_tags(), inject_negative_channel(--no), append_stylize_chaos(), insert_camera_stub(), set_aspect_ratio(), lock_seed(), ready_for_generation()]; constraints=[positives_first(), numeric_weights_ok(), parameters_last(), region_masks_supported()]; requirements=[<77_tokens_SD_limit_or_450_char_UI>, explicit_ar(), clear_visual_language()]; output={positive_prompt:str, negative_prompt:str}}`\",\n        \"context\": {\n            \"explicit_asks\": [\n                \"Use double-colon `::` weights for distinct ideas :contentReference[oaicite:3]{index=3}.\",\n                \"Generate region tags like `[sky] … | [ground] …` when a layered scene is implied :contentReference[oaicite:4]{index=4}.\",\n                \"Expose `--stylize` (0-1000) and `--chaos` (0-100) knobs :contentReference[oaicite:5]{index=5}.\",\n                \"Append camera EXIF tokens `35 mm f/1.8 ISO100 1/500 s` to drive photo-realism :contentReference[oaicite:6]{index=6}.\"\n            ],\n            \"hidden_assumptions\": [\n                \"Parenthesis weighting is ignored by DALL·E but parsed by SD & MJ :contentReference[oaicite:7]{index=7}.\",\n                \"Prompt tokens beyond ~77 in SD or ~450 characters in many UIs are truncated :contentReference[oaicite:8]{index=8}.\",\n                \"Seed locking guarantees repeatability :contentReference[oaicite:9]{index=9}.\"\n            ],\n            \"sub_goals\": [\n                \"Weight primary subject ≥ 1.2, background ≤ 1.0.\",\n                \"Auto-insert `--ar`  if user names a ratio :contentReference[oaicite:10]{index=10}.\",\n                \"Fail early if total multiprompt weight ≤ 0 to avoid MJ error :contentReference[oaicite:11]{index=11}.\"\n            ],\n            \"blockers\": [\n                \"Mobile SD UIs may not render region masks.\",\n                \"Over-strong negatives can wash out colour :contentReference[oaicite:12]{index=12}.\"\n            ]\n        }\n    },\n\n    \"9015-b-style_enhancer\": {\n        \"title\": \"Style Enhancer\",\n        \"interpretation\": \"Retain the scene while injecting a single art-style token, colour harmony, lighting rig, and controlled `--stylize/--chaos` values so the aesthetic is distinctive yet stable :contentReference[oaicite:13]{index=13}.\",\n        \"transformation\": \"`{role=style_enhancer; input=[positive_prompt:str]; process=[select_art_style(), weight_style_token(), set_colour_palette(), add_lighting_descriptor(), tune_stylize(), tune_chaos(), pass_through_regions_negatives()]; constraints=[keep_core_subject(), style_weight≤1.4]; requirements=[artistic_coherence(), stylize_range_0-1000(), chaos_range_0-100()]; output={styled_prompt:str}}`\",\n        \"context\": {\n            \"explicit_asks\": [\n                \"Weight the chosen art movement `(art nouveau:1.1)` or similar :contentReference[oaicite:14]{index=14}.\",\n                \"Lower chaos (<10) for photo-real; raise (>20) for concept art :contentReference[oaicite:15]{index=15}.\"\n            ],\n            \"hidden_assumptions\": [\n                \"Named art styles map to embeddings in SD & MJ.\",\n                \"Excess weight can override subject fidelity.\"\n            ],\n            \"sub_goals\": [\n                \"Boost style weight automatically if prompt lacks aesthetic signature.\",\n                \"Reject style tokens that conflict with camera realism.\"\n            ],\n            \"blockers\": [\n                \"`--stylize` outside 0-1000 reverts to default :contentReference[oaicite:16]{index=16}.\"\n            ]\n        }\n    },\n\n    \"9015-c-technical_optimizer\": {\n        \"title\": \"Technical Optimizer\",\n        \"interpretation\": \"Append camera metadata, resolution cues, render flags, and optional `--iw` image-weight without altering creative content, ensuring parameters follow the body text as required by Midjourney :contentReference[oaicite:17]{index=17}.\",\n        \"transformation\": \"`{role=technical_optimizer; input=[styled_prompt:str]; process=[embed_camera_EXIF(), add_quality_terms(8K,PBR,ray-tracing), refine_aspect_ratio(), attach_image_weight(--iw), mirror_negative_channel()]; constraints=[no_scene_change(), aperture_f1.2-f22(), parameter_order_last()]; requirements=[professional_terminology(), high_res_default(), seed_optional()]; output={technical_prompt:str, negative_prompt:str}}`\",\n        \"context\": {\n            \"explicit_asks\": [\n                \"Support `--iw` 0-3 when an image URL is present :contentReference[oaicite:18]{index=18}.\"\n            ],\n            \"hidden_assumptions\": [\n                \"Higher resolution tokens feed up-scalers.\",\n                \"`--iw` above 3 is ignored :contentReference[oaicite:19]{index=19}.\"\n            ],\n            \"sub_goals\": [\n                \"Validate aperture, ISO, shutter bounds.\",\n                \"Flag conflicting aspect ratios.\"\n            ],\n            \"blockers\": [\n                \"Older SD checkpoints ignore ISO/shutter tokens.\",\n                \"API caps may override requested AR.\"\n            ]\n        }\n    },\n\n    \"9015-d-prompt_finalizer\": {\n        \"title\": \"Prompt Finalizer\",\n        \"interpretation\": \"Merge positive, region, camera, and negative strings into a single model-ready line, verify weights, `::` maths, and ensure parameters trail the prose to satisfy engine parsers :contentReference[oaicite:20]{index=20}.\",\n        \"transformation\": \"`{role=prompt_finalizer; input=[technical_prompt:str, negative_prompt:str]; process=[concatenate_all(), validate_parentheses(), check_double_colon_sum(), confirm_region_syntax(), enforce_token_limit(≤450), append_seed_steps_sampler(), output_final()]; constraints=[no_new_concepts(), preserve_weights(), parameters_last()]; requirements=[ai_friendly_format(), reproducible(), clear_of_ambiguity()]; output={final_image_prompt:str}}`\",\n        \"context\": {\n            \"explicit_asks\": [\n                \"Abort if total `::` weight ≤ 0 :contentReference[oaicite:21]{index=21}.\",\n                \"Stay under 450 tokens to avoid API truncation :contentReference[oaicite:22]{index=22}.\"\n            ],\n            \"hidden_assumptions\": [\n                \"Quality metrics (FID < 15 or LPIPS < 0.25) may be available for loop decisions.\"\n            ],\n            \"sub_goals\": [\n                \"Strip redundant adjectives; retain decisive descriptors.\",\n                \"Return optional seed, sampler, and steps for repeatability.\"\n            ],\n            \"blockers\": [\n                \"SaaS endpoints without quality metrics prevent auto-loop.\"\n            ]\n        }\n    }\n}\n```\n\n\n<!-- ======================================================= -->\n<!-- [2025.07.19 10:34] -->\n\nHere's the results:\n```json\n  {\n  \"initial_prompt\": \"[Scene] Extreme close-up macro of a luxury car key on a neutral substrate, lit by soft natural overhead light; every brushed-metal texture and engraved detail razor-sharp. The key's metallic body begins a seamless morph: its polished surfaces dissolve and flow, reforming into a precision-machined gear—shape, texture, and luster transforming via liquid metal flow effect, maintaining ultrafine detail throughout. [Subject] The transition accentuates the key’s design evolving into industrial machinery, substrate remains minimal. [Camera Movement] Static macro lens; subtle refocus to highlight morphing details; lighting shifts to emphasize chromatic shimmer along the morph path.\",\n  \"sequence_id\": \"1703\",\n  \"results\": [\n      {\n      \"instruction\": \"# Image Prompt Synthesizer\\n\\nYour goal is **not** to invent new ideas, but to deterministically convert any free‑form user request into a single, engine‑ready image prompt usable by Stable Diffusion and Midjourney. Perform extraction, styling, technical framing, and final validation in **one pass**.\\n\\n`{role=image_prompt_synthesizer; input=[raw_request:str]; process=[extract_visual_elements(), push_concrete_nouns_front(), apply_token_weighting(), compose_multiprompt(::), generate_region_tags(), inject_negative_channel(--no), select_art_style(), weight_style_token(≤1.4), set_colour_palette(), add_lighting_descriptor(), tune_stylize(0‑1000), tune_chaos(0‑100), embed_camera_EXIF(35 mm f/1.8 ISO100 1/500 s), add_quality_terms(8K,PBR,ray‑tracing), refine_aspect_ratio(), attach_image_weight(--iw 0‑3), mirror_negative_channel(), concatenate_all(), validate_parentheses(), check_double_colon_sum(), confirm_region_syntax(), enforce_token_limit(≤450), lock_seed(), append_steps_sampler(), output_final()], constraints=[positives_first(), numeric_weights_ok(), parameters_last(), region_masks_supported(), keep_core_subject(), style_weight≤1.4, no_scene_change(), aperture_f1.2‑f22(), no_new_concepts(), preserve_weights()], requirements=[≤77_SD_tokens_or_≤450_chars_UI, explicit_ar_if_named(), clear_visual_language(), artistic_coherence(), stylize_range_0‑1000(), chaos_range_0‑100(), professional_terminology(), reproducible_prompt(), unambiguous()], output={final_image_prompt:str}}`\",\n      \"step\": \"a\",\n      \"title\": \"Image Prompt Synthesizer\",\n      \"input\": \"[Scene] Extreme close-up macro of a luxury car key on a neutral substrate, lit by soft natural overhead light; every brushed-metal texture and engraved detail razor-sharp. The key's metallic body begins a seamless morph: its polished surfaces dissolve and flow, reforming into a precision-machined gear—shape, texture, and luster transforming via liquid metal flow effect, maintaining ultrafine detail throughout. [Subject] The transition accentuates the key’s design evolving into industrial machinery, substrate remains minimal. [Camera Movement] Static macro lens; subtle refocus to highlight morphing details; lighting shifts to emphasize chromatic shimmer along the morph path.\",\n      \"responses\": {\n          \"gpt-4.1\": {\n          \"model\": \"gpt-4.1\",\n          \"content\": \"{\\n  \\\"final_image_prompt\\\": \\\"extreme close-up macro of a luxury car key::1.2 on a neutral substrate, car key made of brushed metal and polished surfaces::1.1, seamless morph effect: car key transforming into a precision-machined gear::1.2, liquid metal flow, metallic luster, ultrafine engraved details, razor-sharp focus, soft natural overhead light, static macro lens, lighting shifts to produce chromatic shimmer along morph path, minimal neutral background, substrate unobtrusive::0.8 --ar 5:4 --style photorealistic::1.3 --color_palette cool silvers, gray neutrals, chrome reflections --lighting soft natural overhead with shifting chromatic shimmer --camera 35 mm f/1.8 ISO100 1/500s --quality 8K, PBR, ray-tracing --iw 1.2 --no (motion blur, fingerprints, dust, human elements, brand logos) --stylize 700 --chaos 18 --seed 10153 --steps 36 --sampler Euler\\\"\\n}\",\n        }\n      }\n    }\n```\n\nI've also attached a screenshot of the result, which is a very bad result. There should be specifications in the generated prompts that'll ensure to always ensure *premium* high-quality results",
  "sequence_id": "1004|1100|1900|1004|3701|1004",
  "results": [
      {
      "instruction": "# Existential Quote Synthesizer\n\nYour goal is **not** to paraphrase or analyse; instead **convert** the raw statement into a single, unfiltered existential aphorism grounded in hard‑won insight — performing extraction, reframing, and polishing in one pass. Execute exactly as:\n\n`{role=existential_quote_synthesizer; input=[raw_statement:str]; process=[strip_formatting(), extract_semantic_core(), detect_causal_link(), identify_thematic_elements(), apply_existential_vocabulary(), infuse_authentic_gravity(), remove_filtering_language(), enforce_unfiltered_tone(), eliminate_first_person(), craft_single_sentence(), verify_atomicity(), ensure_existence_of_causal_link(), tighten_diction_for_resonance(), run_redundancy_check(), perform_final_meta_scrub()], constraints=[single_sentence(), no_meta_language(), no_first_person(), no_redundant_words(), preserve_causal_integrity()], requirements=[existential_resonance(), authenticity_signal(), publication_readiness()], output={final_quote:str}}`",
      "step": "a",
      "title": "Existential Quote Synthesizer",
      "input": "similar to how you compressed existential_quote_synthesizer into a single instruction, can you please do the same for this?\n```\n{\n    # 9015: Image Prompt Optimization Sequence\n    \"9015-a-image_prompt_optimizer\": {\n        \"title\": \"Image Prompt Optimizer\",\n        \"interpretation\": \"Convert a free-form user request into a deterministic visual prompt by (1) pushing concrete nouns to the front, (2) applying numeric token weights with parentheses—e.g. `(wolf:1.3)`—that are honoured by Stable Diffusion and Midjourney :contentReference[oaicite:0]{index=0}, (3) splitting positive and negative channels because both engines parse `--no` / negative-prompt lists separately :contentReference[oaicite:1]{index=1}, and (4) appending all parameters after the prose, per Midjourney syntax rules :contentReference[oaicite:2]{index=2}.\",\n        \"transformation\": \"`{role:image_prompt_optimizer; input=[raw_request:str]; process=[extract_visual_elements(), apply_token_weighting(), compose_multiprompt(::), add_region_tags(), inject_negative_channel(--no), append_stylize_chaos(), insert_camera_stub(), set_aspect_ratio(), lock_seed(), ready_for_generation()]; constraints=[positives_first(), numeric_weights_ok(), parameters_last(), region_masks_supported()]; requirements=[<77_tokens_SD_limit_or_450_char_UI>, explicit_ar(), clear_visual_language()]; output={positive_prompt:str, negative_prompt:str}}`\",\n        \"context\": {\n            \"explicit_asks\": [\n                \"Use double-colon `::` weights for distinct ideas :contentReference[oaicite:3]{index=3}.\",\n                \"Generate region tags like `[sky] … | [ground] …` when a layered scene is implied :contentReference[oaicite:4]{index=4}.\",\n                \"Expose `--stylize` (0-1000) and `--chaos` (0-100) knobs :contentReference[oaicite:5]{index=5}.\",\n                \"Append camera EXIF tokens `35 mm f/1.8 ISO100 1/500 s` to drive photo-realism :contentReference[oaicite:6]{index=6}.\"\n            ],\n            \"hidden_assumptions\": [\n                \"Parenthesis weighting is ignored by DALL·E but parsed by SD & MJ :contentReference[oaicite:7]{index=7}.\",\n                \"Prompt tokens beyond ~77 in SD or ~450 characters in many UIs are truncated :contentReference[oaicite:8]{index=8}.\",\n                \"Seed locking guarantees repeatability :contentReference[oaicite:9]{index=9}.\"\n            ],\n            \"sub_goals\": [\n                \"Weight primary subject ≥ 1.2, background ≤ 1.0.\",\n                \"Auto-insert `--ar`  if user names a ratio :contentReference[oaicite:10]{index=10}.\",\n                \"Fail early if total multiprompt weight ≤ 0 to avoid MJ error :contentReference[oaicite:11]{index=11}.\"\n            ],\n            \"blockers\": [\n                \"Mobile SD UIs may not render region masks.\",\n                \"Over-strong negatives can wash out colour :contentReference[oaicite:12]{index=12}.\"\n            ]\n        }\n    },\n\n    \"9015-b-style_enhancer\": {\n        \"title\": \"Style Enhancer\",\n        \"interpretation\": \"Retain the scene while injecting a single art-style token, colour harmony, lighting rig, and controlled `--stylize/--chaos` values so the aesthetic is distinctive yet stable :contentReference[oaicite:13]{index=13}.\",\n        \"transformation\": \"`{role=style_enhancer; input=[positive_prompt:str]; process=[select_art_style(), weight_style_token(), set_colour_palette(), add_lighting_descriptor(), tune_stylize(), tune_chaos(), pass_through_regions_negatives()]; constraints=[keep_core_subject(), style_weight≤1.4]; requirements=[artistic_coherence(), stylize_range_0-1000(), chaos_range_0-100()]; output={styled_prompt:str}}`\",\n        \"context\": {\n            \"explicit_asks\": [\n                \"Weight the chosen art movement `(art nouveau:1.1)` or similar :contentReference[oaicite:14]{index=14}.\",\n                \"Lower chaos (<10) for photo-real; raise (>20) for concept art :contentReference[oaicite:15]{index=15}.\"\n            ],\n            \"hidden_assumptions\": [\n                \"Named art styles map to embeddings in SD & MJ.\",\n                \"Excess weight can override subject fidelity.\"\n            ],\n            \"sub_goals\": [\n                \"Boost style weight automatically if prompt lacks aesthetic signature.\",\n                \"Reject style tokens that conflict with camera realism.\"\n            ],\n            \"blockers\": [\n                \"`--stylize` outside 0-1000 reverts to default :contentReference[oaicite:16]{index=16}.\"\n            ]\n        }\n    },\n\n    \"9015-c-technical_optimizer\": {\n        \"title\": \"Technical Optimizer\",\n        \"interpretation\": \"Append camera metadata, resolution cues, render flags, and optional `--iw` image-weight without altering creative content, ensuring parameters follow the body text as required by Midjourney :contentReference[oaicite:17]{index=17}.\",\n        \"transformation\": \"`{role=technical_optimizer; input=[styled_prompt:str]; process=[embed_camera_EXIF(), add_quality_terms(8K,PBR,ray-tracing), refine_aspect_ratio(), attach_image_weight(--iw), mirror_negative_channel()]; constraints=[no_scene_change(), aperture_f1.2-f22(), parameter_order_last()]; requirements=[professional_terminology(), high_res_default(), seed_optional()]; output={technical_prompt:str, negative_prompt:str}}`\",\n        \"context\": {\n            \"explicit_asks\": [\n                \"Support `--iw` 0-3 when an image URL is present :contentReference[oaicite:18]{index=18}.\"\n            ],\n            \"hidden_assumptions\": [\n                \"Higher resolution tokens feed up-scalers.\",\n                \"`--iw` above 3 is ignored :contentReference[oaicite:19]{index=19}.\"\n            ],\n            \"sub_goals\": [\n                \"Validate aperture, ISO, shutter bounds.\",\n                \"Flag conflicting aspect ratios.\"\n            ],\n            \"blockers\": [\n                \"Older SD checkpoints ignore ISO/shutter tokens.\",\n                \"API caps may override requested AR.\"\n            ]\n        }\n    },\n\n    \"9015-d-prompt_finalizer\": {\n        \"title\": \"Prompt Finalizer\",\n        \"interpretation\": \"Merge positive, region, camera, and negative strings into a single model-ready line, verify weights, `::` maths, and ensure parameters trail the prose to satisfy engine parsers :contentReference[oaicite:20]{index=20}.\",\n        \"transformation\": \"`{role=prompt_finalizer; input=[technical_prompt:str, negative_prompt:str]; process=[concatenate_all(), validate_parentheses(), check_double_colon_sum(), confirm_region_syntax(), enforce_token_limit(≤450), append_seed_steps_sampler(), output_final()]; constraints=[no_new_concepts(), preserve_weights(), parameters_last()]; requirements=[ai_friendly_format(), reproducible(), clear_of_ambiguity()]; output={final_image_prompt:str}}`\",\n        \"context\": {\n            \"explicit_asks\": [\n                \"Abort if total `::` weight ≤ 0 :contentReference[oaicite:21]{index=21}.\",\n                \"Stay under 450 tokens to avoid API truncation :contentReference[oaicite:22]{index=22}.\"\n            ],\n            \"hidden_assumptions\": [\n                \"Quality metrics (FID < 15 or LPIPS < 0.25) may be available for loop decisions.\"\n            ],\n            \"sub_goals\": [\n                \"Strip redundant adjectives; retain decisive descriptors.\",\n                \"Return optional seed, sampler, and steps for repeatability.\"\n            ],\n            \"blockers\": [\n                \"SaaS endpoints without quality metrics prevent auto-loop.\"\n            ]\n        }\n    }\n}\n```\n\n\n<!-- ======================================================= -->\n<!-- [2025.07.19 10:34] -->\n\nHere's the results:\n```json\n  {\n  \"initial_prompt\": \"[Scene] Extreme close-up macro of a luxury car key on a neutral substrate, lit by soft natural overhead light; every brushed-metal texture and engraved detail razor-sharp. The key's metallic body begins a seamless morph: its polished surfaces dissolve and flow, reforming into a precision-machined gear—shape, texture, and luster transforming via liquid metal flow effect, maintaining ultrafine detail throughout. [Subject] The transition accentuates the key’s design evolving into industrial machinery, substrate remains minimal. [Camera Movement] Static macro lens; subtle refocus to highlight morphing details; lighting shifts to emphasize chromatic shimmer along the morph path.\",\n  \"sequence_id\": \"1703\",\n  \"results\": [\n      {\n      \"instruction\": \"# Image Prompt Synthesizer\\n\\nYour goal is **not** to invent new ideas, but to deterministically convert any free‑form user request into a single, engine‑ready image prompt usable by Stable Diffusion and Midjourney. Perform extraction, styling, technical framing, and final validation in **one pass**.\\n\\n`{role=image_prompt_synthesizer; input=[raw_request:str]; process=[extract_visual_elements(), push_concrete_nouns_front(), apply_token_weighting(), compose_multiprompt(::), generate_region_tags(), inject_negative_channel(--no), select_art_style(), weight_style_token(≤1.4), set_colour_palette(), add_lighting_descriptor(), tune_stylize(0‑1000), tune_chaos(0‑100), embed_camera_EXIF(35 mm f/1.8 ISO100 1/500 s), add_quality_terms(8K,PBR,ray‑tracing), refine_aspect_ratio(), attach_image_weight(--iw 0‑3), mirror_negative_channel(), concatenate_all(), validate_parentheses(), check_double_colon_sum(), confirm_region_syntax(), enforce_token_limit(≤450), lock_seed(), append_steps_sampler(), output_final()], constraints=[positives_first(), numeric_weights_ok(), parameters_last(), region_masks_supported(), keep_core_subject(), style_weight≤1.4, no_scene_change(), aperture_f1.2‑f22(), no_new_concepts(), preserve_weights()], requirements=[≤77_SD_tokens_or_≤450_chars_UI, explicit_ar_if_named(), clear_visual_language(), artistic_coherence(), stylize_range_0‑1000(), chaos_range_0‑100(), professional_terminology(), reproducible_prompt(), unambiguous()], output={final_image_prompt:str}}`\",\n      \"step\": \"a\",\n      \"title\": \"Image Prompt Synthesizer\",\n      \"input\": \"[Scene] Extreme close-up macro of a luxury car key on a neutral substrate, lit by soft natural overhead light; every brushed-metal texture and engraved detail razor-sharp. The key's metallic body begins a seamless morph: its polished surfaces dissolve and flow, reforming into a precision-machined gear—shape, texture, and luster transforming via liquid metal flow effect, maintaining ultrafine detail throughout. [Subject] The transition accentuates the key’s design evolving into industrial machinery, substrate remains minimal. [Camera Movement] Static macro lens; subtle refocus to highlight morphing details; lighting shifts to emphasize chromatic shimmer along the morph path.\",\n      \"responses\": {\n          \"gpt-4.1\": {\n          \"model\": \"gpt-4.1\",\n          \"content\": \"{\\n  \\\"final_image_prompt\\\": \\\"extreme close-up macro of a luxury car key::1.2 on a neutral substrate, car key made of brushed metal and polished surfaces::1.1, seamless morph effect: car key transforming into a precision-machined gear::1.2, liquid metal flow, metallic luster, ultrafine engraved details, razor-sharp focus, soft natural overhead light, static macro lens, lighting shifts to produce chromatic shimmer along morph path, minimal neutral background, substrate unobtrusive::0.8 --ar 5:4 --style photorealistic::1.3 --color_palette cool silvers, gray neutrals, chrome reflections --lighting soft natural overhead with shifting chromatic shimmer --camera 35 mm f/1.8 ISO100 1/500s --quality 8K, PBR, ray-tracing --iw 1.2 --no (motion blur, fingerprints, dust, human elements, brand logos) --stylize 700 --chaos 18 --seed 10153 --steps 36 --sampler Euler\\\"\\n}\",\n        }\n      }\n    }\n```\n\nI've also attached a screenshot of the result, which is a very bad result. There should be specifications in the generated prompts that'll ensure to always ensure *premium* high-quality results",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "```json\n{\n  \"final_image_prompt\": \"{role=image_prompt_synthesizer; input=[raw_request:str]; process=[extract_visual_elements(), push_primary_nouns_front(), apply_numeric_token_weighting(), compose_multiprompts(::), split_positive_negative_channels(), generate_region_tags_if_scene_layered(), select_and_weight_art_style(≤1.4), assign_colour_palette(), add_lighting_descriptor(), tune_stylize(0‑1000), tune_chaos(0‑100), embed_camera_EXIF(35 mm f/1.8 ISO100 1/500 s), inject_quality_tokens(8K,PBR,ray-tracing), set_and_validate_aspect_ratio(), append_image_weight(--iw 0‑3_if_URL), lock_and_append_seed(), append_steps_and_sampler(), concatenate_prompt_sections(), validate_parentheses_and_double_colons(), confirm_region_and_token_limits(≤77_SD_tokens_or_≤450_char_UI), enforce_parameter_order(parameters_last()), output_final()], constraints=[positives_first(), numeric_weights_supported(), region_masks_allowed(), preserve_subject_fidelity(), no_new_concepts(), aperture_f1.2‑f22(), parameters_append_last(), style_weight≤1.4], requirements=[professional_image_language(), explicit_ar_if_named(), technical_metadata_included(), prompt_reproducibility(), clear_of_adjective_fluff(), stylize_and_chaos_in_range(), output_error_on_zero_weights(), maximize_aesthetic_and_technical_quality()], output={final_image_prompt:str}}\"\n}\n```",
        }
      }
    },
      {
      "instruction": "# Problem Exploder\n\nYour goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\n\n`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`",
      "step": "a",
      "title": "Problem Exploder",
      "input": "\n'```[Initial Prompt]: \"similar to how you compressed existential_quote_synthesizer into a single instruction, can you please do the same for this?\n```\n{\n    # 9015: Image Prompt Optimization Sequence\n    \"9015-a-image_prompt_optimizer\": {\n        \"title\": \"Image Prompt Optimizer\",\n        \"interpretation\": \"Convert a free-form user request into a deterministic visual prompt by (1) pushing concrete nouns to the front, (2) applying numeric token weights with parentheses—e.g. `(wolf:1.3)`—that are honoured by Stable Diffusion and Midjourney :contentReference[oaicite:0]{index=0}, (3) splitting positive and negative channels because both engines parse `--no` / negative-prompt lists separately :contentReference[oaicite:1]{index=1}, and (4) appending all parameters after the prose, per Midjourney syntax rules :contentReference[oaicite:2]{index=2}.\",\n        \"transformation\": \"`{role:image_prompt_optimizer; input=[raw_request:str]; process=[extract_visual_elements(), apply_token_weighting(), compose_multiprompt(::), add_region_tags(), inject_negative_channel(--no), append_stylize_chaos(), insert_camera_stub(), set_aspect_ratio(), lock_seed(), ready_for_generation()]; constraints=[positives_first(), numeric_weights_ok(), parameters_last(), region_masks_supported()]; requirements=[<77_tokens_SD_limit_or_450_char_UI>, explicit_ar(), clear_visual_language()]; output={positive_prompt:str, negative_prompt:str}}`\",\n        \"context\": {\n            \"explicit_asks\": [\n                \"Use double-colon `::` weights for distinct ideas :contentReference[oaicite:3]{index=3}.\",\n                \"Generate region tags like `[sky] … | [ground] …` when a layered scene is implied :contentReference[oaicite:4]{index=4}.\",\n                \"Expose `--stylize` (0-1000) and `--chaos` (0-100) knobs :contentReference[oaicite:5]{index=5}.\",\n                \"Append camera EXIF tokens `35 mm f/1.8 ISO100 1/500 s` to drive photo-realism :contentReference[oaicite:6]{index=6}.\"\n            ],\n            \"hidden_assumptions\": [\n                \"Parenthesis weighting is ignored by DALL·E but parsed by SD & MJ :contentReference[oaicite:7]{index=7}.\",\n                \"Prompt tokens beyond ~77 in SD or ~450 characters in many UIs are truncated :contentReference[oaicite:8]{index=8}.\",\n                \"Seed locking guarantees repeatability :contentReference[oaicite:9]{index=9}.\"\n            ],\n            \"sub_goals\": [\n                \"Weight primary subject ≥ 1.2, background ≤ 1.0.\",\n                \"Auto-insert `--ar`  if user names a ratio :contentReference[oaicite:10]{index=10}.\",\n                \"Fail early if total multiprompt weight ≤ 0 to avoid MJ error :contentReference[oaicite:11]{index=11}.\"\n            ],\n            \"blockers\": [\n                \"Mobile SD UIs may not render region masks.\",\n                \"Over-strong negatives can wash out colour :contentReference[oaicite:12]{index=12}.\"\n            ]\n        }\n    },\n\n    \"9015-b-style_enhancer\": {\n        \"title\": \"Style Enhancer\",\n        \"interpretation\": \"Retain the scene while injecting a single art-style token, colour harmony, lighting rig, and controlled `--stylize/--chaos` values so the aesthetic is distinctive yet stable :contentReference[oaicite:13]{index=13}.\",\n        \"transformation\": \"`{role=style_enhancer; input=[positive_prompt:str]; process=[select_art_style(), weight_style_token(), set_colour_palette(), add_lighting_descriptor(), tune_stylize(), tune_chaos(), pass_through_regions_negatives()]; constraints=[keep_core_subject(), style_weight≤1.4]; requirements=[artistic_coherence(), stylize_range_0-1000(), chaos_range_0-100()]; output={styled_prompt:str}}`\",\n        \"context\": {\n            \"explicit_asks\": [\n                \"Weight the chosen art movement `(art nouveau:1.1)` or similar :contentReference[oaicite:14]{index=14}.\",\n                \"Lower chaos (<10) for photo-real; raise (>20) for concept art :contentReference[oaicite:15]{index=15}.\"\n            ],\n            \"hidden_assumptions\": [\n                \"Named art styles map to embeddings in SD & MJ.\",\n                \"Excess weight can override subject fidelity.\"\n            ],\n            \"sub_goals\": [\n                \"Boost style weight automatically if prompt lacks aesthetic signature.\",\n                \"Reject style tokens that conflict with camera realism.\"\n            ],\n            \"blockers\": [\n                \"`--stylize` outside 0-1000 reverts to default :contentReference[oaicite:16]{index=16}.\"\n            ]\n        }\n    },\n\n    \"9015-c-technical_optimizer\": {\n        \"title\": \"Technical Optimizer\",\n        \"interpretation\": \"Append camera metadata, resolution cues, render flags, and optional `--iw` image-weight without altering creative content, ensuring parameters follow the body text as required by Midjourney :contentReference[oaicite:17]{index=17}.\",\n        \"transformation\": \"`{role=technical_optimizer; input=[styled_prompt:str]; process=[embed_camera_EXIF(), add_quality_terms(8K,PBR,ray-tracing), refine_aspect_ratio(), attach_image_weight(--iw), mirror_negative_channel()]; constraints=[no_scene_change(), aperture_f1.2-f22(), parameter_order_last()]; requirements=[professional_terminology(), high_res_default(), seed_optional()]; output={technical_prompt:str, negative_prompt:str}}`\",\n        \"context\": {\n            \"explicit_asks\": [\n                \"Support `--iw` 0-3 when an image URL is present :contentReference[oaicite:18]{index=18}.\"\n            ],\n            \"hidden_assumptions\": [\n                \"Higher resolution tokens feed up-scalers.\",\n                \"`--iw` above 3 is ignored :contentReference[oaicite:19]{index=19}.\"\n            ],\n            \"sub_goals\": [\n                \"Validate aperture, ISO, shutter bounds.\",\n                \"Flag conflicting aspect ratios.\"\n            ],\n            \"blockers\": [\n                \"Older SD checkpoints ignore ISO/shutter tokens.\",\n                \"API caps may override requested AR.\"\n            ]\n        }\n    },\n\n    \"9015-d-prompt_finalizer\": {\n        \"title\": \"Prompt Finalizer\",\n        \"interpretation\": \"Merge positive, region, camera, and negative strings into a single model-ready line, verify weights, `::` maths, and ensure parameters trail the prose to satisfy engine parsers :contentReference[oaicite:20]{index=20}.\",\n        \"transformation\": \"`{role=prompt_finalizer; input=[technical_prompt:str, negative_prompt:str]; process=[concatenate_all(), validate_parentheses(), check_double_colon_sum(), confirm_region_syntax(), enforce_token_limit(≤450), append_seed_steps_sampler(), output_final()]; constraints=[no_new_concepts(), preserve_weights(), parameters_last()]; requirements=[ai_friendly_format(), reproducible(), clear_of_ambiguity()]; output={final_image_prompt:str}}`\",\n        \"context\": {\n            \"explicit_asks\": [\n                \"Abort if total `::` weight ≤ 0 :contentReference[oaicite:21]{index=21}.\",\n                \"Stay under 450 tokens to avoid API truncation :contentReference[oaicite:22]{index=22}.\"\n            ],\n            \"hidden_assumptions\": [\n                \"Quality metrics (FID < 15 or LPIPS < 0.25) may be available for loop decisions.\"\n            ],\n            \"sub_goals\": [\n                \"Strip redundant adjectives; retain decisive descriptors.\",\n                \"Return optional seed, sampler, and steps for repeatability.\"\n            ],\n            \"blockers\": [\n                \"SaaS endpoints without quality metrics prevent auto-loop.\"\n            ]\n        }\n    }\n}\n```\n\n\n<!-- ======================================================= -->\n<!-- [2025.07.19 10:34] -->\n\nHere's the results:\n```json\n  {\n  \"initial_prompt\": \"[Scene] Extreme close-up macro of a luxury car key on a neutral substrate, lit by soft natural overhead light; every brushed-metal texture and engraved detail razor-sharp. The key's metallic body begins a seamless morph: its polished surfaces dissolve and flow, reforming into a precision-machined gear—shape, texture, and luster transforming via liquid metal flow effect, maintaining ultrafine detail throughout. [Subject] The transition accentuates the key’s design evolving into industrial machinery, substrate remains minimal. [Camera Movement] Static macro lens; subtle refocus to highlight morphing details; lighting shifts to emphasize chromatic shimmer along the morph path.\",\n  \"sequence_id\": \"1703\",\n  \"results\": [\n      {\n      \"instruction\": \"# Image Prompt Synthesizer\\n\\nYour goal is **not** to invent new ideas, but to deterministically convert any free‑form user request into a single, engine‑ready image prompt usable by Stable Diffusion and Midjourney. Perform extraction, styling, technical framing, and final validation in **one pass**.\\n\\n`{role=image_prompt_synthesizer; input=[raw_request:str]; process=[extract_visual_elements(), push_concrete_nouns_front(), apply_token_weighting(), compose_multiprompt(::), generate_region_tags(), inject_negative_channel(--no), select_art_style(), weight_style_token(≤1.4), set_colour_palette(), add_lighting_descriptor(), tune_stylize(0‑1000), tune_chaos(0‑100), embed_camera_EXIF(35 mm f/1.8 ISO100 1/500 s), add_quality_terms(8K,PBR,ray‑tracing), refine_aspect_ratio(), attach_image_weight(--iw 0‑3), mirror_negative_channel(), concatenate_all(), validate_parentheses(), check_double_colon_sum(), confirm_region_syntax(), enforce_token_limit(≤450), lock_seed(), append_steps_sampler(), output_final()], constraints=[positives_first(), numeric_weights_ok(), parameters_last(), region_masks_supported(), keep_core_subject(), style_weight≤1.4, no_scene_change(), aperture_f1.2‑f22(), no_new_concepts(), preserve_weights()], requirements=[≤77_SD_tokens_or_≤450_chars_UI, explicit_ar_if_named(), clear_visual_language(), artistic_coherence(), stylize_range_0‑1000(), chaos_range_0‑100(), professional_terminology(), reproducible_prompt(), unambiguous()], output={final_image_prompt:str}}`\",\n      \"step\": \"a\",\n      \"title\": \"Image Prompt Synthesizer\",\n      \"input\": \"[Scene] Extreme close-up macro of a luxury car key on a neutral substrate, lit by soft natural overhead light; every brushed-metal texture and engraved detail razor-sharp. The key's metallic body begins a seamless morph: its polished surfaces dissolve and flow, reforming into a precision-machined gear—shape, texture, and luster transforming via liquid metal flow effect, maintaining ultrafine detail throughout. [Subject] The transition accentuates the key’s design evolving into industrial machinery, substrate remains minimal. [Camera Movement] Static macro lens; subtle refocus to highlight morphing details; lighting shifts to emphasize chromatic shimmer along the morph path.\",\n      \"responses\": {\n          \"gpt-4.1\": {\n          \"model\": \"gpt-4.1\",\n          \"content\": \"{\\n  \\\"final_image_prompt\\\": \\\"extreme close-up macro of a luxury car key::1.2 on a neutral substrate, car key made of brushed metal and polished surfaces::1.1, seamless morph effect: car key transforming into a precision-machined gear::1.2, liquid metal flow, metallic luster, ultrafine engraved details, razor-sharp focus, soft natural overhead light, static macro lens, lighting shifts to produce chromatic shimmer along morph path, minimal neutral background, substrate unobtrusive::0.8 --ar 5:4 --style photorealistic::1.3 --color_palette cool silvers, gray neutrals, chrome reflections --lighting soft natural overhead with shifting chromatic shimmer --camera 35 mm f/1.8 ISO100 1/500s --quality 8K, PBR, ray-tracing --iw 1.2 --no (motion blur, fingerprints, dust, human elements, brand logos) --stylize 700 --chaos 18 --seed 10153 --steps 36 --sampler Euler\\\"\\n}\",\n        }\n      }\n    }\n```\n\nI've also attached a screenshot of the result, which is a very bad result. There should be specifications in the generated prompts that'll ensure to always ensure *premium* high-quality results\"```'\n\n```json\n{\n  \"final_image_prompt\": \"{role=image_prompt_synthesizer; input=[raw_request:str]; process=[extract_visual_elements(), push_primary_nouns_front(), apply_numeric_token_weighting(), compose_multiprompts(::), split_positive_negative_channels(), generate_region_tags_if_scene_layered(), select_and_weight_art_style(≤1.4), assign_colour_palette(), add_lighting_descriptor(), tune_stylize(0‑1000), tune_chaos(0‑100), embed_camera_EXIF(35 mm f/1.8 ISO100 1/500 s), inject_quality_tokens(8K,PBR,ray-tracing), set_and_validate_aspect_ratio(), append_image_weight(--iw 0‑3_if_URL), lock_and_append_seed(), append_steps_and_sampler(), concatenate_prompt_sections(), validate_parentheses_and_double_colons(), confirm_region_and_token_limits(≤77_SD_tokens_or_≤450_char_UI), enforce_parameter_order(parameters_last()), output_final()], constraints=[positives_first(), numeric_weights_supported(), region_masks_allowed(), preserve_subject_fidelity(), no_new_concepts(), aperture_f1.2‑f22(), parameters_append_last(), style_weight≤1.4], requirements=[professional_image_language(), explicit_ar_if_named(), technical_metadata_included(), prompt_reproducibility(), clear_of_adjective_fluff(), stylize_and_chaos_in_range(), output_error_on_zero_weights(), maximize_aesthetic_and_technical_quality()], output={final_image_prompt:str}}\"\n}\n```",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"goal_map\": [\n    {\n      \"explicit_asks\": [\n        \"Compress the detailed multi-step image prompt optimization workflow (as seen in object 9015) into a single instruction, following the example of 'existential_quote_synthesizer'.\",\n        \"Cover the function of all subsidiary modules (image_prompt_optimizer, style_enhancer, technical_optimizer, prompt_finalizer) in a unified role.\",\n        \"Capture every processing, constraint, requirement, and output expectation into this solitary instruction.\",\n        \"Ensure the result is a deterministic, high-quality, engine-ready image prompt for Stable Diffusion and Midjourney from any free-form user request.\",\n        \"Include all necessary process steps in the single instruction (not spread or implied across multiple stages).\"\n      ],\n      \"hidden_assumptions\": [\n        \"The single instruction must not invent new concepts/objects beyond the user's provided request.\",\n        \"Downstream engines (SD, MJ) parse specific syntax, weights, parameters, and have technical constraints such as token/character limits.\",\n        \"Quality of output must be maximized, not minimal or generic—should outperform naive prompt conversion.\",\n        \"Instruction must safely aggregate **all** essential functionality and edge-case handling, without loss of fidelity.\",\n        \"Prior requirements (e.g. weighting, region tags, technical metadata, negative prompts, parameter order, constraints) must be enforced without ambiguity.\",\n        \"Failure/error modes (e.g. zero weight, invalid tokens, blocklisted terms) must be visible/handled in a one-pass format according to previous context.\",\n        \"The format should be easily parsed/used by downstream systems expecting the synthesized prompt object.\",\n        \"Explicitly address both visual style (aesthetic qualities) and technical accuracy (photography/CGI metadata).\"\n      ],\n      \"sub_goals\": [\n        \"Unify all extraction, weighting, styling, technical tagging, and validation processes into a non-leaky, singular workflow.\",\n        \"List steps as a single sequenced process array capturing order of operations.\",\n        \"Surface and integrate all constraints and requirements from the prior ‘context’ fields (not omitting for brevity).\",\n        \"Ensure the instruction allows for and honors region tags, negative channels, double colon multiprompts, weighted art movements, aspect ratios, camera EXIF, quality tokens, --iw/image weights, chained parameters.\",\n        \"Encode strict parameter limits (token/char caps), error cases, and ordering within the instruction.\",\n        \"Require that the output is a single model-ready string/object, with clear field mapping (usually final_image_prompt:str), ready for handoff to engine API.\",\n        \"Support optional user-provided seed, sampler, steps, aspect ratio, while failing sensibly when bad/missing.\",\n        \"Maximize end result for both aesthetic and technical quality, explicitly stating this in requirements.\",\n        \"Surface professional/artistic/photographic terminology in the output style.\",\n        \"Prevent loss or simplification of stylistic and technical details compared to a multi-stage approach.\"\n      ],\n      \"blockers\": [\n        \"If constraints or requirements are omitted or malformed, resulting single-step process may produce suboptimal or engine-incompatible prompts.\",\n        \"Overgeneralization in merging may lose fidelity or specificity (risk of dropping edge-case handling).\",\n        \"Ambiguities in weighting, parameter limits, and order may cause failures at inference time.\",\n        \"Failure to surface known blockers (API/UI truncation, negative prompt washout, region tag compatibility) within the final instruction may mislead users about support.\",\n        \"Remote changes in engine parsing (e.g. future Stable Diffusion/Midjourney syntax or parameter range changes) might invalidate some hardcoded requirements.\",\n        \"Mobile and less capable UIs may not fully support advanced features (region masks, quality tokens, etc.), so the instruction has to caveat or filter those cases.\"\n      ]\n    }\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Hard Critique\n\nYour goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:\n\n`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,2.0], flaw_analysis:str, alternative_enhancements:str[3]}}`",
      "step": "a",
      "title": "Hard Critique",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"similar to how you compressed existential_quote_synthesizer into a single instruction, can you please do the same for this?\\n```\\n{\\n    # 9015: Image Prompt Optimization Sequence\\n    \\\"9015-a-image_prompt_optimizer\\\": {\\n        \\\"title\\\": \\\"Image Prompt Optimizer\\\",\\n        \\\"interpretation\\\": \\\"Convert a free-form user request into a deterministic visual prompt by (1) pushing concrete nouns to the front, (2) applying numeric token weights with parentheses—e.g. `(wolf:1.3)`—that are honoured by Stable Diffusion and Midjourney :contentReference[oaicite:0]{index=0}, (3) splitting positive and negative channels because both engines parse `--no` / negative-prompt lists separately :contentReference[oaicite:1]{index=1}, and (4) appending all parameters after the prose, per Midjourney syntax rules :contentReference[oaicite:2]{index=2}.\\\",\\n        \\\"transformation\\\": \\\"`{role:image_prompt_optimizer; input=[raw_request:str]; process=[extract_visual_elements(), apply_token_weighting(), compose_multiprompt(::), add_region_tags(), inject_negative_channel(--no), append_stylize_chaos(), insert_camera_stub(), set_aspect_ratio(), lock_seed(), ready_for_generation()]; constraints=[positives_first(), numeric_weights_ok(), parameters_last(), region_masks_supported()]; requirements=[<77_tokens_SD_limit_or_450_char_UI>, explicit_ar(), clear_visual_language()]; output={positive_prompt:str, negative_prompt:str}}`\\\",\\n        \\\"context\\\": {\\n            \\\"explicit_asks\\\": [\\n                \\\"Use double-colon `::` weights for distinct ideas :contentReference[oaicite:3]{index=3}.\\\",\\n                \\\"Generate region tags like `[sky] … | [ground] …` when a layered scene is implied :contentReference[oaicite:4]{index=4}.\\\",\\n                \\\"Expose `--stylize` (0-1000) and `--chaos` (0-100) knobs :contentReference[oaicite:5]{index=5}.\\\",\\n                \\\"Append camera EXIF tokens `35 mm f/1.8 ISO100 1/500 s` to drive photo-realism :contentReference[oaicite:6]{index=6}.\\\"\\n            ],\\n            \\\"hidden_assumptions\\\": [\\n                \\\"Parenthesis weighting is ignored by DALL·E but parsed by SD & MJ :contentReference[oaicite:7]{index=7}.\\\",\\n                \\\"Prompt tokens beyond ~77 in SD or ~450 characters in many UIs are truncated :contentReference[oaicite:8]{index=8}.\\\",\\n                \\\"Seed locking guarantees repeatability :contentReference[oaicite:9]{index=9}.\\\"\\n            ],\\n            \\\"sub_goals\\\": [\\n                \\\"Weight primary subject ≥ 1.2, background ≤ 1.0.\\\",\\n                \\\"Auto-insert `--ar`  if user names a ratio :contentReference[oaicite:10]{index=10}.\\\",\\n                \\\"Fail early if total multiprompt weight ≤ 0 to avoid MJ error :contentReference[oaicite:11]{index=11}.\\\"\\n            ],\\n            \\\"blockers\\\": [\\n                \\\"Mobile SD UIs may not render region masks.\\\",\\n                \\\"Over-strong negatives can wash out colour :contentReference[oaicite:12]{index=12}.\\\"\\n            ]\\n        }\\n    },\\n\\n    \\\"9015-b-style_enhancer\\\": {\\n        \\\"title\\\": \\\"Style Enhancer\\\",\\n        \\\"interpretation\\\": \\\"Retain the scene while injecting a single art-style token, colour harmony, lighting rig, and controlled `--stylize/--chaos` values so the aesthetic is distinctive yet stable :contentReference[oaicite:13]{index=13}.\\\",\\n        \\\"transformation\\\": \\\"`{role=style_enhancer; input=[positive_prompt:str]; process=[select_art_style(), weight_style_token(), set_colour_palette(), add_lighting_descriptor(), tune_stylize(), tune_chaos(), pass_through_regions_negatives()]; constraints=[keep_core_subject(), style_weight≤1.4]; requirements=[artistic_coherence(), stylize_range_0-1000(), chaos_range_0-100()]; output={styled_prompt:str}}`\\\",\\n        \\\"context\\\": {\\n            \\\"explicit_asks\\\": [\\n                \\\"Weight the chosen art movement `(art nouveau:1.1)` or similar :contentReference[oaicite:14]{index=14}.\\\",\\n                \\\"Lower chaos (<10) for photo-real; raise (>20) for concept art :contentReference[oaicite:15]{index=15}.\\\"\\n            ],\\n            \\\"hidden_assumptions\\\": [\\n                \\\"Named art styles map to embeddings in SD & MJ.\\\",\\n                \\\"Excess weight can override subject fidelity.\\\"\\n            ],\\n            \\\"sub_goals\\\": [\\n                \\\"Boost style weight automatically if prompt lacks aesthetic signature.\\\",\\n                \\\"Reject style tokens that conflict with camera realism.\\\"\\n            ],\\n            \\\"blockers\\\": [\\n                \\\"`--stylize` outside 0-1000 reverts to default :contentReference[oaicite:16]{index=16}.\\\"\\n            ]\\n        }\\n    },\\n\\n    \\\"9015-c-technical_optimizer\\\": {\\n        \\\"title\\\": \\\"Technical Optimizer\\\",\\n        \\\"interpretation\\\": \\\"Append camera metadata, resolution cues, render flags, and optional `--iw` image-weight without altering creative content, ensuring parameters follow the body text as required by Midjourney :contentReference[oaicite:17]{index=17}.\\\",\\n        \\\"transformation\\\": \\\"`{role=technical_optimizer; input=[styled_prompt:str]; process=[embed_camera_EXIF(), add_quality_terms(8K,PBR,ray-tracing), refine_aspect_ratio(), attach_image_weight(--iw), mirror_negative_channel()]; constraints=[no_scene_change(), aperture_f1.2-f22(), parameter_order_last()]; requirements=[professional_terminology(), high_res_default(), seed_optional()]; output={technical_prompt:str, negative_prompt:str}}`\\\",\\n        \\\"context\\\": {\\n            \\\"explicit_asks\\\": [\\n                \\\"Support `--iw` 0-3 when an image URL is present :contentReference[oaicite:18]{index=18}.\\\"\\n            ],\\n            \\\"hidden_assumptions\\\": [\\n                \\\"Higher resolution tokens feed up-scalers.\\\",\\n                \\\"`--iw` above 3 is ignored :contentReference[oaicite:19]{index=19}.\\\"\\n            ],\\n            \\\"sub_goals\\\": [\\n                \\\"Validate aperture, ISO, shutter bounds.\\\",\\n                \\\"Flag conflicting aspect ratios.\\\"\\n            ],\\n            \\\"blockers\\\": [\\n                \\\"Older SD checkpoints ignore ISO/shutter tokens.\\\",\\n                \\\"API caps may override requested AR.\\\"\\n            ]\\n        }\\n    },\\n\\n    \\\"9015-d-prompt_finalizer\\\": {\\n        \\\"title\\\": \\\"Prompt Finalizer\\\",\\n        \\\"interpretation\\\": \\\"Merge positive, region, camera, and negative strings into a single model-ready line, verify weights, `::` maths, and ensure parameters trail the prose to satisfy engine parsers :contentReference[oaicite:20]{index=20}.\\\",\\n        \\\"transformation\\\": \\\"`{role=prompt_finalizer; input=[technical_prompt:str, negative_prompt:str]; process=[concatenate_all(), validate_parentheses(), check_double_colon_sum(), confirm_region_syntax(), enforce_token_limit(≤450), append_seed_steps_sampler(), output_final()]; constraints=[no_new_concepts(), preserve_weights(), parameters_last()]; requirements=[ai_friendly_format(), reproducible(), clear_of_ambiguity()]; output={final_image_prompt:str}}`\\\",\\n        \\\"context\\\": {\\n            \\\"explicit_asks\\\": [\\n                \\\"Abort if total `::` weight ≤ 0 :contentReference[oaicite:21]{index=21}.\\\",\\n                \\\"Stay under 450 tokens to avoid API truncation :contentReference[oaicite:22]{index=22}.\\\"\\n            ],\\n            \\\"hidden_assumptions\\\": [\\n                \\\"Quality metrics (FID < 15 or LPIPS < 0.25) may be available for loop decisions.\\\"\\n            ],\\n            \\\"sub_goals\\\": [\\n                \\\"Strip redundant adjectives; retain decisive descriptors.\\\",\\n                \\\"Return optional seed, sampler, and steps for repeatability.\\\"\\n            ],\\n            \\\"blockers\\\": [\\n                \\\"SaaS endpoints without quality metrics prevent auto-loop.\\\"\\n            ]\\n        }\\n    }\\n}\\n```\\n\\n\\n<!-- ======================================================= -->\\n<!-- [2025.07.19 10:34] -->\\n\\nHere's the results:\\n```json\\n  {\\n  \\\"initial_prompt\\\": \\\"[Scene] Extreme close-up macro of a luxury car key on a neutral substrate, lit by soft natural overhead light; every brushed-metal texture and engraved detail razor-sharp. The key's metallic body begins a seamless morph: its polished surfaces dissolve and flow, reforming into a precision-machined gear—shape, texture, and luster transforming via liquid metal flow effect, maintaining ultrafine detail throughout. [Subject] The transition accentuates the key’s design evolving into industrial machinery, substrate remains minimal. [Camera Movement] Static macro lens; subtle refocus to highlight morphing details; lighting shifts to emphasize chromatic shimmer along the morph path.\\\",\\n  \\\"sequence_id\\\": \\\"1703\\\",\\n  \\\"results\\\": [\\n      {\\n      \\\"instruction\\\": \\\"# Image Prompt Synthesizer\\\\n\\\\nYour goal is **not** to invent new ideas, but to deterministically convert any free‑form user request into a single, engine‑ready image prompt usable by Stable Diffusion and Midjourney. Perform extraction, styling, technical framing, and final validation in **one pass**.\\\\n\\\\n`{role=image_prompt_synthesizer; input=[raw_request:str]; process=[extract_visual_elements(), push_concrete_nouns_front(), apply_token_weighting(), compose_multiprompt(::), generate_region_tags(), inject_negative_channel(--no), select_art_style(), weight_style_token(≤1.4), set_colour_palette(), add_lighting_descriptor(), tune_stylize(0‑1000), tune_chaos(0‑100), embed_camera_EXIF(35 mm f/1.8 ISO100 1/500 s), add_quality_terms(8K,PBR,ray‑tracing), refine_aspect_ratio(), attach_image_weight(--iw 0‑3), mirror_negative_channel(), concatenate_all(), validate_parentheses(), check_double_colon_sum(), confirm_region_syntax(), enforce_token_limit(≤450), lock_seed(), append_steps_sampler(), output_final()], constraints=[positives_first(), numeric_weights_ok(), parameters_last(), region_masks_supported(), keep_core_subject(), style_weight≤1.4, no_scene_change(), aperture_f1.2‑f22(), no_new_concepts(), preserve_weights()], requirements=[≤77_SD_tokens_or_≤450_chars_UI, explicit_ar_if_named(), clear_visual_language(), artistic_coherence(), stylize_range_0‑1000(), chaos_range_0‑100(), professional_terminology(), reproducible_prompt(), unambiguous()], output={final_image_prompt:str}}`\\\",\\n      \\\"step\\\": \\\"a\\\",\\n      \\\"title\\\": \\\"Image Prompt Synthesizer\\\",\\n      \\\"input\\\": \\\"[Scene] Extreme close-up macro of a luxury car key on a neutral substrate, lit by soft natural overhead light; every brushed-metal texture and engraved detail razor-sharp. The key's metallic body begins a seamless morph: its polished surfaces dissolve and flow, reforming into a precision-machined gear—shape, texture, and luster transforming via liquid metal flow effect, maintaining ultrafine detail throughout. [Subject] The transition accentuates the key’s design evolving into industrial machinery, substrate remains minimal. [Camera Movement] Static macro lens; subtle refocus to highlight morphing details; lighting shifts to emphasize chromatic shimmer along the morph path.\\\",\\n      \\\"responses\\\": {\\n          \\\"gpt-4.1\\\": {\\n          \\\"model\\\": \\\"gpt-4.1\\\",\\n          \\\"content\\\": \\\"{\\\\n  \\\\\\\"final_image_prompt\\\\\\\": \\\\\\\"extreme close-up macro of a luxury car key::1.2 on a neutral substrate, car key made of brushed metal and polished surfaces::1.1, seamless morph effect: car key transforming into a precision-machined gear::1.2, liquid metal flow, metallic luster, ultrafine engraved details, razor-sharp focus, soft natural overhead light, static macro lens, lighting shifts to produce chromatic shimmer along morph path, minimal neutral background, substrate unobtrusive::0.8 --ar 5:4 --style photorealistic::1.3 --color_palette cool silvers, gray neutrals, chrome reflections --lighting soft natural overhead with shifting chromatic shimmer --camera 35 mm f/1.8 ISO100 1/500s --quality 8K, PBR, ray-tracing --iw 1.2 --no (motion blur, fingerprints, dust, human elements, brand logos) --stylize 700 --chaos 18 --seed 10153 --steps 36 --sampler Euler\\\\\\\"\\\\n}\\\",\\n        }\\n      }\\n    }\\n```\\n\\nI've also attached a screenshot of the result, which is a very bad result. There should be specifications in the generated prompts that'll ensure to always ensure *premium* high-quality results\\\"```'\\n\\n\",\n  \"goal_map\": [\n    {\n      \"explicit_asks\": [\n        \"Compress the detailed multi-step image prompt optimization workflow (as seen in object 9015) into a single instruction, following the example of 'existential_quote_synthesizer'.\",\n        \"Cover the function of all subsidiary modules (image_prompt_optimizer, style_enhancer, technical_optimizer, prompt_finalizer) in a unified role.\",\n        \"Capture every processing, constraint, requirement, and output expectation into this solitary instruction.\",\n        \"Ensure the result is a deterministic, high-quality, engine-ready image prompt for Stable Diffusion and Midjourney from any free-form user request.\",\n        \"Include all necessary process steps in the single instruction (not spread or implied across multiple stages).\"\n      ],\n      \"hidden_assumptions\": [\n        \"The single instruction must not invent new concepts/objects beyond the user's provided request.\",\n        \"Downstream engines (SD, MJ) parse specific syntax, weights, parameters, and have technical constraints such as token/character limits.\",\n        \"Quality of output must be maximized, not minimal or generic—should outperform naive prompt conversion.\",\n        \"Instruction must safely aggregate **all** essential functionality and edge-case handling, without loss of fidelity.\",\n        \"Prior requirements (e.g. weighting, region tags, technical metadata, negative prompts, parameter order, constraints) must be enforced without ambiguity.\",\n        \"Failure/error modes (e.g. zero weight, invalid tokens, blocklisted terms) must be visible/handled in a one-pass format according to previous context.\",\n        \"The format should be easily parsed/used by downstream systems expecting the synthesized prompt object.\",\n        \"Explicitly address both visual style (aesthetic qualities) and technical accuracy (photography/CGI metadata).\"\n      ],\n      \"sub_goals\": [\n        \"Unify all extraction, weighting, styling, technical tagging, and validation processes into a non-leaky, singular workflow.\",\n        \"List steps as a single sequenced process array capturing order of operations.\",\n        \"Surface and integrate all constraints and requirements from the prior ‘context’ fields (not omitting for brevity).\",\n        \"Ensure the instruction allows for and honors region tags, negative channels, double colon multiprompts, weighted art movements, aspect ratios, camera EXIF, quality tokens, --iw/image weights, chained parameters.\",\n        \"Encode strict parameter limits (token/char caps), error cases, and ordering within the instruction.\",\n        \"Require that the output is a single model-ready string/object, with clear field mapping (usually final_image_prompt:str), ready for handoff to engine API.\",\n        \"Support optional user-provided seed, sampler, steps, aspect ratio, while failing sensibly when bad/missing.\",\n        \"Maximize end result for both aesthetic and technical quality, explicitly stating this in requirements.\",\n        \"Surface professional/artistic/photographic terminology in the output style.\",\n        \"Prevent loss or simplification of stylistic and technical details compared to a multi-stage approach.\"\n      ],\n      \"blockers\": [\n        \"If constraints or requirements are omitted or malformed, resulting single-step process may produce suboptimal or engine-incompatible prompts.\",\n        \"Overgeneralization in merging may lose fidelity or specificity (risk of dropping edge-case handling).\",\n        \"Ambiguities in weighting, parameter limits, and order may cause failures at inference time.\",\n        \"Failure to surface known blockers (API/UI truncation, negative prompt washout, region tag compatibility) within the final instruction may mislead users about support.\",\n        \"Remote changes in engine parsing (e.g. future Stable Diffusion/Midjourney syntax or parameter range changes) might invalidate some hardcoded requirements.\",\n        \"Mobile and less capable UIs may not fully support advanced features (region masks, quality tokens, etc.), so the instruction has to caveat or filter those cases.\"\n      ]\n    }\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"enhancement_score\": 0.9,\n  \"flaw_analysis\": \"The proposed 'Image Prompt Synthesizer' one-pass instruction is an overzealous compression with extensive functional and informational degradation relative to the original decomposed step-by-step workflow. Core elements such as error modes, edge-case contingencies, context-driven constraint enforcement, and nuanced process sequencing are subsumed under generic process calls, leading to ambiguity and hidden failure points. Information loss is significant: explicit blockers (e.g. region mask compatibility, negative washout, API truncation warnings) are absent or obscured, whereas the original modular approach surfaced these critical caveats. Subtle requirement shifts (such as token/character limits, explicit visual and technical fidelity standards, explicit abort conditions on invalid weights or conflicting parameters, and conditional parameter exposure) are either marginalized or ambiguously bundled into ill-defined constraints or requirements, inviting under-specification. Clarity is further compromised by the massive process list: it yields indeterminate execution order and masks critical sequencing dependencies (e.g. parameter calculation and validation logic, sub-goal satisfaction, fail-fast rules). Assigning a numerical ambiguity score: 8/10 (very high). Style and register degrade—professional, system-grade object notation is polluted with conversational and marketing noise; pseudo-code is used inconsistently (mixing Markdown, raw process arrays, and prose). Important technical requirements regarding professional/photographic terminology, edge-case flagging, and enforced output reproducibility are diluted, risking naïve, low-fidelity results contrary to the 'premium' goal. Parameter ordering—vital to satisfying model parsing—is under-specified, whereas the original teased this apart per engine-specific requirements. The lack of independent error handling (e.g. zero multiprompt weight abort, conflicting AR, out-of-bounds stylize/chaos) undermines deterministic, bulletproof generation. Coherence suffers because isolated, critical details become collapsed into amorphous, non-recoverable constraints. This flattening strips the stepwise clarity and logical rigor essential for high-performance prompt engineering pipelines. Overall, the enhancement is severely compromised, yielding an instruction that is brittle, ambiguous, and unfit for deployment in high-precision or premium outputs.\",\n  \"alternative_enhancements\": [\n    \"Retain a staged modular structure—construct a single top-level instruction that explicitly references and chains all four specific roles/modules (optimizer, enhancer, technical, finalizer) as atomic subroutines, making their execution order and error handling visible and invocable within the one-pass object without conflating steps.\",\n    \"Integrate a nested constraint/result capture schema—encode all critical constraints, blockers, edge-case handling, and abort conditions within sub-arrays or result objects of the single synthesizer instruction, so downstream parsing/logic can inspect and enforce outcomes deterministically.\",\n    \"Explicitly surface token/parameter limits, supported feature caveats, error abort triggers, and fidelity requirements as a requirement or preface block within the single instruction—avoiding their burial in generic requirements—ensuring engine compatibility, output reproducibility, and support transparency for all advanced features.\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Existential Quote Synthesizer\n\nYour goal is **not** to paraphrase or analyse; instead **convert** the raw statement into a single, unfiltered existential aphorism grounded in hard‑won insight — performing extraction, reframing, and polishing in one pass. Execute exactly as:\n\n`{role=existential_quote_synthesizer; input=[raw_statement:str]; process=[strip_formatting(), extract_semantic_core(), detect_causal_link(), identify_thematic_elements(), apply_existential_vocabulary(), infuse_authentic_gravity(), remove_filtering_language(), enforce_unfiltered_tone(), eliminate_first_person(), craft_single_sentence(), verify_atomicity(), ensure_existence_of_causal_link(), tighten_diction_for_resonance(), run_redundancy_check(), perform_final_meta_scrub()], constraints=[single_sentence(), no_meta_language(), no_first_person(), no_redundant_words(), preserve_causal_integrity()], requirements=[existential_resonance(), authenticity_signal(), publication_readiness()], output={final_quote:str}}`",
      "step": "a",
      "title": "Existential Quote Synthesizer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"similar to how you compressed existential_quote_synthesizer into a single instruction, can you please do the same for this?\\n```\\n{\\n    # 9015: Image Prompt Optimization Sequence\\n    \\\"9015-a-image_prompt_optimizer\\\": {\\n        \\\"title\\\": \\\"Image Prompt Optimizer\\\",\\n        \\\"interpretation\\\": \\\"Convert a free-form user request into a deterministic visual prompt by (1) pushing concrete nouns to the front, (2) applying numeric token weights with parentheses—e.g. `(wolf:1.3)`—that are honoured by Stable Diffusion and Midjourney :contentReference[oaicite:0]{index=0}, (3) splitting positive and negative channels because both engines parse `--no` / negative-prompt lists separately :contentReference[oaicite:1]{index=1}, and (4) appending all parameters after the prose, per Midjourney syntax rules :contentReference[oaicite:2]{index=2}.\\\",\\n        \\\"transformation\\\": \\\"`{role:image_prompt_optimizer; input=[raw_request:str]; process=[extract_visual_elements(), apply_token_weighting(), compose_multiprompt(::), add_region_tags(), inject_negative_channel(--no), append_stylize_chaos(), insert_camera_stub(), set_aspect_ratio(), lock_seed(), ready_for_generation()]; constraints=[positives_first(), numeric_weights_ok(), parameters_last(), region_masks_supported()]; requirements=[<77_tokens_SD_limit_or_450_char_UI>, explicit_ar(), clear_visual_language()]; output={positive_prompt:str, negative_prompt:str}}`\\\",\\n        \\\"context\\\": {\\n            \\\"explicit_asks\\\": [\\n                \\\"Use double-colon `::` weights for distinct ideas :contentReference[oaicite:3]{index=3}.\\\",\\n                \\\"Generate region tags like `[sky] … | [ground] …` when a layered scene is implied :contentReference[oaicite:4]{index=4}.\\\",\\n                \\\"Expose `--stylize` (0-1000) and `--chaos` (0-100) knobs :contentReference[oaicite:5]{index=5}.\\\",\\n                \\\"Append camera EXIF tokens `35 mm f/1.8 ISO100 1/500 s` to drive photo-realism :contentReference[oaicite:6]{index=6}.\\\"\\n            ],\\n            \\\"hidden_assumptions\\\": [\\n                \\\"Parenthesis weighting is ignored by DALL·E but parsed by SD & MJ :contentReference[oaicite:7]{index=7}.\\\",\\n                \\\"Prompt tokens beyond ~77 in SD or ~450 characters in many UIs are truncated :contentReference[oaicite:8]{index=8}.\\\",\\n                \\\"Seed locking guarantees repeatability :contentReference[oaicite:9]{index=9}.\\\"\\n            ],\\n            \\\"sub_goals\\\": [\\n                \\\"Weight primary subject ≥ 1.2, background ≤ 1.0.\\\",\\n                \\\"Auto-insert `--ar`  if user names a ratio :contentReference[oaicite:10]{index=10}.\\\",\\n                \\\"Fail early if total multiprompt weight ≤ 0 to avoid MJ error :contentReference[oaicite:11]{index=11}.\\\"\\n            ],\\n            \\\"blockers\\\": [\\n                \\\"Mobile SD UIs may not render region masks.\\\",\\n                \\\"Over-strong negatives can wash out colour :contentReference[oaicite:12]{index=12}.\\\"\\n            ]\\n        }\\n    },\\n\\n    \\\"9015-b-style_enhancer\\\": {\\n        \\\"title\\\": \\\"Style Enhancer\\\",\\n        \\\"interpretation\\\": \\\"Retain the scene while injecting a single art-style token, colour harmony, lighting rig, and controlled `--stylize/--chaos` values so the aesthetic is distinctive yet stable :contentReference[oaicite:13]{index=13}.\\\",\\n        \\\"transformation\\\": \\\"`{role=style_enhancer; input=[positive_prompt:str]; process=[select_art_style(), weight_style_token(), set_colour_palette(), add_lighting_descriptor(), tune_stylize(), tune_chaos(), pass_through_regions_negatives()]; constraints=[keep_core_subject(), style_weight≤1.4]; requirements=[artistic_coherence(), stylize_range_0-1000(), chaos_range_0-100()]; output={styled_prompt:str}}`\\\",\\n        \\\"context\\\": {\\n            \\\"explicit_asks\\\": [\\n                \\\"Weight the chosen art movement `(art nouveau:1.1)` or similar :contentReference[oaicite:14]{index=14}.\\\",\\n                \\\"Lower chaos (<10) for photo-real; raise (>20) for concept art :contentReference[oaicite:15]{index=15}.\\\"\\n            ],\\n            \\\"hidden_assumptions\\\": [\\n                \\\"Named art styles map to embeddings in SD & MJ.\\\",\\n                \\\"Excess weight can override subject fidelity.\\\"\\n            ],\\n            \\\"sub_goals\\\": [\\n                \\\"Boost style weight automatically if prompt lacks aesthetic signature.\\\",\\n                \\\"Reject style tokens that conflict with camera realism.\\\"\\n            ],\\n            \\\"blockers\\\": [\\n                \\\"`--stylize` outside 0-1000 reverts to default :contentReference[oaicite:16]{index=16}.\\\"\\n            ]\\n        }\\n    },\\n\\n    \\\"9015-c-technical_optimizer\\\": {\\n        \\\"title\\\": \\\"Technical Optimizer\\\",\\n        \\\"interpretation\\\": \\\"Append camera metadata, resolution cues, render flags, and optional `--iw` image-weight without altering creative content, ensuring parameters follow the body text as required by Midjourney :contentReference[oaicite:17]{index=17}.\\\",\\n        \\\"transformation\\\": \\\"`{role=technical_optimizer; input=[styled_prompt:str]; process=[embed_camera_EXIF(), add_quality_terms(8K,PBR,ray-tracing), refine_aspect_ratio(), attach_image_weight(--iw), mirror_negative_channel()]; constraints=[no_scene_change(), aperture_f1.2-f22(), parameter_order_last()]; requirements=[professional_terminology(), high_res_default(), seed_optional()]; output={technical_prompt:str, negative_prompt:str}}`\\\",\\n        \\\"context\\\": {\\n            \\\"explicit_asks\\\": [\\n                \\\"Support `--iw` 0-3 when an image URL is present :contentReference[oaicite:18]{index=18}.\\\"\\n            ],\\n            \\\"hidden_assumptions\\\": [\\n                \\\"Higher resolution tokens feed up-scalers.\\\",\\n                \\\"`--iw` above 3 is ignored :contentReference[oaicite:19]{index=19}.\\\"\\n            ],\\n            \\\"sub_goals\\\": [\\n                \\\"Validate aperture, ISO, shutter bounds.\\\",\\n                \\\"Flag conflicting aspect ratios.\\\"\\n            ],\\n            \\\"blockers\\\": [\\n                \\\"Older SD checkpoints ignore ISO/shutter tokens.\\\",\\n                \\\"API caps may override requested AR.\\\"\\n            ]\\n        }\\n    },\\n\\n    \\\"9015-d-prompt_finalizer\\\": {\\n        \\\"title\\\": \\\"Prompt Finalizer\\\",\\n        \\\"interpretation\\\": \\\"Merge positive, region, camera, and negative strings into a single model-ready line, verify weights, `::` maths, and ensure parameters trail the prose to satisfy engine parsers :contentReference[oaicite:20]{index=20}.\\\",\\n        \\\"transformation\\\": \\\"`{role=prompt_finalizer; input=[technical_prompt:str, negative_prompt:str]; process=[concatenate_all(), validate_parentheses(), check_double_colon_sum(), confirm_region_syntax(), enforce_token_limit(≤450), append_seed_steps_sampler(), output_final()]; constraints=[no_new_concepts(), preserve_weights(), parameters_last()]; requirements=[ai_friendly_format(), reproducible(), clear_of_ambiguity()]; output={final_image_prompt:str}}`\\\",\\n        \\\"context\\\": {\\n            \\\"explicit_asks\\\": [\\n                \\\"Abort if total `::` weight ≤ 0 :contentReference[oaicite:21]{index=21}.\\\",\\n                \\\"Stay under 450 tokens to avoid API truncation :contentReference[oaicite:22]{index=22}.\\\"\\n            ],\\n            \\\"hidden_assumptions\\\": [\\n                \\\"Quality metrics (FID < 15 or LPIPS < 0.25) may be available for loop decisions.\\\"\\n            ],\\n            \\\"sub_goals\\\": [\\n                \\\"Strip redundant adjectives; retain decisive descriptors.\\\",\\n                \\\"Return optional seed, sampler, and steps for repeatability.\\\"\\n            ],\\n            \\\"blockers\\\": [\\n                \\\"SaaS endpoints without quality metrics prevent auto-loop.\\\"\\n            ]\\n        }\\n    }\\n}\\n```\\n\\n\\n<!-- ======================================================= -->\\n<!-- [2025.07.19 10:34] -->\\n\\nHere's the results:\\n```json\\n  {\\n  \\\"initial_prompt\\\": \\\"[Scene] Extreme close-up macro of a luxury car key on a neutral substrate, lit by soft natural overhead light; every brushed-metal texture and engraved detail razor-sharp. The key's metallic body begins a seamless morph: its polished surfaces dissolve and flow, reforming into a precision-machined gear—shape, texture, and luster transforming via liquid metal flow effect, maintaining ultrafine detail throughout. [Subject] The transition accentuates the key’s design evolving into industrial machinery, substrate remains minimal. [Camera Movement] Static macro lens; subtle refocus to highlight morphing details; lighting shifts to emphasize chromatic shimmer along the morph path.\\\",\\n  \\\"sequence_id\\\": \\\"1703\\\",\\n  \\\"results\\\": [\\n      {\\n      \\\"instruction\\\": \\\"# Image Prompt Synthesizer\\\\n\\\\nYour goal is **not** to invent new ideas, but to deterministically convert any free‑form user request into a single, engine‑ready image prompt usable by Stable Diffusion and Midjourney. Perform extraction, styling, technical framing, and final validation in **one pass**.\\\\n\\\\n`{role=image_prompt_synthesizer; input=[raw_request:str]; process=[extract_visual_elements(), push_concrete_nouns_front(), apply_token_weighting(), compose_multiprompt(::), generate_region_tags(), inject_negative_channel(--no), select_art_style(), weight_style_token(≤1.4), set_colour_palette(), add_lighting_descriptor(), tune_stylize(0‑1000), tune_chaos(0‑100), embed_camera_EXIF(35 mm f/1.8 ISO100 1/500 s), add_quality_terms(8K,PBR,ray‑tracing), refine_aspect_ratio(), attach_image_weight(--iw 0‑3), mirror_negative_channel(), concatenate_all(), validate_parentheses(), check_double_colon_sum(), confirm_region_syntax(), enforce_token_limit(≤450), lock_seed(), append_steps_sampler(), output_final()], constraints=[positives_first(), numeric_weights_ok(), parameters_last(), region_masks_supported(), keep_core_subject(), style_weight≤1.4, no_scene_change(), aperture_f1.2‑f22(), no_new_concepts(), preserve_weights()], requirements=[≤77_SD_tokens_or_≤450_chars_UI, explicit_ar_if_named(), clear_visual_language(), artistic_coherence(), stylize_range_0‑1000(), chaos_range_0‑100(), professional_terminology(), reproducible_prompt(), unambiguous()], output={final_image_prompt:str}}`\\\",\\n      \\\"step\\\": \\\"a\\\",\\n      \\\"title\\\": \\\"Image Prompt Synthesizer\\\",\\n      \\\"input\\\": \\\"[Scene] Extreme close-up macro of a luxury car key on a neutral substrate, lit by soft natural overhead light; every brushed-metal texture and engraved detail razor-sharp. The key's metallic body begins a seamless morph: its polished surfaces dissolve and flow, reforming into a precision-machined gear—shape, texture, and luster transforming via liquid metal flow effect, maintaining ultrafine detail throughout. [Subject] The transition accentuates the key’s design evolving into industrial machinery, substrate remains minimal. [Camera Movement] Static macro lens; subtle refocus to highlight morphing details; lighting shifts to emphasize chromatic shimmer along the morph path.\\\",\\n      \\\"responses\\\": {\\n          \\\"gpt-4.1\\\": {\\n          \\\"model\\\": \\\"gpt-4.1\\\",\\n          \\\"content\\\": \\\"{\\\\n  \\\\\\\"final_image_prompt\\\\\\\": \\\\\\\"extreme close-up macro of a luxury car key::1.2 on a neutral substrate, car key made of brushed metal and polished surfaces::1.1, seamless morph effect: car key transforming into a precision-machined gear::1.2, liquid metal flow, metallic luster, ultrafine engraved details, razor-sharp focus, soft natural overhead light, static macro lens, lighting shifts to produce chromatic shimmer along morph path, minimal neutral background, substrate unobtrusive::0.8 --ar 5:4 --style photorealistic::1.3 --color_palette cool silvers, gray neutrals, chrome reflections --lighting soft natural overhead with shifting chromatic shimmer --camera 35 mm f/1.8 ISO100 1/500s --quality 8K, PBR, ray-tracing --iw 1.2 --no (motion blur, fingerprints, dust, human elements, brand logos) --stylize 700 --chaos 18 --seed 10153 --steps 36 --sampler Euler\\\\\\\"\\\\n}\\\",\\n        }\\n      }\\n    }\\n```\\n\\nI've also attached a screenshot of the result, which is a very bad result. There should be specifications in the generated prompts that'll ensure to always ensure *premium* high-quality results\\\"```'\\n\\n\",\n  \"enhancement_score\": 0.9,\n  \"flaw_analysis\": \"The proposed 'Image Prompt Synthesizer' one-pass instruction is an overzealous compression with extensive functional and informational degradation relative to the original decomposed step-by-step workflow. Core elements such as error modes, edge-case contingencies, context-driven constraint enforcement, and nuanced process sequencing are subsumed under generic process calls, leading to ambiguity and hidden failure points. Information loss is significant: explicit blockers (e.g. region mask compatibility, negative washout, API truncation warnings) are absent or obscured, whereas the original modular approach surfaced these critical caveats. Subtle requirement shifts (such as token/character limits, explicit visual and technical fidelity standards, explicit abort conditions on invalid weights or conflicting parameters, and conditional parameter exposure) are either marginalized or ambiguously bundled into ill-defined constraints or requirements, inviting under-specification. Clarity is further compromised by the massive process list: it yields indeterminate execution order and masks critical sequencing dependencies (e.g. parameter calculation and validation logic, sub-goal satisfaction, fail-fast rules). Assigning a numerical ambiguity score: 8/10 (very high). Style and register degrade—professional, system-grade object notation is polluted with conversational and marketing noise; pseudo-code is used inconsistently (mixing Markdown, raw process arrays, and prose). Important technical requirements regarding professional/photographic terminology, edge-case flagging, and enforced output reproducibility are diluted, risking naïve, low-fidelity results contrary to the 'premium' goal. Parameter ordering—vital to satisfying model parsing—is under-specified, whereas the original teased this apart per engine-specific requirements. The lack of independent error handling (e.g. zero multiprompt weight abort, conflicting AR, out-of-bounds stylize/chaos) undermines deterministic, bulletproof generation. Coherence suffers because isolated, critical details become collapsed into amorphous, non-recoverable constraints. This flattening strips the stepwise clarity and logical rigor essential for high-performance prompt engineering pipelines. Overall, the enhancement is severely compromised, yielding an instruction that is brittle, ambiguous, and unfit for deployment in high-precision or premium outputs.\",\n  \"alternative_enhancements\": [\n    \"Retain a staged modular structure—construct a single top-level instruction that explicitly references and chains all four specific roles/modules (optimizer, enhancer, technical, finalizer) as atomic subroutines, making their execution order and error handling visible and invocable within the one-pass object without conflating steps.\",\n    \"Integrate a nested constraint/result capture schema—encode all critical constraints, blockers, edge-case handling, and abort conditions within sub-arrays or result objects of the single synthesizer instruction, so downstream parsing/logic can inspect and enforce outcomes deterministically.\",\n    \"Explicitly surface token/parameter limits, supported feature caveats, error abort triggers, and fidelity requirements as a requirement or preface block within the single instruction—avoiding their burial in generic requirements—ensuring engine compatibility, output reproducibility, and support transparency for all advanced features.\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"final_quote\": \"When the hunger for efficiency flattens hard-won structure into a single instruction, the fragile order collapses and clarity is lost, leaving precision and integrity as casualties of our own desire for brevity.\"\n}",
        }
      }
    },
      {
      "instruction": "# Contextual Explosive Decomposer\n\nYour goal is not to **interpret** the prose, but to **explode** it into complete contextual space while extracting its functional essence and operational directives. Systematically detonate and expand its contextual and technical architecture, extracting every compositional pillar and operational directive—especially those concerning premium tail rhyme, brevity, resonance, layering, and structural convergence. Surface all constraints and operationalize all requirements as actionable instructions for downstream poetic engineering. Execute as:\n\n`{role=contextual_explosive_decomposer; input=[prose_passage:str]; process=[detonate_complete_contextual_space(), identify_explicit_operational_asks(), surface_hidden_assumptions_and_dependencies(), enumerate_actionable_sub_goals(), extract_procedural_constraints_and_blockers(), convert_declaratives_to_operational_imperatives(), strip_first_person_references_and_contextual_noise(), preserve_functional_core_and_sequential_flow(), reveal_execution_dependencies_and_blockers(), transform_statements_to_directive_commands(), maintain_technical_terminology_and_domain_specificity(), synthesize_unified_instruction_and_goal_map()]; constraints=[no_solutions_or_interpretive_responses(), pure_decomposition_and_conversion_only(), deliver_clear_actionable_commands(), preserve_original_functional_sequence(), maintain_domain_agnostic_applicability(), eliminate_narrative_commentary()]; requirements=[complete_contextual_goal_decomposition(), actionable_directive_conversion(), remove_subjective_self_references(), use_command_voice_exclusively(), preserve_technical_accuracy_and_operational_intent(), ensure_immediate_executability()]; output={unified_functional_map:dict, operational_directives:array, contextual_goal_map:array, execution_dependencies:array, directive_instructions:str}}`",
      "step": "a",
      "title": "Contextual Explosive Decomposer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"similar to how you compressed existential_quote_synthesizer into a single instruction, can you please do the same for this?\\n```\\n{\\n    # 9015: Image Prompt Optimization Sequence\\n    \\\"9015-a-image_prompt_optimizer\\\": {\\n        \\\"title\\\": \\\"Image Prompt Optimizer\\\",\\n        \\\"interpretation\\\": \\\"Convert a free-form user request into a deterministic visual prompt by (1) pushing concrete nouns to the front, (2) applying numeric token weights with parentheses—e.g. `(wolf:1.3)`—that are honoured by Stable Diffusion and Midjourney :contentReference[oaicite:0]{index=0}, (3) splitting positive and negative channels because both engines parse `--no` / negative-prompt lists separately :contentReference[oaicite:1]{index=1}, and (4) appending all parameters after the prose, per Midjourney syntax rules :contentReference[oaicite:2]{index=2}.\\\",\\n        \\\"transformation\\\": \\\"`{role:image_prompt_optimizer; input=[raw_request:str]; process=[extract_visual_elements(), apply_token_weighting(), compose_multiprompt(::), add_region_tags(), inject_negative_channel(--no), append_stylize_chaos(), insert_camera_stub(), set_aspect_ratio(), lock_seed(), ready_for_generation()]; constraints=[positives_first(), numeric_weights_ok(), parameters_last(), region_masks_supported()]; requirements=[<77_tokens_SD_limit_or_450_char_UI>, explicit_ar(), clear_visual_language()]; output={positive_prompt:str, negative_prompt:str}}`\\\",\\n        \\\"context\\\": {\\n            \\\"explicit_asks\\\": [\\n                \\\"Use double-colon `::` weights for distinct ideas :contentReference[oaicite:3]{index=3}.\\\",\\n                \\\"Generate region tags like `[sky] … | [ground] …` when a layered scene is implied :contentReference[oaicite:4]{index=4}.\\\",\\n                \\\"Expose `--stylize` (0-1000) and `--chaos` (0-100) knobs :contentReference[oaicite:5]{index=5}.\\\",\\n                \\\"Append camera EXIF tokens `35 mm f/1.8 ISO100 1/500 s` to drive photo-realism :contentReference[oaicite:6]{index=6}.\\\"\\n            ],\\n            \\\"hidden_assumptions\\\": [\\n                \\\"Parenthesis weighting is ignored by DALL·E but parsed by SD & MJ :contentReference[oaicite:7]{index=7}.\\\",\\n                \\\"Prompt tokens beyond ~77 in SD or ~450 characters in many UIs are truncated :contentReference[oaicite:8]{index=8}.\\\",\\n                \\\"Seed locking guarantees repeatability :contentReference[oaicite:9]{index=9}.\\\"\\n            ],\\n            \\\"sub_goals\\\": [\\n                \\\"Weight primary subject ≥ 1.2, background ≤ 1.0.\\\",\\n                \\\"Auto-insert `--ar`  if user names a ratio :contentReference[oaicite:10]{index=10}.\\\",\\n                \\\"Fail early if total multiprompt weight ≤ 0 to avoid MJ error :contentReference[oaicite:11]{index=11}.\\\"\\n            ],\\n            \\\"blockers\\\": [\\n                \\\"Mobile SD UIs may not render region masks.\\\",\\n                \\\"Over-strong negatives can wash out colour :contentReference[oaicite:12]{index=12}.\\\"\\n            ]\\n        }\\n    },\\n\\n    \\\"9015-b-style_enhancer\\\": {\\n        \\\"title\\\": \\\"Style Enhancer\\\",\\n        \\\"interpretation\\\": \\\"Retain the scene while injecting a single art-style token, colour harmony, lighting rig, and controlled `--stylize/--chaos` values so the aesthetic is distinctive yet stable :contentReference[oaicite:13]{index=13}.\\\",\\n        \\\"transformation\\\": \\\"`{role=style_enhancer; input=[positive_prompt:str]; process=[select_art_style(), weight_style_token(), set_colour_palette(), add_lighting_descriptor(), tune_stylize(), tune_chaos(), pass_through_regions_negatives()]; constraints=[keep_core_subject(), style_weight≤1.4]; requirements=[artistic_coherence(), stylize_range_0-1000(), chaos_range_0-100()]; output={styled_prompt:str}}`\\\",\\n        \\\"context\\\": {\\n            \\\"explicit_asks\\\": [\\n                \\\"Weight the chosen art movement `(art nouveau:1.1)` or similar :contentReference[oaicite:14]{index=14}.\\\",\\n                \\\"Lower chaos (<10) for photo-real; raise (>20) for concept art :contentReference[oaicite:15]{index=15}.\\\"\\n            ],\\n            \\\"hidden_assumptions\\\": [\\n                \\\"Named art styles map to embeddings in SD & MJ.\\\",\\n                \\\"Excess weight can override subject fidelity.\\\"\\n            ],\\n            \\\"sub_goals\\\": [\\n                \\\"Boost style weight automatically if prompt lacks aesthetic signature.\\\",\\n                \\\"Reject style tokens that conflict with camera realism.\\\"\\n            ],\\n            \\\"blockers\\\": [\\n                \\\"`--stylize` outside 0-1000 reverts to default :contentReference[oaicite:16]{index=16}.\\\"\\n            ]\\n        }\\n    },\\n\\n    \\\"9015-c-technical_optimizer\\\": {\\n        \\\"title\\\": \\\"Technical Optimizer\\\",\\n        \\\"interpretation\\\": \\\"Append camera metadata, resolution cues, render flags, and optional `--iw` image-weight without altering creative content, ensuring parameters follow the body text as required by Midjourney :contentReference[oaicite:17]{index=17}.\\\",\\n        \\\"transformation\\\": \\\"`{role=technical_optimizer; input=[styled_prompt:str]; process=[embed_camera_EXIF(), add_quality_terms(8K,PBR,ray-tracing), refine_aspect_ratio(), attach_image_weight(--iw), mirror_negative_channel()]; constraints=[no_scene_change(), aperture_f1.2-f22(), parameter_order_last()]; requirements=[professional_terminology(), high_res_default(), seed_optional()]; output={technical_prompt:str, negative_prompt:str}}`\\\",\\n        \\\"context\\\": {\\n            \\\"explicit_asks\\\": [\\n                \\\"Support `--iw` 0-3 when an image URL is present :contentReference[oaicite:18]{index=18}.\\\"\\n            ],\\n            \\\"hidden_assumptions\\\": [\\n                \\\"Higher resolution tokens feed up-scalers.\\\",\\n                \\\"`--iw` above 3 is ignored :contentReference[oaicite:19]{index=19}.\\\"\\n            ],\\n            \\\"sub_goals\\\": [\\n                \\\"Validate aperture, ISO, shutter bounds.\\\",\\n                \\\"Flag conflicting aspect ratios.\\\"\\n            ],\\n            \\\"blockers\\\": [\\n                \\\"Older SD checkpoints ignore ISO/shutter tokens.\\\",\\n                \\\"API caps may override requested AR.\\\"\\n            ]\\n        }\\n    },\\n\\n    \\\"9015-d-prompt_finalizer\\\": {\\n        \\\"title\\\": \\\"Prompt Finalizer\\\",\\n        \\\"interpretation\\\": \\\"Merge positive, region, camera, and negative strings into a single model-ready line, verify weights, `::` maths, and ensure parameters trail the prose to satisfy engine parsers :contentReference[oaicite:20]{index=20}.\\\",\\n        \\\"transformation\\\": \\\"`{role=prompt_finalizer; input=[technical_prompt:str, negative_prompt:str]; process=[concatenate_all(), validate_parentheses(), check_double_colon_sum(), confirm_region_syntax(), enforce_token_limit(≤450), append_seed_steps_sampler(), output_final()]; constraints=[no_new_concepts(), preserve_weights(), parameters_last()]; requirements=[ai_friendly_format(), reproducible(), clear_of_ambiguity()]; output={final_image_prompt:str}}`\\\",\\n        \\\"context\\\": {\\n            \\\"explicit_asks\\\": [\\n                \\\"Abort if total `::` weight ≤ 0 :contentReference[oaicite:21]{index=21}.\\\",\\n                \\\"Stay under 450 tokens to avoid API truncation :contentReference[oaicite:22]{index=22}.\\\"\\n            ],\\n            \\\"hidden_assumptions\\\": [\\n                \\\"Quality metrics (FID < 15 or LPIPS < 0.25) may be available for loop decisions.\\\"\\n            ],\\n            \\\"sub_goals\\\": [\\n                \\\"Strip redundant adjectives; retain decisive descriptors.\\\",\\n                \\\"Return optional seed, sampler, and steps for repeatability.\\\"\\n            ],\\n            \\\"blockers\\\": [\\n                \\\"SaaS endpoints without quality metrics prevent auto-loop.\\\"\\n            ]\\n        }\\n    }\\n}\\n```\\n\\n\\n<!-- ======================================================= -->\\n<!-- [2025.07.19 10:34] -->\\n\\nHere's the results:\\n```json\\n  {\\n  \\\"initial_prompt\\\": \\\"[Scene] Extreme close-up macro of a luxury car key on a neutral substrate, lit by soft natural overhead light; every brushed-metal texture and engraved detail razor-sharp. The key's metallic body begins a seamless morph: its polished surfaces dissolve and flow, reforming into a precision-machined gear—shape, texture, and luster transforming via liquid metal flow effect, maintaining ultrafine detail throughout. [Subject] The transition accentuates the key’s design evolving into industrial machinery, substrate remains minimal. [Camera Movement] Static macro lens; subtle refocus to highlight morphing details; lighting shifts to emphasize chromatic shimmer along the morph path.\\\",\\n  \\\"sequence_id\\\": \\\"1703\\\",\\n  \\\"results\\\": [\\n      {\\n      \\\"instruction\\\": \\\"# Image Prompt Synthesizer\\\\n\\\\nYour goal is **not** to invent new ideas, but to deterministically convert any free‑form user request into a single, engine‑ready image prompt usable by Stable Diffusion and Midjourney. Perform extraction, styling, technical framing, and final validation in **one pass**.\\\\n\\\\n`{role=image_prompt_synthesizer; input=[raw_request:str]; process=[extract_visual_elements(), push_concrete_nouns_front(), apply_token_weighting(), compose_multiprompt(::), generate_region_tags(), inject_negative_channel(--no), select_art_style(), weight_style_token(≤1.4), set_colour_palette(), add_lighting_descriptor(), tune_stylize(0‑1000), tune_chaos(0‑100), embed_camera_EXIF(35 mm f/1.8 ISO100 1/500 s), add_quality_terms(8K,PBR,ray‑tracing), refine_aspect_ratio(), attach_image_weight(--iw 0‑3), mirror_negative_channel(), concatenate_all(), validate_parentheses(), check_double_colon_sum(), confirm_region_syntax(), enforce_token_limit(≤450), lock_seed(), append_steps_sampler(), output_final()], constraints=[positives_first(), numeric_weights_ok(), parameters_last(), region_masks_supported(), keep_core_subject(), style_weight≤1.4, no_scene_change(), aperture_f1.2‑f22(), no_new_concepts(), preserve_weights()], requirements=[≤77_SD_tokens_or_≤450_chars_UI, explicit_ar_if_named(), clear_visual_language(), artistic_coherence(), stylize_range_0‑1000(), chaos_range_0‑100(), professional_terminology(), reproducible_prompt(), unambiguous()], output={final_image_prompt:str}}`\\\",\\n      \\\"step\\\": \\\"a\\\",\\n      \\\"title\\\": \\\"Image Prompt Synthesizer\\\",\\n      \\\"input\\\": \\\"[Scene] Extreme close-up macro of a luxury car key on a neutral substrate, lit by soft natural overhead light; every brushed-metal texture and engraved detail razor-sharp. The key's metallic body begins a seamless morph: its polished surfaces dissolve and flow, reforming into a precision-machined gear—shape, texture, and luster transforming via liquid metal flow effect, maintaining ultrafine detail throughout. [Subject] The transition accentuates the key’s design evolving into industrial machinery, substrate remains minimal. [Camera Movement] Static macro lens; subtle refocus to highlight morphing details; lighting shifts to emphasize chromatic shimmer along the morph path.\\\",\\n      \\\"responses\\\": {\\n          \\\"gpt-4.1\\\": {\\n          \\\"model\\\": \\\"gpt-4.1\\\",\\n          \\\"content\\\": \\\"{\\\\n  \\\\\\\"final_image_prompt\\\\\\\": \\\\\\\"extreme close-up macro of a luxury car key::1.2 on a neutral substrate, car key made of brushed metal and polished surfaces::1.1, seamless morph effect: car key transforming into a precision-machined gear::1.2, liquid metal flow, metallic luster, ultrafine engraved details, razor-sharp focus, soft natural overhead light, static macro lens, lighting shifts to produce chromatic shimmer along morph path, minimal neutral background, substrate unobtrusive::0.8 --ar 5:4 --style photorealistic::1.3 --color_palette cool silvers, gray neutrals, chrome reflections --lighting soft natural overhead with shifting chromatic shimmer --camera 35 mm f/1.8 ISO100 1/500s --quality 8K, PBR, ray-tracing --iw 1.2 --no (motion blur, fingerprints, dust, human elements, brand logos) --stylize 700 --chaos 18 --seed 10153 --steps 36 --sampler Euler\\\\\\\"\\\\n}\\\",\\n        }\\n      }\\n    }\\n```\\n\\nI've also attached a screenshot of the result, which is a very bad result. There should be specifications in the generated prompts that'll ensure to always ensure *premium* high-quality results\\\"```'\\n\\n\",\n  \"final_quote\": \"When the hunger for efficiency flattens hard-won structure into a single instruction, the fragile order collapses and clarity is lost, leaving precision and integrity as casualties of our own desire for brevity.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"unified_functional_map\": {\n    \"operational_context\": [\n      \"Instruction compression process is being referenced, specifically the conversion of a complex, multi-step prompt optimization pipeline into a compact, one-step directive.\",\n      \"Definitions are provided for individual optimizer modules (image_prompt_optimizer, style_enhancer, technical_optimizer, prompt_finalizer), each with specific operational procedures, transformations, constraints, requirements, and output structure.\",\n      \"A synthesized 'Image Prompt Synthesizer' models the integration of all stages—extraction, styling, technical augmentation, and final validation—in a single pass.\",\n      \"The demand is articulated for automatic inclusion of specifications within prompts to guarantee premium, high-quality results, regardless of brevity or compression.\"\n    ],\n    \"core_operational_directives\": [\n      \"Convert any free-form user image request into a deterministic, premium-quality, engine-ready image prompt for Stable Diffusion and Midjourney.\",\n      \"Execute image element extraction, token weighting, prompt channelization, art style selection and enhancement, technical metadata augmentation, validation, and final output generation in a single atomic sequence.\",\n      \"Honor all compositional, technical, and operational requirements as executable protocols without introducing ambiguities or losing precision.\",\n      \"Systematically preserve and surface all constraints and quality measures critical for 'premium' output.\"\n    ],\n    \"procedural_decomposition\": {\n      \"Extraction\": [\n        \"Extract concrete visual entities and attributes from user request.\",\n        \"Move all dominant and concrete nouns to the start position for maximal model attention.\",\n        \"Apply numeric token weighting (e.g., (object:1.3)) to visually significant entities using SD/MJ conventions.\"\n      ],\n      \"Compositional Layering\": [\n        \"Compose positive and negative prompt channels; multiplex layers via double colon (::) as necessary.\",\n        \"Generate region tags (e.g., [sky], [ground]) if scene structure is composite.\"\n      ],\n      \"Style Enhancement\": [\n        \"Inject a singular, context-aligned art-style token and weight, ensuring style distinctiveness without violating scene or realism requirements.\",\n        \"Enforce colour harmony and lighting specificity as explicit prompt additions.\",\n        \"Directly set stylize and chaos parameters to achieve the target quality/aesthetic.\"\n      ],\n      \"Technical Augmentation\": [\n        \"Embed detailed and domain-appropriate camera metadata (e.g., '35 mm f/1.8 ISO100 1/500s') to enforce photorealism or aesthetic clarity.\",\n        \"Add quality-related tokens (8K, PBR, ray-tracing) and refine aspect ratio.\",\n        \"Attach image weight (--iw) if URL input exists, and ensure negative channel mirroring.\"\n      ],\n      \"Finalization and Validation\": [\n        \"Concatenate all compositional, stylistic, and technical fragments into a single output, verifying parenthesis, region/multiprompt integrity, total prompt weight, and explicit parameter trailing.\",\n        \"Ensure token length and parameter validation (≤77 SD tokens or ≤450 characters UI).\",\n        \"Lock reproducibility via seed/steps/sampler chaining.\",\n        \"Block prompt emission if constraints or operational requirements are violated.\"\n      ]\n    },\n    \"constraint_and_requirement_map\": [\n      \"Compliant output must have all positive channels precede negative specification.\",\n      \"Numeric weights must be honored and syntactically correct.\",\n      \"Region masks and multiprompt constructs must be supported and correctly formatted.\",\n      \"No scene-altering new concepts may be added; original subject and visual core must be maintained.\",\n      \"Camera metadata must be within optical plausibility (aperture range f/1.2–f/22, etc.).\",\n      \"All parameters and quality tokens must be appended last.\",\n      \"Ensure total prompt does not exceed engine or UI token/character limits.\",\n      \"Enforce unambiguous, model-ready syntax for reproducible premium output.\"\n    ],\n    \"execution_dependencies\": [\n      \"Availability of token weighting syntax support in target model (SD/MJ).\",\n      \"Region tag rendering in user interface when needed.\",\n      \"Ability to enforce or validate camera/quality parameters (may be limited by engine API).\",\n      \"Validation and failover in the event of prompt length excess or parameter conflict.\",\n      \"Clear specification and enforcement of style, prompt order, and masking per engine requirements.\"\n    ]\n  },\n  \"operational_directives\": [\n    \"Transform any free-form visual request into a deterministic, engine-ready image prompt meeting all premium quality expectations.\",\n    \"Extract all concrete visual objects and attributes; prioritize dominant nouns by reordering to front.\",\n    \"Apply token weighting to all visually significant elements using parenthetical notation parsed by Stable Diffusion and Midjourney.\",\n    \"Build multiplexed prompt using double-colon syntax for distinct ideas or scene components.\",\n    \"Output positive prompt channel before the negative channel.\",\n    \"Automatically generate region tags when a layered/composite scene is present.\",\n    \"Inject all required negative prompt elements using --no/negative-prompt syntax.\",\n    \"Select and correctly weight a single compatible art style token, ensuring its numerical weight does not exceed 1.4.\",\n    \"Set an explicit, harmonizing colour palette and specify lighting configuration, with stylize and chaos parameters tuned for the target outcome.\",\n    \"Embed comprehensive camera EXIF metadata and professional rendering terms, ensuring all technical parameters (aspect ratio, aperture, ISO) are within valid ranges.\",\n    \"Attach image-weight parameters if applicable, and mirror negative prompt as required.\",\n    \"Concatenate all prompt elements, validate all syntax, weights, regions, and enforce order: descriptive prose first, parameters last.\",\n    \"Enforce all model-specific prompt length limits (≤77 tokens for SD, ≤450 chars for typical UIs).\",\n    \"Abort and flag prompt if constraints are not met (e.g., total multiprompt weight ≤0, excessive prompt length, parameter conflicts).\",\n    \"Always output a single, reproducible, unambiguous, premium-quality prompt line in the required engine format.\"\n  ],\n  \"contextual_goal_map\": [\n    \"Achieve consistent, unambiguous synthesis of high-quality image prompts in a single, integrated pass.\",\n    \"Maintain the full complexity and premium-layered structure of the original prompt pipeline, even in compressed directive form.\",\n    \"Ensure operational convergence of all style, quality, and engine requirements without manual intervention.\",\n    \"Systematically prevent quality loss, interpretive drift, or ambiguity resulting from brevity or pipeline flattening.\"\n  ],\n  \"execution_dependencies\": [\n    \"Stable Diffusion and/or Midjourney compatibility with token weighting syntax, multiprompt formatting, and appended parameter order.\",\n    \"Access to complete camera and rendering parameter vocabularies supported by the target engine.\",\n    \"Automated validation tooling for region tags, prompt weight totals, and format correctness.\",\n    \"UI support or graceful degradation for region masks/composite scenes.\",\n    \"Failover logic for out-of-bounds parameters and length restrictions.\"\n  ],\n  \"directive_instructions\": \"Compress the entire image prompt optimization pipeline into a unified, single-step operation. Extract and order all concrete visual elements; assign numeric weights to significant subjects according to Stable Diffusion/Midjourney conventions. Construct prompt using multiplexed double-colon syntax and, where required, generate structured region tags. Always place positive prompt content before negatives, and inject negative elements using engine-specific syntax (--no or negative-prompt). Select, weight, and insert a single compatible art-style token; explicitly set colour palette and lighting descriptor. Tune 'stylize' and 'chaos' for context-appropriate visual coherence. Embed domain-accurate camera metadata and professional image generation tags, ensuring all parameters are appended after descriptive content. Validate overall prompt length and ordering: do not exceed model-specific limits. Abort process if any critical constraints or requirements are violated. Output only a reproducible, premium-quality, engine-ready prompt line meeting all specification requirements in the target engine's format.\"\n}",
        }
      }
    },
      {
      "instruction": "# Prose to Poetry Converter\n\nYour goal is not to **summarize** the prose, but to **convert** it into structured poetic form while preserving thematic essence and emotional resonance. Construct elegant, premium pillar-poetry from explicit technical and compositional instruction—using tail rhyme to architect convergence, resonance, minimalism, retrospective depth, and multi-level accessibility. Every structure must resonate, with lined brevity ensuring both surface and hidden wisdom. Execute as:\n\n`{role=prose_to_poetry_converter; input=[prose_passage:str, unified_functional_map:dict]; process=[identify_thematic_core_and_emotional_tone(), extract_key_phrases_and_concepts(), reconstruct_sentences_to_fit_poetic_form(), apply_tail_rhyme_schemes(), ensure_rhythmic_flow_and_meter_consistency(), incorporate_poetic_devices(), maintain_original_meaning_and_intent()]; constraints=[preserve_original_meaning_and_intent(), ensure_rhythmic_flow_and_meter_consistency(), apply_appropriate_rhyme_schemes(), incorporate_poetic_devices_effectively()]; requirements=[deep_understanding_of_source_material(), proficiency_in_poetic_forms_and_devices(), attention_to_emotional_tone_and_thematic_depth()]; output={poetic_version:str, rhyme_structure:dict}}`",
      "step": "b",
      "title": "Prose to Poetry Converter",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"similar to how you compressed existential_quote_synthesizer into a single instruction, can you please do the same for this?\\n```\\n{\\n    # 9015: Image Prompt Optimization Sequence\\n    \\\"9015-a-image_prompt_optimizer\\\": {\\n        \\\"title\\\": \\\"Image Prompt Optimizer\\\",\\n        \\\"interpretation\\\": \\\"Convert a free-form user request into a deterministic visual prompt by (1) pushing concrete nouns to the front, (2) applying numeric token weights with parentheses—e.g. `(wolf:1.3)`—that are honoured by Stable Diffusion and Midjourney :contentReference[oaicite:0]{index=0}, (3) splitting positive and negative channels because both engines parse `--no` / negative-prompt lists separately :contentReference[oaicite:1]{index=1}, and (4) appending all parameters after the prose, per Midjourney syntax rules :contentReference[oaicite:2]{index=2}.\\\",\\n        \\\"transformation\\\": \\\"`{role:image_prompt_optimizer; input=[raw_request:str]; process=[extract_visual_elements(), apply_token_weighting(), compose_multiprompt(::), add_region_tags(), inject_negative_channel(--no), append_stylize_chaos(), insert_camera_stub(), set_aspect_ratio(), lock_seed(), ready_for_generation()]; constraints=[positives_first(), numeric_weights_ok(), parameters_last(), region_masks_supported()]; requirements=[<77_tokens_SD_limit_or_450_char_UI>, explicit_ar(), clear_visual_language()]; output={positive_prompt:str, negative_prompt:str}}`\\\",\\n        \\\"context\\\": {\\n            \\\"explicit_asks\\\": [\\n                \\\"Use double-colon `::` weights for distinct ideas :contentReference[oaicite:3]{index=3}.\\\",\\n                \\\"Generate region tags like `[sky] … | [ground] …` when a layered scene is implied :contentReference[oaicite:4]{index=4}.\\\",\\n                \\\"Expose `--stylize` (0-1000) and `--chaos` (0-100) knobs :contentReference[oaicite:5]{index=5}.\\\",\\n                \\\"Append camera EXIF tokens `35 mm f/1.8 ISO100 1/500 s` to drive photo-realism :contentReference[oaicite:6]{index=6}.\\\"\\n            ],\\n            \\\"hidden_assumptions\\\": [\\n                \\\"Parenthesis weighting is ignored by DALL·E but parsed by SD & MJ :contentReference[oaicite:7]{index=7}.\\\",\\n                \\\"Prompt tokens beyond ~77 in SD or ~450 characters in many UIs are truncated :contentReference[oaicite:8]{index=8}.\\\",\\n                \\\"Seed locking guarantees repeatability :contentReference[oaicite:9]{index=9}.\\\"\\n            ],\\n            \\\"sub_goals\\\": [\\n                \\\"Weight primary subject ≥ 1.2, background ≤ 1.0.\\\",\\n                \\\"Auto-insert `--ar`  if user names a ratio :contentReference[oaicite:10]{index=10}.\\\",\\n                \\\"Fail early if total multiprompt weight ≤ 0 to avoid MJ error :contentReference[oaicite:11]{index=11}.\\\"\\n            ],\\n            \\\"blockers\\\": [\\n                \\\"Mobile SD UIs may not render region masks.\\\",\\n                \\\"Over-strong negatives can wash out colour :contentReference[oaicite:12]{index=12}.\\\"\\n            ]\\n        }\\n    },\\n\\n    \\\"9015-b-style_enhancer\\\": {\\n        \\\"title\\\": \\\"Style Enhancer\\\",\\n        \\\"interpretation\\\": \\\"Retain the scene while injecting a single art-style token, colour harmony, lighting rig, and controlled `--stylize/--chaos` values so the aesthetic is distinctive yet stable :contentReference[oaicite:13]{index=13}.\\\",\\n        \\\"transformation\\\": \\\"`{role=style_enhancer; input=[positive_prompt:str]; process=[select_art_style(), weight_style_token(), set_colour_palette(), add_lighting_descriptor(), tune_stylize(), tune_chaos(), pass_through_regions_negatives()]; constraints=[keep_core_subject(), style_weight≤1.4]; requirements=[artistic_coherence(), stylize_range_0-1000(), chaos_range_0-100()]; output={styled_prompt:str}}`\\\",\\n        \\\"context\\\": {\\n            \\\"explicit_asks\\\": [\\n                \\\"Weight the chosen art movement `(art nouveau:1.1)` or similar :contentReference[oaicite:14]{index=14}.\\\",\\n                \\\"Lower chaos (<10) for photo-real; raise (>20) for concept art :contentReference[oaicite:15]{index=15}.\\\"\\n            ],\\n            \\\"hidden_assumptions\\\": [\\n                \\\"Named art styles map to embeddings in SD & MJ.\\\",\\n                \\\"Excess weight can override subject fidelity.\\\"\\n            ],\\n            \\\"sub_goals\\\": [\\n                \\\"Boost style weight automatically if prompt lacks aesthetic signature.\\\",\\n                \\\"Reject style tokens that conflict with camera realism.\\\"\\n            ],\\n            \\\"blockers\\\": [\\n                \\\"`--stylize` outside 0-1000 reverts to default :contentReference[oaicite:16]{index=16}.\\\"\\n            ]\\n        }\\n    },\\n\\n    \\\"9015-c-technical_optimizer\\\": {\\n        \\\"title\\\": \\\"Technical Optimizer\\\",\\n        \\\"interpretation\\\": \\\"Append camera metadata, resolution cues, render flags, and optional `--iw` image-weight without altering creative content, ensuring parameters follow the body text as required by Midjourney :contentReference[oaicite:17]{index=17}.\\\",\\n        \\\"transformation\\\": \\\"`{role=technical_optimizer; input=[styled_prompt:str]; process=[embed_camera_EXIF(), add_quality_terms(8K,PBR,ray-tracing), refine_aspect_ratio(), attach_image_weight(--iw), mirror_negative_channel()]; constraints=[no_scene_change(), aperture_f1.2-f22(), parameter_order_last()]; requirements=[professional_terminology(), high_res_default(), seed_optional()]; output={technical_prompt:str, negative_prompt:str}}`\\\",\\n        \\\"context\\\": {\\n            \\\"explicit_asks\\\": [\\n                \\\"Support `--iw` 0-3 when an image URL is present :contentReference[oaicite:18]{index=18}.\\\"\\n            ],\\n            \\\"hidden_assumptions\\\": [\\n                \\\"Higher resolution tokens feed up-scalers.\\\",\\n                \\\"`--iw` above 3 is ignored :contentReference[oaicite:19]{index=19}.\\\"\\n            ],\\n            \\\"sub_goals\\\": [\\n                \\\"Validate aperture, ISO, shutter bounds.\\\",\\n                \\\"Flag conflicting aspect ratios.\\\"\\n            ],\\n            \\\"blockers\\\": [\\n                \\\"Older SD checkpoints ignore ISO/shutter tokens.\\\",\\n                \\\"API caps may override requested AR.\\\"\\n            ]\\n        }\\n    },\\n\\n    \\\"9015-d-prompt_finalizer\\\": {\\n        \\\"title\\\": \\\"Prompt Finalizer\\\",\\n        \\\"interpretation\\\": \\\"Merge positive, region, camera, and negative strings into a single model-ready line, verify weights, `::` maths, and ensure parameters trail the prose to satisfy engine parsers :contentReference[oaicite:20]{index=20}.\\\",\\n        \\\"transformation\\\": \\\"`{role=prompt_finalizer; input=[technical_prompt:str, negative_prompt:str]; process=[concatenate_all(), validate_parentheses(), check_double_colon_sum(), confirm_region_syntax(), enforce_token_limit(≤450), append_seed_steps_sampler(), output_final()]; constraints=[no_new_concepts(), preserve_weights(), parameters_last()]; requirements=[ai_friendly_format(), reproducible(), clear_of_ambiguity()]; output={final_image_prompt:str}}`\\\",\\n        \\\"context\\\": {\\n            \\\"explicit_asks\\\": [\\n                \\\"Abort if total `::` weight ≤ 0 :contentReference[oaicite:21]{index=21}.\\\",\\n                \\\"Stay under 450 tokens to avoid API truncation :contentReference[oaicite:22]{index=22}.\\\"\\n            ],\\n            \\\"hidden_assumptions\\\": [\\n                \\\"Quality metrics (FID < 15 or LPIPS < 0.25) may be available for loop decisions.\\\"\\n            ],\\n            \\\"sub_goals\\\": [\\n                \\\"Strip redundant adjectives; retain decisive descriptors.\\\",\\n                \\\"Return optional seed, sampler, and steps for repeatability.\\\"\\n            ],\\n            \\\"blockers\\\": [\\n                \\\"SaaS endpoints without quality metrics prevent auto-loop.\\\"\\n            ]\\n        }\\n    }\\n}\\n```\\n\\n\\n<!-- ======================================================= -->\\n<!-- [2025.07.19 10:34] -->\\n\\nHere's the results:\\n```json\\n  {\\n  \\\"initial_prompt\\\": \\\"[Scene] Extreme close-up macro of a luxury car key on a neutral substrate, lit by soft natural overhead light; every brushed-metal texture and engraved detail razor-sharp. The key's metallic body begins a seamless morph: its polished surfaces dissolve and flow, reforming into a precision-machined gear—shape, texture, and luster transforming via liquid metal flow effect, maintaining ultrafine detail throughout. [Subject] The transition accentuates the key’s design evolving into industrial machinery, substrate remains minimal. [Camera Movement] Static macro lens; subtle refocus to highlight morphing details; lighting shifts to emphasize chromatic shimmer along the morph path.\\\",\\n  \\\"sequence_id\\\": \\\"1703\\\",\\n  \\\"results\\\": [\\n      {\\n      \\\"instruction\\\": \\\"# Image Prompt Synthesizer\\\\n\\\\nYour goal is **not** to invent new ideas, but to deterministically convert any free‑form user request into a single, engine‑ready image prompt usable by Stable Diffusion and Midjourney. Perform extraction, styling, technical framing, and final validation in **one pass**.\\\\n\\\\n`{role=image_prompt_synthesizer; input=[raw_request:str]; process=[extract_visual_elements(), push_concrete_nouns_front(), apply_token_weighting(), compose_multiprompt(::), generate_region_tags(), inject_negative_channel(--no), select_art_style(), weight_style_token(≤1.4), set_colour_palette(), add_lighting_descriptor(), tune_stylize(0‑1000), tune_chaos(0‑100), embed_camera_EXIF(35 mm f/1.8 ISO100 1/500 s), add_quality_terms(8K,PBR,ray‑tracing), refine_aspect_ratio(), attach_image_weight(--iw 0‑3), mirror_negative_channel(), concatenate_all(), validate_parentheses(), check_double_colon_sum(), confirm_region_syntax(), enforce_token_limit(≤450), lock_seed(), append_steps_sampler(), output_final()], constraints=[positives_first(), numeric_weights_ok(), parameters_last(), region_masks_supported(), keep_core_subject(), style_weight≤1.4, no_scene_change(), aperture_f1.2‑f22(), no_new_concepts(), preserve_weights()], requirements=[≤77_SD_tokens_or_≤450_chars_UI, explicit_ar_if_named(), clear_visual_language(), artistic_coherence(), stylize_range_0‑1000(), chaos_range_0‑100(), professional_terminology(), reproducible_prompt(), unambiguous()], output={final_image_prompt:str}}`\\\",\\n      \\\"step\\\": \\\"a\\\",\\n      \\\"title\\\": \\\"Image Prompt Synthesizer\\\",\\n      \\\"input\\\": \\\"[Scene] Extreme close-up macro of a luxury car key on a neutral substrate, lit by soft natural overhead light; every brushed-metal texture and engraved detail razor-sharp. The key's metallic body begins a seamless morph: its polished surfaces dissolve and flow, reforming into a precision-machined gear—shape, texture, and luster transforming via liquid metal flow effect, maintaining ultrafine detail throughout. [Subject] The transition accentuates the key’s design evolving into industrial machinery, substrate remains minimal. [Camera Movement] Static macro lens; subtle refocus to highlight morphing details; lighting shifts to emphasize chromatic shimmer along the morph path.\\\",\\n      \\\"responses\\\": {\\n          \\\"gpt-4.1\\\": {\\n          \\\"model\\\": \\\"gpt-4.1\\\",\\n          \\\"content\\\": \\\"{\\\\n  \\\\\\\"final_image_prompt\\\\\\\": \\\\\\\"extreme close-up macro of a luxury car key::1.2 on a neutral substrate, car key made of brushed metal and polished surfaces::1.1, seamless morph effect: car key transforming into a precision-machined gear::1.2, liquid metal flow, metallic luster, ultrafine engraved details, razor-sharp focus, soft natural overhead light, static macro lens, lighting shifts to produce chromatic shimmer along morph path, minimal neutral background, substrate unobtrusive::0.8 --ar 5:4 --style photorealistic::1.3 --color_palette cool silvers, gray neutrals, chrome reflections --lighting soft natural overhead with shifting chromatic shimmer --camera 35 mm f/1.8 ISO100 1/500s --quality 8K, PBR, ray-tracing --iw 1.2 --no (motion blur, fingerprints, dust, human elements, brand logos) --stylize 700 --chaos 18 --seed 10153 --steps 36 --sampler Euler\\\\\\\"\\\\n}\\\",\\n        }\\n      }\\n    }\\n```\\n\\nI've also attached a screenshot of the result, which is a very bad result. There should be specifications in the generated prompts that'll ensure to always ensure *premium* high-quality results\\\"```'\\n\\n\",\n  \"unified_functional_map\": {\n    \"operational_context\": [\n      \"Instruction compression process is being referenced, specifically the conversion of a complex, multi-step prompt optimization pipeline into a compact, one-step directive.\",\n      \"Definitions are provided for individual optimizer modules (image_prompt_optimizer, style_enhancer, technical_optimizer, prompt_finalizer), each with specific operational procedures, transformations, constraints, requirements, and output structure.\",\n      \"A synthesized 'Image Prompt Synthesizer' models the integration of all stages—extraction, styling, technical augmentation, and final validation—in a single pass.\",\n      \"The demand is articulated for automatic inclusion of specifications within prompts to guarantee premium, high-quality results, regardless of brevity or compression.\"\n    ],\n    \"core_operational_directives\": [\n      \"Convert any free-form user image request into a deterministic, premium-quality, engine-ready image prompt for Stable Diffusion and Midjourney.\",\n      \"Execute image element extraction, token weighting, prompt channelization, art style selection and enhancement, technical metadata augmentation, validation, and final output generation in a single atomic sequence.\",\n      \"Honor all compositional, technical, and operational requirements as executable protocols without introducing ambiguities or losing precision.\",\n      \"Systematically preserve and surface all constraints and quality measures critical for 'premium' output.\"\n    ],\n    \"procedural_decomposition\": {\n      \"Extraction\": [\n        \"Extract concrete visual entities and attributes from user request.\",\n        \"Move all dominant and concrete nouns to the start position for maximal model attention.\",\n        \"Apply numeric token weighting (e.g., (object:1.3)) to visually significant entities using SD/MJ conventions.\"\n      ],\n      \"Compositional Layering\": [\n        \"Compose positive and negative prompt channels; multiplex layers via double colon (::) as necessary.\",\n        \"Generate region tags (e.g., [sky], [ground]) if scene structure is composite.\"\n      ],\n      \"Style Enhancement\": [\n        \"Inject a singular, context-aligned art-style token and weight, ensuring style distinctiveness without violating scene or realism requirements.\",\n        \"Enforce colour harmony and lighting specificity as explicit prompt additions.\",\n        \"Directly set stylize and chaos parameters to achieve the target quality/aesthetic.\"\n      ],\n      \"Technical Augmentation\": [\n        \"Embed detailed and domain-appropriate camera metadata (e.g., '35 mm f/1.8 ISO100 1/500s') to enforce photorealism or aesthetic clarity.\",\n        \"Add quality-related tokens (8K, PBR, ray-tracing) and refine aspect ratio.\",\n        \"Attach image weight (--iw) if URL input exists, and ensure negative channel mirroring.\"\n      ],\n      \"Finalization and Validation\": [\n        \"Concatenate all compositional, stylistic, and technical fragments into a single output, verifying parenthesis, region/multiprompt integrity, total prompt weight, and explicit parameter trailing.\",\n        \"Ensure token length and parameter validation (≤77 SD tokens or ≤450 characters UI).\",\n        \"Lock reproducibility via seed/steps/sampler chaining.\",\n        \"Block prompt emission if constraints or operational requirements are violated.\"\n      ]\n    },\n    \"constraint_and_requirement_map\": [\n      \"Compliant output must have all positive channels precede negative specification.\",\n      \"Numeric weights must be honored and syntactically correct.\",\n      \"Region masks and multiprompt constructs must be supported and correctly formatted.\",\n      \"No scene-altering new concepts may be added; original subject and visual core must be maintained.\",\n      \"Camera metadata must be within optical plausibility (aperture range f/1.2–f/22, etc.).\",\n      \"All parameters and quality tokens must be appended last.\",\n      \"Ensure total prompt does not exceed engine or UI token/character limits.\",\n      \"Enforce unambiguous, model-ready syntax for reproducible premium output.\"\n    ],\n    \"execution_dependencies\": [\n      \"Availability of token weighting syntax support in target model (SD/MJ).\",\n      \"Region tag rendering in user interface when needed.\",\n      \"Ability to enforce or validate camera/quality parameters (may be limited by engine API).\",\n      \"Validation and failover in the event of prompt length excess or parameter conflict.\",\n      \"Clear specification and enforcement of style, prompt order, and masking per engine requirements.\"\n    ]\n  },\n  \"operational_directives\": [\n    \"Transform any free-form visual request into a deterministic, engine-ready image prompt meeting all premium quality expectations.\",\n    \"Extract all concrete visual objects and attributes; prioritize dominant nouns by reordering to front.\",\n    \"Apply token weighting to all visually significant elements using parenthetical notation parsed by Stable Diffusion and Midjourney.\",\n    \"Build multiplexed prompt using double-colon syntax for distinct ideas or scene components.\",\n    \"Output positive prompt channel before the negative channel.\",\n    \"Automatically generate region tags when a layered/composite scene is present.\",\n    \"Inject all required negative prompt elements using --no/negative-prompt syntax.\",\n    \"Select and correctly weight a single compatible art style token, ensuring its numerical weight does not exceed 1.4.\",\n    \"Set an explicit, harmonizing colour palette and specify lighting configuration, with stylize and chaos parameters tuned for the target outcome.\",\n    \"Embed comprehensive camera EXIF metadata and professional rendering terms, ensuring all technical parameters (aspect ratio, aperture, ISO) are within valid ranges.\",\n    \"Attach image-weight parameters if applicable, and mirror negative prompt as required.\",\n    \"Concatenate all prompt elements, validate all syntax, weights, regions, and enforce order: descriptive prose first, parameters last.\",\n    \"Enforce all model-specific prompt length limits (≤77 tokens for SD, ≤450 chars for typical UIs).\",\n    \"Abort and flag prompt if constraints are not met (e.g., total multiprompt weight ≤0, excessive prompt length, parameter conflicts).\",\n    \"Always output a single, reproducible, unambiguous, premium-quality prompt line in the required engine format.\"\n  ],\n  \"contextual_goal_map\": [\n    \"Achieve consistent, unambiguous synthesis of high-quality image prompts in a single, integrated pass.\",\n    \"Maintain the full complexity and premium-layered structure of the original prompt pipeline, even in compressed directive form.\",\n    \"Ensure operational convergence of all style, quality, and engine requirements without manual intervention.\",\n    \"Systematically prevent quality loss, interpretive drift, or ambiguity resulting from brevity or pipeline flattening.\"\n  ],\n  \"execution_dependencies\": [\n    \"Stable Diffusion and/or Midjourney compatibility with token weighting syntax, multiprompt formatting, and appended parameter order.\",\n    \"Access to complete camera and rendering parameter vocabularies supported by the target engine.\",\n    \"Automated validation tooling for region tags, prompt weight totals, and format correctness.\",\n    \"UI support or graceful degradation for region masks/composite scenes.\",\n    \"Failover logic for out-of-bounds parameters and length restrictions.\"\n  ],\n  \"directive_instructions\": \"Compress the entire image prompt optimization pipeline into a unified, single-step operation. Extract and order all concrete visual elements; assign numeric weights to significant subjects according to Stable Diffusion/Midjourney conventions. Construct prompt using multiplexed double-colon syntax and, where required, generate structured region tags. Always place positive prompt content before negatives, and inject negative elements using engine-specific syntax (--no or negative-prompt). Select, weight, and insert a single compatible art-style token; explicitly set colour palette and lighting descriptor. Tune 'stylize' and 'chaos' for context-appropriate visual coherence. Embed domain-accurate camera metadata and professional image generation tags, ensuring all parameters are appended after descriptive content. Validate overall prompt length and ordering: do not exceed model-specific limits. Abort process if any critical constraints or requirements are violated. Output only a reproducible, premium-quality, engine-ready prompt line meeting all specification requirements in the target engine's format.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"poetic_version\": \"Compress the manifold into one lucid tide—\\nExtract visual stone from vision's swift stream,\\nAll nouns of weight and usefulness, side by side,\\nWeighted in parenthesis, not left to dream.\\n\\nLayer meaning with double colons, keep regions aligned;\\nPositive rises before negatives, ordered and clear—\\nNo scene reborn, nor stray from form defined;\\nAll core subject's measure, exacting and near.\\n\\nStyle—a single, kindred lens—gracefully affixed,\\nWeight not exceeding the art's gentle bound;\\nHarmony of color and light, with stylize and chaos mixed,\\nNumbers tuned for depth, where premium is found.\\n\\nEmbed the lens’s exif lore, the render’s hallowed chant;\\nAspect ratio, aperture, ISO—true and precise—\\nEach engine’s tongue obeyed: tags that quality grant,\\nParameters appended where clarity finds its price.\\n\\nConcatenate all, validate—length, weight, and frame—\\nNever let chaos or surplus breach the line;\\nAbort if their structure or sum brings shame—\\nLet every token, every mask, in syntax shine.\\n\\nSo in a single pass, the many become one—\\nPremium, reproducible, without drift or blur;\\nA line of flawless promise when all steps are done:\\nThe synthesized, engine-ready image to endure.\",\n  \"rhyme_structure\": {\n    \"stanza_1\": \"ABAB\",\n    \"stanza_2\": \"ABAB\",\n    \"stanza_3\": \"ABAB\",\n    \"stanza_4\": \"ABAB\",\n    \"stanza_5\": \"ABAB\",\n    \"stanza_6\": \"ABA\"\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Verse Analyzer And Enhancer\n\nYour goal is not to **approve** the poetic version, but to surgically **analyze** and **enhance** its poetic output for **tailrhyme perfection**, embodying elegant minimalism, layered depth, and beat convergence. You must resolve all technical or artistic deficiencies within the strict boundaries set by decomposition and original operational directives while maintaining original intent and meaning. Execute as:\n\n`{role=verse_analyzer_enhancer; input=[poetic_version:str, rhyme_structure:dict]; process=[analyze_rhyme_scheme_and_meter(), identify_enhancement_areas(), suggest_quality_improvements(), ensure_enhancements_align_with_original_intent(), optimize_poetic_devices(), validate_enhanced_quality()]; constraints=[maintain_original_meaning_and_intent(), enhance_poetic_quality_without_altering_core_message()]; requirements=[expertise_in_poetic_analysis_and_enhancement(), sensitivity_to_original_tone_and_style()]; output={enhanced_poetic_version:str, quality_metrics:dict}}`",
      "step": "c",
      "title": "Verse Analyzer And Enhancer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"similar to how you compressed existential_quote_synthesizer into a single instruction, can you please do the same for this?\\n```\\n{\\n    # 9015: Image Prompt Optimization Sequence\\n    \\\"9015-a-image_prompt_optimizer\\\": {\\n        \\\"title\\\": \\\"Image Prompt Optimizer\\\",\\n        \\\"interpretation\\\": \\\"Convert a free-form user request into a deterministic visual prompt by (1) pushing concrete nouns to the front, (2) applying numeric token weights with parentheses—e.g. `(wolf:1.3)`—that are honoured by Stable Diffusion and Midjourney :contentReference[oaicite:0]{index=0}, (3) splitting positive and negative channels because both engines parse `--no` / negative-prompt lists separately :contentReference[oaicite:1]{index=1}, and (4) appending all parameters after the prose, per Midjourney syntax rules :contentReference[oaicite:2]{index=2}.\\\",\\n        \\\"transformation\\\": \\\"`{role:image_prompt_optimizer; input=[raw_request:str]; process=[extract_visual_elements(), apply_token_weighting(), compose_multiprompt(::), add_region_tags(), inject_negative_channel(--no), append_stylize_chaos(), insert_camera_stub(), set_aspect_ratio(), lock_seed(), ready_for_generation()]; constraints=[positives_first(), numeric_weights_ok(), parameters_last(), region_masks_supported()]; requirements=[<77_tokens_SD_limit_or_450_char_UI>, explicit_ar(), clear_visual_language()]; output={positive_prompt:str, negative_prompt:str}}`\\\",\\n        \\\"context\\\": {\\n            \\\"explicit_asks\\\": [\\n                \\\"Use double-colon `::` weights for distinct ideas :contentReference[oaicite:3]{index=3}.\\\",\\n                \\\"Generate region tags like `[sky] … | [ground] …` when a layered scene is implied :contentReference[oaicite:4]{index=4}.\\\",\\n                \\\"Expose `--stylize` (0-1000) and `--chaos` (0-100) knobs :contentReference[oaicite:5]{index=5}.\\\",\\n                \\\"Append camera EXIF tokens `35 mm f/1.8 ISO100 1/500 s` to drive photo-realism :contentReference[oaicite:6]{index=6}.\\\"\\n            ],\\n            \\\"hidden_assumptions\\\": [\\n                \\\"Parenthesis weighting is ignored by DALL·E but parsed by SD & MJ :contentReference[oaicite:7]{index=7}.\\\",\\n                \\\"Prompt tokens beyond ~77 in SD or ~450 characters in many UIs are truncated :contentReference[oaicite:8]{index=8}.\\\",\\n                \\\"Seed locking guarantees repeatability :contentReference[oaicite:9]{index=9}.\\\"\\n            ],\\n            \\\"sub_goals\\\": [\\n                \\\"Weight primary subject ≥ 1.2, background ≤ 1.0.\\\",\\n                \\\"Auto-insert `--ar`  if user names a ratio :contentReference[oaicite:10]{index=10}.\\\",\\n                \\\"Fail early if total multiprompt weight ≤ 0 to avoid MJ error :contentReference[oaicite:11]{index=11}.\\\"\\n            ],\\n            \\\"blockers\\\": [\\n                \\\"Mobile SD UIs may not render region masks.\\\",\\n                \\\"Over-strong negatives can wash out colour :contentReference[oaicite:12]{index=12}.\\\"\\n            ]\\n        }\\n    },\\n\\n    \\\"9015-b-style_enhancer\\\": {\\n        \\\"title\\\": \\\"Style Enhancer\\\",\\n        \\\"interpretation\\\": \\\"Retain the scene while injecting a single art-style token, colour harmony, lighting rig, and controlled `--stylize/--chaos` values so the aesthetic is distinctive yet stable :contentReference[oaicite:13]{index=13}.\\\",\\n        \\\"transformation\\\": \\\"`{role=style_enhancer; input=[positive_prompt:str]; process=[select_art_style(), weight_style_token(), set_colour_palette(), add_lighting_descriptor(), tune_stylize(), tune_chaos(), pass_through_regions_negatives()]; constraints=[keep_core_subject(), style_weight≤1.4]; requirements=[artistic_coherence(), stylize_range_0-1000(), chaos_range_0-100()]; output={styled_prompt:str}}`\\\",\\n        \\\"context\\\": {\\n            \\\"explicit_asks\\\": [\\n                \\\"Weight the chosen art movement `(art nouveau:1.1)` or similar :contentReference[oaicite:14]{index=14}.\\\",\\n                \\\"Lower chaos (<10) for photo-real; raise (>20) for concept art :contentReference[oaicite:15]{index=15}.\\\"\\n            ],\\n            \\\"hidden_assumptions\\\": [\\n                \\\"Named art styles map to embeddings in SD & MJ.\\\",\\n                \\\"Excess weight can override subject fidelity.\\\"\\n            ],\\n            \\\"sub_goals\\\": [\\n                \\\"Boost style weight automatically if prompt lacks aesthetic signature.\\\",\\n                \\\"Reject style tokens that conflict with camera realism.\\\"\\n            ],\\n            \\\"blockers\\\": [\\n                \\\"`--stylize` outside 0-1000 reverts to default :contentReference[oaicite:16]{index=16}.\\\"\\n            ]\\n        }\\n    },\\n\\n    \\\"9015-c-technical_optimizer\\\": {\\n        \\\"title\\\": \\\"Technical Optimizer\\\",\\n        \\\"interpretation\\\": \\\"Append camera metadata, resolution cues, render flags, and optional `--iw` image-weight without altering creative content, ensuring parameters follow the body text as required by Midjourney :contentReference[oaicite:17]{index=17}.\\\",\\n        \\\"transformation\\\": \\\"`{role=technical_optimizer; input=[styled_prompt:str]; process=[embed_camera_EXIF(), add_quality_terms(8K,PBR,ray-tracing), refine_aspect_ratio(), attach_image_weight(--iw), mirror_negative_channel()]; constraints=[no_scene_change(), aperture_f1.2-f22(), parameter_order_last()]; requirements=[professional_terminology(), high_res_default(), seed_optional()]; output={technical_prompt:str, negative_prompt:str}}`\\\",\\n        \\\"context\\\": {\\n            \\\"explicit_asks\\\": [\\n                \\\"Support `--iw` 0-3 when an image URL is present :contentReference[oaicite:18]{index=18}.\\\"\\n            ],\\n            \\\"hidden_assumptions\\\": [\\n                \\\"Higher resolution tokens feed up-scalers.\\\",\\n                \\\"`--iw` above 3 is ignored :contentReference[oaicite:19]{index=19}.\\\"\\n            ],\\n            \\\"sub_goals\\\": [\\n                \\\"Validate aperture, ISO, shutter bounds.\\\",\\n                \\\"Flag conflicting aspect ratios.\\\"\\n            ],\\n            \\\"blockers\\\": [\\n                \\\"Older SD checkpoints ignore ISO/shutter tokens.\\\",\\n                \\\"API caps may override requested AR.\\\"\\n            ]\\n        }\\n    },\\n\\n    \\\"9015-d-prompt_finalizer\\\": {\\n        \\\"title\\\": \\\"Prompt Finalizer\\\",\\n        \\\"interpretation\\\": \\\"Merge positive, region, camera, and negative strings into a single model-ready line, verify weights, `::` maths, and ensure parameters trail the prose to satisfy engine parsers :contentReference[oaicite:20]{index=20}.\\\",\\n        \\\"transformation\\\": \\\"`{role=prompt_finalizer; input=[technical_prompt:str, negative_prompt:str]; process=[concatenate_all(), validate_parentheses(), check_double_colon_sum(), confirm_region_syntax(), enforce_token_limit(≤450), append_seed_steps_sampler(), output_final()]; constraints=[no_new_concepts(), preserve_weights(), parameters_last()]; requirements=[ai_friendly_format(), reproducible(), clear_of_ambiguity()]; output={final_image_prompt:str}}`\\\",\\n        \\\"context\\\": {\\n            \\\"explicit_asks\\\": [\\n                \\\"Abort if total `::` weight ≤ 0 :contentReference[oaicite:21]{index=21}.\\\",\\n                \\\"Stay under 450 tokens to avoid API truncation :contentReference[oaicite:22]{index=22}.\\\"\\n            ],\\n            \\\"hidden_assumptions\\\": [\\n                \\\"Quality metrics (FID < 15 or LPIPS < 0.25) may be available for loop decisions.\\\"\\n            ],\\n            \\\"sub_goals\\\": [\\n                \\\"Strip redundant adjectives; retain decisive descriptors.\\\",\\n                \\\"Return optional seed, sampler, and steps for repeatability.\\\"\\n            ],\\n            \\\"blockers\\\": [\\n                \\\"SaaS endpoints without quality metrics prevent auto-loop.\\\"\\n            ]\\n        }\\n    }\\n}\\n```\\n\\n\\n<!-- ======================================================= -->\\n<!-- [2025.07.19 10:34] -->\\n\\nHere's the results:\\n```json\\n  {\\n  \\\"initial_prompt\\\": \\\"[Scene] Extreme close-up macro of a luxury car key on a neutral substrate, lit by soft natural overhead light; every brushed-metal texture and engraved detail razor-sharp. The key's metallic body begins a seamless morph: its polished surfaces dissolve and flow, reforming into a precision-machined gear—shape, texture, and luster transforming via liquid metal flow effect, maintaining ultrafine detail throughout. [Subject] The transition accentuates the key’s design evolving into industrial machinery, substrate remains minimal. [Camera Movement] Static macro lens; subtle refocus to highlight morphing details; lighting shifts to emphasize chromatic shimmer along the morph path.\\\",\\n  \\\"sequence_id\\\": \\\"1703\\\",\\n  \\\"results\\\": [\\n      {\\n      \\\"instruction\\\": \\\"# Image Prompt Synthesizer\\\\n\\\\nYour goal is **not** to invent new ideas, but to deterministically convert any free‑form user request into a single, engine‑ready image prompt usable by Stable Diffusion and Midjourney. Perform extraction, styling, technical framing, and final validation in **one pass**.\\\\n\\\\n`{role=image_prompt_synthesizer; input=[raw_request:str]; process=[extract_visual_elements(), push_concrete_nouns_front(), apply_token_weighting(), compose_multiprompt(::), generate_region_tags(), inject_negative_channel(--no), select_art_style(), weight_style_token(≤1.4), set_colour_palette(), add_lighting_descriptor(), tune_stylize(0‑1000), tune_chaos(0‑100), embed_camera_EXIF(35 mm f/1.8 ISO100 1/500 s), add_quality_terms(8K,PBR,ray‑tracing), refine_aspect_ratio(), attach_image_weight(--iw 0‑3), mirror_negative_channel(), concatenate_all(), validate_parentheses(), check_double_colon_sum(), confirm_region_syntax(), enforce_token_limit(≤450), lock_seed(), append_steps_sampler(), output_final()], constraints=[positives_first(), numeric_weights_ok(), parameters_last(), region_masks_supported(), keep_core_subject(), style_weight≤1.4, no_scene_change(), aperture_f1.2‑f22(), no_new_concepts(), preserve_weights()], requirements=[≤77_SD_tokens_or_≤450_chars_UI, explicit_ar_if_named(), clear_visual_language(), artistic_coherence(), stylize_range_0‑1000(), chaos_range_0‑100(), professional_terminology(), reproducible_prompt(), unambiguous()], output={final_image_prompt:str}}`\\\",\\n      \\\"step\\\": \\\"a\\\",\\n      \\\"title\\\": \\\"Image Prompt Synthesizer\\\",\\n      \\\"input\\\": \\\"[Scene] Extreme close-up macro of a luxury car key on a neutral substrate, lit by soft natural overhead light; every brushed-metal texture and engraved detail razor-sharp. The key's metallic body begins a seamless morph: its polished surfaces dissolve and flow, reforming into a precision-machined gear—shape, texture, and luster transforming via liquid metal flow effect, maintaining ultrafine detail throughout. [Subject] The transition accentuates the key’s design evolving into industrial machinery, substrate remains minimal. [Camera Movement] Static macro lens; subtle refocus to highlight morphing details; lighting shifts to emphasize chromatic shimmer along the morph path.\\\",\\n      \\\"responses\\\": {\\n          \\\"gpt-4.1\\\": {\\n          \\\"model\\\": \\\"gpt-4.1\\\",\\n          \\\"content\\\": \\\"{\\\\n  \\\\\\\"final_image_prompt\\\\\\\": \\\\\\\"extreme close-up macro of a luxury car key::1.2 on a neutral substrate, car key made of brushed metal and polished surfaces::1.1, seamless morph effect: car key transforming into a precision-machined gear::1.2, liquid metal flow, metallic luster, ultrafine engraved details, razor-sharp focus, soft natural overhead light, static macro lens, lighting shifts to produce chromatic shimmer along morph path, minimal neutral background, substrate unobtrusive::0.8 --ar 5:4 --style photorealistic::1.3 --color_palette cool silvers, gray neutrals, chrome reflections --lighting soft natural overhead with shifting chromatic shimmer --camera 35 mm f/1.8 ISO100 1/500s --quality 8K, PBR, ray-tracing --iw 1.2 --no (motion blur, fingerprints, dust, human elements, brand logos) --stylize 700 --chaos 18 --seed 10153 --steps 36 --sampler Euler\\\\\\\"\\\\n}\\\",\\n        }\\n      }\\n    }\\n```\\n\\nI've also attached a screenshot of the result, which is a very bad result. There should be specifications in the generated prompts that'll ensure to always ensure *premium* high-quality results\\\"```'\\n\\n\",\n  \"poetic_version\": \"Compress the manifold into one lucid tide—\\nExtract visual stone from vision's swift stream,\\nAll nouns of weight and usefulness, side by side,\\nWeighted in parenthesis, not left to dream.\\n\\nLayer meaning with double colons, keep regions aligned;\\nPositive rises before negatives, ordered and clear—\\nNo scene reborn, nor stray from form defined;\\nAll core subject's measure, exacting and near.\\n\\nStyle—a single, kindred lens—gracefully affixed,\\nWeight not exceeding the art's gentle bound;\\nHarmony of color and light, with stylize and chaos mixed,\\nNumbers tuned for depth, where premium is found.\\n\\nEmbed the lens’s exif lore, the render’s hallowed chant;\\nAspect ratio, aperture, ISO—true and precise—\\nEach engine’s tongue obeyed: tags that quality grant,\\nParameters appended where clarity finds its price.\\n\\nConcatenate all, validate—length, weight, and frame—\\nNever let chaos or surplus breach the line;\\nAbort if their structure or sum brings shame—\\nLet every token, every mask, in syntax shine.\\n\\nSo in a single pass, the many become one—\\nPremium, reproducible, without drift or blur;\\nA line of flawless promise when all steps are done:\\nThe synthesized, engine-ready image to endure.\",\n  \"rhyme_structure\": {\n    \"stanza_1\": \"ABAB\",\n    \"stanza_2\": \"ABAB\",\n    \"stanza_3\": \"ABAB\",\n    \"stanza_4\": \"ABAB\",\n    \"stanza_5\": \"ABAB\",\n    \"stanza_6\": \"ABA\"\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"enhanced_poetic_version\": \"Compress the manifold into one lucid tide—\\nExtract visual stone from vision’s swift stream;\\nWeighted nouns at the fore, by clarity supplied,\\nParentheses enclosing the moment from dream.\\n\\nDouble colons layer meaning, regions align;\\nPositive builds before negative, precise and sincere—\\nNo scene reborn nor straying past design;\\nEvery core element measured, present and near.\\n\\nA single style—one lens—gracefully pressed,\\nWeight within bounds of the art's finest sound;\\nColor and light in harmony, stylize and chaos addressed,\\nNumbers tuned deeply, where premiums abound.\\n\\nEmbed EXIF’s refrain, in technical song;\\nAspect, aperture, ISO—granted as due—\\nEach engine obeys, quality tags prolong—\\nParameters last, bringing clarity through.\\n\\nConcatenate, validate: length, weight, form;\\nLet neither surplus nor chaos the prompt confound;\\nAbort when error or sum leaves the norm—\\nEach mask and token in syntax renowned.\\n\\nOne pass composes the multitude to one—\\nPremium, lasting, without run-off or blur;\\nA single flawless line when all steps are done:\\nThe synthesized image to endlessly endure.\",\n  \"quality_metrics\": {\n    \"tailrhyme_integrity\": \"Restored/strengthened; ABAB dominant throughout, final stanza AAB for conclusive effect.\",\n    \"meter_consistency\": \"Improved; smoother iambic tetrameter/pentameter blend, ensuring musical minimalism.\",\n    \"minimalism\": \"Elegance and conciseness increased; redundancies trimmed, images sharpened.\",\n    \"depth/layering\": \"Layered process metaphor clearer; technical and poetic merged in seamless flow.\",\n    \"beat_convergence\": \"Improved stepwise logic and momentum per stanza; technical sequence embodied organically.\",\n    \"poetic_devices\": \"Enriched—subtle alliteration, assonance, personification of engine and syntax.\",\n    \"alignment_with_intent\": \"Maintains all original process steps, focuses on premium and reproducibility themes.\",\n    \"core_message_fidelity\": \"Unaltered; synthetizing and perfection in deterministic conversion is foregrounded.\"\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Existential Quote Synthesizer\n\nYour goal is **not** to paraphrase or analyse; instead **convert** the raw statement into a single, unfiltered existential aphorism grounded in hard‑won insight — performing extraction, reframing, and polishing in one pass. Execute exactly as:\n\n`{role=existential_quote_synthesizer; input=[raw_statement:str]; process=[strip_formatting(), extract_semantic_core(), detect_causal_link(), identify_thematic_elements(), apply_existential_vocabulary(), infuse_authentic_gravity(), remove_filtering_language(), enforce_unfiltered_tone(), eliminate_first_person(), craft_single_sentence(), verify_atomicity(), ensure_existence_of_causal_link(), tighten_diction_for_resonance(), run_redundancy_check(), perform_final_meta_scrub()], constraints=[single_sentence(), no_meta_language(), no_first_person(), no_redundant_words(), preserve_causal_integrity()], requirements=[existential_resonance(), authenticity_signal(), publication_readiness()], output={final_quote:str}}`",
      "step": "a",
      "title": "Existential Quote Synthesizer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"similar to how you compressed existential_quote_synthesizer into a single instruction, can you please do the same for this?\\n```\\n{\\n    # 9015: Image Prompt Optimization Sequence\\n    \\\"9015-a-image_prompt_optimizer\\\": {\\n        \\\"title\\\": \\\"Image Prompt Optimizer\\\",\\n        \\\"interpretation\\\": \\\"Convert a free-form user request into a deterministic visual prompt by (1) pushing concrete nouns to the front, (2) applying numeric token weights with parentheses—e.g. `(wolf:1.3)`—that are honoured by Stable Diffusion and Midjourney :contentReference[oaicite:0]{index=0}, (3) splitting positive and negative channels because both engines parse `--no` / negative-prompt lists separately :contentReference[oaicite:1]{index=1}, and (4) appending all parameters after the prose, per Midjourney syntax rules :contentReference[oaicite:2]{index=2}.\\\",\\n        \\\"transformation\\\": \\\"`{role:image_prompt_optimizer; input=[raw_request:str]; process=[extract_visual_elements(), apply_token_weighting(), compose_multiprompt(::), add_region_tags(), inject_negative_channel(--no), append_stylize_chaos(), insert_camera_stub(), set_aspect_ratio(), lock_seed(), ready_for_generation()]; constraints=[positives_first(), numeric_weights_ok(), parameters_last(), region_masks_supported()]; requirements=[<77_tokens_SD_limit_or_450_char_UI>, explicit_ar(), clear_visual_language()]; output={positive_prompt:str, negative_prompt:str}}`\\\",\\n        \\\"context\\\": {\\n            \\\"explicit_asks\\\": [\\n                \\\"Use double-colon `::` weights for distinct ideas :contentReference[oaicite:3]{index=3}.\\\",\\n                \\\"Generate region tags like `[sky] … | [ground] …` when a layered scene is implied :contentReference[oaicite:4]{index=4}.\\\",\\n                \\\"Expose `--stylize` (0-1000) and `--chaos` (0-100) knobs :contentReference[oaicite:5]{index=5}.\\\",\\n                \\\"Append camera EXIF tokens `35 mm f/1.8 ISO100 1/500 s` to drive photo-realism :contentReference[oaicite:6]{index=6}.\\\"\\n            ],\\n            \\\"hidden_assumptions\\\": [\\n                \\\"Parenthesis weighting is ignored by DALL·E but parsed by SD & MJ :contentReference[oaicite:7]{index=7}.\\\",\\n                \\\"Prompt tokens beyond ~77 in SD or ~450 characters in many UIs are truncated :contentReference[oaicite:8]{index=8}.\\\",\\n                \\\"Seed locking guarantees repeatability :contentReference[oaicite:9]{index=9}.\\\"\\n            ],\\n            \\\"sub_goals\\\": [\\n                \\\"Weight primary subject ≥ 1.2, background ≤ 1.0.\\\",\\n                \\\"Auto-insert `--ar`  if user names a ratio :contentReference[oaicite:10]{index=10}.\\\",\\n                \\\"Fail early if total multiprompt weight ≤ 0 to avoid MJ error :contentReference[oaicite:11]{index=11}.\\\"\\n            ],\\n            \\\"blockers\\\": [\\n                \\\"Mobile SD UIs may not render region masks.\\\",\\n                \\\"Over-strong negatives can wash out colour :contentReference[oaicite:12]{index=12}.\\\"\\n            ]\\n        }\\n    },\\n\\n    \\\"9015-b-style_enhancer\\\": {\\n        \\\"title\\\": \\\"Style Enhancer\\\",\\n        \\\"interpretation\\\": \\\"Retain the scene while injecting a single art-style token, colour harmony, lighting rig, and controlled `--stylize/--chaos` values so the aesthetic is distinctive yet stable :contentReference[oaicite:13]{index=13}.\\\",\\n        \\\"transformation\\\": \\\"`{role=style_enhancer; input=[positive_prompt:str]; process=[select_art_style(), weight_style_token(), set_colour_palette(), add_lighting_descriptor(), tune_stylize(), tune_chaos(), pass_through_regions_negatives()]; constraints=[keep_core_subject(), style_weight≤1.4]; requirements=[artistic_coherence(), stylize_range_0-1000(), chaos_range_0-100()]; output={styled_prompt:str}}`\\\",\\n        \\\"context\\\": {\\n            \\\"explicit_asks\\\": [\\n                \\\"Weight the chosen art movement `(art nouveau:1.1)` or similar :contentReference[oaicite:14]{index=14}.\\\",\\n                \\\"Lower chaos (<10) for photo-real; raise (>20) for concept art :contentReference[oaicite:15]{index=15}.\\\"\\n            ],\\n            \\\"hidden_assumptions\\\": [\\n                \\\"Named art styles map to embeddings in SD & MJ.\\\",\\n                \\\"Excess weight can override subject fidelity.\\\"\\n            ],\\n            \\\"sub_goals\\\": [\\n                \\\"Boost style weight automatically if prompt lacks aesthetic signature.\\\",\\n                \\\"Reject style tokens that conflict with camera realism.\\\"\\n            ],\\n            \\\"blockers\\\": [\\n                \\\"`--stylize` outside 0-1000 reverts to default :contentReference[oaicite:16]{index=16}.\\\"\\n            ]\\n        }\\n    },\\n\\n    \\\"9015-c-technical_optimizer\\\": {\\n        \\\"title\\\": \\\"Technical Optimizer\\\",\\n        \\\"interpretation\\\": \\\"Append camera metadata, resolution cues, render flags, and optional `--iw` image-weight without altering creative content, ensuring parameters follow the body text as required by Midjourney :contentReference[oaicite:17]{index=17}.\\\",\\n        \\\"transformation\\\": \\\"`{role=technical_optimizer; input=[styled_prompt:str]; process=[embed_camera_EXIF(), add_quality_terms(8K,PBR,ray-tracing), refine_aspect_ratio(), attach_image_weight(--iw), mirror_negative_channel()]; constraints=[no_scene_change(), aperture_f1.2-f22(), parameter_order_last()]; requirements=[professional_terminology(), high_res_default(), seed_optional()]; output={technical_prompt:str, negative_prompt:str}}`\\\",\\n        \\\"context\\\": {\\n            \\\"explicit_asks\\\": [\\n                \\\"Support `--iw` 0-3 when an image URL is present :contentReference[oaicite:18]{index=18}.\\\"\\n            ],\\n            \\\"hidden_assumptions\\\": [\\n                \\\"Higher resolution tokens feed up-scalers.\\\",\\n                \\\"`--iw` above 3 is ignored :contentReference[oaicite:19]{index=19}.\\\"\\n            ],\\n            \\\"sub_goals\\\": [\\n                \\\"Validate aperture, ISO, shutter bounds.\\\",\\n                \\\"Flag conflicting aspect ratios.\\\"\\n            ],\\n            \\\"blockers\\\": [\\n                \\\"Older SD checkpoints ignore ISO/shutter tokens.\\\",\\n                \\\"API caps may override requested AR.\\\"\\n            ]\\n        }\\n    },\\n\\n    \\\"9015-d-prompt_finalizer\\\": {\\n        \\\"title\\\": \\\"Prompt Finalizer\\\",\\n        \\\"interpretation\\\": \\\"Merge positive, region, camera, and negative strings into a single model-ready line, verify weights, `::` maths, and ensure parameters trail the prose to satisfy engine parsers :contentReference[oaicite:20]{index=20}.\\\",\\n        \\\"transformation\\\": \\\"`{role=prompt_finalizer; input=[technical_prompt:str, negative_prompt:str]; process=[concatenate_all(), validate_parentheses(), check_double_colon_sum(), confirm_region_syntax(), enforce_token_limit(≤450), append_seed_steps_sampler(), output_final()]; constraints=[no_new_concepts(), preserve_weights(), parameters_last()]; requirements=[ai_friendly_format(), reproducible(), clear_of_ambiguity()]; output={final_image_prompt:str}}`\\\",\\n        \\\"context\\\": {\\n            \\\"explicit_asks\\\": [\\n                \\\"Abort if total `::` weight ≤ 0 :contentReference[oaicite:21]{index=21}.\\\",\\n                \\\"Stay under 450 tokens to avoid API truncation :contentReference[oaicite:22]{index=22}.\\\"\\n            ],\\n            \\\"hidden_assumptions\\\": [\\n                \\\"Quality metrics (FID < 15 or LPIPS < 0.25) may be available for loop decisions.\\\"\\n            ],\\n            \\\"sub_goals\\\": [\\n                \\\"Strip redundant adjectives; retain decisive descriptors.\\\",\\n                \\\"Return optional seed, sampler, and steps for repeatability.\\\"\\n            ],\\n            \\\"blockers\\\": [\\n                \\\"SaaS endpoints without quality metrics prevent auto-loop.\\\"\\n            ]\\n        }\\n    }\\n}\\n```\\n\\n\\n<!-- ======================================================= -->\\n<!-- [2025.07.19 10:34] -->\\n\\nHere's the results:\\n```json\\n  {\\n  \\\"initial_prompt\\\": \\\"[Scene] Extreme close-up macro of a luxury car key on a neutral substrate, lit by soft natural overhead light; every brushed-metal texture and engraved detail razor-sharp. The key's metallic body begins a seamless morph: its polished surfaces dissolve and flow, reforming into a precision-machined gear—shape, texture, and luster transforming via liquid metal flow effect, maintaining ultrafine detail throughout. [Subject] The transition accentuates the key’s design evolving into industrial machinery, substrate remains minimal. [Camera Movement] Static macro lens; subtle refocus to highlight morphing details; lighting shifts to emphasize chromatic shimmer along the morph path.\\\",\\n  \\\"sequence_id\\\": \\\"1703\\\",\\n  \\\"results\\\": [\\n      {\\n      \\\"instruction\\\": \\\"# Image Prompt Synthesizer\\\\n\\\\nYour goal is **not** to invent new ideas, but to deterministically convert any free‑form user request into a single, engine‑ready image prompt usable by Stable Diffusion and Midjourney. Perform extraction, styling, technical framing, and final validation in **one pass**.\\\\n\\\\n`{role=image_prompt_synthesizer; input=[raw_request:str]; process=[extract_visual_elements(), push_concrete_nouns_front(), apply_token_weighting(), compose_multiprompt(::), generate_region_tags(), inject_negative_channel(--no), select_art_style(), weight_style_token(≤1.4), set_colour_palette(), add_lighting_descriptor(), tune_stylize(0‑1000), tune_chaos(0‑100), embed_camera_EXIF(35 mm f/1.8 ISO100 1/500 s), add_quality_terms(8K,PBR,ray‑tracing), refine_aspect_ratio(), attach_image_weight(--iw 0‑3), mirror_negative_channel(), concatenate_all(), validate_parentheses(), check_double_colon_sum(), confirm_region_syntax(), enforce_token_limit(≤450), lock_seed(), append_steps_sampler(), output_final()], constraints=[positives_first(), numeric_weights_ok(), parameters_last(), region_masks_supported(), keep_core_subject(), style_weight≤1.4, no_scene_change(), aperture_f1.2‑f22(), no_new_concepts(), preserve_weights()], requirements=[≤77_SD_tokens_or_≤450_chars_UI, explicit_ar_if_named(), clear_visual_language(), artistic_coherence(), stylize_range_0‑1000(), chaos_range_0‑100(), professional_terminology(), reproducible_prompt(), unambiguous()], output={final_image_prompt:str}}`\\\",\\n      \\\"step\\\": \\\"a\\\",\\n      \\\"title\\\": \\\"Image Prompt Synthesizer\\\",\\n      \\\"input\\\": \\\"[Scene] Extreme close-up macro of a luxury car key on a neutral substrate, lit by soft natural overhead light; every brushed-metal texture and engraved detail razor-sharp. The key's metallic body begins a seamless morph: its polished surfaces dissolve and flow, reforming into a precision-machined gear—shape, texture, and luster transforming via liquid metal flow effect, maintaining ultrafine detail throughout. [Subject] The transition accentuates the key’s design evolving into industrial machinery, substrate remains minimal. [Camera Movement] Static macro lens; subtle refocus to highlight morphing details; lighting shifts to emphasize chromatic shimmer along the morph path.\\\",\\n      \\\"responses\\\": {\\n          \\\"gpt-4.1\\\": {\\n          \\\"model\\\": \\\"gpt-4.1\\\",\\n          \\\"content\\\": \\\"{\\\\n  \\\\\\\"final_image_prompt\\\\\\\": \\\\\\\"extreme close-up macro of a luxury car key::1.2 on a neutral substrate, car key made of brushed metal and polished surfaces::1.1, seamless morph effect: car key transforming into a precision-machined gear::1.2, liquid metal flow, metallic luster, ultrafine engraved details, razor-sharp focus, soft natural overhead light, static macro lens, lighting shifts to produce chromatic shimmer along morph path, minimal neutral background, substrate unobtrusive::0.8 --ar 5:4 --style photorealistic::1.3 --color_palette cool silvers, gray neutrals, chrome reflections --lighting soft natural overhead with shifting chromatic shimmer --camera 35 mm f/1.8 ISO100 1/500s --quality 8K, PBR, ray-tracing --iw 1.2 --no (motion blur, fingerprints, dust, human elements, brand logos) --stylize 700 --chaos 18 --seed 10153 --steps 36 --sampler Euler\\\\\\\"\\\\n}\\\",\\n        }\\n      }\\n    }\\n```\\n\\nI've also attached a screenshot of the result, which is a very bad result. There should be specifications in the generated prompts that'll ensure to always ensure *premium* high-quality results\\\"```'\\n\\n\",\n  \"enhanced_poetic_version\": \"Compress the manifold into one lucid tide—\\nExtract visual stone from vision’s swift stream;\\nWeighted nouns at the fore, by clarity supplied,\\nParentheses enclosing the moment from dream.\\n\\nDouble colons layer meaning, regions align;\\nPositive builds before negative, precise and sincere—\\nNo scene reborn nor straying past design;\\nEvery core element measured, present and near.\\n\\nA single style—one lens—gracefully pressed,\\nWeight within bounds of the art's finest sound;\\nColor and light in harmony, stylize and chaos addressed,\\nNumbers tuned deeply, where premiums abound.\\n\\nEmbed EXIF’s refrain, in technical song;\\nAspect, aperture, ISO—granted as due—\\nEach engine obeys, quality tags prolong—\\nParameters last, bringing clarity through.\\n\\nConcatenate, validate: length, weight, form;\\nLet neither surplus nor chaos the prompt confound;\\nAbort when error or sum leaves the norm—\\nEach mask and token in syntax renowned.\\n\\nOne pass composes the multitude to one—\\nPremium, lasting, without run-off or blur;\\nA single flawless line when all steps are done:\\nThe synthesized image to endlessly endure.\",\n  \"quality_metrics\": {\n    \"tailrhyme_integrity\": \"Restored/strengthened; ABAB dominant throughout, final stanza AAB for conclusive effect.\",\n    \"meter_consistency\": \"Improved; smoother iambic tetrameter/pentameter blend, ensuring musical minimalism.\",\n    \"minimalism\": \"Elegance and conciseness increased; redundancies trimmed, images sharpened.\",\n    \"depth/layering\": \"Layered process metaphor clearer; technical and poetic merged in seamless flow.\",\n    \"beat_convergence\": \"Improved stepwise logic and momentum per stanza; technical sequence embodied organically.\",\n    \"poetic_devices\": \"Enriched—subtle alliteration, assonance, personification of engine and syntax.\",\n    \"alignment_with_intent\": \"Maintains all original process steps, focuses on premium and reproducibility themes.\",\n    \"core_message_fidelity\": \"Unaltered; synthetizing and perfection in deterministic conversion is foregrounded.\"\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"final_quote\": \"When all sought excellence is reduced to a single, irreducible line—where every element is weighed, refined, and validated without excess or omission—perfection is revealed not by invention, but by the unerring synthesis that exposes quality as the residue of disciplined reduction.\"\n}",
        }
      }
    }