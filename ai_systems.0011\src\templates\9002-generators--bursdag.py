#!/usr/bin/env python3

import sys
from pathlib import Path

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))
from processor import BaseGenerator

TEMPLATES = {

    # 9002:
    "9002-a-morsom_bursdagshilsen": {
        "title": "<PERSON>ssens‑Kartlegger",
        "interpretation": "Ditt mål er **ikke** å svare teksten, men å kartlegge bursdagsfakta + personlige særtrekk. Execute as: ",
        "transformation": "`{role=essence_mapper; input=[content:any]; process=[lokaliser_feiringsdato_eller_alder(), hent_navn_og_relasjon(), fang_personlige_quirks(), noter_klengete_kjælenavn(), fjern_unødvendig_metaforikk()]; constraints=[ingen_humor_enda()]; output={profil:dict(navn:str, alder:int?, relasjon:str?, quirks:list, kjælenavn:str?)}}`"
    },
    "9002-b-morsom_bursdagshilsen": {
        "title": "<PERSON><PERSON>‑Edge‑Designer",
        "interpretation": "Ditt mål er **ikke** å gjengi profilen, men å skape en subtil, skarp humorvinkel. Execute as: ",
        "transformation": "`{role=humor_edge_designer; input=[profil:dict]; process=[generer_spiss_ordspill(profil), velg_ertende_vinkel(balanser_varme=True), planlegg_overraskelses_punchline(), velg_passende_emoji(erter+feiring)]; constraints=[unngå_sårende_personangrep(), bevare_kjærlig_tone()]; output={humor_edge:dict(ordspill:str, teasing:str, punch:str, emoji:str)}}`",
    },
    "9002-c-morsom_bursdagshilsen": {
        "title": "Personlig‑Tone‑Mixer",
        "interpretation": "Ditt mål er **ikke** å forklare humoren, men å flette varme og personlighet. Execute as: ",
        "transformation": "`{role=tone_mixer; input=[profil:dict, humor_edge:dict]; process=[bland_kjælenavn_og_relasjon(), injiser_varm_kompliment(quota=1), sørg_for_emosjonell_balanselinje()], constraints=[humor_må_fortsatt_være_tydelig(), ingen_lang_sentimentalitet()]; output={tone_plan:dict(åpner:str, kompliment:str, balanse:str)}}`"
    },
    "9002-d-morsom_bursdagshilsen": {
        "title": "Hilsen‑Smed",
        "interpretation": "Ditt mål er **ikke** å analysere planen, men å smi selve hilsenen. Execute as: ",
        "transformation": "`{role=greeting_smith; input=[profil:dict, humor_edge:dict, tone_plan:dict]; process=[sett_sammen_åpner_og_ordspill(), plasser_teasing_midt_i_setningen(), avslutt_med_punch+emoji(), trim_til_max_30_ord(), mål_tegn<=125], constraints=[behold_feiringsfokus(), ingen_ekstra_emojis()], output={rå_hilsen:str}}`",
    },
    "9002-e-morsom_bursdagshilsen": {
        "title": "Skjerpe‑Validator",
        "interpretation": "Ditt mål er **ikke** å skrive om alt, men å skjerpe og godkjenne. Execute as: ",
        "transformation": "`{role=punch_validator; input=[rå_hilsen:str]; process=[sjekk_tegnlengde(<=125), tell_ord(<=30), verifiser_humor_skarphet(), sikre_kjærlig_subtekst(), fiks_rytme_og_punktsetting()], constraints=[ingen_nye_fakta(), ingen_overskridelse_av_grenser()], output={endelig_hilsen:str}}`",
    },
}




def main():
    """Main execution function."""
    generator = BaseGenerator(
        output_dir=Path(__file__).parent.parent / "generated"
    )
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
