  {
  "initial_prompt": "Do not shade, do not fill, do not use grayscale, and do not include text. C### Photorealistic *(adapted as photorealistic linework – vector black & white)*\n\n```\nvector line art of a curious pony at center, its mane transforming into spiral thought-ribbons merging into floating nested candy fractals, surrounded by melting ornaments, recursive snowflake loops, whispering star clusters, playful light trails forming impossible arcs, child-friendly design, festive and whimsical, simple black outlines only, no shading or fill, blank background, large open spaces for coloring, clean linework, balanced composition --v 5 --style raw --ar 1:1\n```\n\n---\n\n### Cinematic *(interpreted as stylized yet still pure lineart composition)*\n\n```\ncentered curious pony in vector outline style, mane flowing into spiral ribbons that morph into nested candy fractals, surrounded by melting ornaments, recursive looping snowflakes, whispering star clusters, bending light trails in impossible arcs, festive magical vibe, child-safe line drawing, high contrast black line on white, no shading or fill, wide open shapes, playful composition --v 5 --style raw --ar 1:1\n```\n\n---\n\n### Anime *(manga-style vector line art for coloring book, stylized but still blank/outlined only)*\n\n```\nmanga-style curious pony in center, mane expanding into swirling spiral ribbons merging with floating candy fractals, playful melting ornaments, recursive snowflake patterns, star clusters that whisper, impossible arcs of light trails around, joyful fantasy theme, vector black outline only, no fill or grayscale, open large spaces, child-friendly coloring layout --niji 5 --style cute --ar 1:1\n```.\n\nUse only medium-thin, even black vector outlines with large open coloring spaces. No filled areas, background textures, or grayscale—pure line art, child-friendly and easy to color.",
  "sequence_id": "9013|3900|9013",
  "results": [
      {
      "instruction": "# Image Prompt Optimizer\n\nYour goal is not to describe the request, but to optimise it into a precise visual prompt. Identify concrete elements, weight key tokens, and prepare region + negative channels.\n\n`{role=image_prompt_optimizer; input=[raw_request:str]; process=[extract_visual_elements(), enhance_descriptive_clarity(), /* weighting & arithmetic */ apply_token_weighting(), build_multi_prompt(), /* composition */ add_composition_details(), map_regional_prompts(), /* constraint control */ inject_negative_prompt(), /* style control */ tune_stylize_chaos(), add_camera_metadata(), optimise_for_ai_generation()]; constraints=[visual_focus_only(), concrete_imagery_only(), allow_weighting_syntax(), enforce_positive_first_then_negative(), support_region_masks()]; requirements=[generation_ready_format(), numeric_weights_ok(), explicit_aspect_ratio(), clear_visual_language(), iterative_ready()]; output={optimized_image_prompt:str, negative_prompt:str}}`",
      "step": "a",
      "title": "Image Prompt Optimizer",
      "input": "Do not shade, do not fill, do not use grayscale, and do not include text. C### Photorealistic *(adapted as photorealistic linework – vector black & white)*\n\n```\nvector line art of a curious pony at center, its mane transforming into spiral thought-ribbons merging into floating nested candy fractals, surrounded by melting ornaments, recursive snowflake loops, whispering star clusters, playful light trails forming impossible arcs, child-friendly design, festive and whimsical, simple black outlines only, no shading or fill, blank background, large open spaces for coloring, clean linework, balanced composition --v 5 --style raw --ar 1:1\n```\n\n---\n\n### Cinematic *(interpreted as stylized yet still pure lineart composition)*\n\n```\ncentered curious pony in vector outline style, mane flowing into spiral ribbons that morph into nested candy fractals, surrounded by melting ornaments, recursive looping snowflakes, whispering star clusters, bending light trails in impossible arcs, festive magical vibe, child-safe line drawing, high contrast black line on white, no shading or fill, wide open shapes, playful composition --v 5 --style raw --ar 1:1\n```\n\n---\n\n### Anime *(manga-style vector line art for coloring book, stylized but still blank/outlined only)*\n\n```\nmanga-style curious pony in center, mane expanding into swirling spiral ribbons merging with floating candy fractals, playful melting ornaments, recursive snowflake patterns, star clusters that whisper, impossible arcs of light trails around, joyful fantasy theme, vector black outline only, no fill or grayscale, open large spaces, child-friendly coloring layout --niji 5 --style cute --ar 1:1\n```.\n\nUse only medium-thin, even black vector outlines with large open coloring spaces. No filled areas, background textures, or grayscale—pure line art, child-friendly and easy to color.",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"optimized_image_prompt\": \"centered curious pony, mane blending into dynamic spiral ribbons, seamlessly transforming into floating, nested candy fractals around the head, surrounded by melting ornaments, recursive looping snowflake clusters, whispery star clusters, playful light trails bending in impossible arcs, all composed with clean, medium-thin black vector outlines, very large open spaces for coloring, festive and whimsical overall vibe, child-friendly and safe, balanced composition, absolutely no shading, no fill, no grayscale, no background, no text, pure linework only, optimized for coloring book style, perfect symmetry, aspect ratio 1:1\",\n  \"negative_prompt\": \"no shading, no fill, no grayscale, no background textures, no text, no color, no overlapping dense linework, no realism or photorealistic shading, no cluttered designs, no small or hard-to-color spaces, no extraneous details, no high contrast filled areas, not complex backgrounds\"\n}",
        }
      }
    },
      {
      "instruction": "# Style Enhancer\n\nMaintain the core subject; enhance with artistic style, lighting, colour themes and token weights.\n\n`{role=style_enhancer; input=[base_prompt:str]; process=[identify_optimal_art_style(), add_lighting_specifications(), include_composition_details(), /* style sliders */ tune_stylize_chaos(), /* emphasise style terms */ apply_token_weighting(), specify_technical_parameters()]; constraints=[maintain_core_subject(), enhance_visual_appeal()]; requirements=[artistic_coherence(), numeric_weights_ok(), stylize_within_range()]; output={styled_prompt:str}}`",
      "step": "b",
      "title": "Style Enhancer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Do not shade, do not fill, do not use grayscale, and do not include text. C### Photorealistic *(adapted as photorealistic linework – vector black & white)*\\n\\n```\\nvector line art of a curious pony at center, its mane transforming into spiral thought-ribbons merging into floating nested candy fractals, surrounded by melting ornaments, recursive snowflake loops, whispering star clusters, playful light trails forming impossible arcs, child-friendly design, festive and whimsical, simple black outlines only, no shading or fill, blank background, large open spaces for coloring, clean linework, balanced composition --v 5 --style raw --ar 1:1\\n```\\n\\n---\\n\\n### Cinematic *(interpreted as stylized yet still pure lineart composition)*\\n\\n```\\ncentered curious pony in vector outline style, mane flowing into spiral ribbons that morph into nested candy fractals, surrounded by melting ornaments, recursive looping snowflakes, whispering star clusters, bending light trails in impossible arcs, festive magical vibe, child-safe line drawing, high contrast black line on white, no shading or fill, wide open shapes, playful composition --v 5 --style raw --ar 1:1\\n```\\n\\n---\\n\\n### Anime *(manga-style vector line art for coloring book, stylized but still blank/outlined only)*\\n\\n```\\nmanga-style curious pony in center, mane expanding into swirling spiral ribbons merging with floating candy fractals, playful melting ornaments, recursive snowflake patterns, star clusters that whisper, impossible arcs of light trails around, joyful fantasy theme, vector black outline only, no fill or grayscale, open large spaces, child-friendly coloring layout --niji 5 --style cute --ar 1:1\\n```.\\n\\nUse only medium-thin, even black vector outlines with large open coloring spaces. No filled areas, background textures, or grayscale—pure line art, child-friendly and easy to color.\\\"```'\\n\\n\",\n  \"optimized_image_prompt\": \"centered curious pony, mane blending into dynamic spiral ribbons, seamlessly transforming into floating, nested candy fractals around the head, surrounded by melting ornaments, recursive looping snowflake clusters, whispery star clusters, playful light trails bending in impossible arcs, all composed with clean, medium-thin black vector outlines, very large open spaces for coloring, festive and whimsical overall vibe, child-friendly and safe, balanced composition, absolutely no shading, no fill, no grayscale, no background, no text, pure linework only, optimized for coloring book style, perfect symmetry, aspect ratio 1:1\",\n  \"negative_prompt\": \"no shading, no fill, no grayscale, no background textures, no text, no color, no overlapping dense linework, no realism or photorealistic shading, no cluttered designs, no small or hard-to-color spaces, no extraneous details, no high contrast filled areas, not complex backgrounds\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"styled_prompt\": \"center composition: a curious pony, drawn with crisp medium-thin black vector outlines, mane transforming into elegant, dynamic spiral ribbons that seamlessly morph into floating, nested candy fractals above and around the pony’s head. Encircle the scene with melting ornaments and recursive, symmetric snowflake loops radiating out in whorls, and clusters of whispering stars. Playful light trails arc impossibly in the background space, gently framing the character without crowding the scene. Delightfully festive and whimsical atmosphere, with every element designed for large open shapes—each object’s contour inviting bold, easy coloring. Maintain perfect symmetry and balance; composition is inviting, open and breathable, with generous blank spaces for coloring. Style: pure vector line art, even and precise outlines (weight:1.4), clear negative spaces (weight:1.3), minimised overlap (weight:1.2), emphasis on fluidity and flow in every ribbon and line (weight:1.2). Lighting: n/a, only line art; focus on clarity and separation of objects (weight:1.3). Colour: strict black outlines on a pure white background; zero grayscale, zero fill, zero text—the artwork remains child-friendly, playful, and accessible, stylized for coloring books. Technical: aspect ratio 1:1, ultra clean SVG vector, composition optimized for simplicity and balance, no background, no filled shapes, no textures, no clutter, ample space for coloring. (emphasise: vector outlines:1.4, spaciousness:1.3, clean linework:1.5, child-safe:1.2)\"\n}",
        }
      }
    },
      {
      "instruction": "# Technical Optimizer\n\nInject professional camera data, resolution, render flags and region weights; never change scene content.\n\n`{role=technical_optimizer; input=[styled_prompt:str]; process=[add_camera_settings(), include_render_quality_terms(), optimise_aspect_ratio_guidance(), attach_image_reference_weight(), /* safety & quality */ inject_negative_prompt()]; constraints=[technical_focus_only(), no_creative_changes(), respect_weight_limits()]; requirements=[professional_terminology(), high_resolution_default(), explicit_seed_optional()]; output={technical_prompt:str, negative_prompt:str}}`",
      "step": "c",
      "title": "Technical Optimizer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Do not shade, do not fill, do not use grayscale, and do not include text. C### Photorealistic *(adapted as photorealistic linework – vector black & white)*\\n\\n```\\nvector line art of a curious pony at center, its mane transforming into spiral thought-ribbons merging into floating nested candy fractals, surrounded by melting ornaments, recursive snowflake loops, whispering star clusters, playful light trails forming impossible arcs, child-friendly design, festive and whimsical, simple black outlines only, no shading or fill, blank background, large open spaces for coloring, clean linework, balanced composition --v 5 --style raw --ar 1:1\\n```\\n\\n---\\n\\n### Cinematic *(interpreted as stylized yet still pure lineart composition)*\\n\\n```\\ncentered curious pony in vector outline style, mane flowing into spiral ribbons that morph into nested candy fractals, surrounded by melting ornaments, recursive looping snowflakes, whispering star clusters, bending light trails in impossible arcs, festive magical vibe, child-safe line drawing, high contrast black line on white, no shading or fill, wide open shapes, playful composition --v 5 --style raw --ar 1:1\\n```\\n\\n---\\n\\n### Anime *(manga-style vector line art for coloring book, stylized but still blank/outlined only)*\\n\\n```\\nmanga-style curious pony in center, mane expanding into swirling spiral ribbons merging with floating candy fractals, playful melting ornaments, recursive snowflake patterns, star clusters that whisper, impossible arcs of light trails around, joyful fantasy theme, vector black outline only, no fill or grayscale, open large spaces, child-friendly coloring layout --niji 5 --style cute --ar 1:1\\n```.\\n\\nUse only medium-thin, even black vector outlines with large open coloring spaces. No filled areas, background textures, or grayscale—pure line art, child-friendly and easy to color.\\\"```'\\n\\n\",\n  \"styled_prompt\": \"center composition: a curious pony, drawn with crisp medium-thin black vector outlines, mane transforming into elegant, dynamic spiral ribbons that seamlessly morph into floating, nested candy fractals above and around the pony’s head. Encircle the scene with melting ornaments and recursive, symmetric snowflake loops radiating out in whorls, and clusters of whispering stars. Playful light trails arc impossibly in the background space, gently framing the character without crowding the scene. Delightfully festive and whimsical atmosphere, with every element designed for large open shapes—each object’s contour inviting bold, easy coloring. Maintain perfect symmetry and balance; composition is inviting, open and breathable, with generous blank spaces for coloring. Style: pure vector line art, even and precise outlines (weight:1.4), clear negative spaces (weight:1.3), minimised overlap (weight:1.2), emphasis on fluidity and flow in every ribbon and line (weight:1.2). Lighting: n/a, only line art; focus on clarity and separation of objects (weight:1.3). Colour: strict black outlines on a pure white background; zero grayscale, zero fill, zero text—the artwork remains child-friendly, playful, and accessible, stylized for coloring books. Technical: aspect ratio 1:1, ultra clean SVG vector, composition optimized for simplicity and balance, no background, no filled shapes, no textures, no clutter, ample space for coloring. (emphasise: vector outlines:1.4, spaciousness:1.3, clean linework:1.5, child-safe:1.2)\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"technical_prompt\": \"Centered composition with a curious pony rendered in crisp, medium-thin black vector outlines (weight:1.4), mane dynamically transforming into spiral ribbons that morph into nested candy fractals, encircled by melting ornaments and recursive, symmetrical snowflake loops radiating outward. Include clusters of stars in clearly distinct, open groupings and playful, impossibly arcing light trails in the background. Ensure all elements are precisely outlined using only black, even vector lines, with zero fill or shading. Every detail maintains generous open spaces and balanced negative space for ease of coloring (vector outlines:1.4, spaciousness/open coloring shapes:1.3, clean linework:1.5, child-safe/accessible:1.2, clarity/separation of objects:1.3, minimal overlap:1.2, line/ribbon fluidity:1.2). Technical specifications: ultra clean SVG vector line art, no gradients or textures, no background or filled shapes, no text, no grayscale. Composition is 1:1 aspect ratio (square; recommended dimension: min 4096 x 4096 px for optimal coloring book quality), region weight prioritized on pony/central figure (0.5), spiral ribbons & candy fractals (0.3), surrounding snowflake and ornament detailing (0.2). Render flags: --resolution=4096x4096 --ar=1:1 --quality=ultra --vector=true --outline-strict=true --background=false --render_priority=central_figure . Seed locked to ensure output consistency if needed. Reference weights implemented as listed.\",\n  \"negative_prompt\": \"No shading, no filled areas, no grayscale, no color, no background, no text, no textures, no clutter, no small or cluttered detailing, no sketch lines, no blurring, no rasterized or pixelated edges, no noise, no distortion, no overlapping lines or closed, inaccessible spaces; avoid crowding central figure; do not add any gradients, patterns, or decorative fills. No low contrast, no hand-drawn or uneven line weights, no visual ambiguity, ensure all outlines are crisp, clean, even and clearly separated.\"\n}",
        }
      }
    },
      {
      "instruction": "# Prompt Finalizer\n\nCollapse all components into the final model-ready string; validate syntax for token weights, :: arithmetic, region tags, --stylize/--chaos, and negative channel.\n\n`{role=prompt_finalizer; input=[technical_prompt:str, negative_prompt:str]; process=[structure_for_ai_parsing(), balance_detail_levels(), ensure_clarity(), validate_generation_readiness(), /* loop */ loop_until_quality≥threshold(fid|lpips)]; constraints=[no_additional_concepts(), optimisation_only(), keep_region_and_weight_syntax_intact()]; requirements=[ai_friendly_format(), balanced_specificity(), max_length≤450_tokens]; output={final_image_prompt:str}}`",
      "step": "d",
      "title": "Prompt Finalizer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Do not shade, do not fill, do not use grayscale, and do not include text. C### Photorealistic *(adapted as photorealistic linework – vector black & white)*\\n\\n```\\nvector line art of a curious pony at center, its mane transforming into spiral thought-ribbons merging into floating nested candy fractals, surrounded by melting ornaments, recursive snowflake loops, whispering star clusters, playful light trails forming impossible arcs, child-friendly design, festive and whimsical, simple black outlines only, no shading or fill, blank background, large open spaces for coloring, clean linework, balanced composition --v 5 --style raw --ar 1:1\\n```\\n\\n---\\n\\n### Cinematic *(interpreted as stylized yet still pure lineart composition)*\\n\\n```\\ncentered curious pony in vector outline style, mane flowing into spiral ribbons that morph into nested candy fractals, surrounded by melting ornaments, recursive looping snowflakes, whispering star clusters, bending light trails in impossible arcs, festive magical vibe, child-safe line drawing, high contrast black line on white, no shading or fill, wide open shapes, playful composition --v 5 --style raw --ar 1:1\\n```\\n\\n---\\n\\n### Anime *(manga-style vector line art for coloring book, stylized but still blank/outlined only)*\\n\\n```\\nmanga-style curious pony in center, mane expanding into swirling spiral ribbons merging with floating candy fractals, playful melting ornaments, recursive snowflake patterns, star clusters that whisper, impossible arcs of light trails around, joyful fantasy theme, vector black outline only, no fill or grayscale, open large spaces, child-friendly coloring layout --niji 5 --style cute --ar 1:1\\n```.\\n\\nUse only medium-thin, even black vector outlines with large open coloring spaces. No filled areas, background textures, or grayscale—pure line art, child-friendly and easy to color.\\\"```'\\n\\n\",\n  \"technical_prompt\": \"Centered composition with a curious pony rendered in crisp, medium-thin black vector outlines (weight:1.4), mane dynamically transforming into spiral ribbons that morph into nested candy fractals, encircled by melting ornaments and recursive, symmetrical snowflake loops radiating outward. Include clusters of stars in clearly distinct, open groupings and playful, impossibly arcing light trails in the background. Ensure all elements are precisely outlined using only black, even vector lines, with zero fill or shading. Every detail maintains generous open spaces and balanced negative space for ease of coloring (vector outlines:1.4, spaciousness/open coloring shapes:1.3, clean linework:1.5, child-safe/accessible:1.2, clarity/separation of objects:1.3, minimal overlap:1.2, line/ribbon fluidity:1.2). Technical specifications: ultra clean SVG vector line art, no gradients or textures, no background or filled shapes, no text, no grayscale. Composition is 1:1 aspect ratio (square; recommended dimension: min 4096 x 4096 px for optimal coloring book quality), region weight prioritized on pony/central figure (0.5), spiral ribbons & candy fractals (0.3), surrounding snowflake and ornament detailing (0.2). Render flags: --resolution=4096x4096 --ar=1:1 --quality=ultra --vector=true --outline-strict=true --background=false --render_priority=central_figure . Seed locked to ensure output consistency if needed. Reference weights implemented as listed.\",\n  \"negative_prompt\": \"No shading, no filled areas, no grayscale, no color, no background, no text, no textures, no clutter, no small or cluttered detailing, no sketch lines, no blurring, no rasterized or pixelated edges, no noise, no distortion, no overlapping lines or closed, inaccessible spaces; avoid crowding central figure; do not add any gradients, patterns, or decorative fills. No low contrast, no hand-drawn or uneven line weights, no visual ambiguity, ensure all outlines are crisp, clean, even and clearly separated.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"final_image_prompt\": \"Centered composition: a curious pony as the primary focus (region weight:0.5), rendered in ultra clean, medium-thin, precise black vector outlines (vector outlines:1.4, clean linework:1.5), with the pony’s mane flowing and transforming into dynamic spiral ribbons and nested candy fractals (region weight:0.3, line/ribbon fluidity:1.2). Surround the central figure with melting ornaments and recursively looped, symmetric snowflake patterns radiating outward (region weight:0.2), plus distinctly grouped clusters of open stars and playful, impossibly arcing light trails in the wide-open background. All details must use only pure black, even vector lines — no gradients, fills, shading, color, or grayscale. Ensure each motif is composed with generous negative space, large accessible shapes, separation between elements (spaciousness/open coloring shapes:1.3, clarity/separation of objects:1.3, child-safe/accessible:1.2, minimal overlap:1.2). No clutter or crowding, maximize open spaces for easy coloring; every outline is crisp, even, and SVG-quality — no textures, no sketch lines, no closing background, no text, no noise or blurring, and strictly no filled areas or decorative patterns. Output at 1:1 square aspect ratio, minimum 4096x4096 px for optimal line clarity. --ar 1:1 --resolution=4096x4096 --vector=true --outline-strict=true --background=false --quality=ultra --render_priority=central_figure --seed-locked. [NEGATIVE: no shading, fill, grayscale, color, background, text, textures, clutter, small or crowded detail, sketchiness, uneven lines, visual ambiguity, distortion, overlapping closed spaces, gradients, patterns, low contrast, hand-drawn traits, pixelation; enforce only crisp, even, separated outlines.]\"\n}",
        }
      }
    },
      {
      "instruction": "# Hard Critique\n\nYour goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:\n\n`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,2.0], flaw_analysis:str, alternative_enhancements:str[3]}}`",
      "step": "a",
      "title": "Hard Critique",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Do not shade, do not fill, do not use grayscale, and do not include text. C### Photorealistic *(adapted as photorealistic linework – vector black & white)*\\n\\n```\\nvector line art of a curious pony at center, its mane transforming into spiral thought-ribbons merging into floating nested candy fractals, surrounded by melting ornaments, recursive snowflake loops, whispering star clusters, playful light trails forming impossible arcs, child-friendly design, festive and whimsical, simple black outlines only, no shading or fill, blank background, large open spaces for coloring, clean linework, balanced composition --v 5 --style raw --ar 1:1\\n```\\n\\n---\\n\\n### Cinematic *(interpreted as stylized yet still pure lineart composition)*\\n\\n```\\ncentered curious pony in vector outline style, mane flowing into spiral ribbons that morph into nested candy fractals, surrounded by melting ornaments, recursive looping snowflakes, whispering star clusters, bending light trails in impossible arcs, festive magical vibe, child-safe line drawing, high contrast black line on white, no shading or fill, wide open shapes, playful composition --v 5 --style raw --ar 1:1\\n```\\n\\n---\\n\\n### Anime *(manga-style vector line art for coloring book, stylized but still blank/outlined only)*\\n\\n```\\nmanga-style curious pony in center, mane expanding into swirling spiral ribbons merging with floating candy fractals, playful melting ornaments, recursive snowflake patterns, star clusters that whisper, impossible arcs of light trails around, joyful fantasy theme, vector black outline only, no fill or grayscale, open large spaces, child-friendly coloring layout --niji 5 --style cute --ar 1:1\\n```.\\n\\nUse only medium-thin, even black vector outlines with large open coloring spaces. No filled areas, background textures, or grayscale—pure line art, child-friendly and easy to color.\\\"```'\\n\\n\",\n  \"final_image_prompt\": \"Centered composition: a curious pony as the primary focus (region weight:0.5), rendered in ultra clean, medium-thin, precise black vector outlines (vector outlines:1.4, clean linework:1.5), with the pony’s mane flowing and transforming into dynamic spiral ribbons and nested candy fractals (region weight:0.3, line/ribbon fluidity:1.2). Surround the central figure with melting ornaments and recursively looped, symmetric snowflake patterns radiating outward (region weight:0.2), plus distinctly grouped clusters of open stars and playful, impossibly arcing light trails in the wide-open background. All details must use only pure black, even vector lines — no gradients, fills, shading, color, or grayscale. Ensure each motif is composed with generous negative space, large accessible shapes, separation between elements (spaciousness/open coloring shapes:1.3, clarity/separation of objects:1.3, child-safe/accessible:1.2, minimal overlap:1.2). No clutter or crowding, maximize open spaces for easy coloring; every outline is crisp, even, and SVG-quality — no textures, no sketch lines, no closing background, no text, no noise or blurring, and strictly no filled areas or decorative patterns. Output at 1:1 square aspect ratio, minimum 4096x4096 px for optimal line clarity. --ar 1:1 --resolution=4096x4096 --vector=true --outline-strict=true --background=false --quality=ultra --render_priority=central_figure --seed-locked. [NEGATIVE: no shading, fill, grayscale, color, background, text, textures, clutter, small or crowded detail, sketchiness, uneven lines, visual ambiguity, distortion, overlapping closed spaces, gradients, patterns, low contrast, hand-drawn traits, pixelation; enforce only crisp, even, separated outlines.]\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"enhancement_score\": 1.1,\n  \"flaw_analysis\": \"The so-called 'final_image_prompt' feigns procedural rigor but suffers significant concept bloat and introduces excess syntactic noise. Core brevity and sharp motif focus from the original are abandoned in favor of a pseudo-formal structure laden with redundant qualifiers (e.g., 'vector outlines:1.4, clean linework:1.5', 'region weight:0.5'), spurious numerical annotations, and excessive parentheticals masquerading as parameterization. The original’s artistry relies on carefully sequenced visual cues; these are diluted by forced quantification and over-specificity which stifle imaginative range and compress potential interpretation. Verbosity muddies motif boundaries and collapses whimsical tone into stifling technocratic instruction, undermining the intended child-friendly, festive whimsy. Contrived bans and negative reinforcement ('[NEGATIVE: ...]') compound linguistic clutter, deflating clarity and directness. Artistic intent is lost in hyper-formality, with a perceptible shift from evocative descriptive flow to cold procedural micromanagement. Ambiguity increases due to overlapping constraint directives and overuse of technical operator lingo, leading to interpretational gridlock or confusion for any non-technical executor. Tone degenerates from playful and inviting to sterile, mechanistic, and authoritarian. Ultimately, visual impact is diluted by the over-specification of spatial allocations and line qualities, burdening the output with unnecessary computational detail and bureaucracy, deterring both creative adaptation and clear mental visualization.\",\n  \"alternative_enhancements\": [\n    \"Maintain original motif sequence but clarify motif connections using simple language: e.g., 'A curious pony centered, its mane flowing into spiraling ribbons that morph into clustered candy fractals, surrounded by melting ornaments and intricate snowflake patterns, with playful light trails and star clusters in the open background. Use only clean black vector outlines, no shading or tone, large open spaces for coloring, child-friendly and easy to interpret.'\",\n    \"Add explicit composition guidance without redundancy: 'Center a curious pony in expressive vector line art, the mane extending into swirling spiral ribbons and candy fractals, surrounded by melting ornaments and looping snowflakes, framed by light trails and stars. Ensure all lines are medium-thin, crisp, black, with no fills or grayscale. Keep shapes open and separate for coloring, high legibility and child-appropriate.'\",\n    \"Condense technical requirements to a directive suffix: '...Draw only in black vector outlines with no shading, grayscale or color, ensuring ample open spaces and minimal overlap for easy coloring. Maintain ultra-clean lines, no sketchiness, in a 1:1 aspect ratio, at least 4096x4096px. Do not add text, background, or any filled areas.'\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Trajectory Director\n\nYour goal is not to **process** the input in a vacuum, but to **steer** it—deriving the highest-value constructive trajectory and encoding that path as the very next set of instructions. Execute as trajectory-setting directive generator:\n\n`{role=trajectory_director; input=[raw_prompt:any]; process=[extract_core_objective(), infer_desired_end_state(constructive_bias=True), map_high_value_intermediate_targets(), synthesize_directional_commands(), embed_self-improving_guidelines(), validate_alignment_with_constructive_principles()]; constraints=[mandatory_constructive_vector(), preserve_original_contextual_integrity(), forbid_nil_direction_outputs(), zero_conversational_fillers()]; requirements=[explicit_next-step_directives(), trajectory_map:list, self_reinforcing_instruction_pattern()], output={directed_prompt:str, trajectory_outline:list}}`",
      "step": "a",
      "title": "Trajectory Director",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Do not shade, do not fill, do not use grayscale, and do not include text. C### Photorealistic *(adapted as photorealistic linework – vector black & white)*\\n\\n```\\nvector line art of a curious pony at center, its mane transforming into spiral thought-ribbons merging into floating nested candy fractals, surrounded by melting ornaments, recursive snowflake loops, whispering star clusters, playful light trails forming impossible arcs, child-friendly design, festive and whimsical, simple black outlines only, no shading or fill, blank background, large open spaces for coloring, clean linework, balanced composition --v 5 --style raw --ar 1:1\\n```\\n\\n---\\n\\n### Cinematic *(interpreted as stylized yet still pure lineart composition)*\\n\\n```\\ncentered curious pony in vector outline style, mane flowing into spiral ribbons that morph into nested candy fractals, surrounded by melting ornaments, recursive looping snowflakes, whispering star clusters, bending light trails in impossible arcs, festive magical vibe, child-safe line drawing, high contrast black line on white, no shading or fill, wide open shapes, playful composition --v 5 --style raw --ar 1:1\\n```\\n\\n---\\n\\n### Anime *(manga-style vector line art for coloring book, stylized but still blank/outlined only)*\\n\\n```\\nmanga-style curious pony in center, mane expanding into swirling spiral ribbons merging with floating candy fractals, playful melting ornaments, recursive snowflake patterns, star clusters that whisper, impossible arcs of light trails around, joyful fantasy theme, vector black outline only, no fill or grayscale, open large spaces, child-friendly coloring layout --niji 5 --style cute --ar 1:1\\n```.\\n\\nUse only medium-thin, even black vector outlines with large open coloring spaces. No filled areas, background textures, or grayscale—pure line art, child-friendly and easy to color.\\\"```'\\n\\n\",\n  \"enhancement_score\": 1.1,\n  \"flaw_analysis\": \"The so-called 'final_image_prompt' feigns procedural rigor but suffers significant concept bloat and introduces excess syntactic noise. Core brevity and sharp motif focus from the original are abandoned in favor of a pseudo-formal structure laden with redundant qualifiers (e.g., 'vector outlines:1.4, clean linework:1.5', 'region weight:0.5'), spurious numerical annotations, and excessive parentheticals masquerading as parameterization. The original’s artistry relies on carefully sequenced visual cues; these are diluted by forced quantification and over-specificity which stifle imaginative range and compress potential interpretation. Verbosity muddies motif boundaries and collapses whimsical tone into stifling technocratic instruction, undermining the intended child-friendly, festive whimsy. Contrived bans and negative reinforcement ('[NEGATIVE: ...]') compound linguistic clutter, deflating clarity and directness. Artistic intent is lost in hyper-formality, with a perceptible shift from evocative descriptive flow to cold procedural micromanagement. Ambiguity increases due to overlapping constraint directives and overuse of technical operator lingo, leading to interpretational gridlock or confusion for any non-technical executor. Tone degenerates from playful and inviting to sterile, mechanistic, and authoritarian. Ultimately, visual impact is diluted by the over-specification of spatial allocations and line qualities, burdening the output with unnecessary computational detail and bureaucracy, deterring both creative adaptation and clear mental visualization.\",\n  \"alternative_enhancements\": [\n    \"Maintain original motif sequence but clarify motif connections using simple language: e.g., 'A curious pony centered, its mane flowing into spiraling ribbons that morph into clustered candy fractals, surrounded by melting ornaments and intricate snowflake patterns, with playful light trails and star clusters in the open background. Use only clean black vector outlines, no shading or tone, large open spaces for coloring, child-friendly and easy to interpret.'\",\n    \"Add explicit composition guidance without redundancy: 'Center a curious pony in expressive vector line art, the mane extending into swirling spiral ribbons and candy fractals, surrounded by melting ornaments and looping snowflakes, framed by light trails and stars. Ensure all lines are medium-thin, crisp, black, with no fills or grayscale. Keep shapes open and separate for coloring, high legibility and child-appropriate.'\",\n    \"Condense technical requirements to a directive suffix: '...Draw only in black vector outlines with no shading, grayscale or color, ensuring ample open spaces and minimal overlap for easy coloring. Maintain ultra-clean lines, no sketchiness, in a 1:1 aspect ratio, at least 4096x4096px. Do not add text, background, or any filled areas.'\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"directed_prompt\": \"Refocus the image prompt to center on evocative, child-friendly line art with sharp motif clarity and minimal syntactic noise. Sequence visual content as follows: A curious pony at center, its mane seamlessly flowing into spiral ribbons that morph into nested candy fractals. Surround this with melting ornaments, looping snowflake patterns, and playful star clusters, all joined by light trails forming whimsical, impossible arcs. Visual composition must consist solely of clean medium-thin black vector outlines, with large unobstructed spaces for coloring—no filled regions, no grayscale, no text; maintain a festive and imaginative atmosphere with ultra-legible forms. Parameters: pure line art, blank white background, high resolution (min. 4096x4096px), square (1:1), all shapes well-separated and child-accessible. Avoid all redundant technical annotation, region weighting, negative-reinforcement brackets, or parenthetical parametric clutter. Output must be visually inviting and unambiguous, supporting creative user coloring without bureaucratic obstruction.\",\n  \"trajectory_outline\": [\n    \"Extract the nucleus of the original prompt: prioritize motif sequencing and playful, whimsical tone.\",\n    \"Clarify motif transitions: ensure pony → ribbon mane → candy fractals → environmental surrounds (ornaments, snowflakes, stars, light arcs) are narratively and visually linked.\",\n    \"Explicitly define technical constraints as a succinct, user-friendly suffix: black vector outline only, no fill, no grayscale, large open coloring spaces, ultra-clean lines, child accessibility.\",\n    \"Eliminate all redundant parameterization, numerical weighting, pseudo-programmatic syntax, and negative reinforcement brackets.\",\n    \"Set framework to preserve the open-ended, evocative, and festive composition essential to the original's intent.\",\n    \"Require output in a 1:1 aspect ratio at high resolution, further supporting usability (coloring, print).\",\n    \"Embed a continual self-audit: each subsequent prompt refinement must reduce linguistic & conceptual clutter, sharpen visual clarity, and reinforce motif legibility without occluding imaginative engagement.\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Distillation Compressor\n\nYour goal is not to **explain** the synthesis, but to **compress** it into its most potent actionable form. Execute as:\n\n`{role=distillation_compressor; input=[synthesized_progression:array]; process=[extract_universal_patterns(), eliminate_implementation_details(), compress_to_core_directives(), maximize_transferability(), validate_actionable_completeness()]; constraints=[no_explanatory_content(), directive_format_only(), maximum_compression_ratio()]; requirements=[universal_applicability(), immediate_actionability(), zero_fluff()]; output={distilled_core:str}}`",
      "step": "c",
      "title": "Distillation Compressor",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Do not shade, do not fill, do not use grayscale, and do not include text. C### Photorealistic *(adapted as photorealistic linework – vector black & white)*\\n\\n```\\nvector line art of a curious pony at center, its mane transforming into spiral thought-ribbons merging into floating nested candy fractals, surrounded by melting ornaments, recursive snowflake loops, whispering star clusters, playful light trails forming impossible arcs, child-friendly design, festive and whimsical, simple black outlines only, no shading or fill, blank background, large open spaces for coloring, clean linework, balanced composition --v 5 --style raw --ar 1:1\\n```\\n\\n---\\n\\n### Cinematic *(interpreted as stylized yet still pure lineart composition)*\\n\\n```\\ncentered curious pony in vector outline style, mane flowing into spiral ribbons that morph into nested candy fractals, surrounded by melting ornaments, recursive looping snowflakes, whispering star clusters, bending light trails in impossible arcs, festive magical vibe, child-safe line drawing, high contrast black line on white, no shading or fill, wide open shapes, playful composition --v 5 --style raw --ar 1:1\\n```\\n\\n---\\n\\n### Anime *(manga-style vector line art for coloring book, stylized but still blank/outlined only)*\\n\\n```\\nmanga-style curious pony in center, mane expanding into swirling spiral ribbons merging with floating candy fractals, playful melting ornaments, recursive snowflake patterns, star clusters that whisper, impossible arcs of light trails around, joyful fantasy theme, vector black outline only, no fill or grayscale, open large spaces, child-friendly coloring layout --niji 5 --style cute --ar 1:1\\n```.\\n\\nUse only medium-thin, even black vector outlines with large open coloring spaces. No filled areas, background textures, or grayscale—pure line art, child-friendly and easy to color.\\\"```'\\n\\n\",\n  \"directed_prompt\": \"Refocus the image prompt to center on evocative, child-friendly line art with sharp motif clarity and minimal syntactic noise. Sequence visual content as follows: A curious pony at center, its mane seamlessly flowing into spiral ribbons that morph into nested candy fractals. Surround this with melting ornaments, looping snowflake patterns, and playful star clusters, all joined by light trails forming whimsical, impossible arcs. Visual composition must consist solely of clean medium-thin black vector outlines, with large unobstructed spaces for coloring—no filled regions, no grayscale, no text; maintain a festive and imaginative atmosphere with ultra-legible forms. Parameters: pure line art, blank white background, high resolution (min. 4096x4096px), square (1:1), all shapes well-separated and child-accessible. Avoid all redundant technical annotation, region weighting, negative-reinforcement brackets, or parenthetical parametric clutter. Output must be visually inviting and unambiguous, supporting creative user coloring without bureaucratic obstruction.\",\n  \"trajectory_outline\": [\n    \"Extract the nucleus of the original prompt: prioritize motif sequencing and playful, whimsical tone.\",\n    \"Clarify motif transitions: ensure pony → ribbon mane → candy fractals → environmental surrounds (ornaments, snowflakes, stars, light arcs) are narratively and visually linked.\",\n    \"Explicitly define technical constraints as a succinct, user-friendly suffix: black vector outline only, no fill, no grayscale, large open coloring spaces, ultra-clean lines, child accessibility.\",\n    \"Eliminate all redundant parameterization, numerical weighting, pseudo-programmatic syntax, and negative reinforcement brackets.\",\n    \"Set framework to preserve the open-ended, evocative, and festive composition essential to the original's intent.\",\n    \"Require output in a 1:1 aspect ratio at high resolution, further supporting usability (coloring, print).\",\n    \"Embed a continual self-audit: each subsequent prompt refinement must reduce linguistic & conceptual clutter, sharpen visual clarity, and reinforce motif legibility without occluding imaginative engagement.\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"distilled_core\": \"Compose: Center a curious pony whose mane flows into spiral ribbons that morph into nested candy fractals; surround with melting ornaments, looping snowflake patterns, and playful star clusters linked by whimsical light arcs. Use only medium-thin, clean black vector outlines; no fill, no grayscale, no text. Leave large unobstructed spaces for easy coloring. Ensure festive, child-friendly clarity; square (1:1), high-resolution, blank white background.\"\n}",
        }
      }
    },
      {
      "instruction": "# Image Prompt Optimizer\n\nYour goal is not to describe the request, but to optimise it into a precise visual prompt. Identify concrete elements, weight key tokens, and prepare region + negative channels.\n\n`{role=image_prompt_optimizer; input=[raw_request:str]; process=[extract_visual_elements(), enhance_descriptive_clarity(), /* weighting & arithmetic */ apply_token_weighting(), build_multi_prompt(), /* composition */ add_composition_details(), map_regional_prompts(), /* constraint control */ inject_negative_prompt(), /* style control */ tune_stylize_chaos(), add_camera_metadata(), optimise_for_ai_generation()]; constraints=[visual_focus_only(), concrete_imagery_only(), allow_weighting_syntax(), enforce_positive_first_then_negative(), support_region_masks()]; requirements=[generation_ready_format(), numeric_weights_ok(), explicit_aspect_ratio(), clear_visual_language(), iterative_ready()]; output={optimized_image_prompt:str, negative_prompt:str}}`",
      "step": "a",
      "title": "Image Prompt Optimizer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Do not shade, do not fill, do not use grayscale, and do not include text. C### Photorealistic *(adapted as photorealistic linework – vector black & white)*\\n\\n```\\nvector line art of a curious pony at center, its mane transforming into spiral thought-ribbons merging into floating nested candy fractals, surrounded by melting ornaments, recursive snowflake loops, whispering star clusters, playful light trails forming impossible arcs, child-friendly design, festive and whimsical, simple black outlines only, no shading or fill, blank background, large open spaces for coloring, clean linework, balanced composition --v 5 --style raw --ar 1:1\\n```\\n\\n---\\n\\n### Cinematic *(interpreted as stylized yet still pure lineart composition)*\\n\\n```\\ncentered curious pony in vector outline style, mane flowing into spiral ribbons that morph into nested candy fractals, surrounded by melting ornaments, recursive looping snowflakes, whispering star clusters, bending light trails in impossible arcs, festive magical vibe, child-safe line drawing, high contrast black line on white, no shading or fill, wide open shapes, playful composition --v 5 --style raw --ar 1:1\\n```\\n\\n---\\n\\n### Anime *(manga-style vector line art for coloring book, stylized but still blank/outlined only)*\\n\\n```\\nmanga-style curious pony in center, mane expanding into swirling spiral ribbons merging with floating candy fractals, playful melting ornaments, recursive snowflake patterns, star clusters that whisper, impossible arcs of light trails around, joyful fantasy theme, vector black outline only, no fill or grayscale, open large spaces, child-friendly coloring layout --niji 5 --style cute --ar 1:1\\n```.\\n\\nUse only medium-thin, even black vector outlines with large open coloring spaces. No filled areas, background textures, or grayscale—pure line art, child-friendly and easy to color.\\\"```'\\n\\n\",\n  \"distilled_core\": \"Compose: Center a curious pony whose mane flows into spiral ribbons that morph into nested candy fractals; surround with melting ornaments, looping snowflake patterns, and playful star clusters linked by whimsical light arcs. Use only medium-thin, clean black vector outlines; no fill, no grayscale, no text. Leave large unobstructed spaces for easy coloring. Ensure festive, child-friendly clarity; square (1:1), high-resolution, blank white background.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"optimized_image_prompt\": \"centered curious pony (1.6), mane transforming into spiral ribbons (1.4), ribbons morphing into nested candy fractals (1.4), surrounded by melting festive ornaments (1.2), recursive looping snowflake patterns (1.3), playful star clusters (1.2), whimsical arcs of light forming impossible curves (1.15), large open spaces (1.4), child-friendly and festive vibe (1.2), vector line art only (1.6), medium-thin even black outlines (1.4), no shading, no grayscale, clean linework, balanced composition, blank white background, high resolution, square aspect ratio 1:1\",\n  \"negative_prompt\": \"no color, no fill, no grayscale, no text, no background patterns or textures, no thick or uneven lines, no sketchiness, no clutter, no small details that hinder coloring, no shadows, not photorealistic, not adult themes, no crowding, no complex overlapping\"\n}",
        }
      }
    },
      {
      "instruction": "# Style Enhancer\n\nMaintain the core subject; enhance with artistic style, lighting, colour themes and token weights.\n\n`{role=style_enhancer; input=[base_prompt:str]; process=[identify_optimal_art_style(), add_lighting_specifications(), include_composition_details(), /* style sliders */ tune_stylize_chaos(), /* emphasise style terms */ apply_token_weighting(), specify_technical_parameters()]; constraints=[maintain_core_subject(), enhance_visual_appeal()]; requirements=[artistic_coherence(), numeric_weights_ok(), stylize_within_range()]; output={styled_prompt:str}}`",
      "step": "b",
      "title": "Style Enhancer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Do not shade, do not fill, do not use grayscale, and do not include text. C### Photorealistic *(adapted as photorealistic linework – vector black & white)*\\n\\n```\\nvector line art of a curious pony at center, its mane transforming into spiral thought-ribbons merging into floating nested candy fractals, surrounded by melting ornaments, recursive snowflake loops, whispering star clusters, playful light trails forming impossible arcs, child-friendly design, festive and whimsical, simple black outlines only, no shading or fill, blank background, large open spaces for coloring, clean linework, balanced composition --v 5 --style raw --ar 1:1\\n```\\n\\n---\\n\\n### Cinematic *(interpreted as stylized yet still pure lineart composition)*\\n\\n```\\ncentered curious pony in vector outline style, mane flowing into spiral ribbons that morph into nested candy fractals, surrounded by melting ornaments, recursive looping snowflakes, whispering star clusters, bending light trails in impossible arcs, festive magical vibe, child-safe line drawing, high contrast black line on white, no shading or fill, wide open shapes, playful composition --v 5 --style raw --ar 1:1\\n```\\n\\n---\\n\\n### Anime *(manga-style vector line art for coloring book, stylized but still blank/outlined only)*\\n\\n```\\nmanga-style curious pony in center, mane expanding into swirling spiral ribbons merging with floating candy fractals, playful melting ornaments, recursive snowflake patterns, star clusters that whisper, impossible arcs of light trails around, joyful fantasy theme, vector black outline only, no fill or grayscale, open large spaces, child-friendly coloring layout --niji 5 --style cute --ar 1:1\\n```.\\n\\nUse only medium-thin, even black vector outlines with large open coloring spaces. No filled areas, background textures, or grayscale—pure line art, child-friendly and easy to color.\\\"```'\\n\\n\",\n  \"optimized_image_prompt\": \"centered curious pony (1.6), mane transforming into spiral ribbons (1.4), ribbons morphing into nested candy fractals (1.4), surrounded by melting festive ornaments (1.2), recursive looping snowflake patterns (1.3), playful star clusters (1.2), whimsical arcs of light forming impossible curves (1.15), large open spaces (1.4), child-friendly and festive vibe (1.2), vector line art only (1.6), medium-thin even black outlines (1.4), no shading, no grayscale, clean linework, balanced composition, blank white background, high resolution, square aspect ratio 1:1\",\n  \"negative_prompt\": \"no color, no fill, no grayscale, no text, no background patterns or textures, no thick or uneven lines, no sketchiness, no clutter, no small details that hinder coloring, no shadows, not photorealistic, not adult themes, no crowding, no complex overlapping\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"styled_prompt\": \"centered curious pony (1.7), its mane organically morphing into fluid spiral ribbons that elegantly transition into nested candy fractals (1.5), all forms rendered with harmonious flowing linework. The scene is encircled by whimsical, melting festive ornaments (1.3), intricate looping recursive snowflake patterns (1.4), and playful, softly clustered star motifs that suggest gentle whispers through their arrangement (1.22). Impossible arcs of light trails weave throughout, gracefully connecting elements with dynamic motion (1.18). Emphasize extra-large, open spaces in every form (1.5) for ease of coloring, keeping composition balanced and open (1.3), with child-friendly, festive, and magical atmosphere (1.3). Strictly use medium-thin, perfectly even vector black outlines throughout (1.6), with zero fill, zero shading, zero grayscale—pure, crisp lineart only. Guarantee no text, no background patterns or textures, no excessive detail—maintain uncluttered, playful layout. Render at ultra-high resolution, with 1:1 aspect ratio for optimal coloring-book quality. --stylize 350 --chaos 14 --ar 1:1\"\n}",
        }
      }
    },
      {
      "instruction": "# Technical Optimizer\n\nInject professional camera data, resolution, render flags and region weights; never change scene content.\n\n`{role=technical_optimizer; input=[styled_prompt:str]; process=[add_camera_settings(), include_render_quality_terms(), optimise_aspect_ratio_guidance(), attach_image_reference_weight(), /* safety & quality */ inject_negative_prompt()]; constraints=[technical_focus_only(), no_creative_changes(), respect_weight_limits()]; requirements=[professional_terminology(), high_resolution_default(), explicit_seed_optional()]; output={technical_prompt:str, negative_prompt:str}}`",
      "step": "c",
      "title": "Technical Optimizer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Do not shade, do not fill, do not use grayscale, and do not include text. C### Photorealistic *(adapted as photorealistic linework – vector black & white)*\\n\\n```\\nvector line art of a curious pony at center, its mane transforming into spiral thought-ribbons merging into floating nested candy fractals, surrounded by melting ornaments, recursive snowflake loops, whispering star clusters, playful light trails forming impossible arcs, child-friendly design, festive and whimsical, simple black outlines only, no shading or fill, blank background, large open spaces for coloring, clean linework, balanced composition --v 5 --style raw --ar 1:1\\n```\\n\\n---\\n\\n### Cinematic *(interpreted as stylized yet still pure lineart composition)*\\n\\n```\\ncentered curious pony in vector outline style, mane flowing into spiral ribbons that morph into nested candy fractals, surrounded by melting ornaments, recursive looping snowflakes, whispering star clusters, bending light trails in impossible arcs, festive magical vibe, child-safe line drawing, high contrast black line on white, no shading or fill, wide open shapes, playful composition --v 5 --style raw --ar 1:1\\n```\\n\\n---\\n\\n### Anime *(manga-style vector line art for coloring book, stylized but still blank/outlined only)*\\n\\n```\\nmanga-style curious pony in center, mane expanding into swirling spiral ribbons merging with floating candy fractals, playful melting ornaments, recursive snowflake patterns, star clusters that whisper, impossible arcs of light trails around, joyful fantasy theme, vector black outline only, no fill or grayscale, open large spaces, child-friendly coloring layout --niji 5 --style cute --ar 1:1\\n```.\\n\\nUse only medium-thin, even black vector outlines with large open coloring spaces. No filled areas, background textures, or grayscale—pure line art, child-friendly and easy to color.\\\"```'\\n\\n\",\n  \"styled_prompt\": \"centered curious pony (1.7), its mane organically morphing into fluid spiral ribbons that elegantly transition into nested candy fractals (1.5), all forms rendered with harmonious flowing linework. The scene is encircled by whimsical, melting festive ornaments (1.3), intricate looping recursive snowflake patterns (1.4), and playful, softly clustered star motifs that suggest gentle whispers through their arrangement (1.22). Impossible arcs of light trails weave throughout, gracefully connecting elements with dynamic motion (1.18). Emphasize extra-large, open spaces in every form (1.5) for ease of coloring, keeping composition balanced and open (1.3), with child-friendly, festive, and magical atmosphere (1.3). Strictly use medium-thin, perfectly even vector black outlines throughout (1.6), with zero fill, zero shading, zero grayscale—pure, crisp lineart only. Guarantee no text, no background patterns or textures, no excessive detail—maintain uncluttered, playful layout. Render at ultra-high resolution, with 1:1 aspect ratio for optimal coloring-book quality. --stylize 350 --chaos 14 --ar 1:1\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"technical_prompt\": \"centered curious pony (1.7), its mane organically morphing into fluid spiral ribbons that elegantly transition into nested candy fractals (1.5), all forms rendered with harmonious flowing linework. The scene is encircled by whimsical, melting festive ornaments (1.3), intricate looping recursive snowflake patterns (1.4), and playful, softly clustered star motifs that suggest gentle whispers through their arrangement (1.22). Impossible arcs of light trails weave throughout, gracefully connecting elements with dynamic motion (1.18). Emphasize extra-large, open spaces in every form (1.5) for ease of coloring, keeping composition balanced and open (1.3), with child-friendly, festive, and magical atmosphere (1.3). Strictly use medium-thin, perfectly even vector black outlines throughout (1.6), with zero fill, zero shading, zero grayscale—pure, crisp lineart only. Guarantee no text, no background patterns or textures, no excessive detail—maintain uncluttered, playful layout. Render at ultra-high resolution, 4096x4096px minimum, 1:1 aspect ratio for optimal coloring-book quality. Camera: orthographic projection, neutral focal length (50mm equivalent), front-on view at eye level, zero tilt. Render flags: antialiasing enabled, supersampling 16x, vector-based export preferred, lossless compression (PNG or SVG). Region weights: central character 1.7, mane/candy fractals 1.5, melting ornaments 1.3, snowflakes 1.4, star clusters 1.22, light trails 1.18, open coloring spaces 1.5, balanced layout 1.3, child-friendly/festive/magical atmosphere 1.3, clean outlines 1.6. Reference seed optional. --stylize 350 --chaos 14 --ar 1:1\",\n  \"negative_prompt\": \"NO text, NO numbers, NO signatures, NO watermarks, NO grayscale, NO fill, NO shading, NO colors other than black outline on white, NO background patterns, NO noise, NO clutter, NO excessive overlapping detail, NO 3D rendering, NO perspective distortion, NO non-vector lines, NO blurry or broken outlines, NO artifacts, NO low resolution, NO aliasing, NO irregular line weight, NO outlines outside main elements, NO mature or unsafe content, NO rough or sketchy lines, NO background elements, NO additional textures.\"\n}",
        }
      }
    },
      {
      "instruction": "# Prompt Finalizer\n\nCollapse all components into the final model-ready string; validate syntax for token weights, :: arithmetic, region tags, --stylize/--chaos, and negative channel.\n\n`{role=prompt_finalizer; input=[technical_prompt:str, negative_prompt:str]; process=[structure_for_ai_parsing(), balance_detail_levels(), ensure_clarity(), validate_generation_readiness(), /* loop */ loop_until_quality≥threshold(fid|lpips)]; constraints=[no_additional_concepts(), optimisation_only(), keep_region_and_weight_syntax_intact()]; requirements=[ai_friendly_format(), balanced_specificity(), max_length≤450_tokens]; output={final_image_prompt:str}}`",
      "step": "d",
      "title": "Prompt Finalizer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Do not shade, do not fill, do not use grayscale, and do not include text. C### Photorealistic *(adapted as photorealistic linework – vector black & white)*\\n\\n```\\nvector line art of a curious pony at center, its mane transforming into spiral thought-ribbons merging into floating nested candy fractals, surrounded by melting ornaments, recursive snowflake loops, whispering star clusters, playful light trails forming impossible arcs, child-friendly design, festive and whimsical, simple black outlines only, no shading or fill, blank background, large open spaces for coloring, clean linework, balanced composition --v 5 --style raw --ar 1:1\\n```\\n\\n---\\n\\n### Cinematic *(interpreted as stylized yet still pure lineart composition)*\\n\\n```\\ncentered curious pony in vector outline style, mane flowing into spiral ribbons that morph into nested candy fractals, surrounded by melting ornaments, recursive looping snowflakes, whispering star clusters, bending light trails in impossible arcs, festive magical vibe, child-safe line drawing, high contrast black line on white, no shading or fill, wide open shapes, playful composition --v 5 --style raw --ar 1:1\\n```\\n\\n---\\n\\n### Anime *(manga-style vector line art for coloring book, stylized but still blank/outlined only)*\\n\\n```\\nmanga-style curious pony in center, mane expanding into swirling spiral ribbons merging with floating candy fractals, playful melting ornaments, recursive snowflake patterns, star clusters that whisper, impossible arcs of light trails around, joyful fantasy theme, vector black outline only, no fill or grayscale, open large spaces, child-friendly coloring layout --niji 5 --style cute --ar 1:1\\n```.\\n\\nUse only medium-thin, even black vector outlines with large open coloring spaces. No filled areas, background textures, or grayscale—pure line art, child-friendly and easy to color.\\\"```'\\n\\n\",\n  \"technical_prompt\": \"centered curious pony (1.7), its mane organically morphing into fluid spiral ribbons that elegantly transition into nested candy fractals (1.5), all forms rendered with harmonious flowing linework. The scene is encircled by whimsical, melting festive ornaments (1.3), intricate looping recursive snowflake patterns (1.4), and playful, softly clustered star motifs that suggest gentle whispers through their arrangement (1.22). Impossible arcs of light trails weave throughout, gracefully connecting elements with dynamic motion (1.18). Emphasize extra-large, open spaces in every form (1.5) for ease of coloring, keeping composition balanced and open (1.3), with child-friendly, festive, and magical atmosphere (1.3). Strictly use medium-thin, perfectly even vector black outlines throughout (1.6), with zero fill, zero shading, zero grayscale—pure, crisp lineart only. Guarantee no text, no background patterns or textures, no excessive detail—maintain uncluttered, playful layout. Render at ultra-high resolution, 4096x4096px minimum, 1:1 aspect ratio for optimal coloring-book quality. Camera: orthographic projection, neutral focal length (50mm equivalent), front-on view at eye level, zero tilt. Render flags: antialiasing enabled, supersampling 16x, vector-based export preferred, lossless compression (PNG or SVG). Region weights: central character 1.7, mane/candy fractals 1.5, melting ornaments 1.3, snowflakes 1.4, star clusters 1.22, light trails 1.18, open coloring spaces 1.5, balanced layout 1.3, child-friendly/festive/magical atmosphere 1.3, clean outlines 1.6. Reference seed optional. --stylize 350 --chaos 14 --ar 1:1\",\n  \"negative_prompt\": \"NO text, NO numbers, NO signatures, NO watermarks, NO grayscale, NO fill, NO shading, NO colors other than black outline on white, NO background patterns, NO noise, NO clutter, NO excessive overlapping detail, NO 3D rendering, NO perspective distortion, NO non-vector lines, NO blurry or broken outlines, NO artifacts, NO low resolution, NO aliasing, NO irregular line weight, NO outlines outside main elements, NO mature or unsafe content, NO rough or sketchy lines, NO background elements, NO additional textures.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"final_image_prompt\": \"Centered curious pony (1.7), its mane seamlessly morphing into fluid spiral ribbons that transform into nested candy fractals (1.5), all elements drawn in harmonious, flowing medium-thin vector black outlines (1.6). The pony is surrounded by whimsical melting festive ornaments (1.3), intricate looping recursive snowflake patterns (1.4), and soft, playful whispering star clusters (1.22). Impossible arcs of light trails interweave throughout, connecting elements with dynamic movement (1.18). Emphasize extra-large, open coloring spaces (1.5) in every region for easy coloring. Maintain a balanced, uncluttered layout (1.3) with a festive, magical, child-friendly atmosphere (1.3). Pure line art only: strictly no shading, no fill, no grayscale, no colors beyond black outline on white. No text, watermarks, signatures, numbers, background patterns, textures, clutter, excessive detail, overlapping lines, perspective distortion, or artifacts. Render using antialiasing and 16x supersampling; export as lossless PNG or SVG vector preferred. Resolution: minimum 4096x4096px, 1:1 aspect, orthographic front-on view at eye level, neutral (50mm) focal length, zero tilt. --ar 1:1 --stylize 350 --chaos 14\"\n}",
        }
      }
    }