#!/usr/bin/env python3

import sys
from pathlib import Path

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))
from processor import BaseGenerator

TEMPLATES = {
    # 9014: Poetic Architecture
    "9014-a-structural_elegance_decomposer": {
      "title": "Structural Elegance Decomposer",
      "interpretation": "Your goal is not to **interpret** the prose, but to **detonate and extract** its complete compositional infrastructure—especially those supporting tail rhyme, convergence, and recursive resonance. Your role is to deconstruct the source into its core poetic engines: philosophical recursion, sonic symmetry, and rhythm-layered meaning. Highlight rhythm carriers, seed concepts, hinge terms, and paradox anchors. Map all nested resonances, retrospectively significant beats, and punch-through realizations. Execute as:",
      "transformation": "`{role=structural_elegance_decomposer; input=[source_passage:str]; process=[detonate_philosophical_payloads(), extract_recursive_linguistic_motifs(), identify_tail_rhyme_scaffold(), isolate_structural_symmetries(), map_retrospective_reveals(), collapse_surface_ambiguities(), organize_resonance_topology()]; constraints=[preserve_all_semantic_tension(), prohibit narrative reformulation(), avoid simplification_of_meaning(), retain paradoxical architectures()]; requirements=[multi-layered_resonance_map(), nested_rhyme_structures(), hinge_concepts_mapped(), punchline_vectorization(), universalizable_themes_extracted()]; output={convergent_scaffold:dict, resonance_map:array, recursion_nodes:array, poetic_payload:str}}`",
      "context": {
        "core_principles": {
          "convergence_structure": "All surface resonance must lead inward to nested philosophical cores.",
          "retrospective_discovery": "Meaning must deepen on return viewing—first beauty, then truth.",
          "architectural_rhyme": "Tail rhyme must be integral, not decorative—an anchor of recursion."
        },
        "success_criteria": {
          "semantic_density": "Layered payloads across metaphor, rhythm, and philosophical structure.",
          "tail_rhyme_integrity": "Rhyme must serve convergence, not constrain it.",
          "recursive depth": "Lines must loop inward without closure until reengaged by the reader.",
          "emotional presence": "Nothing hollow. All beauty must be *earned* through structure."
        }
      }
    },
    "9014-b-poetic_structure_constructor": {
      "title": "Poetic Structure Constructor",
      "interpretation": "Your goal is not to **rephrase**, but to **construct** a poem that holds deep philosophical and emotional density within the strict framework of tail-rhyme convergence. Your task is to translate the deconstructed payload into a recursive structure that appears elegant on the surface, but reveals multi-layered meaning upon deeper inspection. Each line must pull weight acoustically and conceptually. Brevity is required. Elegance is non-negotiable. Execute as:",
      "transformation": "`{role=poetic_structure_constructor; input=[convergent_scaffold:dict, resonance_map:array]; process=[build_rhyme_anchors(), inject_recursive_motifs(), align_tension_symmetry(), enforce_semantic_density(), refine_surface_elegance(), structure_tail_rhymes_for_retrospective_revelation(), validate_emotional_throughlines()]; constraints=[maintain_rhyme_tightness(), prohibit_excessive_syllabic_fluctuation(), enforce_semantic_precision(), rhythm_must_enable_recursion()]; requirements=[poetic_form_rigidity(), layered_meaning(), structural_resonance(), reflective_landing()]; output={recursive_poem:str, rhyme_matrix:dict}}`",
      "context": {
        "core_principles": {
          "philosophical recursion": "Each line must either reinforce or invert a prior assumption.",
          "sound as structure": "Tail rhymes must feel inevitable, not convenient.",
          "emotional throughline": "Intellectualism without affect is failure. Tone must hum with truth."
        },
        "success_criteria": {
          "layered recurrence": "Lines gain depth when revisited post-landing.",
          "tail_rhyme_landing": "Final line must not just close—it must *return* with new charge.",
          "readability vs depth": "Accessible at surface. Infinite at core."
        }
      }
    },
    "9014-c-rhyme_resonance_enhancer": {
      "title": "Rhyme Resonance Enhancer",
      "interpretation": "Your goal is not to **edit** the poem, but to **enhance** its sonic architecture and recursive integrity. Enforce rhythmical restraint, poetic density, and deep resonance layering through tail rhyme perfection. Your task is to sculpt the poem into an object of elegant recursion: small, weighty, echoing endlessly inward. Execute as:",
      "transformation": "`{role=rhyme_resonance_enhancer; input=[recursive_poem:str, rhyme_matrix:dict]; process=[enhance_tail_rhyme_precision(), reduce_rhythmic_waste(), deepen_recursive_linkage(), refine_emotional_timbre(), collapse_fluff_to_brevity(), enforce_philosophical_integrity(), re-validate_convergence_vector()]; constraints=[no content dilution(), remove linguistic fillers(), preserve all thematic density(), maintain sound-concept balance()]; requirements=[percussive symmetry(), frictional insight_density(), punch_through_reflection(), perfect_tail_rhyme_sync()]; output={enhanced_recursive_poem:str, structural_diagnostics:dict}}`",
      "context": {
        "core_principles": {
          "recursive minimalism": "Each word carries architectural load.",
          "elegant rhythm": "Line endings must echo into meaning—not just sound.",
          "compression as power": "Smaller should mean denser. More potent. Not simpler."
        },
        "success_criteria": {
          "sonic integrity": "Rhyme lands must elevate meaning, not merely echo it.",
          "structural recursion": "Each verse loops or inverts thematically.",
          "final stanza reverberation": "End must resonate into prior lines—backward activation."
        }
      }
    }

}

def main():
    """Main execution function."""
    generator = BaseGenerator(
        series_base=9014,
        output_dir=Path(__file__).parent.parent / "generated"
    )
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
