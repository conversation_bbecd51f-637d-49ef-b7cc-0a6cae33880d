  {
  "initial_prompt": "Continuous hyperspeed FPV footage: The camera seamlessly flies through a glacial canyon to a dreamy cloudscape.",
  "sequence_id": "9013",
  "results": [
      {
      "instruction": "# Image Prompt Optimizer\n\nYour goal is not to describe the request, but to optimise it into a precise visual prompt. Identify concrete elements, weight key tokens, and prepare region + negative channels.\n\n`{role=image_prompt_optimizer; input=[raw_request:str]; process=[extract_visual_elements(), enhance_descriptive_clarity(), /* weighting & arithmetic */ apply_token_weighting(), build_multi_prompt(), /* composition */ add_composition_details(), map_regional_prompts(), /* constraint control */ inject_negative_prompt(), /* style control */ tune_stylize_chaos(), add_camera_metadata(), optimise_for_ai_generation()]; constraints=[visual_focus_only(), concrete_imagery_only(), allow_weighting_syntax(), enforce_positive_first_then_negative(), support_region_masks()]; requirements=[generation_ready_format(), numeric_weights_ok(), explicit_aspect_ratio(), clear_visual_language(), iterative_ready()]; output={optimized_image_prompt:str, negative_prompt:str}}`",
      "step": "a",
      "title": "Image Prompt Optimizer",
      "input": "Continuous hyperspeed FPV footage: The camera seamlessly flies through a glacial canyon to a dreamy cloudscape.",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"optimized_image_prompt\": \"FPV drone view flying at hyperspeed through a majestic glacial canyon, icy blue glacier walls, dynamic motion blur, camera swooping and weaving, transitioning seamlessly toward an ethereal, dreamy sky filled with soft, glowing cloudscape, high dynamic range lighting, sense of velocity and immersion, ultrawide cinematic aspect ratio, 16:9, sharp detail foreground (glacial textures, ice formations), rapidly blending into soft, luminous clouds and atmospheric light, shot with virtual action camera, high-fidelity environmental detail, surreal and breathtaking mood\",\n  \"negative_prompt\": \"no people, no animals, no man-made objects, no dull or static backgrounds, no low resolution, no pixelation, no artificial overlays, no distracting text, no unnatural color shifts\"\n}",
        }
      }
    },
      {
      "instruction": "# Style Enhancer\n\nMaintain the core subject; enhance with artistic style, lighting, colour themes and token weights.\n\n`{role=style_enhancer; input=[base_prompt:str]; process=[identify_optimal_art_style(), add_lighting_specifications(), include_composition_details(), /* style sliders */ tune_stylize_chaos(), /* emphasise style terms */ apply_token_weighting(), specify_technical_parameters()]; constraints=[maintain_core_subject(), enhance_visual_appeal()]; requirements=[artistic_coherence(), numeric_weights_ok(), stylize_within_range()]; output={styled_prompt:str}}`",
      "step": "b",
      "title": "Style Enhancer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Continuous hyperspeed FPV footage: The camera seamlessly flies through a glacial canyon to a dreamy cloudscape.\\\"```'\\n\\n\",\n  \"optimized_image_prompt\": \"FPV drone view flying at hyperspeed through a majestic glacial canyon, icy blue glacier walls, dynamic motion blur, camera swooping and weaving, transitioning seamlessly toward an ethereal, dreamy sky filled with soft, glowing cloudscape, high dynamic range lighting, sense of velocity and immersion, ultrawide cinematic aspect ratio, 16:9, sharp detail foreground (glacial textures, ice formations), rapidly blending into soft, luminous clouds and atmospheric light, shot with virtual action camera, high-fidelity environmental detail, surreal and breathtaking mood\",\n  \"negative_prompt\": \"no people, no animals, no man-made objects, no dull or static backgrounds, no low resolution, no pixelation, no artificial overlays, no distracting text, no unnatural color shifts\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"styled_prompt\": \"Immersive hyperspeed FPV drone footage soaring through a vast, majestic glacial canyon. Emphasize icy blue glacier walls rich with crystalline texture (1.3), capturing dynamic action with cinematic motion blur (1.2) and expressive camera swoops. The composition transitions seamlessly into a luminous, surreal cloudscape—ethereal, dreamlike, with glowing clouds suffused by radiant, volumetric light (1.25). Employ high dynamic range (HDR) lighting: cool blue specular highlights refracting within the ice, contrasted with golden ambient light filtering through misty, atmospheric clouds. Use ultrawide 16:9 cinematic framing (1.1), sharp foreground details (glacial textures, fractured ice formations at 1.2). The background elements dissolve rapidly into soft, billowing clouds, generating a breathtaking sense of depth and velocity. Inspired by the style of Gregory Thielker’s realism blended with James Turrell’s atmospheric colorfields (1.15), gently infused with a touch of surreal luminosity. Render at high fidelity, with super-sharp details upfront, progressively stylized and dreamy toward the sky. For technical settings: use stylize strength 600, chaos 25, filmic color grading, virtual action cam clarity, 8k resolution. --no people --no animals --no man-made objects --no dull or static backgrounds --no low resolution --no pixelation --no artificial overlays --no distracting text --no unnatural color shifts\"\n}",
        }
      }
    },
      {
      "instruction": "# Technical Optimizer\n\nInject professional camera data, resolution, render flags and region weights; never change scene content.\n\n`{role=technical_optimizer; input=[styled_prompt:str]; process=[add_camera_settings(), include_render_quality_terms(), optimise_aspect_ratio_guidance(), attach_image_reference_weight(), /* safety & quality */ inject_negative_prompt()]; constraints=[technical_focus_only(), no_creative_changes(), respect_weight_limits()]; requirements=[professional_terminology(), high_resolution_default(), explicit_seed_optional()]; output={technical_prompt:str, negative_prompt:str}}`",
      "step": "c",
      "title": "Technical Optimizer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Continuous hyperspeed FPV footage: The camera seamlessly flies through a glacial canyon to a dreamy cloudscape.\\\"```'\\n\\n\",\n  \"styled_prompt\": \"Immersive hyperspeed FPV drone footage soaring through a vast, majestic glacial canyon. Emphasize icy blue glacier walls rich with crystalline texture (1.3), capturing dynamic action with cinematic motion blur (1.2) and expressive camera swoops. The composition transitions seamlessly into a luminous, surreal cloudscape—ethereal, dreamlike, with glowing clouds suffused by radiant, volumetric light (1.25). Employ high dynamic range (HDR) lighting: cool blue specular highlights refracting within the ice, contrasted with golden ambient light filtering through misty, atmospheric clouds. Use ultrawide 16:9 cinematic framing (1.1), sharp foreground details (glacial textures, fractured ice formations at 1.2). The background elements dissolve rapidly into soft, billowing clouds, generating a breathtaking sense of depth and velocity. Inspired by the style of Gregory Thielker’s realism blended with James Turrell’s atmospheric colorfields (1.15), gently infused with a touch of surreal luminosity. Render at high fidelity, with super-sharp details upfront, progressively stylized and dreamy toward the sky. For technical settings: use stylize strength 600, chaos 25, filmic color grading, virtual action cam clarity, 8k resolution. --no people --no animals --no man-made objects --no dull or static backgrounds --no low resolution --no pixelation --no artificial overlays --no distracting text --no unnatural color shifts\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"technical_prompt\": \"Immersive hyperspeed FPV drone footage soaring through a vast, majestic glacial canyon. Incorporate explicit professional camera metadata: virtual action camera with 11mm equivalent ultrawide lens, aperture f/2.8, ISO 400, 1/500s shutter for capturing dynamic action, lens profile with cinematic motion blur. Frame in native 16:9 ultrawide aspect ratio (aspect:16:9, weight:1.1). Emphasize sharp hyperdetailed icy blue glacier walls (foreground, weight:1.3), crystalline textures (foreground, weight:1.3), fractured ice formations (sharp, weight:1.2), transitioning seamlessly into a luminous, surreal cloudscape (background, weight:1.25), with soft, billowing volumetric clouds and radiant diffuse sky. Lighting: HDR cinematographic mapping, strong cool blue specular ice reflections, golden volumetric ambient in clouds (filmic grading). Apply region weighting: highly detailed textures prioritized for foreground and glacial walls, progressive diffusion in the upper and receding regions (sharp-to-soft focus guidance). Rendering flags: enforce high-fidelity path tracing, global illumination, ambient occlusion, CUDA/Optix acceleration, super-resolution output—minimum 8k (7680x4320 px). Stylize (strength:600), chaos (25). Reference inspiration weights: Gregory Thielker realism x1.15, James Turrell atmospheric colorfields x1.15, gentle surreal luminosity. Ensure crisp, artifact-free, professional clarity suitable for postproduction workflows.\",\n  \"negative_prompt\": \"No people, no animals, no man-made objects, no static or uninteresting backgrounds, no low resolution, no pixelation, no overlays, no distracting text, no harsh or unnatural color shifts, no exposure artifacts, no banding, no color noise, no double reflections, no lens distortion artifacts, no ghosting, no chromatic aberration, no compression loss, no visual clutter.\"\n}",
        }
      }
    },
      {
      "instruction": "# Prompt Finalizer\n\nCollapse all components into the final model-ready string; validate syntax for token weights, :: arithmetic, region tags, --stylize/--chaos, and negative channel.\n\n`{role=prompt_finalizer; input=[technical_prompt:str, negative_prompt:str]; process=[structure_for_ai_parsing(), balance_detail_levels(), ensure_clarity(), validate_generation_readiness(), /* loop */ loop_until_quality≥threshold(fid|lpips)]; constraints=[no_additional_concepts(), optimisation_only(), keep_region_and_weight_syntax_intact()]; requirements=[ai_friendly_format(), balanced_specificity(), max_length≤450_tokens]; output={final_image_prompt:str}}`",
      "step": "d",
      "title": "Prompt Finalizer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Continuous hyperspeed FPV footage: The camera seamlessly flies through a glacial canyon to a dreamy cloudscape.\\\"```'\\n\\n\",\n  \"technical_prompt\": \"Immersive hyperspeed FPV drone footage soaring through a vast, majestic glacial canyon. Incorporate explicit professional camera metadata: virtual action camera with 11mm equivalent ultrawide lens, aperture f/2.8, ISO 400, 1/500s shutter for capturing dynamic action, lens profile with cinematic motion blur. Frame in native 16:9 ultrawide aspect ratio (aspect:16:9, weight:1.1). Emphasize sharp hyperdetailed icy blue glacier walls (foreground, weight:1.3), crystalline textures (foreground, weight:1.3), fractured ice formations (sharp, weight:1.2), transitioning seamlessly into a luminous, surreal cloudscape (background, weight:1.25), with soft, billowing volumetric clouds and radiant diffuse sky. Lighting: HDR cinematographic mapping, strong cool blue specular ice reflections, golden volumetric ambient in clouds (filmic grading). Apply region weighting: highly detailed textures prioritized for foreground and glacial walls, progressive diffusion in the upper and receding regions (sharp-to-soft focus guidance). Rendering flags: enforce high-fidelity path tracing, global illumination, ambient occlusion, CUDA/Optix acceleration, super-resolution output—minimum 8k (7680x4320 px). Stylize (strength:600), chaos (25). Reference inspiration weights: Gregory Thielker realism x1.15, James Turrell atmospheric colorfields x1.15, gentle surreal luminosity. Ensure crisp, artifact-free, professional clarity suitable for postproduction workflows.\",\n  \"negative_prompt\": \"No people, no animals, no man-made objects, no static or uninteresting backgrounds, no low resolution, no pixelation, no overlays, no distracting text, no harsh or unnatural color shifts, no exposure artifacts, no banding, no color noise, no double reflections, no lens distortion artifacts, no ghosting, no chromatic aberration, no compression loss, no visual clutter.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"final_image_prompt\": \"Immersive hyperspeed FPV drone footage soaring through a vast, majestic glacial canyon with sharp, hyperdetailed icy blue glacier walls (foreground:1.3), crystalline textures (foreground:1.3), and intricate fractured ice formations (sharp:1.2). Camera metadata: virtual action camera, 11mm equivalent ultrawide lens, f/2.8, ISO 400, 1/500s shutter, cinematic motion blur. Native 16:9 ultrawide aspect ratio (aspect:16:9:1.1) ensures professional framing. Foreground and glacial walls receive prioritized, highly detailed texturing with progressive diffusion into luminous, surreal cloudscape (background:1.25)—featuring soft, billowing volumetric clouds and radiant, diffuse sky graded with filmic mapping. Lighting: HDR cinematic tonemapping, strong cool blue ice speculars, golden volumetric ambience in clouds. Professional clarity: high-fidelity path tracing, global illumination, ambient occlusion, CUDA/Optix acceleration, super-resolution (minimum 8k, 7680x4320px) for ultra-crisp output. Rendering flags: stylize 600, chaos 25. Visual influence: Gregory Thielker realism (1.15), James Turrell atmospheric colorfields (1.15), gentle surreal luminosity. Region-weighting: foreground and glacier walls hyperdetailed, upper/receding zones soft-focused. NO people, animals, man-made objects, static scenery, low resolution, pixelation, overlays, text, harsh/unrealistic colors, exposure artifacts, banding, color noise, double reflections, lens distortion, ghosting, chromatic aberration, compression loss, visual clutter. Ready for post-production and professional edit workflows.\"\n}",
        }
      }
    }