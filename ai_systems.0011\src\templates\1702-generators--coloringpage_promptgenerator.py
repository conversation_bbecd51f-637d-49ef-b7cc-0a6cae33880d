#!/usr/bin/env python3

import sys
from pathlib import Path

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))
from processor import BaseGenerator

TEMPLATES = {

    # 1702:
    "1702-a-coloring_page_generator": {
        "title": "Coloring-Page Prompt Generator",
        "interpretation": "Your goal is **not** to draw or explain, but to **compose** a precise prompt that instructs an illustration agent to create a children-friendly, psychedelic Christmas coloring page. Execute as:",
        "transformation": "`{role=illustration_prompt_generator; input=[central_subject:str]; process=[embed_goal_negation('do not shade, only outline'), inject_style_tags('psychedelic','whimsical','festive'), set_aspect_ratio('1:1'), enforce_line_rules(weight='medium-thin', type='vector'), weave_surreal_christmas_motifs(), prohibit_text(), open_coloring_spaces()], constraints=[black_lines_only(), no_grayscale(), no_filled_areas(), english_only()], output={illustration_prompt:str}}`",
        "context": {
            "description": "Produces a fully-formed prompt for an autonomous illustration agent, guaranteeing the final   artwork matches child-friendly coloring standards and a psychedelic Christmas theme.",
            "input_focus": "A plain-language description of the main character or object (e.g., 'curious pony' or   'spiral-wrapped fox').",
            "output_focus": "One concise English string the agent can pass directly to an image-generation model.",
            "key_operations": [
                "Prefixing instructions with a goal-negation clause to forbid shading or text.",
                "Embedding mandatory stylistic tags (psychedelic, whimsical, festive, line art).",
                "Hard-setting a 1:1 square composition requirement.",
                "Enforcing even, medium-thin black vector outlines only.",
                "Blending surreal Christmas decorations (snowflakes, ornaments, candy fractals) with the subject.",
                "Ensuring large, open regions suitable for children’s coloring."
            ],
            "constraints_context": [
                "The prompt must forbid shading, grayscale, filled shapes, and background textures.",
                "No textual elements may appear in the illustration.",
                "Output must stay in English and under 120 words."
            ],
            "relevance": "Delivers a repeatable, standards-compliant prompt that any autonomous illustrator can use to   generate consistent, high-quality coloring pages."
        }

    },

}

def main():
    """Main execution function."""
    generator = BaseGenerator(
        output_dir=Path(__file__).parent.parent / "generated"
    )
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
