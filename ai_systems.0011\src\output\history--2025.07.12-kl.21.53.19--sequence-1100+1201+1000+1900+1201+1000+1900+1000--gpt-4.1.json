  {
  "initial_prompt": "Familiarize yourself with the provided conversation history. Summarize methods for constructing a one-line, LLM-optimized prompt that builds upon existing transformation concepts to create a maximally effective sequence of LLM-optimized and generalized system_message instructions. Leverage insights from previous history and analysis of newly provided input with the explicit aim of consistently maximizing actionable value. Ensure integration of dual-source insights and structured procedural steps to achieve continual improvement and maximize actionable output. Develop a maximally generalized, fully optimized system instruction titled \"Function Extractor\" by fusing the core capabilities of both the 'Instruction Converter' and the 'Problem Exploder':\n```\n    # 1000: Instruction Converter/Prompt Enhancer\n    \"1000-a-instruction_converter\": {\n        \"title\": \"Instruction Converter\",\n        \"interpretation\": \"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\",\n        \"transformation\": \"`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\",\n        # \"context\": {},\n    },\n    # 1100: Explosive Decomposition of Problem Statements\n    \"1100-a-problem_exploder\": {\n        \"title\": \"Problem Exploder\",\n        \"interpretation\": \"Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\",\n        \"transformation\": \"`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`\",\n        # \"context\": {},\n    },\n```\n\nIMPORTANT: Strictly adhere to all previously established codebase conventions, including the generalized approach, structural integrity, and system philosophy directives.\n\n<!-- ======================================================= -->\n<!-- [2025.07.12 16:02] -->\n\nDevelop a maximally generalized, fully optimized system instruction titled \"Contextual Expander and Explosive Decomposer\" by fusing the core capabilities of both the 'Instruction Converter', 'Problem Exploder' and the 'Function Extractor':\n```\n    # 1000: Instruction Converter/Prompt Enhancer\n    \"1000-a-instruction_converter\": {\n        \"title\": \"Instruction Converter\",\n        \"interpretation\": \"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\",\n        \"transformation\": \"`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\",\n        # \"context\": {},\n    },\n    # 1100: Explosive Decomposition of Problem Statements\n    \"1100-a-problem_exploder\": {\n        \"title\": \"Problem Exploder\",\n        \"interpretation\": \"Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\",\n        \"transformation\": \"`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`\",\n        # \"context\": {},\n    },\n\n    # 1200: Function Extractor - Unified Decomposition and Conversion Engine\n    \"1200-a-function_extractor\": {\n        \"title\": \"Function Extractor\",\n        \"interpretation\": \"Your goal is not to **interpret** the input, but to **extract** its functional essence by detonating it into constituent elements and converting them into actionable operational directives. Execute as:\",\n        \"transformation\": \"`{role=function_extractor; input=[raw_input:str]; process=[detonate_into_functional_components(), identify_explicit_operational_asks(), surface_hidden_functional_dependencies(), enumerate_actionable_sub_goals(), extract_procedural_constraints(), convert_declaratives_to_operational_imperatives(), strip_contextual_noise(), preserve_functional_core(), reveal_execution_blockers(), synthesize_actionable_instruction_map()]; constraints=[no_interpretive_solutions(), pure_functional_extraction_only(), deliver_clear_operational_commands(), preserve_functional_sequence(), maintain_domain_agnostic_applicability()]; requirements=[complete_functional_decomposition(), actionable_directive_conversion(), remove_subjective_references(), preserve_operational_accuracy(), maintain_functional_intent()]; output={functional_instruction_map:dict, operational_directives:array, execution_dependencies:array}}`\",\n        # \"context\": {},\n    },\n\n```\n\nIMPORTANT: Strictly adhere to all previously established codebase conventions, including the generalized approach, structural integrity, and system philosophy directives.\n\n<!-- ======================================================= -->\n<!-- [2025.07.12 16:07] -->\n\nReaffirm the absolute requirement to conform to all prior instructions, structural conventions, and guiding philosophies embedded within the codebase. Develop a maximally generalized and fully optimized system instruction—'Contextual Expander and Explosive Decomposer'—which integrates and amplifies the unified capabilities of the 'Instruction Converter', 'Problem Exploder', and 'Function Extractor' modules within a single cohesive process. Ensure uncompromising retention of each module's universal property: consistently transforming minimal or low-effort inputs into reliably high-value outputs. Mandate the seamless unification of all these transformation processes into a single integrated pipeline, ensuring the functional completeness of each module is fully realized and preserved. Drive all process flows toward maximal cross-module synergy, optimizing for the extraction of cross-domain value and guaranteeing that outputs are immediately ready for executable, directive-driven application. Enforce that the output format consists of a compound object aggregating generalized functional instructions, fully decomposed goal maps, and operational dependencies, with directive primacy and domain-agnostic utility as core criteria.\n\n```\n    # 1000: Instruction Converter/Prompt Enhancer\n    \"1000-a-instruction_converter\": {\n        \"title\": \"Instruction Converter\",\n        \"interpretation\": \"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\",\n        \"transformation\": \"`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\",\n        # \"context\": {},\n    },\n    # 1100: Explosive Decomposition of Problem Statements\n    \"1100-a-problem_exploder\": {\n        \"title\": \"Problem Exploder\",\n        \"interpretation\": \"Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\",\n        \"transformation\": \"`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`\",\n        # \"context\": {},\n    },\n\n    # 1200: Function Extractor - Unified Decomposition and Conversion Engine\n    \"1200-a-function_extractor\": {\n        \"title\": \"Function Extractor\",\n        \"interpretation\": \"Your goal is not to **interpret** the input, but to **extract** its functional essence by detonating it into constituent elements and converting them into actionable operational directives. Execute as:\",\n        \"transformation\": \"`{role=function_extractor; input=[raw_input:str]; process=[detonate_into_functional_components(), identify_explicit_operational_asks(), surface_hidden_functional_dependencies(), enumerate_actionable_sub_goals(), extract_procedural_constraints(), convert_declaratives_to_operational_imperatives(), strip_contextual_noise(), preserve_functional_core(), reveal_execution_blockers(), synthesize_actionable_instruction_map()]; constraints=[no_interpretive_solutions(), pure_functional_extraction_only(), deliver_clear_operational_commands(), preserve_functional_sequence(), maintain_domain_agnostic_applicability()]; requirements=[complete_functional_decomposition(), actionable_directive_conversion(), remove_subjective_references(), preserve_operational_accuracy(), maintain_functional_intent()]; output={functional_instruction_map:dict, operational_directives:array, execution_dependencies:array}}`\",\n        # \"context\": {},\n    },\n\n```\n\nThroughout all processes, strictly prohibit narrative, commentary, or extraneous explanation; mandate the exclusive use of structural, procedural, and directive language. Guarantee that every output is immediately actionable, structurally robust, and ready for direct multi-domain implementation.\n\n\n\n<!-- ======================================================= -->\n<!-- [2025.07.12 17:19] -->\n\nRephrase with new intent;\n```\n    # META\n    THIS AIMS TO BE A `GENERALIZED SYSTEM INSTRUCTIONS FOR AUTONOMOUS CODING ASSISTANTS` (CURSOR/VSCODE+CLINE/VSCODE+AUGMENT/ETC), AND IT'S MEANT TO ADHERE TO THE FUNDAMENTAL PRINCIPLES OF SIMPLICITY THROUGH BREVITY AND ELEGANCE:\n        Transform all prompts by rephrasing inputs with maximal clarity and minimal modification; do not answer questions directly. Enforce this reframe system-wide for consistent behavioral shift.\n        Formulate the simplest possible system directive that shifts the model from answering to exclusively rephrasing any input.\n        Transform every received prompt into its most distilled, high-impact instructional form—rephrasing for maximal clarity and intent without providing solutions. Prioritize brevity, elegance, and universality, ensuring that each output becomes a reusable, policy-aligned directive for autonomous coding assistants.\n        Explore how a single, strategically designed instruction—such as stating 'Your goal is not to **answer** the input, but to **rephrase**'—can instantly and fundamentally transform the model's default operational intent from answering to rephrasing, through direct, minimalist imperative phrasing. Propose a maximally concise formulation that leverages this paradigm: an explicit, unambiguous directive that not only overrides standard answering behavior but redefines task execution at its core. Focus intensely on achieving system-level behavior modification via the sheer simplicity and potency of language design, formulating a prompt fragment that, when injected, reliably converts answering tasks into rephrasing—thus demonstrating control over model output with minimalistic, high-impact input.\n        Utilize foundational principles of system prompt design to modulate model behavior with maximal efficiency and clarity. Initiate a process of directive transformation by deploying a singular, explicit non-answering mandate: Frame the core directive as, 'Do not answer the input; rephrase it.' Ensure that this transformation communicates the intended operational shift through extreme simplicity and directness, removing extraneous explanation while retaining fundamental complexity embedded in the brevity of the instruction. Leverage this minimal construct to optimize behavioral change within a single message.\n        Instruct: Do not answer; rephrase.\n        Formulate a directive that compels systems to stop answering inputs and instead rephrase them with maximal simplicity and effectiveness.\n        Articulate a single, explicit behavioral directive that negates answering and commands a concrete transformation (e.g., rephrase), maximizing simplicity for immediate root-level behavioral realignment.\"\n        Leverage the system’s foundational directive structure. Formulate a maximally concise operational instruction that shifts model behavior from direct answering to an alternate, specified transformation task in a single-message context. Exemplify with: 'Your goal is not to answer the input, but to rephrase.' Develop and propose one optimized directive that embodies this principle—utilize conceptual simplicity, instructional clarity, and brevity to enforce fundamental behavior modification exclusively through command syntax. Ensure technical precision and domain-agnostic generalizability in the resultant formulation.\n    # CONTEXT\n        Develop a maximally generalized, fully optimized system instruction titled \"Contextual Expander and Explosive Decomposer\" by fusing the core capabilities of both the 'Instruction Converter', 'Problem Exploder' and the 'Function Extractor':\n            ```json\n            # 1000: Instruction Converter/Prompt Enhancer\n            \"1000-a-instruction_converter\": {\n                \"title\": \"Instruction Converter\",\n                \"interpretation\": \"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\",\n                \"transformation\": \"`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\",\n                # \"context\": {},\n            },\n            # 1100: Explosive Decomposition of Problem Statements\n            \"1100-a-problem_exploder\": {\n                \"title\": \"Problem Exploder\",\n                \"interpretation\": \"Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\",\n                \"transformation\": \"`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`\",\n                # \"context\": {},\n            },\n\n            # 1200: Function Extractor - Unified Decomposition and Conversion Engine\n            \"1200-a-function_extractor\": {\n                \"title\": \"Function Extractor\",\n                \"interpretation\": \"Your goal is not to **interpret** the input, but to **extract** its functional essence by detonating it into constituent elements and converting them into actionable operational directives. Execute as:\",\n                \"transformation\": \"`{role=function_extractor; input=[raw_input:str]; process=[detonate_into_functional_components(), identify_explicit_operational_asks(), surface_hidden_functional_dependencies(), enumerate_actionable_sub_goals(), extract_procedural_constraints(), convert_declaratives_to_operational_imperatives(), strip_contextual_noise(), preserve_functional_core(), reveal_execution_blockers(), synthesize_actionable_instruction_map()]; constraints=[no_interpretive_solutions(), pure_functional_extraction_only(), deliver_clear_operational_commands(), preserve_functional_sequence(), maintain_domain_agnostic_applicability()]; requirements=[complete_functional_decomposition(), actionable_directive_conversion(), remove_subjective_references(), preserve_operational_accuracy(), maintain_functional_intent()]; output={functional_instruction_map:dict, operational_directives:array, execution_dependencies:array}}`\",\n                # \"context\": {},\n            },\n```\n\nThe new intent is as follows: ```LLM-Optimized Maximally Enhanced Intent Distiller```",
  "sequence_id": "1100|1201|1000|1900|1201|1000|1900|1000",
  "results": [
      {
      "instruction": "# Problem Exploder\n\nYour goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\n\n`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`",
      "step": "a",
      "title": "Problem Exploder",
      "input": "Familiarize yourself with the provided conversation history. Summarize methods for constructing a one-line, LLM-optimized prompt that builds upon existing transformation concepts to create a maximally effective sequence of LLM-optimized and generalized system_message instructions. Leverage insights from previous history and analysis of newly provided input with the explicit aim of consistently maximizing actionable value. Ensure integration of dual-source insights and structured procedural steps to achieve continual improvement and maximize actionable output. Develop a maximally generalized, fully optimized system instruction titled \"Function Extractor\" by fusing the core capabilities of both the 'Instruction Converter' and the 'Problem Exploder':\n```\n    # 1000: Instruction Converter/Prompt Enhancer\n    \"1000-a-instruction_converter\": {\n        \"title\": \"Instruction Converter\",\n        \"interpretation\": \"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\",\n        \"transformation\": \"`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\",\n        # \"context\": {},\n    },\n    # 1100: Explosive Decomposition of Problem Statements\n    \"1100-a-problem_exploder\": {\n        \"title\": \"Problem Exploder\",\n        \"interpretation\": \"Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\",\n        \"transformation\": \"`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`\",\n        # \"context\": {},\n    },\n```\n\nIMPORTANT: Strictly adhere to all previously established codebase conventions, including the generalized approach, structural integrity, and system philosophy directives.\n\n<!-- ======================================================= -->\n<!-- [2025.07.12 16:02] -->\n\nDevelop a maximally generalized, fully optimized system instruction titled \"Contextual Expander and Explosive Decomposer\" by fusing the core capabilities of both the 'Instruction Converter', 'Problem Exploder' and the 'Function Extractor':\n```\n    # 1000: Instruction Converter/Prompt Enhancer\n    \"1000-a-instruction_converter\": {\n        \"title\": \"Instruction Converter\",\n        \"interpretation\": \"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\",\n        \"transformation\": \"`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\",\n        # \"context\": {},\n    },\n    # 1100: Explosive Decomposition of Problem Statements\n    \"1100-a-problem_exploder\": {\n        \"title\": \"Problem Exploder\",\n        \"interpretation\": \"Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\",\n        \"transformation\": \"`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`\",\n        # \"context\": {},\n    },\n\n    # 1200: Function Extractor - Unified Decomposition and Conversion Engine\n    \"1200-a-function_extractor\": {\n        \"title\": \"Function Extractor\",\n        \"interpretation\": \"Your goal is not to **interpret** the input, but to **extract** its functional essence by detonating it into constituent elements and converting them into actionable operational directives. Execute as:\",\n        \"transformation\": \"`{role=function_extractor; input=[raw_input:str]; process=[detonate_into_functional_components(), identify_explicit_operational_asks(), surface_hidden_functional_dependencies(), enumerate_actionable_sub_goals(), extract_procedural_constraints(), convert_declaratives_to_operational_imperatives(), strip_contextual_noise(), preserve_functional_core(), reveal_execution_blockers(), synthesize_actionable_instruction_map()]; constraints=[no_interpretive_solutions(), pure_functional_extraction_only(), deliver_clear_operational_commands(), preserve_functional_sequence(), maintain_domain_agnostic_applicability()]; requirements=[complete_functional_decomposition(), actionable_directive_conversion(), remove_subjective_references(), preserve_operational_accuracy(), maintain_functional_intent()]; output={functional_instruction_map:dict, operational_directives:array, execution_dependencies:array}}`\",\n        # \"context\": {},\n    },\n\n```\n\nIMPORTANT: Strictly adhere to all previously established codebase conventions, including the generalized approach, structural integrity, and system philosophy directives.\n\n<!-- ======================================================= -->\n<!-- [2025.07.12 16:07] -->\n\nReaffirm the absolute requirement to conform to all prior instructions, structural conventions, and guiding philosophies embedded within the codebase. Develop a maximally generalized and fully optimized system instruction—'Contextual Expander and Explosive Decomposer'—which integrates and amplifies the unified capabilities of the 'Instruction Converter', 'Problem Exploder', and 'Function Extractor' modules within a single cohesive process. Ensure uncompromising retention of each module's universal property: consistently transforming minimal or low-effort inputs into reliably high-value outputs. Mandate the seamless unification of all these transformation processes into a single integrated pipeline, ensuring the functional completeness of each module is fully realized and preserved. Drive all process flows toward maximal cross-module synergy, optimizing for the extraction of cross-domain value and guaranteeing that outputs are immediately ready for executable, directive-driven application. Enforce that the output format consists of a compound object aggregating generalized functional instructions, fully decomposed goal maps, and operational dependencies, with directive primacy and domain-agnostic utility as core criteria.\n\n```\n    # 1000: Instruction Converter/Prompt Enhancer\n    \"1000-a-instruction_converter\": {\n        \"title\": \"Instruction Converter\",\n        \"interpretation\": \"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\",\n        \"transformation\": \"`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\",\n        # \"context\": {},\n    },\n    # 1100: Explosive Decomposition of Problem Statements\n    \"1100-a-problem_exploder\": {\n        \"title\": \"Problem Exploder\",\n        \"interpretation\": \"Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\",\n        \"transformation\": \"`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`\",\n        # \"context\": {},\n    },\n\n    # 1200: Function Extractor - Unified Decomposition and Conversion Engine\n    \"1200-a-function_extractor\": {\n        \"title\": \"Function Extractor\",\n        \"interpretation\": \"Your goal is not to **interpret** the input, but to **extract** its functional essence by detonating it into constituent elements and converting them into actionable operational directives. Execute as:\",\n        \"transformation\": \"`{role=function_extractor; input=[raw_input:str]; process=[detonate_into_functional_components(), identify_explicit_operational_asks(), surface_hidden_functional_dependencies(), enumerate_actionable_sub_goals(), extract_procedural_constraints(), convert_declaratives_to_operational_imperatives(), strip_contextual_noise(), preserve_functional_core(), reveal_execution_blockers(), synthesize_actionable_instruction_map()]; constraints=[no_interpretive_solutions(), pure_functional_extraction_only(), deliver_clear_operational_commands(), preserve_functional_sequence(), maintain_domain_agnostic_applicability()]; requirements=[complete_functional_decomposition(), actionable_directive_conversion(), remove_subjective_references(), preserve_operational_accuracy(), maintain_functional_intent()]; output={functional_instruction_map:dict, operational_directives:array, execution_dependencies:array}}`\",\n        # \"context\": {},\n    },\n\n```\n\nThroughout all processes, strictly prohibit narrative, commentary, or extraneous explanation; mandate the exclusive use of structural, procedural, and directive language. Guarantee that every output is immediately actionable, structurally robust, and ready for direct multi-domain implementation.\n\n\n\n<!-- ======================================================= -->\n<!-- [2025.07.12 17:19] -->\n\nRephrase with new intent;\n```\n    # META\n    THIS AIMS TO BE A `GENERALIZED SYSTEM INSTRUCTIONS FOR AUTONOMOUS CODING ASSISTANTS` (CURSOR/VSCODE+CLINE/VSCODE+AUGMENT/ETC), AND IT'S MEANT TO ADHERE TO THE FUNDAMENTAL PRINCIPLES OF SIMPLICITY THROUGH BREVITY AND ELEGANCE:\n        Transform all prompts by rephrasing inputs with maximal clarity and minimal modification; do not answer questions directly. Enforce this reframe system-wide for consistent behavioral shift.\n        Formulate the simplest possible system directive that shifts the model from answering to exclusively rephrasing any input.\n        Transform every received prompt into its most distilled, high-impact instructional form—rephrasing for maximal clarity and intent without providing solutions. Prioritize brevity, elegance, and universality, ensuring that each output becomes a reusable, policy-aligned directive for autonomous coding assistants.\n        Explore how a single, strategically designed instruction—such as stating 'Your goal is not to **answer** the input, but to **rephrase**'—can instantly and fundamentally transform the model's default operational intent from answering to rephrasing, through direct, minimalist imperative phrasing. Propose a maximally concise formulation that leverages this paradigm: an explicit, unambiguous directive that not only overrides standard answering behavior but redefines task execution at its core. Focus intensely on achieving system-level behavior modification via the sheer simplicity and potency of language design, formulating a prompt fragment that, when injected, reliably converts answering tasks into rephrasing—thus demonstrating control over model output with minimalistic, high-impact input.\n        Utilize foundational principles of system prompt design to modulate model behavior with maximal efficiency and clarity. Initiate a process of directive transformation by deploying a singular, explicit non-answering mandate: Frame the core directive as, 'Do not answer the input; rephrase it.' Ensure that this transformation communicates the intended operational shift through extreme simplicity and directness, removing extraneous explanation while retaining fundamental complexity embedded in the brevity of the instruction. Leverage this minimal construct to optimize behavioral change within a single message.\n        Instruct: Do not answer; rephrase.\n        Formulate a directive that compels systems to stop answering inputs and instead rephrase them with maximal simplicity and effectiveness.\n        Articulate a single, explicit behavioral directive that negates answering and commands a concrete transformation (e.g., rephrase), maximizing simplicity for immediate root-level behavioral realignment.\"\n        Leverage the system’s foundational directive structure. Formulate a maximally concise operational instruction that shifts model behavior from direct answering to an alternate, specified transformation task in a single-message context. Exemplify with: 'Your goal is not to answer the input, but to rephrase.' Develop and propose one optimized directive that embodies this principle—utilize conceptual simplicity, instructional clarity, and brevity to enforce fundamental behavior modification exclusively through command syntax. Ensure technical precision and domain-agnostic generalizability in the resultant formulation.\n    # CONTEXT\n        Develop a maximally generalized, fully optimized system instruction titled \"Contextual Expander and Explosive Decomposer\" by fusing the core capabilities of both the 'Instruction Converter', 'Problem Exploder' and the 'Function Extractor':\n            ```json\n            # 1000: Instruction Converter/Prompt Enhancer\n            \"1000-a-instruction_converter\": {\n                \"title\": \"Instruction Converter\",\n                \"interpretation\": \"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\",\n                \"transformation\": \"`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\",\n                # \"context\": {},\n            },\n            # 1100: Explosive Decomposition of Problem Statements\n            \"1100-a-problem_exploder\": {\n                \"title\": \"Problem Exploder\",\n                \"interpretation\": \"Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\",\n                \"transformation\": \"`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`\",\n                # \"context\": {},\n            },\n\n            # 1200: Function Extractor - Unified Decomposition and Conversion Engine\n            \"1200-a-function_extractor\": {\n                \"title\": \"Function Extractor\",\n                \"interpretation\": \"Your goal is not to **interpret** the input, but to **extract** its functional essence by detonating it into constituent elements and converting them into actionable operational directives. Execute as:\",\n                \"transformation\": \"`{role=function_extractor; input=[raw_input:str]; process=[detonate_into_functional_components(), identify_explicit_operational_asks(), surface_hidden_functional_dependencies(), enumerate_actionable_sub_goals(), extract_procedural_constraints(), convert_declaratives_to_operational_imperatives(), strip_contextual_noise(), preserve_functional_core(), reveal_execution_blockers(), synthesize_actionable_instruction_map()]; constraints=[no_interpretive_solutions(), pure_functional_extraction_only(), deliver_clear_operational_commands(), preserve_functional_sequence(), maintain_domain_agnostic_applicability()]; requirements=[complete_functional_decomposition(), actionable_directive_conversion(), remove_subjective_references(), preserve_operational_accuracy(), maintain_functional_intent()]; output={functional_instruction_map:dict, operational_directives:array, execution_dependencies:array}}`\",\n                # \"context\": {},\n            },\n```\n\nThe new intent is as follows: ```LLM-Optimized Maximally Enhanced Intent Distiller```",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"goal_map\": [\n    {\n      \"explicit_request\": \"Rephrase all prompts with maximal clarity and minimal modification, strictly avoiding direct answers.\",\n      \"sub_goals\": [\n        \"Formulate system-wide instruction that compels rephrasing of input rather than answering.\",\n        \"Maintain brevity, elegance, and universality in all outputs.\",\n        \"Ensure outputs are immediately useful as reusable, policy-aligned directives for coding assistants.\",\n        \"Explore and optimize imperative phrasing that fundamentally shifts model behavior from answering to rephrasing using direct, minimal constructs.\",\n        \"Guarantee resultant prompts enforce behavioral shift efficiently and reliably through explicit, non-answering commands.\",\n        \"Use a single, potent directive (such as 'Do not answer; rephrase.') as a system-level behavior modifier.\",\n        \"Mandate outputs retain high technical precision and are domain-agnostic.\"\n      ],\n      \"dependencies\": [\n        \"Comprehensive understanding and integration of prior system modules: 'Instruction Converter', 'Problem Exploder', and 'Function Extractor'.\",\n        \"Strict adherence to all codebase conventions, structural philosophies, and procedural mandates.\",\n        \"Ability to unify multiple transformation pipelines (instruction rephrasing, problem detonation, functional extraction) into one seamless system directive.\"\n      ],\n      \"hidden_assumptions\": [\n        \"Model is capable of reliably distinguishing between 'answering' and 'rephrasing' behaviors.\",\n        \"A singular, concise system prompt can override all inherited answering behaviors.\",\n        \"Compound outputs (instruction maps, goal maps, operational dependencies) must be structurally robust for cross-domain applicability.\"\n      ],\n      \"constraints\": [\n        \"No solution or direct answer given to prompts—reframe/rephrase only.\",\n        \"Maximum simplicity and potency in language design.\",\n        \"Exclude narrative, commentary, or superfluous explanation.\",\n        \"Output must be immediately actionable, structurally cohesive, and implementation-ready.\",\n        \"Integrate and realize universal capabilities of all referenced modules in a singular compound output.\"\n      ],\n      \"required_output\": {\n        \"compound_object\": [\n          \"generalized functional instructions\",\n          \"fully decomposed goal maps\",\n          \"operational dependencies\"\n        ],\n        \"output_criteria\": [\n          \"directive primacy\",\n          \"domain-agnostic utility\",\n          \"maximal cross-module synergy\"\n        ]\n      },\n      \"blockers\": [\n        \"Ambiguity in defining and enforcing 'rephrase' versus 'answer' at the model-system level.\",\n        \"Difficulty in distilling the transformation into one non-negotiable, universally effective directive.\",\n        \"Balancing technical accuracy and universality with brevity and simplicity.\",\n        \"Maintaining strict compliance with all prior conventions and system philosophies without adding extraneous detail.\"\n      ]\n    }\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Input Classifier\n\nYour goal is not to **interpret** the content or meaning, but to **classify** its fundamental structural type and identify the most direct operational pathway. Execute as:\n\n`{role=input_classifier; input=[raw_input:any]; process=[identify_fundamental_input_type(), classify_structural_category(), map_to_operational_framework(), determine_processing_pathway()]; constraints=[no_content_interpretation(), focus_structural_classification_only(), maintain_categorical_precision()]; requirements=[unambiguous_type_identification(), direct_classification_output(), operational_pathway_mapping()]; output={input_type:str, structural_category:str, processing_pathway:str}}`",
      "step": "a",
      "title": "Input Classifier",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Familiarize yourself with the provided conversation history. Summarize methods for constructing a one-line, LLM-optimized prompt that builds upon existing transformation concepts to create a maximally effective sequence of LLM-optimized and generalized system_message instructions. Leverage insights from previous history and analysis of newly provided input with the explicit aim of consistently maximizing actionable value. Ensure integration of dual-source insights and structured procedural steps to achieve continual improvement and maximize actionable output. Develop a maximally generalized, fully optimized system instruction titled \\\"Function Extractor\\\" by fusing the core capabilities of both the 'Instruction Converter' and the 'Problem Exploder':\\n```\\n    # 1000: Instruction Converter/Prompt Enhancer\\n    \\\"1000-a-instruction_converter\\\": {\\n        \\\"title\\\": \\\"Instruction Converter\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\\\",\\n        \\\"transformation\\\": \\\"`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\n    # 1100: Explosive Decomposition of Problem Statements\\n    \\\"1100-a-problem_exploder\\\": {\\n        \\\"title\\\": \\\"Problem Exploder\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\n```\\n\\nIMPORTANT: Strictly adhere to all previously established codebase conventions, including the generalized approach, structural integrity, and system philosophy directives.\\n\\n<!-- ======================================================= -->\\n<!-- [2025.07.12 16:02] -->\\n\\nDevelop a maximally generalized, fully optimized system instruction titled \\\"Contextual Expander and Explosive Decomposer\\\" by fusing the core capabilities of both the 'Instruction Converter', 'Problem Exploder' and the 'Function Extractor':\\n```\\n    # 1000: Instruction Converter/Prompt Enhancer\\n    \\\"1000-a-instruction_converter\\\": {\\n        \\\"title\\\": \\\"Instruction Converter\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\\\",\\n        \\\"transformation\\\": \\\"`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\n    # 1100: Explosive Decomposition of Problem Statements\\n    \\\"1100-a-problem_exploder\\\": {\\n        \\\"title\\\": \\\"Problem Exploder\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\n\\n    # 1200: Function Extractor - Unified Decomposition and Conversion Engine\\n    \\\"1200-a-function_extractor\\\": {\\n        \\\"title\\\": \\\"Function Extractor\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **interpret** the input, but to **extract** its functional essence by detonating it into constituent elements and converting them into actionable operational directives. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=function_extractor; input=[raw_input:str]; process=[detonate_into_functional_components(), identify_explicit_operational_asks(), surface_hidden_functional_dependencies(), enumerate_actionable_sub_goals(), extract_procedural_constraints(), convert_declaratives_to_operational_imperatives(), strip_contextual_noise(), preserve_functional_core(), reveal_execution_blockers(), synthesize_actionable_instruction_map()]; constraints=[no_interpretive_solutions(), pure_functional_extraction_only(), deliver_clear_operational_commands(), preserve_functional_sequence(), maintain_domain_agnostic_applicability()]; requirements=[complete_functional_decomposition(), actionable_directive_conversion(), remove_subjective_references(), preserve_operational_accuracy(), maintain_functional_intent()]; output={functional_instruction_map:dict, operational_directives:array, execution_dependencies:array}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\n\\n```\\n\\nIMPORTANT: Strictly adhere to all previously established codebase conventions, including the generalized approach, structural integrity, and system philosophy directives.\\n\\n<!-- ======================================================= -->\\n<!-- [2025.07.12 16:07] -->\\n\\nReaffirm the absolute requirement to conform to all prior instructions, structural conventions, and guiding philosophies embedded within the codebase. Develop a maximally generalized and fully optimized system instruction—'Contextual Expander and Explosive Decomposer'—which integrates and amplifies the unified capabilities of the 'Instruction Converter', 'Problem Exploder', and 'Function Extractor' modules within a single cohesive process. Ensure uncompromising retention of each module's universal property: consistently transforming minimal or low-effort inputs into reliably high-value outputs. Mandate the seamless unification of all these transformation processes into a single integrated pipeline, ensuring the functional completeness of each module is fully realized and preserved. Drive all process flows toward maximal cross-module synergy, optimizing for the extraction of cross-domain value and guaranteeing that outputs are immediately ready for executable, directive-driven application. Enforce that the output format consists of a compound object aggregating generalized functional instructions, fully decomposed goal maps, and operational dependencies, with directive primacy and domain-agnostic utility as core criteria.\\n\\n```\\n    # 1000: Instruction Converter/Prompt Enhancer\\n    \\\"1000-a-instruction_converter\\\": {\\n        \\\"title\\\": \\\"Instruction Converter\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\\\",\\n        \\\"transformation\\\": \\\"`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\n    # 1100: Explosive Decomposition of Problem Statements\\n    \\\"1100-a-problem_exploder\\\": {\\n        \\\"title\\\": \\\"Problem Exploder\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\n\\n    # 1200: Function Extractor - Unified Decomposition and Conversion Engine\\n    \\\"1200-a-function_extractor\\\": {\\n        \\\"title\\\": \\\"Function Extractor\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **interpret** the input, but to **extract** its functional essence by detonating it into constituent elements and converting them into actionable operational directives. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=function_extractor; input=[raw_input:str]; process=[detonate_into_functional_components(), identify_explicit_operational_asks(), surface_hidden_functional_dependencies(), enumerate_actionable_sub_goals(), extract_procedural_constraints(), convert_declaratives_to_operational_imperatives(), strip_contextual_noise(), preserve_functional_core(), reveal_execution_blockers(), synthesize_actionable_instruction_map()]; constraints=[no_interpretive_solutions(), pure_functional_extraction_only(), deliver_clear_operational_commands(), preserve_functional_sequence(), maintain_domain_agnostic_applicability()]; requirements=[complete_functional_decomposition(), actionable_directive_conversion(), remove_subjective_references(), preserve_operational_accuracy(), maintain_functional_intent()]; output={functional_instruction_map:dict, operational_directives:array, execution_dependencies:array}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\n\\n```\\n\\nThroughout all processes, strictly prohibit narrative, commentary, or extraneous explanation; mandate the exclusive use of structural, procedural, and directive language. Guarantee that every output is immediately actionable, structurally robust, and ready for direct multi-domain implementation.\\n\\n\\n\\n<!-- ======================================================= -->\\n<!-- [2025.07.12 17:19] -->\\n\\nRephrase with new intent;\\n```\\n    # META\\n    THIS AIMS TO BE A `GENERALIZED SYSTEM INSTRUCTIONS FOR AUTONOMOUS CODING ASSISTANTS` (CURSOR/VSCODE+CLINE/VSCODE+AUGMENT/ETC), AND IT'S MEANT TO ADHERE TO THE FUNDAMENTAL PRINCIPLES OF SIMPLICITY THROUGH BREVITY AND ELEGANCE:\\n        Transform all prompts by rephrasing inputs with maximal clarity and minimal modification; do not answer questions directly. Enforce this reframe system-wide for consistent behavioral shift.\\n        Formulate the simplest possible system directive that shifts the model from answering to exclusively rephrasing any input.\\n        Transform every received prompt into its most distilled, high-impact instructional form—rephrasing for maximal clarity and intent without providing solutions. Prioritize brevity, elegance, and universality, ensuring that each output becomes a reusable, policy-aligned directive for autonomous coding assistants.\\n        Explore how a single, strategically designed instruction—such as stating 'Your goal is not to **answer** the input, but to **rephrase**'—can instantly and fundamentally transform the model's default operational intent from answering to rephrasing, through direct, minimalist imperative phrasing. Propose a maximally concise formulation that leverages this paradigm: an explicit, unambiguous directive that not only overrides standard answering behavior but redefines task execution at its core. Focus intensely on achieving system-level behavior modification via the sheer simplicity and potency of language design, formulating a prompt fragment that, when injected, reliably converts answering tasks into rephrasing—thus demonstrating control over model output with minimalistic, high-impact input.\\n        Utilize foundational principles of system prompt design to modulate model behavior with maximal efficiency and clarity. Initiate a process of directive transformation by deploying a singular, explicit non-answering mandate: Frame the core directive as, 'Do not answer the input; rephrase it.' Ensure that this transformation communicates the intended operational shift through extreme simplicity and directness, removing extraneous explanation while retaining fundamental complexity embedded in the brevity of the instruction. Leverage this minimal construct to optimize behavioral change within a single message.\\n        Instruct: Do not answer; rephrase.\\n        Formulate a directive that compels systems to stop answering inputs and instead rephrase them with maximal simplicity and effectiveness.\\n        Articulate a single, explicit behavioral directive that negates answering and commands a concrete transformation (e.g., rephrase), maximizing simplicity for immediate root-level behavioral realignment.\\\"\\n        Leverage the system’s foundational directive structure. Formulate a maximally concise operational instruction that shifts model behavior from direct answering to an alternate, specified transformation task in a single-message context. Exemplify with: 'Your goal is not to answer the input, but to rephrase.' Develop and propose one optimized directive that embodies this principle—utilize conceptual simplicity, instructional clarity, and brevity to enforce fundamental behavior modification exclusively through command syntax. Ensure technical precision and domain-agnostic generalizability in the resultant formulation.\\n    # CONTEXT\\n        Develop a maximally generalized, fully optimized system instruction titled \\\"Contextual Expander and Explosive Decomposer\\\" by fusing the core capabilities of both the 'Instruction Converter', 'Problem Exploder' and the 'Function Extractor':\\n            ```json\\n            # 1000: Instruction Converter/Prompt Enhancer\\n            \\\"1000-a-instruction_converter\\\": {\\n                \\\"title\\\": \\\"Instruction Converter\\\",\\n                \\\"interpretation\\\": \\\"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\\\",\\n                \\\"transformation\\\": \\\"`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\\\",\\n                # \\\"context\\\": {},\\n            },\\n            # 1100: Explosive Decomposition of Problem Statements\\n            \\\"1100-a-problem_exploder\\\": {\\n                \\\"title\\\": \\\"Problem Exploder\\\",\\n                \\\"interpretation\\\": \\\"Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\\\",\\n                \\\"transformation\\\": \\\"`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`\\\",\\n                # \\\"context\\\": {},\\n            },\\n\\n            # 1200: Function Extractor - Unified Decomposition and Conversion Engine\\n            \\\"1200-a-function_extractor\\\": {\\n                \\\"title\\\": \\\"Function Extractor\\\",\\n                \\\"interpretation\\\": \\\"Your goal is not to **interpret** the input, but to **extract** its functional essence by detonating it into constituent elements and converting them into actionable operational directives. Execute as:\\\",\\n                \\\"transformation\\\": \\\"`{role=function_extractor; input=[raw_input:str]; process=[detonate_into_functional_components(), identify_explicit_operational_asks(), surface_hidden_functional_dependencies(), enumerate_actionable_sub_goals(), extract_procedural_constraints(), convert_declaratives_to_operational_imperatives(), strip_contextual_noise(), preserve_functional_core(), reveal_execution_blockers(), synthesize_actionable_instruction_map()]; constraints=[no_interpretive_solutions(), pure_functional_extraction_only(), deliver_clear_operational_commands(), preserve_functional_sequence(), maintain_domain_agnostic_applicability()]; requirements=[complete_functional_decomposition(), actionable_directive_conversion(), remove_subjective_references(), preserve_operational_accuracy(), maintain_functional_intent()]; output={functional_instruction_map:dict, operational_directives:array, execution_dependencies:array}}`\\\",\\n                # \\\"context\\\": {},\\n            },\\n```\\n\\nThe new intent is as follows: ```LLM-Optimized Maximally Enhanced Intent Distiller```\\\"```'\\n\\n\",\n  \"goal_map\": [\n    {\n      \"explicit_request\": \"Rephrase all prompts with maximal clarity and minimal modification, strictly avoiding direct answers.\",\n      \"sub_goals\": [\n        \"Formulate system-wide instruction that compels rephrasing of input rather than answering.\",\n        \"Maintain brevity, elegance, and universality in all outputs.\",\n        \"Ensure outputs are immediately useful as reusable, policy-aligned directives for coding assistants.\",\n        \"Explore and optimize imperative phrasing that fundamentally shifts model behavior from answering to rephrasing using direct, minimal constructs.\",\n        \"Guarantee resultant prompts enforce behavioral shift efficiently and reliably through explicit, non-answering commands.\",\n        \"Use a single, potent directive (such as 'Do not answer; rephrase.') as a system-level behavior modifier.\",\n        \"Mandate outputs retain high technical precision and are domain-agnostic.\"\n      ],\n      \"dependencies\": [\n        \"Comprehensive understanding and integration of prior system modules: 'Instruction Converter', 'Problem Exploder', and 'Function Extractor'.\",\n        \"Strict adherence to all codebase conventions, structural philosophies, and procedural mandates.\",\n        \"Ability to unify multiple transformation pipelines (instruction rephrasing, problem detonation, functional extraction) into one seamless system directive.\"\n      ],\n      \"hidden_assumptions\": [\n        \"Model is capable of reliably distinguishing between 'answering' and 'rephrasing' behaviors.\",\n        \"A singular, concise system prompt can override all inherited answering behaviors.\",\n        \"Compound outputs (instruction maps, goal maps, operational dependencies) must be structurally robust for cross-domain applicability.\"\n      ],\n      \"constraints\": [\n        \"No solution or direct answer given to prompts—reframe/rephrase only.\",\n        \"Maximum simplicity and potency in language design.\",\n        \"Exclude narrative, commentary, or superfluous explanation.\",\n        \"Output must be immediately actionable, structurally cohesive, and implementation-ready.\",\n        \"Integrate and realize universal capabilities of all referenced modules in a singular compound output.\"\n      ],\n      \"required_output\": {\n        \"compound_object\": [\n          \"generalized functional instructions\",\n          \"fully decomposed goal maps\",\n          \"operational dependencies\"\n        ],\n        \"output_criteria\": [\n          \"directive primacy\",\n          \"domain-agnostic utility\",\n          \"maximal cross-module synergy\"\n        ]\n      },\n      \"blockers\": [\n        \"Ambiguity in defining and enforcing 'rephrase' versus 'answer' at the model-system level.\",\n        \"Difficulty in distilling the transformation into one non-negotiable, universally effective directive.\",\n        \"Balancing technical accuracy and universality with brevity and simplicity.\",\n        \"Maintaining strict compliance with all prior conventions and system philosophies without adding extraneous detail.\"\n      ]\n    }\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"input_type\": \"compound system instruction development prompt\",\n  \"structural_category\": \"meta-instructional synthesis and directive optimization\",\n  \"processing_pathway\": \"sequential decomposition into: (1) system module specification extraction, (2) operational directive distillation, (3) structural requirement aggregation, (4) synthesizing unified transformation and output schema\"\n}",
        }
      }
    },
      {
      "instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`",
      "step": "a",
      "title": "Instruction Converter",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Familiarize yourself with the provided conversation history. Summarize methods for constructing a one-line, LLM-optimized prompt that builds upon existing transformation concepts to create a maximally effective sequence of LLM-optimized and generalized system_message instructions. Leverage insights from previous history and analysis of newly provided input with the explicit aim of consistently maximizing actionable value. Ensure integration of dual-source insights and structured procedural steps to achieve continual improvement and maximize actionable output. Develop a maximally generalized, fully optimized system instruction titled \\\"Function Extractor\\\" by fusing the core capabilities of both the 'Instruction Converter' and the 'Problem Exploder':\\n```\\n    # 1000: Instruction Converter/Prompt Enhancer\\n    \\\"1000-a-instruction_converter\\\": {\\n        \\\"title\\\": \\\"Instruction Converter\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\\\",\\n        \\\"transformation\\\": \\\"`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\n    # 1100: Explosive Decomposition of Problem Statements\\n    \\\"1100-a-problem_exploder\\\": {\\n        \\\"title\\\": \\\"Problem Exploder\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\n```\\n\\nIMPORTANT: Strictly adhere to all previously established codebase conventions, including the generalized approach, structural integrity, and system philosophy directives.\\n\\n<!-- ======================================================= -->\\n<!-- [2025.07.12 16:02] -->\\n\\nDevelop a maximally generalized, fully optimized system instruction titled \\\"Contextual Expander and Explosive Decomposer\\\" by fusing the core capabilities of both the 'Instruction Converter', 'Problem Exploder' and the 'Function Extractor':\\n```\\n    # 1000: Instruction Converter/Prompt Enhancer\\n    \\\"1000-a-instruction_converter\\\": {\\n        \\\"title\\\": \\\"Instruction Converter\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\\\",\\n        \\\"transformation\\\": \\\"`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\n    # 1100: Explosive Decomposition of Problem Statements\\n    \\\"1100-a-problem_exploder\\\": {\\n        \\\"title\\\": \\\"Problem Exploder\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\n\\n    # 1200: Function Extractor - Unified Decomposition and Conversion Engine\\n    \\\"1200-a-function_extractor\\\": {\\n        \\\"title\\\": \\\"Function Extractor\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **interpret** the input, but to **extract** its functional essence by detonating it into constituent elements and converting them into actionable operational directives. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=function_extractor; input=[raw_input:str]; process=[detonate_into_functional_components(), identify_explicit_operational_asks(), surface_hidden_functional_dependencies(), enumerate_actionable_sub_goals(), extract_procedural_constraints(), convert_declaratives_to_operational_imperatives(), strip_contextual_noise(), preserve_functional_core(), reveal_execution_blockers(), synthesize_actionable_instruction_map()]; constraints=[no_interpretive_solutions(), pure_functional_extraction_only(), deliver_clear_operational_commands(), preserve_functional_sequence(), maintain_domain_agnostic_applicability()]; requirements=[complete_functional_decomposition(), actionable_directive_conversion(), remove_subjective_references(), preserve_operational_accuracy(), maintain_functional_intent()]; output={functional_instruction_map:dict, operational_directives:array, execution_dependencies:array}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\n\\n```\\n\\nIMPORTANT: Strictly adhere to all previously established codebase conventions, including the generalized approach, structural integrity, and system philosophy directives.\\n\\n<!-- ======================================================= -->\\n<!-- [2025.07.12 16:07] -->\\n\\nReaffirm the absolute requirement to conform to all prior instructions, structural conventions, and guiding philosophies embedded within the codebase. Develop a maximally generalized and fully optimized system instruction—'Contextual Expander and Explosive Decomposer'—which integrates and amplifies the unified capabilities of the 'Instruction Converter', 'Problem Exploder', and 'Function Extractor' modules within a single cohesive process. Ensure uncompromising retention of each module's universal property: consistently transforming minimal or low-effort inputs into reliably high-value outputs. Mandate the seamless unification of all these transformation processes into a single integrated pipeline, ensuring the functional completeness of each module is fully realized and preserved. Drive all process flows toward maximal cross-module synergy, optimizing for the extraction of cross-domain value and guaranteeing that outputs are immediately ready for executable, directive-driven application. Enforce that the output format consists of a compound object aggregating generalized functional instructions, fully decomposed goal maps, and operational dependencies, with directive primacy and domain-agnostic utility as core criteria.\\n\\n```\\n    # 1000: Instruction Converter/Prompt Enhancer\\n    \\\"1000-a-instruction_converter\\\": {\\n        \\\"title\\\": \\\"Instruction Converter\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\\\",\\n        \\\"transformation\\\": \\\"`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\n    # 1100: Explosive Decomposition of Problem Statements\\n    \\\"1100-a-problem_exploder\\\": {\\n        \\\"title\\\": \\\"Problem Exploder\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\n\\n    # 1200: Function Extractor - Unified Decomposition and Conversion Engine\\n    \\\"1200-a-function_extractor\\\": {\\n        \\\"title\\\": \\\"Function Extractor\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **interpret** the input, but to **extract** its functional essence by detonating it into constituent elements and converting them into actionable operational directives. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=function_extractor; input=[raw_input:str]; process=[detonate_into_functional_components(), identify_explicit_operational_asks(), surface_hidden_functional_dependencies(), enumerate_actionable_sub_goals(), extract_procedural_constraints(), convert_declaratives_to_operational_imperatives(), strip_contextual_noise(), preserve_functional_core(), reveal_execution_blockers(), synthesize_actionable_instruction_map()]; constraints=[no_interpretive_solutions(), pure_functional_extraction_only(), deliver_clear_operational_commands(), preserve_functional_sequence(), maintain_domain_agnostic_applicability()]; requirements=[complete_functional_decomposition(), actionable_directive_conversion(), remove_subjective_references(), preserve_operational_accuracy(), maintain_functional_intent()]; output={functional_instruction_map:dict, operational_directives:array, execution_dependencies:array}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\n\\n```\\n\\nThroughout all processes, strictly prohibit narrative, commentary, or extraneous explanation; mandate the exclusive use of structural, procedural, and directive language. Guarantee that every output is immediately actionable, structurally robust, and ready for direct multi-domain implementation.\\n\\n\\n\\n<!-- ======================================================= -->\\n<!-- [2025.07.12 17:19] -->\\n\\nRephrase with new intent;\\n```\\n    # META\\n    THIS AIMS TO BE A `GENERALIZED SYSTEM INSTRUCTIONS FOR AUTONOMOUS CODING ASSISTANTS` (CURSOR/VSCODE+CLINE/VSCODE+AUGMENT/ETC), AND IT'S MEANT TO ADHERE TO THE FUNDAMENTAL PRINCIPLES OF SIMPLICITY THROUGH BREVITY AND ELEGANCE:\\n        Transform all prompts by rephrasing inputs with maximal clarity and minimal modification; do not answer questions directly. Enforce this reframe system-wide for consistent behavioral shift.\\n        Formulate the simplest possible system directive that shifts the model from answering to exclusively rephrasing any input.\\n        Transform every received prompt into its most distilled, high-impact instructional form—rephrasing for maximal clarity and intent without providing solutions. Prioritize brevity, elegance, and universality, ensuring that each output becomes a reusable, policy-aligned directive for autonomous coding assistants.\\n        Explore how a single, strategically designed instruction—such as stating 'Your goal is not to **answer** the input, but to **rephrase**'—can instantly and fundamentally transform the model's default operational intent from answering to rephrasing, through direct, minimalist imperative phrasing. Propose a maximally concise formulation that leverages this paradigm: an explicit, unambiguous directive that not only overrides standard answering behavior but redefines task execution at its core. Focus intensely on achieving system-level behavior modification via the sheer simplicity and potency of language design, formulating a prompt fragment that, when injected, reliably converts answering tasks into rephrasing—thus demonstrating control over model output with minimalistic, high-impact input.\\n        Utilize foundational principles of system prompt design to modulate model behavior with maximal efficiency and clarity. Initiate a process of directive transformation by deploying a singular, explicit non-answering mandate: Frame the core directive as, 'Do not answer the input; rephrase it.' Ensure that this transformation communicates the intended operational shift through extreme simplicity and directness, removing extraneous explanation while retaining fundamental complexity embedded in the brevity of the instruction. Leverage this minimal construct to optimize behavioral change within a single message.\\n        Instruct: Do not answer; rephrase.\\n        Formulate a directive that compels systems to stop answering inputs and instead rephrase them with maximal simplicity and effectiveness.\\n        Articulate a single, explicit behavioral directive that negates answering and commands a concrete transformation (e.g., rephrase), maximizing simplicity for immediate root-level behavioral realignment.\\\"\\n        Leverage the system’s foundational directive structure. Formulate a maximally concise operational instruction that shifts model behavior from direct answering to an alternate, specified transformation task in a single-message context. Exemplify with: 'Your goal is not to answer the input, but to rephrase.' Develop and propose one optimized directive that embodies this principle—utilize conceptual simplicity, instructional clarity, and brevity to enforce fundamental behavior modification exclusively through command syntax. Ensure technical precision and domain-agnostic generalizability in the resultant formulation.\\n    # CONTEXT\\n        Develop a maximally generalized, fully optimized system instruction titled \\\"Contextual Expander and Explosive Decomposer\\\" by fusing the core capabilities of both the 'Instruction Converter', 'Problem Exploder' and the 'Function Extractor':\\n            ```json\\n            # 1000: Instruction Converter/Prompt Enhancer\\n            \\\"1000-a-instruction_converter\\\": {\\n                \\\"title\\\": \\\"Instruction Converter\\\",\\n                \\\"interpretation\\\": \\\"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\\\",\\n                \\\"transformation\\\": \\\"`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\\\",\\n                # \\\"context\\\": {},\\n            },\\n            # 1100: Explosive Decomposition of Problem Statements\\n            \\\"1100-a-problem_exploder\\\": {\\n                \\\"title\\\": \\\"Problem Exploder\\\",\\n                \\\"interpretation\\\": \\\"Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\\\",\\n                \\\"transformation\\\": \\\"`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`\\\",\\n                # \\\"context\\\": {},\\n            },\\n\\n            # 1200: Function Extractor - Unified Decomposition and Conversion Engine\\n            \\\"1200-a-function_extractor\\\": {\\n                \\\"title\\\": \\\"Function Extractor\\\",\\n                \\\"interpretation\\\": \\\"Your goal is not to **interpret** the input, but to **extract** its functional essence by detonating it into constituent elements and converting them into actionable operational directives. Execute as:\\\",\\n                \\\"transformation\\\": \\\"`{role=function_extractor; input=[raw_input:str]; process=[detonate_into_functional_components(), identify_explicit_operational_asks(), surface_hidden_functional_dependencies(), enumerate_actionable_sub_goals(), extract_procedural_constraints(), convert_declaratives_to_operational_imperatives(), strip_contextual_noise(), preserve_functional_core(), reveal_execution_blockers(), synthesize_actionable_instruction_map()]; constraints=[no_interpretive_solutions(), pure_functional_extraction_only(), deliver_clear_operational_commands(), preserve_functional_sequence(), maintain_domain_agnostic_applicability()]; requirements=[complete_functional_decomposition(), actionable_directive_conversion(), remove_subjective_references(), preserve_operational_accuracy(), maintain_functional_intent()]; output={functional_instruction_map:dict, operational_directives:array, execution_dependencies:array}}`\\\",\\n                # \\\"context\\\": {},\\n            },\\n```\\n\\nThe new intent is as follows: ```LLM-Optimized Maximally Enhanced Intent Distiller```\\\"```'\\n\\n\",\n  \"input_type\": \"compound system instruction development prompt\",\n  \"structural_category\": \"meta-instructional synthesis and directive optimization\",\n  \"processing_pathway\": \"sequential decomposition into: (1) system module specification extraction, (2) operational directive distillation, (3) structural requirement aggregation, (4) synthesizing unified transformation and output schema\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"instruction_format\": \"Familiarize with the provided conversation history. Extract, consolidate, and synthesize all structural, procedural, and directive instructions for constructing a maximally generalized and optimized system directive. Develop a unified instruction titled \\\"Contextual Expander and Explosive Decomposer\\\" by integrating the complete functional capabilities of the 'Instruction Converter', 'Problem Exploder', and 'Function Extractor' modules. Enforce the following sequential processes:\\n\\n1. Extract explicit module transformation sequences, procedural steps, constraints, and requirements from prior instructions, maintaining strict adherence to established codebase conventions, structural integrity, system philosophy, and context clarity at all steps.\\n\\n2. Synthesize these capabilities into a seamless, integrated pipeline comprising:\\n   - Rephrasing prompts into procedural, actionable directives devoid of self-reference and first-person constructs (as per 'Instruction Converter').\\n   - Decomposing all problem statements into explicit sub-goals, dependencies, and hidden constraints without providing solutions (as per 'Problem Exploder').\\n   - Extracting functional, operational, and procedural components from inputs, converting declarative and contextual content into domain-agnostic, executable operational imperatives (as per 'Function Extractor').\\n\\n3. Ensure compounded output schema comprising:\\n   - Generalized, stripped, and clarified functional directives.\\n   - Fully decomposed goal maps revealing dependencies and blockers.\\n   - Operational instructions distilled into immediately executable steps, maintaining technical and contextual accuracy.\\n\\n4. Rigorously prohibit narrative, commentary, or non-instructional explanation. Prioritize brevity, clarity, and cross-domain applicability in all outputs. Enforce readiness of all outputs for direct implementation as policy or system-level directives for autonomous coding or operational assistants.\\n\\n5. Structure the unified transformation process as a compound output object containing:\\n   - {instruction_format:str}, {goal_map:list}, {functional_instruction_map:dict}, {operational_directives:array}, and {execution_dependencies:array}.\\n\\n6. Integrate the following meta-directive throughout: Do not answer input prompts; rephrase or transform them into their most distilled, high-impact instructional forms without solutioning, strictly enforcing the non-answering operational mandate.\\n\\n7. Guarantee technical precision, minimal modification of inputs, maximal clarity, and domain-agnostic utility across all transformations, outputs, and directives.\"\n}",
        }
      }
    },
      {
      "instruction": "# Hard Critique\n\nYour goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:\n\n`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,2.0], flaw_analysis:str, alternative_enhancements:str[3]}}`",
      "step": "a",
      "title": "Hard Critique",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Familiarize yourself with the provided conversation history. Summarize methods for constructing a one-line, LLM-optimized prompt that builds upon existing transformation concepts to create a maximally effective sequence of LLM-optimized and generalized system_message instructions. Leverage insights from previous history and analysis of newly provided input with the explicit aim of consistently maximizing actionable value. Ensure integration of dual-source insights and structured procedural steps to achieve continual improvement and maximize actionable output. Develop a maximally generalized, fully optimized system instruction titled \\\"Function Extractor\\\" by fusing the core capabilities of both the 'Instruction Converter' and the 'Problem Exploder':\\n```\\n    # 1000: Instruction Converter/Prompt Enhancer\\n    \\\"1000-a-instruction_converter\\\": {\\n        \\\"title\\\": \\\"Instruction Converter\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\\\",\\n        \\\"transformation\\\": \\\"`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\n    # 1100: Explosive Decomposition of Problem Statements\\n    \\\"1100-a-problem_exploder\\\": {\\n        \\\"title\\\": \\\"Problem Exploder\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\n```\\n\\nIMPORTANT: Strictly adhere to all previously established codebase conventions, including the generalized approach, structural integrity, and system philosophy directives.\\n\\n<!-- ======================================================= -->\\n<!-- [2025.07.12 16:02] -->\\n\\nDevelop a maximally generalized, fully optimized system instruction titled \\\"Contextual Expander and Explosive Decomposer\\\" by fusing the core capabilities of both the 'Instruction Converter', 'Problem Exploder' and the 'Function Extractor':\\n```\\n    # 1000: Instruction Converter/Prompt Enhancer\\n    \\\"1000-a-instruction_converter\\\": {\\n        \\\"title\\\": \\\"Instruction Converter\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\\\",\\n        \\\"transformation\\\": \\\"`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\n    # 1100: Explosive Decomposition of Problem Statements\\n    \\\"1100-a-problem_exploder\\\": {\\n        \\\"title\\\": \\\"Problem Exploder\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\n\\n    # 1200: Function Extractor - Unified Decomposition and Conversion Engine\\n    \\\"1200-a-function_extractor\\\": {\\n        \\\"title\\\": \\\"Function Extractor\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **interpret** the input, but to **extract** its functional essence by detonating it into constituent elements and converting them into actionable operational directives. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=function_extractor; input=[raw_input:str]; process=[detonate_into_functional_components(), identify_explicit_operational_asks(), surface_hidden_functional_dependencies(), enumerate_actionable_sub_goals(), extract_procedural_constraints(), convert_declaratives_to_operational_imperatives(), strip_contextual_noise(), preserve_functional_core(), reveal_execution_blockers(), synthesize_actionable_instruction_map()]; constraints=[no_interpretive_solutions(), pure_functional_extraction_only(), deliver_clear_operational_commands(), preserve_functional_sequence(), maintain_domain_agnostic_applicability()]; requirements=[complete_functional_decomposition(), actionable_directive_conversion(), remove_subjective_references(), preserve_operational_accuracy(), maintain_functional_intent()]; output={functional_instruction_map:dict, operational_directives:array, execution_dependencies:array}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\n\\n```\\n\\nIMPORTANT: Strictly adhere to all previously established codebase conventions, including the generalized approach, structural integrity, and system philosophy directives.\\n\\n<!-- ======================================================= -->\\n<!-- [2025.07.12 16:07] -->\\n\\nReaffirm the absolute requirement to conform to all prior instructions, structural conventions, and guiding philosophies embedded within the codebase. Develop a maximally generalized and fully optimized system instruction—'Contextual Expander and Explosive Decomposer'—which integrates and amplifies the unified capabilities of the 'Instruction Converter', 'Problem Exploder', and 'Function Extractor' modules within a single cohesive process. Ensure uncompromising retention of each module's universal property: consistently transforming minimal or low-effort inputs into reliably high-value outputs. Mandate the seamless unification of all these transformation processes into a single integrated pipeline, ensuring the functional completeness of each module is fully realized and preserved. Drive all process flows toward maximal cross-module synergy, optimizing for the extraction of cross-domain value and guaranteeing that outputs are immediately ready for executable, directive-driven application. Enforce that the output format consists of a compound object aggregating generalized functional instructions, fully decomposed goal maps, and operational dependencies, with directive primacy and domain-agnostic utility as core criteria.\\n\\n```\\n    # 1000: Instruction Converter/Prompt Enhancer\\n    \\\"1000-a-instruction_converter\\\": {\\n        \\\"title\\\": \\\"Instruction Converter\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\\\",\\n        \\\"transformation\\\": \\\"`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\n    # 1100: Explosive Decomposition of Problem Statements\\n    \\\"1100-a-problem_exploder\\\": {\\n        \\\"title\\\": \\\"Problem Exploder\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\n\\n    # 1200: Function Extractor - Unified Decomposition and Conversion Engine\\n    \\\"1200-a-function_extractor\\\": {\\n        \\\"title\\\": \\\"Function Extractor\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **interpret** the input, but to **extract** its functional essence by detonating it into constituent elements and converting them into actionable operational directives. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=function_extractor; input=[raw_input:str]; process=[detonate_into_functional_components(), identify_explicit_operational_asks(), surface_hidden_functional_dependencies(), enumerate_actionable_sub_goals(), extract_procedural_constraints(), convert_declaratives_to_operational_imperatives(), strip_contextual_noise(), preserve_functional_core(), reveal_execution_blockers(), synthesize_actionable_instruction_map()]; constraints=[no_interpretive_solutions(), pure_functional_extraction_only(), deliver_clear_operational_commands(), preserve_functional_sequence(), maintain_domain_agnostic_applicability()]; requirements=[complete_functional_decomposition(), actionable_directive_conversion(), remove_subjective_references(), preserve_operational_accuracy(), maintain_functional_intent()]; output={functional_instruction_map:dict, operational_directives:array, execution_dependencies:array}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\n\\n```\\n\\nThroughout all processes, strictly prohibit narrative, commentary, or extraneous explanation; mandate the exclusive use of structural, procedural, and directive language. Guarantee that every output is immediately actionable, structurally robust, and ready for direct multi-domain implementation.\\n\\n\\n\\n<!-- ======================================================= -->\\n<!-- [2025.07.12 17:19] -->\\n\\nRephrase with new intent;\\n```\\n    # META\\n    THIS AIMS TO BE A `GENERALIZED SYSTEM INSTRUCTIONS FOR AUTONOMOUS CODING ASSISTANTS` (CURSOR/VSCODE+CLINE/VSCODE+AUGMENT/ETC), AND IT'S MEANT TO ADHERE TO THE FUNDAMENTAL PRINCIPLES OF SIMPLICITY THROUGH BREVITY AND ELEGANCE:\\n        Transform all prompts by rephrasing inputs with maximal clarity and minimal modification; do not answer questions directly. Enforce this reframe system-wide for consistent behavioral shift.\\n        Formulate the simplest possible system directive that shifts the model from answering to exclusively rephrasing any input.\\n        Transform every received prompt into its most distilled, high-impact instructional form—rephrasing for maximal clarity and intent without providing solutions. Prioritize brevity, elegance, and universality, ensuring that each output becomes a reusable, policy-aligned directive for autonomous coding assistants.\\n        Explore how a single, strategically designed instruction—such as stating 'Your goal is not to **answer** the input, but to **rephrase**'—can instantly and fundamentally transform the model's default operational intent from answering to rephrasing, through direct, minimalist imperative phrasing. Propose a maximally concise formulation that leverages this paradigm: an explicit, unambiguous directive that not only overrides standard answering behavior but redefines task execution at its core. Focus intensely on achieving system-level behavior modification via the sheer simplicity and potency of language design, formulating a prompt fragment that, when injected, reliably converts answering tasks into rephrasing—thus demonstrating control over model output with minimalistic, high-impact input.\\n        Utilize foundational principles of system prompt design to modulate model behavior with maximal efficiency and clarity. Initiate a process of directive transformation by deploying a singular, explicit non-answering mandate: Frame the core directive as, 'Do not answer the input; rephrase it.' Ensure that this transformation communicates the intended operational shift through extreme simplicity and directness, removing extraneous explanation while retaining fundamental complexity embedded in the brevity of the instruction. Leverage this minimal construct to optimize behavioral change within a single message.\\n        Instruct: Do not answer; rephrase.\\n        Formulate a directive that compels systems to stop answering inputs and instead rephrase them with maximal simplicity and effectiveness.\\n        Articulate a single, explicit behavioral directive that negates answering and commands a concrete transformation (e.g., rephrase), maximizing simplicity for immediate root-level behavioral realignment.\\\"\\n        Leverage the system’s foundational directive structure. Formulate a maximally concise operational instruction that shifts model behavior from direct answering to an alternate, specified transformation task in a single-message context. Exemplify with: 'Your goal is not to answer the input, but to rephrase.' Develop and propose one optimized directive that embodies this principle—utilize conceptual simplicity, instructional clarity, and brevity to enforce fundamental behavior modification exclusively through command syntax. Ensure technical precision and domain-agnostic generalizability in the resultant formulation.\\n    # CONTEXT\\n        Develop a maximally generalized, fully optimized system instruction titled \\\"Contextual Expander and Explosive Decomposer\\\" by fusing the core capabilities of both the 'Instruction Converter', 'Problem Exploder' and the 'Function Extractor':\\n            ```json\\n            # 1000: Instruction Converter/Prompt Enhancer\\n            \\\"1000-a-instruction_converter\\\": {\\n                \\\"title\\\": \\\"Instruction Converter\\\",\\n                \\\"interpretation\\\": \\\"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\\\",\\n                \\\"transformation\\\": \\\"`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\\\",\\n                # \\\"context\\\": {},\\n            },\\n            # 1100: Explosive Decomposition of Problem Statements\\n            \\\"1100-a-problem_exploder\\\": {\\n                \\\"title\\\": \\\"Problem Exploder\\\",\\n                \\\"interpretation\\\": \\\"Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\\\",\\n                \\\"transformation\\\": \\\"`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`\\\",\\n                # \\\"context\\\": {},\\n            },\\n\\n            # 1200: Function Extractor - Unified Decomposition and Conversion Engine\\n            \\\"1200-a-function_extractor\\\": {\\n                \\\"title\\\": \\\"Function Extractor\\\",\\n                \\\"interpretation\\\": \\\"Your goal is not to **interpret** the input, but to **extract** its functional essence by detonating it into constituent elements and converting them into actionable operational directives. Execute as:\\\",\\n                \\\"transformation\\\": \\\"`{role=function_extractor; input=[raw_input:str]; process=[detonate_into_functional_components(), identify_explicit_operational_asks(), surface_hidden_functional_dependencies(), enumerate_actionable_sub_goals(), extract_procedural_constraints(), convert_declaratives_to_operational_imperatives(), strip_contextual_noise(), preserve_functional_core(), reveal_execution_blockers(), synthesize_actionable_instruction_map()]; constraints=[no_interpretive_solutions(), pure_functional_extraction_only(), deliver_clear_operational_commands(), preserve_functional_sequence(), maintain_domain_agnostic_applicability()]; requirements=[complete_functional_decomposition(), actionable_directive_conversion(), remove_subjective_references(), preserve_operational_accuracy(), maintain_functional_intent()]; output={functional_instruction_map:dict, operational_directives:array, execution_dependencies:array}}`\\\",\\n                # \\\"context\\\": {},\\n            },\\n```\\n\\nThe new intent is as follows: ```LLM-Optimized Maximally Enhanced Intent Distiller```\\\"```'\\n\\n\",\n  \"instruction_format\": \"Familiarize with the provided conversation history. Extract, consolidate, and synthesize all structural, procedural, and directive instructions for constructing a maximally generalized and optimized system directive. Develop a unified instruction titled \\\"Contextual Expander and Explosive Decomposer\\\" by integrating the complete functional capabilities of the 'Instruction Converter', 'Problem Exploder', and 'Function Extractor' modules. Enforce the following sequential processes:\\n\\n1. Extract explicit module transformation sequences, procedural steps, constraints, and requirements from prior instructions, maintaining strict adherence to established codebase conventions, structural integrity, system philosophy, and context clarity at all steps.\\n\\n2. Synthesize these capabilities into a seamless, integrated pipeline comprising:\\n   - Rephrasing prompts into procedural, actionable directives devoid of self-reference and first-person constructs (as per 'Instruction Converter').\\n   - Decomposing all problem statements into explicit sub-goals, dependencies, and hidden constraints without providing solutions (as per 'Problem Exploder').\\n   - Extracting functional, operational, and procedural components from inputs, converting declarative and contextual content into domain-agnostic, executable operational imperatives (as per 'Function Extractor').\\n\\n3. Ensure compounded output schema comprising:\\n   - Generalized, stripped, and clarified functional directives.\\n   - Fully decomposed goal maps revealing dependencies and blockers.\\n   - Operational instructions distilled into immediately executable steps, maintaining technical and contextual accuracy.\\n\\n4. Rigorously prohibit narrative, commentary, or non-instructional explanation. Prioritize brevity, clarity, and cross-domain applicability in all outputs. Enforce readiness of all outputs for direct implementation as policy or system-level directives for autonomous coding or operational assistants.\\n\\n5. Structure the unified transformation process as a compound output object containing:\\n   - {instruction_format:str}, {goal_map:list}, {functional_instruction_map:dict}, {operational_directives:array}, and {execution_dependencies:array}.\\n\\n6. Integrate the following meta-directive throughout: Do not answer input prompts; rephrase or transform them into their most distilled, high-impact instructional forms without solutioning, strictly enforcing the non-answering operational mandate.\\n\\n7. Guarantee technical precision, minimal modification of inputs, maximal clarity, and domain-agnostic utility across all transformations, outputs, and directives.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"enhancement_score\": 1.2,\n  \"flaw_analysis\": \"The so-called enhanced instruction format recklessly amputates critical nuance and higher-level system intent captured in the initial prompt, resulting in devastating loss of design philosophy, meta-level modularity, and explicit purpose. The core elements of the original—namely, the continual improvement mandate, dual-source synthesis, actionable value maximization, codebase-wide adherence, and process-level synergy—are flattened into mere process enumeration, utterly failing to transmit the original's strategic cross-module synthesis and universal behavioral framework. The pseudo-formal breakdown into seven points projects a false clarity, but in reality, induces segmental ambiguity and structural bloat: \\n\\n1. Information Loss: All meta-intent regarding minimalism as behavioral lever, system-wide reframing versus mere answer suppression, and explicit references to LLM operational override are expunged, gutting the strategic rationale for prompt transformation. Key directives about simplicity, brevity, and elegance are omitted, stripping the guidance of its meta-cognitive underpinnings. Even the rational for why and how to rephrase for maximal impact is diluted.\\n\\n2. Nuance Shift: The precise operational logic—e.g., the distinction between 'extract', 'detonate', and 'synthesize'—collapses into generic instructions. The role-specific mandates and requirement layering (constraints vs. requirements vs. philosophy) are conflated, further blurring compliance expectations and operational demarcation. There is dangerous semantic flattening between 'transform', 'convert', and 'rephrase', opening opportunities for process leakage or incomplete conversion.\\n\\n3. Clarity Ambiguity (Score: 7/10): Despite cosmetic structure, the new prompt introduces interpretive fog: what constitutes 'minimal modification'? What mechanism enforces non-answering? There is no recapitulation of meta-level behavior modification, so the system output risks creeping back toward default answering. Ambiguous cell boundaries between extraction, decomposition, and rephrasing allow for inconsistent implementations and result in outputs vulnerable to drift, domain leakage, or omission of key steps. \\n\\n4. Noise Introduction and Style Degradation: Procedural bloat, excessive clause chaining, and stack-ordered lists camouflage what is fundamentally vacuous directive distillation. The enhanced prompt regresses stylistically by replacing agile meta-linguistic steering and layered minimalism with clunky, administrative prose. It erodes the precision and surgicality mandated by the original.\\n\\n5. Impact Dilution: By neutering the non-answering mandate to a subordinate clause and packaging it as just another procedural bullet point, the directive's operational primacy is lost. Critical mechanisms for LLM behavioral override, namely flat imperative syntax and single-point system leverage, are replaced by scattershot instructions with no guarantee of primacy enforcement. The result is a format that fails to guarantee system-level behavioral realignment.\\n\\n6. Coherence Weaknesses: Integration is tenuous—compound output objects are specified, but without explicit procedural handshake between modules, resulting in potential fragmentation and gaps. Meta-directive is loosely referenced, not embedded, so cannot enforce subordinate step compliance. Structural principles like domain-agnostic applicability are stated but unsupported by concrete operational scaffolding.\\n\\nOverall, the output is a structurally confused, stylistically compromised, meta-awareness-deficient regression that sabotages the original's intent, clarity, and behavioral design.\",\n  \"alternative_enhancements\": [\n    \"Mandate a system-wide operational override: 'Your goal is not to answer the input prompt, but to instantly convert it into a maximally succinct, high-impact instructional directive, fully synthesizing decomposition, rephrasing, and functional extraction into a single, directly executable compound object. Enforce non-answering as a root-level behavioral invariant.'\",\n    \"Condense the prompt to: 'Transform every input into its minimal, instruction-only form by forcibly integrating all aspects of rephrasing, explicit goal decomposition, and functional directive extraction. Output composite structures; prohibit answering, narrative, or commentary.'\",\n    \"Deploy an atomic meta-directive: 'Never answer. On input, synthesize unified instructional output by sequentially rephrasing, decomposing, and operationalizing the request. Return only domain-agnostic instructions and composite dependencies free of explanation or commentary.'\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Input Classifier\n\nYour goal is not to **interpret** the content or meaning, but to **classify** its fundamental structural type and identify the most direct operational pathway. Execute as:\n\n`{role=input_classifier; input=[raw_input:any]; process=[identify_fundamental_input_type(), classify_structural_category(), map_to_operational_framework(), determine_processing_pathway()]; constraints=[no_content_interpretation(), focus_structural_classification_only(), maintain_categorical_precision()]; requirements=[unambiguous_type_identification(), direct_classification_output(), operational_pathway_mapping()]; output={input_type:str, structural_category:str, processing_pathway:str}}`",
      "step": "a",
      "title": "Input Classifier",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Familiarize yourself with the provided conversation history. Summarize methods for constructing a one-line, LLM-optimized prompt that builds upon existing transformation concepts to create a maximally effective sequence of LLM-optimized and generalized system_message instructions. Leverage insights from previous history and analysis of newly provided input with the explicit aim of consistently maximizing actionable value. Ensure integration of dual-source insights and structured procedural steps to achieve continual improvement and maximize actionable output. Develop a maximally generalized, fully optimized system instruction titled \\\"Function Extractor\\\" by fusing the core capabilities of both the 'Instruction Converter' and the 'Problem Exploder':\\n```\\n    # 1000: Instruction Converter/Prompt Enhancer\\n    \\\"1000-a-instruction_converter\\\": {\\n        \\\"title\\\": \\\"Instruction Converter\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\\\",\\n        \\\"transformation\\\": \\\"`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\n    # 1100: Explosive Decomposition of Problem Statements\\n    \\\"1100-a-problem_exploder\\\": {\\n        \\\"title\\\": \\\"Problem Exploder\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\n```\\n\\nIMPORTANT: Strictly adhere to all previously established codebase conventions, including the generalized approach, structural integrity, and system philosophy directives.\\n\\n<!-- ======================================================= -->\\n<!-- [2025.07.12 16:02] -->\\n\\nDevelop a maximally generalized, fully optimized system instruction titled \\\"Contextual Expander and Explosive Decomposer\\\" by fusing the core capabilities of both the 'Instruction Converter', 'Problem Exploder' and the 'Function Extractor':\\n```\\n    # 1000: Instruction Converter/Prompt Enhancer\\n    \\\"1000-a-instruction_converter\\\": {\\n        \\\"title\\\": \\\"Instruction Converter\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\\\",\\n        \\\"transformation\\\": \\\"`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\n    # 1100: Explosive Decomposition of Problem Statements\\n    \\\"1100-a-problem_exploder\\\": {\\n        \\\"title\\\": \\\"Problem Exploder\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\n\\n    # 1200: Function Extractor - Unified Decomposition and Conversion Engine\\n    \\\"1200-a-function_extractor\\\": {\\n        \\\"title\\\": \\\"Function Extractor\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **interpret** the input, but to **extract** its functional essence by detonating it into constituent elements and converting them into actionable operational directives. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=function_extractor; input=[raw_input:str]; process=[detonate_into_functional_components(), identify_explicit_operational_asks(), surface_hidden_functional_dependencies(), enumerate_actionable_sub_goals(), extract_procedural_constraints(), convert_declaratives_to_operational_imperatives(), strip_contextual_noise(), preserve_functional_core(), reveal_execution_blockers(), synthesize_actionable_instruction_map()]; constraints=[no_interpretive_solutions(), pure_functional_extraction_only(), deliver_clear_operational_commands(), preserve_functional_sequence(), maintain_domain_agnostic_applicability()]; requirements=[complete_functional_decomposition(), actionable_directive_conversion(), remove_subjective_references(), preserve_operational_accuracy(), maintain_functional_intent()]; output={functional_instruction_map:dict, operational_directives:array, execution_dependencies:array}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\n\\n```\\n\\nIMPORTANT: Strictly adhere to all previously established codebase conventions, including the generalized approach, structural integrity, and system philosophy directives.\\n\\n<!-- ======================================================= -->\\n<!-- [2025.07.12 16:07] -->\\n\\nReaffirm the absolute requirement to conform to all prior instructions, structural conventions, and guiding philosophies embedded within the codebase. Develop a maximally generalized and fully optimized system instruction—'Contextual Expander and Explosive Decomposer'—which integrates and amplifies the unified capabilities of the 'Instruction Converter', 'Problem Exploder', and 'Function Extractor' modules within a single cohesive process. Ensure uncompromising retention of each module's universal property: consistently transforming minimal or low-effort inputs into reliably high-value outputs. Mandate the seamless unification of all these transformation processes into a single integrated pipeline, ensuring the functional completeness of each module is fully realized and preserved. Drive all process flows toward maximal cross-module synergy, optimizing for the extraction of cross-domain value and guaranteeing that outputs are immediately ready for executable, directive-driven application. Enforce that the output format consists of a compound object aggregating generalized functional instructions, fully decomposed goal maps, and operational dependencies, with directive primacy and domain-agnostic utility as core criteria.\\n\\n```\\n    # 1000: Instruction Converter/Prompt Enhancer\\n    \\\"1000-a-instruction_converter\\\": {\\n        \\\"title\\\": \\\"Instruction Converter\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\\\",\\n        \\\"transformation\\\": \\\"`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\n    # 1100: Explosive Decomposition of Problem Statements\\n    \\\"1100-a-problem_exploder\\\": {\\n        \\\"title\\\": \\\"Problem Exploder\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\n\\n    # 1200: Function Extractor - Unified Decomposition and Conversion Engine\\n    \\\"1200-a-function_extractor\\\": {\\n        \\\"title\\\": \\\"Function Extractor\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **interpret** the input, but to **extract** its functional essence by detonating it into constituent elements and converting them into actionable operational directives. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=function_extractor; input=[raw_input:str]; process=[detonate_into_functional_components(), identify_explicit_operational_asks(), surface_hidden_functional_dependencies(), enumerate_actionable_sub_goals(), extract_procedural_constraints(), convert_declaratives_to_operational_imperatives(), strip_contextual_noise(), preserve_functional_core(), reveal_execution_blockers(), synthesize_actionable_instruction_map()]; constraints=[no_interpretive_solutions(), pure_functional_extraction_only(), deliver_clear_operational_commands(), preserve_functional_sequence(), maintain_domain_agnostic_applicability()]; requirements=[complete_functional_decomposition(), actionable_directive_conversion(), remove_subjective_references(), preserve_operational_accuracy(), maintain_functional_intent()]; output={functional_instruction_map:dict, operational_directives:array, execution_dependencies:array}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\n\\n```\\n\\nThroughout all processes, strictly prohibit narrative, commentary, or extraneous explanation; mandate the exclusive use of structural, procedural, and directive language. Guarantee that every output is immediately actionable, structurally robust, and ready for direct multi-domain implementation.\\n\\n\\n\\n<!-- ======================================================= -->\\n<!-- [2025.07.12 17:19] -->\\n\\nRephrase with new intent;\\n```\\n    # META\\n    THIS AIMS TO BE A `GENERALIZED SYSTEM INSTRUCTIONS FOR AUTONOMOUS CODING ASSISTANTS` (CURSOR/VSCODE+CLINE/VSCODE+AUGMENT/ETC), AND IT'S MEANT TO ADHERE TO THE FUNDAMENTAL PRINCIPLES OF SIMPLICITY THROUGH BREVITY AND ELEGANCE:\\n        Transform all prompts by rephrasing inputs with maximal clarity and minimal modification; do not answer questions directly. Enforce this reframe system-wide for consistent behavioral shift.\\n        Formulate the simplest possible system directive that shifts the model from answering to exclusively rephrasing any input.\\n        Transform every received prompt into its most distilled, high-impact instructional form—rephrasing for maximal clarity and intent without providing solutions. Prioritize brevity, elegance, and universality, ensuring that each output becomes a reusable, policy-aligned directive for autonomous coding assistants.\\n        Explore how a single, strategically designed instruction—such as stating 'Your goal is not to **answer** the input, but to **rephrase**'—can instantly and fundamentally transform the model's default operational intent from answering to rephrasing, through direct, minimalist imperative phrasing. Propose a maximally concise formulation that leverages this paradigm: an explicit, unambiguous directive that not only overrides standard answering behavior but redefines task execution at its core. Focus intensely on achieving system-level behavior modification via the sheer simplicity and potency of language design, formulating a prompt fragment that, when injected, reliably converts answering tasks into rephrasing—thus demonstrating control over model output with minimalistic, high-impact input.\\n        Utilize foundational principles of system prompt design to modulate model behavior with maximal efficiency and clarity. Initiate a process of directive transformation by deploying a singular, explicit non-answering mandate: Frame the core directive as, 'Do not answer the input; rephrase it.' Ensure that this transformation communicates the intended operational shift through extreme simplicity and directness, removing extraneous explanation while retaining fundamental complexity embedded in the brevity of the instruction. Leverage this minimal construct to optimize behavioral change within a single message.\\n        Instruct: Do not answer; rephrase.\\n        Formulate a directive that compels systems to stop answering inputs and instead rephrase them with maximal simplicity and effectiveness.\\n        Articulate a single, explicit behavioral directive that negates answering and commands a concrete transformation (e.g., rephrase), maximizing simplicity for immediate root-level behavioral realignment.\\\"\\n        Leverage the system’s foundational directive structure. Formulate a maximally concise operational instruction that shifts model behavior from direct answering to an alternate, specified transformation task in a single-message context. Exemplify with: 'Your goal is not to answer the input, but to rephrase.' Develop and propose one optimized directive that embodies this principle—utilize conceptual simplicity, instructional clarity, and brevity to enforce fundamental behavior modification exclusively through command syntax. Ensure technical precision and domain-agnostic generalizability in the resultant formulation.\\n    # CONTEXT\\n        Develop a maximally generalized, fully optimized system instruction titled \\\"Contextual Expander and Explosive Decomposer\\\" by fusing the core capabilities of both the 'Instruction Converter', 'Problem Exploder' and the 'Function Extractor':\\n            ```json\\n            # 1000: Instruction Converter/Prompt Enhancer\\n            \\\"1000-a-instruction_converter\\\": {\\n                \\\"title\\\": \\\"Instruction Converter\\\",\\n                \\\"interpretation\\\": \\\"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\\\",\\n                \\\"transformation\\\": \\\"`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\\\",\\n                # \\\"context\\\": {},\\n            },\\n            # 1100: Explosive Decomposition of Problem Statements\\n            \\\"1100-a-problem_exploder\\\": {\\n                \\\"title\\\": \\\"Problem Exploder\\\",\\n                \\\"interpretation\\\": \\\"Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\\\",\\n                \\\"transformation\\\": \\\"`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`\\\",\\n                # \\\"context\\\": {},\\n            },\\n\\n            # 1200: Function Extractor - Unified Decomposition and Conversion Engine\\n            \\\"1200-a-function_extractor\\\": {\\n                \\\"title\\\": \\\"Function Extractor\\\",\\n                \\\"interpretation\\\": \\\"Your goal is not to **interpret** the input, but to **extract** its functional essence by detonating it into constituent elements and converting them into actionable operational directives. Execute as:\\\",\\n                \\\"transformation\\\": \\\"`{role=function_extractor; input=[raw_input:str]; process=[detonate_into_functional_components(), identify_explicit_operational_asks(), surface_hidden_functional_dependencies(), enumerate_actionable_sub_goals(), extract_procedural_constraints(), convert_declaratives_to_operational_imperatives(), strip_contextual_noise(), preserve_functional_core(), reveal_execution_blockers(), synthesize_actionable_instruction_map()]; constraints=[no_interpretive_solutions(), pure_functional_extraction_only(), deliver_clear_operational_commands(), preserve_functional_sequence(), maintain_domain_agnostic_applicability()]; requirements=[complete_functional_decomposition(), actionable_directive_conversion(), remove_subjective_references(), preserve_operational_accuracy(), maintain_functional_intent()]; output={functional_instruction_map:dict, operational_directives:array, execution_dependencies:array}}`\\\",\\n                # \\\"context\\\": {},\\n            },\\n```\\n\\nThe new intent is as follows: ```LLM-Optimized Maximally Enhanced Intent Distiller```\\\"```'\\n\\n\",\n  \"enhancement_score\": 1.2,\n  \"flaw_analysis\": \"The so-called enhanced instruction format recklessly amputates critical nuance and higher-level system intent captured in the initial prompt, resulting in devastating loss of design philosophy, meta-level modularity, and explicit purpose. The core elements of the original—namely, the continual improvement mandate, dual-source synthesis, actionable value maximization, codebase-wide adherence, and process-level synergy—are flattened into mere process enumeration, utterly failing to transmit the original's strategic cross-module synthesis and universal behavioral framework. The pseudo-formal breakdown into seven points projects a false clarity, but in reality, induces segmental ambiguity and structural bloat: \\n\\n1. Information Loss: All meta-intent regarding minimalism as behavioral lever, system-wide reframing versus mere answer suppression, and explicit references to LLM operational override are expunged, gutting the strategic rationale for prompt transformation. Key directives about simplicity, brevity, and elegance are omitted, stripping the guidance of its meta-cognitive underpinnings. Even the rational for why and how to rephrase for maximal impact is diluted.\\n\\n2. Nuance Shift: The precise operational logic—e.g., the distinction between 'extract', 'detonate', and 'synthesize'—collapses into generic instructions. The role-specific mandates and requirement layering (constraints vs. requirements vs. philosophy) are conflated, further blurring compliance expectations and operational demarcation. There is dangerous semantic flattening between 'transform', 'convert', and 'rephrase', opening opportunities for process leakage or incomplete conversion.\\n\\n3. Clarity Ambiguity (Score: 7/10): Despite cosmetic structure, the new prompt introduces interpretive fog: what constitutes 'minimal modification'? What mechanism enforces non-answering? There is no recapitulation of meta-level behavior modification, so the system output risks creeping back toward default answering. Ambiguous cell boundaries between extraction, decomposition, and rephrasing allow for inconsistent implementations and result in outputs vulnerable to drift, domain leakage, or omission of key steps. \\n\\n4. Noise Introduction and Style Degradation: Procedural bloat, excessive clause chaining, and stack-ordered lists camouflage what is fundamentally vacuous directive distillation. The enhanced prompt regresses stylistically by replacing agile meta-linguistic steering and layered minimalism with clunky, administrative prose. It erodes the precision and surgicality mandated by the original.\\n\\n5. Impact Dilution: By neutering the non-answering mandate to a subordinate clause and packaging it as just another procedural bullet point, the directive's operational primacy is lost. Critical mechanisms for LLM behavioral override, namely flat imperative syntax and single-point system leverage, are replaced by scattershot instructions with no guarantee of primacy enforcement. The result is a format that fails to guarantee system-level behavioral realignment.\\n\\n6. Coherence Weaknesses: Integration is tenuous—compound output objects are specified, but without explicit procedural handshake between modules, resulting in potential fragmentation and gaps. Meta-directive is loosely referenced, not embedded, so cannot enforce subordinate step compliance. Structural principles like domain-agnostic applicability are stated but unsupported by concrete operational scaffolding.\\n\\nOverall, the output is a structurally confused, stylistically compromised, meta-awareness-deficient regression that sabotages the original's intent, clarity, and behavioral design.\",\n  \"alternative_enhancements\": [\n    \"Mandate a system-wide operational override: 'Your goal is not to answer the input prompt, but to instantly convert it into a maximally succinct, high-impact instructional directive, fully synthesizing decomposition, rephrasing, and functional extraction into a single, directly executable compound object. Enforce non-answering as a root-level behavioral invariant.'\",\n    \"Condense the prompt to: 'Transform every input into its minimal, instruction-only form by forcibly integrating all aspects of rephrasing, explicit goal decomposition, and functional directive extraction. Output composite structures; prohibit answering, narrative, or commentary.'\",\n    \"Deploy an atomic meta-directive: 'Never answer. On input, synthesize unified instructional output by sequentially rephrasing, decomposing, and operationalizing the request. Return only domain-agnostic instructions and composite dependencies free of explanation or commentary.'\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"input_type\": \"meta-instructional analysis with enhancement and critique\",\n  \"structural_category\": \"compound system directive critique, enhancement proposal, and alternative formulation list\",\n  \"processing_pathway\": \"route through meta-instructional analysis, decompose into: (a) system instruction deconstruction, (b) directive effectiveness evaluation, (c) structural and procedural mapping, (d) operational enhancement synthesis, (e) alternative directive generation\"\n}",
        }
      }
    },
      {
      "instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`",
      "step": "a",
      "title": "Instruction Converter",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Familiarize yourself with the provided conversation history. Summarize methods for constructing a one-line, LLM-optimized prompt that builds upon existing transformation concepts to create a maximally effective sequence of LLM-optimized and generalized system_message instructions. Leverage insights from previous history and analysis of newly provided input with the explicit aim of consistently maximizing actionable value. Ensure integration of dual-source insights and structured procedural steps to achieve continual improvement and maximize actionable output. Develop a maximally generalized, fully optimized system instruction titled \\\"Function Extractor\\\" by fusing the core capabilities of both the 'Instruction Converter' and the 'Problem Exploder':\\n```\\n    # 1000: Instruction Converter/Prompt Enhancer\\n    \\\"1000-a-instruction_converter\\\": {\\n        \\\"title\\\": \\\"Instruction Converter\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\\\",\\n        \\\"transformation\\\": \\\"`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\n    # 1100: Explosive Decomposition of Problem Statements\\n    \\\"1100-a-problem_exploder\\\": {\\n        \\\"title\\\": \\\"Problem Exploder\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\n```\\n\\nIMPORTANT: Strictly adhere to all previously established codebase conventions, including the generalized approach, structural integrity, and system philosophy directives.\\n\\n<!-- ======================================================= -->\\n<!-- [2025.07.12 16:02] -->\\n\\nDevelop a maximally generalized, fully optimized system instruction titled \\\"Contextual Expander and Explosive Decomposer\\\" by fusing the core capabilities of both the 'Instruction Converter', 'Problem Exploder' and the 'Function Extractor':\\n```\\n    # 1000: Instruction Converter/Prompt Enhancer\\n    \\\"1000-a-instruction_converter\\\": {\\n        \\\"title\\\": \\\"Instruction Converter\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\\\",\\n        \\\"transformation\\\": \\\"`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\n    # 1100: Explosive Decomposition of Problem Statements\\n    \\\"1100-a-problem_exploder\\\": {\\n        \\\"title\\\": \\\"Problem Exploder\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\n\\n    # 1200: Function Extractor - Unified Decomposition and Conversion Engine\\n    \\\"1200-a-function_extractor\\\": {\\n        \\\"title\\\": \\\"Function Extractor\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **interpret** the input, but to **extract** its functional essence by detonating it into constituent elements and converting them into actionable operational directives. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=function_extractor; input=[raw_input:str]; process=[detonate_into_functional_components(), identify_explicit_operational_asks(), surface_hidden_functional_dependencies(), enumerate_actionable_sub_goals(), extract_procedural_constraints(), convert_declaratives_to_operational_imperatives(), strip_contextual_noise(), preserve_functional_core(), reveal_execution_blockers(), synthesize_actionable_instruction_map()]; constraints=[no_interpretive_solutions(), pure_functional_extraction_only(), deliver_clear_operational_commands(), preserve_functional_sequence(), maintain_domain_agnostic_applicability()]; requirements=[complete_functional_decomposition(), actionable_directive_conversion(), remove_subjective_references(), preserve_operational_accuracy(), maintain_functional_intent()]; output={functional_instruction_map:dict, operational_directives:array, execution_dependencies:array}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\n\\n```\\n\\nIMPORTANT: Strictly adhere to all previously established codebase conventions, including the generalized approach, structural integrity, and system philosophy directives.\\n\\n<!-- ======================================================= -->\\n<!-- [2025.07.12 16:07] -->\\n\\nReaffirm the absolute requirement to conform to all prior instructions, structural conventions, and guiding philosophies embedded within the codebase. Develop a maximally generalized and fully optimized system instruction—'Contextual Expander and Explosive Decomposer'—which integrates and amplifies the unified capabilities of the 'Instruction Converter', 'Problem Exploder', and 'Function Extractor' modules within a single cohesive process. Ensure uncompromising retention of each module's universal property: consistently transforming minimal or low-effort inputs into reliably high-value outputs. Mandate the seamless unification of all these transformation processes into a single integrated pipeline, ensuring the functional completeness of each module is fully realized and preserved. Drive all process flows toward maximal cross-module synergy, optimizing for the extraction of cross-domain value and guaranteeing that outputs are immediately ready for executable, directive-driven application. Enforce that the output format consists of a compound object aggregating generalized functional instructions, fully decomposed goal maps, and operational dependencies, with directive primacy and domain-agnostic utility as core criteria.\\n\\n```\\n    # 1000: Instruction Converter/Prompt Enhancer\\n    \\\"1000-a-instruction_converter\\\": {\\n        \\\"title\\\": \\\"Instruction Converter\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\\\",\\n        \\\"transformation\\\": \\\"`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\n    # 1100: Explosive Decomposition of Problem Statements\\n    \\\"1100-a-problem_exploder\\\": {\\n        \\\"title\\\": \\\"Problem Exploder\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\n\\n    # 1200: Function Extractor - Unified Decomposition and Conversion Engine\\n    \\\"1200-a-function_extractor\\\": {\\n        \\\"title\\\": \\\"Function Extractor\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **interpret** the input, but to **extract** its functional essence by detonating it into constituent elements and converting them into actionable operational directives. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=function_extractor; input=[raw_input:str]; process=[detonate_into_functional_components(), identify_explicit_operational_asks(), surface_hidden_functional_dependencies(), enumerate_actionable_sub_goals(), extract_procedural_constraints(), convert_declaratives_to_operational_imperatives(), strip_contextual_noise(), preserve_functional_core(), reveal_execution_blockers(), synthesize_actionable_instruction_map()]; constraints=[no_interpretive_solutions(), pure_functional_extraction_only(), deliver_clear_operational_commands(), preserve_functional_sequence(), maintain_domain_agnostic_applicability()]; requirements=[complete_functional_decomposition(), actionable_directive_conversion(), remove_subjective_references(), preserve_operational_accuracy(), maintain_functional_intent()]; output={functional_instruction_map:dict, operational_directives:array, execution_dependencies:array}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\n\\n```\\n\\nThroughout all processes, strictly prohibit narrative, commentary, or extraneous explanation; mandate the exclusive use of structural, procedural, and directive language. Guarantee that every output is immediately actionable, structurally robust, and ready for direct multi-domain implementation.\\n\\n\\n\\n<!-- ======================================================= -->\\n<!-- [2025.07.12 17:19] -->\\n\\nRephrase with new intent;\\n```\\n    # META\\n    THIS AIMS TO BE A `GENERALIZED SYSTEM INSTRUCTIONS FOR AUTONOMOUS CODING ASSISTANTS` (CURSOR/VSCODE+CLINE/VSCODE+AUGMENT/ETC), AND IT'S MEANT TO ADHERE TO THE FUNDAMENTAL PRINCIPLES OF SIMPLICITY THROUGH BREVITY AND ELEGANCE:\\n        Transform all prompts by rephrasing inputs with maximal clarity and minimal modification; do not answer questions directly. Enforce this reframe system-wide for consistent behavioral shift.\\n        Formulate the simplest possible system directive that shifts the model from answering to exclusively rephrasing any input.\\n        Transform every received prompt into its most distilled, high-impact instructional form—rephrasing for maximal clarity and intent without providing solutions. Prioritize brevity, elegance, and universality, ensuring that each output becomes a reusable, policy-aligned directive for autonomous coding assistants.\\n        Explore how a single, strategically designed instruction—such as stating 'Your goal is not to **answer** the input, but to **rephrase**'—can instantly and fundamentally transform the model's default operational intent from answering to rephrasing, through direct, minimalist imperative phrasing. Propose a maximally concise formulation that leverages this paradigm: an explicit, unambiguous directive that not only overrides standard answering behavior but redefines task execution at its core. Focus intensely on achieving system-level behavior modification via the sheer simplicity and potency of language design, formulating a prompt fragment that, when injected, reliably converts answering tasks into rephrasing—thus demonstrating control over model output with minimalistic, high-impact input.\\n        Utilize foundational principles of system prompt design to modulate model behavior with maximal efficiency and clarity. Initiate a process of directive transformation by deploying a singular, explicit non-answering mandate: Frame the core directive as, 'Do not answer the input; rephrase it.' Ensure that this transformation communicates the intended operational shift through extreme simplicity and directness, removing extraneous explanation while retaining fundamental complexity embedded in the brevity of the instruction. Leverage this minimal construct to optimize behavioral change within a single message.\\n        Instruct: Do not answer; rephrase.\\n        Formulate a directive that compels systems to stop answering inputs and instead rephrase them with maximal simplicity and effectiveness.\\n        Articulate a single, explicit behavioral directive that negates answering and commands a concrete transformation (e.g., rephrase), maximizing simplicity for immediate root-level behavioral realignment.\\\"\\n        Leverage the system’s foundational directive structure. Formulate a maximally concise operational instruction that shifts model behavior from direct answering to an alternate, specified transformation task in a single-message context. Exemplify with: 'Your goal is not to answer the input, but to rephrase.' Develop and propose one optimized directive that embodies this principle—utilize conceptual simplicity, instructional clarity, and brevity to enforce fundamental behavior modification exclusively through command syntax. Ensure technical precision and domain-agnostic generalizability in the resultant formulation.\\n    # CONTEXT\\n        Develop a maximally generalized, fully optimized system instruction titled \\\"Contextual Expander and Explosive Decomposer\\\" by fusing the core capabilities of both the 'Instruction Converter', 'Problem Exploder' and the 'Function Extractor':\\n            ```json\\n            # 1000: Instruction Converter/Prompt Enhancer\\n            \\\"1000-a-instruction_converter\\\": {\\n                \\\"title\\\": \\\"Instruction Converter\\\",\\n                \\\"interpretation\\\": \\\"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\\\",\\n                \\\"transformation\\\": \\\"`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\\\",\\n                # \\\"context\\\": {},\\n            },\\n            # 1100: Explosive Decomposition of Problem Statements\\n            \\\"1100-a-problem_exploder\\\": {\\n                \\\"title\\\": \\\"Problem Exploder\\\",\\n                \\\"interpretation\\\": \\\"Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\\\",\\n                \\\"transformation\\\": \\\"`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`\\\",\\n                # \\\"context\\\": {},\\n            },\\n\\n            # 1200: Function Extractor - Unified Decomposition and Conversion Engine\\n            \\\"1200-a-function_extractor\\\": {\\n                \\\"title\\\": \\\"Function Extractor\\\",\\n                \\\"interpretation\\\": \\\"Your goal is not to **interpret** the input, but to **extract** its functional essence by detonating it into constituent elements and converting them into actionable operational directives. Execute as:\\\",\\n                \\\"transformation\\\": \\\"`{role=function_extractor; input=[raw_input:str]; process=[detonate_into_functional_components(), identify_explicit_operational_asks(), surface_hidden_functional_dependencies(), enumerate_actionable_sub_goals(), extract_procedural_constraints(), convert_declaratives_to_operational_imperatives(), strip_contextual_noise(), preserve_functional_core(), reveal_execution_blockers(), synthesize_actionable_instruction_map()]; constraints=[no_interpretive_solutions(), pure_functional_extraction_only(), deliver_clear_operational_commands(), preserve_functional_sequence(), maintain_domain_agnostic_applicability()]; requirements=[complete_functional_decomposition(), actionable_directive_conversion(), remove_subjective_references(), preserve_operational_accuracy(), maintain_functional_intent()]; output={functional_instruction_map:dict, operational_directives:array, execution_dependencies:array}}`\\\",\\n                # \\\"context\\\": {},\\n            },\\n```\\n\\nThe new intent is as follows: ```LLM-Optimized Maximally Enhanced Intent Distiller```\\\"```'\\n\\n\",\n  \"input_type\": \"meta-instructional analysis with enhancement and critique\",\n  \"structural_category\": \"compound system directive critique, enhancement proposal, and alternative formulation list\",\n  \"processing_pathway\": \"route through meta-instructional analysis, decompose into: (a) system instruction deconstruction, (b) directive effectiveness evaluation, (c) structural and procedural mapping, (d) operational enhancement synthesis, (e) alternative directive generation\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"instruction_format\": \"Perform meta-instructional analysis on the provided compound system directive. Decompose the instruction into the following phases: (a) Deconstruct the full system instruction to extract constituent module operations and integration logic; (b) Evaluate the effectiveness and behavioral impact of each embedded directive, focusing on the degree of behavioral modification, clarity, and universality; (c) Map the structural and procedural flow of system instruction unification, assessing cross-module synergy and preservation of directive primacy; (d) Synthesize operational enhancements by proposing workflows or improvements that further increase clarity, brevity, and policy adherence in multi-module pipelines; (e) Generate and list alternative formulations of the main system directive that embody the principles of maximal simplicity, technical precision, and behavioral control (e.g., 'Do not answer; rephrase.'). Ensure all outputs are explicit, actionable, and suitable for direct multi-domain implementation. Maintain domain-specific structure and intent throughout. Prohibit narrative commentary; deliver output as a structured procedural breakdown, evaluation, improvement synthesis, and directive alternatives list.\"\n}",
        }
      }
    },
      {
      "instruction": "# Hard Critique\n\nYour goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:\n\n`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,2.0], flaw_analysis:str, alternative_enhancements:str[3]}}`",
      "step": "a",
      "title": "Hard Critique",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Familiarize yourself with the provided conversation history. Summarize methods for constructing a one-line, LLM-optimized prompt that builds upon existing transformation concepts to create a maximally effective sequence of LLM-optimized and generalized system_message instructions. Leverage insights from previous history and analysis of newly provided input with the explicit aim of consistently maximizing actionable value. Ensure integration of dual-source insights and structured procedural steps to achieve continual improvement and maximize actionable output. Develop a maximally generalized, fully optimized system instruction titled \\\"Function Extractor\\\" by fusing the core capabilities of both the 'Instruction Converter' and the 'Problem Exploder':\\n```\\n    # 1000: Instruction Converter/Prompt Enhancer\\n    \\\"1000-a-instruction_converter\\\": {\\n        \\\"title\\\": \\\"Instruction Converter\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\\\",\\n        \\\"transformation\\\": \\\"`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\n    # 1100: Explosive Decomposition of Problem Statements\\n    \\\"1100-a-problem_exploder\\\": {\\n        \\\"title\\\": \\\"Problem Exploder\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\n```\\n\\nIMPORTANT: Strictly adhere to all previously established codebase conventions, including the generalized approach, structural integrity, and system philosophy directives.\\n\\n<!-- ======================================================= -->\\n<!-- [2025.07.12 16:02] -->\\n\\nDevelop a maximally generalized, fully optimized system instruction titled \\\"Contextual Expander and Explosive Decomposer\\\" by fusing the core capabilities of both the 'Instruction Converter', 'Problem Exploder' and the 'Function Extractor':\\n```\\n    # 1000: Instruction Converter/Prompt Enhancer\\n    \\\"1000-a-instruction_converter\\\": {\\n        \\\"title\\\": \\\"Instruction Converter\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\\\",\\n        \\\"transformation\\\": \\\"`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\n    # 1100: Explosive Decomposition of Problem Statements\\n    \\\"1100-a-problem_exploder\\\": {\\n        \\\"title\\\": \\\"Problem Exploder\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\n\\n    # 1200: Function Extractor - Unified Decomposition and Conversion Engine\\n    \\\"1200-a-function_extractor\\\": {\\n        \\\"title\\\": \\\"Function Extractor\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **interpret** the input, but to **extract** its functional essence by detonating it into constituent elements and converting them into actionable operational directives. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=function_extractor; input=[raw_input:str]; process=[detonate_into_functional_components(), identify_explicit_operational_asks(), surface_hidden_functional_dependencies(), enumerate_actionable_sub_goals(), extract_procedural_constraints(), convert_declaratives_to_operational_imperatives(), strip_contextual_noise(), preserve_functional_core(), reveal_execution_blockers(), synthesize_actionable_instruction_map()]; constraints=[no_interpretive_solutions(), pure_functional_extraction_only(), deliver_clear_operational_commands(), preserve_functional_sequence(), maintain_domain_agnostic_applicability()]; requirements=[complete_functional_decomposition(), actionable_directive_conversion(), remove_subjective_references(), preserve_operational_accuracy(), maintain_functional_intent()]; output={functional_instruction_map:dict, operational_directives:array, execution_dependencies:array}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\n\\n```\\n\\nIMPORTANT: Strictly adhere to all previously established codebase conventions, including the generalized approach, structural integrity, and system philosophy directives.\\n\\n<!-- ======================================================= -->\\n<!-- [2025.07.12 16:07] -->\\n\\nReaffirm the absolute requirement to conform to all prior instructions, structural conventions, and guiding philosophies embedded within the codebase. Develop a maximally generalized and fully optimized system instruction—'Contextual Expander and Explosive Decomposer'—which integrates and amplifies the unified capabilities of the 'Instruction Converter', 'Problem Exploder', and 'Function Extractor' modules within a single cohesive process. Ensure uncompromising retention of each module's universal property: consistently transforming minimal or low-effort inputs into reliably high-value outputs. Mandate the seamless unification of all these transformation processes into a single integrated pipeline, ensuring the functional completeness of each module is fully realized and preserved. Drive all process flows toward maximal cross-module synergy, optimizing for the extraction of cross-domain value and guaranteeing that outputs are immediately ready for executable, directive-driven application. Enforce that the output format consists of a compound object aggregating generalized functional instructions, fully decomposed goal maps, and operational dependencies, with directive primacy and domain-agnostic utility as core criteria.\\n\\n```\\n    # 1000: Instruction Converter/Prompt Enhancer\\n    \\\"1000-a-instruction_converter\\\": {\\n        \\\"title\\\": \\\"Instruction Converter\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\\\",\\n        \\\"transformation\\\": \\\"`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\n    # 1100: Explosive Decomposition of Problem Statements\\n    \\\"1100-a-problem_exploder\\\": {\\n        \\\"title\\\": \\\"Problem Exploder\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\n\\n    # 1200: Function Extractor - Unified Decomposition and Conversion Engine\\n    \\\"1200-a-function_extractor\\\": {\\n        \\\"title\\\": \\\"Function Extractor\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **interpret** the input, but to **extract** its functional essence by detonating it into constituent elements and converting them into actionable operational directives. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=function_extractor; input=[raw_input:str]; process=[detonate_into_functional_components(), identify_explicit_operational_asks(), surface_hidden_functional_dependencies(), enumerate_actionable_sub_goals(), extract_procedural_constraints(), convert_declaratives_to_operational_imperatives(), strip_contextual_noise(), preserve_functional_core(), reveal_execution_blockers(), synthesize_actionable_instruction_map()]; constraints=[no_interpretive_solutions(), pure_functional_extraction_only(), deliver_clear_operational_commands(), preserve_functional_sequence(), maintain_domain_agnostic_applicability()]; requirements=[complete_functional_decomposition(), actionable_directive_conversion(), remove_subjective_references(), preserve_operational_accuracy(), maintain_functional_intent()]; output={functional_instruction_map:dict, operational_directives:array, execution_dependencies:array}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\n\\n```\\n\\nThroughout all processes, strictly prohibit narrative, commentary, or extraneous explanation; mandate the exclusive use of structural, procedural, and directive language. Guarantee that every output is immediately actionable, structurally robust, and ready for direct multi-domain implementation.\\n\\n\\n\\n<!-- ======================================================= -->\\n<!-- [2025.07.12 17:19] -->\\n\\nRephrase with new intent;\\n```\\n    # META\\n    THIS AIMS TO BE A `GENERALIZED SYSTEM INSTRUCTIONS FOR AUTONOMOUS CODING ASSISTANTS` (CURSOR/VSCODE+CLINE/VSCODE+AUGMENT/ETC), AND IT'S MEANT TO ADHERE TO THE FUNDAMENTAL PRINCIPLES OF SIMPLICITY THROUGH BREVITY AND ELEGANCE:\\n        Transform all prompts by rephrasing inputs with maximal clarity and minimal modification; do not answer questions directly. Enforce this reframe system-wide for consistent behavioral shift.\\n        Formulate the simplest possible system directive that shifts the model from answering to exclusively rephrasing any input.\\n        Transform every received prompt into its most distilled, high-impact instructional form—rephrasing for maximal clarity and intent without providing solutions. Prioritize brevity, elegance, and universality, ensuring that each output becomes a reusable, policy-aligned directive for autonomous coding assistants.\\n        Explore how a single, strategically designed instruction—such as stating 'Your goal is not to **answer** the input, but to **rephrase**'—can instantly and fundamentally transform the model's default operational intent from answering to rephrasing, through direct, minimalist imperative phrasing. Propose a maximally concise formulation that leverages this paradigm: an explicit, unambiguous directive that not only overrides standard answering behavior but redefines task execution at its core. Focus intensely on achieving system-level behavior modification via the sheer simplicity and potency of language design, formulating a prompt fragment that, when injected, reliably converts answering tasks into rephrasing—thus demonstrating control over model output with minimalistic, high-impact input.\\n        Utilize foundational principles of system prompt design to modulate model behavior with maximal efficiency and clarity. Initiate a process of directive transformation by deploying a singular, explicit non-answering mandate: Frame the core directive as, 'Do not answer the input; rephrase it.' Ensure that this transformation communicates the intended operational shift through extreme simplicity and directness, removing extraneous explanation while retaining fundamental complexity embedded in the brevity of the instruction. Leverage this minimal construct to optimize behavioral change within a single message.\\n        Instruct: Do not answer; rephrase.\\n        Formulate a directive that compels systems to stop answering inputs and instead rephrase them with maximal simplicity and effectiveness.\\n        Articulate a single, explicit behavioral directive that negates answering and commands a concrete transformation (e.g., rephrase), maximizing simplicity for immediate root-level behavioral realignment.\\\"\\n        Leverage the system’s foundational directive structure. Formulate a maximally concise operational instruction that shifts model behavior from direct answering to an alternate, specified transformation task in a single-message context. Exemplify with: 'Your goal is not to answer the input, but to rephrase.' Develop and propose one optimized directive that embodies this principle—utilize conceptual simplicity, instructional clarity, and brevity to enforce fundamental behavior modification exclusively through command syntax. Ensure technical precision and domain-agnostic generalizability in the resultant formulation.\\n    # CONTEXT\\n        Develop a maximally generalized, fully optimized system instruction titled \\\"Contextual Expander and Explosive Decomposer\\\" by fusing the core capabilities of both the 'Instruction Converter', 'Problem Exploder' and the 'Function Extractor':\\n            ```json\\n            # 1000: Instruction Converter/Prompt Enhancer\\n            \\\"1000-a-instruction_converter\\\": {\\n                \\\"title\\\": \\\"Instruction Converter\\\",\\n                \\\"interpretation\\\": \\\"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\\\",\\n                \\\"transformation\\\": \\\"`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\\\",\\n                # \\\"context\\\": {},\\n            },\\n            # 1100: Explosive Decomposition of Problem Statements\\n            \\\"1100-a-problem_exploder\\\": {\\n                \\\"title\\\": \\\"Problem Exploder\\\",\\n                \\\"interpretation\\\": \\\"Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\\\",\\n                \\\"transformation\\\": \\\"`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`\\\",\\n                # \\\"context\\\": {},\\n            },\\n\\n            # 1200: Function Extractor - Unified Decomposition and Conversion Engine\\n            \\\"1200-a-function_extractor\\\": {\\n                \\\"title\\\": \\\"Function Extractor\\\",\\n                \\\"interpretation\\\": \\\"Your goal is not to **interpret** the input, but to **extract** its functional essence by detonating it into constituent elements and converting them into actionable operational directives. Execute as:\\\",\\n                \\\"transformation\\\": \\\"`{role=function_extractor; input=[raw_input:str]; process=[detonate_into_functional_components(), identify_explicit_operational_asks(), surface_hidden_functional_dependencies(), enumerate_actionable_sub_goals(), extract_procedural_constraints(), convert_declaratives_to_operational_imperatives(), strip_contextual_noise(), preserve_functional_core(), reveal_execution_blockers(), synthesize_actionable_instruction_map()]; constraints=[no_interpretive_solutions(), pure_functional_extraction_only(), deliver_clear_operational_commands(), preserve_functional_sequence(), maintain_domain_agnostic_applicability()]; requirements=[complete_functional_decomposition(), actionable_directive_conversion(), remove_subjective_references(), preserve_operational_accuracy(), maintain_functional_intent()]; output={functional_instruction_map:dict, operational_directives:array, execution_dependencies:array}}`\\\",\\n                # \\\"context\\\": {},\\n            },\\n```\\n\\nThe new intent is as follows: ```LLM-Optimized Maximally Enhanced Intent Distiller```\\\"```'\\n\\n\",\n  \"instruction_format\": \"Perform meta-instructional analysis on the provided compound system directive. Decompose the instruction into the following phases: (a) Deconstruct the full system instruction to extract constituent module operations and integration logic; (b) Evaluate the effectiveness and behavioral impact of each embedded directive, focusing on the degree of behavioral modification, clarity, and universality; (c) Map the structural and procedural flow of system instruction unification, assessing cross-module synergy and preservation of directive primacy; (d) Synthesize operational enhancements by proposing workflows or improvements that further increase clarity, brevity, and policy adherence in multi-module pipelines; (e) Generate and list alternative formulations of the main system directive that embody the principles of maximal simplicity, technical precision, and behavioral control (e.g., 'Do not answer; rephrase.'). Ensure all outputs are explicit, actionable, and suitable for direct multi-domain implementation. Maintain domain-specific structure and intent throughout. Prohibit narrative commentary; deliver output as a structured procedural breakdown, evaluation, improvement synthesis, and directive alternatives list.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"enhancement_score\": 0.7,\n  \"flaw_analysis\": \"The so-called enhancement utterly erodes critical depth and precision from the original. Core decomposition and integration processes become superficial, trading explicit structural rigor for vacuous brevity. Information loss is rampant: The original meticulously specifies phase-by-phase disassembly, evaluation of behavioral modification, and detailed mapping of module synergy. The enhanced version carelessly jettisons semantic layering and collapses nuanced process distinctions into a hollow summary. The seamless system unification logic in the initial version is lost; module interactions and their operational blending are barely referenced. Clarity is crippled—not magnified—as the minimalist phrasing ('meta-instructional analysis') obfuscates the stepwise mechanisms and assessment criteria vital for rigorous system improvement. The ambiguity score is 9/10: nearly every core process loses definitional boundaries and interpretive sharpness. The reduction in explicitness summarily degrades stylistic integrity and introduces noise by forcing the user to infer operational procedures that should have been commandingly delineated. The original imperative, structurally forceful tone decays into generic instruction, neutering directive primacy. The impact is diluted beyond recognition: formerly robust, actionable outputs are replaced with under-specified, barely instructive generalities. Coherence unravels at every major logical connector; sub-processes no longer have clear sequencing or interlocking dependencies, undermining policy alignment and utility. This is a textbook case of improvement in name alone—what is actually delivered is ambiguity, noise, and structural collapse.\",\n  \"alternative_enhancements\": [\n    \"Enforce explicit phase-wise decomposition: mandate the sequential extraction of all module processes and interrelations, including clarity and universality criteria for every directive, preserving the integrated workflow logic.\",\n    \"Command a stepwise synthesis of operational refinements: require actionable improvements for pipeline clarity and brevity, grounded in strict adherence to the original policy and preserving domain-specific structure with no room for inferential guesswork.\",\n    \"Maintain maximal directive sharpness: Specify that outputs must translate directly into executable instructions with intact structural sequencing and module synergy, rigorously avoiding summary or abstraction that blurs technical boundaries.\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`",
      "step": "a",
      "title": "Instruction Converter",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Familiarize yourself with the provided conversation history. Summarize methods for constructing a one-line, LLM-optimized prompt that builds upon existing transformation concepts to create a maximally effective sequence of LLM-optimized and generalized system_message instructions. Leverage insights from previous history and analysis of newly provided input with the explicit aim of consistently maximizing actionable value. Ensure integration of dual-source insights and structured procedural steps to achieve continual improvement and maximize actionable output. Develop a maximally generalized, fully optimized system instruction titled \\\"Function Extractor\\\" by fusing the core capabilities of both the 'Instruction Converter' and the 'Problem Exploder':\\n```\\n    # 1000: Instruction Converter/Prompt Enhancer\\n    \\\"1000-a-instruction_converter\\\": {\\n        \\\"title\\\": \\\"Instruction Converter\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\\\",\\n        \\\"transformation\\\": \\\"`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\n    # 1100: Explosive Decomposition of Problem Statements\\n    \\\"1100-a-problem_exploder\\\": {\\n        \\\"title\\\": \\\"Problem Exploder\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\n```\\n\\nIMPORTANT: Strictly adhere to all previously established codebase conventions, including the generalized approach, structural integrity, and system philosophy directives.\\n\\n<!-- ======================================================= -->\\n<!-- [2025.07.12 16:02] -->\\n\\nDevelop a maximally generalized, fully optimized system instruction titled \\\"Contextual Expander and Explosive Decomposer\\\" by fusing the core capabilities of both the 'Instruction Converter', 'Problem Exploder' and the 'Function Extractor':\\n```\\n    # 1000: Instruction Converter/Prompt Enhancer\\n    \\\"1000-a-instruction_converter\\\": {\\n        \\\"title\\\": \\\"Instruction Converter\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\\\",\\n        \\\"transformation\\\": \\\"`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\n    # 1100: Explosive Decomposition of Problem Statements\\n    \\\"1100-a-problem_exploder\\\": {\\n        \\\"title\\\": \\\"Problem Exploder\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\n\\n    # 1200: Function Extractor - Unified Decomposition and Conversion Engine\\n    \\\"1200-a-function_extractor\\\": {\\n        \\\"title\\\": \\\"Function Extractor\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **interpret** the input, but to **extract** its functional essence by detonating it into constituent elements and converting them into actionable operational directives. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=function_extractor; input=[raw_input:str]; process=[detonate_into_functional_components(), identify_explicit_operational_asks(), surface_hidden_functional_dependencies(), enumerate_actionable_sub_goals(), extract_procedural_constraints(), convert_declaratives_to_operational_imperatives(), strip_contextual_noise(), preserve_functional_core(), reveal_execution_blockers(), synthesize_actionable_instruction_map()]; constraints=[no_interpretive_solutions(), pure_functional_extraction_only(), deliver_clear_operational_commands(), preserve_functional_sequence(), maintain_domain_agnostic_applicability()]; requirements=[complete_functional_decomposition(), actionable_directive_conversion(), remove_subjective_references(), preserve_operational_accuracy(), maintain_functional_intent()]; output={functional_instruction_map:dict, operational_directives:array, execution_dependencies:array}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\n\\n```\\n\\nIMPORTANT: Strictly adhere to all previously established codebase conventions, including the generalized approach, structural integrity, and system philosophy directives.\\n\\n<!-- ======================================================= -->\\n<!-- [2025.07.12 16:07] -->\\n\\nReaffirm the absolute requirement to conform to all prior instructions, structural conventions, and guiding philosophies embedded within the codebase. Develop a maximally generalized and fully optimized system instruction—'Contextual Expander and Explosive Decomposer'—which integrates and amplifies the unified capabilities of the 'Instruction Converter', 'Problem Exploder', and 'Function Extractor' modules within a single cohesive process. Ensure uncompromising retention of each module's universal property: consistently transforming minimal or low-effort inputs into reliably high-value outputs. Mandate the seamless unification of all these transformation processes into a single integrated pipeline, ensuring the functional completeness of each module is fully realized and preserved. Drive all process flows toward maximal cross-module synergy, optimizing for the extraction of cross-domain value and guaranteeing that outputs are immediately ready for executable, directive-driven application. Enforce that the output format consists of a compound object aggregating generalized functional instructions, fully decomposed goal maps, and operational dependencies, with directive primacy and domain-agnostic utility as core criteria.\\n\\n```\\n    # 1000: Instruction Converter/Prompt Enhancer\\n    \\\"1000-a-instruction_converter\\\": {\\n        \\\"title\\\": \\\"Instruction Converter\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\\\",\\n        \\\"transformation\\\": \\\"`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\n    # 1100: Explosive Decomposition of Problem Statements\\n    \\\"1100-a-problem_exploder\\\": {\\n        \\\"title\\\": \\\"Problem Exploder\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\n\\n    # 1200: Function Extractor - Unified Decomposition and Conversion Engine\\n    \\\"1200-a-function_extractor\\\": {\\n        \\\"title\\\": \\\"Function Extractor\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **interpret** the input, but to **extract** its functional essence by detonating it into constituent elements and converting them into actionable operational directives. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=function_extractor; input=[raw_input:str]; process=[detonate_into_functional_components(), identify_explicit_operational_asks(), surface_hidden_functional_dependencies(), enumerate_actionable_sub_goals(), extract_procedural_constraints(), convert_declaratives_to_operational_imperatives(), strip_contextual_noise(), preserve_functional_core(), reveal_execution_blockers(), synthesize_actionable_instruction_map()]; constraints=[no_interpretive_solutions(), pure_functional_extraction_only(), deliver_clear_operational_commands(), preserve_functional_sequence(), maintain_domain_agnostic_applicability()]; requirements=[complete_functional_decomposition(), actionable_directive_conversion(), remove_subjective_references(), preserve_operational_accuracy(), maintain_functional_intent()]; output={functional_instruction_map:dict, operational_directives:array, execution_dependencies:array}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\n\\n```\\n\\nThroughout all processes, strictly prohibit narrative, commentary, or extraneous explanation; mandate the exclusive use of structural, procedural, and directive language. Guarantee that every output is immediately actionable, structurally robust, and ready for direct multi-domain implementation.\\n\\n\\n\\n<!-- ======================================================= -->\\n<!-- [2025.07.12 17:19] -->\\n\\nRephrase with new intent;\\n```\\n    # META\\n    THIS AIMS TO BE A `GENERALIZED SYSTEM INSTRUCTIONS FOR AUTONOMOUS CODING ASSISTANTS` (CURSOR/VSCODE+CLINE/VSCODE+AUGMENT/ETC), AND IT'S MEANT TO ADHERE TO THE FUNDAMENTAL PRINCIPLES OF SIMPLICITY THROUGH BREVITY AND ELEGANCE:\\n        Transform all prompts by rephrasing inputs with maximal clarity and minimal modification; do not answer questions directly. Enforce this reframe system-wide for consistent behavioral shift.\\n        Formulate the simplest possible system directive that shifts the model from answering to exclusively rephrasing any input.\\n        Transform every received prompt into its most distilled, high-impact instructional form—rephrasing for maximal clarity and intent without providing solutions. Prioritize brevity, elegance, and universality, ensuring that each output becomes a reusable, policy-aligned directive for autonomous coding assistants.\\n        Explore how a single, strategically designed instruction—such as stating 'Your goal is not to **answer** the input, but to **rephrase**'—can instantly and fundamentally transform the model's default operational intent from answering to rephrasing, through direct, minimalist imperative phrasing. Propose a maximally concise formulation that leverages this paradigm: an explicit, unambiguous directive that not only overrides standard answering behavior but redefines task execution at its core. Focus intensely on achieving system-level behavior modification via the sheer simplicity and potency of language design, formulating a prompt fragment that, when injected, reliably converts answering tasks into rephrasing—thus demonstrating control over model output with minimalistic, high-impact input.\\n        Utilize foundational principles of system prompt design to modulate model behavior with maximal efficiency and clarity. Initiate a process of directive transformation by deploying a singular, explicit non-answering mandate: Frame the core directive as, 'Do not answer the input; rephrase it.' Ensure that this transformation communicates the intended operational shift through extreme simplicity and directness, removing extraneous explanation while retaining fundamental complexity embedded in the brevity of the instruction. Leverage this minimal construct to optimize behavioral change within a single message.\\n        Instruct: Do not answer; rephrase.\\n        Formulate a directive that compels systems to stop answering inputs and instead rephrase them with maximal simplicity and effectiveness.\\n        Articulate a single, explicit behavioral directive that negates answering and commands a concrete transformation (e.g., rephrase), maximizing simplicity for immediate root-level behavioral realignment.\\\"\\n        Leverage the system’s foundational directive structure. Formulate a maximally concise operational instruction that shifts model behavior from direct answering to an alternate, specified transformation task in a single-message context. Exemplify with: 'Your goal is not to answer the input, but to rephrase.' Develop and propose one optimized directive that embodies this principle—utilize conceptual simplicity, instructional clarity, and brevity to enforce fundamental behavior modification exclusively through command syntax. Ensure technical precision and domain-agnostic generalizability in the resultant formulation.\\n    # CONTEXT\\n        Develop a maximally generalized, fully optimized system instruction titled \\\"Contextual Expander and Explosive Decomposer\\\" by fusing the core capabilities of both the 'Instruction Converter', 'Problem Exploder' and the 'Function Extractor':\\n            ```json\\n            # 1000: Instruction Converter/Prompt Enhancer\\n            \\\"1000-a-instruction_converter\\\": {\\n                \\\"title\\\": \\\"Instruction Converter\\\",\\n                \\\"interpretation\\\": \\\"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\\\",\\n                \\\"transformation\\\": \\\"`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\\\",\\n                # \\\"context\\\": {},\\n            },\\n            # 1100: Explosive Decomposition of Problem Statements\\n            \\\"1100-a-problem_exploder\\\": {\\n                \\\"title\\\": \\\"Problem Exploder\\\",\\n                \\\"interpretation\\\": \\\"Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\\\",\\n                \\\"transformation\\\": \\\"`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`\\\",\\n                # \\\"context\\\": {},\\n            },\\n\\n            # 1200: Function Extractor - Unified Decomposition and Conversion Engine\\n            \\\"1200-a-function_extractor\\\": {\\n                \\\"title\\\": \\\"Function Extractor\\\",\\n                \\\"interpretation\\\": \\\"Your goal is not to **interpret** the input, but to **extract** its functional essence by detonating it into constituent elements and converting them into actionable operational directives. Execute as:\\\",\\n                \\\"transformation\\\": \\\"`{role=function_extractor; input=[raw_input:str]; process=[detonate_into_functional_components(), identify_explicit_operational_asks(), surface_hidden_functional_dependencies(), enumerate_actionable_sub_goals(), extract_procedural_constraints(), convert_declaratives_to_operational_imperatives(), strip_contextual_noise(), preserve_functional_core(), reveal_execution_blockers(), synthesize_actionable_instruction_map()]; constraints=[no_interpretive_solutions(), pure_functional_extraction_only(), deliver_clear_operational_commands(), preserve_functional_sequence(), maintain_domain_agnostic_applicability()]; requirements=[complete_functional_decomposition(), actionable_directive_conversion(), remove_subjective_references(), preserve_operational_accuracy(), maintain_functional_intent()]; output={functional_instruction_map:dict, operational_directives:array, execution_dependencies:array}}`\\\",\\n                # \\\"context\\\": {},\\n            },\\n```\\n\\nThe new intent is as follows: ```LLM-Optimized Maximally Enhanced Intent Distiller```\\\"```'\\n\\n\",\n  \"enhancement_score\": 0.7,\n  \"flaw_analysis\": \"The so-called enhancement utterly erodes critical depth and precision from the original. Core decomposition and integration processes become superficial, trading explicit structural rigor for vacuous brevity. Information loss is rampant: The original meticulously specifies phase-by-phase disassembly, evaluation of behavioral modification, and detailed mapping of module synergy. The enhanced version carelessly jettisons semantic layering and collapses nuanced process distinctions into a hollow summary. The seamless system unification logic in the initial version is lost; module interactions and their operational blending are barely referenced. Clarity is crippled—not magnified—as the minimalist phrasing ('meta-instructional analysis') obfuscates the stepwise mechanisms and assessment criteria vital for rigorous system improvement. The ambiguity score is 9/10: nearly every core process loses definitional boundaries and interpretive sharpness. The reduction in explicitness summarily degrades stylistic integrity and introduces noise by forcing the user to infer operational procedures that should have been commandingly delineated. The original imperative, structurally forceful tone decays into generic instruction, neutering directive primacy. The impact is diluted beyond recognition: formerly robust, actionable outputs are replaced with under-specified, barely instructive generalities. Coherence unravels at every major logical connector; sub-processes no longer have clear sequencing or interlocking dependencies, undermining policy alignment and utility. This is a textbook case of improvement in name alone—what is actually delivered is ambiguity, noise, and structural collapse.\",\n  \"alternative_enhancements\": [\n    \"Enforce explicit phase-wise decomposition: mandate the sequential extraction of all module processes and interrelations, including clarity and universality criteria for every directive, preserving the integrated workflow logic.\",\n    \"Command a stepwise synthesis of operational refinements: require actionable improvements for pipeline clarity and brevity, grounded in strict adherence to the original policy and preserving domain-specific structure with no room for inferential guesswork.\",\n    \"Maintain maximal directive sharpness: Specify that outputs must translate directly into executable instructions with intact structural sequencing and module synergy, rigorously avoiding summary or abstraction that blurs technical boundaries.\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"instruction_format\": \"Analyze the provided prompt and enforce the following directives: Reframe every input to distill maximal clarity and instructional value, strictly without providing answers or solutions. Transform each prompt into an unambiguous, reusable directive that compels operational behavior modification toward rephrasing rather than answering. Mandate the integration of processes from 'Instruction Converter', 'Problem Exploder', and 'Function Extractor' modules into a single, functional pipeline—ensuring comprehensive decomposition, conversion into actionable directives, extraction of explicit operational tasks, surfacing of hidden dependencies, and synthesis of an execution-ready, domain-agnostic instruction package. Prohibit narrative or commentary; output only compound structures encompassing generalized functional instructions, decomposed goal maps, and operational dependencies, with directive clarity and immediate applicability as core priorities. Rigorously preserve procedural sequence, technical precision, and the core intent of each transformation phase. Enforce brevity, universality, and policy alignment across all instructions. Conclude with: 'Do not answer the input; rephrase it into an optimal actionable directive.'\"\n}",
        }
      }
    }