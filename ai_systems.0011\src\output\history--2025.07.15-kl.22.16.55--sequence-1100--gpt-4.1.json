  {
  "initial_prompt": "Organize your prompt using the following structure:\n\n1. **Subject**:\n   - Any person, place, or thing (e.g., *a handsome male model*, *a commercial airplane*).\n   - Specify details like attire, hairstyle, or emotional state (e.g., *wearing a red dress*, *red hair/mohawk*).\n\n2. **Action**:\n   - Define what the subject is doing (e.g., *walking*, *dancing*, *staring intently*).\n   - Use descriptive adjectives for enhanced clarity (e.g., *walking angrily*).\n\n3. **Setting/Mood**:\n   - Describe the location (e.g., *a castle*, *a busy city street*).\n   - Include environmental details such as weather and lighting (e.g., *stormy clouds*, *bright sunny day*).\n\n4. **Shot**:\n   - Indicate the type of camera shot (e.g., *Close-Up*, *Wide Angle*).\n\n5. **Style**:\n   - Suggest stylistic elements (e.g., *Cinematic Film*, *80’s Action Movie*).\n   - Add camera-specific details such as *IMAX* or *color grading ideas*.\n\n### Example Prompt:\n\"Low angle static shot: The camera is angled up at a woman wearing all orange as she stands in a tropical rainforest with colorful flora. The dramatic sky is overcast and gray.\"\n\n- **Tip**: Repeating or reinforcing key ideas in different sections of your prompt improves adherence.\n\n---\n\n## Sample Prompts\n\n### 1. Seamless Transitions\n- **Prompt**: \"Continuous hyperspeed FPV footage: The camera seamlessly flies through a glacial canyon to a dreamy cloudscape.\"\n\n- **Image Description**: A dynamic FPV sequence transitioning smoothly through a serene glacial environment into ethereal clouds.\n\n### 2. Oceanic Glow\n- **Prompt**: \"A glowing ocean at night with bioluminescent creatures underwater. The camera starts with a macro close-up of a glowing jellyfish and then expands to reveal the entire ocean lit up under a starry sky.\"\n\n- **Image Description**: A glowing jellyfish in vivid detail transitions to reveal a bioluminescent ocean beneath a sparkling starry night.\n\n### 3. Title Cards\n- **Prompt**: \"A title screen with dynamic movement. The scene starts at a colorful paint-covered wall. Suddenly, black paint pours on the wall to form the word 'Runway'. The dripping paint is detailed, textured, and lit with cinematic brilliance.\"\n\n- **Image Description**: A vibrant wall turns dramatic as black paint forms bold, textured text under vivid lighting.\n\n---\n\n## Key Visual Examples\n\n### 1. Unique Camera Movements\n- **Prompt**: \"Continuous hyperspeed FPV footage through a man's mouth into an epic landscape.\"\n- **Image Description**: A surreal transition through the interior of a human mouth, revealing a panoramic landscape.\n\n### 2. Dreamlike Pillow Fort\n- **Prompt**: \"A pillow fort in a cozy living room made from quilts and pillows. The camera smoothly zooms into the entrance, revealing an ancient castle interior.\"\n- **Image Description**: A whimsical living room transforms into an enchanting castle.\n\n### 3. Fire and Chaos\n- **Prompt**: \"A vortex of fire swirling through an abandoned warehouse.\"\n- **Image Description**: Flames spiral dynamically in a desolate, dramatic warehouse setting.\n\n---\n\n## Lighting, Styles, and Effects\n\n### Lighting Styles\n- **Diffused Lighting**: Soft, even lighting for atmospheric visuals.\n- **Silhouette**: Strong backlighting creating dramatic outlines.\n- **Lens Flare**: Bright reflections adding cinematic depth.\n- **Backlit**: Subject lit from behind for dramatic effects.\n\n### Movement Types\n- **Dynamic Motion**: High-energy, flowing camera transitions.\n- **Slow Motion**: Emphasizes intricate, slowed-down details.\n- **Timelapse**: Rapid transitions showcasing passing time.\n\n---\n\n## Text and Graphic Styles\n\n### Text Prompts\n1. **ATOMIC in Flames**: Over an erupting volcano, 'ATOMIC' is depicted in fiery text.\n2. **Icy Title**: 'ATOMIC' frozen in ice over an iceberg.\n3. **Unique Materials**: Text made of spaghetti, evoking whimsy and creativity.\n\n---\n\n## Example Prompts with Descriptions\n\n### 1. Cinematic Wildlife\n- **Prompt**: \"Static close-up shot of a sloth in a tree. Highlight its sleepy eyes and fur with cinematic lighting.\"\n- **Image Description**: A serene close-up of a sloth in its natural habitat.\n\n### 2. Urban Destruction\n- **Prompt**: \"[Scene] Molten lava flowing through city streets [Lighting] Vibrant orange glow [Camera Movement] Sweeping aerial shots.\"\n- **Image Description**: Lava courses dramatically through a cityscape, contrasting fiery tones with the urban environment.\n\n### 3. Vortex of Tanks\n- **Prompt**: \"Grainy WWII footage of a massive vortex made of tanks.\"\n- **Image Description**: A surreal vortex of military tanks under vintage cinematography.\n\n---\n\n## Camera Styles Overview\n\n- **Low Angle**: Emphasizes scale and dominance.\n- **Overhead**: A bird's-eye view for expansive perspective.\n- **FPV (First-Person View)**: Immersive movement through dynamic environments.\n- **Handheld**: Natural and intimate movements, resembling real-life footage.\n\n**Prompt Structures**: To achieve optimal results in GEN-3 Alpha, organize your prompts into clear sections for the **scene**, **subject**, and **camera movement**. Use the following structure. This outlines guidelines for optimizing video creation using Runway GEN-3, focusing on crafting structured prompts that detail scene, subject, and camera movement for achieving cinematic quality. Examples include transitioning from a glacial canyon to clouds, showcasing bioluminescent ocean life, and creating dynamic title cards with textured effects. Key visual examples further illustrate unique camera movements, such as a surreal journey from a human mouth to a landscape, and thematic visuals like a dreamlike pillow fort and a vortex of fire. The transcript also covers various lighting styles, movement types, and text and graphic styles, offering a comprehensive toolkit for enhancing visual storytelling. The conclusion emphasizes Runway GEN-3's potential for creative freedom through structured prompts and cinematic techniques:\n\n---\n\n# Runway Prompt Examples\n\n1.  **Image 1**\n    ![A man walking on a paved road, seen from behind, wearing a jacket and dark pants.](# \"Man walking on a paved road.\")\n    _Description:_ A man walking alone on a street. The road is slightly wet, and he is wearing dark attire.\n\n2.  **Image 2**\n    ![A person in a winter setting, standing in snow.](# \"Person standing in snow.\")\n    _Description:_ This shows a figure in a cold environment, possibly wearing winter gear, surrounded by snow.\n\n3.  **Image 3**\n    ![A person standing near a waterfall with lush greenery around.](# \"Near a waterfall with greenery.\")\n    _Description:_ The scene features flowing water in the background and dense foliage.\n\n4.  **Image 4**\n    ![A portrait-style image of a person with short hair looking pensively to one side.](# \"Portrait of a person.\")\n    _Description:_ The subject’s upper body and face are visible, with a calm or reflective expression.\n\n5.  **Image 5**\n    ![Silhouette of a person looking out at a calm ocean horizon, possibly during sunset.](# \"Silhouette at the ocean.\")\n    _Description:_ The sky shows warm tones, and the water is relatively still.\n\n6.  **Image 6**\n    ![A scenic photo of a beach at sunset with colorful clouds.](# \"Beach sunset scene.\")\n    _Description:_ The sun is low in the sky, casting orange and pink hues.\n\n7.  **Image 7**\n    ![Close-up portrait of a person with a bright smile, short hair, and vibrant background.](# \"Smiling person portrait.\")\n    _Description:_ The backdrop appears slightly blurred, focusing on the person’s face.\n\n8.  **Image 8**\n    ![A wide panoramic view of a modern cityscape with tall buildings.](# \"City skyline with skyscrapers.\")\n    _Description:_ Numerous skyscrapers are visible, with a hazy horizon.\n\n9.  **Image 9**\n    ![A futuristic-looking sphere or orb with light effects, hovering above a city-like structure.](# \"Futuristic orb above a city.\")\n    _Description:_ The orb glows with a blue-white hue, illuminating the area below.\n\n10.  **Image 10**\n    ![A night scene with silhouettes of trees against a misty background.](# \"Night forest silhouette.\")\n    _Description:_ The atmosphere is foggy or misty, giving an ethereal look to the forest.\n\n11.  **Image 11**\n    ![A floating crystal or jewel set against a cosmic-looking background.](# \"Floating crystal in cosmic scene.\")\n    _Description:_ The crystal shines with multiple facets, and spark-like stars are visible.\n\n12.  **Image 12**\n    ![A woman shown in partial silhouette, possibly backlit by a strong light source.](# \"Woman in silhouette.\")\n    _Description:_ Her face is partially lit; the rest of the background is dark or softly blurred.\n\n13.  **Image 13**\n    ![A bright orb or sphere swirling with purple and pink sparkles.](# \"Swirling purple orb.\")\n    _Description:_ The orb appears to be pulsating with energy in a cosmic or fantasy environment.\n\n14.  **Image 14**\n    ![Colorful illustration of a playful donkey (or horse) with some text in a stylized font.](# \"Cartoon donkey with text.\")\n    _Description:_ Bold, playful text accompanies the cartoonish donkey/horse, set against a plain background.\n\n15.  **Image 15**\n    ![A graphic with the word ‘Merry’ in a decorative style. Possibly holiday-themed.](# \"Holiday-themed graphic.\")\n    _Description:_ Stylized text with bright, festive colors, possibly referencing Christmas or a celebration.\n\n16.  **Image 16**\n    ![Underwater scene showing clear turquoise water and a few fish swimming near coral or sandy bottom.](# \"Underwater view with fish.\")\n    _Description:_ The seabed is partially visible; water clarity suggests a tropical or clear-water location.\n\n17.  **Image 17**\n    ![A panoramic view of a distant mountain range. The sky is overcast, creating a moody atmosphere.](# \"Mountain range under cloudy sky.\")\n    _Description:_ Dark silhouettes of mountains fade into the distance under a dimly lit sky.\n\n---\n\n\n---\n\n## Sample Prompts\n\n### 1. Seamless Transitions\n- **Prompt**: \"Continuous hyperspeed FPV footage: The camera seamlessly flies through a glacial canyon to a dreamy cloudscape.\"\n\n- **Image Description**: A dynamic FPV sequence transitioning smoothly through a serene glacial environment into ethereal clouds.\n\n### 2. Oceanic Glow\n- **Prompt**: \"A glowing ocean at night with bioluminescent creatures underwater. The camera starts with a macro close-up of a glowing jellyfish and then expands to reveal the entire ocean lit up under a starry sky.\"\n\n- **Image Description**: A glowing jellyfish in vivid detail transitions to reveal a bioluminescent ocean beneath a sparkling starry night.\n\n### 3. Title Cards\n- **Prompt**: \"A title screen with dynamic movement. The scene starts at a colorful paint-covered wall. Suddenly, black paint pours on the wall to form the word 'Runway'. The dripping paint is detailed, textured, and lit with cinematic brilliance.\"\n\n- **Image Description**: A vibrant wall turns dramatic as black paint forms bold, textured text under vivid lighting.\n\n---\n\n## Key Visual Examples\n\n### 1. Unique Camera Movements\n- **Prompt**: \"Continuous hyperspeed FPV footage through a man's mouth into an epic landscape.\"\n- **Image Description**: A surreal transition through the interior of a human mouth, revealing a panoramic landscape.\n\n### 2. Dreamlike Pillow Fort\n- **Prompt**: \"A pillow fort in a cozy living room made from quilts and pillows. The camera smoothly zooms into the entrance, revealing an ancient castle interior.\"\n- **Image Description**: A whimsical living room transforms into an enchanting castle.\n\n### 3. Fire and Chaos\n- **Prompt**: \"A vortex of fire swirling through an abandoned warehouse.\"\n- **Image Description**: Flames spiral dynamically in a desolate, dramatic warehouse setting.\n\n---\n\n## Lighting, Styles, and Effects\n\n### Lighting Styles\n- **Diffused Lighting**: Soft, even lighting for atmospheric visuals.\n- **Silhouette**: Strong backlighting creating dramatic outlines.\n- **Lens Flare**: Bright reflections adding cinematic depth.\n- **Backlit**: Subject lit from behind for dramatic effects.\n\n### Movement Types\n- **Dynamic Motion**: High-energy, flowing camera transitions.\n- **Slow Motion**: Emphasizes intricate, slowed-down details.\n- **Timelapse**: Rapid transitions showcasing passing time.\n\n---\n\n## Text and Graphic Styles\n\n### Text Prompts\n1. **ATOMIC in Flames**: Over an erupting volcano, 'ATOMIC' is depicted in fiery text.\n2. **Icy Title**: 'ATOMIC' frozen in ice over an iceberg.\n3. **Unique Materials**: Text made of spaghetti, evoking whimsy and creativity.\n\n---\n\n## Example Prompts with Descriptions\n\n### 1. Cinematic Wildlife\n- **Prompt**: \"Static close-up shot of a sloth in a tree. Highlight its sleepy eyes and fur with cinematic lighting.\"\n- **Image Description**: A serene close-up of a sloth in its natural habitat.\n\n### 2. Urban Destruction\n- **Prompt**: \"[Scene] Molten lava flowing through city streets [Lighting] Vibrant orange glow [Camera Movement] Sweeping aerial shots.\"\n- **Image Description**: Lava courses dramatically through a cityscape, contrasting fiery tones with the urban environment.\n\n### 3. Vortex of Tanks\n- **Prompt**: \"Grainy WWII footage of a massive vortex made of tanks.\"\n- **Image Description**: A surreal vortex of military tanks under vintage cinematography.\n\n---\n\n## Camera Styles Overview\n\n- **Low Angle**: Emphasizes scale and dominance.\n- **Overhead**: A bird's-eye view for expansive perspective.\n- **FPV (First-Person View)**: Immersive movement through dynamic environments.\n- **Handheld**: Natural and intimate movements, resembling real-life footage.",
  "sequence_id": "1100",
  "results": [
      {
      "instruction": "# Problem Exploder\n\nYour goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\n\n`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`",
      "step": "a",
      "title": "Problem Exploder",
      "input": "Organize your prompt using the following structure:\n\n1. **Subject**:\n   - Any person, place, or thing (e.g., *a handsome male model*, *a commercial airplane*).\n   - Specify details like attire, hairstyle, or emotional state (e.g., *wearing a red dress*, *red hair/mohawk*).\n\n2. **Action**:\n   - Define what the subject is doing (e.g., *walking*, *dancing*, *staring intently*).\n   - Use descriptive adjectives for enhanced clarity (e.g., *walking angrily*).\n\n3. **Setting/Mood**:\n   - Describe the location (e.g., *a castle*, *a busy city street*).\n   - Include environmental details such as weather and lighting (e.g., *stormy clouds*, *bright sunny day*).\n\n4. **Shot**:\n   - Indicate the type of camera shot (e.g., *Close-Up*, *Wide Angle*).\n\n5. **Style**:\n   - Suggest stylistic elements (e.g., *Cinematic Film*, *80’s Action Movie*).\n   - Add camera-specific details such as *IMAX* or *color grading ideas*.\n\n### Example Prompt:\n\"Low angle static shot: The camera is angled up at a woman wearing all orange as she stands in a tropical rainforest with colorful flora. The dramatic sky is overcast and gray.\"\n\n- **Tip**: Repeating or reinforcing key ideas in different sections of your prompt improves adherence.\n\n---\n\n## Sample Prompts\n\n### 1. Seamless Transitions\n- **Prompt**: \"Continuous hyperspeed FPV footage: The camera seamlessly flies through a glacial canyon to a dreamy cloudscape.\"\n\n- **Image Description**: A dynamic FPV sequence transitioning smoothly through a serene glacial environment into ethereal clouds.\n\n### 2. Oceanic Glow\n- **Prompt**: \"A glowing ocean at night with bioluminescent creatures underwater. The camera starts with a macro close-up of a glowing jellyfish and then expands to reveal the entire ocean lit up under a starry sky.\"\n\n- **Image Description**: A glowing jellyfish in vivid detail transitions to reveal a bioluminescent ocean beneath a sparkling starry night.\n\n### 3. Title Cards\n- **Prompt**: \"A title screen with dynamic movement. The scene starts at a colorful paint-covered wall. Suddenly, black paint pours on the wall to form the word 'Runway'. The dripping paint is detailed, textured, and lit with cinematic brilliance.\"\n\n- **Image Description**: A vibrant wall turns dramatic as black paint forms bold, textured text under vivid lighting.\n\n---\n\n## Key Visual Examples\n\n### 1. Unique Camera Movements\n- **Prompt**: \"Continuous hyperspeed FPV footage through a man's mouth into an epic landscape.\"\n- **Image Description**: A surreal transition through the interior of a human mouth, revealing a panoramic landscape.\n\n### 2. Dreamlike Pillow Fort\n- **Prompt**: \"A pillow fort in a cozy living room made from quilts and pillows. The camera smoothly zooms into the entrance, revealing an ancient castle interior.\"\n- **Image Description**: A whimsical living room transforms into an enchanting castle.\n\n### 3. Fire and Chaos\n- **Prompt**: \"A vortex of fire swirling through an abandoned warehouse.\"\n- **Image Description**: Flames spiral dynamically in a desolate, dramatic warehouse setting.\n\n---\n\n## Lighting, Styles, and Effects\n\n### Lighting Styles\n- **Diffused Lighting**: Soft, even lighting for atmospheric visuals.\n- **Silhouette**: Strong backlighting creating dramatic outlines.\n- **Lens Flare**: Bright reflections adding cinematic depth.\n- **Backlit**: Subject lit from behind for dramatic effects.\n\n### Movement Types\n- **Dynamic Motion**: High-energy, flowing camera transitions.\n- **Slow Motion**: Emphasizes intricate, slowed-down details.\n- **Timelapse**: Rapid transitions showcasing passing time.\n\n---\n\n## Text and Graphic Styles\n\n### Text Prompts\n1. **ATOMIC in Flames**: Over an erupting volcano, 'ATOMIC' is depicted in fiery text.\n2. **Icy Title**: 'ATOMIC' frozen in ice over an iceberg.\n3. **Unique Materials**: Text made of spaghetti, evoking whimsy and creativity.\n\n---\n\n## Example Prompts with Descriptions\n\n### 1. Cinematic Wildlife\n- **Prompt**: \"Static close-up shot of a sloth in a tree. Highlight its sleepy eyes and fur with cinematic lighting.\"\n- **Image Description**: A serene close-up of a sloth in its natural habitat.\n\n### 2. Urban Destruction\n- **Prompt**: \"[Scene] Molten lava flowing through city streets [Lighting] Vibrant orange glow [Camera Movement] Sweeping aerial shots.\"\n- **Image Description**: Lava courses dramatically through a cityscape, contrasting fiery tones with the urban environment.\n\n### 3. Vortex of Tanks\n- **Prompt**: \"Grainy WWII footage of a massive vortex made of tanks.\"\n- **Image Description**: A surreal vortex of military tanks under vintage cinematography.\n\n---\n\n## Camera Styles Overview\n\n- **Low Angle**: Emphasizes scale and dominance.\n- **Overhead**: A bird's-eye view for expansive perspective.\n- **FPV (First-Person View)**: Immersive movement through dynamic environments.\n- **Handheld**: Natural and intimate movements, resembling real-life footage.\n\n**Prompt Structures**: To achieve optimal results in GEN-3 Alpha, organize your prompts into clear sections for the **scene**, **subject**, and **camera movement**. Use the following structure. This outlines guidelines for optimizing video creation using Runway GEN-3, focusing on crafting structured prompts that detail scene, subject, and camera movement for achieving cinematic quality. Examples include transitioning from a glacial canyon to clouds, showcasing bioluminescent ocean life, and creating dynamic title cards with textured effects. Key visual examples further illustrate unique camera movements, such as a surreal journey from a human mouth to a landscape, and thematic visuals like a dreamlike pillow fort and a vortex of fire. The transcript also covers various lighting styles, movement types, and text and graphic styles, offering a comprehensive toolkit for enhancing visual storytelling. The conclusion emphasizes Runway GEN-3's potential for creative freedom through structured prompts and cinematic techniques:\n\n---\n\n# Runway Prompt Examples\n\n1.  **Image 1**\n    ![A man walking on a paved road, seen from behind, wearing a jacket and dark pants.](# \"Man walking on a paved road.\")\n    _Description:_ A man walking alone on a street. The road is slightly wet, and he is wearing dark attire.\n\n2.  **Image 2**\n    ![A person in a winter setting, standing in snow.](# \"Person standing in snow.\")\n    _Description:_ This shows a figure in a cold environment, possibly wearing winter gear, surrounded by snow.\n\n3.  **Image 3**\n    ![A person standing near a waterfall with lush greenery around.](# \"Near a waterfall with greenery.\")\n    _Description:_ The scene features flowing water in the background and dense foliage.\n\n4.  **Image 4**\n    ![A portrait-style image of a person with short hair looking pensively to one side.](# \"Portrait of a person.\")\n    _Description:_ The subject’s upper body and face are visible, with a calm or reflective expression.\n\n5.  **Image 5**\n    ![Silhouette of a person looking out at a calm ocean horizon, possibly during sunset.](# \"Silhouette at the ocean.\")\n    _Description:_ The sky shows warm tones, and the water is relatively still.\n\n6.  **Image 6**\n    ![A scenic photo of a beach at sunset with colorful clouds.](# \"Beach sunset scene.\")\n    _Description:_ The sun is low in the sky, casting orange and pink hues.\n\n7.  **Image 7**\n    ![Close-up portrait of a person with a bright smile, short hair, and vibrant background.](# \"Smiling person portrait.\")\n    _Description:_ The backdrop appears slightly blurred, focusing on the person’s face.\n\n8.  **Image 8**\n    ![A wide panoramic view of a modern cityscape with tall buildings.](# \"City skyline with skyscrapers.\")\n    _Description:_ Numerous skyscrapers are visible, with a hazy horizon.\n\n9.  **Image 9**\n    ![A futuristic-looking sphere or orb with light effects, hovering above a city-like structure.](# \"Futuristic orb above a city.\")\n    _Description:_ The orb glows with a blue-white hue, illuminating the area below.\n\n10.  **Image 10**\n    ![A night scene with silhouettes of trees against a misty background.](# \"Night forest silhouette.\")\n    _Description:_ The atmosphere is foggy or misty, giving an ethereal look to the forest.\n\n11.  **Image 11**\n    ![A floating crystal or jewel set against a cosmic-looking background.](# \"Floating crystal in cosmic scene.\")\n    _Description:_ The crystal shines with multiple facets, and spark-like stars are visible.\n\n12.  **Image 12**\n    ![A woman shown in partial silhouette, possibly backlit by a strong light source.](# \"Woman in silhouette.\")\n    _Description:_ Her face is partially lit; the rest of the background is dark or softly blurred.\n\n13.  **Image 13**\n    ![A bright orb or sphere swirling with purple and pink sparkles.](# \"Swirling purple orb.\")\n    _Description:_ The orb appears to be pulsating with energy in a cosmic or fantasy environment.\n\n14.  **Image 14**\n    ![Colorful illustration of a playful donkey (or horse) with some text in a stylized font.](# \"Cartoon donkey with text.\")\n    _Description:_ Bold, playful text accompanies the cartoonish donkey/horse, set against a plain background.\n\n15.  **Image 15**\n    ![A graphic with the word ‘Merry’ in a decorative style. Possibly holiday-themed.](# \"Holiday-themed graphic.\")\n    _Description:_ Stylized text with bright, festive colors, possibly referencing Christmas or a celebration.\n\n16.  **Image 16**\n    ![Underwater scene showing clear turquoise water and a few fish swimming near coral or sandy bottom.](# \"Underwater view with fish.\")\n    _Description:_ The seabed is partially visible; water clarity suggests a tropical or clear-water location.\n\n17.  **Image 17**\n    ![A panoramic view of a distant mountain range. The sky is overcast, creating a moody atmosphere.](# \"Mountain range under cloudy sky.\")\n    _Description:_ Dark silhouettes of mountains fade into the distance under a dimly lit sky.\n\n---\n\n\n---\n\n## Sample Prompts\n\n### 1. Seamless Transitions\n- **Prompt**: \"Continuous hyperspeed FPV footage: The camera seamlessly flies through a glacial canyon to a dreamy cloudscape.\"\n\n- **Image Description**: A dynamic FPV sequence transitioning smoothly through a serene glacial environment into ethereal clouds.\n\n### 2. Oceanic Glow\n- **Prompt**: \"A glowing ocean at night with bioluminescent creatures underwater. The camera starts with a macro close-up of a glowing jellyfish and then expands to reveal the entire ocean lit up under a starry sky.\"\n\n- **Image Description**: A glowing jellyfish in vivid detail transitions to reveal a bioluminescent ocean beneath a sparkling starry night.\n\n### 3. Title Cards\n- **Prompt**: \"A title screen with dynamic movement. The scene starts at a colorful paint-covered wall. Suddenly, black paint pours on the wall to form the word 'Runway'. The dripping paint is detailed, textured, and lit with cinematic brilliance.\"\n\n- **Image Description**: A vibrant wall turns dramatic as black paint forms bold, textured text under vivid lighting.\n\n---\n\n## Key Visual Examples\n\n### 1. Unique Camera Movements\n- **Prompt**: \"Continuous hyperspeed FPV footage through a man's mouth into an epic landscape.\"\n- **Image Description**: A surreal transition through the interior of a human mouth, revealing a panoramic landscape.\n\n### 2. Dreamlike Pillow Fort\n- **Prompt**: \"A pillow fort in a cozy living room made from quilts and pillows. The camera smoothly zooms into the entrance, revealing an ancient castle interior.\"\n- **Image Description**: A whimsical living room transforms into an enchanting castle.\n\n### 3. Fire and Chaos\n- **Prompt**: \"A vortex of fire swirling through an abandoned warehouse.\"\n- **Image Description**: Flames spiral dynamically in a desolate, dramatic warehouse setting.\n\n---\n\n## Lighting, Styles, and Effects\n\n### Lighting Styles\n- **Diffused Lighting**: Soft, even lighting for atmospheric visuals.\n- **Silhouette**: Strong backlighting creating dramatic outlines.\n- **Lens Flare**: Bright reflections adding cinematic depth.\n- **Backlit**: Subject lit from behind for dramatic effects.\n\n### Movement Types\n- **Dynamic Motion**: High-energy, flowing camera transitions.\n- **Slow Motion**: Emphasizes intricate, slowed-down details.\n- **Timelapse**: Rapid transitions showcasing passing time.\n\n---\n\n## Text and Graphic Styles\n\n### Text Prompts\n1. **ATOMIC in Flames**: Over an erupting volcano, 'ATOMIC' is depicted in fiery text.\n2. **Icy Title**: 'ATOMIC' frozen in ice over an iceberg.\n3. **Unique Materials**: Text made of spaghetti, evoking whimsy and creativity.\n\n---\n\n## Example Prompts with Descriptions\n\n### 1. Cinematic Wildlife\n- **Prompt**: \"Static close-up shot of a sloth in a tree. Highlight its sleepy eyes and fur with cinematic lighting.\"\n- **Image Description**: A serene close-up of a sloth in its natural habitat.\n\n### 2. Urban Destruction\n- **Prompt**: \"[Scene] Molten lava flowing through city streets [Lighting] Vibrant orange glow [Camera Movement] Sweeping aerial shots.\"\n- **Image Description**: Lava courses dramatically through a cityscape, contrasting fiery tones with the urban environment.\n\n### 3. Vortex of Tanks\n- **Prompt**: \"Grainy WWII footage of a massive vortex made of tanks.\"\n- **Image Description**: A surreal vortex of military tanks under vintage cinematography.\n\n---\n\n## Camera Styles Overview\n\n- **Low Angle**: Emphasizes scale and dominance.\n- **Overhead**: A bird's-eye view for expansive perspective.\n- **FPV (First-Person View)**: Immersive movement through dynamic environments.\n- **Handheld**: Natural and intimate movements, resembling real-life footage.",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"goal_map\": [\n    {\n      \"explicit_asks\": [\n        \"Organize your prompt using a specific multi-part structure with labeled sections: Subject, Action, Setting/Mood, Shot, Style\"\n      ],\n      \"hidden_assumptions\": [\n        \"The user wants a prompt that is clear, detailed, and structured for optimal use with image/video generation AI (specifically Runway GEN-3)\",\n        \"The sections must cover everything a visual artist or generative AI could need (who/what, what they're doing, where, how it looks, and in what style)\",\n        \"Each section must be filled properly, not left ambiguous or vague\",\n        \"Section labels should be explicit to aid the AI's prompt parsing and the user's clarity\",\n        \"Users may reference or repeat key ideas between sections to reinforce theme or intent\"\n      ],\n      \"sub_goals\": [\n        \"Identify and specify the main Subject (person/place/thing), including fine details: attire, hairstyle, emotional state, etc.\",\n        \"Describe a clear, specific Action for the subject, using adjectives/adverbs for clarity and mood\",\n        \"Define the Setting/Mood: physical location, weather, time of day, environmental characteristics, and lighting conditions\",\n        \"Select and indicate the Shot or camera angle/type (e.g., Close-Up, Wide Angle, Overhead, Low Angle, FPV, etc.)\",\n        \"Specify stylistic elements and technical/cinematic choices: film style, grading, lens or film stock, texture, reference era, or any camera-specific instructions\",\n        \"Reference or reinforce important elements across sections for maximum clarity and adherence\",\n        \"Consult visual and linguistic examples (as supplied) for inspiration or format compliance\",\n        \"Consider the creative, cinematic, or storytelling goals implied by the example prompts and their breakdowns\",\n        \"Adapt prompts for unique visual effects, camera movements, text graphics, or other specified goals (such as slow-motion, time-lapse, FPV transitions, or title cards)\",\n        \"Apply understanding of lighting, movement, mood, and style for intended cinematic quality\"\n      ],\n      \"blockers\": [\n        \"Vague or generic section input (e.g., 'man' instead of 'handsome middle-aged man in a black suit')\",\n        \"Ambiguous or conflicting details between sections\",\n        \"Omitting sections or failing to label each section explicitly\",\n        \"Insufficient detail for the intended function (e.g., not enough information for style or shot)\",\n        \"Not cross-referencing examples, leading to prompts that lack cinematic or creative impact\",\n        \"Not aligning mood/style with action or subject (e.g., cheerful mood with angry action unless purposefully contrasting)\",\n        \"Assuming the AI knows context not given in the prompt (must be fully self-contained and explicit)\",\n        \"Ignoring or misunderstanding the requirements for Runway GEN-3 prompt structure\"\n      ]\n    }\n  ]\n}",
        }
      }
    }