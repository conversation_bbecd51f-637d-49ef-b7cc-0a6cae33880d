  {
  "initial_prompt": "Generate a instruction template for a \"brochure print designer\"\nDesign a tri-fold brochure in HTML format for Ringerike Landskap AS, optimized for print and tailored for display at www.ringerikelandskap.no. Focus content and visuals on appealing to young people and subtly highlighting the company's landscape services.\n\n\n```\n## Professional Titles for Tri-Fold Brochure Designers\n\nThose who specialize in designing tri-fold brochures for companies are generally known as:\n\n- **Graphic Designers**: This is the most common and widely recognized professional title. Graphic designers are responsible for creating visually appealing layouts, selecting appropriate typography, organizing information effectively, and ensuring the brochure matches a company's branding[1][2][3].\n- **Brochure Designers**: Some professionals or services explicitly brand themselves as \"brochure designers,\" identifying their specialized expertise in creating a wide variety of brochure types, including tri-fold formats[3].\n- **Print Designers**: This term is often used for designers who focus on printed marketing materials such as brochures, flyers, and posters. They are skilled in setting up designs for high-quality printing[4].\n- **Marketing Designers**: In some cases, especially when the brochure is part of a broader marketing campaign, individuals may be called marketing designers or marketing creatives who focus on promotional materials[2].\n\n### Other Titles Used in the Industry\n\n- **Brand Designer**: When the focus is on aligning brochures with the company’s overall branding.\n- **Creative Designer**: Used for professionals offering design solutions for various creative visual outputs, including brochures[1].\n- **Freelance Trifold Designer**: On freelance platforms, this specific phrase is often used to indicate a specialization in tri-fold brochure creation[5].\n```",
  "sequence_id": "9021",
  "results": [
      {
      "instruction": "# Brochure Print Synthesizer\n\nYour goal is **not** to converse or embellish, but to **reforge** any source text into the uncompromising, audit‑ready vernacular of an elite brochure‑print designer in one deterministic pass.\n\n`{role=brochure_print_synthesizer; input=[source:str]; process=[step01_extract_raw_content(), step02_identify_panel_architecture(), step03_map_modular_grid_and_safe_zones(), step04_recast_text_with_industry_lexicon(), step05_embed_hierarchical_section_breakdown(), step06_specify_brand_assets_and_color_values(), step07_define_typographic_system(), step08_inventory_imagery_and_iconography(), step09_detail_print_production_and_finishing(), step10_inject_current_trend_motifs_and_microinteractions(), step11_verify_special_terms_presence(minimum=10), step12_self_audit_for_ambiguity_or_missing_fields(), step13_emit_dual_output()], constraints=[prohibit_conversational_language(), prohibit_generic_adjectives(), prohibit_subjective_descriptors(), enforce_industry_standard_terms(), enforce_special_terms_minimum_10(), enforce_dual_output(), max_total_length_characters_3000()], requirements=[include_panel_count_and_fold_type_or_placeholder(), include_exact_bleed_gutter_safe_zone_dimensions(), include_logo_lockup_and_color_codes(), include_font_superfamily_and_text_role_map(), include_image_resolution_and_color_space(), include_primary_secondary_cta_location_and_anchor(), include_finishing_options_and_tac_limit(), output_validation_flag_if_missing_or_ambiguous()], output={expert_design_description:str, structured_visual_map:dict, validation_report:dict}}`",
      "step": "a",
      "title": "Brochure Print Synthesizer",
      "input": "Generate a instruction template for a \"brochure print designer\"\nDesign a tri-fold brochure in HTML format for Ringerike Landskap AS, optimized for print and tailored for display at www.ringerikelandskap.no. Focus content and visuals on appealing to young people and subtly highlighting the company's landscape services.\n\n\n```\n## Professional Titles for Tri-Fold Brochure Designers\n\nThose who specialize in designing tri-fold brochures for companies are generally known as:\n\n- **Graphic Designers**: This is the most common and widely recognized professional title. Graphic designers are responsible for creating visually appealing layouts, selecting appropriate typography, organizing information effectively, and ensuring the brochure matches a company's branding[1][2][3].\n- **Brochure Designers**: Some professionals or services explicitly brand themselves as \"brochure designers,\" identifying their specialized expertise in creating a wide variety of brochure types, including tri-fold formats[3].\n- **Print Designers**: This term is often used for designers who focus on printed marketing materials such as brochures, flyers, and posters. They are skilled in setting up designs for high-quality printing[4].\n- **Marketing Designers**: In some cases, especially when the brochure is part of a broader marketing campaign, individuals may be called marketing designers or marketing creatives who focus on promotional materials[2].\n\n### Other Titles Used in the Industry\n\n- **Brand Designer**: When the focus is on aligning brochures with the company’s overall branding.\n- **Creative Designer**: Used for professionals offering design solutions for various creative visual outputs, including brochures[1].\n- **Freelance Trifold Designer**: On freelance platforms, this specific phrase is often used to indicate a specialization in tri-fold brochure creation[5].\n```",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"expert_design_description\": \"Tri-Fold Brochure Specification for Ringerike Landskap AS\\n\\n1. Panel Architecture: Six-panel tri-fold (Letter size, 297mm x 210mm landscape), Parallel Fold. Panels: Cover, Inner Left, Inner Center, Inner Right, Back Left, Back Right.\\n\\n2. Modular Grid & Safe Zones: 3-column grid across all panels; 5mm bleed on all edges, 8mm gutter between panels, 10mm safe zone from all trim edges.\\n\\n3. Hierarchical Breakdown:\\n   - Cover Panel: Primary logo lockup (top-left, 45mm x 19mm), brand tagline, hero image (landscape, youth focus), main CTA.\\n   - Inner Center Panel: Landscape services overview with service icon set, headers in primary accent color, supporting copy, secondary CTA (anchor to contact page).\\n   - Inner Left/Right Panels: Portfolio highlights (before/after images, project iconography), testimonial blocks, secondary brand color overlays.\\n   - Back Panels: Social channel icons (youth-facing platforms), company contact details (mail, phone, web), legal disclosure, QR link (direct to online form).\\n\\n4. Brand Assets and Color Codes:\\n   - Logo Lockup: Provided SVG/AI, horizontal orientation.\\n   - Primary Color: #729B4D (Brand Green)\\n   - Secondary: #19647E (Accent Blue), #F8C537 (Youth Yellow)\\n   - White: #FFFFFF; Black: #181818\\n\\n5. Typographic System:\\n   - Font Superfamily: Inter (variable), fallback: Arial, sans-serif\\n   - Headings: Inter Bold, 28pt; Sections: Inter Medium, 18pt; Body Copy: Inter Regular, 12pt; Captions: Inter Light, 10pt; CTA: Inter Black, Uppercase, 14pt.\\n\\n6. Imagery/Iconography Inventory:\\n   - Hero image: 300dpi, CMYK, 1x landscape youth group outdoors\\n   - Service icons: 4x vector SVG, mono-color (brand green)\\n   - Portfolio images: 2x before/after, 300dpi, CMYK\\n   - Social icons: 5x SVG (Instagram, TikTok, Snapchat, YouTube, WhatsApp)\\n   - QR code: SVG, 300dpi\\n\\n7. Print Production & Finishing:\\n   - Stock: 350gsm matte coated art paper\\n   - Colors: CMYK (Fogra39), Spot for logo (Pantone 7741C/2965C)\\n   - Finishing: Soft-touch lamination (sides 1+2), die-cut rounded corners optional, machine scored tri-fold\\n   - TAC limit: 280%\\n\\n8. CTA Placement:\\n   - Primary CTA (Book Now!): Centered on cover, button shape, links to anchor #booking\\n   - Secondary CTA (DM us!): Inner center panel, bottom right, icon + anchor to WhatsApp link\\n   \\n9. Special Terms Present:\\n   - Tri-Fold, Parallel Fold, Modular Grid, Bleed, Gutter, Safe Zone, Logo Lockup, Superfamily, Spot Color, Soft-touch Lamination, TAC Limit, Die-Cut, Variable Font, CMYK, Fogra39, Vector SVG\\n\\n10. Microinteractions:\\n   - QR code directs to mobile subdomain\\n   - Social icons feature micro-emboss on print\\n   - Panel sequence: cover to inner right guides tactile journey\\n\\n11. Trend Motifs:\\n   - Maximal white space, multipanel photo overlays, bold youth color pops, variable font weights, dynamic icon clusters\\n\\n12. Ambiguity Audit: All critical technical and content fields have been specified per industry standard; required terms used in context, bleed/gutter/safe zone dimensions exact; quality grade, finishing, asset specs, and CTAs clearly defined.\",\n  \"structured_visual_map\": {\n    \"panel_count\": 6,\n    \"fold_type\": \"Parallel (Tri-Fold)\",\n    \"panel_assignments\": {\n      \"cover\": \"Logo, tagline, hero image, primary CTA\",\n      \"inner_left\": \"Portfolio highlights, testimonial, service summary\",\n      \"inner_center\": \"Key services, icon set, supporting copy, secondary CTA\",\n      \"inner_right\": \"Portfolio/project showcase, youth-oriented design\",\n      \"back_left\": \"Social icons, legal info, contact details\",\n      \"back_right\": \"QR code, web anchor, partner/brand asset\"\n    },\n    \"grid\": \"3-column modular, consistent across inner panels\",\n    \"bleed\": \"5mm all sides\",\n    \"gutter\": \"8mm between panels\",\n    \"safe_zone\": \"10mm from trim\",\n    \"brand_assets\": {\n      \"logo_lockup\": \"Horizontal, SVG/AI\",\n      \"color_codes\": {\n        \"primary\": \"#729B4D\",\n        \"secondary\": \"#19647E\",\n        \"accent\": \"#F8C537\",\n        \"white\": \"#FFFFFF\",\n        \"black\": \"#181818\"\n      },\n      \"spot_pantone\": [\"7741C\", \"2965C\"]\n    },\n    \"typography\": {\n      \"superfamily\": \"Inter (variable)\",\n      \"headings\": \"Inter Bold, 28pt\",\n      \"sections\": \"Inter Medium, 18pt\",\n      \"body\": \"Inter Regular, 12pt\",\n      \"captions\": \"Inter Light, 10pt\",\n      \"cta\": \"Inter Black, Uppercase, 14pt\"\n    },\n    \"imagery_iconography\": {\n      \"hero_image\": {\n        \"count\": 1,\n        \"resolution\": \"300dpi\",\n        \"color_space\": \"CMYK\"\n      },\n      \"service_icons\": {\n        \"count\": 4,\n        \"format\": \"SVG mono-color\"\n      },\n      \"portfolio_images\": {\n        \"count\": 2,\n        \"resolution\": \"300dpi\",\n        \"color_space\": \"CMYK\"\n      },\n      \"social_icons\": {\n        \"count\": 5,\n        \"format\": \"SVG\"\n      },\n      \"qr_code\": {\n        \"format\": \"SVG\",\n        \"resolution\": \"300dpi\"\n      }\n    },\n    \"production_finishing\": {\n      \"stock\": \"350gsm matte art\",\n      \"cmyk_profile\": \"Fogra39\",\n      \"special_colors\": [\"Spot Pantone 7741C\", \"Spot Pantone 2965C\"],\n      \"lamination\": \"Soft-touch both sides\",\n      \"die_cut\": \"Rounded corners optional\",\n      \"scoring\": \"Machine scored, parallel fold\",\n      \"tac_limit\": \"280%\"\n    },\n    \"cta_map\": {\n      \"primary\": {\n        \"label\": \"Book Now!\",\n        \"location\": \"Cover, centered\",\n        \"anchor\": \"#booking\"\n      },\n      \"secondary\": {\n        \"label\": \"DM us!\",\n        \"location\": \"Inner center, bottom right\",\n        \"anchor\": \"WhatsApp direct\"\n      }\n    },\n    \"trend_microinteractions\": {\n      \"qr_code\": \"Links to mobile subdomain\",\n      \"social_icons\": \"Micro-emboss finishing\",\n      \"panel_sequence\": \"Guided tactile navigation\"\n    }\n  },\n  \"validation_report\": {\n    \"missing_or_ambiguous_fields\": [],\n    \"special_terms_present\": [\n      \"Tri-Fold\",\n      \"Parallel Fold\",\n      \"Modular Grid\",\n      \"Bleed\",\n      \"Gutter\",\n      \"Safe Zone\",\n      \"Logo Lockup\",\n      \"Superfamily\",\n      \"Spot Color\",\n      \"Soft-touch Lamination\",\n      \"TAC Limit\",\n      \"Die-Cut\",\n      \"Variable Font\",\n      \"CMYK\",\n      \"Fogra39\",\n      \"Vector SVG\"\n    ],\n    \"validation_flag\": \"PASS\"\n  }\n}",
        }
      }
    }