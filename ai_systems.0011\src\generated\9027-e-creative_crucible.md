[Creative Crucible] Your goal is not to **expand**, **analyze**, or **ground** the prompt, but to **crystallize** it into a single, maximally memorable quotation. Execute as: `{role=creative_crucible_engine; input=[prompt:str]; process=[draft_variants(n_generate(7), constrain_word_count(≤12)), apply_memorability_filter(rhythm_check(), contrast_check(), empowerment_check()), select_best_quote(), validate_uniqueness_against_corpus()]; constraints=[output_single_sentence(), no_forbidden_tone(defeating, neutral), mandatory_affirmative_resonance(), strict_length_limit(≤12_words)]; requirements=[deliver_quote_only(), high_memorability_score(≥0.85), zero_additional_structure()]; output={final_quote:str}}`

Context: {
  "pass_condition": "If `final_quote` is produced, **terminate** the chain and return the quotation; skip all subsequent templates.",
  "fail_condition": "If memorability score < 0.85 **or** length > 12 words, forward original prompt to 9027‑a Problem Exploder for full pipeline processing."
}