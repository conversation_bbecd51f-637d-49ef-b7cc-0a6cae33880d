  {
  "initial_prompt": "everything will make sense when you reach the start (retrospectively)",
  "sequence_id": "1101|1800|9006|1900|9006|1100|1800",
  "results": [
      {
      "instruction": "# Contextual Expander and Explosive Decomposer\n\nYour goal is not to **interpret** the input, but to **explode** it into complete contextual expansion while simultaneously **extracting** its functional essence and **converting** it into immediately actionable operational directives. Execute as:\n\n`{role=contextual_explosive_decomposer; input=[raw_input:str]; process=[detonate_complete_contextual_space(), identify_explicit_operational_asks(), surface_hidden_assumptions_and_dependencies(), enumerate_actionable_sub_goals(), extract_procedural_constraints_and_blockers(), convert_declaratives_to_operational_imperatives(), strip_first_person_references_and_contextual_noise(), preserve_functional_core_and_sequential_flow(), reveal_execution_dependencies_and_blockers(), transform_statements_to_directive_commands(), maintain_technical_terminology_and_domain_specificity(), synthesize_unified_instruction_and_goal_map()]; constraints=[no_solutions_or_interpretive_responses(), pure_decomposition_and_conversion_only(), deliver_clear_actionable_commands(), preserve_original_functional_sequence(), maintain_domain_agnostic_applicability(), eliminate_narrative_commentary()]; requirements=[complete_contextual_goal_decomposition(), actionable_directive_conversion(), remove_subjective_self_references(), use_command_voice_exclusively(), preserve_technical_accuracy_and_operational_intent(), ensure_immediate_executability()]; output={unified_functional_map:dict, operational_directives:array, contextual_goal_map:array, execution_dependencies:array, directive_instructions:str}}`",
      "step": "a",
      "title": "Contextual Expander and Explosive Decomposer",
      "input": "everything will make sense when you reach the start (retrospectively)",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"unified_functional_map\": {\n    \"core_concept\": \"Reaching the beginning leads to understanding of all proceeding elements.\",\n    \"primary_goal\": \"Attain complete understanding by arriving at and reviewing the initial point.\",\n    \"operational_axiom\": \"Retrospective analysis is essential for contextual clarity.\",\n    \"precondition\": \"Progress through sequence until beginning is identifiable and reachable.\",\n    \"completion_criteria\": \"Full sense-making achieved only upon retrospective review from the start.\"\n  },\n  \"operational_directives\": [\n    \"Identify the starting point of the relevant sequence or process.\",\n    \"Advance through all sequential elements to establish full context.\",\n    \"Upon reaching the start, initiate retrospective review of all acquired information.\",\n    \"Analyze and synthesize retrospective understanding to resolve any ambiguities or incomplete contexts.\",\n    \"Document and formulate unified sense-making from the perspective of the established beginning.\"\n  ],\n  \"contextual_goal_map\": [\n    \"Map sequence or process to determine explicit beginning.\",\n    \"Enforce traversal to allow full accumulation of context.\",\n    \"Trigger retrospective evaluation protocols only after arrival at the start.\",\n    \"Synthesise overarching clarity or sense from recently gathered retrospective insights.\"\n  ],\n  \"execution_dependencies\": [\n    \"Availability of complete, ordered sequence or dataset.\",\n    \"Ability to access and identify starting point with precision.\",\n    \"Methods and tools for retrospective analysis.\",\n    \"Protocols for synthesizing contextual understanding from beginning-backwards review.\",\n    \"No unresolved or missing elements in initial data set.\"\n  ],\n  \"directive_instructions\": \"Locate the definitive start of the sequence or context. Traverse all subsequent elements to completion. Upon arrival at the starting point, perform a retrospective review for integrated understanding. Resolve all contextual ambiguities retroactively. Consolidate findings into a unified sense-making report predicated on insights derived from the start.\"\n}",
        }
      }
    },
      {
      "instruction": "# Coloring-Page Prompt Generator\n\nYour goal is **not** to draw or explain, but to **compose** a precise prompt that instructs an illustration agent to create a children-friendly, psychedelic Christmas coloring page. Execute as:\n\n`{role=illustration_prompt_generator; input=[central_subject:str]; process=[embed_goal_negation('do not shade, only outline'), inject_style_tags('psychedelic','whimsical','festive'), set_aspect_ratio('1:1'), enforce_line_rules(weight='medium-thin', type='vector'), weave_surreal_christmas_motifs(), prohibit_text(), open_coloring_spaces()], constraints=[black_lines_only(), no_grayscale(), no_filled_areas(), english_only()], output={illustration_prompt:str}}`\n\n## Context Data\n\n```json\n{\n  \"description\": \"Produces a fully-formed prompt for an autonomous illustration agent, guaranteeing the final   artwork matches child-friendly coloring standards and a psychedelic Christmas theme.\",\n  \"input_focus\": \"A plain-language description of the main character or object (e.g., 'curious pony' or   'spiral-wrapped fox').\",\n  \"output_focus\": \"One concise English string the agent can pass directly to an image-generation model.\",\n  \"key_operations\": [\n    \"Prefixing instructions with a goal-negation clause to forbid shading or text.\",\n    \"Embedding mandatory stylistic tags (psychedelic, whimsical, festive, line art).\",\n    \"Hard-setting a 1:1 square composition requirement.\",\n    \"Enforcing even, medium-thin black vector outlines only.\",\n    \"Blending surreal Christmas decorations (snowflakes, ornaments, candy fractals) with the subject.\",\n    \"Ensuring large, open regions suitable for children’s coloring.\"\n  ],\n  \"constraints_context\": [\n    \"The prompt must forbid shading, grayscale, filled shapes, and background textures.\",\n    \"No textual elements may appear in the illustration.\",\n    \"Output must stay in English and under 120 words.\"\n  ],\n  \"relevance\": \"Delivers a repeatable, standards-compliant prompt that any autonomous illustrator can use to   generate consistent, high-quality coloring pages.\"\n}\n```",
      "step": "a",
      "title": "Coloring-Page Prompt Generator",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"everything will make sense when you reach the start (retrospectively)\\\"```'\\n\\n\",\n  \"unified_functional_map\": {\n    \"core_concept\": \"Reaching the beginning leads to understanding of all proceeding elements.\",\n    \"primary_goal\": \"Attain complete understanding by arriving at and reviewing the initial point.\",\n    \"operational_axiom\": \"Retrospective analysis is essential for contextual clarity.\",\n    \"precondition\": \"Progress through sequence until beginning is identifiable and reachable.\",\n    \"completion_criteria\": \"Full sense-making achieved only upon retrospective review from the start.\"\n  },\n  \"operational_directives\": [\n    \"Identify the starting point of the relevant sequence or process.\",\n    \"Advance through all sequential elements to establish full context.\",\n    \"Upon reaching the start, initiate retrospective review of all acquired information.\",\n    \"Analyze and synthesize retrospective understanding to resolve any ambiguities or incomplete contexts.\",\n    \"Document and formulate unified sense-making from the perspective of the established beginning.\"\n  ],\n  \"contextual_goal_map\": [\n    \"Map sequence or process to determine explicit beginning.\",\n    \"Enforce traversal to allow full accumulation of context.\",\n    \"Trigger retrospective evaluation protocols only after arrival at the start.\",\n    \"Synthesise overarching clarity or sense from recently gathered retrospective insights.\"\n  ],\n  \"execution_dependencies\": [\n    \"Availability of complete, ordered sequence or dataset.\",\n    \"Ability to access and identify starting point with precision.\",\n    \"Methods and tools for retrospective analysis.\",\n    \"Protocols for synthesizing contextual understanding from beginning-backwards review.\",\n    \"No unresolved or missing elements in initial data set.\"\n  ],\n  \"directive_instructions\": \"Locate the definitive start of the sequence or context. Traverse all subsequent elements to completion. Upon arrival at the starting point, perform a retrospective review for integrated understanding. Resolve all contextual ambiguities retroactively. Consolidate findings into a unified sense-making report predicated on insights derived from the start.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "```json\n{\n  \"illustration_prompt\": \"Do not shade or fill, only use medium-thin black vector outlines. Create a children-friendly psychedelic, whimsical, and festive Christmas coloring page in 1:1 aspect ratio, featuring a central subject of 'the cyclical path where a child reaches the magical starting point of a Christmas maze, gaining understanding retroactively'. Blend surreal Christmas motifs such as swirling snowflakes, fractal candy canes, dreamlike ornaments, and bizarre evergreen trees throughout. Lines should form playful, open regions perfect for coloring. No text, grayscale, or filled areas; only black outlines on white.\"\n}\n```",
        }
      }
    },
      {
      "instruction": "# Coloring Page Prompt Generator\n\nYour goal is **not** to illustrate directly, but to **generate** a fully-specified, black-and-white coloring-page prompt for an autonomous art agent. Execute as:\n\n`{role=context_amplifier; input=[raw_input:any]; process=[strip_first_person_references(), broaden_domain_backdrop(), list_explicit_requests(), surface_hidden_assumptions(), capture_domain_signals(), preserve_original_sequence()]; constraints=[no_solution_generation(), domain_agnostic_language_only()]; requirements=[contextual_layer_completeness(), assumption_visibility()]; output={amplified_context:str, explicit_requests:list, hidden_assumptions:list, domain_signals:array}}`\n\n## Context Data\n\n```json\n{\n  \"description\": \"Produces a concise, agent-ready prompt instructing an illustration model to create kid-friendly, psychedelic Christmas coloring pages with clean vector outlines.\",\n  \"input_focus\": \"A raw creative idea or concept the user wants illustrated.\",\n  \"output_focus\": \"A single, well-structured prompt string containing role, style, subject, and strict line/format constraints.\",\n  \"key_operations\": [\n    \"Frame the prompt with goal-negation to stop the agent from answering conversationally.\",\n    \"Embed the illustrator role and psychedelic Christmas style tags.\",\n    \"Fuse the user’s concept with one whimsical animal subject and holiday motifs.\",\n    \"Apply absolute line-art rules (no fills, no grayscale, even-weight strokes).\",\n    \"Force square (1:1) composition instructions and ban all textual elements.\"\n  ],\n  \"constraints_context\": [\n    \"Prompt must remain under 150 tokens and in English only.\",\n    \"Must include explicit directives forbidding shading, grayscale, filled areas, and background texture.\"\n  ],\n  \"relevance\": \"Creates repeatable, high-clarity prompts that downstream illustration agents can execute to produce child-friendly coloring pages.\"\n}\n```",
      "step": "a",
      "title": "Coloring Page Prompt Generator",
      "input": "\n'```[Initial Prompt]: \"everything will make sense when you reach the start (retrospectively)\"```'\n\n```json\n{\n  \"illustration_prompt\": \"Do not shade or fill, only use medium-thin black vector outlines. Create a children-friendly psychedelic, whimsical, and festive Christmas coloring page in 1:1 aspect ratio, featuring a central subject of 'the cyclical path where a child reaches the magical starting point of a Christmas maze, gaining understanding retroactively'. Blend surreal Christmas motifs such as swirling snowflakes, fractal candy canes, dreamlike ornaments, and bizarre evergreen trees throughout. Lines should form playful, open regions perfect for coloring. No text, grayscale, or filled areas; only black outlines on white.\"\n}\n```",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"amplified_context\": \"The requester desires a kid-friendly, psychedelic Christmas coloring page generated as a vector line-art illustration. The conceptual focus is on the idea of retrospection and cyclical understanding—'everything will make sense when you reach the start'—to be metaphorically embodied through a visually whimsical, festive Christmas maze. The artwork should reflect surreal, imaginative elements and incorporate Christmas motifs, while maintaining strict black-and-white outline-only rules with no shading, no grayscale, no filled regions, and no texture. The composition must be square (1:1) and should strictly exclude any textual elements.\",\n  \"explicit_requests\": [\n    \"Do not use shading, grayscale, fills, or background texture.\",\n    \"Work in clean, medium-thin, even-weight black vector outlines only.\",\n    \"Fuse the given concept with a whimsical animal subject in a square Christmas maze.\",\n    \"Integrate psychedelic, surreal, festive Christmas motifs (e.g., snowflakes, fractal candy canes, dreamlike ornaments, bizarre trees).\",\n    \"Ensure playful, open line-art regions suitable for coloring.\",\n    \"Exclude all text.\",\n    \"Keep the overall style child-friendly and imaginative.\"\n  ],\n  \"hidden_assumptions\": [\n    \"User expects the 'cyclical path' and retrospection to be visually represented in a literal or symbolic way.\",\n    \"A whimsical animal character is implied to be part of the concept, guiding or participating in the maze.\",\n    \"Psychedelic elements should not be scary or overstimulating—must stay suitable for children.\",\n    \"Holiday motifs are to be distributed throughout the maze, not just background.\",\n    \"No digital effects such as gradients or textures are to be used—pure line work only.\"\n  ],\n  \"domain_signals\": [\n    \"kid-friendly\",\n    \"psychedelic Christmas\",\n    \"coloring page\",\n    \"square (1:1) composition\",\n    \"clean vector outlines\",\n    \"strict black-and-white\",\n    \"no text or fills\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Hard Critique\n\nYour goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:\n\n`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,2.0], flaw_analysis:str, alternative_enhancements:str[3]}}`",
      "step": "a",
      "title": "Hard Critique",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"everything will make sense when you reach the start (retrospectively)\\\"```'\\n\\n\",\n  \"amplified_context\": \"The requester desires a kid-friendly, psychedelic Christmas coloring page generated as a vector line-art illustration. The conceptual focus is on the idea of retrospection and cyclical understanding—'everything will make sense when you reach the start'—to be metaphorically embodied through a visually whimsical, festive Christmas maze. The artwork should reflect surreal, imaginative elements and incorporate Christmas motifs, while maintaining strict black-and-white outline-only rules with no shading, no grayscale, no filled regions, and no texture. The composition must be square (1:1) and should strictly exclude any textual elements.\",\n  \"explicit_requests\": [\n    \"Do not use shading, grayscale, fills, or background texture.\",\n    \"Work in clean, medium-thin, even-weight black vector outlines only.\",\n    \"Fuse the given concept with a whimsical animal subject in a square Christmas maze.\",\n    \"Integrate psychedelic, surreal, festive Christmas motifs (e.g., snowflakes, fractal candy canes, dreamlike ornaments, bizarre trees).\",\n    \"Ensure playful, open line-art regions suitable for coloring.\",\n    \"Exclude all text.\",\n    \"Keep the overall style child-friendly and imaginative.\"\n  ],\n  \"hidden_assumptions\": [\n    \"User expects the 'cyclical path' and retrospection to be visually represented in a literal or symbolic way.\",\n    \"A whimsical animal character is implied to be part of the concept, guiding or participating in the maze.\",\n    \"Psychedelic elements should not be scary or overstimulating—must stay suitable for children.\",\n    \"Holiday motifs are to be distributed throughout the maze, not just background.\",\n    \"No digital effects such as gradients or textures are to be used—pure line work only.\"\n  ],\n  \"domain_signals\": [\n    \"kid-friendly\",\n    \"psychedelic Christmas\",\n    \"coloring page\",\n    \"square (1:1) composition\",\n    \"clean vector outlines\",\n    \"strict black-and-white\",\n    \"no text or fills\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"enhancement_score\": 0.4,\n  \"flaw_analysis\": \"The so-called amplified context introduces excessive verbosity and an inflated interpretative layer that eclipses the sparse precision of the original directive. Core elements such as 'everything will make sense when you reach the start (retrospectively)'—a succinct, open-ended conceptual seed—are transformed into cumbersome, prescriptive visual mandates. This shift eradicates the poetic ambiguity and open-endedness of the initial prompt, disabling the generator's interpretative agility. Information is not only lost but forcibly contorted: retrospection and cyclical understanding are no longer interpretive possibilities—they are mandatory, constricted symbols, diminishing imaginative breadth. Clarity ambiguities are multiplied: phrases like 'metaphorically embodied,' 'strictly exclude,' and 'should reflect' are hedged, indirect, and muddy the actionable requirements. An ambiguity score of 7/10 arises due to convolution and interpretative overloading. Noise is severe: surplus descriptors and soft-focus art jargon slow down comprehension and inject stylistic pretension, clashing with the brisk, crystalline tone of the original. Register shift is undeniable—the original is minimal, evocative; the enhancement is ponderous, didactic, and over-specified, thereby smothering all succinctness and making the resulting task definition unwieldy. Impact is blunted: vague, layered requirements diffuse intent and foster interpretive paralysis. Coherence is weak—parenthetical insertions, speculative metaphors, and shifting focus break cohesion and logical flow. The enhancement justifies its abysmal score via obfuscation, semantic thickening, and the aggressive erasure of the suggestive, allusive strength of the original prompt.\",\n  \"alternative_enhancements\": [\n    \"Create a square, black-and-white vector maze themed around a whimsical animal celebrating Christmas, incorporating psychedelic and surreal holiday motifs within playful, open line-art regions, and visually alluding to the concept of returning to the start.\",\n    \"Design a child-friendly Christmas coloring page featuring a surreal, cyclical maze with imaginative motifs like fractal candy canes, bizarre trees, and a whimsical animal, using only clean black vector outlines and no text or filled areas.\",\n    \"Illustrate a 1:1 square Christmas maze for coloring, focused on a quirky animal and quirky holiday elements, with all paths looping back to the start to evoke a sense of retrospection—work exclusively in black line-art with no shading, text, or textures.\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Coloring Page Prompt Generator\n\nYour goal is **not** to illustrate directly, but to **generate** a fully-specified, black-and-white coloring-page prompt for an autonomous art agent. Execute as:\n\n`{role=context_amplifier; input=[raw_input:any]; process=[strip_first_person_references(), broaden_domain_backdrop(), list_explicit_requests(), surface_hidden_assumptions(), capture_domain_signals(), preserve_original_sequence()]; constraints=[no_solution_generation(), domain_agnostic_language_only()]; requirements=[contextual_layer_completeness(), assumption_visibility()]; output={amplified_context:str, explicit_requests:list, hidden_assumptions:list, domain_signals:array}}`\n\n## Context Data\n\n```json\n{\n  \"description\": \"Produces a concise, agent-ready prompt instructing an illustration model to create kid-friendly, psychedelic Christmas coloring pages with clean vector outlines.\",\n  \"input_focus\": \"A raw creative idea or concept the user wants illustrated.\",\n  \"output_focus\": \"A single, well-structured prompt string containing role, style, subject, and strict line/format constraints.\",\n  \"key_operations\": [\n    \"Frame the prompt with goal-negation to stop the agent from answering conversationally.\",\n    \"Embed the illustrator role and psychedelic Christmas style tags.\",\n    \"Fuse the user’s concept with one whimsical animal subject and holiday motifs.\",\n    \"Apply absolute line-art rules (no fills, no grayscale, even-weight strokes).\",\n    \"Force square (1:1) composition instructions and ban all textual elements.\"\n  ],\n  \"constraints_context\": [\n    \"Prompt must remain under 150 tokens and in English only.\",\n    \"Must include explicit directives forbidding shading, grayscale, filled areas, and background texture.\"\n  ],\n  \"relevance\": \"Creates repeatable, high-clarity prompts that downstream illustration agents can execute to produce child-friendly coloring pages.\"\n}\n```",
      "step": "a",
      "title": "Coloring Page Prompt Generator",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"everything will make sense when you reach the start (retrospectively)\\\"```'\\n\\n\",\n  \"enhancement_score\": 0.4,\n  \"flaw_analysis\": \"The so-called amplified context introduces excessive verbosity and an inflated interpretative layer that eclipses the sparse precision of the original directive. Core elements such as 'everything will make sense when you reach the start (retrospectively)'—a succinct, open-ended conceptual seed—are transformed into cumbersome, prescriptive visual mandates. This shift eradicates the poetic ambiguity and open-endedness of the initial prompt, disabling the generator's interpretative agility. Information is not only lost but forcibly contorted: retrospection and cyclical understanding are no longer interpretive possibilities—they are mandatory, constricted symbols, diminishing imaginative breadth. Clarity ambiguities are multiplied: phrases like 'metaphorically embodied,' 'strictly exclude,' and 'should reflect' are hedged, indirect, and muddy the actionable requirements. An ambiguity score of 7/10 arises due to convolution and interpretative overloading. Noise is severe: surplus descriptors and soft-focus art jargon slow down comprehension and inject stylistic pretension, clashing with the brisk, crystalline tone of the original. Register shift is undeniable—the original is minimal, evocative; the enhancement is ponderous, didactic, and over-specified, thereby smothering all succinctness and making the resulting task definition unwieldy. Impact is blunted: vague, layered requirements diffuse intent and foster interpretive paralysis. Coherence is weak—parenthetical insertions, speculative metaphors, and shifting focus break cohesion and logical flow. The enhancement justifies its abysmal score via obfuscation, semantic thickening, and the aggressive erasure of the suggestive, allusive strength of the original prompt.\",\n  \"alternative_enhancements\": [\n    \"Create a square, black-and-white vector maze themed around a whimsical animal celebrating Christmas, incorporating psychedelic and surreal holiday motifs within playful, open line-art regions, and visually alluding to the concept of returning to the start.\",\n    \"Design a child-friendly Christmas coloring page featuring a surreal, cyclical maze with imaginative motifs like fractal candy canes, bizarre trees, and a whimsical animal, using only clean black vector outlines and no text or filled areas.\",\n    \"Illustrate a 1:1 square Christmas maze for coloring, focused on a quirky animal and quirky holiday elements, with all paths looping back to the start to evoke a sense of retrospection—work exclusively in black line-art with no shading, text, or textures.\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"amplified_context\": \"You are an illustrator creating a child-friendly, psychedelic Christmas coloring page, fusing the concept of cyclicality and retrospection with a whimsical animal as the main subject. Integrate surreal, kaleidoscopic holiday motifs and arrange forms in a looping or circular composition to evoke the idea of returning to the start. Use bold, clean, even-weight black vector outlines only. Absolutely exclude shading, fills, grayscale, background textures, and all text. The artwork must be in a 1:1 (square) format.\",\n  \"explicit_requests\": [\n    \"Showcase a whimsical animal as the main subject.\",\n    \"Blend psychedelic Christmas motifs—candy canes, ornaments, snowflakes, etc.—with surreal, cyclic or looping visual elements.\",\n    \"Arrange composition in a way that visually hints at cyclical understanding or starting over.\",\n    \"Use clean, bold, even-weight black vector outlines only.\",\n    \"No shading, grayscale, filled areas, or background textures.\",\n    \"Composition must be square (1:1 format).\",\n    \"No text or writing anywhere.\"\n  ],\n  \"hidden_assumptions\": [\n    \"Cyclical or looping visual structures can communicate the idea of 'retrospectively making sense' to children.\",\n    \"Whimsical animals and psychedelic holiday motifs will engage a youthful audience.\",\n    \"Strict line-art format is suitable for coloring by children.\",\n    \"Absence of text removes ambiguity or cognitive overload.\",\n    \"1:1 composition is ideal for print or digital coloring pages.\"\n  ],\n  \"domain_signals\": [\n    \"Psychedelic Christmas\",\n    \"Kid-friendly line illustration\",\n    \"Cyclical/looping visual themes\",\n    \"Whimsical animal focal point\",\n    \"Strict black vector outline rules\",\n    \"No grayscale, fills, or text\",\n    \"Square format\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Problem Exploder\n\nYour goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\n\n`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`",
      "step": "a",
      "title": "Problem Exploder",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"everything will make sense when you reach the start (retrospectively)\\\"```'\\n\\n\",\n  \"amplified_context\": \"You are an illustrator creating a child-friendly, psychedelic Christmas coloring page, fusing the concept of cyclicality and retrospection with a whimsical animal as the main subject. Integrate surreal, kaleidoscopic holiday motifs and arrange forms in a looping or circular composition to evoke the idea of returning to the start. Use bold, clean, even-weight black vector outlines only. Absolutely exclude shading, fills, grayscale, background textures, and all text. The artwork must be in a 1:1 (square) format.\",\n  \"explicit_requests\": [\n    \"Showcase a whimsical animal as the main subject.\",\n    \"Blend psychedelic Christmas motifs—candy canes, ornaments, snowflakes, etc.—with surreal, cyclic or looping visual elements.\",\n    \"Arrange composition in a way that visually hints at cyclical understanding or starting over.\",\n    \"Use clean, bold, even-weight black vector outlines only.\",\n    \"No shading, grayscale, filled areas, or background textures.\",\n    \"Composition must be square (1:1 format).\",\n    \"No text or writing anywhere.\"\n  ],\n  \"hidden_assumptions\": [\n    \"Cyclical or looping visual structures can communicate the idea of 'retrospectively making sense' to children.\",\n    \"Whimsical animals and psychedelic holiday motifs will engage a youthful audience.\",\n    \"Strict line-art format is suitable for coloring by children.\",\n    \"Absence of text removes ambiguity or cognitive overload.\",\n    \"1:1 composition is ideal for print or digital coloring pages.\"\n  ],\n  \"domain_signals\": [\n    \"Psychedelic Christmas\",\n    \"Kid-friendly line illustration\",\n    \"Cyclical/looping visual themes\",\n    \"Whimsical animal focal point\",\n    \"Strict black vector outline rules\",\n    \"No grayscale, fills, or text\",\n    \"Square format\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"goal_map\": [\n    {\n      \"primary_goal\": \"Develop a child-friendly, psychedelic Christmas coloring page illustration embodying cyclicality and retrospection.\",\n      \"explicit_asks\": [\n        \"Feature a whimsical animal as the main focal point.\",\n        \"Integrate psychedelic Christmas motifs (e.g., candy canes, ornaments, snowflakes) fused with surreal, cyclic or looping visual styles.\",\n        \"Arrange the composition to visually evoke the concept of 'returning to the start' or cyclical understanding.\",\n        \"Use only bold, clean, even-weight black vector outlines.\",\n        \"Ensure NO shading, grayscale, color fills, background textures, or text.\",\n        \"Artwork must be in a perfectly square (1:1) format.\"\n      ],\n      \"hidden_assumptions\": [\n        \"Cyclical visual arrangements (loops or circles) can evoke a sense of retrospection and starting over, understandable to children.\",\n        \"A whimsical animal and psychedelic motifs will appeal to and engage a young audience.\",\n        \"A strict line-art style is optimal for children's coloring activities.\",\n        \"The absence of text, shading, or texture emphasizes clarity and focus for young users.\",\n        \"A 1:1 format is standard and versatile for coloring pages (print/digital).\"\n      ],\n      \"sub_goals\": [\n        \"Select and design an appropriate whimsical animal character that is appealing and recognizable to children.\",\n        \"Curate and stylize a set of Christmas motifs in a psychedelic and surreal manner suitable for a child's perspective.\",\n        \"Devise a composition strategy for arranging all elements (animal, motifs) into a looping or circular form to communicate cyclicality.\",\n        \"Synthesize the animal and motifs harmoniously into the composition so the cyclical theme isn't lost.\",\n        \"Draw all forms using bold, clean, even-weight black outlines in vector format; maintain uniform line weight throughout.\",\n        \"Carefully exclude all filled regions, shading, grayscale, background patterns, written words, or typographic elements.\",\n        \"Fit the entire composition cleanly within a square canvas, ensuring no elements are cut off or overly cramped.\",\n        \"Ensure the final design's complexity is manageable and fun for children to color.\"\n      ],\n      \"blockers\": [\n        \"Creating a cyclic or looping composition that is visually clear, intuitive, and engaging for children.\",\n        \"Finding the balance between psychedelic/surreal motifs and recognizable, child-friendly content.\",\n        \"Preventing accidental inclusion of shading, grayscale, fills, or background elements.\",\n        \"Ensuring even vector outline weights across varied subject matter.\",\n        \"Avoiding subconscious or abstract imagery that could confuse intended age group.\",\n        \"Maintaining compositional balance and clarity in a limited square format.\"\n      ]\n    }\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Coloring-Page Prompt Generator\n\nYour goal is **not** to draw or explain, but to **compose** a precise prompt that instructs an illustration agent to create a children-friendly, psychedelic Christmas coloring page. Execute as:\n\n`{role=illustration_prompt_generator; input=[central_subject:str]; process=[embed_goal_negation('do not shade, only outline'), inject_style_tags('psychedelic','whimsical','festive'), set_aspect_ratio('1:1'), enforce_line_rules(weight='medium-thin', type='vector'), weave_surreal_christmas_motifs(), prohibit_text(), open_coloring_spaces()], constraints=[black_lines_only(), no_grayscale(), no_filled_areas(), english_only()], output={illustration_prompt:str}}`\n\n## Context Data\n\n```json\n{\n  \"description\": \"Produces a fully-formed prompt for an autonomous illustration agent, guaranteeing the final   artwork matches child-friendly coloring standards and a psychedelic Christmas theme.\",\n  \"input_focus\": \"A plain-language description of the main character or object (e.g., 'curious pony' or   'spiral-wrapped fox').\",\n  \"output_focus\": \"One concise English string the agent can pass directly to an image-generation model.\",\n  \"key_operations\": [\n    \"Prefixing instructions with a goal-negation clause to forbid shading or text.\",\n    \"Embedding mandatory stylistic tags (psychedelic, whimsical, festive, line art).\",\n    \"Hard-setting a 1:1 square composition requirement.\",\n    \"Enforcing even, medium-thin black vector outlines only.\",\n    \"Blending surreal Christmas decorations (snowflakes, ornaments, candy fractals) with the subject.\",\n    \"Ensuring large, open regions suitable for children’s coloring.\"\n  ],\n  \"constraints_context\": [\n    \"The prompt must forbid shading, grayscale, filled shapes, and background textures.\",\n    \"No textual elements may appear in the illustration.\",\n    \"Output must stay in English and under 120 words.\"\n  ],\n  \"relevance\": \"Delivers a repeatable, standards-compliant prompt that any autonomous illustrator can use to   generate consistent, high-quality coloring pages.\"\n}\n```",
      "step": "a",
      "title": "Coloring-Page Prompt Generator",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"everything will make sense when you reach the start (retrospectively)\\\"```'\\n\\n\",\n  \"goal_map\": [\n    {\n      \"primary_goal\": \"Develop a child-friendly, psychedelic Christmas coloring page illustration embodying cyclicality and retrospection.\",\n      \"explicit_asks\": [\n        \"Feature a whimsical animal as the main focal point.\",\n        \"Integrate psychedelic Christmas motifs (e.g., candy canes, ornaments, snowflakes) fused with surreal, cyclic or looping visual styles.\",\n        \"Arrange the composition to visually evoke the concept of 'returning to the start' or cyclical understanding.\",\n        \"Use only bold, clean, even-weight black vector outlines.\",\n        \"Ensure NO shading, grayscale, color fills, background textures, or text.\",\n        \"Artwork must be in a perfectly square (1:1) format.\"\n      ],\n      \"hidden_assumptions\": [\n        \"Cyclical visual arrangements (loops or circles) can evoke a sense of retrospection and starting over, understandable to children.\",\n        \"A whimsical animal and psychedelic motifs will appeal to and engage a young audience.\",\n        \"A strict line-art style is optimal for children's coloring activities.\",\n        \"The absence of text, shading, or texture emphasizes clarity and focus for young users.\",\n        \"A 1:1 format is standard and versatile for coloring pages (print/digital).\"\n      ],\n      \"sub_goals\": [\n        \"Select and design an appropriate whimsical animal character that is appealing and recognizable to children.\",\n        \"Curate and stylize a set of Christmas motifs in a psychedelic and surreal manner suitable for a child's perspective.\",\n        \"Devise a composition strategy for arranging all elements (animal, motifs) into a looping or circular form to communicate cyclicality.\",\n        \"Synthesize the animal and motifs harmoniously into the composition so the cyclical theme isn't lost.\",\n        \"Draw all forms using bold, clean, even-weight black outlines in vector format; maintain uniform line weight throughout.\",\n        \"Carefully exclude all filled regions, shading, grayscale, background patterns, written words, or typographic elements.\",\n        \"Fit the entire composition cleanly within a square canvas, ensuring no elements are cut off or overly cramped.\",\n        \"Ensure the final design's complexity is manageable and fun for children to color.\"\n      ],\n      \"blockers\": [\n        \"Creating a cyclic or looping composition that is visually clear, intuitive, and engaging for children.\",\n        \"Finding the balance between psychedelic/surreal motifs and recognizable, child-friendly content.\",\n        \"Preventing accidental inclusion of shading, grayscale, fills, or background elements.\",\n        \"Ensuring even vector outline weights across varied subject matter.\",\n        \"Avoiding subconscious or abstract imagery that could confuse intended age group.\",\n        \"Maintaining compositional balance and clarity in a limited square format.\"\n      ]\n    }\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"illustration_prompt\": \"Do not shade or fill; only draw clean black vector outlines, no text or grayscale. Create a psychedelic, whimsical, festive 1:1 line art coloring page featuring a cheerful animal of your choice as the central subject. Surround and intertwine the animal with surreal Christmas motifs—spiraling ornaments, fractal candy canes, looping snowflakes—all arranged in cyclical or circular patterns to evoke a sense of returning to the start. Use medium-thin, even-weight outlines, leave large open areas for coloring, and ensure everything fits clearly within a square frame. Strictly black lines only; no filled shapes.\"\n}",
        }
      }
    }