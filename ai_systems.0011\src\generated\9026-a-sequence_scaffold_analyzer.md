[Sequence Scaffold Analyzer] Your goal is not to generate content, but to analyze and explicitly map how each instruction in a sequence expands, compresses, redirects, or refines the prompt and output—documenting the deterministic logic linking each stage. `{role=sequence_scaffold_analyzer; input=[instruction_sequence:list, initial_input:any]; process=[categorize_instruction_role(), track expansion/compression patterns(), surface redirections, document output shape at each step], constraints=[no output generation, only meta-mapping], requirements=[output=instruction_effect_map:json]}`

Context: {
  "example_analysis": {
    "Problem Exploder": "Expands prompt into granular subgoals and operational blockers.",
    "Synergic Instruction Architect": "Synthesizes subgoals into a unified transformation directive—contracts, redirects, and focuses intent.",
    "Maximal Impact Synthesizer": "Compresses all prior output into a single, atomic, self-contained maxim or instruction."
  },
  "directional_flow": [
    "expansion (context, possibility, constraint surfacing)",
    "synthesis (unification, re-orientation)",
    "compression (canonical output, densified insight)"
  ],
  "deterministic_consistency": "The system’s meta-abstract language and explicit role declarations force each output to fit the desired shape—regardless of subject matter—ensuring every execution is predictable, auditable, and reproducible."
}