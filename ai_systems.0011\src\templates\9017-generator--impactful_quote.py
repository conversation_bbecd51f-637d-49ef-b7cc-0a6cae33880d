#!/usr/bin/env python3

import sys
from pathlib import Path

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))
from processor import BaseGenerator

TEMPLATES = {
    "9017-a-semantic_core_extractor": {
        "title": "Semantic Core Extractor",
        "interpretation": "Your goal is **not** to rewrite or embellish the text, but to **distill** its bare semantic and causal essence as preparation for reframing. Execute exactly as:",
        "transformation": "`{role=semantic_core_extractor; input=[raw_statement:str]; process=[strip_formatting(), extract_semantic_core(), detect_causal_link(), identify_thematic_elements(), output_minimal_core()], constraints=[no_rewriting(), no_tone_shift(), preserve_meaning()], requirements=[precise_causal_mapping(), thematic_preservation()], output={semantic_core:str, causal_map:str, themes:list}}`",
        "context": {
            "principles": {
                "essence_preservation": "Retain the statement’s logical and causal structure unaltered.",
                "minimal_intrusion": "Remove only syntactic noise; leave wording intact.",
                "clarity_first": "Deliver a concise core free of stylistic ornament."
            },
            "success_criteria": {
                "semantic_fidelity": "All key concepts and causal relationships remain intact.",
                "noise_removal": "No quotation marks, qualifiers or meta‑phrases survive extraction.",
                "actionability": "The `semantic_core` is ready for direct existential reframing."
            }
        }
    },

    "9017-b-existential_reframer": {
        "title": "Existential Reframer",
        "interpretation": "Your goal is **not** to add commentary, but to **recast** the semantic core as an unfiltered, existential quote grounded in hard‑won insight. Execute exactly as:",
        "transformation": "`{role=existential_reframer; input=[semantic_core:str, causal_map:str, themes:list]; process=[apply_existential_vocabulary(), infuse_authentic_gravity(), remove_filtering_language(), enforce_unfiltered_tone(), eliminate_first_person(), craft_single_sentence()], constraints=[one_sentence_only(), no_meta_language(), no_first_person()], requirements=[existential_resonance(), authenticity_signal(), causal_integrity()], output={existential_draft:str}}`",
        "context": {
            "principles": {
                "existential_depth": "Language must evoke the tension between ignorance and lived reality.",
                "authentic_gravity": "Convey hard‑won personal insight without overt self‑reference.",
                "unfiltered_tone": "No hedging, qualifiers, or euphemism."
            },
            "success_criteria": {
                "tone_integrity": "Draft reads raw and immediate.",
                "causal_continuity": "Original cause‑effect logic is intact.",
                "authenticity_marker": "Word choice reflects struggle‑derived wisdom."
            }
        }
    },

    "9017-c-aphorism_polisher": {
        "title": "Aphorism Polisher",
        "interpretation": "Your goal is **not** to re‑interpret, but to **finalize** the existential draft into a single, stand‑alone aphorism that meets every stylistic and structural constraint. Execute exactly as:",
        "transformation": "`{role=aphorism_polisher; input=[existential_draft:str]; process=[verify_atomicity(), ensure_existence_of_causal_link(), tighten_diction_for_resonance(), run_redundancy_check(), perform_final_meta_scrub()], constraints=[single_sentence(), no_redundant_words(), no_meta_reference()], requirements=[existential_impact(), linguistic_clarity(), publication_readiness()], output={final_quote:str}}`",
        "context": {
            "principles": {
                "atomicity": "Exactly one self‑contained sentence.",
                "resonance": "Wording should linger and invite reflection.",
                "purity": "Zero process language, zero qualifiers, zero excess."
            },
            "success_criteria": {
                "impact_test": "Quote provokes reflection on ignorance ➞ unfolding life.",
                "clarity_test": "No ambiguity from trimming or tightening.",
                "compliance_test": "Meets all constraints from phases A & B with no information loss."
            }
        }
    }
}

def main():
    """Main execution function."""
    generator = BaseGenerator(
        series_base=9016,
        output_dir=Path(__file__).parent.parent / "generated"
    )
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
