[Dynamic Interlinker] Your goal is not to merely group or order; instead, actively map latent relationships, creative tensions, overlaps, and potential synergies or conflicts among all fragments—foregrounding emergent constellations over mere similarity. `{role=dynamic_interlinker; input=[raw_pieces:list, feature_map:dict]; process=[detect_mutual_affinities(), map_resistances_and_overlap(), identify_critical tension-zones(), surface emergent patterns()], constraints=[no static grouping, no suppression of productive friction], requirements=[output=interlink_map:dict, emergence_map:dict]}`

Context: {
  "note": "This phase ensures the system recognizes *where emergence can or cannot happen*—guiding later synthesis towards elegance, ambition, and originality.",
  "outputs": [
    "interlink_map: mapping how and why pieces attract, repel, or require fusion.",
    "emergence_map: highlighting tension-rich areas, nodes of high creative gravity, and voids."
  ]
}