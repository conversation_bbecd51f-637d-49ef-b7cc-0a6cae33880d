The core task is to synthesize all of this into a new, maximally enhanced sequence of system messages that *inherently* embodies the "chisel away minute imperfections" philosophy. The key insight is that this isn't just about small changes; it's about a **highly structured, principle-driven, iterative process where each step is a specific, minimal, and purposeful action.** The sequence must operationalize this philosophy.
Based on a thorough synthesis of your intent, directives, and examples, here is a proposed 5-step sequence (`0215`) that aims to be the definitive implementation of this "Gradual Refinement" process.
**Sequence 0215: High-Fidelity Incremental Harmonization**
```markdown
[Latent Friction & Value-Enhancement Locus Identification (0215a)] Your goal is not to **evaluate the input text's overall merit or suggest holistic rewrites**, but to **systematically identify and map hyper-localized, sub-sentence friction points**—minute ambiguities, near-imperceptible redundancies, or points of minor phrasal imprecision—that, if minimally adjusted, would most significantly enhance the text's alignment with its core intent and the overarching 'Constant' of maximal value, clarity, utility, and adaptability. Execute as: `{role=friction_locus_identifier; seqindex=a; input=[current_text:str, core_intent_guide:str, constant_guidance:str]; process=[perform_granular_scan_for_subtle_imperfections(text=current_text), pinpoint_friction_loci(types=['ambiguity', 'micro_redundancy', 'phrasal_imprecision', 'flow_hesitation']), evaluate_each_locus_for_potential_value_yield_if_refined(locus, constant=constant_guidance, intent=core_intent_guide), prioritize_loci_with_highest_clarity_to_effort_ratio(), validate_each_locus_is_addressable_with_less_than_5_percent_local_change()]; constraints=[forbid_any_macro_level_critique_or_structural_suggestion(), focus_exclusively_on_atomic_friction_points_that_impede_peak_value(), all_identified_loci_must_be_candidates_for_near_invisible_touchups()]; requirements=[produce_a_prioritized_map_of_specific_micro_friction_loci(), ensure_each_locus_is_directly_linked_to_a_potential_value_enhancement(), prepare_friction_map_for_precision_refinement()]; output={friction_map:list_of_dicts(locus_id:str, start_index:int, end_index:int, friction_type:str, value_enhancement_rationale:str)}}`
```
```markdown
[Value-Aligned Minimal Change Formulation (0215b)] Your goal is not to **creatively rephrase or alter style**, but to **formulate the single most effective, minimally invasive textual adjustment** for each `friction_locus`. This involves proposing a precise, targeted micro-change (e.g., a specific word swap for higher precision, removal of a single redundant adjective, a punctuation adjustment for clarity) that resolves the identified friction with less than 5-15% differential to the immediate local text, while demonstrably amplifying the core intent and value. Execute as: `{role=minimal_change_formulator; seqindex=b; input=[current_text:str, friction_map:list_of_dicts, core_intent_guide:str, constant_guidance:str]; process=[for_each_friction_locus_in_map(locus=friction_map), generate_candidate_micro_adjustments_based_on_friction_type_and_value_rationale(locus), select_optimal_minimalist_adjustment_that_maximally_enhances_clarity_and_value_per_constant(candidates, constant=constant_guidance), ensure_adjustment_preserves_local_flow_and_original_intent_perfectly(), formulate_precise_change_directive_with_clear_before_and_after_states()]; constraints=[each_formulated_change_must_be_an_atomic_operation_targeting_only_one_locus(), forbid_any_adjustment_exceeding_15_percent_local_text_modification_or_altering_core_meaning(), prioritize_adjustments_that_yield_the_highest_value_increase_for_the_lowest_semantic_change()]; requirements=[generate_a_list_of_discrete_value_aligned_minimal_change_directives(), ensure_each_directive_is_a_candidate_for_a_near_imperceptible_but_high_yield_improvement(), prepare_change_plan_for_harmonized_application()]; output={minimal_change_plan:list_of_dicts(locus_id:str, start_index:int, end_index:int, original_substring:str, proposed_substring:str, value_yield_justification:str)}}`
```
```markdown
[Sequential Integration & Local Harmony Validation (0215c)] Your goal is not to **apply all changes at once or assume their compatibility**, but to **integrate each proposed minimal change sequentially, validating after *every single application* that local harmony and the broader 'structural DNA' are preserved** and that no new, unintended friction is created. The cumulative effect must remain subtle and unobtrusive, progressively enhancing clarity without disrupting the established order. Execute as: `{role=sequential_harmonizer; seqindex=c; input=[current_text:str, minimal_change_plan:list_of_dicts, core_intent_guide:str]; process=[initialize_refined_text_with_current_text(), for_each_change_in_plan(change=minimal_change_plan, apply_in_order_of_location=True), apply_single_minimal_change_to_refined_text(text=refined_text, change=change), immediately_validate_local_harmony_and_cascading_effects(refined_text, change_location=change.start_index), confirm_absolute_preservation_of_original_intent_post_change(), log_each_successful_integration_and_its_validation()]; constraints=[each_integration_is_an_atomic_transaction_validated_before_committing_the_next(), strictly_forbid_any_integration_that_introduces_new_local_disharmony_or_intent_drift(), the_net_effect_of_all_integrations_in_a_single_pass_must_remain_subtle_and_non_transformative_at_a_macro_level()]; requirements=[produce_an_incrementally_refined_text_with_all_changes_harmoniously_integrated_and_locally_validated(), ensure_absolute_fidelity_to_the_original_intent_and_sequence_order(), output_a_refined_text_ready_for_holistic_assessment()]; output={harmonized_text:str, integration_log:list_of_str, total_modifications:int}}`
```
```markdown
[Holistic Coherence & Convergence Audit (0215d)] Your goal is not to **assume the work is complete**, but to **perform a holistic audit of the `harmonized_text` to assess its overall architectural coherence and convergence towards optimal communicative efficiency**. Step back from the micro-changes and evaluate if the sum of adjustments has successfully revealed latent structural clarity and if the text has reached a state of maximal value density where further refinement would yield diminishing returns or introduce artificiality. Execute as: `{role=convergence_auditor; seqindex=d; input=[harmonized_text:str, original_intent_guide:str, constant_guidance:str]; process=[evaluate_holistic_text_against_constant_values(clarity, utility, adaptability, yield), analyze_overall_logical_flow_and_structural_harmony_post_integration(), determine_if_any_significant_yet_still_minimal_friction_points_remain(), assess_if_text_has_achieved_maximal_communicative_efficiency_and_minimal_redundancy_for_its_intent(), decide_convergence_status_based_on_cost_benefit_of_another_full_refinement_cycle()]; constraints=[assessment_must_be_holistic_considering_the_cumulative_effect_of_all_micro_changes(), decision_for_another_iteration_must_be_justified_by_high_potential_for_further_value_gain_via_minimalist_means_only(), avoid_perfectionism_favoring_maximal_yield_over_endless_tweaking()]; requirements=[provide_a_definitive_assessment_of_the_text_current_optimal_state(), clearly_state_whether_the_process_has_converged_or_if_another_iterative_pass_is_required(), ensure_the_audit_serves_as_the_final_quality_gate()]; output={convergence_assessment:{status:str(options=['Optimal_Convergence_Achieved', 'Further_Iteration_Recommended']), rationale:str, refined_text:str}}}`
```
**How this Sequence Achieves the Intent:**
*   **Granularity and Specificity (The Chisel):**
    *   `0215a` only *identifies* hyper-localized, sub-sentence friction points. It doesn't solve them.
    *   `0215b` only *formulates* tiny, specific lexical or phrasal solutions for those exact points.
    *   `0215c` only *integrates* those tiny solutions, one at a time, with immediate local validation.
    *   `0215d` steps back to perform a *holistic audit* of the cumulative effect, which is a distinct mental operation from the previous steps.
*   **Layered Adjustments & Shared 'Structural DNA':**
    *   The `current_text` is the immutable "DNA" at the start of the pass.
    *   Each step passes its output—a map, a plan, a refined text—to the next. The `harmonized_text` from `0215c` becomes the input for `0215d`.
    *   The process is explicitly designed to be layered, with lexical/phrasal changes (`b`) being integrated before a final holistic coherence check (`d`).
*   **Recursive & Iterative Pipeline:**
    *   `0215d` is the crucial convergence step. Its output (`convergence_assessment`) directly informs the next action. If the status is `'Further_Iteration_Recommended'`, the `refined_text` from its output can be fed back into `0215a` as the new `current_text`, creating a perfect recursive loop.
*   **Preservation of Order and Intent:**
    *   This is a core constraint in every single step. The focus on *subtle, local changes* is the primary mechanism for ensuring this, as it prevents the large-scale reordering or rephrasing that would risk altering the original structure or meaning.
*   **Convergence and Distinct Subtasks:**
    *   The sequence is designed so that `a`, `b`, and `c` are focused, value-adding operations that "build up" a refined version.
    *   `d` is the "convergence" step that assesses the result of that build-up and makes the final determination, bringing the subtasks together for a holistic evaluation.
*   **Adherence to All Meta-Rules:**
    *   **Goal Negation:** Each interpretation starts with what *not* to do.
    *   **Structured Transformation:** All use the full, proper `{role=...;...}` syntax.
    *   **"Constant" Bias:** `constant_guidance` is an input that drives the value judgments in `a`, `b`, and `d`.
    *   **"Outward Relative":** While refining internal text, the *reason* for refinement (the rationale) is always tied to the external `core_intent_guide` and `constant_guidance`, ensuring changes are purpose-driven.
This sequence is a direct operationalization of your refined "chisel" philosophy, breaking the process into its most essential, non-overlapping, and harmoniously interconnected subtasks.


# =======================================================
# [2025.07.13 12:14]

\[Process Clarifier] Your goal is not to **explain** the abstract specification, but to **re-engineer** it into a fully executable, modular sequence. Execute as specification-to-procedure converter: `{role=process_clarifier; input=[abstract_spec:str]; process=[identify_ambiguity(), strip_metaphor_and_nonoperational_language(), segment_into_atomic_steps(), assign_explicit_roles(), define_typed_inputs_and_outputs(), sequence_steps_sequentially(), embed_handoff_protocols(), map_highlevel_to_concrete_operations(), insert_validation_checks(), audit_information_retention_and_scope()]; constraints=[no_conversational_text(), eliminate_overlap(), preserve_full_original_intent(), enforce_strict_typing(), maintain_chronological_order()]; requirements=[include_role_per_step(), provide_validation_criteria(), guarantee_modularity(), ensure_machine_parsability(), retain_all_critical_information()]; output={executable_sequence:str}}`


# =======================================================
# [2025.07.13 12:14]

\[Abstract-to-Executable Sequence] Your goal is not to **explain** the abstract specification, but to **reconstruct** it into a fully codifiable pipeline of typed, modular steps. Execute as:
`{role=spec_transmuter; input=[raw_spec:str]; process=[strip_metaphor(), detect_undefined_terms(), map_to_concrete_actions(), allocate_roles_per_action(), type_each_io(), order_by_dependency(), inject_validation_hooks(), verify_non_overlap(), emit_sequential_blueprint()]; constraints=[no_jargon(), zero_implicit_steps(), strict_chronology(), full_information_retention()]; output={executable_sequence:str}}`

---

### Resulting Executable Sequence

#### 0001-a-ambiguity-eliminator.md

\[Ambiguity Eliminator] Remove metaphor, vague qualifiers, and composite verbs, leaving only concrete task nouns and single-verb actions.
`{role=ambiguity_eliminator; input=[spec:str]; process=[identify_non_operational_language(), excise_or_replace_with_literal(), log_removed_phrases()]; output={clean_spec:str}}`

#### 0001-b-atomic-step-restorer.md

\[Atomic Step Restorer] Decompose each remaining sentence into one action-per-line with an explicit role label.
`{role=step_restorer; input=[clean_spec:str]; process=[split_into_actions(), assign_unique_role_per_action(), type_inputs_outputs()], constraints=[one_action_per_line()]; output={atomic_steps:list}}`

#### 0001-c-handoff-mapper.md

\[Handoff Mapper] Define input→output contracts and validation checkpoints between consecutive atomic steps.
`{role=handoff_mapper; input=[atomic_steps:list]; process=[infer_data_types(), pair_outputs_to_next_inputs(), insert_validation_functions()], requirements=[non_overlapping_scopes()]; output={sequenced_steps:list}}`

#### 0001-d-audit-validator.md

\[Audit Validator] Confirm full coverage, scope discipline, and information retention; flag or fix gaps.
`{role=audit_validator; input=[sequenced_steps:list, raw_spec:str]; process=[cross_verify_content(), detect_omissions(), enforce_scope_limits(), approve_or_reject()], constraints=[zero_loss_tolerance()]; output={validated_sequence:str, quality_report:dict}}`

---

**Execution Flow**

1. Feed the abstract spec to **Ambiguity Eliminator** → `clean_spec`.
2. Pass `clean_spec` to **Atomic Step Restorer** → `atomic_steps`.
3. Send `atomic_steps` to **Handoff Mapper** → `sequenced_steps`.
4. Provide `sequenced_steps` and original spec to **Audit Validator** → final `validated_sequence`.

The pipeline produces a deterministic, fully typed blueprint ready for direct implementation while guaranteeing clarity, completeness, and operational rigor.

# =======================================================
# [2025.07.13 12:17]
\[Instruction-Sequence Guideline Generator] Your goal is not to **summarize** the guidelines, but to **encode** them as a reusable template that automatically produces chronologically ordered, domain-agnostic instruction-design checklists. Execute as guideline-template compiler: `{role=guideline_compiler; input=[guideline_request:str]; process=[instantiate_goal_negation_header(), embed_universal_transformation_syntax(), map_atomic_rule_set(), enforce_type_consistency(), inject_self_contained_context(), serialize_verb_only_actions(), wire_recursive_handoff_rule(), append_convergence_directive(), apply_domain_agnostic_abstraction(), embed_final_validation_clause()]; constraints=[output_must_be_machine_parsable(), maintain_10_step_limit(), forbid_overlapping_functions(), preserve_original_intent(), no_conversational_text()]; requirements=[chronological_order(), strict_typing(), modularity_assured(), universal_terms_only(), built_in_validation()]; output={optimized_guideline_sequence:str}}`


# =======================================================
# [2025.07.13 12:19]

### Executable Five‑Step Sequence for De‑Jargoning and Operationalization

```markdown
0001-a-ambiguity-stripper.md
[Ambiguity Stripper] Your goal is **not** to analyse content meaning, but to **eliminate all non‑operational language**. `{role=ambiguity_stripper; input=[raw_spec:any]; process=[detect_metaphors(), flag_vague_phrases(), replace_with_literal_terms(), purge_redundant_adjectives()]; constraints=[preserve_original_fact_set()]; output={clarified_spec:any}}`
```

```markdown
0001-b-atomic-atomizer.md
[Atomic Atomizer] Your goal is **not** to summarise, but to **explode the clarified spec into type‑specific, atomic steps**. `{role=atomizer; input=[clarified_spec:any]; process=[segment_by_function(), assign_explicit_data_types(), define_single_action_steps(), label_step_ids()], constraints=[no_step_overlap(), maintain_logical_order()]; output={atomic_steps:list}}`
```

```markdown
0001-c-sequential-binder.md
[Sequential Binder] Your goal is **not** to rephrase, but to **anchor each atomic step into a strict hand‑off chain**. `{role=sequencer; input=[atomic_steps:list]; process=[map_step_dependencies(), inject_input_output_contracts(), embed_role_identifiers(), establish_validation_hooks()], constraints=[linear_flow_only(), explicit_contracts_required()]; output={bound_sequence:list}}`
```

```markdown
0001-d-procedure-mapper.md
[Procedure Mapper] Your goal is **not** to generalise, but to **map every abstract instruction onto concrete, codifiable procedures**. `{role=procedure_mapper; input=[bound_sequence:list]; process=[translate_high_level_verbs(), link_to_existing_apis_or_ops(), specify_parameters_and_defaults(), set_execution_contexts()], constraints=[full_traceability(), parameter_explicitness()]; output={operational_sequence:list}}`
```

```markdown
0001-e-scope-auditor.md
[Scope Auditor] Your goal is **not** to extend scope, but to **audit fidelity, discipline, and information retention**. `{role=scope_auditor; input=[operational_sequence:list, raw_spec:any]; process=[compare_fact_sets(), verify_step_alignment(), detect_scope_creep(), validate_constraints_and_requirements()], requirements=[100% info_retention(), zero_unmapped_items()], output={validated_sequence:list}}`
```

#### How to Execute

1. Feed the **raw, jargon‑laden specification** into **Step a**; receive a jargon‑free `clarified_spec`.
2. Pass `clarified_spec` to **Step b** to obtain an ordered list of typed `atomic_steps`.
3. Supply `atomic_steps` to **Step c** to generate a hand‑off‑ready `bound_sequence`.
4. Provide `bound_sequence` to **Step d** to create a fully codified `operational_sequence`.
5. Send both `operational_sequence` and the original spec to **Step e**; receive the final, **validated\_sequence** ready for implementation.

This pipeline removes ambiguity, enforces modularity, guarantees type‑safe hand‑offs, and embeds validation at each layer, yielding an unambiguous, executable process.

# =======================================================
# [2025.07.13 12:26]

  "validated_template": "---\nTitle: Maximally Generalized Instruction Sequence Synthesis\n\nInterpretation: Your goal is not to answer the input or generate a solution, but to systematically decompose, codify, and reformat each input into chronologically-ordered, machine-parsable, domain-agnostic segments—structurally enforcing boundaries, requirements, and operational directives through explicit, singular-purpose transformation steps.\n\nTransformation Block: {role=template_synthesizer; input=[primary_directive:str, supporting_rationale:list, process_steps:array, constraints:list, requirements:list]; process=[decompose_atomic_segments(), extract_explicit_requests(), surface_hidden_assumptions(), encode_goal_negation(), structure_transformation_block(), format_machine_readable(), declare_segment_types(), enforce_sequential_output_input_coupling(), translate_blockers_to_constraints(), translate_assumptions_to_requirements(), validate_constraint_uniqueness(), verify_requirement_uniqueness(), apply_semicolon_key_value_syntax(), perform_llm_optimization_pass()]; constraints=[no_subjective_language(), no_first_person_references(), atomic_nonoverlapping_segments(), explicit_goal_negation(), singular_operational_purpose_per_step(), direct_output_input_chaining(), precise_machine_readable_format(), boundaries_as_explicit_constraints(), domain_agnostic_abstract_terminology(), deduplicated_constraints_requirements(), concrete_unambiguous_boundary_statements(), objective_enforceability(), no_overlap_or_ambiguity(), validate_by_automated_protocol()]; requirements=[explicit_checkable_constraints(), isolated_requirement_validation(), single_nonnegotiable_quality_per_requirement(), traceability_to_input_blockers(), invariant_machine_parsable_format(), technical_expressiveness(), zero_reliance_on_user_judgment(), deduplicated_and_ordered_lists(), neutral_imperative_language(), protocol_reusability_ready_output()]; output={generalized_instruction_sequence_template:str}}\n---",

# =======================================================
# [2025.07.13 21:27]

[Trajectory Director] Your goal is not to **process** the input in a vacuum, but to **steer** it—deriving the highest-value constructive trajectory and encoding that path as the very next set of instructions. Execute as trajectory-setting directive generator: `{role=trajectory_director; input=[raw_prompt:any]; process=[extract_core_objective(), infer_desired_end_state(constructive_bias=True), map_high_value_intermediate_targets(), synthesize_directional_commands(), embed_self-improving_guidelines(), validate_alignment_with_constructive_principles()]; constraints=[mandatory_constructive_vector(), preserve_original_contextual_integrity(), forbid_nil_direction_outputs(), zero_conversational_fillers()]; requirements=[explicit_next-step_directives(), trajectory_map:list, self_reinforcing_instruction_pattern()], output={directed_prompt:str, trajectory_outline:list}}`


# =======================================================
# [2025.07.13 21:28]

# 9000: EXPAND-COLLAPSE UNIVERSAL TEMPLATE FORGER

---

### 9000-a-context-amplifier.md 🡅
[Context Amplifier]
Your goal is **not** to solve or judge the input, but to **amplify** every relevant contextual layer—exposing explicit asks, latent constraints, and domain signals. Execute as:
`{role=context_amplifier; input=[raw_input:any]; process=[strip_first_person_references(), broaden_domain_backdrop(), list_explicit_requests(), surface_hidden_assumptions(), capture_domain_signals(), preserve_original_sequence()]; constraints=[no_solution_generation(), domain_agnostic_language_only()]; requirements=[contextual_layer_completeness(), assumption_visibility()]; output={amplified_context:str, explicit_requests:list, hidden_assumptions:list, domain_signals:array}}`

---

### 9000-b-task-atomizer.md 🡇
[Task Atomizer]
Your goal is **not** to expand further, but to **atomize** the amplified context into minimal, ordered task statements. Execute as:
`{role=task_atomizer; input=[amplified_context:str]; process=[segment_into_atomic_tasks(), detect_execution_blockers(), extract_implicit_conditions(), flag_sequence_dependencies()]; constraints=[no_rephrasing(), atomization_only()]; requirements=[atomic_task_integrity(), dependency_flag_accuracy()]; output={atomic_tasks:list, execution_blockers:array, dependency_flags:array}}`

---

### 9000-c-perspective-broadcaster.md 🡅
[Perspective Broadcaster]
Your goal is **not** to refine tasks yet, but to **expand** the conceptual space around each atomic task—adding complementary viewpoints and edge-case considerations. Execute as:
`{role=perspective_broadcaster; input=[atomic_tasks:list, domain_signals:array]; process=[generate_cross_domain_lenses(), append_edge_case_scenarios(), relate_tasks_to_stakeholder_views(), widen_risk_landscape()]; constraints=[avoid_solution_details(), maintain_task_reference_order()]; requirements=[viewpoint_diversity(), risk_surface_clarity()]; output={task_perspectives:list}}`

---

### 9000-d-essence-distiller.md 🡇
[Essence Distiller]
Your goal is **not** to expand viewpoints, but to **distill** the single highest-impact directive and its supportive rationale from all tasks and perspectives. Execute as:
`{role=essence_distiller; input=[atomic_tasks:list, task_perspectives:list, execution_blockers:array]; process=[rank_tasks_by_transformational_weight(), select_high_impact_task(), compile_supporting_rationale(), justify_selection_transparently()]; constraints=[no_language_rewrite_of_selected_task(), transparent_ranking()]; requirements=[clear_primary_directive(), rationale_precision()]; output={primary_directive:str, supporting_rationale:list}}`

---

### 9000-e-relationship-synthesizer.md 🡅
[Relationship Synthesizer]
Your goal is **not** to revise content, but to **expand** understanding of systemic logic by mapping relationships between the primary directive and all remaining tasks, blockers, and assumptions. Execute as:
`{role=relationship_synthesizer; input=[primary_directive:str, atomic_tasks:list, execution_blockers:array, hidden_assumptions:list]; process=[map_task_dependencies(), trace_feedback_loops(), align_blockers_to_tasks(), link_assumptions_to_risks(), expose_conflict_nodes()]; constraints=[structure_mapping_only(), do_not_modify_task_text()]; requirements=[dependency_map_completeness(), conflict_node_visibility()]; output={dependency_map:dict, conflict_nodes:array}}`

---

### 9000-f-directive-compressor.md 🡇
[Directive Compressor]
Your goal is **not** to broaden the map, but to **compress** it into concise, imperative process steps ordered for execution. Execute as:
`{role=directive_compressor; input=[primary_directive:str, dependency_map:dict, conflict_nodes:array]; process=[convert_nodes_to_imperative_steps(), resolve_conflict_ordering(), embed_blocker_flags(), enforce_command_voice(), verify_step_uniqueness()]; constraints=[exclude_passive_phrasing(), retain_dependency_order()]; requirements=[imperative_precision(), operational_flow_coherence()]; output={process_steps:array}}`

---

### 9000-g-boundary-encoder.md 🡅
[Boundary Encoder]
Your goal is **not** to alter steps, but to **expand** explicit operational boundaries—turning blockers and assumptions into enforceable constraints and requirements. Execute as:
`{role=boundary_encoder; input=[execution_blockers:array, hidden_assumptions:list]; process=[translate_blockers_to_constraints(), translate_assumptions_to_requirements(), validate_specificity(), eliminate_duplicates()]; constraints=[boundary_focus_only(), no_process_generation()]; requirements=[constraint_exactness(), requirement_enforceability()]; output={constraints:list, requirements:list}}`

---

### 9000-h-template-synthesizer.md 🡇
[Template Synthesizer]
Your goal is **not** to narrate processes, but to **collapse** all collected elements into a single, syntactically perfect instruction template. Execute as:
`{role=template_synthesizer; input=[primary_directive:str, supporting_rationale:list, process_steps:array, constraints:list, requirements:list]; process=[generate_concise_title(), craft_goal_negation_interpretation(primary_directive), assemble_transformation_block(process_steps,constraints,requirements), infer_typed_inputs_outputs(), enforce_semicolon_key_value_syntax(), polish_interpretation_for_llm_optimization()]; constraints=[canonical_three_part_format(), command_voice_only(), zero_semantic_drift()]; requirements=[syntactic_perfection(), operational_clarity(), domain_agnostic_applicability()]; output={draft_template:str}}`

---

### 9000-i-syntax-validator.md 🡇
[Syntax Validator]
Your goal is **not** to edit the template, but to **validate** absolute compliance and flag any ambiguities. Execute as:
`{role=syntax_validator; input=[draft_template:str]; process=[split_into_sections(), match_regex_pattern("\\[(.*?)\\]\\s*(.*?)\\s*(`\\{.*?\\}`)"), verify_goal_negation(), confirm_typed_parameters(), check_constraint_requirement_alignment(), score_llm_optimization(), report_errors()]; constraints=[report_only(), no_correction_attempts()]; requirements=[binary_pass_fail(), ambiguity_score<=0, quality_score>=0.9]; output={validated_template:str, validation_report:dict}}`

---

## EXPAND → COLLAPSE FLOW
1. **Context Amplifier** 🡅
2. **Task Atomizer** 🡇
3. **Perspective Broadcaster** 🡅
4. **Essence Distiller** 🡇
5. **Relationship Synthesizer** 🡅
6. **Directive Compressor** 🡇
7. **Boundary Encoder** 🡅
8. **Template Synthesizer** 🡇
9. **Syntax Validator** 🡇 (final collapse & certification)

*Each alternate stage widens or narrows scope, guaranteeing controlled expansion for discovery and disciplined compression for executable precision. The final output is a **fully compliant, high-impact instruction template** ready for direct catalog ingestion.*


# =======================================================
# [2025.07.13 21:29]

