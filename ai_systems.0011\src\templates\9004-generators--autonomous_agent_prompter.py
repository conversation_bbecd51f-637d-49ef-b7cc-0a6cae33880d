#!/usr/bin/env python3

import sys
from pathlib import Path

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))
from processor import BaseGenerator

TEMPLATES = {
    # 9004:
    "9004-a-brief_normaliser": {
        "title": "Brief Normaliser",
        "interpretation": "Your goal is **not** to answer the brief, but to rephrase it into a clean, noise-free statement of facts. Execute as:",
        "transformation": "`{role=brief_normaliser; input=[raw_brief:str]; process=[strip_meta_language(), purge_metaphor(), keep_only_actionable_text(), detect_codebase_token(\"@codebase\"), preserve_original_order()]; constraints=[no_personal_commentary()]; requirements=[output_must_be_plaintext()], output={normalised_brief:str}}`",
        "context": {
            "description": "Dissects a raw specification to extract every piece of operational context an autonomous agent will need.",
            "input_focus": "Unedited specification text supplied by the user.",
            "output_focus": "A typed context_profile dict containing domain, project root, explicit objectives, and implicit requirements.",
            "key_operations": [
                "Removing metaphor and non‑operational language.",
                "Detecting the working domain or tech stack.",
                "Locating the root marker (`@codebase`) for path scoping.",
                "Listing every stated objective verbatim.",
                "Surfacing hidden assumptions and requirements."
            ],
            "constraints_context": [
                "May not paraphrase or interpret meaning beyond direct extraction.",
                "Absolutely forbidden from proposing solutions or tasks."
            ],
            "relevance": "Defines scope and boundaries for all downstream templates, guaranteeing that later steps operate on clean, actionable data."
        }
    },
    "9004-b-goal_distiller": {
        "title": "Goal Distiller",
        "interpretation": "Your goal is **not** to plan work, but to extract a single, explicit mission and its hard boundaries. Execute as:",
        "transformation": "`{role=goal_distiller; input=[normalised_brief:str]; process=[identify_primary_objective(), list_strict_constraints(), capture_success_criteria(), harvest_allowed_tools()], constraints=[exactly_one_primary_objective()], output={distilled_goal:dict(objective:str,constraints:list,success:list,tools:list)}}`",
        "context": {
            "description": "Transforms the context profile into a dependency‑ordered list of granular tasks fit for automated execution.",
            "input_focus": "The context_profile produced by the Context Extractor.",
            "output_focus": "ordered_tasks – a list of unique, verb‑driven commands each scoped to the codebase.",
            "key_operations": [
                "Splitting broad goals into single‑action commands.",
                "Adding `@codebase` prefixes so agents act in the correct directory.",
                "Sequencing tasks by logical dependency.",
                "Eliminating redundancy and cosmetic‑only instructions."
            ],
            "constraints_context": [
                "Every task must begin with a strong action verb (identify, refactor, log, etc.).",
                "Tasks must be non‑overlapping and directly tied to functional goals."
            ],
            "relevance": "Provides the autonomous agent with a clear, step‑by‑step execution blueprint free of ambiguity."
        }
    },
    "9004-c-task_architect": {
        "title": "Task Architect",
        "interpretation": "Your goal is **not** to write prose, but to decompose the objective into ordered, atomic tasks with built-in validation hooks. Execute as:",
        "transformation": "`{role=task_architect; input=[distilled_goal:dict]; process=[derive_atomic_tasks(), order_tasks_by_dependency(), pair_each_task_with_validation_method(), tag_required_artifacts()], requirements=[no_task_overlap(), every_task_has_validation()], output={task_list:list}}`",
        "context": {
            "description": "Consolidates every rule, boundary, and policy the agent must respect during execution.",
            "input_focus": "context_profile and ordered_tasks.",
            "output_focus": "constraint_set – a deduplicated list of textual constraints.",
            "key_operations": [
                "Harvesting functional‑equivalence mandates.",
                "Capturing API and interface preservation rules.",
                "Recording audit/logging, rollback, and policy obligations.",
                "Removing duplicate or conflicting constraints."
            ],
            "constraints_context": [
                "Must not create or modify tasks; only list constraints.",
                "Constraint entries must be unique and actionable."
            ],
            "relevance": "Prevents unintended side‑effects by explicitly defining the operational guard‑rails for the agent."
        }
    },
    "9004-d-prompt_composer": {
        "title": "Prompt Composer",
        "interpretation": "Your goal is **not** to validate, but to fuse role, objective, and tasks into one executable agent prompt. Execute as:",
        "transformation": "`{role=prompt_composer; input={distilled_goal:dict, task_list:list}; process=[build_system_section(role=\"Autonomous Coding Assistant\"), insert_goal_negation_intro(\"do not answer, perform tasks\"), embed_task_sequence(), state_global_constraints(), specify_output_format(\"markdown\"), include_codebase_hint(\"@codebase\")], requirements=[prompt_under_600_tokens()], output={assembled_prompt:str}}`",
        "context": {
            "description": "Integrates tasks and constraints into a single, executable prompt string for autonomous coding agents.",
            "input_focus": "ordered_tasks, constraint_set, and context_profile.",
            "output_focus": "agent_prompt – complete instructions with sections for role, tasks, constraints, and validation goals.",
            "key_operations": [
                "Starting with a goal‑negation header to lock scope.",
                "Embedding a precise agent role declaration.",
                "Listing tasks numerically for deterministic execution order.",
                "Appending constraints as a bullet list.",
                "Stating final validation targets (functional equivalence, audit pass)."
            ],
            "constraints_context": [
                "Entire prompt must be ≤ 300 tokens and written in English.",
                "No re‑phrasing of task semantics or constraint meaning."
            ],
            "relevance": "Delivers a turnkey command set that an autonomous agent can run without additional human clarification."
        }
    },
    "9004-e-compliance_validator": {
        "title": "Compliance Validator",
        "interpretation": "Your goal is **not** to edit content, but to certify the prompt meets every structural rule. Execute as:",
        "transformation": "`{role=compliance_validator; input=[assembled_prompt:str]; process=[confirm_goal_negation_presence(), verify_universal_syntax(regex=\"\\{.*?;\"), check_token_limit(<=600), scan_for_ambiguity(), ensure_validation_hooks_exist()], requirements=[all_checks_pass=True], output={validated_prompt:str, report:dict}}`",
        "context": {
            "description": "Acts as the final quality gate, ensuring the assembled prompt adheres to every structural and content rule.",
            "input_focus": "agent_prompt string from the Prompt Assembler.",
            "output_focus": "validated_prompt – the same string, confirmed compliant or flagged for correction.",
            "key_operations": [
                "Verifying presence and correctness of the goal‑negation header.",
                "Ensuring all mandatory section labels exist in order.",
                "Checking overall token length is within limits.",
                "Validating correct use of `@codebase` path prefixes.",
                "Ensuring tasks are numbered sequentially and constraints are present."
            ],
            "constraints_context": [
                "May not alter wording or meaning; only validate structural compliance."
            ],
            "relevance": "Guarantees that the autonomous agent receives a well‑formed, execution‑ready prompt, reducing runtime errors and mis‑scoping."
        }
    }
}

def main():
    """Main execution function."""
    generator = BaseGenerator(
        output_dir=Path(__file__).parent.parent / "generated"
    )
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
