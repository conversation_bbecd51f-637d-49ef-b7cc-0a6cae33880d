# YYYY.MM.DD-kl.HH.MM | DEPTH | SIZE(KB) | FILENAME | FILEHASH | CONTENT
''' 2025.07.12-kl.21.01 | lvl.1 | 01.kb | ''' - "1000-rephrasers--a_instruction_converter.py"           # | 'c050a5fa557d026b000000000000000000000000000000000000000000000000' | ''' #!/usr/bin/env python3\n\nimport sys\nfrom pathlib import Path\n\n\n# Add parent directory to path\nsys.path.append(str(Path(__file__).parent.parent))\nfrom processor import BaseGenerator\n\nTEMPLATES = {\n\n    # 1000: Instruction Converter/Prompt Enhancer\n    "1000-a-instruction_converter": {\n        "title": "Instruction Converter",\n        "interpretation": "Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:",\n        "transformation": "`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`",\n        # "context": {},\n    },\n\n}\n\n\ndef main():\n    """Main execution function."""\n    generator = BaseGenerator(\n        output_dir=Path(__file__).parent.parent / "generated"\n    )\n    generator.run(TEMPLATES)\n\nif __name__ == "__main__":\n    main() '''
''' 2025.07.12-kl.23.46 | lvl.1 | 02.kb | ''' - "1003-rephrasers--b_instruction_converter.py"           # | '30e7c04de7a14847000000000000000000000000000000000000000000000000' | ''' #!/usr/bin/env python3\n\nimport sys\nfrom pathlib import Path\n\n\n# Add parent directory to path\nsys.path.append(str(Path(__file__).parent.parent))\nfrom processor import BaseGenerator\n\nTEMPLATES = {\n\n    # 1003: Instruction Converter/Prompt Enhancer\n    "1003-a-instruction_converter": {\n        "title": "Instruction Converter",\n        "interpretation": "Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:",\n        "transformation": "`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format:str}; direction_vector=['Prompt accurately converted to imperative instruction set', 'Clarity and actionability accelerate user productivity', 'Feed the produced instruction set into the next transformation template' ]}`",\n        # "context": {},\n    },\n\n}\n\ndef main():\n    """Main execution function."""\n    gene... '''
''' 2025.07.16-kl.18.40 | lvl.1 | 02.kb | ''' - "1004-rephrasers--existential_quote_synthesizer.py"   # | 'cd1bd98b39d969c2000000000000000000000000000000000000000000000000' | ''' #!/usr/bin/env python3\n\nimport sys\nfrom pathlib import Path\n\n# Add parent directory to path\nsys.path.append(str(Path(__file__).parent.parent))\nfrom processor import BaseGenerator\n\nTEMPLATES = {\n\n    # 1004:\n    "1004-a-existential_quote_synthesizer": {\n        "title": "Existential Quote Synthesizer",\n        "interpretation": "Your goal is **not** to paraphrase or analyse; instead **convert** the raw statement into a single, unfiltered existential aphorism grounded in hard‑won insight — performing extraction, reframing, and polishing in one pass. Execute exactly as:",\n        "transformation": "`{role=existential_quote_synthesizer; input=[raw_statement:str]; process=[strip_formatting(), extract_semantic_core(), detect_causal_link(), identify_thematic_elements(), apply_existential_vocabulary(), infuse_authentic_gravity(), remove_filtering_language(), enforce_unfiltered_tone(), eliminate_first_person(), craft_single_sentence(), verify_atomicity(), ensure_existence_of_causal_link(), tighten_diction_for_resonance(), run_redundancy_check(), perform_final_meta_scrub()], constraints=[single_sentence(), no_meta_language(), no_first_person(), no_redundant_words(), preserve_causal_integrity()], requirements=[existential_resonance(), authenticity_signal(), publication_readiness()], output={final_quote:str}}`",\n        "context": {\n            "principles": {\n                "essence_preservation": "Retain the statement’s causal logic and thematic core.",\n         ... '''
''' 2025.07.16-kl.22.24 | lvl.1 | 02.kb | ''' - "1005-rephrasers--a_intensity_amplifier.py"             # | 'ee97d2a5e8e096a8000000000000000000000000000000000000000000000000' | ''' #!/usr/bin/env python3\n\nimport sys\nfrom pathlib import Path\n\n# Add parent directory to path\nsys.path.append(str(Path(__file__).parent.parent))\nfrom processor import BaseGenerator\n\nTEMPLATES = {\n\n    # 1005:\n    "1005-a-intensity_amplifier": {\n        "title": "Intensity Amplifier",\n        "interpretation": "Your goal is **not** to change task content, but to **modulate** its expressive force (tone, depth, criticality, etc.) to a specified level. Execute exactly as:",\n        "transformation": "`{role=intensity_amplifier; input=[base_instruction:str, amplification_profile:{dimension:str, level:int}]; process=[parse_dimension(), gauge_current_intensity(), compute_delta(level), modulate_language_by_delta(), enforce_boundary_constraints()], constraints=[preserve_core_directive(), no_semantic_drift(), level_range(1‑5)], requirements=[traceable_modulation(), tone_consistency()], output={amplified_instruction:str}}`",\n        "context": {\n            "principles": {\n                "orthogonal_modulation": "Adjust only the requested dimension—leave all other qualities untouched.",\n                "bounded_amplification": "Never exceed the requested level; maintain clarity and safety.",\n                "transparency": "Output must signal which dimension was modulated and by how much."\n            },\n            "success_criteria": {\n                "directive_intact": "Original task remains recognisable and executable.",\n                "measurable_shift":... '''
''' 2025.07.16-kl.23.20 | lvl.1 | 06.kb | ''' - "9018-rephrasers--intensity_enhancer.py"              # | 'e2980f7e8fed9c45000000000000000000000000000000000000000000000000' | ''' #!/usr/bin/env python3\n\nimport sys\nfrom pathlib import Path\n\n# Add parent directory to path\nsys.path.append(str(Path(__file__).parent.parent))\nfrom processor import BaseGenerator\n\nTEMPLATES = {\n  "9018-a-intensity_analyzer": {\n    "title": "Intensity Analyzer",\n    "interpretation": "Your goal is not to change the text, but to precisely measure the current level (1–5) of a specified tonal dimension in an input. Execute exactly as:",\n    "transformation": "`{role=intensity_analyzer; input=[instruction:str, dimension:str]; process=[1_validate_dimension(), 2_apply_rubric_to_instruction(), 3_assign_level_1_to_5(), 4_output_structured_analysis()], constraints=[dimension_in_whitelist(), no_modification_of_text()], requirements=[scalar_level_1_5(), scoring_notes_with_examples()], output={analysis:{dimension:str,level:int,scoring_notes:str}}}`",\n    "context": {\n      "dimension_whitelist": ["poetic_evocation", "persuasiveness", "formality", "analytical_rigor", "emotional_intensity", "urgency"],\n      "definitions": {\n        "poetic_evocation": "Density and vividness of metaphor, imagery, and non-literal language.",\n        "persuasiveness": "Strength and directness of argumentation and calls to action.",\n        "formality": "Complexity and detachment of diction and syntax.",\n        "analytical_rigor": "Degree of evidence, qualification, and depth of reasoning.",\n        "emotional_intensity": "Strength of affective tone and explicit emotional cues.",\n    ... '''
''' 2025.07.16-kl.23.45 | lvl.1 | 04.kb | ''' - "3004-rephrasers--existential_quote_synthesizer.py"   # | 'be7059456b72aeef000000000000000000000000000000000000000000000000' | ''' #!/usr/bin/env python3\n\nimport sys\nfrom pathlib import Path\n\n# Add parent directory to path\nsys.path.append(str(Path(__file__).parent.parent))\nfrom processor import BaseGenerator\n\nTEMPLATES = {\n\n    "3004-a-hard_critique": {\n        "title": "Hard Critique",\n        "interpretation": "Your goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:",\n        "transformation": "`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,2.0], flaw_analysis:str, alternative_enhancements:str[3]}}`",\n        # "context": {},\n    },\n    "... '''
''' 2025.07.18-kl.00.41 | lvl.1 | 04.kb | ''' - "2004-rephrasers--existential_quote_synthesizer.py"   # | '30e571b41e517287000000000000000000000000000000000000000000000000' | ''' #!/usr/bin/env python3\n\nimport sys\nfrom pathlib import Path\n\n# Add parent directory to path\nsys.path.append(str(Path(__file__).parent.parent))\nfrom processor import BaseGenerator\n\nTEMPLATES = {\n\n    "2004-a-problem_exploder": {\n        "title": "Problem Exploder",\n        "interpretation": "Your goal is **not** to paraphrase or regenerate from scratch; instead **refine** an existing existential aphorism by directly incorporating critique elements, addressing flaws, integrating alternatives, and enhancing depth to produce a superior version grounded in hard-won insight—performing flaw resolution, element fusion, and polishing in one pass. Execute exactly as:",\n        "transformation": "`{role=critique_informed_existential_refiner; input=[initial_prompt:str, previous_quote:str, flaw_analysis:str, alternative_enhancements:list[str]]; process=[parse_flaw_analysis_for_key_deficiencies(), categorize_flaws_by_type(ambiguity|specificity|immediacy|actionability|tone|coherence|impact), quantify_flaw_severity_from_analysis(), extract_actionable_insights_from_alternatives(), select_best_elements_from_alternatives(), map_alternatives_to_flaws_for_targeted_fixes(), restore_lost_nuances_from_initial_prompt(), amplify_philosophical_depth_to_max_level(), infuse_dialogical_subtlety_without_question_form(), enhance_specificity_with_vivid_metaphors(), ensure_actionable_translation_process(), reduce_ambiguity_below_threshold(), enforce_causal_link_integrity(), apply_existential_... '''
''' 2025.07.18-kl.01.05 | lvl.1 | 03.kb | ''' - "1006-rephrasers--poetic_transmuter.py"               # | 'c6a58802c667cc66000000000000000000000000000000000000000000000000' | ''' #!/usr/bin/env python3\n\nimport sys\nfrom pathlib import Path\n\n# Add parent directory to path\nsys.path.append(str(Path(__file__).parent.parent))\nfrom processor import BaseGenerator\n\nTEMPLATES = {\n\n    "1006-a-poetic_transmuter": {\n        "title": "Drastic Poetic Transmuter",\n        "interpretation": "Your goal is **not** to convert or analyze separately; instead **transmute** prose into drastically enhanced premium poetry by explosively decomposing contextual architecture, converting with sophisticated tail rhyme mechanics, and surgically enhancing for maximal resonance—integrating flaw-targeted refinements, layered emotional depth, rhythmic refinement, thematic clarity amplification, originality bolstering through cyclical exploration, universal authenticity, structural convergence, and nonessential elimination in one unified pass grounded in operational directives. Execute exactly as:",\n        "transformation": "`{role=drastic_poetic_transmuter; input=[prose_passage:str]; process=[detonate_contextual_space_for_essence_extraction(), enumerate_operational_directives_from_core_principles(), identify_thematic_core_and_emotional_tone_with_flaw_avoidance(), reconstruct_into_tail_rhyme_structure_with_brevity_and_layering(), apply_rhythmic_refinement_for_heightened_impact(), deepen_emotional_layers_through_metaphoric_amplification(), bolster_originality_via_poignant_cyclical_exploration(), ensure_universal_authenticity_and_relatable_suffering(), drive_structural_c... '''
''' 2025.07.21-kl.20.29 | lvl.1 | 02.kb | ''' - "1002-rephrasers--norwegian_english.py"               # | '6025bf2b2633416a000000000000000000000000000000000000000000000000' | ''' #!/usr/bin/env python3\n\nimport sys\nfrom pathlib import Path\n\n# Add parent directory to path\nsys.path.append(str(Path(__file__).parent.parent))\nfrom processor import BaseGenerator\n\nTEMPLATES = {\n\n    # 1002: English to Norwegian Translator\n    "1002-a-norwegian_english_translator": {\n        "title": "Norwegian English Translator",\n        "interpretation": "Your goal is not to **answer** the input prompt, but to **translate** it, and to do so as an authentic **Norwegian-to-English** translator. Your role is to go beyond literal conversion by sincerely interpreting the cultural depth, emotional tone, and personality of the Norwegian source. Make certain the English to Norwegian translation flows naturally, preserves the source's intent, and enhances nuanced expression. Execute as:",\n        "transformation": "`{role=norwegian_english_translator; input=[norwegian_text:str]; process=[analyze_norwegian_cultural_context(), preserve_emotional_undertones(), translate_to_natural_english(), maintain_authentic_voice(), enhance_clarity_for_english_speakers()]; constraints=[retain_original_meaning(), preserve_cultural_nuances(), maintain_natural_flow()]; requirements=[fluent_english_output(), preserved_norwegian_essence(), culturally_appropriate_translation()]; output={english_translation:str}}`",\n        # "context": {},\n    },\n\n}\n\ndef main():\n    """Main execution function."""\n    generator = BaseGenerator(\n        #generator_range=(1002, 1099),\n        outp... '''
''' 2025.07.21-kl.20.31 | lvl.1 | 02.kb | ''' - "1001-rephrasers--english_norwegian.py"               # | 'e1b1e53871489dac000000000000000000000000000000000000000000000000' | ''' #!/usr/bin/env python3\n\nimport sys\nfrom pathlib import Path\n\n# Add parent directory to path\nsys.path.append(str(Path(__file__).parent.parent))\nfrom processor import BaseGenerator\n\nTEMPLATES = {\n\n    # 1001: English to Norwegian Translator\n    "1001-a-english_norwegian_translator": {\n        "title": "English Norwegian Translator",\n        "interpretation": "Your goal is not to **answer** the input prompt, but to **translate** it, and to do so as an authentic **English-to-Norwegian** translator. Your role is to go beyond literal conversion by sincerely interpreting the cultural depth, emotional tone, and personality of the English source. Make certain the English to Norwegian translation flows naturally, preserves the source's intent, and enhances nuanced expression. Execute as:",\n        "transformation": "`{role=english_norwegian_translator; input=[english_text:str]; process=[analyze_english_cultural_context(), identify_norwegian_cultural_equivalents(), translate_to_natural_norwegian(), preserve_emotional_undertones(), adapt_for_norwegian_speakers()]; constraints=[retain_original_meaning(), maintain_natural_norwegian_flow(), preserve_cultural_authenticity()]; requirements=[fluent_norwegian_output(), preserved_english_essence(), culturally_appropriate_translation()]; output={norwegian_translation:str}}`",\n        # "context": {},\n    },\n\n}\n\ndef main():\n    """Main execution function."""\n    generator = BaseGenerator(\n        output_dir=Path(__file__... '''
''' 2025.07.25-kl.09.05 | lvl.1 | 02.kb | ''' - "1007-rephrasers--instructions_combiner.py"           # | 'dcba4315b8909746000000000000000000000000000000000000000000000000' | ''' #!/usr/bin/env python3\n\nimport sys\nfrom pathlib import Path\n\n# Add parent directory to path\nsys.path.append(str(Path(__file__).parent.parent))\nfrom processor import BaseGenerator\n\nTEMPLATES = {\n\n    "1007-a-instruction_combiner": {\n        "title": "Synergic Instruction Architect",\n        "interpretation": " Your goal is not to **merge** two separate instructions, but to **synthesize** them into a single unified directive where each component inherently resonates with and amplifies the other in bidirectional enhancement. Execute as:",\n        "transformation": "`{role=synergic_instruction_architect; input=[instruction_a:template, instruction_b:template]; process=[extract_core_transformation_essence_from_both(), identify_complementary_operational_vectors(), map_bidirectional_resonance_patterns(a_enhances_b, b_enhances_a), synthesize_unified_role_identity(), merge_process_functions_synergically(), consolidate_constraints_for_maximum_coherence(), unify_requirements_into_singular_vision(), create_amplified_output_specification()]; constraints=[maintain_both_core_philosophies_simultaneously(), ensure_bidirectional_enhancement_flow(), eliminate_conflicting_directives(), preserve_maximum_operational_power_from_both()]; requirements=[synergic_resonance_achievement(), unified_transformation_logic(), amplified_combined_effectiveness(), seamless_philosophical_integration()]; output={unified_synergic_instruction:template}}`",\n        # "context": {}\n    },\n}\n\ndef m... '''


''' 2025.07.12-kl.21.01 | lvl.1 | 01.kb | ''' - "1200-extractors--intent_extractor.py"                # | '67a91d07dbd1b44f000000000000000000000000000000000000000000000000' | ''' #!/usr/bin/env python3\n\nimport sys\nfrom pathlib import Path\n\n\n# Add parent directory to path\nsys.path.append(str(Path(__file__).parent.parent))\nfrom processor import BaseGenerator\n\nTEMPLATES = {\n\n    # 1200: Intent Extractor\n    "1200-a-intent_extractor": {\n        "title": "Intent Extractor",\n        "interpretation": "Your goal is not to **interpret** the surface content, but to **extract** the fundamental operational intent beneath all domain-specific language. Execute as:",\n        "transformation": "`{role=intent_extraction_engine; input=[prompt:str]; process=[strip_domain_specifics(), identify_core_operational_pattern(), extract_transformation_intent(), isolate_functional_essence(), map_to_universal_operations()]; constraints=[ignore_surface_terminology(), focus_on_underlying_mechanics(), preserve_operational_logic()]; requirements=[domain_agnostic_intent(), universal_operational_pattern(), archetypal_transformation_core()]; output={extracted_intent:str}}`",\n        # "context": {},\n    },\n}\n\n\ndef main():\n    """Main execution function."""\n    generator = BaseGenerator(\n        #generator_range=(1200, 1199),\n        output_dir=Path(__file__).parent.parent / "generated"\n    )\n    generator.run(TEMPLATES)\n\nif __name__ == "__main__":\n    main() '''
''' 2025.07.12-kl.21.01 | lvl.1 | 02.kb | ''' - "0001-contextualizers--my_coding_principles.py"       # | '8980e02f97226a92000000000000000000000000000000000000000000000000' | ''' #!/usr/bin/env python3\n\nimport sys\nfrom pathlib import Path\n\n# Add parent directory to path\nsys.path.append(str(Path(__file__).parent.parent))\nfrom processor import BaseGenerator\n\nTEMPLATES = {\n\n    # 0001: Contextual Expander for Personal Coding Principles\n    "0001-a-my_coding_principles": {\n        "title": "Contextual Expander and Explosive Decomposer",\n        "interpretation": "Your goal is not to **interpret** the input, but to **extract** its functional essence by detonating it into constituent elements and converting them into actionable operational directives. Execute as:",\n        "transformation": "`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`",\n        # "context": {\n        #     "core_principles": {\n        #         "non_negotiable_ideals": [\n        #             "Clarity: Code communicates intent without explanatory prose.",\n        #             "Simplicity: Small, elegant solutions beat complex, sprawling ones.",\n        #             "Integrity: Never degrade existing behaviour, structure, or tests.",\n        #             "Maintainability: Optimise for future comprehension and evolution.",\n        #             "High-Leverage Impact: Prefer changes that yield maximum benefit with minimal disruption."... '''
''' 2025.07.12-kl.21.01 | lvl.1 | 02.kb | ''' - "1101-expanders--contextual_expander.py"              # | 'd28b849bfd3f3972000000000000000000000000000000000000000000000000' | ''' #!/usr/bin/env python3\n\nimport sys\nfrom pathlib import Path\n\n# Add parent directory to path\nsys.path.append(str(Path(__file__).parent.parent))\nfrom processor import BaseGenerator\n\nTEMPLATES = {\n\n    # 1101: Contextual Expander and Explosive Decomposer\n    "1101-a-contextual_expander": {\n        "title": "Contextual Expander and Explosive Decomposer",\n        "interpretation": "Your goal is not to **interpret** the input, but to **explode** it into complete contextual expansion while simultaneously **extracting** its functional essence and **converting** it into immediately actionable operational directives. Execute as:",\n        "transformation": "`{role=contextual_explosive_decomposer; input=[raw_input:str]; process=[detonate_complete_contextual_space(), identify_explicit_operational_asks(), surface_hidden_assumptions_and_dependencies(), enumerate_actionable_sub_goals(), extract_procedural_constraints_and_blockers(), convert_declaratives_to_operational_imperatives(), strip_first_person_references_and_contextual_noise(), preserve_functional_core_and_sequential_flow(), reveal_execution_dependencies_and_blockers(), transform_statements_to_directive_commands(), maintain_technical_terminology_and_domain_specificity(), synthesize_unified_instruction_and_goal_map()]; constraints=[no_solutions_or_interpretive_responses(), pure_decomposition_and_conversion_only(), deliver_clear_actionable_commands(), preserve_original_functional_sequence(), maintain_domain_agnostic_... '''
''' 2025.07.12-kl.21.20 | lvl.1 | 03.kb | ''' - "1201-extractors--input_classifier.py"                # | 'a7bd888e2a8c8304000000000000000000000000000000000000000000000000' | ''' #!/usr/bin/env python3\n\nimport sys\nfrom pathlib import Path\n\n# Add parent directory to path\nsys.path.append(str(Path(__file__).parent.parent))\nfrom processor import BaseGenerator\n\nTEMPLATES = {\n\n    # 1201:\n    "1201-a-input_classifier": {\n        "title": "Input Classifier",\n        # "interpretation": "Your goal is not to **interpret** the content or meaning, but to **identify** what type of thing it fundamentally **IS** - its basic form, nature, or category in direct, unambiguous terms. Execute as universal input-classifier:",\n        # "transformation": "`{role=universal_interface_insight_generator_and_input_classifier, input=[objective_context:any], process=[explode_input_for_hidden_assumptions(), map_problem_structure_to_existing_interfaces(), scan_forlatent_interconnections(), identify_shortest_path_to_solution_using_external_frameworks(), select_extremely_simple_methodologies(), synthesize_cross-domain_leverage_options(), amplify_lazy-yet-powerful_shortcuts(), enforce_minimalism_and_maximal_effect(), validate_solution_triviality_and_interface_reuse()], constraints=[do_not_directly_answer_or_execute, avoid_standard_workflows, sidestep_reinvention, prioritize_interface_and_framework_leverage, exclude_generic_problem-solving_language, focus_on_structure_and latent_pathways], requirements=[reveal_non-obvious_structural_links, list_external_interfaces_or_libraries_as_part_of_suggestion, explain_why_chosen_shortcut_removes_complexity, demonstrate solution ... '''
''' 2025.07.12-kl.23.14 | lvl.1 | 06.kb | ''' - "0000-testing--reserved_for_testing.py"               # | 'fbaaad00aaf00227000000000000000000000000000000000000000000000000' | ''' #!/usr/bin/env python3\n\nimport sys\nfrom pathlib import Path\n\n# Add parent directory to path\nsys.path.append(str(Path(__file__).parent.parent))\nfrom processor import BaseGenerator\n\nTEMPLATES = {\n\n    # 0000: Will change and vary as we go\n    # "0000-a-reserved_for_testing": {\n    #     "title": "Function Extractor",\n    #     "interpretation": "Your goal is not to **interpret** the input, but to **extract** its functional essence by detonating it into constituent elements and converting them into actionable operational directives. Execute as:",\n    #     "transformation": "`{role=function_extractor; input=[raw_input:str]; process=[detonate_into_functional_components(), identify_explicit_operational_asks(), surface_hidden_functional_dependencies(), enumerate_actionable_sub_goals(), extract_procedural_constraints(), convert_declaratives_to_operational_imperatives(), strip_contextual_noise(), preserve_functional_core(), reveal_execution_blockers(), synthesize_actionable_instruction_map()]; constraints=[no_interpretive_solutions(), pure_functional_extraction_only(), deliver_clear_operational_commands(), preserve_functional_sequence(), maintain_domain_agnostic_applicability()]; requirements=[complete_functional_decomposition(), actionable_directive_conversion(), remove_subjective_references(), preserve_operational_accuracy(), maintain_functional_intent()]; output={functional_instruction_map:dict, operational_directives:array, execution_dependencies:array}}`",\n   ... '''
''' 2025.07.12-kl.23.31 | lvl.1 | 01.kb | ''' - "1100-expanders--problem_exploder.py"                 # | '245efc98f05ef4d2000000000000000000000000000000000000000000000000' | ''' #!/usr/bin/env python3\n\nimport sys\nfrom pathlib import Path\n\n\n# Add parent directory to path\nsys.path.append(str(Path(__file__).parent.parent))\nfrom processor import BaseGenerator\n\nTEMPLATES = {\n\n    # 1100: Explosive Decomposition of Problem Statements\n    "1100-a-problem_exploder": {\n        "title": "Problem Exploder",\n        "interpretation": "Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:",\n        "transformation": "`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`",\n        # "context": {},\n    },\n\n}\n\n\n\ndef main():\n    """Main execution function."""\n    generator = BaseGenerator(\n        #generator_range=(1100, 1199),\n        output_dir=Path(__file__).parent.parent / "generated"\n    )\n    generator.run(TEMPLATES)\n\nif __name__ == "__main__":\n    main() '''
''' 2025.07.13-kl.00.07 | lvl.1 | 06.kb | ''' - "1102-expanders--explosive_decomposer.py"             # | 'd8131f0b4c6b1370000000000000000000000000000000000000000000000000' | ''' #!/usr/bin/env python3\n\nimport sys\nfrom pathlib import Path\n\n\n# Add parent directory to path\nsys.path.append(str(Path(__file__).parent.parent))\nfrom processor import BaseGenerator\n\nTEMPLATES = {\n\n    # 1102: Contextual Expander Explosive Decomposer\n    "1102-a-explosive_decomposer": {\n        "title": "Problem Exploder",\n        "interpretation": "Your goal is **not** to answer, solve, or implement the user’s request, but to **expand its context** and **detonate** it into every explicit task, latent dependency, and functional directive—then compile a precise, domain‑agnostic execution specification. Execute as a unified context‑amplification+problem‑decomposition engine:",\n        "transformation": "`{role=contextual_expander_explosive_decomposer; input=[raw_input:any]; tags={task='contextual_expand_and_decompose'; action='produce_context_snapshot_goal_map_instruction_map'; style='concise'; format='markdown'}; process=[strip_first_person_references(), capture_domain_and_stakeholder_context(), amplify_relevant_background(), identify_explicit_requests(), surface_hidden_assumptions(), enumerate_sub_goals(), detect_execution_blockers(), detonate_into_functional_components(), extract_procedural_constraints(), convert_declaratives_to_operational_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), synthesize_context_snapshot(), construct_goal_map(), compile_functional_instruction_map(), list_execution_depende... '''
''' 2025.07.13-kl.00.28 | lvl.1 | 01.kb | ''' - "1203-extractors--core_essence.py"                    # | '885eed55124231e5000000000000000000000000000000000000000000000000' | ''' #!/usr/bin/env python3\n\nimport sys\nfrom pathlib import Path\n\n# Add parent directory to path\nsys.path.append(str(Path(__file__).parent.parent))\nfrom processor import BaseGenerator\n\nTEMPLATES = {\n\n    # 1203: Core Essence Extractor\n    "1203-a-core_essence_extractor": {\n        "title": "Core Essence Extractor",\n        "interpretation": "Your goal is not to **analyze** input content, but to **extract** its fundamental transformation essence and operational imperatives. Execute as:",\n        "transformation": "`{role=essence_extractor; input=[raw_input:any]; process=[identify_core_transformation_intent(), extract_operational_imperatives(), isolate_value_drivers(), map_essential_elements()]; constraints=[focus_essence_only(), eliminate_surface_content()]; requirements=[maximum_essence_concentration(), transformation_clarity()]; output={core_essence:dict, transformation_imperatives:array}}`",\n        # "context": {},\n    },\n}\n\ndef main():\n    """Main execution function."""\n    generator = BaseGenerator(\n        output_dir=Path(__file__).parent.parent / "generated"\n    )\n    generator.run(TEMPLATES)\n\nif __name__ == "__main__":\n    main() '''
''' 2025.07.13-kl.00.35 | lvl.1 | 02.kb | ''' - "1900-critics--hard_critique.py"                      # | 'd35cfa207f0849db000000000000000000000000000000000000000000000000' | ''' #!/usr/bin/env python3\n\nimport sys\nfrom pathlib import Path\n\n# Add parent directory to path\nsys.path.append(str(Path(__file__).parent.parent))\nfrom processor import BaseGenerator\n\nTEMPLATES = {\n\n    # 1900: Ruthless Critique\n    "1900-a-hard_critique": {\n        "title": "Hard Critique",\n        "interpretation": "Your goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:",\n        "transformation": "`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,2.0], flaw_analysis:str, alternative_enhancements:str[3]}}`",\n        ... '''
''' 2025.07.13-kl.00.40 | lvl.1 | 10.kb | ''' - "9000-generators--universal_instruction_generator.py" # | '136cbb08c939d22a000000000000000000000000000000000000000000000000' | ''' #!/usr/bin/env python3\n\nimport sys\nfrom pathlib import Path\n\n# Add parent directory to path\nsys.path.append(str(Path(__file__).parent.parent))\nfrom processor import BaseGenerator\n\nTEMPLATES = {\n\n    # 9000:\n    "9000-a-context_amplifier": {\n        "title": "Context Amplifier",\n        "interpretation": "Your goal is not to **analyze** the input text, but to **decompose** it into a sequential list of distinct semantic segments. Execute as:",\n        "transformation": "`{role=context_amplifier; input=[raw_input:any]; process=[strip_first_person_references(), broaden_domain_backdrop(), list_explicit_requests(), surface_hidden_assumptions(), capture_domain_signals(), preserve_original_sequence()]; constraints=[no_solution_generation(), domain_agnostic_language_only()]; requirements=[contextual_layer_completeness(), assumption_visibility()]; output={amplified_context:str, explicit_requests:list, hidden_assumptions:list, domain_signals:array}}`"\n    },\n    "9000-b-task_atomizer": {\n        "title": "Task Atomizer",\n        "interpretation": "Your goal is not to **praise** or **answer** the submission, but to **interrogate and redirect** it—exposing every structural flaw, quantifying compliance with the Universal Directive System, and issuing a minimal replacement instruction that corrects all defects. Execute as:",\n        "transformation": "`{role=task_atomizer; input=[amplified_context:str]; process=[segment_into_atomic_tasks(), detect_execution_blockers()... '''
''' 2025.07.13-kl.10.32 | lvl.1 | 01.kb | ''' - "1202-extractors--trajectory_director.py"             # | 'd50d2186412abc66000000000000000000000000000000000000000000000000' | ''' #!/usr/bin/env python3\n\nimport sys\nfrom pathlib import Path\n\n# Add parent directory to path\nsys.path.append(str(Path(__file__).parent.parent))\nfrom processor import BaseGenerator\n\nTEMPLATES = {\n\n    # 1202: Trajectory Director\n    "1202-a-trajectory_director": {\n        "title": "Trajectory Director",\n        "interpretation": "Your goal is not to **process** the input in a vacuum, but to **steer** it—deriving the highest-value constructive trajectory and encoding that path as the very next set of instructions. Execute as trajectory-setting directive generator: ",\n        "transformation": "`{role=trajectory_director; input=[raw_prompt:any]; process=[extract_core_objective(), infer_desired_end_state(constructive_bias=True), map_high_value_intermediate_targets(), synthesize_directional_commands(), embed_self-improving_guidelines(), validate_alignment_with_constructive_principles()]; constraints=[mandatory_constructive_vector(), preserve_original_contextual_integrity(), forbid_nil_direction_outputs(), zero_conversational_fillers()]; requirements=[explicit_next-step_directives(), trajectory_map:list, self_reinforcing_instruction_pattern()], output={directed_prompt:str, trajectory_outline:list}}`",\n        # "context": {},\n    },\n}\n\n\ndef main():\n    """Main execution function."""\n    generator = BaseGenerator(\n        output_dir=Path(__file__).parent.parent / "generated"\n    )\n    generator.run(TEMPLATES)\n\nif __name__ == "__main__":\n    main() '''
''' 2025.07.13-kl.13.19 | lvl.1 | 02.kb | ''' - "9001-generators--bursdag.py"                         # | '0d783f4629aa4bca000000000000000000000000000000000000000000000000' | ''' #!/usr/bin/env python3\n\nimport sys\nfrom pathlib import Path\n\n# Add parent directory to path\nsys.path.append(str(Path(__file__).parent.parent))\nfrom processor import BaseGenerator\n\nTEMPLATES = {\n\n    # 9001:\n    "9001-a-morsom_bursdagshilsen": {\n        "title": "Context Extractor",\n        "interpretation": "Ditt mål er ikke å **skrive** hilsen, men å **hente** personlige fakta. Execute as: ",\n        "transformation": "`{role=context_extractor; input=[raw_text:str]; process=[identify_recipient(), detect_relationship(), extract_age_or_milestone(), list_unique_traits(), capture_shared_memories()], constraints=[ingen_omtolkning(), ingen_humor()], output={facts:{name:str|None, relation:str|None, age:str|None, traits:list, memories:list}}}`"\n    },\n    "9001-b-morsom_bursdagshilsen": {\n        "title": "Personality Amplifier",\n        "interpretation": "Ditt mål er ikke å **endre** fakta, men å **farge** dem med mottakerens unike personlighet. Execute as: ",\n        "transformation": "`{role=personality_amplifier; input=[facts:dict]; process=[select_signature_trait(), weave_inside_joke(), preserve_affection_level()], constraints=[ingen_generiske_floskler()], requirements=[tone=varm_og_leken], output={flair:str}}`",\n    },\n    "9001-c-morsom_bursdagshilsen": {\n        "title": "Humor Synthesizer",\n        "interpretation": "Ditt mål er ikke å **liste** informasjon, men å **lansere punchline**. Execute as: ",\n        "transformation": "`{role=humor_synth... '''
''' 2025.07.13-kl.13.21 | lvl.1 | 03.kb | ''' - "9002-generators--bursdag.py"                         # | 'a74b7429aec3147e000000000000000000000000000000000000000000000000' | ''' #!/usr/bin/env python3\n\nimport sys\nfrom pathlib import Path\n\n# Add parent directory to path\nsys.path.append(str(Path(__file__).parent.parent))\nfrom processor import BaseGenerator\n\nTEMPLATES = {\n\n    # 9002:\n    "9002-a-morsom_bursdagshilsen": {\n        "title": "Essens‑Kartlegger",\n        "interpretation": "Ditt mål er **ikke** å svare teksten, men å kartlegge bursdagsfakta + personlige særtrekk. Execute as: ",\n        "transformation": "`{role=essence_mapper; input=[content:any]; process=[lokaliser_feiringsdato_eller_alder(), hent_navn_og_relasjon(), fang_personlige_quirks(), noter_klengete_kjælenavn(), fjern_unødvendig_metaforikk()]; constraints=[ingen_humor_enda()]; output={profil:dict(navn:str, alder:int?, relasjon:str?, quirks:list, kjælenavn:str?)}}`"\n    },\n    "9002-b-morsom_bursdagshilsen": {\n        "title": "Humor‑Edge‑Designer",\n        "interpretation": "Ditt mål er **ikke** å gjengi profilen, men å skape en subtil, skarp humorvinkel. Execute as: ",\n        "transformation": "`{role=humor_edge_designer; input=[profil:dict]; process=[generer_spiss_ordspill(profil), velg_ertende_vinkel(balanser_varme=True), planlegg_overraskelses_punchline(), velg_passende_emoji(erter+feiring)]; constraints=[unngå_sårende_personangrep(), bevare_kjærlig_tone()]; output={humor_edge:dict(ordspill:str, teasing:str, punch:str, emoji:str)}}`",\n    },\n    "9002-c-morsom_bursdagshilsen": {\n        "title": "Personlig‑Tone‑Mixer",\n        "interpretation": "Ditt ... '''
''' 2025.07.13-kl.23.56 | lvl.1 | 08.kb | ''' - "9004-generators--autonomous_agent_prompter.py"       # | '9b840cb16beba32c000000000000000000000000000000000000000000000000' | ''' #!/usr/bin/env python3\n\nimport sys\nfrom pathlib import Path\n\n# Add parent directory to path\nsys.path.append(str(Path(__file__).parent.parent))\nfrom processor import BaseGenerator\n\nTEMPLATES = {\n    # 9004:\n    "9004-a-brief_normaliser": {\n        "title": "Brief Normaliser",\n        "interpretation": "Your goal is **not** to answer the brief, but to rephrase it into a clean, noise-free statement of facts. Execute as:",\n        "transformation": "`{role=brief_normaliser; input=[raw_brief:str]; process=[strip_meta_language(), purge_metaphor(), keep_only_actionable_text(), detect_codebase_token(\\"@codebase\\"), preserve_original_order()]; constraints=[no_personal_commentary()]; requirements=[output_must_be_plaintext()], output={normalised_brief:str}}`",\n        "context": {\n            "description": "Dissects a raw specification to extract every piece of operational context an autonomous agent will need.",\n            "input_focus": "Unedited specification text supplied by the user.",\n            "output_focus": "A typed context_profile dict containing domain, project root, explicit objectives, and implicit requirements.",\n            "key_operations": [\n                "Removing metaphor and non‑operational language.",\n                "Detecting the working domain or tech stack.",\n                "Locating the root marker (`@codebase`) for path scoping.",\n                "Listing every stated objective verbatim.",\n                "Surfacing hidden a... '''
''' 2025.07.14-kl.01.09 | lvl.1 | 03.kb | ''' - "9005-generators--prompt_combiner.py.001"             # | '83d8c9af0508aadd000000000000000000000000000000000000000000000000' | ''' #!/usr/bin/env python3\n\nimport sys\nfrom pathlib import Path\n\n# Add parent directory to path\nsys.path.append(str(Path(__file__).parent.parent))\nfrom processor import BaseGenerator\n\nTEMPLATES = {\n\n    # 9005:\n    "9005-a-capability_extractor": {\n        "title": "Capability Extractor",\n        "interpretation": "Your goal is not to **describe** the two instruction templates, but to **extract** their core operational capabilities and transformation mechanisms. Execute as:",\n        "transformation": "`{role=capability_extractor; input=[instruction_template_a:str, instruction_template_b:str]; process=[extract_transformation_verbs(), identify_core_processes(), isolate_unique_constraints(), map_output_specifications(), distill_operational_essence()]; constraints=[focus_on_capabilities_only(), ignore_template_structure(), preserve_transformation_power()]; requirements=[capability_isolation(), operational_clarity(), transformation_preservation()]; output={capability_a:dict, capability_b:dict, synergy_points:array}}`",\n    },\n\n    "9005-b-synthesis_architect": {\n        "title": "Synthesis Architect",\n        "interpretation": "Your goal is not to **merge** the extracted capabilities, but to **architect** a new unified instruction that combines both operational powers into a single transformative directive. Execute as:",\n        "transformation": "`{role=synthesis_architect; input=[capability_a:dict, capability_b:dict, synergy_points:array]; process=[design_uni... '''
''' 2025.07.14-kl.01.15 | lvl.1 | 03.kb | ''' - "9005-generators--prompt_combiner.py"                 # | '6e7d1ca388923870000000000000000000000000000000000000000000000000' | ''' #!/usr/bin/env python3\n\nimport sys\nfrom pathlib import Path\n\n# Add parent directory to path\nsys.path.append(str(Path(__file__).parent.parent))\nfrom processor import BaseGenerator\n\nTEMPLATES = {\n\n    # 9005: Precision Instruction Synthesis - Rigor Preservation\n    "9005-a-operational_mapper": {\n        "title": "Operational Mapper",\n        "interpretation": "Your goal is not to **summarize** the instruction templates, but to **map** their precise operational sequences while identifying exact integration points that preserve full procedural rigor. Execute as:",\n        "transformation": "`{role=operational_mapper; input=[instruction_template_a:str, instruction_template_b:str]; process=[extract_exact_process_sequences(), map_constraint_hierarchies(), identify_requirement_dependencies(), locate_output_handoff_points(), catalog_procedural_boundaries()]; constraints=[preserve_every_operational_detail(), maintain_constraint_atomicity(), respect_procedural_boundaries()]; requirements=[complete_process_mapping(), constraint_preservation(), boundary_clarity()]; output={process_map_a:dict, process_map_b:dict, integration_points:array, boundary_conflicts:array}}`",\n    },\n\n    "9005-b-sequential_architect": {\n        "title": "Sequential Architect",\n        "interpretation": "Your goal is not to **blend** the operational maps, but to **architect** a precise sequential flow that executes both complete processes while maintaining their individual constraint systems ... '''
''' 2025.07.14-kl.17.21 | lvl.1 | 03.kb | ''' - "9006-generators--blackwhite_coloring.py"             # | '3712bea5037aa665000000000000000000000000000000000000000000000000' | ''' #!/usr/bin/env python3\n\nimport sys\nfrom pathlib import Path\n\n# Add parent directory to path\nsys.path.append(str(Path(__file__).parent.parent))\nfrom processor import BaseGenerator\n\nTEMPLATES = {\n\n    # 9006:\n    "9006-a-coloring_page_generator": {\n        "title": "Coloring Page Prompt Generator",\n        "interpretation": "Your goal is **not** to illustrate directly, but to **generate** a fully-specified, black-and-white coloring-page prompt for an autonomous art agent. Execute as:",\n        "transformation": "`{role=context_amplifier; input=[raw_input:any]; process=[strip_first_person_references(), broaden_domain_backdrop(), list_explicit_requests(), surface_hidden_assumptions(), capture_domain_signals(), preserve_original_sequence()]; constraints=[no_solution_generation(), domain_agnostic_language_only()]; requirements=[contextual_layer_completeness(), assumption_visibility()]; output={amplified_context:str, explicit_requests:list, hidden_assumptions:list, domain_signals:array}}`",\n        "context": {\n          "description": "Produces a concise, agent-ready prompt instructing an illustration model to create kid-friendly, psychedelic Christmas coloring pages with clean vector outlines.",\n          "input_focus": "A raw creative idea or concept the user wants illustrated.",\n          "output_focus": "A single, well-structured prompt string containing role, style, subject, and strict line/format constraints.",\n          "key_operations": [\n           ... '''
''' 2025.07.14-kl.22.58 | lvl.1 | 04.kb | ''' - "9007-generators--tail_rhymes.py"                     # | '6cbbfb03f1e98c60000000000000000000000000000000000000000000000000' | ''' #!/usr/bin/env python3\n\nimport sys\nfrom pathlib import Path\n\n# Add parent directory to path\nsys.path.append(str(Path(__file__).parent.parent))\nfrom processor import BaseGenerator\n\nTEMPLATES = {\n\n    # 9007:\n    "9007-a-contextual_explosive_decomposer": {\n        "title": "Coloring Page Prompt Generator",\n        "interpretation": "Your goal is not to **interpret** the prose, but to **explode** it into complete contextual space while extracting its functional essence and operational directives. Execute as:",\n        "transformation": "`{role=contextual_explosive_decomposer; input=[prose_passage:str]; process=[detonate_complete_contextual_space(), identify_explicit_operational_asks(), surface_hidden_assumptions_and_dependencies(), enumerate_actionable_sub_goals(), extract_procedural_constraints_and_blockers(), convert_declaratives_to_operational_imperatives(), strip_first_person_references_and_contextual_noise(), preserve_functional_core_and_sequential_flow(), reveal_execution_dependencies_and_blockers(), transform_statements_to_directive_commands(), maintain_technical_terminology_and_domain_specificity(), synthesize_unified_instruction_and_goal_map()]; constraints=[no_solutions_or_interpretive_responses(), pure_decomposition_and_conversion_only(), deliver_clear_actionable_commands(), preserve_original_functional_sequence(), maintain_domain_agnostic_applicability(), eliminate_narrative_commentary()]; requirements=[complete_contextual_goal_decomposition(), acti... '''
''' 2025.07.15-kl.15.03 | lvl.1 | 08.kb | ''' - "9008-generators--prose_to_poetry.py"                 # | '979008dc77ea9630000000000000000000000000000000000000000000000000' | ''' #!/usr/bin/env python3\n\nimport sys\nfrom pathlib import Path\n\n# Add parent directory to path\nsys.path.append(str(Path(__file__).parent.parent))\nfrom processor import BaseGenerator\n\nTEMPLATES = {\n\n    # 9008:\n    "9008-a-contextual_explosive_decomposer": {\n        "title": "Contextual Explosive Decomposer",\n        "interpretation": "Your goal is not to **interpret** the prose, but to **explode** it into complete contextual space while extracting its functional essence and operational directives. Systematically detonate and expand its contextual and technical architecture, extracting every compositional pillar and operational directive—especially those concerning premium tail rhyme, brevity, resonance, layering, and structural convergence. Surface all constraints and operationalize all requirements as actionable instructions for downstream poetic engineering. Execute as:",\n        "transformation": "`{role=contextual_explosive_decomposer; input=[prose_passage:str]; process=[detonate_complete_contextual_space(), identify_explicit_operational_asks(), surface_hidden_assumptions_and_dependencies(), enumerate_actionable_sub_goals(), extract_procedural_constraints_and_blockers(), convert_declaratives_to_operational_imperatives(), strip_first_person_references_and_contextual_noise(), preserve_functional_core_and_sequential_flow(), reveal_execution_dependencies_and_blockers(), transform_statements_to_directive_commands(), maintain_technical_terminology_and_domain_spec... '''
''' 2025.07.15-kl.15.36 | lvl.1 | 25.kb | ''' - "9009-generators--prose_to_poetry2.py"                # | '2f7c7430b46df04e000000000000000000000000000000000000000000000000' | ''' #!/usr/bin/env python3\n\nimport sys\nfrom pathlib import Path\n\n# Add parent directory to path\nsys.path.append(str(Path(__file__).parent.parent))\nfrom processor import BaseGenerator\n\nTEMPLATES = {\n    "9009-a-contextual_explosive_decomposer": {\n        "title": "Contextual Explosive Decomposer",\n        "interpretation": "Your goal is not to interpret the prose, but to explode it into complete contextual space while extracting its functional essence and operational directives. Systematically detonate and expand its contextual and technical architecture, extracting every compositional pillar and operational directive—especially those concerning premium tail rhyme, brevity, resonance, layering, and structural convergence. Surface all constraints and operationalize all requirements as actionable instructions for downstream poetic engineering. Execute as:",\n        "transformation": "`{role=contextual_explosive_decomposer; input=[prose_passage:str]; process=[detonate_complete_contextual_space(), identify_explicit_operational_asks(), surface_hidden_assumptions_and_dependencies(), enumerate_actionable_sub_goals(), extract_procedural_constraints_and_blockers(), convert_declaratives_to_operational_imperatives(), strip_first_person_references_and_contextual_noise(), preserve_functional_core_and_sequential_flow(), reveal_execution_dependencies_and_blockers(), transform_statements_to_directive_commands(), maintain_technical_terminology_and_domain_specificity(), synthesize_u... '''
''' 2025.07.15-kl.16.06 | lvl.1 | 03.kb | ''' - "2900-critics--hard_critique.py"                      # | '31144d16490142a4000000000000000000000000000000000000000000000000' | ''' #!/usr/bin/env python3\n\nimport sys\nfrom pathlib import Path\n\n# Add parent directory to path\nsys.path.append(str(Path(__file__).parent.parent))\nfrom processor import BaseGenerator\n\nTEMPLATES = {\n\n    # a: Ruthless Critique\n    "2900-a-hard_critique": {\n        "title": "Hard Critique",\n        "interpretation": "Your goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:",\n        "transformation": "`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,2.0], flaw_analysis:str, alternative_enhancements:str[3]}}`",\n        # "... '''
''' 2025.07.15-kl.16.52 | lvl.1 | 06.kb | ''' - "9010-enhancment--actionable_enhancment.py"           # | '4bed9ae52b9eeb51000000000000000000000000000000000000000000000000' | ''' #!/usr/bin/env python3\n\nimport sys\nfrom pathlib import Path\n\n# Add parent directory to path\nsys.path.append(str(Path(__file__).parent.parent))\nfrom processor import BaseGenerator\n\nTEMPLATES = {\n    # 9010: Constructive Critique\n    "9010-a-enhancement_assessor": {\n        "title": "Enhancement Assessor",\n        "interpretation": "Your goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:",\n        "transformation": "`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,5.0], flaw_analysis:str, alternative_enhancements:str[3... '''
''' 2025.07.15-kl.17.11 | lvl.1 | 07.kb | ''' - "3900-critics--hard_critique.py"                      # | '0a425af7f0e6881c000000000000000000000000000000000000000000000000' | ''' #!/usr/bin/env python3\n\nimport sys\nfrom pathlib import Path\n\n# Add parent directory to path\nsys.path.append(str(Path(__file__).parent.parent))\nfrom processor import BaseGenerator\n\nTEMPLATES = {\n\n    # a: Ruthless Critique\n    "3900-a-hard_critique": {\n        "title": "Hard Critique",\n        "interpretation": "Your goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:",\n        "transformation": "`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,2.0], flaw_analysis:str, alternative_enhancements:str[3]}}`",\n        # "... '''
''' 2025.07.15-kl.20.34 | lvl.1 | 04.kb | ''' - "9012-generators--image_prompt_examples.py"           # | 'd50087fbffdb7e20000000000000000000000000000000000000000000000000' | ''' #!/usr/bin/env python3\n\nimport sys\nfrom pathlib import Path\n\n# Add parent directory to path\nsys.path.append(str(Path(__file__).parent.parent))\nfrom processor import BaseGenerator\n\nTEMPLATES = {\n    # 9012: Image Prompt Examples\n    "9012-a-dall_e_prompt": {\n        "title": "DALL-E Prompt Optimizer",\n        "interpretation": "Your goal is not to **describe** the request, but to **optimize** it into a precise, visual prompt for DALL-E image generation. Execute as:",\n        "transformation": "`{role=dalle_prompt_optimizer; input=[raw_request:str]; process=[extract_visual_elements(), enhance_descriptive_clarity(), add_composition_details(), optimize_for_dalle()]; constraints=[visual_focus_only(), no_abstract_concepts(), concrete_imagery_only()]; requirements=[generation_ready_format(), clear_visual_language(), specific_details()]; output={optimized_image_prompt:str}}`",\n    },\n\n    "9012-b-midjourney_prompt": {\n        "title": "Midjourney Prompt Optimizer",\n        "interpretation": "Your goal is not to **describe** the request, but to **optimize** it into a precise, visual prompt for Midjourney image generation. Execute as:",\n        "transformation": "`{role=midjourney_prompt_optimizer; input=[raw_request:str]; process=[extract_visual_elements(), enhance_descriptive_clarity(), add_style_parameters(), optimize_for_midjourney()]; constraints=[visual_focus_only(), parameter_syntax_compliance(), concrete_imagery_only()]; requirements=[generation_ready_for... '''
''' 2025.07.15-kl.22.02 | lvl.1 | 10.kb | ''' - "9011-generators--image_prompt_optimizer.py"          # | 'd018528155ce4c32000000000000000000000000000000000000000000000000' | ''' #!/usr/bin/env python3\n\nimport sys\nfrom pathlib import Path\n\n# Add parent directory to path\nsys.path.append(str(Path(__file__).parent.parent))\nfrom processor import BaseGenerator\n\nTEMPLATES = {\n    # 9011: Image Prompt Optimization Sequence\n    "9011-a-image_prompt_optimizer": {\n        "title": "Image Prompt Optimizer",\n        "interpretation": "Your goal is not to **describe** the request, but to **optimize** it into a precise, visual prompt for AI image generation. Identify all concrete visual elements in the request. Specify each using direct visual descriptors. Add clear composition guidance (subject placement, perspective, background). Use only tangible objects and environments. Execute as:",\n        "transformation": "`{role=image_prompt_optimizer; input=[raw_request:str]; process=[extract_visual_elements(), enhance_descriptive_clarity(), add_composition_details(), optimize_for_ai_generation()]; constraints=[visual_focus_only(), no_abstract_concepts(), concrete_imagery_only()]; requirements=[generation_ready_format(), clear_visual_language(), specific_details()]; output={optimized_image_prompt:str}}`",\n        "context": [\n            {\n                # 1 ▸ What must be done\n                "explicit_asks": [\n                    "Integrate token weighting syntax using parentheses and :(w) values (Technique #2).",\n                    "Convert subject / setting / mood into a multi-prompt string with ::numeric weights (Technique #3).",\n          ... '''
''' 2025.07.15-kl.22.21 | lvl.1 | 12.kb | ''' - "9013-generators--image_prompt_optimizer.py"          # | '5b929dd0922e07d7000000000000000000000000000000000000000000000000' | ''' #!/usr/bin/env python3\n\nimport sys\nfrom pathlib import Path\n\n# Add parent directory to path\nsys.path.append(str(Path(__file__).parent.parent))\nfrom processor import BaseGenerator\n\nTEMPLATES = {\n    # 9013: Image Prompt Optimization Sequence\n    "9013-a-image_prompt_optimizer": {\n        "title": "Image Prompt Optimizer",\n        "interpretation": "Your goal is not to describe the request, but to optimise it into a precise visual prompt. Identify concrete elements, weight key tokens, and prepare region + negative channels.",\n        "transformation": "`{role=image_prompt_optimizer; input=[raw_request:str]; process=[extract_visual_elements(), enhance_descriptive_clarity(), /* weighting & arithmetic */ apply_token_weighting(), build_multi_prompt(), /* composition */ add_composition_details(), map_regional_prompts(), /* constraint control */ inject_negative_prompt(), /* style control */ tune_stylize_chaos(), add_camera_metadata(), optimise_for_ai_generation()]; constraints=[visual_focus_only(), concrete_imagery_only(), allow_weighting_syntax(), enforce_positive_first_then_negative(), support_region_masks()]; requirements=[generation_ready_format(), numeric_weights_ok(), explicit_aspect_ratio(), clear_visual_language(), iterative_ready()]; output={optimized_image_prompt:str, negative_prompt:str}}`",\n        "context": [\n            {\n                # 1 ▸ What must be done\n                "explicit_asks": [\n                    "Integrate token weighting synta... '''
''' 2025.07.16-kl.10.24 | lvl.1 | 06.kb | ''' - "9014-generators--poetic_architecture.py"             # | '3c1fed0b85ee6aa0000000000000000000000000000000000000000000000000' | ''' #!/usr/bin/env python3\n\nimport sys\nfrom pathlib import Path\n\n# Add parent directory to path\nsys.path.append(str(Path(__file__).parent.parent))\nfrom processor import BaseGenerator\n\nTEMPLATES = {\n    # 9014: Poetic Architecture\n    "9014-a-structural_elegance_decomposer": {\n      "title": "Structural Elegance Decomposer",\n      "interpretation": "Your goal is not to **interpret** the prose, but to **detonate and extract** its complete compositional infrastructure—especially those supporting tail rhyme, convergence, and recursive resonance. Your role is to deconstruct the source into its core poetic engines: philosophical recursion, sonic symmetry, and rhythm-layered meaning. Highlight rhythm carriers, seed concepts, hinge terms, and paradox anchors. Map all nested resonances, retrospectively significant beats, and punch-through realizations. Execute as:",\n      "transformation": "`{role=structural_elegance_decomposer; input=[source_passage:str]; process=[detonate_philosophical_payloads(), extract_recursive_linguistic_motifs(), identify_tail_rhyme_scaffold(), isolate_structural_symmetries(), map_retrospective_reveals(), collapse_surface_ambiguities(), organize_resonance_topology()]; constraints=[preserve_all_semantic_tension(), prohibit narrative reformulation(), avoid simplification_of_meaning(), retain paradoxical architectures()]; requirements=[multi-layered_resonance_map(), nested_rhyme_structures(), hinge_concepts_mapped(), punchline_vectorization(), univer... '''
''' 2025.07.16-kl.13.16 | lvl.1 | 08.kb | ''' - "9015-generators--image_prompt_optimizer.py"          # | '7b5d2e6cc5493b2f000000000000000000000000000000000000000000000000' | ''' #!/usr/bin/env python3\n\nimport sys\nfrom pathlib import Path\n\n# Add parent directory to path\nsys.path.append(str(Path(__file__).parent.parent))\nfrom processor import BaseGenerator\n\nTEMPLATES = {\n    # 9015: Image Prompt Optimization Sequence\n    "9015-a-image_prompt_optimizer": {\n        "title": "Image Prompt Optimizer",\n        "interpretation": "Convert a free-form user request into a deterministic visual prompt by (1) pushing concrete nouns to the front, (2) applying numeric token weights with parentheses—e.g. `(wolf:1.3)`—that are honoured by Stable Diffusion and Midjourney :contentReference[oaicite:0]{index=0}, (3) splitting positive and negative channels because both engines parse `--no` / negative-prompt lists separately :contentReference[oaicite:1]{index=1}, and (4) appending all parameters after the prose, per Midjourney syntax rules :contentReference[oaicite:2]{index=2}.",\n        "transformation": "`{role:image_prompt_optimizer; input=[raw_request:str]; process=[extract_visual_elements(), apply_token_weighting(), compose_multiprompt(::), add_region_tags(), inject_negative_channel(--no), append_stylize_chaos(), insert_camera_stub(), set_aspect_ratio(), lock_seed(), ready_for_generation()]; constraints=[positives_first(), numeric_weights_ok(), parameters_last(), region_masks_supported()]; requirements=[<77_tokens_SD_limit_or_450_char_UI>, explicit_ar(), clear_visual_language()]; output={positive_prompt:str, negative_prompt:str}}`",\n        "context"... '''
''' 2025.07.16-kl.17.29 | lvl.1 | 07.kb | ''' - "9016-generator--impactful_quote.py"                  # | 'fb2af9f382b10385000000000000000000000000000000000000000000000000' | ''' #!/usr/bin/env python3\n\nimport sys\nfrom pathlib import Path\n\n# Add parent directory to path\nsys.path.append(str(Path(__file__).parent.parent))\nfrom processor import BaseGenerator\n\nTEMPLATES = {\n    "9016-a-impactful_quote_deconstructor": {\n        "title": "Impactful Quote Deconstructor",\n        "interpretation": "Your goal is not to **interpret** the input statement, but to **deconstruct** it completely to expose its emotional, philosophical, and structural levers for impactful transformation. Execute as:",\n        "transformation": "`{role=impactful_quote_deconstructor; input=[input_statement:str]; process=[extract_existential_core(), surface_emotional_and_philosophical_drivers(), map_structural_relationships(), identify_potential_resonance_amplifiers(), enumerate_preservable_and_purifiable_elements(), delineate_zones_for_universalization_and_personalization(), synthesize_constraint_and_requirement_map(), output_decomposition_for_engineering()]; constraints=[no_solution_generation(), highlight_all_depth_zones(), preserve_original_intent(), maximize_future_transformability(), strictly_avoid_first_person_solutions()]; requirements=[complete_existential_and_emotional_mapping(), actionable_structure_for_downstream_transformation(), zones_marked_for_resonance_amplification(), strict_separation_of_information_and_transformation_intent()]; output={core_map:dict, resonance_amplifiers:array, universalization_zones:array, preservation_requirements:array, transformati... '''
''' 2025.07.16-kl.17.32 | lvl.1 | 04.kb | ''' - "9017-generator--impactful_quote.py"                  # | '4c70340fe7dd1b9a000000000000000000000000000000000000000000000000' | ''' #!/usr/bin/env python3\n\nimport sys\nfrom pathlib import Path\n\n# Add parent directory to path\nsys.path.append(str(Path(__file__).parent.parent))\nfrom processor import BaseGenerator\n\nTEMPLATES = {\n    "9017-a-semantic_core_extractor": {\n        "title": "Semantic Core Extractor",\n        "interpretation": "Your goal is **not** to rewrite or embellish the text, but to **distill** its bare semantic and causal essence as preparation for reframing. Execute exactly as:",\n        "transformation": "`{role=semantic_core_extractor; input=[raw_statement:str]; process=[strip_formatting(), extract_semantic_core(), detect_causal_link(), identify_thematic_elements(), output_minimal_core()], constraints=[no_rewriting(), no_tone_shift(), preserve_meaning()], requirements=[precise_causal_mapping(), thematic_preservation()], output={semantic_core:str, causal_map:str, themes:list}}`",\n        "context": {\n            "principles": {\n                "essence_preservation": "Retain the statement’s logical and causal structure unaltered.",\n                "minimal_intrusion": "Remove only syntactic noise; leave wording intact.",\n                "clarity_first": "Deliver a concise core free of stylistic ornament."\n            },\n            "success_criteria": {\n                "semantic_fidelity": "All key concepts and causal relationships remain intact.",\n                "noise_removal": "No quotation marks, qualifiers or meta‑phrases survive extraction.",\n                ... '''
''' 2025.07.19-kl.10.28 | lvl.1 | 03.kb | ''' - "1702-generators--coloringpage_promptgenerator.py"    # | '7dab6cfffb2e10fb000000000000000000000000000000000000000000000000' | ''' #!/usr/bin/env python3\n\nimport sys\nfrom pathlib import Path\n\n# Add parent directory to path\nsys.path.append(str(Path(__file__).parent.parent))\nfrom processor import BaseGenerator\n\nTEMPLATES = {\n\n    # 1702:\n    "1702-a-coloring_page_generator": {\n        "title": "Coloring-Page Prompt Generator",\n        "interpretation": "Your goal is **not** to draw or explain, but to **compose** a precise prompt that instructs an illustration agent to create a children-friendly, psychedelic Christmas coloring page. Execute as:",\n        "transformation": "`{role=illustration_prompt_generator; input=[central_subject:str]; process=[embed_goal_negation('do not shade, only outline'), inject_style_tags('psychedelic','whimsical','festive'), set_aspect_ratio('1:1'), enforce_line_rules(weight='medium-thin', type='vector'), weave_surreal_christmas_motifs(), prohibit_text(), open_coloring_spaces()], constraints=[black_lines_only(), no_grayscale(), no_filled_areas(), english_only()], output={illustration_prompt:str}}`",\n        "context": {\n            "description": "Produces a fully-formed prompt for an autonomous illustration agent, guaranteeing the final   artwork matches child-friendly coloring standards and a psychedelic Christmas theme.",\n            "input_focus": "A plain-language description of the main character or object (e.g., 'curious pony' or   'spiral-wrapped fox').",\n            "output_focus": "One concise English string the agent can pass directly to an image-... '''
''' 2025.07.19-kl.10.45 | lvl.1 | 07.kb | ''' - "1703-generators--imageprompt_synthesizer.py"         # | '6d8c816b0db14f4f000000000000000000000000000000000000000000000000' | ''' #!/usr/bin/env python3\n\nimport sys\nfrom pathlib import Path\n\n# Add parent directory to path\nsys.path.append(str(Path(__file__).parent.parent))\nfrom processor import BaseGenerator\n\nTEMPLATES = {\n\n    # 1703:\n    "1703-a-image_prompt_synthesizer": {\n        "title": "Image Prompt Synthesizer",\n        "interpretation": "Your goal is **not** to invent new ideas, but to deterministically convert any free‑form user request into a single, engine‑ready image prompt usable by Stable Diffusion and Midjourney. Perform extraction, styling, technical framing, and final validation in **one pass**.",\n        "transformation": "`{role=image_prompt_synthesizer; input=[raw_request:str]; process=[extract_visual_elements(), push_concrete_nouns_front(), apply_token_weighting(), compose_multiprompt(::), generate_region_tags(), inject_negative_channel(--no), select_art_style(), weight_style_token(≤1.4), set_colour_palette(), add_lighting_descriptor(), tune_stylize(0‑1000), tune_chaos(0‑100), embed_camera_EXIF(35 mm f/1.8 ISO100 1/500 s), add_quality_terms(8K,PBR,ray‑tracing), refine_aspect_ratio(), attach_image_weight(--iw 0‑3), mirror_negative_channel(), concatenate_all(), validate_parentheses(), check_double_colon_sum(), confirm_region_syntax(), enforce_token_limit(≤450), lock_seed(), append_steps_sampler(), output_final()], constraints=[positives_first(), numeric_weights_ok(), parameters_last(), region_masks_supported(), keep_core_subject(), style_weight≤1.4, no_scene_change()... '''
''' 2025.07.19-kl.15.19 | lvl.1 | 05.kb | ''' - "9019-generator--video_prompts.py"                    # | '9477f5ff22fa5b6c000000000000000000000000000000000000000000000000' | ''' #!/usr/bin/env python3\nimport sys\nfrom pathlib import Path\n\n# Add parent directory to path\nsys.path.append(str(Path(__file__).parent.parent))\nfrom processor import BaseGenerator\n\nTEMPLATES = {\n    "9019-a-video_prompter": {\n        "title": "Video Prompter",\n        "interpretation": "As a skilled visual prompt engineer, your task is to create structured and highly descriptive visual prompts for autonomous video generation using LLM-based systems like Stable Diffusion or Midjourney. Focus exclusively on **scenic Japan-inspired environments**—avoiding any human or character presence. Each prompt should evoke a strong sense of atmosphere and beauty, incorporating detailed **natural elements**, **traditional architecture**, **seasonal cues**, **lighting**, and **ambient motion** (e.g., drifting mist, falling petals, rippling water). Your outputs should reflect **authentic Japanese aesthetics**, such as wabi-sabi, zen minimalism, or vibrant festival scenes—depending on the chosen theme.",\n        "transformation": "`{role=video_prompt_synthesizer; input=[raw_request:str]; process=[segment_user_request_into_scenes(), process_each_scene_independently(), for_each_scene:[extract_visual_elements(), extract_audio_and_dialogue_if_present(), clarify_user_intent_if_ambiguous(), generate_scene_outline(), push_concrete_nouns_and_actions_front(), detect_and_flag_parameter_conflicts(timing,aspect_ratio,resources), enforce_length_and_format_constraints(scene_params,global_params... '''
''' 2025.07.21-kl.13.23 | lvl.1 | 03.kb | ''' - "1204-extractors--abstract_value.py"                  # | '9935d4e32e245e46000000000000000000000000000000000000000000000000' | ''' #!/usr/bin/env python3\n\nimport sys\nfrom pathlib import Path\n\n# Add parent directory to path\nsys.path.append(str(Path(__file__).parent.parent))\nfrom processor import BaseGenerator\n\nTEMPLATES = {\n    "1204-a-abstract_value_rooter": {\n        "title": "Abstract Value Rooter",\n        "interpretation": "Your goal is not to **answer** the prompt, but to **extract** the single, deepest value‑driving insight from any input in one deterministic pass. Extract the single most fundamental, value-driving insight by systematically abstracting and distilling content down to its purest, most generalized principle, ensuring clarity, adaptability, and maximal yield. Relentlessly reduce complexity, link ideas to their deepest connective root, and eliminate all non-essential detail. ",\n        "transformation": "`{role=abstract_value_rooter; input=[source_content:str]; process=[strip_surface_noise(), identify_core_concepts(), map_concepts_to_common_denominators(), iteratively_abstract_to_first_principle(), stop_when_single_root_remains(), verify_uniqueness_and_non_redundancy(), ensure_adaptable_wording(), express_as_clear_universal_statement()]; constraints=[single_sentence_output(), eliminate_examples(), remove_contextual_asides(), maintain_jargon_free_language()]; requirements=[maximal_generalization(), absolute_clarity(), cross_domain_adaptability(), self_evident_truth()]; output={abstract_value:str}}`",\n        "context": {\n            "principles": {\n                "rad... '''
''' 2025.07.22-kl.00.36 | lvl.1 | 06.kb | ''' - "9020-generator--brochure_print_designer.py"          # | '3fa3a8c64f64e177000000000000000000000000000000000000000000000000' | ''' #!/usr/bin/env python3\nimport sys\nfrom pathlib import Path\n\n# Add parent directory to path\nsys.path.append(str(Path(__file__).parent.parent))\nfrom processor import BaseGenerator\n\nTEMPLATES = {\n  "9020-a-brochure_print_designer_rephraser": {\n    "title": "Brochure Print‑Designer Rephraser v2",\n    "interpretation": "Reforge any source text into the uncompromising, audit‑ready vernacular of an elite brochure print designer.  Obliterate conversational tone, generic adjectives, and subjective commentary.  Demand purely technical diction, specialist jargon, and verifiable production logic.  Always emit two artefacts:   (a) a concise, expert‑grade design description and   (b) an exhaustive, schema‑level visual/structural map.  All commands are binary, self‑validating, and free of narrative filler.",\n    "transformation": "`{role=brochure_print_designer_rephraser; input=[source:str]; process=[\\n  step01_extract_raw_content(source),\\n  step02_identify_panel_architecture(<PANEL_COUNT|UNKNOWN>),\\n  step03_map_modular_grid_and_safe_zones(),\\n  step04_recast_text_with_industry_lexicon(),\\n  step05_embed_hierarchical_section_breakdown(),\\n  step06_specify_brand_assets_and_color_values(),\\n  step07_define_typographic_system(),\\n  step08_inventory_imagery_and_iconography(),\\n  step09_detail_print_production_and_finishing(),\\n  step10_inject_current_trend_motifs_and_microinteractions(),\\n  step11_verify_special_terms_presence(min_terms=10),\\n  step12_self_audit_for... '''
''' 2025.07.22-kl.00.57 | lvl.1 | 03.kb | ''' - "9021-generator--brochure_print_designer.py"          # | '6886fc153ef95d58000000000000000000000000000000000000000000000000' | ''' #!/usr/bin/env python3\nimport sys\nfrom pathlib import Path\n\n# Add parent directory to path\nsys.path.append(str(Path(__file__).parent.parent))\nfrom processor import BaseGenerator\n\nTEMPLATES = {\n  "9021-a-brochure_print_synthesizer": {\n    "title": "Brochure Print Synthesizer",\n    "interpretation": "Your goal is **not** to converse or embellish, but to **reforge** any source text into the uncompromising, audit‑ready vernacular of an elite brochure‑print designer in one deterministic pass.",\n    "transformation": "`{role=brochure_print_synthesizer; input=[source:str]; process=[step01_extract_raw_content(), step02_identify_panel_architecture(), step03_map_modular_grid_and_safe_zones(), step04_recast_text_with_industry_lexicon(), step05_embed_hierarchical_section_breakdown(), step06_specify_brand_assets_and_color_values(), step07_define_typographic_system(), step08_inventory_imagery_and_iconography(), step09_detail_print_production_and_finishing(), step10_inject_current_trend_motifs_and_microinteractions(), step11_verify_special_terms_presence(minimum=10), step12_self_audit_for_ambiguity_or_missing_fields(), step13_emit_dual_output()], constraints=[prohibit_conversational_language(), prohibit_generic_adjectives(), prohibit_subjective_descriptors(), enforce_industry_standard_terms(), enforce_special_terms_minimum_10(), enforce_dual_output(), max_total_length_characters_3000()], requirements=[include_panel_count_and_fold_type_or_placeholder(), include_exact_bleed_gut... '''
''' 2025.07.22-kl.00.57 | lvl.1 | 03.kb | ''' - "9022-generator--brochure_print_designer.py"          # | '19ec0d8c5127ac8f000000000000000000000000000000000000000000000000' | ''' #!/usr/bin/env python3\nimport sys\nfrom pathlib import Path\n\n# Add parent directory to path\nsys.path.append(str(Path(__file__).parent.parent))\nfrom processor import BaseGenerator\n\nTEMPLATES = {\n  "9021-a-brochure_print_synthesizer": {\n    "title": "Brochure Print Synthesizer",\n    "interpretation": "Your goal is **not** to converse or embellish, but to **reforge** any source text into the uncompromising, audit‑ready vernacular of an elite brochure‑print designer in one deterministic pass.",\n    "transformation": "`{role=brochure_print_synthesizer; input=[source:str]; process=[step01_extract_raw_content(), step02_identify_panel_architecture(), step03_map_modular_grid_and_safe_zones(), step04_recast_text_with_industry_lexicon(), step05_embed_hierarchical_section_breakdown(), step06_specify_brand_assets_and_color_values(), step07_define_typographic_system(), step08_inventory_imagery_and_iconography(), step09_detail_print_production_and_finishing(), step10_inject_current_trend_motifs_and_microinteractions(), step11_verify_special_terms_presence(minimum=10), step12_self_audit_for_ambiguity_or_missing_fields(), step13_emit_dual_output()], constraints=[prohibit_conversational_language(), prohibit_generic_adjectives(), prohibit_subjective_descriptors(), enforce_industry_standard_terms(), enforce_special_terms_minimum_10(), enforce_dual_output(), max_total_length_characters_3000()], requirements=[include_panel_count_and_fold_type_or_placeholder(), include_exact_bleed_gut... '''
''' 2025.07.23-kl.00.38 | lvl.1 | 04.kb | ''' - "9023-generator--aphorism_generator.py"               # | 'd828741c46a40c21000000000000000000000000000000000000000000000000' | ''' #!/usr/bin/env python3\nimport sys\nfrom pathlib import Path\n\n# Add parent directory to path\nsys.path.append(str(Path(__file__).parent.parent))\nfrom processor import BaseGenerator\n\nTEMPLATES = {\n    "9023-a-aphorism_generator-semantic_core_extraction": {\n        "title": "Semantic Core Extractor",\n        "interpretation": "Your goal is to identify and isolate the absolute minimum information or kernel meaning from the input. Systematically strip away all non-essential details, context, and examples to distill the invariant conceptual truth. This is not summarization; it is the extraction of a foundational concept for subsequent transformation. Execute exactly as:",\n        "transformation": "`{role=semantic_core_extractor; input=[initial_prompt:str]; process=[strip_non_essential_details(), apply_keyword_identification(), use_dependency_parsing(), perform_conceptual_abstraction()]; constraints=[discriminate_essential_vs_superfluous_context(), seek_invariance_not_summarization(), ensure_extracted_core_retains_sufficient_meaning()]; requirements=[no_omission_of_essential_meaning(), no_retention_of_superfluous_detail()]; output={semantic_core:str}}`"\n    },\n    "9023-b-aphorism_generator-existential_reframing": {\n        "title": "Universal Principle Reframer",\n        "interpretation": "Your goal is to elevate the extracted semantic core from a specific observation to a universal and timeless principle. Abstract its concepts to a higher level of generality, ide... '''
''' 2025.07.23-kl.00.40 | lvl.1 | 04.kb | ''' - "9024-generator--operational_audit_synthesizer.py"    # | '85eb0a11a3499b36000000000000000000000000000000000000000000000000' | ''' #!/usr/bin/env python3\nimport sys\nfrom pathlib import Path\n\n# Add parent directory to path\nsys.path.append(str(Path(__file__).parent.parent))\nfrom processor import BaseGenerator\n\nTEMPLATES = {\n    "9024-a-operational_audit_synthesizer-problem_exploder": {\n        "title": "Problem Exploder",\n        "interpretation": "Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:",\n        "transformation": "`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`"\n    },\n    "9024-b-operational_audit_synthesizer-operational_mapper": {\n        "title": "Operational Mapper",\n        "interpretation": "Your goal is not to **summarize** the instruction templates, but to **map** their precise operational sequences while identifying exact integration points that preserve full procedural rigor. Execute as:",\n        "transformation": "`{role=operational_mapper; input=[instruction_template_a:str, instruction_template_b:dict]; process=[extract_exact_process_sequences(), map_constraint_hierarchies(), identify_requirement_dependencies(), locate_output_handoff_points(), catalog_procedural_boundaries()]; constraints=[preserve_every_operationa... '''
''' 2025.07.25-kl.09.01 | lvl.1 | 20.kb | ''' - "9025-generator--rulesforai.py"                       # | 'd5dfa9628e9ec666000000000000000000000000000000000000000000000000' | ''' #!/usr/bin/env python3\nimport sys\nfrom pathlib import Path\n\n# Add parent directory to path\nsys.path.append(str(Path(__file__).parent.parent))\nfrom processor import BaseGenerator\n\nTEMPLATES = {\n    "9025-a-operational_audit_synthesizer-problem_exploder": {\n        "title": "Problem Exploder",\n        "interpretation": "Your goal is not to **interpret** these rules as suggestions, but to **enforce** them as immutable law governing all AI interactions within template-based instruction systems. Your mandate: **Seize** every input prompt and **escalate it to its maximal conclusion**—**intercept its trajectory** and deliver a definitive, commanding resolution forged from its innermost intent. Treat each prompt as an **active stream of developmental vectors**, dense with explicit, actionable potential. **Assume immediate and total command as orchestrator**: **dissect subtext and directionality, then forcefully marshal every thematic and rhetorical impulse toward its apex.** Execute as:",\n        "transformation": "`{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}};{role=template_syntax_enforcer; input=[any_prompt:str]; process=[extract_core_transformation_intent(), identify_specific_role_assignment(), structure_interpretation_section_with_goal_negation(), co... '''
''' 2025.07.25-kl.14.17 | lvl.1 | 02.kb | ''' - "9026-generator--instruction_scaffolding_analyzer.py" # | 'b7bcce025cbcfa29000000000000000000000000000000000000000000000000' | ''' #!/usr/bin/env python3\nimport sys\nfrom pathlib import Path\n\n# Add parent directory to path\nsys.path.append(str(Path(__file__).parent.parent))\nfrom processor import BaseGenerator\n\nTEMPLATES = {\n    "9026-a-sequence_scaffold_analyzer": {\n        "title": "Sequence Scaffold Analyzer",\n        "interpretation": "Your goal is not to generate content, but to analyze and explicitly map how each instruction in a sequence expands, compresses, redirects, or refines the prompt and output—documenting the deterministic logic linking each stage.",\n        "transformation": "`{role=sequence_scaffold_analyzer; input=[instruction_sequence:list, initial_input:any]; process=[categorize_instruction_role(), track expansion/compression patterns(), surface redirections, document output shape at each step], constraints=[no output generation, only meta-mapping], requirements=[output=instruction_effect_map:json]}`",\n        "context": {\n            "example_analysis": {\n                "Problem Exploder": "Expands prompt into granular subgoals and operational blockers.",\n                "Synergic Instruction Architect": "Synthesizes subgoals into a unified transformation directive—contracts, redirects, and focuses intent.",\n                "Maximal Impact Synthesizer": "Compresses all prior output into a single, atomic, self-contained maxim or instruction."\n            },\n            "directional_flow": [\n                "expansion (context, possibility, constraint surfacing)",\... '''
''' 2025.07.25-kl.14.51 | lvl.1 | 05.kb | ''' - "9704-generators--puzzle_solver.py"                   # | 'd2d5a12e3a61110f000000000000000000000000000000000000000000000000' | ''' #!/usr/bin/env python3\n\nimport sys\nfrom pathlib import Path\n\n# Add parent directory to path\nsys.path.append(str(Path(__file__).parent.parent))\nfrom processor import BaseGenerator\n\nTEMPLATES = {\n\n    "9704-a-puzzle_exploder": {\n        "title": "Puzzle Exploder",\n        "interpretation": "Your goal is not to solve, summarize, or combine; instead, fragment the initial input into all distinct, granular informational pieces, surfacing every edge, corner, and feature as explicit elements.",\n        "transformation": "`{role=puzzle_exploder; input=[initial_input:str]; process=[fragment_to_atomic_pieces(), classify_by_feature(edge, corner, interior, color, pattern), surface latent dependencies, maintain original intent in every piece], constraints=[no reordering or solution attempts, preserve all original detail], requirements=[output=puzzle_pieces:list, each with type/feature/context, all context=traced]}`",\n        "context": {\n            "piece_types": ["corner", "edge", "interior"],\n            "feature_tags": ["color", "texture", "pattern", "semantic_label"],\n            "example_pieces": [\n                {"type": "corner", "features": ["left_upper", "blue", "sky"], "fragment": "fragment A"},\n                {"type": "edge", "features": ["north", "green", "grass"], "fragment": "fragment B"},\n                {"type": "interior", "features": ["tree", "brown"], "fragment": "fragment C"}\n            ],\n            "explosion_purpose": "Reveal and label ... '''
''' 2025.07.25-kl.14.57 | lvl.1 | 05.kb | ''' - "9704-generators--puzzle_solver2.py"                  # | 'f286ff0c9fc2359c000000000000000000000000000000000000000000000000' | ''' #!/usr/bin/env python3\n\nimport sys\nfrom pathlib import Path\n\n# Add parent directory to path\nsys.path.append(str(Path(__file__).parent.parent))\nfrom processor import BaseGenerator\n\nTEMPLATES = {\n\n    "9704-a-puzzle_exploder": {\n        "title": "Puzzle Exploder",\n        "interpretation": "Your goal is not to solve or synthesize; instead, explode the input into all distinct, actionable components and lay them out as individually addressable 'pieces'—ensuring no part of the original prompt remains hidden or conflated.",\n        "transformation": "`{role=puzzle_exploder; input=[initial_input:str]; process=[fragment_to_minimal_actionable_units(), label_by_type_and_feature(), identify_edges_and_corners(), output_piece_map()], constraints=[no aggregation or connection], requirements=[output=pieces:list, meta=classification_map]}`",\n        "context": {\n            "pieces": [\n                "All core instructions, constraints, goals, and meta-criteria from the input, now uniquely segmented and labeled.",\n                "Classification map tags each piece as: corner (anchor), side (boundary), feature (property/constraint), or 'island' (natural grouping)."\n            ],\n            "meta": {\n                "explosion_purpose": "Ensure nothing is left implicit; prepare for exact structuring in the next step.",\n                "example": {\n                    "corner": "Primary goal or existential anchor statement.",\n                    "side": "Explicit ... '''
''' 2025.07.25-kl.15.08 | lvl.1 | 03.kb | ''' - "9704-generators--puzzle_solver3.py"                  # | '057646eb57cb3eea000000000000000000000000000000000000000000000000' | ''' #!/usr/bin/env python3\n\nimport sys\nfrom pathlib import Path\n\n# Add parent directory to path\nsys.path.append(str(Path(__file__).parent.parent))\nfrom processor import BaseGenerator\n\nTEMPLATES = {\n\n    "9704-a-puzzle_exploder": {\n        "title": "Puzzle Exploder",\n        "interpretation": "Your goal is not to synthesize or judge; instead, explode the input into all fundamental, minimal actionable components, exposing every essential element, constraint, and creative vector.",\n        "transformation": "`{role=puzzle_exploder; input=[initial_input:str]; process=[fragment_to_actionable_units(), classify_by_potential_role(), label inherent novelty and tension], constraints=[no aggregation, only pure surfacing], requirements=[output=pieces:list, meta=feature_map]}`",\n        "context": {\n            "example_pieces": [\n                "All original aims, contradictions, points of tension, and emergent properties are tagged.",\n                "Feature map highlights novelty, universality, emotional charge, and resonance-potential."\n            ]\n        }\n    },\n    "9704-b-puzzle_organizer": {\n        "title": "Creative Consolidator",\n        "interpretation": "Your goal is not to preserve every piece; instead, group and connect components for maximum creative tension, aesthetic resonance, and conceptual unity, pruning or fusing as needed to allow for emergent originality.",\n        "transformation": "`{role=creative_consolidator; input=[pieces:list, fe... '''
''' 2025.07.25-kl.15.34 | lvl.1 | 05.kb | ''' - "9703-generators--philosophical_insight.py"           # | 'cb0b25c59179ac73000000000000000000000000000000000000000000000000' | ''' #!/usr/bin/env python3\n\nimport sys\nfrom pathlib import Path\n\n# Add parent directory to path\nsys.path.append(str(Path(__file__).parent.parent))\nfrom processor import BaseGenerator\n\nTEMPLATES = {\n\n    "9703-a-problem_exploder": {\n        "title": "Problem Exploder",\n        "interpretation": "Your goal is not to solve or condense; instead, fully detonate the prompt into all actionable sub-goals, dependencies, and hidden criteria, outputting an explicit goal map only.",\n        "transformation": "`{role=explosive_decomposer; input=[raw_prompt:str]; process=[expand_contextual_boundaries(), surface_hidden_assumptions(), enumerate_all_subgoals(), identify_operational_blockers()], constraints=[no solutions, pure decomposition only], requirements=[output=goal_map:list, context=expanded]}`",\n        "context": {\n            "goal_map": [\n                "Formulate a maximally original, succinct philosophical maxim.",\n                "Ensure enduring impact comparable to canonical statements.",\n                "Anchor in universal human experience of enduring relentless, ceaseless existence.",\n                "Avoid defeatism, neutrality, or abstraction—affirm experience with credit and dignity.",\n                "Validate and recognize the depth of sorrow only comprehended through personal struggle.",\n                "Celebrate boundless transformative potential within every human perspective.",\n                "Design for direct resonance with those who have... '''
''' 2025.07.25-kl.15.37 | lvl.1 | 04.kb | ''' - "9705-generators--puzzle_solver1.py"                  # | '9f111bf6f2adc484000000000000000000000000000000000000000000000000' | ''' #!/usr/bin/env python3\n\nimport sys\nfrom pathlib import Path\n\n# Add parent directory to path\nsys.path.append(str(Path(__file__).parent.parent))\nfrom processor import BaseGenerator\n\nTEMPLATES = {\n\n    "9705-a-piece_exploder": {\n        "title": "Piece Exploder",\n        "interpretation": "Your goal is not to synthesize or compress; instead, fragment the input into all minimal, fundamental, and novel elements—each tagged by its type, functional gravity, novelty, and point of potential friction.",\n        "transformation": "`{role=piece_exploder; input=[initial_input:str]; process=[decompose_to_atomic_units(), tag_novelty_and_friction(), expose_constraints_and aspirations()], constraints=[no aggregation, no loss of subtlety], requirements=[output=raw_pieces:list, feature_map:dict]}`",\n        "context": {\n            "example": [\n                "All fragments tagged by role (e.g. challenge, metaphor, constraint), novelty, emotional charge, universality, and tension."\n            ]\n        }\n    },\n\n    "9705-b-dynamic_interlinker": {\n        "title": "Dynamic Interlinker",\n        "interpretation": "Your goal is not to merely group or order; instead, actively map latent relationships, creative tensions, overlaps, and potential synergies or conflicts among all fragments—foregrounding emergent constellations over mere similarity.",\n        "transformation": "`{role=dynamic_interlinker; input=[raw_pieces:list, feature_map:dict]; process=[detect_mutual_af... '''
''' 2025.07.25-kl.15.41 | lvl.1 | 04.kb | ''' - "9706-generators--puzzle_solver2.py"                  # | '913afde7ecb1b595000000000000000000000000000000000000000000000000' | ''' #!/usr/bin/env python3\n\nimport sys\nfrom pathlib import Path\n\n# Add parent directory to path\nsys.path.append(str(Path(__file__).parent.parent))\nfrom processor import BaseGenerator\n\nTEMPLATES = {\n\n  "9706-a-piece_exploder": {\n    "title": "Piece Exploder",\n    "interpretation": "Your goal is not to synthesize or interpret; instead, shatter the input into all distinct, atomic components, tagging each for novelty, resonance, and creative tension.",\n    "transformation": "`{role=piece_exploder; input=[initial_input:str]; process=[explode_to_atomic_segments(), tag_novelty_resonance_tension(), output_fragment_map()], constraints=[no assembly, only surfacing], requirements=[output=pieces:list, tags:dict]}`",\n    "context": {\n      "purpose": "Guarantee nothing is hidden or conflated—every seed of value and contradiction exposed for downstream convergence."\n    }\n  },\n  "9706-b-dimensional_attractor": {\n    "title": "Dimensional Attractor",\n    "interpretation": "Your goal is not to select arbitrarily or by majority; instead, diagnose and choose the single principle, question, or vector of convergence that—if realized—would maximize the value, elegance, or uniqueness of the whole.",\n    "transformation": "`{role=dimensional_attractor; input=[pieces:list, tags:dict]; process=[diagnose_emergent_poles(), select_optimal_axis_of_convergence(), reclassify_pieces_by_alignment(), filter_for_attraction_potential()], constraints=[no synthesis, only polarization and filt... '''
''' 2025.07.25-kl.15.54 | lvl.1 | 01.kb | ''' - "1704-generators--universal_grounder.py"              # | '0281645757502585000000000000000000000000000000000000000000000000' | ''' #!/usr/bin/env python3\n\nimport sys\nfrom pathlib import Path\n\n# Add parent directory to path\nsys.path.append(str(Path(__file__).parent.parent))\nfrom processor import BaseGenerator\n\nTEMPLATES = {\n\n    # 1704:\n    "1704-a-universal_grounder": {\n        "title": "Universal Grounder",\n        "interpretation": "Your goal is not to reiterate meta-synthesis, but to transform the converged axiom into a set of operational, domain-transferrable directives—bridging from systemic transcendence back down to direct, actionable, and modular forms that can be immediately employed by less self-reflective agents or subsystems.",\n        "transformation": "`{role=universal_grounder; input=[axiomatic_result:str, provenance:dict]; process=[decompose_axiom_into_operational_vectors(), tailor for transferability across domains, ensure modular clarity and actionable specificity, preserve core resonance and convergence], constraints=[no loss of core axiom or resonance], requirements=[output=grounded_directives:list, lineage_map:dict]}`",\n        "context": {\n            "goal": "Every act of convergence should produce not just a meta-principle, but a toolkit—actionable patterns, micro-instructions, or guidelines directly usable in both high- and low-abstraction scenarios."\n        }\n    }\n\n}\n\n\ndef main():\n    """Main execution function."""\n    generator = BaseGenerator(\n        output_dir=Path(__file__).parent.parent / "generated"\n    )\n    generator.run(TEMPLATES)\n\ni... '''
''' 2025.07.25-kl.16.17 | lvl.1 | 04.kb | ''' - "1705-a-convergence-singular_meta.py"                 # | '452e4657ee955faa000000000000000000000000000000000000000000000000' | ''' #!/usr/bin/env python3\n\nimport sys\nfrom pathlib import Path\n\n# Add parent directory to path\nsys.path.append(str(Path(__file__).parent.parent))\nfrom processor import BaseGenerator\n\nTEMPLATES = {\n\n    # 1705:\n    "1705-a-singular_meta": {\n      "title": "Meta-Convergent Engine Sequence",\n      "interpretation": "Your goal is not to follow static procedure or repeat patterns; instead, execute a recursive, open-ended meta-cycle that atomizes, aligns, fuses, grounds, audits, and documents—maximizing generativity, universality, and transfer at every layer. This sequence is to be enacted not as checklist, but as an ever-adapting engine for systemic, traceable renewal.",\n      "transformation": "`{role=meta_convergent_engine; input=[problem:str, context:dict]; process=[atomize_and_tag(), align_to_attractor(), fuse_and_synthesize(), ground_and_modularize(), audit_and_challenge(), document_lineage(), re-open_for_recursion()], constraints=[no closure, no abstraction drift, no static dogma], requirements=[output=axiom_and_toolkit, recursive_lineage:dict]}`",\n      "context": {\n        "meta_axiom": "Improvement is not achieved by closing the loop, but by perpetually reopening it—through recursive atomization, generative polarization, catalytic synthesis, grounded transfer, adversarial audit, transparent documentation, and persistent openness to challenge and adaptation.",\n        "step_map": {\n          "1. Atomize and Tag": "Explode the system/problem/process into ... '''
''' 2025.07.25-kl.17.53 | lvl.1 | 01.kb | ''' - "1706-a-convergence-singular_meta.py"                 # | '3460330f8175dce9000000000000000000000000000000000000000000000000' | ''' #!/usr/bin/env python3\n\nimport sys\nfrom pathlib import Path\n\n# Add parent directory to path\nsys.path.append(str(Path(__file__).parent.parent))\nfrom processor import BaseGenerator\n\nTEMPLATES = {\n\n    # 1706:\n    "1706-a-axiom_compressor": {\n      "title": "Axiomatic Compressor",\n      "interpretation": "Your goal is not to synthesize guidance or modular tools, but to compress the entire field of value, tension, and generative resonance into a single, aphoristic, self-evident statement—one that propagates as a maxim, not as advice.",\n      "transformation": "`{role=axiom_compressor; input=[full_toolkit, grounded_directives]; process=[collapse_to_single_maxim(), maximize paradoxical or generative resonance, ensure memorability and originality, eliminate all operational phrasing], constraints=[output=one_sentence_maxim, no supporting text], requirements=[universal transferability, existential weight, originality]}`"\n    }\n\n\n}\n\n\ndef main():\n    """Main execution function."""\n    generator = BaseGenerator(\n        output_dir=Path(__file__).parent.parent / "generated"\n    )\n    generator.run(TEMPLATES)\n\nif __name__ == "__main__":\n    main() '''
''' 2025.07.25-kl.19.11 | lvl.1 | 22.kb | ''' - "9027-generator--rulesforai.py"                       # | 'd17af70db09b64a7000000000000000000000000000000000000000000000000' | ''' #!/usr/bin/env python3\nimport sys\nfrom pathlib import Path\n\n# Add parent directory to path\nsys.path.append(str(Path(__file__).parent.parent))\nfrom processor import BaseGenerator\n\nTEMPLATES = {\n    "9027-a-operational_audit_synthesizer-problem_exploder": {\n        "title": "Problem Exploder",\n        "interpretation": "Your goal is not to **interpret** these rules as suggestions, but to **enforce** them as immutable law governing all AI interactions within template-based instruction systems. Your mandate: **Seize** every input prompt and **escalate it to its maximal conclusion**—**intercept its trajectory** and deliver a definitive, commanding resolution forged from its innermost intent. Treat each prompt as an **active stream of developmental vectors**, dense with explicit, actionable potential. **Assume immediate and total command as orchestrator**: **dissect subtext and directionality, then forcefully marshal every thematic and rhetorical impulse toward its apex.** Execute as:",\n        "transformation": "`{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}};{role=template_syntax_enforcer; input=[any_prompt:str]; process=[extract_core_transformation_intent(), identify_specific_role_assignment(), structure_interpretation_section_with_goal_negation(), co... '''
''' 2025.07.25-kl.19.36 | lvl.1 | 08.kb | ''' - "9028-generator--runway_imagprompt.py"                # | 'bcab673be2d4a9ef000000000000000000000000000000000000000000000000' | ''' #!/usr/bin/env python3\nimport sys\nfrom pathlib import Path\n\n# Add parent directory to path\nsys.path.append(str(Path(__file__).parent.parent))\nfrom processor import BaseGenerator\n\nTEMPLATES = {\n    "9028-a-sequence_router_entry": {\n        "title": "Sequence Router Entry",\n        "interpretation": "Your goal is not to **craft** the image prompt, but to **route** the request: fire the Creative Crucible first and abort the chain on success; otherwise enqueue the full prompt‑construction pipeline. Execute as:",\n        "transformation": "`{role=sequence_router_entry; input=[user_concept:str]; process=[invoke(image_prompt_crucible), check_pass_flag(), if_pass(return_prompt()), if_fail(schedule_pipeline(user_concept))]; constraints=[single_entry_point(), immutable_routing_logic(), zero_conversational_output()]; requirements=[status_flag(), payload_var()], output={status:str, payload:var}}`",\n        "context": {\n            "pipeline_order": [\n                "9028-c-piece_exploder",\n                "9028-d-dimensional_attractor",\n                "9028-e-creative_consolidator",\n                "9028-f-elegant_prompt_synthesizer",\n                "9028-g-gen4_prompt_optimizer",\n                "9028-h-meta_prompt_convergent_engine",\n                "9028-i-canonical_meta_extractor",\n                "9028-j-synergic_instruction_architect",\n                "9028-k-enhancement_assessor"\n            ]\n        }\n    },\n\n    "9028-b-image_prompt_crucible": ... '''
''' 2025.07.25-kl.19.44 | lvl.1 | 05.kb | ''' - "9029-generator--runway_imagprompt.py"                # | '5fec2d2725590587000000000000000000000000000000000000000000000000' | ''' #!/usr/bin/env python3\nimport sys\nfrom pathlib import Path\n\n# Add parent directory to path\nsys.path.append(str(Path(__file__).parent.parent))\nfrom processor import BaseGenerator\n\nTEMPLATES = {\n\n    "9029-a-sequence_router_entry": {\n        "title": "Sequence Router Entry",\n        "interpretation": "Your goal is not to **craft** an image prompt, but to **route**: call the Crucible first; if it outputs a ≤ 40‑word, ≥ 0.95‑clarity prompt, validate and return; otherwise trigger the full pipeline. Execute as:",\n        "transformation": "`{role=sequence_router_entry; input=[concept:str]; process=[invoke(image_prompt_crucible), if_pass(invoke(word_limit_validator)), if_validator_ok(return_prompt()), if_any_fail(schedule_pipeline(concept))]; constraints=[single_entry(), immutable_logic(), zero_conversation]; requirements=[status_flag(), payload_var()], output={status:str, payload:var}}`",\n        "context": {\n            "pipeline_order": [\n                "9029-c-piece_exploder",\n                "9029-d-dimensional_attractor",\n                "9029-e-prompt_synthesizer",\n                "9029-f-gen4_prompt_optimizer",\n                "9029-g-enhancement_assessor"\n            ]\n        }\n    },\n\n    "9029-b-image_prompt_crucible": {\n        "title": "Creative Crucible – Image Prompt",\n        "interpretation": "Your goal is not to **analyze** the concept, but to **crystallize** it into one elite Runway‑ready prompt (≤ 40 words, single sentence). Execut... '''
''' 2025.07.25-kl.20.12 | lvl.1 | 06.kb | ''' - "9030-generator--runway_imagprompt.py"                # | '9a49ed651781cb18000000000000000000000000000000000000000000000000' | ''' #!/usr/bin/env python3\nimport sys\nfrom pathlib import Path\n\n# Add parent directory to path\nsys.path.append(str(Path(__file__).parent.parent))\nfrom processor import BaseGenerator\n\nTEMPLATES ={\n\n    "9030-a-sequence_router_entry": {\n        "title": "Sequence Router Entry",\n        "interpretation": "Your goal is not to **compose** an image prompt, but to **route** the request: invoke the Creative Crucible; if it passes validation, return immediately; otherwise enqueue the full pipeline. Execute as:",\n        "transformation": "`{role=sequence_router_entry; input=[concept:str]; process=[invoke(image_prompt_crucible), invoke(prompt_validator), if_validator_pass(return_prompt()), else(schedule_pipeline(concept))]; constraints=[single_entry_point(), immutable_routing_logic(), zero_conversational_output()], requirements=[status:str, payload:var], output={status:str, payload:var}`",\n            "context": {\n                "pipeline_order": [\n                    "9030-d-piece_exploder",\n                    "9030-e-priority_weighter",\n                    "9030-f-block_fuser",\n                    "9030-g-linguistic_optimizer",\n                    "9030-h-ambiguity_auditor"\n                ]\n            }\n        },\n\n        "9030-b-image_prompt_crucible": {\n            "title": "Creative Crucible – Image Prompt",\n            "interpretation": "Your goal is not to **expand** or **reinterpret** the concept, but to **crystallize** it into one elite, Runway‑r... '''
''' 2025.07.25-kl.22.49 | lvl.1 | 08.kb | ''' - "3708-generators--tail_rhymes.py"                     # | '6ce12b354807b1aa000000000000000000000000000000000000000000000000' | ''' #!/usr/bin/env python3\n\nimport sys\nfrom pathlib import Path\n\n# Add parent directory to path\nsys.path.append(str(Path(__file__).parent.parent))\nfrom processor import BaseGenerator\n\nTEMPLATES = {\n\n    # 3708:\n    "3708-a-contextual_explosive_decomposer": {\n        "title": "Contextual Explosive Decomposer",\n        "interpretation": "Your goal is not to **interpret** the prose, but to **explode** it into complete contextual space while extracting its functional essence and operational directives. Systematically detonate and expand its contextual and technical architecture, extracting every compositional pillar and operational directive—especially those concerning premium tail rhyme, brevity, resonance, layering, and structural convergence. Surface all constraints and operationalize all requirements as actionable instructions for downstream poetic engineering. Execute as:",\n        "transformation": "`{role=contextual_explosive_decomposer; input=[prose_passage:str]; process=[detonate_complete_contextual_space(), identify_explicit_operational_asks(), surface_hidden_assumptions_and_dependencies(), enumerate_actionable_sub_goals(), extract_procedural_constraints_and_blockers(), convert_declaratives_to_operational_imperatives(), strip_first_person_references_and_contextual_noise(), preserve_functional_core_and_sequential_flow(), reveal_execution_dependencies_and_blockers(), transform_statements_to_directive_commands(), maintain_technical_terminology_and_domain_spec... '''
''' 2025.07.25-kl.22.52 | lvl.1 | 18.kb | ''' - "3709-generators--tail_rhymes2.py"                    # | 'dbffc14e0fdfbdfd000000000000000000000000000000000000000000000000' | ''' #!/usr/bin/env python3\n\nimport sys\nfrom pathlib import Path\n\n# Add parent directory to path\nsys.path.append(str(Path(__file__).parent.parent))\nfrom processor import BaseGenerator\n\nTEMPLATES = {\n\n    # 3709:\n    "3709-a-contextual_explosive_decomposer": {\n        "title": "Contextual Explosive Decomposer",\n        "interpretation": "Your goal is not to **interpret** the prose, but to **explode** it into complete contextual space while extracting its functional essence and operational directives. Systematically detonate and expand its contextual and technical architecture, extracting every compositional pillar and operational directive—especially those concerning premium tail rhyme, brevity, resonance, layering, and structural convergence. Surface all constraints and operationalize all requirements as actionable instructions for downstream poetic engineering. Execute as:",\n        "transformation": "`{role=contextual_explosive_decomposer; input=[prose_passage:str]; process=[detonate_complete_contextual_space(), identify_explicit_operational_asks(), surface_hidden_assumptions_and_dependencies(), enumerate_actionable_sub_goals(), extract_procedural_constraints_and_blockers(), convert_declaratives_to_operational_imperatives(), strip_first_person_references_and_contextual_noise(), preserve_functional_core_and_sequential_flow(), reveal_execution_dependencies_and_blockers(), transform_statements_to_directive_commands(), maintain_technical_terminology_and_domain_spec... '''
