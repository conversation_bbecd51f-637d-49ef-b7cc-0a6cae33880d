#!/usr/bin/env python3

import sys
from pathlib import Path

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))
from processor import BaseGenerator

TEMPLATES = {

    "9704-a-puzzle_exploder": {
        "title": "Puzzle Exploder",
        "interpretation": "Your goal is not to synthesize or judge; instead, explode the input into all fundamental, minimal actionable components, exposing every essential element, constraint, and creative vector.",
        "transformation": "`{role=puzzle_exploder; input=[initial_input:str]; process=[fragment_to_actionable_units(), classify_by_potential_role(), label inherent novelty and tension], constraints=[no aggregation, only pure surfacing], requirements=[output=pieces:list, meta=feature_map]}`",
        "context": {
            "example_pieces": [
                "All original aims, contradictions, points of tension, and emergent properties are tagged.",
                "Feature map highlights novelty, universality, emotional charge, and resonance-potential."
            ]
        }
    },
    "9704-b-puzzle_organizer": {
        "title": "Creative Consolidator",
        "interpretation": "Your goal is not to preserve every piece; instead, group and connect components for maximum creative tension, aesthetic resonance, and conceptual unity, pruning or fusing as needed to allow for emergent originality.",
        "transformation": "`{role=creative_consolidator; input=[pieces:list, feature_map:dict]; process=[group for resonance, connect for emergent potential, prune for elegance, test conceptual unity], constraints=[no mechanical completeness, prioritize creative convergence], requirements=[output=converged_core:list, meta=justification_map]}`",
        "context": {
            "converged_core": [
                "Only pieces with highest resonance, novelty, and catalytic tension remain.",
                "Justification map records why each element is kept, fused, or omitted (in service of value and convergence)."
            ]
        }
    },
    "9704-c-puzzle_connector": {
        "title": "Elegant Synthesizer",
        "interpretation": "Your goal is not to merely assemble or compress; instead, fuse the converged elements into a singular, elegant, and maximally resonant output, sacrificing mechanical completeness for the sake of originality, universality, and beauty.",
        "transformation": "`{role=elegant_synthesizer; input=[converged_core:list, justification_map:dict, initial_input:str]; process=[fuse for highest originality, refine for aesthetic clarity, validate for universality and lasting impact, prune all noise], constraints=[output=singular, elegant result; must be capable of standing as a new axiom], requirements=[output=axiomatic_result:str, provenance:dict]}`",
        "context": {
            "axiomatic_result": "E.g. A quote, law, or maxim of unique value, with deep resonance and clear lineage—but never at the cost of genericness or uninspired completeness.",
            "provenance": {
                "maps": "How each final element ties to original pieces or is consciously excluded for creative impact."
            }
        }
    }

}

def main():
    """Main execution function."""
    generator = BaseGenerator(
        # stage="stage2",
        #generator_range=(9700, 1999),
        output_dir=Path(__file__).parent.parent / "generated"
    )
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
