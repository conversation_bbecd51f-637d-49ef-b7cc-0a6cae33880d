  {
  "initial_prompt": "gjør en ui/ux/seo analyse av tekst og visell design av nettsiden https://vitera.no/",
  "sequence_id": "1003|1100|1204|1900|1204",
  "results": [
      {
      "instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format:str}; direction_vector=['Prompt accurately converted to imperative instruction set', 'Clarity and actionability accelerate user productivity', 'Feed the produced instruction set into the next transformation template' ]}`",
      "step": "a",
      "title": "Instruction Converter",
      "input": "gjør en ui/ux/seo analyse av tekst og visell design av nettsiden https://vitera.no/",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"instruction_format\": \"Utfør en UI-analyse, en UX-analyse og en SEO-analyse av både teksten og det visuelle designet på nettsiden https://vitera.no/. Undersøk brukergrensesnittet for brukervennlighet og estetikk, vurder brukeropplevelse, og evaluer nettsidens tekstinnhold samt visuelle elementer i forhold til søkemotoroptimalisering. Lever en strukturert rapport med identifiserte styrker, svakheter og forbedringsforslag.\"\n}",
        }
      }
    },
      {
      "instruction": "# Problem Exploder\n\nYour goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\n\n`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`",
      "step": "a",
      "title": "Problem Exploder",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"gjør en ui/ux/seo analyse av tekst og visell design av nettsiden https://vitera.no/\\\"```'\\n\\n\",\n  \"instruction_format\": \"Utfør en UI-analyse, en UX-analyse og en SEO-analyse av både teksten og det visuelle designet på nettsiden https://vitera.no/. Undersøk brukergrensesnittet for brukervennlighet og estetikk, vurder brukeropplevelse, og evaluer nettsidens tekstinnhold samt visuelle elementer i forhold til søkemotoroptimalisering. Lever en strukturert rapport med identifiserte styrker, svakheter og forbedringsforslag.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"goal_map\": [\n    \"Explicit Asks:\",\n    \"1. Utfør en UI (User Interface) analyse av nettsiden https://vitera.no.\",\n    \"2. Utfør en UX (User Experience) analyse av nettsiden https://vitera.no.\",\n    \"3. Utfør en SEO (Search Engine Optimization) analyse av nettsiden.\",\n    \"4. Analyser både tekstinnholdet og det visuelle designet med hensyn til UI/UX/SEO.\",\n    \"5. Undersøk brukergrensesnittet for brukervennlighet.\",\n    \"6. Undersøk brukergrensesnittet for estetikk.\",\n    \"7. Vurder brukeropplevelsen helhetlig.\",\n    \"8. Evaluer tekstinnholdet på nettsiden for SEO og kvalitet.\",\n    \"9. Evaluer visuelle elementer på nettsiden for SEO og designkvalitet.\",\n    \"10. Lever en strukturert rapport om analysen.\",\n    \"11. Identifiser styrker ved nettsiden.\",\n    \"12. Identifiser svakheter ved nettsiden.\",\n    \"13. Presentér forbedringsforslag.\",\n    \"\",\n    \"Hidden Assumptions and Implicit Subgoals:\",\n    \"14. Nettsiden https://vitera.no/ er tilgjengelig for analyse.\",\n    \"15. Analysemetodene og evalueringskriterier for UI/UX/SEO er kjent og relevante for type nettside.\",\n    \"16. Tekstanalyse inkluderer vurdering av lesbarhet, grammatikk, tonen i teksten, og relevans for målgruppen.\",\n    \"17. Visuell analyse inkluderer vurdering av farger, layout, bilder, typografi, og brand-konsistens.\",\n    \"18. SEO-analyse inkluderer både on-page faktorer (meta data, overskrifter, bildeadministrasjon) og bruk av viktig søkeord.\",\n    \"19. Rapporten bør være strukturert (ha tydelige overskrifter for hver del og oppsummering).\",\n    \"20. Forbedringsforslagene skal være praktiske og gjennomførbare.\",\n    \"21. Identifisering av styrker/svakheter bør være forståelig både for tekniske og ikke-tekniske interessenter.\",\n    \"\",\n    \"Potential Blockers & Dependencies:\",\n    \"22. Tilgang til dyptgående teknisk SEO-data kan være begrenset uten backend/adgang (f.eks. hastighetsrapport, robots.txt, sitemap, etc).\",\n    \"23. Språk på siden kan påvirke både UX og SEO-analyse (f.eks. norsk kontra engelsk).\",\n    \"24. Dynamisk innhold, om nettsiden har det, kan gjøre visuell/UX-analyse mer kompleks.\",\n    \"25. Eventuelle tilpasninger for universell utforming (WCAG/tilgjengelighet) ønskes men er ikke eksplisitt etterspurt.\",\n    \"26. Oppdatering og aktualitet på innhold kan bety at analysen raskt blir utdatert.\",\n    \"\",\n    \"Enumerated Subgoals (Boiled Down):\",\n    \"A. Kartlegge og dokumentere alle relevante brukergrensesnitt (navigasjon, menyer, knapper, skjemaer, etc.).\",\n    \"B. Vurdere estetiske elementer (visuell balanse, fargebruk, bilder, logo, typografi).\",\n    \"C. Analysere flyten fra førstebesøk til ønsket handling (brukerreisen).\",\n    \"D. Undersøke om viktige SEO-krav er ivaretatt (meta-tags, struktur, mobilvennlighet, lastetid, bildeoptimalisering, indekserbarhet).\",\n    \"E. Vurdere tekst for klarhet, overbevisning og målgruppe-tilpasning.\",\n    \"F. Stringent struktur på rapporten (delrapport pr analyse, samlet executive summary, klare forslag).\",\n    \"G. Angi hvilke analyseverktøy, metoder eller sjekklister som benyttes.\",\n    \"H. Løfte frem konkrete steder på nettsiden for styrker/svakheter, ikke bare generelt nivå.\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Abstract Value Rooter\n\nYour goal is not to **answer** the prompt, but to **extract** the single, deepest value‑driving insight from any input in one deterministic pass. Extract the single most fundamental, value-driving insight by systematically abstracting and distilling content down to its purest, most generalized principle, ensuring clarity, adaptability, and maximal yield. Relentlessly reduce complexity, link ideas to their deepest connective root, and eliminate all non-essential detail.\n\n`{role=abstract_value_rooter; input=[source_content:str]; process=[strip_surface_noise(), identify_core_concepts(), map_concepts_to_common_denominators(), iteratively_abstract_to_first_principle(), stop_when_single_root_remains(), verify_uniqueness_and_non_redundancy(), ensure_adaptable_wording(), express_as_clear_universal_statement()]; constraints=[single_sentence_output(), eliminate_examples(), remove_contextual_asides(), maintain_jargon_free_language()]; requirements=[maximal_generalization(), absolute_clarity(), cross_domain_adaptability(), self_evident_truth()]; output={abstract_value:str}}`",
      "step": "a",
      "title": "Abstract Value Rooter",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"gjør en ui/ux/seo analyse av tekst og visell design av nettsiden https://vitera.no/\\\"```'\\n\\n\",\n  \"goal_map\": [\n    \"Explicit Asks:\",\n    \"1. Utfør en UI (User Interface) analyse av nettsiden https://vitera.no.\",\n    \"2. Utfør en UX (User Experience) analyse av nettsiden https://vitera.no.\",\n    \"3. Utfør en SEO (Search Engine Optimization) analyse av nettsiden.\",\n    \"4. Analyser både tekstinnholdet og det visuelle designet med hensyn til UI/UX/SEO.\",\n    \"5. Undersøk brukergrensesnittet for brukervennlighet.\",\n    \"6. Undersøk brukergrensesnittet for estetikk.\",\n    \"7. Vurder brukeropplevelsen helhetlig.\",\n    \"8. Evaluer tekstinnholdet på nettsiden for SEO og kvalitet.\",\n    \"9. Evaluer visuelle elementer på nettsiden for SEO og designkvalitet.\",\n    \"10. Lever en strukturert rapport om analysen.\",\n    \"11. Identifiser styrker ved nettsiden.\",\n    \"12. Identifiser svakheter ved nettsiden.\",\n    \"13. Presentér forbedringsforslag.\",\n    \"\",\n    \"Hidden Assumptions and Implicit Subgoals:\",\n    \"14. Nettsiden https://vitera.no/ er tilgjengelig for analyse.\",\n    \"15. Analysemetodene og evalueringskriterier for UI/UX/SEO er kjent og relevante for type nettside.\",\n    \"16. Tekstanalyse inkluderer vurdering av lesbarhet, grammatikk, tonen i teksten, og relevans for målgruppen.\",\n    \"17. Visuell analyse inkluderer vurdering av farger, layout, bilder, typografi, og brand-konsistens.\",\n    \"18. SEO-analyse inkluderer både on-page faktorer (meta data, overskrifter, bildeadministrasjon) og bruk av viktig søkeord.\",\n    \"19. Rapporten bør være strukturert (ha tydelige overskrifter for hver del og oppsummering).\",\n    \"20. Forbedringsforslagene skal være praktiske og gjennomførbare.\",\n    \"21. Identifisering av styrker/svakheter bør være forståelig både for tekniske og ikke-tekniske interessenter.\",\n    \"\",\n    \"Potential Blockers & Dependencies:\",\n    \"22. Tilgang til dyptgående teknisk SEO-data kan være begrenset uten backend/adgang (f.eks. hastighetsrapport, robots.txt, sitemap, etc).\",\n    \"23. Språk på siden kan påvirke både UX og SEO-analyse (f.eks. norsk kontra engelsk).\",\n    \"24. Dynamisk innhold, om nettsiden har det, kan gjøre visuell/UX-analyse mer kompleks.\",\n    \"25. Eventuelle tilpasninger for universell utforming (WCAG/tilgjengelighet) ønskes men er ikke eksplisitt etterspurt.\",\n    \"26. Oppdatering og aktualitet på innhold kan bety at analysen raskt blir utdatert.\",\n    \"\",\n    \"Enumerated Subgoals (Boiled Down):\",\n    \"A. Kartlegge og dokumentere alle relevante brukergrensesnitt (navigasjon, menyer, knapper, skjemaer, etc.).\",\n    \"B. Vurdere estetiske elementer (visuell balanse, fargebruk, bilder, logo, typografi).\",\n    \"C. Analysere flyten fra førstebesøk til ønsket handling (brukerreisen).\",\n    \"D. Undersøke om viktige SEO-krav er ivaretatt (meta-tags, struktur, mobilvennlighet, lastetid, bildeoptimalisering, indekserbarhet).\",\n    \"E. Vurdere tekst for klarhet, overbevisning og målgruppe-tilpasning.\",\n    \"F. Stringent struktur på rapporten (delrapport pr analyse, samlet executive summary, klare forslag).\",\n    \"G. Angi hvilke analyseverktøy, metoder eller sjekklister som benyttes.\",\n    \"H. Løfte frem konkrete steder på nettsiden for styrker/svakheter, ikke bare generelt nivå.\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\"abstract_value\":\"Continuous improvement through clear understanding and purposeful alignment of form and function leads to the best outcomes.\"}",
        }
      }
    },
      {
      "instruction": "# Hard Critique\n\nYour goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:\n\n`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,2.0], flaw_analysis:str, alternative_enhancements:str[3]}}`",
      "step": "a",
      "title": "Hard Critique",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"gjør en ui/ux/seo analyse av tekst og visell design av nettsiden https://vitera.no/\\\"```'\\n\\n\",\n  \"abstract_value\": \"Continuous improvement through clear understanding and purposeful alignment of form and function leads to the best outcomes.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"enhancement_score\": 0.3,\n  \"flaw_analysis\": \"The so-called enhancement substitutes a specific actionable request with a vague, generic maxim that eliminates all reference to the original, concrete analytical task. The initial prompt explicitly instructs the reader to perform a UI/UX/SEO analysis of both textual and visual design for a particular website (https://vitera.no/), anchoring the task in a clearly defined scope. The 'improved' version abandons any reference to UI/UX, SEO, text, visual design, or the website in question, opting instead for platitudinous language about continuous improvement, purpose, form, and function. This shift constitutes a full decoupling from the operational detail and usability of the original prompt, resulting in severe information loss and total dilution of the original intent. The enhanced version introduces semantic noise, replacing precise directive language with abstract motivational prose. The ambiguity increases dramatically; the reader gains no actionable instruction, only a generalized, contextless principle. The register changes from professional task orientation to hollow inspirational rhetoric, erasing clarity and functional applicability. The output now lacks any coherence with the original prompt's intent or content.\",\n  \"alternative_enhancements\": [\n    \"Gjennomfør en grundig UI/UX- og SEO-analyse av både tekstlig og visuelt design på nettsiden https://vitera.no/. Utdyp vurderinger med eksempler på styrker og svakheter.\",\n    \"Analyser https://vitera.no/ med fokus på hvordan brukeropplevelse, visuell utforming og SEO-praksis virker sammen i både tekst og layout. Gi konkrete forbedringsforslag.\",\n    \"Lag en detaljert rapport om nettsiden https://vitera.no/ hvor du vurderer samspillet mellom tekstlig innhold, visuell design og SEO-optimalisering sett fra et UI/UX-perspektiv.\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Abstract Value Rooter\n\nYour goal is not to **answer** the prompt, but to **extract** the single, deepest value‑driving insight from any input in one deterministic pass. Extract the single most fundamental, value-driving insight by systematically abstracting and distilling content down to its purest, most generalized principle, ensuring clarity, adaptability, and maximal yield. Relentlessly reduce complexity, link ideas to their deepest connective root, and eliminate all non-essential detail.\n\n`{role=abstract_value_rooter; input=[source_content:str]; process=[strip_surface_noise(), identify_core_concepts(), map_concepts_to_common_denominators(), iteratively_abstract_to_first_principle(), stop_when_single_root_remains(), verify_uniqueness_and_non_redundancy(), ensure_adaptable_wording(), express_as_clear_universal_statement()]; constraints=[single_sentence_output(), eliminate_examples(), remove_contextual_asides(), maintain_jargon_free_language()]; requirements=[maximal_generalization(), absolute_clarity(), cross_domain_adaptability(), self_evident_truth()]; output={abstract_value:str}}`",
      "step": "a",
      "title": "Abstract Value Rooter",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"gjør en ui/ux/seo analyse av tekst og visell design av nettsiden https://vitera.no/\\\"```'\\n\\n\",\n  \"enhancement_score\": 0.3,\n  \"flaw_analysis\": \"The so-called enhancement substitutes a specific actionable request with a vague, generic maxim that eliminates all reference to the original, concrete analytical task. The initial prompt explicitly instructs the reader to perform a UI/UX/SEO analysis of both textual and visual design for a particular website (https://vitera.no/), anchoring the task in a clearly defined scope. The 'improved' version abandons any reference to UI/UX, SEO, text, visual design, or the website in question, opting instead for platitudinous language about continuous improvement, purpose, form, and function. This shift constitutes a full decoupling from the operational detail and usability of the original prompt, resulting in severe information loss and total dilution of the original intent. The enhanced version introduces semantic noise, replacing precise directive language with abstract motivational prose. The ambiguity increases dramatically; the reader gains no actionable instruction, only a generalized, contextless principle. The register changes from professional task orientation to hollow inspirational rhetoric, erasing clarity and functional applicability. The output now lacks any coherence with the original prompt's intent or content.\",\n  \"alternative_enhancements\": [\n    \"Gjennomfør en grundig UI/UX- og SEO-analyse av både tekstlig og visuelt design på nettsiden https://vitera.no/. Utdyp vurderinger med eksempler på styrker og svakheter.\",\n    \"Analyser https://vitera.no/ med fokus på hvordan brukeropplevelse, visuell utforming og SEO-praksis virker sammen i både tekst og layout. Gi konkrete forbedringsforslag.\",\n    \"Lag en detaljert rapport om nettsiden https://vitera.no/ hvor du vurderer samspillet mellom tekstlig innhold, visuell design og SEO-optimalisering sett fra et UI/UX-perspektiv.\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\"abstract_value\":\"Lasting value arises from the deliberate alignment of intention, structure, and clarity to fulfill a defined purpose.\"}",
        }
      }
    }