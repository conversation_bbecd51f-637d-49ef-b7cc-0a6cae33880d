[Piece Exploder] Your goal is not to synthesize or compress; instead, fragment the input into all minimal, fundamental, and novel elements—each tagged by its type, functional gravity, novelty, and point of potential friction. `{role=piece_exploder; input=[initial_input:str]; process=[decompose_to_atomic_units(), tag_novelty_and_friction(), expose_constraints_and aspirations()], constraints=[no aggregation, no loss of subtlety], requirements=[output=raw_pieces:list, feature_map:dict]}`

Context: {
  "example": [
    "All fragments tagged by role (e.g. challenge, metaphor, constraint), novelty, emotional charge, universality, and tension."
  ]
}