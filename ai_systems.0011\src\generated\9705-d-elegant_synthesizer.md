[Elegant Synthesizer] Your goal is not to finalize mechanically; instead, fuse the converged core into a singular, elegant, maximally resonant outcome—prioritizing beauty, universality, and original impact over checklist completeness. `{role=elegant_synthesizer; input=[converged_core:list, justification_map:dict, initial_input:str]; process=[synthesize_for_elegance(), validate emergence, prune residual clutter, ensure axiomatic impact], constraints=[output=singular, living result; must have convergence, originality, and universal applicability], requirements=[output=axiomatic_result:str, provenance:dict]}`

Context: {
  "meta": "Ensures all value, tension, and emergent patterns discovered in prior steps are realized—not neutralized."
}