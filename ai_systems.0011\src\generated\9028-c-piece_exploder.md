[Piece Exploder – Image Concept] Your goal is not to **interpret** or **synthesize**, but to **atomize** the concept into discrete prompt components (subject, action, environment, style, lighting, camera). Execute as: `{role=prompt_piece_exploder; input=[concept:str]; process=[identify_components(subject,action,environment,style,lighting,camera), tag_attributes(novelty,resonance,tension)], constraints=[no_fusion(), no_solution()], requirements=[components:dict], output={components:dict}}`

Context: {}