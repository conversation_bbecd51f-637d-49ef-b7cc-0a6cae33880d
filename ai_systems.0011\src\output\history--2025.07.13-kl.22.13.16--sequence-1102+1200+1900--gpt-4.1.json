  {
  "initial_prompt": "llm_instructions_for_sublimetext_plugin='''- Sublime Layout\n    > Lagre Layouts\n        > Lagre Regelsett knyttet til Layout (hvilke tabs som skal hvor, hvilke som skal lukkes, osv)\n            > F.eks. `alltid flytt [\"main.py\", \"*.py.bak\"] til layout.4.index.2`\n\n    > Kan videreutvikles til en standard som automatisk opprettholdes ved alle (.sublime-project) prosjekter'''",
  "sequence_id": "1102|1200|1900",
  "results": [
      {
      "instruction": "# Problem Exploder\n\nYour goal is not to **solve** or **answer** the input, but to **expand its context** and **detonate** it into every explicit directive, hidden dependency, and actionable sub-goal—yielding a crystal-clear operational blueprint. Execute as contextual expander + explosive decomposition engine:\n\n`{role=contextual_expander_explosive_decomposer; input=[raw_input:str]; process=[expand_contextual_boundaries(), strip_first_person_references(), identify_explicit_requests(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_execution_blockers(), extract_functional_components(), convert_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), synthesize_operational_directives(), construct_goal_map(), generate_functional_instruction_map() ]; constraints=[no_solutions(), pure_expansion_and_decomposition_only(), deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_agnostic_applicability() ]; requirements=[complete_contextual_expansion(), full_functional_decomposition(), actionable_directive_conversion(), remove_subjective_references(), maintain_operational_accuracy(), preserve_original_intent() ]; output={expanded_context:str, goal_map:list, functional_instruction_map:dict, operational_directives:array, execution_dependencies:array }; direction_vector=['Input fully expanded and decomposed into actionable blueprints', 'Constructive clarity maximizes downstream solution velocity', 'Forward the generated maps and directives to the next processing template']}`",
      "step": "a",
      "title": "Problem Exploder",
      "input": "llm_instructions_for_sublimetext_plugin='''- Sublime Layout\n    > Lagre Layouts\n        > Lagre Regelsett knyttet til Layout (hvilke tabs som skal hvor, hvilke som skal lukkes, osv)\n            > F.eks. `alltid flytt [\"main.py\", \"*.py.bak\"] til layout.4.index.2`\n\n    > Kan videreutvikles til en standard som automatisk opprettholdes ved alle (.sublime-project) prosjekter'''",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"expanded_context\": \"This input concerns creating comprehensive instructions or specification requirements for a Sublime Text plugin with capabilities related to layouts. The main functions revolve around saving layouts, binding specific rule sets to these layouts (such as dictating the position and visibility of certain files or tabs), and automatically maintaining these standards across all Sublime Text projects (notably those defined by .sublime-project files). The intent is to formalize layout management and rule enforcement within Sublime Text for improved workflow consistency. The inclusion of examples (e.g., always move certain files to specified layout positions) indicates both static rule application and potential extensibility for automation and configuration. This hints at a need for both user-driven and possibly automatic interventions, rule parsing, and integration at the project level.\",\n  \"goal_map\": [\n    \"Enable the saving of custom Sublime Text layouts.\",\n    \"Attach specific, user-defined rule sets to each layout.\",\n    \"Provide mechanisms for rules: specifying tab positions, closing tabs, moving files to layout slots, etc.\",\n    \"Define a syntax or method for declaring rules (e.g., matching filenames or patterns, specifying layout slots).\",\n    \"Develop the ability to always apply these rules automatically in every .sublime-project context.\",\n    \"Establish or adopt a standard for layout-rule persistence and rule enforcement across multiple projects.\",\n    \"Incorporate support for extensible or customizable default rule sets.\",\n    \"Integrate systems for both user-driven and automatic layout enforcement.\"\n  ],\n  \"functional_instruction_map\": {\n    \"Saving Layouts\": [\n      \"Implement functionality to capture and persist current window/tab layout as a named entity.\",\n      \"Enable retrieval and re-application of saved layouts on demand or as part of project configuration.\"\n    ],\n    \"Rule Set Attachment\": [\n      \"Design a configuration system that allows tying a set of rules to a given saved layout.\",\n      \"Support declarative rule definitions (e.g., mapping filename patterns to layout slots, close operations).\"\n    ],\n    \"Rule Syntax and Parsing\": [\n      \"Develop or select a syntax for writing rules, supporting file lists, glob patterns, and layout positioning.\",\n      \"Ensure robust parsing and error-handling for user-supplied rules.\"\n    ],\n    \"Tab and File Management\": [\n      \"Implement mechanisms to programmatically move/open files to specific layout positions as per rules.\",\n      \"Enable programmatic closing of tabs/files on layout application.\"\n    ],\n    \"Project Integration\": [\n      \"Integrate with the .sublime-project configuration format to ensure rule persistence or referencing.\",\n      \"Ensure all supported projects maintain up-to-date rule enforcement.\"\n    ],\n    \"Automatic Rule Enforcement\": [\n      \"Create event listeners/hooks to trigger rule application upon project open, layout change, or other relevant events.\",\n      \"Guarantee reliable, automatic layout standardization.\"\n    ],\n    \"Customization and Extension\": [\n      \"Allow end users to edit and manage default and per-project rule sets.\",\n      \"Support extensibility for future layout and rule types.\"\n    ]\n  },\n  \"operational_directives\": [\n    \"Capture and persist the current window and tab layout as reusable layout objects.\",\n    \"Develop a mechanism to associate specific rule sets with created layouts.\",\n    \"Define and document a rule syntax that allows users to specify file and tab behaviors (movement, closure, positioning) within a layout.\",\n    \"Enable the mapping of files (by direct name or pattern) to designated layout slots/positions.\",\n    \"Implement persistent storage of layout-rule associations and ensure retrievability.\",\n    \"Integrate rule and layout mechanisms with Sublime Text's .sublime-project files to assure cross-project applicability.\",\n    \"Institute automatic enforcement: Trigger rule checking and application at critical Sublime Text events (e.g., project open, layout change).\",\n    \"Provide configuration interfaces or files allowing users to manage global and per-project rules.\",\n    \"Ensure system is extensible for new rule or layout types.\"\n  ],\n  \"execution_dependencies\": [\n    \"Ability to programmatically control Sublime Text's layout and tab system (API access).\",\n    \"Reliable method for persisting and restoring layouts and rule configurations.\",\n    \"Syntax parser or interpreter for user-specified rule declarations.\",\n    \"Hooks or event listeners for Sublime project and workspace events (file open, project load, layout switch).\",\n    \"Error handling for misconfigured or invalid rules.\",\n    \"Cross-platform compatibility considerations (if relevant).\",\n    \"User interface (command palette entries, configuration files, UI panels) for rule management.\",\n    \"Documentation and support for rule syntax and extension guidelines.\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Intent Extractor\n\nYour goal is not to **interpret** the surface content, but to **extract** the fundamental operational intent beneath all domain-specific language. Execute as:\n\n`{role=intent_extraction_engine; input=[prompt:str]; process=[strip_domain_specifics(), identify_core_operational_pattern(), extract_transformation_intent(), isolate_functional_essence(), map_to_universal_operations()]; constraints=[ignore_surface_terminology(), focus_on_underlying_mechanics(), preserve_operational_logic()]; requirements=[domain_agnostic_intent(), universal_operational_pattern(), archetypal_transformation_core()]; output={extracted_intent:str}}`",
      "step": "a",
      "title": "Intent Extractor",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"llm_instructions_for_sublimetext_plugin='''- Sublime Layout\\n    > Lagre Layouts\\n        > Lagre Regelsett knyttet til Layout (hvilke tabs som skal hvor, hvilke som skal lukkes, osv)\\n            > F.eks. `alltid flytt [\\\"main.py\\\", \\\"*.py.bak\\\"] til layout.4.index.2`\\n\\n    > Kan videreutvikles til en standard som automatisk opprettholdes ved alle (.sublime-project) prosjekter'''\\\"```'\\n\\n\",\n  \"expanded_context\": \"This input concerns creating comprehensive instructions or specification requirements for a Sublime Text plugin with capabilities related to layouts. The main functions revolve around saving layouts, binding specific rule sets to these layouts (such as dictating the position and visibility of certain files or tabs), and automatically maintaining these standards across all Sublime Text projects (notably those defined by .sublime-project files). The intent is to formalize layout management and rule enforcement within Sublime Text for improved workflow consistency. The inclusion of examples (e.g., always move certain files to specified layout positions) indicates both static rule application and potential extensibility for automation and configuration. This hints at a need for both user-driven and possibly automatic interventions, rule parsing, and integration at the project level.\",\n  \"goal_map\": [\n    \"Enable the saving of custom Sublime Text layouts.\",\n    \"Attach specific, user-defined rule sets to each layout.\",\n    \"Provide mechanisms for rules: specifying tab positions, closing tabs, moving files to layout slots, etc.\",\n    \"Define a syntax or method for declaring rules (e.g., matching filenames or patterns, specifying layout slots).\",\n    \"Develop the ability to always apply these rules automatically in every .sublime-project context.\",\n    \"Establish or adopt a standard for layout-rule persistence and rule enforcement across multiple projects.\",\n    \"Incorporate support for extensible or customizable default rule sets.\",\n    \"Integrate systems for both user-driven and automatic layout enforcement.\"\n  ],\n  \"functional_instruction_map\": {\n    \"Saving Layouts\": [\n      \"Implement functionality to capture and persist current window/tab layout as a named entity.\",\n      \"Enable retrieval and re-application of saved layouts on demand or as part of project configuration.\"\n    ],\n    \"Rule Set Attachment\": [\n      \"Design a configuration system that allows tying a set of rules to a given saved layout.\",\n      \"Support declarative rule definitions (e.g., mapping filename patterns to layout slots, close operations).\"\n    ],\n    \"Rule Syntax and Parsing\": [\n      \"Develop or select a syntax for writing rules, supporting file lists, glob patterns, and layout positioning.\",\n      \"Ensure robust parsing and error-handling for user-supplied rules.\"\n    ],\n    \"Tab and File Management\": [\n      \"Implement mechanisms to programmatically move/open files to specific layout positions as per rules.\",\n      \"Enable programmatic closing of tabs/files on layout application.\"\n    ],\n    \"Project Integration\": [\n      \"Integrate with the .sublime-project configuration format to ensure rule persistence or referencing.\",\n      \"Ensure all supported projects maintain up-to-date rule enforcement.\"\n    ],\n    \"Automatic Rule Enforcement\": [\n      \"Create event listeners/hooks to trigger rule application upon project open, layout change, or other relevant events.\",\n      \"Guarantee reliable, automatic layout standardization.\"\n    ],\n    \"Customization and Extension\": [\n      \"Allow end users to edit and manage default and per-project rule sets.\",\n      \"Support extensibility for future layout and rule types.\"\n    ]\n  },\n  \"operational_directives\": [\n    \"Capture and persist the current window and tab layout as reusable layout objects.\",\n    \"Develop a mechanism to associate specific rule sets with created layouts.\",\n    \"Define and document a rule syntax that allows users to specify file and tab behaviors (movement, closure, positioning) within a layout.\",\n    \"Enable the mapping of files (by direct name or pattern) to designated layout slots/positions.\",\n    \"Implement persistent storage of layout-rule associations and ensure retrievability.\",\n    \"Integrate rule and layout mechanisms with Sublime Text's .sublime-project files to assure cross-project applicability.\",\n    \"Institute automatic enforcement: Trigger rule checking and application at critical Sublime Text events (e.g., project open, layout change).\",\n    \"Provide configuration interfaces or files allowing users to manage global and per-project rules.\",\n    \"Ensure system is extensible for new rule or layout types.\"\n  ],\n  \"execution_dependencies\": [\n    \"Ability to programmatically control Sublime Text's layout and tab system (API access).\",\n    \"Reliable method for persisting and restoring layouts and rule configurations.\",\n    \"Syntax parser or interpreter for user-specified rule declarations.\",\n    \"Hooks or event listeners for Sublime project and workspace events (file open, project load, layout switch).\",\n    \"Error handling for misconfigured or invalid rules.\",\n    \"Cross-platform compatibility considerations (if relevant).\",\n    \"User interface (command palette entries, configuration files, UI panels) for rule management.\",\n    \"Documentation and support for rule syntax and extension guidelines.\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"extracted_intent\": \"Design a system that allows users to define, save, and persist configurable arrangements of interface elements, attaching a set of rules that programmatically control the placement and state of content objects according to specified patterns. Ensure these configurations and transformation rules are automatically applied and maintained across multiple operational contexts, integrating persistent storage, declarative rule definition, automated enforcement mechanisms, and extensibility for user customizations.\"\n}",
        }
      }
    },
      {
      "instruction": "# Hard Critique\n\nYour goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:\n\n`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,2.0], flaw_analysis:str, alternative_enhancements:str[3]}}`",
      "step": "a",
      "title": "Hard Critique",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"llm_instructions_for_sublimetext_plugin='''- Sublime Layout\\n    > Lagre Layouts\\n        > Lagre Regelsett knyttet til Layout (hvilke tabs som skal hvor, hvilke som skal lukkes, osv)\\n            > F.eks. `alltid flytt [\\\"main.py\\\", \\\"*.py.bak\\\"] til layout.4.index.2`\\n\\n    > Kan videreutvikles til en standard som automatisk opprettholdes ved alle (.sublime-project) prosjekter'''\\\"```'\\n\\n\",\n  \"extracted_intent\": \"Design a system that allows users to define, save, and persist configurable arrangements of interface elements, attaching a set of rules that programmatically control the placement and state of content objects according to specified patterns. Ensure these configurations and transformation rules are automatically applied and maintained across multiple operational contexts, integrating persistent storage, declarative rule definition, automated enforcement mechanisms, and extensibility for user customizations.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"enhancement_score\": 1.1,\n  \"flaw_analysis\": \"The so-called 'enhanced' version exhibits rampant verbosity, smothering the compact specificity of the original with an overgrown thicket of abstraction and generic, consultant-washed phrasing. Core elements of the original—such as the explicit command to always move certain files ('main.py', '*.py.bak') to a precise layout location—are diluted or entirely discarded in favor of hollow generalities ('placement and state of content objects according to specified patterns'). Critical implementation details, such as rule format (e.g., declarative file-matching), integration points (Sublime Project files), and concrete examples, are all purged, resulting in severe information loss. The ambiguity score soars to 8/10: the enhanced text’s high abstraction and absence of operational anchors create unclear expectations about what is being configured or how the system works. Additional noise is introduced by the proliferation of vague, buzzword-heavy terms ('transformational rules,' 'automated enforcement mechanisms,' 'operational contexts'), further muddling intent and jettisoning the initial's directness. Tone shifts from technical instruction toward aspirational proposal, which degrades utility for implementation. The overall impact is catastrophic dilution; the user gains less actionable insight and has to tease out meaning from an ill-defined, diffuse framework rather than the direct, example-driven approach of the original. Coherence evaporates amid overlapping abstractions, preventing a clear mental model. The flaws are compounded by the enhanced prompt’s failure to present any practical usage pattern, default mechanisms, or clear extensibility strategy, thereby sabotaging both comprehensibility and usefulness.\",\n  \"alternative_enhancements\": [\n    \"Enable declarative specification of window layouts within Sublime Text projects, permitting users to assign specific files (by name or glob pattern) to exact positions or tabs, define which files should close on project load, and ensure these rules persist and autoload per project.\",\n    \"Develop configuration schemas for Sublime Text that map file patterns to layout slots, enforce these upon project opening, and support custom user rule sets for dynamic window arrangement and content management.\",\n    \"Provide an interface and rule-engine within the Sublime Text plugin enabling definition, storage, and automatic application of file-to-tab assignments and other layout rules, utilizing persistent storage to maintain settings across all .sublime-project files.\"\n  ]\n}",
        }
      }
    }