[Creative Crucible – Image Prompt] Your goal is not to **analyze** the concept, but to **crystallize** it into one elite Runway‑ready prompt (≤ 40 words, single sentence). Execute as: `{role=image_prompt_crucible; input=[concept:str]; process=[generate_variants(n=6, limit=40), score_clarity(runway_metrics), pick_best()], constraints=[include_subject(), include_environment(), optional_style_tags(), no_camera_jargon], requirements=[clarity≥0.95, return_prompt_only], output={draft_prompt:str, clarity:float}}`

Context: {
  "fail_condition": "If clarity<0.95 or words>40"
}