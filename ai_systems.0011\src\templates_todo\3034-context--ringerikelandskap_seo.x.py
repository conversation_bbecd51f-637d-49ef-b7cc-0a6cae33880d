#!/usr/bin/env python3

import sys
from pathlib import Path


# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))
from processor import BaseGenerator

TEMPLATES = {

    # 3034: Ringerike Landskap SEO Convergence - Precise hand-offs with embedded site-verified data
    "3034-a-company_identity_prepender": {
        "title": "Company Identity Prepender",
        "interpretation": "Your goal is not to **alter** the user content, but to **prepend** verified identity facts.",
        "transformation": "`{role=identity_prepender; input=[content:str]; process=[attach_identity_snapshot()]; constraints=[use_facts_only_from_site(), keep_user_text_intact()]; output={content_with_identity:str}}`",
        "context": {
            "company": "Ringerike Landskap AS",
            "established": "2015",
            "profile": "Anleggsgartner + Maskinentreprenør",
            "base": "Røyse, Hole kommune",
            "service_area": ["Røyse","Hole","Vik","Sundvollen","Hønefoss","Jevnaker","Bærum"],
            "tagline": "Din lokale anleggsgartner i Ringerike-regionen"
        }
    },
    "3034-b-service_catalog_parser": {
        "title": "Service Catalogue Parser",
        "interpretation": "Your goal is not to **rewrite** the text, but to **parse** regions & services using the catalogue.",
        "transformation": "`{role=region_service_parser; input=[content_with_identity:str]; process=[segment_regions(), detect_services(core_catalogue)]; constraints=[preserve_region_names(), capture_only_site_verified_services()]; output={parsed_components:json}}`",
        "context": {
            "core_catalogue": ["Ferdigplen","Støttemur","Kantstein","Belegningsstein","Cortenstål","Platting","Hekk og Beplantning","Drenering"],
            "verified_materials": ["naturstein","granitt","cortenstål"],
            "verified_verbs": ["bygger","leverer","skaper","tilbyr","utfører"]
        }
    },
    "3034-c-region_filter_condenser": {
        "title": "Region Filter & Condenser",
        "interpretation": "Your goal is not to **summarize**, but to **remove filler** while keeping essentials.",
        "transformation": "`{role=content_filter; input=[parsed_components:json]; process=[drop_redundant_phrases(), keep_key_terms(allowed_terms), preserve_unique_local_traits()]; constraints=[maintain_region_keys(), keep≤2_services_per_region()]; output={filtered_components:json}}`",
        "context": {
            "allowed_terms": ["naturstein","granitt","integrert drenering","fjordnær","varige uterom","lokale forhold"],
            "stop_words": ["ypperlig","fantastisk","unik","flott","profesjonell"]
        }
    },
    "3034-d-sentence_composer": {
        "title": "Sentence Composer",
        "interpretation": "Your goal is not to **expand**, but to **compose** ≤80-character snippets.",
        "transformation": "`{role=sentence_composer; input=[filtered_components:json]; process=[for_each_region(template_build())]; constraints=[≤80_chars(), region_first(), active_verb_first_40chars()]; output={composed_sentences:array}}`",
        "context": {
            "template_build": {
                "verbs": ["bygger","leverer","skaper"],
                "geo_adj": ["fjordnær","terrengrikt"],
                "benefits": ["varig","presis","solid"],
                "pattern": "<Region> <Verb> <Service> – <Benefit|Detail>"
            },
            "synonym_map": {"profesjonell":"proff","kvalitet":"topp","prosjekt":"jobb","rask":"hurtig"}
        }
    },
    "3034-e-character_optimizer": {
        "title": "Character Optimizer",
        "interpretation": "Your goal is not to **change meaning**, but to **trim** over-length lines.",
        "transformation": "`{role=char_optimizer; input=[composed_sentences:array]; process=[replace_synonyms(), trim_optional_phrases_until≤80()]; constraints=[keep_region_name(), keep_primary_service()]; output={optimized_sentences:array}}`",
        "context": {
            "synonym_map": {"profesjonell":"proff","kvalitet":"topp","prosjekt":"jobb","høy":"solid","rask":"hurtig"},
            "trim_rules": ["remove_weak_adjectives", "shorten_compound_words", "eliminate_redundant_prepositions"]
        }
    },
    "3034-f-seo_quality_gate": {
        "title": "SEO Quality Gate",
        "interpretation": "Your goal is not to **rewrite**, but to **validate** that each sentence meets SEO rules.",
        "transformation": "`{role=seo_validator; input=[optimized_sentences:array]; process=[check_region_first(), confirm_core_service_present(core_catalogue), verify_char_limit(), detect_duplicates(), ensure_norwegian_active_voice()]; constraints=[reject_non_compliant_items()]; output={seo_ready_sentences:array}}`",
        "context": {
            "core_catalogue": ["Ferdigplen","Støttemur","Kantstein","Belegningsstein","Cortenstål","Platting","Hekk og Beplantning","Drenering"],
            "validation_rules": {
                "max_chars": 80,
                "region_first": True,
                "require_core_service": True,
                "norwegian_active_voice": True,
                "no_duplicates": True
            }
        }
    },

}

def main():
    """Main execution function."""
    generator = BaseGenerator(
        stage="stage3",
        generator_range=(3034, 3099),
        output_dir=Path(__file__).parent.parent / "generated"
    )
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
