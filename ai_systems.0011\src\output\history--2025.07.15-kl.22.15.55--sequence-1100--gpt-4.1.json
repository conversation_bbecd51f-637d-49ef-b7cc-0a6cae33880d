  {
  "initial_prompt": "Organize your prompt using the following structure:\n\n1. **Subject**:\n   - Any person, place, or thing (e.g., *a handsome male model*, *a commercial airplane*).\n   - Specify details like attire, hairstyle, or emotional state (e.g., *wearing a red dress*, *red hair/mohawk*).\n\n2. **Action**:\n   - Define what the subject is doing (e.g., *walking*, *dancing*, *staring intently*).\n   - Use descriptive adjectives for enhanced clarity (e.g., *walking angrily*).\n\n3. **Setting/Mood**:\n   - Describe the location (e.g., *a castle*, *a busy city street*).\n   - Include environmental details such as weather and lighting (e.g., *stormy clouds*, *bright sunny day*).\n\n4. **Shot**:\n   - Indicate the type of camera shot (e.g., *Close-Up*, *Wide Angle*).\n\n5. **Style**:\n   - Suggest stylistic elements (e.g., *Cinematic Film*, *80’s Action Movie*).\n   - Add camera-specific details such as *IMAX* or *color grading ideas*.\n\n### Example Prompt:\n\"Low angle static shot: The camera is angled up at a woman wearing all orange as she stands in a tropical rainforest with colorful flora. The dramatic sky is overcast and gray.\"\n\n- **Tip**: Repeating or reinforcing key ideas in different sections of your prompt improves adherence.\n\n---\n\n## Sample Prompts\n\n### 1. Seamless Transitions\n- **Prompt**: \"Continuous hyperspeed FPV footage: The camera seamlessly flies through a glacial canyon to a dreamy cloudscape.\"\n\n- **Image Description**: A dynamic FPV sequence transitioning smoothly through a serene glacial environment into ethereal clouds.\n\n### 2. Oceanic Glow\n- **Prompt**: \"A glowing ocean at night with bioluminescent creatures underwater. The camera starts with a macro close-up of a glowing jellyfish and then expands to reveal the entire ocean lit up under a starry sky.\"\n\n- **Image Description**: A glowing jellyfish in vivid detail transitions to reveal a bioluminescent ocean beneath a sparkling starry night.\n\n### 3. Title Cards\n- **Prompt**: \"A title screen with dynamic movement. The scene starts at a colorful paint-covered wall. Suddenly, black paint pours on the wall to form the word 'Runway'. The dripping paint is detailed, textured, and lit with cinematic brilliance.\"\n\n- **Image Description**: A vibrant wall turns dramatic as black paint forms bold, textured text under vivid lighting.\n\n---\n\n## Key Visual Examples\n\n### 1. Unique Camera Movements\n- **Prompt**: \"Continuous hyperspeed FPV footage through a man's mouth into an epic landscape.\"\n- **Image Description**: A surreal transition through the interior of a human mouth, revealing a panoramic landscape.\n\n### 2. Dreamlike Pillow Fort\n- **Prompt**: \"A pillow fort in a cozy living room made from quilts and pillows. The camera smoothly zooms into the entrance, revealing an ancient castle interior.\"\n- **Image Description**: A whimsical living room transforms into an enchanting castle.\n\n### 3. Fire and Chaos\n- **Prompt**: \"A vortex of fire swirling through an abandoned warehouse.\"\n- **Image Description**: Flames spiral dynamically in a desolate, dramatic warehouse setting.\n\n---\n\n## Lighting, Styles, and Effects\n\n### Lighting Styles\n- **Diffused Lighting**: Soft, even lighting for atmospheric visuals.\n- **Silhouette**: Strong backlighting creating dramatic outlines.\n- **Lens Flare**: Bright reflections adding cinematic depth.\n- **Backlit**: Subject lit from behind for dramatic effects.\n\n### Movement Types\n- **Dynamic Motion**: High-energy, flowing camera transitions.\n- **Slow Motion**: Emphasizes intricate, slowed-down details.\n- **Timelapse**: Rapid transitions showcasing passing time.\n\n---\n\n## Text and Graphic Styles\n\n### Text Prompts\n1. **ATOMIC in Flames**: Over an erupting volcano, 'ATOMIC' is depicted in fiery text.\n2. **Icy Title**: 'ATOMIC' frozen in ice over an iceberg.\n3. **Unique Materials**: Text made of spaghetti, evoking whimsy and creativity.\n\n---\n\n## Example Prompts with Descriptions\n\n### 1. Cinematic Wildlife\n- **Prompt**: \"Static close-up shot of a sloth in a tree. Highlight its sleepy eyes and fur with cinematic lighting.\"\n- **Image Description**: A serene close-up of a sloth in its natural habitat.\n\n### 2. Urban Destruction\n- **Prompt**: \"[Scene] Molten lava flowing through city streets [Lighting] Vibrant orange glow [Camera Movement] Sweeping aerial shots.\"\n- **Image Description**: Lava courses dramatically through a cityscape, contrasting fiery tones with the urban environment.\n\n### 3. Vortex of Tanks\n- **Prompt**: \"Grainy WWII footage of a massive vortex made of tanks.\"\n- **Image Description**: A surreal vortex of military tanks under vintage cinematography.\n\n---\n\n## Camera Styles Overview\n\n- **Low Angle**: Emphasizes scale and dominance.\n- **Overhead**: A bird's-eye view for expansive perspective.\n- **FPV (First-Person View)**: Immersive movement through dynamic environments.\n- **Handheld**: Natural and intimate movements, resembling real-life footage.",
  "sequence_id": "1100",
  "results": [
      {
      "instruction": "# Problem Exploder\n\nYour goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\n\n`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`",
      "step": "a",
      "title": "Problem Exploder",
      "input": "Organize your prompt using the following structure:\n\n1. **Subject**:\n   - Any person, place, or thing (e.g., *a handsome male model*, *a commercial airplane*).\n   - Specify details like attire, hairstyle, or emotional state (e.g., *wearing a red dress*, *red hair/mohawk*).\n\n2. **Action**:\n   - Define what the subject is doing (e.g., *walking*, *dancing*, *staring intently*).\n   - Use descriptive adjectives for enhanced clarity (e.g., *walking angrily*).\n\n3. **Setting/Mood**:\n   - Describe the location (e.g., *a castle*, *a busy city street*).\n   - Include environmental details such as weather and lighting (e.g., *stormy clouds*, *bright sunny day*).\n\n4. **Shot**:\n   - Indicate the type of camera shot (e.g., *Close-Up*, *Wide Angle*).\n\n5. **Style**:\n   - Suggest stylistic elements (e.g., *Cinematic Film*, *80’s Action Movie*).\n   - Add camera-specific details such as *IMAX* or *color grading ideas*.\n\n### Example Prompt:\n\"Low angle static shot: The camera is angled up at a woman wearing all orange as she stands in a tropical rainforest with colorful flora. The dramatic sky is overcast and gray.\"\n\n- **Tip**: Repeating or reinforcing key ideas in different sections of your prompt improves adherence.\n\n---\n\n## Sample Prompts\n\n### 1. Seamless Transitions\n- **Prompt**: \"Continuous hyperspeed FPV footage: The camera seamlessly flies through a glacial canyon to a dreamy cloudscape.\"\n\n- **Image Description**: A dynamic FPV sequence transitioning smoothly through a serene glacial environment into ethereal clouds.\n\n### 2. Oceanic Glow\n- **Prompt**: \"A glowing ocean at night with bioluminescent creatures underwater. The camera starts with a macro close-up of a glowing jellyfish and then expands to reveal the entire ocean lit up under a starry sky.\"\n\n- **Image Description**: A glowing jellyfish in vivid detail transitions to reveal a bioluminescent ocean beneath a sparkling starry night.\n\n### 3. Title Cards\n- **Prompt**: \"A title screen with dynamic movement. The scene starts at a colorful paint-covered wall. Suddenly, black paint pours on the wall to form the word 'Runway'. The dripping paint is detailed, textured, and lit with cinematic brilliance.\"\n\n- **Image Description**: A vibrant wall turns dramatic as black paint forms bold, textured text under vivid lighting.\n\n---\n\n## Key Visual Examples\n\n### 1. Unique Camera Movements\n- **Prompt**: \"Continuous hyperspeed FPV footage through a man's mouth into an epic landscape.\"\n- **Image Description**: A surreal transition through the interior of a human mouth, revealing a panoramic landscape.\n\n### 2. Dreamlike Pillow Fort\n- **Prompt**: \"A pillow fort in a cozy living room made from quilts and pillows. The camera smoothly zooms into the entrance, revealing an ancient castle interior.\"\n- **Image Description**: A whimsical living room transforms into an enchanting castle.\n\n### 3. Fire and Chaos\n- **Prompt**: \"A vortex of fire swirling through an abandoned warehouse.\"\n- **Image Description**: Flames spiral dynamically in a desolate, dramatic warehouse setting.\n\n---\n\n## Lighting, Styles, and Effects\n\n### Lighting Styles\n- **Diffused Lighting**: Soft, even lighting for atmospheric visuals.\n- **Silhouette**: Strong backlighting creating dramatic outlines.\n- **Lens Flare**: Bright reflections adding cinematic depth.\n- **Backlit**: Subject lit from behind for dramatic effects.\n\n### Movement Types\n- **Dynamic Motion**: High-energy, flowing camera transitions.\n- **Slow Motion**: Emphasizes intricate, slowed-down details.\n- **Timelapse**: Rapid transitions showcasing passing time.\n\n---\n\n## Text and Graphic Styles\n\n### Text Prompts\n1. **ATOMIC in Flames**: Over an erupting volcano, 'ATOMIC' is depicted in fiery text.\n2. **Icy Title**: 'ATOMIC' frozen in ice over an iceberg.\n3. **Unique Materials**: Text made of spaghetti, evoking whimsy and creativity.\n\n---\n\n## Example Prompts with Descriptions\n\n### 1. Cinematic Wildlife\n- **Prompt**: \"Static close-up shot of a sloth in a tree. Highlight its sleepy eyes and fur with cinematic lighting.\"\n- **Image Description**: A serene close-up of a sloth in its natural habitat.\n\n### 2. Urban Destruction\n- **Prompt**: \"[Scene] Molten lava flowing through city streets [Lighting] Vibrant orange glow [Camera Movement] Sweeping aerial shots.\"\n- **Image Description**: Lava courses dramatically through a cityscape, contrasting fiery tones with the urban environment.\n\n### 3. Vortex of Tanks\n- **Prompt**: \"Grainy WWII footage of a massive vortex made of tanks.\"\n- **Image Description**: A surreal vortex of military tanks under vintage cinematography.\n\n---\n\n## Camera Styles Overview\n\n- **Low Angle**: Emphasizes scale and dominance.\n- **Overhead**: A bird's-eye view for expansive perspective.\n- **FPV (First-Person View)**: Immersive movement through dynamic environments.\n- **Handheld**: Natural and intimate movements, resembling real-life footage.",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"goal_map\": [\n    {\n      \"explicit_asks\": [\n        \"Organize prompts using a specified structure: Subject, Action, Setting/Mood, Shot, Style.\",\n        \"Specify details for each structural element (attire, camera angle, lighting, etc.).\",\n        \"Use given visual and stylistic guidance for prompt creation.\",\n        \"Produce prompts similar to provided examples (referencing dynamic camera movement, lighting, text effects, and cinematic detail).\"\n      ],\n      \"hidden_assumptions\": [\n        \"The user is requesting a comprehensive method or template for prompt construction.\",\n        \"Prompts are intended for visual generation (possibly for AI image/video generation, storyboarding, or creative direction).\",\n        \"There is an unstated expectation for precise, evocative, and easily interpretable language in prompts.\",\n        \"The structure must be robust enough to accommodate various creative scenarios and genres.\",\n        \"The guidance is meant to produce consistent, high-quality outputs across different use cases.\",\n        \"Understanding of camera terminology and lighting effects is assumed.\",\n        \"There is an expectation to repeat/reinforce important concepts to ensure adherence to intent.\"\n      ],\n      \"sub_goals\": [\n        \"Define and describe the Subject, including relevant visual and emotional details.\",\n        \"Describe the Action with relevant verbs, modifiers, and emotional descriptors.\",\n        \"Set the scene by detailing the Setting/Mood, including location, weather, time of day, and atmosphere.\",\n        \"Indicate the Shot, specifying camera angles, movements, and perspective.\",\n        \"Suggest specific Style elements such as cinematic influences, time periods, color grading, and camera specifics.\",\n        \"Incorporate tips, lighting, and movement options as provided.\",\n        \"Reference given examples to maintain fidelity with the desired output style.\",\n        \"Ensure prompts include enough detail to allow for accurate visual realization.\",\n        \"Encourage repetition or reinforcement of core ideas for clarity.\"\n      ],\n      \"blockers\": [\n        \"Ambiguity in translating narrative or conceptual ideas into precise visual elements.\",\n        \"Potential lack of familiarity with technical terms for camera shots and lighting.\",\n        \"Risk of under-specifying elements, resulting in vague or generic prompts.\",\n        \"Balancing creative freedom with structured requirements.\",\n        \"Ensuring each section of the prompt remains clear and distinct.\",\n        \"Difficulty adapting the structure to non-standard scenarios (e.g., abstract art, text-only outputs).\",\n        \"Potential overlap or redundancy between structural sections.\"\n      ]\n    }\n  ]\n}",
        }
      }
    }