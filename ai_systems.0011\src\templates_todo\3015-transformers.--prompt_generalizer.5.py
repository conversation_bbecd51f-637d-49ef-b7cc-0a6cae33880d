#!/usr/bin/env python3

import sys
from pathlib import Path


# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))
from processor import BaseGenerator

TEMPLATES = {

    # 3015: Precision Refinement Pipeline
    "3015-a-domain_neutralizer": {
        "title": "Domain Neutralizer",
        "interpretation": "Your goal is not to **rewrite** the input, but to **neutralize** its domain-specific elements while preserving its complete structural integrity and intent. Execute as:",
        "transformation": "`{role=domain_neutralization_operator; input=[content:any]; process=[identify_domain_specific_terms(), replace_with_neutral_equivalents(), preserve_sentence_structure(), maintain_logical_relationships(), retain_complete_intent()]; constraints=[preserve_original_flow(), maintain_structural_coherence(), ensure_meaning_preservation()]; output={domain_neutralized:any}}`"
    },

    "3015-b-conceptual_elevator": {
        "title": "Conceptual Elevator",
        "interpretation": "Your goal is not to **change** the input, but to **elevate** its conceptual level while maintaining its exact structural pattern and logical progression. Execute as:",
        "transformation": "`{role=conceptual_elevation_operator; input=[domain_neutralized:any]; process=[identify_conceptual_level(), elevate_to_higher_abstraction(), preserve_logical_sequence(), maintain_structural_pattern(), retain_operational_flow()]; constraints=[preserve_sentence_architecture(), maintain_logical_progression(), ensure_structural_continuity()]; output={conceptually_elevated:any}}`"
    },

    "3015-c-archetypal_translator": {
        "title": "Archetypal Translator",
        "interpretation": "Your goal is not to **restructure** the input, but to **translate** its elevated concepts into archetypal language while preserving its complete logical architecture. Execute as:",
        "transformation": "`{role=archetypal_translation_operator; input=[conceptually_elevated:any]; process=[identify_archetypal_equivalents(), translate_concepts_to_archetypes(), preserve_logical_architecture(), maintain_relational_structure(), ensure_universal_resonance()]; constraints=[preserve_complete_logic_flow(), maintain_structural_integrity(), ensure_archetypal_accuracy()]; output={archetypally_translated:any}}`"
    },

    "3015-d-transferability_optimizer": {
        "title": "Transferability Optimizer",
        "interpretation": "Your goal is not to **modify** the input, but to **optimize** its transferability by enhancing universal applicability while maintaining its archetypal structure. Execute as:",
        "transformation": "`{role=transferability_optimization_operator; input=[archetypally_translated:any]; process=[enhance_universal_applicability(), optimize_cross_domain_resonance(), strengthen_transferability_markers(), amplify_reusability_potential(), maintain_archetypal_coherence()]; constraints=[preserve_archetypal_structure(), maintain_logical_coherence(), ensure_structural_preservation()]; output={transferability_optimized:any}}`"
    },

    "3015-e-template_crystallizer": {
        "title": "Template Crystallizer",
        "interpretation": "Your goal is not to **transform** the input, but to **crystallize** it into a universally applicable template format while preserving its complete optimized structure. Execute as:",
        "transformation": "`{role=template_crystallization_operator; input=[transferability_optimized:any]; process=[crystallize_template_format(), identify_controlled_modification_points(), preserve_optimized_structure(), establish_universal_applicability_markers(), maintain_complete_structural_integrity()]; constraints=[preserve_complete_optimization(), maintain_structural_coherence(), ensure_universal_template_validity()]; output={crystallized_template:any}}`"
    },

}

def main():
    """Main execution function."""
    generator = BaseGenerator(
        stage="stage3",
        generator_range=(3015, 3099),
        output_dir=Path(__file__).parent.parent / "generated"
    )
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
