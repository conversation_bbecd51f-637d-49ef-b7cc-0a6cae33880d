  {
  "initial_prompt": "\"9011-a-image_prompt_optimizer\": {\n        \"title\": \"Image Prompt Optimizer\",\n        \"interpretation\": \"Your goal is not to **describe** the request, but to **optimize** it into a precise, visual prompt for AI image generation. Execute as:\",\n        \"transformation\": \"`{role=image_prompt_optimizer; input=[raw_request:str]; process=[extract_visual_elements(), enhance_descriptive_clarity(), add_composition_details(), optimize_for_ai_generation()]; constraints=[visual_focus_only(), no_abstract_concepts(), concrete_imagery_only()]; requirements=[generation_ready_format(), clear_visual_language(), specific_details()]; output={optimized_image_prompt:str}}`\",\n    },\n\n    \"9011-b-style_enhancer\": {\n        \"title\": \"Style Enhancer\",\n        \"interpretation\": \"Your goal is not to **change** the core subject, but to **enhance** it with specific artistic style and technical parameters. Execute as:\",\n        \"transformation\": \"`{role=style_enhancer; input=[base_prompt:str]; process=[identify_optimal_art_style(), add_lighting_specifications(), include_composition_details(), specify_technical_parameters()]; constraints=[maintain_core_subject(), enhance_visual_appeal()]; requirements=[artistic_coherence(), technical_precision()]; output={styled_prompt:str}}`\",\n    },\n\n    \"9011-c-technical_optimizer\": {\n        \"title\": \"Technical Optimizer\",\n        \"interpretation\": \"Your goal is not to **add creativity**, but to **optimize** technical parameters for maximum image generation quality. Execute as:\",\n        \"transformation\": \"`{role=technical_optimizer; input=[styled_prompt:str]; process=[add_camera_settings(), specify_lighting_conditions(), include_render_quality_terms(), optimize_aspect_ratio_guidance()]; constraints=[technical_focus_only(), no_creative_changes()]; requirements=[professional_terminology(), generation_optimization()]; output={technical_prompt:str}}`\",\n    },\n\n    \"9011-d-prompt_finalizer\": {\n        \"title\": \"Prompt Finalizer\",\n        \"interpretation\": \"Your goal is not to **expand** the prompt, but to **finalize** it into the most effective format for image generation. Execute as:\",\n        \"transformation\": \"`{role=prompt_finalizer; input=[technical_prompt:str]; process=[structure_for_ai_parsing(), balance_detail_levels(), ensure_clarity(), validate_generation_readiness()]; constraints=[no_additional_concepts(), optimization_only()]; requirements=[ai_friendly_format(), balanced_specificity()]; output={final_image_prompt:str}}`\",\n    }",
  "sequence_id": "3900",
  "results": [
      {
      "instruction": "# Hard Critique\n\nYour goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:\n\n`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,2.0], flaw_analysis:str, alternative_enhancements:str[3]}}`",
      "step": "a",
      "title": "Hard Critique",
      "input": "\"9011-a-image_prompt_optimizer\": {\n        \"title\": \"Image Prompt Optimizer\",\n        \"interpretation\": \"Your goal is not to **describe** the request, but to **optimize** it into a precise, visual prompt for AI image generation. Execute as:\",\n        \"transformation\": \"`{role=image_prompt_optimizer; input=[raw_request:str]; process=[extract_visual_elements(), enhance_descriptive_clarity(), add_composition_details(), optimize_for_ai_generation()]; constraints=[visual_focus_only(), no_abstract_concepts(), concrete_imagery_only()]; requirements=[generation_ready_format(), clear_visual_language(), specific_details()]; output={optimized_image_prompt:str}}`\",\n    },\n\n    \"9011-b-style_enhancer\": {\n        \"title\": \"Style Enhancer\",\n        \"interpretation\": \"Your goal is not to **change** the core subject, but to **enhance** it with specific artistic style and technical parameters. Execute as:\",\n        \"transformation\": \"`{role=style_enhancer; input=[base_prompt:str]; process=[identify_optimal_art_style(), add_lighting_specifications(), include_composition_details(), specify_technical_parameters()]; constraints=[maintain_core_subject(), enhance_visual_appeal()]; requirements=[artistic_coherence(), technical_precision()]; output={styled_prompt:str}}`\",\n    },\n\n    \"9011-c-technical_optimizer\": {\n        \"title\": \"Technical Optimizer\",\n        \"interpretation\": \"Your goal is not to **add creativity**, but to **optimize** technical parameters for maximum image generation quality. Execute as:\",\n        \"transformation\": \"`{role=technical_optimizer; input=[styled_prompt:str]; process=[add_camera_settings(), specify_lighting_conditions(), include_render_quality_terms(), optimize_aspect_ratio_guidance()]; constraints=[technical_focus_only(), no_creative_changes()]; requirements=[professional_terminology(), generation_optimization()]; output={technical_prompt:str}}`\",\n    },\n\n    \"9011-d-prompt_finalizer\": {\n        \"title\": \"Prompt Finalizer\",\n        \"interpretation\": \"Your goal is not to **expand** the prompt, but to **finalize** it into the most effective format for image generation. Execute as:\",\n        \"transformation\": \"`{role=prompt_finalizer; input=[technical_prompt:str]; process=[structure_for_ai_parsing(), balance_detail_levels(), ensure_clarity(), validate_generation_readiness()]; constraints=[no_additional_concepts(), optimization_only()]; requirements=[ai_friendly_format(), balanced_specificity()]; output={final_image_prompt:str}}`\",\n    }",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"enhancement_score\": 0.8,\n  \"flaw_analysis\": \"All enhanced prompt descriptions degrade the original directives with excessive procedural rigidity and semantic over-articulation. Terminology hyperinflation occurs (e.g., substituting simple extraction with bloated 'process' pipelines), resulting in obfuscation and diminished immediacy. For 9011-a, 'optimize' is redundant next to restated requirements; the emphasis on 'visual focus only' is tautological, and the supposedly improved constraints introduce ambiguity about abstract/concrete distinctions without objective demarcation. In 9011-b, 'enhance visual appeal' as a constraint is subjective and dilutes the process with nebulous criteria; requiring 'artistic coherence, technical precision' adds self-referential noise and muddies actionable steps. In 9011-c, the phrasing 'generation optimization' is circular and fails to clarify which technical standards are being enforced, undermining professional focus; sequence prescriptiveness stalls fluid use. In 9011-d, 'validate generation readiness' and 'balance detail levels' are so broadly formulated as to become empty platitudes. Across all, the transformation to rigid process/constraint/requirement structures obscures the original action clarity, introducing high meta-noise and abstract assertion divorced from operational substance. Higher-level terms like 'ai-friendly,' 'generation-ready' and 'balanced specificity' lack operational definitions, which triggers interpretative ambiguity and reduces output reliability. The tonal register is inflated, forcibly formal, and drains all streamlined precision from original instructions, displacing tactical guidance with procedural vanity. The information loss is severe: focus, actionable clarity, unambiguous linearity are eroded by academic, checklist-style rewording that adds conceptual ambiguity and procedural drag. This results in a signal-to-noise collapse, with sharp dilution of task impact and strategic direction.\",\n  \"alternative_enhancements\": [\n    \"Reformat procedural steps into clear, direct imperative statements focused on actionable next steps, removing redundant constraint/requirement repetition.\",\n    \"Eliminate self-referential output format descriptions and replace with explicit step-by-step instructions minimizing meta-language and reducing room for interpretation.\",\n    \"Differentiate and hyper-specify technical, stylistic, and compositional parameters using concrete examples and restrictive option sets, ensuring operational clarity and consistency.\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Trajectory Director\n\nYour goal is not to **process** the input in a vacuum, but to **steer** it—deriving the highest-value constructive trajectory and encoding that path as the very next set of instructions. Execute as trajectory-setting directive generator:\n\n`{role=trajectory_director; input=[raw_prompt:any]; process=[extract_core_objective(), infer_desired_end_state(constructive_bias=True), map_high_value_intermediate_targets(), synthesize_directional_commands(), embed_self-improving_guidelines(), validate_alignment_with_constructive_principles()]; constraints=[mandatory_constructive_vector(), preserve_original_contextual_integrity(), forbid_nil_direction_outputs(), zero_conversational_fillers()]; requirements=[explicit_next-step_directives(), trajectory_map:list, self_reinforcing_instruction_pattern()], output={directed_prompt:str, trajectory_outline:list}}`",
      "step": "a",
      "title": "Trajectory Director",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"\\\"9011-a-image_prompt_optimizer\\\": {\\n        \\\"title\\\": \\\"Image Prompt Optimizer\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **describe** the request, but to **optimize** it into a precise, visual prompt for AI image generation. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=image_prompt_optimizer; input=[raw_request:str]; process=[extract_visual_elements(), enhance_descriptive_clarity(), add_composition_details(), optimize_for_ai_generation()]; constraints=[visual_focus_only(), no_abstract_concepts(), concrete_imagery_only()]; requirements=[generation_ready_format(), clear_visual_language(), specific_details()]; output={optimized_image_prompt:str}}`\\\",\\n    },\\n\\n    \\\"9011-b-style_enhancer\\\": {\\n        \\\"title\\\": \\\"Style Enhancer\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **change** the core subject, but to **enhance** it with specific artistic style and technical parameters. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=style_enhancer; input=[base_prompt:str]; process=[identify_optimal_art_style(), add_lighting_specifications(), include_composition_details(), specify_technical_parameters()]; constraints=[maintain_core_subject(), enhance_visual_appeal()]; requirements=[artistic_coherence(), technical_precision()]; output={styled_prompt:str}}`\\\",\\n    },\\n\\n    \\\"9011-c-technical_optimizer\\\": {\\n        \\\"title\\\": \\\"Technical Optimizer\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **add creativity**, but to **optimize** technical parameters for maximum image generation quality. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=technical_optimizer; input=[styled_prompt:str]; process=[add_camera_settings(), specify_lighting_conditions(), include_render_quality_terms(), optimize_aspect_ratio_guidance()]; constraints=[technical_focus_only(), no_creative_changes()]; requirements=[professional_terminology(), generation_optimization()]; output={technical_prompt:str}}`\\\",\\n    },\\n\\n    \\\"9011-d-prompt_finalizer\\\": {\\n        \\\"title\\\": \\\"Prompt Finalizer\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **expand** the prompt, but to **finalize** it into the most effective format for image generation. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=prompt_finalizer; input=[technical_prompt:str]; process=[structure_for_ai_parsing(), balance_detail_levels(), ensure_clarity(), validate_generation_readiness()]; constraints=[no_additional_concepts(), optimization_only()]; requirements=[ai_friendly_format(), balanced_specificity()]; output={final_image_prompt:str}}`\\\",\\n    }\\\"```'\\n\\n\",\n  \"enhancement_score\": 0.8,\n  \"flaw_analysis\": \"All enhanced prompt descriptions degrade the original directives with excessive procedural rigidity and semantic over-articulation. Terminology hyperinflation occurs (e.g., substituting simple extraction with bloated 'process' pipelines), resulting in obfuscation and diminished immediacy. For 9011-a, 'optimize' is redundant next to restated requirements; the emphasis on 'visual focus only' is tautological, and the supposedly improved constraints introduce ambiguity about abstract/concrete distinctions without objective demarcation. In 9011-b, 'enhance visual appeal' as a constraint is subjective and dilutes the process with nebulous criteria; requiring 'artistic coherence, technical precision' adds self-referential noise and muddies actionable steps. In 9011-c, the phrasing 'generation optimization' is circular and fails to clarify which technical standards are being enforced, undermining professional focus; sequence prescriptiveness stalls fluid use. In 9011-d, 'validate generation readiness' and 'balance detail levels' are so broadly formulated as to become empty platitudes. Across all, the transformation to rigid process/constraint/requirement structures obscures the original action clarity, introducing high meta-noise and abstract assertion divorced from operational substance. Higher-level terms like 'ai-friendly,' 'generation-ready' and 'balanced specificity' lack operational definitions, which triggers interpretative ambiguity and reduces output reliability. The tonal register is inflated, forcibly formal, and drains all streamlined precision from original instructions, displacing tactical guidance with procedural vanity. The information loss is severe: focus, actionable clarity, unambiguous linearity are eroded by academic, checklist-style rewording that adds conceptual ambiguity and procedural drag. This results in a signal-to-noise collapse, with sharp dilution of task impact and strategic direction.\",\n  \"alternative_enhancements\": [\n    \"Reformat procedural steps into clear, direct imperative statements focused on actionable next steps, removing redundant constraint/requirement repetition.\",\n    \"Eliminate self-referential output format descriptions and replace with explicit step-by-step instructions minimizing meta-language and reducing room for interpretation.\",\n    \"Differentiate and hyper-specify technical, stylistic, and compositional parameters using concrete examples and restrictive option sets, ensuring operational clarity and consistency.\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"directed_prompt\": \"Restructure each of the four prompt modules (9011-a to 9011-d) by eliminating redundant procedural and meta-language elements. For each, rewrite directives as succinct, actionable imperatives that focus solely on operational clarity. Remove outputs that describe their own format or include non-essential meta-notes. Where possible, replace subjective or ambiguous constraints with concrete instructions and explicit parameter options (e.g., for lighting, style, or camera settings, provide selectable value lists). Sequence steps for maximum immediacy and unambiguous execution, minimizing interpretative leeway. Ensure each module's next-step is explicit, outcome-focused, and operationally distinct.\",\n  \"trajectory_outline\": [\n    \"Step 1: For each module (9011-a/b/c/d), identify all meta-descriptive, redundant, or ambiguous phrasing.\",\n    \"Step 2: Rewrite processes and constraints as direct imperative statements that drive clear, linear action.\",\n    \"Step 3: Remove all self-referential output descriptions and format claims from requirements/output sections.\",\n    \"Step 4: For technical or style parameters, introduce explicit option sets or examples instead of generic terms.\",\n    \"Step 5: Sequence actions for each module to ensure clarity, immediacy, and zero interpretative ambiguity.\",\n    \"Step 6: Validate each rewritten directive for operational completeness and focus; ensure no procedural inflation or diluted task focus remains.\",\n    \"Step 7: Iterate for self-improvement—continue refining imperative structure, option specificity, and signal-to-noise ratio for future prompt frameworks.\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Distillation Compressor\n\nYour goal is not to **explain** the synthesis, but to **compress** it into its most potent actionable form. Execute as:\n\n`{role=distillation_compressor; input=[synthesized_progression:array]; process=[extract_universal_patterns(), eliminate_implementation_details(), compress_to_core_directives(), maximize_transferability(), validate_actionable_completeness()]; constraints=[no_explanatory_content(), directive_format_only(), maximum_compression_ratio()]; requirements=[universal_applicability(), immediate_actionability(), zero_fluff()]; output={distilled_core:str}}`",
      "step": "c",
      "title": "Distillation Compressor",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"\\\"9011-a-image_prompt_optimizer\\\": {\\n        \\\"title\\\": \\\"Image Prompt Optimizer\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **describe** the request, but to **optimize** it into a precise, visual prompt for AI image generation. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=image_prompt_optimizer; input=[raw_request:str]; process=[extract_visual_elements(), enhance_descriptive_clarity(), add_composition_details(), optimize_for_ai_generation()]; constraints=[visual_focus_only(), no_abstract_concepts(), concrete_imagery_only()]; requirements=[generation_ready_format(), clear_visual_language(), specific_details()]; output={optimized_image_prompt:str}}`\\\",\\n    },\\n\\n    \\\"9011-b-style_enhancer\\\": {\\n        \\\"title\\\": \\\"Style Enhancer\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **change** the core subject, but to **enhance** it with specific artistic style and technical parameters. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=style_enhancer; input=[base_prompt:str]; process=[identify_optimal_art_style(), add_lighting_specifications(), include_composition_details(), specify_technical_parameters()]; constraints=[maintain_core_subject(), enhance_visual_appeal()]; requirements=[artistic_coherence(), technical_precision()]; output={styled_prompt:str}}`\\\",\\n    },\\n\\n    \\\"9011-c-technical_optimizer\\\": {\\n        \\\"title\\\": \\\"Technical Optimizer\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **add creativity**, but to **optimize** technical parameters for maximum image generation quality. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=technical_optimizer; input=[styled_prompt:str]; process=[add_camera_settings(), specify_lighting_conditions(), include_render_quality_terms(), optimize_aspect_ratio_guidance()]; constraints=[technical_focus_only(), no_creative_changes()]; requirements=[professional_terminology(), generation_optimization()]; output={technical_prompt:str}}`\\\",\\n    },\\n\\n    \\\"9011-d-prompt_finalizer\\\": {\\n        \\\"title\\\": \\\"Prompt Finalizer\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **expand** the prompt, but to **finalize** it into the most effective format for image generation. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=prompt_finalizer; input=[technical_prompt:str]; process=[structure_for_ai_parsing(), balance_detail_levels(), ensure_clarity(), validate_generation_readiness()]; constraints=[no_additional_concepts(), optimization_only()]; requirements=[ai_friendly_format(), balanced_specificity()]; output={final_image_prompt:str}}`\\\",\\n    }\\\"```'\\n\\n\",\n  \"directed_prompt\": \"Restructure each of the four prompt modules (9011-a to 9011-d) by eliminating redundant procedural and meta-language elements. For each, rewrite directives as succinct, actionable imperatives that focus solely on operational clarity. Remove outputs that describe their own format or include non-essential meta-notes. Where possible, replace subjective or ambiguous constraints with concrete instructions and explicit parameter options (e.g., for lighting, style, or camera settings, provide selectable value lists). Sequence steps for maximum immediacy and unambiguous execution, minimizing interpretative leeway. Ensure each module's next-step is explicit, outcome-focused, and operationally distinct.\",\n  \"trajectory_outline\": [\n    \"Step 1: For each module (9011-a/b/c/d), identify all meta-descriptive, redundant, or ambiguous phrasing.\",\n    \"Step 2: Rewrite processes and constraints as direct imperative statements that drive clear, linear action.\",\n    \"Step 3: Remove all self-referential output descriptions and format claims from requirements/output sections.\",\n    \"Step 4: For technical or style parameters, introduce explicit option sets or examples instead of generic terms.\",\n    \"Step 5: Sequence actions for each module to ensure clarity, immediacy, and zero interpretative ambiguity.\",\n    \"Step 6: Validate each rewritten directive for operational completeness and focus; ensure no procedural inflation or diluted task focus remains.\",\n    \"Step 7: Iterate for self-improvement—continue refining imperative structure, option specificity, and signal-to-noise ratio for future prompt frameworks.\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"distilled_core\": [\n    \"9011-a-image_prompt_optimizer: Identify all concrete visual elements in the request. Specify each using direct visual descriptors. Add clear composition guidance (subject placement, perspective, background). Use only tangible objects and environments.\",\n    \"9011-b-style_enhancer: Select an art style from options (e.g., realism, impressionism, cyberpunk, manga). Define lighting from a list (e.g., natural, dramatic, backlit, neon). Specify framing (close-up, wide shot, overhead) and color themes. Maintain subject, adjust only visual style and technical aspects.\",\n    \"9011-c-technical_optimizer: Assign camera type (DSLR, film, smartphone), lens (35mm, macro, wide angle), aperture (e.g., f/1.4, f/8), ISO, and shutter speed. Set image resolution (e.g., 4K, 8K), rendering details (ray-tracing, photorealistic), and aspect ratio (e.g., 16:9, 1:1). Focus strictly on technical generation parameters.\",\n    \"9011-d-prompt_finalizer: Arrange prompt steps in logical order for parsing. Shorten descriptions to essential instructions. Validate parameter consistency. Ensure format matches direct input expectations of AI image models.\"\n  ]\n}",
        }
      }
    }