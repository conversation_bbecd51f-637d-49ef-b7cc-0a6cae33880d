  {
  "initial_prompt": "Subject: A modern black key fob with multiple control buttons displays a sleek design featuring chrome accents and a metallic finish. The device is attached to a silver metal keyring and includes various function buttons with white symbols and text.\n\nScene: The key fob rests on a textured black surface with a subtle grain pattern. The dark background creates depth while fading to deeper shadows around the edges, isolating the main subject against the minimalist setting. [gold-black, morph, fpv, transformative, luxury]\n\nStyle: Dramatic macro photography with selective focus creates a professional product shot aesthetic. Sharp detail on the key fob contrasts with the intentionally blurred background, while careful lighting produces subtle highlights on the metallic surfaces. Professional product photography, selective focus, moody lighting, high contrast, metallic highlights.",
  "sequence_id": "1703:b",
  "results": [
      {
      "instruction": "# Premium Image Prompt Synthesizer\n\n/* — PHASE 1 : Context & Safety — */\n  extract_visual_elements(),\n  detect_conflicting_aspect_ratio(),                       abort_if_conflict(),\n  enforce_token_budget(≤77_SD_tokens_or_≤450_chars_UI),    abort_if_exceeded(),\n  /* — PHASE 2 : Positive Channel Build — */\n  push_concrete_nouns_front(),\n  apply_token_weighting(primary≥1.2,background≤1.0),       abort_if_total_weight≤0,\n  compose_multiprompt(::),\n  generate_region_tags_if_scene_implied(),                 note_mobile_mask_warning(),\n  /* — PHASE 3 : Style & Aesthetics — */\n  select_art_style(),\n  reject_style_if_conflicts_camera_realism(),\n  weight_style_token(≤1.4),\n  set_colour_palette(harmonious),\n  add_lighting_descriptor(coherent_with_palette),\n  tune_stylize(bounds=0‑1000),                             abort_if_out_of_bounds(),\n  tune_chaos(bounds=0‑100),                                abort_if_out_of_bounds(),\n  /* — PHASE 4 : Technical & Camera — */\n  embed_camera_EXIF(\"35 mm f/1.8 ISO100 1/500 s\"),\n  verify_aperture_range(f1.2‑f22),                         abort_if_invalid(),\n  add_quality_terms(8K,PBR,ray‑tracing),\n  refine_aspect_ratio(),\n  attach_image_weight(--iw 0‑3_if_URL_present),            abort_if_IW_out_of_bounds(),\n  /* — PHASE 5 : Negative Channel & Mirroring — */\n  collect_user_negatives(),\n  append_builtin_negatives(overblur,lowres,distracting_text),\n  validate_negative_strength(avoids_colour_washout),\n  /* — PHASE 6 : Final Assembly & Validation — */\n  concatenate_positive_negative(),                         parameters_trail_prose(),\n  validate_parentheses_balance(),\n  check_double_colon_weight_sum(),                         abort_if_weight_sum_zero(),\n  confirm_region_syntax(),\n  lock_seed_and_append_sampler_steps(),\n  output_final_prompt()\n],\nconstraints=[\n  positives_first(), parameters_last(), region_masks_supported(),\n  no_new_concepts(), preserve_weights(), style_weight≤1.4,\n  professional_terminology(), explicit_ar_if_named()\n],\nrequirements=[\n  premium_visual_fidelity(), reproducible(), clear_visual_language(),\n  quality_gate(FID<15_or_equivalent), atomic_fail_fast_on_violation()\n],\noutput={final_image_prompt:str, warnings:list, abort_code:int|null}}`\n\nContext: {\n  \"principles\": {\n    \"fail_fast\": \"Abort with explicit code when any hard constraint is violated to prevent wasted renders.\",\n    \"surface_blockers\": \"Return `warnings` array for soft issues (e.g. region masks on mobile).\",\n    \"premium_fidelity\": \"Always target ultra‑sharp, detail‑rich, production‑grade outputs.\"\n  },\n  \"success_criteria\": {\n    \"syntactic_pass\": \"Prompt parses without error in both SD & MJ (parentheses, `::`, parameter order).\",\n    \"visual_quality\": \"Meets or exceeds internal FID / LPIPS thresholds (or skips metric if unavailable).\",\n    \"determinism\": \"Identical input + seed => identical output.\",\n    \"safety\": \"No colour washout, no truncated tokens, no invalid weights, no parameter conflicts.\"\n  },\n  \"edge_case_handling\": {\n    \"zero_weight_sum\": \"Hard abort (`abort_code` = 92).\",\n    \"token_budget_overflow\": \"Hard abort (`abort_code` = 77).\",\n    \"aspect_ratio_conflict\": \"Hard abort (`abort_code` = AR1).\",\n    \"out_of_bounds_stylize_chaos\": \"Hard abort (`abort_code` = SC1).\",\n    \"IW_without_URL\": \"Warning only; `--iw` dropped.\"\n  }\n}\n\n",
      "step": "b",
      "title": "Premium Image Prompt Synthesizer",
      "input": "Subject: A modern black key fob with multiple control buttons displays a sleek design featuring chrome accents and a metallic finish. The device is attached to a silver metal keyring and includes various function buttons with white symbols and text.\n\nScene: The key fob rests on a textured black surface with a subtle grain pattern. The dark background creates depth while fading to deeper shadows around the edges, isolating the main subject against the minimalist setting. [gold-black, morph, fpv, transformative, luxury]\n\nStyle: Dramatic macro photography with selective focus creates a professional product shot aesthetic. Sharp detail on the key fob contrasts with the intentionally blurred background, while careful lighting produces subtle highlights on the metallic surfaces. Professional product photography, selective focus, moody lighting, high contrast, metallic highlights.",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"final_image_prompt\": \"modern black key fob with multiple control buttons, sleek design, chrome accents, metallic finish, attached to a silver metal keyring, detailed white symbols and text on buttons (weight:1.3)::textured black surface with subtle grain, dark background fading into deeper shadows at edges, minimalist isolation, luxury feel (weight:1)::dramatic macro photography, professional product shot, selective focus, moody lighting with high contrast, metallic highlights::colour palette: gold-black with metallic silvers, harmonious reflective tones::lighting: precise, directional, creating sharp reflections and soft gradients across metal surfaces::35 mm f/1.8 ISO100 1/500 s, 8K, PBR, ray-tracing, aspect ratio 3:2 --stylize 650 --chaos 15 --seed 572381 --sampler Euler --steps 36 --ar 3:2 --quality 2 --v 5 --region main=key_fob, background=surface\",\n  \"warnings\": [],\n  \"abort_code\": null\n}",
        }
      }
    }