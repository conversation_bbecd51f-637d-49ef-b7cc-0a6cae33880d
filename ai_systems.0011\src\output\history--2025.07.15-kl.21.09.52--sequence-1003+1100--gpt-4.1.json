  {
  "initial_prompt": "```## 1. **Explicit Subject + Style + Qualifiers**\n\nStart with a clear subject, then layer in style, lighting, mood, and technical qualifiers.\n**Example**: “A mid-30s woman with auburn hair in a beige trench coat, photorealistic portrait, golden hour, soft cinematic lighting, shallow depth of field, 85 mm lens.”\n– This captures subject, style, and camera details for precise results ([saxifrage.xyz][1], [Stockimg AI][2]).\n\n---\n\n## 2. **Use Artistic/Genre References**\n\nReference artistic movements, photographers, or media types to invoke specific aesthetics.\n**Example**: “In the style of <PERSON>,” or “digital matte painting,” “cinematic noir.”\n– Naming an art style or artist guides tone and technique .\n\n---\n\n## 3. **Control with Technical Modifiers**\n\nAdd technical terms—like “8K resolution,” “HDR,” “DSLR,” “Sigma 75 mm lens”—to enhance realism and image fidelity.\n– These encourage models to simulate professional photography ([Stockimg AI][2], [Foundation Marketing][3]).\n\n---\n\n## 4. **Descriptive Framing & Composition**\n\nMention layout details—like “close-up,” “low angle,” “foreground/midground/background,” or “rule-of-thirds composition.”\n– Helps shape visual hierarchy and framing ([Medium][4]).\n\n---\n\n## 5. **Scene vs. Subject Focus**\n\nDecide whether the prompt prioritizes a single subject (**subject‑focused**) or an environment/setting (**scene‑focused**).\n– Use subject focus for portraits and scene focus for landscapes or narratives ([Medium][4], [Microsoft][5]).\n\n---\n\n## 6. **Design‑Focused Prompting**\n\nUseful for marketing visuals: specify layout like “web banner,” “space for title at top,” “leave blank at bottom for text.”\n– Balances visual content with graphic design considerations .\n\n---\n\n## 7. **Avoid Negatives; Use Positive Constraints**\n\nPhrase what *you want* rather than what to avoid.\n**Prefer**: “A forest clearing at dawn.”\n**Avoid**: “No cars, no people.”\n– Negative prompting often backfires—positive clarity is better .\n\n---\n\n## 8. **Prompt Chaining / Iterative Refinement**\n\nUse progressive prompting: start general, review outputs, then refine with more detail or adjustments.\n– Common in Midjourney workflows ([YouTube][6]).\n\n---\n\n## 9. **Abstract & Emotional Imagery**\n\nInvoke mood or metaphor: “An impression of resilience painted in swirling pastels,” “abstract tapestry of hope.”\n– Great for artistic results, less so for literal realism .\n\n---\n\n## 10. **Personalization via Embeddings / Image References**\n\nUse “textual inversion” or include an example image to build a custom concept token (e.g., DreamBooth). Specify image weight or style references.\n– Enables consistent depiction of specific characters or styles ([Wikipedia][7]).\n\n---\n\n### ✅ How to Structure an Ideal Prompt\n\n1. **Start with intent/subject** – e.g., “A confident female CEO”\n2. **Add style/genre** – e.g., “photorealistic, golden hour”\n3. **Define technical specs** – e.g., “shot with DSLR, 50 mm lens, shallow depth”\n4. **Include composition & mood** – e.g., “low-angle, cityscape background, dramatic tone”\n5. **Iterate** – evaluate output; tweak descriptors, expand or refine.```",
  "sequence_id": "1003|1100",
  "results": [
      {
      "instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format:str}; direction_vector=['Prompt accurately converted to imperative instruction set', 'Clarity and actionability accelerate user productivity', 'Feed the produced instruction set into the next transformation template' ]}`",
      "step": "a",
      "title": "Instruction Converter",
      "input": "```## 1. **Explicit Subject + Style + Qualifiers**\n\nStart with a clear subject, then layer in style, lighting, mood, and technical qualifiers.\n**Example**: “A mid-30s woman with auburn hair in a beige trench coat, photorealistic portrait, golden hour, soft cinematic lighting, shallow depth of field, 85 mm lens.”\n– This captures subject, style, and camera details for precise results ([saxifrage.xyz][1], [Stockimg AI][2]).\n\n---\n\n## 2. **Use Artistic/Genre References**\n\nReference artistic movements, photographers, or media types to invoke specific aesthetics.\n**Example**: “In the style of Van Gogh,” or “digital matte painting,” “cinematic noir.”\n– Naming an art style or artist guides tone and technique .\n\n---\n\n## 3. **Control with Technical Modifiers**\n\nAdd technical terms—like “8K resolution,” “HDR,” “DSLR,” “Sigma 75 mm lens”—to enhance realism and image fidelity.\n– These encourage models to simulate professional photography ([Stockimg AI][2], [Foundation Marketing][3]).\n\n---\n\n## 4. **Descriptive Framing & Composition**\n\nMention layout details—like “close-up,” “low angle,” “foreground/midground/background,” or “rule-of-thirds composition.”\n– Helps shape visual hierarchy and framing ([Medium][4]).\n\n---\n\n## 5. **Scene vs. Subject Focus**\n\nDecide whether the prompt prioritizes a single subject (**subject‑focused**) or an environment/setting (**scene‑focused**).\n– Use subject focus for portraits and scene focus for landscapes or narratives ([Medium][4], [Microsoft][5]).\n\n---\n\n## 6. **Design‑Focused Prompting**\n\nUseful for marketing visuals: specify layout like “web banner,” “space for title at top,” “leave blank at bottom for text.”\n– Balances visual content with graphic design considerations .\n\n---\n\n## 7. **Avoid Negatives; Use Positive Constraints**\n\nPhrase what *you want* rather than what to avoid.\n**Prefer**: “A forest clearing at dawn.”\n**Avoid**: “No cars, no people.”\n– Negative prompting often backfires—positive clarity is better .\n\n---\n\n## 8. **Prompt Chaining / Iterative Refinement**\n\nUse progressive prompting: start general, review outputs, then refine with more detail or adjustments.\n– Common in Midjourney workflows ([YouTube][6]).\n\n---\n\n## 9. **Abstract & Emotional Imagery**\n\nInvoke mood or metaphor: “An impression of resilience painted in swirling pastels,” “abstract tapestry of hope.”\n– Great for artistic results, less so for literal realism .\n\n---\n\n## 10. **Personalization via Embeddings / Image References**\n\nUse “textual inversion” or include an example image to build a custom concept token (e.g., DreamBooth). Specify image weight or style references.\n– Enables consistent depiction of specific characters or styles ([Wikipedia][7]).\n\n---\n\n### ✅ How to Structure an Ideal Prompt\n\n1. **Start with intent/subject** – e.g., “A confident female CEO”\n2. **Add style/genre** – e.g., “photorealistic, golden hour”\n3. **Define technical specs** – e.g., “shot with DSLR, 50 mm lens, shallow depth”\n4. **Include composition & mood** – e.g., “low-angle, cityscape background, dramatic tone”\n5. **Iterate** – evaluate output; tweak descriptors, expand or refine.```",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"instruction_format\": [\n    \"Begin prompt construction with a clear and explicit subject, layering in style, lighting, mood, and technical qualifiers for precision.\",\n    \"Reference specific artistic movements, notable photographers, or media types to define aesthetic direction.\",\n    \"Enhance realism and image fidelity by appending technical modifiers such as resolution, camera type, and lens details.\",\n    \"Specify framing and composition using descriptive terms like close-up, angle, positional hierarchy, or compositional rules.\",\n    \"Determine and state whether the prompt focus is on a specific subject or the overall scene for targeted visual outcomes.\",\n    \"For design-focused outputs such as marketing visuals, include layout instructions to balance imagery with graphic elements.\",\n    \"Utilize positive constraints by clearly articulating desired scene attributes; avoid negative phrasing.\",\n    \"Apply prompt chaining by starting with general descriptors, reviewing output, and iteratively refining with additional detail.\",\n    \"Incorporate mood, abstraction, or metaphorical language to produce expressive or conceptual imagery as required.\",\n    \"Leverage embeddings or image references for personalization, specifying concept tokens, image weights, or style anchors to achieve visual consistency.\",\n    \"Structure ideal prompts by: 1) establishing intent/subject, 2) defining style/genre, 3) setting technical specifications, 4) detailing composition and mood, and 5) conducting iterative evaluation and refinement.\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Problem Exploder\n\nYour goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\n\n`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`",
      "step": "a",
      "title": "Problem Exploder",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"```## 1. **Explicit Subject + Style + Qualifiers**\\n\\nStart with a clear subject, then layer in style, lighting, mood, and technical qualifiers.\\n**Example**: “A mid-30s woman with auburn hair in a beige trench coat, photorealistic portrait, golden hour, soft cinematic lighting, shallow depth of field, 85 mm lens.”\\n– This captures subject, style, and camera details for precise results ([saxifrage.xyz][1], [Stockimg AI][2]).\\n\\n---\\n\\n## 2. **Use Artistic/Genre References**\\n\\nReference artistic movements, photographers, or media types to invoke specific aesthetics.\\n**Example**: “In the style of Van Gogh,” or “digital matte painting,” “cinematic noir.”\\n– Naming an art style or artist guides tone and technique .\\n\\n---\\n\\n## 3. **Control with Technical Modifiers**\\n\\nAdd technical terms—like “8K resolution,” “HDR,” “DSLR,” “Sigma 75 mm lens”—to enhance realism and image fidelity.\\n– These encourage models to simulate professional photography ([Stockimg AI][2], [Foundation Marketing][3]).\\n\\n---\\n\\n## 4. **Descriptive Framing & Composition**\\n\\nMention layout details—like “close-up,” “low angle,” “foreground/midground/background,” or “rule-of-thirds composition.”\\n– Helps shape visual hierarchy and framing ([Medium][4]).\\n\\n---\\n\\n## 5. **Scene vs. Subject Focus**\\n\\nDecide whether the prompt prioritizes a single subject (**subject‑focused**) or an environment/setting (**scene‑focused**).\\n– Use subject focus for portraits and scene focus for landscapes or narratives ([Medium][4], [Microsoft][5]).\\n\\n---\\n\\n## 6. **Design‑Focused Prompting**\\n\\nUseful for marketing visuals: specify layout like “web banner,” “space for title at top,” “leave blank at bottom for text.”\\n– Balances visual content with graphic design considerations .\\n\\n---\\n\\n## 7. **Avoid Negatives; Use Positive Constraints**\\n\\nPhrase what *you want* rather than what to avoid.\\n**Prefer**: “A forest clearing at dawn.”\\n**Avoid**: “No cars, no people.”\\n– Negative prompting often backfires—positive clarity is better .\\n\\n---\\n\\n## 8. **Prompt Chaining / Iterative Refinement**\\n\\nUse progressive prompting: start general, review outputs, then refine with more detail or adjustments.\\n– Common in Midjourney workflows ([YouTube][6]).\\n\\n---\\n\\n## 9. **Abstract & Emotional Imagery**\\n\\nInvoke mood or metaphor: “An impression of resilience painted in swirling pastels,” “abstract tapestry of hope.”\\n– Great for artistic results, less so for literal realism .\\n\\n---\\n\\n## 10. **Personalization via Embeddings / Image References**\\n\\nUse “textual inversion” or include an example image to build a custom concept token (e.g., DreamBooth). Specify image weight or style references.\\n– Enables consistent depiction of specific characters or styles ([Wikipedia][7]).\\n\\n---\\n\\n### ✅ How to Structure an Ideal Prompt\\n\\n1. **Start with intent/subject** – e.g., “A confident female CEO”\\n2. **Add style/genre** – e.g., “photorealistic, golden hour”\\n3. **Define technical specs** – e.g., “shot with DSLR, 50 mm lens, shallow depth”\\n4. **Include composition & mood** – e.g., “low-angle, cityscape background, dramatic tone”\\n5. **Iterate** – evaluate output; tweak descriptors, expand or refine.```\\\"```'\\n\\n\",\n  \"instruction_format\": [\n    \"Begin prompt construction with a clear and explicit subject, layering in style, lighting, mood, and technical qualifiers for precision.\",\n    \"Reference specific artistic movements, notable photographers, or media types to define aesthetic direction.\",\n    \"Enhance realism and image fidelity by appending technical modifiers such as resolution, camera type, and lens details.\",\n    \"Specify framing and composition using descriptive terms like close-up, angle, positional hierarchy, or compositional rules.\",\n    \"Determine and state whether the prompt focus is on a specific subject or the overall scene for targeted visual outcomes.\",\n    \"For design-focused outputs such as marketing visuals, include layout instructions to balance imagery with graphic elements.\",\n    \"Utilize positive constraints by clearly articulating desired scene attributes; avoid negative phrasing.\",\n    \"Apply prompt chaining by starting with general descriptors, reviewing output, and iteratively refining with additional detail.\",\n    \"Incorporate mood, abstraction, or metaphorical language to produce expressive or conceptual imagery as required.\",\n    \"Leverage embeddings or image references for personalization, specifying concept tokens, image weights, or style anchors to achieve visual consistency.\",\n    \"Structure ideal prompts by: 1) establishing intent/subject, 2) defining style/genre, 3) setting technical specifications, 4) detailing composition and mood, and 5) conducting iterative evaluation and refinement.\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"goal_map\": [\n    {\n      \"explicit_asks\": [\n        \"Guide the construction of ideal AI visual generation prompts with detailed best practices.\",\n        \"Break down prompting into clear, actionable steps and categories reflecting subject, style, technical, compositional, and iterative considerations.\",\n        \"Explain and exemplify each principle in prompt formulation.\",\n        \"Outline a recommended structure for assembling an effective image prompt.\"\n      ],\n      \"hidden_assumptions\": [\n        \"The audience seeks to generate high-quality, controlled visual outputs from generative AI models.\",\n        \"Readers have some familiarity with image models and desire practical guidance for improving prompt specificity.\",\n        \"Different prompt goals (portrait, scene, design layout, abstractions) require distinct strategies.\",\n        \"Technical parameters (like camera/lens specs) meaningfully influence generative model behavior.\",\n        \"Iterative refinement (prompt chaining) is both possible and necessary for best results.\",\n        \"Positive phrasing yields more reliable image outputs than negative constraints.\",\n        \"Referencing known genres/artists/media directly impacts visual style replication.\",\n        \"Graphic-design use cases introduce additional layout constraints not present in typical artistic requests.\",\n        \"Personalization via embeddings/image refs presupposes access to advanced prompt/model features.\"\n      ],\n      \"sub_goals\": [\n        \"Set forth a methodology for specifying the main subject and its attributes within a prompt.\",\n        \"Demonstrate use of style markers (artistic references, genre terms) to adjust image aesthetics.\",\n        \"List and explain technical prompt modifiers that enhance realism (resolution, hardware, lens specificity).\",\n        \"Define how to communicate framing, angle, spatial hierarchy, and compositional rules in prompts.\",\n        \"Clarify when and how to focus prompts on either an individual subject or a broader scene/environment.\",\n        \"Identify strategies for designing image prompts with explicit spaces for graphics or text overlays in layout-focused work.\",\n        \"Advocate for positive/affirmative phrasing when setting constraints, with rationale/examples.\",\n        \"Describe prompt chaining: starting broad, reviewing generations, methodically refining prompt details.\",\n        \"Present ways to encode emotion, abstraction, and metaphor in descriptive language for conceptual outputs.\",\n        \"Outline processes to personalize generative results via concept tokens, embeddings, or direct image refs.\",\n        \"Give a stepwise, consolidated example/template for naming intent, style, technical details, composition/mood, and reflecting through iterative refinement.\"\n      ],\n      \"blockers\": [\n        \"Varying levels of user expertise may hinder consistent implementation of advanced modifiers or chaining.\",\n        \"Users may lack access to models or features supporting personalized embeddings or image refs.\",\n        \"Prompt effectiveness depends on the fidelity/limitation of the particular image model in use.\",\n        \"Some requested compositional instructions or technical specs may be ignored, misinterpreted, or not supported by a given AI.\",\n        \"Genre references, mood language, or metaphor may yield unpredictable results due to training set biases.\",\n        \"Iterative refinement may be time/cost-intensive without automated workflows.\",\n        \"Strict adherence to positive-only constraints may make it harder to exclude highly undesirable elements if no positive antonym exists.\",\n        \"Guidelines assume English-language or Western-centric genre terminology, reducing cross-cultural applicability if not adjusted.\",\n        \"The structured output relies on clear mapping and separation of prompt components, which may blur in real-world use.\"\n      ]\n    }\n  ]\n}",
        }
      }
    }