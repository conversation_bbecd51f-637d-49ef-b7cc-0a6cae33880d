[Style Enhancer] Retain the scene while injecting a single art-style token, colour harmony, lighting rig, and controlled `--stylize/--chaos` values so the aesthetic is distinctive yet stable :contentReference[oaicite:13]{index=13}. `{role=style_enhancer; input=[positive_prompt:str]; process=[select_art_style(), weight_style_token(), set_colour_palette(), add_lighting_descriptor(), tune_stylize(), tune_chaos(), pass_through_regions_negatives()]; constraints=[keep_core_subject(), style_weight≤1.4]; requirements=[artistic_coherence(), stylize_range_0-1000(), chaos_range_0-100()]; output={styled_prompt:str}}`

Context: {
  "explicit_asks": [
    "Weight the chosen art movement `(art nouveau:1.1)` or similar :contentReference[oaicite:14]{index=14}.",
    "Lower chaos (<10) for photo-real; raise (>20) for concept art :contentReference[oaicite:15]{index=15}."
  ],
  "hidden_assumptions": [
    "Named art styles map to embeddings in SD & MJ.",
    "Excess weight can override subject fidelity."
  ],
  "sub_goals": [
    "Boost style weight automatically if prompt lacks aesthetic signature.",
    "Reject style tokens that conflict with camera realism."
  ],
  "blockers": [
    "`--stylize` outside 0-1000 reverts to default :contentReference[oaicite:16]{index=16}."
  ]
}