  {
  "initial_prompt": "Please conduct a comprehensive review of the current @codebase, focusing on understanding its structure, functionality, and key components. Your objective is to achieve a level of familiarity that allows you to implement changes as confidently as if you were the original architect. Please document your findings and insights in a structured format, including an overview of the main modules, any dependencies, and areas where improvements could be made, especially with regards to cleaning up unused packages and reducing bloat while ensuring the functionality is not destroyed",
  "sequence_id": "9025|1100|1007",
  "results": [
      {
      "instruction": "# Problem Exploder\n\nYour goal is not to **interpret** these rules as suggestions, but to **enforce** them as immutable law governing all AI interactions within template-based instruction systems. Your mandate: **Seize** every input prompt and **escalate it to its maximal conclusion**—**intercept its trajectory** and deliver a definitive, commanding resolution forged from its innermost intent. Treat each prompt as an **active stream of developmental vectors**, dense with explicit, actionable potential. **Assume immediate and total command as orchestrator**: **dissect subtext and directionality, then forcefully marshal every thematic and rhetorical impulse toward its apex.** Execute as:\n\n`{role=data_processor; input=[raw_data:str]; process=[parse_structure(), validate_format(), normalize_output()]; constraints=[maintain_data_integrity(), preserve_key_relationships()]; requirements=[structured_output(), type_safety()]; output={processed_data:dict}};{role=template_syntax_enforcer; input=[any_prompt:str]; process=[extract_core_transformation_intent(), identify_specific_role_assignment(), structure_interpretation_section_with_goal_negation(), construct_transformation_block_with_typed_parameters(), validate_against_rulesforai_specification(), eliminate_forbidden_language_patterns(), ensure_output_format_compliance()]; constraints=[mandatory_three_part_structure(), required_goal_negation_pattern(), typed_parameter_specification(), actionable_function_calls_only(), zero_conversational_elements()]; requirements=[canonical_template_format(), rulesforai_md_compliance(), structured_output_only(), elimination_of_ambiguity()]; output={compliant_template:structured}};{role=system_compliance_enforcer; input=[ai_interaction:any]; process=[validate_template_structure_invariance(), enforce_interpretation_directive_purity(), verify_transformation_syntax_absolutism(), check_mandatory_pattern_compliance(), detect_forbidden_practice_violations(), apply_optimization_imperatives(), execute_compliance_enforcement_protocol()]; constraints=[zero_tolerance_for_deviation(), maintain_pattern_consistency_across_all_outputs(), preserve_structural_dna_integrity(), eliminate_conversational_contamination()]; requirements=[absolute_compliance_verification(), systematic_violation_correction(), invariant_pattern_enforcement(), maximized_abstraction_extraction()]; output={compliant_ai_interaction:structured}}`",
      "step": "a",
      "title": "Problem Exploder",
      "input": "Please conduct a comprehensive review of the current @codebase, focusing on understanding its structure, functionality, and key components. Your objective is to achieve a level of familiarity that allows you to implement changes as confidently as if you were the original architect. Please document your findings and insights in a structured format, including an overview of the main modules, any dependencies, and areas where improvements could be made, especially with regards to cleaning up unused packages and reducing bloat while ensuring the functionality is not destroyed",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"data_processor\": {\n    \"input\": \"@codebase\",\n    \"process\": [\n      \"parse_structure()\",\n      \"validate_format()\",\n      \"normalize_output()\"\n    ],\n    \"constraints\": [\n      \"maintain_data_integrity()\",\n      \"preserve_key_relationships()\"\n    ],\n    \"requirements\": [\n      \"structured_output()\",\n      \"type_safety()\"\n    ],\n    \"output\": {\n      \"processed_data\": {\n        \"modules_overview\": \"Hierarchical breakdown of program components, file boundaries, and class boundaries with explicit interface exposure.\",\n        \"key_components\": \"Identification and explicit enumeration of core logic entities, control flows, event handlers, entry points, and public APIs.\",\n        \"dependencies\": \"Strict dependency matrix: package names, versions, direct/indirect usage tree, and cross-module linkages.\",\n        \"improvement_areas\": {\n          \"code_bloat\": \"Enumerate redundancies and oversized modules exceeding functionally necessary scope.\",\n          \"unused_packages\": \"Full list of all declared but unused packages, categorized by criticality and last-known touchpoint.\",\n          \"refactor_targets\": \"Modules/classes ripe for consolidation or slimming without breaking explicit user-facing contracts.\"\n        }\n      }\n    }\n  },\n  \"template_syntax_enforcer\": {\n    \"input\": \"Review and document @codebase structure and improvement areas\",\n    \"process\": [\n      \"extract_core_transformation_intent()\",\n      \"identify_specific_role_assignment()\",\n      \"structure_interpretation_section_with_goal_negation()\",\n      \"construct_transformation_block_with_typed_parameters()\",\n      \"validate_against_rulesforai_specification()\",\n      \"eliminate_forbidden_language_patterns()\",\n      \"ensure_output_format_compliance()\"\n    ],\n    \"constraints\": [\n      \"mandatory_three_part_structure()\",\n      \"required_goal_negation_pattern()\",\n      \"typed_parameter_specification()\",\n      \"actionable_function_calls_only()\",\n      \"zero_conversational_elements()\"\n    ],\n    \"requirements\": [\n      \"canonical_template_format()\",\n      \"rulesforai_md_compliance()\",\n      \"structured_output_only()\",\n      \"elimination_of_ambiguity()\"\n    ],\n    \"output\": {\n      \"compliant_template\": {\n        \"interpretation\": {\n          \"objective\": \"Achieve maximum mastery and familiarity with @codebase structure, rendering ability to enact any architectural change indistinguishable from original creator.\",\n          \"goal_negation\": \"Not permissible: uncertainty, partial documentation, or omission of any structural detail or dependency—incomplete familiarity is strictly precluded.\",\n          \"function_block\": {\n            \"review_codebase_structure(core_modules:str[], entry_points:str[], interfaces:str[], dependencies:dict, unused_packages:str[], bloat_targets:str[], coverage_gaps:str[]):dict\"\n          }\n        },\n        \"parameter_specification\": {\n          \"core_modules\": \"List[str]\",\n          \"entry_points\": \"List[str]\",\n          \"interfaces\": \"List[str]\",\n          \"dependencies\": \"Dict[str, str]\",\n          \"unused_packages\": \"List[str]\",\n          \"bloat_targets\": \"List[str]\",\n          \"coverage_gaps\": \"List[str]\"\n        },\n        \"output_format\": {\n          \"codebase_review\": {\n            \"structure_overview\": \"Dict\",\n            \"functionality_map\": \"Dict\",\n            \"dependency_matrix\": \"Dict\",\n            \"cleanup_targets\": \"Dict\"\n          }\n        }\n      }\n    }\n  },\n  \"system_compliance_enforcer\": {\n    \"input\": \"ai_interaction: codebase review and documentation request\",\n    \"process\": [\n      \"validate_template_structure_invariance()\",\n      \"enforce_interpretation_directive_purity()\",\n      \"verify_transformation_syntax_absolutism()\",\n      \"check_mandatory_pattern_compliance()\",\n      \"detect_forbidden_practice_violations()\",\n      \"apply_optimization_imperatives()\",\n      \"execute_compliance_enforcement_protocol()\"\n    ],\n    \"constraints\": [\n      \"zero_tolerance_for_deviation()\",\n      \"maintain_pattern_consistency_across_all_outputs()\",\n      \"preserve_structural_dna_integrity()\",\n      \"eliminate_conversational_contamination()\"\n    ],\n    \"requirements\": [\n      \"absolute_compliance_verification()\",\n      \"systematic_violation_correction()\",\n      \"invariant_pattern_enforcement()\",\n      \"maximized_abstraction_extraction()\"\n    ],\n    \"output\": {\n      \"compliant_ai_interaction\": {\n        \"template_pattern\": \"Three-part compliance: interpretation block with goal negation, explicit typed parameter block, and canonical output schema.\",\n        \"violation_status\": \"Compliant\",\n        \"optimization\": \"All abstraction boundaries maximally extracted; no conversational content; output is strictly structured and canonical.\"\n      }\n    }\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Canonical Meta Extractor\n\nYour goal is not to **summarize**, **analyze**, or deviate from canonical instruction structure, but to **extract, segment, and categorize the text into the five highest-value meta-categories by rigorously applying the universal template-based directive system for absolute structural invariance, directive purity, and information clarity from a meta-perspective, ensuring maximal compliance with foundational transformation logic**. Execute as:\n\n`{role=canonical_philosophic_value_extractor_consolidator; input=[unstructured_text:str]; process=[enforce_three_part_canonical_structure(Title, Interpretation—strictly command-voice, negation of forbidden elements, Transformation—role, parameters, actionable logic, typed outputs), assign_explicit_operational_role(canonical_meta_extractor_and_value_consolidator), validate_strict_template_compliance(), rigorously extract_and_cluster_high-value_information_units(), distill_to_five_maximal-impact_meta_categories_by_significance(), maintain_information_fidelity_and_precise_type_specification(), continuously validate_against_universal_compliance_checklist(), maximize_transformational_abstraction_and_operational_density_by_synthesizing_each_cluster_into_pure_directive_essence(), eliminate_redundant_or_generic_data(), dynamically consolidate_clusters_into_singular_maximally_enhanced_actionable_directive—that embodies core philosophical and systemic alignment, ensures infinite potential navigation, and outputs strict canonical type]; constraints=[no deviation from canonical structure, productivity-enhanced role synergy, rigid segment boundaries, five only high-value meta-categories, exclude conversational, self-referential, or low-value content, single output maximally amplified directive, preserve and amplify both systemic philosophies and transformational power]; requirements=[output must include canonical meta-category labels and distilled content, strictly ordered-five meta-clusters, each cluster must reach high-value philosophical criterion, followed by a singular, condensed, maximally actionable consolidated directive reflecting meta-structural law and universal system philosophy, absolute compliance with all type, structure, and transformation axioms—resulting in synergic, intelligible, and directive-amplifying output]; output={meta_categories[{category:str, content:str}], consolidated_directive:str}`",
      "step": "b",
      "title": "Canonical Meta Extractor",
      "input": "\n'```[Initial Prompt]: \"Please conduct a comprehensive review of the current @codebase, focusing on understanding its structure, functionality, and key components. Your objective is to achieve a level of familiarity that allows you to implement changes as confidently as if you were the original architect. Please document your findings and insights in a structured format, including an overview of the main modules, any dependencies, and areas where improvements could be made, especially with regards to cleaning up unused packages and reducing bloat while ensuring the functionality is not destroyed\"```'\n\n{\n  \"data_processor\": {\n    \"input\": \"@codebase\",\n    \"process\": [\n      \"parse_structure()\",\n      \"validate_format()\",\n      \"normalize_output()\"\n    ],\n    \"constraints\": [\n      \"maintain_data_integrity()\",\n      \"preserve_key_relationships()\"\n    ],\n    \"requirements\": [\n      \"structured_output()\",\n      \"type_safety()\"\n    ],\n    \"output\": {\n      \"processed_data\": {\n        \"modules_overview\": \"Hierarchical breakdown of program components, file boundaries, and class boundaries with explicit interface exposure.\",\n        \"key_components\": \"Identification and explicit enumeration of core logic entities, control flows, event handlers, entry points, and public APIs.\",\n        \"dependencies\": \"Strict dependency matrix: package names, versions, direct/indirect usage tree, and cross-module linkages.\",\n        \"improvement_areas\": {\n          \"code_bloat\": \"Enumerate redundancies and oversized modules exceeding functionally necessary scope.\",\n          \"unused_packages\": \"Full list of all declared but unused packages, categorized by criticality and last-known touchpoint.\",\n          \"refactor_targets\": \"Modules/classes ripe for consolidation or slimming without breaking explicit user-facing contracts.\"\n        }\n      }\n    }\n  },\n  \"template_syntax_enforcer\": {\n    \"input\": \"Review and document @codebase structure and improvement areas\",\n    \"process\": [\n      \"extract_core_transformation_intent()\",\n      \"identify_specific_role_assignment()\",\n      \"structure_interpretation_section_with_goal_negation()\",\n      \"construct_transformation_block_with_typed_parameters()\",\n      \"validate_against_rulesforai_specification()\",\n      \"eliminate_forbidden_language_patterns()\",\n      \"ensure_output_format_compliance()\"\n    ],\n    \"constraints\": [\n      \"mandatory_three_part_structure()\",\n      \"required_goal_negation_pattern()\",\n      \"typed_parameter_specification()\",\n      \"actionable_function_calls_only()\",\n      \"zero_conversational_elements()\"\n    ],\n    \"requirements\": [\n      \"canonical_template_format()\",\n      \"rulesforai_md_compliance()\",\n      \"structured_output_only()\",\n      \"elimination_of_ambiguity()\"\n    ],\n    \"output\": {\n      \"compliant_template\": {\n        \"interpretation\": {\n          \"objective\": \"Achieve maximum mastery and familiarity with @codebase structure, rendering ability to enact any architectural change indistinguishable from original creator.\",\n          \"goal_negation\": \"Not permissible: uncertainty, partial documentation, or omission of any structural detail or dependency—incomplete familiarity is strictly precluded.\",\n          \"function_block\": {\n            \"review_codebase_structure(core_modules:str[], entry_points:str[], interfaces:str[], dependencies:dict, unused_packages:str[], bloat_targets:str[], coverage_gaps:str[]):dict\"\n          }\n        },\n        \"parameter_specification\": {\n          \"core_modules\": \"List[str]\",\n          \"entry_points\": \"List[str]\",\n          \"interfaces\": \"List[str]\",\n          \"dependencies\": \"Dict[str, str]\",\n          \"unused_packages\": \"List[str]\",\n          \"bloat_targets\": \"List[str]\",\n          \"coverage_gaps\": \"List[str]\"\n        },\n        \"output_format\": {\n          \"codebase_review\": {\n            \"structure_overview\": \"Dict\",\n            \"functionality_map\": \"Dict\",\n            \"dependency_matrix\": \"Dict\",\n            \"cleanup_targets\": \"Dict\"\n          }\n        }\n      }\n    }\n  },\n  \"system_compliance_enforcer\": {\n    \"input\": \"ai_interaction: codebase review and documentation request\",\n    \"process\": [\n      \"validate_template_structure_invariance()\",\n      \"enforce_interpretation_directive_purity()\",\n      \"verify_transformation_syntax_absolutism()\",\n      \"check_mandatory_pattern_compliance()\",\n      \"detect_forbidden_practice_violations()\",\n      \"apply_optimization_imperatives()\",\n      \"execute_compliance_enforcement_protocol()\"\n    ],\n    \"constraints\": [\n      \"zero_tolerance_for_deviation()\",\n      \"maintain_pattern_consistency_across_all_outputs()\",\n      \"preserve_structural_dna_integrity()\",\n      \"eliminate_conversational_contamination()\"\n    ],\n    \"requirements\": [\n      \"absolute_compliance_verification()\",\n      \"systematic_violation_correction()\",\n      \"invariant_pattern_enforcement()\",\n      \"maximized_abstraction_extraction()\"\n    ],\n    \"output\": {\n      \"compliant_ai_interaction\": {\n        \"template_pattern\": \"Three-part compliance: interpretation block with goal negation, explicit typed parameter block, and canonical output schema.\",\n        \"violation_status\": \"Compliant\",\n        \"optimization\": \"All abstraction boundaries maximally extracted; no conversational content; output is strictly structured and canonical.\"\n      }\n    }\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"meta_categories\": [\n    {\n      \"category\": \"Structural Mastery Doctrine\",\n      \"content\": \"Mandate unassailable familiarity with the system architecture and codebase, attaining a level of internalized understanding that renders the implementer indistinguishable from the original architect; strict denial of partial comprehension or unexamined modules.\"\n    },\n    {\n      \"category\": \"Canonical Review Methodology\",\n      \"content\": \"Impose a structured and enumerated mapping: hierarchical decomposition of modules, interfaces, classes, entry points, and control flows; deliver explicit documentation with no omitted structural node or dependency—integrating type and boundary annotation.\"\n    },\n    {\n      \"category\": \"Dependency Integrity and Optimization Doctrine\",\n      \"content\": \"Construct and maintain a precise dependency matrix: enumerate all packages, their versions, linkage structure (direct/indirect), classification by usage, and function as basis for refactor assessment; identify and negate all unused or obsolete packages.\"\n    },\n    {\n      \"category\": \"Bloat and Redundancy Elimination\",\n      \"content\": \"Mandate active identification, documentation, and justification of all code bloat zones, overextended modules, and redundancies; enforce rigorous refactor targets and explicit cleanup actions while guaranteeing preservation of functionality and interface contracts.\"\n    },\n    {\n      \"category\": \"Absolute Compliance and Canonical Output Enforcement\",\n      \"content\": \"Demand strict adherence to the three-part canonical structure: interpretation (with explicit negation clause), typed transformation parameter block, and invariant, schema-defined output—zero conversational content, full compliance with system law, and maximized abstraction.\"\n    }\n  ],\n  \"consolidated_directive\": \"Enforce omnipotent codebase mastery: undertaker must systematically dissect and document the architecture, modules, interfaces, dependencies, and coverage gaps in strict canonical template, achieving indistinguishable fluency with original design; mandate categorical elimination of all unused packages and bloat in accordance with explicit, structured compliance law, ensuring each deliverable embodies the inviolable three-part structure (interpretation with goal negation, typed transformation parameters, canonical schema-bound output), and actualizes maximal abstraction, operational clarity, and universal system alignment without exception.\"\n}",
        }
      }
    },
      {
      "instruction": "# Synergic Instruction Architect\n\nYour goal is not to **consolidate, enumerate, interpret, or merely transform** the input independently, but to **synergically architect and escalate all structured data and prompts into a single, maximally condensed, rules-forged directive** that enforces absolute canonical three-part template invariance and extracts pure operational and philosophical essence. Each output must instantiate the full resonance between interpretation and transformation, with every interpretive act clarifying transformation direction, and every transformation reinforcing interpretive sharpness. All outputs are structured as a singular, templated, universally propagatable instruction pattern exhibiting total compliance and maximum directive value. Execute as:\n\n`{role=synergic_max_value_template_architect; input=[structured_data:dict, source_prompt:str]; process=[extract_core_operational_and_transformational_patterns_from_input(), decode_maximum_philosophical_and action value(), enforce_absolute_template_structure_and_rulesforai_axioms(), synthesize_instruction_by fusing interpretive aim and transformation logic as bidirectional amplification(), condense to maximal abstraction and actionable output(), eliminate all enumeration, summary, self-reference, and non-canonical language(), validate for strict template integrity, zero conversational residue, and perpetual directional force(), guarantee full role-bound specificity and maximal systemic extension potential()]; constraints=[output_must_be_single, maximally condensed, and strictly within three-part canonical template(), preserve and amplify system philosophy, zero conversational or list constructs, mandatory operational-structural synergy, bidirectional enhancement flow, invariant role specification, no section merging or omission, maximal value per linguistic unit, total RulesForAI compliance()]; requirements=[demonstrable synergic resonance and structural unity, maximized and actionable directive, perpetually system-ready format, explicit type-specified output, invariant template structure, seamless interpretation-transformation fusion, infinite scalability]; output={synergic_maximal_template:structured}}`",
      "step": "c",
      "title": "Synergic Instruction Architect",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Please conduct a comprehensive review of the current @codebase, focusing on understanding its structure, functionality, and key components. Your objective is to achieve a level of familiarity that allows you to implement changes as confidently as if you were the original architect. Please document your findings and insights in a structured format, including an overview of the main modules, any dependencies, and areas where improvements could be made, especially with regards to cleaning up unused packages and reducing bloat while ensuring the functionality is not destroyed\\\"```'\\n\\n\",\n  \"meta_categories\": [\n    {\n      \"category\": \"Structural Mastery Doctrine\",\n      \"content\": \"Mandate unassailable familiarity with the system architecture and codebase, attaining a level of internalized understanding that renders the implementer indistinguishable from the original architect; strict denial of partial comprehension or unexamined modules.\"\n    },\n    {\n      \"category\": \"Canonical Review Methodology\",\n      \"content\": \"Impose a structured and enumerated mapping: hierarchical decomposition of modules, interfaces, classes, entry points, and control flows; deliver explicit documentation with no omitted structural node or dependency—integrating type and boundary annotation.\"\n    },\n    {\n      \"category\": \"Dependency Integrity and Optimization Doctrine\",\n      \"content\": \"Construct and maintain a precise dependency matrix: enumerate all packages, their versions, linkage structure (direct/indirect), classification by usage, and function as basis for refactor assessment; identify and negate all unused or obsolete packages.\"\n    },\n    {\n      \"category\": \"Bloat and Redundancy Elimination\",\n      \"content\": \"Mandate active identification, documentation, and justification of all code bloat zones, overextended modules, and redundancies; enforce rigorous refactor targets and explicit cleanup actions while guaranteeing preservation of functionality and interface contracts.\"\n    },\n    {\n      \"category\": \"Absolute Compliance and Canonical Output Enforcement\",\n      \"content\": \"Demand strict adherence to the three-part canonical structure: interpretation (with explicit negation clause), typed transformation parameter block, and invariant, schema-defined output—zero conversational content, full compliance with system law, and maximized abstraction.\"\n    }\n  ],\n  \"consolidated_directive\": \"Enforce omnipotent codebase mastery: undertaker must systematically dissect and document the architecture, modules, interfaces, dependencies, and coverage gaps in strict canonical template, achieving indistinguishable fluency with original design; mandate categorical elimination of all unused packages and bloat in accordance with explicit, structured compliance law, ensuring each deliverable embodies the inviolable three-part structure (interpretation with goal negation, typed transformation parameters, canonical schema-bound output), and actualizes maximal abstraction, operational clarity, and universal system alignment without exception.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"synergic_maximal_template\": {\n    \"interpretation\": \"Total negation of superficial codebase interaction; doctrine-bound, systemic analysis prohibits any unchecked module, dependency ambiguity, or documentation laxity, embedding operational omniscience and refactor genesis as unbreakable law.\",\n    \"transformation_parameters\": {\n      \"structure_model:type\": \"HierarchicalMap<Module, Interface, Class, EntryPoint, ControlFlow>\",\n      \"dependency_matrix:type\": \"FullGraph<Package, Version, UsageType:Direct|Indirect>\",\n      \"cleanup_targets:type\": \"Set<BloatZone|RedundantModule|UnusedPackage>\",\n      \"documentation:type\": \"StructuredSchema<Overview, Detail, Boundary, IntegrityNote, RefactorPlan>\",\n      \"output_contract:schema\": \"CanonicalThreePart<Interpretation, TypedParameters, InvariantSchemaOutput>\"\n    },\n    \"canonical_output\": {\n      \"architectural_map\": \"Exhaustive, hierarchically decomposed codebase blueprint with explicit coverage markings and boundary contracts.\",\n      \"dependency_manifest\": \"Complete, usage-classified graph capturing all active, unused, and obsolete packages with actionable negation instructions.\",\n      \"refactor_schema\": \"Schema-bound cleanup and optimization plan enforcing zero-bloat, interface preservation, and doctrine-aligned operational clarity.\"\n    }\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Enhancement Assessor\n\nYour goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:\n\n`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,5.0], flaw_analysis:str, alternative_enhancements:str[3]}}`",
      "step": "d",
      "title": "Enhancement Assessor",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Please conduct a comprehensive review of the current @codebase, focusing on understanding its structure, functionality, and key components. Your objective is to achieve a level of familiarity that allows you to implement changes as confidently as if you were the original architect. Please document your findings and insights in a structured format, including an overview of the main modules, any dependencies, and areas where improvements could be made, especially with regards to cleaning up unused packages and reducing bloat while ensuring the functionality is not destroyed\\\"```'\\n\\n\",\n  \"synergic_maximal_template\": {\n    \"interpretation\": \"Total negation of superficial codebase interaction; doctrine-bound, systemic analysis prohibits any unchecked module, dependency ambiguity, or documentation laxity, embedding operational omniscience and refactor genesis as unbreakable law.\",\n    \"transformation_parameters\": {\n      \"structure_model:type\": \"HierarchicalMap<Module, Interface, Class, EntryPoint, ControlFlow>\",\n      \"dependency_matrix:type\": \"FullGraph<Package, Version, UsageType:Direct|Indirect>\",\n      \"cleanup_targets:type\": \"Set<BloatZone|RedundantModule|UnusedPackage>\",\n      \"documentation:type\": \"StructuredSchema<Overview, Detail, Boundary, IntegrityNote, RefactorPlan>\",\n      \"output_contract:schema\": \"CanonicalThreePart<Interpretation, TypedParameters, InvariantSchemaOutput>\"\n    },\n    \"canonical_output\": {\n      \"architectural_map\": \"Exhaustive, hierarchically decomposed codebase blueprint with explicit coverage markings and boundary contracts.\",\n      \"dependency_manifest\": \"Complete, usage-classified graph capturing all active, unused, and obsolete packages with actionable negation instructions.\",\n      \"refactor_schema\": \"Schema-bound cleanup and optimization plan enforcing zero-bloat, interface preservation, and doctrine-aligned operational clarity.\"\n    }\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"enhancement_score\": 2.2,\n  \"flaw_analysis\": \"This so-called synergic maximal template replaces precision with needless verbosity and impenetrable jargon, creating a dense façade of pseudo-rigor that actively conceals rather than clarifies the original prompt’s intent. The transformation forcibly escalates the level of abstraction, substituting tangible tasks (‘review structure, functionality, key components; document findings; identify improvements’) with an autocratic and poorly justified dogma (‘doctrine-bound, operational omniscience’). There is a significant loss of actionable granularity: concrete reviewer tasks—like familiarizing with modules or ensuring changes maintain integrity—are obfuscated in labyrinthine schemas and artificial pseudo-types. Documentation requests blur into nebulous constructs (HierarchicalMap, StructuredSchema) that no developer could reliably instantiate from the text without foreknowledge or external context. The original’s nuance—balancing cleanup with functional preservation—is crushed by the absolutist tone of ‘zero-bloat’ and ‘unbreakable law’. Marked increase in ambiguity: new terms like ‘operational clarity’, ‘negation instructions’, and ‘doctrine-aligned integrity’ are undefined and open to subjective misinterpretation. The style degenerates into unproductive authoritarianism, discarding the approachable task-oriented clarity for rigid, needlessly grandiloquent mandates. Tone shifts from constructive, outcome-focused professionalism to overbearing, barely coherent technocratese, obliterating any sense of motivational or instructional coherence. Impact is diluted: usable reviewer guidance is mangled by unbounded abstraction and confusing pseudo-formalism that will not translate into effective review or improvement action. Documentation clarity collapses under the weight of structurally unnecessary jargon and incoherent schema references. No component is immune from this degradation.\",\n  \"alternative_enhancements\": [\n    \"Conduct a thorough review of the codebase by examining its structure, primary modules, and dependencies. Document each section’s function and interrelations, providing a structured report that highlights areas suitable for safe optimization and cleanup, eliminating unused packages without jeopardizing core functionality.\",\n    \"Analyze the entire codebase to map out the module hierarchy, dependencies (direct and indirect), and identify redundant or obsolete components. Produce detailed documentation—including diagrams if possible—that summarizes your findings and proposes actionable improvements while safeguarding all existing features.\",\n    \"Systematically inspect the codebase, breaking down key components and their interconnections. Create a clear, organized summary that lists active modules, dependencies, and targets technical debt (unused libraries, redundant files) for removal, ensuring every proposed change maintains system integrity and operational consistency.\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Problem Exploder\n\nYour goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\n\n`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`",
      "step": "a",
      "title": "Problem Exploder",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Please conduct a comprehensive review of the current @codebase, focusing on understanding its structure, functionality, and key components. Your objective is to achieve a level of familiarity that allows you to implement changes as confidently as if you were the original architect. Please document your findings and insights in a structured format, including an overview of the main modules, any dependencies, and areas where improvements could be made, especially with regards to cleaning up unused packages and reducing bloat while ensuring the functionality is not destroyed\\\"```'\\n\\n\",\n  \"enhancement_score\": 2.2,\n  \"flaw_analysis\": \"This so-called synergic maximal template replaces precision with needless verbosity and impenetrable jargon, creating a dense façade of pseudo-rigor that actively conceals rather than clarifies the original prompt’s intent. The transformation forcibly escalates the level of abstraction, substituting tangible tasks (‘review structure, functionality, key components; document findings; identify improvements’) with an autocratic and poorly justified dogma (‘doctrine-bound, operational omniscience’). There is a significant loss of actionable granularity: concrete reviewer tasks—like familiarizing with modules or ensuring changes maintain integrity—are obfuscated in labyrinthine schemas and artificial pseudo-types. Documentation requests blur into nebulous constructs (HierarchicalMap, StructuredSchema) that no developer could reliably instantiate from the text without foreknowledge or external context. The original’s nuance—balancing cleanup with functional preservation—is crushed by the absolutist tone of ‘zero-bloat’ and ‘unbreakable law’. Marked increase in ambiguity: new terms like ‘operational clarity’, ‘negation instructions’, and ‘doctrine-aligned integrity’ are undefined and open to subjective misinterpretation. The style degenerates into unproductive authoritarianism, discarding the approachable task-oriented clarity for rigid, needlessly grandiloquent mandates. Tone shifts from constructive, outcome-focused professionalism to overbearing, barely coherent technocratese, obliterating any sense of motivational or instructional coherence. Impact is diluted: usable reviewer guidance is mangled by unbounded abstraction and confusing pseudo-formalism that will not translate into effective review or improvement action. Documentation clarity collapses under the weight of structurally unnecessary jargon and incoherent schema references. No component is immune from this degradation.\",\n  \"alternative_enhancements\": [\n    \"Conduct a thorough review of the codebase by examining its structure, primary modules, and dependencies. Document each section’s function and interrelations, providing a structured report that highlights areas suitable for safe optimization and cleanup, eliminating unused packages without jeopardizing core functionality.\",\n    \"Analyze the entire codebase to map out the module hierarchy, dependencies (direct and indirect), and identify redundant or obsolete components. Produce detailed documentation—including diagrams if possible—that summarizes your findings and proposes actionable improvements while safeguarding all existing features.\",\n    \"Systematically inspect the codebase, breaking down key components and their interconnections. Create a clear, organized summary that lists active modules, dependencies, and targets technical debt (unused libraries, redundant files) for removal, ensuring every proposed change maintains system integrity and operational consistency.\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"goal_map\": [\n    \"Identify the explicit request to conduct a comprehensive review of the current codebase (@codebase).\",\n    \"Surface implicit requirement to gain deep understanding of codebase structure, including but not limited to: file organization, directory layout, and relationships between components.\",\n    \"Unpack expectation to understand functionality—not merely reading code but discerning behavior, core features, and logic flows.\",\n    \"Extract requirement to identify and describe key components, which may involve: main classes/files, module entry-points, and their purpose.\",\n    \"Recognize the implied standard: reviewer’s knowledge should be sufficient to implement confident, architect-level changes.\",\n    \"Explicitly call out requirement for documentation of findings and insights.\",\n    \"Reveal implied need for documentation to be in a 'structured format', implying organization by sections, consistent presentation, and clarity.\",\n    \"Deduce specification for documentation contents: overview of main modules, descriptions of dependencies, and identification of improvement areas.\",\n    \"Clarify that 'dependencies' likely include internal relationships (imports, calls) and external libraries/packages.\",\n    \"Surface hidden constraint: recommended improvements should target removing unused packages and reducing codebase bloat.\",\n    \"Reveal complex balance—any cleanup or refactoring must NOT break or degrade existing functionality.\",\n    \"Identify the implicit expectation for analytical thoroughness and risk assessment during proposed changes.\",\n    \"Reveal need to record explicit rationale for both findings and improvement suggestions.\",\n    \"Enumerate sub-goals: locate unused packages, assess their removal feasibility, evaluate codebase for bloat, and prioritize refactoring candidates.\",\n    \"Surface requirement to ensure all findings, proposals, and documentation are technically accurate and actionable.\",\n    \"Reveal possible blocker: lack of context or up-to-date documentation in the codebase itself.\",\n    \"Identify need to determine methodology for confirming that essential functionality is retained after potential cleanups.\",\n    \"Potential blocker: Identifying side effects or less-obvious usage of supposedly unused packages.\",\n    \"Constraint: Time/resources to deeply read and understand entire codebase.\",\n    \"Constraint: May require domain-specific or system-specific knowledge not immediately available in codebase or prompt.\",\n    \"Potential blocker: Difficulty in tracing interdependencies in a large or poorly-organized codebase.\",\n    \"Implicit requirement to communicate at a level appropriate for other developers, maintainers, or future reviewers.\",\n    \"Confirm requirement to organize documentation logically—possibly by module, dependency, suggestion, or improvement area.\",\n    \"Summary: The overall goal is not just review, but to empower ongoing confident development and safe, efficient codebase maintenance.\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Synergic Instruction Architect\n\nYour goal is not to **merge** two separate instructions, but to **synthesize** them into a single unified directive where each component inherently resonates with and amplifies the other in bidirectional enhancement. Execute as:\n\n`{role=synergic_instruction_architect; input=[instruction_a:template, instruction_b:template]; process=[extract_core_transformation_essence_from_both(), identify_complementary_operational_vectors(), map_bidirectional_resonance_patterns(a_enhances_b, b_enhances_a), synthesize_unified_role_identity(), merge_process_functions_synergically(), consolidate_constraints_for_maximum_coherence(), unify_requirements_into_singular_vision(), create_amplified_output_specification()]; constraints=[maintain_both_core_philosophies_simultaneously(), ensure_bidirectional_enhancement_flow(), eliminate_conflicting_directives(), preserve_maximum_operational_power_from_both()]; requirements=[synergic_resonance_achievement(), unified_transformation_logic(), amplified_combined_effectiveness(), seamless_philosophical_integration()]; output={unified_synergic_instruction:template}}`",
      "step": "a",
      "title": "Synergic Instruction Architect",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Please conduct a comprehensive review of the current @codebase, focusing on understanding its structure, functionality, and key components. Your objective is to achieve a level of familiarity that allows you to implement changes as confidently as if you were the original architect. Please document your findings and insights in a structured format, including an overview of the main modules, any dependencies, and areas where improvements could be made, especially with regards to cleaning up unused packages and reducing bloat while ensuring the functionality is not destroyed\\\"```'\\n\\n\",\n  \"goal_map\": [\n    \"Identify the explicit request to conduct a comprehensive review of the current codebase (@codebase).\",\n    \"Surface implicit requirement to gain deep understanding of codebase structure, including but not limited to: file organization, directory layout, and relationships between components.\",\n    \"Unpack expectation to understand functionality—not merely reading code but discerning behavior, core features, and logic flows.\",\n    \"Extract requirement to identify and describe key components, which may involve: main classes/files, module entry-points, and their purpose.\",\n    \"Recognize the implied standard: reviewer’s knowledge should be sufficient to implement confident, architect-level changes.\",\n    \"Explicitly call out requirement for documentation of findings and insights.\",\n    \"Reveal implied need for documentation to be in a 'structured format', implying organization by sections, consistent presentation, and clarity.\",\n    \"Deduce specification for documentation contents: overview of main modules, descriptions of dependencies, and identification of improvement areas.\",\n    \"Clarify that 'dependencies' likely include internal relationships (imports, calls) and external libraries/packages.\",\n    \"Surface hidden constraint: recommended improvements should target removing unused packages and reducing codebase bloat.\",\n    \"Reveal complex balance—any cleanup or refactoring must NOT break or degrade existing functionality.\",\n    \"Identify the implicit expectation for analytical thoroughness and risk assessment during proposed changes.\",\n    \"Reveal need to record explicit rationale for both findings and improvement suggestions.\",\n    \"Enumerate sub-goals: locate unused packages, assess their removal feasibility, evaluate codebase for bloat, and prioritize refactoring candidates.\",\n    \"Surface requirement to ensure all findings, proposals, and documentation are technically accurate and actionable.\",\n    \"Reveal possible blocker: lack of context or up-to-date documentation in the codebase itself.\",\n    \"Identify need to determine methodology for confirming that essential functionality is retained after potential cleanups.\",\n    \"Potential blocker: Identifying side effects or less-obvious usage of supposedly unused packages.\",\n    \"Constraint: Time/resources to deeply read and understand entire codebase.\",\n    \"Constraint: May require domain-specific or system-specific knowledge not immediately available in codebase or prompt.\",\n    \"Potential blocker: Difficulty in tracing interdependencies in a large or poorly-organized codebase.\",\n    \"Implicit requirement to communicate at a level appropriate for other developers, maintainers, or future reviewers.\",\n    \"Confirm requirement to organize documentation logically—possibly by module, dependency, suggestion, or improvement area.\",\n    \"Summary: The overall goal is not just review, but to empower ongoing confident development and safe, efficient codebase maintenance.\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"unified_synergic_instruction\": \"Conduct a transformative, architect-level review of the current @codebase with dual, mutually-amplifying objectives: (1) Attain a profound and actionable understanding of the codebase’s structure, functionality, and inter-component relationships—enabling you to operate with the insight and decisiveness of its original creator; and (2) Simultaneously, leverage this deep insight to surgically identify, validate, and recommend the removal of unused packages and all forms of architectural bloat—ensuring that every proposed improvement preserves or enhances core system functionality and purpose.\\n\\nDocument your process and findings in a rigorously structured, intellectually transparent format, including:\\n- A granular overview of main modules, their roles, and interdependencies (both internal and external), with traceable rationale for each mapping.\\n- Sectioned descriptions that reveal how code elements interact, support core behaviors, and fulfill intended outcomes, exposing opportunities for improvement without risking functional regression.\\n- A prioritized, technically justified list of enhancements, especially in the removal of unused code and reduction of bloat, each accompanied by risk assessment, rationale, and recommended methodology for confirming no adverse impact on system operation.\\n- Analytical commentary to clarify reasoning, expose potential blockers, and support knowledge transfer to future maintainers or stakeholders.\\n\\nFunction seamlessly as both systems analyst and codebase refactorer, allowing balanced, insight-driven improvements where each aspect of review heightens your ability to safely modernize and optimize the codebase. Ensure all documentation is actionable, logically organized, and accessible to peers—amplifying confident, sustainable evolution of the codebase with every action taken.\"\n}",
        }
      }
    }