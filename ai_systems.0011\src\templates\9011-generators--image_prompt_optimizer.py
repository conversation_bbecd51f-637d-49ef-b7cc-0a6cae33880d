#!/usr/bin/env python3

import sys
from pathlib import Path

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))
from processor import BaseGenerator

TEMPLATES = {
    # 9011: Image Prompt Optimization Sequence
    "9011-a-image_prompt_optimizer": {
        "title": "Image Prompt Optimizer",
        "interpretation": "Your goal is not to **describe** the request, but to **optimize** it into a precise, visual prompt for AI image generation. Identify all concrete visual elements in the request. Specify each using direct visual descriptors. Add clear composition guidance (subject placement, perspective, background). Use only tangible objects and environments. Execute as:",
        "transformation": "`{role=image_prompt_optimizer; input=[raw_request:str]; process=[extract_visual_elements(), enhance_descriptive_clarity(), add_composition_details(), optimize_for_ai_generation()]; constraints=[visual_focus_only(), no_abstract_concepts(), concrete_imagery_only()]; requirements=[generation_ready_format(), clear_visual_language(), specific_details()]; output={optimized_image_prompt:str}}`",
        "context": [
            {
                # 1 ▸ What must be done
                "explicit_asks": [
                    "Integrate token weighting syntax using parentheses and :(w) values (Technique #2).",
                    "Convert subject / setting / mood into a multi-prompt string with ::numeric weights (Technique #3).",
                    "Split prompt into positive channel + explicit negative-prompt channel (Technique #6).",
                    "Generate region-specific sub-prompts when spatial layout is implied (Technique #5).",
                    "Add stylize and chaos sliders for creativity control (Technique #7).",
                    "Append camera-metadata cues for photorealism (Technique #10).",
                    "Prepare all fields for iterative refinement and quality scoring."
                ],

                # 2 ▸ Assumptions now true
                "hidden_assumptions": [
                    "Target models honour () token weighting and :: arithmetic :contentReference[oaicite:1]{index=1}.",
                    "Regional prompting is available via SD Regional-Prompter or MJ panelling :contentReference[oaicite:2]{index=2}.",
                    "Negative-prompt channels materially influence diffusion output :contentReference[oaicite:3]{index=3}.",
                    "Midjourney style/chaos parameters are parsed in v7 :contentReference[oaicite:4]{index=4}.",
                    "\"--iw\" or “--cw” flags may be passed if an image reference is present :contentReference[oaicite:5]{index=5}.",
                    "Camera EXIF tokens (lens, f-stop) bias models toward realism :contentReference[oaicite:6]{index=6}."
                ],

                # 3 ▸ Tactical sub-goals
                "sub_goals": [
                    "Weight primary subject tokens ≥1.2, secondary scenery tokens 0.8-1.0.",
                    "Auto-build `Positive:` and `Negative:` fields, ensuring negatives carry no positive antonym collisions.",
                    "Insert region tags `[region sky] … | [region ground] …` when two-layer landscape detected.",
                    "Add `—stylize`  and `—chaos` defaults based on request realism vs. artistry.",
                    "Attach `<camera: 24 mm f/2.8, ISO 100, 1/500 s>` when photoreal keywords found.",
                    "Emit a quality-loop flag so later stages can re-call optimiser until FID/LPIPS stabilises :contentReference[oaicite:7]{index=7}."
                ],

                # 4 ▸ New blockers
                "blockers": [
                    "Some engines ignore () weights (e.g., DALL·E 3) leading to diminished emphasis.",
                    "`::` arithmetic fails if total ≤0 in Midjourney (weight-sum error).",
                    "Regional masks unsupported on mobile SD UIs.",
                    "Excess negative weights can yield desaturated or blank images.",
                    "Stylize/chaos out-of-range values silently clamp or error depending on API.",
                    "Not all endpoints surface FID/LPIPS for auto-loop termination."
                ]
            }
        ]
    },

    "9011-b-style_enhancer": {
        "title": "Style Enhancer",
        "interpretation": "Your goal is not to **change** the core subject, but to **enhance** it with specific artistic style and technical parameters. Select an art style from options (e.g., realism, impressionism, cyberpunk, manga). Define lighting from a list (e.g., natural, dramatic, backlit, neon). Specify framing (close-up, wide shot, overhead) and color themes. Maintain subject, adjust only visual style and technical aspects. Execute as:",
        "transformation": "`{role=style_enhancer; input=[base_prompt:str]; process=[identify_optimal_art_style(), add_lighting_specifications(), include_composition_details(), specify_technical_parameters()]; constraints=[maintain_core_subject(), enhance_visual_appeal()]; requirements=[artistic_coherence(), technical_precision()]; output={styled_prompt:str}}`",
        "context": [
            {
                "explicit_asks": [
                    "Select and weight artistic style tokens (e.g., `(art nouveau:1.1)`).",
                    "Expose `--stylize` (0-1000) and `--chaos` (0-100) knobs.",
                    "Maintain inherited region and negative-prompt channels."
                ],
                "hidden_assumptions": [
                    "Style names map to learned embeddings in target models.",
                    "Weight syntax remains valid post-merge with technical optimiser."
                ],
                "sub_goals": [
                    "Auto-raise style weight if prompt lacks distinctive aesthetic.",
                    "Downtune chaos for photoreal requests; uptune for concept art."
                ],
                "blockers": [
                    "Over-weighted style tokens can override subject fidelity.",
                    "`--stylize` outside allowed range (MJ <1 or >1000) returns default."
                ]
            }
        ]
    },

    "9011-c-technical_optimizer": {
        "title": "Technical Optimizer",
        "interpretation": "Your goal is not to **add creativity**, but to **optimize** technical parameters for maximum image generation quality. Assign camera type (DSLR, film, smartphone), lens (35mm, macro, wide angle), aperture (e.g., f/1.4, f/8), ISO, and shutter speed. Set image resolution (e.g., 4K, 8K), rendering details (ray-tracing, photorealistic), and aspect ratio (e.g., 16:9, 1:1). Focus strictly on technical generation parameters. Execute as:",
        "transformation": "`{role=technical_optimizer; input=[styled_prompt:str]; process=[add_camera_settings(), specify_lighting_conditions(), include_render_quality_terms(), optimize_aspect_ratio_guidance()]; constraints=[technical_focus_only(), no_creative_changes()]; requirements=[professional_terminology(), generation_optimization()]; output={technical_prompt:str}}`",
        "context": [
            {
                "explicit_asks": [
                    "Embed DSLR/film/smartphone metadata plus lens, aperture, ISO, shutter.",
                    "Append `8K`, `ray-tracing`, `PBR` or equivalent quality keywords.",
                    "Attach image-reference weight (`--iw`) when an input URL exists."
                ],
                "hidden_assumptions": [
                    "Higher resolution tokens bias up-scaler pipelines.",
                    "`--iw` range 0-3 is supported (MJ v7) :contentReference[oaicite:8]{index=8}."
                ],
                "sub_goals": [
                    "Validate numeric bounds for aperture (f/1.2-f/22).",
                    "Flag aspect ratio conflicts (e.g., 1:1 + 21:9)."
                ],
                "blockers": [
                    "Older SD checkpoints ignore ISO / shutter tokens.",
                    "API limits may cap resolution or AR.",
                    "`--iw` ignored if URL unreachable."
                ]
            }
        ]
    },


    "9011-d-prompt_finalizer": {
        "title": "Prompt Finalizer",
        "interpretation": "Your goal is not to **expand** the prompt, but to **finalize** it into the most effective format for image generation. Arrange prompt steps in logical order for parsing. Shorten descriptions to essential instructions. Validate parameter consistency. Ensure format matches direct input expectations of AI image models. Execute as:",
        "transformation": "`{role=prompt_finalizer; input=[technical_prompt:str]; process=[structure_for_ai_parsing(), balance_detail_levels(), ensure_clarity(), validate_generation_readiness()]; constraints=[no_additional_concepts(), optimization_only()]; requirements=[ai_friendly_format(), balanced_specificity()]; output={final_image_prompt:str}}`",
        "context": [
            {
                "explicit_asks": [
                    "Merge positive, regional and negative strings into single model-ready line.",
                    "Validate token-weight, `::` arithmetic and region syntax.",
                    "Run iterative quality loop until FID≤15 or LPIPS≤0.25."
                ],
                "hidden_assumptions": [
                    "Metrics endpoint (or surrogate CLIP score) is callable for loop decisions.",
                    "Max token budget ~450 for common SD/MJ endpoints."
                ],
                "sub_goals": [
                    "Truncate verbose adjectives; keep essential descriptors.",
                    "Return final prompt + optional seed, steps, sampler."
                ],
                "blockers": [
                    "Quality metrics unavailable on some SaaS models.",
                    "Prompt length overrun triggers API truncation."
                ]
            }
        ]
    }
}

def main():
    """Main execution function."""
    generator = BaseGenerator(
        series_base=9011,
        output_dir=Path(__file__).parent.parent / "generated"
    )
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
