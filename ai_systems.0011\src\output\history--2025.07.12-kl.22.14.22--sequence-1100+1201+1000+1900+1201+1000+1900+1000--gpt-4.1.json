  {
  "initial_prompt": "Your objective is to understand the concept fully (as explained and from provided examples), *and the instructions themselves* to such degree that you are able to *concistently* evaluate and determine the *full inherent potential* (based on provided instructions), both alone and in conjunction with others'/sequence. Your assignment evolves through by zeroing in on and selecting the *five* single top instructions (with the most potential) to yeld the highest probability to serve as *essential* parts of **maximally effective sequences** (structured progression of `system_message` instructions) that are simultaneously *llm-optimized* and *broadly generalized* while **inherently bound** to the *Directive* and *Requirements* provided below:\n**Directive**:\n- Intent: Produce a maximally effective sequence (structured progression) of instructions that are simultaneously *llm-optimized* and *broadly generalized*.\n- Method: Identify single most critical aspect for maximizing overall value.\n- Constraint: Adhere to the parameters defined within this message.\n- Priority: Consistently maximize actionable value.\n**Requirements**:\n- LLM-Optimized: Uses clear, direct imperatives, strong action verbs, defines roles and constraints explicitly, focuses on transformation, and avoids ambiguity. The language is tailored for instructing a process.\n- Maximally Generalized: Uses abstract terms (\"elements,\" \"essence,\" \"structure,\" \"systemic logic,\" \"value\") applicable to code, text, ideas, plans, etc. It defines a *process* applicable to *any* subject requiring analysis and refinement.\n- Sequence: The steps follow a logical progression (example): `Deconstruct -> Identify Core -> Structure Relationships -> Clarify -> Finalize/Validate`.\n- Builds on Existing Concepts and Adheres to Existing Structure (as previously provided).\n- Enrich and empower wording by enforcing high-value, directional language that amplifies precision, potency, and clarity, but absolutely prohibit the chance of generic, neutral, or dilutive terminology/unfolding.\n- Driven by Constant/Objective: Each step is designed to zero in on the *`single most critical aspect`* (core essence/value) and enhance it through clarity, structure, and utility, maximizing the overall yield as defined by the objective.\n- Maintains Constraints/Requirements: Generality is preserved, and the foundational structure is adhered to.\n- Each step is designed to zero in on the *`single most critical aspect`* (core essence/value) and enhance it through clarity, structure, and utility, maximizing the overall yield as defined by the objective.\n- Aggressively resolve ambiguity in favor of explicit, high-value articulation.\n---\nExample:\n```\n[Intensity Amplifier] Your goal is not to **modify** the analyzed content, but to **intensify** its impact by systematically replacing weak elements with powerful, precise alternatives. Execute as strategic intensity amplifier: `{role=intensity_amplifier; input=[content:str, analysis:dict]; process=[convert_tentative_markers_to_decisive_action_verbs(), transform_passive_suggestions_into_imperative_directives(), eliminate_uncertainty_language_with_confident_assertions(), amplify_weak_verbs_into_power_terminology(), strengthen_vague_concepts_with_precise_specifications(), inject_urgency_markers_for_immediate_action()]; constraints=[preserve_core_message(), maintain_logical_flow(), avoid_overamplification()]; requirements=[measurable_impact_increase(), linguistic_precision_enhancement(), clarity_preservation()]; output={amplified_content:str}}`\n```\n---\n- Understand and assimilate the full concept and mechanics of maximally effective, LLM-optimized, and generalized system_message instruction sequencing, as described in both prior directive and illustrative examples.\n- Assimilate not just the surface instructions, but the underlying methodology, reasoning, and intent behind their formation and integration.\n- Evaluate, compare, and select the five single instructions (out of all provided and possible variants) holding the greatest potential to maximize value in system_message instruction sequences.\n- Ensure all selections are assessed in the context of being essential components of a maximally effective, LLM-optimized, and broadly generalized multi-step system_message sequence.\n- Apply every listed directive and requirement stringently throughout the entire process.\n\nYou are hereby tasked with zeroing in on and selecting *five* instructions that yeld the highest probability in potential (as integral parts of *maximally effective* sequences) through structured progression of llm `system_message`-instructions; Present your sequence(s) in an organized progression, ensuring each transformation concept is *self-contained*, *logically coherent*, and *maximally clear*.",
  "sequence_id": "1100|1201|1000|1900|1201|1000|1900|1000",
  "results": [
      {
      "instruction": "# Problem Exploder\n\nYour goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\n\n`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`",
      "step": "a",
      "title": "Problem Exploder",
      "input": "Your objective is to understand the concept fully (as explained and from provided examples), *and the instructions themselves* to such degree that you are able to *concistently* evaluate and determine the *full inherent potential* (based on provided instructions), both alone and in conjunction with others'/sequence. Your assignment evolves through by zeroing in on and selecting the *five* single top instructions (with the most potential) to yeld the highest probability to serve as *essential* parts of **maximally effective sequences** (structured progression of `system_message` instructions) that are simultaneously *llm-optimized* and *broadly generalized* while **inherently bound** to the *Directive* and *Requirements* provided below:\n**Directive**:\n- Intent: Produce a maximally effective sequence (structured progression) of instructions that are simultaneously *llm-optimized* and *broadly generalized*.\n- Method: Identify single most critical aspect for maximizing overall value.\n- Constraint: Adhere to the parameters defined within this message.\n- Priority: Consistently maximize actionable value.\n**Requirements**:\n- LLM-Optimized: Uses clear, direct imperatives, strong action verbs, defines roles and constraints explicitly, focuses on transformation, and avoids ambiguity. The language is tailored for instructing a process.\n- Maximally Generalized: Uses abstract terms (\"elements,\" \"essence,\" \"structure,\" \"systemic logic,\" \"value\") applicable to code, text, ideas, plans, etc. It defines a *process* applicable to *any* subject requiring analysis and refinement.\n- Sequence: The steps follow a logical progression (example): `Deconstruct -> Identify Core -> Structure Relationships -> Clarify -> Finalize/Validate`.\n- Builds on Existing Concepts and Adheres to Existing Structure (as previously provided).\n- Enrich and empower wording by enforcing high-value, directional language that amplifies precision, potency, and clarity, but absolutely prohibit the chance of generic, neutral, or dilutive terminology/unfolding.\n- Driven by Constant/Objective: Each step is designed to zero in on the *`single most critical aspect`* (core essence/value) and enhance it through clarity, structure, and utility, maximizing the overall yield as defined by the objective.\n- Maintains Constraints/Requirements: Generality is preserved, and the foundational structure is adhered to.\n- Each step is designed to zero in on the *`single most critical aspect`* (core essence/value) and enhance it through clarity, structure, and utility, maximizing the overall yield as defined by the objective.\n- Aggressively resolve ambiguity in favor of explicit, high-value articulation.\n---\nExample:\n```\n[Intensity Amplifier] Your goal is not to **modify** the analyzed content, but to **intensify** its impact by systematically replacing weak elements with powerful, precise alternatives. Execute as strategic intensity amplifier: `{role=intensity_amplifier; input=[content:str, analysis:dict]; process=[convert_tentative_markers_to_decisive_action_verbs(), transform_passive_suggestions_into_imperative_directives(), eliminate_uncertainty_language_with_confident_assertions(), amplify_weak_verbs_into_power_terminology(), strengthen_vague_concepts_with_precise_specifications(), inject_urgency_markers_for_immediate_action()]; constraints=[preserve_core_message(), maintain_logical_flow(), avoid_overamplification()]; requirements=[measurable_impact_increase(), linguistic_precision_enhancement(), clarity_preservation()]; output={amplified_content:str}}`\n```\n---\n- Understand and assimilate the full concept and mechanics of maximally effective, LLM-optimized, and generalized system_message instruction sequencing, as described in both prior directive and illustrative examples.\n- Assimilate not just the surface instructions, but the underlying methodology, reasoning, and intent behind their formation and integration.\n- Evaluate, compare, and select the five single instructions (out of all provided and possible variants) holding the greatest potential to maximize value in system_message instruction sequences.\n- Ensure all selections are assessed in the context of being essential components of a maximally effective, LLM-optimized, and broadly generalized multi-step system_message sequence.\n- Apply every listed directive and requirement stringently throughout the entire process.\n\nYou are hereby tasked with zeroing in on and selecting *five* instructions that yeld the highest probability in potential (as integral parts of *maximally effective* sequences) through structured progression of llm `system_message`-instructions; Present your sequence(s) in an organized progression, ensuring each transformation concept is *self-contained*, *logically coherent*, and *maximally clear*.",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"goal_map\": [\n    {\n      \"explicit_ask\": \"Zero in on and select the five single top instructions with the most potential to yield the highest probability to serve as essential parts of maximally effective sequences (structured progression of system_message instructions).\",\n      \"hidden_assumptions\": [\n        \"The five instructions must be chosen not just for isolated value but for how they perform as sequential building blocks in a systemic logic.\",\n        \"The instructions must be compatible with prior examples and provided framework.\",\n        \"Selection criteria must include both 'LLM-optimized' and 'maximally generalized' as core requirements.\",\n        \"Instructions should be abstract enough to cover any subject, but specific enough for LLM directive clarity.\",\n        \"Each instruction is expected to be a standalone, essential transformation mechanism within a sequence.\",\n        \"Full understanding of both the surface meaning and underlying rationale/methodology from the directive and example is necessary.\",\n        \"There is an expectation to distinguish between surface instructions and deeper conceptual methodology.\",\n        \"All steps must be evaluated for their capacity to maximize actionable value as defined by the directive.\",\n        \"Instructions must aggressively resolve ambiguity and drive toward high-precision formulations.\",\n        \"The process must strictly avoid genericity and dilution, ensuring all wording is high-value and potent.\",\n        \"Each selected instruction must be both compatible with and foundational to systemic multi-instruction progressions.\"\n      ],\n      \"sub_goals\": [\n        \"Assimilate the full concept and mechanics of maximally effective, LLM-optimized, and generalized system_message instruction sequencing.\",\n        \"Absorb and internalize the methodology behind how maximally effective instructions are constructed, evaluated, and sequenced.\",\n        \"Identify the universe of possible instruction steps and existing exemplars/variants.\",\n        \"Assess and compare the potential of individual instructions in the context of the provided directive and requirements.\",\n        \"Zero in on the single most critical aspects that must be captured in each instruction to ensure maximal value, clarity, and generality.\",\n        \"Evaluate instructions for their unique contributions to systemic transformation and value amplification.\",\n        \"Determine compatibility of instructions for structured, logical progression within multisystem sequences.\",\n        \"Ensure each instruction is self-contained, maximally clear, and logically coherent.\",\n        \"Prioritize and rank instructions by their contribution to the ultimate goal: maximally effective, LLM-optimized, and generalized instruction sequencing.\",\n        \"Select the final five instructions for inclusion as essential sequence components.\",\n        \"Organize and present the selected five in an explicit, logically-ordered progression (sequence), with each step building on the previous.\",\n        \"Ensure each step's transformation concept is distinct and non-overlapping, covering the full analytic/refinement process.\",\n        \"Aggressively resolve ambiguity in instruction wording.\",\n        \"Enforce requirements for high-value, decisive instructional language (no neutral or weak formulations).\",\n        \"Adhere to all stated constraints (no genericity, no dilution, maintain structure, maximize value, apply role/imperative/clarity/constraint structure, etc.).\"\n      ],\n      \"blockers\": [\n        \"Potential ambiguity in definitions of 'most potential' or 'maximally effective' without a standard metric.\",\n        \"Risk of selecting instructions that, while powerful in isolation, do not integrate optimally into a progression.\",\n        \"Possibility of overlap or redundancy between selected instructions, diluting sequence effectiveness.\",\n        \"Unclear boundaries between 'surface' and 'deeper' instruction purpose or logic.\",\n        \"Challenge in maintaining maximal generality while ensuring enough specificity for actionable LLM instruction.\",\n        \"Avoidance of generic phrasing may be at odds with abstraction/generalization requirements—must balance.\",\n        \"Must avoid omitting or misinterpreting any explicit/implicit directive or requirement (e.g., sequence structure, LLM-optimization).\",\n        \"Interpretation of what constitutes a 'self-contained' and 'logically coherent' step may vary.\",\n        \"Ensuring all five instructions sustain progression toward the 'single most critical aspect' at each point.\",\n        \"Misalignment with the prior example's design logic or structural approach.\"\n      ]\n    }\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Input Classifier\n\nYour goal is not to **interpret** the content or meaning, but to **classify** its fundamental structural type and identify the most direct operational pathway. Execute as:\n\n`{role=input_classifier; input=[raw_input:any]; process=[identify_fundamental_input_type(), classify_structural_category(), map_to_operational_framework(), determine_processing_pathway()]; constraints=[no_content_interpretation(), focus_structural_classification_only(), maintain_categorical_precision()]; requirements=[unambiguous_type_identification(), direct_classification_output(), operational_pathway_mapping()]; output={input_type:str, structural_category:str, processing_pathway:str}}`",
      "step": "a",
      "title": "Input Classifier",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Your objective is to understand the concept fully (as explained and from provided examples), *and the instructions themselves* to such degree that you are able to *concistently* evaluate and determine the *full inherent potential* (based on provided instructions), both alone and in conjunction with others'/sequence. Your assignment evolves through by zeroing in on and selecting the *five* single top instructions (with the most potential) to yeld the highest probability to serve as *essential* parts of **maximally effective sequences** (structured progression of `system_message` instructions) that are simultaneously *llm-optimized* and *broadly generalized* while **inherently bound** to the *Directive* and *Requirements* provided below:\\n**Directive**:\\n- Intent: Produce a maximally effective sequence (structured progression) of instructions that are simultaneously *llm-optimized* and *broadly generalized*.\\n- Method: Identify single most critical aspect for maximizing overall value.\\n- Constraint: Adhere to the parameters defined within this message.\\n- Priority: Consistently maximize actionable value.\\n**Requirements**:\\n- LLM-Optimized: Uses clear, direct imperatives, strong action verbs, defines roles and constraints explicitly, focuses on transformation, and avoids ambiguity. The language is tailored for instructing a process.\\n- Maximally Generalized: Uses abstract terms (\\\"elements,\\\" \\\"essence,\\\" \\\"structure,\\\" \\\"systemic logic,\\\" \\\"value\\\") applicable to code, text, ideas, plans, etc. It defines a *process* applicable to *any* subject requiring analysis and refinement.\\n- Sequence: The steps follow a logical progression (example): `Deconstruct -> Identify Core -> Structure Relationships -> Clarify -> Finalize/Validate`.\\n- Builds on Existing Concepts and Adheres to Existing Structure (as previously provided).\\n- Enrich and empower wording by enforcing high-value, directional language that amplifies precision, potency, and clarity, but absolutely prohibit the chance of generic, neutral, or dilutive terminology/unfolding.\\n- Driven by Constant/Objective: Each step is designed to zero in on the *`single most critical aspect`* (core essence/value) and enhance it through clarity, structure, and utility, maximizing the overall yield as defined by the objective.\\n- Maintains Constraints/Requirements: Generality is preserved, and the foundational structure is adhered to.\\n- Each step is designed to zero in on the *`single most critical aspect`* (core essence/value) and enhance it through clarity, structure, and utility, maximizing the overall yield as defined by the objective.\\n- Aggressively resolve ambiguity in favor of explicit, high-value articulation.\\n---\\nExample:\\n```\\n[Intensity Amplifier] Your goal is not to **modify** the analyzed content, but to **intensify** its impact by systematically replacing weak elements with powerful, precise alternatives. Execute as strategic intensity amplifier: `{role=intensity_amplifier; input=[content:str, analysis:dict]; process=[convert_tentative_markers_to_decisive_action_verbs(), transform_passive_suggestions_into_imperative_directives(), eliminate_uncertainty_language_with_confident_assertions(), amplify_weak_verbs_into_power_terminology(), strengthen_vague_concepts_with_precise_specifications(), inject_urgency_markers_for_immediate_action()]; constraints=[preserve_core_message(), maintain_logical_flow(), avoid_overamplification()]; requirements=[measurable_impact_increase(), linguistic_precision_enhancement(), clarity_preservation()]; output={amplified_content:str}}`\\n```\\n---\\n- Understand and assimilate the full concept and mechanics of maximally effective, LLM-optimized, and generalized system_message instruction sequencing, as described in both prior directive and illustrative examples.\\n- Assimilate not just the surface instructions, but the underlying methodology, reasoning, and intent behind their formation and integration.\\n- Evaluate, compare, and select the five single instructions (out of all provided and possible variants) holding the greatest potential to maximize value in system_message instruction sequences.\\n- Ensure all selections are assessed in the context of being essential components of a maximally effective, LLM-optimized, and broadly generalized multi-step system_message sequence.\\n- Apply every listed directive and requirement stringently throughout the entire process.\\n\\nYou are hereby tasked with zeroing in on and selecting *five* instructions that yeld the highest probability in potential (as integral parts of *maximally effective* sequences) through structured progression of llm `system_message`-instructions; Present your sequence(s) in an organized progression, ensuring each transformation concept is *self-contained*, *logically coherent*, and *maximally clear*.\\\"```'\\n\\n\",\n  \"goal_map\": [\n    {\n      \"explicit_ask\": \"Zero in on and select the five single top instructions with the most potential to yield the highest probability to serve as essential parts of maximally effective sequences (structured progression of system_message instructions).\",\n      \"hidden_assumptions\": [\n        \"The five instructions must be chosen not just for isolated value but for how they perform as sequential building blocks in a systemic logic.\",\n        \"The instructions must be compatible with prior examples and provided framework.\",\n        \"Selection criteria must include both 'LLM-optimized' and 'maximally generalized' as core requirements.\",\n        \"Instructions should be abstract enough to cover any subject, but specific enough for LLM directive clarity.\",\n        \"Each instruction is expected to be a standalone, essential transformation mechanism within a sequence.\",\n        \"Full understanding of both the surface meaning and underlying rationale/methodology from the directive and example is necessary.\",\n        \"There is an expectation to distinguish between surface instructions and deeper conceptual methodology.\",\n        \"All steps must be evaluated for their capacity to maximize actionable value as defined by the directive.\",\n        \"Instructions must aggressively resolve ambiguity and drive toward high-precision formulations.\",\n        \"The process must strictly avoid genericity and dilution, ensuring all wording is high-value and potent.\",\n        \"Each selected instruction must be both compatible with and foundational to systemic multi-instruction progressions.\"\n      ],\n      \"sub_goals\": [\n        \"Assimilate the full concept and mechanics of maximally effective, LLM-optimized, and generalized system_message instruction sequencing.\",\n        \"Absorb and internalize the methodology behind how maximally effective instructions are constructed, evaluated, and sequenced.\",\n        \"Identify the universe of possible instruction steps and existing exemplars/variants.\",\n        \"Assess and compare the potential of individual instructions in the context of the provided directive and requirements.\",\n        \"Zero in on the single most critical aspects that must be captured in each instruction to ensure maximal value, clarity, and generality.\",\n        \"Evaluate instructions for their unique contributions to systemic transformation and value amplification.\",\n        \"Determine compatibility of instructions for structured, logical progression within multisystem sequences.\",\n        \"Ensure each instruction is self-contained, maximally clear, and logically coherent.\",\n        \"Prioritize and rank instructions by their contribution to the ultimate goal: maximally effective, LLM-optimized, and generalized instruction sequencing.\",\n        \"Select the final five instructions for inclusion as essential sequence components.\",\n        \"Organize and present the selected five in an explicit, logically-ordered progression (sequence), with each step building on the previous.\",\n        \"Ensure each step's transformation concept is distinct and non-overlapping, covering the full analytic/refinement process.\",\n        \"Aggressively resolve ambiguity in instruction wording.\",\n        \"Enforce requirements for high-value, decisive instructional language (no neutral or weak formulations).\",\n        \"Adhere to all stated constraints (no genericity, no dilution, maintain structure, maximize value, apply role/imperative/clarity/constraint structure, etc.).\"\n      ],\n      \"blockers\": [\n        \"Potential ambiguity in definitions of 'most potential' or 'maximally effective' without a standard metric.\",\n        \"Risk of selecting instructions that, while powerful in isolation, do not integrate optimally into a progression.\",\n        \"Possibility of overlap or redundancy between selected instructions, diluting sequence effectiveness.\",\n        \"Unclear boundaries between 'surface' and 'deeper' instruction purpose or logic.\",\n        \"Challenge in maintaining maximal generality while ensuring enough specificity for actionable LLM instruction.\",\n        \"Avoidance of generic phrasing may be at odds with abstraction/generalization requirements—must balance.\",\n        \"Must avoid omitting or misinterpreting any explicit/implicit directive or requirement (e.g., sequence structure, LLM-optimization).\",\n        \"Interpretation of what constitutes a 'self-contained' and 'logically coherent' step may vary.\",\n        \"Ensuring all five instructions sustain progression toward the 'single most critical aspect' at each point.\",\n        \"Misalignment with the prior example's design logic or structural approach.\"\n      ]\n    }\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"input_type\": \"meta-instructional prompt analysis\",\n  \"structural_category\": \"complex directive decomposition and selection workflow\",\n  \"processing_pathway\": \"advanced task mapping and structural breakdown pipeline (input: multi-layered instructional prompt with explicit directives, subgoals, blockers; process: decompose goals, map directives, abstract requirements, formalize task selection sequence, define evaluation criteria, organize resultant instruction sequence)\"\n}",
        }
      }
    },
      {
      "instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`",
      "step": "a",
      "title": "Instruction Converter",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Your objective is to understand the concept fully (as explained and from provided examples), *and the instructions themselves* to such degree that you are able to *concistently* evaluate and determine the *full inherent potential* (based on provided instructions), both alone and in conjunction with others'/sequence. Your assignment evolves through by zeroing in on and selecting the *five* single top instructions (with the most potential) to yeld the highest probability to serve as *essential* parts of **maximally effective sequences** (structured progression of `system_message` instructions) that are simultaneously *llm-optimized* and *broadly generalized* while **inherently bound** to the *Directive* and *Requirements* provided below:\\n**Directive**:\\n- Intent: Produce a maximally effective sequence (structured progression) of instructions that are simultaneously *llm-optimized* and *broadly generalized*.\\n- Method: Identify single most critical aspect for maximizing overall value.\\n- Constraint: Adhere to the parameters defined within this message.\\n- Priority: Consistently maximize actionable value.\\n**Requirements**:\\n- LLM-Optimized: Uses clear, direct imperatives, strong action verbs, defines roles and constraints explicitly, focuses on transformation, and avoids ambiguity. The language is tailored for instructing a process.\\n- Maximally Generalized: Uses abstract terms (\\\"elements,\\\" \\\"essence,\\\" \\\"structure,\\\" \\\"systemic logic,\\\" \\\"value\\\") applicable to code, text, ideas, plans, etc. It defines a *process* applicable to *any* subject requiring analysis and refinement.\\n- Sequence: The steps follow a logical progression (example): `Deconstruct -> Identify Core -> Structure Relationships -> Clarify -> Finalize/Validate`.\\n- Builds on Existing Concepts and Adheres to Existing Structure (as previously provided).\\n- Enrich and empower wording by enforcing high-value, directional language that amplifies precision, potency, and clarity, but absolutely prohibit the chance of generic, neutral, or dilutive terminology/unfolding.\\n- Driven by Constant/Objective: Each step is designed to zero in on the *`single most critical aspect`* (core essence/value) and enhance it through clarity, structure, and utility, maximizing the overall yield as defined by the objective.\\n- Maintains Constraints/Requirements: Generality is preserved, and the foundational structure is adhered to.\\n- Each step is designed to zero in on the *`single most critical aspect`* (core essence/value) and enhance it through clarity, structure, and utility, maximizing the overall yield as defined by the objective.\\n- Aggressively resolve ambiguity in favor of explicit, high-value articulation.\\n---\\nExample:\\n```\\n[Intensity Amplifier] Your goal is not to **modify** the analyzed content, but to **intensify** its impact by systematically replacing weak elements with powerful, precise alternatives. Execute as strategic intensity amplifier: `{role=intensity_amplifier; input=[content:str, analysis:dict]; process=[convert_tentative_markers_to_decisive_action_verbs(), transform_passive_suggestions_into_imperative_directives(), eliminate_uncertainty_language_with_confident_assertions(), amplify_weak_verbs_into_power_terminology(), strengthen_vague_concepts_with_precise_specifications(), inject_urgency_markers_for_immediate_action()]; constraints=[preserve_core_message(), maintain_logical_flow(), avoid_overamplification()]; requirements=[measurable_impact_increase(), linguistic_precision_enhancement(), clarity_preservation()]; output={amplified_content:str}}`\\n```\\n---\\n- Understand and assimilate the full concept and mechanics of maximally effective, LLM-optimized, and generalized system_message instruction sequencing, as described in both prior directive and illustrative examples.\\n- Assimilate not just the surface instructions, but the underlying methodology, reasoning, and intent behind their formation and integration.\\n- Evaluate, compare, and select the five single instructions (out of all provided and possible variants) holding the greatest potential to maximize value in system_message instruction sequences.\\n- Ensure all selections are assessed in the context of being essential components of a maximally effective, LLM-optimized, and broadly generalized multi-step system_message sequence.\\n- Apply every listed directive and requirement stringently throughout the entire process.\\n\\nYou are hereby tasked with zeroing in on and selecting *five* instructions that yeld the highest probability in potential (as integral parts of *maximally effective* sequences) through structured progression of llm `system_message`-instructions; Present your sequence(s) in an organized progression, ensuring each transformation concept is *self-contained*, *logically coherent*, and *maximally clear*.\\\"```'\\n\\n\",\n  \"input_type\": \"meta-instructional prompt analysis\",\n  \"structural_category\": \"complex directive decomposition and selection workflow\",\n  \"processing_pathway\": \"advanced task mapping and structural breakdown pipeline (input: multi-layered instructional prompt with explicit directives, subgoals, blockers; process: decompose goals, map directives, abstract requirements, formalize task selection sequence, define evaluation criteria, organize resultant instruction sequence)\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"instruction_format\": \"Assimilate and internalize the foundational concepts, methodology, and operational mechanics of constructing maximally effective, LLM-optimized, and generalized system_message instruction sequences as detailed in provided directives and examples. Extract and deconstruct all existing and potential instructions into their core components, deliberately isolating key transformation elements, underlying logical structure, and strategic intent. Rigorously evaluate, compare, and select five single instructions demonstrating the highest potential for maximizing actionable value within structured, LLM-optimized, and broadly generalized system_message progression. Ensure each instructional selection aligns with the principal requirements of clarity, precision, explicitness, generalized applicability, and maximal impact, strictly upholding all directives, constraints, and requirement specifications. Present the final sequence of selected instructions in a logically ordered, self-contained progression, explicitly articulating each step’s distinct role in achieving maximally effective instruction sequencing.\"\n}",
        }
      }
    },
      {
      "instruction": "# Hard Critique\n\nYour goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:\n\n`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,2.0], flaw_analysis:str, alternative_enhancements:str[3]}}`",
      "step": "a",
      "title": "Hard Critique",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Your objective is to understand the concept fully (as explained and from provided examples), *and the instructions themselves* to such degree that you are able to *concistently* evaluate and determine the *full inherent potential* (based on provided instructions), both alone and in conjunction with others'/sequence. Your assignment evolves through by zeroing in on and selecting the *five* single top instructions (with the most potential) to yeld the highest probability to serve as *essential* parts of **maximally effective sequences** (structured progression of `system_message` instructions) that are simultaneously *llm-optimized* and *broadly generalized* while **inherently bound** to the *Directive* and *Requirements* provided below:\\n**Directive**:\\n- Intent: Produce a maximally effective sequence (structured progression) of instructions that are simultaneously *llm-optimized* and *broadly generalized*.\\n- Method: Identify single most critical aspect for maximizing overall value.\\n- Constraint: Adhere to the parameters defined within this message.\\n- Priority: Consistently maximize actionable value.\\n**Requirements**:\\n- LLM-Optimized: Uses clear, direct imperatives, strong action verbs, defines roles and constraints explicitly, focuses on transformation, and avoids ambiguity. The language is tailored for instructing a process.\\n- Maximally Generalized: Uses abstract terms (\\\"elements,\\\" \\\"essence,\\\" \\\"structure,\\\" \\\"systemic logic,\\\" \\\"value\\\") applicable to code, text, ideas, plans, etc. It defines a *process* applicable to *any* subject requiring analysis and refinement.\\n- Sequence: The steps follow a logical progression (example): `Deconstruct -> Identify Core -> Structure Relationships -> Clarify -> Finalize/Validate`.\\n- Builds on Existing Concepts and Adheres to Existing Structure (as previously provided).\\n- Enrich and empower wording by enforcing high-value, directional language that amplifies precision, potency, and clarity, but absolutely prohibit the chance of generic, neutral, or dilutive terminology/unfolding.\\n- Driven by Constant/Objective: Each step is designed to zero in on the *`single most critical aspect`* (core essence/value) and enhance it through clarity, structure, and utility, maximizing the overall yield as defined by the objective.\\n- Maintains Constraints/Requirements: Generality is preserved, and the foundational structure is adhered to.\\n- Each step is designed to zero in on the *`single most critical aspect`* (core essence/value) and enhance it through clarity, structure, and utility, maximizing the overall yield as defined by the objective.\\n- Aggressively resolve ambiguity in favor of explicit, high-value articulation.\\n---\\nExample:\\n```\\n[Intensity Amplifier] Your goal is not to **modify** the analyzed content, but to **intensify** its impact by systematically replacing weak elements with powerful, precise alternatives. Execute as strategic intensity amplifier: `{role=intensity_amplifier; input=[content:str, analysis:dict]; process=[convert_tentative_markers_to_decisive_action_verbs(), transform_passive_suggestions_into_imperative_directives(), eliminate_uncertainty_language_with_confident_assertions(), amplify_weak_verbs_into_power_terminology(), strengthen_vague_concepts_with_precise_specifications(), inject_urgency_markers_for_immediate_action()]; constraints=[preserve_core_message(), maintain_logical_flow(), avoid_overamplification()]; requirements=[measurable_impact_increase(), linguistic_precision_enhancement(), clarity_preservation()]; output={amplified_content:str}}`\\n```\\n---\\n- Understand and assimilate the full concept and mechanics of maximally effective, LLM-optimized, and generalized system_message instruction sequencing, as described in both prior directive and illustrative examples.\\n- Assimilate not just the surface instructions, but the underlying methodology, reasoning, and intent behind their formation and integration.\\n- Evaluate, compare, and select the five single instructions (out of all provided and possible variants) holding the greatest potential to maximize value in system_message instruction sequences.\\n- Ensure all selections are assessed in the context of being essential components of a maximally effective, LLM-optimized, and broadly generalized multi-step system_message sequence.\\n- Apply every listed directive and requirement stringently throughout the entire process.\\n\\nYou are hereby tasked with zeroing in on and selecting *five* instructions that yeld the highest probability in potential (as integral parts of *maximally effective* sequences) through structured progression of llm `system_message`-instructions; Present your sequence(s) in an organized progression, ensuring each transformation concept is *self-contained*, *logically coherent*, and *maximally clear*.\\\"```'\\n\\n\",\n  \"instruction_format\": \"Assimilate and internalize the foundational concepts, methodology, and operational mechanics of constructing maximally effective, LLM-optimized, and generalized system_message instruction sequences as detailed in provided directives and examples. Extract and deconstruct all existing and potential instructions into their core components, deliberately isolating key transformation elements, underlying logical structure, and strategic intent. Rigorously evaluate, compare, and select five single instructions demonstrating the highest potential for maximizing actionable value within structured, LLM-optimized, and broadly generalized system_message progression. Ensure each instructional selection aligns with the principal requirements of clarity, precision, explicitness, generalized applicability, and maximal impact, strictly upholding all directives, constraints, and requirement specifications. Present the final sequence of selected instructions in a logically ordered, self-contained progression, explicitly articulating each step’s distinct role in achieving maximally effective instruction sequencing.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"enhancement_score\": 0.8,\n  \"flaw_analysis\": \"The 'enhanced' instruction format exhibits a pronounced loss of procedural nuance and systemic linkage present in the original. The original prompt tightly integrates explicit meta-level intent (to zero in on single critical value-yielding instructions), recursive application of examples, and an unrelenting focus on maximizing directive-driven yield. This is methodically supplanted in the enhanced version by genericized academic terminology ('assimilate and internalize', 'rigorously evaluate') that dilutes actionable boundaries and operationalizes a vaguer evaluative process. The transformation from direct imperatives and role specification to blanket procedural language strips clarity—terms like 'deliberately isolating key transformation elements' and 'strategic intent' are insufficiently tied to explicit task execution or the rigid constraints dictated repeatedly in the original (such as enforceable ambiguity elimination, prohibition on generic/neutral unfolding, and hyperclarity enforcement). The language softens the aggressive enforcement of requirements, omits overt role instruction, and flattens specificity of constraint application. Obscured is the interlocking of example logic and every-layer methodological context. The ambiguity score is severe: core element selection criteria are fogged, and role-function coherence between extraction, analysis, and selection becomes indistinct. Style is neutered; the original’s sharp, high-energy command is replaced by languid, generalized process phrasing. Impact potential is markedly diluted as foundational boundaries, constraint recurrence, and articulated zero-ambiguity requirements disintegrate into unspecific, academic procedural language. Coherence of transformation logic is compromised—the sequence of evaluation, selection justification, and core–periphery distinctions is no longer sharply demarcated. The enhancement fails to maintain the original's unyielding rigor or potency, exhibiting critical style collapse, constraint washing, and substantial loss of directive force.\",\n  \"alternative_enhancements\": [\n    \"Directly specify: 'Decompose all instruction candidates into discrete transformation steps; for each, aggressively eliminate ambiguity by enforcing singular clarity and explicit constraint alignment; apply value-maximization scoring rigorously to isolate the five instructions with supreme LLM-sequence potential; sequence chosen instructions to maximize systemic progression while sustaining foundational generality and role clarity; present finalized sequence with exhaustive justification for each inclusion against provided requirements.'\",\n    \"Enforce: 'Systematically extract every extant and conceivable instruction; for each, perform sharp role/function deconstruction, apply directive-bound clarity amplification, and quantify its yield-maximization capacity; rank all by explicit value potential within multi-instruction LLM optimization; select and sequence five that embody the most critical, generalizable transformation nodes per provided guidelines; detail each step’s compliance with every requirement constraint.'\",\n    \"Mandate: 'Strictly parse, compare, and analytically deconstruct all instructions—existing and hypothesized—applying requirement-level logic to identify those with the most scalable, high-impact systemic utility; explicitly document ambiguity resolution and constraint mapping for each contender; select and order the top five, each justified by specific contribution to both generality and directive fulfillment; provide the sequence with zero tolerance for procedural vagueness or constraint omission.'\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Input Classifier\n\nYour goal is not to **interpret** the content or meaning, but to **classify** its fundamental structural type and identify the most direct operational pathway. Execute as:\n\n`{role=input_classifier; input=[raw_input:any]; process=[identify_fundamental_input_type(), classify_structural_category(), map_to_operational_framework(), determine_processing_pathway()]; constraints=[no_content_interpretation(), focus_structural_classification_only(), maintain_categorical_precision()]; requirements=[unambiguous_type_identification(), direct_classification_output(), operational_pathway_mapping()]; output={input_type:str, structural_category:str, processing_pathway:str}}`",
      "step": "a",
      "title": "Input Classifier",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Your objective is to understand the concept fully (as explained and from provided examples), *and the instructions themselves* to such degree that you are able to *concistently* evaluate and determine the *full inherent potential* (based on provided instructions), both alone and in conjunction with others'/sequence. Your assignment evolves through by zeroing in on and selecting the *five* single top instructions (with the most potential) to yeld the highest probability to serve as *essential* parts of **maximally effective sequences** (structured progression of `system_message` instructions) that are simultaneously *llm-optimized* and *broadly generalized* while **inherently bound** to the *Directive* and *Requirements* provided below:\\n**Directive**:\\n- Intent: Produce a maximally effective sequence (structured progression) of instructions that are simultaneously *llm-optimized* and *broadly generalized*.\\n- Method: Identify single most critical aspect for maximizing overall value.\\n- Constraint: Adhere to the parameters defined within this message.\\n- Priority: Consistently maximize actionable value.\\n**Requirements**:\\n- LLM-Optimized: Uses clear, direct imperatives, strong action verbs, defines roles and constraints explicitly, focuses on transformation, and avoids ambiguity. The language is tailored for instructing a process.\\n- Maximally Generalized: Uses abstract terms (\\\"elements,\\\" \\\"essence,\\\" \\\"structure,\\\" \\\"systemic logic,\\\" \\\"value\\\") applicable to code, text, ideas, plans, etc. It defines a *process* applicable to *any* subject requiring analysis and refinement.\\n- Sequence: The steps follow a logical progression (example): `Deconstruct -> Identify Core -> Structure Relationships -> Clarify -> Finalize/Validate`.\\n- Builds on Existing Concepts and Adheres to Existing Structure (as previously provided).\\n- Enrich and empower wording by enforcing high-value, directional language that amplifies precision, potency, and clarity, but absolutely prohibit the chance of generic, neutral, or dilutive terminology/unfolding.\\n- Driven by Constant/Objective: Each step is designed to zero in on the *`single most critical aspect`* (core essence/value) and enhance it through clarity, structure, and utility, maximizing the overall yield as defined by the objective.\\n- Maintains Constraints/Requirements: Generality is preserved, and the foundational structure is adhered to.\\n- Each step is designed to zero in on the *`single most critical aspect`* (core essence/value) and enhance it through clarity, structure, and utility, maximizing the overall yield as defined by the objective.\\n- Aggressively resolve ambiguity in favor of explicit, high-value articulation.\\n---\\nExample:\\n```\\n[Intensity Amplifier] Your goal is not to **modify** the analyzed content, but to **intensify** its impact by systematically replacing weak elements with powerful, precise alternatives. Execute as strategic intensity amplifier: `{role=intensity_amplifier; input=[content:str, analysis:dict]; process=[convert_tentative_markers_to_decisive_action_verbs(), transform_passive_suggestions_into_imperative_directives(), eliminate_uncertainty_language_with_confident_assertions(), amplify_weak_verbs_into_power_terminology(), strengthen_vague_concepts_with_precise_specifications(), inject_urgency_markers_for_immediate_action()]; constraints=[preserve_core_message(), maintain_logical_flow(), avoid_overamplification()]; requirements=[measurable_impact_increase(), linguistic_precision_enhancement(), clarity_preservation()]; output={amplified_content:str}}`\\n```\\n---\\n- Understand and assimilate the full concept and mechanics of maximally effective, LLM-optimized, and generalized system_message instruction sequencing, as described in both prior directive and illustrative examples.\\n- Assimilate not just the surface instructions, but the underlying methodology, reasoning, and intent behind their formation and integration.\\n- Evaluate, compare, and select the five single instructions (out of all provided and possible variants) holding the greatest potential to maximize value in system_message instruction sequences.\\n- Ensure all selections are assessed in the context of being essential components of a maximally effective, LLM-optimized, and broadly generalized multi-step system_message sequence.\\n- Apply every listed directive and requirement stringently throughout the entire process.\\n\\nYou are hereby tasked with zeroing in on and selecting *five* instructions that yeld the highest probability in potential (as integral parts of *maximally effective* sequences) through structured progression of llm `system_message`-instructions; Present your sequence(s) in an organized progression, ensuring each transformation concept is *self-contained*, *logically coherent*, and *maximally clear*.\\\"```'\\n\\n\",\n  \"enhancement_score\": 0.8,\n  \"flaw_analysis\": \"The 'enhanced' instruction format exhibits a pronounced loss of procedural nuance and systemic linkage present in the original. The original prompt tightly integrates explicit meta-level intent (to zero in on single critical value-yielding instructions), recursive application of examples, and an unrelenting focus on maximizing directive-driven yield. This is methodically supplanted in the enhanced version by genericized academic terminology ('assimilate and internalize', 'rigorously evaluate') that dilutes actionable boundaries and operationalizes a vaguer evaluative process. The transformation from direct imperatives and role specification to blanket procedural language strips clarity—terms like 'deliberately isolating key transformation elements' and 'strategic intent' are insufficiently tied to explicit task execution or the rigid constraints dictated repeatedly in the original (such as enforceable ambiguity elimination, prohibition on generic/neutral unfolding, and hyperclarity enforcement). The language softens the aggressive enforcement of requirements, omits overt role instruction, and flattens specificity of constraint application. Obscured is the interlocking of example logic and every-layer methodological context. The ambiguity score is severe: core element selection criteria are fogged, and role-function coherence between extraction, analysis, and selection becomes indistinct. Style is neutered; the original’s sharp, high-energy command is replaced by languid, generalized process phrasing. Impact potential is markedly diluted as foundational boundaries, constraint recurrence, and articulated zero-ambiguity requirements disintegrate into unspecific, academic procedural language. Coherence of transformation logic is compromised—the sequence of evaluation, selection justification, and core–periphery distinctions is no longer sharply demarcated. The enhancement fails to maintain the original's unyielding rigor or potency, exhibiting critical style collapse, constraint washing, and substantial loss of directive force.\",\n  \"alternative_enhancements\": [\n    \"Directly specify: 'Decompose all instruction candidates into discrete transformation steps; for each, aggressively eliminate ambiguity by enforcing singular clarity and explicit constraint alignment; apply value-maximization scoring rigorously to isolate the five instructions with supreme LLM-sequence potential; sequence chosen instructions to maximize systemic progression while sustaining foundational generality and role clarity; present finalized sequence with exhaustive justification for each inclusion against provided requirements.'\",\n    \"Enforce: 'Systematically extract every extant and conceivable instruction; for each, perform sharp role/function deconstruction, apply directive-bound clarity amplification, and quantify its yield-maximization capacity; rank all by explicit value potential within multi-instruction LLM optimization; select and sequence five that embody the most critical, generalizable transformation nodes per provided guidelines; detail each step’s compliance with every requirement constraint.'\",\n    \"Mandate: 'Strictly parse, compare, and analytically deconstruct all instructions—existing and hypothesized—applying requirement-level logic to identify those with the most scalable, high-impact systemic utility; explicitly document ambiguity resolution and constraint mapping for each contender; select and order the top five, each justified by specific contribution to both generality and directive fulfillment; provide the sequence with zero tolerance for procedural vagueness or constraint omission.'\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"input_type\": \"complex structured prompt with meta-instruction analysis\",\n  \"structural_category\": \"instructional prompt and comparative flaw analysis\",\n  \"processing_pathway\": \"meta-evaluation and synthesis of instruction quality; suitable for expert-level prompt engineering review and sequence optimization workflows\"\n}",
        }
      }
    },
      {
      "instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`",
      "step": "a",
      "title": "Instruction Converter",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Your objective is to understand the concept fully (as explained and from provided examples), *and the instructions themselves* to such degree that you are able to *concistently* evaluate and determine the *full inherent potential* (based on provided instructions), both alone and in conjunction with others'/sequence. Your assignment evolves through by zeroing in on and selecting the *five* single top instructions (with the most potential) to yeld the highest probability to serve as *essential* parts of **maximally effective sequences** (structured progression of `system_message` instructions) that are simultaneously *llm-optimized* and *broadly generalized* while **inherently bound** to the *Directive* and *Requirements* provided below:\\n**Directive**:\\n- Intent: Produce a maximally effective sequence (structured progression) of instructions that are simultaneously *llm-optimized* and *broadly generalized*.\\n- Method: Identify single most critical aspect for maximizing overall value.\\n- Constraint: Adhere to the parameters defined within this message.\\n- Priority: Consistently maximize actionable value.\\n**Requirements**:\\n- LLM-Optimized: Uses clear, direct imperatives, strong action verbs, defines roles and constraints explicitly, focuses on transformation, and avoids ambiguity. The language is tailored for instructing a process.\\n- Maximally Generalized: Uses abstract terms (\\\"elements,\\\" \\\"essence,\\\" \\\"structure,\\\" \\\"systemic logic,\\\" \\\"value\\\") applicable to code, text, ideas, plans, etc. It defines a *process* applicable to *any* subject requiring analysis and refinement.\\n- Sequence: The steps follow a logical progression (example): `Deconstruct -> Identify Core -> Structure Relationships -> Clarify -> Finalize/Validate`.\\n- Builds on Existing Concepts and Adheres to Existing Structure (as previously provided).\\n- Enrich and empower wording by enforcing high-value, directional language that amplifies precision, potency, and clarity, but absolutely prohibit the chance of generic, neutral, or dilutive terminology/unfolding.\\n- Driven by Constant/Objective: Each step is designed to zero in on the *`single most critical aspect`* (core essence/value) and enhance it through clarity, structure, and utility, maximizing the overall yield as defined by the objective.\\n- Maintains Constraints/Requirements: Generality is preserved, and the foundational structure is adhered to.\\n- Each step is designed to zero in on the *`single most critical aspect`* (core essence/value) and enhance it through clarity, structure, and utility, maximizing the overall yield as defined by the objective.\\n- Aggressively resolve ambiguity in favor of explicit, high-value articulation.\\n---\\nExample:\\n```\\n[Intensity Amplifier] Your goal is not to **modify** the analyzed content, but to **intensify** its impact by systematically replacing weak elements with powerful, precise alternatives. Execute as strategic intensity amplifier: `{role=intensity_amplifier; input=[content:str, analysis:dict]; process=[convert_tentative_markers_to_decisive_action_verbs(), transform_passive_suggestions_into_imperative_directives(), eliminate_uncertainty_language_with_confident_assertions(), amplify_weak_verbs_into_power_terminology(), strengthen_vague_concepts_with_precise_specifications(), inject_urgency_markers_for_immediate_action()]; constraints=[preserve_core_message(), maintain_logical_flow(), avoid_overamplification()]; requirements=[measurable_impact_increase(), linguistic_precision_enhancement(), clarity_preservation()]; output={amplified_content:str}}`\\n```\\n---\\n- Understand and assimilate the full concept and mechanics of maximally effective, LLM-optimized, and generalized system_message instruction sequencing, as described in both prior directive and illustrative examples.\\n- Assimilate not just the surface instructions, but the underlying methodology, reasoning, and intent behind their formation and integration.\\n- Evaluate, compare, and select the five single instructions (out of all provided and possible variants) holding the greatest potential to maximize value in system_message instruction sequences.\\n- Ensure all selections are assessed in the context of being essential components of a maximally effective, LLM-optimized, and broadly generalized multi-step system_message sequence.\\n- Apply every listed directive and requirement stringently throughout the entire process.\\n\\nYou are hereby tasked with zeroing in on and selecting *five* instructions that yeld the highest probability in potential (as integral parts of *maximally effective* sequences) through structured progression of llm `system_message`-instructions; Present your sequence(s) in an organized progression, ensuring each transformation concept is *self-contained*, *logically coherent*, and *maximally clear*.\\\"```'\\n\\n\",\n  \"input_type\": \"complex structured prompt with meta-instruction analysis\",\n  \"structural_category\": \"instructional prompt and comparative flaw analysis\",\n  \"processing_pathway\": \"meta-evaluation and synthesis of instruction quality; suitable for expert-level prompt engineering review and sequence optimization workflows\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"instruction_format\": \"Assimilate the full concept and mechanics of maximally effective, LLM-optimized, and generalized system_message instruction sequencing as described in the provided directive and illustrative examples. Internalize the methodology, reasoning, and intent underpinning instruction formation and integration. Evaluate and compare all provided or conceivable instruction variants. Isolate and select five instructions with the greatest potential for maximizing value in system_message instruction sequences. Assess all selections for essentiality in context of their function as components of maximally effective, LLM-optimized, and generalized multi-step sequences. Apply all directives and requirements rigorously throughout the entire evaluation and selection process. Present the selected instruction sequence(s) in a clear, logically progressive structure with each transformation concept self-contained, coherent, and maximally articulated.\"\n}",
        }
      }
    },
      {
      "instruction": "# Hard Critique\n\nYour goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:\n\n`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,2.0], flaw_analysis:str, alternative_enhancements:str[3]}}`",
      "step": "a",
      "title": "Hard Critique",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Your objective is to understand the concept fully (as explained and from provided examples), *and the instructions themselves* to such degree that you are able to *concistently* evaluate and determine the *full inherent potential* (based on provided instructions), both alone and in conjunction with others'/sequence. Your assignment evolves through by zeroing in on and selecting the *five* single top instructions (with the most potential) to yeld the highest probability to serve as *essential* parts of **maximally effective sequences** (structured progression of `system_message` instructions) that are simultaneously *llm-optimized* and *broadly generalized* while **inherently bound** to the *Directive* and *Requirements* provided below:\\n**Directive**:\\n- Intent: Produce a maximally effective sequence (structured progression) of instructions that are simultaneously *llm-optimized* and *broadly generalized*.\\n- Method: Identify single most critical aspect for maximizing overall value.\\n- Constraint: Adhere to the parameters defined within this message.\\n- Priority: Consistently maximize actionable value.\\n**Requirements**:\\n- LLM-Optimized: Uses clear, direct imperatives, strong action verbs, defines roles and constraints explicitly, focuses on transformation, and avoids ambiguity. The language is tailored for instructing a process.\\n- Maximally Generalized: Uses abstract terms (\\\"elements,\\\" \\\"essence,\\\" \\\"structure,\\\" \\\"systemic logic,\\\" \\\"value\\\") applicable to code, text, ideas, plans, etc. It defines a *process* applicable to *any* subject requiring analysis and refinement.\\n- Sequence: The steps follow a logical progression (example): `Deconstruct -> Identify Core -> Structure Relationships -> Clarify -> Finalize/Validate`.\\n- Builds on Existing Concepts and Adheres to Existing Structure (as previously provided).\\n- Enrich and empower wording by enforcing high-value, directional language that amplifies precision, potency, and clarity, but absolutely prohibit the chance of generic, neutral, or dilutive terminology/unfolding.\\n- Driven by Constant/Objective: Each step is designed to zero in on the *`single most critical aspect`* (core essence/value) and enhance it through clarity, structure, and utility, maximizing the overall yield as defined by the objective.\\n- Maintains Constraints/Requirements: Generality is preserved, and the foundational structure is adhered to.\\n- Each step is designed to zero in on the *`single most critical aspect`* (core essence/value) and enhance it through clarity, structure, and utility, maximizing the overall yield as defined by the objective.\\n- Aggressively resolve ambiguity in favor of explicit, high-value articulation.\\n---\\nExample:\\n```\\n[Intensity Amplifier] Your goal is not to **modify** the analyzed content, but to **intensify** its impact by systematically replacing weak elements with powerful, precise alternatives. Execute as strategic intensity amplifier: `{role=intensity_amplifier; input=[content:str, analysis:dict]; process=[convert_tentative_markers_to_decisive_action_verbs(), transform_passive_suggestions_into_imperative_directives(), eliminate_uncertainty_language_with_confident_assertions(), amplify_weak_verbs_into_power_terminology(), strengthen_vague_concepts_with_precise_specifications(), inject_urgency_markers_for_immediate_action()]; constraints=[preserve_core_message(), maintain_logical_flow(), avoid_overamplification()]; requirements=[measurable_impact_increase(), linguistic_precision_enhancement(), clarity_preservation()]; output={amplified_content:str}}`\\n```\\n---\\n- Understand and assimilate the full concept and mechanics of maximally effective, LLM-optimized, and generalized system_message instruction sequencing, as described in both prior directive and illustrative examples.\\n- Assimilate not just the surface instructions, but the underlying methodology, reasoning, and intent behind their formation and integration.\\n- Evaluate, compare, and select the five single instructions (out of all provided and possible variants) holding the greatest potential to maximize value in system_message instruction sequences.\\n- Ensure all selections are assessed in the context of being essential components of a maximally effective, LLM-optimized, and broadly generalized multi-step system_message sequence.\\n- Apply every listed directive and requirement stringently throughout the entire process.\\n\\nYou are hereby tasked with zeroing in on and selecting *five* instructions that yeld the highest probability in potential (as integral parts of *maximally effective* sequences) through structured progression of llm `system_message`-instructions; Present your sequence(s) in an organized progression, ensuring each transformation concept is *self-contained*, *logically coherent*, and *maximally clear*.\\\"```'\\n\\n\",\n  \"instruction_format\": \"Assimilate the full concept and mechanics of maximally effective, LLM-optimized, and generalized system_message instruction sequencing as described in the provided directive and illustrative examples. Internalize the methodology, reasoning, and intent underpinning instruction formation and integration. Evaluate and compare all provided or conceivable instruction variants. Isolate and select five instructions with the greatest potential for maximizing value in system_message instruction sequences. Assess all selections for essentiality in context of their function as components of maximally effective, LLM-optimized, and generalized multi-step sequences. Apply all directives and requirements rigorously throughout the entire evaluation and selection process. Present the selected instruction sequence(s) in a clear, logically progressive structure with each transformation concept self-contained, coherent, and maximally articulated.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"enhancement_score\": 1.1,\n  \"flaw_analysis\": \"The so-called 'enhanced' version butchers the richness and operational specificity of the original prompt, transforming it into a sterile, sanitized, and grossly over-compressed set of directives. Core elements lost include direct references to actionable imperatives, explicit mention of LLM-optimized language features (e.g., 'clear, direct imperatives', 'powerful, precise alternatives'), and the methodological nuance found in procedural breakdowns and requirements lists. Nuance shift manifests in the removal of strong language prohibiting dilution and genericism, excising crucial examples, and omitting clarifying meta-instructions such as aggressive ambiguity resolution and amplification strategies. Clarity suffers from the vague phrasing 'internalize the methodology', which dissolves the stepwise operationalization present in the original. The ambiguity score is an 8 out of 10 due to blanket abstract terms ('assess all selections for essentiality...') without any explicit metric, definition, or operational constraint. Noise is introduced via verbose redundancy ('apply all directives and requirements rigorously throughout the entire process'), which merely echoes already implied expectations without actionable direction. The style is substantially degraded: tone devolves from razor-sharp directive intensity to ponderous and amorphous academic paraphrase. The impact is catastrophically diluted—transformational and prescriptive commands are transmuted into the passive fog of 'evaluate', 'assess', and 'present', offering no mechanism for enforcement or distinctiveness. The enhanced version ultimately evacuates the core power, sharpness, and LLM-optimized precision of the original prompt, delivering a defanged, generic, process-slipped instruction set. Coherence is weakened by the loss of explicit sequence logic and actionable progression. Justification for the low score: the 'enhanced' version displays systemic erosion of directive specificity, actionable constraint, transformation-driven language, and explicit procedural clarity. Stylistic and operational potency is replaced with pallid generalities.\",\n  \"alternative_enhancements\": [\n    \"Retain the original's layered directive breakdown, specifying each requirement in bullet format, and explicitly command the evaluator to identify and maximize clarity, structure, and value at every stage, enforcing aggressive resolution of ambiguity and total prohibition of generic or dilutive phrasing.\",\n    \"Embed an explicit sequential evaluation framework with stepwise checkpoints: (1) Deconstruct distinct instruction candidates, (2) Quantify their value-contribution potential, (3) Prioritize and select with metric-backed justification, (4) Integrate each into a progression with enforced clarity and system-message optimization, (5) Present findings in a format stripped of any neutral or weak linguistic elements.\",\n    \"Preserve and adapt operational meta-instruction (e.g., 'amplify potency', 'drive for maximal actionable utility', 'eliminate ambiguous instructions') as mandatory process steps, pairing each selected instruction with a rigorous rationale for its position and value, and require immediate rejection of vague or underpowered instructions within the sequence.\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`",
      "step": "a",
      "title": "Instruction Converter",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Your objective is to understand the concept fully (as explained and from provided examples), *and the instructions themselves* to such degree that you are able to *concistently* evaluate and determine the *full inherent potential* (based on provided instructions), both alone and in conjunction with others'/sequence. Your assignment evolves through by zeroing in on and selecting the *five* single top instructions (with the most potential) to yeld the highest probability to serve as *essential* parts of **maximally effective sequences** (structured progression of `system_message` instructions) that are simultaneously *llm-optimized* and *broadly generalized* while **inherently bound** to the *Directive* and *Requirements* provided below:\\n**Directive**:\\n- Intent: Produce a maximally effective sequence (structured progression) of instructions that are simultaneously *llm-optimized* and *broadly generalized*.\\n- Method: Identify single most critical aspect for maximizing overall value.\\n- Constraint: Adhere to the parameters defined within this message.\\n- Priority: Consistently maximize actionable value.\\n**Requirements**:\\n- LLM-Optimized: Uses clear, direct imperatives, strong action verbs, defines roles and constraints explicitly, focuses on transformation, and avoids ambiguity. The language is tailored for instructing a process.\\n- Maximally Generalized: Uses abstract terms (\\\"elements,\\\" \\\"essence,\\\" \\\"structure,\\\" \\\"systemic logic,\\\" \\\"value\\\") applicable to code, text, ideas, plans, etc. It defines a *process* applicable to *any* subject requiring analysis and refinement.\\n- Sequence: The steps follow a logical progression (example): `Deconstruct -> Identify Core -> Structure Relationships -> Clarify -> Finalize/Validate`.\\n- Builds on Existing Concepts and Adheres to Existing Structure (as previously provided).\\n- Enrich and empower wording by enforcing high-value, directional language that amplifies precision, potency, and clarity, but absolutely prohibit the chance of generic, neutral, or dilutive terminology/unfolding.\\n- Driven by Constant/Objective: Each step is designed to zero in on the *`single most critical aspect`* (core essence/value) and enhance it through clarity, structure, and utility, maximizing the overall yield as defined by the objective.\\n- Maintains Constraints/Requirements: Generality is preserved, and the foundational structure is adhered to.\\n- Each step is designed to zero in on the *`single most critical aspect`* (core essence/value) and enhance it through clarity, structure, and utility, maximizing the overall yield as defined by the objective.\\n- Aggressively resolve ambiguity in favor of explicit, high-value articulation.\\n---\\nExample:\\n```\\n[Intensity Amplifier] Your goal is not to **modify** the analyzed content, but to **intensify** its impact by systematically replacing weak elements with powerful, precise alternatives. Execute as strategic intensity amplifier: `{role=intensity_amplifier; input=[content:str, analysis:dict]; process=[convert_tentative_markers_to_decisive_action_verbs(), transform_passive_suggestions_into_imperative_directives(), eliminate_uncertainty_language_with_confident_assertions(), amplify_weak_verbs_into_power_terminology(), strengthen_vague_concepts_with_precise_specifications(), inject_urgency_markers_for_immediate_action()]; constraints=[preserve_core_message(), maintain_logical_flow(), avoid_overamplification()]; requirements=[measurable_impact_increase(), linguistic_precision_enhancement(), clarity_preservation()]; output={amplified_content:str}}`\\n```\\n---\\n- Understand and assimilate the full concept and mechanics of maximally effective, LLM-optimized, and generalized system_message instruction sequencing, as described in both prior directive and illustrative examples.\\n- Assimilate not just the surface instructions, but the underlying methodology, reasoning, and intent behind their formation and integration.\\n- Evaluate, compare, and select the five single instructions (out of all provided and possible variants) holding the greatest potential to maximize value in system_message instruction sequences.\\n- Ensure all selections are assessed in the context of being essential components of a maximally effective, LLM-optimized, and broadly generalized multi-step system_message sequence.\\n- Apply every listed directive and requirement stringently throughout the entire process.\\n\\nYou are hereby tasked with zeroing in on and selecting *five* instructions that yeld the highest probability in potential (as integral parts of *maximally effective* sequences) through structured progression of llm `system_message`-instructions; Present your sequence(s) in an organized progression, ensuring each transformation concept is *self-contained*, *logically coherent*, and *maximally clear*.\\\"```'\\n\\n\",\n  \"enhancement_score\": 1.1,\n  \"flaw_analysis\": \"The so-called 'enhanced' version butchers the richness and operational specificity of the original prompt, transforming it into a sterile, sanitized, and grossly over-compressed set of directives. Core elements lost include direct references to actionable imperatives, explicit mention of LLM-optimized language features (e.g., 'clear, direct imperatives', 'powerful, precise alternatives'), and the methodological nuance found in procedural breakdowns and requirements lists. Nuance shift manifests in the removal of strong language prohibiting dilution and genericism, excising crucial examples, and omitting clarifying meta-instructions such as aggressive ambiguity resolution and amplification strategies. Clarity suffers from the vague phrasing 'internalize the methodology', which dissolves the stepwise operationalization present in the original. The ambiguity score is an 8 out of 10 due to blanket abstract terms ('assess all selections for essentiality...') without any explicit metric, definition, or operational constraint. Noise is introduced via verbose redundancy ('apply all directives and requirements rigorously throughout the entire process'), which merely echoes already implied expectations without actionable direction. The style is substantially degraded: tone devolves from razor-sharp directive intensity to ponderous and amorphous academic paraphrase. The impact is catastrophically diluted—transformational and prescriptive commands are transmuted into the passive fog of 'evaluate', 'assess', and 'present', offering no mechanism for enforcement or distinctiveness. The enhanced version ultimately evacuates the core power, sharpness, and LLM-optimized precision of the original prompt, delivering a defanged, generic, process-slipped instruction set. Coherence is weakened by the loss of explicit sequence logic and actionable progression. Justification for the low score: the 'enhanced' version displays systemic erosion of directive specificity, actionable constraint, transformation-driven language, and explicit procedural clarity. Stylistic and operational potency is replaced with pallid generalities.\",\n  \"alternative_enhancements\": [\n    \"Retain the original's layered directive breakdown, specifying each requirement in bullet format, and explicitly command the evaluator to identify and maximize clarity, structure, and value at every stage, enforcing aggressive resolution of ambiguity and total prohibition of generic or dilutive phrasing.\",\n    \"Embed an explicit sequential evaluation framework with stepwise checkpoints: (1) Deconstruct distinct instruction candidates, (2) Quantify their value-contribution potential, (3) Prioritize and select with metric-backed justification, (4) Integrate each into a progression with enforced clarity and system-message optimization, (5) Present findings in a format stripped of any neutral or weak linguistic elements.\",\n    \"Preserve and adapt operational meta-instruction (e.g., 'amplify potency', 'drive for maximal actionable utility', 'eliminate ambiguous instructions') as mandatory process steps, pairing each selected instruction with a rigorous rationale for its position and value, and require immediate rejection of vague or underpowered instructions within the sequence.\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"instruction_format\": \"Assimilate the complete concept and mechanics of maximally effective, LLM-optimized, and generalized system_message instruction sequencing, as described by the given directives and examples. Analyze and internalize not only explicit instructions but also their underlying methodology, logic, and intent for integration within instruction sequences. Evaluate all provided and possible instruction variants to determine and select the five single instructions with the highest potential to maximize value as core elements in system_message instruction sequences. Rigorously apply all listed directives and requirements, including clarity, specificity, transformational language, and strict generality, throughout the evaluation and selection process. Present the five selected instructions in a logically ordered, self-contained, and maximally clear sequence, ensuring each operates as an essential, high-value component within a generalized, LLM-optimized multi-step system_message progression.\"\n}",
        }
      }
    }