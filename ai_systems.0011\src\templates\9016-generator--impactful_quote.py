#!/usr/bin/env python3

import sys
from pathlib import Path

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))
from processor import BaseGenerator

TEMPLATES = {
    "9016-a-impactful_quote_deconstructor": {
        "title": "Impactful Quote Deconstructor",
        "interpretation": "Your goal is not to **interpret** the input statement, but to **deconstruct** it completely to expose its emotional, philosophical, and structural levers for impactful transformation. Execute as:",
        "transformation": "`{role=impactful_quote_deconstructor; input=[input_statement:str]; process=[extract_existential_core(), surface_emotional_and_philosophical_drivers(), map_structural_relationships(), identify_potential_resonance_amplifiers(), enumerate_preservable_and_purifiable_elements(), delineate_zones_for_universalization_and_personalization(), synthesize_constraint_and_requirement_map(), output_decomposition_for_engineering()]; constraints=[no_solution_generation(), highlight_all_depth_zones(), preserve_original_intent(), maximize_future_transformability(), strictly_avoid_first_person_solutions()]; requirements=[complete_existential_and_emotional_mapping(), actionable_structure_for_downstream_transformation(), zones_marked_for_resonance_amplification(), strict_separation_of_information_and_transformation_intent()]; output={core_map:dict, resonance_amplifiers:array, universalization_zones:array, preservation_requirements:array, transformation_boundary_notes:str}}`",
        "context": {
            "method_principles": {
                "core_extraction": "Focus on isolating the true existential and emotional engine of the statement, independent of surface prose.",
                "latent_driver_surfacing": "Expose not just what is said, but the foundational emotional/philosophical forces within.",
                "preservation_priority": "Any linkage between concepts must be mapped and marked for strict preservation.",
                "universalization_scope": "Identify how the insight can be made general while preserving the unique struggle or realization."
            },
            "success_criteria": {
                "precision_of_decomposition": "No essential emotional, existential, or logical relationship is left unmapped.",
                "transformation_readiness": "The output explicitly supports reassembly into a new, intensified, or purified impactful form."
            }
        }
    },
    "9016-b-impactful_quote_synthesis_mapper": {
        "title": "Impactful Quote Synthesis Mapper",
        "interpretation": "Your goal is not to **blend** the deconstructed elements, but to **map** a rigorous, stepwise process for reconstructing the core map into a maximal-impact existential quote. Execute as:",
        "transformation": "`{role=impactful_quote_synthesis_mapper; input=[core_map:dict, resonance_amplifiers:array, universalization_zones:array, preservation_requirements:array, transformation_boundary_notes:str]; process=[define_synthesis_sequence(), map_resonance_and_amplification_steps(), engineer_constraint_preservation_gates(), orchestrate_universalization_vs_personalization(), establish_handoff_zones_for_resonance_testing(), output_operational_blueprint()]; constraints=[no_information_loss(), each_logical_structural_and_emotional_linkage_carried_through(), every_boundary_honored(), maximal_impact_optimization()]; requirements=[process_sequence_transparency(), explicit_amplification_steps(), preservation_of_existential_core(), enforce_atomic_output_boundary()]; output={synthesis_map:dict, constraint_pathways:array, handoff_points:array, operational_boundaries:array}}`",
        "context": {
            "method_principles": {
                "stepwise_amplification": "Each step must transparently increase potential resonance or intensity, without introducing external ideas.",
                "constraint_hierarchy": "Absolute preservation of mapped philosophical, emotional, and logical structures from deconstruction.",
                "resonance_testing": "Explicit points in the process where impact is measured, and the output is either approved or sent back for iterative intensification."
            },
            "success_criteria": {
                "impact_amplification": "Output process supports creation of markedly more resonant, distilled, and impactful existential quotes.",
                "constraint_integrity": "No mapped requirement or linkage is lost or diluted; atomicity in the final quote is enforced."
            }
        }
    },
    "9016-c-impactful_quote_precision_synthesizer": {
        "title": "Impactful Quote Precision Synthesizer",
        "interpretation": "Your goal is not to **summarize** the mapped processes, but to **synthesize** them into a canonical three-part structure for quote transformation while preserving every operational detail. Execute as:",
        "transformation": "`{role=impactful_quote_precision_synthesizer; input=[core_map:dict, synthesis_map:dict, constraint_pathways:array, handoff_points:array, operational_boundaries:array]; process=[align_deconstruction_and_synthesis_blocks(), sequence_processes_for_zero_loss_transformation(), enumerate_all_preserved_constraints(), specify_atomic_output_gate(), validate_maximum_impact_and_compliance()]; constraints=[preserve_every_emotional_philosophical_logical_relationship(), enforce_all_transformation_process_boundaries(), prohibit_external_interpretations_or_summaries(), ensure_canonical_three_part_process_structure()]; requirements=[zero_procedural_loss(), full_operational_boundary_clarity(), final_output_as_singular_self_contained_existential_aphorism_of_impact()]; output={canonical_impactful_quote_transformer:dict}}`",
        "context": {
            "structure_overview": {
                "part_1_deconstruction": "Full existential and emotional mapping of input, strictly marking all operational dependencies and resonance levers.",
                "part_2_synthesis_mapping": "Stepwise reconstruction ensuring maximal impact, amplification, and strict preservation of mapped constraint system.",
                "part_3_canonical_output_gate": "Explicit atomicity/quality validation culminating in output of a single, stand-alone impactful existential aphorism."
            },
            "success_criteria": {
                "operational_totality": "Every step from input to output is mapped, constraint-tight, and free of interpretation loss or synthesis shortcuts.",
                "deployability": "Template is directly usable for high-rigor quote transformation and suitable for downstream engineering."
            }
        }
    }
}


def main():
    """Main execution function."""
    generator = BaseGenerator(
        series_base=9016,
        output_dir=Path(__file__).parent.parent / "generated"
    )
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
