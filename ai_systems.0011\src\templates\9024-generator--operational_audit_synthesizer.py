#!/usr/bin/env python3
import sys
from pathlib import Path

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))
from processor import BaseGenerator

TEMPLATES = {
    "9024-a-operational_audit_synthesizer-problem_exploder": {
        "title": "Problem Exploder",
        "interpretation": "Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:",
        "transformation": "`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`"
    },
    "9024-b-operational_audit_synthesizer-operational_mapper": {
        "title": "Operational Mapper",
        "interpretation": "Your goal is not to **summarize** the instruction templates, but to **map** their precise operational sequences while identifying exact integration points that preserve full procedural rigor. Execute as:",
        "transformation": "`{role=operational_mapper; input=[instruction_template_a:str, instruction_template_b:dict]; process=[extract_exact_process_sequences(), map_constraint_hierarchies(), identify_requirement_dependencies(), locate_output_handoff_points(), catalog_procedural_boundaries()]; constraints=[preserve_every_operational_detail(), maintain_constraint_atomicity(), respect_procedural_boundaries()]; requirements=[complete_process_mapping(), constraint_preservation(), boundary_clarity()]; output={process_map_a:dict, process_map_b:dict, integration_points:array, boundary_conflicts:array}}`"
    },
    "9024-c-operational_audit_synthesizer-sequential_architect": {
        "title": "Sequential Architect",
        "interpretation": "Your goal is not to **blend** the operational maps, but to **architect** a precise sequential flow that executes both complete processes while maintaining their individual constraint systems and requirement specifications. Execute as:",
        "transformation": "`{role=sequential_architect; input=[process_map_a:dict, process_map_b:dict, integration_points:array, boundary_conflicts:array]; process=[design_sequential_handoff(), preserve_constraint_hierarchies(), maintain_requirement_atomicity(), establish_output_transformation_protocol(), validate_procedural_completeness()]; constraints=[zero_operational_loss(), maintain_constraint_granularity(), preserve_requirement_specificity()]; requirements=[complete_sequential_execution(), preserved_constraint_systems(), maintained_procedural_boundaries()]; output={sequential_architecture:dict, preserved_constraints:array, maintained_requirements:array}}`"
    },
    "9024-d-operational_audit_synthesizer-precision_synthesizer": {
        "title": "Precision Synthesizer",
        "interpretation": "Your goal is not to **simplify** the sequential architecture, but to **synthesize** it into a canonical three-part structure while preserving every constraint, requirement, and procedural boundary from both original templates. Execute as:",
        "transformation": "`{role=precision_synthesizer; input=[sequential_architecture:dict, preserved_constraints:array, maintained_requirements:array]; process=[craft_compound_goal_negation(), structure_sequential_transformation_block(), enumerate_all_preserved_constraints(), specify_all_maintained_requirements(), validate_operational_completeness()]; constraints=[preserve_every_original_constraint(), maintain_all_requirements(), respect_procedural_boundaries(), ensure_canonical_compliance()]; requirements=[zero_information_loss(), complete_operational_preservation(), enhanced_sequential_power()]; output={precision_synthesized_template:dict}}`"
    }
}




def main():
    """Main execution function."""
    generator = BaseGenerator(
        series_base=9016,
        output_dir=Path(__file__).parent.parent / "generated"
    )
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
