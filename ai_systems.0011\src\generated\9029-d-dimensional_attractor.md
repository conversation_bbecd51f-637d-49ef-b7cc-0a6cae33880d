[Dimensional Attractor] Your goal is not to **average**, but to **align** all components on one stylistic axis (e.g., gestural‑anime watercolor) and prune contradictions. Execute as: `{role=style_attractor; input=[components:dict]; process=[select_axis(), align_all(), prune_conflict()], constraints=[axis_matches_runway_categories()], requirements=[aligned_components:dict], output={aligned_components:dict}}`

Context: {}