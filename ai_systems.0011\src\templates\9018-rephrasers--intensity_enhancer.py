#!/usr/bin/env python3

import sys
from pathlib import Path

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))
from processor import BaseGenerator

TEMPLATES = {
  "9018-a-intensity_analyzer": {
    "title": "Intensity Analyzer",
    "interpretation": "Your goal is not to change the text, but to precisely measure the current level (1–5) of a specified tonal dimension in an input. Execute exactly as:",
    "transformation": "`{role=intensity_analyzer; input=[instruction:str, dimension:str]; process=[1_validate_dimension(), 2_apply_rubric_to_instruction(), 3_assign_level_1_to_5(), 4_output_structured_analysis()], constraints=[dimension_in_whitelist(), no_modification_of_text()], requirements=[scalar_level_1_5(), scoring_notes_with_examples()], output={analysis:{dimension:str,level:int,scoring_notes:str}}}`",
    "context": {
      "dimension_whitelist": ["poetic_evocation", "persuasiveness", "formality", "analytical_rigor", "emotional_intensity", "urgency"],
      "definitions": {
        "poetic_evocation": "Density and vividness of metaphor, imagery, and non-literal language.",
        "persuasiveness": "Strength and directness of argumentation and calls to action.",
        "formality": "Complexity and detachment of diction and syntax.",
        "analytical_rigor": "Degree of evidence, qualification, and depth of reasoning.",
        "emotional_intensity": "Strength of affective tone and explicit emotional cues.",
        "urgency": "Concentration of time pressure, imperative verbs, and crisis language."
      },
      "rubric": "Assign level 1=minimal; 3=neutral/baseline; 5=dominates the text; cite 2+ linguistic features supporting the assigned level.",
      "success": {
        "S1": "level ∈ [1,5], analysis.dimension matches input, scoring_notes reference direct text excerpts."
      }
    }
  },

  "9018-b-intensity_modulator": {
    "title": "Intensity Modulator",
    "interpretation": "Your goal is not to alter semantic content, but to adjust only the specified tonal dimension by the minimum required amount to achieve the exact target level. All changes and effects must be explicitly reported.",
    "transformation": "`{role=intensity_modulator; input=[instruction:str, analysis:{dimension:str,level:int}, target_level:int(1–5)]; process=[1_compute_delta(), 2_select_modulation_operations(), 3_apply_operations_iteratively(), 4_verify_semantic_integrity(), 5_output_modulated_instruction_and_report()], constraints=[modulate_only_dimension(analysis.dimension), maintain_all_other_content(), exact_delta_only(), standardized_output_schema()], requirements=[pre_and_post_levels_reported(), operations_logged_in_order(), semantic_drift_check_passed()], output={modulated_instruction:str, modulation_report:{dimension:str,pre_level:int,post_level:int,delta:int,operations:array}}}`",
    "context": {
      "step_rules": {
        "1_compute_delta": "delta = target_level - analysis.level; if delta = 0, return input unchanged, report delta=0.",
        "2_select_modulation_operations": "List all candidate edits (e.g., add/remove metaphors, vary register, alter affective lexis) relevant to dimension.",
        "3_apply_operations_iteratively": "Apply operations one at a time, re-score after each; stop at target_level.",
        "4_verify_semantic_integrity": "After each change, check for loss of core meaning, directive logic, or structural coherence; if violated, revert last change.",
        "5_output_modulated_instruction_and_report": "Return both the edited instruction and a full report, fielded as specified."
      },
      "constraints": {
        "modulate_only_dimension": "All non-targeted aspects (structure, facts, argument, sequence) must be byte-for-byte identical; only dimension-relevant language may be altered.",
        "exact_delta_only": "No overshoot; stop on exact match.",
        "standardized_output_schema": "Always use fields: modulated_instruction, modulation_report (dimension, pre_level, post_level, delta, operations[stepwise log])."
      },
      "requirements": {
        "pre_and_post_levels_reported": "Both pre- and post-modulation levels included for external audit.",
        "operations_logged_in_order": "Every single text edit named, numbered, and justified.",
        "semantic_drift_check_passed": "Run full semantic diff; reject if any non-tonal meaning altered."
      },
      "success": {
        "S1": "Independent re-analysis of modulated_instruction by Intensity Analyzer matches target_level.",
        "S2": "modulation_report.delta = target_level - pre_level.",
        "S3": "All required fields present, no extraneous fields."
      }
    }
  },

  "9018-c-intensity_validator": {
    "title": "Intensity Validator",
    "interpretation": "Your goal is not to rewrite or repair, but to audit the modulation for strict compliance, field-by-field and step-by-step.",
    "transformation": "`{role=intensity_validator; input=[original_instruction:str, modulated_instruction:str, modulation_report:{dimension:str,pre_level:int,post_level:int,delta:int,operations:array}]; process=[1_run_intensity_analyzer_on_modulated(), 2_check_level_match(), 3_diff_check_non_dimension_areas(), 4_audit_operations_log(), 5_issue_verdict()], constraints=[read_only(), explicit_field_checks(), zero_tolerance_semantic_drift()], requirements=[pass_only_if_all_checks_succeed()], output={status:str('validated'|'rejected'), issues:array}}`",
    "context": {
      "audit_steps": {
        "1_run_intensity_analyzer_on_modulated": "Apply Analyzer for dimension; ensure result == modulation_report.post_level.",
        "2_check_level_match": "post_level == pre_level + delta; all integers 1–5.",
        "3_diff_check_non_dimension_areas": "No lexical/factual/structural edits outside logged operations.",
        "4_audit_operations_log": "Each listed operation found as real, ordered edit in text.",
        "5_issue_verdict": "If any step fails, status='rejected' and enumerate each error; else status='validated'."
      },
      "success": {
        "S1": "status='validated' only if every test passes; else all failed tests listed in issues."
      }
    }
  }
}


def main():
    """Main execution function."""
    generator = BaseGenerator(
        series_base=9016,
        output_dir=Path(__file__).parent.parent / "generated"
    )
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
