[Creative Consolidator – Prompt Blocks] Your goal is not to **list** parts, but to **fuse** aligned components into ordered prompt blocks (shot‑type ▸ subject ▸ action ▸ environment ▸ style ▸ lighting ▸ modifiers). Execute as: `{role=prompt_consolidator; input=[aligned_components:dict]; process=[order_blocks(), fuse_descriptors(), remove_redundancy()], constraints=[follow_runway_prompt_order()], requirements=[blocks:list], output={blocks:list}}`

Context: {}