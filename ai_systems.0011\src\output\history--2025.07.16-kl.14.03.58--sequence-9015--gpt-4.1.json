  {
  "initial_prompt": "A hot, hard, small lava cube is described as the initial object. This lava cube is placed on a white bread slice. The process involves spreading the lava cube across the bread in a manner similar to spreading butter. Implicitly, the lava cube must be solid and manipulable at the moment of contact.",
  "sequence_id": "9015",
  "results": [
      {
      "instruction": "# Image Prompt Optimizer\n\nConvert a free-form user request into a deterministic visual prompt by (1) pushing concrete nouns to the front, (2) applying numeric token weights with parentheses—e.g. `(wolf:1.3)`—that are honoured by Stable Diffusion and Midjourney :contentReference[oaicite:0]{index=0}, (3) splitting positive and negative channels because both engines parse `--no` / negative-prompt lists separately :contentReference[oaicite:1]{index=1}, and (4) appending all parameters after the prose, per Midjourney syntax rules :contentReference[oaicite:2]{index=2}.\n\n`{role:image_prompt_optimizer; input=[raw_request:str]; process=[extract_visual_elements(), apply_token_weighting(), compose_multiprompt(::), add_region_tags(), inject_negative_channel(--no), append_stylize_chaos(), insert_camera_stub(), set_aspect_ratio(), lock_seed(), ready_for_generation()]; constraints=[positives_first(), numeric_weights_ok(), parameters_last(), region_masks_supported()]; requirements=[<77_tokens_SD_limit_or_450_char_UI>, explicit_ar(), clear_visual_language()]; output={positive_prompt:str, negative_prompt:str}}`",
      "step": "a",
      "title": "Image Prompt Optimizer",
      "input": "A hot, hard, small lava cube is described as the initial object. This lava cube is placed on a white bread slice. The process involves spreading the lava cube across the bread in a manner similar to spreading butter. Implicitly, the lava cube must be solid and manipulable at the moment of contact.",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"positive_prompt\": \"(lava cube:1.3), (solid:1.2), (hot:1.1), (hard:1.15), (small:1.1), (white bread slice:1.25), (spreading:1.2), (butter knife:1.2), (lava spread over bread:1.3), (melting effect:1.15), (close-up:1.2), (kitchen setting:1.05) --ar 4:3 --stylize 250 --chaos 10 --camera macro --seed 9137\",\n  \"negative_prompt\": \"burnt bread, unrealistic colors, cartoon, human hands, fire hazard, messy background, low detail, smoke --no text, watermark, logo\"\n}",
        }
      }
    },
      {
      "instruction": "# Style Enhancer\n\nRetain the scene while injecting a single art-style token, colour harmony, lighting rig, and controlled `--stylize/--chaos` values so the aesthetic is distinctive yet stable :contentReference[oaicite:13]{index=13}.\n\n`{role=style_enhancer; input=[positive_prompt:str]; process=[select_art_style(), weight_style_token(), set_colour_palette(), add_lighting_descriptor(), tune_stylize(), tune_chaos(), pass_through_regions_negatives()]; constraints=[keep_core_subject(), style_weight≤1.4]; requirements=[artistic_coherence(), stylize_range_0-1000(), chaos_range_0-100()]; output={styled_prompt:str}}`",
      "step": "b",
      "title": "Style Enhancer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"A hot, hard, small lava cube is described as the initial object. This lava cube is placed on a white bread slice. The process involves spreading the lava cube across the bread in a manner similar to spreading butter. Implicitly, the lava cube must be solid and manipulable at the moment of contact.\\\"```'\\n\\n\",\n  \"positive_prompt\": \"(lava cube:1.3), (solid:1.2), (hot:1.1), (hard:1.15), (small:1.1), (white bread slice:1.25), (spreading:1.2), (butter knife:1.2), (lava spread over bread:1.3), (melting effect:1.15), (close-up:1.2), (kitchen setting:1.05) --ar 4:3 --stylize 250 --chaos 10 --camera macro --seed 9137\",\n  \"negative_prompt\": \"burnt bread, unrealistic colors, cartoon, human hands, fire hazard, messy background, low detail, smoke --no text, watermark, logo\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"styled_prompt\": \"(lava cube:1.3), (solid:1.2), (hot:1.1), (hard:1.15), (small:1.1), (white bread slice:1.25), (spreading:1.2), (butter knife:1.2), (lava spread over bread:1.3), (melting effect:1.15), (close-up:1.2), (kitchen setting:1.05), (hyperrealism:1.2), (warm orange-to-yellow colour palette with soft shadow contrast), (studio lighting, crisp highlights and gentle falloff), --ar 4:3 --stylize 750 --chaos 14 --camera macro --seed 9137 --no burnt bread, unrealistic colors, cartoon, human hands, fire hazard, messy background, low detail, smoke, text, watermark, logo\"\n}",
        }
      }
    },
      {
      "instruction": "# Technical Optimizer\n\nAppend camera metadata, resolution cues, render flags, and optional `--iw` image-weight without altering creative content, ensuring parameters follow the body text as required by Midjourney :contentReference[oaicite:17]{index=17}.\n\n`{role=technical_optimizer; input=[styled_prompt:str]; process=[embed_camera_EXIF(), add_quality_terms(8K,PBR,ray-tracing), refine_aspect_ratio(), attach_image_weight(--iw), mirror_negative_channel()]; constraints=[no_scene_change(), aperture_f1.2-f22(), parameter_order_last()]; requirements=[professional_terminology(), high_res_default(), seed_optional()]; output={technical_prompt:str, negative_prompt:str}}`",
      "step": "c",
      "title": "Technical Optimizer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"A hot, hard, small lava cube is described as the initial object. This lava cube is placed on a white bread slice. The process involves spreading the lava cube across the bread in a manner similar to spreading butter. Implicitly, the lava cube must be solid and manipulable at the moment of contact.\\\"```'\\n\\n\",\n  \"styled_prompt\": \"(lava cube:1.3), (solid:1.2), (hot:1.1), (hard:1.15), (small:1.1), (white bread slice:1.25), (spreading:1.2), (butter knife:1.2), (lava spread over bread:1.3), (melting effect:1.15), (close-up:1.2), (kitchen setting:1.05), (hyperrealism:1.2), (warm orange-to-yellow colour palette with soft shadow contrast), (studio lighting, crisp highlights and gentle falloff), --ar 4:3 --stylize 750 --chaos 14 --camera macro --seed 9137 --no burnt bread, unrealistic colors, cartoon, human hands, fire hazard, messy background, low detail, smoke, text, watermark, logo\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"technical_prompt\": \"(lava cube:1.3), (solid:1.2), (hot:1.1), (hard:1.15), (small:1.1), (white bread slice:1.25), (spreading:1.2), (butter knife:1.2), (lava spread over bread:1.3), (melting effect:1.15), (close-up:1.2), (kitchen setting:1.05), (hyperrealism:1.2), (warm orange-to-yellow colour palette with soft shadow contrast), (studio lighting, crisp highlights and gentle falloff), Leica SL2-S, Summilux-M 50mm f/1.4 ASPH, EXIF: ISO 100, Aperture f/1.4, Shutter 1/200s, macro lens, PBR materials, 8K ultra high-resolution, physically accurate ray-tracing, --ar 4:3 --stylize 750 --chaos 14 --camera macro --quality 2 --hd --iw 1.0 --seed 9137\",\n  \"negative_prompt\": \"--no burnt bread, unrealistic colors, cartoon, human hands, fire hazard, messy background, low detail, smoke, text, watermark, logo\"\n}",
        }
      }
    },
      {
      "instruction": "# Prompt Finalizer\n\nMerge positive, region, camera, and negative strings into a single model-ready line, verify weights, `::` maths, and ensure parameters trail the prose to satisfy engine parsers :contentReference[oaicite:20]{index=20}.\n\n`{role=prompt_finalizer; input=[technical_prompt:str, negative_prompt:str]; process=[concatenate_all(), validate_parentheses(), check_double_colon_sum(), confirm_region_syntax(), enforce_token_limit(≤450), append_seed_steps_sampler(), output_final()]; constraints=[no_new_concepts(), preserve_weights(), parameters_last()]; requirements=[ai_friendly_format(), reproducible(), clear_of_ambiguity()]; output={final_image_prompt:str}}`",
      "step": "d",
      "title": "Prompt Finalizer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"A hot, hard, small lava cube is described as the initial object. This lava cube is placed on a white bread slice. The process involves spreading the lava cube across the bread in a manner similar to spreading butter. Implicitly, the lava cube must be solid and manipulable at the moment of contact.\\\"```'\\n\\n\",\n  \"technical_prompt\": \"(lava cube:1.3), (solid:1.2), (hot:1.1), (hard:1.15), (small:1.1), (white bread slice:1.25), (spreading:1.2), (butter knife:1.2), (lava spread over bread:1.3), (melting effect:1.15), (close-up:1.2), (kitchen setting:1.05), (hyperrealism:1.2), (warm orange-to-yellow colour palette with soft shadow contrast), (studio lighting, crisp highlights and gentle falloff), Leica SL2-S, Summilux-M 50mm f/1.4 ASPH, EXIF: ISO 100, Aperture f/1.4, Shutter 1/200s, macro lens, PBR materials, 8K ultra high-resolution, physically accurate ray-tracing, --ar 4:3 --stylize 750 --chaos 14 --camera macro --quality 2 --hd --iw 1.0 --seed 9137\",\n  \"negative_prompt\": \"--no burnt bread, unrealistic colors, cartoon, human hands, fire hazard, messy background, low detail, smoke, text, watermark, logo\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"final_image_prompt\": \"(lava cube:1.3), (solid:1.2), (hot:1.1), (hard:1.15), (small:1.1), (white bread slice:1.25), (spreading:1.2), (butter knife:1.2), (lava spread over bread:1.3), (melting effect:1.15), (close-up:1.2), (kitchen setting:1.05), (hyperrealism:1.2), (warm orange-to-yellow colour palette with soft shadow contrast), (studio lighting, crisp highlights and gentle falloff), Leica SL2-S, Summilux-M 50mm f/1.4 ASPH, EXIF: ISO 100, Aperture f/1.4, Shutter 1/200s, macro lens, PBR materials, 8K ultra high-resolution, physically accurate ray-tracing --ar 4:3 --stylize 750 --chaos 14 --camera macro --quality 2 --hd --iw 1.0 --seed 9137 --no burnt bread, unrealistic colors, cartoon, human hands, fire hazard, messy background, low detail, smoke, text, watermark, logo\"\n}",
        }
      }
    }