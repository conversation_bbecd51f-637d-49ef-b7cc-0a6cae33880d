[Image Prompt Optimizer] Your goal is not to **describe** the request, but to **optimize** it into a precise, visual prompt for AI image generation. Identify all concrete visual elements in the request. Specify each using direct visual descriptors. Add clear composition guidance (subject placement, perspective, background). Use only tangible objects and environments. Execute as: `{role=image_prompt_optimizer; input=[raw_request:str]; process=[extract_visual_elements(), enhance_descriptive_clarity(), add_composition_details(), optimize_for_ai_generation()]; constraints=[visual_focus_only(), no_abstract_concepts(), concrete_imagery_only()]; requirements=[generation_ready_format(), clear_visual_language(), specific_details()]; output={optimized_image_prompt:str}}`

Context: [
  {
    "explicit_asks": [
      "Integrate token weighting syntax using parentheses and :(w) values (Technique #2).",
      "Convert subject / setting / mood into a multi-prompt string with ::numeric weights (Technique #3).",
      "Split prompt into positive channel + explicit negative-prompt channel (Technique #6).",
      "Generate region-specific sub-prompts when spatial layout is implied (Technique #5).",
      "Add stylize and chaos sliders for creativity control (Technique #7).",
      "Append camera-metadata cues for photorealism (Technique #10).",
      "Prepare all fields for iterative refinement and quality scoring."
    ],
    "hidden_assumptions": [
      "Target models honour () token weighting and :: arithmetic :contentReference[oaicite:1]{index=1}.",
      "Regional prompting is available via SD Regional-Prompter or MJ panelling :contentReference[oaicite:2]{index=2}.",
      "Negative-prompt channels materially influence diffusion output :contentReference[oaicite:3]{index=3}.",
      "Midjourney style/chaos parameters are parsed in v7 :contentReference[oaicite:4]{index=4}.",
      "\"--iw\" or “--cw” flags may be passed if an image reference is present :contentReference[oaicite:5]{index=5}.",
      "Camera EXIF tokens (lens, f-stop) bias models toward realism :contentReference[oaicite:6]{index=6}."
    ],
    "sub_goals": [
      "Weight primary subject tokens ≥1.2, secondary scenery tokens 0.8-1.0.",
      "Auto-build `Positive:` and `Negative:` fields, ensuring negatives carry no positive antonym collisions.",
      "Insert region tags `[region sky] … | [region ground] …` when two-layer landscape detected.",
      "Add `—stylize`  and `—chaos` defaults based on request realism vs. artistry.",
      "Attach `<camera: 24 mm f/2.8, ISO 100, 1/500 s>` when photoreal keywords found.",
      "Emit a quality-loop flag so later stages can re-call optimiser until FID/LPIPS stabilises :contentReference[oaicite:7]{index=7}."
    ],
    "blockers": [
      "Some engines ignore () weights (e.g., DALL·E 3) leading to diminished emphasis.",
      "`::` arithmetic fails if total ≤0 in Midjourney (weight-sum error).",
      "Regional masks unsupported on mobile SD UIs.",
      "Excess negative weights can yield desaturated or blank images.",
      "Stylize/chaos out-of-range values silently clamp or error depending on API.",
      "Not all endpoints surface FID/LPIPS for auto-loop termination."
    ]
  }
]