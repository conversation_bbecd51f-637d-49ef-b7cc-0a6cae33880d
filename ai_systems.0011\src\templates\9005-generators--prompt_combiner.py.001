#!/usr/bin/env python3

import sys
from pathlib import Path

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))
from processor import BaseGenerator

TEMPLATES = {

    # 9005:
    "9005-a-capability_extractor": {
        "title": "Capability Extractor",
        "interpretation": "Your goal is not to **describe** the two instruction templates, but to **extract** their core operational capabilities and transformation mechanisms. Execute as:",
        "transformation": "`{role=capability_extractor; input=[instruction_template_a:str, instruction_template_b:str]; process=[extract_transformation_verbs(), identify_core_processes(), isolate_unique_constraints(), map_output_specifications(), distill_operational_essence()]; constraints=[focus_on_capabilities_only(), ignore_template_structure(), preserve_transformation_power()]; requirements=[capability_isolation(), operational_clarity(), transformation_preservation()]; output={capability_a:dict, capability_b:dict, synergy_points:array}}`",
    },

    "9005-b-synthesis_architect": {
        "title": "Synthesis Architect",
        "interpretation": "Your goal is not to **merge** the extracted capabilities, but to **architect** a new unified instruction that combines both operational powers into a single transformative directive. Execute as:",
        "transformation": "`{role=synthesis_architect; input=[capability_a:dict, capability_b:dict, synergy_points:array]; process=[design_unified_goal_negation(), create_combined_transformation_verb(), synthesize_process_functions(), merge_constraint_systems(), unify_output_specifications()]; constraints=[preserve_both_powers(), eliminate_operational_conflicts(), maximize_synergistic_amplification()]; requirements=[single_coherent_instruction(), amplified_transformation_capability(), unified_operational_flow()]; output={synthesized_instruction_template:str}}`",
    },

    "9005-c-template_crystallizer": {
        "title": "Template Crystallizer",
        "interpretation": "Your goal is not to **format** the synthesized instruction, but to **crystallize** it into perfect three-part canonical structure with maximum operational potency. Execute as:",
        "transformation": "`{role=template_crystallizer; input=[synthesized_instruction_template:str]; process=[craft_precise_title(), formulate_goal_negation_interpretation(), structure_transformation_block(), validate_canonical_compliance(), optimize_execution_clarity()]; constraints=[maintain_three_part_structure(), preserve_combined_capabilities(), ensure_executable_precision()]; requirements=[canonical_format_compliance(), maximum_directive_potency(), crystallized_operational_unity()]; output={final_instruction_template:dict}}`",
    }
}

def main():
    """Main execution function."""
    generator = BaseGenerator(
        output_dir=Path(__file__).parent.parent / "generated"
    )
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
