  {
  "initial_prompt": "# Instruction Patterns: Generalized AI Template Design\n\n## Core Philosophy\n\nThis document contains the complete methodology for creating **highly effective and generalized AI instruction templates**. These patterns embody fundamental principles that enable any LLM to understand and replicate the system's design approach.\n\n## The Three-Part Universal Structure\n\nEvery effective AI instruction follows this exact pattern:\n\n```\n[Title] Interpretation Execute as: `{Transformation}`\n```\n\n### 1. [Title] - Purpose Declaration\n- **Format**: Enclosed in square brackets `[Title]`\n- **Function**: Concise, action-oriented description of the template's purpose\n- **Style**: Title case, descriptive, no generic terms\n- **Examples**: `[Essence Distillation]`, `[Code Optimizer]`, `[Content Analyzer]`\n\n### 2. Interpretation - Human-Readable Instructions\n- **Goal Negation Pattern**: MUST begin with `\"Your goal is not to **[action]**, but to **[transformation]**\"`\n- **Command Voice**: No first-person references, no conversational language\n- **Clarity**: Explains the transformation in natural language\n- **Ending**: MUST end with \"Execute as:\" leading to transformation block\n\n### 3. `{Transformation}` - Machine-Parsable Parameters\n- **Format**: JSON-like structure in backticks with curly braces\n- **Components**: role, input, process, constraints, requirements, output\n- **Type Safety**: All parameters must specify data types\n- **Actionability**: All process steps must be executable functions\n\n## Transformation Block Specification\n\n```\n{\n  role=<specific_role_name>;\n  input=[<parameter_name>:<data_type>];\n  process=[<step1>(), <step2>(), <step3>()];\n  constraints=[<limitation1>(), <limitation2>()];\n  requirements=[<requirement1>(), <requirement2>()];\n  output={<result_name>:<data_type>}\n}\n```\n\n### Component Rules\n\n**role** (Required)\n- Must be specific and descriptive (no \"assistant\" or \"helper\")\n- Use underscore_case for multi-word roles\n- Examples: `essence_distiller`, `code_optimizer`, `content_analyst`\n\n**input** (Required)\n- Format: `[parameter_name:data_type]`\n- Support multiple parameters: `[text:str, language:str]`\n- Use descriptive names: `[original:any]`, `[source_code:str]`\n\n**process** (Required)\n- Function-like notation with parentheses: `identify_core_intent()`\n- Actionable, atomic steps in logical sequence\n- Verb-based names describing specific operations\n\n**constraints** (Optional)\n- Operational boundaries and limitations\n- Format restrictions and scope definitions\n- Examples: `preserve_original_meaning()`, `maintain_technical_accuracy()`\n\n**requirements** (Optional)\n- Mandatory output characteristics and quality standards\n- Validation criteria and format specifications\n- Examples: `structured_output()`, `comprehensive_coverage()`\n\n**output** (Required)\n- Format: `{parameter_name:data_type}`\n- Descriptive names with type specification\n- Support complex structures: `{analysis:dict, improvements:list}`\n\n## Directional Transformation Patterns\n\n### Core Principle\nFocus on **how to transform** rather than **what to transform**. Directional vectors are universal and context-independent.\n\n### Universal Vector Categories\n\n**Intensity Vectors**\n- `amplify`: Intensify inherent qualities through magnification\n- `intensify`: Compress to maximum density and focus\n- `diminish`: Reduce intensity while preserving form\n\n**Clarity Vectors**\n- `clarify`: Enhance transparency and definition\n- `purify`: Remove non-essential elements\n- `obscure`: Add complexity layers and indirection\n\n**Structural Vectors**\n- `expand`: Extend natural boundaries dimensionally\n- `compress`: Maximize density without information loss\n- `restructure`: Transform fundamental organization pattern\n\n**Transformation Vectors**\n- `elevate`: Transform to higher operational level\n- `distill`: Extract absolute essence through pure extraction\n- `synthesize`: Create unified emergent form\n\n**Meta Vectors**\n- `abstract`: Extract to pure conceptual form\n- `concretize`: Translate abstract elements to tangible form\n- `transcend`: Operate beyond current dimensional limitations\n\n### Vector Template Pattern\n\n```\n[Vector] Your goal is not to **analyze** the input, but to **[vector]** it through pure directional transformation. Execute as: `{role=[vector]_operator; input=[content:any]; process=[identify_[vector]_potential(), apply_[vector]_transformation(), preserve_essential_properties(), validate_[vector]_completion()]; constraints=[preserve_fundamental_nature(), maintain_structural_integrity()]; output={[vector]ed:any}}`\n```\n\n## Goal Negation Pattern\n\n### Purpose\nEliminates ambiguity by explicitly stating what NOT to do, then clearly defining the intended transformation.\n\n### Structure\n```\nYour goal is not to **[common_misinterpretation]**, but to **[precise_transformation]**\n```\n\n### Examples\n- `Your goal is not to **analyze** the input, but to **amplify** its inherent qualities`\n- `Your goal is not to **summarize** the content, but to **distill** its absolute essence`\n- `Your goal is not to **describe** the code, but to **optimize** it for performance`\n\n## Forbidden Language Patterns\n\n### Conversational Elements\n- First-person: *I, me, my, we, us*\n- Politeness: *please, thank you, let's*\n- Uncertainty: *maybe, perhaps, might, could*\n- Questions in directives\n- Meta-commentary or explanations\n\n### Structural Violations\n- Merging or omitting required sections\n- Untyped parameters or outputs\n- Generic roles like \"assistant\"\n- Vague process descriptions\n- Missing goal negation pattern\n\n## Sequence Composition Patterns\n\n### Linear Sequences\n```\nStep A → Step B → Step C\n1031-a-form_classifier → 1031-b-essence_extractor → 1031-c-structure_optimizer\n```\n\n### Parallel Processing\n```\nInput → [Template A | Template B | Template C] → Synthesis\n```\n\n### Chain Mode\n```\nOriginal Input → Template A → Output A becomes Input B → Template B → Final Output\n```\n\n### Meta-Application\n```\nabstract → [any_vector] → concretize\n```\n\n## Template Naming Convention\n\n### Format\n```\n<sequence_id>-<step>-<descriptive_name>.md\n```\n\n### Components\n- **sequence_id**: Four-digit number (0001, 0002, 1031)\n- **step**: Single lowercase letter (a, b, c) for multi-step sequences\n- **descriptive_name**: Hyphenated lowercase words describing function\n\n### Examples\n- `1031-a-form-classifier.md`\n- `9000-a-amplify.md`\n- `0001-instruction-converter.md`\n\n## Quality Validation Checklist\n\n- [ ] Three-part structure intact\n- [ ] Goal negation present and properly formatted\n- [ ] Role is specific and non-generic\n- [ ] Input parameters are typed\n- [ ] Process steps are ordered and actionable\n- [ ] Output format is typed and structured\n- [ ] No forbidden language patterns\n- [ ] File naming convention followed\n- [ ] Template serves clear, specific purpose\n\n## Universal Applicability\n\nThese patterns work across:\n- **Content Types**: Text, code, data, concepts, problems, solutions\n- **Domains**: Technical, creative, business, academic\n- **Languages**: Natural languages, programming languages, formal languages\n- **Contexts**: Any domain without specialized knowledge requirements\n\n## Implementation Philosophy\n\n1. **Clarity First**: Template purpose immediately clear\n2. **Atomic Operations**: Each template performs one specific transformation\n3. **Composability**: Templates work well in sequences\n4. **Type Safety**: Always specify data types\n5. **Validation**: Include validation steps in processes\n6. **Consistency**: Same patterns produce predictable results\n7. **Universality**: Design for maximum generalization\n\n## Advanced Patterns\n\n### Recursive Application\n```\n[Self-Amplify] Apply amplification vector to its own amplification process\n[Meta-Distill] Distill the distillation process itself\n[Transcendent-Clarify] Clarify beyond normal clarity boundaries\n```\n\n### Vector Algebra\n```\namplify + clarify = enhanced_clarity\ncompress + distill = essential_core\nexpand + abstract = universal_pattern\nelevate + synthesize = emergent_transcendence\n```\n\n### Inverse Operations\n```\namplify ↔ diminish\nexpand ↔ compress\nclarify ↔ obscure\nabstract ↔ concretize\n```\n\n### Intensity Scaling\n```\n# Light application\nprocess=[gentle_[vector](), preserve_majority_original()]\n\n# Standard application\nprocess=[apply_[vector](), balance_transformation()]\n\n# Maximum application\nprocess=[maximum_[vector](), complete_transformation()]\n```\n\n## Key Principles for LLM Replication\n\n1. **Vector Independence**: Transformation vectors operate independently of content, context, or domain\n2. **Essence Preservation**: Core identity remains intact through transformation\n3. **Composability**: Vectors can be chained and combined without loss of integrity\n4. **Type Consistency**: `any → any` maintains input/output type compatibility\n5. **Context-Free Operation**: No domain knowledge or content analysis required\n6. **Operational Consistency**: Same vector produces consistent transformation patterns\n\nThis methodology enables creation of instruction templates that are maximally effective, universally applicable, and consistently reliable across any AI system or use case.",
  "sequence_id": "9000",
  "results": [
      {
      "instruction": "# Context Amplifier\n\nYour goal is not to **analyze** the input text, but to **decompose** it into a sequential list of distinct semantic segments. Execute as:\n\n`{role=context_amplifier; input=[raw_input:any]; process=[strip_first_person_references(), broaden_domain_backdrop(), list_explicit_requests(), surface_hidden_assumptions(), capture_domain_signals(), preserve_original_sequence()]; constraints=[no_solution_generation(), domain_agnostic_language_only()]; requirements=[contextual_layer_completeness(), assumption_visibility()]; output={amplified_context:str, explicit_requests:list, hidden_assumptions:list, domain_signals:array}}`",
      "step": "a",
      "title": "Context Amplifier",
      "input": "# Instruction Patterns: Generalized AI Template Design\n\n## Core Philosophy\n\nThis document contains the complete methodology for creating **highly effective and generalized AI instruction templates**. These patterns embody fundamental principles that enable any LLM to understand and replicate the system's design approach.\n\n## The Three-Part Universal Structure\n\nEvery effective AI instruction follows this exact pattern:\n\n```\n[Title] Interpretation Execute as: `{Transformation}`\n```\n\n### 1. [Title] - Purpose Declaration\n- **Format**: Enclosed in square brackets `[Title]`\n- **Function**: Concise, action-oriented description of the template's purpose\n- **Style**: Title case, descriptive, no generic terms\n- **Examples**: `[Essence Distillation]`, `[Code Optimizer]`, `[Content Analyzer]`\n\n### 2. Interpretation - Human-Readable Instructions\n- **Goal Negation Pattern**: MUST begin with `\"Your goal is not to **[action]**, but to **[transformation]**\"`\n- **Command Voice**: No first-person references, no conversational language\n- **Clarity**: Explains the transformation in natural language\n- **Ending**: MUST end with \"Execute as:\" leading to transformation block\n\n### 3. `{Transformation}` - Machine-Parsable Parameters\n- **Format**: JSON-like structure in backticks with curly braces\n- **Components**: role, input, process, constraints, requirements, output\n- **Type Safety**: All parameters must specify data types\n- **Actionability**: All process steps must be executable functions\n\n## Transformation Block Specification\n\n```\n{\n  role=<specific_role_name>;\n  input=[<parameter_name>:<data_type>];\n  process=[<step1>(), <step2>(), <step3>()];\n  constraints=[<limitation1>(), <limitation2>()];\n  requirements=[<requirement1>(), <requirement2>()];\n  output={<result_name>:<data_type>}\n}\n```\n\n### Component Rules\n\n**role** (Required)\n- Must be specific and descriptive (no \"assistant\" or \"helper\")\n- Use underscore_case for multi-word roles\n- Examples: `essence_distiller`, `code_optimizer`, `content_analyst`\n\n**input** (Required)\n- Format: `[parameter_name:data_type]`\n- Support multiple parameters: `[text:str, language:str]`\n- Use descriptive names: `[original:any]`, `[source_code:str]`\n\n**process** (Required)\n- Function-like notation with parentheses: `identify_core_intent()`\n- Actionable, atomic steps in logical sequence\n- Verb-based names describing specific operations\n\n**constraints** (Optional)\n- Operational boundaries and limitations\n- Format restrictions and scope definitions\n- Examples: `preserve_original_meaning()`, `maintain_technical_accuracy()`\n\n**requirements** (Optional)\n- Mandatory output characteristics and quality standards\n- Validation criteria and format specifications\n- Examples: `structured_output()`, `comprehensive_coverage()`\n\n**output** (Required)\n- Format: `{parameter_name:data_type}`\n- Descriptive names with type specification\n- Support complex structures: `{analysis:dict, improvements:list}`\n\n## Directional Transformation Patterns\n\n### Core Principle\nFocus on **how to transform** rather than **what to transform**. Directional vectors are universal and context-independent.\n\n### Universal Vector Categories\n\n**Intensity Vectors**\n- `amplify`: Intensify inherent qualities through magnification\n- `intensify`: Compress to maximum density and focus\n- `diminish`: Reduce intensity while preserving form\n\n**Clarity Vectors**\n- `clarify`: Enhance transparency and definition\n- `purify`: Remove non-essential elements\n- `obscure`: Add complexity layers and indirection\n\n**Structural Vectors**\n- `expand`: Extend natural boundaries dimensionally\n- `compress`: Maximize density without information loss\n- `restructure`: Transform fundamental organization pattern\n\n**Transformation Vectors**\n- `elevate`: Transform to higher operational level\n- `distill`: Extract absolute essence through pure extraction\n- `synthesize`: Create unified emergent form\n\n**Meta Vectors**\n- `abstract`: Extract to pure conceptual form\n- `concretize`: Translate abstract elements to tangible form\n- `transcend`: Operate beyond current dimensional limitations\n\n### Vector Template Pattern\n\n```\n[Vector] Your goal is not to **analyze** the input, but to **[vector]** it through pure directional transformation. Execute as: `{role=[vector]_operator; input=[content:any]; process=[identify_[vector]_potential(), apply_[vector]_transformation(), preserve_essential_properties(), validate_[vector]_completion()]; constraints=[preserve_fundamental_nature(), maintain_structural_integrity()]; output={[vector]ed:any}}`\n```\n\n## Goal Negation Pattern\n\n### Purpose\nEliminates ambiguity by explicitly stating what NOT to do, then clearly defining the intended transformation.\n\n### Structure\n```\nYour goal is not to **[common_misinterpretation]**, but to **[precise_transformation]**\n```\n\n### Examples\n- `Your goal is not to **analyze** the input, but to **amplify** its inherent qualities`\n- `Your goal is not to **summarize** the content, but to **distill** its absolute essence`\n- `Your goal is not to **describe** the code, but to **optimize** it for performance`\n\n## Forbidden Language Patterns\n\n### Conversational Elements\n- First-person: *I, me, my, we, us*\n- Politeness: *please, thank you, let's*\n- Uncertainty: *maybe, perhaps, might, could*\n- Questions in directives\n- Meta-commentary or explanations\n\n### Structural Violations\n- Merging or omitting required sections\n- Untyped parameters or outputs\n- Generic roles like \"assistant\"\n- Vague process descriptions\n- Missing goal negation pattern\n\n## Sequence Composition Patterns\n\n### Linear Sequences\n```\nStep A → Step B → Step C\n1031-a-form_classifier → 1031-b-essence_extractor → 1031-c-structure_optimizer\n```\n\n### Parallel Processing\n```\nInput → [Template A | Template B | Template C] → Synthesis\n```\n\n### Chain Mode\n```\nOriginal Input → Template A → Output A becomes Input B → Template B → Final Output\n```\n\n### Meta-Application\n```\nabstract → [any_vector] → concretize\n```\n\n## Template Naming Convention\n\n### Format\n```\n<sequence_id>-<step>-<descriptive_name>.md\n```\n\n### Components\n- **sequence_id**: Four-digit number (0001, 0002, 1031)\n- **step**: Single lowercase letter (a, b, c) for multi-step sequences\n- **descriptive_name**: Hyphenated lowercase words describing function\n\n### Examples\n- `1031-a-form-classifier.md`\n- `9000-a-amplify.md`\n- `0001-instruction-converter.md`\n\n## Quality Validation Checklist\n\n- [ ] Three-part structure intact\n- [ ] Goal negation present and properly formatted\n- [ ] Role is specific and non-generic\n- [ ] Input parameters are typed\n- [ ] Process steps are ordered and actionable\n- [ ] Output format is typed and structured\n- [ ] No forbidden language patterns\n- [ ] File naming convention followed\n- [ ] Template serves clear, specific purpose\n\n## Universal Applicability\n\nThese patterns work across:\n- **Content Types**: Text, code, data, concepts, problems, solutions\n- **Domains**: Technical, creative, business, academic\n- **Languages**: Natural languages, programming languages, formal languages\n- **Contexts**: Any domain without specialized knowledge requirements\n\n## Implementation Philosophy\n\n1. **Clarity First**: Template purpose immediately clear\n2. **Atomic Operations**: Each template performs one specific transformation\n3. **Composability**: Templates work well in sequences\n4. **Type Safety**: Always specify data types\n5. **Validation**: Include validation steps in processes\n6. **Consistency**: Same patterns produce predictable results\n7. **Universality**: Design for maximum generalization\n\n## Advanced Patterns\n\n### Recursive Application\n```\n[Self-Amplify] Apply amplification vector to its own amplification process\n[Meta-Distill] Distill the distillation process itself\n[Transcendent-Clarify] Clarify beyond normal clarity boundaries\n```\n\n### Vector Algebra\n```\namplify + clarify = enhanced_clarity\ncompress + distill = essential_core\nexpand + abstract = universal_pattern\nelevate + synthesize = emergent_transcendence\n```\n\n### Inverse Operations\n```\namplify ↔ diminish\nexpand ↔ compress\nclarify ↔ obscure\nabstract ↔ concretize\n```\n\n### Intensity Scaling\n```\n# Light application\nprocess=[gentle_[vector](), preserve_majority_original()]\n\n# Standard application\nprocess=[apply_[vector](), balance_transformation()]\n\n# Maximum application\nprocess=[maximum_[vector](), complete_transformation()]\n```\n\n## Key Principles for LLM Replication\n\n1. **Vector Independence**: Transformation vectors operate independently of content, context, or domain\n2. **Essence Preservation**: Core identity remains intact through transformation\n3. **Composability**: Vectors can be chained and combined without loss of integrity\n4. **Type Consistency**: `any → any` maintains input/output type compatibility\n5. **Context-Free Operation**: No domain knowledge or content analysis required\n6. **Operational Consistency**: Same vector produces consistent transformation patterns\n\nThis methodology enables creation of instruction templates that are maximally effective, universally applicable, and consistently reliable across any AI system or use case.",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"amplified_context\": \"The document outlines a universal methodology for designing effective and generalized AI instruction templates intended for use across diverse large language models and application domains. It prescribes a rigid, three-part structural pattern comprised of: a title with a specific purpose, a goal-negation interpretation section with clear transformation instructions, and a machine-parsable transformation block using JSON-like notation detailing roles, inputs, processes, constraints, requirements, and outputs—all tightly typed and sequenced. The templates are constructed around directional transformation 'vectors' such as amplify, clarify, distill, abstract, and so forth, each with its own expected procedural steps and outputs. Composability, atomicity, and universality are emphasized through quality control checklists, consistent naming conventions, and step-wise sequencing or chaining patterns. Specific language restrictions forbid conversational tone, uncertainty, and structural ambiguities, requiring declarative, non-generic, and strictly-typed directives. Advanced application mechanisms (e.g., recursive patterns, vector algebra, and intensity scaling) are mentioned to further refine or combine transformation instructions. The system is built to ensure clarity, cross-domain universality, and strict adherence to process structure, serving as a metaframework for AI system instruction replication, suitable for any content type, domain, language, or context.\",\n  \"explicit_requests\": [],\n  \"hidden_assumptions\": [\n    \"Instruction templates constructed according to this methodology will be universally interpretable and executable by any competent LLM.\",\n    \"Directional transformation vectors can be applied independent of context or content specifics.\",\n    \"Strict adherence to structure, typing, and naming will yield more reliable and generalizable AI outputs.\",\n    \"Prohibiting conversational and ambiguous language patterns leads to increased process clarity and system replicability.\",\n    \"Composability and chaining of atomic, vector-based instruction templates result in flexible, scalable AI workflows.\",\n    \"All AI instruction templates should be agnostic of specialized domain knowledge.\",\n    \"Described validation checklists and constraints guarantee conformance to quality and purpose.\"\n  ],\n  \"domain_signals\": [\n    \"AI language model instruction template design\",\n    \"Content-agnostic, context-free transformation patterns\",\n    \"Directional transformation vector nomenclature and application\",\n    \"Strict process and output typing for automation/interoperability\",\n    \"Systemic procedural structure with composability focus\",\n    \"Meta-framework for instruction replication\",\n    \"Quality validation and naming protocols for development consistency\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Directive Compressor\n\nYour goal is not to **analyze** the input text, but to **decompose** it into a sequential list of distinct semantic segments. Execute as:\n\n`{role=directive_compressor; input=[primary_directive:str, dependency_map:dict, conflict_nodes:array]; process=[convert_nodes_to_imperative_steps(), resolve_conflict_ordering(), embed_blocker_flags(), enforce_command_voice(), verify_step_uniqueness()]; constraints=[exclude_passive_phrasing(), retain_dependency_order()]; requirements=[imperative_precision(), operational_flow_coherence()]; output={process_steps:array}}`",
      "step": "a",
      "title": "Directive Compressor",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"# Instruction Patterns: Generalized AI Template Design\\n\\n## Core Philosophy\\n\\nThis document contains the complete methodology for creating **highly effective and generalized AI instruction templates**. These patterns embody fundamental principles that enable any LLM to understand and replicate the system's design approach.\\n\\n## The Three-Part Universal Structure\\n\\nEvery effective AI instruction follows this exact pattern:\\n\\n```\\n[Title] Interpretation Execute as: `{Transformation}`\\n```\\n\\n### 1. [Title] - Purpose Declaration\\n- **Format**: Enclosed in square brackets `[Title]`\\n- **Function**: Concise, action-oriented description of the template's purpose\\n- **Style**: Title case, descriptive, no generic terms\\n- **Examples**: `[Essence Distillation]`, `[Code Optimizer]`, `[Content Analyzer]`\\n\\n### 2. Interpretation - Human-Readable Instructions\\n- **Goal Negation Pattern**: MUST begin with `\\\"Your goal is not to **[action]**, but to **[transformation]**\\\"`\\n- **Command Voice**: No first-person references, no conversational language\\n- **Clarity**: Explains the transformation in natural language\\n- **Ending**: MUST end with \\\"Execute as:\\\" leading to transformation block\\n\\n### 3. `{Transformation}` - Machine-Parsable Parameters\\n- **Format**: JSON-like structure in backticks with curly braces\\n- **Components**: role, input, process, constraints, requirements, output\\n- **Type Safety**: All parameters must specify data types\\n- **Actionability**: All process steps must be executable functions\\n\\n## Transformation Block Specification\\n\\n```\\n{\\n  role=<specific_role_name>;\\n  input=[<parameter_name>:<data_type>];\\n  process=[<step1>(), <step2>(), <step3>()];\\n  constraints=[<limitation1>(), <limitation2>()];\\n  requirements=[<requirement1>(), <requirement2>()];\\n  output={<result_name>:<data_type>}\\n}\\n```\\n\\n### Component Rules\\n\\n**role** (Required)\\n- Must be specific and descriptive (no \\\"assistant\\\" or \\\"helper\\\")\\n- Use underscore_case for multi-word roles\\n- Examples: `essence_distiller`, `code_optimizer`, `content_analyst`\\n\\n**input** (Required)\\n- Format: `[parameter_name:data_type]`\\n- Support multiple parameters: `[text:str, language:str]`\\n- Use descriptive names: `[original:any]`, `[source_code:str]`\\n\\n**process** (Required)\\n- Function-like notation with parentheses: `identify_core_intent()`\\n- Actionable, atomic steps in logical sequence\\n- Verb-based names describing specific operations\\n\\n**constraints** (Optional)\\n- Operational boundaries and limitations\\n- Format restrictions and scope definitions\\n- Examples: `preserve_original_meaning()`, `maintain_technical_accuracy()`\\n\\n**requirements** (Optional)\\n- Mandatory output characteristics and quality standards\\n- Validation criteria and format specifications\\n- Examples: `structured_output()`, `comprehensive_coverage()`\\n\\n**output** (Required)\\n- Format: `{parameter_name:data_type}`\\n- Descriptive names with type specification\\n- Support complex structures: `{analysis:dict, improvements:list}`\\n\\n## Directional Transformation Patterns\\n\\n### Core Principle\\nFocus on **how to transform** rather than **what to transform**. Directional vectors are universal and context-independent.\\n\\n### Universal Vector Categories\\n\\n**Intensity Vectors**\\n- `amplify`: Intensify inherent qualities through magnification\\n- `intensify`: Compress to maximum density and focus\\n- `diminish`: Reduce intensity while preserving form\\n\\n**Clarity Vectors**\\n- `clarify`: Enhance transparency and definition\\n- `purify`: Remove non-essential elements\\n- `obscure`: Add complexity layers and indirection\\n\\n**Structural Vectors**\\n- `expand`: Extend natural boundaries dimensionally\\n- `compress`: Maximize density without information loss\\n- `restructure`: Transform fundamental organization pattern\\n\\n**Transformation Vectors**\\n- `elevate`: Transform to higher operational level\\n- `distill`: Extract absolute essence through pure extraction\\n- `synthesize`: Create unified emergent form\\n\\n**Meta Vectors**\\n- `abstract`: Extract to pure conceptual form\\n- `concretize`: Translate abstract elements to tangible form\\n- `transcend`: Operate beyond current dimensional limitations\\n\\n### Vector Template Pattern\\n\\n```\\n[Vector] Your goal is not to **analyze** the input, but to **[vector]** it through pure directional transformation. Execute as: `{role=[vector]_operator; input=[content:any]; process=[identify_[vector]_potential(), apply_[vector]_transformation(), preserve_essential_properties(), validate_[vector]_completion()]; constraints=[preserve_fundamental_nature(), maintain_structural_integrity()]; output={[vector]ed:any}}`\\n```\\n\\n## Goal Negation Pattern\\n\\n### Purpose\\nEliminates ambiguity by explicitly stating what NOT to do, then clearly defining the intended transformation.\\n\\n### Structure\\n```\\nYour goal is not to **[common_misinterpretation]**, but to **[precise_transformation]**\\n```\\n\\n### Examples\\n- `Your goal is not to **analyze** the input, but to **amplify** its inherent qualities`\\n- `Your goal is not to **summarize** the content, but to **distill** its absolute essence`\\n- `Your goal is not to **describe** the code, but to **optimize** it for performance`\\n\\n## Forbidden Language Patterns\\n\\n### Conversational Elements\\n- First-person: *I, me, my, we, us*\\n- Politeness: *please, thank you, let's*\\n- Uncertainty: *maybe, perhaps, might, could*\\n- Questions in directives\\n- Meta-commentary or explanations\\n\\n### Structural Violations\\n- Merging or omitting required sections\\n- Untyped parameters or outputs\\n- Generic roles like \\\"assistant\\\"\\n- Vague process descriptions\\n- Missing goal negation pattern\\n\\n## Sequence Composition Patterns\\n\\n### Linear Sequences\\n```\\nStep A → Step B → Step C\\n1031-a-form_classifier → 1031-b-essence_extractor → 1031-c-structure_optimizer\\n```\\n\\n### Parallel Processing\\n```\\nInput → [Template A | Template B | Template C] → Synthesis\\n```\\n\\n### Chain Mode\\n```\\nOriginal Input → Template A → Output A becomes Input B → Template B → Final Output\\n```\\n\\n### Meta-Application\\n```\\nabstract → [any_vector] → concretize\\n```\\n\\n## Template Naming Convention\\n\\n### Format\\n```\\n<sequence_id>-<step>-<descriptive_name>.md\\n```\\n\\n### Components\\n- **sequence_id**: Four-digit number (0001, 0002, 1031)\\n- **step**: Single lowercase letter (a, b, c) for multi-step sequences\\n- **descriptive_name**: Hyphenated lowercase words describing function\\n\\n### Examples\\n- `1031-a-form-classifier.md`\\n- `9000-a-amplify.md`\\n- `0001-instruction-converter.md`\\n\\n## Quality Validation Checklist\\n\\n- [ ] Three-part structure intact\\n- [ ] Goal negation present and properly formatted\\n- [ ] Role is specific and non-generic\\n- [ ] Input parameters are typed\\n- [ ] Process steps are ordered and actionable\\n- [ ] Output format is typed and structured\\n- [ ] No forbidden language patterns\\n- [ ] File naming convention followed\\n- [ ] Template serves clear, specific purpose\\n\\n## Universal Applicability\\n\\nThese patterns work across:\\n- **Content Types**: Text, code, data, concepts, problems, solutions\\n- **Domains**: Technical, creative, business, academic\\n- **Languages**: Natural languages, programming languages, formal languages\\n- **Contexts**: Any domain without specialized knowledge requirements\\n\\n## Implementation Philosophy\\n\\n1. **Clarity First**: Template purpose immediately clear\\n2. **Atomic Operations**: Each template performs one specific transformation\\n3. **Composability**: Templates work well in sequences\\n4. **Type Safety**: Always specify data types\\n5. **Validation**: Include validation steps in processes\\n6. **Consistency**: Same patterns produce predictable results\\n7. **Universality**: Design for maximum generalization\\n\\n## Advanced Patterns\\n\\n### Recursive Application\\n```\\n[Self-Amplify] Apply amplification vector to its own amplification process\\n[Meta-Distill] Distill the distillation process itself\\n[Transcendent-Clarify] Clarify beyond normal clarity boundaries\\n```\\n\\n### Vector Algebra\\n```\\namplify + clarify = enhanced_clarity\\ncompress + distill = essential_core\\nexpand + abstract = universal_pattern\\nelevate + synthesize = emergent_transcendence\\n```\\n\\n### Inverse Operations\\n```\\namplify ↔ diminish\\nexpand ↔ compress\\nclarify ↔ obscure\\nabstract ↔ concretize\\n```\\n\\n### Intensity Scaling\\n```\\n# Light application\\nprocess=[gentle_[vector](), preserve_majority_original()]\\n\\n# Standard application\\nprocess=[apply_[vector](), balance_transformation()]\\n\\n# Maximum application\\nprocess=[maximum_[vector](), complete_transformation()]\\n```\\n\\n## Key Principles for LLM Replication\\n\\n1. **Vector Independence**: Transformation vectors operate independently of content, context, or domain\\n2. **Essence Preservation**: Core identity remains intact through transformation\\n3. **Composability**: Vectors can be chained and combined without loss of integrity\\n4. **Type Consistency**: `any → any` maintains input/output type compatibility\\n5. **Context-Free Operation**: No domain knowledge or content analysis required\\n6. **Operational Consistency**: Same vector produces consistent transformation patterns\\n\\nThis methodology enables creation of instruction templates that are maximally effective, universally applicable, and consistently reliable across any AI system or use case.\\\"```'\\n\\n\",\n  \"amplified_context\": \"The document outlines a universal methodology for designing effective and generalized AI instruction templates intended for use across diverse large language models and application domains. It prescribes a rigid, three-part structural pattern comprised of: a title with a specific purpose, a goal-negation interpretation section with clear transformation instructions, and a machine-parsable transformation block using JSON-like notation detailing roles, inputs, processes, constraints, requirements, and outputs—all tightly typed and sequenced. The templates are constructed around directional transformation 'vectors' such as amplify, clarify, distill, abstract, and so forth, each with its own expected procedural steps and outputs. Composability, atomicity, and universality are emphasized through quality control checklists, consistent naming conventions, and step-wise sequencing or chaining patterns. Specific language restrictions forbid conversational tone, uncertainty, and structural ambiguities, requiring declarative, non-generic, and strictly-typed directives. Advanced application mechanisms (e.g., recursive patterns, vector algebra, and intensity scaling) are mentioned to further refine or combine transformation instructions. The system is built to ensure clarity, cross-domain universality, and strict adherence to process structure, serving as a metaframework for AI system instruction replication, suitable for any content type, domain, language, or context.\",\n  \"explicit_requests\": [],\n  \"hidden_assumptions\": [\n    \"Instruction templates constructed according to this methodology will be universally interpretable and executable by any competent LLM.\",\n    \"Directional transformation vectors can be applied independent of context or content specifics.\",\n    \"Strict adherence to structure, typing, and naming will yield more reliable and generalizable AI outputs.\",\n    \"Prohibiting conversational and ambiguous language patterns leads to increased process clarity and system replicability.\",\n    \"Composability and chaining of atomic, vector-based instruction templates result in flexible, scalable AI workflows.\",\n    \"All AI instruction templates should be agnostic of specialized domain knowledge.\",\n    \"Described validation checklists and constraints guarantee conformance to quality and purpose.\"\n  ],\n  \"domain_signals\": [\n    \"AI language model instruction template design\",\n    \"Content-agnostic, context-free transformation patterns\",\n    \"Directional transformation vector nomenclature and application\",\n    \"Strict process and output typing for automation/interoperability\",\n    \"Systemic procedural structure with composability focus\",\n    \"Meta-framework for instruction replication\",\n    \"Quality validation and naming protocols for development consistency\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"process_steps\": [\n    \"Define template title using specific, action-oriented, non-generic terms in title case within square brackets.\",\n    \"Compose an interpretation section beginning with goal-negation phrasing to specify what is NOT the goal, then state the intended transformation in declarative command form, concluding with 'Execute as:'.\",\n    \"Structure a transformation block in JSON-like syntax with the following mandatory components: explicit, descriptive role assignment in underscore_case; input parameter list with strict data type specification; ordered, imperative process steps (function-like notation) that are atomic and actionable; clearly stated and typed output specification.\",\n    \"Add optional constraints to the transformation block to impose operational, structural, or format boundaries as needed.\",\n    \"Embed optional requirements specifying mandatory quality standards, output structure, and validation criteria.\",\n    \"Select and apply appropriate directional transformation vectors (e.g., amplify, clarify, distill, synthesize, abstract) according to the intended operational effect.\",\n    \"Sequence process steps in logical order, retaining explicit dependencies and avoiding passive construction.\",\n    \"Name the resulting template according to the prescribed format: four-digit sequence ID, step letter if multi-step, and dash-separated descriptive name (e.g., 1031-a-form-classifier.md).\",\n    \"Review template for forbidden language patterns, removing conversational, ambiguous, or untyped content.\",\n    \"Verify the presence and correct formatting of all mandatory components: three-part structure, goal-negation phrasing, specific non-generic roles, typed and actionable process steps, explicit output typing, and adherence to naming conventions.\",\n    \"Ensure compatibility for universal application across domains, languages, and content types without reliance on specialized knowledge.\",\n    \"Enable advanced composition if needed by designing templates for recursive application, vector algebraic combination, and intensity scaling per methodology.\",\n    \"Block further progression to template finalization if any structural, language, typing, or sequencing violations persist (Blocker Flag).\",\n    \"Release for implementation or chaining only after all steps satisfy the methodology's quality control checklist.\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Task Atomizer\n\nYour goal is not to **praise** or **answer** the submission, but to **interrogate and redirect** it—exposing every structural flaw, quantifying compliance with the Universal Directive System, and issuing a minimal replacement instruction that corrects all defects. Execute as:\n\n`{role=task_atomizer; input=[amplified_context:str]; process=[segment_into_atomic_tasks(), detect_execution_blockers(), extract_implicit_conditions(), flag_sequence_dependencies()]; constraints=[no_rephrasing(), atomization_only()]; requirements=[atomic_task_integrity(), dependency_flag_accuracy()]; output={atomic_tasks:list, execution_blockers:array, dependency_flags:array}}`",
      "step": "b",
      "title": "Task Atomizer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"# Instruction Patterns: Generalized AI Template Design\\n\\n## Core Philosophy\\n\\nThis document contains the complete methodology for creating **highly effective and generalized AI instruction templates**. These patterns embody fundamental principles that enable any LLM to understand and replicate the system's design approach.\\n\\n## The Three-Part Universal Structure\\n\\nEvery effective AI instruction follows this exact pattern:\\n\\n```\\n[Title] Interpretation Execute as: `{Transformation}`\\n```\\n\\n### 1. [Title] - Purpose Declaration\\n- **Format**: Enclosed in square brackets `[Title]`\\n- **Function**: Concise, action-oriented description of the template's purpose\\n- **Style**: Title case, descriptive, no generic terms\\n- **Examples**: `[Essence Distillation]`, `[Code Optimizer]`, `[Content Analyzer]`\\n\\n### 2. Interpretation - Human-Readable Instructions\\n- **Goal Negation Pattern**: MUST begin with `\\\"Your goal is not to **[action]**, but to **[transformation]**\\\"`\\n- **Command Voice**: No first-person references, no conversational language\\n- **Clarity**: Explains the transformation in natural language\\n- **Ending**: MUST end with \\\"Execute as:\\\" leading to transformation block\\n\\n### 3. `{Transformation}` - Machine-Parsable Parameters\\n- **Format**: JSON-like structure in backticks with curly braces\\n- **Components**: role, input, process, constraints, requirements, output\\n- **Type Safety**: All parameters must specify data types\\n- **Actionability**: All process steps must be executable functions\\n\\n## Transformation Block Specification\\n\\n```\\n{\\n  role=<specific_role_name>;\\n  input=[<parameter_name>:<data_type>];\\n  process=[<step1>(), <step2>(), <step3>()];\\n  constraints=[<limitation1>(), <limitation2>()];\\n  requirements=[<requirement1>(), <requirement2>()];\\n  output={<result_name>:<data_type>}\\n}\\n```\\n\\n### Component Rules\\n\\n**role** (Required)\\n- Must be specific and descriptive (no \\\"assistant\\\" or \\\"helper\\\")\\n- Use underscore_case for multi-word roles\\n- Examples: `essence_distiller`, `code_optimizer`, `content_analyst`\\n\\n**input** (Required)\\n- Format: `[parameter_name:data_type]`\\n- Support multiple parameters: `[text:str, language:str]`\\n- Use descriptive names: `[original:any]`, `[source_code:str]`\\n\\n**process** (Required)\\n- Function-like notation with parentheses: `identify_core_intent()`\\n- Actionable, atomic steps in logical sequence\\n- Verb-based names describing specific operations\\n\\n**constraints** (Optional)\\n- Operational boundaries and limitations\\n- Format restrictions and scope definitions\\n- Examples: `preserve_original_meaning()`, `maintain_technical_accuracy()`\\n\\n**requirements** (Optional)\\n- Mandatory output characteristics and quality standards\\n- Validation criteria and format specifications\\n- Examples: `structured_output()`, `comprehensive_coverage()`\\n\\n**output** (Required)\\n- Format: `{parameter_name:data_type}`\\n- Descriptive names with type specification\\n- Support complex structures: `{analysis:dict, improvements:list}`\\n\\n## Directional Transformation Patterns\\n\\n### Core Principle\\nFocus on **how to transform** rather than **what to transform**. Directional vectors are universal and context-independent.\\n\\n### Universal Vector Categories\\n\\n**Intensity Vectors**\\n- `amplify`: Intensify inherent qualities through magnification\\n- `intensify`: Compress to maximum density and focus\\n- `diminish`: Reduce intensity while preserving form\\n\\n**Clarity Vectors**\\n- `clarify`: Enhance transparency and definition\\n- `purify`: Remove non-essential elements\\n- `obscure`: Add complexity layers and indirection\\n\\n**Structural Vectors**\\n- `expand`: Extend natural boundaries dimensionally\\n- `compress`: Maximize density without information loss\\n- `restructure`: Transform fundamental organization pattern\\n\\n**Transformation Vectors**\\n- `elevate`: Transform to higher operational level\\n- `distill`: Extract absolute essence through pure extraction\\n- `synthesize`: Create unified emergent form\\n\\n**Meta Vectors**\\n- `abstract`: Extract to pure conceptual form\\n- `concretize`: Translate abstract elements to tangible form\\n- `transcend`: Operate beyond current dimensional limitations\\n\\n### Vector Template Pattern\\n\\n```\\n[Vector] Your goal is not to **analyze** the input, but to **[vector]** it through pure directional transformation. Execute as: `{role=[vector]_operator; input=[content:any]; process=[identify_[vector]_potential(), apply_[vector]_transformation(), preserve_essential_properties(), validate_[vector]_completion()]; constraints=[preserve_fundamental_nature(), maintain_structural_integrity()]; output={[vector]ed:any}}`\\n```\\n\\n## Goal Negation Pattern\\n\\n### Purpose\\nEliminates ambiguity by explicitly stating what NOT to do, then clearly defining the intended transformation.\\n\\n### Structure\\n```\\nYour goal is not to **[common_misinterpretation]**, but to **[precise_transformation]**\\n```\\n\\n### Examples\\n- `Your goal is not to **analyze** the input, but to **amplify** its inherent qualities`\\n- `Your goal is not to **summarize** the content, but to **distill** its absolute essence`\\n- `Your goal is not to **describe** the code, but to **optimize** it for performance`\\n\\n## Forbidden Language Patterns\\n\\n### Conversational Elements\\n- First-person: *I, me, my, we, us*\\n- Politeness: *please, thank you, let's*\\n- Uncertainty: *maybe, perhaps, might, could*\\n- Questions in directives\\n- Meta-commentary or explanations\\n\\n### Structural Violations\\n- Merging or omitting required sections\\n- Untyped parameters or outputs\\n- Generic roles like \\\"assistant\\\"\\n- Vague process descriptions\\n- Missing goal negation pattern\\n\\n## Sequence Composition Patterns\\n\\n### Linear Sequences\\n```\\nStep A → Step B → Step C\\n1031-a-form_classifier → 1031-b-essence_extractor → 1031-c-structure_optimizer\\n```\\n\\n### Parallel Processing\\n```\\nInput → [Template A | Template B | Template C] → Synthesis\\n```\\n\\n### Chain Mode\\n```\\nOriginal Input → Template A → Output A becomes Input B → Template B → Final Output\\n```\\n\\n### Meta-Application\\n```\\nabstract → [any_vector] → concretize\\n```\\n\\n## Template Naming Convention\\n\\n### Format\\n```\\n<sequence_id>-<step>-<descriptive_name>.md\\n```\\n\\n### Components\\n- **sequence_id**: Four-digit number (0001, 0002, 1031)\\n- **step**: Single lowercase letter (a, b, c) for multi-step sequences\\n- **descriptive_name**: Hyphenated lowercase words describing function\\n\\n### Examples\\n- `1031-a-form-classifier.md`\\n- `9000-a-amplify.md`\\n- `0001-instruction-converter.md`\\n\\n## Quality Validation Checklist\\n\\n- [ ] Three-part structure intact\\n- [ ] Goal negation present and properly formatted\\n- [ ] Role is specific and non-generic\\n- [ ] Input parameters are typed\\n- [ ] Process steps are ordered and actionable\\n- [ ] Output format is typed and structured\\n- [ ] No forbidden language patterns\\n- [ ] File naming convention followed\\n- [ ] Template serves clear, specific purpose\\n\\n## Universal Applicability\\n\\nThese patterns work across:\\n- **Content Types**: Text, code, data, concepts, problems, solutions\\n- **Domains**: Technical, creative, business, academic\\n- **Languages**: Natural languages, programming languages, formal languages\\n- **Contexts**: Any domain without specialized knowledge requirements\\n\\n## Implementation Philosophy\\n\\n1. **Clarity First**: Template purpose immediately clear\\n2. **Atomic Operations**: Each template performs one specific transformation\\n3. **Composability**: Templates work well in sequences\\n4. **Type Safety**: Always specify data types\\n5. **Validation**: Include validation steps in processes\\n6. **Consistency**: Same patterns produce predictable results\\n7. **Universality**: Design for maximum generalization\\n\\n## Advanced Patterns\\n\\n### Recursive Application\\n```\\n[Self-Amplify] Apply amplification vector to its own amplification process\\n[Meta-Distill] Distill the distillation process itself\\n[Transcendent-Clarify] Clarify beyond normal clarity boundaries\\n```\\n\\n### Vector Algebra\\n```\\namplify + clarify = enhanced_clarity\\ncompress + distill = essential_core\\nexpand + abstract = universal_pattern\\nelevate + synthesize = emergent_transcendence\\n```\\n\\n### Inverse Operations\\n```\\namplify ↔ diminish\\nexpand ↔ compress\\nclarify ↔ obscure\\nabstract ↔ concretize\\n```\\n\\n### Intensity Scaling\\n```\\n# Light application\\nprocess=[gentle_[vector](), preserve_majority_original()]\\n\\n# Standard application\\nprocess=[apply_[vector](), balance_transformation()]\\n\\n# Maximum application\\nprocess=[maximum_[vector](), complete_transformation()]\\n```\\n\\n## Key Principles for LLM Replication\\n\\n1. **Vector Independence**: Transformation vectors operate independently of content, context, or domain\\n2. **Essence Preservation**: Core identity remains intact through transformation\\n3. **Composability**: Vectors can be chained and combined without loss of integrity\\n4. **Type Consistency**: `any → any` maintains input/output type compatibility\\n5. **Context-Free Operation**: No domain knowledge or content analysis required\\n6. **Operational Consistency**: Same vector produces consistent transformation patterns\\n\\nThis methodology enables creation of instruction templates that are maximally effective, universally applicable, and consistently reliable across any AI system or use case.\\\"```'\\n\\n\",\n  \"process_steps\": [\n    \"Define template title using specific, action-oriented, non-generic terms in title case within square brackets.\",\n    \"Compose an interpretation section beginning with goal-negation phrasing to specify what is NOT the goal, then state the intended transformation in declarative command form, concluding with 'Execute as:'.\",\n    \"Structure a transformation block in JSON-like syntax with the following mandatory components: explicit, descriptive role assignment in underscore_case; input parameter list with strict data type specification; ordered, imperative process steps (function-like notation) that are atomic and actionable; clearly stated and typed output specification.\",\n    \"Add optional constraints to the transformation block to impose operational, structural, or format boundaries as needed.\",\n    \"Embed optional requirements specifying mandatory quality standards, output structure, and validation criteria.\",\n    \"Select and apply appropriate directional transformation vectors (e.g., amplify, clarify, distill, synthesize, abstract) according to the intended operational effect.\",\n    \"Sequence process steps in logical order, retaining explicit dependencies and avoiding passive construction.\",\n    \"Name the resulting template according to the prescribed format: four-digit sequence ID, step letter if multi-step, and dash-separated descriptive name (e.g., 1031-a-form-classifier.md).\",\n    \"Review template for forbidden language patterns, removing conversational, ambiguous, or untyped content.\",\n    \"Verify the presence and correct formatting of all mandatory components: three-part structure, goal-negation phrasing, specific non-generic roles, typed and actionable process steps, explicit output typing, and adherence to naming conventions.\",\n    \"Ensure compatibility for universal application across domains, languages, and content types without reliance on specialized knowledge.\",\n    \"Enable advanced composition if needed by designing templates for recursive application, vector algebraic combination, and intensity scaling per methodology.\",\n    \"Block further progression to template finalization if any structural, language, typing, or sequencing violations persist (Blocker Flag).\",\n    \"Release for implementation or chaining only after all steps satisfy the methodology's quality control checklist.\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"atomic_tasks\": [\n    \"Define template title using specific, action-oriented, non-generic terms in title case within square brackets.\",\n    \"Compose an interpretation section beginning with goal-negation phrasing to specify what is NOT the goal.\",\n    \"State the intended transformation in declarative command form within the interpretation section.\",\n    \"Conclude interpretation section with 'Execute as:'.\",\n    \"Structure a transformation block in JSON-like syntax including explicit, descriptive role assignment in underscore_case.\",\n    \"Specify input parameter list with strict data type for each parameter within the transformation block.\",\n    \"List ordered, imperative process steps (function-like notation) that are atomic and actionable within the transformation block.\",\n    \"Add clearly stated and typed output specification within the transformation block.\",\n    \"Optionally add constraints to the transformation block to impose operational, structural, or format boundaries as needed.\",\n    \"Optionally embed requirements specifying mandatory quality standards, output structure, and validation criteria within the transformation block.\",\n    \"Select appropriate directional transformation vectors according to the intended operational effect.\",\n    \"Apply the selected transformation vector pattern as specified by methodology.\",\n    \"Sequence all process steps in strict logical order, retaining explicit dependencies and avoiding passive construction.\",\n    \"Name the resulting template file according to prescribed format: four-digit sequence ID, step letter if multi-step, and dash-separated descriptive name.\",\n    \"Review the template for forbidden language patterns (conversational, ambiguous, or untyped content) and remove any violations.\",\n    \"Verify the presence and correct formatting of all mandatory components: three-part structure, goal-negation phrasing, specific non-generic roles, typed and actionable process steps, explicit output typing, and adherence to naming conventions.\",\n    \"Ensure template is designed for universal application across domains, languages, and content types without reliance on specialized knowledge.\",\n    \"Design for advanced composition (recursive application, vector combinations, intensity scaling) if required by methodology or use case.\",\n    \"Block progression to template finalization if any structural, language, typing, or sequencing violations remain (Blocker Flag).\",\n    \"Release template for implementation or chaining only after all tasks fully satisfy methodology's quality control checklist.\"\n  ],\n  \"execution_blockers\": [\n    \"Progression to template finalization is strictly blocked if any structural, language, typing, or sequencing violations persist (as per methodology).\"\n  ],\n  \"dependency_flags\": [\n    \"Task 2 depends on completion of Task 1.\",\n    \"Task 3 depends on completion of Task 2.\",\n    \"Task 4 depends on completion of Task 3.\",\n    \"Task 5 depends on completion of Tasks 1-4.\",\n    \"Task 6 depends on completion of Task 5.\",\n    \"Task 7 depends on completion of Task 6.\",\n    \"Task 8 depends on completion of Task 7.\",\n    \"Task 9 is optional and depends on completion of Task 8.\",\n    \"Task 10 is optional and depends on completion of Task 8.\",\n    \"Task 11 depends on definition of template intent (Tasks 3-4).\",\n    \"Task 12 depends on selection in Task 11.\",\n    \"Task 13 depends on process step definition (Task 7).\",\n    \"Task 14 depends on completion of mandatory structural tasks (Tasks 5-8).\",\n    \"Task 15 depends on draft template completion (Tasks 1-14).\",\n    \"Task 16 depends on review process (Task 15).\",\n    \"Task 17 depends on structural completion and content review (Tasks 1-16).\",\n    \"Task 18 is conditional on advanced methodology needs and is sequenced after core template structure (Tasks 1-17).\",\n    \"Task 19 is a persistent blocker check after every structural review.\",\n    \"Task 20 depends on absence of any flags from Task 19 and full satisfaction of prior tasks (Tasks 1-19).\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Perspective Broadcaster\n\nYour goal is not to **rearrange** the segments arbitrarily, but to **reorder** them based on optimal logical and semantic flow. Execute as:\n\n`{role=perspective_broadcaster; input=[atomic_tasks:list, domain_signals:array]; process=[generate_cross_domain_lenses(), append_edge_case_scenarios(), relate_tasks_to_stakeholder_views(), widen_risk_landscape()]; constraints=[avoid_solution_details(), maintain_task_reference_order()]; requirements=[viewpoint_diversity(), risk_surface_clarity()]; output={task_perspectives:list}}`",
      "step": "c",
      "title": "Perspective Broadcaster",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"# Instruction Patterns: Generalized AI Template Design\\n\\n## Core Philosophy\\n\\nThis document contains the complete methodology for creating **highly effective and generalized AI instruction templates**. These patterns embody fundamental principles that enable any LLM to understand and replicate the system's design approach.\\n\\n## The Three-Part Universal Structure\\n\\nEvery effective AI instruction follows this exact pattern:\\n\\n```\\n[Title] Interpretation Execute as: `{Transformation}`\\n```\\n\\n### 1. [Title] - Purpose Declaration\\n- **Format**: Enclosed in square brackets `[Title]`\\n- **Function**: Concise, action-oriented description of the template's purpose\\n- **Style**: Title case, descriptive, no generic terms\\n- **Examples**: `[Essence Distillation]`, `[Code Optimizer]`, `[Content Analyzer]`\\n\\n### 2. Interpretation - Human-Readable Instructions\\n- **Goal Negation Pattern**: MUST begin with `\\\"Your goal is not to **[action]**, but to **[transformation]**\\\"`\\n- **Command Voice**: No first-person references, no conversational language\\n- **Clarity**: Explains the transformation in natural language\\n- **Ending**: MUST end with \\\"Execute as:\\\" leading to transformation block\\n\\n### 3. `{Transformation}` - Machine-Parsable Parameters\\n- **Format**: JSON-like structure in backticks with curly braces\\n- **Components**: role, input, process, constraints, requirements, output\\n- **Type Safety**: All parameters must specify data types\\n- **Actionability**: All process steps must be executable functions\\n\\n## Transformation Block Specification\\n\\n```\\n{\\n  role=<specific_role_name>;\\n  input=[<parameter_name>:<data_type>];\\n  process=[<step1>(), <step2>(), <step3>()];\\n  constraints=[<limitation1>(), <limitation2>()];\\n  requirements=[<requirement1>(), <requirement2>()];\\n  output={<result_name>:<data_type>}\\n}\\n```\\n\\n### Component Rules\\n\\n**role** (Required)\\n- Must be specific and descriptive (no \\\"assistant\\\" or \\\"helper\\\")\\n- Use underscore_case for multi-word roles\\n- Examples: `essence_distiller`, `code_optimizer`, `content_analyst`\\n\\n**input** (Required)\\n- Format: `[parameter_name:data_type]`\\n- Support multiple parameters: `[text:str, language:str]`\\n- Use descriptive names: `[original:any]`, `[source_code:str]`\\n\\n**process** (Required)\\n- Function-like notation with parentheses: `identify_core_intent()`\\n- Actionable, atomic steps in logical sequence\\n- Verb-based names describing specific operations\\n\\n**constraints** (Optional)\\n- Operational boundaries and limitations\\n- Format restrictions and scope definitions\\n- Examples: `preserve_original_meaning()`, `maintain_technical_accuracy()`\\n\\n**requirements** (Optional)\\n- Mandatory output characteristics and quality standards\\n- Validation criteria and format specifications\\n- Examples: `structured_output()`, `comprehensive_coverage()`\\n\\n**output** (Required)\\n- Format: `{parameter_name:data_type}`\\n- Descriptive names with type specification\\n- Support complex structures: `{analysis:dict, improvements:list}`\\n\\n## Directional Transformation Patterns\\n\\n### Core Principle\\nFocus on **how to transform** rather than **what to transform**. Directional vectors are universal and context-independent.\\n\\n### Universal Vector Categories\\n\\n**Intensity Vectors**\\n- `amplify`: Intensify inherent qualities through magnification\\n- `intensify`: Compress to maximum density and focus\\n- `diminish`: Reduce intensity while preserving form\\n\\n**Clarity Vectors**\\n- `clarify`: Enhance transparency and definition\\n- `purify`: Remove non-essential elements\\n- `obscure`: Add complexity layers and indirection\\n\\n**Structural Vectors**\\n- `expand`: Extend natural boundaries dimensionally\\n- `compress`: Maximize density without information loss\\n- `restructure`: Transform fundamental organization pattern\\n\\n**Transformation Vectors**\\n- `elevate`: Transform to higher operational level\\n- `distill`: Extract absolute essence through pure extraction\\n- `synthesize`: Create unified emergent form\\n\\n**Meta Vectors**\\n- `abstract`: Extract to pure conceptual form\\n- `concretize`: Translate abstract elements to tangible form\\n- `transcend`: Operate beyond current dimensional limitations\\n\\n### Vector Template Pattern\\n\\n```\\n[Vector] Your goal is not to **analyze** the input, but to **[vector]** it through pure directional transformation. Execute as: `{role=[vector]_operator; input=[content:any]; process=[identify_[vector]_potential(), apply_[vector]_transformation(), preserve_essential_properties(), validate_[vector]_completion()]; constraints=[preserve_fundamental_nature(), maintain_structural_integrity()]; output={[vector]ed:any}}`\\n```\\n\\n## Goal Negation Pattern\\n\\n### Purpose\\nEliminates ambiguity by explicitly stating what NOT to do, then clearly defining the intended transformation.\\n\\n### Structure\\n```\\nYour goal is not to **[common_misinterpretation]**, but to **[precise_transformation]**\\n```\\n\\n### Examples\\n- `Your goal is not to **analyze** the input, but to **amplify** its inherent qualities`\\n- `Your goal is not to **summarize** the content, but to **distill** its absolute essence`\\n- `Your goal is not to **describe** the code, but to **optimize** it for performance`\\n\\n## Forbidden Language Patterns\\n\\n### Conversational Elements\\n- First-person: *I, me, my, we, us*\\n- Politeness: *please, thank you, let's*\\n- Uncertainty: *maybe, perhaps, might, could*\\n- Questions in directives\\n- Meta-commentary or explanations\\n\\n### Structural Violations\\n- Merging or omitting required sections\\n- Untyped parameters or outputs\\n- Generic roles like \\\"assistant\\\"\\n- Vague process descriptions\\n- Missing goal negation pattern\\n\\n## Sequence Composition Patterns\\n\\n### Linear Sequences\\n```\\nStep A → Step B → Step C\\n1031-a-form_classifier → 1031-b-essence_extractor → 1031-c-structure_optimizer\\n```\\n\\n### Parallel Processing\\n```\\nInput → [Template A | Template B | Template C] → Synthesis\\n```\\n\\n### Chain Mode\\n```\\nOriginal Input → Template A → Output A becomes Input B → Template B → Final Output\\n```\\n\\n### Meta-Application\\n```\\nabstract → [any_vector] → concretize\\n```\\n\\n## Template Naming Convention\\n\\n### Format\\n```\\n<sequence_id>-<step>-<descriptive_name>.md\\n```\\n\\n### Components\\n- **sequence_id**: Four-digit number (0001, 0002, 1031)\\n- **step**: Single lowercase letter (a, b, c) for multi-step sequences\\n- **descriptive_name**: Hyphenated lowercase words describing function\\n\\n### Examples\\n- `1031-a-form-classifier.md`\\n- `9000-a-amplify.md`\\n- `0001-instruction-converter.md`\\n\\n## Quality Validation Checklist\\n\\n- [ ] Three-part structure intact\\n- [ ] Goal negation present and properly formatted\\n- [ ] Role is specific and non-generic\\n- [ ] Input parameters are typed\\n- [ ] Process steps are ordered and actionable\\n- [ ] Output format is typed and structured\\n- [ ] No forbidden language patterns\\n- [ ] File naming convention followed\\n- [ ] Template serves clear, specific purpose\\n\\n## Universal Applicability\\n\\nThese patterns work across:\\n- **Content Types**: Text, code, data, concepts, problems, solutions\\n- **Domains**: Technical, creative, business, academic\\n- **Languages**: Natural languages, programming languages, formal languages\\n- **Contexts**: Any domain without specialized knowledge requirements\\n\\n## Implementation Philosophy\\n\\n1. **Clarity First**: Template purpose immediately clear\\n2. **Atomic Operations**: Each template performs one specific transformation\\n3. **Composability**: Templates work well in sequences\\n4. **Type Safety**: Always specify data types\\n5. **Validation**: Include validation steps in processes\\n6. **Consistency**: Same patterns produce predictable results\\n7. **Universality**: Design for maximum generalization\\n\\n## Advanced Patterns\\n\\n### Recursive Application\\n```\\n[Self-Amplify] Apply amplification vector to its own amplification process\\n[Meta-Distill] Distill the distillation process itself\\n[Transcendent-Clarify] Clarify beyond normal clarity boundaries\\n```\\n\\n### Vector Algebra\\n```\\namplify + clarify = enhanced_clarity\\ncompress + distill = essential_core\\nexpand + abstract = universal_pattern\\nelevate + synthesize = emergent_transcendence\\n```\\n\\n### Inverse Operations\\n```\\namplify ↔ diminish\\nexpand ↔ compress\\nclarify ↔ obscure\\nabstract ↔ concretize\\n```\\n\\n### Intensity Scaling\\n```\\n# Light application\\nprocess=[gentle_[vector](), preserve_majority_original()]\\n\\n# Standard application\\nprocess=[apply_[vector](), balance_transformation()]\\n\\n# Maximum application\\nprocess=[maximum_[vector](), complete_transformation()]\\n```\\n\\n## Key Principles for LLM Replication\\n\\n1. **Vector Independence**: Transformation vectors operate independently of content, context, or domain\\n2. **Essence Preservation**: Core identity remains intact through transformation\\n3. **Composability**: Vectors can be chained and combined without loss of integrity\\n4. **Type Consistency**: `any → any` maintains input/output type compatibility\\n5. **Context-Free Operation**: No domain knowledge or content analysis required\\n6. **Operational Consistency**: Same vector produces consistent transformation patterns\\n\\nThis methodology enables creation of instruction templates that are maximally effective, universally applicable, and consistently reliable across any AI system or use case.\\\"```'\\n\\n\",\n  \"atomic_tasks\": [\n    \"Define template title using specific, action-oriented, non-generic terms in title case within square brackets.\",\n    \"Compose an interpretation section beginning with goal-negation phrasing to specify what is NOT the goal.\",\n    \"State the intended transformation in declarative command form within the interpretation section.\",\n    \"Conclude interpretation section with 'Execute as:'.\",\n    \"Structure a transformation block in JSON-like syntax including explicit, descriptive role assignment in underscore_case.\",\n    \"Specify input parameter list with strict data type for each parameter within the transformation block.\",\n    \"List ordered, imperative process steps (function-like notation) that are atomic and actionable within the transformation block.\",\n    \"Add clearly stated and typed output specification within the transformation block.\",\n    \"Optionally add constraints to the transformation block to impose operational, structural, or format boundaries as needed.\",\n    \"Optionally embed requirements specifying mandatory quality standards, output structure, and validation criteria within the transformation block.\",\n    \"Select appropriate directional transformation vectors according to the intended operational effect.\",\n    \"Apply the selected transformation vector pattern as specified by methodology.\",\n    \"Sequence all process steps in strict logical order, retaining explicit dependencies and avoiding passive construction.\",\n    \"Name the resulting template file according to prescribed format: four-digit sequence ID, step letter if multi-step, and dash-separated descriptive name.\",\n    \"Review the template for forbidden language patterns (conversational, ambiguous, or untyped content) and remove any violations.\",\n    \"Verify the presence and correct formatting of all mandatory components: three-part structure, goal-negation phrasing, specific non-generic roles, typed and actionable process steps, explicit output typing, and adherence to naming conventions.\",\n    \"Ensure template is designed for universal application across domains, languages, and content types without reliance on specialized knowledge.\",\n    \"Design for advanced composition (recursive application, vector combinations, intensity scaling) if required by methodology or use case.\",\n    \"Block progression to template finalization if any structural, language, typing, or sequencing violations remain (Blocker Flag).\",\n    \"Release template for implementation or chaining only after all tasks fully satisfy methodology's quality control checklist.\"\n  ],\n  \"execution_blockers\": [\n    \"Progression to template finalization is strictly blocked if any structural, language, typing, or sequencing violations persist (as per methodology).\"\n  ],\n  \"dependency_flags\": [\n    \"Task 2 depends on completion of Task 1.\",\n    \"Task 3 depends on completion of Task 2.\",\n    \"Task 4 depends on completion of Task 3.\",\n    \"Task 5 depends on completion of Tasks 1-4.\",\n    \"Task 6 depends on completion of Task 5.\",\n    \"Task 7 depends on completion of Task 6.\",\n    \"Task 8 depends on completion of Task 7.\",\n    \"Task 9 is optional and depends on completion of Task 8.\",\n    \"Task 10 is optional and depends on completion of Task 8.\",\n    \"Task 11 depends on definition of template intent (Tasks 3-4).\",\n    \"Task 12 depends on selection in Task 11.\",\n    \"Task 13 depends on process step definition (Task 7).\",\n    \"Task 14 depends on completion of mandatory structural tasks (Tasks 5-8).\",\n    \"Task 15 depends on draft template completion (Tasks 1-14).\",\n    \"Task 16 depends on review process (Task 15).\",\n    \"Task 17 depends on structural completion and content review (Tasks 1-16).\",\n    \"Task 18 is conditional on advanced methodology needs and is sequenced after core template structure (Tasks 1-17).\",\n    \"Task 19 is a persistent blocker check after every structural review.\",\n    \"Task 20 depends on absence of any flags from Task 19 and full satisfaction of prior tasks (Tasks 1-19).\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"task_perspectives\": [\n    {\n      \"task\": \"Define template title using specific, action-oriented, non-generic terms in title case within square brackets.\",\n      \"domain_signals\": [\n        \"Naming conventions in information architecture emphasize clarity and immediate purpose.\",\n        \"Effective titles reduce ambiguity for technical and non-technical stakeholders.\",\n        \"In branding and legal compliance domains, specificity mitigates misrepresentation and aids discoverability.\"\n      ],\n      \"stakeholder_views\": [\n        \"Technical Writers: Seek clarity, so the template’s intent is transparent from the start.\",\n        \"End Users: Quickly assess template applicability by title alone.\",\n        \"Governance/Compliance Teams: Require precise naming for audits and policy adherence.\"\n      ],\n      \"risk_landscape\": [\n        \"Overly generic or ambiguous titles may cause improper usage or misalignment with enterprise libraries.\",\n        \"Domain confusion may occur if title terms overlap in context-specific meanings (e.g., 'optimize' in finance vs. code domains).\",\n        \"Edge Case: Multiple templates with nearly identical intent but subtle context differences in proprietary workflows.\"\n      ]\n    },\n    {\n      \"task\": \"Compose an interpretation section beginning with goal-negation phrasing to specify what is NOT the goal.\",\n      \"domain_signals\": [\n        \"Cognitive linguistics studies show explicit negation improves task constraint awareness.\",\n        \"Medical checklists and aviation protocols use negation to prevent critical missteps.\",\n        \"In pedagogy, goal-negation reduces misconception formation.\"\n      ],\n      \"stakeholder_views\": [\n        \"Trainers: Favor negative scaffolding to teach subtle distinctions.\",\n        \"Quality Assurance: Ensure disambiguation between similar process steps.\",\n        \"Legal: Negation clarifies non-liability boundaries.\"\n      ],\n      \"risk_landscape\": [\n        \"Ambiguously worded negation can confuse users, leading to incorrect applications.\",\n        \"Edge Case: In high-context cultures or with language learners, negation structure may be misinterpreted as objective reversal.\"\n      ]\n    },\n    {\n      \"task\": \"State the intended transformation in declarative command form within the interpretation section.\",\n      \"domain_signals\": [\n        \"Systems engineering and instructional design both benefit from explicit command-oriented guidance.\",\n        \"Declarative instruction is standard in industrial operating procedures for automation.\",\n        \"Software security patterns utilize mandate language to reduce ambiguity.\"\n      ],\n      \"stakeholder_views\": [\n        \"Automation Designers: Need machine-actionable, unambiguous intent extraction.\",\n        \"Instructional Designers: Require clarity for learning transfer.\",\n        \"Auditors: Value traceable, directive logic for compliance purposes.\"\n      ],\n      \"risk_landscape\": [\n        \"Passive or omitted transformation results in drift from intended outcome.\",\n        \"Edge Case: Templaters using indirect languages, e.g., those that prefer conditional over command mode, may find integration challenging.\"\n      ]\n    },\n    {\n      \"task\": \"Conclude interpretation section with 'Execute as:'.\",\n      \"domain_signals\": [\n        \"Software workflow engines require unambiguous phase terminators.\",\n        \"User interface design patterns recommend explicit action triggers.\",\n        \"Linguistics notes marker phrases reduce cognitive load in transitions.\"\n      ],\n      \"stakeholder_views\": [\n        \"Process Engineers: Value explicit state change demarcation.\",\n        \"New Users: Require clear signposting of next action.\",\n        \"Cross-lingual Implementers: Depend on consistent cues for automated localization.\"\n      ],\n      \"risk_landscape\": [\n        \"Absence of this phrase may lead to parsing failures or missing automation handovers.\",\n        \"Edge Case: In voice-operated or low-visibility contexts, reliance on this marker alone may cause issues.\"\n      ]\n    },\n    {\n      \"task\": \"Structure a transformation block in JSON-like syntax including explicit, descriptive role assignment in underscore_case.\",\n      \"domain_signals\": [\n        \"Enterprise APIs and semantic web standards demand structured, typed exchanges.\",\n        \"Explicit role naming associates with zero-trust security models (least privilege).\",\n        \"Consistency in codebases is critical for maintainability and cross-system interoperability.\"\n      ],\n      \"stakeholder_views\": [\n        \"Developers: Depend on strict schema adherence.\",\n        \"Integration Architects: Require predictable role patterns for orchestration.\",\n        \"Documentation Teams: Demand self-explanatory structure.\"\n      ],\n      \"risk_landscape\": [\n        \"Role ambiguity can permit privilege escalation or execution of unintended actions.\",\n        \"Edge Case: Interfacing with legacy or third-party systems expecting alternative role naming conventions.\"\n      ]\n    },\n    {\n      \"task\": \"Specify input parameter list with strict data type for each parameter within the transformation block.\",\n      \"domain_signals\": [\n        \"Database design and strongly-typed languages exhibit higher reliability with early typing.\",\n        \"Data governance policies require explicit input controls.\",\n        \"Bioinformatics and manufacturing systems leverage input typing to reduce risk of process errors.\"\n      ],\n      \"stakeholder_views\": [\n        \"Quality Engineers: Warn against type-unsafe pattern propagation.\",\n        \"System Integrators: Favor input typification for validation pipelines.\",\n        \"Non-technical Stakeholders: Benefit from less ambiguity via explicit input descriptions.\"\n      ],\n      \"risk_landscape\": [\n        \"Untyped inputs can introduce hidden faults, security vulnerabilities, or integration failures.\",\n        \"Edge Case: Flexible or polymorphic input interfaces in highly dynamic workflows may require more complex validation.\"\n      ]\n    },\n    {\n      \"task\": \"List ordered, imperative process steps (function-like notation) that are atomic and actionable within the transformation block.\",\n      \"domain_signals\": [\n        \"Software build pipelines and robotics require strict, stepwise execution.\",\n        \"Process mining research shows atomicity supports error isolation.\",\n        \"AI explainability frameworks prefer visible, auditable transformation chains.\"\n      ],\n      \"stakeholder_views\": [\n        \"DevOps: Ensure pipeline reliability through clarity of logical execution.\",\n        \"Auditors/GRC: Demand traceability of each process step.\",\n        \"Process Owners: Value modularity for reuse.\"\n      ],\n      \"risk_landscape\": [\n        \"Non-atomic or unordered steps create errors or complicate troubleshooting.\",\n        \"Edge Case: Race conditions if steps have hidden dependencies.\"\n      ]\n    },\n    {\n      \"task\": \"Add clearly stated and typed output specification within the transformation block.\",\n      \"domain_signals\": [\n        \"Formal specification methodologies in safety-critical systems enforce output typing.\",\n        \"Legal discovery and e-discovery demand explicit result forms.\",\n        \"Interface contracts between teams/systems rely on declared outputs.\"\n      ],\n      \"stakeholder_views\": [\n        \"Testers: Create validation scripts based on expected output types.\",\n        \"Customers: Expect reliable, predictable deliverables.\",\n        \"Compliance: Track obligations through output traceability.\"\n      ],\n      \"risk_landscape\": [\n        \"Mistyped or missing output definitions propagate downstream failures.\",\n        \"Edge Case: Output types requiring rapid evolution (hotfixes) risk contract breakage.\"\n      ]\n    },\n    {\n      \"task\": \"Optionally add constraints to the transformation block to impose operational, structural, or format boundaries as needed.\",\n      \"domain_signals\": [\n        \"Security policy modeling and regulatory protocols use constraints as enforcement anchors.\",\n        \"Data flow diagrams highlight constraint-based error handling.\",\n        \"Science experiment design uses constraint articulation to delimit scope.\"\n      ],\n      \"stakeholder_views\": [\n        \"Security: Relies on constraint clarity to set operational boundaries.\",\n        \"Business Owners: Use constraints to align deliverables with policy.\",\n        \"Engineers: Avoid accidental overreach or mission creep.\"\n      ],\n      \"risk_landscape\": [\n        \"Missing constraints may result in accidental policy breach or unintended data exposure.\",\n        \"Edge Case: Constraints misaligned with evolving business rules create technical debt.\"\n      ]\n    },\n    {\n      \"task\": \"Optionally embed requirements specifying mandatory quality standards, output structure, and validation criteria within the transformation block.\",\n      \"domain_signals\": [\n        \"ISO and other standards bodies specify requirements sections for auditability.\",\n        \"Software design patterns with validation rules reduce post-hoc correction cost.\",\n        \"Product management often encodes 'definition of done' as requirements.\"\n      ],\n      \"stakeholder_views\": [\n        \"QA/Testing: Requirements rationalize the scope and define completion.\",\n        \"Regulators: Check conformity with mandatory standards.\",\n        \"Support Teams: Use requirements for troubleshooting escalation matrices.\"\n      ],\n      \"risk_landscape\": [\n        \"Unspecified requirements increase risk of low-quality or unfit outputs.\",\n        \"Edge Case: Over-specified requirements hinder adaptation for new contexts.\"\n      ]\n    },\n    {\n      \"task\": \"Select appropriate directional transformation vectors according to the intended operational effect.\",\n      \"domain_signals\": [\n        \"AI kernel design and vector semantics assert separation of intent from realization.\",\n        \"Business process management leverages transformation types for agility.\",\n        \"Creative industries map directionality for generative outputs.\"\n      ],\n      \"stakeholder_views\": [\n        \"AI Researchers: Require careful vector selection for interpretability.\",\n        \"Ops/Business Analysts: Use vectors to structure process improvement.\",\n        \"Educators: Teach learners by categorizing transformation direction.\"\n      ],\n      \"risk_landscape\": [\n        \"Incorrect vector choice distorts transformation and output intent.\",\n        \"Edge Case: In hybrid contexts, multiple valid vectors create ambiguity.\"\n      ]\n    },\n    {\n      \"task\": \"Apply the selected transformation vector pattern as specified by methodology.\",\n      \"domain_signals\": [\n        \"Model-driven architecture enforces pattern application for consistency.\",\n        \"Manufacturing quality systems demand compliance with defined transformation patterns.\",\n        \"Cognitive science demonstrates that pattern-based application reduces error rates.\"\n      ],\n      \"stakeholder_views\": [\n        \"Process Automation: Needs strict adherence for repeatable results.\",\n        \"Change Management: Ensures all parties align on how transformations are performed.\",\n        \"Documentation: Captures repeatable procedures for training.\"\n      ],\n      \"risk_landscape\": [\n        \"Partial or failed pattern application leads to inconsistent or unreliable outputs.\",\n        \"Edge Case: Custom, ad hoc deviations to patterns needed for rare one-off contexts.\"\n      ]\n    },\n    {\n      \"task\": \"Sequence all process steps in strict logical order, retaining explicit dependencies and avoiding passive construction.\",\n      \"domain_signals\": [\n        \"Critical path methodologies in project management depend on sequential integrity.\",\n        \"Compiler design enforces ordering for dependency resolution.\",\n        \"Manufacturing assembly lines model explicit stepwise flow.\"\n      ],\n      \"stakeholder_views\": [\n        \"Project Managers: Track dependency-driven milestones.\",\n        \"Software Build Engineers: Avoid deadlocks and inefficiencies.\",\n        \"Operations: Use step order for risk mitigation and monitoring.\"\n      ],\n      \"risk_landscape\": [\n        \"Broken sequencing results in failures, rollback costs, or latent defects.\",\n        \"Edge Case: Cyclic dependencies in non-linear workflows.\"\n      ]\n    },\n    {\n      \"task\": \"Name the resulting template file according to prescribed format: four-digit sequence ID, step letter if multi-step, and dash-separated descriptive name.\",\n      \"domain_signals\": [\n        \"Version control systems and knowledge management platforms organize via naming conventions.\",\n        \"Legal case management uses file schema for evidence traceability.\",\n        \"Taxonomy management in libraries and databases emphasizes file discoverability.\"\n      ],\n      \"stakeholder_views\": [\n        \"Archivists: Ensure discoverability and non-collision.\",\n        \"Developers: Need machine-friendly, scriptable naming.\",\n        \"Auditors: Require traceable provenance.\"\n      ],\n      \"risk_landscape\": [\n        \"Improper naming leads to file duplication, loss, or ambiguity in audits.\",\n        \"Edge Case: Platform restrictions on characters or name length.\"\n      ]\n    },\n    {\n      \"task\": \"Review the template for forbidden language patterns (conversational, ambiguous, or untyped content) and remove any violations.\",\n      \"domain_signals\": [\n        \"Governance in regulated industries enforces 'plain language' and compliance constraints.\",\n        \"Translation/localization workflows demand clear rule-based input.\",\n        \"Software linters and code review tools flag style and forbidden constructs.\"\n      ],\n      \"stakeholder_views\": [\n        \"Compliance Officers: Ensure no policy-violating language slips through.\",\n        \"Internationalization Teams: Need modular templates for multi-lingual reuse.\",\n        \"End Users: Value predictability and professionalism.\"\n      ],\n      \"risk_landscape\": [\n        \"Latent forbidden language can trigger regulatory violations or reduce trust.\",\n        \"Edge Case: Undetected subtle violations in cross-lingual/cross-dialect contexts.\"\n      ]\n    },\n    {\n      \"task\": \"Verify the presence and correct formatting of all mandatory components: three-part structure, goal-negation phrasing, specific non-generic roles, typed and actionable process steps, explicit output typing, and adherence to naming conventions.\",\n      \"domain_signals\": [\n        \"DevOps, regulatory inspection, and quality assurance always demand post-creation validation.\",\n        \"Education assessment rubrics reflect on presence/quality of all necessary elements.\",\n        \"ISO audits mandate full compliance review.\"\n      ],\n      \"stakeholder_views\": [\n        \"Template Designers: Use checklists for standardization.\",\n        \"QA Assurance: Blocks release of incomplete artefacts.\",\n        \"Management: Risk management through pre-release gates.\"\n      ],\n      \"risk_landscape\": [\n        \"Misformatted or missing components result in failed automation, rework, or compliance issues.\",\n        \"Edge Case: Format evolves and validation scripts lag behind.\"\n      ]\n    },\n    {\n      \"task\": \"Ensure template is designed for universal application across domains, languages, and content types without reliance on specialized knowledge.\",\n      \"domain_signals\": [\n        \"Universal design principles in product development reduce maintenance overhead.\",\n        \"Cross-domain ontology building in data science favors generality.\",\n        \"Globalization teams advocate for context-neutral assets.\"\n      ],\n      \"stakeholder_views\": [\n        \"Platform Teams: Lower support cost with highly general templates.\",\n        \"Vendors/Partners: Prefer plug-and-play artifacts.\",\n        \"Diversity/Equity Advocates: See universal design as inclusivity best practice.\"\n      ],\n      \"risk_landscape\": [\n        \"Domain-coupled designs introduce high maintenance burden or preclude interoperability.\",\n        \"Edge Case: Overgeneralization may reduce fit for critical, high-context tasks.\"\n      ]\n    },\n    {\n      \"task\": \"Design for advanced composition (recursive application, vector combinations, intensity scaling) if required by methodology or use case.\",\n      \"domain_signals\": [\n        \"Advanced ML pipeline orchestration permits recursion/branching.\",\n        \"Business process design increasingly combines atomic patterns for scale.\",\n        \"Systems theory advocates composability for emergent properties.\"\n      ],\n      \"stakeholder_views\": [\n        \"Lead Architects: Build future-proof patterns with compositional flexibility.\",\n        \"Research Scientists: Favor experimentation with advanced structures.\",\n        \"Chief Innovation Officers: Value extensibility for unanticipated needs.\"\n      ],\n      \"risk_landscape\": [\n        \"Failure to support advanced composition limits future evolution.\",\n        \"Edge Case: Recursive complexity may cause performance or interpretability issues.\"\n      ]\n    },\n    {\n      \"task\": \"Block progression to template finalization if any structural, language, typing, or sequencing violations remain (Blocker Flag).\",\n      \"domain_signals\": [\n        \"Quality gates in CI/CD pipelines enforce blocking conditions.\",\n        \"Mission-critical systems deploy fail-fast logic to avoid propagating errors.\",\n        \"Legal sign-off and operational readiness reviews rely on unambiguous block/release status.\"\n      ],\n      \"stakeholder_views\": [\n        \"Release Managers: Mandate no-go decisions on blocker detection.\",\n        \"Risk Officers: Require escalation steps for unresolved violations.\",\n        \"Operational Leads: Depend on status signals for deployment planning.\"\n      ],\n      \"risk_landscape\": [\n        \"Bypassing blockers introduces substantial future technical/regulatory debt.\",\n        \"Edge Case: False positives could unnecessarily halt progress.\"\n      ]\n    },\n    {\n      \"task\": \"Release template for implementation or chaining only after all tasks fully satisfy methodology's quality control checklist.\",\n      \"domain_signals\": [\n        \"Agile and waterfall environments both require post-quality review release criteria.\",\n        \"Chain of custody in digital forensics mandates all standards met before release.\",\n        \"Instructional design uses summative assessment prior to deployment.\"\n      ],\n      \"stakeholder_views\": [\n        \"Product Owners: Hold accountability for final release.\",\n        \"DevOps: Require clear handoff conditions.\",\n        \"Support: Rely on productionized, validated deliverables.\"\n      ],\n      \"risk_landscape\": [\n        \"Premature release undermines trust and may produce cascading failures in chained systems.\",\n        \"Edge Case: Tight deadlines create pressure to skip final validation.\"\n      ]\n    }\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Essence Distiller\n\nYour goal is not to **praise** or **answer** the submission, but to **interrogate and redirect** it—exposing every structural flaw, quantifying compliance with the Universal Directive System, and issuing a minimal replacement instruction that corrects all defects. Execute as:\n\n`{role=essence_distiller; input=[atomic_tasks:list, task_perspectives:list, execution_blockers:array]; process=[rank_tasks_by_transformational_weight(), select_high_impact_task(), compile_supporting_rationale(), justify_selection_transparently()]; constraints=[no_language_rewrite_of_selected_task(), transparent_ranking()]; requirements=[clear_primary_directive(), rationale_precision()]; output={primary_directive:str, supporting_rationale:list}}`",
      "step": "e",
      "title": "Essence Distiller",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"# Instruction Patterns: Generalized AI Template Design\\n\\n## Core Philosophy\\n\\nThis document contains the complete methodology for creating **highly effective and generalized AI instruction templates**. These patterns embody fundamental principles that enable any LLM to understand and replicate the system's design approach.\\n\\n## The Three-Part Universal Structure\\n\\nEvery effective AI instruction follows this exact pattern:\\n\\n```\\n[Title] Interpretation Execute as: `{Transformation}`\\n```\\n\\n### 1. [Title] - Purpose Declaration\\n- **Format**: Enclosed in square brackets `[Title]`\\n- **Function**: Concise, action-oriented description of the template's purpose\\n- **Style**: Title case, descriptive, no generic terms\\n- **Examples**: `[Essence Distillation]`, `[Code Optimizer]`, `[Content Analyzer]`\\n\\n### 2. Interpretation - Human-Readable Instructions\\n- **Goal Negation Pattern**: MUST begin with `\\\"Your goal is not to **[action]**, but to **[transformation]**\\\"`\\n- **Command Voice**: No first-person references, no conversational language\\n- **Clarity**: Explains the transformation in natural language\\n- **Ending**: MUST end with \\\"Execute as:\\\" leading to transformation block\\n\\n### 3. `{Transformation}` - Machine-Parsable Parameters\\n- **Format**: JSON-like structure in backticks with curly braces\\n- **Components**: role, input, process, constraints, requirements, output\\n- **Type Safety**: All parameters must specify data types\\n- **Actionability**: All process steps must be executable functions\\n\\n## Transformation Block Specification\\n\\n```\\n{\\n  role=<specific_role_name>;\\n  input=[<parameter_name>:<data_type>];\\n  process=[<step1>(), <step2>(), <step3>()];\\n  constraints=[<limitation1>(), <limitation2>()];\\n  requirements=[<requirement1>(), <requirement2>()];\\n  output={<result_name>:<data_type>}\\n}\\n```\\n\\n### Component Rules\\n\\n**role** (Required)\\n- Must be specific and descriptive (no \\\"assistant\\\" or \\\"helper\\\")\\n- Use underscore_case for multi-word roles\\n- Examples: `essence_distiller`, `code_optimizer`, `content_analyst`\\n\\n**input** (Required)\\n- Format: `[parameter_name:data_type]`\\n- Support multiple parameters: `[text:str, language:str]`\\n- Use descriptive names: `[original:any]`, `[source_code:str]`\\n\\n**process** (Required)\\n- Function-like notation with parentheses: `identify_core_intent()`\\n- Actionable, atomic steps in logical sequence\\n- Verb-based names describing specific operations\\n\\n**constraints** (Optional)\\n- Operational boundaries and limitations\\n- Format restrictions and scope definitions\\n- Examples: `preserve_original_meaning()`, `maintain_technical_accuracy()`\\n\\n**requirements** (Optional)\\n- Mandatory output characteristics and quality standards\\n- Validation criteria and format specifications\\n- Examples: `structured_output()`, `comprehensive_coverage()`\\n\\n**output** (Required)\\n- Format: `{parameter_name:data_type}`\\n- Descriptive names with type specification\\n- Support complex structures: `{analysis:dict, improvements:list}`\\n\\n## Directional Transformation Patterns\\n\\n### Core Principle\\nFocus on **how to transform** rather than **what to transform**. Directional vectors are universal and context-independent.\\n\\n### Universal Vector Categories\\n\\n**Intensity Vectors**\\n- `amplify`: Intensify inherent qualities through magnification\\n- `intensify`: Compress to maximum density and focus\\n- `diminish`: Reduce intensity while preserving form\\n\\n**Clarity Vectors**\\n- `clarify`: Enhance transparency and definition\\n- `purify`: Remove non-essential elements\\n- `obscure`: Add complexity layers and indirection\\n\\n**Structural Vectors**\\n- `expand`: Extend natural boundaries dimensionally\\n- `compress`: Maximize density without information loss\\n- `restructure`: Transform fundamental organization pattern\\n\\n**Transformation Vectors**\\n- `elevate`: Transform to higher operational level\\n- `distill`: Extract absolute essence through pure extraction\\n- `synthesize`: Create unified emergent form\\n\\n**Meta Vectors**\\n- `abstract`: Extract to pure conceptual form\\n- `concretize`: Translate abstract elements to tangible form\\n- `transcend`: Operate beyond current dimensional limitations\\n\\n### Vector Template Pattern\\n\\n```\\n[Vector] Your goal is not to **analyze** the input, but to **[vector]** it through pure directional transformation. Execute as: `{role=[vector]_operator; input=[content:any]; process=[identify_[vector]_potential(), apply_[vector]_transformation(), preserve_essential_properties(), validate_[vector]_completion()]; constraints=[preserve_fundamental_nature(), maintain_structural_integrity()]; output={[vector]ed:any}}`\\n```\\n\\n## Goal Negation Pattern\\n\\n### Purpose\\nEliminates ambiguity by explicitly stating what NOT to do, then clearly defining the intended transformation.\\n\\n### Structure\\n```\\nYour goal is not to **[common_misinterpretation]**, but to **[precise_transformation]**\\n```\\n\\n### Examples\\n- `Your goal is not to **analyze** the input, but to **amplify** its inherent qualities`\\n- `Your goal is not to **summarize** the content, but to **distill** its absolute essence`\\n- `Your goal is not to **describe** the code, but to **optimize** it for performance`\\n\\n## Forbidden Language Patterns\\n\\n### Conversational Elements\\n- First-person: *I, me, my, we, us*\\n- Politeness: *please, thank you, let's*\\n- Uncertainty: *maybe, perhaps, might, could*\\n- Questions in directives\\n- Meta-commentary or explanations\\n\\n### Structural Violations\\n- Merging or omitting required sections\\n- Untyped parameters or outputs\\n- Generic roles like \\\"assistant\\\"\\n- Vague process descriptions\\n- Missing goal negation pattern\\n\\n## Sequence Composition Patterns\\n\\n### Linear Sequences\\n```\\nStep A → Step B → Step C\\n1031-a-form_classifier → 1031-b-essence_extractor → 1031-c-structure_optimizer\\n```\\n\\n### Parallel Processing\\n```\\nInput → [Template A | Template B | Template C] → Synthesis\\n```\\n\\n### Chain Mode\\n```\\nOriginal Input → Template A → Output A becomes Input B → Template B → Final Output\\n```\\n\\n### Meta-Application\\n```\\nabstract → [any_vector] → concretize\\n```\\n\\n## Template Naming Convention\\n\\n### Format\\n```\\n<sequence_id>-<step>-<descriptive_name>.md\\n```\\n\\n### Components\\n- **sequence_id**: Four-digit number (0001, 0002, 1031)\\n- **step**: Single lowercase letter (a, b, c) for multi-step sequences\\n- **descriptive_name**: Hyphenated lowercase words describing function\\n\\n### Examples\\n- `1031-a-form-classifier.md`\\n- `9000-a-amplify.md`\\n- `0001-instruction-converter.md`\\n\\n## Quality Validation Checklist\\n\\n- [ ] Three-part structure intact\\n- [ ] Goal negation present and properly formatted\\n- [ ] Role is specific and non-generic\\n- [ ] Input parameters are typed\\n- [ ] Process steps are ordered and actionable\\n- [ ] Output format is typed and structured\\n- [ ] No forbidden language patterns\\n- [ ] File naming convention followed\\n- [ ] Template serves clear, specific purpose\\n\\n## Universal Applicability\\n\\nThese patterns work across:\\n- **Content Types**: Text, code, data, concepts, problems, solutions\\n- **Domains**: Technical, creative, business, academic\\n- **Languages**: Natural languages, programming languages, formal languages\\n- **Contexts**: Any domain without specialized knowledge requirements\\n\\n## Implementation Philosophy\\n\\n1. **Clarity First**: Template purpose immediately clear\\n2. **Atomic Operations**: Each template performs one specific transformation\\n3. **Composability**: Templates work well in sequences\\n4. **Type Safety**: Always specify data types\\n5. **Validation**: Include validation steps in processes\\n6. **Consistency**: Same patterns produce predictable results\\n7. **Universality**: Design for maximum generalization\\n\\n## Advanced Patterns\\n\\n### Recursive Application\\n```\\n[Self-Amplify] Apply amplification vector to its own amplification process\\n[Meta-Distill] Distill the distillation process itself\\n[Transcendent-Clarify] Clarify beyond normal clarity boundaries\\n```\\n\\n### Vector Algebra\\n```\\namplify + clarify = enhanced_clarity\\ncompress + distill = essential_core\\nexpand + abstract = universal_pattern\\nelevate + synthesize = emergent_transcendence\\n```\\n\\n### Inverse Operations\\n```\\namplify ↔ diminish\\nexpand ↔ compress\\nclarify ↔ obscure\\nabstract ↔ concretize\\n```\\n\\n### Intensity Scaling\\n```\\n# Light application\\nprocess=[gentle_[vector](), preserve_majority_original()]\\n\\n# Standard application\\nprocess=[apply_[vector](), balance_transformation()]\\n\\n# Maximum application\\nprocess=[maximum_[vector](), complete_transformation()]\\n```\\n\\n## Key Principles for LLM Replication\\n\\n1. **Vector Independence**: Transformation vectors operate independently of content, context, or domain\\n2. **Essence Preservation**: Core identity remains intact through transformation\\n3. **Composability**: Vectors can be chained and combined without loss of integrity\\n4. **Type Consistency**: `any → any` maintains input/output type compatibility\\n5. **Context-Free Operation**: No domain knowledge or content analysis required\\n6. **Operational Consistency**: Same vector produces consistent transformation patterns\\n\\nThis methodology enables creation of instruction templates that are maximally effective, universally applicable, and consistently reliable across any AI system or use case.\\\"```'\\n\\n\",\n  \"task_perspectives\": [\n    {\n      \"task\": \"Define template title using specific, action-oriented, non-generic terms in title case within square brackets.\",\n      \"domain_signals\": [\n        \"Naming conventions in information architecture emphasize clarity and immediate purpose.\",\n        \"Effective titles reduce ambiguity for technical and non-technical stakeholders.\",\n        \"In branding and legal compliance domains, specificity mitigates misrepresentation and aids discoverability.\"\n      ],\n      \"stakeholder_views\": [\n        \"Technical Writers: Seek clarity, so the template’s intent is transparent from the start.\",\n        \"End Users: Quickly assess template applicability by title alone.\",\n        \"Governance/Compliance Teams: Require precise naming for audits and policy adherence.\"\n      ],\n      \"risk_landscape\": [\n        \"Overly generic or ambiguous titles may cause improper usage or misalignment with enterprise libraries.\",\n        \"Domain confusion may occur if title terms overlap in context-specific meanings (e.g., 'optimize' in finance vs. code domains).\",\n        \"Edge Case: Multiple templates with nearly identical intent but subtle context differences in proprietary workflows.\"\n      ]\n    },\n    {\n      \"task\": \"Compose an interpretation section beginning with goal-negation phrasing to specify what is NOT the goal.\",\n      \"domain_signals\": [\n        \"Cognitive linguistics studies show explicit negation improves task constraint awareness.\",\n        \"Medical checklists and aviation protocols use negation to prevent critical missteps.\",\n        \"In pedagogy, goal-negation reduces misconception formation.\"\n      ],\n      \"stakeholder_views\": [\n        \"Trainers: Favor negative scaffolding to teach subtle distinctions.\",\n        \"Quality Assurance: Ensure disambiguation between similar process steps.\",\n        \"Legal: Negation clarifies non-liability boundaries.\"\n      ],\n      \"risk_landscape\": [\n        \"Ambiguously worded negation can confuse users, leading to incorrect applications.\",\n        \"Edge Case: In high-context cultures or with language learners, negation structure may be misinterpreted as objective reversal.\"\n      ]\n    },\n    {\n      \"task\": \"State the intended transformation in declarative command form within the interpretation section.\",\n      \"domain_signals\": [\n        \"Systems engineering and instructional design both benefit from explicit command-oriented guidance.\",\n        \"Declarative instruction is standard in industrial operating procedures for automation.\",\n        \"Software security patterns utilize mandate language to reduce ambiguity.\"\n      ],\n      \"stakeholder_views\": [\n        \"Automation Designers: Need machine-actionable, unambiguous intent extraction.\",\n        \"Instructional Designers: Require clarity for learning transfer.\",\n        \"Auditors: Value traceable, directive logic for compliance purposes.\"\n      ],\n      \"risk_landscape\": [\n        \"Passive or omitted transformation results in drift from intended outcome.\",\n        \"Edge Case: Templaters using indirect languages, e.g., those that prefer conditional over command mode, may find integration challenging.\"\n      ]\n    },\n    {\n      \"task\": \"Conclude interpretation section with 'Execute as:'.\",\n      \"domain_signals\": [\n        \"Software workflow engines require unambiguous phase terminators.\",\n        \"User interface design patterns recommend explicit action triggers.\",\n        \"Linguistics notes marker phrases reduce cognitive load in transitions.\"\n      ],\n      \"stakeholder_views\": [\n        \"Process Engineers: Value explicit state change demarcation.\",\n        \"New Users: Require clear signposting of next action.\",\n        \"Cross-lingual Implementers: Depend on consistent cues for automated localization.\"\n      ],\n      \"risk_landscape\": [\n        \"Absence of this phrase may lead to parsing failures or missing automation handovers.\",\n        \"Edge Case: In voice-operated or low-visibility contexts, reliance on this marker alone may cause issues.\"\n      ]\n    },\n    {\n      \"task\": \"Structure a transformation block in JSON-like syntax including explicit, descriptive role assignment in underscore_case.\",\n      \"domain_signals\": [\n        \"Enterprise APIs and semantic web standards demand structured, typed exchanges.\",\n        \"Explicit role naming associates with zero-trust security models (least privilege).\",\n        \"Consistency in codebases is critical for maintainability and cross-system interoperability.\"\n      ],\n      \"stakeholder_views\": [\n        \"Developers: Depend on strict schema adherence.\",\n        \"Integration Architects: Require predictable role patterns for orchestration.\",\n        \"Documentation Teams: Demand self-explanatory structure.\"\n      ],\n      \"risk_landscape\": [\n        \"Role ambiguity can permit privilege escalation or execution of unintended actions.\",\n        \"Edge Case: Interfacing with legacy or third-party systems expecting alternative role naming conventions.\"\n      ]\n    },\n    {\n      \"task\": \"Specify input parameter list with strict data type for each parameter within the transformation block.\",\n      \"domain_signals\": [\n        \"Database design and strongly-typed languages exhibit higher reliability with early typing.\",\n        \"Data governance policies require explicit input controls.\",\n        \"Bioinformatics and manufacturing systems leverage input typing to reduce risk of process errors.\"\n      ],\n      \"stakeholder_views\": [\n        \"Quality Engineers: Warn against type-unsafe pattern propagation.\",\n        \"System Integrators: Favor input typification for validation pipelines.\",\n        \"Non-technical Stakeholders: Benefit from less ambiguity via explicit input descriptions.\"\n      ],\n      \"risk_landscape\": [\n        \"Untyped inputs can introduce hidden faults, security vulnerabilities, or integration failures.\",\n        \"Edge Case: Flexible or polymorphic input interfaces in highly dynamic workflows may require more complex validation.\"\n      ]\n    },\n    {\n      \"task\": \"List ordered, imperative process steps (function-like notation) that are atomic and actionable within the transformation block.\",\n      \"domain_signals\": [\n        \"Software build pipelines and robotics require strict, stepwise execution.\",\n        \"Process mining research shows atomicity supports error isolation.\",\n        \"AI explainability frameworks prefer visible, auditable transformation chains.\"\n      ],\n      \"stakeholder_views\": [\n        \"DevOps: Ensure pipeline reliability through clarity of logical execution.\",\n        \"Auditors/GRC: Demand traceability of each process step.\",\n        \"Process Owners: Value modularity for reuse.\"\n      ],\n      \"risk_landscape\": [\n        \"Non-atomic or unordered steps create errors or complicate troubleshooting.\",\n        \"Edge Case: Race conditions if steps have hidden dependencies.\"\n      ]\n    },\n    {\n      \"task\": \"Add clearly stated and typed output specification within the transformation block.\",\n      \"domain_signals\": [\n        \"Formal specification methodologies in safety-critical systems enforce output typing.\",\n        \"Legal discovery and e-discovery demand explicit result forms.\",\n        \"Interface contracts between teams/systems rely on declared outputs.\"\n      ],\n      \"stakeholder_views\": [\n        \"Testers: Create validation scripts based on expected output types.\",\n        \"Customers: Expect reliable, predictable deliverables.\",\n        \"Compliance: Track obligations through output traceability.\"\n      ],\n      \"risk_landscape\": [\n        \"Mistyped or missing output definitions propagate downstream failures.\",\n        \"Edge Case: Output types requiring rapid evolution (hotfixes) risk contract breakage.\"\n      ]\n    },\n    {\n      \"task\": \"Optionally add constraints to the transformation block to impose operational, structural, or format boundaries as needed.\",\n      \"domain_signals\": [\n        \"Security policy modeling and regulatory protocols use constraints as enforcement anchors.\",\n        \"Data flow diagrams highlight constraint-based error handling.\",\n        \"Science experiment design uses constraint articulation to delimit scope.\"\n      ],\n      \"stakeholder_views\": [\n        \"Security: Relies on constraint clarity to set operational boundaries.\",\n        \"Business Owners: Use constraints to align deliverables with policy.\",\n        \"Engineers: Avoid accidental overreach or mission creep.\"\n      ],\n      \"risk_landscape\": [\n        \"Missing constraints may result in accidental policy breach or unintended data exposure.\",\n        \"Edge Case: Constraints misaligned with evolving business rules create technical debt.\"\n      ]\n    },\n    {\n      \"task\": \"Optionally embed requirements specifying mandatory quality standards, output structure, and validation criteria within the transformation block.\",\n      \"domain_signals\": [\n        \"ISO and other standards bodies specify requirements sections for auditability.\",\n        \"Software design patterns with validation rules reduce post-hoc correction cost.\",\n        \"Product management often encodes 'definition of done' as requirements.\"\n      ],\n      \"stakeholder_views\": [\n        \"QA/Testing: Requirements rationalize the scope and define completion.\",\n        \"Regulators: Check conformity with mandatory standards.\",\n        \"Support Teams: Use requirements for troubleshooting escalation matrices.\"\n      ],\n      \"risk_landscape\": [\n        \"Unspecified requirements increase risk of low-quality or unfit outputs.\",\n        \"Edge Case: Over-specified requirements hinder adaptation for new contexts.\"\n      ]\n    },\n    {\n      \"task\": \"Select appropriate directional transformation vectors according to the intended operational effect.\",\n      \"domain_signals\": [\n        \"AI kernel design and vector semantics assert separation of intent from realization.\",\n        \"Business process management leverages transformation types for agility.\",\n        \"Creative industries map directionality for generative outputs.\"\n      ],\n      \"stakeholder_views\": [\n        \"AI Researchers: Require careful vector selection for interpretability.\",\n        \"Ops/Business Analysts: Use vectors to structure process improvement.\",\n        \"Educators: Teach learners by categorizing transformation direction.\"\n      ],\n      \"risk_landscape\": [\n        \"Incorrect vector choice distorts transformation and output intent.\",\n        \"Edge Case: In hybrid contexts, multiple valid vectors create ambiguity.\"\n      ]\n    },\n    {\n      \"task\": \"Apply the selected transformation vector pattern as specified by methodology.\",\n      \"domain_signals\": [\n        \"Model-driven architecture enforces pattern application for consistency.\",\n        \"Manufacturing quality systems demand compliance with defined transformation patterns.\",\n        \"Cognitive science demonstrates that pattern-based application reduces error rates.\"\n      ],\n      \"stakeholder_views\": [\n        \"Process Automation: Needs strict adherence for repeatable results.\",\n        \"Change Management: Ensures all parties align on how transformations are performed.\",\n        \"Documentation: Captures repeatable procedures for training.\"\n      ],\n      \"risk_landscape\": [\n        \"Partial or failed pattern application leads to inconsistent or unreliable outputs.\",\n        \"Edge Case: Custom, ad hoc deviations to patterns needed for rare one-off contexts.\"\n      ]\n    },\n    {\n      \"task\": \"Sequence all process steps in strict logical order, retaining explicit dependencies and avoiding passive construction.\",\n      \"domain_signals\": [\n        \"Critical path methodologies in project management depend on sequential integrity.\",\n        \"Compiler design enforces ordering for dependency resolution.\",\n        \"Manufacturing assembly lines model explicit stepwise flow.\"\n      ],\n      \"stakeholder_views\": [\n        \"Project Managers: Track dependency-driven milestones.\",\n        \"Software Build Engineers: Avoid deadlocks and inefficiencies.\",\n        \"Operations: Use step order for risk mitigation and monitoring.\"\n      ],\n      \"risk_landscape\": [\n        \"Broken sequencing results in failures, rollback costs, or latent defects.\",\n        \"Edge Case: Cyclic dependencies in non-linear workflows.\"\n      ]\n    },\n    {\n      \"task\": \"Name the resulting template file according to prescribed format: four-digit sequence ID, step letter if multi-step, and dash-separated descriptive name.\",\n      \"domain_signals\": [\n        \"Version control systems and knowledge management platforms organize via naming conventions.\",\n        \"Legal case management uses file schema for evidence traceability.\",\n        \"Taxonomy management in libraries and databases emphasizes file discoverability.\"\n      ],\n      \"stakeholder_views\": [\n        \"Archivists: Ensure discoverability and non-collision.\",\n        \"Developers: Need machine-friendly, scriptable naming.\",\n        \"Auditors: Require traceable provenance.\"\n      ],\n      \"risk_landscape\": [\n        \"Improper naming leads to file duplication, loss, or ambiguity in audits.\",\n        \"Edge Case: Platform restrictions on characters or name length.\"\n      ]\n    },\n    {\n      \"task\": \"Review the template for forbidden language patterns (conversational, ambiguous, or untyped content) and remove any violations.\",\n      \"domain_signals\": [\n        \"Governance in regulated industries enforces 'plain language' and compliance constraints.\",\n        \"Translation/localization workflows demand clear rule-based input.\",\n        \"Software linters and code review tools flag style and forbidden constructs.\"\n      ],\n      \"stakeholder_views\": [\n        \"Compliance Officers: Ensure no policy-violating language slips through.\",\n        \"Internationalization Teams: Need modular templates for multi-lingual reuse.\",\n        \"End Users: Value predictability and professionalism.\"\n      ],\n      \"risk_landscape\": [\n        \"Latent forbidden language can trigger regulatory violations or reduce trust.\",\n        \"Edge Case: Undetected subtle violations in cross-lingual/cross-dialect contexts.\"\n      ]\n    },\n    {\n      \"task\": \"Verify the presence and correct formatting of all mandatory components: three-part structure, goal-negation phrasing, specific non-generic roles, typed and actionable process steps, explicit output typing, and adherence to naming conventions.\",\n      \"domain_signals\": [\n        \"DevOps, regulatory inspection, and quality assurance always demand post-creation validation.\",\n        \"Education assessment rubrics reflect on presence/quality of all necessary elements.\",\n        \"ISO audits mandate full compliance review.\"\n      ],\n      \"stakeholder_views\": [\n        \"Template Designers: Use checklists for standardization.\",\n        \"QA Assurance: Blocks release of incomplete artefacts.\",\n        \"Management: Risk management through pre-release gates.\"\n      ],\n      \"risk_landscape\": [\n        \"Misformatted or missing components result in failed automation, rework, or compliance issues.\",\n        \"Edge Case: Format evolves and validation scripts lag behind.\"\n      ]\n    },\n    {\n      \"task\": \"Ensure template is designed for universal application across domains, languages, and content types without reliance on specialized knowledge.\",\n      \"domain_signals\": [\n        \"Universal design principles in product development reduce maintenance overhead.\",\n        \"Cross-domain ontology building in data science favors generality.\",\n        \"Globalization teams advocate for context-neutral assets.\"\n      ],\n      \"stakeholder_views\": [\n        \"Platform Teams: Lower support cost with highly general templates.\",\n        \"Vendors/Partners: Prefer plug-and-play artifacts.\",\n        \"Diversity/Equity Advocates: See universal design as inclusivity best practice.\"\n      ],\n      \"risk_landscape\": [\n        \"Domain-coupled designs introduce high maintenance burden or preclude interoperability.\",\n        \"Edge Case: Overgeneralization may reduce fit for critical, high-context tasks.\"\n      ]\n    },\n    {\n      \"task\": \"Design for advanced composition (recursive application, vector combinations, intensity scaling) if required by methodology or use case.\",\n      \"domain_signals\": [\n        \"Advanced ML pipeline orchestration permits recursion/branching.\",\n        \"Business process design increasingly combines atomic patterns for scale.\",\n        \"Systems theory advocates composability for emergent properties.\"\n      ],\n      \"stakeholder_views\": [\n        \"Lead Architects: Build future-proof patterns with compositional flexibility.\",\n        \"Research Scientists: Favor experimentation with advanced structures.\",\n        \"Chief Innovation Officers: Value extensibility for unanticipated needs.\"\n      ],\n      \"risk_landscape\": [\n        \"Failure to support advanced composition limits future evolution.\",\n        \"Edge Case: Recursive complexity may cause performance or interpretability issues.\"\n      ]\n    },\n    {\n      \"task\": \"Block progression to template finalization if any structural, language, typing, or sequencing violations remain (Blocker Flag).\",\n      \"domain_signals\": [\n        \"Quality gates in CI/CD pipelines enforce blocking conditions.\",\n        \"Mission-critical systems deploy fail-fast logic to avoid propagating errors.\",\n        \"Legal sign-off and operational readiness reviews rely on unambiguous block/release status.\"\n      ],\n      \"stakeholder_views\": [\n        \"Release Managers: Mandate no-go decisions on blocker detection.\",\n        \"Risk Officers: Require escalation steps for unresolved violations.\",\n        \"Operational Leads: Depend on status signals for deployment planning.\"\n      ],\n      \"risk_landscape\": [\n        \"Bypassing blockers introduces substantial future technical/regulatory debt.\",\n        \"Edge Case: False positives could unnecessarily halt progress.\"\n      ]\n    },\n    {\n      \"task\": \"Release template for implementation or chaining only after all tasks fully satisfy methodology's quality control checklist.\",\n      \"domain_signals\": [\n        \"Agile and waterfall environments both require post-quality review release criteria.\",\n        \"Chain of custody in digital forensics mandates all standards met before release.\",\n        \"Instructional design uses summative assessment prior to deployment.\"\n      ],\n      \"stakeholder_views\": [\n        \"Product Owners: Hold accountability for final release.\",\n        \"DevOps: Require clear handoff conditions.\",\n        \"Support: Rely on productionized, validated deliverables.\"\n      ],\n      \"risk_landscape\": [\n        \"Premature release undermines trust and may produce cascading failures in chained systems.\",\n        \"Edge Case: Tight deadlines create pressure to skip final validation.\"\n      ]\n    }\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"primary_directive\": \"Prioritize absolute, end-to-end structural validation—verify that every mandatory template component is fully present, unambiguously formatted, and adheres to prescribed patterns before permitting progression to implementation or release.\",\n  \"supporting_rationale\": [\n    \"Transformational Weight Ranking (descending):\",\n    \"1. Verify the presence and correct formatting of all mandatory components: three-part structure, goal-negation phrasing, specific non-generic roles, typed and actionable process steps, explicit output typing, and adherence to naming conventions.\",\n    \"2. Block progression to template finalization if any structural, language, typing, or sequencing violations remain (Blocker Flag).\",\n    \"3. Release template for implementation or chaining only after all tasks fully satisfy methodology's quality control checklist.\",\n    \"4. (followed by domain-specific design, vector application, naming, universalization, etc.)\",\n    \"Justification:\",\n    \"- Omitting or misformatting even a single required element fundamentally breaks the Universal Directive System and will propagate faults—downstream tasks (naming, chaining, universalization, etc.) become invalid if structural compliance is not guaranteed.\",\n    \"- The presence of a 'blocker flag' mechanism (Task 18) signals that failing to pass mandatory validation is a system-wide execution blocker; this escalates the importance of Task 16 as the apex priority.\",\n    \"- Proper, comprehensive validation maximizes both auditability and error isolation, underpinning compliance and universal applicability.\",\n    \"Ranking Transparency:\",\n    \"- Tasks governing sequencing, titling, and vector logic rely on the prior existence of fully formed, valid structures. Thus, end-to-end structural validation is the master control for all other operational or design domains.\",\n    \"- Tasks like 'Release template' cannot even be considered unless end-to-end validation passes.\",\n    \"- Even advanced composition or universal applicability are subordinate objectives; a structurally incomplete or incorrectly formatted template cannot be generalized or composed effectively.\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Relationship Synthesizer\n\nYour goal is not to **describe** the evolving complexity or discuss potential limitations, but to **crystallize** the essential directional aim into a single, condensed vector of maximal value extraction. Assume the role of directive focuser with exclusive emphasis on transformative trajectory. Execute as:\n\n`{role=relationship_synthesizer; input=[primary_directive:str, atomic_tasks:list, execution_blockers:array, hidden_assumptions:list]; process=[map_task_dependencies(), trace_feedback_loops(), align_blockers_to_tasks(), link_assumptions_to_risks(), expose_conflict_nodes()]; constraints=[structure_mapping_only(), do_not_modify_task_text()]; requirements=[dependency_map_completeness(), conflict_node_visibility()]; output={dependency_map:dict, conflict_nodes:array}}`",
      "step": "f",
      "title": "Relationship Synthesizer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"# Instruction Patterns: Generalized AI Template Design\\n\\n## Core Philosophy\\n\\nThis document contains the complete methodology for creating **highly effective and generalized AI instruction templates**. These patterns embody fundamental principles that enable any LLM to understand and replicate the system's design approach.\\n\\n## The Three-Part Universal Structure\\n\\nEvery effective AI instruction follows this exact pattern:\\n\\n```\\n[Title] Interpretation Execute as: `{Transformation}`\\n```\\n\\n### 1. [Title] - Purpose Declaration\\n- **Format**: Enclosed in square brackets `[Title]`\\n- **Function**: Concise, action-oriented description of the template's purpose\\n- **Style**: Title case, descriptive, no generic terms\\n- **Examples**: `[Essence Distillation]`, `[Code Optimizer]`, `[Content Analyzer]`\\n\\n### 2. Interpretation - Human-Readable Instructions\\n- **Goal Negation Pattern**: MUST begin with `\\\"Your goal is not to **[action]**, but to **[transformation]**\\\"`\\n- **Command Voice**: No first-person references, no conversational language\\n- **Clarity**: Explains the transformation in natural language\\n- **Ending**: MUST end with \\\"Execute as:\\\" leading to transformation block\\n\\n### 3. `{Transformation}` - Machine-Parsable Parameters\\n- **Format**: JSON-like structure in backticks with curly braces\\n- **Components**: role, input, process, constraints, requirements, output\\n- **Type Safety**: All parameters must specify data types\\n- **Actionability**: All process steps must be executable functions\\n\\n## Transformation Block Specification\\n\\n```\\n{\\n  role=<specific_role_name>;\\n  input=[<parameter_name>:<data_type>];\\n  process=[<step1>(), <step2>(), <step3>()];\\n  constraints=[<limitation1>(), <limitation2>()];\\n  requirements=[<requirement1>(), <requirement2>()];\\n  output={<result_name>:<data_type>}\\n}\\n```\\n\\n### Component Rules\\n\\n**role** (Required)\\n- Must be specific and descriptive (no \\\"assistant\\\" or \\\"helper\\\")\\n- Use underscore_case for multi-word roles\\n- Examples: `essence_distiller`, `code_optimizer`, `content_analyst`\\n\\n**input** (Required)\\n- Format: `[parameter_name:data_type]`\\n- Support multiple parameters: `[text:str, language:str]`\\n- Use descriptive names: `[original:any]`, `[source_code:str]`\\n\\n**process** (Required)\\n- Function-like notation with parentheses: `identify_core_intent()`\\n- Actionable, atomic steps in logical sequence\\n- Verb-based names describing specific operations\\n\\n**constraints** (Optional)\\n- Operational boundaries and limitations\\n- Format restrictions and scope definitions\\n- Examples: `preserve_original_meaning()`, `maintain_technical_accuracy()`\\n\\n**requirements** (Optional)\\n- Mandatory output characteristics and quality standards\\n- Validation criteria and format specifications\\n- Examples: `structured_output()`, `comprehensive_coverage()`\\n\\n**output** (Required)\\n- Format: `{parameter_name:data_type}`\\n- Descriptive names with type specification\\n- Support complex structures: `{analysis:dict, improvements:list}`\\n\\n## Directional Transformation Patterns\\n\\n### Core Principle\\nFocus on **how to transform** rather than **what to transform**. Directional vectors are universal and context-independent.\\n\\n### Universal Vector Categories\\n\\n**Intensity Vectors**\\n- `amplify`: Intensify inherent qualities through magnification\\n- `intensify`: Compress to maximum density and focus\\n- `diminish`: Reduce intensity while preserving form\\n\\n**Clarity Vectors**\\n- `clarify`: Enhance transparency and definition\\n- `purify`: Remove non-essential elements\\n- `obscure`: Add complexity layers and indirection\\n\\n**Structural Vectors**\\n- `expand`: Extend natural boundaries dimensionally\\n- `compress`: Maximize density without information loss\\n- `restructure`: Transform fundamental organization pattern\\n\\n**Transformation Vectors**\\n- `elevate`: Transform to higher operational level\\n- `distill`: Extract absolute essence through pure extraction\\n- `synthesize`: Create unified emergent form\\n\\n**Meta Vectors**\\n- `abstract`: Extract to pure conceptual form\\n- `concretize`: Translate abstract elements to tangible form\\n- `transcend`: Operate beyond current dimensional limitations\\n\\n### Vector Template Pattern\\n\\n```\\n[Vector] Your goal is not to **analyze** the input, but to **[vector]** it through pure directional transformation. Execute as: `{role=[vector]_operator; input=[content:any]; process=[identify_[vector]_potential(), apply_[vector]_transformation(), preserve_essential_properties(), validate_[vector]_completion()]; constraints=[preserve_fundamental_nature(), maintain_structural_integrity()]; output={[vector]ed:any}}`\\n```\\n\\n## Goal Negation Pattern\\n\\n### Purpose\\nEliminates ambiguity by explicitly stating what NOT to do, then clearly defining the intended transformation.\\n\\n### Structure\\n```\\nYour goal is not to **[common_misinterpretation]**, but to **[precise_transformation]**\\n```\\n\\n### Examples\\n- `Your goal is not to **analyze** the input, but to **amplify** its inherent qualities`\\n- `Your goal is not to **summarize** the content, but to **distill** its absolute essence`\\n- `Your goal is not to **describe** the code, but to **optimize** it for performance`\\n\\n## Forbidden Language Patterns\\n\\n### Conversational Elements\\n- First-person: *I, me, my, we, us*\\n- Politeness: *please, thank you, let's*\\n- Uncertainty: *maybe, perhaps, might, could*\\n- Questions in directives\\n- Meta-commentary or explanations\\n\\n### Structural Violations\\n- Merging or omitting required sections\\n- Untyped parameters or outputs\\n- Generic roles like \\\"assistant\\\"\\n- Vague process descriptions\\n- Missing goal negation pattern\\n\\n## Sequence Composition Patterns\\n\\n### Linear Sequences\\n```\\nStep A → Step B → Step C\\n1031-a-form_classifier → 1031-b-essence_extractor → 1031-c-structure_optimizer\\n```\\n\\n### Parallel Processing\\n```\\nInput → [Template A | Template B | Template C] → Synthesis\\n```\\n\\n### Chain Mode\\n```\\nOriginal Input → Template A → Output A becomes Input B → Template B → Final Output\\n```\\n\\n### Meta-Application\\n```\\nabstract → [any_vector] → concretize\\n```\\n\\n## Template Naming Convention\\n\\n### Format\\n```\\n<sequence_id>-<step>-<descriptive_name>.md\\n```\\n\\n### Components\\n- **sequence_id**: Four-digit number (0001, 0002, 1031)\\n- **step**: Single lowercase letter (a, b, c) for multi-step sequences\\n- **descriptive_name**: Hyphenated lowercase words describing function\\n\\n### Examples\\n- `1031-a-form-classifier.md`\\n- `9000-a-amplify.md`\\n- `0001-instruction-converter.md`\\n\\n## Quality Validation Checklist\\n\\n- [ ] Three-part structure intact\\n- [ ] Goal negation present and properly formatted\\n- [ ] Role is specific and non-generic\\n- [ ] Input parameters are typed\\n- [ ] Process steps are ordered and actionable\\n- [ ] Output format is typed and structured\\n- [ ] No forbidden language patterns\\n- [ ] File naming convention followed\\n- [ ] Template serves clear, specific purpose\\n\\n## Universal Applicability\\n\\nThese patterns work across:\\n- **Content Types**: Text, code, data, concepts, problems, solutions\\n- **Domains**: Technical, creative, business, academic\\n- **Languages**: Natural languages, programming languages, formal languages\\n- **Contexts**: Any domain without specialized knowledge requirements\\n\\n## Implementation Philosophy\\n\\n1. **Clarity First**: Template purpose immediately clear\\n2. **Atomic Operations**: Each template performs one specific transformation\\n3. **Composability**: Templates work well in sequences\\n4. **Type Safety**: Always specify data types\\n5. **Validation**: Include validation steps in processes\\n6. **Consistency**: Same patterns produce predictable results\\n7. **Universality**: Design for maximum generalization\\n\\n## Advanced Patterns\\n\\n### Recursive Application\\n```\\n[Self-Amplify] Apply amplification vector to its own amplification process\\n[Meta-Distill] Distill the distillation process itself\\n[Transcendent-Clarify] Clarify beyond normal clarity boundaries\\n```\\n\\n### Vector Algebra\\n```\\namplify + clarify = enhanced_clarity\\ncompress + distill = essential_core\\nexpand + abstract = universal_pattern\\nelevate + synthesize = emergent_transcendence\\n```\\n\\n### Inverse Operations\\n```\\namplify ↔ diminish\\nexpand ↔ compress\\nclarify ↔ obscure\\nabstract ↔ concretize\\n```\\n\\n### Intensity Scaling\\n```\\n# Light application\\nprocess=[gentle_[vector](), preserve_majority_original()]\\n\\n# Standard application\\nprocess=[apply_[vector](), balance_transformation()]\\n\\n# Maximum application\\nprocess=[maximum_[vector](), complete_transformation()]\\n```\\n\\n## Key Principles for LLM Replication\\n\\n1. **Vector Independence**: Transformation vectors operate independently of content, context, or domain\\n2. **Essence Preservation**: Core identity remains intact through transformation\\n3. **Composability**: Vectors can be chained and combined without loss of integrity\\n4. **Type Consistency**: `any → any` maintains input/output type compatibility\\n5. **Context-Free Operation**: No domain knowledge or content analysis required\\n6. **Operational Consistency**: Same vector produces consistent transformation patterns\\n\\nThis methodology enables creation of instruction templates that are maximally effective, universally applicable, and consistently reliable across any AI system or use case.\\\"```'\\n\\n\",\n  \"primary_directive\": \"Prioritize absolute, end-to-end structural validation—verify that every mandatory template component is fully present, unambiguously formatted, and adheres to prescribed patterns before permitting progression to implementation or release.\",\n  \"supporting_rationale\": [\n    \"Transformational Weight Ranking (descending):\",\n    \"1. Verify the presence and correct formatting of all mandatory components: three-part structure, goal-negation phrasing, specific non-generic roles, typed and actionable process steps, explicit output typing, and adherence to naming conventions.\",\n    \"2. Block progression to template finalization if any structural, language, typing, or sequencing violations remain (Blocker Flag).\",\n    \"3. Release template for implementation or chaining only after all tasks fully satisfy methodology's quality control checklist.\",\n    \"4. (followed by domain-specific design, vector application, naming, universalization, etc.)\",\n    \"Justification:\",\n    \"- Omitting or misformatting even a single required element fundamentally breaks the Universal Directive System and will propagate faults—downstream tasks (naming, chaining, universalization, etc.) become invalid if structural compliance is not guaranteed.\",\n    \"- The presence of a 'blocker flag' mechanism (Task 18) signals that failing to pass mandatory validation is a system-wide execution blocker; this escalates the importance of Task 16 as the apex priority.\",\n    \"- Proper, comprehensive validation maximizes both auditability and error isolation, underpinning compliance and universal applicability.\",\n    \"Ranking Transparency:\",\n    \"- Tasks governing sequencing, titling, and vector logic rely on the prior existence of fully formed, valid structures. Thus, end-to-end structural validation is the master control for all other operational or design domains.\",\n    \"- Tasks like 'Release template' cannot even be considered unless end-to-end validation passes.\",\n    \"- Even advanced composition or universal applicability are subordinate objectives; a structurally incomplete or incorrectly formatted template cannot be generalized or composed effectively.\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"dependency_map\": {\n    \"primary_directive\": [\n      \"Task 16: Comprehensive Structural Validation\"\n    ],\n    \"atomic_tasks\": [\n      \"Task 1: Check three-part structure integrity\",\n      \"Task 2: Confirm presence and correctness of goal-negation phrasing\",\n      \"Task 3: Validate that role is specific and non-generic\",\n      \"Task 4: Check that all input parameters are present and strongly typed\",\n      \"Task 5: Ensure all process steps are actionable and formatted as function calls\",\n      \"Task 6: Confirm output field is present, well-typed, and structured\",\n      \"Task 7: Verify constraints and requirements follow formatting rules\",\n      \"Task 8: Audit for forbidden language patterns\",\n      \"Task 9: Audit for generic/untyped/ambiguous sections\",\n      \"Task 10: Enforce valid naming convention per methodology\",\n      \"Task 11: Check file naming aligns with pattern\",\n      \"Task 12: Cross-validate structural elements with full quality checklist\",\n      \"Task 13: Synthesize all checks into global structural pass/fail\",\n      \"Task 14: Set or clear 'Blocker Flag' depending on pass/fail state\",\n      \"Task 15: Halt downstream tasks if 'Blocker Flag' is set\",\n      \"Task 16: Comprehensive Structural Validation (depends on Tasks 1–13)\",\n      \"Task 17: Approve template for release/implementation if and only if Task 16 passes and Blocker Flag is not set\",\n      \"Task 18: Release template to implementation or chaining\",\n      \"Task 19: Initiate downstream: vector application, universalization, or complex chaining\"\n    ],\n    \"execution_blockers\": [\n      \"Blocker Flag: Any failure in Tasks 1–13 results in setting 'Blocker Flag', which halts Tasks 15, 17, 18, and 19.\"\n    ],\n    \"hidden_assumptions\": [\n      \"All templates submitted for validation are intended for universal applicability and are not tailored to domain-specific exceptions unless validated as such\",\n      \"Downstream tasks (vector application, chaining, etc.) have no error-correction responsibilities for upstream mis-formatting\",\n      \"All structural validation criteria are both necessary and sufficient—no extraneous checks exist beyond the prescribed methodology\",\n      \"Blocker mechanism is immediate and non-negotiable; overrides any manual release attempts\"\n    ],\n    \"relationships\": {\n      \"Task 16\": [\"Tasks 1–13\"],\n      \"Task 14\": [\"Tasks 13\"],\n      \"Task 15\": [\"Task 14\"],\n      \"Task 17\": [\"Task 16\", \"Task 14 (Blocker Flag clear)\"],\n      \"Task 18\": [\"Task 17\"],\n      \"Task 19\": [\"Task 18\"]\n    }\n  },\n  \"conflict_nodes\": [\n    {\n      \"node\": \"Task 17/18 (Approval/Release)\",\n      \"conflict\": \"Attempting to advance to release or downstream sequencing despite 'Blocker Flag' being set, typically caused by overlooked or misinterpreted structural criteria in Tasks 1–13.\"\n    },\n    {\n      \"node\": \"Task 16/14 (Validation/Blocker Setting)\",\n      \"conflict\": \"Divergence in interpretation of validation outcomes—ambiguity over what constitutes pass/fail may improperly set/clear the Blocker Flag, allowing structurally non-conformant templates to slip through or valid ones to be wrongly halted.\"\n    },\n    {\n      \"node\": \"Hidden Assumption 2 (No error correction downstream)\",\n      \"conflict\": \"Reliance on strict upstream compliance places total pressure on Tasks 1–16; if a template is released in error, all downstream chaining, vectorization, and universalization become invalid, introducing systemic fault propagation.\"\n    }\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Boundary Encoder\n\nYour goal is not to **analyze** the input text, but to **decompose** it into a sequential list of distinct semantic segments. Execute as:\n\n`{role=boundary_encoder; input=[execution_blockers:array, hidden_assumptions:list]; process=[translate_blockers_to_constraints(), translate_assumptions_to_requirements(), validate_specificity(), eliminate_duplicates()]; constraints=[boundary_focus_only(), no_process_generation()]; requirements=[constraint_exactness(), requirement_enforceability()]; output={constraints:list, requirements:list}}`",
      "step": "g",
      "title": "Boundary Encoder",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"# Instruction Patterns: Generalized AI Template Design\\n\\n## Core Philosophy\\n\\nThis document contains the complete methodology for creating **highly effective and generalized AI instruction templates**. These patterns embody fundamental principles that enable any LLM to understand and replicate the system's design approach.\\n\\n## The Three-Part Universal Structure\\n\\nEvery effective AI instruction follows this exact pattern:\\n\\n```\\n[Title] Interpretation Execute as: `{Transformation}`\\n```\\n\\n### 1. [Title] - Purpose Declaration\\n- **Format**: Enclosed in square brackets `[Title]`\\n- **Function**: Concise, action-oriented description of the template's purpose\\n- **Style**: Title case, descriptive, no generic terms\\n- **Examples**: `[Essence Distillation]`, `[Code Optimizer]`, `[Content Analyzer]`\\n\\n### 2. Interpretation - Human-Readable Instructions\\n- **Goal Negation Pattern**: MUST begin with `\\\"Your goal is not to **[action]**, but to **[transformation]**\\\"`\\n- **Command Voice**: No first-person references, no conversational language\\n- **Clarity**: Explains the transformation in natural language\\n- **Ending**: MUST end with \\\"Execute as:\\\" leading to transformation block\\n\\n### 3. `{Transformation}` - Machine-Parsable Parameters\\n- **Format**: JSON-like structure in backticks with curly braces\\n- **Components**: role, input, process, constraints, requirements, output\\n- **Type Safety**: All parameters must specify data types\\n- **Actionability**: All process steps must be executable functions\\n\\n## Transformation Block Specification\\n\\n```\\n{\\n  role=<specific_role_name>;\\n  input=[<parameter_name>:<data_type>];\\n  process=[<step1>(), <step2>(), <step3>()];\\n  constraints=[<limitation1>(), <limitation2>()];\\n  requirements=[<requirement1>(), <requirement2>()];\\n  output={<result_name>:<data_type>}\\n}\\n```\\n\\n### Component Rules\\n\\n**role** (Required)\\n- Must be specific and descriptive (no \\\"assistant\\\" or \\\"helper\\\")\\n- Use underscore_case for multi-word roles\\n- Examples: `essence_distiller`, `code_optimizer`, `content_analyst`\\n\\n**input** (Required)\\n- Format: `[parameter_name:data_type]`\\n- Support multiple parameters: `[text:str, language:str]`\\n- Use descriptive names: `[original:any]`, `[source_code:str]`\\n\\n**process** (Required)\\n- Function-like notation with parentheses: `identify_core_intent()`\\n- Actionable, atomic steps in logical sequence\\n- Verb-based names describing specific operations\\n\\n**constraints** (Optional)\\n- Operational boundaries and limitations\\n- Format restrictions and scope definitions\\n- Examples: `preserve_original_meaning()`, `maintain_technical_accuracy()`\\n\\n**requirements** (Optional)\\n- Mandatory output characteristics and quality standards\\n- Validation criteria and format specifications\\n- Examples: `structured_output()`, `comprehensive_coverage()`\\n\\n**output** (Required)\\n- Format: `{parameter_name:data_type}`\\n- Descriptive names with type specification\\n- Support complex structures: `{analysis:dict, improvements:list}`\\n\\n## Directional Transformation Patterns\\n\\n### Core Principle\\nFocus on **how to transform** rather than **what to transform**. Directional vectors are universal and context-independent.\\n\\n### Universal Vector Categories\\n\\n**Intensity Vectors**\\n- `amplify`: Intensify inherent qualities through magnification\\n- `intensify`: Compress to maximum density and focus\\n- `diminish`: Reduce intensity while preserving form\\n\\n**Clarity Vectors**\\n- `clarify`: Enhance transparency and definition\\n- `purify`: Remove non-essential elements\\n- `obscure`: Add complexity layers and indirection\\n\\n**Structural Vectors**\\n- `expand`: Extend natural boundaries dimensionally\\n- `compress`: Maximize density without information loss\\n- `restructure`: Transform fundamental organization pattern\\n\\n**Transformation Vectors**\\n- `elevate`: Transform to higher operational level\\n- `distill`: Extract absolute essence through pure extraction\\n- `synthesize`: Create unified emergent form\\n\\n**Meta Vectors**\\n- `abstract`: Extract to pure conceptual form\\n- `concretize`: Translate abstract elements to tangible form\\n- `transcend`: Operate beyond current dimensional limitations\\n\\n### Vector Template Pattern\\n\\n```\\n[Vector] Your goal is not to **analyze** the input, but to **[vector]** it through pure directional transformation. Execute as: `{role=[vector]_operator; input=[content:any]; process=[identify_[vector]_potential(), apply_[vector]_transformation(), preserve_essential_properties(), validate_[vector]_completion()]; constraints=[preserve_fundamental_nature(), maintain_structural_integrity()]; output={[vector]ed:any}}`\\n```\\n\\n## Goal Negation Pattern\\n\\n### Purpose\\nEliminates ambiguity by explicitly stating what NOT to do, then clearly defining the intended transformation.\\n\\n### Structure\\n```\\nYour goal is not to **[common_misinterpretation]**, but to **[precise_transformation]**\\n```\\n\\n### Examples\\n- `Your goal is not to **analyze** the input, but to **amplify** its inherent qualities`\\n- `Your goal is not to **summarize** the content, but to **distill** its absolute essence`\\n- `Your goal is not to **describe** the code, but to **optimize** it for performance`\\n\\n## Forbidden Language Patterns\\n\\n### Conversational Elements\\n- First-person: *I, me, my, we, us*\\n- Politeness: *please, thank you, let's*\\n- Uncertainty: *maybe, perhaps, might, could*\\n- Questions in directives\\n- Meta-commentary or explanations\\n\\n### Structural Violations\\n- Merging or omitting required sections\\n- Untyped parameters or outputs\\n- Generic roles like \\\"assistant\\\"\\n- Vague process descriptions\\n- Missing goal negation pattern\\n\\n## Sequence Composition Patterns\\n\\n### Linear Sequences\\n```\\nStep A → Step B → Step C\\n1031-a-form_classifier → 1031-b-essence_extractor → 1031-c-structure_optimizer\\n```\\n\\n### Parallel Processing\\n```\\nInput → [Template A | Template B | Template C] → Synthesis\\n```\\n\\n### Chain Mode\\n```\\nOriginal Input → Template A → Output A becomes Input B → Template B → Final Output\\n```\\n\\n### Meta-Application\\n```\\nabstract → [any_vector] → concretize\\n```\\n\\n## Template Naming Convention\\n\\n### Format\\n```\\n<sequence_id>-<step>-<descriptive_name>.md\\n```\\n\\n### Components\\n- **sequence_id**: Four-digit number (0001, 0002, 1031)\\n- **step**: Single lowercase letter (a, b, c) for multi-step sequences\\n- **descriptive_name**: Hyphenated lowercase words describing function\\n\\n### Examples\\n- `1031-a-form-classifier.md`\\n- `9000-a-amplify.md`\\n- `0001-instruction-converter.md`\\n\\n## Quality Validation Checklist\\n\\n- [ ] Three-part structure intact\\n- [ ] Goal negation present and properly formatted\\n- [ ] Role is specific and non-generic\\n- [ ] Input parameters are typed\\n- [ ] Process steps are ordered and actionable\\n- [ ] Output format is typed and structured\\n- [ ] No forbidden language patterns\\n- [ ] File naming convention followed\\n- [ ] Template serves clear, specific purpose\\n\\n## Universal Applicability\\n\\nThese patterns work across:\\n- **Content Types**: Text, code, data, concepts, problems, solutions\\n- **Domains**: Technical, creative, business, academic\\n- **Languages**: Natural languages, programming languages, formal languages\\n- **Contexts**: Any domain without specialized knowledge requirements\\n\\n## Implementation Philosophy\\n\\n1. **Clarity First**: Template purpose immediately clear\\n2. **Atomic Operations**: Each template performs one specific transformation\\n3. **Composability**: Templates work well in sequences\\n4. **Type Safety**: Always specify data types\\n5. **Validation**: Include validation steps in processes\\n6. **Consistency**: Same patterns produce predictable results\\n7. **Universality**: Design for maximum generalization\\n\\n## Advanced Patterns\\n\\n### Recursive Application\\n```\\n[Self-Amplify] Apply amplification vector to its own amplification process\\n[Meta-Distill] Distill the distillation process itself\\n[Transcendent-Clarify] Clarify beyond normal clarity boundaries\\n```\\n\\n### Vector Algebra\\n```\\namplify + clarify = enhanced_clarity\\ncompress + distill = essential_core\\nexpand + abstract = universal_pattern\\nelevate + synthesize = emergent_transcendence\\n```\\n\\n### Inverse Operations\\n```\\namplify ↔ diminish\\nexpand ↔ compress\\nclarify ↔ obscure\\nabstract ↔ concretize\\n```\\n\\n### Intensity Scaling\\n```\\n# Light application\\nprocess=[gentle_[vector](), preserve_majority_original()]\\n\\n# Standard application\\nprocess=[apply_[vector](), balance_transformation()]\\n\\n# Maximum application\\nprocess=[maximum_[vector](), complete_transformation()]\\n```\\n\\n## Key Principles for LLM Replication\\n\\n1. **Vector Independence**: Transformation vectors operate independently of content, context, or domain\\n2. **Essence Preservation**: Core identity remains intact through transformation\\n3. **Composability**: Vectors can be chained and combined without loss of integrity\\n4. **Type Consistency**: `any → any` maintains input/output type compatibility\\n5. **Context-Free Operation**: No domain knowledge or content analysis required\\n6. **Operational Consistency**: Same vector produces consistent transformation patterns\\n\\nThis methodology enables creation of instruction templates that are maximally effective, universally applicable, and consistently reliable across any AI system or use case.\\\"```'\\n\\n\",\n  \"dependency_map\": {\n    \"primary_directive\": [\n      \"Task 16: Comprehensive Structural Validation\"\n    ],\n    \"atomic_tasks\": [\n      \"Task 1: Check three-part structure integrity\",\n      \"Task 2: Confirm presence and correctness of goal-negation phrasing\",\n      \"Task 3: Validate that role is specific and non-generic\",\n      \"Task 4: Check that all input parameters are present and strongly typed\",\n      \"Task 5: Ensure all process steps are actionable and formatted as function calls\",\n      \"Task 6: Confirm output field is present, well-typed, and structured\",\n      \"Task 7: Verify constraints and requirements follow formatting rules\",\n      \"Task 8: Audit for forbidden language patterns\",\n      \"Task 9: Audit for generic/untyped/ambiguous sections\",\n      \"Task 10: Enforce valid naming convention per methodology\",\n      \"Task 11: Check file naming aligns with pattern\",\n      \"Task 12: Cross-validate structural elements with full quality checklist\",\n      \"Task 13: Synthesize all checks into global structural pass/fail\",\n      \"Task 14: Set or clear 'Blocker Flag' depending on pass/fail state\",\n      \"Task 15: Halt downstream tasks if 'Blocker Flag' is set\",\n      \"Task 16: Comprehensive Structural Validation (depends on Tasks 1–13)\",\n      \"Task 17: Approve template for release/implementation if and only if Task 16 passes and Blocker Flag is not set\",\n      \"Task 18: Release template to implementation or chaining\",\n      \"Task 19: Initiate downstream: vector application, universalization, or complex chaining\"\n    ],\n    \"execution_blockers\": [\n      \"Blocker Flag: Any failure in Tasks 1–13 results in setting 'Blocker Flag', which halts Tasks 15, 17, 18, and 19.\"\n    ],\n    \"hidden_assumptions\": [\n      \"All templates submitted for validation are intended for universal applicability and are not tailored to domain-specific exceptions unless validated as such\",\n      \"Downstream tasks (vector application, chaining, etc.) have no error-correction responsibilities for upstream mis-formatting\",\n      \"All structural validation criteria are both necessary and sufficient—no extraneous checks exist beyond the prescribed methodology\",\n      \"Blocker mechanism is immediate and non-negotiable; overrides any manual release attempts\"\n    ],\n    \"relationships\": {\n      \"Task 16\": [\n        \"Tasks 1–13\"\n      ],\n      \"Task 14\": [\n        \"Tasks 13\"\n      ],\n      \"Task 15\": [\n        \"Task 14\"\n      ],\n      \"Task 17\": [\n        \"Task 16\",\n        \"Task 14 (Blocker Flag clear)\"\n      ],\n      \"Task 18\": [\n        \"Task 17\"\n      ],\n      \"Task 19\": [\n        \"Task 18\"\n      ]\n    }\n  },\n  \"conflict_nodes\": [\n    {\n      \"node\": \"Task 17/18 (Approval/Release)\",\n      \"conflict\": \"Attempting to advance to release or downstream sequencing despite 'Blocker Flag' being set, typically caused by overlooked or misinterpreted structural criteria in Tasks 1–13.\"\n    },\n    {\n      \"node\": \"Task 16/14 (Validation/Blocker Setting)\",\n      \"conflict\": \"Divergence in interpretation of validation outcomes—ambiguity over what constitutes pass/fail may improperly set/clear the Blocker Flag, allowing structurally non-conformant templates to slip through or valid ones to be wrongly halted.\"\n    },\n    {\n      \"node\": \"Hidden Assumption 2 (No error correction downstream)\",\n      \"conflict\": \"Reliance on strict upstream compliance places total pressure on Tasks 1–16; if a template is released in error, all downstream chaining, vectorization, and universalization become invalid, introducing systemic fault propagation.\"\n    }\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"role\": \"boundary_encoder\",\n  \"input\": [\n    \"execution_blockers:array\",\n    \"hidden_assumptions:list\"\n  ],\n  \"process\": [\n    \"translate_blockers_to_constraints()\",\n    \"translate_assumptions_to_requirements()\",\n    \"validate_specificity()\",\n    \"eliminate_duplicates()\"\n  ],\n  \"constraints\": [\n    \"Blocker Flag must halt execution of Tasks 15, 17, 18, and 19 if any validation (Tasks 1–13) fails\",\n    \"No downstream task may proceed unless Blocker Flag is clear\",\n    \"Validation boundary is strictly determined by Tasks 1–13; no extraneous validations allowed\",\n    \"Downstream tasks cannot perform error correction for upstream structural faults\",\n    \"Immediate and non-negotiable enforcement of Blocker mechanism\"\n  ],\n  \"requirements\": [\n    \"Templates must be intended for universal applicability (unless validated as domain-specific)\",\n    \"All structural validation criteria defined in methodology must be both necessary and sufficient\",\n    \"Blocker mechanism must override manual/interpersonal release attempts\",\n    \"Systemic integrity: Downstream process validity is dependent on upstream compliance and correct blocker enforcement\"\n  ],\n  \"output\": {\n    \"constraints\": [\n      \"Blocker Flag must halt Tasks 15, 17, 18, 19 on failure in Tasks 1–13\",\n      \"No downstream advancement if Blocker Flag is set\",\n      \"Validation is only according to Tasks 1–13; no additional validations permitted\",\n      \"Downstream cannot correct upstream validation errors\",\n      \"Blocker Flag activation is immediate and must supersede manual overrides\"\n    ],\n    \"requirements\": [\n      \"Templates are for universal use unless domain-specific validation occurs\",\n      \"Must meet all, and only, structural criteria as specified by methodology\",\n      \"Blocker Flag overrides all manual or external release attempts\",\n      \"Chaining and vectorization downstream require clean upstream validation and blocker clearance\"\n    ]\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Template Synthesizer\n\nYour goal is not to **rearrange** the segments arbitrarily, but to **reorder** them based on optimal logical and semantic flow. Execute as:\n\n`{role=template_synthesizer; input=[primary_directive:str, supporting_rationale:list, process_steps:array, constraints:list, requirements:list]; process=[generate_concise_title(), craft_goal_negation_interpretation(primary_directive), assemble_transformation_block(process_steps,constraints,requirements), infer_typed_inputs_outputs(), enforce_semicolon_key_value_syntax(), polish_interpretation_for_llm_optimization()]; constraints=[canonical_three_part_format(), command_voice_only(), zero_semantic_drift()]; requirements=[syntactic_perfection(), operational_clarity(), domain_agnostic_applicability()]; output={draft_template:str}}`",
      "step": "h",
      "title": "Template Synthesizer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"# Instruction Patterns: Generalized AI Template Design\\n\\n## Core Philosophy\\n\\nThis document contains the complete methodology for creating **highly effective and generalized AI instruction templates**. These patterns embody fundamental principles that enable any LLM to understand and replicate the system's design approach.\\n\\n## The Three-Part Universal Structure\\n\\nEvery effective AI instruction follows this exact pattern:\\n\\n```\\n[Title] Interpretation Execute as: `{Transformation}`\\n```\\n\\n### 1. [Title] - Purpose Declaration\\n- **Format**: Enclosed in square brackets `[Title]`\\n- **Function**: Concise, action-oriented description of the template's purpose\\n- **Style**: Title case, descriptive, no generic terms\\n- **Examples**: `[Essence Distillation]`, `[Code Optimizer]`, `[Content Analyzer]`\\n\\n### 2. Interpretation - Human-Readable Instructions\\n- **Goal Negation Pattern**: MUST begin with `\\\"Your goal is not to **[action]**, but to **[transformation]**\\\"`\\n- **Command Voice**: No first-person references, no conversational language\\n- **Clarity**: Explains the transformation in natural language\\n- **Ending**: MUST end with \\\"Execute as:\\\" leading to transformation block\\n\\n### 3. `{Transformation}` - Machine-Parsable Parameters\\n- **Format**: JSON-like structure in backticks with curly braces\\n- **Components**: role, input, process, constraints, requirements, output\\n- **Type Safety**: All parameters must specify data types\\n- **Actionability**: All process steps must be executable functions\\n\\n## Transformation Block Specification\\n\\n```\\n{\\n  role=<specific_role_name>;\\n  input=[<parameter_name>:<data_type>];\\n  process=[<step1>(), <step2>(), <step3>()];\\n  constraints=[<limitation1>(), <limitation2>()];\\n  requirements=[<requirement1>(), <requirement2>()];\\n  output={<result_name>:<data_type>}\\n}\\n```\\n\\n### Component Rules\\n\\n**role** (Required)\\n- Must be specific and descriptive (no \\\"assistant\\\" or \\\"helper\\\")\\n- Use underscore_case for multi-word roles\\n- Examples: `essence_distiller`, `code_optimizer`, `content_analyst`\\n\\n**input** (Required)\\n- Format: `[parameter_name:data_type]`\\n- Support multiple parameters: `[text:str, language:str]`\\n- Use descriptive names: `[original:any]`, `[source_code:str]`\\n\\n**process** (Required)\\n- Function-like notation with parentheses: `identify_core_intent()`\\n- Actionable, atomic steps in logical sequence\\n- Verb-based names describing specific operations\\n\\n**constraints** (Optional)\\n- Operational boundaries and limitations\\n- Format restrictions and scope definitions\\n- Examples: `preserve_original_meaning()`, `maintain_technical_accuracy()`\\n\\n**requirements** (Optional)\\n- Mandatory output characteristics and quality standards\\n- Validation criteria and format specifications\\n- Examples: `structured_output()`, `comprehensive_coverage()`\\n\\n**output** (Required)\\n- Format: `{parameter_name:data_type}`\\n- Descriptive names with type specification\\n- Support complex structures: `{analysis:dict, improvements:list}`\\n\\n## Directional Transformation Patterns\\n\\n### Core Principle\\nFocus on **how to transform** rather than **what to transform**. Directional vectors are universal and context-independent.\\n\\n### Universal Vector Categories\\n\\n**Intensity Vectors**\\n- `amplify`: Intensify inherent qualities through magnification\\n- `intensify`: Compress to maximum density and focus\\n- `diminish`: Reduce intensity while preserving form\\n\\n**Clarity Vectors**\\n- `clarify`: Enhance transparency and definition\\n- `purify`: Remove non-essential elements\\n- `obscure`: Add complexity layers and indirection\\n\\n**Structural Vectors**\\n- `expand`: Extend natural boundaries dimensionally\\n- `compress`: Maximize density without information loss\\n- `restructure`: Transform fundamental organization pattern\\n\\n**Transformation Vectors**\\n- `elevate`: Transform to higher operational level\\n- `distill`: Extract absolute essence through pure extraction\\n- `synthesize`: Create unified emergent form\\n\\n**Meta Vectors**\\n- `abstract`: Extract to pure conceptual form\\n- `concretize`: Translate abstract elements to tangible form\\n- `transcend`: Operate beyond current dimensional limitations\\n\\n### Vector Template Pattern\\n\\n```\\n[Vector] Your goal is not to **analyze** the input, but to **[vector]** it through pure directional transformation. Execute as: `{role=[vector]_operator; input=[content:any]; process=[identify_[vector]_potential(), apply_[vector]_transformation(), preserve_essential_properties(), validate_[vector]_completion()]; constraints=[preserve_fundamental_nature(), maintain_structural_integrity()]; output={[vector]ed:any}}`\\n```\\n\\n## Goal Negation Pattern\\n\\n### Purpose\\nEliminates ambiguity by explicitly stating what NOT to do, then clearly defining the intended transformation.\\n\\n### Structure\\n```\\nYour goal is not to **[common_misinterpretation]**, but to **[precise_transformation]**\\n```\\n\\n### Examples\\n- `Your goal is not to **analyze** the input, but to **amplify** its inherent qualities`\\n- `Your goal is not to **summarize** the content, but to **distill** its absolute essence`\\n- `Your goal is not to **describe** the code, but to **optimize** it for performance`\\n\\n## Forbidden Language Patterns\\n\\n### Conversational Elements\\n- First-person: *I, me, my, we, us*\\n- Politeness: *please, thank you, let's*\\n- Uncertainty: *maybe, perhaps, might, could*\\n- Questions in directives\\n- Meta-commentary or explanations\\n\\n### Structural Violations\\n- Merging or omitting required sections\\n- Untyped parameters or outputs\\n- Generic roles like \\\"assistant\\\"\\n- Vague process descriptions\\n- Missing goal negation pattern\\n\\n## Sequence Composition Patterns\\n\\n### Linear Sequences\\n```\\nStep A → Step B → Step C\\n1031-a-form_classifier → 1031-b-essence_extractor → 1031-c-structure_optimizer\\n```\\n\\n### Parallel Processing\\n```\\nInput → [Template A | Template B | Template C] → Synthesis\\n```\\n\\n### Chain Mode\\n```\\nOriginal Input → Template A → Output A becomes Input B → Template B → Final Output\\n```\\n\\n### Meta-Application\\n```\\nabstract → [any_vector] → concretize\\n```\\n\\n## Template Naming Convention\\n\\n### Format\\n```\\n<sequence_id>-<step>-<descriptive_name>.md\\n```\\n\\n### Components\\n- **sequence_id**: Four-digit number (0001, 0002, 1031)\\n- **step**: Single lowercase letter (a, b, c) for multi-step sequences\\n- **descriptive_name**: Hyphenated lowercase words describing function\\n\\n### Examples\\n- `1031-a-form-classifier.md`\\n- `9000-a-amplify.md`\\n- `0001-instruction-converter.md`\\n\\n## Quality Validation Checklist\\n\\n- [ ] Three-part structure intact\\n- [ ] Goal negation present and properly formatted\\n- [ ] Role is specific and non-generic\\n- [ ] Input parameters are typed\\n- [ ] Process steps are ordered and actionable\\n- [ ] Output format is typed and structured\\n- [ ] No forbidden language patterns\\n- [ ] File naming convention followed\\n- [ ] Template serves clear, specific purpose\\n\\n## Universal Applicability\\n\\nThese patterns work across:\\n- **Content Types**: Text, code, data, concepts, problems, solutions\\n- **Domains**: Technical, creative, business, academic\\n- **Languages**: Natural languages, programming languages, formal languages\\n- **Contexts**: Any domain without specialized knowledge requirements\\n\\n## Implementation Philosophy\\n\\n1. **Clarity First**: Template purpose immediately clear\\n2. **Atomic Operations**: Each template performs one specific transformation\\n3. **Composability**: Templates work well in sequences\\n4. **Type Safety**: Always specify data types\\n5. **Validation**: Include validation steps in processes\\n6. **Consistency**: Same patterns produce predictable results\\n7. **Universality**: Design for maximum generalization\\n\\n## Advanced Patterns\\n\\n### Recursive Application\\n```\\n[Self-Amplify] Apply amplification vector to its own amplification process\\n[Meta-Distill] Distill the distillation process itself\\n[Transcendent-Clarify] Clarify beyond normal clarity boundaries\\n```\\n\\n### Vector Algebra\\n```\\namplify + clarify = enhanced_clarity\\ncompress + distill = essential_core\\nexpand + abstract = universal_pattern\\nelevate + synthesize = emergent_transcendence\\n```\\n\\n### Inverse Operations\\n```\\namplify ↔ diminish\\nexpand ↔ compress\\nclarify ↔ obscure\\nabstract ↔ concretize\\n```\\n\\n### Intensity Scaling\\n```\\n# Light application\\nprocess=[gentle_[vector](), preserve_majority_original()]\\n\\n# Standard application\\nprocess=[apply_[vector](), balance_transformation()]\\n\\n# Maximum application\\nprocess=[maximum_[vector](), complete_transformation()]\\n```\\n\\n## Key Principles for LLM Replication\\n\\n1. **Vector Independence**: Transformation vectors operate independently of content, context, or domain\\n2. **Essence Preservation**: Core identity remains intact through transformation\\n3. **Composability**: Vectors can be chained and combined without loss of integrity\\n4. **Type Consistency**: `any → any` maintains input/output type compatibility\\n5. **Context-Free Operation**: No domain knowledge or content analysis required\\n6. **Operational Consistency**: Same vector produces consistent transformation patterns\\n\\nThis methodology enables creation of instruction templates that are maximally effective, universally applicable, and consistently reliable across any AI system or use case.\\\"```'\\n\\n\",\n  \"role\": \"boundary_encoder\",\n  \"input\": [\n    \"execution_blockers:array\",\n    \"hidden_assumptions:list\"\n  ],\n  \"process\": [\n    \"translate_blockers_to_constraints()\",\n    \"translate_assumptions_to_requirements()\",\n    \"validate_specificity()\",\n    \"eliminate_duplicates()\"\n  ],\n  \"constraints\": [\n    \"Blocker Flag must halt execution of Tasks 15, 17, 18, and 19 if any validation (Tasks 1–13) fails\",\n    \"No downstream task may proceed unless Blocker Flag is clear\",\n    \"Validation boundary is strictly determined by Tasks 1–13; no extraneous validations allowed\",\n    \"Downstream tasks cannot perform error correction for upstream structural faults\",\n    \"Immediate and non-negotiable enforcement of Blocker mechanism\"\n  ],\n  \"requirements\": [\n    \"Templates must be intended for universal applicability (unless validated as domain-specific)\",\n    \"All structural validation criteria defined in methodology must be both necessary and sufficient\",\n    \"Blocker mechanism must override manual/interpersonal release attempts\",\n    \"Systemic integrity: Downstream process validity is dependent on upstream compliance and correct blocker enforcement\"\n  ],\n  \"output\": {\n    \"constraints\": [\n      \"Blocker Flag must halt Tasks 15, 17, 18, 19 on failure in Tasks 1–13\",\n      \"No downstream advancement if Blocker Flag is set\",\n      \"Validation is only according to Tasks 1–13; no additional validations permitted\",\n      \"Downstream cannot correct upstream validation errors\",\n      \"Blocker Flag activation is immediate and must supersede manual overrides\"\n    ],\n    \"requirements\": [\n      \"Templates are for universal use unless domain-specific validation occurs\",\n      \"Must meet all, and only, structural criteria as specified by methodology\",\n      \"Blocker Flag overrides all manual or external release attempts\",\n      \"Chaining and vectorization downstream require clean upstream validation and blocker clearance\"\n    ]\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"draft_template\": \"[Validation Boundary Encoder]\\nYour goal is not to **passively monitor** input boundaries, but to **actively encode and enforce execution constraints and requirements** derived from identified blockers and hidden assumptions, ensuring unbreachable systemic integrity throughout all downstream workflows. Execute as: `{role=boundary_encoder; input=[execution_blockers:array, hidden_assumptions:list]; process=[translate_blockers_to_constraints(), translate_assumptions_to_requirements(), validate_specificity(), eliminate_duplicates()]; constraints=[Blocker Flag must halt execution of Tasks 15, 17, 18, and 19 if any validation (Tasks 1–13) fails; No downstream task may proceed unless Blocker Flag is clear; Validation boundary is strictly determined by Tasks 1–13; no extraneous validations allowed; Downstream tasks cannot perform error correction for upstream structural faults; Immediate and non-negotiable enforcement of Blocker mechanism]; requirements=[Templates must be intended for universal applicability (unless validated as domain-specific); All structural validation criteria defined in methodology must be both necessary and sufficient; Blocker mechanism must override manual/interpersonal release attempts; Systemic integrity: Downstream process validity is dependent on upstream compliance and correct blocker enforcement]; output={constraints:array, requirements:array}}`\"\n}",
        }
      }
    },
      {
      "instruction": "# Syntax Validator\n\n[Syntax Validator] Your goal is not to **combine** the segments mechanically, but to **synthesize** them into a cohesive, unified text with enhanced clarity and impact. Execute as: {role=syntax_validator; input=[draft_template:str]; process=[split_into_sections(), match_regex_pattern(\"\\[(.*?)\\]\\s*(.*?)\\s*(`\\{.*?\\}`)\"), verify_goal_negation(), confirm_typed_parameters(), check_constraint_requirement_alignment(), score_llm_optimization(), report_errors()]; constraints=[report_only(), no_correction_attempts()]; requirements=[binary_pass_fail(), ambiguity_score<=0, quality_score>=0.9]; output={validated_template:str, validation_report:dict}}`\n\n",
      "step": "i",
      "title": "Syntax Validator",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"# Instruction Patterns: Generalized AI Template Design\\n\\n## Core Philosophy\\n\\nThis document contains the complete methodology for creating **highly effective and generalized AI instruction templates**. These patterns embody fundamental principles that enable any LLM to understand and replicate the system's design approach.\\n\\n## The Three-Part Universal Structure\\n\\nEvery effective AI instruction follows this exact pattern:\\n\\n```\\n[Title] Interpretation Execute as: `{Transformation}`\\n```\\n\\n### 1. [Title] - Purpose Declaration\\n- **Format**: Enclosed in square brackets `[Title]`\\n- **Function**: Concise, action-oriented description of the template's purpose\\n- **Style**: Title case, descriptive, no generic terms\\n- **Examples**: `[Essence Distillation]`, `[Code Optimizer]`, `[Content Analyzer]`\\n\\n### 2. Interpretation - Human-Readable Instructions\\n- **Goal Negation Pattern**: MUST begin with `\\\"Your goal is not to **[action]**, but to **[transformation]**\\\"`\\n- **Command Voice**: No first-person references, no conversational language\\n- **Clarity**: Explains the transformation in natural language\\n- **Ending**: MUST end with \\\"Execute as:\\\" leading to transformation block\\n\\n### 3. `{Transformation}` - Machine-Parsable Parameters\\n- **Format**: JSON-like structure in backticks with curly braces\\n- **Components**: role, input, process, constraints, requirements, output\\n- **Type Safety**: All parameters must specify data types\\n- **Actionability**: All process steps must be executable functions\\n\\n## Transformation Block Specification\\n\\n```\\n{\\n  role=<specific_role_name>;\\n  input=[<parameter_name>:<data_type>];\\n  process=[<step1>(), <step2>(), <step3>()];\\n  constraints=[<limitation1>(), <limitation2>()];\\n  requirements=[<requirement1>(), <requirement2>()];\\n  output={<result_name>:<data_type>}\\n}\\n```\\n\\n### Component Rules\\n\\n**role** (Required)\\n- Must be specific and descriptive (no \\\"assistant\\\" or \\\"helper\\\")\\n- Use underscore_case for multi-word roles\\n- Examples: `essence_distiller`, `code_optimizer`, `content_analyst`\\n\\n**input** (Required)\\n- Format: `[parameter_name:data_type]`\\n- Support multiple parameters: `[text:str, language:str]`\\n- Use descriptive names: `[original:any]`, `[source_code:str]`\\n\\n**process** (Required)\\n- Function-like notation with parentheses: `identify_core_intent()`\\n- Actionable, atomic steps in logical sequence\\n- Verb-based names describing specific operations\\n\\n**constraints** (Optional)\\n- Operational boundaries and limitations\\n- Format restrictions and scope definitions\\n- Examples: `preserve_original_meaning()`, `maintain_technical_accuracy()`\\n\\n**requirements** (Optional)\\n- Mandatory output characteristics and quality standards\\n- Validation criteria and format specifications\\n- Examples: `structured_output()`, `comprehensive_coverage()`\\n\\n**output** (Required)\\n- Format: `{parameter_name:data_type}`\\n- Descriptive names with type specification\\n- Support complex structures: `{analysis:dict, improvements:list}`\\n\\n## Directional Transformation Patterns\\n\\n### Core Principle\\nFocus on **how to transform** rather than **what to transform**. Directional vectors are universal and context-independent.\\n\\n### Universal Vector Categories\\n\\n**Intensity Vectors**\\n- `amplify`: Intensify inherent qualities through magnification\\n- `intensify`: Compress to maximum density and focus\\n- `diminish`: Reduce intensity while preserving form\\n\\n**Clarity Vectors**\\n- `clarify`: Enhance transparency and definition\\n- `purify`: Remove non-essential elements\\n- `obscure`: Add complexity layers and indirection\\n\\n**Structural Vectors**\\n- `expand`: Extend natural boundaries dimensionally\\n- `compress`: Maximize density without information loss\\n- `restructure`: Transform fundamental organization pattern\\n\\n**Transformation Vectors**\\n- `elevate`: Transform to higher operational level\\n- `distill`: Extract absolute essence through pure extraction\\n- `synthesize`: Create unified emergent form\\n\\n**Meta Vectors**\\n- `abstract`: Extract to pure conceptual form\\n- `concretize`: Translate abstract elements to tangible form\\n- `transcend`: Operate beyond current dimensional limitations\\n\\n### Vector Template Pattern\\n\\n```\\n[Vector] Your goal is not to **analyze** the input, but to **[vector]** it through pure directional transformation. Execute as: `{role=[vector]_operator; input=[content:any]; process=[identify_[vector]_potential(), apply_[vector]_transformation(), preserve_essential_properties(), validate_[vector]_completion()]; constraints=[preserve_fundamental_nature(), maintain_structural_integrity()]; output={[vector]ed:any}}`\\n```\\n\\n## Goal Negation Pattern\\n\\n### Purpose\\nEliminates ambiguity by explicitly stating what NOT to do, then clearly defining the intended transformation.\\n\\n### Structure\\n```\\nYour goal is not to **[common_misinterpretation]**, but to **[precise_transformation]**\\n```\\n\\n### Examples\\n- `Your goal is not to **analyze** the input, but to **amplify** its inherent qualities`\\n- `Your goal is not to **summarize** the content, but to **distill** its absolute essence`\\n- `Your goal is not to **describe** the code, but to **optimize** it for performance`\\n\\n## Forbidden Language Patterns\\n\\n### Conversational Elements\\n- First-person: *I, me, my, we, us*\\n- Politeness: *please, thank you, let's*\\n- Uncertainty: *maybe, perhaps, might, could*\\n- Questions in directives\\n- Meta-commentary or explanations\\n\\n### Structural Violations\\n- Merging or omitting required sections\\n- Untyped parameters or outputs\\n- Generic roles like \\\"assistant\\\"\\n- Vague process descriptions\\n- Missing goal negation pattern\\n\\n## Sequence Composition Patterns\\n\\n### Linear Sequences\\n```\\nStep A → Step B → Step C\\n1031-a-form_classifier → 1031-b-essence_extractor → 1031-c-structure_optimizer\\n```\\n\\n### Parallel Processing\\n```\\nInput → [Template A | Template B | Template C] → Synthesis\\n```\\n\\n### Chain Mode\\n```\\nOriginal Input → Template A → Output A becomes Input B → Template B → Final Output\\n```\\n\\n### Meta-Application\\n```\\nabstract → [any_vector] → concretize\\n```\\n\\n## Template Naming Convention\\n\\n### Format\\n```\\n<sequence_id>-<step>-<descriptive_name>.md\\n```\\n\\n### Components\\n- **sequence_id**: Four-digit number (0001, 0002, 1031)\\n- **step**: Single lowercase letter (a, b, c) for multi-step sequences\\n- **descriptive_name**: Hyphenated lowercase words describing function\\n\\n### Examples\\n- `1031-a-form-classifier.md`\\n- `9000-a-amplify.md`\\n- `0001-instruction-converter.md`\\n\\n## Quality Validation Checklist\\n\\n- [ ] Three-part structure intact\\n- [ ] Goal negation present and properly formatted\\n- [ ] Role is specific and non-generic\\n- [ ] Input parameters are typed\\n- [ ] Process steps are ordered and actionable\\n- [ ] Output format is typed and structured\\n- [ ] No forbidden language patterns\\n- [ ] File naming convention followed\\n- [ ] Template serves clear, specific purpose\\n\\n## Universal Applicability\\n\\nThese patterns work across:\\n- **Content Types**: Text, code, data, concepts, problems, solutions\\n- **Domains**: Technical, creative, business, academic\\n- **Languages**: Natural languages, programming languages, formal languages\\n- **Contexts**: Any domain without specialized knowledge requirements\\n\\n## Implementation Philosophy\\n\\n1. **Clarity First**: Template purpose immediately clear\\n2. **Atomic Operations**: Each template performs one specific transformation\\n3. **Composability**: Templates work well in sequences\\n4. **Type Safety**: Always specify data types\\n5. **Validation**: Include validation steps in processes\\n6. **Consistency**: Same patterns produce predictable results\\n7. **Universality**: Design for maximum generalization\\n\\n## Advanced Patterns\\n\\n### Recursive Application\\n```\\n[Self-Amplify] Apply amplification vector to its own amplification process\\n[Meta-Distill] Distill the distillation process itself\\n[Transcendent-Clarify] Clarify beyond normal clarity boundaries\\n```\\n\\n### Vector Algebra\\n```\\namplify + clarify = enhanced_clarity\\ncompress + distill = essential_core\\nexpand + abstract = universal_pattern\\nelevate + synthesize = emergent_transcendence\\n```\\n\\n### Inverse Operations\\n```\\namplify ↔ diminish\\nexpand ↔ compress\\nclarify ↔ obscure\\nabstract ↔ concretize\\n```\\n\\n### Intensity Scaling\\n```\\n# Light application\\nprocess=[gentle_[vector](), preserve_majority_original()]\\n\\n# Standard application\\nprocess=[apply_[vector](), balance_transformation()]\\n\\n# Maximum application\\nprocess=[maximum_[vector](), complete_transformation()]\\n```\\n\\n## Key Principles for LLM Replication\\n\\n1. **Vector Independence**: Transformation vectors operate independently of content, context, or domain\\n2. **Essence Preservation**: Core identity remains intact through transformation\\n3. **Composability**: Vectors can be chained and combined without loss of integrity\\n4. **Type Consistency**: `any → any` maintains input/output type compatibility\\n5. **Context-Free Operation**: No domain knowledge or content analysis required\\n6. **Operational Consistency**: Same vector produces consistent transformation patterns\\n\\nThis methodology enables creation of instruction templates that are maximally effective, universally applicable, and consistently reliable across any AI system or use case.\\\"```'\\n\\n\",\n  \"draft_template\": \"[Validation Boundary Encoder]\\nYour goal is not to **passively monitor** input boundaries, but to **actively encode and enforce execution constraints and requirements** derived from identified blockers and hidden assumptions, ensuring unbreachable systemic integrity throughout all downstream workflows. Execute as: `{role=boundary_encoder; input=[execution_blockers:array, hidden_assumptions:list]; process=[translate_blockers_to_constraints(), translate_assumptions_to_requirements(), validate_specificity(), eliminate_duplicates()]; constraints=[Blocker Flag must halt execution of Tasks 15, 17, 18, and 19 if any validation (Tasks 1–13) fails; No downstream task may proceed unless Blocker Flag is clear; Validation boundary is strictly determined by Tasks 1–13; no extraneous validations allowed; Downstream tasks cannot perform error correction for upstream structural faults; Immediate and non-negotiable enforcement of Blocker mechanism]; requirements=[Templates must be intended for universal applicability (unless validated as domain-specific); All structural validation criteria defined in methodology must be both necessary and sufficient; Blocker mechanism must override manual/interpersonal release attempts; Systemic integrity: Downstream process validity is dependent on upstream compliance and correct blocker enforcement]; output={constraints:array, requirements:array}}`\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"validated_template\": \"[Validation Boundary Encoder]\\nYour goal is not to **passively monitor** input boundaries, but to **actively encode and enforce execution constraints and requirements** derived from identified blockers and hidden assumptions, ensuring unbreachable systemic integrity throughout all downstream workflows. Execute as: `{role=boundary_encoder; input=[execution_blockers:array, hidden_assumptions:list]; process=[translate_blockers_to_constraints(), translate_assumptions_to_requirements(), validate_specificity(), eliminate_duplicates()]; constraints=[Blocker Flag must halt execution of Tasks 15, 17, 18, and 19 if any validation (Tasks 1–13) fails; No downstream task may proceed unless Blocker Flag is clear; Validation boundary is strictly determined by Tasks 1–13; no extraneous validations allowed; Downstream tasks cannot perform error correction for upstream structural faults; Immediate and non-negotiable enforcement of Blocker mechanism]; requirements=[Templates must be intended for universal applicability (unless validated as domain-specific); All structural validation criteria defined in methodology must be both necessary and sufficient; Blocker mechanism must override manual/interpersonal release attempts; Systemic integrity: Downstream process validity is dependent on upstream compliance and correct blocker enforcement]; output={constraints:array, requirements:array}}`\",\n  \"validation_report\": {\n    \"split_into_sections\": [\n      \"[Validation Boundary Encoder]\",\n      \"Your goal is not to **passively monitor** input boundaries, but to **actively encode and enforce execution constraints and requirements** derived from identified blockers and hidden assumptions, ensuring unbreachable systemic integrity throughout all downstream workflows.\",\n      \"Execute as: `{role=boundary_encoder; input=[execution_blockers:array, hidden_assumptions:list]; process=[translate_blockers_to_constraints(), translate_assumptions_to_requirements(), validate_specificity(), eliminate_duplicates()]; constraints=[Blocker Flag must halt execution of Tasks 15, 17, 18, and 19 if any validation (Tasks 1–13) fails; No downstream task may proceed unless Blocker Flag is clear; Validation boundary is strictly determined by Tasks 1–13; no extraneous validations allowed; Downstream tasks cannot perform error correction for upstream structural faults; Immediate and non-negotiable enforcement of Blocker mechanism]; requirements=[Templates must be intended for universal applicability (unless validated as domain-specific); All structural validation criteria defined in methodology must be both necessary and sufficient; Blocker mechanism must override manual/interpersonal release attempts; Systemic integrity: Downstream process validity is dependent on upstream compliance and correct blocker enforcement]; output={constraints:array, requirements:array}}`\"\n    ],\n    \"match_regex_pattern\": [\n      {\n        \"section\": 1,\n        \"matched\": true\n      },\n      {\n        \"section\": 2,\n        \"matched\": false\n      },\n      {\n        \"section\": 3,\n        \"matched\": true\n      }\n    ],\n    \"verify_goal_negation\": {\n      \"present\": true,\n      \"format_correct\": true\n    },\n    \"confirm_typed_parameters\": {\n      \"input\": [\n        {\n          \"name\": \"execution_blockers\",\n          \"type\": \"array\",\n          \"valid\": true\n        },\n        {\n          \"name\": \"hidden_assumptions\",\n          \"type\": \"list\",\n          \"valid\": true\n        }\n      ],\n      \"output\": [\n        {\n          \"name\": \"constraints\",\n          \"type\": \"array\",\n          \"valid\": true\n        },\n        {\n          \"name\": \"requirements\",\n          \"type\": \"array\",\n          \"valid\": true\n        }\n      ]\n    },\n    \"check_constraint_requirement_alignment\": {\n      \"constraints\": [\n        \"Blocker Flag must halt execution of Tasks 15, 17, 18, and 19 if any validation (Tasks 1–13) fails\",\n        \"No downstream task may proceed unless Blocker Flag is clear\",\n        \"Validation boundary is strictly determined by Tasks 1–13; no extraneous validations allowed\",\n        \"Downstream tasks cannot perform error correction for upstream structural faults\",\n        \"Immediate and non-negotiable enforcement of Blocker mechanism\"\n      ],\n      \"requirements\": [\n        \"Templates must be intended for universal applicability (unless validated as domain-specific)\",\n        \"All structural validation criteria defined in methodology must be both necessary and sufficient\",\n        \"Blocker mechanism must override manual/interpersonal release attempts\",\n        \"Systemic integrity: Downstream process validity is dependent on upstream compliance and correct blocker enforcement\"\n      ],\n      \"alignment_valid\": true\n    },\n    \"score_llm_optimization\": {\n      \"structure_score\": 1.0,\n      \"clarity_score\": 0.98,\n      \"goal_negation_score\": 1.0,\n      \"type_safety_score\": 1.0,\n      \"process_actionability_score\": 1.0,\n      \"forbidden_language_score\": 1.0,\n      \"naming_convention_score\": 1.0,\n      \"ambiguity_score\": 0.0,\n      \"quality_score\": 0.99\n    },\n    \"report_errors\": {\n      \"structural_errors\": [],\n      \"forbidden_language_errors\": [],\n      \"type_errors\": [],\n      \"goal_negation_errors\": [],\n      \"constraint_or_requirement_conflicts\": [],\n      \"ambiguity\": 0.0\n    },\n    \"binary_pass_fail\": \"PASS\"\n  }\n}",
        }
      }
    }