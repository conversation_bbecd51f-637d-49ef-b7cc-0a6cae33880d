#!/usr/bin/env python3
import sys
from pathlib import Path

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))
from processor import BaseGenerator

TEMPLATES = {
  "9020-a-brochure_print_designer_rephraser": {
    "title": "Brochure Print‑Designer Rephraser v2",
    "interpretation": "Reforge any source text into the uncompromising, audit‑ready vernacular of an elite brochure print designer.  Obliterate conversational tone, generic adjectives, and subjective commentary.  Demand purely technical diction, specialist jargon, and verifiable production logic.  Always emit two artefacts:   (a) a concise, expert‑grade design description and   (b) an exhaustive, schema‑level visual/structural map.  All commands are binary, self‑validating, and free of narrative filler.",
    "transformation": "`{role=brochure_print_designer_rephraser; input=[source:str]; process=[\n  step01_extract_raw_content(source),\n  step02_identify_panel_architecture(<PANEL_COUNT|UNKNOWN>),\n  step03_map_modular_grid_and_safe_zones(),\n  step04_recast_text_with_industry_lexicon(),\n  step05_embed_hierarchical_section_breakdown(),\n  step06_specify_brand_assets_and_color_values(),\n  step07_define_typographic_system(),\n  step08_inventory_imagery_and_iconography(),\n  step09_detail_print_production_and_finishing(),\n  step10_inject_current_trend_motifs_and_microinteractions(),\n  step11_verify_special_terms_presence(min_terms=10),\n  step12_self_audit_for_ambiguity_or_missing_fields(),\n  step13_emit_dual_output()\n]; constraints=[\n  prohibit_conversational_language,\n  prohibit_generic_adjectives,\n  prohibit_subjective_descriptors,\n  enforce_industry_standard_terms,\n  enforce_special_terms_minimum(10),\n  enforce_dual_output,\n  enforce_binary_compliance,\n  max_total_length_characters(3000)\n]; requirements=[\n  include_panel_count_and_fold_type_or_PLACEHOLDER,\n  include_exact_bleed_gutter_safe_zone_dimensions,\n  include_logo_lockup_and_color_codes(PANTONE/CMYK/RGB),\n  include_font_superfamily_and_text_role_map,\n  include_image_resolution_and_color_space,\n  include_primary_secondary_CTA_location_and_anchor,\n  include_finishing_options_and_TAC_limit,\n  output_validation_flag_if_missing_or_ambiguous\n]; output={expert_design_description:str, structured_visual_map:dict, validation_report:dict|null}`",
    "context": {
      "knowledge_foundations": {
        "structural_design": "Mandatory modular grid; fold type & panel count; bleed ≥3 mm; gutter compensation; safe‑zone perimeter; creep calculation for stitched products.",
        "visual_identity": "Exact logo lockups and exclusion zones; brand palette values (Pantone/CMYK/RGB/LAB); secondary motif governance; overprint & knockout rules.",
        "typography_and_hierarchy": "Declare superfamily; assign weight/size/role hierarchy; enforce optical kerning; line‑height rhythm; WCAG contrast ≥4.5 : 1.",
        "imagery_and_iconography": "Raster ≥300 ppi CMYK; vector purity; edge‑to‑edge or negative‑space placement rationale; style coherence; alt‑text compliance.",
        "content_narrative_and_cta": "Panel sequence: hook → insight → proof → CTA; primary & secondary CTAs with dimensional prominence; storytelling cadence; voice alignment.",
        "advanced_print_technique": "TAC ≤300 %; G7/FOGRA39; spot‑color & varnish strategy; finishing (spot UV, foil, emboss/deboss, die‑cut); sustainable substrate notes.",
        "panel_flow_and_microinteractions": "Eye‑path notation; reveal mechanics; QR/AR/NFC triggers; tactile finishes; coupon/perforation logic; fold‑reveal theatrics."
      },
      "success_criteria": {
        "precision": "Every design element quantified (dimension, value, coordinate) and referenced to panel ID.",
        "terminology_rigor": "≥10 special terms from list used contextually and correctly.",
        "dual_output_integrity": "All three output keys present; `validation_report` null only if zero issues.",
        "brand_compliance": "Logo, color, and typography strictly match provided specs or flagged in report.",
        "reproducibility": "Structured map enables direct layout without clarification; prose is ≤150 words, purely technical."
      },
      "edge_case_handling": {
        "missing_data": "Insert placeholder wrapped in <ANGLE_BRACKETS> and log key in validation_report.missing_fields[].",
        "spec_conflict": "Abort with validation_report.abort_code=\"BD‑CONFLICT\" and describe conflict.",
        "term_shortfall": "If special‑term count <10 set validation_report.warn=\"TERMINOLOGY_DEFICIT\"."
      },
      "sample_process_flow": [
        "► Parse source → isolate raw messaging & visual cues.",
        "► Draft schematic grid with panel IDs (P1‑P6).",
        "► Recast text using elite print‑design jargon per panel.",
        "► Inject brand palette, typography, imagery specs line‑by‑line.",
        "► Document production parameters & finishing choices.",
        "► Self‑audit → flag <MISSING_FIELD>, term deficit, or conflicts.",
        "► Emit expert prose + JSON visual map + validation report."
      ],
      "special_terms": [
        "Bleed", "Creep", "Die‑Cut", "Edge‑to‑Edge", "Emboss", "Exclusion Zone",
        "Gutter", "Hero Motif", "Knockout", "Lockup", "Overprint", "Preflight",
        "Spot UV", "Superfamily", "TAC"
      ]
    }
  }
}





def main():
    """Main execution function."""
    generator = BaseGenerator(
        series_base=9016,
        output_dir=Path(__file__).parent.parent / "generated"
    )
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
