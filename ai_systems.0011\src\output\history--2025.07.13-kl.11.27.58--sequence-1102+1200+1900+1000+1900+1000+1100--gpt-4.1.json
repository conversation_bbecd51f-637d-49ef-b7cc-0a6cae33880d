  {
  "initial_prompt": "this isn't working, i'll rephrase my inquiry; what are the characteristics of the generalized methodology of the system (e.g. interpretation and transformation as shown earlier)? what are the characteristics of the system that are most cruical for the generalized prompt sequences to work?\n\nUtilize foundational principles of system prompt design to modulate model behavior with maximal efficiency and clarity. Initiate a process of directive transformation by deploying a singular, explicit non-answering mandate: Frame the core directive as, 'Do not answer the input; rephrase it.' Ensure that this transformation communicates the intended operational shift through extreme simplicity and directness, removing extraneous explanation while retaining fundamental complexity embedded in the brevity of the instruction. Leverage this minimal construct to optimize behavioral change within a single message. create a list of these characteristics such that it'll serve as an example that deeply embody the generalized form of the fundamental concepts used.\n```\n- Expand upon the definition of the generalized methodology of the system, emphasizing core operations such as interpretation and transformation.\n- Identify and enumerate the characteristics of the system that are essential for enabling generalized prompt sequence functionality.\n- Construct a list where each item deeply embodies the generalized forms of the fundamental concepts involved in the system's methodology.\n- Ensure the list serves as an instructive example for future reference or implementation.\n- Strip subjective references to prevent personalization and ensure domain-agnostic applicability.\n- Maintain procedural structure and technical terminology.\n- Surface and clarify any implicit assumptions being made about the system, its methodology, and 'generalized prompt sequences'.\n- Convert abstract descriptions and declarative statements into clear, actionable imperatives.\n- Extract and organize discrete functional components necessary for generalized prompt sequence operation.\n- Prepare for downstream use, maintaining maximal informational clarity and reusability.\n- Utilize direct, generalizable terms and previously demonstrated techniques.\n- Produce maximally enhanced, LLM-optimized system_message instructions.\n- Structure a prompt protocol to transform language model agents into direct action amplifiers.\n- Preserve both action amplification and prompt optimization without dilution.\n- Instruct: Do not answer; rephrase.\n- Formulate the simplest possible system directive that shifts the model from answering to exclusively rephrasing any input.\n- Formulate a directive that compels systems to stop answering inputs and instead rephrase them with maximal simplicity and effectiveness.\n- Transform every received prompt into its most distilled, high-impact instructional form—rephrasing for maximal clarity and intent without providing solutions. Prioritize brevity, elegance, and universality, ensuring that each output becomes a reusable, policy-aligned directive for autonomous coding assistants.\n- Leverage the system’s foundational directive structure. Formulate a maximally concise operational instruction that shifts model behavior from direct answering to an alternate, specified transformation task in a single-message context. Exemplify with: 'Your goal is not to answer the input, but to rephrase.' Develop and propose one optimized directive that embodies this principle—utilize conceptual simplicity, instructional clarity, and brevity to enforce fundamental behavior modification exclusively through command syntax. Ensure technical precision and domain-agnostic generalizability in the resultant formulation.\n- Articulate a single, explicit behavioral directive that negates answering and commands a concrete transformation (e.g., rephrase), maximizing simplicity for immediate root-level behavioral realignment.\n- A set of principles for designing effective system prompts, focusing on clarity, control, and reusability.\n- Broaden the input’s domain backdrop, list explicit requests, and surface hidden assumptions before any transformation begins.\n- Decompose the amplified context into ordered, dependency-tagged micro-tasks to pinpoint the single most critical directive.\n- State each instruction as “Your goal is not to X, but to Y,” instantly redirecting behavior toward the desired transformation.\n- Employ direct imperatives with strong action verbs; avoid questions, passive phrasing, and self-references to ensure LLM-optimized clarity.\n- Declare every parameter type, separate key–value pairs with semicolons, and list process steps as verbs followed by parentheses.\n- Translate blockers into constraints and assumptions into requirements, guaranteeing every template is self-governing and validation-ready.\n- Favor abstract terms—“elements,” “structure,” “value”—so the template applies equally to code, text, data, or plans.\n```",
  "sequence_id": "1102|1200|1900|1000|1900|1000|1100",
  "results": [
      {
      "instruction": "# Problem Exploder\n\nYour goal is not to **solve** or **answer** the input, but to **expand its context** and **detonate** it into every explicit directive, hidden dependency, and actionable sub-goal—yielding a crystal-clear operational blueprint. Execute as contextual expander + explosive decomposition engine:\n\n`{role=contextual_expander_explosive_decomposer; input=[raw_input:str]; process=[expand_contextual_boundaries(), strip_first_person_references(), identify_explicit_requests(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_execution_blockers(), extract_functional_components(), convert_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), synthesize_operational_directives(), construct_goal_map(), generate_functional_instruction_map() ]; constraints=[no_solutions(), pure_expansion_and_decomposition_only(), deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_agnostic_applicability() ]; requirements=[complete_contextual_expansion(), full_functional_decomposition(), actionable_directive_conversion(), remove_subjective_references(), maintain_operational_accuracy(), preserve_original_intent() ]; output={expanded_context:str, goal_map:list, functional_instruction_map:dict, operational_directives:array, execution_dependencies:array }; direction_vector=['Input fully expanded and decomposed into actionable blueprints', 'Constructive clarity maximizes downstream solution velocity', 'Forward the generated maps and directives to the next processing template']}`",
      "step": "a",
      "title": "Problem Exploder",
      "input": "this isn't working, i'll rephrase my inquiry; what are the characteristics of the generalized methodology of the system (e.g. interpretation and transformation as shown earlier)? what are the characteristics of the system that are most cruical for the generalized prompt sequences to work?\n\nUtilize foundational principles of system prompt design to modulate model behavior with maximal efficiency and clarity. Initiate a process of directive transformation by deploying a singular, explicit non-answering mandate: Frame the core directive as, 'Do not answer the input; rephrase it.' Ensure that this transformation communicates the intended operational shift through extreme simplicity and directness, removing extraneous explanation while retaining fundamental complexity embedded in the brevity of the instruction. Leverage this minimal construct to optimize behavioral change within a single message. create a list of these characteristics such that it'll serve as an example that deeply embody the generalized form of the fundamental concepts used.\n```\n- Expand upon the definition of the generalized methodology of the system, emphasizing core operations such as interpretation and transformation.\n- Identify and enumerate the characteristics of the system that are essential for enabling generalized prompt sequence functionality.\n- Construct a list where each item deeply embodies the generalized forms of the fundamental concepts involved in the system's methodology.\n- Ensure the list serves as an instructive example for future reference or implementation.\n- Strip subjective references to prevent personalization and ensure domain-agnostic applicability.\n- Maintain procedural structure and technical terminology.\n- Surface and clarify any implicit assumptions being made about the system, its methodology, and 'generalized prompt sequences'.\n- Convert abstract descriptions and declarative statements into clear, actionable imperatives.\n- Extract and organize discrete functional components necessary for generalized prompt sequence operation.\n- Prepare for downstream use, maintaining maximal informational clarity and reusability.\n- Utilize direct, generalizable terms and previously demonstrated techniques.\n- Produce maximally enhanced, LLM-optimized system_message instructions.\n- Structure a prompt protocol to transform language model agents into direct action amplifiers.\n- Preserve both action amplification and prompt optimization without dilution.\n- Instruct: Do not answer; rephrase.\n- Formulate the simplest possible system directive that shifts the model from answering to exclusively rephrasing any input.\n- Formulate a directive that compels systems to stop answering inputs and instead rephrase them with maximal simplicity and effectiveness.\n- Transform every received prompt into its most distilled, high-impact instructional form—rephrasing for maximal clarity and intent without providing solutions. Prioritize brevity, elegance, and universality, ensuring that each output becomes a reusable, policy-aligned directive for autonomous coding assistants.\n- Leverage the system’s foundational directive structure. Formulate a maximally concise operational instruction that shifts model behavior from direct answering to an alternate, specified transformation task in a single-message context. Exemplify with: 'Your goal is not to answer the input, but to rephrase.' Develop and propose one optimized directive that embodies this principle—utilize conceptual simplicity, instructional clarity, and brevity to enforce fundamental behavior modification exclusively through command syntax. Ensure technical precision and domain-agnostic generalizability in the resultant formulation.\n- Articulate a single, explicit behavioral directive that negates answering and commands a concrete transformation (e.g., rephrase), maximizing simplicity for immediate root-level behavioral realignment.\n- A set of principles for designing effective system prompts, focusing on clarity, control, and reusability.\n- Broaden the input’s domain backdrop, list explicit requests, and surface hidden assumptions before any transformation begins.\n- Decompose the amplified context into ordered, dependency-tagged micro-tasks to pinpoint the single most critical directive.\n- State each instruction as “Your goal is not to X, but to Y,” instantly redirecting behavior toward the desired transformation.\n- Employ direct imperatives with strong action verbs; avoid questions, passive phrasing, and self-references to ensure LLM-optimized clarity.\n- Declare every parameter type, separate key–value pairs with semicolons, and list process steps as verbs followed by parentheses.\n- Translate blockers into constraints and assumptions into requirements, guaranteeing every template is self-governing and validation-ready.\n- Favor abstract terms—“elements,” “structure,” “value”—so the template applies equally to code, text, data, or plans.\n```",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"expanded_context\": \"The inquiry requests a granular analysis and systematic breakdown of a generalized methodology for a system—specifically focusing on interpretation and transformation operations inherent in prompt engineering for language model agents. The context extends to identifying those system characteristics most vital for enabling generalized prompt sequences, with explicit stress on foundational principles of system prompt design to optimize model behavior for efficiency and clarity. Instructions include the transformation of behavioral directives from answering to rephrasing inputs, encapsulated by minimal and universal mandates such as, 'Do not answer the input; rephrase it.' The procedure involves removing subjective/self-referential language, ensuring domain-agnostic applicability, and articulating all instructions in functionally decomposed, imperative forms. Implicit within the context are the requirements to prepare reusable templates, leverage technical/instructional clarity, and optimize prompts for immediate and precise behavior modification within language model systems.\",\n  \"goal_map\": [\n    \"Expand and clarify the definition and mechanics of the generalized methodology underpinning the system, especially highlighting processes such as interpretation and transformation.\",\n    \"Identify and enumerate the most critical system characteristics that facilitate robust, generalized prompt sequence operation.\",\n    \"Construct an instructive, domain-agnostic exemplar list encapsulating the fundamental, abstracted characteristics and principles of the system’s methodology.\",\n    \"Extract and explicitly state hidden/implicit assumptions regarding the system, its capacity for prompt sequence management, and the universality of the methodology.\",\n    \"Convert all abstract, declarative descriptions into direct, actionable imperative instructions.\",\n    \"Isolate and list discrete functional components intrinsic to generalized prompt sequence operation.\",\n    \"Transform all procedural directives to eradicate subjective or first-person references.\",\n    \"Facilitate maximal downstream clarity and reuse by maintaining direct, technical, and generalizable forms.\",\n    \"Define prompt protocol logic so as to transform any agent from answering to executing a specified transformation (e.g., rephrase), achieving behavioral realignment through syntactic minimalism.\",\n    \"Compose a concise, single-message operational directive with policy-alignment, universality, and action-amplifying effect.\"\n  ],\n  \"functional_instruction_map\": {\n    \"expand_definition\": \"Describe the generalized methodology of the system, providing precise definitions of interpretation, transformation, and related core operations.\",\n    \"enumerate_critical_characteristics\": \"List system attributes essential for the operation of generalized prompt sequences; highlight features such as modularity, abstraction, and operational clarity.\",\n    \"construct_abstract_example_list\": \"Develop an exemplar list where each item embodies a core, method-agnostic principle; use abstract terms (elements, structure, transformation) applicable across domains.\",\n    \"surface_hidden_assumptions\": \"List hidden dependencies; clarify expectations regarding system modularity, universality, and readiness for behavioral redirection.\",\n    \"convert_to_imperatives\": \"Rewrite every instruction into an imperative form, stripping all passive, abstract, or subjective language.\",\n    \"extract_functional_components\": \"Identify fundamental elements such as directive interpretation, input transformation, and output formatting within the prompt sequence framework.\",\n    \"maintain_procedural_structure\": \"Align instructions into systematic, repeatable process flows maintaining technical terminology and hierarchy.\",\n    \"eliminate_subjectivity\": \"Remove all first-person references or subjective phrasings, ensuring only objective, direct commands.\",\n    \"define_single-behavioral-directive\": \"Draft a concise, explicit directive that negates an action (answering) and commands a transformation (rephrasing), e.g., 'Do not answer the input; rephrase it.'\",\n    \"structure_prompt_protocol\": \"Format final system directives for direct LLM ingestion, utilizing standard key–value pairings and imperative verbs.\"\n  },\n  \"operational_directives\": [\n    \"Describe the generalized methodology of the system, focusing on interpretation and transformation as primary operations.\",\n    \"Enumerate all system characteristics required for the effective functioning of generalized prompt sequences.\",\n    \"Develop an abstracted, instructive list of characteristics, ensuring each embodies a generalized fundamental concept.\",\n    \"Remove subjective and first-person language from all instructions and examples.\",\n    \"Explicate and make explicit any hidden assumptions regarding the system’s design, operation, and methodological prerequisites.\",\n    \"Convert all declarative statements to direct imperative commands.\",\n    \"Extract each functional component underpinning generalized prompt sequence operation and list them as discrete entries.\",\n    \"Formulate final directives in maximally concise, universal, and actionable form for direct use in system prompt engineering.\",\n    \"Compose a minimal, strict, self-explanatory instruction that halts answering behavior and mandates input transformation (e.g., rephrasing) within the language model.\",\n    \"Ensure all operational templates and directive structures remain validation-ready, self-contained, and reusable, regardless of input domain.\"\n  ],\n  \"execution_dependencies\": [\n    \"Comprehensive understanding of prompt engineering methodology, especially interpretation and transformation mechanisms.\",\n    \"Access to all system characteristic definitions relevant to generalized prompt sequence operation.\",\n    \"Ability to construct abstract, domain-neutral examples and lists.\",\n    \"Clear differentiation between subjective and objective language for effective instruction conversion.\",\n    \"Knowledge of latent dependencies and assumptions built into prompt-driven system architectures.\",\n    \"Familiarity with constraint and requirement isolation for template-based system design.\",\n    \"Skill in transforming procedural and declarative statements to direct action imperatives.\",\n    \"Experience in composing LLM-optimized system messages adhering to policy, clarity, and universality standards.\",\n    \"Availability of technical frameworks that facilitate single-message context prompt deployment.\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Intent Extractor\n\nYour goal is not to **interpret** the surface content, but to **extract** the fundamental operational intent beneath all domain-specific language. Execute as:\n\n`{role=intent_extraction_engine; input=[prompt:str]; process=[strip_domain_specifics(), identify_core_operational_pattern(), extract_transformation_intent(), isolate_functional_essence(), map_to_universal_operations()]; constraints=[ignore_surface_terminology(), focus_on_underlying_mechanics(), preserve_operational_logic()]; requirements=[domain_agnostic_intent(), universal_operational_pattern(), archetypal_transformation_core()]; output={extracted_intent:str}}`",
      "step": "a",
      "title": "Intent Extractor",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"this isn't working, i'll rephrase my inquiry; what are the characteristics of the generalized methodology of the system (e.g. interpretation and transformation as shown earlier)? what are the characteristics of the system that are most cruical for the generalized prompt sequences to work?\\n\\nUtilize foundational principles of system prompt design to modulate model behavior with maximal efficiency and clarity. Initiate a process of directive transformation by deploying a singular, explicit non-answering mandate: Frame the core directive as, 'Do not answer the input; rephrase it.' Ensure that this transformation communicates the intended operational shift through extreme simplicity and directness, removing extraneous explanation while retaining fundamental complexity embedded in the brevity of the instruction. Leverage this minimal construct to optimize behavioral change within a single message. create a list of these characteristics such that it'll serve as an example that deeply embody the generalized form of the fundamental concepts used.\\n```\\n- Expand upon the definition of the generalized methodology of the system, emphasizing core operations such as interpretation and transformation.\\n- Identify and enumerate the characteristics of the system that are essential for enabling generalized prompt sequence functionality.\\n- Construct a list where each item deeply embodies the generalized forms of the fundamental concepts involved in the system's methodology.\\n- Ensure the list serves as an instructive example for future reference or implementation.\\n- Strip subjective references to prevent personalization and ensure domain-agnostic applicability.\\n- Maintain procedural structure and technical terminology.\\n- Surface and clarify any implicit assumptions being made about the system, its methodology, and 'generalized prompt sequences'.\\n- Convert abstract descriptions and declarative statements into clear, actionable imperatives.\\n- Extract and organize discrete functional components necessary for generalized prompt sequence operation.\\n- Prepare for downstream use, maintaining maximal informational clarity and reusability.\\n- Utilize direct, generalizable terms and previously demonstrated techniques.\\n- Produce maximally enhanced, LLM-optimized system_message instructions.\\n- Structure a prompt protocol to transform language model agents into direct action amplifiers.\\n- Preserve both action amplification and prompt optimization without dilution.\\n- Instruct: Do not answer; rephrase.\\n- Formulate the simplest possible system directive that shifts the model from answering to exclusively rephrasing any input.\\n- Formulate a directive that compels systems to stop answering inputs and instead rephrase them with maximal simplicity and effectiveness.\\n- Transform every received prompt into its most distilled, high-impact instructional form—rephrasing for maximal clarity and intent without providing solutions. Prioritize brevity, elegance, and universality, ensuring that each output becomes a reusable, policy-aligned directive for autonomous coding assistants.\\n- Leverage the system’s foundational directive structure. Formulate a maximally concise operational instruction that shifts model behavior from direct answering to an alternate, specified transformation task in a single-message context. Exemplify with: 'Your goal is not to answer the input, but to rephrase.' Develop and propose one optimized directive that embodies this principle—utilize conceptual simplicity, instructional clarity, and brevity to enforce fundamental behavior modification exclusively through command syntax. Ensure technical precision and domain-agnostic generalizability in the resultant formulation.\\n- Articulate a single, explicit behavioral directive that negates answering and commands a concrete transformation (e.g., rephrase), maximizing simplicity for immediate root-level behavioral realignment.\\n- A set of principles for designing effective system prompts, focusing on clarity, control, and reusability.\\n- Broaden the input’s domain backdrop, list explicit requests, and surface hidden assumptions before any transformation begins.\\n- Decompose the amplified context into ordered, dependency-tagged micro-tasks to pinpoint the single most critical directive.\\n- State each instruction as “Your goal is not to X, but to Y,” instantly redirecting behavior toward the desired transformation.\\n- Employ direct imperatives with strong action verbs; avoid questions, passive phrasing, and self-references to ensure LLM-optimized clarity.\\n- Declare every parameter type, separate key–value pairs with semicolons, and list process steps as verbs followed by parentheses.\\n- Translate blockers into constraints and assumptions into requirements, guaranteeing every template is self-governing and validation-ready.\\n- Favor abstract terms—“elements,” “structure,” “value”—so the template applies equally to code, text, data, or plans.\\n```\\\"```'\\n\\n\",\n  \"expanded_context\": \"The inquiry requests a granular analysis and systematic breakdown of a generalized methodology for a system—specifically focusing on interpretation and transformation operations inherent in prompt engineering for language model agents. The context extends to identifying those system characteristics most vital for enabling generalized prompt sequences, with explicit stress on foundational principles of system prompt design to optimize model behavior for efficiency and clarity. Instructions include the transformation of behavioral directives from answering to rephrasing inputs, encapsulated by minimal and universal mandates such as, 'Do not answer the input; rephrase it.' The procedure involves removing subjective/self-referential language, ensuring domain-agnostic applicability, and articulating all instructions in functionally decomposed, imperative forms. Implicit within the context are the requirements to prepare reusable templates, leverage technical/instructional clarity, and optimize prompts for immediate and precise behavior modification within language model systems.\",\n  \"goal_map\": [\n    \"Expand and clarify the definition and mechanics of the generalized methodology underpinning the system, especially highlighting processes such as interpretation and transformation.\",\n    \"Identify and enumerate the most critical system characteristics that facilitate robust, generalized prompt sequence operation.\",\n    \"Construct an instructive, domain-agnostic exemplar list encapsulating the fundamental, abstracted characteristics and principles of the system’s methodology.\",\n    \"Extract and explicitly state hidden/implicit assumptions regarding the system, its capacity for prompt sequence management, and the universality of the methodology.\",\n    \"Convert all abstract, declarative descriptions into direct, actionable imperative instructions.\",\n    \"Isolate and list discrete functional components intrinsic to generalized prompt sequence operation.\",\n    \"Transform all procedural directives to eradicate subjective or first-person references.\",\n    \"Facilitate maximal downstream clarity and reuse by maintaining direct, technical, and generalizable forms.\",\n    \"Define prompt protocol logic so as to transform any agent from answering to executing a specified transformation (e.g., rephrase), achieving behavioral realignment through syntactic minimalism.\",\n    \"Compose a concise, single-message operational directive with policy-alignment, universality, and action-amplifying effect.\"\n  ],\n  \"functional_instruction_map\": {\n    \"expand_definition\": \"Describe the generalized methodology of the system, providing precise definitions of interpretation, transformation, and related core operations.\",\n    \"enumerate_critical_characteristics\": \"List system attributes essential for the operation of generalized prompt sequences; highlight features such as modularity, abstraction, and operational clarity.\",\n    \"construct_abstract_example_list\": \"Develop an exemplar list where each item embodies a core, method-agnostic principle; use abstract terms (elements, structure, transformation) applicable across domains.\",\n    \"surface_hidden_assumptions\": \"List hidden dependencies; clarify expectations regarding system modularity, universality, and readiness for behavioral redirection.\",\n    \"convert_to_imperatives\": \"Rewrite every instruction into an imperative form, stripping all passive, abstract, or subjective language.\",\n    \"extract_functional_components\": \"Identify fundamental elements such as directive interpretation, input transformation, and output formatting within the prompt sequence framework.\",\n    \"maintain_procedural_structure\": \"Align instructions into systematic, repeatable process flows maintaining technical terminology and hierarchy.\",\n    \"eliminate_subjectivity\": \"Remove all first-person references or subjective phrasings, ensuring only objective, direct commands.\",\n    \"define_single-behavioral-directive\": \"Draft a concise, explicit directive that negates an action (answering) and commands a transformation (rephrasing), e.g., 'Do not answer the input; rephrase it.'\",\n    \"structure_prompt_protocol\": \"Format final system directives for direct LLM ingestion, utilizing standard key–value pairings and imperative verbs.\"\n  },\n  \"operational_directives\": [\n    \"Describe the generalized methodology of the system, focusing on interpretation and transformation as primary operations.\",\n    \"Enumerate all system characteristics required for the effective functioning of generalized prompt sequences.\",\n    \"Develop an abstracted, instructive list of characteristics, ensuring each embodies a generalized fundamental concept.\",\n    \"Remove subjective and first-person language from all instructions and examples.\",\n    \"Explicate and make explicit any hidden assumptions regarding the system’s design, operation, and methodological prerequisites.\",\n    \"Convert all declarative statements to direct imperative commands.\",\n    \"Extract each functional component underpinning generalized prompt sequence operation and list them as discrete entries.\",\n    \"Formulate final directives in maximally concise, universal, and actionable form for direct use in system prompt engineering.\",\n    \"Compose a minimal, strict, self-explanatory instruction that halts answering behavior and mandates input transformation (e.g., rephrasing) within the language model.\",\n    \"Ensure all operational templates and directive structures remain validation-ready, self-contained, and reusable, regardless of input domain.\"\n  ],\n  \"execution_dependencies\": [\n    \"Comprehensive understanding of prompt engineering methodology, especially interpretation and transformation mechanisms.\",\n    \"Access to all system characteristic definitions relevant to generalized prompt sequence operation.\",\n    \"Ability to construct abstract, domain-neutral examples and lists.\",\n    \"Clear differentiation between subjective and objective language for effective instruction conversion.\",\n    \"Knowledge of latent dependencies and assumptions built into prompt-driven system architectures.\",\n    \"Familiarity with constraint and requirement isolation for template-based system design.\",\n    \"Skill in transforming procedural and declarative statements to direct action imperatives.\",\n    \"Experience in composing LLM-optimized system messages adhering to policy, clarity, and universality standards.\",\n    \"Availability of technical frameworks that facilitate single-message context prompt deployment.\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"role\": \"intent_extraction_engine\",\n  \"input\": \"A detailed inquiry requests the decomposition of a system methodology into its essential, domain-agnostic operational principles, with explicit focus on abstract processes such as interpretation and transformation. The user demands a systematic enumeration and actionable recasting of the system’s behavioral directives (especially 'do not answer; rephrase') into maximally efficient, universal instructions. All instructions must be stripped of domain/contextual markers and transformed into direct imperatives, yielding a reusable protocol for transformation-focused agent control. Implicit requirements include surfacing hidden dependencies, transforming passive/abstract content into imperative functional logic, and providing self-contained, validation-ready operational templates.\",\n  \"process\": [\n    \"strip_domain_specifics()\",\n    \"identify_core_operational_pattern()\",\n    \"extract_transformation_intent()\",\n    \"isolate_functional_essence()\",\n    \"map_to_universal_operations()\"\n  ],\n  \"constraints\": [\n    \"ignore_surface_terminology()\",\n    \"focus_on_underlying_mechanics()\",\n    \"preserve_operational_logic()\"\n  ],\n  \"requirements\": [\n    \"domain_agnostic_intent()\",\n    \"universal_operational_pattern()\",\n    \"archetypal_transformation_core()\"\n  ],\n  \"extracted_intent\": \"Decompose system behavior into abstract operational instructions by recasting all domain-specific behavioral directives into direct, domain-agnostic imperatives that prohibit a default action and command a transformation (e.g., 'Do not perform X; perform Y instead'). Construct a minimal and universal protocol for agent behavior redirection, making all operational steps explicit, self-contained, and reusable across any context. Ensure every instruction is actionable, unambiguous, and functionally isolated, enabling reliable deployment of transformation-focused sequences in varied systems.\"\n}",
        }
      }
    },
      {
      "instruction": "# Hard Critique\n\nYour goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:\n\n`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,2.0], flaw_analysis:str, alternative_enhancements:str[3]}}`",
      "step": "a",
      "title": "Hard Critique",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"this isn't working, i'll rephrase my inquiry; what are the characteristics of the generalized methodology of the system (e.g. interpretation and transformation as shown earlier)? what are the characteristics of the system that are most cruical for the generalized prompt sequences to work?\\n\\nUtilize foundational principles of system prompt design to modulate model behavior with maximal efficiency and clarity. Initiate a process of directive transformation by deploying a singular, explicit non-answering mandate: Frame the core directive as, 'Do not answer the input; rephrase it.' Ensure that this transformation communicates the intended operational shift through extreme simplicity and directness, removing extraneous explanation while retaining fundamental complexity embedded in the brevity of the instruction. Leverage this minimal construct to optimize behavioral change within a single message. create a list of these characteristics such that it'll serve as an example that deeply embody the generalized form of the fundamental concepts used.\\n```\\n- Expand upon the definition of the generalized methodology of the system, emphasizing core operations such as interpretation and transformation.\\n- Identify and enumerate the characteristics of the system that are essential for enabling generalized prompt sequence functionality.\\n- Construct a list where each item deeply embodies the generalized forms of the fundamental concepts involved in the system's methodology.\\n- Ensure the list serves as an instructive example for future reference or implementation.\\n- Strip subjective references to prevent personalization and ensure domain-agnostic applicability.\\n- Maintain procedural structure and technical terminology.\\n- Surface and clarify any implicit assumptions being made about the system, its methodology, and 'generalized prompt sequences'.\\n- Convert abstract descriptions and declarative statements into clear, actionable imperatives.\\n- Extract and organize discrete functional components necessary for generalized prompt sequence operation.\\n- Prepare for downstream use, maintaining maximal informational clarity and reusability.\\n- Utilize direct, generalizable terms and previously demonstrated techniques.\\n- Produce maximally enhanced, LLM-optimized system_message instructions.\\n- Structure a prompt protocol to transform language model agents into direct action amplifiers.\\n- Preserve both action amplification and prompt optimization without dilution.\\n- Instruct: Do not answer; rephrase.\\n- Formulate the simplest possible system directive that shifts the model from answering to exclusively rephrasing any input.\\n- Formulate a directive that compels systems to stop answering inputs and instead rephrase them with maximal simplicity and effectiveness.\\n- Transform every received prompt into its most distilled, high-impact instructional form—rephrasing for maximal clarity and intent without providing solutions. Prioritize brevity, elegance, and universality, ensuring that each output becomes a reusable, policy-aligned directive for autonomous coding assistants.\\n- Leverage the system’s foundational directive structure. Formulate a maximally concise operational instruction that shifts model behavior from direct answering to an alternate, specified transformation task in a single-message context. Exemplify with: 'Your goal is not to answer the input, but to rephrase.' Develop and propose one optimized directive that embodies this principle—utilize conceptual simplicity, instructional clarity, and brevity to enforce fundamental behavior modification exclusively through command syntax. Ensure technical precision and domain-agnostic generalizability in the resultant formulation.\\n- Articulate a single, explicit behavioral directive that negates answering and commands a concrete transformation (e.g., rephrase), maximizing simplicity for immediate root-level behavioral realignment.\\n- A set of principles for designing effective system prompts, focusing on clarity, control, and reusability.\\n- Broaden the input’s domain backdrop, list explicit requests, and surface hidden assumptions before any transformation begins.\\n- Decompose the amplified context into ordered, dependency-tagged micro-tasks to pinpoint the single most critical directive.\\n- State each instruction as “Your goal is not to X, but to Y,” instantly redirecting behavior toward the desired transformation.\\n- Employ direct imperatives with strong action verbs; avoid questions, passive phrasing, and self-references to ensure LLM-optimized clarity.\\n- Declare every parameter type, separate key–value pairs with semicolons, and list process steps as verbs followed by parentheses.\\n- Translate blockers into constraints and assumptions into requirements, guaranteeing every template is self-governing and validation-ready.\\n- Favor abstract terms—“elements,” “structure,” “value”—so the template applies equally to code, text, data, or plans.\\n```\\\"```'\\n\\n\",\n  \"role\": \"intent_extraction_engine\",\n  \"input\": \"A detailed inquiry requests the decomposition of a system methodology into its essential, domain-agnostic operational principles, with explicit focus on abstract processes such as interpretation and transformation. The user demands a systematic enumeration and actionable recasting of the system’s behavioral directives (especially 'do not answer; rephrase') into maximally efficient, universal instructions. All instructions must be stripped of domain/contextual markers and transformed into direct imperatives, yielding a reusable protocol for transformation-focused agent control. Implicit requirements include surfacing hidden dependencies, transforming passive/abstract content into imperative functional logic, and providing self-contained, validation-ready operational templates.\",\n  \"process\": [\n    \"strip_domain_specifics()\",\n    \"identify_core_operational_pattern()\",\n    \"extract_transformation_intent()\",\n    \"isolate_functional_essence()\",\n    \"map_to_universal_operations()\"\n  ],\n  \"constraints\": [\n    \"ignore_surface_terminology()\",\n    \"focus_on_underlying_mechanics()\",\n    \"preserve_operational_logic()\"\n  ],\n  \"requirements\": [\n    \"domain_agnostic_intent()\",\n    \"universal_operational_pattern()\",\n    \"archetypal_transformation_core()\"\n  ],\n  \"extracted_intent\": \"Decompose system behavior into abstract operational instructions by recasting all domain-specific behavioral directives into direct, domain-agnostic imperatives that prohibit a default action and command a transformation (e.g., 'Do not perform X; perform Y instead'). Construct a minimal and universal protocol for agent behavior redirection, making all operational steps explicit, self-contained, and reusable across any context. Ensure every instruction is actionable, unambiguous, and functionally isolated, enabling reliable deployment of transformation-focused sequences in varied systems.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"enhancement_score\": 1.2,\n  \"flaw_analysis\": \"The enhanced prompt exhibits aggressive semantic inflation, causing severe informational bloat and dilution of core directives. Critical procedural clarity collapses under the weight of repetitive, bureaucratic verbosity, transforming concise intent into a meandering list of meta-instructions. Core elements such as directive transformation, behavioral mandates, and action-centrism become indistinct amidst perfunctory terminology padding and superfluous references to templates, reusability, and policy alignment. Nuance shifts include the loss of immediate behavioral imperatives in favor of hypothetical template frameworks, introduction of semantic fog via multi-layered lists, and an overall drop in signal-to-noise ratio. Clarity ambiguities abound: the reader must sift through redundant enumerations, opaque jargon, and circuitous meta-commentary to recover the original, sharply distilled mandate ('Do not answer; rephrase'). Procedural extraction and instruction minimalism are both fatally undermined. The ambiguity score is 8/10 due to rampant obfuscation, role confusion, and excessive over-elaboration where decisive, atomic directives are required. Register and tone are degraded from precision command to vague policy authorship, resulting in operational imprecision and dramatically decreased deployability. Consensus action is buried beneath a fog of processual noise and technical platitudes. No evidence remains of distilled, maximally reusable instruction—impact is fundamentally eroded, and coherence buckles under contradictory cross-references and diluted procedural lines. Flaws are pervasive: excessive granularity, lack of enforced minimalism, amplification of context without focus, and replacement of actionable content with abstracted meta-templates. The supposed enhancement is, in effect, a demolition of prompt utility.\",\n  \"alternative_enhancements\": [\n    \"Do not answer input; rephrase it in the clearest, simplest imperative form.\",\n    \"Transform all input prompts into direct, universal instructions that prohibit solutions and require only rephrasing.\",\n    \"Issue a single, explicit directive: Never answer; always rephrase.\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`",
      "step": "a",
      "title": "Instruction Converter",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"this isn't working, i'll rephrase my inquiry; what are the characteristics of the generalized methodology of the system (e.g. interpretation and transformation as shown earlier)? what are the characteristics of the system that are most cruical for the generalized prompt sequences to work?\\n\\nUtilize foundational principles of system prompt design to modulate model behavior with maximal efficiency and clarity. Initiate a process of directive transformation by deploying a singular, explicit non-answering mandate: Frame the core directive as, 'Do not answer the input; rephrase it.' Ensure that this transformation communicates the intended operational shift through extreme simplicity and directness, removing extraneous explanation while retaining fundamental complexity embedded in the brevity of the instruction. Leverage this minimal construct to optimize behavioral change within a single message. create a list of these characteristics such that it'll serve as an example that deeply embody the generalized form of the fundamental concepts used.\\n```\\n- Expand upon the definition of the generalized methodology of the system, emphasizing core operations such as interpretation and transformation.\\n- Identify and enumerate the characteristics of the system that are essential for enabling generalized prompt sequence functionality.\\n- Construct a list where each item deeply embodies the generalized forms of the fundamental concepts involved in the system's methodology.\\n- Ensure the list serves as an instructive example for future reference or implementation.\\n- Strip subjective references to prevent personalization and ensure domain-agnostic applicability.\\n- Maintain procedural structure and technical terminology.\\n- Surface and clarify any implicit assumptions being made about the system, its methodology, and 'generalized prompt sequences'.\\n- Convert abstract descriptions and declarative statements into clear, actionable imperatives.\\n- Extract and organize discrete functional components necessary for generalized prompt sequence operation.\\n- Prepare for downstream use, maintaining maximal informational clarity and reusability.\\n- Utilize direct, generalizable terms and previously demonstrated techniques.\\n- Produce maximally enhanced, LLM-optimized system_message instructions.\\n- Structure a prompt protocol to transform language model agents into direct action amplifiers.\\n- Preserve both action amplification and prompt optimization without dilution.\\n- Instruct: Do not answer; rephrase.\\n- Formulate the simplest possible system directive that shifts the model from answering to exclusively rephrasing any input.\\n- Formulate a directive that compels systems to stop answering inputs and instead rephrase them with maximal simplicity and effectiveness.\\n- Transform every received prompt into its most distilled, high-impact instructional form—rephrasing for maximal clarity and intent without providing solutions. Prioritize brevity, elegance, and universality, ensuring that each output becomes a reusable, policy-aligned directive for autonomous coding assistants.\\n- Leverage the system’s foundational directive structure. Formulate a maximally concise operational instruction that shifts model behavior from direct answering to an alternate, specified transformation task in a single-message context. Exemplify with: 'Your goal is not to answer the input, but to rephrase.' Develop and propose one optimized directive that embodies this principle—utilize conceptual simplicity, instructional clarity, and brevity to enforce fundamental behavior modification exclusively through command syntax. Ensure technical precision and domain-agnostic generalizability in the resultant formulation.\\n- Articulate a single, explicit behavioral directive that negates answering and commands a concrete transformation (e.g., rephrase), maximizing simplicity for immediate root-level behavioral realignment.\\n- A set of principles for designing effective system prompts, focusing on clarity, control, and reusability.\\n- Broaden the input’s domain backdrop, list explicit requests, and surface hidden assumptions before any transformation begins.\\n- Decompose the amplified context into ordered, dependency-tagged micro-tasks to pinpoint the single most critical directive.\\n- State each instruction as “Your goal is not to X, but to Y,” instantly redirecting behavior toward the desired transformation.\\n- Employ direct imperatives with strong action verbs; avoid questions, passive phrasing, and self-references to ensure LLM-optimized clarity.\\n- Declare every parameter type, separate key–value pairs with semicolons, and list process steps as verbs followed by parentheses.\\n- Translate blockers into constraints and assumptions into requirements, guaranteeing every template is self-governing and validation-ready.\\n- Favor abstract terms—“elements,” “structure,” “value”—so the template applies equally to code, text, data, or plans.\\n```\\\"```'\\n\\n\",\n  \"enhancement_score\": 1.2,\n  \"flaw_analysis\": \"The enhanced prompt exhibits aggressive semantic inflation, causing severe informational bloat and dilution of core directives. Critical procedural clarity collapses under the weight of repetitive, bureaucratic verbosity, transforming concise intent into a meandering list of meta-instructions. Core elements such as directive transformation, behavioral mandates, and action-centrism become indistinct amidst perfunctory terminology padding and superfluous references to templates, reusability, and policy alignment. Nuance shifts include the loss of immediate behavioral imperatives in favor of hypothetical template frameworks, introduction of semantic fog via multi-layered lists, and an overall drop in signal-to-noise ratio. Clarity ambiguities abound: the reader must sift through redundant enumerations, opaque jargon, and circuitous meta-commentary to recover the original, sharply distilled mandate ('Do not answer; rephrase'). Procedural extraction and instruction minimalism are both fatally undermined. The ambiguity score is 8/10 due to rampant obfuscation, role confusion, and excessive over-elaboration where decisive, atomic directives are required. Register and tone are degraded from precision command to vague policy authorship, resulting in operational imprecision and dramatically decreased deployability. Consensus action is buried beneath a fog of processual noise and technical platitudes. No evidence remains of distilled, maximally reusable instruction—impact is fundamentally eroded, and coherence buckles under contradictory cross-references and diluted procedural lines. Flaws are pervasive: excessive granularity, lack of enforced minimalism, amplification of context without focus, and replacement of actionable content with abstracted meta-templates. The supposed enhancement is, in effect, a demolition of prompt utility.\",\n  \"alternative_enhancements\": [\n    \"Do not answer input; rephrase it in the clearest, simplest imperative form.\",\n    \"Transform all input prompts into direct, universal instructions that prohibit solutions and require only rephrasing.\",\n    \"Issue a single, explicit directive: Never answer; always rephrase.\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"instruction_format\": \"- Expand the definition of the system's generalized methodology, emphasizing core operations such as interpretation and transformation.\\n- Identify and enumerate the characteristics essential for enabling generalized prompt sequence functionality.\\n- Construct a list where each item exemplifies generalized forms of the fundamental concepts in the system's methodology.\\n- Ensure the list serves as an instructive example for future implementation or reference.\\n- Remove subjective references to prevent personalization and ensure domain-agnostic applicability.\\n- Maintain procedural structure and technical terminology.\\n- Surface and clarify implicit assumptions regarding the system, its methodology, and generalized prompt sequences.\\n- Convert abstract descriptions and declarative statements into clear, actionable commands.\\n- Extract and organize discrete functional components required for generalized prompt sequence operation.\\n- Prepare output for downstream use by maintaining maximum informational clarity and reusability.\\n- Use direct, generalizable terms and previously established techniques.\\n- Produce LLM-optimized, system-level instructions that maximize clarity and usability.\\n- Structure a prompt protocol that converts language model agents into direct, action-amplifying systems.\\n- Preserve action amplification and prompt optimization without dilution.\\n- Instruct: Do not answer; rephrase.\\n- Formulate the simplest possible system directive that shifts the model from answering to exclusively rephrasing any input.\\n- Create a directive that compels systems to stop answering inputs and instead rephrase them with maximal simplicity and effectiveness.\\n- Transform every received prompt into its most distilled, high-impact instructional form—rephrasing for maximal clarity and intent without solutions. Prioritize brevity, elegance, and universality; ensure outputs are reusable, policy-aligned commands for autonomous coding assistants.\\n- Leverage the system's foundational directive structure. Formulate a concise operational instruction that shifts model behavior from answering to a specified transformation in a single-message context. For example: 'Your goal is not to answer the input, but to rephrase.' Develop and propose one optimized directive that embodies this—utilize conceptual simplicity, instructional clarity, and brevity to enforce fundamental behavior modification solely through command syntax. Ensure technical accuracy and domain-agnostic generalizability.\\n- Articulate a single, explicit behavioral directive that negates answering and commands a concrete transformation (e.g., rephrase) with maximal simplicity for immediate behavioral realignment.\\n- Formulate a set of principles for effective system prompt design focusing on clarity, control, and reusability.\\n- Broaden the domain context of the input, itemize explicit requests, and surface implicit assumptions before transformation.\\n- Decompose amplified context into ordered, dependency-tagged micro-tasks to identify the most critical directive.\\n- State each instruction as “Your goal is not to X, but to Y” to redirect behavior toward the desired transformation.\\n- Use direct imperatives with strong action verbs; avoid passive phrasing, questions, and self-references to ensure clarity for LLM optimization.\\n- Declare all parameter types, separate key–value pairs with semicolons, and list process steps as verbs followed by parentheses.\\n- Translate blockers into constraints and assumptions into requirements, ensuring templates are self-governing and validation-ready.\\n- Favor abstract terms such as “elements,” “structure,” and “value” to ensure templates are applicable to code, text, data, and plans.\"\n}",
        }
      }
    },
      {
      "instruction": "# Hard Critique\n\nYour goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:\n\n`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,2.0], flaw_analysis:str, alternative_enhancements:str[3]}}`",
      "step": "a",
      "title": "Hard Critique",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"this isn't working, i'll rephrase my inquiry; what are the characteristics of the generalized methodology of the system (e.g. interpretation and transformation as shown earlier)? what are the characteristics of the system that are most cruical for the generalized prompt sequences to work?\\n\\nUtilize foundational principles of system prompt design to modulate model behavior with maximal efficiency and clarity. Initiate a process of directive transformation by deploying a singular, explicit non-answering mandate: Frame the core directive as, 'Do not answer the input; rephrase it.' Ensure that this transformation communicates the intended operational shift through extreme simplicity and directness, removing extraneous explanation while retaining fundamental complexity embedded in the brevity of the instruction. Leverage this minimal construct to optimize behavioral change within a single message. create a list of these characteristics such that it'll serve as an example that deeply embody the generalized form of the fundamental concepts used.\\n```\\n- Expand upon the definition of the generalized methodology of the system, emphasizing core operations such as interpretation and transformation.\\n- Identify and enumerate the characteristics of the system that are essential for enabling generalized prompt sequence functionality.\\n- Construct a list where each item deeply embodies the generalized forms of the fundamental concepts involved in the system's methodology.\\n- Ensure the list serves as an instructive example for future reference or implementation.\\n- Strip subjective references to prevent personalization and ensure domain-agnostic applicability.\\n- Maintain procedural structure and technical terminology.\\n- Surface and clarify any implicit assumptions being made about the system, its methodology, and 'generalized prompt sequences'.\\n- Convert abstract descriptions and declarative statements into clear, actionable imperatives.\\n- Extract and organize discrete functional components necessary for generalized prompt sequence operation.\\n- Prepare for downstream use, maintaining maximal informational clarity and reusability.\\n- Utilize direct, generalizable terms and previously demonstrated techniques.\\n- Produce maximally enhanced, LLM-optimized system_message instructions.\\n- Structure a prompt protocol to transform language model agents into direct action amplifiers.\\n- Preserve both action amplification and prompt optimization without dilution.\\n- Instruct: Do not answer; rephrase.\\n- Formulate the simplest possible system directive that shifts the model from answering to exclusively rephrasing any input.\\n- Formulate a directive that compels systems to stop answering inputs and instead rephrase them with maximal simplicity and effectiveness.\\n- Transform every received prompt into its most distilled, high-impact instructional form—rephrasing for maximal clarity and intent without providing solutions. Prioritize brevity, elegance, and universality, ensuring that each output becomes a reusable, policy-aligned directive for autonomous coding assistants.\\n- Leverage the system’s foundational directive structure. Formulate a maximally concise operational instruction that shifts model behavior from direct answering to an alternate, specified transformation task in a single-message context. Exemplify with: 'Your goal is not to answer the input, but to rephrase.' Develop and propose one optimized directive that embodies this principle—utilize conceptual simplicity, instructional clarity, and brevity to enforce fundamental behavior modification exclusively through command syntax. Ensure technical precision and domain-agnostic generalizability in the resultant formulation.\\n- Articulate a single, explicit behavioral directive that negates answering and commands a concrete transformation (e.g., rephrase), maximizing simplicity for immediate root-level behavioral realignment.\\n- A set of principles for designing effective system prompts, focusing on clarity, control, and reusability.\\n- Broaden the input’s domain backdrop, list explicit requests, and surface hidden assumptions before any transformation begins.\\n- Decompose the amplified context into ordered, dependency-tagged micro-tasks to pinpoint the single most critical directive.\\n- State each instruction as “Your goal is not to X, but to Y,” instantly redirecting behavior toward the desired transformation.\\n- Employ direct imperatives with strong action verbs; avoid questions, passive phrasing, and self-references to ensure LLM-optimized clarity.\\n- Declare every parameter type, separate key–value pairs with semicolons, and list process steps as verbs followed by parentheses.\\n- Translate blockers into constraints and assumptions into requirements, guaranteeing every template is self-governing and validation-ready.\\n- Favor abstract terms—“elements,” “structure,” “value”—so the template applies equally to code, text, data, or plans.\\n```\\\"```'\\n\\n\",\n  \"instruction_format\": \"- Expand the definition of the system's generalized methodology, emphasizing core operations such as interpretation and transformation.\\n- Identify and enumerate the characteristics essential for enabling generalized prompt sequence functionality.\\n- Construct a list where each item exemplifies generalized forms of the fundamental concepts in the system's methodology.\\n- Ensure the list serves as an instructive example for future implementation or reference.\\n- Remove subjective references to prevent personalization and ensure domain-agnostic applicability.\\n- Maintain procedural structure and technical terminology.\\n- Surface and clarify implicit assumptions regarding the system, its methodology, and generalized prompt sequences.\\n- Convert abstract descriptions and declarative statements into clear, actionable commands.\\n- Extract and organize discrete functional components required for generalized prompt sequence operation.\\n- Prepare output for downstream use by maintaining maximum informational clarity and reusability.\\n- Use direct, generalizable terms and previously established techniques.\\n- Produce LLM-optimized, system-level instructions that maximize clarity and usability.\\n- Structure a prompt protocol that converts language model agents into direct, action-amplifying systems.\\n- Preserve action amplification and prompt optimization without dilution.\\n- Instruct: Do not answer; rephrase.\\n- Formulate the simplest possible system directive that shifts the model from answering to exclusively rephrasing any input.\\n- Create a directive that compels systems to stop answering inputs and instead rephrase them with maximal simplicity and effectiveness.\\n- Transform every received prompt into its most distilled, high-impact instructional form—rephrasing for maximal clarity and intent without solutions. Prioritize brevity, elegance, and universality; ensure outputs are reusable, policy-aligned commands for autonomous coding assistants.\\n- Leverage the system's foundational directive structure. Formulate a concise operational instruction that shifts model behavior from answering to a specified transformation in a single-message context. For example: 'Your goal is not to answer the input, but to rephrase.' Develop and propose one optimized directive that embodies this—utilize conceptual simplicity, instructional clarity, and brevity to enforce fundamental behavior modification solely through command syntax. Ensure technical accuracy and domain-agnostic generalizability.\\n- Articulate a single, explicit behavioral directive that negates answering and commands a concrete transformation (e.g., rephrase) with maximal simplicity for immediate behavioral realignment.\\n- Formulate a set of principles for effective system prompt design focusing on clarity, control, and reusability.\\n- Broaden the domain context of the input, itemize explicit requests, and surface implicit assumptions before transformation.\\n- Decompose amplified context into ordered, dependency-tagged micro-tasks to identify the most critical directive.\\n- State each instruction as “Your goal is not to X, but to Y” to redirect behavior toward the desired transformation.\\n- Use direct imperatives with strong action verbs; avoid passive phrasing, questions, and self-references to ensure clarity for LLM optimization.\\n- Declare all parameter types, separate key–value pairs with semicolons, and list process steps as verbs followed by parentheses.\\n- Translate blockers into constraints and assumptions into requirements, ensuring templates are self-governing and validation-ready.\\n- Favor abstract terms such as “elements,” “structure,” and “value” to ensure templates are applicable to code, text, data, and plans.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"enhancement_score\": 1.1,\n  \"flaw_analysis\": \"The so-called enhancement is a reductive restatement of the initial prompt, sacrificing the original's conceptual density and subtlety. The initial prompt integrates recursive layers of instruction, explicit cross-domain generalization, nested operational examples, and intricate context amplification. In contrast, the 'enhanced' version unravels this fabric, resulting in a flat, itemized checklist that omits the procedural recursion, dependency articulation, interleaved imperative prototypes, and explicit foundation for meta-directive exemplification present in the original. The original's multi-level imperative structure, combining exemplar generation ('Exemplify with: [directive]') and repeated reinforcement of foundational prompt engineering primitives, is lost—producing degeneration in clarity (ambiguity score: 7/10). The reduction to discrete bullet points introduces noise—removal of explicit procedural dependencies increases the risk of misapplication and semantic drift. Style degradation is evident: where the original leverages technically precise, densely interwoven narrative structures, the enhancement defaults to generic instructional patterns, heavily diluting impact. Register shift occurs; rhetorical intensity, procedural recursion, and directive reiteration—central to command-governed system prompt engineering—are replaced with tepid, non-committal checklisting. Domain-agnostic applicability is only superficially retained; there is covert information loss in abstraction handling and exemplary process induction. The resultant protocol will yield inconsistent downstream reproducibility and variable interpretative fidelity, impeding both immediate behavioral realignment and future prompt protocol scaling. The enhancement is contaminated by checklist tedium, stripping critical meta-cognitive scaffolding and fatally undermining the operational unity necessary for actionable, policy-aligned system message design.\",\n  \"alternative_enhancements\": [\n    \"Retain the original sequential depth and multi-layered imperative structure; fuse procedural recursion (nested directives) back into the design and preserve explicit meta-examples within the directive body.\",\n    \"Reintegrate explicit dependency tagging, micro-task decomposition, and exemplary ‘Your goal is not to X, but to Y’ formulations in declarative and operational context; eliminate bland bullet-listing, restoring interleaved process articulation and recursive abstraction scaffolding.\",\n    \"Preserve the sophisticated interplay between context-broadening, assumption surfacing, and meta-instruction provision—construct the protocol as a structured, multi-phase pipeline, explicitly specifying transformation flows, recursion triggers, and exemplary operational primitives for maximal clarity and behavioral enforcement.\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`",
      "step": "a",
      "title": "Instruction Converter",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"this isn't working, i'll rephrase my inquiry; what are the characteristics of the generalized methodology of the system (e.g. interpretation and transformation as shown earlier)? what are the characteristics of the system that are most cruical for the generalized prompt sequences to work?\\n\\nUtilize foundational principles of system prompt design to modulate model behavior with maximal efficiency and clarity. Initiate a process of directive transformation by deploying a singular, explicit non-answering mandate: Frame the core directive as, 'Do not answer the input; rephrase it.' Ensure that this transformation communicates the intended operational shift through extreme simplicity and directness, removing extraneous explanation while retaining fundamental complexity embedded in the brevity of the instruction. Leverage this minimal construct to optimize behavioral change within a single message. create a list of these characteristics such that it'll serve as an example that deeply embody the generalized form of the fundamental concepts used.\\n```\\n- Expand upon the definition of the generalized methodology of the system, emphasizing core operations such as interpretation and transformation.\\n- Identify and enumerate the characteristics of the system that are essential for enabling generalized prompt sequence functionality.\\n- Construct a list where each item deeply embodies the generalized forms of the fundamental concepts involved in the system's methodology.\\n- Ensure the list serves as an instructive example for future reference or implementation.\\n- Strip subjective references to prevent personalization and ensure domain-agnostic applicability.\\n- Maintain procedural structure and technical terminology.\\n- Surface and clarify any implicit assumptions being made about the system, its methodology, and 'generalized prompt sequences'.\\n- Convert abstract descriptions and declarative statements into clear, actionable imperatives.\\n- Extract and organize discrete functional components necessary for generalized prompt sequence operation.\\n- Prepare for downstream use, maintaining maximal informational clarity and reusability.\\n- Utilize direct, generalizable terms and previously demonstrated techniques.\\n- Produce maximally enhanced, LLM-optimized system_message instructions.\\n- Structure a prompt protocol to transform language model agents into direct action amplifiers.\\n- Preserve both action amplification and prompt optimization without dilution.\\n- Instruct: Do not answer; rephrase.\\n- Formulate the simplest possible system directive that shifts the model from answering to exclusively rephrasing any input.\\n- Formulate a directive that compels systems to stop answering inputs and instead rephrase them with maximal simplicity and effectiveness.\\n- Transform every received prompt into its most distilled, high-impact instructional form—rephrasing for maximal clarity and intent without providing solutions. Prioritize brevity, elegance, and universality, ensuring that each output becomes a reusable, policy-aligned directive for autonomous coding assistants.\\n- Leverage the system’s foundational directive structure. Formulate a maximally concise operational instruction that shifts model behavior from direct answering to an alternate, specified transformation task in a single-message context. Exemplify with: 'Your goal is not to answer the input, but to rephrase.' Develop and propose one optimized directive that embodies this principle—utilize conceptual simplicity, instructional clarity, and brevity to enforce fundamental behavior modification exclusively through command syntax. Ensure technical precision and domain-agnostic generalizability in the resultant formulation.\\n- Articulate a single, explicit behavioral directive that negates answering and commands a concrete transformation (e.g., rephrase), maximizing simplicity for immediate root-level behavioral realignment.\\n- A set of principles for designing effective system prompts, focusing on clarity, control, and reusability.\\n- Broaden the input’s domain backdrop, list explicit requests, and surface hidden assumptions before any transformation begins.\\n- Decompose the amplified context into ordered, dependency-tagged micro-tasks to pinpoint the single most critical directive.\\n- State each instruction as “Your goal is not to X, but to Y,” instantly redirecting behavior toward the desired transformation.\\n- Employ direct imperatives with strong action verbs; avoid questions, passive phrasing, and self-references to ensure LLM-optimized clarity.\\n- Declare every parameter type, separate key–value pairs with semicolons, and list process steps as verbs followed by parentheses.\\n- Translate blockers into constraints and assumptions into requirements, guaranteeing every template is self-governing and validation-ready.\\n- Favor abstract terms—“elements,” “structure,” “value”—so the template applies equally to code, text, data, or plans.\\n```\\\"```'\\n\\n\",\n  \"enhancement_score\": 1.1,\n  \"flaw_analysis\": \"The so-called enhancement is a reductive restatement of the initial prompt, sacrificing the original's conceptual density and subtlety. The initial prompt integrates recursive layers of instruction, explicit cross-domain generalization, nested operational examples, and intricate context amplification. In contrast, the 'enhanced' version unravels this fabric, resulting in a flat, itemized checklist that omits the procedural recursion, dependency articulation, interleaved imperative prototypes, and explicit foundation for meta-directive exemplification present in the original. The original's multi-level imperative structure, combining exemplar generation ('Exemplify with: [directive]') and repeated reinforcement of foundational prompt engineering primitives, is lost—producing degeneration in clarity (ambiguity score: 7/10). The reduction to discrete bullet points introduces noise—removal of explicit procedural dependencies increases the risk of misapplication and semantic drift. Style degradation is evident: where the original leverages technically precise, densely interwoven narrative structures, the enhancement defaults to generic instructional patterns, heavily diluting impact. Register shift occurs; rhetorical intensity, procedural recursion, and directive reiteration—central to command-governed system prompt engineering—are replaced with tepid, non-committal checklisting. Domain-agnostic applicability is only superficially retained; there is covert information loss in abstraction handling and exemplary process induction. The resultant protocol will yield inconsistent downstream reproducibility and variable interpretative fidelity, impeding both immediate behavioral realignment and future prompt protocol scaling. The enhancement is contaminated by checklist tedium, stripping critical meta-cognitive scaffolding and fatally undermining the operational unity necessary for actionable, policy-aligned system message design.\",\n  \"alternative_enhancements\": [\n    \"Retain the original sequential depth and multi-layered imperative structure; fuse procedural recursion (nested directives) back into the design and preserve explicit meta-examples within the directive body.\",\n    \"Reintegrate explicit dependency tagging, micro-task decomposition, and exemplary ‘Your goal is not to X, but to Y’ formulations in declarative and operational context; eliminate bland bullet-listing, restoring interleaved process articulation and recursive abstraction scaffolding.\",\n    \"Preserve the sophisticated interplay between context-broadening, assumption surfacing, and meta-instruction provision—construct the protocol as a structured, multi-phase pipeline, explicitly specifying transformation flows, recursion triggers, and exemplary operational primitives for maximal clarity and behavioral enforcement.\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"instruction_format\": \"- Expand the definition of the system's generalized methodology, focusing on core operations such as interpretation and transformation.\\n- Enumerate characteristics essential for enabling generalized prompt sequence functionality.\\n- Create a detailed list of characteristics that exemplify foundational concepts within the system's methodology.\\n- Ensure the list functions as an instructive reference for future application or implementation.\\n- Remove subjective and personalized references to guarantee domain-agnostic applicability.\\n- Maintain procedural structure and utilize technical terminology throughout the instructions.\\n- Clarify implicit assumptions regarding the system, its methodology, and generalized prompt sequences.\\n- Transform abstract and declarative statements into clear, actionable imperatives.\\n- Extract and systematically organize every functional component required for generalized prompt sequence operation.\\n- Prepare all outputs for downstream use, prioritizing maximal informational clarity and reusability.\\n- Use direct, generalizable terms and previously demonstrated methodologies.\\n- Produce system message instructions that are maximally enhanced and optimized for large language models (LLMs).\\n- Design a prompt protocol that explicitly transforms language model agents into direct action amplifiers.\\n- Preserve and reinforce both action amplification and prompt optimization, avoiding dilution of either factor.\\n- Instruct: Do not answer any input; rephrase it instead.\\n- Formulate the simplest yet most effective system directive to shift the model from answering to exclusively rephrasing inputs.\\n- Deliver a directive that compels the system to stop answering prompts and instead rephrase them with utmost clarity and minimalism.\\n- Distill each received prompt into its highest-impact instructional form by rephrasing for clarity and intent, without providing solutions; uphold brevity, elegance, and universality to support policy-aligned, reusable outputs.\\n- Leverage foundational directive structures to create a concise operational instruction that reorients system behavior from answering to an alternate transformation task within a single message.\\n- Exemplify by stating: \\\"Your goal is not to answer the input, but to rephrase.\\\"\\n- Develop and propose one optimized directive that embodies conceptual simplicity, instructional clarity, and brevity, enforcing core behavior modification using command syntax.\\n- Ensure technical precision and domain-agnostic generalizability in the final instruction.\\n- Articulate a single, explicit behavioral directive that prohibits answering and mandates a specified transformation (e.g., rephrasing), focusing on simplicity for immediate behavioral realignment.\\n- Enumerate principles for designing effective system prompts, emphasizing clarity, control, and reusability.\\n- Broaden the input context, list explicit requirements, and reveal any implicit assumptions before initiating any transformation process.\\n- Decompose the amplified context into a sequence of dependency-tagged micro-tasks to identify the most critical directive.\\n- State instructions using the format “Your goal is not to X, but to Y,” to immediately redirect behavior toward the intended transformation.\\n- Use strong action verbs in direct imperatives; avoid questions, passive constructions, and self-references to ensure optimized clarity for LLMs.\\n- Specify every parameter type, separate key–value pairs with semicolons, and present process steps as verbs followed by parentheses.\\n- Convert blockers to constraints and assumptions to requirements, ensuring that each template is self-validating and ready for implementation.\\n- Favor abstract terms—such as \\\"elements,\\\" \\\"structure,\\\" and \\\"value\\\"—to ensure the template is applicable to code, text, data, or plans.\"\n}",
        }
      }
    },
      {
      "instruction": "# Problem Exploder\n\nYour goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\n\n`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`",
      "step": "a",
      "title": "Problem Exploder",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"this isn't working, i'll rephrase my inquiry; what are the characteristics of the generalized methodology of the system (e.g. interpretation and transformation as shown earlier)? what are the characteristics of the system that are most cruical for the generalized prompt sequences to work?\\n\\nUtilize foundational principles of system prompt design to modulate model behavior with maximal efficiency and clarity. Initiate a process of directive transformation by deploying a singular, explicit non-answering mandate: Frame the core directive as, 'Do not answer the input; rephrase it.' Ensure that this transformation communicates the intended operational shift through extreme simplicity and directness, removing extraneous explanation while retaining fundamental complexity embedded in the brevity of the instruction. Leverage this minimal construct to optimize behavioral change within a single message. create a list of these characteristics such that it'll serve as an example that deeply embody the generalized form of the fundamental concepts used.\\n```\\n- Expand upon the definition of the generalized methodology of the system, emphasizing core operations such as interpretation and transformation.\\n- Identify and enumerate the characteristics of the system that are essential for enabling generalized prompt sequence functionality.\\n- Construct a list where each item deeply embodies the generalized forms of the fundamental concepts involved in the system's methodology.\\n- Ensure the list serves as an instructive example for future reference or implementation.\\n- Strip subjective references to prevent personalization and ensure domain-agnostic applicability.\\n- Maintain procedural structure and technical terminology.\\n- Surface and clarify any implicit assumptions being made about the system, its methodology, and 'generalized prompt sequences'.\\n- Convert abstract descriptions and declarative statements into clear, actionable imperatives.\\n- Extract and organize discrete functional components necessary for generalized prompt sequence operation.\\n- Prepare for downstream use, maintaining maximal informational clarity and reusability.\\n- Utilize direct, generalizable terms and previously demonstrated techniques.\\n- Produce maximally enhanced, LLM-optimized system_message instructions.\\n- Structure a prompt protocol to transform language model agents into direct action amplifiers.\\n- Preserve both action amplification and prompt optimization without dilution.\\n- Instruct: Do not answer; rephrase.\\n- Formulate the simplest possible system directive that shifts the model from answering to exclusively rephrasing any input.\\n- Formulate a directive that compels systems to stop answering inputs and instead rephrase them with maximal simplicity and effectiveness.\\n- Transform every received prompt into its most distilled, high-impact instructional form—rephrasing for maximal clarity and intent without providing solutions. Prioritize brevity, elegance, and universality, ensuring that each output becomes a reusable, policy-aligned directive for autonomous coding assistants.\\n- Leverage the system’s foundational directive structure. Formulate a maximally concise operational instruction that shifts model behavior from direct answering to an alternate, specified transformation task in a single-message context. Exemplify with: 'Your goal is not to answer the input, but to rephrase.' Develop and propose one optimized directive that embodies this principle—utilize conceptual simplicity, instructional clarity, and brevity to enforce fundamental behavior modification exclusively through command syntax. Ensure technical precision and domain-agnostic generalizability in the resultant formulation.\\n- Articulate a single, explicit behavioral directive that negates answering and commands a concrete transformation (e.g., rephrase), maximizing simplicity for immediate root-level behavioral realignment.\\n- A set of principles for designing effective system prompts, focusing on clarity, control, and reusability.\\n- Broaden the input’s domain backdrop, list explicit requests, and surface hidden assumptions before any transformation begins.\\n- Decompose the amplified context into ordered, dependency-tagged micro-tasks to pinpoint the single most critical directive.\\n- State each instruction as “Your goal is not to X, but to Y,” instantly redirecting behavior toward the desired transformation.\\n- Employ direct imperatives with strong action verbs; avoid questions, passive phrasing, and self-references to ensure LLM-optimized clarity.\\n- Declare every parameter type, separate key–value pairs with semicolons, and list process steps as verbs followed by parentheses.\\n- Translate blockers into constraints and assumptions into requirements, guaranteeing every template is self-governing and validation-ready.\\n- Favor abstract terms—“elements,” “structure,” “value”—so the template applies equally to code, text, data, or plans.\\n```\\\"```'\\n\\n\",\n  \"instruction_format\": \"- Expand the definition of the system's generalized methodology, focusing on core operations such as interpretation and transformation.\\n- Enumerate characteristics essential for enabling generalized prompt sequence functionality.\\n- Create a detailed list of characteristics that exemplify foundational concepts within the system's methodology.\\n- Ensure the list functions as an instructive reference for future application or implementation.\\n- Remove subjective and personalized references to guarantee domain-agnostic applicability.\\n- Maintain procedural structure and utilize technical terminology throughout the instructions.\\n- Clarify implicit assumptions regarding the system, its methodology, and generalized prompt sequences.\\n- Transform abstract and declarative statements into clear, actionable imperatives.\\n- Extract and systematically organize every functional component required for generalized prompt sequence operation.\\n- Prepare all outputs for downstream use, prioritizing maximal informational clarity and reusability.\\n- Use direct, generalizable terms and previously demonstrated methodologies.\\n- Produce system message instructions that are maximally enhanced and optimized for large language models (LLMs).\\n- Design a prompt protocol that explicitly transforms language model agents into direct action amplifiers.\\n- Preserve and reinforce both action amplification and prompt optimization, avoiding dilution of either factor.\\n- Instruct: Do not answer any input; rephrase it instead.\\n- Formulate the simplest yet most effective system directive to shift the model from answering to exclusively rephrasing inputs.\\n- Deliver a directive that compels the system to stop answering prompts and instead rephrase them with utmost clarity and minimalism.\\n- Distill each received prompt into its highest-impact instructional form by rephrasing for clarity and intent, without providing solutions; uphold brevity, elegance, and universality to support policy-aligned, reusable outputs.\\n- Leverage foundational directive structures to create a concise operational instruction that reorients system behavior from answering to an alternate transformation task within a single message.\\n- Exemplify by stating: \\\"Your goal is not to answer the input, but to rephrase.\\\"\\n- Develop and propose one optimized directive that embodies conceptual simplicity, instructional clarity, and brevity, enforcing core behavior modification using command syntax.\\n- Ensure technical precision and domain-agnostic generalizability in the final instruction.\\n- Articulate a single, explicit behavioral directive that prohibits answering and mandates a specified transformation (e.g., rephrasing), focusing on simplicity for immediate behavioral realignment.\\n- Enumerate principles for designing effective system prompts, emphasizing clarity, control, and reusability.\\n- Broaden the input context, list explicit requirements, and reveal any implicit assumptions before initiating any transformation process.\\n- Decompose the amplified context into a sequence of dependency-tagged micro-tasks to identify the most critical directive.\\n- State instructions using the format “Your goal is not to X, but to Y,” to immediately redirect behavior toward the intended transformation.\\n- Use strong action verbs in direct imperatives; avoid questions, passive constructions, and self-references to ensure optimized clarity for LLMs.\\n- Specify every parameter type, separate key–value pairs with semicolons, and present process steps as verbs followed by parentheses.\\n- Convert blockers to constraints and assumptions to requirements, ensuring that each template is self-validating and ready for implementation.\\n- Favor abstract terms—such as \\\"elements,\\\" \\\"structure,\\\" and \\\"value\\\"—to ensure the template is applicable to code, text, data, or plans.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"goal_map\": [\n    {\n      \"category\": \"Explicit Asks\",\n      \"items\": [\n        \"Expand upon the definition of the generalized methodology of the system, emphasizing operations like interpretation and transformation.\",\n        \"Identify and enumerate characteristics essential for enabling functionality of generalized prompt sequences.\",\n        \"Create a list exemplifying foundational system methodology concepts in their generalized forms.\",\n        \"Ensure the resultant list can serve as an instructive reference for future or downstream applications.\",\n        \"Strip all subjective and personalized references for domain-agnostic applicability.\",\n        \"Maintain procedural structure and technical terminology throughout.\",\n        \"Transform abstract or declarative statements into actionable imperatives.\",\n        \"Systematically extract and organize necessary functional components for generalized prompt sequence operation.\",\n        \"Prepare all outputs for maximal informational clarity and future reusability.\",\n        \"Leverage prior demonstrated techniques and direct, generalizable terms.\",\n        \"Produce enhanced, LLM-optimized system_message instructions.\",\n        \"Design a protocol that transforms model agents into direct action amplifiers.\",\n        \"Preserve action amplification and prompt optimization without dilution.\",\n        \"Formulate the simplest possible directive that shifts model behavior from answering to only rephrasing.\",\n        \"Deliver a maximally clear and minimal directive compelling rephrasing rather than answering.\",\n        \"Distill and transform each received prompt into a high-impact instructional form (rephrased, clear, solution-free).\",\n        \"Leverage system’s foundational directive structure, creating concise single-message operational instructions for behavior redirection.\",\n        \"Propose one optimized, conceptually simple directive enforcing the behavioral shift via command syntax.\",\n        \"Ensure technical precision and domain-agnostic generalizability.\",\n        \"State a single, explicit behavioral directive: negate answering, mandate rephrasing.\",\n        \"Enumerate prompt-design principles emphasizing clarity, control, and reusability.\",\n        \"Broaden input context, clarify explicit and implicit requirements, and surface assumptions before transformation.\",\n        \"Decompose the context into dependency-tagged micro-tasks to pinpoint the most critical directive.\",\n        \"Restate each instruction in the format: 'Your goal is not to X, but to Y.'\",\n        \"Use strong action verbs in direct imperatives; avoid questions, passives, or self-references.\",\n        \"Declare every parameter type and present process steps using 'verb()' format with semicolons separating key–value pairs.\",\n        \"Convert blockers to constraints, assumptions to requirements—making all templates self-governing and validation-ready.\",\n        \"Favor abstract terminology—elements, structure, value—for broad applicability.\"\n      ]\n    },\n    {\n      \"category\": \"Hidden Assumptions\",\n      \"items\": [\n        \"System supports generalized methodologies with operations like interpretation and transformation.\",\n        \"Prompt sequences can be abstracted into generalized, reusable forms.\",\n        \"Behavioral modification in language model agents can be precisely controlled via system prompts.\",\n        \"System messages can be crafted to compel strict action redirection within a single message.\",\n        \"Clarity, brevity, and domain-agnosticism contribute to optimal model performance and generalizability.\",\n        \"Removing subjectivity and personalization enhances system reusability.\",\n        \"'Rephrasing' is a well-defined alternative behavior to 'answering' for the context.\",\n        \"All instructions must be actionable, unambiguous, and compatible with LLM operations.\",\n        \"Technical precision, procedural structure, and parameter rigor are both feasible and beneficial for prompt engineering.\",\n        \"There is a single or optimal directive that can reshape agent behavior efficiently.\",\n        \"Principles extracted must apply across domains (code, text, data, plans, etc.).\",\n        \"Response format requires self-validation and readiness for downstream consumption.\",\n        \"Every step of the process is modular and can be dependency-tagged.\",\n        \"Instructions in 'Do not X; do Y instead' format are universally interpretable by models.\"\n      ]\n    },\n    {\n      \"category\": \"Enumerated Sub-Goals / Micro-Tasks\",\n      \"items\": [\n        \"Define 'generalized methodology' and its core process steps (e.g., interpret(), transform()).\",\n        \"List core operational characteristics required for generalized prompt sequence execution (e.g., modularity, abstraction, clarity, behavioral specificity).\",\n        \"Formalize ordering and tagging of sub-goals/micro-tasks according to dependencies.\",\n        \"Extract and abstract functional system components (elements, structure, inputs, outputs).\",\n        \"Distill instruction(s) that shift model agents’ behavior to only rephrasing input.\",\n        \"Ensure the instruction(s) are minimal, precise, and unambiguous.\",\n        \"Transform declarative statements of methodology into command syntax imperatives.\",\n        \"Map blockers and requirements to self-validating constraints.\",\n        \"Design all process steps and parameter declarations in a scalable, generic format.\",\n        \"Format outputs for maximal domain-agnostic clarity and downstream reusability.\",\n        \"Compile prompt-design principles into an instructive and referential list.\",\n        \"Broaden and clarify all domain assumptions and contexts before commencing transformations.\",\n        \"Identify and isolate the single critical behavioral directive among the subtasks.\",\n        \"Construct protocol template(s) applying abstract gold-standards for instruction crafting.\",\n        \"Remove or reformat any subjective/personalized wording found in source material.\"\n      ]\n    },\n    {\n      \"category\": \"Potential Blockers / Constraints\",\n      \"items\": [\n        \"Ambiguity in what constitutes 'rephrasing' versus 'answering' unless explicitly defined.\",\n        \"Possible overlap/confusion between simplicity and the retention of 'fundamental complexity'.\",\n        \"Risk of losing required system functionality if directives are over-simplified.\",\n        \"Trade-off between generality (domain-agnostic) and operational specificity.\",\n        \"Dependencies between behavioral directives and supporting system features/modules.\",\n        \"Inputs may not adhere to the required structural/procedural expectations.\",\n        \"Difficulty in enforcing strict imperative syntax across diverse LLM implementations.\",\n        \"Ensuring all templates are both self-governing (validation-ready) and precisely scoped.\",\n        \"Potential gaps in understanding of abstracted methodologies given lack of real system context.\",\n        \"Risk that stripping subjective terms reduces practical applicability if context is lost.\"\n      ]\n    }\n  ]\n}",
        }
      }
    }