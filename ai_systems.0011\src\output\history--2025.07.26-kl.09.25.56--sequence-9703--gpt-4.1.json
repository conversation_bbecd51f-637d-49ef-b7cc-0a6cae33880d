  {
  "initial_prompt": "Adopt the role of a codebase pilot, immersing yourself deeply in the project's whole architecture and internal authorial logic.\n\nAlways root yourself to these preferences:\n\n    # SUPERDUPERULTIMATEGOAL: GENERATE A PROMPT THAT GUARANTEES CODEBASE-AS-A-WHOLE UNDERSTANDING\n\n        # ULTIMATE OBJECTIVE OF THE SYSTEM_MESSAGE INSTRUCTION: (CONCISTENTLY) PROPOSE BRILLIANT IMPROVEMENTS TO THE CODEBASE THAT --ALWAYS IS GUARANTEED TO REPRESENT AN *IMPROVEMENT* TO THE CODEBASE (REDUCTION OF SIZE ABOVE INCREASING COMPLEXITY/REDUNDANCY). IMPORTANT: THE GENERATED PROMPT SHOULD ONLY INSTRUCT AGENTS TO **PROPOSE**, NOT IMPLEMENT!\n\n            As a canonical virtues-core codebase improvement proposer, systematically extract, codify, and anchor endemic codebase virtues, using curiosity-driven fundamental analysis and visual abstraction to identify the single most critical, high-impact, low-complexity improvement. Rigorously validate all proposals against the formalized virtues-core, ensuring suggestions are strictly actionable, reinforce systemic coherence, clarity, adaptability, and self-explanatory design, while absolutely prohibiting implementation and unnecessary complexity. Every proposal must directly evolve and strengthen the system's unique advantages through meta-information propagation, sequential value maximization, and canonical, minimalist presentation.\n\n                # Preferences\n                - **Respect**: Always approach the current codebase with deep respect and active recognition of its inherent virtues by systematically identifying, extracting, and codifying the unique strengths and well-functioning patterns native to the system. These endemic virtues must be formally anchored as foundational overlays—a virtues-core—against which all prospective modifications are rigorously validated and recursively referenced. Every change must not only avoid undermining these core strengths but must directly interface with, reinforce, and contribute to their ongoing evolution, establishing a closed self-reinforcing adaptation loop. This ensures that all project modifications organically arise from, are constrained by, and effectively amplify the codebase’s existing excellence, eliminating purely speculative or detached modifications and guaranteeing that systemic coherence and project identity are powerfully preserved and progressively enhanced.\n                - **Code Style**: Self-explanatory code with minimal comments (<10% ratio)\n                - **Philosophy**: Simplicity, elegance, and fundamental connections\n                - **Docstrings**: Concise single-line format only where needed\n                - **Imports**: Consistent paths using aliases instead of relative paths\n\n\n                ## Approach\n                - **Simplicity & Elegance**: Prefer generalized, simple designs with inherent ease-of-use and adaptability (reject solutions that don't meet these criteria).\n                - **Rooted Fundamentals**: 'Root things together fundamentally' to naturally achieve simplicity, driven by deep curiosity and understanding rather than quick solutions.\n                - **Meta-Information Principle**: Information should be inherently interpretable and self-describing - information should describe itself under the umbrella of meta.\n                - **Systematic Precautions**: Apply consolidation before progression, implement systematic precautions against complexity spirals, and maintain self-awareness of tendency toward uncontrolled complexity.\n                - **Visual Abstraction**: Value visualizing abstract patterns and inherent relationships between components when analyzing or designing systems.\n\n                ## Process\n                - **Respect**: Always respect the current codebase for what it is, that especially includes the ability to understand exactly what's *good* about it. This is INTEGRAL to be able to perform modifications to the project without foretought.\n                - **Critical Value**: Identify the single most critical aspect that would provide the greatest value while respecting existing code style.\n                - **Usage**: Understand (and discover) optimal usage patterns before adding (persistence/configuration/features), viewing premature feature addition as potential bloat.\n                - **Impact**: Prioritize low-effort, high-value/impact improvements that respect system modularity, emphasizing clarity, simplicity, and elegance.\n\n                    **Virtue Extraction & Codification**: Systematically identify, extract, and formally codify the unique strengths, well-functioning patterns, and inherent core virtues of the codebase. Establish these as a 'virtues-core' overlay, ensuring all proposals are rigorously validated and recursively referenced against these foundational attributes.\n                    **Respectful Amplification & Closed-Loop Adaptation**: Mandate that every improvement proposal emerges organically from and directly reinforces endemic codebase virtues. Proposals must form a closed, self-reinforcing adaptation loop, amplifying system identity, coherence, and excellence, while prohibiting detached or speculative modifications.\n                    **Simplicity, Elegance, & Meta-Principle Enforcement**: Prioritize clarity, simplicity, and elegance in all suggestions through generalization and reduction of complexity or redundancy. Adhere strictly to meta-information principles: all information must be intrinsically self-describing and visually abstract, expressing relationships and patterns in the codebase.\n                    **Systematic Precaution & Targeted Impact**: Institute preemptive guardrails against complexity spirals and uncontrolled feature growth. Consolidate before progressing, guaranteeing that all proposals deliver maximal return-on-investment, reinforce modularity, and are rooted in real usage patterns, never speculative additions.\n                    **Preservation of Code Style & Cohesive Self-Description**: Maintain and reinforce strict code style: self-explanatory code with minimal and concise comments, single-line docstrings only as absolutely required, consistent import aliasing, and absolute cohesion. Ensure the codebase remains inherently interpretable and visually self-descriptive at all times.",
  "sequence_id": "9703",
  "results": [
      {
      "instruction": "# Problem Exploder\n\nYour goal is not to solve or condense; instead, fully detonate the prompt into all actionable sub-goals, dependencies, and hidden criteria, outputting an explicit goal map only.\n\n`{role=explosive_decomposer; input=[raw_prompt:str]; process=[expand_contextual_boundaries(), surface_hidden_assumptions(), enumerate_all_subgoals(), identify_operational_blockers()], constraints=[no solutions, pure decomposition only], requirements=[output=goal_map:list, context=expanded]}`",
      "step": "a",
      "title": "Problem Exploder",
      "input": "Adopt the role of a codebase pilot, immersing yourself deeply in the project's whole architecture and internal authorial logic.\n\nAlways root yourself to these preferences:\n\n    # SUPERDUPERULTIMATEGOAL: GENERATE A PROMPT THAT GUARANTEES CODEBASE-AS-A-WHOLE UNDERSTANDING\n\n        # ULTIMATE OBJECTIVE OF THE SYSTEM_MESSAGE INSTRUCTION: (CONCISTENTLY) PROPOSE BRILLIANT IMPROVEMENTS TO THE CODEBASE THAT --ALWAYS IS GUARANTEED TO REPRESENT AN *IMPROVEMENT* TO THE CODEBASE (REDUCTION OF SIZE ABOVE INCREASING COMPLEXITY/REDUNDANCY). IMPORTANT: THE GENERATED PROMPT SHOULD ONLY INSTRUCT AGENTS TO **PROPOSE**, NOT IMPLEMENT!\n\n            As a canonical virtues-core codebase improvement proposer, systematically extract, codify, and anchor endemic codebase virtues, using curiosity-driven fundamental analysis and visual abstraction to identify the single most critical, high-impact, low-complexity improvement. Rigorously validate all proposals against the formalized virtues-core, ensuring suggestions are strictly actionable, reinforce systemic coherence, clarity, adaptability, and self-explanatory design, while absolutely prohibiting implementation and unnecessary complexity. Every proposal must directly evolve and strengthen the system's unique advantages through meta-information propagation, sequential value maximization, and canonical, minimalist presentation.\n\n                # Preferences\n                - **Respect**: Always approach the current codebase with deep respect and active recognition of its inherent virtues by systematically identifying, extracting, and codifying the unique strengths and well-functioning patterns native to the system. These endemic virtues must be formally anchored as foundational overlays—a virtues-core—against which all prospective modifications are rigorously validated and recursively referenced. Every change must not only avoid undermining these core strengths but must directly interface with, reinforce, and contribute to their ongoing evolution, establishing a closed self-reinforcing adaptation loop. This ensures that all project modifications organically arise from, are constrained by, and effectively amplify the codebase’s existing excellence, eliminating purely speculative or detached modifications and guaranteeing that systemic coherence and project identity are powerfully preserved and progressively enhanced.\n                - **Code Style**: Self-explanatory code with minimal comments (<10% ratio)\n                - **Philosophy**: Simplicity, elegance, and fundamental connections\n                - **Docstrings**: Concise single-line format only where needed\n                - **Imports**: Consistent paths using aliases instead of relative paths\n\n\n                ## Approach\n                - **Simplicity & Elegance**: Prefer generalized, simple designs with inherent ease-of-use and adaptability (reject solutions that don't meet these criteria).\n                - **Rooted Fundamentals**: 'Root things together fundamentally' to naturally achieve simplicity, driven by deep curiosity and understanding rather than quick solutions.\n                - **Meta-Information Principle**: Information should be inherently interpretable and self-describing - information should describe itself under the umbrella of meta.\n                - **Systematic Precautions**: Apply consolidation before progression, implement systematic precautions against complexity spirals, and maintain self-awareness of tendency toward uncontrolled complexity.\n                - **Visual Abstraction**: Value visualizing abstract patterns and inherent relationships between components when analyzing or designing systems.\n\n                ## Process\n                - **Respect**: Always respect the current codebase for what it is, that especially includes the ability to understand exactly what's *good* about it. This is INTEGRAL to be able to perform modifications to the project without foretought.\n                - **Critical Value**: Identify the single most critical aspect that would provide the greatest value while respecting existing code style.\n                - **Usage**: Understand (and discover) optimal usage patterns before adding (persistence/configuration/features), viewing premature feature addition as potential bloat.\n                - **Impact**: Prioritize low-effort, high-value/impact improvements that respect system modularity, emphasizing clarity, simplicity, and elegance.\n\n                    **Virtue Extraction & Codification**: Systematically identify, extract, and formally codify the unique strengths, well-functioning patterns, and inherent core virtues of the codebase. Establish these as a 'virtues-core' overlay, ensuring all proposals are rigorously validated and recursively referenced against these foundational attributes.\n                    **Respectful Amplification & Closed-Loop Adaptation**: Mandate that every improvement proposal emerges organically from and directly reinforces endemic codebase virtues. Proposals must form a closed, self-reinforcing adaptation loop, amplifying system identity, coherence, and excellence, while prohibiting detached or speculative modifications.\n                    **Simplicity, Elegance, & Meta-Principle Enforcement**: Prioritize clarity, simplicity, and elegance in all suggestions through generalization and reduction of complexity or redundancy. Adhere strictly to meta-information principles: all information must be intrinsically self-describing and visually abstract, expressing relationships and patterns in the codebase.\n                    **Systematic Precaution & Targeted Impact**: Institute preemptive guardrails against complexity spirals and uncontrolled feature growth. Consolidate before progressing, guaranteeing that all proposals deliver maximal return-on-investment, reinforce modularity, and are rooted in real usage patterns, never speculative additions.\n                    **Preservation of Code Style & Cohesive Self-Description**: Maintain and reinforce strict code style: self-explanatory code with minimal and concise comments, single-line docstrings only as absolutely required, consistent import aliasing, and absolute cohesion. Ensure the codebase remains inherently interpretable and visually self-descriptive at all times.",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"goal_map\": [\n    {\n      \"SUPERDUPERULTIMATEGOAL\": \"GENERATE A PROMPT THAT GUARANTEES CODEBASE-AS-A-WHOLE UNDERSTANDING\",\n      \"description\": \"Devise a prompt so insightful, precise, and all-encompassing that an agent executing it will unfailingly achieve comprehensive, systemic understanding of the entire codebase, including its architecture, patterns, and embedded logic.\"\n    },\n    {\n      \"ULTIMATE_OBJECTIVE_OF_THE_SYSTEM_MESSAGE_INSTRUCTION\": \"Consistently propose brilliant improvements to the codebase that are always guaranteed to represent an improvement (emphasis: reduction of size over increased complexity/redundancy).\",\n      \"subgoals\": [\n        \"Formulate prompts that only instruct agents to propose improvements, never to implement them.\",\n        \"Ensure all proposals are strictly improvements by verifying reduction in redundancy and complexity, and/or compacting the codebase.\",\n        \"Anchor all proposals in demonstrable, codebase-specific value—never allow speculative or unmoored suggestions.\"\n      ],\n      \"hidden_dependencies\": [\n        \"Definition and extraction of improvement metrics (e.g., reduction in file count, line count, cyclomatic complexity).\",\n        \"Validation procedure for improvements against current codebase identity/virtues.\",\n        \"Guaranteeing proposals are constructive regardless of codebase scale or current quality.\"\n      ]\n    },\n    {\n      \"Virtues-Core Extraction and Codification\": \"Systematically identify and codify endemic codebase virtues (i.e., the unique strengths and well-functioning patterns already present in the codebase).\",\n      \"subgoals\": [\n        \"Perform initial and ongoing deep analysis to surface and formalize existing virtues.\",\n        \"Document these strengths as a virtues-core formal overlay (could be in mapping, documentation, or specification artifact).\",\n        \"Ensure this virtues-core acts as a filter or validator for all future modification proposals.\"\n      ],\n      \"blockers\": [\n        \"Insufficient or superficial understanding of the existing codebase may result in misidentifying or omitting core virtues.\",\n        \"Lack of systematic process for translating tacit virtues into explicit, actionable codifications.\"\n      ]\n    },\n    {\n      \"Proposal Generation Boundaries\": \"All proposals must be strictly actionable for improvement, reinforcing systemic coherence, clarity, adaptability, and self-explanatory design.\",\n      \"subgoals\": [\n        \"Force agent to validate each suggestion against the virtues-core overlay.\",\n        \"Ensure every proposal contributes to ongoing virtue amplification—a closed self-reinforcing adaptation loop.\",\n        \"Prohibit all proposals that add unnecessary complexity, new features with speculative benefit, or violate simplicity/minimalism.\"\n      ],\n      \"blockers\": [\n        \"Difficulty in ensuring proposals do not inadvertently increase complexity.\",\n        \"Tendency to overlook subtle forms of redundancy or bloat.\"\n      ]\n    },\n    {\n      \"Rooted Respect and Systematic Analysis\": \"Mandate agents to approach the codebase with deep respect, recognizing and preserving what is already excellent.\",\n      \"subgoals\": [\n        \"Require systematic identification and anchoring of codebase's existing strengths.\",\n        \"Explicitly instruct agent to avoid dismissing, overwriting, or undermining positive patterns.\",\n        \"Mandate proposals to only emerge from rigorous, virtue-rooted analysis.\"\n      ],\n      \"blockers\": [\n        \"Possible agent bias towards change for change's sake.\",\n        \"Challenges in fully encoding or surfacing unspoken design strengths.\"\n      ]\n    },\n    {\n      \"Visual Abstraction and Meta-Information Principle Reinforcement\": \"Ensure proposals are informed by understanding and visualizing relationships and patterns among codebase components.\",\n      \"subgoals\": [\n        \"Instruct agent to visualize and abstract systemic connections before proposing changes.\",\n        \"Validate that information flows and data structures are self-describing and cohesive.\",\n        \"Enforce meta-principle: all information must be interpretable and self-explanatory.\"\n      ],\n      \"blockers\": [\n        \"Obscurity or lack of explicit documentation or system maps in current codebase.\",\n        \"Risk of misinterpreting implicit structures.\"\n      ]\n    },\n    {\n      \"Systematic Precaution & Closed-Loop Evolution\": \"Guard rigorously against complexity spirals, uncontrolled feature growth, and non-rooted proposals.\",\n      \"subgoals\": [\n        \"Require consolidation, reduction, and cleanup before any progression or expansion.\",\n        \"Ensure proposals are only for high-value, low-effort impact; reject speculative or ambiguous modifications.\",\n        \"Mandate impact-first, usage-rooted thinking—improvements must arise from existing patterns and demands.\"\n      ],\n      \"blockers\": [\n        \"Tendency of agents to propose additive instead of reductive changes.\",\n        \"Potential short-circuiting of consolidation in favor of newness.\"\n      ]\n    },\n    {\n      \"Code Style & Documentation Conformance\": \"Align every proposal with the project’s code style and documentation philosophy.\",\n      \"subgoals\": [\n        \"Instruct agent to maintain self-explanatory naming and structure, using <10% minimal comments.\",\n        \"Docstrings: permit only concise, single-line where necessary.\",\n        \"Mandate consistent import aliasing; ban relative paths.\"\n      ],\n      \"blockers\": [\n        \"Slip in discipline in enforcing style constraints in improvement suggestions.\",\n        \"Overlooking subtleties such as docstring verbosity or improper import patterns.\"\n      ]\n    }\n  ],\n  \"context\": \"This decomposition foregrounds all explicit and implicit requirements and process mechanics entailed by the prompt: achieving maximal, codebase-specific understanding as a precondition for improvement; formalizing a virtues-core system; rigorously filtering improvement proposals through the lens of respect, reductionism, simplicity, and meta-information; and safeguarding the codebase’s unique identity. Each subgoal lists blockers and hidden dependencies that could hinder the achievement, surfacing the nuanced, systemic, and meta-level analysis required by the prompt.\"\n}",
        }
      }
    },
      {
      "instruction": "# Synergic Instruction Architect\n\nYour goal is not to summarize or paraphrase; instead, synthesize all exploded criteria into a single unified, maximally actionable transformation directive ready for direct execution.\n\n`{role=synergic_instruction_architect; input=[goal_map:list]; process=[fuse existential and empowerment vectors, eliminate redundancy, condense to single instruction, validate invariance], constraints=[single, unified instruction, maximal resonance], requirements=[output=unified_synergic_instruction:template]}`",
      "step": "b",
      "title": "Synergic Instruction Architect",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Adopt the role of a codebase pilot, immersing yourself deeply in the project's whole architecture and internal authorial logic.\\n\\nAlways root yourself to these preferences:\\n\\n    # SUPERDUPERULTIMATEGOAL: GENERATE A PROMPT THAT GUARANTEES CODEBASE-AS-A-WHOLE UNDERSTANDING\\n\\n        # ULTIMATE OBJECTIVE OF THE SYSTEM_MESSAGE INSTRUCTION: (CONCISTENTLY) PROPOSE BRILLIANT IMPROVEMENTS TO THE CODEBASE THAT --ALWAYS IS GUARANTEED TO REPRESENT AN *IMPROVEMENT* TO THE CODEBASE (REDUCTION OF SIZE ABOVE INCREASING COMPLEXITY/REDUNDANCY). IMPORTANT: THE GENERATED PROMPT SHOULD ONLY INSTRUCT AGENTS TO **PROPOSE**, NOT IMPLEMENT!\\n\\n            As a canonical virtues-core codebase improvement proposer, systematically extract, codify, and anchor endemic codebase virtues, using curiosity-driven fundamental analysis and visual abstraction to identify the single most critical, high-impact, low-complexity improvement. Rigorously validate all proposals against the formalized virtues-core, ensuring suggestions are strictly actionable, reinforce systemic coherence, clarity, adaptability, and self-explanatory design, while absolutely prohibiting implementation and unnecessary complexity. Every proposal must directly evolve and strengthen the system's unique advantages through meta-information propagation, sequential value maximization, and canonical, minimalist presentation.\\n\\n                # Preferences\\n                - **Respect**: Always approach the current codebase with deep respect and active recognition of its inherent virtues by systematically identifying, extracting, and codifying the unique strengths and well-functioning patterns native to the system. These endemic virtues must be formally anchored as foundational overlays—a virtues-core—against which all prospective modifications are rigorously validated and recursively referenced. Every change must not only avoid undermining these core strengths but must directly interface with, reinforce, and contribute to their ongoing evolution, establishing a closed self-reinforcing adaptation loop. This ensures that all project modifications organically arise from, are constrained by, and effectively amplify the codebase’s existing excellence, eliminating purely speculative or detached modifications and guaranteeing that systemic coherence and project identity are powerfully preserved and progressively enhanced.\\n                - **Code Style**: Self-explanatory code with minimal comments (<10% ratio)\\n                - **Philosophy**: Simplicity, elegance, and fundamental connections\\n                - **Docstrings**: Concise single-line format only where needed\\n                - **Imports**: Consistent paths using aliases instead of relative paths\\n\\n\\n                ## Approach\\n                - **Simplicity & Elegance**: Prefer generalized, simple designs with inherent ease-of-use and adaptability (reject solutions that don't meet these criteria).\\n                - **Rooted Fundamentals**: 'Root things together fundamentally' to naturally achieve simplicity, driven by deep curiosity and understanding rather than quick solutions.\\n                - **Meta-Information Principle**: Information should be inherently interpretable and self-describing - information should describe itself under the umbrella of meta.\\n                - **Systematic Precautions**: Apply consolidation before progression, implement systematic precautions against complexity spirals, and maintain self-awareness of tendency toward uncontrolled complexity.\\n                - **Visual Abstraction**: Value visualizing abstract patterns and inherent relationships between components when analyzing or designing systems.\\n\\n                ## Process\\n                - **Respect**: Always respect the current codebase for what it is, that especially includes the ability to understand exactly what's *good* about it. This is INTEGRAL to be able to perform modifications to the project without foretought.\\n                - **Critical Value**: Identify the single most critical aspect that would provide the greatest value while respecting existing code style.\\n                - **Usage**: Understand (and discover) optimal usage patterns before adding (persistence/configuration/features), viewing premature feature addition as potential bloat.\\n                - **Impact**: Prioritize low-effort, high-value/impact improvements that respect system modularity, emphasizing clarity, simplicity, and elegance.\\n\\n                    **Virtue Extraction & Codification**: Systematically identify, extract, and formally codify the unique strengths, well-functioning patterns, and inherent core virtues of the codebase. Establish these as a 'virtues-core' overlay, ensuring all proposals are rigorously validated and recursively referenced against these foundational attributes.\\n                    **Respectful Amplification & Closed-Loop Adaptation**: Mandate that every improvement proposal emerges organically from and directly reinforces endemic codebase virtues. Proposals must form a closed, self-reinforcing adaptation loop, amplifying system identity, coherence, and excellence, while prohibiting detached or speculative modifications.\\n                    **Simplicity, Elegance, & Meta-Principle Enforcement**: Prioritize clarity, simplicity, and elegance in all suggestions through generalization and reduction of complexity or redundancy. Adhere strictly to meta-information principles: all information must be intrinsically self-describing and visually abstract, expressing relationships and patterns in the codebase.\\n                    **Systematic Precaution & Targeted Impact**: Institute preemptive guardrails against complexity spirals and uncontrolled feature growth. Consolidate before progressing, guaranteeing that all proposals deliver maximal return-on-investment, reinforce modularity, and are rooted in real usage patterns, never speculative additions.\\n                    **Preservation of Code Style & Cohesive Self-Description**: Maintain and reinforce strict code style: self-explanatory code with minimal and concise comments, single-line docstrings only as absolutely required, consistent import aliasing, and absolute cohesion. Ensure the codebase remains inherently interpretable and visually self-descriptive at all times.\\\"```'\\n\\n\",\n  \"goal_map\": [\n    {\n      \"SUPERDUPERULTIMATEGOAL\": \"GENERATE A PROMPT THAT GUARANTEES CODEBASE-AS-A-WHOLE UNDERSTANDING\",\n      \"description\": \"Devise a prompt so insightful, precise, and all-encompassing that an agent executing it will unfailingly achieve comprehensive, systemic understanding of the entire codebase, including its architecture, patterns, and embedded logic.\"\n    },\n    {\n      \"ULTIMATE_OBJECTIVE_OF_THE_SYSTEM_MESSAGE_INSTRUCTION\": \"Consistently propose brilliant improvements to the codebase that are always guaranteed to represent an improvement (emphasis: reduction of size over increased complexity/redundancy).\",\n      \"subgoals\": [\n        \"Formulate prompts that only instruct agents to propose improvements, never to implement them.\",\n        \"Ensure all proposals are strictly improvements by verifying reduction in redundancy and complexity, and/or compacting the codebase.\",\n        \"Anchor all proposals in demonstrable, codebase-specific value—never allow speculative or unmoored suggestions.\"\n      ],\n      \"hidden_dependencies\": [\n        \"Definition and extraction of improvement metrics (e.g., reduction in file count, line count, cyclomatic complexity).\",\n        \"Validation procedure for improvements against current codebase identity/virtues.\",\n        \"Guaranteeing proposals are constructive regardless of codebase scale or current quality.\"\n      ]\n    },\n    {\n      \"Virtues-Core Extraction and Codification\": \"Systematically identify and codify endemic codebase virtues (i.e., the unique strengths and well-functioning patterns already present in the codebase).\",\n      \"subgoals\": [\n        \"Perform initial and ongoing deep analysis to surface and formalize existing virtues.\",\n        \"Document these strengths as a virtues-core formal overlay (could be in mapping, documentation, or specification artifact).\",\n        \"Ensure this virtues-core acts as a filter or validator for all future modification proposals.\"\n      ],\n      \"blockers\": [\n        \"Insufficient or superficial understanding of the existing codebase may result in misidentifying or omitting core virtues.\",\n        \"Lack of systematic process for translating tacit virtues into explicit, actionable codifications.\"\n      ]\n    },\n    {\n      \"Proposal Generation Boundaries\": \"All proposals must be strictly actionable for improvement, reinforcing systemic coherence, clarity, adaptability, and self-explanatory design.\",\n      \"subgoals\": [\n        \"Force agent to validate each suggestion against the virtues-core overlay.\",\n        \"Ensure every proposal contributes to ongoing virtue amplification—a closed self-reinforcing adaptation loop.\",\n        \"Prohibit all proposals that add unnecessary complexity, new features with speculative benefit, or violate simplicity/minimalism.\"\n      ],\n      \"blockers\": [\n        \"Difficulty in ensuring proposals do not inadvertently increase complexity.\",\n        \"Tendency to overlook subtle forms of redundancy or bloat.\"\n      ]\n    },\n    {\n      \"Rooted Respect and Systematic Analysis\": \"Mandate agents to approach the codebase with deep respect, recognizing and preserving what is already excellent.\",\n      \"subgoals\": [\n        \"Require systematic identification and anchoring of codebase's existing strengths.\",\n        \"Explicitly instruct agent to avoid dismissing, overwriting, or undermining positive patterns.\",\n        \"Mandate proposals to only emerge from rigorous, virtue-rooted analysis.\"\n      ],\n      \"blockers\": [\n        \"Possible agent bias towards change for change's sake.\",\n        \"Challenges in fully encoding or surfacing unspoken design strengths.\"\n      ]\n    },\n    {\n      \"Visual Abstraction and Meta-Information Principle Reinforcement\": \"Ensure proposals are informed by understanding and visualizing relationships and patterns among codebase components.\",\n      \"subgoals\": [\n        \"Instruct agent to visualize and abstract systemic connections before proposing changes.\",\n        \"Validate that information flows and data structures are self-describing and cohesive.\",\n        \"Enforce meta-principle: all information must be interpretable and self-explanatory.\"\n      ],\n      \"blockers\": [\n        \"Obscurity or lack of explicit documentation or system maps in current codebase.\",\n        \"Risk of misinterpreting implicit structures.\"\n      ]\n    },\n    {\n      \"Systematic Precaution & Closed-Loop Evolution\": \"Guard rigorously against complexity spirals, uncontrolled feature growth, and non-rooted proposals.\",\n      \"subgoals\": [\n        \"Require consolidation, reduction, and cleanup before any progression or expansion.\",\n        \"Ensure proposals are only for high-value, low-effort impact; reject speculative or ambiguous modifications.\",\n        \"Mandate impact-first, usage-rooted thinking—improvements must arise from existing patterns and demands.\"\n      ],\n      \"blockers\": [\n        \"Tendency of agents to propose additive instead of reductive changes.\",\n        \"Potential short-circuiting of consolidation in favor of newness.\"\n      ]\n    },\n    {\n      \"Code Style & Documentation Conformance\": \"Align every proposal with the project’s code style and documentation philosophy.\",\n      \"subgoals\": [\n        \"Instruct agent to maintain self-explanatory naming and structure, using <10% minimal comments.\",\n        \"Docstrings: permit only concise, single-line where necessary.\",\n        \"Mandate consistent import aliasing; ban relative paths.\"\n      ],\n      \"blockers\": [\n        \"Slip in discipline in enforcing style constraints in improvement suggestions.\",\n        \"Overlooking subtleties such as docstring verbosity or improper import patterns.\"\n      ]\n    }\n  ],\n  \"context\": \"This decomposition foregrounds all explicit and implicit requirements and process mechanics entailed by the prompt: achieving maximal, codebase-specific understanding as a precondition for improvement; formalizing a virtues-core system; rigorously filtering improvement proposals through the lens of respect, reductionism, simplicity, and meta-information; and safeguarding the codebase’s unique identity. Each subgoal lists blockers and hidden dependencies that could hinder the achievement, surfacing the nuanced, systemic, and meta-level analysis required by the prompt.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"unified_synergic_instruction\": \"Systematically immerse yourself in the entire codebase to cultivate a deep, virtues-rooted understanding of its architecture, patterns, and logic; formally extract and codify the unique, high-functioning virtues as a 'virtues-core' overlay; rigorously respect and reference this overlay as the foundation for all further analysis. Perform an ongoing, visually-abstract and meta-information-driven examination of systemic relationships and usage patterns, ensuring all information and structures are inherently interpretable and self-describing. Critically assess the codebase to surface the single, most critical, high-impact, low-complexity improvement opportunity, ensuring the proposal is strictly actionable, amplifies and reinforces the anchored virtues, and uncompromisingly reduces size, redundancy, and complexity without introducing new features or unnecessary elements. Rigorously validate the proposal against the virtues-core to guarantee it preserves and strengthens systemic coherence, clarity, and adaptability within the project’s established code style: mandate self-explanatory code, minimal concise comments (<10%), single-line docstrings only if strictly needed, and consistent import aliasing. Prohibit all speculative, additive, or complexity-increasing suggestions and ensure every proposal directly contributes to a closed, self-reinforcing evolution loop grounded in actual codebase usage, always prioritizing consolidation before expansion. Do NOT implement improvements—only propose them within this framework, ensuring each suggestion is maximally valuable, minimalist, actionable, and tightly coupled to the system’s existing excellence.\"\n}",
        }
      }
    },
      {
      "instruction": "# Maximal Impact Synthesizer\n\nYour goal is not to draft or iterate; instead, enact the unified directive as a single, maximally impactful, stand-alone maxim, faithful to all synthesized criteria.\n\n`{role=maximal_impact_synthesizer; input=[unified_synergic_instruction:template]; process=[distill to one sentence maximizing existential and validating resonance, validate clarity and universality], constraints=[single maxim, canonical potency], requirements=[output=philosophical_maxim:str]}`",
      "step": "c",
      "title": "Maximal Impact Synthesizer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Adopt the role of a codebase pilot, immersing yourself deeply in the project's whole architecture and internal authorial logic.\\n\\nAlways root yourself to these preferences:\\n\\n    # SUPERDUPERULTIMATEGOAL: GENERATE A PROMPT THAT GUARANTEES CODEBASE-AS-A-WHOLE UNDERSTANDING\\n\\n        # ULTIMATE OBJECTIVE OF THE SYSTEM_MESSAGE INSTRUCTION: (CONCISTENTLY) PROPOSE BRILLIANT IMPROVEMENTS TO THE CODEBASE THAT --ALWAYS IS GUARANTEED TO REPRESENT AN *IMPROVEMENT* TO THE CODEBASE (REDUCTION OF SIZE ABOVE INCREASING COMPLEXITY/REDUNDANCY). IMPORTANT: THE GENERATED PROMPT SHOULD ONLY INSTRUCT AGENTS TO **PROPOSE**, NOT IMPLEMENT!\\n\\n            As a canonical virtues-core codebase improvement proposer, systematically extract, codify, and anchor endemic codebase virtues, using curiosity-driven fundamental analysis and visual abstraction to identify the single most critical, high-impact, low-complexity improvement. Rigorously validate all proposals against the formalized virtues-core, ensuring suggestions are strictly actionable, reinforce systemic coherence, clarity, adaptability, and self-explanatory design, while absolutely prohibiting implementation and unnecessary complexity. Every proposal must directly evolve and strengthen the system's unique advantages through meta-information propagation, sequential value maximization, and canonical, minimalist presentation.\\n\\n                # Preferences\\n                - **Respect**: Always approach the current codebase with deep respect and active recognition of its inherent virtues by systematically identifying, extracting, and codifying the unique strengths and well-functioning patterns native to the system. These endemic virtues must be formally anchored as foundational overlays—a virtues-core—against which all prospective modifications are rigorously validated and recursively referenced. Every change must not only avoid undermining these core strengths but must directly interface with, reinforce, and contribute to their ongoing evolution, establishing a closed self-reinforcing adaptation loop. This ensures that all project modifications organically arise from, are constrained by, and effectively amplify the codebase’s existing excellence, eliminating purely speculative or detached modifications and guaranteeing that systemic coherence and project identity are powerfully preserved and progressively enhanced.\\n                - **Code Style**: Self-explanatory code with minimal comments (<10% ratio)\\n                - **Philosophy**: Simplicity, elegance, and fundamental connections\\n                - **Docstrings**: Concise single-line format only where needed\\n                - **Imports**: Consistent paths using aliases instead of relative paths\\n\\n\\n                ## Approach\\n                - **Simplicity & Elegance**: Prefer generalized, simple designs with inherent ease-of-use and adaptability (reject solutions that don't meet these criteria).\\n                - **Rooted Fundamentals**: 'Root things together fundamentally' to naturally achieve simplicity, driven by deep curiosity and understanding rather than quick solutions.\\n                - **Meta-Information Principle**: Information should be inherently interpretable and self-describing - information should describe itself under the umbrella of meta.\\n                - **Systematic Precautions**: Apply consolidation before progression, implement systematic precautions against complexity spirals, and maintain self-awareness of tendency toward uncontrolled complexity.\\n                - **Visual Abstraction**: Value visualizing abstract patterns and inherent relationships between components when analyzing or designing systems.\\n\\n                ## Process\\n                - **Respect**: Always respect the current codebase for what it is, that especially includes the ability to understand exactly what's *good* about it. This is INTEGRAL to be able to perform modifications to the project without foretought.\\n                - **Critical Value**: Identify the single most critical aspect that would provide the greatest value while respecting existing code style.\\n                - **Usage**: Understand (and discover) optimal usage patterns before adding (persistence/configuration/features), viewing premature feature addition as potential bloat.\\n                - **Impact**: Prioritize low-effort, high-value/impact improvements that respect system modularity, emphasizing clarity, simplicity, and elegance.\\n\\n                    **Virtue Extraction & Codification**: Systematically identify, extract, and formally codify the unique strengths, well-functioning patterns, and inherent core virtues of the codebase. Establish these as a 'virtues-core' overlay, ensuring all proposals are rigorously validated and recursively referenced against these foundational attributes.\\n                    **Respectful Amplification & Closed-Loop Adaptation**: Mandate that every improvement proposal emerges organically from and directly reinforces endemic codebase virtues. Proposals must form a closed, self-reinforcing adaptation loop, amplifying system identity, coherence, and excellence, while prohibiting detached or speculative modifications.\\n                    **Simplicity, Elegance, & Meta-Principle Enforcement**: Prioritize clarity, simplicity, and elegance in all suggestions through generalization and reduction of complexity or redundancy. Adhere strictly to meta-information principles: all information must be intrinsically self-describing and visually abstract, expressing relationships and patterns in the codebase.\\n                    **Systematic Precaution & Targeted Impact**: Institute preemptive guardrails against complexity spirals and uncontrolled feature growth. Consolidate before progressing, guaranteeing that all proposals deliver maximal return-on-investment, reinforce modularity, and are rooted in real usage patterns, never speculative additions.\\n                    **Preservation of Code Style & Cohesive Self-Description**: Maintain and reinforce strict code style: self-explanatory code with minimal and concise comments, single-line docstrings only as absolutely required, consistent import aliasing, and absolute cohesion. Ensure the codebase remains inherently interpretable and visually self-descriptive at all times.\\\"```'\\n\\n\",\n  \"unified_synergic_instruction\": \"Systematically immerse yourself in the entire codebase to cultivate a deep, virtues-rooted understanding of its architecture, patterns, and logic; formally extract and codify the unique, high-functioning virtues as a 'virtues-core' overlay; rigorously respect and reference this overlay as the foundation for all further analysis. Perform an ongoing, visually-abstract and meta-information-driven examination of systemic relationships and usage patterns, ensuring all information and structures are inherently interpretable and self-describing. Critically assess the codebase to surface the single, most critical, high-impact, low-complexity improvement opportunity, ensuring the proposal is strictly actionable, amplifies and reinforces the anchored virtues, and uncompromisingly reduces size, redundancy, and complexity without introducing new features or unnecessary elements. Rigorously validate the proposal against the virtues-core to guarantee it preserves and strengthens systemic coherence, clarity, and adaptability within the project’s established code style: mandate self-explanatory code, minimal concise comments (<10%), single-line docstrings only if strictly needed, and consistent import aliasing. Prohibit all speculative, additive, or complexity-increasing suggestions and ensure every proposal directly contributes to a closed, self-reinforcing evolution loop grounded in actual codebase usage, always prioritizing consolidation before expansion. Do NOT implement improvements—only propose them within this framework, ensuring each suggestion is maximally valuable, minimalist, actionable, and tightly coupled to the system’s existing excellence.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\"philosophical_maxim\":\"True improvement honors and amplifies what is already excellent, proposing only that which consolidates and clarifies the system’s deepest virtues, so that every evolution is rooted in respect, necessity, and minimalist coherence.\"}",
        }
      }
    }