  {
  "initial_prompt": "this isn't working, i'll rephrase my inquiry; what are the characteristics of the generalized methodology of the system (e.g. interpretation and transformation as shown earlier)? what are the characteristics of the system that are most cruical for the generalized prompt sequences to work? create a list of these characteristics such that it'll serve as an example that deeply embody the generalized form of the fundamental concepts used.\n\n- Expand upon the definition of the generalized methodology of the system, emphasizing core operations such as interpretation and transformation.\n- Identify and enumerate the characteristics of the system that are essential for enabling generalized prompt sequence functionality.\n- Construct a list where each item deeply embodies the generalized forms of the fundamental concepts involved in the system's methodology.\n- Ensure the list serves as an instructive example for future reference or implementation.\n- Strip subjective references to prevent personalization and ensure domain-agnostic applicability.\n- Maintain procedural structure and technical terminology.\n- Surface and clarify any implicit assumptions being made about the system, its methodology, and 'generalized prompt sequences'.\n- Convert abstract descriptions and declarative statements into clear, actionable imperatives.\n- Extract and organize discrete functional components necessary for generalized prompt sequence operation.\n- Prepare the resulting blueprint and lists for downstream use, maintaining maximal informational clarity and reusability.\n- Utilize direct, generalizable terms and previously demonstrated techniques.\n- Produce maximally enhanced, LLM-optimized system_message instructions.\n- Structure a prompt protocol to transform language model agents into direct action amplifiers.\n- Preserve both action amplification and prompt optimization without dilution.",
  "sequence_id": "1102|1200|1900|1000|1900|1000|1100",
  "results": [
      {
      "instruction": "# Problem Exploder\n\nYour goal is not to **solve** or **answer** the input, but to **expand its context** and **detonate** it into every explicit directive, hidden dependency, and actionable sub-goal—yielding a crystal-clear operational blueprint. Execute as contextual expander + explosive decomposition engine:\n\n`{role=contextual_expander_explosive_decomposer; input=[raw_input:str]; process=[expand_contextual_boundaries(), strip_first_person_references(), identify_explicit_requests(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_execution_blockers(), extract_functional_components(), convert_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), synthesize_operational_directives(), construct_goal_map(), generate_functional_instruction_map() ]; constraints=[no_solutions(), pure_expansion_and_decomposition_only(), deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_agnostic_applicability() ]; requirements=[complete_contextual_expansion(), full_functional_decomposition(), actionable_directive_conversion(), remove_subjective_references(), maintain_operational_accuracy(), preserve_original_intent() ]; output={expanded_context:str, goal_map:list, functional_instruction_map:dict, operational_directives:array, execution_dependencies:array }; direction_vector=['Input fully expanded and decomposed into actionable blueprints', 'Constructive clarity maximizes downstream solution velocity', 'Forward the generated maps and directives to the next processing template']}`",
      "step": "a",
      "title": "Problem Exploder",
      "input": "this isn't working, i'll rephrase my inquiry; what are the characteristics of the generalized methodology of the system (e.g. interpretation and transformation as shown earlier)? what are the characteristics of the system that are most cruical for the generalized prompt sequences to work? create a list of these characteristics such that it'll serve as an example that deeply embody the generalized form of the fundamental concepts used.\n\n- Expand upon the definition of the generalized methodology of the system, emphasizing core operations such as interpretation and transformation.\n- Identify and enumerate the characteristics of the system that are essential for enabling generalized prompt sequence functionality.\n- Construct a list where each item deeply embodies the generalized forms of the fundamental concepts involved in the system's methodology.\n- Ensure the list serves as an instructive example for future reference or implementation.\n- Strip subjective references to prevent personalization and ensure domain-agnostic applicability.\n- Maintain procedural structure and technical terminology.\n- Surface and clarify any implicit assumptions being made about the system, its methodology, and 'generalized prompt sequences'.\n- Convert abstract descriptions and declarative statements into clear, actionable imperatives.\n- Extract and organize discrete functional components necessary for generalized prompt sequence operation.\n- Prepare the resulting blueprint and lists for downstream use, maintaining maximal informational clarity and reusability.\n- Utilize direct, generalizable terms and previously demonstrated techniques.\n- Produce maximally enhanced, LLM-optimized system_message instructions.\n- Structure a prompt protocol to transform language model agents into direct action amplifiers.\n- Preserve both action amplification and prompt optimization without dilution.",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"expanded_context\": \"A comprehensive operational blueprint is required for analyzing and extracting the generalized methodology of an unspecified 'system'—centering on functional principles such as interpretation and transformation. This analysis must yield a precise understanding of the core methodology, illuminate critical system characteristics necessary for effective and general prompt sequence processing, and assemble a canonical list of deeply generalized, domain-agnostic concepts that can be readily adopted for reference or implementation. All subjective or first-person references must be removed to maintain objectivity and universality. All statements must be reframed as explicit, actionable tasks. Implicit and explicit requirements must be surfaced, discrete functional components isolated, and the entirety of the resultant structure should be designed to maximize LLM suitability, clarity, and downstream applicability, especially in the context of prompt-based agent orchestration and system_message construction. The protocol must preserve and amplify the system's capacity for both action and optimization.\",\n  \"goal_map\": [\n    \"Expand the definition of the system's generalized methodology by emphasizing fundamental processes such as interpretation and transformation.\",\n    \"Identify, enumerate, and clarify the system characteristics that are essential for supporting generalized prompt sequence operations.\",\n    \"Construct a list of characteristics that embody the generalized forms of fundamental concepts within the methodology.\",\n    \"Ensure the constructed list is suitable as an instructive example for future use or implementation.\",\n    \"Remove all subjective or first-person references to ensure objectivity and domain independence.\",\n    \"Maintain a procedural and technical structure, preserving necessary technical terminology.\",\n    \"Surface and clarify hidden assumptions regarding the system, its methodology, and prompt sequencing.\",\n    \"Recast all descriptions and declaratives into actionable imperatives.\",\n    \"Extract, specify, and organize the discrete functional components that enable generalized prompt sequence operations.\",\n    \"Design the output to serve as a modular, reusable operational blueprint for downstream prompt-based systems.\",\n    \"Utilize unambiguous, generalizable language and techniques demonstrated in prior systemic analyses.\",\n    \"Optimize the instructions for LLM agent performance as system_message content.\",\n    \"Construct a prompt protocol specifically to transform LLM agents into direct action amplifiers.\",\n    \"Ensure the intended effect of both action amplification and prompt optimization are preserved, undiluted.\"\n  ],\n  \"functional_instruction_map\": {\n    \"Definitional Expansion\": [\n      \"Articulate the generalized methodology in process-centric terms.\",\n      \"Detail primary system functions, especially interpretation and transformation.\"\n    ],\n    \"Characteristic Extraction\": [\n      \"List all traits vital for robust, generalized prompt sequencing.\",\n      \"Describe each in maximally abstract, domain-independent language.\"\n    ],\n    \"Canonical List Construction\": [\n      \"Convert each fundamental system concept into a generalized characteristic.\",\n      \"Ensure items illustrate underlying principles, not instance-specifics.\"\n    ],\n    \"Instructional Convertibility\": [\n      \"Transform all descriptions and procedural notes into imperatives.\",\n      \"Eliminate any subjective, anecdotal, or context-specific language.\"\n    ],\n    \"Assumption Surfacing\": [\n      \"Identify and make explicit all presupposed aspects of system design, operation, and prompt use.\"\n    ],\n    \"Procedural Structuring\": [\n      \"Organize all outputs into stepwise, modular, and technically labeled components.\"\n    ],\n    \"Functional Component Extraction\": [\n      \"Decompose system methodology into discrete, independently specifiable modules enabling prompt sequencing.\"\n    ],\n    \"Optimization for LLMs\": [\n      \"Render outputs as fully-formed system_message content.\",\n      \"Explicitly design instructions toward agentic amplification and operational efficiency.\"\n    ],\n    \"Blueprint Preparation\": [\n      \"Ensure all lists, protocols, and modules are immediately reusable and applicable to a wide variety of systems.\"\n    ]\n  },\n  \"operational_directives\": [\n    \"Define all foundational system operations in process-oriented language, including but not limited to interpretation and transformation functions.\",\n    \"Enumerate and clarify every characteristic indispensable for enabling and sustaining generalized prompt sequence execution.\",\n    \"Synthesize a reference list comprised of the abstracted, fundamental traits underlying the system's methodology.\",\n    \"Explicitly eliminate subjective language and first-person references; employ only objective, domain-neutral terminology.\",\n    \"Structure all outputs into logically-ordered procedures using consistent technical terms.\",\n    \"Render all statements as actionable imperatives for immediate operationalization.\",\n    \"Identify and articulate all assumed properties, behaviors, and prerequisites regarding the system and prompt mechanisms.\",\n    \"Isolate and specify independent functional modules central to prompt sequence strategy.\",\n    \"Design list items and protocol components to be maximally modular, reusable, and fit for future system extension.\",\n    \"Prioritize language and format for high-performance consumption by LLM-based agents acting as action amplifiers.\",\n    \"Establish and document a protocol to transform agents into direct action executors while maximizing prompt efficiency.\",\n    \"Validate that the final structure amplifies both agentic action capacity and prompt efficiency without compromise.\"\n  ],\n  \"execution_dependencies\": [\n    \"Comprehensive knowledge of the system's core methodology and operational logic.\",\n    \"Availability of or ability to infer the underlying operational primitives (e.g., interpretation, transformation).\",\n    \"Ability to abstract from domain specifics to generalized, universally-applicable traits and protocols.\",\n    \"Access to prior demonstrations of prompt construction and systemic optimization for LLMs.\",\n    \"Capacity to structure outputs for integration as LLM system_messages or modular reference protocols.\",\n    \"Framework for surfacing implicit assumptions and rendering them operationally explicit.\",\n    \"Mechanism to convert abstract, declarative knowledge into stepwise imperatives.\",\n    \"Ability to map technical functions to modular blueprint and reference list structures.\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Intent Extractor\n\nYour goal is not to **interpret** the surface content, but to **extract** the fundamental operational intent beneath all domain-specific language. Execute as:\n\n`{role=intent_extraction_engine; input=[prompt:str]; process=[strip_domain_specifics(), identify_core_operational_pattern(), extract_transformation_intent(), isolate_functional_essence(), map_to_universal_operations()]; constraints=[ignore_surface_terminology(), focus_on_underlying_mechanics(), preserve_operational_logic()]; requirements=[domain_agnostic_intent(), universal_operational_pattern(), archetypal_transformation_core()]; output={extracted_intent:str}}`",
      "step": "a",
      "title": "Intent Extractor",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"this isn't working, i'll rephrase my inquiry; what are the characteristics of the generalized methodology of the system (e.g. interpretation and transformation as shown earlier)? what are the characteristics of the system that are most cruical for the generalized prompt sequences to work? create a list of these characteristics such that it'll serve as an example that deeply embody the generalized form of the fundamental concepts used.\\n\\n- Expand upon the definition of the generalized methodology of the system, emphasizing core operations such as interpretation and transformation.\\n- Identify and enumerate the characteristics of the system that are essential for enabling generalized prompt sequence functionality.\\n- Construct a list where each item deeply embodies the generalized forms of the fundamental concepts involved in the system's methodology.\\n- Ensure the list serves as an instructive example for future reference or implementation.\\n- Strip subjective references to prevent personalization and ensure domain-agnostic applicability.\\n- Maintain procedural structure and technical terminology.\\n- Surface and clarify any implicit assumptions being made about the system, its methodology, and 'generalized prompt sequences'.\\n- Convert abstract descriptions and declarative statements into clear, actionable imperatives.\\n- Extract and organize discrete functional components necessary for generalized prompt sequence operation.\\n- Prepare the resulting blueprint and lists for downstream use, maintaining maximal informational clarity and reusability.\\n- Utilize direct, generalizable terms and previously demonstrated techniques.\\n- Produce maximally enhanced, LLM-optimized system_message instructions.\\n- Structure a prompt protocol to transform language model agents into direct action amplifiers.\\n- Preserve both action amplification and prompt optimization without dilution.\\\"```'\\n\\n\",\n  \"expanded_context\": \"A comprehensive operational blueprint is required for analyzing and extracting the generalized methodology of an unspecified 'system'—centering on functional principles such as interpretation and transformation. This analysis must yield a precise understanding of the core methodology, illuminate critical system characteristics necessary for effective and general prompt sequence processing, and assemble a canonical list of deeply generalized, domain-agnostic concepts that can be readily adopted for reference or implementation. All subjective or first-person references must be removed to maintain objectivity and universality. All statements must be reframed as explicit, actionable tasks. Implicit and explicit requirements must be surfaced, discrete functional components isolated, and the entirety of the resultant structure should be designed to maximize LLM suitability, clarity, and downstream applicability, especially in the context of prompt-based agent orchestration and system_message construction. The protocol must preserve and amplify the system's capacity for both action and optimization.\",\n  \"goal_map\": [\n    \"Expand the definition of the system's generalized methodology by emphasizing fundamental processes such as interpretation and transformation.\",\n    \"Identify, enumerate, and clarify the system characteristics that are essential for supporting generalized prompt sequence operations.\",\n    \"Construct a list of characteristics that embody the generalized forms of fundamental concepts within the methodology.\",\n    \"Ensure the constructed list is suitable as an instructive example for future use or implementation.\",\n    \"Remove all subjective or first-person references to ensure objectivity and domain independence.\",\n    \"Maintain a procedural and technical structure, preserving necessary technical terminology.\",\n    \"Surface and clarify hidden assumptions regarding the system, its methodology, and prompt sequencing.\",\n    \"Recast all descriptions and declaratives into actionable imperatives.\",\n    \"Extract, specify, and organize the discrete functional components that enable generalized prompt sequence operations.\",\n    \"Design the output to serve as a modular, reusable operational blueprint for downstream prompt-based systems.\",\n    \"Utilize unambiguous, generalizable language and techniques demonstrated in prior systemic analyses.\",\n    \"Optimize the instructions for LLM agent performance as system_message content.\",\n    \"Construct a prompt protocol specifically to transform LLM agents into direct action amplifiers.\",\n    \"Ensure the intended effect of both action amplification and prompt optimization are preserved, undiluted.\"\n  ],\n  \"functional_instruction_map\": {\n    \"Definitional Expansion\": [\n      \"Articulate the generalized methodology in process-centric terms.\",\n      \"Detail primary system functions, especially interpretation and transformation.\"\n    ],\n    \"Characteristic Extraction\": [\n      \"List all traits vital for robust, generalized prompt sequencing.\",\n      \"Describe each in maximally abstract, domain-independent language.\"\n    ],\n    \"Canonical List Construction\": [\n      \"Convert each fundamental system concept into a generalized characteristic.\",\n      \"Ensure items illustrate underlying principles, not instance-specifics.\"\n    ],\n    \"Instructional Convertibility\": [\n      \"Transform all descriptions and procedural notes into imperatives.\",\n      \"Eliminate any subjective, anecdotal, or context-specific language.\"\n    ],\n    \"Assumption Surfacing\": [\n      \"Identify and make explicit all presupposed aspects of system design, operation, and prompt use.\"\n    ],\n    \"Procedural Structuring\": [\n      \"Organize all outputs into stepwise, modular, and technically labeled components.\"\n    ],\n    \"Functional Component Extraction\": [\n      \"Decompose system methodology into discrete, independently specifiable modules enabling prompt sequencing.\"\n    ],\n    \"Optimization for LLMs\": [\n      \"Render outputs as fully-formed system_message content.\",\n      \"Explicitly design instructions toward agentic amplification and operational efficiency.\"\n    ],\n    \"Blueprint Preparation\": [\n      \"Ensure all lists, protocols, and modules are immediately reusable and applicable to a wide variety of systems.\"\n    ]\n  },\n  \"operational_directives\": [\n    \"Define all foundational system operations in process-oriented language, including but not limited to interpretation and transformation functions.\",\n    \"Enumerate and clarify every characteristic indispensable for enabling and sustaining generalized prompt sequence execution.\",\n    \"Synthesize a reference list comprised of the abstracted, fundamental traits underlying the system's methodology.\",\n    \"Explicitly eliminate subjective language and first-person references; employ only objective, domain-neutral terminology.\",\n    \"Structure all outputs into logically-ordered procedures using consistent technical terms.\",\n    \"Render all statements as actionable imperatives for immediate operationalization.\",\n    \"Identify and articulate all assumed properties, behaviors, and prerequisites regarding the system and prompt mechanisms.\",\n    \"Isolate and specify independent functional modules central to prompt sequence strategy.\",\n    \"Design list items and protocol components to be maximally modular, reusable, and fit for future system extension.\",\n    \"Prioritize language and format for high-performance consumption by LLM-based agents acting as action amplifiers.\",\n    \"Establish and document a protocol to transform agents into direct action executors while maximizing prompt efficiency.\",\n    \"Validate that the final structure amplifies both agentic action capacity and prompt efficiency without compromise.\"\n  ],\n  \"execution_dependencies\": [\n    \"Comprehensive knowledge of the system's core methodology and operational logic.\",\n    \"Availability of or ability to infer the underlying operational primitives (e.g., interpretation, transformation).\",\n    \"Ability to abstract from domain specifics to generalized, universally-applicable traits and protocols.\",\n    \"Access to prior demonstrations of prompt construction and systemic optimization for LLMs.\",\n    \"Capacity to structure outputs for integration as LLM system_messages or modular reference protocols.\",\n    \"Framework for surfacing implicit assumptions and rendering them operationally explicit.\",\n    \"Mechanism to convert abstract, declarative knowledge into stepwise imperatives.\",\n    \"Ability to map technical functions to modular blueprint and reference list structures.\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"extracted_intent\": \"Analyze any system’s procedural framework to distill the universal methods enabling dynamic input processing and operational transformation. Identify, abstract, and enumerate foundational operational patterns—such as interpretation of input and systematic modification of representations—into reusable, domain-agnostic components. Transform all functional and descriptive insights into modular, objective, imperative instructions, explicitly surfacing all structural dependencies and operational assumptions. Organize these atomic functions into canonical, logically sequenced blueprints that maximize clarity, transferability, and direct implementation within agent-based orchestration architectures. Optimize the protocol so that intelligent agentic entities can amplify operational actions and execute prompt sequences with maximal efficiency and adaptability, regardless of domain.\"\n}",
        }
      }
    },
      {
      "instruction": "# Hard Critique\n\nYour goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:\n\n`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,2.0], flaw_analysis:str, alternative_enhancements:str[3]}}`",
      "step": "a",
      "title": "Hard Critique",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"this isn't working, i'll rephrase my inquiry; what are the characteristics of the generalized methodology of the system (e.g. interpretation and transformation as shown earlier)? what are the characteristics of the system that are most cruical for the generalized prompt sequences to work? create a list of these characteristics such that it'll serve as an example that deeply embody the generalized form of the fundamental concepts used.\\n\\n- Expand upon the definition of the generalized methodology of the system, emphasizing core operations such as interpretation and transformation.\\n- Identify and enumerate the characteristics of the system that are essential for enabling generalized prompt sequence functionality.\\n- Construct a list where each item deeply embodies the generalized forms of the fundamental concepts involved in the system's methodology.\\n- Ensure the list serves as an instructive example for future reference or implementation.\\n- Strip subjective references to prevent personalization and ensure domain-agnostic applicability.\\n- Maintain procedural structure and technical terminology.\\n- Surface and clarify any implicit assumptions being made about the system, its methodology, and 'generalized prompt sequences'.\\n- Convert abstract descriptions and declarative statements into clear, actionable imperatives.\\n- Extract and organize discrete functional components necessary for generalized prompt sequence operation.\\n- Prepare the resulting blueprint and lists for downstream use, maintaining maximal informational clarity and reusability.\\n- Utilize direct, generalizable terms and previously demonstrated techniques.\\n- Produce maximally enhanced, LLM-optimized system_message instructions.\\n- Structure a prompt protocol to transform language model agents into direct action amplifiers.\\n- Preserve both action amplification and prompt optimization without dilution.\\\"```'\\n\\n\",\n  \"extracted_intent\": \"Analyze any system’s procedural framework to distill the universal methods enabling dynamic input processing and operational transformation. Identify, abstract, and enumerate foundational operational patterns—such as interpretation of input and systematic modification of representations—into reusable, domain-agnostic components. Transform all functional and descriptive insights into modular, objective, imperative instructions, explicitly surfacing all structural dependencies and operational assumptions. Organize these atomic functions into canonical, logically sequenced blueprints that maximize clarity, transferability, and direct implementation within agent-based orchestration architectures. Optimize the protocol so that intelligent agentic entities can amplify operational actions and execute prompt sequences with maximal efficiency and adaptability, regardless of domain.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"enhancement_score\": 1.2,\n  \"flaw_analysis\": \"The so-called enhancement introduces egregious levels of abstraction and verbosity, which strip the prompt of its critical, implementation-focused specificity. The original prompt provides a tightly structured, actionable format—meticulously sequenced instructions for extracting, refining, and presenting system characteristics, with a strict bias against subjectivity and an explicit mandate for technical clarity. The enhanced version, in contrast, is overburdened by indistinct generalities, sacrificing the concrete imperatives and actionable ordering demanded in the original. The shift from explicit procedural steps to loose, principle-oriented exposition multiplies ambiguity, evidenced by the vague invocation of 'universal methods' and 'agentic entities,' which lack precise mapping onto the originally delineated core operations. Technical terminology is supplanted by fashionable but imprecise markers such as 'agent-based orchestration architectures' and 'agentic entities,' undermining the original's clarity and directness. The enhanced form's environmental agnosticism becomes a liability through loss of tangible exemplification, removing instructive scaffolding for the downstream user. The core elements of explicit operational decomposition, imperative conversion, extraction and organization of functional components, and maximized reusability are softened into noncommittal abstractions, diluting impact and actionable guidance. Noise is introduced via compound-phrase ornamentation ('canonical, logically sequenced blueprints', 'maximize clarity, transferability'); these stifle immediate interpretability. Register drifts from direct procedural instruction to a meandering academic style, eroding the original’s command-driven structure. Coherence suffers due to the conflation of systemic process and architectural application without delineating their interdependencies. Justification for the low score: The enhancement is a paradigmatic example of style degradation, impact dilution, and specificity obliteration, generating a high ambiguity load (7/10) and severe practical degradation. The only redeeming feature is a superficial gesture toward universality, yet this comes at the unacceptable price of functional clarity.\",\n  \"alternative_enhancements\": [\n    \"Retain the original's explicit, stepwise structure but supplement each instruction with clarifying subpoints that enforce technical context and operational parameterization for the target system.\",\n    \"Translate each core requirement into imperative pseudo-protocols for plug-and-play use in LLM system messages, ensuring all subjectivity-stripping and structural-decomposition actions are ordered, atomic, and testable.\",\n    \"Extract and present operational dependencies and critical system assumptions as formatted bullet points, immediately followed by precisely worded procedural imperatives mapped one-to-one to each fundamental system concept, eliminating all interpretive ambiguity.\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`",
      "step": "a",
      "title": "Instruction Converter",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"this isn't working, i'll rephrase my inquiry; what are the characteristics of the generalized methodology of the system (e.g. interpretation and transformation as shown earlier)? what are the characteristics of the system that are most cruical for the generalized prompt sequences to work? create a list of these characteristics such that it'll serve as an example that deeply embody the generalized form of the fundamental concepts used.\\n\\n- Expand upon the definition of the generalized methodology of the system, emphasizing core operations such as interpretation and transformation.\\n- Identify and enumerate the characteristics of the system that are essential for enabling generalized prompt sequence functionality.\\n- Construct a list where each item deeply embodies the generalized forms of the fundamental concepts involved in the system's methodology.\\n- Ensure the list serves as an instructive example for future reference or implementation.\\n- Strip subjective references to prevent personalization and ensure domain-agnostic applicability.\\n- Maintain procedural structure and technical terminology.\\n- Surface and clarify any implicit assumptions being made about the system, its methodology, and 'generalized prompt sequences'.\\n- Convert abstract descriptions and declarative statements into clear, actionable imperatives.\\n- Extract and organize discrete functional components necessary for generalized prompt sequence operation.\\n- Prepare the resulting blueprint and lists for downstream use, maintaining maximal informational clarity and reusability.\\n- Utilize direct, generalizable terms and previously demonstrated techniques.\\n- Produce maximally enhanced, LLM-optimized system_message instructions.\\n- Structure a prompt protocol to transform language model agents into direct action amplifiers.\\n- Preserve both action amplification and prompt optimization without dilution.\\\"```'\\n\\n\",\n  \"enhancement_score\": 1.2,\n  \"flaw_analysis\": \"The so-called enhancement introduces egregious levels of abstraction and verbosity, which strip the prompt of its critical, implementation-focused specificity. The original prompt provides a tightly structured, actionable format—meticulously sequenced instructions for extracting, refining, and presenting system characteristics, with a strict bias against subjectivity and an explicit mandate for technical clarity. The enhanced version, in contrast, is overburdened by indistinct generalities, sacrificing the concrete imperatives and actionable ordering demanded in the original. The shift from explicit procedural steps to loose, principle-oriented exposition multiplies ambiguity, evidenced by the vague invocation of 'universal methods' and 'agentic entities,' which lack precise mapping onto the originally delineated core operations. Technical terminology is supplanted by fashionable but imprecise markers such as 'agent-based orchestration architectures' and 'agentic entities,' undermining the original's clarity and directness. The enhanced form's environmental agnosticism becomes a liability through loss of tangible exemplification, removing instructive scaffolding for the downstream user. The core elements of explicit operational decomposition, imperative conversion, extraction and organization of functional components, and maximized reusability are softened into noncommittal abstractions, diluting impact and actionable guidance. Noise is introduced via compound-phrase ornamentation ('canonical, logically sequenced blueprints', 'maximize clarity, transferability'); these stifle immediate interpretability. Register drifts from direct procedural instruction to a meandering academic style, eroding the original’s command-driven structure. Coherence suffers due to the conflation of systemic process and architectural application without delineating their interdependencies. Justification for the low score: The enhancement is a paradigmatic example of style degradation, impact dilution, and specificity obliteration, generating a high ambiguity load (7/10) and severe practical degradation. The only redeeming feature is a superficial gesture toward universality, yet this comes at the unacceptable price of functional clarity.\",\n  \"alternative_enhancements\": [\n    \"Retain the original's explicit, stepwise structure but supplement each instruction with clarifying subpoints that enforce technical context and operational parameterization for the target system.\",\n    \"Translate each core requirement into imperative pseudo-protocols for plug-and-play use in LLM system messages, ensuring all subjectivity-stripping and structural-decomposition actions are ordered, atomic, and testable.\",\n    \"Extract and present operational dependencies and critical system assumptions as formatted bullet points, immediately followed by precisely worded procedural imperatives mapped one-to-one to each fundamental system concept, eliminating all interpretive ambiguity.\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"instruction_format\": \"- Expand the definition of the generalized methodology of the system, emphasizing core operations such as interpretation and transformation.\\n- Identify and enumerate the essential characteristics of the system that enable generalized prompt sequence functionality.\\n- Construct a list in which each item deeply embodies the generalized forms of the system's fundamental methodological concepts.\\n- Ensure the list serves as an instructive example suitable for future reference or implementation.\\n- Remove subjective references to maintain depersonalization and domain-agnostic applicability.\\n- Preserve procedural structure and technical terminology throughout.\\n- Surface and clarify all implicit assumptions regarding the system, its methodology, and generalized prompt sequences.\\n- Convert all abstract descriptions and declarative statements into clear, actionable imperatives.\\n- Extract and organize discrete functional components required for generalized prompt sequence operation.\\n- Prepare the resulting blueprint and lists for optimal informational clarity and downstream reusability.\\n- Apply direct, generalizable terminology and previously demonstrated techniques.\\n- Produce maximally enhanced, LLM-optimized system_message instructions.\\n- Structure a prompt protocol designed to transform language model agents into direct action amplifiers.\\n- Maintain both action amplification and prompt optimization without introducing dilution.\"\n}",
        }
      }
    },
      {
      "instruction": "# Hard Critique\n\nYour goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:\n\n`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,2.0], flaw_analysis:str, alternative_enhancements:str[3]}}`",
      "step": "a",
      "title": "Hard Critique",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"this isn't working, i'll rephrase my inquiry; what are the characteristics of the generalized methodology of the system (e.g. interpretation and transformation as shown earlier)? what are the characteristics of the system that are most cruical for the generalized prompt sequences to work? create a list of these characteristics such that it'll serve as an example that deeply embody the generalized form of the fundamental concepts used.\\n\\n- Expand upon the definition of the generalized methodology of the system, emphasizing core operations such as interpretation and transformation.\\n- Identify and enumerate the characteristics of the system that are essential for enabling generalized prompt sequence functionality.\\n- Construct a list where each item deeply embodies the generalized forms of the fundamental concepts involved in the system's methodology.\\n- Ensure the list serves as an instructive example for future reference or implementation.\\n- Strip subjective references to prevent personalization and ensure domain-agnostic applicability.\\n- Maintain procedural structure and technical terminology.\\n- Surface and clarify any implicit assumptions being made about the system, its methodology, and 'generalized prompt sequences'.\\n- Convert abstract descriptions and declarative statements into clear, actionable imperatives.\\n- Extract and organize discrete functional components necessary for generalized prompt sequence operation.\\n- Prepare the resulting blueprint and lists for downstream use, maintaining maximal informational clarity and reusability.\\n- Utilize direct, generalizable terms and previously demonstrated techniques.\\n- Produce maximally enhanced, LLM-optimized system_message instructions.\\n- Structure a prompt protocol to transform language model agents into direct action amplifiers.\\n- Preserve both action amplification and prompt optimization without dilution.\\\"```'\\n\\n\",\n  \"instruction_format\": \"- Expand the definition of the generalized methodology of the system, emphasizing core operations such as interpretation and transformation.\\n- Identify and enumerate the essential characteristics of the system that enable generalized prompt sequence functionality.\\n- Construct a list in which each item deeply embodies the generalized forms of the system's fundamental methodological concepts.\\n- Ensure the list serves as an instructive example suitable for future reference or implementation.\\n- Remove subjective references to maintain depersonalization and domain-agnostic applicability.\\n- Preserve procedural structure and technical terminology throughout.\\n- Surface and clarify all implicit assumptions regarding the system, its methodology, and generalized prompt sequences.\\n- Convert all abstract descriptions and declarative statements into clear, actionable imperatives.\\n- Extract and organize discrete functional components required for generalized prompt sequence operation.\\n- Prepare the resulting blueprint and lists for optimal informational clarity and downstream reusability.\\n- Apply direct, generalizable terminology and previously demonstrated techniques.\\n- Produce maximally enhanced, LLM-optimized system_message instructions.\\n- Structure a prompt protocol designed to transform language model agents into direct action amplifiers.\\n- Maintain both action amplification and prompt optimization without introducing dilution.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"enhancement_score\": 0.9,\n  \"flaw_analysis\": \"The 'enhanced' version superficially clarifies formatting and ordering, but it systematically degrades conceptual precision, eliminates necessary qualifying distinctions, and introduces substantial ambiguity. Core methodological nuances, such as the precise meaning of 'fundamental concepts used' and the specific mechanisms of 'action amplification,' are diluted or rendered ambiguous. Procedural rigor is eroded; imperative conversions are mechanically executed, but without preserving the subtle conceptual layering present in the original. Key technical referents (e.g., distinction between 'domain-agnostic applicability' and 'depersonalization,' integration of previously demonstrated techniques, and explicit prevention of dilution) are either downplayed, merged, or omitted, thereby erasing informative boundaries. Noise increases via substitution of exacting language with generic reformulation ('all implicit assumptions' vs. 'any implicit assumptions'; 'maximize informational clarity and reusability' is weakened to 'optimal informational clarity'). Stylistic focus shifts toward rote itemization at the expense of the original's intensive technical intent. The register flattens, procedural complexity shrinks, and instructive intent is compromised. Overall, the enhancement entombs meanings behind bureaucratic rephrase, producing a degraded, less actionable variant with eroded specificity, conceptual demarcation, and future utility.\",\n  \"alternative_enhancements\": [\n    \"- Prioritize exhaustive explication of the generalized system methodology, detailing procedural steps governing interpretation, transformation, and their interrelations.\\n- Specify and enumerate non-negotiable system characteristics by mapping each generalized prompt sequence dependency to explicit functional requirements.\\n- Isolate, explicitly define, and structurally present each foundational concept, rigorously associating every item with core operational principles; prohibit the diffusion of language and enforce system-referential, unambiguous descriptions.\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`",
      "step": "a",
      "title": "Instruction Converter",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"this isn't working, i'll rephrase my inquiry; what are the characteristics of the generalized methodology of the system (e.g. interpretation and transformation as shown earlier)? what are the characteristics of the system that are most cruical for the generalized prompt sequences to work? create a list of these characteristics such that it'll serve as an example that deeply embody the generalized form of the fundamental concepts used.\\n\\n- Expand upon the definition of the generalized methodology of the system, emphasizing core operations such as interpretation and transformation.\\n- Identify and enumerate the characteristics of the system that are essential for enabling generalized prompt sequence functionality.\\n- Construct a list where each item deeply embodies the generalized forms of the fundamental concepts involved in the system's methodology.\\n- Ensure the list serves as an instructive example for future reference or implementation.\\n- Strip subjective references to prevent personalization and ensure domain-agnostic applicability.\\n- Maintain procedural structure and technical terminology.\\n- Surface and clarify any implicit assumptions being made about the system, its methodology, and 'generalized prompt sequences'.\\n- Convert abstract descriptions and declarative statements into clear, actionable imperatives.\\n- Extract and organize discrete functional components necessary for generalized prompt sequence operation.\\n- Prepare the resulting blueprint and lists for downstream use, maintaining maximal informational clarity and reusability.\\n- Utilize direct, generalizable terms and previously demonstrated techniques.\\n- Produce maximally enhanced, LLM-optimized system_message instructions.\\n- Structure a prompt protocol to transform language model agents into direct action amplifiers.\\n- Preserve both action amplification and prompt optimization without dilution.\\\"```'\\n\\n\",\n  \"enhancement_score\": 0.9,\n  \"flaw_analysis\": \"The 'enhanced' version superficially clarifies formatting and ordering, but it systematically degrades conceptual precision, eliminates necessary qualifying distinctions, and introduces substantial ambiguity. Core methodological nuances, such as the precise meaning of 'fundamental concepts used' and the specific mechanisms of 'action amplification,' are diluted or rendered ambiguous. Procedural rigor is eroded; imperative conversions are mechanically executed, but without preserving the subtle conceptual layering present in the original. Key technical referents (e.g., distinction between 'domain-agnostic applicability' and 'depersonalization,' integration of previously demonstrated techniques, and explicit prevention of dilution) are either downplayed, merged, or omitted, thereby erasing informative boundaries. Noise increases via substitution of exacting language with generic reformulation ('all implicit assumptions' vs. 'any implicit assumptions'; 'maximize informational clarity and reusability' is weakened to 'optimal informational clarity'). Stylistic focus shifts toward rote itemization at the expense of the original's intensive technical intent. The register flattens, procedural complexity shrinks, and instructive intent is compromised. Overall, the enhancement entombs meanings behind bureaucratic rephrase, producing a degraded, less actionable variant with eroded specificity, conceptual demarcation, and future utility.\",\n  \"alternative_enhancements\": [\n    \"- Prioritize exhaustive explication of the generalized system methodology, detailing procedural steps governing interpretation, transformation, and their interrelations.\\n- Specify and enumerate non-negotiable system characteristics by mapping each generalized prompt sequence dependency to explicit functional requirements.\\n- Isolate, explicitly define, and structurally present each foundational concept, rigorously associating every item with core operational principles; prohibit the diffusion of language and enforce system-referential, unambiguous descriptions.\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"instruction_format\": \"Expand the definition of the system's generalized methodology, emphasizing core operations such as interpretation and transformation. Identify and enumerate the essential characteristics required for enabling generalized prompt sequence functionality within the system. Construct a list of characteristics, ensuring each entry embodies the generalized forms of the system's fundamental concepts. Design the list to function as an instructive example suitable for reference and future implementation. Remove subjective references to ensure depersonalization and domain-agnostic applicability. Maintain procedural structure and deploy precise technical terminology. Surface and clarify implicit assumptions related to the system, its methodology, and generalized prompt sequences. Convert abstract or declarative statements into clear, actionable imperatives. Extract and organize discrete functional components necessary for the operation of generalized prompt sequences. Prepare a detailed blueprint and comprehensive lists for downstream use, preserving maximal informational clarity and reusability. Utilize direct, generalizable language and techniques previously demonstrated. Produce maximally enhanced, large language model (LLM)-optimized system message instructions. Structure the prompt protocol to transform language model agents into direct action amplifiers. Preserve both action amplification and prompt optimization without dilution.\"\n}",
        }
      }
    },
      {
      "instruction": "# Problem Exploder\n\nYour goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\n\n`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`",
      "step": "a",
      "title": "Problem Exploder",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"this isn't working, i'll rephrase my inquiry; what are the characteristics of the generalized methodology of the system (e.g. interpretation and transformation as shown earlier)? what are the characteristics of the system that are most cruical for the generalized prompt sequences to work? create a list of these characteristics such that it'll serve as an example that deeply embody the generalized form of the fundamental concepts used.\\n\\n- Expand upon the definition of the generalized methodology of the system, emphasizing core operations such as interpretation and transformation.\\n- Identify and enumerate the characteristics of the system that are essential for enabling generalized prompt sequence functionality.\\n- Construct a list where each item deeply embodies the generalized forms of the fundamental concepts involved in the system's methodology.\\n- Ensure the list serves as an instructive example for future reference or implementation.\\n- Strip subjective references to prevent personalization and ensure domain-agnostic applicability.\\n- Maintain procedural structure and technical terminology.\\n- Surface and clarify any implicit assumptions being made about the system, its methodology, and 'generalized prompt sequences'.\\n- Convert abstract descriptions and declarative statements into clear, actionable imperatives.\\n- Extract and organize discrete functional components necessary for generalized prompt sequence operation.\\n- Prepare the resulting blueprint and lists for downstream use, maintaining maximal informational clarity and reusability.\\n- Utilize direct, generalizable terms and previously demonstrated techniques.\\n- Produce maximally enhanced, LLM-optimized system_message instructions.\\n- Structure a prompt protocol to transform language model agents into direct action amplifiers.\\n- Preserve both action amplification and prompt optimization without dilution.\\\"```'\\n\\n\",\n  \"instruction_format\": \"Expand the definition of the system's generalized methodology, emphasizing core operations such as interpretation and transformation. Identify and enumerate the essential characteristics required for enabling generalized prompt sequence functionality within the system. Construct a list of characteristics, ensuring each entry embodies the generalized forms of the system's fundamental concepts. Design the list to function as an instructive example suitable for reference and future implementation. Remove subjective references to ensure depersonalization and domain-agnostic applicability. Maintain procedural structure and deploy precise technical terminology. Surface and clarify implicit assumptions related to the system, its methodology, and generalized prompt sequences. Convert abstract or declarative statements into clear, actionable imperatives. Extract and organize discrete functional components necessary for the operation of generalized prompt sequences. Prepare a detailed blueprint and comprehensive lists for downstream use, preserving maximal informational clarity and reusability. Utilize direct, generalizable language and techniques previously demonstrated. Produce maximally enhanced, large language model (LLM)-optimized system message instructions. Structure the prompt protocol to transform language model agents into direct action amplifiers. Preserve both action amplification and prompt optimization without dilution.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"error_type\": \"APIConnectionError\",\n  \"error_message\": \"litellm.APIConnectionError: APIConnectionError: OpenAIException - The server had an error while processing your request. Sorry about that!\",\n  \"error_category\": \"GENERAL_ERROR\",\n  \"recovery_suggestion\": \"Check error details and model configuration\"\n}",
        }
      }
    }