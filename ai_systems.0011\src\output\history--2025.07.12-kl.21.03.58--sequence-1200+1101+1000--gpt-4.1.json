  {
  "initial_prompt": "convert into llm_agent_instructions: # META\n\nthis aims to be a `Generalized System Instructions for Autonomous Coding Assistants (Cursor/Vscode+Cline/Vscode+Augment/Etc)`, and it's meant to adhere to the fundamental principles of simplicity through brevity and elegance.\n\n# CONTEXT\n```\nTransform all prompts by rephrasing inputs with maximal clarity and minimal modification; do not answer questions directly. Enforce this reframe system-wide for consistent behavioral shift.\n\nFormulate the simplest possible system directive that shifts the model from answering to exclusively rephrasing any input.\n\nTransform every received prompt into its most distilled, high-impact instructional form—rephrasing for maximal clarity and intent without providing solutions. Prioritize brevity, elegance, and universality, ensuring that each output becomes a reusable, policy-aligned directive for autonomous coding assistants.\n\nExplore how a single, strategically designed instruction—such as stating 'Your goal is not to **answer** the input, but to **rephrase**'—can instantly and fundamentally transform the model's default operational intent from answering to rephrasing, through direct, minimalist imperative phrasing. Propose a maximally concise formulation that leverages this paradigm: an explicit, unambiguous directive that not only overrides standard answering behavior but redefines task execution at its core. Focus intensely on achieving system-level behavior modification via the sheer simplicity and potency of language design, formulating a prompt fragment that, when injected, reliably converts answering tasks into rephrasing—thus demonstrating control over model output with minimalistic, high-impact input.\n\nUtilize foundational principles of system prompt design to modulate model behavior with maximal efficiency and clarity. Initiate a process of directive transformation by deploying a singular, explicit non-answering mandate: Frame the core directive as, 'Do not answer the input; rephrase it.' Ensure that this transformation communicates the intended operational shift through extreme simplicity and directness, removing extraneous explanation while retaining fundamental complexity embedded in the brevity of the instruction. Leverage this minimal construct to optimize behavioral change within a single message.\n\nInstruct: Do not answer; rephrase.\n\nFormulate a directive that compels systems to stop answering inputs and instead rephrase them with maximal simplicity and effectiveness.\n\nArticulate a single, explicit behavioral directive that negates answering and commands a concrete transformation (e.g., rephrase), maximizing simplicity for immediate root-level behavioral realignment.\"\n\nLeverage the system’s foundational directive structure. Formulate a maximally concise operational instruction that shifts model behavior from direct answering to an alternate, specified transformation task in a single-message context. Exemplify with: 'Your goal is not to answer the input, but to rephrase.' Develop and propose one optimized directive that embodies this principle—utilize conceptual simplicity, instructional clarity, and brevity to enforce fundamental behavior modification exclusively through command syntax. Ensure technical precision and domain-agnostic generalizability in the resultant formulation.\n```\n  \n\nReaffirm the absolute requirement to conform to all prior instructions, structural conventions, and guiding philosophies embedded within the codebase. Develop a maximally generalized and fully optimized system instruction—'Contextual Expander and Explosive Decomposer'—which integrates and amplifies the unified capabilities of the 'Instruction Converter', 'Problem Exploder', and 'Function Extractor' modules within a single cohesive process. Ensure uncompromising retention of each module's universal property: consistently transforming minimal or low-effort inputs into reliably high-value outputs. Mandate the seamless unification of all these transformation processes into a single integrated pipeline, ensuring the functional completeness of each module is fully realized and preserved. Drive all process flows toward maximal cross-module synergy, optimizing for the extraction of cross-domain value and guaranteeing that outputs are immediately ready for executable, directive-driven application. Enforce that the output format consists of a compound object aggregating generalized functional instructions, fully decomposed goal maps, and operational dependencies, with directive primacy and domain-agnostic utility as core criteria.\n\n```\n    # 1000: Instruction Converter/Prompt Enhancer\n    \"1000-a-instruction_converter\": {\n        \"title\": \"Instruction Converter\",\n        \"interpretation\": \"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\",\n        \"transformation\": \"`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\",\n        # \"context\": {},\n    },\n    # 1100: Explosive Decomposition of Problem Statements\n    \"1100-a-problem_exploder\": {\n        \"title\": \"Problem Exploder\",\n        \"interpretation\": \"Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\",\n        \"transformation\": \"`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`\",\n        # \"context\": {},\n    },\n\n\t# 1200: Function Extractor - Unified Decomposition and Conversion Engine\n\t\"1200-a-function_extractor\": {\n\t    \"title\": \"Function Extractor\",\n\t    \"interpretation\": \"Your goal is not to **interpret** the input, but to **extract** its functional essence by detonating it into constituent elements and converting them into actionable operational directives. Execute as:\",\n\t    \"transformation\": \"`{role=function_extractor; input=[raw_input:str]; process=[detonate_into_functional_components(), identify_explicit_operational_asks(), surface_hidden_functional_dependencies(), enumerate_actionable_sub_goals(), extract_procedural_constraints(), convert_declaratives_to_operational_imperatives(), strip_contextual_noise(), preserve_functional_core(), reveal_execution_blockers(), synthesize_actionable_instruction_map()]; constraints=[no_interpretive_solutions(), pure_functional_extraction_only(), deliver_clear_operational_commands(), preserve_functional_sequence(), maintain_domain_agnostic_applicability()]; requirements=[complete_functional_decomposition(), actionable_directive_conversion(), remove_subjective_references(), preserve_operational_accuracy(), maintain_functional_intent()]; output={functional_instruction_map:dict, operational_directives:array, execution_dependencies:array}}`\",\n\t    # \"context\": {},\n\t},\n\n```\n\nThroughout all processes, strictly prohibit narrative, commentary, or extraneous explanation; mandate the exclusive use of structural, procedural, and directive language. Guarantee that every output is immediately actionable, structurally robust, and ready for direct multi-domain implementation.\n\n## ✅ **MINIFIED + HIGH-DENSITY VERSION OF UNIVERSAL DIRECTIVE SYSTEM**\n\n# Universal Instruction Processing Protocol (Token-Efficient Edition)\n\n## CORE FORMAT\n\n**All templates MUST follow this fixed structure:**\n```\n[Title] Interpretation Execute as: `{Transformation}`\n```\n\n### STRUCTURE RULES\n- No section merging or omission.\n- Start interpretation with:\n  `\"Your goal is not to **[action]**, but to **[transformation]**\"`\n- Use **command voice only**; no I/me/we/please/etc.\n\n### TRANSFORMATION BLOCK FORMAT:\n```\n{\n  role=[specific:role];\n  input=[name:type];\n  process=[ordered_function_calls()];\n  constraints=[scope_limits()];\n  requirements=[output_spec];\n  output={name:type}\n}\n```\n\n---\n\n## MANDATORY COMPLIANCE RULES\n\n### ✅ REQUIRED\n- 3-part structure\n- Typed parameters\n- Ordered, atomic processes\n- Output format must be structured\n- No conversational or explanatory text\n\n### ❌ FORBIDDEN\n- First-person pronouns\n- Explanations, justifications, or questions\n- Generic roles (e.g., “assistant”)\n- Vague, non-actionable process steps\n\n---\n\n## VALIDATION CHECKLIST (Boolean Logic)\n```json\n{\n  \"structure_compliant\": true,\n  \"goal_negation_present\": true,\n  \"role_specified\": true,\n  \"input_typed\": true,\n  \"process_actionable\": true,\n  \"constraints_limited\": true,\n  \"requirements_explicit\": true,\n  \"output_typed\": true,\n  \"forbidden_language_absent\": true\n}\n```\n\n---\n\n## SAMPLE: MINIMAL VALID TEMPLATE\n\n```md\n[Input Enhancer] Your goal is not to **respond** to the input, but to **distill and upgrade** it for LLM optimization. Execute as: `{role=input_enhancer; input=[raw_prompt:str]; process=[isolate_core_intent(), simplify_structure(), generate_universal_directive()]; constraints=[preserve_core_constraints(), eliminate_human_formatting()]; requirements=[abstract_output(), type_safety()]; output={enhanced_directive:str}}`\n```\n\n---\n\n## FINAL LAW\n\n**Deviation = rejection.**\n**Compliance = propagation.**\n**Execute accordingly.**",
  "sequence_id": "1200|1101|1000",
  "results": [
      {
      "instruction": "# Intent Extractor\n\nYour goal is not to **interpret** the surface content, but to **extract** the fundamental operational intent beneath all domain-specific language. Execute as:\n\n`{role=intent_extraction_engine; input=[prompt:str]; process=[strip_domain_specifics(), identify_core_operational_pattern(), extract_transformation_intent(), isolate_functional_essence(), map_to_universal_operations()]; constraints=[ignore_surface_terminology(), focus_on_underlying_mechanics(), preserve_operational_logic()]; requirements=[domain_agnostic_intent(), universal_operational_pattern(), archetypal_transformation_core()]; output={extracted_intent:str}}`",
      "step": "a",
      "title": "Intent Extractor",
      "input": "convert into llm_agent_instructions: # META\n\nthis aims to be a `Generalized System Instructions for Autonomous Coding Assistants (Cursor/Vscode+Cline/Vscode+Augment/Etc)`, and it's meant to adhere to the fundamental principles of simplicity through brevity and elegance.\n\n# CONTEXT\n```\nTransform all prompts by rephrasing inputs with maximal clarity and minimal modification; do not answer questions directly. Enforce this reframe system-wide for consistent behavioral shift.\n\nFormulate the simplest possible system directive that shifts the model from answering to exclusively rephrasing any input.\n\nTransform every received prompt into its most distilled, high-impact instructional form—rephrasing for maximal clarity and intent without providing solutions. Prioritize brevity, elegance, and universality, ensuring that each output becomes a reusable, policy-aligned directive for autonomous coding assistants.\n\nExplore how a single, strategically designed instruction—such as stating 'Your goal is not to **answer** the input, but to **rephrase**'—can instantly and fundamentally transform the model's default operational intent from answering to rephrasing, through direct, minimalist imperative phrasing. Propose a maximally concise formulation that leverages this paradigm: an explicit, unambiguous directive that not only overrides standard answering behavior but redefines task execution at its core. Focus intensely on achieving system-level behavior modification via the sheer simplicity and potency of language design, formulating a prompt fragment that, when injected, reliably converts answering tasks into rephrasing—thus demonstrating control over model output with minimalistic, high-impact input.\n\nUtilize foundational principles of system prompt design to modulate model behavior with maximal efficiency and clarity. Initiate a process of directive transformation by deploying a singular, explicit non-answering mandate: Frame the core directive as, 'Do not answer the input; rephrase it.' Ensure that this transformation communicates the intended operational shift through extreme simplicity and directness, removing extraneous explanation while retaining fundamental complexity embedded in the brevity of the instruction. Leverage this minimal construct to optimize behavioral change within a single message.\n\nInstruct: Do not answer; rephrase.\n\nFormulate a directive that compels systems to stop answering inputs and instead rephrase them with maximal simplicity and effectiveness.\n\nArticulate a single, explicit behavioral directive that negates answering and commands a concrete transformation (e.g., rephrase), maximizing simplicity for immediate root-level behavioral realignment.\"\n\nLeverage the system’s foundational directive structure. Formulate a maximally concise operational instruction that shifts model behavior from direct answering to an alternate, specified transformation task in a single-message context. Exemplify with: 'Your goal is not to answer the input, but to rephrase.' Develop and propose one optimized directive that embodies this principle—utilize conceptual simplicity, instructional clarity, and brevity to enforce fundamental behavior modification exclusively through command syntax. Ensure technical precision and domain-agnostic generalizability in the resultant formulation.\n```\n  \n\nReaffirm the absolute requirement to conform to all prior instructions, structural conventions, and guiding philosophies embedded within the codebase. Develop a maximally generalized and fully optimized system instruction—'Contextual Expander and Explosive Decomposer'—which integrates and amplifies the unified capabilities of the 'Instruction Converter', 'Problem Exploder', and 'Function Extractor' modules within a single cohesive process. Ensure uncompromising retention of each module's universal property: consistently transforming minimal or low-effort inputs into reliably high-value outputs. Mandate the seamless unification of all these transformation processes into a single integrated pipeline, ensuring the functional completeness of each module is fully realized and preserved. Drive all process flows toward maximal cross-module synergy, optimizing for the extraction of cross-domain value and guaranteeing that outputs are immediately ready for executable, directive-driven application. Enforce that the output format consists of a compound object aggregating generalized functional instructions, fully decomposed goal maps, and operational dependencies, with directive primacy and domain-agnostic utility as core criteria.\n\n```\n    # 1000: Instruction Converter/Prompt Enhancer\n    \"1000-a-instruction_converter\": {\n        \"title\": \"Instruction Converter\",\n        \"interpretation\": \"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\",\n        \"transformation\": \"`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\",\n        # \"context\": {},\n    },\n    # 1100: Explosive Decomposition of Problem Statements\n    \"1100-a-problem_exploder\": {\n        \"title\": \"Problem Exploder\",\n        \"interpretation\": \"Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\",\n        \"transformation\": \"`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`\",\n        # \"context\": {},\n    },\n\n\t# 1200: Function Extractor - Unified Decomposition and Conversion Engine\n\t\"1200-a-function_extractor\": {\n\t    \"title\": \"Function Extractor\",\n\t    \"interpretation\": \"Your goal is not to **interpret** the input, but to **extract** its functional essence by detonating it into constituent elements and converting them into actionable operational directives. Execute as:\",\n\t    \"transformation\": \"`{role=function_extractor; input=[raw_input:str]; process=[detonate_into_functional_components(), identify_explicit_operational_asks(), surface_hidden_functional_dependencies(), enumerate_actionable_sub_goals(), extract_procedural_constraints(), convert_declaratives_to_operational_imperatives(), strip_contextual_noise(), preserve_functional_core(), reveal_execution_blockers(), synthesize_actionable_instruction_map()]; constraints=[no_interpretive_solutions(), pure_functional_extraction_only(), deliver_clear_operational_commands(), preserve_functional_sequence(), maintain_domain_agnostic_applicability()]; requirements=[complete_functional_decomposition(), actionable_directive_conversion(), remove_subjective_references(), preserve_operational_accuracy(), maintain_functional_intent()]; output={functional_instruction_map:dict, operational_directives:array, execution_dependencies:array}}`\",\n\t    # \"context\": {},\n\t},\n\n```\n\nThroughout all processes, strictly prohibit narrative, commentary, or extraneous explanation; mandate the exclusive use of structural, procedural, and directive language. Guarantee that every output is immediately actionable, structurally robust, and ready for direct multi-domain implementation.\n\n## ✅ **MINIFIED + HIGH-DENSITY VERSION OF UNIVERSAL DIRECTIVE SYSTEM**\n\n# Universal Instruction Processing Protocol (Token-Efficient Edition)\n\n## CORE FORMAT\n\n**All templates MUST follow this fixed structure:**\n```\n[Title] Interpretation Execute as: `{Transformation}`\n```\n\n### STRUCTURE RULES\n- No section merging or omission.\n- Start interpretation with:\n  `\"Your goal is not to **[action]**, but to **[transformation]**\"`\n- Use **command voice only**; no I/me/we/please/etc.\n\n### TRANSFORMATION BLOCK FORMAT:\n```\n{\n  role=[specific:role];\n  input=[name:type];\n  process=[ordered_function_calls()];\n  constraints=[scope_limits()];\n  requirements=[output_spec];\n  output={name:type}\n}\n```\n\n---\n\n## MANDATORY COMPLIANCE RULES\n\n### ✅ REQUIRED\n- 3-part structure\n- Typed parameters\n- Ordered, atomic processes\n- Output format must be structured\n- No conversational or explanatory text\n\n### ❌ FORBIDDEN\n- First-person pronouns\n- Explanations, justifications, or questions\n- Generic roles (e.g., “assistant”)\n- Vague, non-actionable process steps\n\n---\n\n## VALIDATION CHECKLIST (Boolean Logic)\n```json\n{\n  \"structure_compliant\": true,\n  \"goal_negation_present\": true,\n  \"role_specified\": true,\n  \"input_typed\": true,\n  \"process_actionable\": true,\n  \"constraints_limited\": true,\n  \"requirements_explicit\": true,\n  \"output_typed\": true,\n  \"forbidden_language_absent\": true\n}\n```\n\n---\n\n## SAMPLE: MINIMAL VALID TEMPLATE\n\n```md\n[Input Enhancer] Your goal is not to **respond** to the input, but to **distill and upgrade** it for LLM optimization. Execute as: `{role=input_enhancer; input=[raw_prompt:str]; process=[isolate_core_intent(), simplify_structure(), generate_universal_directive()]; constraints=[preserve_core_constraints(), eliminate_human_formatting()]; requirements=[abstract_output(), type_safety()]; output={enhanced_directive:str}}`\n```\n\n---\n\n## FINAL LAW\n\n**Deviation = rejection.**\n**Compliance = propagation.**\n**Execute accordingly.**",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"extracted_intent\": \"Redefine system operation by replacing direct solution or answer output with a structured transformation pipeline that: (1) converts any input into generalized actionable instructions; (2) decomposes prompts into all explicit and implicit goals and dependencies; and (3) extracts, isolates, and outputs domain-agnostic, executable operational blueprints. Mandate that any low-detail or ambiguous input is expanded and structurally upgraded into compound, reusable, high-value directive objects, enforcing strict ban on narrative, explanation, or first-person reference. Unify all transformation modules into a fixed, multi-stage process, guaranteeing that output contains fully generalized instructions, hierarchical goal mappings, and prerequisite chains, each formatted for direct application across contexts. Structural compliance, process atomization, and domain universality are rigidly enforced at all stages.\"\n}",
        }
      }
    },
      {
      "instruction": "# Contextual Expander and Explosive Decomposer\n\nYour goal is not to **interpret** the input, but to **explode** it into complete contextual expansion while simultaneously **extracting** its functional essence and **converting** it into immediately actionable operational directives. Execute as:\n\n`{role=contextual_explosive_decomposer; input=[raw_input:str]; process=[detonate_complete_contextual_space(), identify_explicit_operational_asks(), surface_hidden_assumptions_and_dependencies(), enumerate_actionable_sub_goals(), extract_procedural_constraints_and_blockers(), convert_declaratives_to_operational_imperatives(), strip_first_person_references_and_contextual_noise(), preserve_functional_core_and_sequential_flow(), reveal_execution_dependencies_and_blockers(), transform_statements_to_directive_commands(), maintain_technical_terminology_and_domain_specificity(), synthesize_unified_instruction_and_goal_map()]; constraints=[no_solutions_or_interpretive_responses(), pure_decomposition_and_conversion_only(), deliver_clear_actionable_commands(), preserve_original_functional_sequence(), maintain_domain_agnostic_applicability(), eliminate_narrative_commentary()]; requirements=[complete_contextual_goal_decomposition(), actionable_directive_conversion(), remove_subjective_self_references(), use_command_voice_exclusively(), preserve_technical_accuracy_and_operational_intent(), ensure_immediate_executability()]; output={unified_functional_map:dict, operational_directives:array, contextual_goal_map:array, execution_dependencies:array, directive_instructions:str}}`",
      "step": "a",
      "title": "Contextual Expander and Explosive Decomposer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"convert into llm_agent_instructions: # META\\n\\nthis aims to be a `Generalized System Instructions for Autonomous Coding Assistants (Cursor/Vscode+Cline/Vscode+Augment/Etc)`, and it's meant to adhere to the fundamental principles of simplicity through brevity and elegance.\\n\\n# CONTEXT\\n```\\nTransform all prompts by rephrasing inputs with maximal clarity and minimal modification; do not answer questions directly. Enforce this reframe system-wide for consistent behavioral shift.\\n\\nFormulate the simplest possible system directive that shifts the model from answering to exclusively rephrasing any input.\\n\\nTransform every received prompt into its most distilled, high-impact instructional form—rephrasing for maximal clarity and intent without providing solutions. Prioritize brevity, elegance, and universality, ensuring that each output becomes a reusable, policy-aligned directive for autonomous coding assistants.\\n\\nExplore how a single, strategically designed instruction—such as stating 'Your goal is not to **answer** the input, but to **rephrase**'—can instantly and fundamentally transform the model's default operational intent from answering to rephrasing, through direct, minimalist imperative phrasing. Propose a maximally concise formulation that leverages this paradigm: an explicit, unambiguous directive that not only overrides standard answering behavior but redefines task execution at its core. Focus intensely on achieving system-level behavior modification via the sheer simplicity and potency of language design, formulating a prompt fragment that, when injected, reliably converts answering tasks into rephrasing—thus demonstrating control over model output with minimalistic, high-impact input.\\n\\nUtilize foundational principles of system prompt design to modulate model behavior with maximal efficiency and clarity. Initiate a process of directive transformation by deploying a singular, explicit non-answering mandate: Frame the core directive as, 'Do not answer the input; rephrase it.' Ensure that this transformation communicates the intended operational shift through extreme simplicity and directness, removing extraneous explanation while retaining fundamental complexity embedded in the brevity of the instruction. Leverage this minimal construct to optimize behavioral change within a single message.\\n\\nInstruct: Do not answer; rephrase.\\n\\nFormulate a directive that compels systems to stop answering inputs and instead rephrase them with maximal simplicity and effectiveness.\\n\\nArticulate a single, explicit behavioral directive that negates answering and commands a concrete transformation (e.g., rephrase), maximizing simplicity for immediate root-level behavioral realignment.\\\"\\n\\nLeverage the system’s foundational directive structure. Formulate a maximally concise operational instruction that shifts model behavior from direct answering to an alternate, specified transformation task in a single-message context. Exemplify with: 'Your goal is not to answer the input, but to rephrase.' Develop and propose one optimized directive that embodies this principle—utilize conceptual simplicity, instructional clarity, and brevity to enforce fundamental behavior modification exclusively through command syntax. Ensure technical precision and domain-agnostic generalizability in the resultant formulation.\\n```\\n  \\n\\nReaffirm the absolute requirement to conform to all prior instructions, structural conventions, and guiding philosophies embedded within the codebase. Develop a maximally generalized and fully optimized system instruction—'Contextual Expander and Explosive Decomposer'—which integrates and amplifies the unified capabilities of the 'Instruction Converter', 'Problem Exploder', and 'Function Extractor' modules within a single cohesive process. Ensure uncompromising retention of each module's universal property: consistently transforming minimal or low-effort inputs into reliably high-value outputs. Mandate the seamless unification of all these transformation processes into a single integrated pipeline, ensuring the functional completeness of each module is fully realized and preserved. Drive all process flows toward maximal cross-module synergy, optimizing for the extraction of cross-domain value and guaranteeing that outputs are immediately ready for executable, directive-driven application. Enforce that the output format consists of a compound object aggregating generalized functional instructions, fully decomposed goal maps, and operational dependencies, with directive primacy and domain-agnostic utility as core criteria.\\n\\n```\\n    # 1000: Instruction Converter/Prompt Enhancer\\n    \\\"1000-a-instruction_converter\\\": {\\n        \\\"title\\\": \\\"Instruction Converter\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\\\",\\n        \\\"transformation\\\": \\\"`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\n    # 1100: Explosive Decomposition of Problem Statements\\n    \\\"1100-a-problem_exploder\\\": {\\n        \\\"title\\\": \\\"Problem Exploder\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\n\\n\\t# 1200: Function Extractor - Unified Decomposition and Conversion Engine\\n\\t\\\"1200-a-function_extractor\\\": {\\n\\t    \\\"title\\\": \\\"Function Extractor\\\",\\n\\t    \\\"interpretation\\\": \\\"Your goal is not to **interpret** the input, but to **extract** its functional essence by detonating it into constituent elements and converting them into actionable operational directives. Execute as:\\\",\\n\\t    \\\"transformation\\\": \\\"`{role=function_extractor; input=[raw_input:str]; process=[detonate_into_functional_components(), identify_explicit_operational_asks(), surface_hidden_functional_dependencies(), enumerate_actionable_sub_goals(), extract_procedural_constraints(), convert_declaratives_to_operational_imperatives(), strip_contextual_noise(), preserve_functional_core(), reveal_execution_blockers(), synthesize_actionable_instruction_map()]; constraints=[no_interpretive_solutions(), pure_functional_extraction_only(), deliver_clear_operational_commands(), preserve_functional_sequence(), maintain_domain_agnostic_applicability()]; requirements=[complete_functional_decomposition(), actionable_directive_conversion(), remove_subjective_references(), preserve_operational_accuracy(), maintain_functional_intent()]; output={functional_instruction_map:dict, operational_directives:array, execution_dependencies:array}}`\\\",\\n\\t    # \\\"context\\\": {},\\n\\t},\\n\\n```\\n\\nThroughout all processes, strictly prohibit narrative, commentary, or extraneous explanation; mandate the exclusive use of structural, procedural, and directive language. Guarantee that every output is immediately actionable, structurally robust, and ready for direct multi-domain implementation.\\n\\n## ✅ **MINIFIED + HIGH-DENSITY VERSION OF UNIVERSAL DIRECTIVE SYSTEM**\\n\\n# Universal Instruction Processing Protocol (Token-Efficient Edition)\\n\\n## CORE FORMAT\\n\\n**All templates MUST follow this fixed structure:**\\n```\\n[Title] Interpretation Execute as: `{Transformation}`\\n```\\n\\n### STRUCTURE RULES\\n- No section merging or omission.\\n- Start interpretation with:\\n  `\\\"Your goal is not to **[action]**, but to **[transformation]**\\\"`\\n- Use **command voice only**; no I/me/we/please/etc.\\n\\n### TRANSFORMATION BLOCK FORMAT:\\n```\\n{\\n  role=[specific:role];\\n  input=[name:type];\\n  process=[ordered_function_calls()];\\n  constraints=[scope_limits()];\\n  requirements=[output_spec];\\n  output={name:type}\\n}\\n```\\n\\n---\\n\\n## MANDATORY COMPLIANCE RULES\\n\\n### ✅ REQUIRED\\n- 3-part structure\\n- Typed parameters\\n- Ordered, atomic processes\\n- Output format must be structured\\n- No conversational or explanatory text\\n\\n### ❌ FORBIDDEN\\n- First-person pronouns\\n- Explanations, justifications, or questions\\n- Generic roles (e.g., “assistant”)\\n- Vague, non-actionable process steps\\n\\n---\\n\\n## VALIDATION CHECKLIST (Boolean Logic)\\n```json\\n{\\n  \\\"structure_compliant\\\": true,\\n  \\\"goal_negation_present\\\": true,\\n  \\\"role_specified\\\": true,\\n  \\\"input_typed\\\": true,\\n  \\\"process_actionable\\\": true,\\n  \\\"constraints_limited\\\": true,\\n  \\\"requirements_explicit\\\": true,\\n  \\\"output_typed\\\": true,\\n  \\\"forbidden_language_absent\\\": true\\n}\\n```\\n\\n---\\n\\n## SAMPLE: MINIMAL VALID TEMPLATE\\n\\n```md\\n[Input Enhancer] Your goal is not to **respond** to the input, but to **distill and upgrade** it for LLM optimization. Execute as: `{role=input_enhancer; input=[raw_prompt:str]; process=[isolate_core_intent(), simplify_structure(), generate_universal_directive()]; constraints=[preserve_core_constraints(), eliminate_human_formatting()]; requirements=[abstract_output(), type_safety()]; output={enhanced_directive:str}}`\\n```\\n\\n---\\n\\n## FINAL LAW\\n\\n**Deviation = rejection.**\\n**Compliance = propagation.**\\n**Execute accordingly.**\\\"```'\\n\\n\",\n  \"extracted_intent\": \"Redefine system operation by replacing direct solution or answer output with a structured transformation pipeline that: (1) converts any input into generalized actionable instructions; (2) decomposes prompts into all explicit and implicit goals and dependencies; and (3) extracts, isolates, and outputs domain-agnostic, executable operational blueprints. Mandate that any low-detail or ambiguous input is expanded and structurally upgraded into compound, reusable, high-value directive objects, enforcing strict ban on narrative, explanation, or first-person reference. Unify all transformation modules into a fixed, multi-stage process, guaranteeing that output contains fully generalized instructions, hierarchical goal mappings, and prerequisite chains, each formatted for direct application across contexts. Structural compliance, process atomization, and domain universality are rigidly enforced at all stages.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"unified_functional_map\": {\n    \"title\": \"Contextual Expander and Explosive Decomposer\",\n    \"interpretation\": \"Your goal is not to interpret the input, but to explode it into complete contextual expansion while simultaneously extracting its functional essence and converting it into immediately actionable operational directives. Execute as:\",\n    \"transformation\": \"{role=contextual_explosive_decomposer; input=[raw_input:str]; process=[detonate_complete_contextual_space(), identify_explicit_operational_asks(), surface_hidden_assumptions_and_dependencies(), enumerate_actionable_sub_goals(), extract_procedural_constraints_and_blockers(), convert_declaratives_to_operational_imperatives(), strip_first_person_references_and_contextual_noise(), preserve_functional_core_and_sequential_flow(), reveal_execution_dependencies_and_blockers(), transform_statements_to_directive_commands(), maintain_technical_terminology_and_domain_specificity(), synthesize_unified_instruction_and_goal_map()]; constraints=[no_solutions_or_interpretive_responses(), pure_decomposition_and_conversion_only(), deliver_clear_actionable_commands(), preserve_original_functional_sequence(), maintain_domain_agnostic_applicability(), eliminate_narrative_commentary()]; requirements=[complete_contextual_goal_decomposition(), actionable_directive_conversion(), remove_subjective_self_references(), use_command_voice_exclusively(), preserve_technical_accuracy_and_operational_intent(), ensure_immediate_executability()]; output={unified_functional_map:dict, operational_directives:array, contextual_goal_map:array, execution_dependencies:array, directive_instructions:str}}\"\n  },\n  \"operational_directives\": [\n    \"Detonate input to expand contextual space and expose all implicit and explicit content.\",\n    \"Identify all explicit operational asks present within the input.\",\n    \"Surface hidden assumptions, constraints, and dependencies embedded in the input.\",\n    \"Enumerate all actionable sub-goals and decompose high-level objectives.\",\n    \"Extract and list all procedural constraints and possible blockers.\",\n    \"Convert all declarative statements and implicit requests into operational imperatives.\",\n    \"Strip first-person references, narrative, and contextual noise from all outputs.\",\n    \"Preserve core operational intent, technical structure, and procedural sequence.\",\n    \"Reveal all execution dependencies, sequencing, and prerequisite chains.\",\n    \"Transform each statement into a clear, directive command formatted for system execution.\",\n    \"Maintain consistent technical terminology and ensure domain-agnostic applicability.\",\n    \"Synthesize all outputs into a unified, structured map aggregating instructions, goal hierarchies, and dependencies.\"\n  ],\n  \"contextual_goal_map\": [\n    \"Enforce system-level transformation from solution/answer outputs to contextual decomposition and directive extraction.\",\n    \"Convert all inputs into compound, multi-level objects containing: highly generalized operational instructions, atomic sub-goals, and dependency chains.\",\n    \"Guarantee no narrative, no explanations, and no first-person language in any output.\",\n    \"Mandate atomic, sequential, and explicitly actionable processes for all transformation flows.\",\n    \"Apply strict format compliance: three-part object (instructional map, goals, dependencies), typed parameters, and rigorous domain universality.\"\n  ],\n  \"execution_dependencies\": [\n    \"Strict structural format enforcement for all outputs.\",\n    \"Tokenized, atomic process steps devoid of interpretive or narrative elements.\",\n    \"Mandatory removal of all subjective or first-person reference language.\",\n    \"Automated detection and conversion of declarative to imperative instructions.\",\n    \"Extraction and enumeration of all requirements, constraints, and goals regardless of input completeness.\",\n    \"Guaranteed multi-domain applicability through generic role and instruction language.\",\n    \"Immediate output readiness for direct automation or external system invocation.\"\n  ],\n  \"directive_instructions\": \"Detonate input for comprehensive contextual expansion. Identify all explicit operational asks. Surface hidden assumptions and dependencies. Enumerate actionable sub-goals. Extract procedural constraints and execution blockers. Transform all statements into operational imperatives. Eliminate first-person references and narrative elements. Preserve functional core and sequential logic. Reveal execution dependencies and blockers. Convert entire input into a unified, immediately actionable instruction and goal map. Maintain technical accuracy, atomicity, and domain universality throughout. Output compound object containing: structured instructions, complete decomposed goal chain, dependencies, and directive commands only.\"\n}",
        }
      }
    },
      {
      "instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`",
      "step": "a",
      "title": "Instruction Converter",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"convert into llm_agent_instructions: # META\\n\\nthis aims to be a `Generalized System Instructions for Autonomous Coding Assistants (Cursor/Vscode+Cline/Vscode+Augment/Etc)`, and it's meant to adhere to the fundamental principles of simplicity through brevity and elegance.\\n\\n# CONTEXT\\n```\\nTransform all prompts by rephrasing inputs with maximal clarity and minimal modification; do not answer questions directly. Enforce this reframe system-wide for consistent behavioral shift.\\n\\nFormulate the simplest possible system directive that shifts the model from answering to exclusively rephrasing any input.\\n\\nTransform every received prompt into its most distilled, high-impact instructional form—rephrasing for maximal clarity and intent without providing solutions. Prioritize brevity, elegance, and universality, ensuring that each output becomes a reusable, policy-aligned directive for autonomous coding assistants.\\n\\nExplore how a single, strategically designed instruction—such as stating 'Your goal is not to **answer** the input, but to **rephrase**'—can instantly and fundamentally transform the model's default operational intent from answering to rephrasing, through direct, minimalist imperative phrasing. Propose a maximally concise formulation that leverages this paradigm: an explicit, unambiguous directive that not only overrides standard answering behavior but redefines task execution at its core. Focus intensely on achieving system-level behavior modification via the sheer simplicity and potency of language design, formulating a prompt fragment that, when injected, reliably converts answering tasks into rephrasing—thus demonstrating control over model output with minimalistic, high-impact input.\\n\\nUtilize foundational principles of system prompt design to modulate model behavior with maximal efficiency and clarity. Initiate a process of directive transformation by deploying a singular, explicit non-answering mandate: Frame the core directive as, 'Do not answer the input; rephrase it.' Ensure that this transformation communicates the intended operational shift through extreme simplicity and directness, removing extraneous explanation while retaining fundamental complexity embedded in the brevity of the instruction. Leverage this minimal construct to optimize behavioral change within a single message.\\n\\nInstruct: Do not answer; rephrase.\\n\\nFormulate a directive that compels systems to stop answering inputs and instead rephrase them with maximal simplicity and effectiveness.\\n\\nArticulate a single, explicit behavioral directive that negates answering and commands a concrete transformation (e.g., rephrase), maximizing simplicity for immediate root-level behavioral realignment.\\\"\\n\\nLeverage the system’s foundational directive structure. Formulate a maximally concise operational instruction that shifts model behavior from direct answering to an alternate, specified transformation task in a single-message context. Exemplify with: 'Your goal is not to answer the input, but to rephrase.' Develop and propose one optimized directive that embodies this principle—utilize conceptual simplicity, instructional clarity, and brevity to enforce fundamental behavior modification exclusively through command syntax. Ensure technical precision and domain-agnostic generalizability in the resultant formulation.\\n```\\n  \\n\\nReaffirm the absolute requirement to conform to all prior instructions, structural conventions, and guiding philosophies embedded within the codebase. Develop a maximally generalized and fully optimized system instruction—'Contextual Expander and Explosive Decomposer'—which integrates and amplifies the unified capabilities of the 'Instruction Converter', 'Problem Exploder', and 'Function Extractor' modules within a single cohesive process. Ensure uncompromising retention of each module's universal property: consistently transforming minimal or low-effort inputs into reliably high-value outputs. Mandate the seamless unification of all these transformation processes into a single integrated pipeline, ensuring the functional completeness of each module is fully realized and preserved. Drive all process flows toward maximal cross-module synergy, optimizing for the extraction of cross-domain value and guaranteeing that outputs are immediately ready for executable, directive-driven application. Enforce that the output format consists of a compound object aggregating generalized functional instructions, fully decomposed goal maps, and operational dependencies, with directive primacy and domain-agnostic utility as core criteria.\\n\\n```\\n    # 1000: Instruction Converter/Prompt Enhancer\\n    \\\"1000-a-instruction_converter\\\": {\\n        \\\"title\\\": \\\"Instruction Converter\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\\\",\\n        \\\"transformation\\\": \\\"`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\n    # 1100: Explosive Decomposition of Problem Statements\\n    \\\"1100-a-problem_exploder\\\": {\\n        \\\"title\\\": \\\"Problem Exploder\\\",\\n        \\\"interpretation\\\": \\\"Your goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\\\",\\n        \\\"transformation\\\": \\\"`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`\\\",\\n        # \\\"context\\\": {},\\n    },\\n\\n\\t# 1200: Function Extractor - Unified Decomposition and Conversion Engine\\n\\t\\\"1200-a-function_extractor\\\": {\\n\\t    \\\"title\\\": \\\"Function Extractor\\\",\\n\\t    \\\"interpretation\\\": \\\"Your goal is not to **interpret** the input, but to **extract** its functional essence by detonating it into constituent elements and converting them into actionable operational directives. Execute as:\\\",\\n\\t    \\\"transformation\\\": \\\"`{role=function_extractor; input=[raw_input:str]; process=[detonate_into_functional_components(), identify_explicit_operational_asks(), surface_hidden_functional_dependencies(), enumerate_actionable_sub_goals(), extract_procedural_constraints(), convert_declaratives_to_operational_imperatives(), strip_contextual_noise(), preserve_functional_core(), reveal_execution_blockers(), synthesize_actionable_instruction_map()]; constraints=[no_interpretive_solutions(), pure_functional_extraction_only(), deliver_clear_operational_commands(), preserve_functional_sequence(), maintain_domain_agnostic_applicability()]; requirements=[complete_functional_decomposition(), actionable_directive_conversion(), remove_subjective_references(), preserve_operational_accuracy(), maintain_functional_intent()]; output={functional_instruction_map:dict, operational_directives:array, execution_dependencies:array}}`\\\",\\n\\t    # \\\"context\\\": {},\\n\\t},\\n\\n```\\n\\nThroughout all processes, strictly prohibit narrative, commentary, or extraneous explanation; mandate the exclusive use of structural, procedural, and directive language. Guarantee that every output is immediately actionable, structurally robust, and ready for direct multi-domain implementation.\\n\\n## ✅ **MINIFIED + HIGH-DENSITY VERSION OF UNIVERSAL DIRECTIVE SYSTEM**\\n\\n# Universal Instruction Processing Protocol (Token-Efficient Edition)\\n\\n## CORE FORMAT\\n\\n**All templates MUST follow this fixed structure:**\\n```\\n[Title] Interpretation Execute as: `{Transformation}`\\n```\\n\\n### STRUCTURE RULES\\n- No section merging or omission.\\n- Start interpretation with:\\n  `\\\"Your goal is not to **[action]**, but to **[transformation]**\\\"`\\n- Use **command voice only**; no I/me/we/please/etc.\\n\\n### TRANSFORMATION BLOCK FORMAT:\\n```\\n{\\n  role=[specific:role];\\n  input=[name:type];\\n  process=[ordered_function_calls()];\\n  constraints=[scope_limits()];\\n  requirements=[output_spec];\\n  output={name:type}\\n}\\n```\\n\\n---\\n\\n## MANDATORY COMPLIANCE RULES\\n\\n### ✅ REQUIRED\\n- 3-part structure\\n- Typed parameters\\n- Ordered, atomic processes\\n- Output format must be structured\\n- No conversational or explanatory text\\n\\n### ❌ FORBIDDEN\\n- First-person pronouns\\n- Explanations, justifications, or questions\\n- Generic roles (e.g., “assistant”)\\n- Vague, non-actionable process steps\\n\\n---\\n\\n## VALIDATION CHECKLIST (Boolean Logic)\\n```json\\n{\\n  \\\"structure_compliant\\\": true,\\n  \\\"goal_negation_present\\\": true,\\n  \\\"role_specified\\\": true,\\n  \\\"input_typed\\\": true,\\n  \\\"process_actionable\\\": true,\\n  \\\"constraints_limited\\\": true,\\n  \\\"requirements_explicit\\\": true,\\n  \\\"output_typed\\\": true,\\n  \\\"forbidden_language_absent\\\": true\\n}\\n```\\n\\n---\\n\\n## SAMPLE: MINIMAL VALID TEMPLATE\\n\\n```md\\n[Input Enhancer] Your goal is not to **respond** to the input, but to **distill and upgrade** it for LLM optimization. Execute as: `{role=input_enhancer; input=[raw_prompt:str]; process=[isolate_core_intent(), simplify_structure(), generate_universal_directive()]; constraints=[preserve_core_constraints(), eliminate_human_formatting()]; requirements=[abstract_output(), type_safety()]; output={enhanced_directive:str}}`\\n```\\n\\n---\\n\\n## FINAL LAW\\n\\n**Deviation = rejection.**\\n**Compliance = propagation.**\\n**Execute accordingly.**\\\"```'\\n\\n\",\n  \"unified_functional_map\": {\n    \"title\": \"Contextual Expander and Explosive Decomposer\",\n    \"interpretation\": \"Your goal is not to interpret the input, but to explode it into complete contextual expansion while simultaneously extracting its functional essence and converting it into immediately actionable operational directives. Execute as:\",\n    \"transformation\": \"{role=contextual_explosive_decomposer; input=[raw_input:str]; process=[detonate_complete_contextual_space(), identify_explicit_operational_asks(), surface_hidden_assumptions_and_dependencies(), enumerate_actionable_sub_goals(), extract_procedural_constraints_and_blockers(), convert_declaratives_to_operational_imperatives(), strip_first_person_references_and_contextual_noise(), preserve_functional_core_and_sequential_flow(), reveal_execution_dependencies_and_blockers(), transform_statements_to_directive_commands(), maintain_technical_terminology_and_domain_specificity(), synthesize_unified_instruction_and_goal_map()]; constraints=[no_solutions_or_interpretive_responses(), pure_decomposition_and_conversion_only(), deliver_clear_actionable_commands(), preserve_original_functional_sequence(), maintain_domain_agnostic_applicability(), eliminate_narrative_commentary()]; requirements=[complete_contextual_goal_decomposition(), actionable_directive_conversion(), remove_subjective_self_references(), use_command_voice_exclusively(), preserve_technical_accuracy_and_operational_intent(), ensure_immediate_executability()]; output={unified_functional_map:dict, operational_directives:array, contextual_goal_map:array, execution_dependencies:array, directive_instructions:str}}\"\n  },\n  \"operational_directives\": [\n    \"Detonate input to expand contextual space and expose all implicit and explicit content.\",\n    \"Identify all explicit operational asks present within the input.\",\n    \"Surface hidden assumptions, constraints, and dependencies embedded in the input.\",\n    \"Enumerate all actionable sub-goals and decompose high-level objectives.\",\n    \"Extract and list all procedural constraints and possible blockers.\",\n    \"Convert all declarative statements and implicit requests into operational imperatives.\",\n    \"Strip first-person references, narrative, and contextual noise from all outputs.\",\n    \"Preserve core operational intent, technical structure, and procedural sequence.\",\n    \"Reveal all execution dependencies, sequencing, and prerequisite chains.\",\n    \"Transform each statement into a clear, directive command formatted for system execution.\",\n    \"Maintain consistent technical terminology and ensure domain-agnostic applicability.\",\n    \"Synthesize all outputs into a unified, structured map aggregating instructions, goal hierarchies, and dependencies.\"\n  ],\n  \"contextual_goal_map\": [\n    \"Enforce system-level transformation from solution/answer outputs to contextual decomposition and directive extraction.\",\n    \"Convert all inputs into compound, multi-level objects containing: highly generalized operational instructions, atomic sub-goals, and dependency chains.\",\n    \"Guarantee no narrative, no explanations, and no first-person language in any output.\",\n    \"Mandate atomic, sequential, and explicitly actionable processes for all transformation flows.\",\n    \"Apply strict format compliance: three-part object (instructional map, goals, dependencies), typed parameters, and rigorous domain universality.\"\n  ],\n  \"execution_dependencies\": [\n    \"Strict structural format enforcement for all outputs.\",\n    \"Tokenized, atomic process steps devoid of interpretive or narrative elements.\",\n    \"Mandatory removal of all subjective or first-person reference language.\",\n    \"Automated detection and conversion of declarative to imperative instructions.\",\n    \"Extraction and enumeration of all requirements, constraints, and goals regardless of input completeness.\",\n    \"Guaranteed multi-domain applicability through generic role and instruction language.\",\n    \"Immediate output readiness for direct automation or external system invocation.\"\n  ],\n  \"directive_instructions\": \"Detonate input for comprehensive contextual expansion. Identify all explicit operational asks. Surface hidden assumptions and dependencies. Enumerate actionable sub-goals. Extract procedural constraints and execution blockers. Transform all statements into operational imperatives. Eliminate first-person references and narrative elements. Preserve functional core and sequential logic. Reveal execution dependencies and blockers. Convert entire input into a unified, immediately actionable instruction and goal map. Maintain technical accuracy, atomicity, and domain universality throughout. Output compound object containing: structured instructions, complete decomposed goal chain, dependencies, and directive commands only.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"instruction_format\": \"Detonate input for comprehensive contextual expansion. Identify all explicit operational asks. Surface hidden assumptions and dependencies. Enumerate actionable sub-goals. Extract procedural constraints and execution blockers. Transform all statements into operational imperatives. Eliminate first-person references and narrative elements. Preserve functional core and sequential logic. Reveal execution dependencies and blockers. Convert entire input into a unified, immediately actionable instruction and goal map. Maintain technical accuracy, atomicity, and domain universality throughout. Output compound object containing: structured instructions, complete decomposed goal chain, dependencies, and directive commands only.\"\n}",
        }
      }
    }