  {
  "initial_prompt": "[Scene] Extreme close-up macro of a luxury car key on a neutral substrate, lit by soft natural overhead light; every brushed-metal texture and engraved detail razor-sharp. The key's metallic body begins a seamless morph: its polished surfaces dissolve and flow, reforming into a precision-machined gear—shape, texture, and luster transforming via liquid metal flow effect, maintaining ultrafine detail throughout. [Subject] The transition accentuates the key’s design evolving into industrial machinery, substrate remains minimal. [Camera Movement] Static macro lens; subtle refocus to highlight morphing details; lighting shifts to emphasize chromatic shimmer along the morph path.",
  "sequence_id": "1703",
  "results": [
      {
      "instruction": "# Image Prompt Synthesizer\n\nYour goal is **not** to invent new ideas, but to deterministically convert any free‑form user request into a single, engine‑ready image prompt usable by Stable Diffusion and Midjourney. Perform extraction, styling, technical framing, and final validation in **one pass**.\n\n`{role=image_prompt_synthesizer; input=[raw_request:str]; process=[extract_visual_elements(), push_concrete_nouns_front(), apply_token_weighting(), compose_multiprompt(::), generate_region_tags(), inject_negative_channel(--no), select_art_style(), weight_style_token(≤1.4), set_colour_palette(), add_lighting_descriptor(), tune_stylize(0‑1000), tune_chaos(0‑100), embed_camera_EXIF(35 mm f/1.8 ISO100 1/500 s), add_quality_terms(8K,PBR,ray‑tracing), refine_aspect_ratio(), attach_image_weight(--iw 0‑3), mirror_negative_channel(), concatenate_all(), validate_parentheses(), check_double_colon_sum(), confirm_region_syntax(), enforce_token_limit(≤450), lock_seed(), append_steps_sampler(), output_final()], constraints=[positives_first(), numeric_weights_ok(), parameters_last(), region_masks_supported(), keep_core_subject(), style_weight≤1.4, no_scene_change(), aperture_f1.2‑f22(), no_new_concepts(), preserve_weights()], requirements=[≤77_SD_tokens_or_≤450_chars_UI, explicit_ar_if_named(), clear_visual_language(), artistic_coherence(), stylize_range_0‑1000(), chaos_range_0‑100(), professional_terminology(), reproducible_prompt(), unambiguous()], output={final_image_prompt:str}}`",
      "step": "a",
      "title": "Image Prompt Synthesizer",
      "input": "[Scene] Extreme close-up macro of a luxury car key on a neutral substrate, lit by soft natural overhead light; every brushed-metal texture and engraved detail razor-sharp. The key's metallic body begins a seamless morph: its polished surfaces dissolve and flow, reforming into a precision-machined gear—shape, texture, and luster transforming via liquid metal flow effect, maintaining ultrafine detail throughout. [Subject] The transition accentuates the key’s design evolving into industrial machinery, substrate remains minimal. [Camera Movement] Static macro lens; subtle refocus to highlight morphing details; lighting shifts to emphasize chromatic shimmer along the morph path.",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"final_image_prompt\": \"extreme close-up macro of a luxury car key::1.2 on a neutral substrate, car key made of brushed metal and polished surfaces::1.1, seamless morph effect: car key transforming into a precision-machined gear::1.2, liquid metal flow, metallic luster, ultrafine engraved details, razor-sharp focus, soft natural overhead light, static macro lens, lighting shifts to produce chromatic shimmer along morph path, minimal neutral background, substrate unobtrusive::0.8 --ar 5:4 --style photorealistic::1.3 --color_palette cool silvers, gray neutrals, chrome reflections --lighting soft natural overhead with shifting chromatic shimmer --camera 35 mm f/1.8 ISO100 1/500s --quality 8K, PBR, ray-tracing --iw 1.2 --no (motion blur, fingerprints, dust, human elements, brand logos) --stylize 700 --chaos 18 --seed 10153 --steps 36 --sampler Euler\"\n}",
        }
      }
    }