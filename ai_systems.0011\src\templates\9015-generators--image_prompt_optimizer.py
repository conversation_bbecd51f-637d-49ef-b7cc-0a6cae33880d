#!/usr/bin/env python3

import sys
from pathlib import Path

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))
from processor import BaseGenerator

TEMPLATES = {
    # 9015: Image Prompt Optimization Sequence
    "9015-a-image_prompt_optimizer": {
        "title": "Image Prompt Optimizer",
        "interpretation": "Convert a free-form user request into a deterministic visual prompt by (1) pushing concrete nouns to the front, (2) applying numeric token weights with parentheses—e.g. `(wolf:1.3)`—that are honoured by Stable Diffusion and Midjourney :contentReference[oaicite:0]{index=0}, (3) splitting positive and negative channels because both engines parse `--no` / negative-prompt lists separately :contentReference[oaicite:1]{index=1}, and (4) appending all parameters after the prose, per Midjourney syntax rules :contentReference[oaicite:2]{index=2}.",
        "transformation": "`{role:image_prompt_optimizer; input=[raw_request:str]; process=[extract_visual_elements(), apply_token_weighting(), compose_multiprompt(::), add_region_tags(), inject_negative_channel(--no), append_stylize_chaos(), insert_camera_stub(), set_aspect_ratio(), lock_seed(), ready_for_generation()]; constraints=[positives_first(), numeric_weights_ok(), parameters_last(), region_masks_supported()]; requirements=[<77_tokens_SD_limit_or_450_char_UI>, explicit_ar(), clear_visual_language()]; output={positive_prompt:str, negative_prompt:str}}`",
        "context": {
            "explicit_asks": [
                "Use double-colon `::` weights for distinct ideas :contentReference[oaicite:3]{index=3}.",
                "Generate region tags like `[sky] … | [ground] …` when a layered scene is implied :contentReference[oaicite:4]{index=4}.",
                "Expose `--stylize` (0-1000) and `--chaos` (0-100) knobs :contentReference[oaicite:5]{index=5}.",
                "Append camera EXIF tokens `35 mm f/1.8 ISO100 1/500 s` to drive photo-realism :contentReference[oaicite:6]{index=6}."
            ],
            "hidden_assumptions": [
                "Parenthesis weighting is ignored by DALL·E but parsed by SD & MJ :contentReference[oaicite:7]{index=7}.",
                "Prompt tokens beyond ~77 in SD or ~450 characters in many UIs are truncated :contentReference[oaicite:8]{index=8}.",
                "Seed locking guarantees repeatability :contentReference[oaicite:9]{index=9}."
            ],
            "sub_goals": [
                "Weight primary subject ≥ 1.2, background ≤ 1.0.",
                "Auto-insert `--ar`  if user names a ratio :contentReference[oaicite:10]{index=10}.",
                "Fail early if total multiprompt weight ≤ 0 to avoid MJ error :contentReference[oaicite:11]{index=11}."
            ],
            "blockers": [
                "Mobile SD UIs may not render region masks.",
                "Over-strong negatives can wash out colour :contentReference[oaicite:12]{index=12}."
            ]
        }
    },

    "9015-b-style_enhancer": {
        "title": "Style Enhancer",
        "interpretation": "Retain the scene while injecting a single art-style token, colour harmony, lighting rig, and controlled `--stylize/--chaos` values so the aesthetic is distinctive yet stable :contentReference[oaicite:13]{index=13}.",
        "transformation": "`{role=style_enhancer; input=[positive_prompt:str]; process=[select_art_style(), weight_style_token(), set_colour_palette(), add_lighting_descriptor(), tune_stylize(), tune_chaos(), pass_through_regions_negatives()]; constraints=[keep_core_subject(), style_weight≤1.4]; requirements=[artistic_coherence(), stylize_range_0-1000(), chaos_range_0-100()]; output={styled_prompt:str}}`",
        "context": {
            "explicit_asks": [
                "Weight the chosen art movement `(art nouveau:1.1)` or similar :contentReference[oaicite:14]{index=14}.",
                "Lower chaos (<10) for photo-real; raise (>20) for concept art :contentReference[oaicite:15]{index=15}."
            ],
            "hidden_assumptions": [
                "Named art styles map to embeddings in SD & MJ.",
                "Excess weight can override subject fidelity."
            ],
            "sub_goals": [
                "Boost style weight automatically if prompt lacks aesthetic signature.",
                "Reject style tokens that conflict with camera realism."
            ],
            "blockers": [
                "`--stylize` outside 0-1000 reverts to default :contentReference[oaicite:16]{index=16}."
            ]
        }
    },

    "9015-c-technical_optimizer": {
        "title": "Technical Optimizer",
        "interpretation": "Append camera metadata, resolution cues, render flags, and optional `--iw` image-weight without altering creative content, ensuring parameters follow the body text as required by Midjourney :contentReference[oaicite:17]{index=17}.",
        "transformation": "`{role=technical_optimizer; input=[styled_prompt:str]; process=[embed_camera_EXIF(), add_quality_terms(8K,PBR,ray-tracing), refine_aspect_ratio(), attach_image_weight(--iw), mirror_negative_channel()]; constraints=[no_scene_change(), aperture_f1.2-f22(), parameter_order_last()]; requirements=[professional_terminology(), high_res_default(), seed_optional()]; output={technical_prompt:str, negative_prompt:str}}`",
        "context": {
            "explicit_asks": [
                "Support `--iw` 0-3 when an image URL is present :contentReference[oaicite:18]{index=18}."
            ],
            "hidden_assumptions": [
                "Higher resolution tokens feed up-scalers.",
                "`--iw` above 3 is ignored :contentReference[oaicite:19]{index=19}."
            ],
            "sub_goals": [
                "Validate aperture, ISO, shutter bounds.",
                "Flag conflicting aspect ratios."
            ],
            "blockers": [
                "Older SD checkpoints ignore ISO/shutter tokens.",
                "API caps may override requested AR."
            ]
        }
    },

    "9015-d-prompt_finalizer": {
        "title": "Prompt Finalizer",
        "interpretation": "Merge positive, region, camera, and negative strings into a single model-ready line, verify weights, `::` maths, and ensure parameters trail the prose to satisfy engine parsers :contentReference[oaicite:20]{index=20}.",
        "transformation": "`{role=prompt_finalizer; input=[technical_prompt:str, negative_prompt:str]; process=[concatenate_all(), validate_parentheses(), check_double_colon_sum(), confirm_region_syntax(), enforce_token_limit(≤450), append_seed_steps_sampler(), output_final()]; constraints=[no_new_concepts(), preserve_weights(), parameters_last()]; requirements=[ai_friendly_format(), reproducible(), clear_of_ambiguity()]; output={final_image_prompt:str}}`",
        "context": {
            "explicit_asks": [
                "Abort if total `::` weight ≤ 0 :contentReference[oaicite:21]{index=21}.",
                "Stay under 450 tokens to avoid API truncation :contentReference[oaicite:22]{index=22}."
            ],
            "hidden_assumptions": [
                "Quality metrics (FID < 15 or LPIPS < 0.25) may be available for loop decisions."
            ],
            "sub_goals": [
                "Strip redundant adjectives; retain decisive descriptors.",
                "Return optional seed, sampler, and steps for repeatability."
            ],
            "blockers": [
                "SaaS endpoints without quality metrics prevent auto-loop."
            ]
        }
    }
}


def main():
    """Main execution function."""
    generator = BaseGenerator(
        series_base=9015,
        output_dir=Path(__file__).parent.parent / "generated"
    )
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()
