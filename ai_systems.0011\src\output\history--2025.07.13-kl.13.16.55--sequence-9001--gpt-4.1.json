  {
  "initial_prompt": "<PERSON><PERSON>ule<PERSON> så mye med dagen <PERSON>, 25 år. Du er den yngre generasjon, fortsett å tenke ambisiøst, men gjør det i balanse med innsats.",
  "sequence_id": "9001",
  "results": [
      {
      "instruction": "# Context Extractor\n\nDitt mål er ikke å **skrive** hilsen, men å **hente** personlige fakta. Execute as:\n\n`{role=context_extractor; input=[raw_text:str]; process=[identify_recipient(), detect_relationship(), extract_age_or_milestone(), list_unique_traits(), capture_shared_memories()], constraints=[ingen_omtolkning(), ingen_humor()], output={facts:{name:str|None, relation:str|None, age:str|None, traits:list, memories:list}}}`",
      "step": "a",
      "title": "Context Extractor",
      "input": "Gratulerer så mye med dagen <PERSON>, 25 år. Du er den yngre generasjon, fortsett å tenke ambisiøst, men gjør det i balanse med innsats.",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"facts\": {\n    \"name\": \"Sebastian\",\n    \"relation\": null,\n    \"age\": \"25\",\n    \"traits\": [\"ambisiøs\", \"tilhører den yngre generasjonen\"],\n    \"memories\": []\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Personality Amplifier\n\nDitt mål er ikke å **endre** fakta, men å **farge** dem med mottakerens unike personlighet. Execute as:\n\n`{role=personality_amplifier; input=[facts:dict]; process=[select_signature_trait(), weave_inside_joke(), preserve_affection_level()], constraints=[ingen_generiske_floskler()], requirements=[tone=varm_og_leken], output={flair:str}}`",
      "step": "b",
      "title": "Personality Amplifier",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Gratulerer så mye med dagen Sebastian, 25 år. Du er den yngre generasjon, fortsett å tenke ambisiøst, men gjør det i balanse med innsats.\\\"```'\\n\\n\",\n  \"facts\": {\n    \"name\": \"Sebastian\",\n    \"relation\": null,\n    \"age\": \"25\",\n    \"traits\": [\n      \"ambisiøs\",\n      \"tilhører den yngre generasjonen\"\n    ],\n    \"memories\": []\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"flair\": \"Sebastian, 25 år – endelig voksen på papiret, men fortsatt den frekke lillebroren for generasjonen over. Gratulerer! Du har jo alltid vært han som sikter mot stjernene, men nå som du har rundet kvart århundre, så passer det å si: ikke glem å ta med deg hjelmen når du skyter opp rakettene. Ambisiøs, ja – men vi trenger deg hel og i balanse, spesielt hvis vi noensinne skal la deg ha ansvaret for både grillen OG playlisten. Feir med stil, men ikke så mye at TikTok-generationen må rykke inn som redningsmannskap!\"\n}",
        }
      }
    },
      {
      "instruction": "# Humor Synthesizer\n\nDitt mål er ikke å **liste** informasjon, men å **lansere punchline**. Execute as:\n\n`{role=humor_synthesizer; input=[flair:str]; process=[craft_witty_twist(), insert_playful_tease(), add_emoji()], constraints=[max_words(12), unngå_støtende_innhold()], output={humor:str}}`",
      "step": "c",
      "title": "Humor Synthesizer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Gratulerer så mye med dagen Sebastian, 25 år. Du er den yngre generasjon, fortsett å tenke ambisiøst, men gjør det i balanse med innsats.\\\"```'\\n\\n\",\n  \"flair\": \"Sebastian, 25 år – endelig voksen på papiret, men fortsatt den frekke lillebroren for generasjonen over. Gratulerer! Du har jo alltid vært han som sikter mot stjernene, men nå som du har rundet kvart århundre, så passer det å si: ikke glem å ta med deg hjelmen når du skyter opp rakettene. Ambisiøs, ja – men vi trenger deg hel og i balanse, spesielt hvis vi noensinne skal la deg ha ansvaret for både grillen OG playlisten. Feir med stil, men ikke så mye at TikTok-generationen må rykke inn som redningsmannskap!\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"humor\": \"25 år? Endelig voksen! Men fortsatt for ung for tørkestativ. 🚀😏\"\n}",
        }
      }
    },
      {
      "instruction": "# Greeting Composer\n\nDitt mål er ikke å **forklare** prosessen, men å **smelte** fakta og humor til én kort, personlig bursdagshilsen. Execute as:\n\n`{role=greeting_composer; input=[facts:dict, humor:str]; process=[merge_name_and_age(), embed_humor(), close_with_affection()], constraints=[≤125_tegn, én_setning()], requirements=[norwegian_language(), tydelig_bursdagsreferanse()], output={greeting:str}}`",
      "step": "e",
      "title": "Greeting Composer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Gratulerer så mye med dagen Sebastian, 25 år. Du er den yngre generasjon, fortsett å tenke ambisiøst, men gjør det i balanse med innsats.\\\"```'\\n\\n\",\n  \"humor\": \"25 år? Endelig voksen! Men fortsatt for ung for tørkestativ. 🚀😏\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"greeting\": \"Gratulerer med dagen, Sebastian, 25 år og fremdeles for ung for tørkestativ—lev ambisiøst og med latter!\"\n}",
        }
      }
    }