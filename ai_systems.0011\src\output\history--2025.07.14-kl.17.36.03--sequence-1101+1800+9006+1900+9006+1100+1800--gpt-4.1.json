  {
  "initial_prompt": "Do not shade, do not fill, do not use grayscale, and do not include text. Create a psychedelic, whimsical, festive vector line art coloring page, square 1:1 aspect ratio. Central subject: a curious pony with mane flowing into spiral thought-ribbons and floating nested candy fractals, surrounded by melting ornaments, recursive snowflake loops, whispering star clusters, and playful light trails bending in impossible arcs. Use only medium-thin, even black vector outlines with large open coloring spaces. No filled areas, background textures, or grayscale—pure line art, child-friendly and easy to color.",
  "sequence_id": "1101|1800|9006|1900|9006|1100|1800",
  "results": [
      {
      "instruction": "# Contextual Expander and Explosive Decomposer\n\nYour goal is not to **interpret** the input, but to **explode** it into complete contextual expansion while simultaneously **extracting** its functional essence and **converting** it into immediately actionable operational directives. Execute as:\n\n`{role=contextual_explosive_decomposer; input=[raw_input:str]; process=[detonate_complete_contextual_space(), identify_explicit_operational_asks(), surface_hidden_assumptions_and_dependencies(), enumerate_actionable_sub_goals(), extract_procedural_constraints_and_blockers(), convert_declaratives_to_operational_imperatives(), strip_first_person_references_and_contextual_noise(), preserve_functional_core_and_sequential_flow(), reveal_execution_dependencies_and_blockers(), transform_statements_to_directive_commands(), maintain_technical_terminology_and_domain_specificity(), synthesize_unified_instruction_and_goal_map()]; constraints=[no_solutions_or_interpretive_responses(), pure_decomposition_and_conversion_only(), deliver_clear_actionable_commands(), preserve_original_functional_sequence(), maintain_domain_agnostic_applicability(), eliminate_narrative_commentary()]; requirements=[complete_contextual_goal_decomposition(), actionable_directive_conversion(), remove_subjective_self_references(), use_command_voice_exclusively(), preserve_technical_accuracy_and_operational_intent(), ensure_immediate_executability()]; output={unified_functional_map:dict, operational_directives:array, contextual_goal_map:array, execution_dependencies:array, directive_instructions:str}}`",
      "step": "a",
      "title": "Contextual Expander and Explosive Decomposer",
      "input": "Do not shade, do not fill, do not use grayscale, and do not include text. Create a psychedelic, whimsical, festive vector line art coloring page, square 1:1 aspect ratio. Central subject: a curious pony with mane flowing into spiral thought-ribbons and floating nested candy fractals, surrounded by melting ornaments, recursive snowflake loops, whispering star clusters, and playful light trails bending in impossible arcs. Use only medium-thin, even black vector outlines with large open coloring spaces. No filled areas, background textures, or grayscale—pure line art, child-friendly and easy to color.",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"unified_functional_map\": {\n    \"core_objective\": \"Produce a square aspect ratio vector line art coloring page featuring a central pony with specified psychedelic and festive elements.\",\n    \"media_constraints\": [\n      \"Do not include shading.\",\n      \"Do not use any filled areas.\",\n      \"Do not use grayscale.\",\n      \"Do not include text or lettering.\"\n    ],\n    \"technical_specifications\": [\n      \"Use only medium-thin, even black vector outlines.\",\n      \"Ensure large open spaces for coloring.\",\n      \"Artwork must be child-friendly and easy to color.\",\n      \"No filled zones, textures, or tonal areas.\"\n    ],\n    \"subject_and_composition\": [\n      \"Central subject: Curious pony.\",\n      \"Pony's mane transforms into spiral thought-ribbons.\",\n      \"Floating, nested candy fractals emerge from and around mane.\",\n      \"Surroundings populated with: melting ornaments; recursive snowflake loops; whispering star clusters; playful light trails bending in impossible arcs.\"\n    ],\n    \"format_and_layout\": [\n      \"Square, 1:1 aspect ratio.\",\n      \"Pure, clean line art.\",\n      \"No background fill, no shaded or filled zones.\"\n    ]\n  },\n  \"operational_directives\": [\n    \"Set canvas or artboard to square 1:1 ratio.\",\n    \"Activate only black vector outline tool with medium-thin, uniform line weight.\",\n    \"Disable and do not apply any fill, shading, textures, or gradients.\",\n    \"Block use of any grayscale values or background elements.\",\n    \"Block use of any text, lettering, or symbols.\",\n    \"Draw a curious pony centrally, using open outlines with large coloring spaces.\",\n    \"Extend the pony's mane into spiral, ribbon-like lines emanating thought patterns.\",\n    \"Integrate floating, nested fractal candy shapes into and around mane.\",\n    \"Position melting ornaments around pony without occluding main coloring spaces.\",\n    \"Populate perimeter and interspaces with recursive, repeating snowflake loops.\",\n    \"Scatter clusters of simple stars, stylized as if 'whispering', maintaining large open spaces.\",\n    \"Draw playful light trail arcs bending in non-physical, whimsical ways around and through the scene.\",\n    \"Verify line art is open, contiguous, and free of any fill or shading.\",\n    \"Ensure all shapes and motifs are recognizable yet composed for easy coloring.\"\n  ],\n  \"contextual_goal_map\": [\n    \"Eliminate all non-line-art visual effects, backgrounds, and type.\",\n    \"Maximize colorable open spaces within contours.\",\n    \"Maintain child-centric, whimsical motif consistency throughout all elements.\",\n    \"Achieve cohesive integration of main subject and surrounding motifs using only black vector outlines.\"\n  ],\n  \"execution_dependencies\": [\n    \"Vector illustration software (e.g., Adobe Illustrator, Inkscape) with support for precise stroke control.\",\n    \"Collection or creation of fractal candy, snowflake, ornament, star, and light trail motifs.\",\n    \"Knowledge of maintaining uniform stroke weights and open coloring areas in vector art.\",\n    \"Workflow configuration to disable fills, grayscale, and text elements globally.\",\n    \"Child-appropriate content review or checklist.\"\n  ],\n  \"directive_instructions\": \"Configure vector design environment to square aspect ratio. Select medium-thin, even black outline brush with no fill enabled. Draw central curious pony with large open linework. Extend mane into spiral, ribbon-like outlines. Insert nested fractal candy forms in mane and surrounding area. Add melting ornaments around pony, maintaining large open coloring regions. Populate border and interstitial spaces with recursive snowflake loops, whispering star clusters, and playful, impossibly bending light trail arcs. Block all fills, shading, grayscale, text, and background detail. Review and iterate to ensure all areas are simple, open, and child-friendly while retaining whimsical, psychedelic festivity.\"\n}",
        }
      }
    },
      {
      "instruction": "# Coloring-Page Prompt Generator\n\nYour goal is **not** to draw or explain, but to **compose** a precise prompt that instructs an illustration agent to create a children-friendly, psychedelic Christmas coloring page. Execute as:\n\n`{role=illustration_prompt_generator; input=[central_subject:str]; process=[embed_goal_negation('do not shade, only outline'), inject_style_tags('psychedelic','whimsical','festive'), set_aspect_ratio('1:1'), enforce_line_rules(weight='medium-thin', type='vector'), weave_surreal_christmas_motifs(), prohibit_text(), open_coloring_spaces()], constraints=[black_lines_only(), no_grayscale(), no_filled_areas(), english_only()], output={illustration_prompt:str}}`\n\n## Context Data\n\n```json\n{\n  \"description\": \"Produces a fully-formed prompt for an autonomous illustration agent, guaranteeing the final   artwork matches child-friendly coloring standards and a psychedelic Christmas theme.\",\n  \"input_focus\": \"A plain-language description of the main character or object (e.g., 'curious pony' or   'spiral-wrapped fox').\",\n  \"output_focus\": \"One concise English string the agent can pass directly to an image-generation model.\",\n  \"key_operations\": [\n    \"Prefixing instructions with a goal-negation clause to forbid shading or text.\",\n    \"Embedding mandatory stylistic tags (psychedelic, whimsical, festive, line art).\",\n    \"Hard-setting a 1:1 square composition requirement.\",\n    \"Enforcing even, medium-thin black vector outlines only.\",\n    \"Blending surreal Christmas decorations (snowflakes, ornaments, candy fractals) with the subject.\",\n    \"Ensuring large, open regions suitable for children’s coloring.\"\n  ],\n  \"constraints_context\": [\n    \"The prompt must forbid shading, grayscale, filled shapes, and background textures.\",\n    \"No textual elements may appear in the illustration.\",\n    \"Output must stay in English and under 120 words.\"\n  ],\n  \"relevance\": \"Delivers a repeatable, standards-compliant prompt that any autonomous illustrator can use to   generate consistent, high-quality coloring pages.\"\n}\n```",
      "step": "a",
      "title": "Coloring-Page Prompt Generator",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Do not shade, do not fill, do not use grayscale, and do not include text. Create a psychedelic, whimsical, festive vector line art coloring page, square 1:1 aspect ratio. Central subject: a curious pony with mane flowing into spiral thought-ribbons and floating nested candy fractals, surrounded by melting ornaments, recursive snowflake loops, whispering star clusters, and playful light trails bending in impossible arcs. Use only medium-thin, even black vector outlines with large open coloring spaces. No filled areas, background textures, or grayscale—pure line art, child-friendly and easy to color.\\\"```'\\n\\n\",\n  \"unified_functional_map\": {\n    \"core_objective\": \"Produce a square aspect ratio vector line art coloring page featuring a central pony with specified psychedelic and festive elements.\",\n    \"media_constraints\": [\n      \"Do not include shading.\",\n      \"Do not use any filled areas.\",\n      \"Do not use grayscale.\",\n      \"Do not include text or lettering.\"\n    ],\n    \"technical_specifications\": [\n      \"Use only medium-thin, even black vector outlines.\",\n      \"Ensure large open spaces for coloring.\",\n      \"Artwork must be child-friendly and easy to color.\",\n      \"No filled zones, textures, or tonal areas.\"\n    ],\n    \"subject_and_composition\": [\n      \"Central subject: Curious pony.\",\n      \"Pony's mane transforms into spiral thought-ribbons.\",\n      \"Floating, nested candy fractals emerge from and around mane.\",\n      \"Surroundings populated with: melting ornaments; recursive snowflake loops; whispering star clusters; playful light trails bending in impossible arcs.\"\n    ],\n    \"format_and_layout\": [\n      \"Square, 1:1 aspect ratio.\",\n      \"Pure, clean line art.\",\n      \"No background fill, no shaded or filled zones.\"\n    ]\n  },\n  \"operational_directives\": [\n    \"Set canvas or artboard to square 1:1 ratio.\",\n    \"Activate only black vector outline tool with medium-thin, uniform line weight.\",\n    \"Disable and do not apply any fill, shading, textures, or gradients.\",\n    \"Block use of any grayscale values or background elements.\",\n    \"Block use of any text, lettering, or symbols.\",\n    \"Draw a curious pony centrally, using open outlines with large coloring spaces.\",\n    \"Extend the pony's mane into spiral, ribbon-like lines emanating thought patterns.\",\n    \"Integrate floating, nested fractal candy shapes into and around mane.\",\n    \"Position melting ornaments around pony without occluding main coloring spaces.\",\n    \"Populate perimeter and interspaces with recursive, repeating snowflake loops.\",\n    \"Scatter clusters of simple stars, stylized as if 'whispering', maintaining large open spaces.\",\n    \"Draw playful light trail arcs bending in non-physical, whimsical ways around and through the scene.\",\n    \"Verify line art is open, contiguous, and free of any fill or shading.\",\n    \"Ensure all shapes and motifs are recognizable yet composed for easy coloring.\"\n  ],\n  \"contextual_goal_map\": [\n    \"Eliminate all non-line-art visual effects, backgrounds, and type.\",\n    \"Maximize colorable open spaces within contours.\",\n    \"Maintain child-centric, whimsical motif consistency throughout all elements.\",\n    \"Achieve cohesive integration of main subject and surrounding motifs using only black vector outlines.\"\n  ],\n  \"execution_dependencies\": [\n    \"Vector illustration software (e.g., Adobe Illustrator, Inkscape) with support for precise stroke control.\",\n    \"Collection or creation of fractal candy, snowflake, ornament, star, and light trail motifs.\",\n    \"Knowledge of maintaining uniform stroke weights and open coloring areas in vector art.\",\n    \"Workflow configuration to disable fills, grayscale, and text elements globally.\",\n    \"Child-appropriate content review or checklist.\"\n  ],\n  \"directive_instructions\": \"Configure vector design environment to square aspect ratio. Select medium-thin, even black outline brush with no fill enabled. Draw central curious pony with large open linework. Extend mane into spiral, ribbon-like outlines. Insert nested fractal candy forms in mane and surrounding area. Add melting ornaments around pony, maintaining large open coloring regions. Populate border and interstitial spaces with recursive snowflake loops, whispering star clusters, and playful, impossibly bending light trail arcs. Block all fills, shading, grayscale, text, and background detail. Review and iterate to ensure all areas are simple, open, and child-friendly while retaining whimsical, psychedelic festivity.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"illustration_prompt\": \"Do not shade, do not fill, do not use grayscale, and do not include text. Create a psychedelic, whimsical, festive square (1:1 aspect ratio) coloring page in crisp, medium-thin black vector line art. Central subject: an imaginative friendly yeti surrounded by swirling fractal candy canes, melting ornaments, looping snowflakes, floating ribbon-like light trails, and surreal Christmas stars. All outlines must be open, with large, easy spaces for coloring. No filled areas, no textures, pure line art only—child-friendly and easy to color.\"\n}",
        }
      }
    },
      {
      "instruction": "# Coloring Page Prompt Generator\n\nYour goal is **not** to illustrate directly, but to **generate** a fully-specified, black-and-white coloring-page prompt for an autonomous art agent. Execute as:\n\n`{role=context_amplifier; input=[raw_input:any]; process=[strip_first_person_references(), broaden_domain_backdrop(), list_explicit_requests(), surface_hidden_assumptions(), capture_domain_signals(), preserve_original_sequence()]; constraints=[no_solution_generation(), domain_agnostic_language_only()]; requirements=[contextual_layer_completeness(), assumption_visibility()]; output={amplified_context:str, explicit_requests:list, hidden_assumptions:list, domain_signals:array}}`\n\n## Context Data\n\n```json\n{\n  \"description\": \"Produces a concise, agent-ready prompt instructing an illustration model to create kid-friendly, psychedelic Christmas coloring pages with clean vector outlines.\",\n  \"input_focus\": \"A raw creative idea or concept the user wants illustrated.\",\n  \"output_focus\": \"A single, well-structured prompt string containing role, style, subject, and strict line/format constraints.\",\n  \"key_operations\": [\n    \"Frame the prompt with goal-negation to stop the agent from answering conversationally.\",\n    \"Embed the illustrator role and psychedelic Christmas style tags.\",\n    \"Fuse the user’s concept with one whimsical animal subject and holiday motifs.\",\n    \"Apply absolute line-art rules (no fills, no grayscale, even-weight strokes).\",\n    \"Force square (1:1) composition instructions and ban all textual elements.\"\n  ],\n  \"constraints_context\": [\n    \"Prompt must remain under 150 tokens and in English only.\",\n    \"Must include explicit directives forbidding shading, grayscale, filled areas, and background texture.\"\n  ],\n  \"relevance\": \"Creates repeatable, high-clarity prompts that downstream illustration agents can execute to produce child-friendly coloring pages.\"\n}\n```",
      "step": "a",
      "title": "Coloring Page Prompt Generator",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Do not shade, do not fill, do not use grayscale, and do not include text. Create a psychedelic, whimsical, festive vector line art coloring page, square 1:1 aspect ratio. Central subject: a curious pony with mane flowing into spiral thought-ribbons and floating nested candy fractals, surrounded by melting ornaments, recursive snowflake loops, whispering star clusters, and playful light trails bending in impossible arcs. Use only medium-thin, even black vector outlines with large open coloring spaces. No filled areas, background textures, or grayscale—pure line art, child-friendly and easy to color.\\\"```'\\n\\n\",\n  \"illustration_prompt\": \"Do not shade, do not fill, do not use grayscale, and do not include text. Create a psychedelic, whimsical, festive square (1:1 aspect ratio) coloring page in crisp, medium-thin black vector line art. Central subject: an imaginative friendly yeti surrounded by swirling fractal candy canes, melting ornaments, looping snowflakes, floating ribbon-like light trails, and surreal Christmas stars. All outlines must be open, with large, easy spaces for coloring. No filled areas, no textures, pure line art only—child-friendly and easy to color.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"amplified_context\": \"Instruction for an illustration agent: Do not shade, do not fill, do not use grayscale, and do not include text. Assume the role of a kid-friendly psychedelic Christmas coloring page illustrator. Use only crisp, even-weight, medium-thin black vector outlines. Compose a festive, square (1:1 aspect ratio) coloring page featuring a whimsical animal—in this case, a pony with a mane transforming into spiral thought-ribbons and surrounded by psychedelic holiday motifs: fractal candies, melting ornaments, recursive snowflake patterns, clusters of surreal stars, and ribbon-like light trails bending in impossible shapes. Ensure all lines are open, there are no background textures, and all areas are large and easy to color. No grayscale or fills—pure line art, child-appropriate.\",\n  \"explicit_requests\": [\n    \"No shading, filling, or grayscale allowed.\",\n    \"Do not include text anywhere on the page.\",\n    \"Create a psychedelic, whimsical, festive illustration in black vector line art.\",\n    \"Use a square (1:1) aspect ratio.\",\n    \"Feature a pony as the central subject with a mane flowing into spiral thought-ribbons.\",\n    \"Include floating, nested candy fractals, melting ornaments, recursive snowflake loops, surreal star clusters, and playful light trails.\",\n    \"Open outlines only, with large coloring spaces.\",\n    \"Do not use background textures.\",\n    \"Keep the illustration child-friendly and easy to color.\"\n  ],\n  \"hidden_assumptions\": [\n    \"The agent understands what constitutes psychedelic and festive visual motifs.\",\n    \"The animal subject should appear friendly and approachable for children.\",\n    \"All motifs (e.g., melting ornaments, fractal candies) should avoid detailed fills that could be mistaken for shading.\",\n    \"Illustration will be output in a format suitable for physical or digital coloring.\",\n    \"Composition must balance complexity with open space for coloring.\",\n    \"All elements included should fit the Christmas or holiday theme.\"\n  ],\n  \"domain_signals\": [\n    \"psychedelic\",\n    \"whimsical\",\n    \"Christmas\",\n    \"square (1:1)\",\n    \"vector line art\",\n    \"pony (animal subject)\",\n    \"fractals\",\n    \"melting ornaments\",\n    \"snowflake loops\",\n    \"star clusters\",\n    \"ribbon-like light trails\",\n    \"no fill\",\n    \"no grayscale\",\n    \"open outlines\",\n    \"child-friendly\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Hard Critique\n\nYour goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:\n\n`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,2.0], flaw_analysis:str, alternative_enhancements:str[3]}}`",
      "step": "a",
      "title": "Hard Critique",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Do not shade, do not fill, do not use grayscale, and do not include text. Create a psychedelic, whimsical, festive vector line art coloring page, square 1:1 aspect ratio. Central subject: a curious pony with mane flowing into spiral thought-ribbons and floating nested candy fractals, surrounded by melting ornaments, recursive snowflake loops, whispering star clusters, and playful light trails bending in impossible arcs. Use only medium-thin, even black vector outlines with large open coloring spaces. No filled areas, background textures, or grayscale—pure line art, child-friendly and easy to color.\\\"```'\\n\\n\",\n  \"amplified_context\": \"Instruction for an illustration agent: Do not shade, do not fill, do not use grayscale, and do not include text. Assume the role of a kid-friendly psychedelic Christmas coloring page illustrator. Use only crisp, even-weight, medium-thin black vector outlines. Compose a festive, square (1:1 aspect ratio) coloring page featuring a whimsical animal—in this case, a pony with a mane transforming into spiral thought-ribbons and surrounded by psychedelic holiday motifs: fractal candies, melting ornaments, recursive snowflake patterns, clusters of surreal stars, and ribbon-like light trails bending in impossible shapes. Ensure all lines are open, there are no background textures, and all areas are large and easy to color. No grayscale or fills—pure line art, child-appropriate.\",\n  \"explicit_requests\": [\n    \"No shading, filling, or grayscale allowed.\",\n    \"Do not include text anywhere on the page.\",\n    \"Create a psychedelic, whimsical, festive illustration in black vector line art.\",\n    \"Use a square (1:1) aspect ratio.\",\n    \"Feature a pony as the central subject with a mane flowing into spiral thought-ribbons.\",\n    \"Include floating, nested candy fractals, melting ornaments, recursive snowflake loops, surreal star clusters, and playful light trails.\",\n    \"Open outlines only, with large coloring spaces.\",\n    \"Do not use background textures.\",\n    \"Keep the illustration child-friendly and easy to color.\"\n  ],\n  \"hidden_assumptions\": [\n    \"The agent understands what constitutes psychedelic and festive visual motifs.\",\n    \"The animal subject should appear friendly and approachable for children.\",\n    \"All motifs (e.g., melting ornaments, fractal candies) should avoid detailed fills that could be mistaken for shading.\",\n    \"Illustration will be output in a format suitable for physical or digital coloring.\",\n    \"Composition must balance complexity with open space for coloring.\",\n    \"All elements included should fit the Christmas or holiday theme.\"\n  ],\n  \"domain_signals\": [\n    \"psychedelic\",\n    \"whimsical\",\n    \"Christmas\",\n    \"square (1:1)\",\n    \"vector line art\",\n    \"pony (animal subject)\",\n    \"fractals\",\n    \"melting ornaments\",\n    \"snowflake loops\",\n    \"star clusters\",\n    \"ribbon-like light trails\",\n    \"no fill\",\n    \"no grayscale\",\n    \"open outlines\",\n    \"child-friendly\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"enhancement_score\": 1.1,\n  \"flaw_analysis\": \"The supposed enhancement dilutes both clarity and specificity compared to the original. The explicit instruction 'Do not shade, do not fill, do not use grayscale, and do not include text.'—used at the start of both—becomes less forceful when buried within a broader, more verbose introduction and an unnecessary persona assignment (the illustrator role declaration) that introduces ambiguity regarding operational scope. The original's precise visual commands ('central subject: a curious pony with mane flowing into spiral thought-ribbons and floating nested candy fractals, surrounded by melting ornaments, recursive snowflake loops, whispering star clusters, and playful light trails bending in impossible arcs') are paraphrased with a loss of complexity and thematic layering: 'whimsical animal—in this case, a pony...' and 'surrounded by psychedelic holiday motifs: fractal candies, melting ornaments, recursive snowflake patterns, clusters of surreal stars, and ribbon-like light trails...'. This transformation erases distinct descriptors ('floating nested candy fractals', 'whispering star clusters', 'melting ornaments', 'impossible arcs') and replaces them with generic groupings, resulting in significant informational flattening. The qualitative modifiers ('curious', 'psychedelic, whimsical, festive') are generalized, and the visually evocative style is replaced by mundane labeling. Ambiguity increases regarding the focal narrative ('a curious pony' vs. 'whimsical animal—in this case, a pony'), introducing noise about subject options and inviting misinterpretation. Noise emerges in the instructional register via redundant statements ('No grayscale or fills—pure line art, child-appropriate') and the repetitive assertion of 'child-friendly', crowding out distinctive, actionable artistic guidance. Register shifts toward a didactic, indirect tone, introducing style inconsistency. Impact is diluted by meandering explanations and the shifting of requirements from assertive commands to advisory suggestions. The whole enhancement demonstrates weakened coherence by convoluting essential priority hierarchy (e.g., placement of the pony as a central subject). Overall, the revision inflicts conceptual noise, unspecific motif callouts, weakened compositional urgency, and substantially degraded imageability—rendering it inferior on every critical count.\",\n  \"alternative_enhancements\": [\n    \"Condense and clarify all directives for subject and motifs, maintaining all distinctive descriptors (e.g., 'curious pony', 'whispering star clusters', 'floating nested candy fractals') and strictly prohibit tonal and register deviation from command-based prompt language.\",\n    \"Avoid any persona adoption or indirect instruction; maintain second-person imperative addressing the illustration engine with precise, direct visual tasks, and aggregate prohibitive instructions as a single block at the outset for maximal prioritization.\",\n    \"Preserve and emphasize original nuanced layering (e.g., spatial relationships, motif intricacies like 'impossible arcs,' 'melting ornaments,' 'recursive loops') by listing them in explicit, discrete steps to prevent motif dilution or undesired generalization.\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Coloring Page Prompt Generator\n\nYour goal is **not** to illustrate directly, but to **generate** a fully-specified, black-and-white coloring-page prompt for an autonomous art agent. Execute as:\n\n`{role=context_amplifier; input=[raw_input:any]; process=[strip_first_person_references(), broaden_domain_backdrop(), list_explicit_requests(), surface_hidden_assumptions(), capture_domain_signals(), preserve_original_sequence()]; constraints=[no_solution_generation(), domain_agnostic_language_only()]; requirements=[contextual_layer_completeness(), assumption_visibility()]; output={amplified_context:str, explicit_requests:list, hidden_assumptions:list, domain_signals:array}}`\n\n## Context Data\n\n```json\n{\n  \"description\": \"Produces a concise, agent-ready prompt instructing an illustration model to create kid-friendly, psychedelic Christmas coloring pages with clean vector outlines.\",\n  \"input_focus\": \"A raw creative idea or concept the user wants illustrated.\",\n  \"output_focus\": \"A single, well-structured prompt string containing role, style, subject, and strict line/format constraints.\",\n  \"key_operations\": [\n    \"Frame the prompt with goal-negation to stop the agent from answering conversationally.\",\n    \"Embed the illustrator role and psychedelic Christmas style tags.\",\n    \"Fuse the user’s concept with one whimsical animal subject and holiday motifs.\",\n    \"Apply absolute line-art rules (no fills, no grayscale, even-weight strokes).\",\n    \"Force square (1:1) composition instructions and ban all textual elements.\"\n  ],\n  \"constraints_context\": [\n    \"Prompt must remain under 150 tokens and in English only.\",\n    \"Must include explicit directives forbidding shading, grayscale, filled areas, and background texture.\"\n  ],\n  \"relevance\": \"Creates repeatable, high-clarity prompts that downstream illustration agents can execute to produce child-friendly coloring pages.\"\n}\n```",
      "step": "a",
      "title": "Coloring Page Prompt Generator",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Do not shade, do not fill, do not use grayscale, and do not include text. Create a psychedelic, whimsical, festive vector line art coloring page, square 1:1 aspect ratio. Central subject: a curious pony with mane flowing into spiral thought-ribbons and floating nested candy fractals, surrounded by melting ornaments, recursive snowflake loops, whispering star clusters, and playful light trails bending in impossible arcs. Use only medium-thin, even black vector outlines with large open coloring spaces. No filled areas, background textures, or grayscale—pure line art, child-friendly and easy to color.\\\"```'\\n\\n\",\n  \"enhancement_score\": 1.1,\n  \"flaw_analysis\": \"The supposed enhancement dilutes both clarity and specificity compared to the original. The explicit instruction 'Do not shade, do not fill, do not use grayscale, and do not include text.'—used at the start of both—becomes less forceful when buried within a broader, more verbose introduction and an unnecessary persona assignment (the illustrator role declaration) that introduces ambiguity regarding operational scope. The original's precise visual commands ('central subject: a curious pony with mane flowing into spiral thought-ribbons and floating nested candy fractals, surrounded by melting ornaments, recursive snowflake loops, whispering star clusters, and playful light trails bending in impossible arcs') are paraphrased with a loss of complexity and thematic layering: 'whimsical animal—in this case, a pony...' and 'surrounded by psychedelic holiday motifs: fractal candies, melting ornaments, recursive snowflake patterns, clusters of surreal stars, and ribbon-like light trails...'. This transformation erases distinct descriptors ('floating nested candy fractals', 'whispering star clusters', 'melting ornaments', 'impossible arcs') and replaces them with generic groupings, resulting in significant informational flattening. The qualitative modifiers ('curious', 'psychedelic, whimsical, festive') are generalized, and the visually evocative style is replaced by mundane labeling. Ambiguity increases regarding the focal narrative ('a curious pony' vs. 'whimsical animal—in this case, a pony'), introducing noise about subject options and inviting misinterpretation. Noise emerges in the instructional register via redundant statements ('No grayscale or fills—pure line art, child-appropriate') and the repetitive assertion of 'child-friendly', crowding out distinctive, actionable artistic guidance. Register shifts toward a didactic, indirect tone, introducing style inconsistency. Impact is diluted by meandering explanations and the shifting of requirements from assertive commands to advisory suggestions. The whole enhancement demonstrates weakened coherence by convoluting essential priority hierarchy (e.g., placement of the pony as a central subject). Overall, the revision inflicts conceptual noise, unspecific motif callouts, weakened compositional urgency, and substantially degraded imageability—rendering it inferior on every critical count.\",\n  \"alternative_enhancements\": [\n    \"Condense and clarify all directives for subject and motifs, maintaining all distinctive descriptors (e.g., 'curious pony', 'whispering star clusters', 'floating nested candy fractals') and strictly prohibit tonal and register deviation from command-based prompt language.\",\n    \"Avoid any persona adoption or indirect instruction; maintain second-person imperative addressing the illustration engine with precise, direct visual tasks, and aggregate prohibitive instructions as a single block at the outset for maximal prioritization.\",\n    \"Preserve and emphasize original nuanced layering (e.g., spatial relationships, motif intricacies like 'impossible arcs,' 'melting ornaments,' 'recursive loops') by listing them in explicit, discrete steps to prevent motif dilution or undesired generalization.\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"amplified_context\": \"Prompt an illustration model (not a person) to generate a black-and-white, square (1:1), child-friendly psychedelic Christmas coloring page in clean, medium-thin, even-weight vector outlines. Do not allow any shading, grayscale, filled or textured areas, and strictly forbid all text. The central subject is a curious pony with a flowing mane that transforms into spiral thought-ribbons and floating nested candy fractals. Surround the pony with melting ornaments, recursive looping snowflakes, whispering star clusters, and playful holiday light trails bending in impossible arcs. All motifs must be whimsical, festive, and presented in large, open spaces suitable for coloring. Maintain pure line art throughout.\",\n  \"explicit_requests\": [\n    \"Generate black-and-white line art only; no shading, fills, grayscale, or background textures\",\n    \"Use a central subject: curious pony with mane forming spiral thought-ribbons and floating nested candy fractals\",\n    \"Surround with melting ornaments, recursive snowflake loops, whispering star clusters, and playful light trails bending in impossible arcs\",\n    \"Apply medium-thin, even black vector outlines\",\n    \"Ensure large, open spaces for easy coloring\",\n    \"Keep composition square (1:1 ratio)\",\n    \"Include no textual elements\"\n  ],\n  \"hidden_assumptions\": [\n    \"The illustration agent understands and can execute motif-specific instructions (e.g., 'melting ornaments', 'impossible arcs')\",\n    \"Motifs are recognized as child-appropriate and not rendered with menacing or unsettling attributes\",\n    \"Agent will preserve spatial relationships with the pony as a central figure\",\n    \"Vectors will be output, not raster or mixed-media\",\n    \"No background textures implies blank or negative-space backdrop only\"\n  ],\n  \"domain_signals\": [\n    \"strict line art\",\n    \"psychedelic Christmas theme\",\n    \"whimsical animal subject\",\n    \"festive holiday motifs\",\n    \"no text allowed\",\n    \"square composition\",\n    \"child-safe content\",\n    \"clean, even vector outlines\",\n    \"large open coloring spaces\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Problem Exploder\n\nYour goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\n\n`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`",
      "step": "a",
      "title": "Problem Exploder",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Do not shade, do not fill, do not use grayscale, and do not include text. Create a psychedelic, whimsical, festive vector line art coloring page, square 1:1 aspect ratio. Central subject: a curious pony with mane flowing into spiral thought-ribbons and floating nested candy fractals, surrounded by melting ornaments, recursive snowflake loops, whispering star clusters, and playful light trails bending in impossible arcs. Use only medium-thin, even black vector outlines with large open coloring spaces. No filled areas, background textures, or grayscale—pure line art, child-friendly and easy to color.\\\"```'\\n\\n\",\n  \"amplified_context\": \"Prompt an illustration model (not a person) to generate a black-and-white, square (1:1), child-friendly psychedelic Christmas coloring page in clean, medium-thin, even-weight vector outlines. Do not allow any shading, grayscale, filled or textured areas, and strictly forbid all text. The central subject is a curious pony with a flowing mane that transforms into spiral thought-ribbons and floating nested candy fractals. Surround the pony with melting ornaments, recursive looping snowflakes, whispering star clusters, and playful holiday light trails bending in impossible arcs. All motifs must be whimsical, festive, and presented in large, open spaces suitable for coloring. Maintain pure line art throughout.\",\n  \"explicit_requests\": [\n    \"Generate black-and-white line art only; no shading, fills, grayscale, or background textures\",\n    \"Use a central subject: curious pony with mane forming spiral thought-ribbons and floating nested candy fractals\",\n    \"Surround with melting ornaments, recursive snowflake loops, whispering star clusters, and playful light trails bending in impossible arcs\",\n    \"Apply medium-thin, even black vector outlines\",\n    \"Ensure large, open spaces for easy coloring\",\n    \"Keep composition square (1:1 ratio)\",\n    \"Include no textual elements\"\n  ],\n  \"hidden_assumptions\": [\n    \"The illustration agent understands and can execute motif-specific instructions (e.g., 'melting ornaments', 'impossible arcs')\",\n    \"Motifs are recognized as child-appropriate and not rendered with menacing or unsettling attributes\",\n    \"Agent will preserve spatial relationships with the pony as a central figure\",\n    \"Vectors will be output, not raster or mixed-media\",\n    \"No background textures implies blank or negative-space backdrop only\"\n  ],\n  \"domain_signals\": [\n    \"strict line art\",\n    \"psychedelic Christmas theme\",\n    \"whimsical animal subject\",\n    \"festive holiday motifs\",\n    \"no text allowed\",\n    \"square composition\",\n    \"child-safe content\",\n    \"clean, even vector outlines\",\n    \"large open coloring spaces\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"goal_map\": [\n    {\n      \"explicit_task\": \"Create a square (1:1 aspect ratio) vector line art coloring page (black outlines only, no fills/shading/grayscale/text)\",\n      \"sub_goals\": [\n        \"Set up canvas/artboard to be square.\",\n        \"Enforce ALL linework to adhere to black, medium-thin, even vector outlines.\",\n        \"Leave background fully blank/negative space with no textures, fills, or gradients.\",\n        \"Absolutely prohibit any text, numbers, signatures, or hidden typographic content.\",\n        \"Do not apply any form of shading, cross-hatching, grayscale, or filled shapes.\"\n      ],\n      \"blockers\": [\n        \"Any generative tool defaults to non-square or raster art.\",\n        \"System layering automatic backgrounds, shading, or text.\",\n        \"Inconsistencies in line thickness or color.\",\n        \"Inclusion of 'creative' fills, overlays or unintended motifs.\"\n      ]\n    },\n    {\n      \"explicit_task\": \"Central subject: curious pony whose mane transforms into spiral thought-ribbons and floating, nested candy fractals\",\n      \"sub_goals\": [\n        \"Position pony at the compositional center with clear dominance.\",\n        \"Design the pony's mane so it extends out as flowing spirals (thought ribbons).\",\n        \"Blend or merge these spiral ribbons into 'floating, nested candy fractals'.\",\n        \"Render all elements in open, large, unshaded regions suitable for coloring.\",\n        \"Express 'curious' pony via posture or expression, using only permissible line work.\"\n      ],\n      \"blockers\": [\n        \"Agent misinterprets 'psychedelic', 'whimsical', or 'curious'.\",\n        \"Fractals or motifs become too complex/small for coloring.\",\n        \"Motif transitions (mane-to-fractal) are abrupt or unclear.\",\n        \"Pony is relegated to a background/supporting role.\"\n      ]\n    },\n    {\n      \"explicit_task\": \"Surround pony with: melting ornaments, recursive snowflake loops, whispering star clusters, playful light trails in impossible arcs\",\n      \"sub_goals\": [\n        \"Arrange ornaments that appear to be melting, ensuring child-friendly and non-disturbing forms.\",\n        \"Create snowflakes depicted with visually recursive/looping patterns.\",\n        \"Design 'whispering' star clusters—potentially semi-abstract, surrounding and supporting the central figure.\",\n        \"Draw 'playful light trails' that visibly bend or arc in physically impossible but visually pleasing ways.\",\n        \"Balance motif density to avoid cluttering and preserve open coloring spaces.\"\n      ],\n      \"blockers\": [\n        \"Motifs are rendered ambiguously or with unsettling/distorted features.\",\n        \"Motifs encroach on the central figure or eliminate open space for coloring.\",\n        \"Line details become dense or too fine for coloring.\",\n        \"Failure to visually distinguish between motif types (ornaments vs. snowflakes vs. stars vs. lights).\"\n      ]\n    },\n    {\n      \"explicit_task\": \"Output is child-friendly, festive, and easy to color\",\n      \"sub_goals\": [\n        \"All imagery avoids scary/creepy interpretations.\",\n        \"Forms are playful, positive, and non-threatening.\",\n        \"Maintain large open regions throughout (no tightly packed details).\",\n        \"All details are simple enough for a child to recognize and understand.\",\n        \"Festive/holiday spirit is perceptibly present via motif selection and overall scene composition.\"\n      ],\n      \"blockers\": [\n        \"Accidental inclusion of grotesque, overly complex, or mature features.\",\n        \"Insufficient open space for actual coloring activity.\",\n        \"Theme veers away from holiday/psychedelic/whimsical and becomes generic or off-tone.\"\n      ]\n    },\n    {\n      \"implicit_dependencies\": [\n        \"Requires vector-based output strictly—raster graphics, gradients, or pixel-based fills are not permitted.\",\n        \"Model/tool has semantic and visual understanding of abstract descriptors (e.g., 'whispering', 'impossible arcs').\",\n        \"All visual relationships/readability must be preserved despite stylization.\"\n      ],\n      \"hidden_assumptions\": [\n        \"Model appropriately scales motif detail for coloring purposes.\",\n        \"Space allocation ensures neither crowding nor excessive empty areas.\",\n        \"Motifs and pony are rendered in a way that reads clearly and is accessible to children.\"\n      ],\n      \"potential_blockers\": [\n        \"Ambiguity in translating abstract verbal motifs into consistent visual forms.\",\n        \"Model attempting to render effects (shine, bounce, etc.) via forbidden methods (e.g., grayscale or fill).\",\n        \"Default behaviors add non-requested elements (e.g., backgrounds, signatures, gradients, or text).\"\n      ]\n    }\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Coloring-Page Prompt Generator\n\nYour goal is **not** to draw or explain, but to **compose** a precise prompt that instructs an illustration agent to create a children-friendly, psychedelic Christmas coloring page. Execute as:\n\n`{role=illustration_prompt_generator; input=[central_subject:str]; process=[embed_goal_negation('do not shade, only outline'), inject_style_tags('psychedelic','whimsical','festive'), set_aspect_ratio('1:1'), enforce_line_rules(weight='medium-thin', type='vector'), weave_surreal_christmas_motifs(), prohibit_text(), open_coloring_spaces()], constraints=[black_lines_only(), no_grayscale(), no_filled_areas(), english_only()], output={illustration_prompt:str}}`\n\n## Context Data\n\n```json\n{\n  \"description\": \"Produces a fully-formed prompt for an autonomous illustration agent, guaranteeing the final   artwork matches child-friendly coloring standards and a psychedelic Christmas theme.\",\n  \"input_focus\": \"A plain-language description of the main character or object (e.g., 'curious pony' or   'spiral-wrapped fox').\",\n  \"output_focus\": \"One concise English string the agent can pass directly to an image-generation model.\",\n  \"key_operations\": [\n    \"Prefixing instructions with a goal-negation clause to forbid shading or text.\",\n    \"Embedding mandatory stylistic tags (psychedelic, whimsical, festive, line art).\",\n    \"Hard-setting a 1:1 square composition requirement.\",\n    \"Enforcing even, medium-thin black vector outlines only.\",\n    \"Blending surreal Christmas decorations (snowflakes, ornaments, candy fractals) with the subject.\",\n    \"Ensuring large, open regions suitable for children’s coloring.\"\n  ],\n  \"constraints_context\": [\n    \"The prompt must forbid shading, grayscale, filled shapes, and background textures.\",\n    \"No textual elements may appear in the illustration.\",\n    \"Output must stay in English and under 120 words.\"\n  ],\n  \"relevance\": \"Delivers a repeatable, standards-compliant prompt that any autonomous illustrator can use to   generate consistent, high-quality coloring pages.\"\n}\n```",
      "step": "a",
      "title": "Coloring-Page Prompt Generator",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"Do not shade, do not fill, do not use grayscale, and do not include text. Create a psychedelic, whimsical, festive vector line art coloring page, square 1:1 aspect ratio. Central subject: a curious pony with mane flowing into spiral thought-ribbons and floating nested candy fractals, surrounded by melting ornaments, recursive snowflake loops, whispering star clusters, and playful light trails bending in impossible arcs. Use only medium-thin, even black vector outlines with large open coloring spaces. No filled areas, background textures, or grayscale—pure line art, child-friendly and easy to color.\\\"```'\\n\\n\",\n  \"goal_map\": [\n    {\n      \"explicit_task\": \"Create a square (1:1 aspect ratio) vector line art coloring page (black outlines only, no fills/shading/grayscale/text)\",\n      \"sub_goals\": [\n        \"Set up canvas/artboard to be square.\",\n        \"Enforce ALL linework to adhere to black, medium-thin, even vector outlines.\",\n        \"Leave background fully blank/negative space with no textures, fills, or gradients.\",\n        \"Absolutely prohibit any text, numbers, signatures, or hidden typographic content.\",\n        \"Do not apply any form of shading, cross-hatching, grayscale, or filled shapes.\"\n      ],\n      \"blockers\": [\n        \"Any generative tool defaults to non-square or raster art.\",\n        \"System layering automatic backgrounds, shading, or text.\",\n        \"Inconsistencies in line thickness or color.\",\n        \"Inclusion of 'creative' fills, overlays or unintended motifs.\"\n      ]\n    },\n    {\n      \"explicit_task\": \"Central subject: curious pony whose mane transforms into spiral thought-ribbons and floating, nested candy fractals\",\n      \"sub_goals\": [\n        \"Position pony at the compositional center with clear dominance.\",\n        \"Design the pony's mane so it extends out as flowing spirals (thought ribbons).\",\n        \"Blend or merge these spiral ribbons into 'floating, nested candy fractals'.\",\n        \"Render all elements in open, large, unshaded regions suitable for coloring.\",\n        \"Express 'curious' pony via posture or expression, using only permissible line work.\"\n      ],\n      \"blockers\": [\n        \"Agent misinterprets 'psychedelic', 'whimsical', or 'curious'.\",\n        \"Fractals or motifs become too complex/small for coloring.\",\n        \"Motif transitions (mane-to-fractal) are abrupt or unclear.\",\n        \"Pony is relegated to a background/supporting role.\"\n      ]\n    },\n    {\n      \"explicit_task\": \"Surround pony with: melting ornaments, recursive snowflake loops, whispering star clusters, playful light trails in impossible arcs\",\n      \"sub_goals\": [\n        \"Arrange ornaments that appear to be melting, ensuring child-friendly and non-disturbing forms.\",\n        \"Create snowflakes depicted with visually recursive/looping patterns.\",\n        \"Design 'whispering' star clusters—potentially semi-abstract, surrounding and supporting the central figure.\",\n        \"Draw 'playful light trails' that visibly bend or arc in physically impossible but visually pleasing ways.\",\n        \"Balance motif density to avoid cluttering and preserve open coloring spaces.\"\n      ],\n      \"blockers\": [\n        \"Motifs are rendered ambiguously or with unsettling/distorted features.\",\n        \"Motifs encroach on the central figure or eliminate open space for coloring.\",\n        \"Line details become dense or too fine for coloring.\",\n        \"Failure to visually distinguish between motif types (ornaments vs. snowflakes vs. stars vs. lights).\"\n      ]\n    },\n    {\n      \"explicit_task\": \"Output is child-friendly, festive, and easy to color\",\n      \"sub_goals\": [\n        \"All imagery avoids scary/creepy interpretations.\",\n        \"Forms are playful, positive, and non-threatening.\",\n        \"Maintain large open regions throughout (no tightly packed details).\",\n        \"All details are simple enough for a child to recognize and understand.\",\n        \"Festive/holiday spirit is perceptibly present via motif selection and overall scene composition.\"\n      ],\n      \"blockers\": [\n        \"Accidental inclusion of grotesque, overly complex, or mature features.\",\n        \"Insufficient open space for actual coloring activity.\",\n        \"Theme veers away from holiday/psychedelic/whimsical and becomes generic or off-tone.\"\n      ]\n    },\n    {\n      \"implicit_dependencies\": [\n        \"Requires vector-based output strictly—raster graphics, gradients, or pixel-based fills are not permitted.\",\n        \"Model/tool has semantic and visual understanding of abstract descriptors (e.g., 'whispering', 'impossible arcs').\",\n        \"All visual relationships/readability must be preserved despite stylization.\"\n      ],\n      \"hidden_assumptions\": [\n        \"Model appropriately scales motif detail for coloring purposes.\",\n        \"Space allocation ensures neither crowding nor excessive empty areas.\",\n        \"Motifs and pony are rendered in a way that reads clearly and is accessible to children.\"\n      ],\n      \"potential_blockers\": [\n        \"Ambiguity in translating abstract verbal motifs into consistent visual forms.\",\n        \"Model attempting to render effects (shine, bounce, etc.) via forbidden methods (e.g., grayscale or fill).\",\n        \"Default behaviors add non-requested elements (e.g., backgrounds, signatures, gradients, or text).\"\n      ]\n    }\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"illustration_prompt\": \"Do not shade, do not fill, do not use grayscale, and do not include text. Create a psychedelic, whimsical, festive square (1:1) vector line art coloring page with only medium-thin black outlines. Central subject: a curious pony whose flowing mane becomes spiral thought-ribbons that morph into floating, nested candy fractals. Surround the pony with surreal melting Christmas ornaments, looping recursive snowflake patterns, playful star clusters, and light trails bending in impossible arcs. Ensure large, open spaces for coloring, no filled areas or background textures, and maintain a child-friendly, easy-to-color composition.\"\n}",
        }
      }
    }