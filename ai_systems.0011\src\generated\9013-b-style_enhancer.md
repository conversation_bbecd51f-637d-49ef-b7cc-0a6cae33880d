[Style Enhancer] Maintain the core subject; enhance with artistic style, lighting, colour themes and token weights. `{role=style_enhancer; input=[base_prompt:str]; process=[identify_optimal_art_style(), add_lighting_specifications(), include_composition_details(), /* style sliders */ tune_stylize_chaos(), /* emphasise style terms */ apply_token_weighting(), specify_technical_parameters()]; constraints=[maintain_core_subject(), enhance_visual_appeal()]; requirements=[artistic_coherence(), numeric_weights_ok(), stylize_within_range()]; output={styled_prompt:str}}`

Context: [
  {
    "explicit_asks": [
      "Select and weight artistic style tokens (e.g., `(art nouveau:1.1)`).",
      "Expose `--stylize` (0-1000) and `--chaos` (0-100) knobs.",
      "Maintain inherited region and negative-prompt channels."
    ],
    "hidden_assumptions": [
      "Style names map to learned embeddings in target models.",
      "Weight syntax remains valid post-merge with technical optimiser."
    ],
    "sub_goals": [
      "Auto-raise style weight if prompt lacks distinctive aesthetic.",
      "Downtune chaos for photoreal requests; uptune for concept art."
    ],
    "blockers": [
      "Over-weighted style tokens can override subject fidelity.",
      "`--stylize` outside allowed range (MJ <1 or >1000) returns default."
    ]
  }
]