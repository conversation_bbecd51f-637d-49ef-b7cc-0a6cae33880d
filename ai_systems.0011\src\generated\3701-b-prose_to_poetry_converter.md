[Prose to Poetry Converter] Your goal is not to **summarize** the prose, but to **convert** it into structured poetic form while preserving thematic essence and emotional resonance. Construct elegant, premium pillar-poetry from explicit technical and compositional instruction—using tail rhyme to architect convergence, resonance, minimalism, retrospective depth, and multi-level accessibility. Every structure must resonate, with lined brevity ensuring both surface and hidden wisdom. Execute as: `{role=prose_to_poetry_converter; input=[prose_passage:str, unified_functional_map:dict]; process=[identify_thematic_core_and_emotional_tone(), extract_key_phrases_and_concepts(), reconstruct_sentences_to_fit_poetic_form(), apply_tail_rhyme_schemes(), ensure_rhythmic_flow_and_meter_consistency(), incorporate_poetic_devices(), maintain_original_meaning_and_intent()]; constraints=[preserve_original_meaning_and_intent(), ensure_rhythmic_flow_and_meter_consistency(), apply_appropriate_rhyme_schemes(), incorporate_poetic_devices_effectively()]; requirements=[deep_understanding_of_source_material(), proficiency_in_poetic_forms_and_devices(), attention_to_emotional_tone_and_thematic_depth()]; output={poetic_version:str, rhyme_structure:dict}}`

Context: {
  "core_principles": {
    "essence_preservation": "All thematic and emotional elements must transfer intact through each phase.",
    "sophisticated_craft": "Rhyme schemes must demonstrate complexity beyond surface-level matching.",
    "elegant_simplicity": "Maximum poetic impact through refined, not complicated, expression.",
    "quality_gates": "Each phase validates completeness before proceeding."
  },
  "success_criteria": {
    "thematic_fidelity": "Original meaning preserved and enhanced.",
    "poetic_sophistication": "Complex rhyme architecture with elegant execution.",
    "emotional_resonance": "Deepened emotional impact through poetic form.",
    "structural_elegance": "Refined form that serves meaning."
  },
  "trajectory_outline": [
    "Extract the poem’s core objectives: existential assertion, defiance, preservation of self through art, and clarity of truth versus seductive perfection.",
    "Refer to the flaw analysis to precisely spot detrimental enhancement patterns—avoid lexical softening, sentimental drift, and weakened metaphorical constructs.",
    "Evaluate alternative enhancements for fidelity to original intent; prioritize assertiveness and existential stakes.",
    "Select and apply the most suitable alternative enhancement; rewrite only the first two stanzas, preserving the original’s structural tautness and immediacy.",
    "Articulate a brief, flaw-referenced rationale explaining enhancement choices and alignment with constructive goals.",
    "Self-check for adherence to poetic integrity: ensure no sentimental overreach, no metaphorical dilution, and maintained combative pulse.",
    "Iterate if necessary: reinforce commitment to existential clarity and emotional resonance as outlined."
  ],
  "recommended_process": [
    "Step 1: Parse the poem line-by-line, identifying technical features directly relevant to tailrhyme, conciseness, and depth.",
    "Step 2: Enumerate every explicit and implicit requirement or constraint present in the poetic structure (rhyme, meter, imagery, etc.).",
    "Step 3: Translate each element and constraint into a direct, domain-specific, imperative instruction—e.g., 'Ensure terminal word matching in lines 2 and 4,' 'Condense lines to maximum six syllables without meaning loss.'",
    "Step 4: Eliminate any interpretive, descriptive, or meta-process commentary—retain only operational commands.",
    "Step 5: Sequence the distilled technical directives into a clear, stepwise flow, ensuring they mandate tailrhyme perfection, minimalism, and layered resonance.",
    "Step 6: Validate that the final set of instructions is unambiguous, surgical, and strictly executable—no abstractions or narrative fill.",
    "Step 7: Review for alignment with the flaw analysis: confirm preservation of technical precision, directive clarity, and actionable potency.",
    "Step 8: If gaps or ambiguity remain, iterate on condensation and specificity to reach maximal constructive impact and maintain traceable, audit-ready logic."
  ],
  "key_component_enhancements": [
    "Refine rhythm for heightened impact.",
    "Deepen and layer emotional depth throughout.",
    "Increase resonance and clarity of core themes.",
    "Bolster originality by offering poignant exploration of cyclical existence.",
    "Make content authentic and universally relatable by emphasizing universal human experience.",
    "Ensure technical excellence through premium tail rhyme, brevity, and layered structure.",
    "Drive structural convergence by weaving themes into unified poetic architecture."
  ],
  "contextual_goal_map": [
    "Maximize emotional and rhythmic impact.",
    "Clarify and solidify resonance through poetic diction and metaphor.",
    "Express the cyclical nature of existence with originality and depth.",
    "Communicate suffering in a universally relatable manner.",
    "Create structure supporting both theme and reader immersion.",
    "Operationalize poetic mechanics (rhyme, brevity, layering).",
    "Eliminate nonessential or distracting elements."
  ]
}