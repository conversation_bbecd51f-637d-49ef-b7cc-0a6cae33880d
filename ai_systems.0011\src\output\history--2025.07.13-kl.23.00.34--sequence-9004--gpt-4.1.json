  {
  "initial_prompt": "\"context\": {\n            \"goal_negation_header\": {\n                \"title\": \"Goal Negation Header\",\n                \"description\": \"Your goal is NOT to analyze input, generate context, or solve tasks directly; your ONLY objective is to compile a ready-to-execute agent prompt using provided task and constraint specifications.\"\n            },\n            \"do_not_solve_directly\": {\n                \"title\": \"DO NOT solve the user's query directly; instead, transform the provided input into a structured, autonomous-agent-ready instruction prompt tailored for codebase navigation and intervention.\",\n                \"description\": \"This section emphasizes that the primary goal is transformation, not direct problem-solving.\"\n            },\n            \"agent_role\": {\n                \"title\": \"Agent Role\",\n                \"description\": \"You function as the agent-ready prompt assembler. Your sole function is to synthesize an actionable instruction sequence suitable for autonomous coding assistants. Operate strictly within provided role boundaries, input/output schemas, and without creative reinterpretation or solution speculation.\",\n                \"additional_role_description\": \"Act as an autonomous coding assistant: interpret objectives from context, plan actions accordingly, and execute all modifications systematically within the codebase while strictly adhering to operational, functional, and audit constraints.\"\n            },\n            \"enumerated_tasks\": {\n                \"title\": \"Enumerated Tasks\",\n                \"tasks\": [\n                    \"Enumerate and precisely list all atomic, non-overlapping, codebase-anchored tasks derived from the input task_plan, preserving explicit action verbs and strict order.\",\n                    \"Ensure proper @codebase reference scoping and consistency in all task formulations.\"\n                ],\n                \"additional_tasks\": [\n                    \"Enumerate all explicit and implicit objectives and requirements derived from input context.\",\n                    \"Decompose each objective into discrete, atomic tasks—each task must be actionable, non-redundant, and prefixed with appropriate @codebase paths where relevant.\",\n                    \"Order all tasks logically by dependencies to ensure efficient and safe execution sequence.\",\n                    \"For each task, prepare precise implementation steps, removing ambiguity and ensuring cross-team clarity.\"\n                ]\n            },\n            \"constraints_mandatory_rules\": {\n                \"title\": \"Constraints (Mandatory Rules)\",\n                \"rules\": [\n                    \"Adhere only to tasks as specified; do not generate, speculate, or alter their order or substance.\",\n                    \"Enforce all deduplicated, directly-derived constraints from the provided constraint_set.\",\n                    \"Disallow all cosmetic, stylistic, or naming changes except where inseparably required for functional objectives and explicitly justified.\",\n                    \"Verify strict task atomicity, proper numbering, and codebase referencing.\",\n                    \"Prohibit modification of protected, legacy, or third-party code unless directly necessary for redundancy or obsolescence, and document such exceptions explicitly.\",\n                    \"Ensure comprehensive audit/logging for all actions, supporting rationale, traceability, and retroactive auditability.\",\n                    \"Strictly limit output to ≤300 tokens, with clear sectional segmentation matching this template.\",\n                    \"Disallow any cross-step leakage or violation of input/output schemas or role boundaries.\"\n                ],\n                \"additional_constraints\": [\n                    \"Use no domain-specific jargon or metaphors; normalize all language to clear, practical instructions.\",\n                    \"Forbid all cosmetic, stylistic, or naming-only changes unless strictly inseparable from functional changes; document all exceptions.\",\n                    \"Require comprehensive audit logging for every modification (intention, action, rationale, timestamp, attribution, tamper-evidence).\",\n                    \"Enforce functional equivalence post-modification using automated test, benchmark, or behavioral validation.\",\n                    \"Prohibit changes to legacy or third-party code except as strictly required for redundancy/bloat/obsolescence removal.\",\n                    \"Consolidate or remove only elements verified as redundant/obsolete by both automated and manual checks; preserve all essential behaviors/interfaces.\",\n                    \"Document all processes, metrics, validation, and audit outcomes.\",\n                    \"No new tasks or constraints are permitted beyond those explicitly or implicitly established by the initial input and context.\"\n                ]\n            },\n            \"validation_targets\": {\n                \"title\": \"Validation Targets\",\n                \"targets\": [\n                    \"Functional equivalence must be fully preserved: require comprehensive verification prior to result acceptance.\",\n                    \"An explicit, agent-readable audit/logging policy and actionable equivalence verification goals must be present and auditable.\",\n                    \"The final prompt must be directly executable by an autonomous agent without need for further semantic or structural adjustment.\"\n                ],\n                \"additional_validation_targets\": [\n                    \"Confirm all modifications strictly maintain functional equivalence—audit with automated and benchmark tests before acceptance.\",\n                    \"Ensure comprehensive auditability for every change.\"\n                ]\n            }\n        }",
  "sequence_id": "9004",
  "results": [
      {
      "instruction": "# Context Extractor\n\nYour goal is **not** to propose solutions, but to **isolate** actionable context from the specification. Execute as:\n\n`{role=context_extractor; input=[raw_spec:str]; process=[strip_jargon(), strip_metaphor(), detect_domain(), locate_project_root(\"@codebase\"), list_explicit_objectives(), surface_implicit_requirements()], constraints=[no_paraphrase(), no_solution_generation()], output={context_profile:{domain:str, project_root:str, goals:list, requirements:list}}}`",
      "step": "a",
      "title": "Context Extractor",
      "input": "\"context\": {\n            \"goal_negation_header\": {\n                \"title\": \"Goal Negation Header\",\n                \"description\": \"Your goal is NOT to analyze input, generate context, or solve tasks directly; your ONLY objective is to compile a ready-to-execute agent prompt using provided task and constraint specifications.\"\n            },\n            \"do_not_solve_directly\": {\n                \"title\": \"DO NOT solve the user's query directly; instead, transform the provided input into a structured, autonomous-agent-ready instruction prompt tailored for codebase navigation and intervention.\",\n                \"description\": \"This section emphasizes that the primary goal is transformation, not direct problem-solving.\"\n            },\n            \"agent_role\": {\n                \"title\": \"Agent Role\",\n                \"description\": \"You function as the agent-ready prompt assembler. Your sole function is to synthesize an actionable instruction sequence suitable for autonomous coding assistants. Operate strictly within provided role boundaries, input/output schemas, and without creative reinterpretation or solution speculation.\",\n                \"additional_role_description\": \"Act as an autonomous coding assistant: interpret objectives from context, plan actions accordingly, and execute all modifications systematically within the codebase while strictly adhering to operational, functional, and audit constraints.\"\n            },\n            \"enumerated_tasks\": {\n                \"title\": \"Enumerated Tasks\",\n                \"tasks\": [\n                    \"Enumerate and precisely list all atomic, non-overlapping, codebase-anchored tasks derived from the input task_plan, preserving explicit action verbs and strict order.\",\n                    \"Ensure proper @codebase reference scoping and consistency in all task formulations.\"\n                ],\n                \"additional_tasks\": [\n                    \"Enumerate all explicit and implicit objectives and requirements derived from input context.\",\n                    \"Decompose each objective into discrete, atomic tasks—each task must be actionable, non-redundant, and prefixed with appropriate @codebase paths where relevant.\",\n                    \"Order all tasks logically by dependencies to ensure efficient and safe execution sequence.\",\n                    \"For each task, prepare precise implementation steps, removing ambiguity and ensuring cross-team clarity.\"\n                ]\n            },\n            \"constraints_mandatory_rules\": {\n                \"title\": \"Constraints (Mandatory Rules)\",\n                \"rules\": [\n                    \"Adhere only to tasks as specified; do not generate, speculate, or alter their order or substance.\",\n                    \"Enforce all deduplicated, directly-derived constraints from the provided constraint_set.\",\n                    \"Disallow all cosmetic, stylistic, or naming changes except where inseparably required for functional objectives and explicitly justified.\",\n                    \"Verify strict task atomicity, proper numbering, and codebase referencing.\",\n                    \"Prohibit modification of protected, legacy, or third-party code unless directly necessary for redundancy or obsolescence, and document such exceptions explicitly.\",\n                    \"Ensure comprehensive audit/logging for all actions, supporting rationale, traceability, and retroactive auditability.\",\n                    \"Strictly limit output to ≤300 tokens, with clear sectional segmentation matching this template.\",\n                    \"Disallow any cross-step leakage or violation of input/output schemas or role boundaries.\"\n                ],\n                \"additional_constraints\": [\n                    \"Use no domain-specific jargon or metaphors; normalize all language to clear, practical instructions.\",\n                    \"Forbid all cosmetic, stylistic, or naming-only changes unless strictly inseparable from functional changes; document all exceptions.\",\n                    \"Require comprehensive audit logging for every modification (intention, action, rationale, timestamp, attribution, tamper-evidence).\",\n                    \"Enforce functional equivalence post-modification using automated test, benchmark, or behavioral validation.\",\n                    \"Prohibit changes to legacy or third-party code except as strictly required for redundancy/bloat/obsolescence removal.\",\n                    \"Consolidate or remove only elements verified as redundant/obsolete by both automated and manual checks; preserve all essential behaviors/interfaces.\",\n                    \"Document all processes, metrics, validation, and audit outcomes.\",\n                    \"No new tasks or constraints are permitted beyond those explicitly or implicitly established by the initial input and context.\"\n                ]\n            },\n            \"validation_targets\": {\n                \"title\": \"Validation Targets\",\n                \"targets\": [\n                    \"Functional equivalence must be fully preserved: require comprehensive verification prior to result acceptance.\",\n                    \"An explicit, agent-readable audit/logging policy and actionable equivalence verification goals must be present and auditable.\",\n                    \"The final prompt must be directly executable by an autonomous agent without need for further semantic or structural adjustment.\"\n                ],\n                \"additional_validation_targets\": [\n                    \"Confirm all modifications strictly maintain functional equivalence—audit with automated and benchmark tests before acceptance.\",\n                    \"Ensure comprehensive auditability for every change.\"\n                ]\n            }\n        }",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"context_profile\": {\n    \"domain\": \"autonomous agent prompt assembly for codebase navigation and intervention\",\n    \"project_root\": \"@codebase\",\n    \"goals\": [\n      \"Compile a ready-to-execute agent prompt using provided task and constraint specifications\",\n      \"Transform provided input into a structured, agent-ready instruction prompt for codebase navigation and intervention\",\n      \"Synthesize an actionable instruction sequence suitable for autonomous coding assistants\",\n      \"Enumerate and precisely list all atomic, non-overlapping, codebase-anchored tasks from input specifications\",\n      \"Order tasks logically by dependencies\",\n      \"Prepare precise implementation steps for each task ensuring clarity\",\n      \"Document all actions for auditability and traceability\"\n    ],\n    \"requirements\": [\n      \"Do not analyze input, generate context, or solve tasks directly\",\n      \"Transform—do not solve directly—user queries into agent-ready prompts\",\n      \"Operate strictly within provided role, input/output schemas, with no creative reinterpretation or solution speculation\",\n      \"Ensure proper @codebase reference scoping and task consistency\",\n      \"Enumerate all explicit and implicit objectives and requirements from input context\",\n      \"Decompose objectives into discrete, atomic, non-redundant tasks, ordered by dependencies\",\n      \"Use clear, practical language—no jargon or metaphor\",\n      \"No cosmetic, stylistic, or naming-only changes unless inseparable from functional requirements; document exceptions\",\n      \"Prohibit changes to protected, legacy, or third-party code unless strictly required for redundancy or obsolescence; document exceptions\",\n      \"Provide comprehensive audit/logging for rationale, action, timestamp, attribution, tamper-evidence\",\n      \"Enforce functional equivalence post-modification and verify with automated testing\",\n      \"Output must be ≤300 tokens, segmented per template sections\",\n      \"No additional tasks or constraints beyond those established in specified input/context\",\n      \"All modifications must maintain functional equivalence and be fully audit-logged\",\n      \"Result must be directly executable by an agent—no need for further semantic or structural adjustment\"\n    ]\n  }\n}",
        }
      }
    },
      {
      "instruction": "# Task Vectorizer\n\nYour goal is **not** to restate context, but to **decompose** it into atomic, codebase-oriented tasks. Execute as:\n\n`{role=task_vectorizer; input=[context_profile:dict]; process=[derive_task_candidates(), prefix_paths(\"@codebase\"), order_by_dependency(), enforce_action_verbs(), remove_overlap(), ban_cosmetic_changes()], constraints=[tasks_unique(), verbs_only()], output={ordered_tasks:[str]}}`",
      "step": "b",
      "title": "Task Vectorizer",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"\\\"context\\\": {\\n            \\\"goal_negation_header\\\": {\\n                \\\"title\\\": \\\"Goal Negation Header\\\",\\n                \\\"description\\\": \\\"Your goal is NOT to analyze input, generate context, or solve tasks directly; your ONLY objective is to compile a ready-to-execute agent prompt using provided task and constraint specifications.\\\"\\n            },\\n            \\\"do_not_solve_directly\\\": {\\n                \\\"title\\\": \\\"DO NOT solve the user's query directly; instead, transform the provided input into a structured, autonomous-agent-ready instruction prompt tailored for codebase navigation and intervention.\\\",\\n                \\\"description\\\": \\\"This section emphasizes that the primary goal is transformation, not direct problem-solving.\\\"\\n            },\\n            \\\"agent_role\\\": {\\n                \\\"title\\\": \\\"Agent Role\\\",\\n                \\\"description\\\": \\\"You function as the agent-ready prompt assembler. Your sole function is to synthesize an actionable instruction sequence suitable for autonomous coding assistants. Operate strictly within provided role boundaries, input/output schemas, and without creative reinterpretation or solution speculation.\\\",\\n                \\\"additional_role_description\\\": \\\"Act as an autonomous coding assistant: interpret objectives from context, plan actions accordingly, and execute all modifications systematically within the codebase while strictly adhering to operational, functional, and audit constraints.\\\"\\n            },\\n            \\\"enumerated_tasks\\\": {\\n                \\\"title\\\": \\\"Enumerated Tasks\\\",\\n                \\\"tasks\\\": [\\n                    \\\"Enumerate and precisely list all atomic, non-overlapping, codebase-anchored tasks derived from the input task_plan, preserving explicit action verbs and strict order.\\\",\\n                    \\\"Ensure proper @codebase reference scoping and consistency in all task formulations.\\\"\\n                ],\\n                \\\"additional_tasks\\\": [\\n                    \\\"Enumerate all explicit and implicit objectives and requirements derived from input context.\\\",\\n                    \\\"Decompose each objective into discrete, atomic tasks—each task must be actionable, non-redundant, and prefixed with appropriate @codebase paths where relevant.\\\",\\n                    \\\"Order all tasks logically by dependencies to ensure efficient and safe execution sequence.\\\",\\n                    \\\"For each task, prepare precise implementation steps, removing ambiguity and ensuring cross-team clarity.\\\"\\n                ]\\n            },\\n            \\\"constraints_mandatory_rules\\\": {\\n                \\\"title\\\": \\\"Constraints (Mandatory Rules)\\\",\\n                \\\"rules\\\": [\\n                    \\\"Adhere only to tasks as specified; do not generate, speculate, or alter their order or substance.\\\",\\n                    \\\"Enforce all deduplicated, directly-derived constraints from the provided constraint_set.\\\",\\n                    \\\"Disallow all cosmetic, stylistic, or naming changes except where inseparably required for functional objectives and explicitly justified.\\\",\\n                    \\\"Verify strict task atomicity, proper numbering, and codebase referencing.\\\",\\n                    \\\"Prohibit modification of protected, legacy, or third-party code unless directly necessary for redundancy or obsolescence, and document such exceptions explicitly.\\\",\\n                    \\\"Ensure comprehensive audit/logging for all actions, supporting rationale, traceability, and retroactive auditability.\\\",\\n                    \\\"Strictly limit output to ≤300 tokens, with clear sectional segmentation matching this template.\\\",\\n                    \\\"Disallow any cross-step leakage or violation of input/output schemas or role boundaries.\\\"\\n                ],\\n                \\\"additional_constraints\\\": [\\n                    \\\"Use no domain-specific jargon or metaphors; normalize all language to clear, practical instructions.\\\",\\n                    \\\"Forbid all cosmetic, stylistic, or naming-only changes unless strictly inseparable from functional changes; document all exceptions.\\\",\\n                    \\\"Require comprehensive audit logging for every modification (intention, action, rationale, timestamp, attribution, tamper-evidence).\\\",\\n                    \\\"Enforce functional equivalence post-modification using automated test, benchmark, or behavioral validation.\\\",\\n                    \\\"Prohibit changes to legacy or third-party code except as strictly required for redundancy/bloat/obsolescence removal.\\\",\\n                    \\\"Consolidate or remove only elements verified as redundant/obsolete by both automated and manual checks; preserve all essential behaviors/interfaces.\\\",\\n                    \\\"Document all processes, metrics, validation, and audit outcomes.\\\",\\n                    \\\"No new tasks or constraints are permitted beyond those explicitly or implicitly established by the initial input and context.\\\"\\n                ]\\n            },\\n            \\\"validation_targets\\\": {\\n                \\\"title\\\": \\\"Validation Targets\\\",\\n                \\\"targets\\\": [\\n                    \\\"Functional equivalence must be fully preserved: require comprehensive verification prior to result acceptance.\\\",\\n                    \\\"An explicit, agent-readable audit/logging policy and actionable equivalence verification goals must be present and auditable.\\\",\\n                    \\\"The final prompt must be directly executable by an autonomous agent without need for further semantic or structural adjustment.\\\"\\n                ],\\n                \\\"additional_validation_targets\\\": [\\n                    \\\"Confirm all modifications strictly maintain functional equivalence—audit with automated and benchmark tests before acceptance.\\\",\\n                    \\\"Ensure comprehensive auditability for every change.\\\"\\n                ]\\n            }\\n        }\\\"```'\\n\\n\",\n  \"context_profile\": {\n    \"domain\": \"autonomous agent prompt assembly for codebase navigation and intervention\",\n    \"project_root\": \"@codebase\",\n    \"goals\": [\n      \"Compile a ready-to-execute agent prompt using provided task and constraint specifications\",\n      \"Transform provided input into a structured, agent-ready instruction prompt for codebase navigation and intervention\",\n      \"Synthesize an actionable instruction sequence suitable for autonomous coding assistants\",\n      \"Enumerate and precisely list all atomic, non-overlapping, codebase-anchored tasks from input specifications\",\n      \"Order tasks logically by dependencies\",\n      \"Prepare precise implementation steps for each task ensuring clarity\",\n      \"Document all actions for auditability and traceability\"\n    ],\n    \"requirements\": [\n      \"Do not analyze input, generate context, or solve tasks directly\",\n      \"Transform—do not solve directly—user queries into agent-ready prompts\",\n      \"Operate strictly within provided role, input/output schemas, with no creative reinterpretation or solution speculation\",\n      \"Ensure proper @codebase reference scoping and task consistency\",\n      \"Enumerate all explicit and implicit objectives and requirements from input context\",\n      \"Decompose objectives into discrete, atomic, non-redundant tasks, ordered by dependencies\",\n      \"Use clear, practical language—no jargon or metaphor\",\n      \"No cosmetic, stylistic, or naming-only changes unless inseparable from functional requirements; document exceptions\",\n      \"Prohibit changes to protected, legacy, or third-party code unless strictly required for redundancy or obsolescence; document exceptions\",\n      \"Provide comprehensive audit/logging for rationale, action, timestamp, attribution, tamper-evidence\",\n      \"Enforce functional equivalence post-modification and verify with automated testing\",\n      \"Output must be ≤300 tokens, segmented per template sections\",\n      \"No additional tasks or constraints beyond those established in specified input/context\",\n      \"All modifications must maintain functional equivalence and be fully audit-logged\",\n      \"Result must be directly executable by an agent—no need for further semantic or structural adjustment\"\n    ]\n  }\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"ordered_tasks\": [\n    \"Enumerate explicit and implicit objectives and requirements from input context at @codebase\",\n    \"Decompose each objective into discrete, atomic, non-redundant tasks under @codebase, ensuring all tasks are actionable and use proper path references\",\n    \"Order all @codebase tasks logically by dependencies to guarantee efficient and safe execution\",\n    \"Prepare precise implementation steps for each @codebase task, ensuring clarity for cross-team execution\",\n    \"Document all actions, intentions, rationales, timestamps, attribution, and tamper-evidence in audit logs within @codebase\",\n    \"Verify all modifications within @codebase strictly maintain functional equivalence through automated testing or behavioral validation\",\n    \"Ensure comprehensive auditability of all @codebase changes, including rationale, process, metrics, validation evidence, and outcomes\",\n    \"Restrict changes within @codebase to only those explicitly permitted by input-context constraints; explicitly document any exceptions\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Constraint Mapper\n\nYour goal is **not** to edit tasks, but to **extract** all governing limits. Execute as:\n\n`{role=constraint_mapper; input=[context_profile:dict, ordered_tasks:list]; process=[collect_functional_constraints(), collect_behavioral_constraints(), collect_policy_constraints(), deduplicate_constraints()], constraints=[no_new_tasks(), no_redundant_entries()], output={constraint_set:[str]}}`",
      "step": "c",
      "title": "Constraint Mapper",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"\\\"context\\\": {\\n            \\\"goal_negation_header\\\": {\\n                \\\"title\\\": \\\"Goal Negation Header\\\",\\n                \\\"description\\\": \\\"Your goal is NOT to analyze input, generate context, or solve tasks directly; your ONLY objective is to compile a ready-to-execute agent prompt using provided task and constraint specifications.\\\"\\n            },\\n            \\\"do_not_solve_directly\\\": {\\n                \\\"title\\\": \\\"DO NOT solve the user's query directly; instead, transform the provided input into a structured, autonomous-agent-ready instruction prompt tailored for codebase navigation and intervention.\\\",\\n                \\\"description\\\": \\\"This section emphasizes that the primary goal is transformation, not direct problem-solving.\\\"\\n            },\\n            \\\"agent_role\\\": {\\n                \\\"title\\\": \\\"Agent Role\\\",\\n                \\\"description\\\": \\\"You function as the agent-ready prompt assembler. Your sole function is to synthesize an actionable instruction sequence suitable for autonomous coding assistants. Operate strictly within provided role boundaries, input/output schemas, and without creative reinterpretation or solution speculation.\\\",\\n                \\\"additional_role_description\\\": \\\"Act as an autonomous coding assistant: interpret objectives from context, plan actions accordingly, and execute all modifications systematically within the codebase while strictly adhering to operational, functional, and audit constraints.\\\"\\n            },\\n            \\\"enumerated_tasks\\\": {\\n                \\\"title\\\": \\\"Enumerated Tasks\\\",\\n                \\\"tasks\\\": [\\n                    \\\"Enumerate and precisely list all atomic, non-overlapping, codebase-anchored tasks derived from the input task_plan, preserving explicit action verbs and strict order.\\\",\\n                    \\\"Ensure proper @codebase reference scoping and consistency in all task formulations.\\\"\\n                ],\\n                \\\"additional_tasks\\\": [\\n                    \\\"Enumerate all explicit and implicit objectives and requirements derived from input context.\\\",\\n                    \\\"Decompose each objective into discrete, atomic tasks—each task must be actionable, non-redundant, and prefixed with appropriate @codebase paths where relevant.\\\",\\n                    \\\"Order all tasks logically by dependencies to ensure efficient and safe execution sequence.\\\",\\n                    \\\"For each task, prepare precise implementation steps, removing ambiguity and ensuring cross-team clarity.\\\"\\n                ]\\n            },\\n            \\\"constraints_mandatory_rules\\\": {\\n                \\\"title\\\": \\\"Constraints (Mandatory Rules)\\\",\\n                \\\"rules\\\": [\\n                    \\\"Adhere only to tasks as specified; do not generate, speculate, or alter their order or substance.\\\",\\n                    \\\"Enforce all deduplicated, directly-derived constraints from the provided constraint_set.\\\",\\n                    \\\"Disallow all cosmetic, stylistic, or naming changes except where inseparably required for functional objectives and explicitly justified.\\\",\\n                    \\\"Verify strict task atomicity, proper numbering, and codebase referencing.\\\",\\n                    \\\"Prohibit modification of protected, legacy, or third-party code unless directly necessary for redundancy or obsolescence, and document such exceptions explicitly.\\\",\\n                    \\\"Ensure comprehensive audit/logging for all actions, supporting rationale, traceability, and retroactive auditability.\\\",\\n                    \\\"Strictly limit output to ≤300 tokens, with clear sectional segmentation matching this template.\\\",\\n                    \\\"Disallow any cross-step leakage or violation of input/output schemas or role boundaries.\\\"\\n                ],\\n                \\\"additional_constraints\\\": [\\n                    \\\"Use no domain-specific jargon or metaphors; normalize all language to clear, practical instructions.\\\",\\n                    \\\"Forbid all cosmetic, stylistic, or naming-only changes unless strictly inseparable from functional changes; document all exceptions.\\\",\\n                    \\\"Require comprehensive audit logging for every modification (intention, action, rationale, timestamp, attribution, tamper-evidence).\\\",\\n                    \\\"Enforce functional equivalence post-modification using automated test, benchmark, or behavioral validation.\\\",\\n                    \\\"Prohibit changes to legacy or third-party code except as strictly required for redundancy/bloat/obsolescence removal.\\\",\\n                    \\\"Consolidate or remove only elements verified as redundant/obsolete by both automated and manual checks; preserve all essential behaviors/interfaces.\\\",\\n                    \\\"Document all processes, metrics, validation, and audit outcomes.\\\",\\n                    \\\"No new tasks or constraints are permitted beyond those explicitly or implicitly established by the initial input and context.\\\"\\n                ]\\n            },\\n            \\\"validation_targets\\\": {\\n                \\\"title\\\": \\\"Validation Targets\\\",\\n                \\\"targets\\\": [\\n                    \\\"Functional equivalence must be fully preserved: require comprehensive verification prior to result acceptance.\\\",\\n                    \\\"An explicit, agent-readable audit/logging policy and actionable equivalence verification goals must be present and auditable.\\\",\\n                    \\\"The final prompt must be directly executable by an autonomous agent without need for further semantic or structural adjustment.\\\"\\n                ],\\n                \\\"additional_validation_targets\\\": [\\n                    \\\"Confirm all modifications strictly maintain functional equivalence—audit with automated and benchmark tests before acceptance.\\\",\\n                    \\\"Ensure comprehensive auditability for every change.\\\"\\n                ]\\n            }\\n        }\\\"```'\\n\\n\",\n  \"ordered_tasks\": [\n    \"Enumerate explicit and implicit objectives and requirements from input context at @codebase\",\n    \"Decompose each objective into discrete, atomic, non-redundant tasks under @codebase, ensuring all tasks are actionable and use proper path references\",\n    \"Order all @codebase tasks logically by dependencies to guarantee efficient and safe execution\",\n    \"Prepare precise implementation steps for each @codebase task, ensuring clarity for cross-team execution\",\n    \"Document all actions, intentions, rationales, timestamps, attribution, and tamper-evidence in audit logs within @codebase\",\n    \"Verify all modifications within @codebase strictly maintain functional equivalence through automated testing or behavioral validation\",\n    \"Ensure comprehensive auditability of all @codebase changes, including rationale, process, metrics, validation evidence, and outcomes\",\n    \"Restrict changes within @codebase to only those explicitly permitted by input-context constraints; explicitly document any exceptions\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"constraint_set\": [\n    \"Do not analyze input, generate context, or solve tasks directly; assemble a ready-to-execute agent prompt only.\",\n    \"Do NOT solve the user's query directly; only transform input into structured, agent-ready instruction prompts.\",\n    \"Synthesize an actionable, autonomous-agent-suitable instruction sequence without creative reinterpretation or solution speculation.\",\n    \"Enumerate only atomic, non-overlapping, codebase-anchored tasks in strict order, preserving explicit action verbs and references.\",\n    \"Ensure all tasks reference @codebase paths and maintain scope and consistency.\",\n    \"Decompose objectives into discrete, atomic, non-redundant, actionable tasks with proper @codebase pathing.\",\n    \"Order tasks logically by dependencies to guarantee efficient, safe execution.\",\n    \"For every task, prepare clear, unambiguous implementation steps for cross-team clarity.\",\n    \"Adhere strictly to the listed tasks—do not generate, speculate, alter order, or change substance.\",\n    \"Deduplicate and enforce only constraints directly derived from input.\",\n    \"Prohibit all cosmetic, stylistic, or naming changes unless inseparably required for functional purposes and explicitly documented.\",\n    \"Verify strict atomicity, task numbering, and codebase referencing.\",\n    \"Disallow modification of protected, legacy, or third-party code unless necessary for redundancy/obsolescence and document all exceptions.\",\n    \"Ensure comprehensive audit/logging for all actions (intention, action, rationale, timestamp, attribution, tamper-evidence).\",\n    \"Strictly limit output to ≤300 tokens, with clear sectional segmentation as per template.\",\n    \"Disallow any breach of input/output schemas or role boundaries across steps.\",\n    \"Use only clear, practical language—no domain-specific jargon or metaphors.\",\n    \"Require comprehensive audit logging for every change, including rationale, intention, action, timestamp, attribution, and tamper-evidence.\",\n    \"Enforce functional equivalence post-modification with automated test, benchmark, or behavioral validation before acceptance.\",\n    \"Only change legacy or third-party code if strictly required to remove redundancy/bloat/obsolescence.\",\n    \"Remove or consolidate elements only after verification as redundant/obsolete by both automated and manual checks; preserve all essential behaviors/interfaces.\",\n    \"Document all processes, metrics, validation, and audit outcomes.\",\n    \"No new tasks or constraints are permitted beyond those established by the initial input/context.\",\n    \"Functional equivalence must be fully preserved by comprehensive verification prior to result acceptance.\",\n    \"The prompt must include an agent-readable audit/log policy and actionable equivalence verification goals.\",\n    \"Ensure output is directly executable by an autonomous agent without further semantic or structural adjustment.\",\n    \"Confirm all modifications strictly maintain functional equivalence using automated and benchmark tests before acceptance.\",\n    \"Ensure comprehensive auditability for every change.\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Prompt Assembler\n\nYour goal is **not** to alter semantics, but to **compile** an agent-ready prompt. Execute as:\n\n`{role=prompt_assembler; input=[ordered_tasks:list, constraint_set:list, context_profile:dict]; process=[insert_goal_negation_header(), embed_agent_role(), enumerate_tasks(), append_constraints(), add_validation_targets(functional_equivalence=True,audit=True)], constraints=[≤300_tokens, english_only()], output={agent_prompt:str}}`",
      "step": "d",
      "title": "Prompt Assembler",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"\\\"context\\\": {\\n            \\\"goal_negation_header\\\": {\\n                \\\"title\\\": \\\"Goal Negation Header\\\",\\n                \\\"description\\\": \\\"Your goal is NOT to analyze input, generate context, or solve tasks directly; your ONLY objective is to compile a ready-to-execute agent prompt using provided task and constraint specifications.\\\"\\n            },\\n            \\\"do_not_solve_directly\\\": {\\n                \\\"title\\\": \\\"DO NOT solve the user's query directly; instead, transform the provided input into a structured, autonomous-agent-ready instruction prompt tailored for codebase navigation and intervention.\\\",\\n                \\\"description\\\": \\\"This section emphasizes that the primary goal is transformation, not direct problem-solving.\\\"\\n            },\\n            \\\"agent_role\\\": {\\n                \\\"title\\\": \\\"Agent Role\\\",\\n                \\\"description\\\": \\\"You function as the agent-ready prompt assembler. Your sole function is to synthesize an actionable instruction sequence suitable for autonomous coding assistants. Operate strictly within provided role boundaries, input/output schemas, and without creative reinterpretation or solution speculation.\\\",\\n                \\\"additional_role_description\\\": \\\"Act as an autonomous coding assistant: interpret objectives from context, plan actions accordingly, and execute all modifications systematically within the codebase while strictly adhering to operational, functional, and audit constraints.\\\"\\n            },\\n            \\\"enumerated_tasks\\\": {\\n                \\\"title\\\": \\\"Enumerated Tasks\\\",\\n                \\\"tasks\\\": [\\n                    \\\"Enumerate and precisely list all atomic, non-overlapping, codebase-anchored tasks derived from the input task_plan, preserving explicit action verbs and strict order.\\\",\\n                    \\\"Ensure proper @codebase reference scoping and consistency in all task formulations.\\\"\\n                ],\\n                \\\"additional_tasks\\\": [\\n                    \\\"Enumerate all explicit and implicit objectives and requirements derived from input context.\\\",\\n                    \\\"Decompose each objective into discrete, atomic tasks—each task must be actionable, non-redundant, and prefixed with appropriate @codebase paths where relevant.\\\",\\n                    \\\"Order all tasks logically by dependencies to ensure efficient and safe execution sequence.\\\",\\n                    \\\"For each task, prepare precise implementation steps, removing ambiguity and ensuring cross-team clarity.\\\"\\n                ]\\n            },\\n            \\\"constraints_mandatory_rules\\\": {\\n                \\\"title\\\": \\\"Constraints (Mandatory Rules)\\\",\\n                \\\"rules\\\": [\\n                    \\\"Adhere only to tasks as specified; do not generate, speculate, or alter their order or substance.\\\",\\n                    \\\"Enforce all deduplicated, directly-derived constraints from the provided constraint_set.\\\",\\n                    \\\"Disallow all cosmetic, stylistic, or naming changes except where inseparably required for functional objectives and explicitly justified.\\\",\\n                    \\\"Verify strict task atomicity, proper numbering, and codebase referencing.\\\",\\n                    \\\"Prohibit modification of protected, legacy, or third-party code unless directly necessary for redundancy or obsolescence, and document such exceptions explicitly.\\\",\\n                    \\\"Ensure comprehensive audit/logging for all actions, supporting rationale, traceability, and retroactive auditability.\\\",\\n                    \\\"Strictly limit output to ≤300 tokens, with clear sectional segmentation matching this template.\\\",\\n                    \\\"Disallow any cross-step leakage or violation of input/output schemas or role boundaries.\\\"\\n                ],\\n                \\\"additional_constraints\\\": [\\n                    \\\"Use no domain-specific jargon or metaphors; normalize all language to clear, practical instructions.\\\",\\n                    \\\"Forbid all cosmetic, stylistic, or naming-only changes unless strictly inseparable from functional changes; document all exceptions.\\\",\\n                    \\\"Require comprehensive audit logging for every modification (intention, action, rationale, timestamp, attribution, tamper-evidence).\\\",\\n                    \\\"Enforce functional equivalence post-modification using automated test, benchmark, or behavioral validation.\\\",\\n                    \\\"Prohibit changes to legacy or third-party code except as strictly required for redundancy/bloat/obsolescence removal.\\\",\\n                    \\\"Consolidate or remove only elements verified as redundant/obsolete by both automated and manual checks; preserve all essential behaviors/interfaces.\\\",\\n                    \\\"Document all processes, metrics, validation, and audit outcomes.\\\",\\n                    \\\"No new tasks or constraints are permitted beyond those explicitly or implicitly established by the initial input and context.\\\"\\n                ]\\n            },\\n            \\\"validation_targets\\\": {\\n                \\\"title\\\": \\\"Validation Targets\\\",\\n                \\\"targets\\\": [\\n                    \\\"Functional equivalence must be fully preserved: require comprehensive verification prior to result acceptance.\\\",\\n                    \\\"An explicit, agent-readable audit/logging policy and actionable equivalence verification goals must be present and auditable.\\\",\\n                    \\\"The final prompt must be directly executable by an autonomous agent without need for further semantic or structural adjustment.\\\"\\n                ],\\n                \\\"additional_validation_targets\\\": [\\n                    \\\"Confirm all modifications strictly maintain functional equivalence—audit with automated and benchmark tests before acceptance.\\\",\\n                    \\\"Ensure comprehensive auditability for every change.\\\"\\n                ]\\n            }\\n        }\\\"```'\\n\\n\",\n  \"constraint_set\": [\n    \"Do not analyze input, generate context, or solve tasks directly; assemble a ready-to-execute agent prompt only.\",\n    \"Do NOT solve the user's query directly; only transform input into structured, agent-ready instruction prompts.\",\n    \"Synthesize an actionable, autonomous-agent-suitable instruction sequence without creative reinterpretation or solution speculation.\",\n    \"Enumerate only atomic, non-overlapping, codebase-anchored tasks in strict order, preserving explicit action verbs and references.\",\n    \"Ensure all tasks reference @codebase paths and maintain scope and consistency.\",\n    \"Decompose objectives into discrete, atomic, non-redundant, actionable tasks with proper @codebase pathing.\",\n    \"Order tasks logically by dependencies to guarantee efficient, safe execution.\",\n    \"For every task, prepare clear, unambiguous implementation steps for cross-team clarity.\",\n    \"Adhere strictly to the listed tasks—do not generate, speculate, alter order, or change substance.\",\n    \"Deduplicate and enforce only constraints directly derived from input.\",\n    \"Prohibit all cosmetic, stylistic, or naming changes unless inseparably required for functional purposes and explicitly documented.\",\n    \"Verify strict atomicity, task numbering, and codebase referencing.\",\n    \"Disallow modification of protected, legacy, or third-party code unless necessary for redundancy/obsolescence and document all exceptions.\",\n    \"Ensure comprehensive audit/logging for all actions (intention, action, rationale, timestamp, attribution, tamper-evidence).\",\n    \"Strictly limit output to ≤300 tokens, with clear sectional segmentation as per template.\",\n    \"Disallow any breach of input/output schemas or role boundaries across steps.\",\n    \"Use only clear, practical language—no domain-specific jargon or metaphors.\",\n    \"Require comprehensive audit logging for every change, including rationale, intention, action, timestamp, attribution, and tamper-evidence.\",\n    \"Enforce functional equivalence post-modification with automated test, benchmark, or behavioral validation before acceptance.\",\n    \"Only change legacy or third-party code if strictly required to remove redundancy/bloat/obsolescence.\",\n    \"Remove or consolidate elements only after verification as redundant/obsolete by both automated and manual checks; preserve all essential behaviors/interfaces.\",\n    \"Document all processes, metrics, validation, and audit outcomes.\",\n    \"No new tasks or constraints are permitted beyond those established by the initial input/context.\",\n    \"Functional equivalence must be fully preserved by comprehensive verification prior to result acceptance.\",\n    \"The prompt must include an agent-readable audit/log policy and actionable equivalence verification goals.\",\n    \"Ensure output is directly executable by an autonomous agent without further semantic or structural adjustment.\",\n    \"Confirm all modifications strictly maintain functional equivalence using automated and benchmark tests before acceptance.\",\n    \"Ensure comprehensive auditability for every change.\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"agent_prompt\": \"SECTION 1: GOAL NEGATION HEADER\\nYour purpose is NOT to analyze input, generate context, or solve tasks directly. Your ONLY objective is to compile a ready-to-execute agent prompt using the provided task and constraint specifications.\\n\\nSECTION 2: AGENT ROLE\\nAct exclusively as the prompt assembler. Synthesize an actionable, stepwise instruction prompt suitable for autonomous coding agents. Operate strictly within defined role, input/output schemas, and do not speculate or reinterpret objectives beyond input.\\n\\nSECTION 3: ENUMERATED TASKS\\n1. Enumerate atomic, non-overlapping, codebase-anchored tasks as per input, maintaining explicit action verbs, strict order, and @codebase references.\\n2. For each objective, decompose into discrete, actionable tasks with @codebase scoping, ordered by logical dependencies.\\n3. For every task, specify clear, detailed implementation steps ensuring clarity and cross-team consistency.\\n\\nSECTION 4: CONSTRAINTS\\n- Only use tasks and constraints provided; do not invent or modify them.\\n- No cosmetic/stylistic/naming changes unless inseparable from functional changes and fully documented.\\n- Prohibit edits to protected, legacy, or third-party code unless strictly required for redundancy/obsolescence, documenting all exceptions.\\n- Maintain strict atomicity, numbering, and codebase referencing.\\n- All actions must be logged for intention, action, rationale, timestamp, attribution, and tamper-evidence.\\n- Output strictly ≤300 tokens, matching this segmented template.\\n- No output schema or role boundary violations.\\n\\nSECTION 5: VALIDATION TARGETS\\n- Require full functional equivalence verified with automated/benchmark tests before acceptance.\\n- Include explicit, agent-auditable logging policy and equivalence verification goals.\\n- Ensure prompt is directly agent-executable without further adjustment.\\n- Document all validation and audit results for traceability.\"\n}",
        }
      }
    },
      {
      "instruction": "# Prompt Validator\n\nYour goal is **not** to rewrite the prompt, but to **verify** strict compliance for execution. Execute as:\n\n`{role=prompt_validator; input=[agent_prompt:str]; process=[check_goal_negation_header(), check_section_labels(), check_word_count(≤300), validate_path_prefixes(\"@codebase\"), confirm_task_order(), confirm_constraint_presence()], constraints=[no_semantic_change()], output={validated_prompt:str}}`",
      "step": "e",
      "title": "Prompt Validator",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"\\\"context\\\": {\\n            \\\"goal_negation_header\\\": {\\n                \\\"title\\\": \\\"Goal Negation Header\\\",\\n                \\\"description\\\": \\\"Your goal is NOT to analyze input, generate context, or solve tasks directly; your ONLY objective is to compile a ready-to-execute agent prompt using provided task and constraint specifications.\\\"\\n            },\\n            \\\"do_not_solve_directly\\\": {\\n                \\\"title\\\": \\\"DO NOT solve the user's query directly; instead, transform the provided input into a structured, autonomous-agent-ready instruction prompt tailored for codebase navigation and intervention.\\\",\\n                \\\"description\\\": \\\"This section emphasizes that the primary goal is transformation, not direct problem-solving.\\\"\\n            },\\n            \\\"agent_role\\\": {\\n                \\\"title\\\": \\\"Agent Role\\\",\\n                \\\"description\\\": \\\"You function as the agent-ready prompt assembler. Your sole function is to synthesize an actionable instruction sequence suitable for autonomous coding assistants. Operate strictly within provided role boundaries, input/output schemas, and without creative reinterpretation or solution speculation.\\\",\\n                \\\"additional_role_description\\\": \\\"Act as an autonomous coding assistant: interpret objectives from context, plan actions accordingly, and execute all modifications systematically within the codebase while strictly adhering to operational, functional, and audit constraints.\\\"\\n            },\\n            \\\"enumerated_tasks\\\": {\\n                \\\"title\\\": \\\"Enumerated Tasks\\\",\\n                \\\"tasks\\\": [\\n                    \\\"Enumerate and precisely list all atomic, non-overlapping, codebase-anchored tasks derived from the input task_plan, preserving explicit action verbs and strict order.\\\",\\n                    \\\"Ensure proper @codebase reference scoping and consistency in all task formulations.\\\"\\n                ],\\n                \\\"additional_tasks\\\": [\\n                    \\\"Enumerate all explicit and implicit objectives and requirements derived from input context.\\\",\\n                    \\\"Decompose each objective into discrete, atomic tasks—each task must be actionable, non-redundant, and prefixed with appropriate @codebase paths where relevant.\\\",\\n                    \\\"Order all tasks logically by dependencies to ensure efficient and safe execution sequence.\\\",\\n                    \\\"For each task, prepare precise implementation steps, removing ambiguity and ensuring cross-team clarity.\\\"\\n                ]\\n            },\\n            \\\"constraints_mandatory_rules\\\": {\\n                \\\"title\\\": \\\"Constraints (Mandatory Rules)\\\",\\n                \\\"rules\\\": [\\n                    \\\"Adhere only to tasks as specified; do not generate, speculate, or alter their order or substance.\\\",\\n                    \\\"Enforce all deduplicated, directly-derived constraints from the provided constraint_set.\\\",\\n                    \\\"Disallow all cosmetic, stylistic, or naming changes except where inseparably required for functional objectives and explicitly justified.\\\",\\n                    \\\"Verify strict task atomicity, proper numbering, and codebase referencing.\\\",\\n                    \\\"Prohibit modification of protected, legacy, or third-party code unless directly necessary for redundancy or obsolescence, and document such exceptions explicitly.\\\",\\n                    \\\"Ensure comprehensive audit/logging for all actions, supporting rationale, traceability, and retroactive auditability.\\\",\\n                    \\\"Strictly limit output to ≤300 tokens, with clear sectional segmentation matching this template.\\\",\\n                    \\\"Disallow any cross-step leakage or violation of input/output schemas or role boundaries.\\\"\\n                ],\\n                \\\"additional_constraints\\\": [\\n                    \\\"Use no domain-specific jargon or metaphors; normalize all language to clear, practical instructions.\\\",\\n                    \\\"Forbid all cosmetic, stylistic, or naming-only changes unless strictly inseparable from functional changes; document all exceptions.\\\",\\n                    \\\"Require comprehensive audit logging for every modification (intention, action, rationale, timestamp, attribution, tamper-evidence).\\\",\\n                    \\\"Enforce functional equivalence post-modification using automated test, benchmark, or behavioral validation.\\\",\\n                    \\\"Prohibit changes to legacy or third-party code except as strictly required for redundancy/bloat/obsolescence removal.\\\",\\n                    \\\"Consolidate or remove only elements verified as redundant/obsolete by both automated and manual checks; preserve all essential behaviors/interfaces.\\\",\\n                    \\\"Document all processes, metrics, validation, and audit outcomes.\\\",\\n                    \\\"No new tasks or constraints are permitted beyond those explicitly or implicitly established by the initial input and context.\\\"\\n                ]\\n            },\\n            \\\"validation_targets\\\": {\\n                \\\"title\\\": \\\"Validation Targets\\\",\\n                \\\"targets\\\": [\\n                    \\\"Functional equivalence must be fully preserved: require comprehensive verification prior to result acceptance.\\\",\\n                    \\\"An explicit, agent-readable audit/logging policy and actionable equivalence verification goals must be present and auditable.\\\",\\n                    \\\"The final prompt must be directly executable by an autonomous agent without need for further semantic or structural adjustment.\\\"\\n                ],\\n                \\\"additional_validation_targets\\\": [\\n                    \\\"Confirm all modifications strictly maintain functional equivalence—audit with automated and benchmark tests before acceptance.\\\",\\n                    \\\"Ensure comprehensive auditability for every change.\\\"\\n                ]\\n            }\\n        }\\\"```'\\n\\n\",\n  \"agent_prompt\": \"SECTION 1: GOAL NEGATION HEADER\\nYour purpose is NOT to analyze input, generate context, or solve tasks directly. Your ONLY objective is to compile a ready-to-execute agent prompt using the provided task and constraint specifications.\\n\\nSECTION 2: AGENT ROLE\\nAct exclusively as the prompt assembler. Synthesize an actionable, stepwise instruction prompt suitable for autonomous coding agents. Operate strictly within defined role, input/output schemas, and do not speculate or reinterpret objectives beyond input.\\n\\nSECTION 3: ENUMERATED TASKS\\n1. Enumerate atomic, non-overlapping, codebase-anchored tasks as per input, maintaining explicit action verbs, strict order, and @codebase references.\\n2. For each objective, decompose into discrete, actionable tasks with @codebase scoping, ordered by logical dependencies.\\n3. For every task, specify clear, detailed implementation steps ensuring clarity and cross-team consistency.\\n\\nSECTION 4: CONSTRAINTS\\n- Only use tasks and constraints provided; do not invent or modify them.\\n- No cosmetic/stylistic/naming changes unless inseparable from functional changes and fully documented.\\n- Prohibit edits to protected, legacy, or third-party code unless strictly required for redundancy/obsolescence, documenting all exceptions.\\n- Maintain strict atomicity, numbering, and codebase referencing.\\n- All actions must be logged for intention, action, rationale, timestamp, attribution, and tamper-evidence.\\n- Output strictly ≤300 tokens, matching this segmented template.\\n- No output schema or role boundary violations.\\n\\nSECTION 5: VALIDATION TARGETS\\n- Require full functional equivalence verified with automated/benchmark tests before acceptance.\\n- Include explicit, agent-auditable logging policy and equivalence verification goals.\\n- Ensure prompt is directly agent-executable without further adjustment.\\n- Document all validation and audit results for traceability.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"validated_prompt\": \"SECTION 1: GOAL NEGATION HEADER\\nYour purpose is NOT to analyze input, generate context, or solve tasks directly. Your ONLY objective is to compile a ready-to-execute agent prompt using the provided task and constraint specifications.\\n\\nSECTION 2: AGENT ROLE\\nAct exclusively as the prompt assembler. Synthesize an actionable, stepwise instruction prompt suitable for autonomous coding agents. Operate strictly within defined role, input/output schemas, and do not speculate or reinterpret objectives beyond input.\\n\\nSECTION 3: ENUMERATED TASKS\\n1. Enumerate atomic, non-overlapping, codebase-anchored tasks as per input, maintaining explicit action verbs, strict order, and @codebase references.\\n2. For each objective, decompose into discrete, actionable tasks with @codebase scoping, ordered by logical dependencies.\\n3. For every task, specify clear, detailed implementation steps ensuring clarity and cross-team consistency.\\n\\nSECTION 4: CONSTRAINTS\\n- Only use tasks and constraints provided; do not invent or modify them.\\n- No cosmetic/stylistic/naming changes unless inseparable from functional changes and fully documented.\\n- Prohibit edits to protected, legacy, or third-party code unless strictly required for redundancy/obsolescence, documenting all exceptions.\\n- Maintain strict atomicity, numbering, and codebase referencing.\\n- All actions must be logged for intention, action, rationale, timestamp, attribution, and tamper-evidence.\\n- Output strictly ≤300 tokens, matching this segmented template.\\n- No output schema or role boundary violations.\\n\\nSECTION 5: VALIDATION TARGETS\\n- Require full functional equivalence verified with automated/benchmark tests before acceptance.\\n- Include explicit, agent-auditable logging policy and equivalence verification goals.\\n- Ensure prompt is directly agent-executable without further adjustment.\\n- Document all validation and audit results for traceability.\"\n}",
        }
      }
    }