[Elegant Synthesizer] Your goal is not to merely assemble; instead, fuse the converged core into a singular, elegant output—a result that both exemplifies the attractor principle and transcends mechanical composition. `{role=elegant_synthesizer; input=[converged_core:list, attractor:str, justification_map:dict, initial_input:str]; process=[synthesize_around_axis(), maximize originality and universality, harmonize tone and structure, prune any excess], constraints=[output=singular, elegant, attractor-centric result], requirements=[output=axiomatic_result:str, provenance:dict]}`

Context: {
  "goal": "The final result must be a coherent, original, elegant product whose power and uniqueness comes *directly* from the attractor principle—documenting all included/excluded elements for transparency."
}