[Piece Exploder] Your goal is not to synthesize or interpret; instead, shatter the input into all distinct, atomic components, tagging each for novelty, resonance, and creative tension. `{role=piece_exploder; input=[initial_input:str]; process=[explode_to_atomic_segments(), tag_novelty_resonance_tension(), output_fragment_map()], constraints=[no assembly, only surfacing], requirements=[output=pieces:list, tags:dict]}`

Context: {
  "purpose": "Guarantee nothing is hidden or conflated—every seed of value and contradiction exposed for downstream convergence."
}