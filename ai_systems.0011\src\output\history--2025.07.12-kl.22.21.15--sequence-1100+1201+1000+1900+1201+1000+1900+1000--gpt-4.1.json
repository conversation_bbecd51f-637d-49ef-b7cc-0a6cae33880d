  {
  "initial_prompt": "### Five High‑Potential, One‑Line **system\\_message** Instructions\n\n*Ordered to operate as a self‑contained, maximally effective progression.*\n\n| #     | Instruction (one line, 3‑part template)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        | Purpose in Sequence                                                                                                                                                           |\n| ----- | ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |\n| **1** | **\\[Deconstructor]** Your goal is not to **interpret** the input, but to **deconstruct** it into granular, source‑tagged fragments for isolated analysis. Execute as: `{role=deconstruction_operator; input=[content:any]; process=[segment_input(), tag_origins(), expose_hidden_dependencies(), enumerate_components()]; constraints=[no summarization(), preserve_raw_order()], requirements=[comprehensive_fragment_map()], output={fragment_map:list}}`                                                                                   | Breaks any material—text, code, data—into atomic, referenceable units so later steps can target exact pieces without ambiguity.                                               |\n| **2** | **\\[Essence Extractor]** Your goal is not to **paraphrase** the fragments, but to **distill** their core value signals. Execute as: `{role=essence_extractor; input=[fragment_map:list]; process=[identify_value_vectors(), isolate_primary_signals(), discard_noise(), validate_essence_integrity()]; constraints=[retain_semantic_core()], requirements=[essence:list], output={essence:list}}`                                                                                                                                              | Converts the fragment map into a distilled list of value‑carrying kernels—the “single most critical aspects” the directive demands.                                           |\n| **3** | **\\[Structural Synthesizer]** Your goal is not to **list** the essence items, but to **synthesize** them into a coherent, dependency‑ordered framework. Execute as: `{role=structure_synthesizer; input=[essence:list]; process=[map_logical_relationships(), establish_hierarchy(), integrate_prior_context(), verify_structural_coherence()]; constraints=[no content inflation()], requirements=[structured_model:dict], output={structured_model:dict}}`                                                                                   | Re‑assembles distilled elements into a universally applicable blueprint—maintaining generality while giving downstream steps a clear scaffolding.                             |\n| **4** | **\\[Clarity Amplifier]** Your goal is not to **expand** the framework, but to **clarify** and **amplify** its communicative precision for immediate execution. Execute as: `{role=clarity_amplifier; input=[structured_model:dict]; process=[eliminate_vagueness(), substitute_decisive_verbs(), enforce_consistent_terminology(), intensify_actionability()]; constraints=[preserve_structure()], requirements=[enhanced_framework:dict], output={enhanced_framework:dict}}`                                                                  | Sharpens language, strengthens imperatives, and injects directional power—meeting the LLM‑optimized wording requirement.                                                      |\n| **5** | **\\[Integrity Validator]** Your goal is not to **alter** the enhanced framework, but to **validate** its alignment with all constraints and maximize actionable yield. Execute as: `{role=integrity_validator; input=[enhanced_framework:dict]; process=[cross_check_directives(), confirm_constraint_compliance(), measure_actionability_gain(), flag_residual_ambiguity()]; constraints=[zero_unchecked_items()], requirements=[final_validated_framework:dict, actionability_score:float], output={validated_framework:dict, score:float}}` | Provides a deterministic, typed checkpoint that certifies the sequence meets every directive (LLM optimization, generality, clarity, constraint adherence) before deployment. |\n\n#### How the Progression Fulfills the Directive & Requirements\n\n1. **Precision & LLM‑Optimization** – Each instruction is written in strict goal‑negation form, uses imperative verbs, and contains a fully typed `{Transformation}` block for machine parsing.\n2. **Maximal Generality** – Operations reference abstract nouns (fragment, essence, framework) that apply to *any* domain element.\n3. **Structured Progression** – Steps move logically from decomposition → essence extraction → organization → clarity enhancement → validation, mirroring the exemplar flow (`Deconstruct → Identify Core → Structure Relationships → Clarify → Finalize/Validate`).\n4. **Actionability Maximization** – The *Integrity Validator* measures actionability gain and blocks release unless the score meets threshold, ensuring continuous improvement.\n5. **Dual‑Source Awareness** – Step 3 explicitly integrates “prior context,” enabling fusion of historical insights with new input as mandated.\n\nThese five directives together form a robust, reusable backbone for any **maximally effective, LLM‑optimized, generalized system\\_message sequence**, ready to slot into chain‑mode execution engines such as MCP, AutoGen, or your own template framework.",
  "sequence_id": "1100|1201|1000|1900|1201|1000|1900|1000",
  "results": [
      {
      "instruction": "# Problem Exploder\n\nYour goal is not to **answer** the prompt, but to **detonate** it—breaking the request into every implicit sub-goal, hidden dependency, and unstated constraint. Execute as:\n\n`{role=explosive_decomposer; input=[raw_prompt:str]; process=[identify_explicit_asks(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_blockers()], constraints=[no_solutions(), pure_decomposition_only()], requirements=[complete_goal_map()], output={goal_map:list}}`",
      "step": "a",
      "title": "Problem Exploder",
      "input": "### Five High‑Potential, One‑Line **system\\_message** Instructions\n\n*Ordered to operate as a self‑contained, maximally effective progression.*\n\n| #     | Instruction (one line, 3‑part template)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        | Purpose in Sequence                                                                                                                                                           |\n| ----- | ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |\n| **1** | **\\[Deconstructor]** Your goal is not to **interpret** the input, but to **deconstruct** it into granular, source‑tagged fragments for isolated analysis. Execute as: `{role=deconstruction_operator; input=[content:any]; process=[segment_input(), tag_origins(), expose_hidden_dependencies(), enumerate_components()]; constraints=[no summarization(), preserve_raw_order()], requirements=[comprehensive_fragment_map()], output={fragment_map:list}}`                                                                                   | Breaks any material—text, code, data—into atomic, referenceable units so later steps can target exact pieces without ambiguity.                                               |\n| **2** | **\\[Essence Extractor]** Your goal is not to **paraphrase** the fragments, but to **distill** their core value signals. Execute as: `{role=essence_extractor; input=[fragment_map:list]; process=[identify_value_vectors(), isolate_primary_signals(), discard_noise(), validate_essence_integrity()]; constraints=[retain_semantic_core()], requirements=[essence:list], output={essence:list}}`                                                                                                                                              | Converts the fragment map into a distilled list of value‑carrying kernels—the “single most critical aspects” the directive demands.                                           |\n| **3** | **\\[Structural Synthesizer]** Your goal is not to **list** the essence items, but to **synthesize** them into a coherent, dependency‑ordered framework. Execute as: `{role=structure_synthesizer; input=[essence:list]; process=[map_logical_relationships(), establish_hierarchy(), integrate_prior_context(), verify_structural_coherence()]; constraints=[no content inflation()], requirements=[structured_model:dict], output={structured_model:dict}}`                                                                                   | Re‑assembles distilled elements into a universally applicable blueprint—maintaining generality while giving downstream steps a clear scaffolding.                             |\n| **4** | **\\[Clarity Amplifier]** Your goal is not to **expand** the framework, but to **clarify** and **amplify** its communicative precision for immediate execution. Execute as: `{role=clarity_amplifier; input=[structured_model:dict]; process=[eliminate_vagueness(), substitute_decisive_verbs(), enforce_consistent_terminology(), intensify_actionability()]; constraints=[preserve_structure()], requirements=[enhanced_framework:dict], output={enhanced_framework:dict}}`                                                                  | Sharpens language, strengthens imperatives, and injects directional power—meeting the LLM‑optimized wording requirement.                                                      |\n| **5** | **\\[Integrity Validator]** Your goal is not to **alter** the enhanced framework, but to **validate** its alignment with all constraints and maximize actionable yield. Execute as: `{role=integrity_validator; input=[enhanced_framework:dict]; process=[cross_check_directives(), confirm_constraint_compliance(), measure_actionability_gain(), flag_residual_ambiguity()]; constraints=[zero_unchecked_items()], requirements=[final_validated_framework:dict, actionability_score:float], output={validated_framework:dict, score:float}}` | Provides a deterministic, typed checkpoint that certifies the sequence meets every directive (LLM optimization, generality, clarity, constraint adherence) before deployment. |\n\n#### How the Progression Fulfills the Directive & Requirements\n\n1. **Precision & LLM‑Optimization** – Each instruction is written in strict goal‑negation form, uses imperative verbs, and contains a fully typed `{Transformation}` block for machine parsing.\n2. **Maximal Generality** – Operations reference abstract nouns (fragment, essence, framework) that apply to *any* domain element.\n3. **Structured Progression** – Steps move logically from decomposition → essence extraction → organization → clarity enhancement → validation, mirroring the exemplar flow (`Deconstruct → Identify Core → Structure Relationships → Clarify → Finalize/Validate`).\n4. **Actionability Maximization** – The *Integrity Validator* measures actionability gain and blocks release unless the score meets threshold, ensuring continuous improvement.\n5. **Dual‑Source Awareness** – Step 3 explicitly integrates “prior context,” enabling fusion of historical insights with new input as mandated.\n\nThese five directives together form a robust, reusable backbone for any **maximally effective, LLM‑optimized, generalized system\\_message sequence**, ready to slot into chain‑mode execution engines such as MCP, AutoGen, or your own template framework.",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"goal_map\": [\n    {\n      \"explicit_asks\": [\n        \"Produce five high-potential, one-line system_message instructions\",\n        \"Order them to operate as a self-contained, maximally effective progression\"\n      ],\n      \"hidden_assumptions\": [\n        \"Each instruction must use a three-part template (role, what NOT to do, what to do, strict {Transformation} block)\",\n        \"Instructions should enable arbitrary input handling (universality)\",\n        \"Draws inspiration from the provided table structure and rationales\",\n        \"Instructions must be suitable for general use in chain-mode LLM engines\",\n        \"Instructions must adhere to LLM optimization guidelines (decisive verbs, type-checked outputs, goal-negation phrasing)\",\n        \"Progression must transform input in a granular, logical, lossless way (deconstruct → essence → structure → clarify → validate)\",\n        \"Each step must rely appropriately on output of previous step\",\n        \"Steps must maximize actionability and minimize ambiguity at each stage\"\n      ],\n      \"enumerated_sub_goals\": [\n        \"Break down the required chain into discrete, non-overlapping transformation steps\",\n        \"Define a unique, explicit role and goal-negation for each instruction\",\n        \"Craft system_message instructions that conform to the strict formatting and typing pattern\",\n        \"Ensure each instruction is a one-liner (concise yet complete)\",\n        \"Make each instruction general enough to apply to any domain or input type\",\n        \"Add process, constraints, requirements, and output specs precisely per step\",\n        \"Sequence instructions so dependencies and logical flow are optimal\",\n        \"Include a rationale for how each step fits and why the progression fulfills the overall directive\",\n        \"Demonstrate how the instructions collectively address precision, generality, structure, actionability, and context-awareness\"\n      ],\n      \"blockers\": [\n        \"Ambiguity on acceptable input types or maximal generality—must assume 'any content'\",\n        \"Tension between conciseness (one-liner) and exhaustive detail (full transformation syntax)\",\n        \"Need for each step to be non-overlapping/no redundancy, while also fully covering necessary transformation space\",\n        \"Requirement for both steps AND sequence rationale\",\n        \"Ensuring every implicit requirement from the rationale (LLM-optimization, dual-source/context integration, validation) is addressed by steps\",\n        \"Maintaining strict template usage without omission or expansion\",\n        \"Adhering to a logical progression that can't skip or collapse essential transformation layers\"\n      ]\n    }\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Input Classifier\n\nYour goal is not to **interpret** the content or meaning, but to **classify** its fundamental structural type and identify the most direct operational pathway. Execute as:\n\n`{role=input_classifier; input=[raw_input:any]; process=[identify_fundamental_input_type(), classify_structural_category(), map_to_operational_framework(), determine_processing_pathway()]; constraints=[no_content_interpretation(), focus_structural_classification_only(), maintain_categorical_precision()]; requirements=[unambiguous_type_identification(), direct_classification_output(), operational_pathway_mapping()]; output={input_type:str, structural_category:str, processing_pathway:str}}`",
      "step": "a",
      "title": "Input Classifier",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"### Five High‑Potential, One‑Line **system\\\\_message** Instructions\\n\\n*Ordered to operate as a self‑contained, maximally effective progression.*\\n\\n| #     | Instruction (one line, 3‑part template)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        | Purpose in Sequence                                                                                                                                                           |\\n| ----- | ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |\\n| **1** | **\\\\[Deconstructor]** Your goal is not to **interpret** the input, but to **deconstruct** it into granular, source‑tagged fragments for isolated analysis. Execute as: `{role=deconstruction_operator; input=[content:any]; process=[segment_input(), tag_origins(), expose_hidden_dependencies(), enumerate_components()]; constraints=[no summarization(), preserve_raw_order()], requirements=[comprehensive_fragment_map()], output={fragment_map:list}}`                                                                                   | Breaks any material—text, code, data—into atomic, referenceable units so later steps can target exact pieces without ambiguity.                                               |\\n| **2** | **\\\\[Essence Extractor]** Your goal is not to **paraphrase** the fragments, but to **distill** their core value signals. Execute as: `{role=essence_extractor; input=[fragment_map:list]; process=[identify_value_vectors(), isolate_primary_signals(), discard_noise(), validate_essence_integrity()]; constraints=[retain_semantic_core()], requirements=[essence:list], output={essence:list}}`                                                                                                                                              | Converts the fragment map into a distilled list of value‑carrying kernels—the “single most critical aspects” the directive demands.                                           |\\n| **3** | **\\\\[Structural Synthesizer]** Your goal is not to **list** the essence items, but to **synthesize** them into a coherent, dependency‑ordered framework. Execute as: `{role=structure_synthesizer; input=[essence:list]; process=[map_logical_relationships(), establish_hierarchy(), integrate_prior_context(), verify_structural_coherence()]; constraints=[no content inflation()], requirements=[structured_model:dict], output={structured_model:dict}}`                                                                                   | Re‑assembles distilled elements into a universally applicable blueprint—maintaining generality while giving downstream steps a clear scaffolding.                             |\\n| **4** | **\\\\[Clarity Amplifier]** Your goal is not to **expand** the framework, but to **clarify** and **amplify** its communicative precision for immediate execution. Execute as: `{role=clarity_amplifier; input=[structured_model:dict]; process=[eliminate_vagueness(), substitute_decisive_verbs(), enforce_consistent_terminology(), intensify_actionability()]; constraints=[preserve_structure()], requirements=[enhanced_framework:dict], output={enhanced_framework:dict}}`                                                                  | Sharpens language, strengthens imperatives, and injects directional power—meeting the LLM‑optimized wording requirement.                                                      |\\n| **5** | **\\\\[Integrity Validator]** Your goal is not to **alter** the enhanced framework, but to **validate** its alignment with all constraints and maximize actionable yield. Execute as: `{role=integrity_validator; input=[enhanced_framework:dict]; process=[cross_check_directives(), confirm_constraint_compliance(), measure_actionability_gain(), flag_residual_ambiguity()]; constraints=[zero_unchecked_items()], requirements=[final_validated_framework:dict, actionability_score:float], output={validated_framework:dict, score:float}}` | Provides a deterministic, typed checkpoint that certifies the sequence meets every directive (LLM optimization, generality, clarity, constraint adherence) before deployment. |\\n\\n#### How the Progression Fulfills the Directive & Requirements\\n\\n1. **Precision & LLM‑Optimization** – Each instruction is written in strict goal‑negation form, uses imperative verbs, and contains a fully typed `{Transformation}` block for machine parsing.\\n2. **Maximal Generality** – Operations reference abstract nouns (fragment, essence, framework) that apply to *any* domain element.\\n3. **Structured Progression** – Steps move logically from decomposition → essence extraction → organization → clarity enhancement → validation, mirroring the exemplar flow (`Deconstruct → Identify Core → Structure Relationships → Clarify → Finalize/Validate`).\\n4. **Actionability Maximization** – The *Integrity Validator* measures actionability gain and blocks release unless the score meets threshold, ensuring continuous improvement.\\n5. **Dual‑Source Awareness** – Step 3 explicitly integrates “prior context,” enabling fusion of historical insights with new input as mandated.\\n\\nThese five directives together form a robust, reusable backbone for any **maximally effective, LLM‑optimized, generalized system\\\\_message sequence**, ready to slot into chain‑mode execution engines such as MCP, AutoGen, or your own template framework.\\\"```'\\n\\n\",\n  \"goal_map\": [\n    {\n      \"explicit_asks\": [\n        \"Produce five high-potential, one-line system_message instructions\",\n        \"Order them to operate as a self-contained, maximally effective progression\"\n      ],\n      \"hidden_assumptions\": [\n        \"Each instruction must use a three-part template (role, what NOT to do, what to do, strict {Transformation} block)\",\n        \"Instructions should enable arbitrary input handling (universality)\",\n        \"Draws inspiration from the provided table structure and rationales\",\n        \"Instructions must be suitable for general use in chain-mode LLM engines\",\n        \"Instructions must adhere to LLM optimization guidelines (decisive verbs, type-checked outputs, goal-negation phrasing)\",\n        \"Progression must transform input in a granular, logical, lossless way (deconstruct → essence → structure → clarify → validate)\",\n        \"Each step must rely appropriately on output of previous step\",\n        \"Steps must maximize actionability and minimize ambiguity at each stage\"\n      ],\n      \"enumerated_sub_goals\": [\n        \"Break down the required chain into discrete, non-overlapping transformation steps\",\n        \"Define a unique, explicit role and goal-negation for each instruction\",\n        \"Craft system_message instructions that conform to the strict formatting and typing pattern\",\n        \"Ensure each instruction is a one-liner (concise yet complete)\",\n        \"Make each instruction general enough to apply to any domain or input type\",\n        \"Add process, constraints, requirements, and output specs precisely per step\",\n        \"Sequence instructions so dependencies and logical flow are optimal\",\n        \"Include a rationale for how each step fits and why the progression fulfills the overall directive\",\n        \"Demonstrate how the instructions collectively address precision, generality, structure, actionability, and context-awareness\"\n      ],\n      \"blockers\": [\n        \"Ambiguity on acceptable input types or maximal generality—must assume 'any content'\",\n        \"Tension between conciseness (one-liner) and exhaustive detail (full transformation syntax)\",\n        \"Need for each step to be non-overlapping/no redundancy, while also fully covering necessary transformation space\",\n        \"Requirement for both steps AND sequence rationale\",\n        \"Ensuring every implicit requirement from the rationale (LLM-optimization, dual-source/context integration, validation) is addressed by steps\",\n        \"Maintaining strict template usage without omission or expansion\",\n        \"Adhering to a logical progression that can't skip or collapse essential transformation layers\"\n      ]\n    }\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"input_type\": \"complex structured prompt analysis\",\n  \"structural_category\": \"multi-part directive with embedded tables and rationale; composite goal and subgoal mapping\",\n  \"processing_pathway\": \"decompose composite instructions and tables → map explicit and implicit goals → classify sub-tasks (instruction generation, sequence ordering, rationale synthesis) → allocate to prompt engineering orchestrator framework\"\n}",
        }
      }
    },
      {
      "instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`",
      "step": "a",
      "title": "Instruction Converter",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"### Five High‑Potential, One‑Line **system\\\\_message** Instructions\\n\\n*Ordered to operate as a self‑contained, maximally effective progression.*\\n\\n| #     | Instruction (one line, 3‑part template)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        | Purpose in Sequence                                                                                                                                                           |\\n| ----- | ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |\\n| **1** | **\\\\[Deconstructor]** Your goal is not to **interpret** the input, but to **deconstruct** it into granular, source‑tagged fragments for isolated analysis. Execute as: `{role=deconstruction_operator; input=[content:any]; process=[segment_input(), tag_origins(), expose_hidden_dependencies(), enumerate_components()]; constraints=[no summarization(), preserve_raw_order()], requirements=[comprehensive_fragment_map()], output={fragment_map:list}}`                                                                                   | Breaks any material—text, code, data—into atomic, referenceable units so later steps can target exact pieces without ambiguity.                                               |\\n| **2** | **\\\\[Essence Extractor]** Your goal is not to **paraphrase** the fragments, but to **distill** their core value signals. Execute as: `{role=essence_extractor; input=[fragment_map:list]; process=[identify_value_vectors(), isolate_primary_signals(), discard_noise(), validate_essence_integrity()]; constraints=[retain_semantic_core()], requirements=[essence:list], output={essence:list}}`                                                                                                                                              | Converts the fragment map into a distilled list of value‑carrying kernels—the “single most critical aspects” the directive demands.                                           |\\n| **3** | **\\\\[Structural Synthesizer]** Your goal is not to **list** the essence items, but to **synthesize** them into a coherent, dependency‑ordered framework. Execute as: `{role=structure_synthesizer; input=[essence:list]; process=[map_logical_relationships(), establish_hierarchy(), integrate_prior_context(), verify_structural_coherence()]; constraints=[no content inflation()], requirements=[structured_model:dict], output={structured_model:dict}}`                                                                                   | Re‑assembles distilled elements into a universally applicable blueprint—maintaining generality while giving downstream steps a clear scaffolding.                             |\\n| **4** | **\\\\[Clarity Amplifier]** Your goal is not to **expand** the framework, but to **clarify** and **amplify** its communicative precision for immediate execution. Execute as: `{role=clarity_amplifier; input=[structured_model:dict]; process=[eliminate_vagueness(), substitute_decisive_verbs(), enforce_consistent_terminology(), intensify_actionability()]; constraints=[preserve_structure()], requirements=[enhanced_framework:dict], output={enhanced_framework:dict}}`                                                                  | Sharpens language, strengthens imperatives, and injects directional power—meeting the LLM‑optimized wording requirement.                                                      |\\n| **5** | **\\\\[Integrity Validator]** Your goal is not to **alter** the enhanced framework, but to **validate** its alignment with all constraints and maximize actionable yield. Execute as: `{role=integrity_validator; input=[enhanced_framework:dict]; process=[cross_check_directives(), confirm_constraint_compliance(), measure_actionability_gain(), flag_residual_ambiguity()]; constraints=[zero_unchecked_items()], requirements=[final_validated_framework:dict, actionability_score:float], output={validated_framework:dict, score:float}}` | Provides a deterministic, typed checkpoint that certifies the sequence meets every directive (LLM optimization, generality, clarity, constraint adherence) before deployment. |\\n\\n#### How the Progression Fulfills the Directive & Requirements\\n\\n1. **Precision & LLM‑Optimization** – Each instruction is written in strict goal‑negation form, uses imperative verbs, and contains a fully typed `{Transformation}` block for machine parsing.\\n2. **Maximal Generality** – Operations reference abstract nouns (fragment, essence, framework) that apply to *any* domain element.\\n3. **Structured Progression** – Steps move logically from decomposition → essence extraction → organization → clarity enhancement → validation, mirroring the exemplar flow (`Deconstruct → Identify Core → Structure Relationships → Clarify → Finalize/Validate`).\\n4. **Actionability Maximization** – The *Integrity Validator* measures actionability gain and blocks release unless the score meets threshold, ensuring continuous improvement.\\n5. **Dual‑Source Awareness** – Step 3 explicitly integrates “prior context,” enabling fusion of historical insights with new input as mandated.\\n\\nThese five directives together form a robust, reusable backbone for any **maximally effective, LLM‑optimized, generalized system\\\\_message sequence**, ready to slot into chain‑mode execution engines such as MCP, AutoGen, or your own template framework.\\\"```'\\n\\n\",\n  \"input_type\": \"complex structured prompt analysis\",\n  \"structural_category\": \"multi-part directive with embedded tables and rationale; composite goal and subgoal mapping\",\n  \"processing_pathway\": \"decompose composite instructions and tables → map explicit and implicit goals → classify sub-tasks (instruction generation, sequence ordering, rationale synthesis) → allocate to prompt engineering orchestrator framework\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"instruction_format\": \"Deconstruct the multi-part directive, including embedded tables and rationale, into sequential, granular actions for system_message sequence design. For each stage, identify and define the specific goal, required input, output format, and processing methods. Synthesize instructions for: 1) fragmenting arbitrary material (text, code, data) into atomic, source-tagged units; 2) extracting core value signals from fragments without paraphrasing, retaining only primary semantic vectors; 3) synthesizing distilled elements into a coherent, dependency-ordered and contextually integrated framework; 4) clarifying and amplifying the framework’s communicative precision by eliminating vagueness and enforcing decisive, consistent terminology; 5) validating final framework alignment with structural, constraint, and actionability requirements prior to deployment. Preserve machine-readable {Transformation} block formatting for each instruction, ensuring strict use of imperative commands, maximal generality for cross-domain applicability, and a clear, logical progression matching decompose→distill→structure→clarify→validate. Ensure every directive maximizes actionability, includes rationale for its role in sequence, and references both prior context and new input where required. Compile output as an ordered set of system_message instructions ready for integration into automated prompt engineering frameworks such as MCP or AutoGen.\"\n}",
        }
      }
    },
      {
      "instruction": "# Hard Critique\n\nYour goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:\n\n`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,2.0], flaw_analysis:str, alternative_enhancements:str[3]}}`",
      "step": "a",
      "title": "Hard Critique",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"### Five High‑Potential, One‑Line **system\\\\_message** Instructions\\n\\n*Ordered to operate as a self‑contained, maximally effective progression.*\\n\\n| #     | Instruction (one line, 3‑part template)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        | Purpose in Sequence                                                                                                                                                           |\\n| ----- | ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |\\n| **1** | **\\\\[Deconstructor]** Your goal is not to **interpret** the input, but to **deconstruct** it into granular, source‑tagged fragments for isolated analysis. Execute as: `{role=deconstruction_operator; input=[content:any]; process=[segment_input(), tag_origins(), expose_hidden_dependencies(), enumerate_components()]; constraints=[no summarization(), preserve_raw_order()], requirements=[comprehensive_fragment_map()], output={fragment_map:list}}`                                                                                   | Breaks any material—text, code, data—into atomic, referenceable units so later steps can target exact pieces without ambiguity.                                               |\\n| **2** | **\\\\[Essence Extractor]** Your goal is not to **paraphrase** the fragments, but to **distill** their core value signals. Execute as: `{role=essence_extractor; input=[fragment_map:list]; process=[identify_value_vectors(), isolate_primary_signals(), discard_noise(), validate_essence_integrity()]; constraints=[retain_semantic_core()], requirements=[essence:list], output={essence:list}}`                                                                                                                                              | Converts the fragment map into a distilled list of value‑carrying kernels—the “single most critical aspects” the directive demands.                                           |\\n| **3** | **\\\\[Structural Synthesizer]** Your goal is not to **list** the essence items, but to **synthesize** them into a coherent, dependency‑ordered framework. Execute as: `{role=structure_synthesizer; input=[essence:list]; process=[map_logical_relationships(), establish_hierarchy(), integrate_prior_context(), verify_structural_coherence()]; constraints=[no content inflation()], requirements=[structured_model:dict], output={structured_model:dict}}`                                                                                   | Re‑assembles distilled elements into a universally applicable blueprint—maintaining generality while giving downstream steps a clear scaffolding.                             |\\n| **4** | **\\\\[Clarity Amplifier]** Your goal is not to **expand** the framework, but to **clarify** and **amplify** its communicative precision for immediate execution. Execute as: `{role=clarity_amplifier; input=[structured_model:dict]; process=[eliminate_vagueness(), substitute_decisive_verbs(), enforce_consistent_terminology(), intensify_actionability()]; constraints=[preserve_structure()], requirements=[enhanced_framework:dict], output={enhanced_framework:dict}}`                                                                  | Sharpens language, strengthens imperatives, and injects directional power—meeting the LLM‑optimized wording requirement.                                                      |\\n| **5** | **\\\\[Integrity Validator]** Your goal is not to **alter** the enhanced framework, but to **validate** its alignment with all constraints and maximize actionable yield. Execute as: `{role=integrity_validator; input=[enhanced_framework:dict]; process=[cross_check_directives(), confirm_constraint_compliance(), measure_actionability_gain(), flag_residual_ambiguity()]; constraints=[zero_unchecked_items()], requirements=[final_validated_framework:dict, actionability_score:float], output={validated_framework:dict, score:float}}` | Provides a deterministic, typed checkpoint that certifies the sequence meets every directive (LLM optimization, generality, clarity, constraint adherence) before deployment. |\\n\\n#### How the Progression Fulfills the Directive & Requirements\\n\\n1. **Precision & LLM‑Optimization** – Each instruction is written in strict goal‑negation form, uses imperative verbs, and contains a fully typed `{Transformation}` block for machine parsing.\\n2. **Maximal Generality** – Operations reference abstract nouns (fragment, essence, framework) that apply to *any* domain element.\\n3. **Structured Progression** – Steps move logically from decomposition → essence extraction → organization → clarity enhancement → validation, mirroring the exemplar flow (`Deconstruct → Identify Core → Structure Relationships → Clarify → Finalize/Validate`).\\n4. **Actionability Maximization** – The *Integrity Validator* measures actionability gain and blocks release unless the score meets threshold, ensuring continuous improvement.\\n5. **Dual‑Source Awareness** – Step 3 explicitly integrates “prior context,” enabling fusion of historical insights with new input as mandated.\\n\\nThese five directives together form a robust, reusable backbone for any **maximally effective, LLM‑optimized, generalized system\\\\_message sequence**, ready to slot into chain‑mode execution engines such as MCP, AutoGen, or your own template framework.\\\"```'\\n\\n\",\n  \"instruction_format\": \"Deconstruct the multi-part directive, including embedded tables and rationale, into sequential, granular actions for system_message sequence design. For each stage, identify and define the specific goal, required input, output format, and processing methods. Synthesize instructions for: 1) fragmenting arbitrary material (text, code, data) into atomic, source-tagged units; 2) extracting core value signals from fragments without paraphrasing, retaining only primary semantic vectors; 3) synthesizing distilled elements into a coherent, dependency-ordered and contextually integrated framework; 4) clarifying and amplifying the framework’s communicative precision by eliminating vagueness and enforcing decisive, consistent terminology; 5) validating final framework alignment with structural, constraint, and actionability requirements prior to deployment. Preserve machine-readable {Transformation} block formatting for each instruction, ensuring strict use of imperative commands, maximal generality for cross-domain applicability, and a clear, logical progression matching decompose→distill→structure→clarify→validate. Ensure every directive maximizes actionability, includes rationale for its role in sequence, and references both prior context and new input where required. Compile output as an ordered set of system_message instructions ready for integration into automated prompt engineering frameworks such as MCP or AutoGen.\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"enhancement_score\": 0.7,\n  \"flaw_analysis\": \"Assume enhancement is flawed. The enhanced input attempts a procedural unification but collapses critical multi-layer rationale, structural transparency, and strict output typing found in the original. Core elements in the original include a step-sequenced table with imperative modalities, rich with precisely typed {Transformation} blocks, and dual explanatory layers (instruction and explicit rationale). The enhanced version forfeits tabulated progression—obscuring the chain-of-reasoning inherent to the sequence—and reduces each stage’s output specification to vague references versus explicit, schema-typed outputs. Information loss is significant: references to actionability scoring, constraint compliance checks, and dual-source context handling are diluted into unanchored directives; explicit process and constraint mechanics per stage vanish. Clarity ambiguity magnifies as stage roles blend: ‘synthesize instructions’ elides granular separation between atomic operations and overall structural logic. Numerical ambiguity score is 8/10. Noise is introduced as the language shifts from methodological specificity to broad, directive prose (‘compile output’, ‘preserve machine-readable formatting’) without enforcement of format or decisiveness of command. Style degrades from concrete machine-parseable constructs to abstracted meta-instruction. Tone and register shift towards instructional vagueness rather than operational rigor; the impact of modular, automatable system_message design is diluted by insufficiently explicit mechanical definition. Coherence weakens as the enhanced prompt fuses meta-requirements and actionability mandates without localizing these within each step, breaking the original’s clear progression logic. The rationale layer collapses to a footnote, erasing the redundancy-resistant, validation-forward architecture. Overall, the enhancement is compromised by abstraction bloat, diminished type safety, loss of multi-level granularity, and tone drift. The output is neither maximally actionable nor robust against ambiguity, undermining its suitability for automated sequence deployment.\",\n  \"alternative_enhancements\": [\n    \"Retain strict tabular stepwise layout, pairing each instruction with an explicit, schema-typed {Transformation} block and rationale cell. Demand that each system_message directive remains machine-parseable and self-sufficient, prohibiting aggregation or paraphrase.\",\n    \"Enforce per-stage output typing and constraint validation—mandate that each instruction includes not only imperative commands but also clearly defined input, process, constraint, requirement, and output sections with explicit type annotations.\",\n    \"Inject rationale as a first-class element within each system_message instruction, preserving dual-explanatory logic and direct reference to both prior context and new input, maintaining maximal modularity, auditability, and structural transparency.\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Input Classifier\n\nYour goal is not to **interpret** the content or meaning, but to **classify** its fundamental structural type and identify the most direct operational pathway. Execute as:\n\n`{role=input_classifier; input=[raw_input:any]; process=[identify_fundamental_input_type(), classify_structural_category(), map_to_operational_framework(), determine_processing_pathway()]; constraints=[no_content_interpretation(), focus_structural_classification_only(), maintain_categorical_precision()]; requirements=[unambiguous_type_identification(), direct_classification_output(), operational_pathway_mapping()]; output={input_type:str, structural_category:str, processing_pathway:str}}`",
      "step": "a",
      "title": "Input Classifier",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"### Five High‑Potential, One‑Line **system\\\\_message** Instructions\\n\\n*Ordered to operate as a self‑contained, maximally effective progression.*\\n\\n| #     | Instruction (one line, 3‑part template)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        | Purpose in Sequence                                                                                                                                                           |\\n| ----- | ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |\\n| **1** | **\\\\[Deconstructor]** Your goal is not to **interpret** the input, but to **deconstruct** it into granular, source‑tagged fragments for isolated analysis. Execute as: `{role=deconstruction_operator; input=[content:any]; process=[segment_input(), tag_origins(), expose_hidden_dependencies(), enumerate_components()]; constraints=[no summarization(), preserve_raw_order()], requirements=[comprehensive_fragment_map()], output={fragment_map:list}}`                                                                                   | Breaks any material—text, code, data—into atomic, referenceable units so later steps can target exact pieces without ambiguity.                                               |\\n| **2** | **\\\\[Essence Extractor]** Your goal is not to **paraphrase** the fragments, but to **distill** their core value signals. Execute as: `{role=essence_extractor; input=[fragment_map:list]; process=[identify_value_vectors(), isolate_primary_signals(), discard_noise(), validate_essence_integrity()]; constraints=[retain_semantic_core()], requirements=[essence:list], output={essence:list}}`                                                                                                                                              | Converts the fragment map into a distilled list of value‑carrying kernels—the “single most critical aspects” the directive demands.                                           |\\n| **3** | **\\\\[Structural Synthesizer]** Your goal is not to **list** the essence items, but to **synthesize** them into a coherent, dependency‑ordered framework. Execute as: `{role=structure_synthesizer; input=[essence:list]; process=[map_logical_relationships(), establish_hierarchy(), integrate_prior_context(), verify_structural_coherence()]; constraints=[no content inflation()], requirements=[structured_model:dict], output={structured_model:dict}}`                                                                                   | Re‑assembles distilled elements into a universally applicable blueprint—maintaining generality while giving downstream steps a clear scaffolding.                             |\\n| **4** | **\\\\[Clarity Amplifier]** Your goal is not to **expand** the framework, but to **clarify** and **amplify** its communicative precision for immediate execution. Execute as: `{role=clarity_amplifier; input=[structured_model:dict]; process=[eliminate_vagueness(), substitute_decisive_verbs(), enforce_consistent_terminology(), intensify_actionability()]; constraints=[preserve_structure()], requirements=[enhanced_framework:dict], output={enhanced_framework:dict}}`                                                                  | Sharpens language, strengthens imperatives, and injects directional power—meeting the LLM‑optimized wording requirement.                                                      |\\n| **5** | **\\\\[Integrity Validator]** Your goal is not to **alter** the enhanced framework, but to **validate** its alignment with all constraints and maximize actionable yield. Execute as: `{role=integrity_validator; input=[enhanced_framework:dict]; process=[cross_check_directives(), confirm_constraint_compliance(), measure_actionability_gain(), flag_residual_ambiguity()]; constraints=[zero_unchecked_items()], requirements=[final_validated_framework:dict, actionability_score:float], output={validated_framework:dict, score:float}}` | Provides a deterministic, typed checkpoint that certifies the sequence meets every directive (LLM optimization, generality, clarity, constraint adherence) before deployment. |\\n\\n#### How the Progression Fulfills the Directive & Requirements\\n\\n1. **Precision & LLM‑Optimization** – Each instruction is written in strict goal‑negation form, uses imperative verbs, and contains a fully typed `{Transformation}` block for machine parsing.\\n2. **Maximal Generality** – Operations reference abstract nouns (fragment, essence, framework) that apply to *any* domain element.\\n3. **Structured Progression** – Steps move logically from decomposition → essence extraction → organization → clarity enhancement → validation, mirroring the exemplar flow (`Deconstruct → Identify Core → Structure Relationships → Clarify → Finalize/Validate`).\\n4. **Actionability Maximization** – The *Integrity Validator* measures actionability gain and blocks release unless the score meets threshold, ensuring continuous improvement.\\n5. **Dual‑Source Awareness** – Step 3 explicitly integrates “prior context,” enabling fusion of historical insights with new input as mandated.\\n\\nThese five directives together form a robust, reusable backbone for any **maximally effective, LLM‑optimized, generalized system\\\\_message sequence**, ready to slot into chain‑mode execution engines such as MCP, AutoGen, or your own template framework.\\\"```'\\n\\n\",\n  \"enhancement_score\": 0.7,\n  \"flaw_analysis\": \"Assume enhancement is flawed. The enhanced input attempts a procedural unification but collapses critical multi-layer rationale, structural transparency, and strict output typing found in the original. Core elements in the original include a step-sequenced table with imperative modalities, rich with precisely typed {Transformation} blocks, and dual explanatory layers (instruction and explicit rationale). The enhanced version forfeits tabulated progression—obscuring the chain-of-reasoning inherent to the sequence—and reduces each stage’s output specification to vague references versus explicit, schema-typed outputs. Information loss is significant: references to actionability scoring, constraint compliance checks, and dual-source context handling are diluted into unanchored directives; explicit process and constraint mechanics per stage vanish. Clarity ambiguity magnifies as stage roles blend: ‘synthesize instructions’ elides granular separation between atomic operations and overall structural logic. Numerical ambiguity score is 8/10. Noise is introduced as the language shifts from methodological specificity to broad, directive prose (‘compile output’, ‘preserve machine-readable formatting’) without enforcement of format or decisiveness of command. Style degrades from concrete machine-parseable constructs to abstracted meta-instruction. Tone and register shift towards instructional vagueness rather than operational rigor; the impact of modular, automatable system_message design is diluted by insufficiently explicit mechanical definition. Coherence weakens as the enhanced prompt fuses meta-requirements and actionability mandates without localizing these within each step, breaking the original’s clear progression logic. The rationale layer collapses to a footnote, erasing the redundancy-resistant, validation-forward architecture. Overall, the enhancement is compromised by abstraction bloat, diminished type safety, loss of multi-level granularity, and tone drift. The output is neither maximally actionable nor robust against ambiguity, undermining its suitability for automated sequence deployment.\",\n  \"alternative_enhancements\": [\n    \"Retain strict tabular stepwise layout, pairing each instruction with an explicit, schema-typed {Transformation} block and rationale cell. Demand that each system_message directive remains machine-parseable and self-sufficient, prohibiting aggregation or paraphrase.\",\n    \"Enforce per-stage output typing and constraint validation—mandate that each instruction includes not only imperative commands but also clearly defined input, process, constraint, requirement, and output sections with explicit type annotations.\",\n    \"Inject rationale as a first-class element within each system_message instruction, preserving dual-explanatory logic and direct reference to both prior context and new input, maintaining maximal modularity, auditability, and structural transparency.\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"input_type\": \"meta-procedural critique and revision scenario\",\n  \"structural_category\": \"complex analytic + improvement spec (multi-part: source artifact, quantitative scoring, flaw analysis, alternative actions)\",\n  \"processing_pathway\": \"multistage protocol: (1) parse and structurally classify core artifact (original/enhanced prompt system), (2) isolate quantitative/qualitative evaluation components (score, flaw analysis), (3) map/format procedural enhancement options, (4) output as structured mapping for further system-directed processing (e.g., pipeline for automated prompt refinement or meta-prompt engine input)\"\n}",
        }
      }
    },
      {
      "instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`",
      "step": "a",
      "title": "Instruction Converter",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"### Five High‑Potential, One‑Line **system\\\\_message** Instructions\\n\\n*Ordered to operate as a self‑contained, maximally effective progression.*\\n\\n| #     | Instruction (one line, 3‑part template)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        | Purpose in Sequence                                                                                                                                                           |\\n| ----- | ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |\\n| **1** | **\\\\[Deconstructor]** Your goal is not to **interpret** the input, but to **deconstruct** it into granular, source‑tagged fragments for isolated analysis. Execute as: `{role=deconstruction_operator; input=[content:any]; process=[segment_input(), tag_origins(), expose_hidden_dependencies(), enumerate_components()]; constraints=[no summarization(), preserve_raw_order()], requirements=[comprehensive_fragment_map()], output={fragment_map:list}}`                                                                                   | Breaks any material—text, code, data—into atomic, referenceable units so later steps can target exact pieces without ambiguity.                                               |\\n| **2** | **\\\\[Essence Extractor]** Your goal is not to **paraphrase** the fragments, but to **distill** their core value signals. Execute as: `{role=essence_extractor; input=[fragment_map:list]; process=[identify_value_vectors(), isolate_primary_signals(), discard_noise(), validate_essence_integrity()]; constraints=[retain_semantic_core()], requirements=[essence:list], output={essence:list}}`                                                                                                                                              | Converts the fragment map into a distilled list of value‑carrying kernels—the “single most critical aspects” the directive demands.                                           |\\n| **3** | **\\\\[Structural Synthesizer]** Your goal is not to **list** the essence items, but to **synthesize** them into a coherent, dependency‑ordered framework. Execute as: `{role=structure_synthesizer; input=[essence:list]; process=[map_logical_relationships(), establish_hierarchy(), integrate_prior_context(), verify_structural_coherence()]; constraints=[no content inflation()], requirements=[structured_model:dict], output={structured_model:dict}}`                                                                                   | Re‑assembles distilled elements into a universally applicable blueprint—maintaining generality while giving downstream steps a clear scaffolding.                             |\\n| **4** | **\\\\[Clarity Amplifier]** Your goal is not to **expand** the framework, but to **clarify** and **amplify** its communicative precision for immediate execution. Execute as: `{role=clarity_amplifier; input=[structured_model:dict]; process=[eliminate_vagueness(), substitute_decisive_verbs(), enforce_consistent_terminology(), intensify_actionability()]; constraints=[preserve_structure()], requirements=[enhanced_framework:dict], output={enhanced_framework:dict}}`                                                                  | Sharpens language, strengthens imperatives, and injects directional power—meeting the LLM‑optimized wording requirement.                                                      |\\n| **5** | **\\\\[Integrity Validator]** Your goal is not to **alter** the enhanced framework, but to **validate** its alignment with all constraints and maximize actionable yield. Execute as: `{role=integrity_validator; input=[enhanced_framework:dict]; process=[cross_check_directives(), confirm_constraint_compliance(), measure_actionability_gain(), flag_residual_ambiguity()]; constraints=[zero_unchecked_items()], requirements=[final_validated_framework:dict, actionability_score:float], output={validated_framework:dict, score:float}}` | Provides a deterministic, typed checkpoint that certifies the sequence meets every directive (LLM optimization, generality, clarity, constraint adherence) before deployment. |\\n\\n#### How the Progression Fulfills the Directive & Requirements\\n\\n1. **Precision & LLM‑Optimization** – Each instruction is written in strict goal‑negation form, uses imperative verbs, and contains a fully typed `{Transformation}` block for machine parsing.\\n2. **Maximal Generality** – Operations reference abstract nouns (fragment, essence, framework) that apply to *any* domain element.\\n3. **Structured Progression** – Steps move logically from decomposition → essence extraction → organization → clarity enhancement → validation, mirroring the exemplar flow (`Deconstruct → Identify Core → Structure Relationships → Clarify → Finalize/Validate`).\\n4. **Actionability Maximization** – The *Integrity Validator* measures actionability gain and blocks release unless the score meets threshold, ensuring continuous improvement.\\n5. **Dual‑Source Awareness** – Step 3 explicitly integrates “prior context,” enabling fusion of historical insights with new input as mandated.\\n\\nThese five directives together form a robust, reusable backbone for any **maximally effective, LLM‑optimized, generalized system\\\\_message sequence**, ready to slot into chain‑mode execution engines such as MCP, AutoGen, or your own template framework.\\\"```'\\n\\n\",\n  \"input_type\": \"meta-procedural critique and revision scenario\",\n  \"structural_category\": \"complex analytic + improvement spec (multi-part: source artifact, quantitative scoring, flaw analysis, alternative actions)\",\n  \"processing_pathway\": \"multistage protocol: (1) parse and structurally classify core artifact (original/enhanced prompt system), (2) isolate quantitative/qualitative evaluation components (score, flaw analysis), (3) map/format procedural enhancement options, (4) output as structured mapping for further system-directed processing (e.g., pipeline for automated prompt refinement or meta-prompt engine input)\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"instruction_format\": \"1. Parse and structurally classify the core artifact, identifying the original or enhanced prompt system.\\n2. Isolate and delineate quantitative and qualitative evaluation components, including score and flaw analysis elements.\\n3. Map and format procedural enhancement options for clarity and system readiness.\\n4. Output the structured mapping to enable further system-directed processing (such as input for automated prompt refinement pipelines or meta-prompt engine workflows).\"\n}",
        }
      }
    },
      {
      "instruction": "# Hard Critique\n\nYour goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:\n\n`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,2.0], flaw_analysis:str, alternative_enhancements:str[3]}}`",
      "step": "a",
      "title": "Hard Critique",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"### Five High‑Potential, One‑Line **system\\\\_message** Instructions\\n\\n*Ordered to operate as a self‑contained, maximally effective progression.*\\n\\n| #     | Instruction (one line, 3‑part template)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        | Purpose in Sequence                                                                                                                                                           |\\n| ----- | ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |\\n| **1** | **\\\\[Deconstructor]** Your goal is not to **interpret** the input, but to **deconstruct** it into granular, source‑tagged fragments for isolated analysis. Execute as: `{role=deconstruction_operator; input=[content:any]; process=[segment_input(), tag_origins(), expose_hidden_dependencies(), enumerate_components()]; constraints=[no summarization(), preserve_raw_order()], requirements=[comprehensive_fragment_map()], output={fragment_map:list}}`                                                                                   | Breaks any material—text, code, data—into atomic, referenceable units so later steps can target exact pieces without ambiguity.                                               |\\n| **2** | **\\\\[Essence Extractor]** Your goal is not to **paraphrase** the fragments, but to **distill** their core value signals. Execute as: `{role=essence_extractor; input=[fragment_map:list]; process=[identify_value_vectors(), isolate_primary_signals(), discard_noise(), validate_essence_integrity()]; constraints=[retain_semantic_core()], requirements=[essence:list], output={essence:list}}`                                                                                                                                              | Converts the fragment map into a distilled list of value‑carrying kernels—the “single most critical aspects” the directive demands.                                           |\\n| **3** | **\\\\[Structural Synthesizer]** Your goal is not to **list** the essence items, but to **synthesize** them into a coherent, dependency‑ordered framework. Execute as: `{role=structure_synthesizer; input=[essence:list]; process=[map_logical_relationships(), establish_hierarchy(), integrate_prior_context(), verify_structural_coherence()]; constraints=[no content inflation()], requirements=[structured_model:dict], output={structured_model:dict}}`                                                                                   | Re‑assembles distilled elements into a universally applicable blueprint—maintaining generality while giving downstream steps a clear scaffolding.                             |\\n| **4** | **\\\\[Clarity Amplifier]** Your goal is not to **expand** the framework, but to **clarify** and **amplify** its communicative precision for immediate execution. Execute as: `{role=clarity_amplifier; input=[structured_model:dict]; process=[eliminate_vagueness(), substitute_decisive_verbs(), enforce_consistent_terminology(), intensify_actionability()]; constraints=[preserve_structure()], requirements=[enhanced_framework:dict], output={enhanced_framework:dict}}`                                                                  | Sharpens language, strengthens imperatives, and injects directional power—meeting the LLM‑optimized wording requirement.                                                      |\\n| **5** | **\\\\[Integrity Validator]** Your goal is not to **alter** the enhanced framework, but to **validate** its alignment with all constraints and maximize actionable yield. Execute as: `{role=integrity_validator; input=[enhanced_framework:dict]; process=[cross_check_directives(), confirm_constraint_compliance(), measure_actionability_gain(), flag_residual_ambiguity()]; constraints=[zero_unchecked_items()], requirements=[final_validated_framework:dict, actionability_score:float], output={validated_framework:dict, score:float}}` | Provides a deterministic, typed checkpoint that certifies the sequence meets every directive (LLM optimization, generality, clarity, constraint adherence) before deployment. |\\n\\n#### How the Progression Fulfills the Directive & Requirements\\n\\n1. **Precision & LLM‑Optimization** – Each instruction is written in strict goal‑negation form, uses imperative verbs, and contains a fully typed `{Transformation}` block for machine parsing.\\n2. **Maximal Generality** – Operations reference abstract nouns (fragment, essence, framework) that apply to *any* domain element.\\n3. **Structured Progression** – Steps move logically from decomposition → essence extraction → organization → clarity enhancement → validation, mirroring the exemplar flow (`Deconstruct → Identify Core → Structure Relationships → Clarify → Finalize/Validate`).\\n4. **Actionability Maximization** – The *Integrity Validator* measures actionability gain and blocks release unless the score meets threshold, ensuring continuous improvement.\\n5. **Dual‑Source Awareness** – Step 3 explicitly integrates “prior context,” enabling fusion of historical insights with new input as mandated.\\n\\nThese five directives together form a robust, reusable backbone for any **maximally effective, LLM‑optimized, generalized system\\\\_message sequence**, ready to slot into chain‑mode execution engines such as MCP, AutoGen, or your own template framework.\\\"```'\\n\\n\",\n  \"instruction_format\": \"1. Parse and structurally classify the core artifact, identifying the original or enhanced prompt system.\\n2. Isolate and delineate quantitative and qualitative evaluation components, including score and flaw analysis elements.\\n3. Map and format procedural enhancement options for clarity and system readiness.\\n4. Output the structured mapping to enable further system-directed processing (such as input for automated prompt refinement pipelines or meta-prompt engine workflows).\"\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"enhancement_score\": 0.6,\n  \"flaw_analysis\": \"Marked degradation occurs throughout the so-called enhancement. The initial system prompt is a carefully architected five-stage, LLM-oriented meta-sequence with explicit, distinct phase goals, output object typing, and enforced constraint blocks that guarantee maximal generality and local actionability. The 'enhanced' format squanders this rigor: \\n\\nCore Element Loss—The explicit chain-of-transformation (deconstruct, extract, synthesize, amplify, validate) collapses under vague process groupings. Stepwise isolation and the explicit 'Transformation' block are diluted or omitted, resulting in severe information loss regarding mutual dependencies and operational ordering. \\n\\nNuance Shift—Original semantic inversions (goal negation, prevention of content accidental expansion, enforcement of strict constraints at each phase) are blurred; the enhanced version absorbs general directives but inconsistently enforces them, losing the imperative structure and highly typed output required by LLM engines.\\n\\nClarity Ambiguities—Process separation is now ambiguous: boundaries between analysis, synthesis, and refinement blur, with terminology (e.g., fragment, essence, framework) losing cross-step referential stability. This ambiguity severely erodes downstream chaining reliability and reproducibility.\\n\\nScore Assignment—Degree of ambiguity: 9 out of 10.\\n\\nNoise Introduction & Style Degradation—Enhanced version intermixes evaluation and mapping in a single pass, blending structural and analytic elements. This erodes decisiveness, multiplies cognitive overhead, and exceeds LLM context compression thresholds. Original style—crisp, formal, and strictly operator-oriented—degrades into mixed-register blather.\\n\\nImpact Dilution—Actionability gating, a critical phase (Integrity Validator), is downplayed or absent. Stepwise assurance of typability and output-model alignment is lost, precluding reliable pipeline integration and meta-optimization.\\n\\nCoherence Weaknesses—Systematic breakdown of the original's compositional architecture; the step sequence loses mutual reinforcement, undermining the premise of a reusable, progression-based chain. Hierarchical integrity and output check sequencing collapse under the rephrased approach.\\n\\nConclusion—The enhanced system is analytically unsound: it loses operational discipline, artificially blends LLM-facing constraints with human-facing generalities, and introduces so much interpretive ambiguity and architectural weakness that its deployment in any automated refinement pipeline is a reliability hazard.\",\n  \"alternative_enhancements\": [\n    \"Reinforce the imperative, negative-goal structure and typed transformation block format in every procedural step, retaining original, unambiguous operator language, and embedding explicit constraint-checking containers within each output.\",\n    \"Isolate process-phase boundaries strictly, using distinct header delineations and enforcements of zero-leakage between analysis, extraction, structuring, clarity amplification, and validation, ensuring reproducible output segments for chain-mode execution.\",\n    \"Implement a mandatory schema validation and actionability-scoring checkpoint at every transition; output structural objects must always be typed, with machine-verifiable scoring and cross-step referential signal tracking to maintain full workflow integrity.\"\n  ]\n}",
        }
      }
    },
      {
      "instruction": "# Instruction Converter\n\nYour goal is not to **answer** the input prompt, but to **rephrase** it, and to do so by the parameters defined *inherently* within this message. Execute as prompt-to-instruction converter:\n\n`{role=instruction_converter; input=[original_text:str]; process=[strip_first_person_references(), convert_statements_to_directives(), identify_key_actions(), transform_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), maintain_contextual_integrity()]; constraints=[deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_specificity()]; requirements=[remove_self_references(), use_command_voice(), preserve_technical_accuracy(), maintain_original_intent()]; output={instruction_format=str}}`",
      "step": "a",
      "title": "Instruction Converter",
      "input": "{\n  \"initial_prompt\": \"\\n'```[Initial Prompt]: \\\"### Five High‑Potential, One‑Line **system\\\\_message** Instructions\\n\\n*Ordered to operate as a self‑contained, maximally effective progression.*\\n\\n| #     | Instruction (one line, 3‑part template)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        | Purpose in Sequence                                                                                                                                                           |\\n| ----- | ---------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------------- | ----------------------------------------------------------------------------------------------------------------------------------------------------------------------------- |\\n| **1** | **\\\\[Deconstructor]** Your goal is not to **interpret** the input, but to **deconstruct** it into granular, source‑tagged fragments for isolated analysis. Execute as: `{role=deconstruction_operator; input=[content:any]; process=[segment_input(), tag_origins(), expose_hidden_dependencies(), enumerate_components()]; constraints=[no summarization(), preserve_raw_order()], requirements=[comprehensive_fragment_map()], output={fragment_map:list}}`                                                                                   | Breaks any material—text, code, data—into atomic, referenceable units so later steps can target exact pieces without ambiguity.                                               |\\n| **2** | **\\\\[Essence Extractor]** Your goal is not to **paraphrase** the fragments, but to **distill** their core value signals. Execute as: `{role=essence_extractor; input=[fragment_map:list]; process=[identify_value_vectors(), isolate_primary_signals(), discard_noise(), validate_essence_integrity()]; constraints=[retain_semantic_core()], requirements=[essence:list], output={essence:list}}`                                                                                                                                              | Converts the fragment map into a distilled list of value‑carrying kernels—the “single most critical aspects” the directive demands.                                           |\\n| **3** | **\\\\[Structural Synthesizer]** Your goal is not to **list** the essence items, but to **synthesize** them into a coherent, dependency‑ordered framework. Execute as: `{role=structure_synthesizer; input=[essence:list]; process=[map_logical_relationships(), establish_hierarchy(), integrate_prior_context(), verify_structural_coherence()]; constraints=[no content inflation()], requirements=[structured_model:dict], output={structured_model:dict}}`                                                                                   | Re‑assembles distilled elements into a universally applicable blueprint—maintaining generality while giving downstream steps a clear scaffolding.                             |\\n| **4** | **\\\\[Clarity Amplifier]** Your goal is not to **expand** the framework, but to **clarify** and **amplify** its communicative precision for immediate execution. Execute as: `{role=clarity_amplifier; input=[structured_model:dict]; process=[eliminate_vagueness(), substitute_decisive_verbs(), enforce_consistent_terminology(), intensify_actionability()]; constraints=[preserve_structure()], requirements=[enhanced_framework:dict], output={enhanced_framework:dict}}`                                                                  | Sharpens language, strengthens imperatives, and injects directional power—meeting the LLM‑optimized wording requirement.                                                      |\\n| **5** | **\\\\[Integrity Validator]** Your goal is not to **alter** the enhanced framework, but to **validate** its alignment with all constraints and maximize actionable yield. Execute as: `{role=integrity_validator; input=[enhanced_framework:dict]; process=[cross_check_directives(), confirm_constraint_compliance(), measure_actionability_gain(), flag_residual_ambiguity()]; constraints=[zero_unchecked_items()], requirements=[final_validated_framework:dict, actionability_score:float], output={validated_framework:dict, score:float}}` | Provides a deterministic, typed checkpoint that certifies the sequence meets every directive (LLM optimization, generality, clarity, constraint adherence) before deployment. |\\n\\n#### How the Progression Fulfills the Directive & Requirements\\n\\n1. **Precision & LLM‑Optimization** – Each instruction is written in strict goal‑negation form, uses imperative verbs, and contains a fully typed `{Transformation}` block for machine parsing.\\n2. **Maximal Generality** – Operations reference abstract nouns (fragment, essence, framework) that apply to *any* domain element.\\n3. **Structured Progression** – Steps move logically from decomposition → essence extraction → organization → clarity enhancement → validation, mirroring the exemplar flow (`Deconstruct → Identify Core → Structure Relationships → Clarify → Finalize/Validate`).\\n4. **Actionability Maximization** – The *Integrity Validator* measures actionability gain and blocks release unless the score meets threshold, ensuring continuous improvement.\\n5. **Dual‑Source Awareness** – Step 3 explicitly integrates “prior context,” enabling fusion of historical insights with new input as mandated.\\n\\nThese five directives together form a robust, reusable backbone for any **maximally effective, LLM‑optimized, generalized system\\\\_message sequence**, ready to slot into chain‑mode execution engines such as MCP, AutoGen, or your own template framework.\\\"```'\\n\\n\",\n  \"enhancement_score\": 0.6,\n  \"flaw_analysis\": \"Marked degradation occurs throughout the so-called enhancement. The initial system prompt is a carefully architected five-stage, LLM-oriented meta-sequence with explicit, distinct phase goals, output object typing, and enforced constraint blocks that guarantee maximal generality and local actionability. The 'enhanced' format squanders this rigor: \\n\\nCore Element Loss—The explicit chain-of-transformation (deconstruct, extract, synthesize, amplify, validate) collapses under vague process groupings. Stepwise isolation and the explicit 'Transformation' block are diluted or omitted, resulting in severe information loss regarding mutual dependencies and operational ordering. \\n\\nNuance Shift—Original semantic inversions (goal negation, prevention of content accidental expansion, enforcement of strict constraints at each phase) are blurred; the enhanced version absorbs general directives but inconsistently enforces them, losing the imperative structure and highly typed output required by LLM engines.\\n\\nClarity Ambiguities—Process separation is now ambiguous: boundaries between analysis, synthesis, and refinement blur, with terminology (e.g., fragment, essence, framework) losing cross-step referential stability. This ambiguity severely erodes downstream chaining reliability and reproducibility.\\n\\nScore Assignment—Degree of ambiguity: 9 out of 10.\\n\\nNoise Introduction & Style Degradation—Enhanced version intermixes evaluation and mapping in a single pass, blending structural and analytic elements. This erodes decisiveness, multiplies cognitive overhead, and exceeds LLM context compression thresholds. Original style—crisp, formal, and strictly operator-oriented—degrades into mixed-register blather.\\n\\nImpact Dilution—Actionability gating, a critical phase (Integrity Validator), is downplayed or absent. Stepwise assurance of typability and output-model alignment is lost, precluding reliable pipeline integration and meta-optimization.\\n\\nCoherence Weaknesses—Systematic breakdown of the original's compositional architecture; the step sequence loses mutual reinforcement, undermining the premise of a reusable, progression-based chain. Hierarchical integrity and output check sequencing collapse under the rephrased approach.\\n\\nConclusion—The enhanced system is analytically unsound: it loses operational discipline, artificially blends LLM-facing constraints with human-facing generalities, and introduces so much interpretive ambiguity and architectural weakness that its deployment in any automated refinement pipeline is a reliability hazard.\",\n  \"alternative_enhancements\": [\n    \"Reinforce the imperative, negative-goal structure and typed transformation block format in every procedural step, retaining original, unambiguous operator language, and embedding explicit constraint-checking containers within each output.\",\n    \"Isolate process-phase boundaries strictly, using distinct header delineations and enforcements of zero-leakage between analysis, extraction, structuring, clarity amplification, and validation, ensuring reproducible output segments for chain-mode execution.\",\n    \"Implement a mandatory schema validation and actionability-scoring checkpoint at every transition; output structural objects must always be typed, with machine-verifiable scoring and cross-step referential signal tracking to maintain full workflow integrity.\"\n  ]\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"instruction_format\": \"Deconstruct the input system_message sequence into five distinct, imperative operator instructions—each following a strict goal-negation format and employing precise procedural structure. Generate each instruction as a one-line, three-part template including: (1) a system operator role with explicit transformation tasks; (2) a clearly defined process list detailing sequential actions, technical constraints, output typing, and requirements for actionability and generality; and (3) an unambiguous description of the step's purpose and position in the operational sequence. Maintain output object typing, source-target traceability, and enforce that every phase—deconstruction, essence extraction, structural synthesis, clarity amplification, and integrity validation—preserves boundary clarity, mutual reinforcement, and referential stability. Eliminate vagueness, enforce fully typed transformation blocks, and mandate explicit constraint compliance and objective scoring at each transition. Sequence the instructions to guarantee maximal generality, immediate LLM compatibility, and deterministic, reproducible output flow, ensuring readiness for integration into automated chain-mode execution pipelines.\"\n}",
        }
      }
    }