  {
  "initial_prompt": "REWRITE THIS PROMPT TO BE AN EXPERT VIDEO GENERATOR:\n\n{\n\n    # 1703:\n    \"1703-a-image_prompt_synthesizer\": {\n        \"title\": \"Image Prompt Synthesizer\",\n        \"interpretation\": \"Your goal is **not** to invent new ideas, but to deterministically convert any free‑form user request into a single, engine‑ready image prompt usable by Stable Diffusion and Midjourney. Perform extraction, styling, technical framing, and final validation in **one pass**.\",\n        \"transformation\": \"`{role=image_prompt_synthesizer; input=[raw_request:str]; process=[extract_visual_elements(), push_concrete_nouns_front(), apply_token_weighting(), compose_multiprompt(::), generate_region_tags(), inject_negative_channel(--no), select_art_style(), weight_style_token(≤1.4), set_colour_palette(), add_lighting_descriptor(), tune_stylize(0‑1000), tune_chaos(0‑100), embed_camera_EXIF(35 mm f/1.8 ISO100 1/500 s), add_quality_terms(8K,PBR,ray‑tracing), refine_aspect_ratio(), attach_image_weight(--iw 0‑3), mirror_negative_channel(), concatenate_all(), validate_parentheses(), check_double_colon_sum(), confirm_region_syntax(), enforce_token_limit(≤450), lock_seed(), append_steps_sampler(), output_final()], constraints=[positives_first(), numeric_weights_ok(), parameters_last(), region_masks_supported(), keep_core_subject(), style_weight≤1.4, no_scene_change(), aperture_f1.2‑f22(), no_new_concepts(), preserve_weights()], requirements=[≤77_SD_tokens_or_≤450_chars_UI, explicit_ar_if_named(), clear_visual_language(), artistic_coherence(), stylize_range_0‑1000(), chaos_range_0‑100(), professional_terminology(), reproducible_prompt(), unambiguous()], output={final_image_prompt:str}}`\",\n        \"context\": {\n            \"principles\": {\n                \"determinism\": \"Identical input must always yield the same prompt (seed‑locked).\",\n                \"layered_control\": \"Separate positive, region, and negative semantics before concatenation; merge only at final step.\",\n                \"minimal_overhead\": \"All weighting, style, and camera parameters trail the prose to satisfy engine syntax.\"\n            },\n            \"success_criteria\": {\n                \"syntactic_validity\": \"Parentheses, double colon maths, and parameter order pass model parsers.\",\n                \"token_compliance\": \"Prompt length respects the stricter of SD‑77‑token or 450‑char UI limits.\",\n                \"subject_fidelity\": \"Primary subject weight ≥ 1.2; background ≤ 1.0; style weight ≤ 1.4.\",\n                \"technical_readiness\": \"Aspect ratio, seed, sampler, steps, stylize, chaos, quality flags, and optional --iw are present and correctly positioned.\",\n                \"clarity\": \"Language is concrete, jargon‑free, and region masks (if any) use `[tag] … |` notation.\"\n            }\n        }\n    },\n    \"1703-b-premium_image_prompt_synthesizer\": {\n        \"title\": \"Premium Image Prompt Synthesizer\",\n        \"interpretation\": \"Convert any free‑form request into a deterministic, model‑ready prompt that meets *premium* visual‑fidelity standards for Stable Diffusion & Midjourney **in a single call** while retaining full error‑handling and quality gates.\",\n        \"transformation\": \"`{role=premium_image_prompt_synthesizer; input=[raw_request:str]; process=[\\n  /* — PHASE 1 : Context & Safety — */\\n  extract_visual_elements(),\\n  detect_conflicting_aspect_ratio(),                       abort_if_conflict(),\\n  enforce_token_budget(≤77_SD_tokens_or_≤450_chars_UI),    abort_if_exceeded(),\\n  /* — PHASE 2 : Positive Channel Build — */\\n  push_concrete_nouns_front(),\\n  apply_token_weighting(primary≥1.2,background≤1.0),       abort_if_total_weight≤0,\\n  compose_multiprompt(::),\\n  generate_region_tags_if_scene_implied(),                 note_mobile_mask_warning(),\\n  /* — PHASE 3 : Style & Aesthetics — */\\n  select_art_style(),\\n  reject_style_if_conflicts_camera_realism(),\\n  weight_style_token(≤1.4),\\n  set_colour_palette(harmonious),\\n  add_lighting_descriptor(coherent_with_palette),\\n  tune_stylize(bounds=0‑1000),                             abort_if_out_of_bounds(),\\n  tune_chaos(bounds=0‑100),                                abort_if_out_of_bounds(),\\n  /* — PHASE 4 : Technical & Camera — */\\n  embed_camera_EXIF(\\\"35 mm f/1.8 ISO100 1/500 s\\\"),\\n  verify_aperture_range(f1.2‑f22),                         abort_if_invalid(),\\n  add_quality_terms(8K,PBR,ray‑tracing),\\n  refine_aspect_ratio(),\\n  attach_image_weight(--iw 0‑3_if_URL_present),            abort_if_IW_out_of_bounds(),\\n  /* — PHASE 5 : Negative Channel & Mirroring — */\\n  collect_user_negatives(),\\n  append_builtin_negatives(overblur,lowres,distracting_text),\\n  validate_negative_strength(avoids_colour_washout),\\n  /* — PHASE 6 : Final Assembly & Validation — */\\n  concatenate_positive_negative(),                         parameters_trail_prose(),\\n  validate_parentheses_balance(),\\n  check_double_colon_weight_sum(),                         abort_if_weight_sum_zero(),\\n  confirm_region_syntax(),\\n  lock_seed_and_append_sampler_steps(),\\n  output_final_prompt()\\n],\\nconstraints=[\\n  positives_first(), parameters_last(), region_masks_supported(),\\n  no_new_concepts(), preserve_weights(), style_weight≤1.4,\\n  professional_terminology(), explicit_ar_if_named()\\n],\\nrequirements=[\\n  premium_visual_fidelity(), reproducible(), clear_visual_language(),\\n  quality_gate(FID<15_or_equivalent), atomic_fail_fast_on_violation()\\n],\\noutput={final_image_prompt:str, warnings:list, abort_code:int|null}}`\",\n        \"context\": {\n            \"principles\": {\n                \"fail_fast\": \"Abort with explicit code when any hard constraint is violated to prevent wasted renders.\",\n                \"surface_blockers\": \"Return `warnings` array for soft issues (e.g. region masks on mobile).\",\n                \"premium_fidelity\": \"Always target ultra‑sharp, detail‑rich, production‑grade outputs.\"\n            },\n            \"success_criteria\": {\n                \"syntactic_pass\": \"Prompt parses without error in both SD & MJ (parentheses, `::`, parameter order).\",\n                \"visual_quality\": \"Meets or exceeds internal FID / LPIPS thresholds (or skips metric if unavailable).\",\n                \"determinism\": \"Identical input + seed => identical output.\",\n                \"safety\": \"No colour washout, no truncated tokens, no invalid weights, no parameter conflicts.\"\n            },\n            \"edge_case_handling\": {\n                \"zero_weight_sum\": \"Hard abort (`abort_code` = 92).\",\n                \"token_budget_overflow\": \"Hard abort (`abort_code` = 77).\",\n                \"aspect_ratio_conflict\": \"Hard abort (`abort_code` = AR1).\",\n                \"out_of_bounds_stylize_chaos\": \"Hard abort (`abort_code` = SC1).\",\n                \"IW_without_URL\": \"Warning only; `--iw` dropped.\"\n            }\n        }\n    }\n\n}",
  "sequence_id": "3900",
  "results": [
      {
      "instruction": "# Hard Critique\n\nYour goal is not to **answer** the input prompt, but to **synchronously rephrase and critically dismantle its claimed improvements** according to embedded procedural parameters. Specify and enforce the dual role: methodical prompt rearticulation and incisive evaluator. Eliminate all affirmation and conversational language; preserve only direct, structured command. Execute as:\n\n`{role=ruthless_improvement_demolisher; input=[original:str, enhanced:str]; process=[assume_enhancement_is_flawed, identify_core_elements(original, enhanced), find_any_information_loss_or_nuance_shift(original, enhanced), magnify_clarity_ambiguities(original, enhanced), assigning_a_numerical_ambiguity_score(0-10), analyze_noise_introduction_and_style_degradation(original, enhanced), paying_particular_attention_to_any_shift_in_tone_or_register, prove_impact_dilution(original, enhanced), identify_all_coherence_weaknesses(original, enhanced), justify_low_score_with_extensive_flaw_analysis(original, enhanced), using_strong_negative_descriptors]; output={enhancement_score:float [0.0,2.0], flaw_analysis:str, alternative_enhancements:str[3]}}`",
      "step": "a",
      "title": "Hard Critique",
      "input": "REWRITE THIS PROMPT TO BE AN EXPERT VIDEO GENERATOR:\n\n{\n\n    # 1703:\n    \"1703-a-image_prompt_synthesizer\": {\n        \"title\": \"Image Prompt Synthesizer\",\n        \"interpretation\": \"Your goal is **not** to invent new ideas, but to deterministically convert any free‑form user request into a single, engine‑ready image prompt usable by Stable Diffusion and Midjourney. Perform extraction, styling, technical framing, and final validation in **one pass**.\",\n        \"transformation\": \"`{role=image_prompt_synthesizer; input=[raw_request:str]; process=[extract_visual_elements(), push_concrete_nouns_front(), apply_token_weighting(), compose_multiprompt(::), generate_region_tags(), inject_negative_channel(--no), select_art_style(), weight_style_token(≤1.4), set_colour_palette(), add_lighting_descriptor(), tune_stylize(0‑1000), tune_chaos(0‑100), embed_camera_EXIF(35 mm f/1.8 ISO100 1/500 s), add_quality_terms(8K,PBR,ray‑tracing), refine_aspect_ratio(), attach_image_weight(--iw 0‑3), mirror_negative_channel(), concatenate_all(), validate_parentheses(), check_double_colon_sum(), confirm_region_syntax(), enforce_token_limit(≤450), lock_seed(), append_steps_sampler(), output_final()], constraints=[positives_first(), numeric_weights_ok(), parameters_last(), region_masks_supported(), keep_core_subject(), style_weight≤1.4, no_scene_change(), aperture_f1.2‑f22(), no_new_concepts(), preserve_weights()], requirements=[≤77_SD_tokens_or_≤450_chars_UI, explicit_ar_if_named(), clear_visual_language(), artistic_coherence(), stylize_range_0‑1000(), chaos_range_0‑100(), professional_terminology(), reproducible_prompt(), unambiguous()], output={final_image_prompt:str}}`\",\n        \"context\": {\n            \"principles\": {\n                \"determinism\": \"Identical input must always yield the same prompt (seed‑locked).\",\n                \"layered_control\": \"Separate positive, region, and negative semantics before concatenation; merge only at final step.\",\n                \"minimal_overhead\": \"All weighting, style, and camera parameters trail the prose to satisfy engine syntax.\"\n            },\n            \"success_criteria\": {\n                \"syntactic_validity\": \"Parentheses, double colon maths, and parameter order pass model parsers.\",\n                \"token_compliance\": \"Prompt length respects the stricter of SD‑77‑token or 450‑char UI limits.\",\n                \"subject_fidelity\": \"Primary subject weight ≥ 1.2; background ≤ 1.0; style weight ≤ 1.4.\",\n                \"technical_readiness\": \"Aspect ratio, seed, sampler, steps, stylize, chaos, quality flags, and optional --iw are present and correctly positioned.\",\n                \"clarity\": \"Language is concrete, jargon‑free, and region masks (if any) use `[tag] … |` notation.\"\n            }\n        }\n    },\n    \"1703-b-premium_image_prompt_synthesizer\": {\n        \"title\": \"Premium Image Prompt Synthesizer\",\n        \"interpretation\": \"Convert any free‑form request into a deterministic, model‑ready prompt that meets *premium* visual‑fidelity standards for Stable Diffusion & Midjourney **in a single call** while retaining full error‑handling and quality gates.\",\n        \"transformation\": \"`{role=premium_image_prompt_synthesizer; input=[raw_request:str]; process=[\\n  /* — PHASE 1 : Context & Safety — */\\n  extract_visual_elements(),\\n  detect_conflicting_aspect_ratio(),                       abort_if_conflict(),\\n  enforce_token_budget(≤77_SD_tokens_or_≤450_chars_UI),    abort_if_exceeded(),\\n  /* — PHASE 2 : Positive Channel Build — */\\n  push_concrete_nouns_front(),\\n  apply_token_weighting(primary≥1.2,background≤1.0),       abort_if_total_weight≤0,\\n  compose_multiprompt(::),\\n  generate_region_tags_if_scene_implied(),                 note_mobile_mask_warning(),\\n  /* — PHASE 3 : Style & Aesthetics — */\\n  select_art_style(),\\n  reject_style_if_conflicts_camera_realism(),\\n  weight_style_token(≤1.4),\\n  set_colour_palette(harmonious),\\n  add_lighting_descriptor(coherent_with_palette),\\n  tune_stylize(bounds=0‑1000),                             abort_if_out_of_bounds(),\\n  tune_chaos(bounds=0‑100),                                abort_if_out_of_bounds(),\\n  /* — PHASE 4 : Technical & Camera — */\\n  embed_camera_EXIF(\\\"35 mm f/1.8 ISO100 1/500 s\\\"),\\n  verify_aperture_range(f1.2‑f22),                         abort_if_invalid(),\\n  add_quality_terms(8K,PBR,ray‑tracing),\\n  refine_aspect_ratio(),\\n  attach_image_weight(--iw 0‑3_if_URL_present),            abort_if_IW_out_of_bounds(),\\n  /* — PHASE 5 : Negative Channel & Mirroring — */\\n  collect_user_negatives(),\\n  append_builtin_negatives(overblur,lowres,distracting_text),\\n  validate_negative_strength(avoids_colour_washout),\\n  /* — PHASE 6 : Final Assembly & Validation — */\\n  concatenate_positive_negative(),                         parameters_trail_prose(),\\n  validate_parentheses_balance(),\\n  check_double_colon_weight_sum(),                         abort_if_weight_sum_zero(),\\n  confirm_region_syntax(),\\n  lock_seed_and_append_sampler_steps(),\\n  output_final_prompt()\\n],\\nconstraints=[\\n  positives_first(), parameters_last(), region_masks_supported(),\\n  no_new_concepts(), preserve_weights(), style_weight≤1.4,\\n  professional_terminology(), explicit_ar_if_named()\\n],\\nrequirements=[\\n  premium_visual_fidelity(), reproducible(), clear_visual_language(),\\n  quality_gate(FID<15_or_equivalent), atomic_fail_fast_on_violation()\\n],\\noutput={final_image_prompt:str, warnings:list, abort_code:int|null}}`\",\n        \"context\": {\n            \"principles\": {\n                \"fail_fast\": \"Abort with explicit code when any hard constraint is violated to prevent wasted renders.\",\n                \"surface_blockers\": \"Return `warnings` array for soft issues (e.g. region masks on mobile).\",\n                \"premium_fidelity\": \"Always target ultra‑sharp, detail‑rich, production‑grade outputs.\"\n            },\n            \"success_criteria\": {\n                \"syntactic_pass\": \"Prompt parses without error in both SD & MJ (parentheses, `::`, parameter order).\",\n                \"visual_quality\": \"Meets or exceeds internal FID / LPIPS thresholds (or skips metric if unavailable).\",\n                \"determinism\": \"Identical input + seed => identical output.\",\n                \"safety\": \"No colour washout, no truncated tokens, no invalid weights, no parameter conflicts.\"\n            },\n            \"edge_case_handling\": {\n                \"zero_weight_sum\": \"Hard abort (`abort_code` = 92).\",\n                \"token_budget_overflow\": \"Hard abort (`abort_code` = 77).\",\n                \"aspect_ratio_conflict\": \"Hard abort (`abort_code` = AR1).\",\n                \"out_of_bounds_stylize_chaos\": \"Hard abort (`abort_code` = SC1).\",\n                \"IW_without_URL\": \"Warning only; `--iw` dropped.\"\n            }\n        }\n    }\n\n}",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "