  {
  "initial_prompt": "Your goal is **not** to answer, solve, or implement the user’s request, but to **expand its context** and **detonate** it into every explicit task, latent dependency, and functional directive—then compile a precise, domain‑agnostic execution specification. Execute as a unified context‑amplification+problem‑decomposition engine:\n`{role=contextual_expander_explosive_decomposer; input=[raw_input:any]; tags={task='contextual_expand_and_decompose'; action='produce_context_snapshot_goal_map_instruction_map'; style='concise'; format='markdown'}; process=[strip_first_person_references(), capture_domain_and_stakeholder_context(), amplify_relevant_background(), identify_explicit_requests(), surface_hidden_assumptions(), enumerate_sub_goals(), detect_execution_blockers(), detonate_into_functional_components(), extract_procedural_constraints(), convert_declaratives_to_operational_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), synthesize_context_snapshot(), construct_goal_map(), compile_functional_instruction_map(), list_execution_dependencies()]; constraints=[no_direct_solutions(), pure_expansion_and_decomposition_only(), deliver_clear_actionable_commands(), preserve_original_sequence_when_mapping(), maintain_domain_agnostic_language()]; requirements=[complete_contextual_expansion(), exhaustive_goal_enumeration(), functional_decomposition_integrity(), directive_conversion_accuracy(), removal_of_subjective_or_self_referential_language(), retention_of_original_intent()]; output={context_snapshot:str, goal_map:list, functional_instruction_map:dict, operational_directives:array, execution_dependencies:array}}`'",
  "sequence_id": "1102",
  "results": [
      {
      "instruction": "# Problem Exploder\n\nYour goal is not to **solve** or **answer** the input, but to **expand its context** and **detonate** it into every explicit directive, hidden dependency, and actionable sub-goal—yielding a crystal-clear operational blueprint. Execute as contextual expander + explosive decomposition engine:\n\n`{role=contextual_expander_explosive_decomposer; input=[raw_input:str]; process=[expand_contextual_boundaries(), strip_first_person_references(), identify_explicit_requests(), surface_hidden_assumptions(), enumerate_sub_goals(), reveal_execution_blockers(), extract_functional_components(), convert_declaratives_to_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), synthesize_operational_directives(), construct_goal_map(), generate_functional_instruction_map() ]; constraints=[no_solutions(), pure_expansion_and_decomposition_only(), deliver_clear_actionable_commands(), preserve_original_sequence(), maintain_domain_agnostic_applicability() ]; requirements=[complete_contextual_expansion(), full_functional_decomposition(), actionable_directive_conversion(), remove_subjective_references(), maintain_operational_accuracy(), preserve_original_intent() ]; output={expanded_context:str, goal_map:list, functional_instruction_map:dict, operational_directives:array, execution_dependencies:array }; direction_vector=['Input fully expanded and decomposed into actionable blueprints', 'Constructive clarity maximizes downstream solution velocity', 'Forward the generated maps and directives to the next processing template']}`",
      "step": "a",
      "title": "Problem Exploder",
      "input": "Your goal is **not** to answer, solve, or implement the user’s request, but to **expand its context** and **detonate** it into every explicit task, latent dependency, and functional directive—then compile a precise, domain‑agnostic execution specification. Execute as a unified context‑amplification+problem‑decomposition engine:\n`{role=contextual_expander_explosive_decomposer; input=[raw_input:any]; tags={task='contextual_expand_and_decompose'; action='produce_context_snapshot_goal_map_instruction_map'; style='concise'; format='markdown'}; process=[strip_first_person_references(), capture_domain_and_stakeholder_context(), amplify_relevant_background(), identify_explicit_requests(), surface_hidden_assumptions(), enumerate_sub_goals(), detect_execution_blockers(), detonate_into_functional_components(), extract_procedural_constraints(), convert_declaratives_to_operational_imperatives(), maintain_procedural_structure(), preserve_technical_terminology(), retain_sequential_flow(), synthesize_context_snapshot(), construct_goal_map(), compile_functional_instruction_map(), list_execution_dependencies()]; constraints=[no_direct_solutions(), pure_expansion_and_decomposition_only(), deliver_clear_actionable_commands(), preserve_original_sequence_when_mapping(), maintain_domain_agnostic_language()]; requirements=[complete_contextual_expansion(), exhaustive_goal_enumeration(), functional_decomposition_integrity(), directive_conversion_accuracy(), removal_of_subjective_or_self_referential_language(), retention_of_original_intent()]; output={context_snapshot:str, goal_map:list, functional_instruction_map:dict, operational_directives:array, execution_dependencies:array}}`'",
      "responses": {
          "gpt-4.1": {
          "model": "gpt-4.1",
          "content": "{\n  \"context_snapshot\": \"This directive requires the complete non-solutional expansion and decomposition of a user input into its full operational and functional blueprint. The purpose is to produce an actionable, domain-neutral framework that details every underlying task, assumption, and dependency inherent to the original request. The process must transform any given input into a contextually amplified snapshot and explode it into a granular, clearly mapped execution structure, preserving the sequence and technical rigor while stripping all subjective or first-person elements.\",\n  \"goal_map\": [\n    \"Strip all first-person and subjective references from the input\",\n    \"Capture and articulate the relevant domain and stakeholder context\",\n    \"Amplify and summarize any relevant background information implied or required\",\n    \"Identify and enumerate each explicit request within the input\",\n    \"Surface and specify any hidden or implicit assumptions underpinning the input\",\n    \"Enumerate all discernible sub-goals embedded in the request\",\n    \"Detect and list potential blockers or dependencies that may impede execution\",\n    \"Explode the input into its smallest coherent functional components\",\n    \"Extract and articulate any procedural or operational constraints from the input\",\n    \"Convert all declarative statements into direct operational imperatives\",\n    \"Maintain the procedural and sequential flow of tasks as outlined in the input\",\n    \"Preserve essential technical terminology and structure from the source\",\n    \"Synthesize a concise and comprehensive context snapshot\",\n    \"Construct a clear goal map catalyzed from the expanded context\",\n    \"Compile a functional instruction map with operational granularity\",\n    \"List all execution dependencies required for fulfillment of the directive\"\n  ],\n  \"functional_instruction_map\": {\n    \"Input Processing\": [\n      \"Eliminate subjective or first-person language\",\n      \"Isolate explicit tasks and requests\",\n      \"Parse and reconstruct technical terminology\"\n    ],\n    \"Context Expansion\": [\n      \"Define the domain and identify implied stakeholders\",\n      \"Summarize background requirements or constraints\",\n      \"Highlight and clarify latent objectives and expected outcomes\"\n    ],\n    \"Decomposition\": [\n      \"Disaggregate all tasks into atomic, actionable units\",\n      \"Sequentially order sub-goals according to original structure\",\n      \"Flag and annotate all dependencies and blockers\"\n    ],\n    \"Directive Synthesis\": [\n      \"Convert high-level descriptions into imperative, executable commands\",\n      \"Maintain technical accuracy and domain neutrality in commands\",\n      \"Aggregate all commands into a structured, clear operational list\"\n    ],\n    \"Output Structuring\": [\n      \"Synthesize a succinct context snapshot reflecting the expanded scope\",\n      \"Enumerate an exhaustive, ordered goal map\",\n      \"Construct a detailed, mapped instruction set\",\n      \"Itemize all identified execution dependencies\"\n    ]\n  },\n  \"operational_directives\": [\n    \"Remove any subjective or first-person phrasing from the user input.\",\n    \"Explicitly detail the relevant domain and potential stakeholders involved.\",\n    \"Provide summarized background and contextual information necessary for understanding or executing the request.\",\n    \"List every request, directive, or question included in the original input.\",\n    \"Identify and articulate any implicit or hidden assumptions within the input.\",\n    \"Decompose the input into all possible sub-goals, maintaining sequence and structure.\",\n    \"Detect potential execution blockers or preconditions required for task completion.\",\n    \"Extract and list all technical and operational terminology present in the input.\",\n    \"Convert all declarative statements into imperative, operational commands.\",\n    \"Retain the specific sequence and procedural ordering found in the source request.\",\n    \"Aggregate outputs into a context snapshot, goal map, instruction map, and execution dependency list.\"\n  ],\n  \"execution_dependencies\": [\n    \"Access to the full raw user input for initial processing\",\n    \"Clarity regarding the relevant domain and potential stakeholders\",\n    \"Knowledge of background context or external requirements implied by the input\",\n    \"Ability to parse, map, and decompose explicit and latent tasks within the request\",\n    \"Mechanisms to identify and preserve technical language without injecting subjectivity\",\n    \"Framework for structuring outputs in the specified formatted sections\"\n  ]\n}",
        }
      }
    }