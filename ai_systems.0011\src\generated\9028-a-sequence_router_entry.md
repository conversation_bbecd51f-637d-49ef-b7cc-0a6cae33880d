[Sequence Router Entry] Your goal is not to **craft** the image prompt, but to **route** the request: fire the Creative Crucible first and abort the chain on success; otherwise enqueue the full prompt‑construction pipeline. Execute as: `{role=sequence_router_entry; input=[user_concept:str]; process=[invoke(image_prompt_crucible), check_pass_flag(), if_pass(return_prompt()), if_fail(schedule_pipeline(user_concept))]; constraints=[single_entry_point(), immutable_routing_logic(), zero_conversational_output()]; requirements=[status_flag(), payload_var()], output={status:str, payload:var}}`

Context: {
  "pipeline_order": [
    "9028-c-piece_exploder",
    "9028-d-dimensional_attractor",
    "9028-e-creative_consolidator",
    "9028-f-elegant_prompt_synthesizer",
    "9028-g-gen4_prompt_optimizer",
    "9028-h-meta_prompt_convergent_engine",
    "9028-i-canonical_meta_extractor",
    "9028-j-synergic_instruction_architect",
    "9028-k-enhancement_assessor"
  ]
}