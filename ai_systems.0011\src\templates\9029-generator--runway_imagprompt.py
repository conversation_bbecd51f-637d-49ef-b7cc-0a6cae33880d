#!/usr/bin/env python3
import sys
from pathlib import Path

# Add parent directory to path
sys.path.append(str(Path(__file__).parent.parent))
from processor import BaseGenerator

TEMPLATES = {

    "9029-a-sequence_router_entry": {
        "title": "Sequence Router Entry",
        "interpretation": "Your goal is not to **craft** an image prompt, but to **route**: call the Crucible first; if it outputs a ≤ 40‑word, ≥ 0.95‑clarity prompt, validate and return; otherwise trigger the full pipeline. Execute as:",
        "transformation": "`{role=sequence_router_entry; input=[concept:str]; process=[invoke(image_prompt_crucible), if_pass(invoke(word_limit_validator)), if_validator_ok(return_prompt()), if_any_fail(schedule_pipeline(concept))]; constraints=[single_entry(), immutable_logic(), zero_conversation]; requirements=[status_flag(), payload_var()], output={status:str, payload:var}}`",
        "context": {
            "pipeline_order": [
                "9029-c-piece_exploder",
                "9029-d-dimensional_attractor",
                "9029-e-prompt_synthesizer",
                "9029-f-gen4_prompt_optimizer",
                "9029-g-enhancement_assessor"
            ]
        }
    },

    "9029-b-image_prompt_crucible": {
        "title": "Creative Crucible – Image Prompt",
        "interpretation": "Your goal is not to **analyze** the concept, but to **crystallize** it into one elite Runway‑ready prompt (≤ 40 words, single sentence). Execute as:",
        "transformation": "`{role=image_prompt_crucible; input=[concept:str]; process=[generate_variants(n=6, limit=40), score_clarity(runway_metrics), pick_best()], constraints=[include_subject(), include_environment(), optional_style_tags(), no_camera_jargon], requirements=[clarity≥0.95, return_prompt_only], output={draft_prompt:str, clarity:float}}`",
        "context": {
            "fail_condition": "If clarity<0.95 or words>40"
        }
    },

    "9029-b2-word_limit_validator": {
        "title": "Word‑Limit Validator",
        "interpretation": "Your goal is not to **edit** the prompt, but to **confirm** it contains ≤ 40 tokens and no forbidden wording. Execute as:",
        "transformation": "`{role=word_limit_validator; input=[draft_prompt:str]; process=[count_words(), scan_for_banned_terms()], constraints=[max_words(40)], requirements=[return_ok_flag()], output={validator_status:str}}`",
        "context": {}
    },

    "9029-c-piece_exploder": {
        "title": "Piece Exploder – Image Concept",
        "interpretation": "Your goal is not to **synthesize**, but to **atomize** the concept into discrete prompt components. Execute as:",
        "transformation": "`{role=prompt_piece_exploder; input=[concept:str]; process=[identify(subject,environment,style,lighting), tag(novelty,resonance)], constraints=[no_fusion], requirements=[component_dict:dict], output={components:dict}}`",
        "context": {}
    },

    "9029-d-dimensional_attractor": {
        "title": "Dimensional Attractor",
        "interpretation": "Your goal is not to **average**, but to **align** all components on one stylistic axis (e.g., gestural‑anime watercolor) and prune contradictions. Execute as:",
        "transformation": "`{role=style_attractor; input=[components:dict]; process=[select_axis(), align_all(), prune_conflict()], constraints=[axis_matches_runway_categories()], requirements=[aligned_components:dict], output={aligned_components:dict}}`",
        "context": {}
    },

    "9029-e-prompt_synthesizer": {
        "title": "Prompt Synthesizer",
        "interpretation": "Your goal is not to **list**, but to **fuse** aligned components into one 40‑word max sentence. Execute as:",
        "transformation": "`{role=prompt_synthesizer; input=[aligned_components:dict]; process=[construct_sentence(order=shot→subject→action→environment→style→lighting), trim_excess()], constraints=[max_words(40)], requirements=[draft_prompt:str], output={draft_prompt:str}}`",
        "context": {}
    },

    "9029-f-gen4_prompt_optimizer": {
        "title": "Gen‑4 Prompt Optimizer",
        "interpretation": "Your goal is not to **rewrite** arbitrarily, but to **verify** that the draft prompt passes Runway clarity and keyword heuristics, then patch only where needed. Execute as:",
        "transformation": "`{role=gen4_prompt_optimizer; input=[draft_prompt:str]; process=[run_clarity_test(), if<0.95(apply_minimal_fixes()), remove_banned_words()], constraints=[max_words(40)], requirements=[final_prompt:str, clarity_score:float], output={final_prompt:str}}`",
        "context": {}
    },

    "9029-g-enhancement_assessor": {
        "title": "Enhancement Assessor",
        "interpretation": "Your goal is not to **endorse** the optimizer, but to **attack** its output—score ambiguity and produce three sharper alternatives. Execute as:",
        "transformation": "`{role=prompt_enhancement_assessor; input=[original:str, improved:str]; process=[assume_flawed(), detect_blur(), score(0‑10), craft_three_alternatives()], constraints=[must_return_three()], requirements=[score:float, alternatives:list], output={assessment:dict}}`",
        "context": {}
    }

}

def main():
    """Main execution function."""
    generator = BaseGenerator(
        series_base=9016,
        output_dir=Path(__file__).parent.parent / "generated"
    )
    generator.run(TEMPLATES)

if __name__ == "__main__":
    main()

